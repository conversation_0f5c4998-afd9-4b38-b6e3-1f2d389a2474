# Database Connection Timeout Fixes

## Problem Analysis

The `ConnectionAcquireTimeoutError` in `LinePlanAcquisitionServiceTx.ts` was caused by several critical issues:

### Root Causes Identified:

1. **Insufficient Connection Pool Size**: Only 5 max connections with 3-second timeout
2. **Multiple Connection Pool Instances**: `ReserveRewriteHandler` created separate pool
3. **Long-Running Transactions**: Complex business logic holding connections too long
4. **Nested Transaction Blocks**: Multiple `retryQuery` blocks creating new transactions
5. **Row Locking with Delays**: Up to 5 retry attempts with 1-second waits each

## Fixes Implemented

### 1. Connection Pool Configuration (`src/database/psql.ts`)

**Before:**
```typescript
pool: {
    max: 5,
    min: 0,
    idle: 0,
    acquire: 3000,
}
```

**After:**
```typescript
pool: {
    max: 15,        // Increased from 5
    min: 2,         // Keep minimum connections
    idle: 30000,    // 30 seconds idle time
    acquire: 10000, // 10 seconds timeout
    evict: 60000,   // 1 minute eviction
}
```

### 2. Connection Pool Monitoring

Added comprehensive monitoring:
- Connection acquire/release logging
- Pool status endpoint: `GET /portal/db/connection-pool-status`
- Error tracking for connection issues
- Retry configuration for connection errors

### 3. Fixed Duplicate Connection Pools

**Before (ReserveRewriteHandler):**
```typescript
private async useCorePsql(): Promise<Sequelize> {
    // Created separate connection pool
    const sequelize = new Sequelize(psqlDBURI, { pool: {...} });
}
```

**After:**
```typescript
private async useCorePsql(): Promise<Sequelize> {
    // Use shared connection pool
    const { usePsql } = await import("@/database/psql");
    return await usePsql();
}
```

## Monitoring and Debugging

### Connection Pool Status Endpoint

Access: `GET /portal/db/connection-pool-status`

Returns:
```json
{
    "connectionPool": {
        "size": 3,
        "available": 2,
        "using": 1,
        "waiting": 0,
        "max": 15,
        "min": 2,
        "idle": 30000,
        "acquire": 10000,
        "evict": 60000
    },
    "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### Console Logging

Connection events are now logged:
- `Connection acquired: {uuid}`
- `Connection released: {uuid}`
- `Connection removed: {uuid}`
- `Connection pool create error: {error}`
- `Connection pool acquire error: {error}`

## Additional Recommendations

### 1. Transaction Optimization in LinePlanAcquisitionServiceTx

Consider breaking down the large transaction into smaller ones:

```typescript
// Instead of one large transaction, use multiple smaller ones
const result1 = await retryQuery(context, "step1", async () => {
    const tx = await sequelize.transaction();
    try {
        // Do step 1 work
        await tx.commit();
        return result;
    } catch (e) {
        await tx.rollback();
        throw e;
    }
});

const result2 = await retryQuery(context, "step2", async () => {
    const tx = await sequelize.transaction();
    try {
        // Do step 2 work
        await tx.commit();
        return result;
    } catch (e) {
        await tx.rollback();
        throw e;
    }
});
```

### 2. Row Locking Optimization

Consider using `SELECT ... FOR UPDATE NOWAIT` to fail fast instead of waiting:

```sql
SELECT * FROM lines WHERE line_id = ? FOR UPDATE NOWAIT
```

### 3. Connection Pool Monitoring Alerts

Set up alerts when:
- `using` connections > 80% of `max`
- `waiting` connections > 0
- Connection acquire errors occur

### 4. Database Performance

Consider:
- Adding database indexes for frequently queried columns
- Analyzing slow queries
- Implementing read replicas for read-heavy operations

## Testing the Fixes

1. **Monitor Connection Pool**: Check the new endpoint regularly
2. **Load Testing**: Test with concurrent requests to LinePlanAcquisitionServiceTx
3. **Error Monitoring**: Watch for connection timeout errors in logs
4. **Performance**: Measure response times before/after changes

## Rollback Plan

If issues occur, revert these files:
- `src/database/psql.ts`
- `src/functions/triggers/ReserveRewriteHandler.ts`
- Remove `src/functions/portal/GetConnectionPoolStatusHandler.ts`
- Remove the endpoint from `src/index.ts`
