## 概要 (Overview)

対象API: 
- 回線追加チャージ(クーポン)容量・期間追加: /Lines/charge
- 回線プラン変更: /Lines/plan
- 回線グループ追加チャージ(クーポン)容量・期間追加: /LinesGroup/charge

tenantTypeに関するロジックを調査し、tenant RKM000に対して新しいtenantType (5)の処理を追加します。

## ゴール (Objective)

// 日本語：RKM000のtenantType 5の注文に対して、特別な処理を追加します。tenantType 1、2、または3の注文と同様に動作し、エラーが発生しないことを確認します。


## 仕様 (Specifications)

### フロー概要 (Flow Overview)

https://docs.google.com/presentation/d/1BR6in4EHU5gQR3ChSY-YdLMUVXF-ua9D/edit?slide=id.p5#slide=id.p5

## 対応内容 (What to do)

以下のAPIでtenantType 5の処理を追加します。

- 回線追加チャージ(クーポン)容量・期間追加: /Lines/charge:
「Swimmyへの連携を不要」の判断ロジックを追加する
「社内テナント種別が1または2または3の場合」→「社内テナント種別が1または2または3または5の場合」
参考：4.3.3-回線追加チャージ(クーポン)容量追加・期間追加機能.docx (P.8)

- 回線プラン変更: /Lines/plan
・グループ所属状態でのプラン変更を可能にする。
参考：4.3.7-回線プラン変更.docx (P.7)
「1.社内テナント種別が2（B-OCN）または3（UNOモバイル）または4（ICMS）または・・・」→
「1.社内テナント種別が2（B-OCN）または3（UNOモバイル）または4（ICMS）または5（RINKモバイル）または・・・」

・半黒状態でのプラン変更を許可する。
参考: 4.3.7-回線プラン変更.docx (P.15)
「社内テナント種別が”2”（B-OCN）もしくは”3”（UNOモバイル）もしくは”4”（ICM）以外、・・・」→
「社内テナント種別が”2”（B-OCN）もしくは”3”（UNOモバイル）もしくは”4”（ICM）もしくは”5”（RINKモバイル）以外、・・・」

- 回線グループ追加チャージ(クーポン)容量・期間追加: /LinesGroup/charge
・Swimmyへの連携を不要とする。
参考：4.4.3-回線グループ追加チャージ(クーポン)容量追加・期間追加機能.docx (P.9)
「社内テナント種別が1または2または3の場合」→「社内テナント種別が1または2または3または5の場合」

## テスト観点 (Test Scenarios)

RKM000としてオーダーを実行し、以下のAPIで正常に動作することを確認します。
// - 回線追加チャージ(クーポン)容量・期間追加 → Swimmyへの連携を不要
// - 回線プラン変更 → グループ所属状態でのプラン変更を可能
// - 回線プラン変更 → 半黒状態でのプラン変更を許可
// - 回線グループ追加チャージ(クーポン)容量・期間追加 → Swimmyへの連携を不要    
// また、tenantType 0, 1、2、3のオーダーも正常に動作し、エラーが発生しないことを確認します。


## 対応済 (Implemented)

// Vietnamese: Liệt kê các phần đã được thực hiện trong tính năng này.

// 日本語：この機能で実装された部分をリストアップします。

APIの修正:

    POST Lines/charge -> RINKモバイルテナント許可、Swimmyへの連携を不要とする
    POST Lines/plan -> RINKモバイルテナント許可, Swimmy N番をnullに変更は未対応
    POST LinesGroup/charge -> RINKモバイルテナント許可、Swimmyへの連携を不要とする

// English: List the parts that have been implemented in this feature.

Modified APIs:
    POST Lines/charge -> Allow RINKMobile Tenant、Skip Swimmy connection
    POST Lines/plan -> Allow RINKMobile Tenant, leave Swimmy N number set to null as for now
    POST LinesGroup/charge -> Allow RINKMobile Tenant、Skip Swimmy connection

// Chinese: 列出此功能中已實現的部分。

// Mongolian: Энэ онцлогт хэрэгжүүлсэн хэсгүүдийг жагсаана уу.

// Indonesian: Daftar bagian yang telah diimplementasikan dalam fitur ini.

## 関連ファイル・コード・チケット (Related Files, Code, and Tickets)

// Vietnamese: Liệt kê các tệp, mã nguồn và vé liên quan đến tính năng này.

// 日本語：この機能に関連するファイル、コード、およびチケットをリストアップします。

// English: List the files, code, and tickets related to this feature.

Related files + corresponding tests:
    ```
    LineAddAcquisitionService.ts
    LinePlanAcquisitionServiceTx.ts
    LineGroupAddAcquisitionService.ts
    LineAddAcquisitionHandler.ts
    LineGroupAddAcquisitionHandler.ts
    LineAddAcquisitionHandler.test.ts
    LineGroupAddAcquisitionHandler.test.ts
    ```
Routes:
    ```
    POST Lines/charge
    POST Lines/plan
    POST LinesGroup/charge
    ```
// Chinese: 列出與此功能相關的文件、代碼和工單。

// Mongolian: Энэ онцлогтой холбоотой файлууд, код болон тасалбаруудыг жагсаана уу.