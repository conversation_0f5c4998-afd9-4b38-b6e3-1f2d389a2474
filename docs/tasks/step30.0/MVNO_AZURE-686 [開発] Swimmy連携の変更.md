## 概要 (Overview)

対象API: 
- 回線グループ所属変更: /LinesGroup/LineGroup_modify

「回線グループ所属変更」APIにおいて契約番号（N番）を送付する際、RINKモバイルのN番ではなく、NULLで送る（key:valueなしで送る）。

## ゴール (Objective)

// 日本語：RKM000のtenantType 5の注文に対して、契約番号（N番）を送付する際、RINKモバイルのN番ではなく、NULLで送る（key:valueなしで送る）。


## 仕様 (Specifications)

### フロー概要 (Flow Overview)

https://docs.google.com/presentation/d/1-XUua0vvw6rvAvcoK5dZz5CBvE_s8rzo/edit?slide=id.p10#slide=id.p10

## 対応内容 (What to do)

以下のAPIでtenantType 5の処理を追加します。

- 回線グループ所属変更: /LinesGroup/LineGroup_modify:
RKM000のtenantType 5の場合Swimmyへの連携をする際にN番ではなくNUllを送る
「SwimmyへN番のデータを送る」→「RKM000のtenantType 5 の場合取得したN番をnullしてSwimmyへN番のデータは送らない。他のテナントの場合は通常通り」

## テスト観点 (Test Scenarios)

RKM000としてオーダーを実行し、以下のAPIで正常に動作することを確認します。
// - 回線グループ所属変更 → Swimmyへ連携する際に契約番号を送らない


## 対応済 (Implemented)

// Vietnamese: Liệt kê các phần đã được thực hiện trong tính năng này.

// 日本語：この機能で実装された部分をリストアップします。

// English: List the parts that have been implemented in this feature.

// Chinese: 列出此功能中已實現的部分。

// Mongolian: Энэ онцлогт хэрэгжүүлсэн хэсгүүдийг жагсаана уу.

// Indonesian: Daftar bagian yang telah diimplementasikan dalam fitur ini.

## 関連ファイル・コード・チケット (Related Files, Code, and Tickets)

// Vietnamese: Liệt kê các tệp, mã nguồn và vé liên quan đến tính năng này.

// 日本語：この機能に関連するファイル、コード、およびチケットをリストアップします。

// English: List the files, code, and tickets related to this feature.

// Chinese: 列出與此功能相關的文件、代碼和工單。

// Mongolian: Энэ онцлогтой холбоотой файлууд, код болон тасалбаруудыг жагсаана уу.