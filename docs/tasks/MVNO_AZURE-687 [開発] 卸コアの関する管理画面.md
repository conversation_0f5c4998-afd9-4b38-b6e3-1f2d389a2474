## 概要 (Overview)

// 日本語：CORE APIの処理に関連する管理画面と必要なPortal APIを作成します。

// English: Create the necessary management screen and Portal API for managing and performing the required operations related to the processing of CORE API.

## ゴール (Objective)

// 日本語：CORE APIサービスの詳細画面を作成し、CORE APIサービスに関連する情報と、CORE APIの処理を管理および実行するために必要な操作を表示します。

// English: Create a detailed screen for CORE API Service, displaying information related to CORE API Service and the necessary operations to manage and perform the processing of CORE API.

## 仕様 (Specifications)

### フロー概要 (Flow Overview)

// 日本語：
- CORE APIサービスの詳細画面に表示できる処理やステータスを調査します。
- CORE APIサービスの稼働状況を確認するために、サービスステータスにCORE APIサービスのヘルスチェックを追加します。
- 新しい画面を設計し、フローは「CORE APIサービスをクリック -> CORE APIサービスの詳細画面に移動」とします。
- この画面では、CORE APIサービスに関連する情報と、CORE APIの処理を管理および実行するために必要な操作を表示します。

// English:
- Investigate the processes and statuses that can be displayed on the detailed screen of CORE API Service
- Add a service health check for CORE API Service to check the operational status of CORE API Service
- Design a new screen with the flow: Click on CORE API Service -> Navigate to the detailed screen of CORE API Service
- On this screen, display information related to CORE API Service and the necessary operations to manage and perform the processing of CORE API

### 詳細仕様 (Detailed Specifications)

// 日本語：CORE APIサービスの詳細画面には、以下の情報とCORE APIの処理を管理および実行するために必要な操作を表示する必要があります。
- 各tenantIdの接続数を表示し、現在の接続数をリセットできるようにする（Redisに保存）
- 特定のtenantIdの現在の接続数を選択してリセットできるようにする
- 可能であれば、CORE APIサービスに関連するCron Jobのリストとステータスを表示し、必要に応じてこれらのCron Jobを再試行できるようにする
- セキュリティ要件：この画面にはSuper Adminのみがアクセスでき、CORE APIサービスに関連する操作を実行できるようにする

// English: The detailed screen of CORE API Service should display the following information and necessary operations to manage and perform the processing of CORE API:
- Display the list of tenantId and the current number of connections for each tenantId (stored in Redis)
- Allow selecting and resetting the current number of connections for a specific tenantId
- If possible, display the list and status of Cron Jobs related to CORE API Service, with the ability to retry these Cron Jobs if necessary
- Security Requirements: Only Super Admin can access this screen and perform operations related to CORE API Service.

## テスト観点 (Test Scenarios)

// 日本語：
- Super Adminと他のロールのアクセス権限を確認する
- tenantIdのリストと現在の接続数を表示する機能を確認
- 特定のtenantIdの現在の接続数をリセットする機能を確認
- CORE APIサービスに関連するCron Jobのリストとステータスを表示する機能を確認
- Cron Jobを再試行する機能を確認

// English:
- Verify the access permissions of Super Admin and other roles
- Check the functionality to display the list of tenantId and the current number of connections
- Verify the functionality to reset the current number of connections for a specific tenantId
- Check the functionality to display the list and status of Cron Jobs related to CORE API Service
- Verify the functionality to retry the Cron Jobs

## 対応済 (Implemented)

// 日本語：この機能で実装された部分をリストアップします。

APIの実装:
- `GET portal/tenants/connections`: テナントIDと現在の接続数のリストを取得するAPIを実装しました。
- `POST portal/tenants/connections/reset`: 特定のテナントIDの現在の接続数をリセットするAPIを実装しました。
- `GET portal/cronjob/list`: CORE APIサービスに関連するCron Jobのリストを取得するAPIを実装しました。
- `POST portal/cronjob/restart`: CORE APIサービスに関連する特定のCron Jobを再起動するAPIを実装しました。
- MVNO_GUIに、管理者機能 -> Core API管理ページに新しいテーブルを追加し、必要な情報と操作を表示しました。

// English: List the parts that have been implemented in this feature.

Created four APIs:
- `GET portal/tenants/connections`: Retrieves the list of tenantId and current connection count.
- `POST portal/tenants/connections/reset`: Resets the current connection count for a specific tenantId.
- `GET portal/cronjob/list`: Retrieves the list of Cron Jobs related to CORE API Service.
- `POST portal/cronjob/retry`: Retries a specific Cron Job related to CORE API Service.
- Added a new table at MVNO_GUI under 管理者機能 -> Core API管理 page, displaying the necessary information and operations.


## 関連ファイル・コード・チケット (Related Files, Code, and Tickets)

// 日本語：この機能に関連するファイル、コード、およびチケットをリストアップします。

// English: List the files, code, and tickets related to this feature.

Related files + corresponding tests:
```markdown
RESTComon.ts: resetTenantConnectCount
GetTenantConnectionsHandler.ts
ResetTenantConnectionsHandler.ts
GetCronjobListHandler.ts
RestartCronjobHandler.ts
```

Routes:
```markdown
GET  portal/tenants/connections
POST portal/tenants/connections/reset
GET  portal/cronjob/list
POST portal/cronjob/retry
```
