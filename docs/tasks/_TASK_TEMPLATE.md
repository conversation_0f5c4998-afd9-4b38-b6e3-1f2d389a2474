## 概要 (Overview)

// Vietnamese: <PERSON>ô tả ngắn gọn về tính năng / xử lý nghiệp vụ này là gì và lý do tại sao cần thực hiện.

// 日本語：このタスクの概要と目的を簡潔に説明します。

// English: Summary of the task and its purpose.

// Chinese: 簡要描述此任務的功能和目的。

// Mongolian: Энэ даалгаврын зорилго, зорилгыг товчхон тайлбарлана уу。

// Indonesian: Ringkasan tugas dan tujuannya.

## ゴール (Objective)

// Vietnamese: Nêu rõ mục tiêu cần đạt được

// 日本語：このタスクで達成すべき目標を明確に記述します。

// English: Clearly state the objectives to be achieved in this task.

// Chinese: 清楚說明此任務需要達成的目標。

// Mongolian: Энэ даалгавраар биелүүлэх ёстой зорилтуудыг тодорхой бичнэ үү.

// Indonesian: <PERSON><PERSON><PERSON> dengan jelas tujuan yang harus dicapai dalam tugas ini.

## 仕様 (Specifications)

### フロー概要 (Flow Overview)

// Vietnamese: Mô tả ngắn gọn về luồng xử lý nghiệp vụ này.

// 日本語：この業務処理のフローを簡潔に説明します。

// English: Briefly describe the flow of this business process.

// Chinese: 簡要描述此業務處理的流程。

// Mongolian: Энэ бизнесийн үйл явцыг товчхон тайлбарлана уу.

// Indonesian: Jelaskan secara singkat alur proses bisnis ini.

### 詳細仕様 (Detailed Specifications)

// Vietnamese: Cung cấp chi tiết về các yêu cầu kỹ thuật, bao gồm:

// 日本語：技術的な要件の詳細を提供します。

// English: Provide detailed technical requirements, including:

// Chinese: 提供詳細的技術要求，包括：

// Mongolian: Техникийн шаардлагын дэлгэрэнгүй мэдээллийг өгнө, үүнд:

// Indonesian: Berikan rincian persyaratan teknis, termasuk:

- API エンドポイント (API Endpoints)
- リクエストとレスポンスのフォーマット (Request and Response Formats)
- データベースのスキーマ (Database Schema)
- エラーハンドリング (Error Handling)
- セキュリティ要件 (Security Requirements)
- その他の技術的な詳細 (Other Technical Details)

## データ構造 (Database References)

// Vietnamese: Mô tả cấu trúc dữ liệu liên quan đến tính năng này, bao gồm các bảng, trường và mối quan hệ.

// 日本語：この機能に関連するデータ構造を説明します。テーブル、フィールド、および関係を含みます。

// English: Describe the data structure related to this feature, including tables, fields, and relationships.

// Chinese: 描述與此功能相關的數據結構，包括表格、字段和關係。

// Mongolian: Энэ онцлогтой холбоотой өгөгдлийн бүтэц, хүснэгтүүд, талбарууд болон харилцааг тайлбарлана уу.

// Indonesian: Jelaskan struktur data yang terkait dengan fitur ini, termasuk tabel, kolom, dan hubungan.

## 対応内容 (What to do)

// Vietnamese: Liệt kê các công việc cần thực hiện để hoàn thành tính năng này, bao gồm:

// 日本語：この機能を完成させるために実行する必要がある作業をリストアップします。

// English: List the tasks that need to be performed to complete this feature, including:

// Chinese: 列出完成此功能所需執行的任務，包括：

// Mongolian: Энэ онцлогийг гүйцэтгэхийн тулд хийх ёстой ажлуудыг жагсаана уу, үүнд:

// Indonesian: Daftar tugas yang perlu dilakukan untuk menyelesaikan fitur ini, termasuk:

- コーディング (Coding)
- テスト (Testing)
- ドキュメンテーション (Documentation)
- デプロイ (Deployment)
- その他の関連作業 (Other Related Tasks)

## テスト観点 (Test Scenarios)

// Vietnamese: Mô tả các kịch bản kiểm thử cần thực hiện để đảm bảo tính năng hoạt động đúng.

// 日本語：機能が正しく動作することを確認するために実行する必要があるテストシナリオを説明します。

// English: Describe the test scenarios that need to be executed to ensure the feature works correctly.

// Chinese: 描述需要執行的測試場景，以確保功能正確運作。

// Mongolian: Онцлог нь зөв ажиллаж байгааг баталгаажуулахын тулд гүйцэтгэх шаардлагатай тестийн нөхцөл байдлыг тайлбарлана уу.

// Indonesian: Jelaskan skenario pengujian yang perlu dijalankan untuk memastikan fitur berfungsi dengan benar.

## 対応済 (Implemented)

// Vietnamese: Liệt kê các phần đã được thực hiện trong tính năng này.

// 日本語：この機能で実装された部分をリストアップします。

// English: List the parts that have been implemented in this feature.

// Chinese: 列出此功能中已實現的部分。

// Mongolian: Энэ онцлогт хэрэгжүүлсэн хэсгүүдийг жагсаана уу.

// Indonesian: Daftar bagian yang telah diimplementasikan dalam fitur ini.

## 関連ファイル・コード・チケット (Related Files, Code, and Tickets)

// Vietnamese: Liệt kê các tệp, mã nguồn và vé liên quan đến tính năng này.

// 日本語：この機能に関連するファイル、コード、およびチケットをリストアップします。

// English: List the files, code, and tickets related to this feature.

// Chinese: 列出與此功能相關的文件、代碼和工單。

// Mongolian: Энэ онцлогтой холбоотой файлууд, код болон тасалбаруудыг жагсаана уу.

⭐️ Tips: Write by markdown format to make it easy to read and understand.