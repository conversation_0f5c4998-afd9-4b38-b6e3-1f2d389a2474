## 仕様

対象API：仮登録回線追加（/Lines/preadd）

同一MVNE間のMNP転入の場合、Swimmyには以下の順に連携する必要がある。
- ①転入元テナントの回線廃止
- ②転入先テナントの回線新設
- ③その他のオーダ（プラン変更、回線オプション変更、NW暗証番号変更…等）

①を連携する前に、予約オーダがあった場合は予約キャンセル（オーダ取消）をSwimmyに連携する。
- 回線廃止、プラン変更など。これらがSwimmyに予約された状態で回線廃止を連携してもエラーになってしまうため、これをキャンセルしたい
- 卸ポータルフロントの回線廃止オーダ、卸ポータルコアのSO管理テーブルを確認し、予約が入っていた場合はSwimmyにオーダ取消を送る

## 対応内容

「①転入元テナントの回線廃止」を連携する前に、卸ポータルコアのSO管理テーブル（service_orders）　を確認し、転入元テナントの予約中の下記のオーダ種別があれば、先にSwimmyにオーダ取消を送る
- プラン変更：Swimmyにオーダ取消を送ると同時に、卸コアのSO管理テーブル上のステータスもキャンセル済に変更してください。（卸コアAPIの予約キャンセル機能を利用してもよいかも）
- 回線廃止：Swimmyにオーダ取消を送ると同時に、卸フロントの廃止SO管理テーブル上のステータスもキャンセル済に変更してください。

## 対応済

PreLineAddServiceの処理が正常に完了後、Swimmy連携を行う。※csvUnnecessaryFlag != 1
Swimmy連携パターンは以下である。

### 同一MVNEではない仮登録回線追加

同一MVNEではない場合は回線追加(007)オーダをCoreSwimmyApiLogとして登録する。

### 同一MVNEの仮登録回線追加

#### メイン処理(service関数)

- プラン変更と回線廃止**以外**の予約オーダをキャンセルする。(DBの`order_status`更新のみ)

#### メイン処理後

1. プラン変更と回線廃止の予約中のオーダがあれば先にキャンセルAPIへ送る。（CoreSwimmy連携が必要ため）

    ・プラン変更：コア外部APIのSOキャンセルAPIを用いてリクエストを投げる
    ```
    PreLineAddHandler     CoreAPI (/so_cancel)       Swimmy連携
            ○  ----------------->  |                  |
            |                      ○  ------------->  |
            |  <-----------------  ○                  |
    ```

    ・回線廃止：フロント外部APIの回線廃止予約キャンセルAPIへリクエストを投げる (MongoDB更新のため)
    ```
    PreLineAddHandler     FrontExtAPI (/cancel)   CoreAPI (/so_frontcancel)    Swimmy連携
            ○  ------------------>  |                         |                 |
            |                       ○  -------------------->  |                 |
            |                       |                         ○  ------------>  |
            |                       |  <--------------------  ○                 |
            |  <------------------  ○                         |                 |
    ```

> [!IMPORTANT] Swimmy連携 part is done asynchronously (by sending cancel order to Service Bus)

2. 回線廃止のSwimmyオーダ：CoreSwimmyApiLogを作成し、すぐServiceBusに入れない
3. 回線追加のSwimmyオーダ：
   1. CoreSwimmyApiLogを作成する (setWaitingForId <- 廃止オーダ)
   2. 回線追加オーダをServiceBusに入れない
   3. 回線廃止オーダをServiceBusに入れる

## 同一MVNEのチェーンオーダについて

同一MVNEの仮登録回線はCoreSwimmyに２つのリクエストを以下の順に送ります。

1. 回線廃止
2. 仮登録回線

「仮登録回線」が連携されるまでは新しいオーダが来る場合は保留にする必要があり、「仮登録回線」の連携が完了後、新しいオーダを
連携します。保留されているオーダは前のオーダの`needToActivateTheseIDs`で保存され、キューにすぐ入れません。

CoreSwimmy連携（ServiceBusトリガー）は以下のロジックでチェーンオーダを処理します。

1. オーダのリクエストをCoreSwimmyに送る
2. ステータス更新
3. ステータスが正常の場合は `needToActivateTheseIDs`にあるオーダをキューに入れます。※

※回線追加のオーダはスケジュールメッセージとしてキューに入れます。