# Swimmy Request Delay

When sending request to <PERSON><PERSON><PERSON>, if there are multiple orders with same line number sent in
succession, there is a high chance that the latter request will be failed due to <PERSON><PERSON><PERSON>'s processing
delay. To avoid this, subsequent requests should wait for a specific time before being forwarded to
<PERSON><PERSON><PERSON>.

There are two types of transactions and the handling is different
- 同一MVNE転入
- other transactions

### 同一MVNE転入 creation

> [!NOTE]
> see: [同一MVNE間のMNP転入の追加対応.md](MVNO_AZURE-662.%20同一MVNE間のMNP転入の追加対応.md)

```mermaid
flowchart TD
    Start@{ shape: sm-circ, label: "Small start" }
    Stop@{ shape: framed-circle, label: "Stop" }
    isSameMvne{Is Same MVNE?}
    hasCancelOrder{Has Sent Cancel Order?}
    shinki[Create 新規 transaction]
    shinkiMvne[Create 新規 transaction]
    haishi[Create 廃止 transaction]
    qShinki[Send 新規 to queue: immediate]
    qiHaishi[Send 廃止 to queue: immediate]
    qsHaishi[Send 廃止 to queue: scheduled]

    Start --> isSameMvne
    isSameMvne -- No --> shinki --> qShinki --> Stop
    isSameMvne -- Yes --> haishi --> shinkiMvne
    shinkiMvne --> hasCancelOrder
    hasCancelOrder -- Yes --> qsHaishi --> Stop
    hasCancelOrder -- No --> qiHaishi --> Stop
```

### Other transactions creation

```mermaid
flowchart TD
    Start@{ shape: sm-circ, label: "Small start" }
    Stop@{ shape: framed-circle, label: "Stop" }
    getPreviousTX[Get latest transaction with same kaisenNo and createdAt within 1 minute]
    checkPreviousTX{Previous transactions found?}
    checkPrevStatus{Is previous status 未登録依頼 or 待機中 ?}
    checkPrevSameMvne{Is previous transaction 同一MVNE転入 ?}
    tagCurrentMvne[Set current's isChainedTransaction=true]
    createNormal[Create TX<br/>未登録依頼 & jikkouchuFlg=true]
    createLinked[Create TX<br/>待機中 & jikkouchuFlg=false]
    createSched[Create TX<br/>未登録依頼 & jikkouchuFlg=true & scheduledAt +1min]
    linkToPrev[Add id to previous TX's needToActivateTheseIDs]
    sendImmediate[Send to queue: immediate]
    sendScheduled[Send to queue: scheduled]

    Start --> getPreviousTX
    getPreviousTX --> checkPreviousTX
    checkPreviousTX -- No --> createNormal --> sendImmediate --> Stop
    checkPreviousTX -- Yes --> checkPrevStatus
    checkPrevStatus -- Yes --> checkPrevSameMvne
    checkPrevSameMvne -- Yes --> tagCurrentMvne --> createLinked
    checkPrevSameMvne -- No --> createLinked
    createLinked --> linkToPrev --> Stop
    checkPrevStatus -- No --> createSched --> sendScheduled --> Stop
```

## Message Handling

Depending on whether the transaction is part of 同一MVNE転入 or not:
- if it is, need to make sure current transaction result is 登録依頼OK before activating the next transaction
- if not, just activate the next transaction without checking current result

```mermaid
flowchart TD
    Start@{ shape: sm-circ, label: "Small start" }
    Stop@{ shape: framed-circle, label: "Stop" }
    doProceedActivation{current status=登録依頼OK or isChainedTransaction!=true ?}
    activate[Activate each transaction in needToActivateTheseIDs]
    updateStatus[Set jikkouchuFlg=true, status=未登録依頼]
    isSameMvneAdd{is swimmyType=回線追加 and part of 同一MVNE転入 ?}
    scheduleCustom[use masking time logic for schedule time]
    scheduleDefault[use default 1 minute delay for schedule time]
    sendToQueue[Send to queue: scheduled]

    Start --> doProceedActivation
    doProceedActivation -- Yes --> activate --> updateStatus
    subgraph proceedActivation [For each transaction]
        updateStatus --> isSameMvneAdd
        isSameMvneAdd -- Yes --> scheduleCustom --> sendToQueue
        isSameMvneAdd -- No --> scheduleDefault --> sendToQueue
    end
    sendToQueue --> Stop
    doProceedActivation -- No --> Stop
```