# MVNO Core API Project Guide

## Project Overview

The **MVNO Core API** is a comprehensive Azure Functions-based API system designed for Mobile Virtual Network Operator (MVNO) services. This project serves as the core backend infrastructure for managing mobile network operations, including line management, plan changes, SIM operations, and portal functionalities.

### Key Features

- **Line Management**: Complete lifecycle management of mobile lines including activation, suspension, termination, and modification
- **Plan Management**: Handle plan changes, coupon acquisitions, and data gift operations
- **Group Operations**: Manage line groups with comprehensive CRUD operations
- **SIM Operations**: Handle SIM reissuance and terminal management
- **Portal Integration**: Dedicated portal functions for user interface operations
- **Service Order Management**: Complete SO (Service Order) lifecycle management
- **Real-time Processing**: Queue-based and timer-triggered operations for asynchronous processing

## Project Architecture

This is a **serverless Azure Functions** application built with **TypeScript** and **Node.js**, designed for high scalability and cloud-native operations.

### Technology Stack

- **Runtime**: Node.js v20
- **Language**: TypeScript 5.3.3
- **Framework**: Azure Functions v4
- **Package Manager**: pnpm 8.9.2
- **Databases**:
  - MongoDB 4.2+ (Document storage)
  - PostgreSQL 14+ (Relational data)
  - Redis 7+ (Caching and session management)
- **Testing**: Jest with comprehensive unit and integration testing
- **Build Tools**: TypeScript compiler with tsc-alias for path mapping

### Core Dependencies

- **HTTP Client**: axios for external API communications
- **Date Handling**: date-fns for modern date operations
- **Database ORMs**:
  - Sequelize with sequelize-typescript for PostgreSQL
  - Mongoose for MongoDB
- **Validation**: Joi for request/response validation
- **Caching**: ioredis for Redis operations
- **Monitoring**: Application Insights for telemetry

## Environment Requirements

### Prerequisites

Before setting up the project, ensure you have the following installed:

1. **Azure Functions Core Tools v4** (v4.0.5382 or higher)
   - Download: https://github.com/Azure/azure-functions-core-tools

2. **Node.js v20**
   - Download: https://nodejs.org/en/

3. **pnpm 8.9.2**
   - Install: https://pnpm.io/installation

4. **MongoDB 4.2+**
   - Install: https://www.mongodb.com/docs/manual/installation/

5. **PostgreSQL 14+**
   - Install: https://www.postgresql.org/download/

6. **Redis 7+**
   - Install: https://redis.io/docs/latest/operate/oss_and_stack/install/install-redis/install-redis-on-mac-os/

## Getting Started

### 1. Clone the Repository

```bash
git clone <SSH_LINK> --recursive
```

### 2. Install Dependencies

```bash
pnpm install
```

### 3. Environment Configuration

Ensure your local environment variables are properly configured in `.env.json` and `local.settings.json` files.

### 4. Start Development Environment

The project requires multiple services to run simultaneously. Open **three separate terminal windows**:

**Terminal 1 - Storage Services:**
```bash
npm run start-storage-service
```

**Terminal 2 - Azure Functions:**
```bash
pnpm start
```

**Terminal 3 - TypeScript Watch Mode:**
```bash
pnpm watch
```

### 5. Verify Installation

Access the health check endpoint to verify the setup:
```
http://localhost:7712/MVNO/api/V100/healthcheck
```

## Project Structure

```
MVNO_CORE_API/
├── config/                     # Configuration files
├── dist/                       # Compiled JavaScript output
├── src/                        # Main source code
│   ├── appconfig/             # Azure App Configuration & environment variables
│   ├── constants/             # Application constants and enums
│   ├── core/                  # Core business logic (similar to Java package structure)
│   │   ├── common/           # Common utilities and shared components
│   │   ├── constant/         # Core constants
│   │   ├── dao/              # Data Access Objects
│   │   ├── dto/              # Data Transfer Objects
│   │   ├── entity/           # Entity definitions
│   │   ├── restclient/       # External API clients
│   │   └── service/          # Business service layer
│   ├── database/              # Database connection configurations
│   │   ├── mongo.ts          # MongoDB connection
│   │   ├── psql.ts           # PostgreSQL connection
│   │   └── redis.ts          # Redis connection
│   ├── functions/             # Azure Functions endpoints
│   │   ├── portal/           # Portal-specific functions
│   │   ├── triggers/         # Timer and queue triggered functions
│   │   └── *.ts              # HTTP triggered functions
│   ├── helpers/               # Helper utilities and base classes
│   ├── middleware/            # Request/response middleware
│   ├── models/                # Database models and schemas
│   ├── services/              # Application services
│   ├── types/                 # TypeScript type definitions
│   ├── utils/                 # Utility functions
│   └── index.ts              # Application entry point and endpoint registration
├── test/                      # Test files (mirrors src structure)
│   ├── functions/            # API-level integration tests
│   ├── testing/              # Test utilities and Azure Function test contexts
│   └── ...                   # Unit tests for each src component
├── guides/                    # Project documentation
├── host.json                  # Azure Functions host configuration
├── local.settings.json        # Local development settings
├── package.json              # Project dependencies and scripts
└── tsconfig.json             # TypeScript configuration
```

## Testing Strategy

The project implements a comprehensive two-tier testing approach:

### 1. Unit Tests (Function Level)
Located in `test/*` (excluding functions folder)

**Run all unit tests:**
```bash
pnpm run test:unit
```

**Run specific test file:**
```bash
pnpm run test:unit -- <filename>
# Example:
pnpm run test:unit -- Check.test.ts
```

### 2. Integration Tests (API Level)
Located in `test/functions/<function-name>.test.ts`

**Run all API tests:**
```bash
pnpm run test:functions
```

**Run specific API test:**
```bash
pnpm run test:functions -- <filename>
# Example:
pnpm run test:functions -- LinesDelHandler.test.ts
```

## API Endpoints

The API follows a RESTful design pattern with the base route prefix: `/MVNO/api/V100`

### Example API Call
```bash
curl -XPOST http://localhost:7713/MVNO/api/V100/portal/coreswimmylog/get_maintenance_mode \
  -H "X-role-id: 1" \
  -H "X-tenant-id: TST000" \
  -H "x-user-plus-id: ZZZ@test0" \
  -H "Content-Type: application/json" \
  -d '{}'
```

## Development Workflow

1. **Code Development**: Write TypeScript code in the `src/` directory
2. **Build**: Use `pnpm run build` to compile TypeScript to JavaScript
3. **Watch Mode**: Use `pnpm watch` for automatic recompilation during development
4. **Testing**: Write and run tests using Jest framework
5. **Linting**: Use `npm run lint:check` to ensure code quality

## Key Components

### Functions
- **Line Management**: Handle mobile line operations (add, delete, modify, suspend, enable)
- **Plan Operations**: Manage plan changes and acquisitions
- **Group Management**: Complete CRUD operations for line groups
- **Portal Functions**: User interface backend operations
- **Service Orders**: SO lifecycle management and processing

### Services
- **Core Swimmy Service**: Integration with external MVNO core systems
- **Notification Service**: Handle system notifications
- **Cache Service**: Redis-based caching for performance optimization
- **Storage Service**: Azure Table Storage operations

### Middleware
- **Base Handlers**: Common request/response processing
- **Validation**: Request validation using Joi schemas
- **Authentication**: Role-based access control
- **Error Handling**: Centralized error processing

This project serves as the backbone for MVNO operations, providing reliable, scalable, and maintainable API services for mobile network management.