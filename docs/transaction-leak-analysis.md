# Critical Transaction Leak Analysis - LinePlanAcquisitionServiceTx.ts

## 🚨 ROOT CAUSE IDENTIFIED: MASSIVE TRANSACTION LEAKS

### Problem Summary
The `CODE_060134` error with ~500 occurrences is caused by **multiple transaction leaks** in `LinePlanAcquisitionServiceTx.ts`. The service creates database transactions but exits the function via `return output;` statements **without committing or rolling back** the transactions.

### Critical Issues Found

#### 1. **Transaction Leak at Line 849** ✅ FIXED
```typescript
// BEFORE (LEAKED TRANSACTION):
return output;

// AFTER (FIXED):
return await this.cleanupTransactionAndReturn(tx, output);
```

#### 2. **Transaction Leak at Line 941** ✅ FIXED
```typescript
// BEFORE (LEAKED TRANSACTION):
return output;

// AFTER (FIXED):
return await this.cleanupTransactionAndReturn(tx, output);
```

#### 3. **Transaction Leak at Line 1075** ✅ FIXED
```typescript
// BEFORE (LEAKED TRANSACTION):
return output;

// AFTER (FIXED):
return await this.cleanupTransactionAndReturn(tx, output);
```

#### 4. **Transaction Leak at Line 1107** ✅ FIXED
```typescript
// BEFORE (LEAKED TRANSACTION):
return output;

// AFTER (FIXED):
return await this.cleanupTransactionAndReturn(tx, output);
```

#### 5. **Multiple Additional Transaction Leaks** ⚠️ NEEDS FIXING
Found **66+ more `return output;` statements** inside transaction blocks that need fixing:
- Lines 1206, 1263, 1303, 1362, 1409, 1437, 1483, 1522, 1595, 1634, 1664, 1713, 1750, 1789, 1820, 1850, 1899, 1934, 1983, 2029, 2103, 2171, 2200, 2251, 2303, 2350, 2396, 2425, 2478, 2517, 2551, 2586, 2697, 2729, 2765, 2889, 2948, 3029, 3060, 3106, 3146, 3178, 3221

### Impact Analysis

**Before Fix:**
- Each leaked transaction holds a database connection indefinitely
- With 500+ cases, this exhausts the connection pool (max 15 connections)
- New requests timeout waiting for available connections
- Results in `CODE_060134: All connections in the pool is concurrently used`

**After Fix:**
- Transactions are properly cleaned up before function returns
- Database connections are released back to the pool
- Connection pool exhaustion is prevented
- `CODE_060134` errors should be eliminated

### Solution Implemented

#### 1. **Helper Function Added**
```typescript
private async cleanupTransactionAndReturn(
    tx: Transaction | null,
    output: LinePlanAcquisitionOutputDto,
): Promise<LinePlanAcquisitionOutputDto> {
    if (tx) {
        try {
            await tx.rollback();
            this.context.log("LinePlanAcquisitionServiceTx: Transaction rolled back before return");
        } catch (e) {
            this.context.warn("LinePlanAcquisitionServiceTx: Error during transaction rollback", e);
        }
    }
    return output;
}
```

#### 2. **Transaction Cleanup Pattern**
Replace all instances of:
```typescript
return output;  // INSIDE TRANSACTION BLOCK
```

With:
```typescript
return await this.cleanupTransactionAndReturn(tx, output);
```

### Next Steps Required

1. **Complete the fixes** for all remaining transaction leaks (69+ locations)
2. **Test the changes** to verify `CODE_060134` errors are eliminated
3. **Monitor connection pool usage** via the monitoring endpoint
4. **Add transaction timeout** as additional safety measure

### Monitoring

Use the connection pool monitoring endpoint to track improvements:
```
GET /portal/db/connection-pool-status
```

Expected results after fix:
- `using` count should stay low (< 10)
- `waiting` count should be 0
- No more `CODE_060134` errors in logs

### Risk Assessment

**High Priority Fix Required:**
- This is a **critical production issue** causing service unavailability
- Each transaction leak permanently consumes a database connection
- With only 15 max connections, the service becomes unusable quickly
- Fix must be deployed immediately to restore service stability
