# Critical Transaction Leak Analysis - LinePlanAcquisitionServiceTx.ts

## 🚨 ROOT CAUSE IDENTIFIED: MASSIVE TRANSACTION LEAKS

### Problem Summary
The `CODE_060134` error with ~500 occurrences is caused by **multiple transaction leaks** in `LinePlanAcquisitionServiceTx.ts`. The service creates database transactions but exits the function via `return output;` statements **without committing or rolling back** the transactions.

### Critical Issues Found

#### 1. **Transaction Leak at Line 852** ✅ FIXED
```typescript
// BEFORE (LEAKED TRANSACTION):
return output;

// AFTER (FIXED):
return await this.cleanupTransactionAndReturn(tx, output);
```

#### 2. **Transaction Leak at Line 944** ✅ FIXED  
```typescript
// BEFORE (LEAKED TRANSACTION):
return output;

// AFTER (FIXED):
return await this.cleanupTransactionAndReturn(tx, output);
```

#### 3. **Multiple Additional Transaction Leaks** ⚠️ NEEDS FIXING
Found **69+ more `return output;` statements** inside transaction blocks that need fixing:
- Lines 1074, 1112, 1242, 1299, 1339, 1398, 1447, 1475, 1521, 1560, 1633, 1672, 1702, 1751, 1788, 1827, 1858, 1888, 1937, 1972, 2021, 2067, 2141, 2209, 2238, 2289, 2345, 2392, 2438, 2467, 2521, 2560, 2594, 2629, 2740, 2772, 2808, 2932, 2991, 3072, 3103, 3150, 3190, 3222, 3265

### Impact Analysis

**Before Fix:**
- Each leaked transaction holds a database connection indefinitely
- With 500+ cases, this exhausts the connection pool (max 15 connections)
- New requests timeout waiting for available connections
- Results in `CODE_060134: All connections in the pool is concurrently used`

**After Fix:**
- Transactions are properly cleaned up before function returns
- Database connections are released back to the pool
- Connection pool exhaustion is prevented
- `CODE_060134` errors should be eliminated

### Solution Implemented

#### 1. **Helper Function Added**
```typescript
private async cleanupTransactionAndReturn(
    tx: Transaction | null,
    output: LinePlanAcquisitionOutputDto,
): Promise<LinePlanAcquisitionOutputDto> {
    if (tx) {
        try {
            await tx.rollback();
            this.context.log("LinePlanAcquisitionServiceTx: Transaction rolled back before return");
        } catch (e) {
            this.context.warn("LinePlanAcquisitionServiceTx: Error during transaction rollback", e);
        }
    }
    return output;
}
```

#### 2. **Transaction Cleanup Pattern**
Replace all instances of:
```typescript
return output;  // INSIDE TRANSACTION BLOCK
```

With:
```typescript
return await this.cleanupTransactionAndReturn(tx, output);
```

### Next Steps Required

1. **Complete the fixes** for all remaining transaction leaks (69+ locations)
2. **Test the changes** to verify `CODE_060134` errors are eliminated
3. **Monitor connection pool usage** via the monitoring endpoint
4. **Add transaction timeout** as additional safety measure

### Monitoring

Use the connection pool monitoring endpoint to track improvements:
```
GET /portal/db/connection-pool-status
```

Expected results after fix:
- `using` count should stay low (< 10)
- `waiting` count should be 0
- No more `CODE_060134` errors in logs

### Risk Assessment

**High Priority Fix Required:**
- This is a **critical production issue** causing service unavailability
- Each transaction leak permanently consumes a database connection
- With only 15 max connections, the service becomes unusable quickly
- Fix must be deployed immediately to restore service stability
