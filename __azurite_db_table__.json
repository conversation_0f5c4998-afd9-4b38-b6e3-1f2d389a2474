{"filename": "/Users/<USER>/dev/mvno_azure/MVNO_CORE_API/__azurite_db_table__.json", "collections": [{"name": "$TABLES_COLLECTION$", "data": [{"account": "devstoreaccount1", "table": "ExecReservedSOPid", "meta": {"revision": 0, "created": *************, "version": 0}, "$loki": 1}, {"account": "devstoreaccount1", "table": "ReservedSO", "meta": {"revision": 0, "created": *************, "version": 0}, "$loki": 2}, {"account": "devstoreaccount1", "table": "AzureFunctionsDiagnosticEvents202509", "meta": {"revision": 0, "created": *************, "version": 0}, "$loki": 3}, {"account": "devstoreaccount1", "table": "AutoLineGroupModbucketPid", "meta": {"revision": 0, "created": *************, "version": 0}, "$loki": 4}, {"account": "devstoreaccount1", "table": "MntScriptPid", "meta": {"revision": 0, "created": *************, "version": 0}, "$loki": 5}, {"account": "devstoreaccount1", "table": "AzureFunctionsDiagnosticEvents202510", "meta": {"revision": 0, "created": *************, "version": 0}, "$loki": 6}], "idIndex": null, "binaryIndices": {"account": {"name": "account", "dirty": false, "values": [5, 4, 3, 2, 1, 0]}, "table": {"name": "table", "dirty": false, "values": [3, 2, 5, 0, 4, 1]}}, "constraints": null, "uniqueNames": [], "transforms": {}, "objType": "$TABLES_COLLECTION$", "dirty": false, "cachedIndex": null, "cachedBinaryIndex": null, "cachedData": null, "adaptiveBinaryIndices": true, "transactional": false, "cloneObjects": false, "cloneMethod": "parse-stringify", "asyncListeners": false, "disableMeta": false, "disableChangesApi": true, "disableDeltaChangesApi": true, "autoupdate": false, "serializableIndices": true, "disableFreeze": true, "ttl": null, "maxId": 6, "DynamicViews": [], "events": {"insert": [], "update": [], "pre-insert": [], "pre-update": [], "close": [], "flushbuffer": [], "error": [], "delete": [null], "warning": [null]}, "changes": [], "dirtyIds": []}, {"name": "$SERVICES_COLLECTION$", "data": [], "idIndex": null, "binaryIndices": {}, "constraints": null, "uniqueNames": ["accountName"], "transforms": {}, "objType": "$SERVICES_COLLECTION$", "dirty": false, "cachedIndex": null, "cachedBinaryIndex": null, "cachedData": null, "adaptiveBinaryIndices": true, "transactional": false, "cloneObjects": false, "cloneMethod": "parse-stringify", "asyncListeners": false, "disableMeta": false, "disableChangesApi": true, "disableDeltaChangesApi": true, "autoupdate": false, "serializableIndices": true, "disableFreeze": true, "ttl": null, "maxId": 0, "DynamicViews": [], "events": {"insert": [], "update": [], "pre-insert": [], "pre-update": [], "close": [], "flushbuffer": [], "error": [], "delete": [null], "warning": [null]}, "changes": [], "dirtyIds": []}, {"name": "devstoreaccount1$ExecReservedSOPid", "data": [], "idIndex": null, "binaryIndices": {"PartitionKey": {"name": "PartitionKey", "dirty": false, "values": []}, "RowKey": {"name": "<PERSON><PERSON><PERSON>", "dirty": false, "values": []}}, "constraints": null, "uniqueNames": [], "transforms": {}, "objType": "devstoreaccount1$ExecReservedSOPid", "dirty": false, "cachedIndex": null, "cachedBinaryIndex": null, "cachedData": null, "adaptiveBinaryIndices": true, "transactional": false, "cloneObjects": false, "cloneMethod": "parse-stringify", "asyncListeners": false, "disableMeta": false, "disableChangesApi": true, "disableDeltaChangesApi": true, "autoupdate": false, "serializableIndices": true, "disableFreeze": true, "ttl": null, "maxId": 167, "DynamicViews": [], "events": {"insert": [], "update": [], "pre-insert": [], "pre-update": [], "close": [], "flushbuffer": [], "error": [], "delete": [null], "warning": [null]}, "changes": [], "dirtyIds": []}, {"name": "devstoreaccount1$ReservedSO", "data": [{"PartitionKey": "orders", "RowKey": "***************", "properties": {"PartitionKey": "orders", "RowKey": "***************", "status": "pending", "Timestamp": "2025-09-17T06:15:00.4874185Z", "<EMAIL>": "Edm.DateTime"}, "lastModifiedTime": "2025-09-17T06:15:00.4874185Z", "eTag": "W/\"datetime'2025-09-17T06%3A15%3A00.4874185Z'\"", "meta": {"revision": 0, "created": *************, "version": 0}, "$loki": 1}, {"PartitionKey": "orders", "RowKey": "***************", "properties": {"PartitionKey": "orders", "RowKey": "***************", "status": "pending", "Timestamp": "2025-09-17T06:15:00.7496805Z", "<EMAIL>": "Edm.DateTime"}, "lastModifiedTime": "2025-09-17T06:15:00.7496805Z", "eTag": "W/\"datetime'2025-09-17T06%3A15%3A00.7496805Z'\"", "meta": {"revision": 0, "created": *************, "version": 0}, "$loki": 2}, {"PartitionKey": "orders", "RowKey": "***************", "properties": {"PartitionKey": "orders", "RowKey": "***************", "status": "pending", "Timestamp": "2025-09-17T06:15:00.9859173Z", "<EMAIL>": "Edm.DateTime"}, "lastModifiedTime": "2025-09-17T06:15:00.9859173Z", "eTag": "W/\"datetime'2025-09-17T06%3A15%3A00.9859173Z'\"", "meta": {"revision": 0, "created": 1758089700986, "version": 0}, "$loki": 3}, {"PartitionKey": "orders", "RowKey": "AP0900860161224", "properties": {"PartitionKey": "orders", "RowKey": "AP0900860161224", "status": "pending", "Timestamp": "2025-09-17T06:15:01.2091417Z", "<EMAIL>": "Edm.DateTime"}, "lastModifiedTime": "2025-09-17T06:15:01.2091417Z", "eTag": "W/\"datetime'2025-09-17T06%3A15%3A01.2091417Z'\"", "meta": {"revision": 0, "created": 1758089701210, "version": 0}, "$loki": 4}, {"PartitionKey": "orders", "RowKey": "AP0900646627740", "properties": {"PartitionKey": "orders", "RowKey": "AP0900646627740", "status": "pending", "Timestamp": "2025-09-17T06:15:01.4313634Z", "<EMAIL>": "Edm.DateTime"}, "lastModifiedTime": "2025-09-17T06:15:01.4313634Z", "eTag": "W/\"datetime'2025-09-17T06%3A15%3A01.4313634Z'\"", "meta": {"revision": 0, "created": 1758089701432, "version": 0}, "$loki": 5}, {"PartitionKey": "orders", "RowKey": "AP0900897628521", "properties": {"PartitionKey": "orders", "RowKey": "AP0900897628521", "status": "pending", "Timestamp": "2025-09-17T06:15:01.6906229Z", "<EMAIL>": "Edm.DateTime"}, "lastModifiedTime": "2025-09-17T06:15:01.6906229Z", "eTag": "W/\"datetime'2025-09-17T06%3A15%3A01.6906229Z'\"", "meta": {"revision": 0, "created": 1758089701691, "version": 0}, "$loki": 6}, {"PartitionKey": "orders", "RowKey": "AP0900486178690", "properties": {"PartitionKey": "orders", "RowKey": "AP0900486178690", "status": "pending", "Timestamp": "2025-09-17T06:15:01.9298609Z", "<EMAIL>": "Edm.DateTime"}, "lastModifiedTime": "2025-09-17T06:15:01.9298609Z", "eTag": "W/\"datetime'2025-09-17T06%3A15%3A01.9298609Z'\"", "meta": {"revision": 0, "created": 1758089701929, "version": 0}, "$loki": 7}, {"PartitionKey": "orders", "RowKey": "AP0900117471599", "properties": {"PartitionKey": "orders", "RowKey": "AP0900117471599", "status": "pending", "Timestamp": "2025-09-17T06:15:02.1548603Z", "<EMAIL>": "Edm.DateTime"}, "lastModifiedTime": "2025-09-17T06:15:02.1548603Z", "eTag": "W/\"datetime'2025-09-17T06%3A15%3A02.1548603Z'\"", "meta": {"revision": 0, "created": 1758089702154, "version": 0}, "$loki": 8}, {"PartitionKey": "orders", "RowKey": "AP0900166636484", "properties": {"PartitionKey": "orders", "RowKey": "AP0900166636484", "status": "pending", "Timestamp": "2025-09-17T06:15:02.3953269Z", "<EMAIL>": "Edm.DateTime"}, "lastModifiedTime": "2025-09-17T06:15:02.3953269Z", "eTag": "W/\"datetime'2025-09-17T06%3A15%3A02.3953269Z'\"", "meta": {"revision": 0, "created": 1758089702395, "version": 0}, "$loki": 9}, {"PartitionKey": "orders", "RowKey": "AP0900125034214", "properties": {"PartitionKey": "orders", "RowKey": "AP0900125034214", "status": "pending", "Timestamp": "2025-09-17T06:15:02.6595913Z", "<EMAIL>": "Edm.DateTime"}, "lastModifiedTime": "2025-09-17T06:15:02.6595913Z", "eTag": "W/\"datetime'2025-09-17T06%3A15%3A02.6595913Z'\"", "meta": {"revision": 0, "created": 1758089702660, "version": 0}, "$loki": 10}, {"PartitionKey": "orders", "RowKey": "AP0900298181810", "properties": {"PartitionKey": "orders", "RowKey": "AP0900298181810", "status": "pending", "Timestamp": "2025-09-17T06:15:02.9158468Z", "<EMAIL>": "Edm.DateTime"}, "lastModifiedTime": "2025-09-17T06:15:02.9158468Z", "eTag": "W/\"datetime'2025-09-17T06%3A15%3A02.9158468Z'\"", "meta": {"revision": 0, "created": 1758089702915, "version": 0}, "$loki": 11}, {"PartitionKey": "orders", "RowKey": "AP0900619157916", "properties": {"PartitionKey": "orders", "RowKey": "AP0900619157916", "status": "pending", "Timestamp": "2025-09-17T06:15:03.1599178Z", "<EMAIL>": "Edm.DateTime"}, "lastModifiedTime": "2025-09-17T06:15:03.1599178Z", "eTag": "W/\"datetime'2025-09-17T06%3A15%3A03.1599178Z'\"", "meta": {"revision": 0, "created": 1758089703160, "version": 0}, "$loki": 12}, {"PartitionKey": "orders", "RowKey": "AP0900403682488", "properties": {"PartitionKey": "orders", "RowKey": "AP0900403682488", "status": "pending", "Timestamp": "2025-09-17T06:15:03.4033357Z", "<EMAIL>": "Edm.DateTime"}, "lastModifiedTime": "2025-09-17T06:15:03.4033357Z", "eTag": "W/\"datetime'2025-09-17T06%3A15%3A03.4033357Z'\"", "meta": {"revision": 0, "created": 1758089703404, "version": 0}, "$loki": 13}, {"PartitionKey": "orders", "RowKey": "AP0900364110699", "properties": {"PartitionKey": "orders", "RowKey": "AP0900364110699", "status": "pending", "Timestamp": "2025-09-17T06:15:03.6455772Z", "<EMAIL>": "Edm.DateTime"}, "lastModifiedTime": "2025-09-17T06:15:03.6455772Z", "eTag": "W/\"datetime'2025-09-17T06%3A15%3A03.6455772Z'\"", "meta": {"revision": 0, "created": 1758089703646, "version": 0}, "$loki": 14}, {"PartitionKey": "orders", "RowKey": "AP0900313893762", "properties": {"PartitionKey": "orders", "RowKey": "AP0900313893762", "status": "pending", "Timestamp": "2025-09-17T06:15:03.8928244Z", "<EMAIL>": "Edm.DateTime"}, "lastModifiedTime": "2025-09-17T06:15:03.8928244Z", "eTag": "W/\"datetime'2025-09-17T06%3A15%3A03.8928244Z'\"", "meta": {"revision": 0, "created": 1758089703893, "version": 0}, "$loki": 15}, {"PartitionKey": "orders", "RowKey": "AP0900531960938", "properties": {"PartitionKey": "orders", "RowKey": "AP0900531960938", "status": "pending", "Timestamp": "2025-09-17T06:15:04.1225379Z", "<EMAIL>": "Edm.DateTime"}, "lastModifiedTime": "2025-09-17T06:15:04.1225379Z", "eTag": "W/\"datetime'2025-09-17T06%3A15%3A04.1225379Z'\"", "meta": {"revision": 0, "created": 1758089704122, "version": 0}, "$loki": 16}, {"PartitionKey": "orders", "RowKey": "AP0900871827259", "properties": {"PartitionKey": "orders", "RowKey": "AP0900871827259", "status": "pending", "Timestamp": "2025-09-17T06:15:04.3552873Z", "<EMAIL>": "Edm.DateTime"}, "lastModifiedTime": "2025-09-17T06:15:04.3552873Z", "eTag": "W/\"datetime'2025-09-17T06%3A15%3A04.3552873Z'\"", "meta": {"revision": 0, "created": 1758089704356, "version": 0}, "$loki": 17}, {"PartitionKey": "orders", "RowKey": "AP0900350595452", "properties": {"PartitionKey": "orders", "RowKey": "AP0900350595452", "status": "pending", "Timestamp": "2025-09-17T06:15:04.6295614Z", "<EMAIL>": "Edm.DateTime"}, "lastModifiedTime": "2025-09-17T06:15:04.6295614Z", "eTag": "W/\"datetime'2025-09-17T06%3A15%3A04.6295614Z'\"", "meta": {"revision": 0, "created": *************, "version": 0}, "$loki": 18}], "idIndex": null, "binaryIndices": {"PartitionKey": {"name": "PartitionKey", "dirty": false, "values": [17, 16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 0]}, "RowKey": {"name": "<PERSON><PERSON><PERSON>", "dirty": false, "values": [7, 9, 1, 8, 0, 10, 14, 17, 13, 12, 6, 2, 15, 11, 4, 3, 16, 5]}}, "constraints": null, "uniqueNames": [], "transforms": {}, "objType": "devstoreaccount1$ReservedSO", "dirty": false, "cachedIndex": null, "cachedBinaryIndex": null, "cachedData": null, "adaptiveBinaryIndices": true, "transactional": false, "cloneObjects": false, "cloneMethod": "parse-stringify", "asyncListeners": false, "disableMeta": false, "disableChangesApi": true, "disableDeltaChangesApi": true, "autoupdate": false, "serializableIndices": true, "disableFreeze": true, "ttl": null, "maxId": 18, "DynamicViews": [], "events": {"insert": [], "update": [], "pre-insert": [], "pre-update": [], "close": [], "flushbuffer": [], "error": [], "delete": [null], "warning": [null]}, "changes": [], "dirtyIds": []}, {"name": "devstoreaccount1$AzureFunctionsDiagnosticEvents202509", "data": [], "idIndex": null, "binaryIndices": {"PartitionKey": {"name": "PartitionKey", "dirty": false, "values": []}, "RowKey": {"name": "<PERSON><PERSON><PERSON>", "dirty": false, "values": []}}, "constraints": null, "uniqueNames": [], "transforms": {}, "objType": "devstoreaccount1$AzureFunctionsDiagnosticEvents202509", "dirty": false, "cachedIndex": null, "cachedBinaryIndex": null, "cachedData": null, "adaptiveBinaryIndices": true, "transactional": false, "cloneObjects": false, "cloneMethod": "parse-stringify", "asyncListeners": false, "disableMeta": false, "disableChangesApi": true, "disableDeltaChangesApi": true, "autoupdate": false, "serializableIndices": true, "disableFreeze": true, "ttl": null, "maxId": 0, "DynamicViews": [], "events": {"insert": [], "update": [], "pre-insert": [], "pre-update": [], "close": [], "flushbuffer": [], "error": [], "delete": [null], "warning": [null]}, "changes": [], "dirtyIds": []}, {"name": "devstoreaccount1$AutoLineGroupModbucketPid", "data": [], "idIndex": null, "binaryIndices": {"PartitionKey": {"name": "PartitionKey", "dirty": false, "values": []}, "RowKey": {"name": "<PERSON><PERSON><PERSON>", "dirty": false, "values": []}}, "constraints": null, "uniqueNames": [], "transforms": {}, "objType": "devstoreaccount1$AutoLineGroupModbucketPid", "dirty": false, "cachedIndex": null, "cachedBinaryIndex": null, "cachedData": null, "adaptiveBinaryIndices": true, "transactional": false, "cloneObjects": false, "cloneMethod": "parse-stringify", "asyncListeners": false, "disableMeta": false, "disableChangesApi": true, "disableDeltaChangesApi": true, "autoupdate": false, "serializableIndices": true, "disableFreeze": true, "ttl": null, "maxId": 4, "DynamicViews": [], "events": {"insert": [], "update": [], "pre-insert": [], "pre-update": [], "close": [], "flushbuffer": [], "error": [], "delete": [null], "warning": [null]}, "changes": [], "dirtyIds": []}, {"name": "devstoreaccount1$MntScriptPid", "data": [], "idIndex": null, "binaryIndices": {"PartitionKey": {"name": "PartitionKey", "dirty": false, "values": []}, "RowKey": {"name": "<PERSON><PERSON><PERSON>", "dirty": false, "values": []}}, "constraints": null, "uniqueNames": [], "transforms": {}, "objType": "devstoreaccount1$MntScriptPid", "dirty": false, "cachedIndex": null, "cachedBinaryIndex": null, "cachedData": null, "adaptiveBinaryIndices": true, "transactional": false, "cloneObjects": false, "cloneMethod": "parse-stringify", "asyncListeners": false, "disableMeta": false, "disableChangesApi": true, "disableDeltaChangesApi": true, "autoupdate": false, "serializableIndices": true, "disableFreeze": true, "ttl": null, "maxId": 4, "DynamicViews": [], "events": {"insert": [], "update": [], "pre-insert": [], "pre-update": [], "close": [], "flushbuffer": [], "error": [], "delete": [null], "warning": [null]}, "changes": [], "dirtyIds": []}, {"name": "devstoreaccount1$AzureFunctionsDiagnosticEvents202510", "data": [], "idIndex": null, "binaryIndices": {"PartitionKey": {"name": "PartitionKey", "dirty": false, "values": []}, "RowKey": {"name": "<PERSON><PERSON><PERSON>", "dirty": false, "values": []}}, "constraints": null, "uniqueNames": [], "transforms": {}, "objType": "devstoreaccount1$AzureFunctionsDiagnosticEvents202510", "dirty": false, "cachedIndex": null, "cachedBinaryIndex": null, "cachedData": null, "adaptiveBinaryIndices": true, "transactional": false, "cloneObjects": false, "cloneMethod": "parse-stringify", "asyncListeners": false, "disableMeta": false, "disableChangesApi": true, "disableDeltaChangesApi": true, "autoupdate": false, "serializableIndices": true, "disableFreeze": true, "ttl": null, "maxId": 0, "DynamicViews": [], "events": {"insert": [], "update": [], "pre-insert": [], "pre-update": [], "close": [], "flushbuffer": [], "error": [], "delete": [null], "warning": [null]}, "changes": [], "dirtyIds": []}], "databaseVersion": 1.5, "engineVersion": 1.5, "autosave": true, "autosaveInterval": 5000, "autosaveHandle": null, "throttledSaves": true, "options": {"persistenceMethod": "fs", "autosave": true, "autosaveInterval": 5000, "serializationMethod": "normal", "destructureDelimiter": "$<\n"}, "persistenceMethod": "fs", "persistenceAdapter": null, "verbose": false, "events": {"init": [null], "loaded": [], "flushChanges": [], "close": [], "changes": [], "warning": []}, "ENV": "NODEJS"}