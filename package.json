{"name": "mvno_core_api", "version": "1.0.0", "description": "", "scripts": {"build": "tsc && tsc-alias && npm run build:post", "watch": "tsc && (concurrently \"tsc -w\" \"tsc-alias -w\")", "clean": "<PERSON><PERSON><PERSON> dist", "prestart": "npm run clean && npm run build", "start": "NODE_CONFIG_DIR=./dist/config func start", "test:src": "env-cmd --file .env.json jest -i --testPathIgnorePatterns=dist/", "test:dist": "env-cmd --file .env.json jest -i  --modulePathIgnorePatterns=\"<rootDir>/test\"", "test:functions": "env-cmd --file .env.json jest -i --testPathIgnorePatterns=\"test/(?!functions)\"", "test:functions:src": "env-cmd --file .env.json jest -i --testPathIgnorePatterns=dist/ --testPathIgnorePatterns=\"test/(?!functions)\"", "test:functions:dist": "env-cmd --file .env.json jest -i  --modulePathIgnorePatterns=\"<rootDir>/test\" --testPathIgnorePatterns=\"test/(?!functions)\"", "test:unit": "env-cmd --file .env.json jest -i --testPathIgnorePatterns=test/functions", "test:unit:src": "env-cmd --file .env.json jest -i --testPathIgnorePatterns=test/functions --testPathIgnorePatterns=dist/", "test:unit:dist": "env-cmd --file .env.json jest -i --testPathIgnorePatterns=test/functions  --modulePathIgnorePatterns=\"<rootDir>/test\"", "lint:check": "tslint --project tsconfig.json -c tslint.json 'src/**/*.ts'", "build:post": "npm run build:post:replace", "build:post:replace": "replace \"exports\\.default\" \"module.exports\" ./dist/config/*.js", "start-storage-service": "azurite -l __azurite__ --skipApiVersionCheck"}, "dependencies": {"@azure/app-configuration": "^1.6.0", "@azure/data-tables": "^13.3.0", "@azure/functions": "^4.4.0", "@azure/service-bus": "^7.9.4", "@types/mongoose": "^5.11.97", "applicationinsights": "^2.9.5", "axios": "^1.7.2", "bignumber.js": "^9.1.2", "concurrently": "^8.2.2", "config": "^3.3.12", "date-fns": "^3.6.0", "ioredis": "^5.4.1", "joi": "^17.13.1", "js-md2": "^0.2.2", "mongoose": "^8.3.4", "pg": "^8.11.5", "pg-hstore": "^2.3.4", "reflect-metadata": "^0.2.2", "sequelize": "^6.37.2", "sequelize-typescript": "^2.1.6", "tslint": "^6.1.3", "xmlbuilder2": "^3.1.1", "xmldom": "^0.6.0", "xpath": "^0.0.34"}, "devDependencies": {"@jest/globals": "^29.7.0", "@types/config": "^3.3.5", "@types/ioredis-mock": "^8.2.5", "@types/jest": "^29.5.12", "@types/node": "^18.19.30", "@types/validator": "^13.11.9", "@types/xmldom": "^0.1.34", "axios-mock-adapter": "^1.22.0", "env-cmd": "^10.1.0", "ioredis-mock": "^8.9.0", "jest": "^29.7.0", "nock": "^13.5.4", "replace": "^1.2.2", "rimraf": "^5.0.5", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "tsc-alias": "^1.8.8", "tsconfig-paths": "^4.2.0", "typescript": "^5.3.3"}, "main": "dist/src/*.js"}