{"LOCAL_SETTINGS_CONFIG": true, "POSTGRE_CONNECTION_STRING": "postgresql://localhost:5432/mawp", "DATABASE_MONGODB_CONNECTION_STRING": "mongodb://localhost:27017/", "SKIP_DB_TEST": false, "SERVICEBUS_CONNECTION_STRING": "Endpoint=sb://mvno-dev.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=Aw3nUy/qvlVsP2Enpe34b09VGnsKehXmm+ASbAuWKLE=", "SERVICEBUS_QUEUE_CORE_SWIMMY_API_REQUESTS": "core-swimmy-api-requests--berna", "SERVICEBUS_QUEUE_NOTIFICATION": "mvno-notifications--dev", "STORAGE_CONNECTION_STRING": "UseDevelopmentStorage=true", "DATABASE_CORE_POSTGRESQL_CONNECTION_STRING": "postgresql://localhost:5432/mawp", "CORE_SWIMMY_API_ENDPOINT_ORIGIN": "http://localhost:9010", "CORE_SWIMMY_API_ENDPOINT_PATH": "/v1/order-management/order/entry", "CORE_SWIMMY_API_MOCK_MODE": true, "CORE_TPC_API_MOCK_MODE": true, "CORE_MVNO_ReservationDateExecutionUnits": "15", "CORE_MVNO_ReservationsLimitDays": "60", "CORE_MVNO_ServerConnectionLimit": "120", "CORE_MVNO_TpcSourceIpAddress": "*************", "CORE_MVNO_TpcDestIpAddress": "**************", "CORE_MVNO_TpcDestIpAddress2": "**************", "CORE_MVNO_TpcDestIpAddress3": "**************", "CORE_MVNO_TpcDestServiceName": "http://${IpAddr}/services/serviceProfileOperation", "CORE_MVNO_TpcLiteRequestURI": "http://${IpAddr}/services/serviceProfileOperation", "CORE_MVNO_PreLineAddNgTime": "0:00-4:00", "CORE_MVNO_GroupPlanChangeNgTime": "0:00-4:00", "CORE_MVNO_PlanChangeNgTime": "0:00-4:00", "V1_HAS_STOPPED_RUNNING": true, "RESERVED_SO_API_ENDPOINT": "http://localhost:7713", "FRONT_SERVICE_EXTERNAL_API": "http://localhost:8000", "FRONT_SERVICE_MOCK_MODE": "true"}