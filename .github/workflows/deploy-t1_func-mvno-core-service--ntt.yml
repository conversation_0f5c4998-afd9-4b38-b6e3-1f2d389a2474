name: Deploy Node.js project to Azure Function App

on:
    push:
        branches:
            - deploy-t1

# CONFIGURATION
# For help, go to https://github.com/Azure/Actions
#
# 1. Set up the following secrets in your repository:
#   AZURE_FUNCTIONAPP_PUBLISH_PROFILE_90c64500f14e4eb4a716af99399c32e2
#
# 2. Change these variables for your configuration:
env:
    AZURE_FUNCTIONAPP_NAME: 'dev-mvnopc-func-core-api' # set this to your function app name on Azure
    AZURE_FUNCTIONAPP_PACKAGE_PATH: '.' # set this to the path to your function app project, defaults to the repository root
    NODE_VERSION: '20.x' # set this to the node version to use (e.g. '8.x', '10.x', '12.x')

jobs:
    build-and-deploy:
        runs-on: ubuntu-latest
        environment: dev
        steps:
            - name: 'Checkout GitHub Action'
              uses: actions/checkout@v2

            - name: Setup Node ${{ env.NODE_VERSION }} Environment
              uses: actions/setup-node@v3
              with:
                  node-version: ${{ env.NODE_VERSION }}

            - name: 'Resolve Project Dependencies Using Npm'
              shell: bash
              env:
                  TZ: 'Asia/Tokyo'
              run: |
                  pushd './${{ env.AZURE_FUNCTIONAPP_PACKAGE_PATH }}'
                  echo $GITHUB_SHA > __version__
                  echo $(git ls-tree HEAD __shared__ | awk '{print $3}') > __version_shared__
                  echo $(date +'%Y.%m.%dT%H.%M.%S') > __build_date__
                  npm install
                  npm run build --if-present

            - name: 'Run Azure Functions Action'
              uses: Azure/functions-action@v1
              id: fa
              with:
                  app-name: ${{ env.AZURE_FUNCTIONAPP_NAME }}
                  package: ${{ env.AZURE_FUNCTIONAPP_PACKAGE_PATH }}
                  publish-profile: ${{ secrets.AZURE_FUNCTIONAPP_PUBLISH_PROFILE }}
# For more samples to get started with GitHub Action workflows to deploy to Azure, refer to https://github.com/Azure/actions-workflow-samples
