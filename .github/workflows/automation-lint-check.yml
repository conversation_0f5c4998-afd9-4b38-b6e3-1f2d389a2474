# This workflow will do a clean installation of node dependencies, cache/restore them, build the source code and run tests across different versions of node
# For more information see: https://help.github.com/actions/language-and-framework-guides/using-nodejs-with-github-actions

name: Automation run lint check

on:
    push:
        branches: ["develop"]
    pull_request:
        branches: ["develop"]

jobs:
    build:
        runs-on: ubuntu-latest

        strategy:
            matrix:
                node-version: [20.x]
                # See supported Node.js release schedule at https://nodejs.org/en/about/releases/

        steps:
            - uses: actions/checkout@v3
            - name: Use Node ${{ matrix.node-version }}
              uses: actions/setup-node@v3
              with:
                node-version: ${{ matrix.node-version }}
            - uses: pnpm/action-setup@v4
              name: Install pnpm
              with:
                version: 8
                run_install: false
            - name: Get pnpm store directory
              shell: bash
              run: |
                echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV
            - uses: actions/cache@v3
              name: Setup pnpm cache
              with:
                path: ${{ env.STORE_PATH }}
                key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
                restore-keys: |
                  ${{ runner.os }}-pnpm-store-
            - run: pnpm install
            - run: pnpm build
            - run: pnpm run lint:check
