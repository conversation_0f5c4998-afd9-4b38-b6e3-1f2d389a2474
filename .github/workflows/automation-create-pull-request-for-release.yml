name: Create a pull request for release production.

on:
    push:
        branches: [main]

jobs:
    create-pr-pnl:
        strategy:
            matrix:
                branch:
                    [
                        'deploy-staging',
                    ]
        runs-on: ubuntu-latest
        env:
            GH_TOKEN: ${{ secrets.PAT }}
        steps:
            - name: 'Checkout GitHub Action'
              uses: actions/checkout@v2
              with:
                  token: ${{ secrets.PAT }}
                  submodules: recursive
            - name: Check if PR exists
              id: checkPNL
              env:
                  GITHUB_TOKEN: ${{ secrets.PAT }}
              run: |
                  prs=$(gh pr list \
                      --repo "$GITHUB_REPOSITORY" \
                      --json baseRefName,headRefName \
                      --jq '
                          map(select(.baseRefName ==  "${{ matrix.branch }}" and .headRefName == "main"))
                          | length
                      ')
                  if ((prs > 0)); then
                      echo "skip=true" >> "$GITHUB_OUTPUT"
                  fi
            - name: Create pull request for deploy
              if: '!steps.checkPNL.outputs.skip'
              run: |
                  gh pr create -B  "${{ matrix.branch }}" -t "[${{ matrix.branch }}] Build and release to Azure Production" -b "automated pull request for deploy to Azure Production" -a "@me"