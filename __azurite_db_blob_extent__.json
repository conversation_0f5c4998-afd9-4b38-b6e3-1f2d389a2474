{"filename": "/Users/<USER>/dev/mvno_azure/MVNO_CORE_API/__azurite_db_blob_extent__.json", "collections": [{"name": "$EXTENTS_COLLECTION$", "data": [{"id": "686d4526-e0c6-48e6-8afe-c30d680ff8c6", "locationId": "<PERSON><PERSON><PERSON>", "path": "686d4526-e0c6-48e6-8afe-c30d680ff8c6", "size": 1601, "lastModifiedInMS": 1758089207942, "meta": {"revision": 14, "created": 1758089207943, "version": 0, "updated": 1758099600089}, "$loki": 1, "LastModifyInMS": 1758099600089}, {"id": "87696dfc-a140-4915-bbaf-66d255f7727f", "locationId": "<PERSON><PERSON><PERSON>", "path": "87696dfc-a140-4915-bbaf-66d255f7727f", "size": 120, "lastModifiedInMS": 1758089207946, "meta": {"revision": 1, "created": 1758089207946, "version": 0, "updated": 1758089207983}, "$loki": 2, "LastModifyInMS": 1758089207983}, {"id": "03bc8280-a763-4362-b82f-99a2d3b73e25", "locationId": "<PERSON><PERSON><PERSON>", "path": "03bc8280-a763-4362-b82f-99a2d3b73e25", "size": 120, "lastModifiedInMS": 1758089207947, "meta": {"revision": 1, "created": 1758089207947, "version": 0, "updated": 1758089207984}, "$loki": 3, "LastModifyInMS": 1758089207984}, {"id": "b628ed7f-4bdf-4fb1-b67b-043667d5fd21", "locationId": "<PERSON><PERSON><PERSON>", "path": "b628ed7f-4bdf-4fb1-b67b-043667d5fd21", "size": 372, "lastModifiedInMS": 1758501516075, "meta": {"revision": 4, "created": 1758501516075, "version": 0, "updated": 1758501547178}, "$loki": 8, "LastModifyInMS": 1758501547178}, {"id": "349e233d-a786-41aa-9432-307ffc03e189", "locationId": "<PERSON><PERSON><PERSON>", "path": "349e233d-a786-41aa-9432-307ffc03e189", "size": 4914, "lastModifiedInMS": 1758501516078, "meta": {"revision": 43, "created": 1758501516078, "version": 0, "updated": 1758534300041}, "$loki": 9, "LastModifyInMS": 1758534300041}, {"id": "5712afa3-15bd-456c-a81e-71073058bd8a", "locationId": "<PERSON><PERSON><PERSON>", "path": "5712afa3-15bd-456c-a81e-71073058bd8a", "size": 120, "lastModifiedInMS": 1758501547108, "meta": {"revision": 1, "created": 1758501547108, "version": 0, "updated": 1758501547182}, "$loki": 10, "LastModifyInMS": 1758501547182}, {"id": "2b1e55f5-2b7c-4acb-80a5-c14657058c33", "locationId": "<PERSON><PERSON><PERSON>", "path": "2b1e55f5-2b7c-4acb-80a5-c14657058c33", "size": 120, "lastModifiedInMS": 1758501547108, "meta": {"revision": 1, "created": 1758501547108, "version": 0, "updated": 1758501547185}, "$loki": 11, "LastModifyInMS": 1758501547185}, {"id": "03020b19-eeee-4cc3-877b-b349b4f14ef7", "locationId": "<PERSON><PERSON><PERSON>", "path": "03020b19-eeee-4cc3-877b-b349b4f14ef7", "size": 120, "lastModifiedInMS": 1758501547108, "meta": {"revision": 1, "created": 1758501547108, "version": 0, "updated": 1758501547190}, "$loki": 12, "LastModifyInMS": 1758501547190}, {"id": "57ded15d-33a5-4eaf-ae69-0de28f99e309", "locationId": "<PERSON><PERSON><PERSON>", "path": "57ded15d-33a5-4eaf-ae69-0de28f99e309", "size": 120, "lastModifiedInMS": 1758501547109, "meta": {"revision": 1, "created": 1758501547109, "version": 0, "updated": 1758501547182}, "$loki": 13, "LastModifyInMS": 1758501547182}, {"id": "b0a527e9-2aeb-43a5-8be7-e86a77d59d83", "locationId": "<PERSON><PERSON><PERSON>", "path": "b0a527e9-2aeb-43a5-8be7-e86a77d59d83", "size": 120, "lastModifiedInMS": 1758501547110, "meta": {"revision": 1, "created": 1758501547110, "version": 0, "updated": 1758501547178}, "$loki": 14, "LastModifyInMS": 1758501547178}, {"id": "de57f37e-c7f9-418d-88b7-c1404cb26a71", "locationId": "<PERSON><PERSON><PERSON>", "path": "de57f37e-c7f9-418d-88b7-c1404cb26a71", "size": 120, "lastModifiedInMS": 1758501547111, "meta": {"revision": 1, "created": 1758501547111, "version": 0, "updated": 1758501547185}, "$loki": 15, "LastModifyInMS": 1758501547185}, {"id": "f2a36dde-9097-4dcd-9087-6f73cca85373", "locationId": "<PERSON><PERSON><PERSON>", "path": "f2a36dde-9097-4dcd-9087-6f73cca85373", "size": 120, "lastModifiedInMS": 1758501547193, "meta": {"revision": 0, "created": 1758501547193, "version": 0}, "$loki": 16}, {"id": "4c2babd1-c842-481a-bf78-34dcf1402ec9", "locationId": "<PERSON><PERSON><PERSON>", "path": "4c2babd1-c842-481a-bf78-34dcf1402ec9", "size": 120, "lastModifiedInMS": 1758501547194, "meta": {"revision": 0, "created": 1758501547194, "version": 0}, "$loki": 17}, {"id": "50f0250b-989d-46de-9f3f-fd4db13c487a", "locationId": "<PERSON><PERSON><PERSON>", "path": "50f0250b-989d-46de-9f3f-fd4db13c487a", "size": 21375, "lastModifiedInMS": 1758761387316, "meta": {"revision": 174, "created": 1758761387316, "version": 0, "updated": 1758792625911}, "$loki": 20, "LastModifyInMS": 1758792625911}, {"id": "5109e4b4-d3ad-40ad-a35f-756fe9b3513a", "locationId": "<PERSON><PERSON><PERSON>", "path": "5109e4b4-d3ad-40ad-a35f-756fe9b3513a", "size": 127, "lastModifiedInMS": 1758761387320, "meta": {"revision": 0, "created": 1758761387320, "version": 0}, "$loki": 22}], "idIndex": null, "binaryIndices": {"id": {"name": "id", "dirty": false, "values": [7, 2, 6, 4, 12, 13, 14, 5, 8, 0, 1, 9, 3, 10, 11]}}, "constraints": null, "uniqueNames": [], "transforms": {}, "objType": "$EXTENTS_COLLECTION$", "dirty": false, "cachedIndex": null, "cachedBinaryIndex": null, "cachedData": null, "adaptiveBinaryIndices": true, "transactional": false, "cloneObjects": false, "cloneMethod": "parse-stringify", "asyncListeners": false, "disableMeta": false, "disableChangesApi": true, "disableDeltaChangesApi": true, "autoupdate": false, "serializableIndices": true, "disableFreeze": true, "ttl": null, "maxId": 22, "DynamicViews": [], "events": {"insert": [], "update": [], "pre-insert": [], "pre-update": [], "close": [], "flushbuffer": [], "error": [], "delete": [null], "warning": [null]}, "changes": [], "dirtyIds": []}], "databaseVersion": 1.5, "engineVersion": 1.5, "autosave": true, "autosaveInterval": 5000, "autosaveHandle": null, "throttledSaves": true, "options": {"persistenceMethod": "fs", "autosave": true, "autosaveInterval": 5000, "serializationMethod": "normal", "destructureDelimiter": "$<\n"}, "persistenceMethod": "fs", "persistenceAdapter": null, "verbose": false, "events": {"init": [null], "loaded": [], "flushChanges": [], "close": [], "changes": [], "warning": []}, "ENV": "NODEJS"}