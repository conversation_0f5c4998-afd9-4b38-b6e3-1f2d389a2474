export const CommonError = {
    NO_ERROR: 0,

    NOT_LOGGED_IN: 100,
    INVALID_USER: 101,

    BAD_REQUEST: 150,
    UNKNOWN_REQUEST: 151,

    SERVER_ERROR: 200,
    WRONG_VERSION: 201,
    REACHED_REQUESTS_PER_SECOND_PER_USER_LIMIT: 202,

    NO_PERMISSION: 400,
    PERMISSION_ERROR: 402, // NTTCMVNOWebBrowserAPIError

    PASSWORD_STRING_FORMAT_ERROR: 701,
    PASSWORD_IS_SAME_TO_OLD_PASSWORD: 702,

    INVALID_PARAMETER: '994200',
    TARGETS_INVALID: '996500',
};

const errorNameMap = Object.assign({}, ...Object.keys(CommonError).map((key) => ({ [CommonError[key]]: key })));

export const getCommonErrorName = (error) => errorNameMap[error];