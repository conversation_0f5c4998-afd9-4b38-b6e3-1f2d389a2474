/**
 * This file contains constants related to notification service (Redis & SignalR)
 *
 * values must be in sync with the ones in MVNO_COMMON_SHARED repo
 */

/**
 * Notification type to be sent to SignalR
 */
export enum NotificationType {
    /** 0035でんわトランザクションエラー */
    HAS_0035_ERROR = "0035-error",
    /** コアSwimmy連携エラー */
    HAS_CORE_SWIMMY_ERROR = "core-swimmy-error",
}

/**
 * Key used in Redis cache
 */
export enum NotificationCacheKey {
    HAS_ERROR_0035_KEY = "2_hasError0035",
    HAS_CORE_SWIMMY_ERROR_KEY = "3_hasErrorCoreSwimmy",
}
