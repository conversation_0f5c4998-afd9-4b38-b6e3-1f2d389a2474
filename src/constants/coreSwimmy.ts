// export const CORE_SWIMMY_STATUS = {
//     TOUROKU_IRAI_NOT_YET_SENT: 0,
//     TOUROKU_IRAI_RESULT_OK: 1,
//     TOUROKU_IRAI_RESULT_NG: 2,
//     TORISAGE_RESULT_NG: 3,
//     UNKNOWN_FOR_ERROR: 90,
// };

// NOTE changes here may require update to `@types/coreSwimmyApiLog`
export const CORE_SWIMMY_TYPES = {
    /** 容量・期間追加 */
    CAPACITY_ADD: "001",
    /** 回線変更 */
    KAISEN_HENKOU: "002",
    /** SIM再発行 */
    SIM_SAI_HAKKOU: "004",
    /** SIMステータス変更 */
    STATUS_CHANGE: "005",
    /** オプション変更 */
    KAISEN_OPERATION_HENKOU: "006",
    /** 回線追加 */
    KAISEN_TSUIKA: "007",
    /** 回線廃止 */
    KAISEN_HAISHI: "008",
    /** アクセス方式変更 */
    ACCESS_HOUSHIKI_HENKOU: "009",
    /** その他（フロント用予約キャンセル等） */
    ORDER_TORIKESHI: "000",
};

/**
 * 再送の際に同一MVNEフラグがつけられるトランザクション
 *
 * https://mobilus.backlog.jp/view/MVNO_N_M-3203#comment-276191132
 */
export const RESEND_SAME_MVNE_FLAG_TYPES = [
    CORE_SWIMMY_TYPES.KAISEN_TSUIKA,
    CORE_SWIMMY_TYPES.KAISEN_HAISHI,
];

export const CORE_SWIMMY_TYPES_TEXT = {
    [CORE_SWIMMY_TYPES.CAPACITY_ADD]: "容量追加",
    [CORE_SWIMMY_TYPES.KAISEN_HENKOU]: "回線変更", // 回線プラン変更・回線グループ所属回線変更
    [CORE_SWIMMY_TYPES.SIM_SAI_HAKKOU]: "SIM再発行",
    [CORE_SWIMMY_TYPES.STATUS_CHANGE]: "SIMステータス変更", // 黒化
    [CORE_SWIMMY_TYPES.KAISEN_OPERATION_HENKOU]: "回線オプション変更",
    [CORE_SWIMMY_TYPES.KAISEN_TSUIKA]: "回線追加",
    [CORE_SWIMMY_TYPES.KAISEN_HAISHI]: "回線廃止",
    [CORE_SWIMMY_TYPES.ACCESS_HOUSHIKI_HENKOU]: "アクセス方式変更",
    [CORE_SWIMMY_TYPES.ORDER_TORIKESHI]: "オーダ取消",
};


/** status codes for CoreSwimmyApiLog */
export enum CoreSwimmyStatus {
    ON_HOLD = -1,
    TOUROKU_IRAI_NOT_YET_SENT = 0,
    TOUROKU_IRAI_RESULT_OK = 1,
    TOUROKU_IRAI_RESULT_NG = 2,
    TORISAGE_RESULT_NG = 3,
    STOPPING_FOR_ERROR = 90,
}

export const CORE_SWIMMY_STATUS_TEXT = {
    [CoreSwimmyStatus.ON_HOLD]: "待機中",
    [CoreSwimmyStatus.TOUROKU_IRAI_NOT_YET_SENT]: "未登録依頼",
    [CoreSwimmyStatus.TOUROKU_IRAI_RESULT_OK]: "登録依頼済OK",
    [CoreSwimmyStatus.TOUROKU_IRAI_RESULT_NG]: "登録依頼済NG",
    [CoreSwimmyStatus.TORISAGE_RESULT_NG]: "取り下げ",
    [CoreSwimmyStatus.STOPPING_FOR_ERROR]: "不明なエラー",
};

export const CORE_SWIMMY_RESPONSE = {
    RESULT_CODE: {
        OK: "0001",
        NG: "0002",
    },
    SERVICE_PROCESS_RESULT_TYPE: {
        /** 正常 */
        OK: "0",
        /** システムエラー */
        NG: "S",
    },
    SERVICE_PROCESS_RESULT_DT_TYPE: {
        /** システムエラー（共通） */
        SYSTEM_ERROR: "S00",
        /** システムエラー（2重実行） */
        DUPLICATE_ERROR: "S01",
        /** システムエラー（業務規制） */
        VALIDATION_ERROR: "S02",
    },
};
