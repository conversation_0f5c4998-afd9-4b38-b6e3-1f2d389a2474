import { Document, model, Model, Schema } from "mongoose";

export interface ICoreServicesRestrictSetting {
    lastUpdateUserId: string;
    updatedAt: number;
    fiveGEnabledTenants: string[];
    kohaiEnableTenants: string[];
    eSIMEnabledTenants: string[];
    bbUnibaEnabledTenants: string[];
}

export interface ICoreServicesRestrictSettingDocument
    extends ICoreServicesRestrictSetting,
        Document {}

interface CoreServicesRestrictSettingModel extends Model<ICoreServicesRestrictSettingDocument> {
    /**
     * Get the list of tenant IDs that have BB Uniba enabled.
     */
    getBbUnibaEnabledTenants(): Promise<string[]>;
}

const coreServicesRestrictSettingSchema: Schema = new Schema({
    lastUpdateUserId: { type: String, required: true },
    updatedAt: { type: Number, required: true },
    fiveGEnabledTenants: { type: [String], required: true, default: [] },
    kohaiEnableTenants: { type: [String], required: true, default: [] },
    eSIMEnabledTenants: { type: [String], required: true, default: [] },
    bbUnibaEnabledTenants: { type: [String], required: true, default: [] },
});

coreServicesRestrictSettingSchema.statics.getBbUnibaEnabledTenants = async function (): Promise<string[]> {
    const setting = await this.findOne().exec();
    return setting ? setting.bbUnibaEnabledTenants : [];
};

const CoreServicesRestrictSetting = model<
    ICoreServicesRestrictSettingDocument,
    CoreServicesRestrictSettingModel
>("CoreServicesRestrictSetting", coreServicesRestrictSettingSchema, "services_restrict_setting");
export default CoreServicesRestrictSetting;