import { InvocationContext } from "@azure/functions";
import AppConfig from "@/appconfig";
import { CoreSwimmyStatus } from "@/constants/coreSwimmy";
import { setHasCoreSwimmyError } from "@/services/redisCacheService";
import { SwimmyType } from "@/types/coreSwimmyApiLog";
import { getNumberValue, isNone } from "@/utils";
import {
    model,
    Schema,
    Model,
    Document,
    FilterQuery,
    Query,
    UpdateQuery,
    UpdateWriteOpResult,
} from "mongoose";

/**
 * NG status list for error notification
 */
const NG_STATUS = [
    CoreSwimmyStatus.TOUROKU_IRAI_RESULT_NG,
    CoreSwimmyStatus.STOPPING_FOR_ERROR,
];

/** these fields are not needed during create */
type HistoryFields =
    | "statusLog"
    | "statusTime"
    | "responseLog"
    | "ngReasonLog"
    | "httpStatusCodeLog";
/** optional fields (have default values) */
type OptionalFields =
    | "needToActiveTheseIDs"
    | "isChainedTransaction"
    | "scheduledAt";

export interface ICoreSwimmyApiLog {
    tenantId: string;
    tempoId?: string;
    kaisenNo: string;
    swimmyType: SwimmyType;
    /** latest status code */
    status: CoreSwimmyStatus;
    /** latest status update timestamp (millisecond) */
    statusTime: number;
    /** status log (key = millisecond) */
    statusLog: { [key: string]: CoreSwimmyStatus };
    /** 卸ポータルフロントのオーダーID (PF0xxx) */
    soId: string;
    /** 卸コアのオーダーID (AP0xxx) */
    requestOrderId: string;
    requestParam?: any;
    /** latest response object */
    responseParam?: any;
    /** response param history (key = millisecond) */
    responseLog: { [key: string]: any };
    /** NG reason */
    ngReason?: string;
    /** history of NG reason */
    ngReasonLog?: { [key: string]: string };
    /** UNIX timestamp */
    createdAt?: number;
    /** UNIX timestamp */
    lastUpdatedAt?: number;
    /** flag whether this transaction is already in queue or not */
    jikkouchuFlg: boolean;
    /** timestamp when received response */
    getResponseAt?: number;
    updateRetryUsers?: { [key: string]: string };
    updateStatusUsers?: { [key: string]: string };
    /** HTTP code from API response */
    httpStatusCodeLog?: { [key: string]: number };
    /** 当トランザクションのAPI連携が正常完了後、次に連携させるトランザクションID `id` (string version of `_id`) */
    // NOTE use `_id` because 1 requestOrderId/soId can have multiple transactions
    needToActiveTheseIDs: string[];
    /** flag to mark a transaction part of 廃止→追加 chain (including the first 2) */
    isChainedTransaction: boolean;
    /**
     * Send to ASB queue as scheduled message with this timestamp (in UNIX seconds)
     *
     * NOTE: not all scheduled transactions will have value on this field, only those which are not
     * linked but need to be scheduled for later processing (needed for passing NeedForWaitingResult
     * to CoreSwimmyService after creation)
     */
    scheduledAt?: number;
}

export interface ICoreSwimmyApiLogDocument
    extends ICoreSwimmyApiLog,
        Document {}

export interface ICoreSwimmySearchResult {
    result: ICoreSwimmyApiLogDocument[];
    count: number;
}

export interface ICoreSwimmyResendQuery {
    _id: string;
    status: object;
    jikkouchuFlg: boolean;
}

export interface ICoreSwimmyResendUpdate {
    status: CoreSwimmyStatus;
    statusTime: number;
    jikkouchuFlg: boolean;
    // [key: `statusLog.${string}`]: CoreSwimmyStatus,
    // [key: `updateRetryUsers.${string}`]: string
    requestParam?: any;
}

type ICoreSwimmyApiLogCreate = Omit<
    ICoreSwimmyApiLog,
    HistoryFields | OptionalFields
> &
    Partial<Pick<ICoreSwimmyApiLog, OptionalFields>>;

/** `createSwimmyApiLog` options */
type CoreSwimmyApiLogCreateOptions = {
    /**
     * no need to run `checkNeedForWaiting`
     *
     * usually no need to use this, but it is needed when creating first transaction of
     * 同一MVNE転入 chain (we are assuming previous transactions are already completed / cancelled)
     */
    skipWaitingCheck?: boolean;
    /**
     * override result of `checkNeedForWaiting` to use this specific `CoreSwimmyApiLog`'s `id`.
     * no need to use this except for 同一MVNE転入 chain 2nd transaction
     *
     * (if `skipWaitingCheck = true`, this option will be ignored)
     */
    setWaitingForId?: string;
};

/**
 * Response type for `checkNeedForWaiting` function
 */
type NeedForWaitingResult = {
    /**
     * true if there is recent transactions with same `kaisenNo`
     * - `false`: send to queue as immediate message
     * - `true`: check value of `waitForId`
     */
    needToWait: boolean;
    /**
     * if the previous transaction is found (`needToWait = true`), depends on its status:
     * - on hold / in process : add to its `needToActiveTheseIDs`, don't send to queue yet
     * - completed / cancelled : send to queue as scheduled message
     */
    waitForId?: string;
    /**
     * If the preceeding transaction is part of 同一MVNE転入 chain, current one should also set
     * `isChainedTransaction = true`.
     *
     * This is used to determine status validation of preceeding
     * transaction before activing current one.
     * @see HandleCoreSwimmyApiRequestQueue.execCoreSwimmyAPI
     */
    isSameMvneChain?: boolean;
    /**
     * if `waitForId` is not set (meaning previous transaction is already completed/cancelled),
     * set this to schedule next message to ASB queue.
     */
    scheduledAt?: number;
};

interface CoreSwimmyApiLogModel extends Model<ICoreSwimmyApiLogDocument> {
    search(
        query: FilterQuery<ICoreSwimmyApiLog>,
        sort: number,
        limit: number,
        skip: number,
    ): Promise<ICoreSwimmySearchResult>;
    findByRequestOrderId(
        requestOrderId: string,
    ): Promise<ICoreSwimmyApiLogDocument[]>;
    findByTenantId(tenantId: string): Promise<ICoreSwimmyApiLogDocument[]>;
    /**
     * Find CoreSwimmyApiLog by requestOrderId and swimmyType
     * @param requestOrderId
     * @param swimmyType
     */
    findByOrderIdAndType(
        requestOrderId: string,
        swimmyType: SwimmyType,
    ): Promise<ICoreSwimmyApiLogDocument>;
    /**
     * Register new CoreSwimmyApiLog
     *
     * __NOTE__: Before sending to Service Bus, check the actual saved value of `jikkouchuFlg` from
     * returned document
     * @param swimmyApiLog
     * @param createOptions
     */
    createSwimmyApiLog(
        swimmyApiLog: ICoreSwimmyApiLogCreate,
        createOptions?: CoreSwimmyApiLogCreateOptions,
    ): Promise<ICoreSwimmyApiLogDocument>;
    /**
     * Update CoreSwimmyApiLog status
     */
    updateStatus(
        context: InvocationContext,
        requestOrderId: string,
        swimmyType: SwimmyType,
        newStatus: CoreSwimmyStatus,
        ngReason?: string,
        updateStatusUsers?: string,
        httpStatusCode?: number,
    ): Promise<UpdateWriteOpResult>;
    updateJikkouchuFlg(
        requestOrderId: string,
        swimmyType: SwimmyType,
        newJikkouchuFlg: boolean,
    ): Promise<void>;
    /**
     * Update CoreSwimmyApiLog status and response object
     */
    updateStatusAndResponse(
        context: InvocationContext,
        requestOrderId: string,
        swimmyType: SwimmyType,
        newStatus: CoreSwimmyStatus,
        responseParam: any,
        ngReason?: string,
        httpStatusCode?: number,
    ): Promise<void>;
    updateResendRequest(
        query: ICoreSwimmyResendQuery,
        update: ICoreSwimmyResendUpdate,
    ): Promise<ICoreSwimmyApiLogDocument>;
    revertUpdateResendRequest(
        query: ICoreSwimmyResendQuery,
        update: ICoreSwimmyResendUpdate,
    ): Promise<void>;
    /**
     * Set status to 未登録依頼 and jikkouchuFlg to true and return new updated document
     * @param ids transaction's `.id`
     */
    activateWaitingTransaction(id: string): Promise<ICoreSwimmyApiLogDocument>;
}

const coreSwimmyApiLogSchema: Schema = new Schema({
    tenantId: { type: String, required: true, index: true },
    tempoId: { type: String },
    kaisenNo: { type: String, required: true, index: true },
    swimmyType: { type: String, index: true },
    soId: { type: String, required: true, index: true },
    requestOrderId: { type: String, required: true, index: true },
    requestParam: { type: Object },
    responseParam: { type: Object },
    createdAt: {
        type: Number,
        required: true,
        default: () => Math.floor(Date.now() / 1000),
        index: true,
    },
    lastUpdatedAt: {
        type: Number,
        required: true,
        default: () => Math.floor(Date.now() / 1000),
    },
    status: {
        type: Number,
        required: true,
        default: CoreSwimmyStatus.TOUROKU_IRAI_NOT_YET_SENT,
        index: true,
    },
    statusTime: {
        type: Number,
        required: true,
        default: () => Date.now(),
    },
    statusLog: {
        type: Object,
        default: {},
    },
    responseLog: {
        type: Object,
        default: {},
    },
    jikkouchuFlg: {
        type: Boolean,
        required: true,
        default: false,
        index: true,
    },
    getResponseAt: {
        type: Number,
        set: (d) => {
            if (typeof d === "number") {
                return d;
            } else if (d instanceof Date) {
                return Math.floor(d.getTime() / 1000);
            } else {
                throw new Error("Invalid type for getResponseAt");
            }
        },
    },
    updateRetryUsers: { type: Object },
    ngReason: { type: String },
    ngReasonLog: { type: Object },
    updateStatusUsers: { type: Object },
    httpStatusCodeLog: { type: Object },
    needToActiveTheseIDs: { type: [String], default: () => [] },
    isChainedTransaction: { type: Boolean, default: false, index: true },
    scheduledAt: { type: Number, default: () => null },
});

coreSwimmyApiLogSchema.index({ tenantId: 1, requestOrderId: 1 });
coreSwimmyApiLogSchema.index({ tenantId: 1, soId: 1 });
coreSwimmyApiLogSchema.index({ tenantId: 1, kaisenNo: 1 });
coreSwimmyApiLogSchema.index({ status: 1, jikkouchuFlg: 1 }); // for resend
coreSwimmyApiLogSchema.index({ tenantId: 1, swimmyType: 1, status: 1 });
coreSwimmyApiLogSchema.index({ tenantId: 1, status: 1, createdAt: 1 });
coreSwimmyApiLogSchema.index({
    tenantId: 1,
    swimmyType: 1,
    status: 1,
    createdAt: 1,
});
coreSwimmyApiLogSchema.index({
    status: 1,
    isChainedTransaction: 1,
});
coreSwimmyApiLogSchema.index({
    requestOrderId: 1,
    swimmyType: 1,
});

// Static methods
coreSwimmyApiLogSchema.statics.findByTenantId = async function (
    tenantId: string,
): Promise<ICoreSwimmyApiLogDocument[]> {
    return await this.find({ tenantId });
};

coreSwimmyApiLogSchema.statics.findByRequestOrderId = async function (
    requestOrderId: string,
): Promise<ICoreSwimmyApiLogDocument[]> {
    return await this.find({ requestOrderId }).lean().exec();
};

coreSwimmyApiLogSchema.statics.findByOrderIdAndType = async function (
    requestOrderId: string,
    swimmyType: SwimmyType,
): Promise<ICoreSwimmyApiLogDocument> {
    return await this.findOne({ requestOrderId, swimmyType });
};

// createSwimmyApiLog example
coreSwimmyApiLogSchema.statics.createSwimmyApiLog = async function (
    swimmyApiLog: ICoreSwimmyApiLogCreate,
    createOptions?: CoreSwimmyApiLogCreateOptions,
): Promise<ICoreSwimmyApiLogDocument> {
    const newApiLog: Partial<ICoreSwimmyApiLog> = swimmyApiLog;
    const nowMillis = Date.now();
    newApiLog.statusTime = nowMillis;
    newApiLog.statusLog = { [nowMillis]: swimmyApiLog.status };
    if (!isNone(swimmyApiLog.responseParam)) {
        newApiLog.responseLog = { [nowMillis]: swimmyApiLog.responseParam };
    }

    let needCheckWaiting: NeedForWaitingResult = null;
    let previousApiLog: string = null;
    if (createOptions?.skipWaitingCheck !== true) {
        if (!isNone(createOptions?.setWaitingForId)) {
            previousApiLog = createOptions.setWaitingForId;
        } else {
            needCheckWaiting = await checkNeedForWaiting(
                swimmyApiLog.swimmyType,
                swimmyApiLog.kaisenNo,
            );
            if (needCheckWaiting.needToWait) {
                if (needCheckWaiting.isSameMvneChain) {
                    // pass down 同一MVNE chain flag
                    newApiLog.isChainedTransaction = true;
                }
                if (!isNone(needCheckWaiting.waitForId)) {
                    previousApiLog = needCheckWaiting.waitForId;
                } else {
                    // case previous transaction is already completed/cancelled
                    // previousApiLog remains null but we will send as scheduled message
                    // status and jikkouchuFlg won't be changed
                    newApiLog.scheduledAt = needCheckWaiting.scheduledAt;
                }
            }
        }
    }
    if (previousApiLog) {
        // NOTE if previous apiLog is defined, put current transaction status ON_HOLD (待機中)
        newApiLog.status = CoreSwimmyStatus.ON_HOLD;
        newApiLog.statusLog[nowMillis] = CoreSwimmyStatus.ON_HOLD;
        newApiLog.jikkouchuFlg = false;
        if (!isNone(createOptions?.setWaitingForId)) {
            // if setWaitingForId is used, it means the previous transaction is 同一MVNE chain
            newApiLog.isChainedTransaction = true;
        }
    }

    const doc = (await this.create(newApiLog)) as ICoreSwimmyApiLogDocument;

    if (previousApiLog) {
        await this.updateOne(
            { _id: previousApiLog },
            { $push: { needToActiveTheseIDs: doc.id } },
        );
    }
    return doc;
};

coreSwimmyApiLogSchema.statics.search = async function (
    query: FilterQuery<ICoreSwimmyApiLog>,
    sort: number,
    limit: number,
    skip: number,
): Promise<ICoreSwimmySearchResult> {
    let result: ICoreSwimmyApiLogDocument[];
    if (!isNone(limit) && isNone(skip)) {
        result = await this.find(query).sort(sort).limit(limit).lean().exec();
    } else if (isNone(limit) && !isNone(skip)) {
        result = await this.find(query).sort(sort).skip(skip).lean().exec();
    } else {
        result = await this.find(query)
            .sort(sort)
            .skip(skip)
            .limit(limit)
            .lean()
            .exec();
    }
    const count: number = await this.countDocuments(query).exec();
    return { result, count };
};

coreSwimmyApiLogSchema.statics.updateStatus = async function (
    context: InvocationContext,
    requestOrderId: string,
    swimmyType: SwimmyType,
    newStatus: CoreSwimmyStatus,
    ngReason?: string,
    updateStatusUsers?: string,
    httpStatusCode?: number,
) {
    const nowMillis = Date.now();
    const update: UpdateQuery<ICoreSwimmyApiLog> = {
        $set: {
            status: newStatus,
            statusTime: nowMillis,
            [`statusLog.${nowMillis}`]: newStatus,
        },
    };
    if (!isNone(ngReason)) {
        update.$set.ngReason = ngReason;
        update.$set[`ngReasonLog.${nowMillis}`] = ngReason;
    }
    if (!isNone(updateStatusUsers)) {
        update.$set[`updateStatusUsers.${nowMillis}`] = updateStatusUsers;
    }
    if (!isNone(httpStatusCode) && typeof httpStatusCode === "number") {
        update.$set[`httpStatusCodeLog.${nowMillis}`] = httpStatusCode;
    }

    const result = await this.updateOne(
        {
            requestOrderId,
            swimmyType,
        },
        update,
    );
    await updateErrorFlagIfNeeded(context, newStatus);

    return result;
};

coreSwimmyApiLogSchema.statics.updateJikkouchuFlg = async function (
    requestOrderId: string,
    swimmyType: SwimmyType,
    newJikkouchuFlg: boolean,
) {
    return await this.updateOne(
        {
            requestOrderId,
            swimmyType,
        },
        {
            $set: {
                jikkouchuFlg: newJikkouchuFlg,
            },
        },
    );
};

coreSwimmyApiLogSchema.statics.updateStatusAndResponse = async function (
    context: InvocationContext,
    requestOrderId: string,
    swimmyType: SwimmyType,
    newStatus: CoreSwimmyStatus,
    responseParam: any,
    ngReason?: string,
    httpStatusCode?: number,
) {
    const nowMillis = Date.now();
    const update: UpdateQuery<ICoreSwimmyApiLog> = {
        $set: {
            status: newStatus,
            statusTime: nowMillis,
            responseParam,
            getResponseAt: Math.floor(Date.now() / 1000),
            [`statusLog.${nowMillis}`]: newStatus,
            [`responseLog.${nowMillis}`]: responseParam,
        },
    };
    if (!isNone(ngReason)) {
        update.$set.ngReason = ngReason;
        update.$set[`ngReasonLog.${nowMillis}`] = ngReason;
    }
    if (!isNone(httpStatusCode) && typeof httpStatusCode === "number") {
        update.$set[`httpStatusCodeLog.${nowMillis}`] = httpStatusCode;
    }
    await this.updateOne(
        {
            requestOrderId,
            swimmyType,
        },
        update,
    );
    await updateErrorFlagIfNeeded(context, newStatus);
};

coreSwimmyApiLogSchema.statics.updateResendRequest = async function (
    query: ICoreSwimmyResendQuery,
    update: ICoreSwimmyResendUpdate,
): Promise<ICoreSwimmyApiLogDocument> {
    return await this.findOneAndUpdate(query, update, {
        new: true,
        runValidators: true,
    });
};

coreSwimmyApiLogSchema.statics.activateWaitingTransaction = async function (
    id: string,
): Promise<ICoreSwimmyApiLogDocument> {
    return this.findOneAndUpdate(
        { _id: id, status: CoreSwimmyStatus.ON_HOLD }, // activate only 待機中 status
        {
            $set: {
                status: CoreSwimmyStatus.TOUROKU_IRAI_NOT_YET_SENT,
                jikkouchuFlg: true,
            },
        },
        { new: true },
    );
};

const CoreSwimmyApiLog = model<
    ICoreSwimmyApiLogDocument,
    CoreSwimmyApiLogModel
>("CoreSwimmyApiLog", coreSwimmyApiLogSchema, "core_swimmy_api_logs");

export default CoreSwimmyApiLog;

// internal functions below

/**
 * Count all NG documents and update hasCoreSwimmyError flag
 *
 * - if there is any NG documents, set hasCoreSwimmyError to true
 * - if there is no NG documents, set hasCoreSwimmyError to false
 */
async function updateErrorFlag(context: InvocationContext) {
    try {
        context.log("CoreSwimmyApiLog.updateErrorFlag start");
        const totalErrorFound = await CoreSwimmyApiLog.countDocuments({
            status: {
                $in: NG_STATUS,
            },
        });
        context.log(
            `CoreSwimmyApiLog.updateErrorFlag totalErrorFound ${totalErrorFound}`,
        );
        if (totalErrorFound > 0) {
            await setHasCoreSwimmyError(context, true);
        } else {
            await setHasCoreSwimmyError(context, false);
        }
    } catch (error) {
        context.warn("CoreSwimmyApiLog.updateErrorFlag error:", error);
    }
}

/**
 * Notify error based on newStatus
 */
async function updateErrorFlagIfNeeded(
    context: InvocationContext,
    newStatus: CoreSwimmyStatus,
) {
    const isError = NG_STATUS.includes(newStatus);
    context.log("CoreSwimmyApiLog.updateErrorFlagIfNeeded", isError, newStatus);
    if (isError) {
        try {
            // if current newStatus is NG, no need to check other documents
            await setHasCoreSwimmyError(context, true);
        } catch (error) {
            context.warn(
                "CoreSwimmyApiLog.updateErrorFlagIfNeeded setHasCoreSwimmyError error:",
                error,
            );
        }
    } else {
        await updateErrorFlag(context);
    }
}

/**
 * Check whether current CoreSwimmyApiLog need to be set on hold (status=待機中)
 * (no need to send to ServiceBus)
 * @param swimmyType
 * @param lineNo
 * @param session
 * @returns `id` of the previous transaction or null if not found
 */
async function checkNeedForWaiting(
    swimmyType: SwimmyType,
    lineNo: string,
): Promise<NeedForWaitingResult> {
    const result: NeedForWaitingResult = {
        needToWait: false, // default: send as immediate message
    };
    const config = AppConfig.getCoreConfig(null);
    const previousSeconds = getNumberValue(
        config.CORE_SWIMMY_API.MESSAGE_DELAY_SECONDS,
        60,
    );
    const timeframe = Math.floor(Date.now() / 1000) - previousSeconds;
    const apiLogs = await CoreSwimmyApiLog.find(
        {
            kaisenNo: lineNo,
            $or: [
                { createdAt: { $gte: timeframe } }, // only check last X time
                {
                    // same MVNE chain may have longer scheduled time
                    // join the chain only if last transaction is not finished yet
                    isChainedTransaction: true,
                    status: {
                        $in: [
                            CoreSwimmyStatus.TOUROKU_IRAI_NOT_YET_SENT,
                            CoreSwimmyStatus.ON_HOLD,
                        ],
                    },
                },
            ],
        },
        null,
        { sort: { _id: -1 } },
    );
    if (apiLogs.length > 0) {
        // find latest transaction (the one with empty needToActiveTheseIDs)
        const latest = apiLogs.find(
            (log) =>
                Array.isArray(log.needToActiveTheseIDs) &&
                log.needToActiveTheseIDs.length === 0,
        );
        result.needToWait = true; // send as scheduled message or wait for previous transaction
        if (!latest) {
            // ??? should not happen, but just send as scheduled message
            return result;
        }
        if (
            [
                CoreSwimmyStatus.TOUROKU_IRAI_NOT_YET_SENT,
                CoreSwimmyStatus.ON_HOLD,
            ].includes(latest.status)
        ) {
            result.waitForId = latest.id;
            result.isSameMvneChain = latest.isChainedTransaction === true;
        } else {
            result.scheduledAt =
                Math.floor(Date.now() / 1000) + previousSeconds;
        }
    }

    return result;
}
