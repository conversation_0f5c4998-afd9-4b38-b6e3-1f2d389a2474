/**
 * フロント外部APIのリクエストヘッダー
 */
export interface FrontExternalApiRequestHeader {
    /**
     * 送信番号 (7桁の数字)
     */
    sequenceNo: string;
    /**
     * 送信元システムID (4桁の英数字)
     */
    senderSystemId: string;
    /**
     * API認証キー (72桁の英数字)
     */
    apiKey: string;
    /**
     * 昨日種別 (2桁の数字)
     */
    functionType: string;
}

/**
 * フロント外部APIのレスポンスヘッダー
 */
interface FrontExternalApiResponseHeader {
    /**
     * 送信番号 (7桁の数字)
     */
    sequenceNo: string;
    /**
     * 受信日時 (yyyy/MM/dd HH:mm:ss)
     */
    receivedDate: string;
    /**
     * 処理コード (6桁の数字)
     */
    processCode: string;
    /**
     * API処理ID (27桁の英数字)
     */
    apiProcessID: string;
}

interface BaseRequest {
    /**
     * ヘッダー情報
     */
    requestHeader: FrontExternalApiRequestHeader;
    /**
     * テナントID
     */
    tenantId: string;
}

export interface FrontExternalApiBaseResponse {
    /**
     * ヘッダー情報
     */
    responseHeader: FrontExternalApiResponseHeader;
}

export interface FrontExternalSoCancelRequest extends BaseRequest {
    /**
     * SOーID
     */
    soId: string;
    /**
     * CoreAPI連携有無
     */
    skipCore?: '0' | '1';
}

// tslint:disable-next-line:no-empty-interface
export interface FrontExternalSoCancelResponse
    extends FrontExternalApiBaseResponse {}
