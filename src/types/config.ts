export interface CoreConfig {
    DB_NAME: {
        CORE_API_SERVICE: string;
    };
    THROTTLING: {
        WINDOW_SIZE: number;
    };
    CORE_SWIMMY_API: {
        POLLING: {
            MAX_CONNECTION: number;
            SERVICE_BUS_CONNECTION_STRING: string;
            SERVICE_BUS_QUEUE_NAME: string;
        };
        /** Core Swimmy API origin (`https://<server-name>:<port>`) */
        ENDPOINT_ORIGIN: string;
        /** API path for sending order to Core Swimmy (e.g. `/v1/order-management/order/entry`) */
        ENDPOINT_PATH: string;
        /** Flag to decide where the request will be sent: a mock or actual CoreSwimmy server */
        RUN_WITH_MOCK: boolean;
        /**
         * Next-hour minute value to schedule on hold order due to masking process.
         * Value should be between 0 and 59.
         *
         * Example: `15` means that the on hold order will be scheduled at the next hour at 15 minute.
         */
        MASKING_ONHOLD_MINUTE: string;
        /**
         * Array of hours to skip when scheduling on hold order due to long-running masking process.
         *
         * Example: `"[21, 22]"` means that 20:xx order will be scheduled at 23:yy
         */
        MASKING_ONHOLD_SKIP_HOURS: string;
        /**
         * Schedule next message to ASB for same kaisenNo to avoid immediate processing.
         *
         * Also used to delay 廃止 part of 廃止->追加 同一MVNE chain if there is any cancel API called
         * before.
         */
        MESSAGE_DELAY_SECONDS: string;
    };
    CORE_TPC_API: {
        /** Flag to decide where the request will be sent: a mock or actual TPC server */
        RUN_WITH_MOCK: boolean;
    };
    NOTIFICATION: {
        SERVICE_BUS_CONNECTION_STRING: string;
        SERVICE_BUS_QUEUE_NAME: string;
    };
    RESERVED_SO: {
        SERVICE_BUS_CONNECTION_STRING: string;
        SERVICE_BUS_QUEUE_NAME: string;
        /** API endpoint for service order execution */
        API_ENDPOINT?: string;
        TIMER: {
            SCHEDULE: string;
        };
    };
    AUTO_LINE_GROUP_MOD_BUCKET: {
        TIMER: {
            SCHEDULE: string;
        };
    };
    RESERVE_REWRITE: {
        TIMER: {
            SCHEDULE: string;
        };
    }
    /**
     * 外部API設定
     */
    API_SERVICE: {
        /**
         * API endpoint for external API used to cancel reserved service orders
         */
        FRONT_EXTERNAL_API: string;
        /**
         * API endpoint for Core API (self)
         *
         * *Note*: using same value as `RESERVED_SO.API_ENDPOINT`
         */
        CORE_EXTERNAL_API: string;
        /**
         * モックモード
         */
        RUN_WITH_MOCK: boolean;
    };
    V1_HAS_STOPPED_RUNNING: boolean;
}
