import OptionUpdate from "@/core/dto/OptionUpdate";

export interface TransactionPayloadBase {
    /**
     * REST APIで受け付けた際のシステム日付
     * (yyyyMMdd)
     */
    receivedDate: string;
    /**
     * 卸ポータルフロントのSO-ID (PF0~)
     */
    frontSoId: string;
    /**
     * 卸ポータルコアのSO-ID (AP0~)
     */
    coreSoId: string;
    /**
     * 卸ポータルテナントID (from dto `tenantId` field)
     */
    opfTenantId: string;
    /**
     * 対象回線のテナントID (from dto `targetTenantId` field)
     */
    tenantId: string;
    /**
     * オーダー種別 (from FunctionMeta.getOrderType)
     */
    orderType?: string;
    /**
     * 回線番号
     */
    lineNo: string;
    /**
     * 紐づくN番
     */
    nNo: string;
}

/**
 * 送付先情報
 */
interface DeliveryAddress {
    /**
     * 送付先郵便番号
     */
    postcode: string;
    /**
     * 送付先都道府県
     */
    prefecture: string;
    /**
     * 送付先市区郡町村
     */
    city: string;
    /**
     * 送付先大字通称
     */
    ooaza: string;
    /**
     * 送付先字
     */
    aza: string;
    /**
     * 送付先丁目番地
     */
    block: string;
    /**
     * 送付先ビル名等
     */
    building: string;
    /**
     * 送付先氏名
     */
    name: string;
    /**
     * 送付先部課名
     */
    department: string;
    /**
     * 送付先担当者
     */
    contact: string;
    /**
     * 送付先電話番号
     */
    tel: string;
}

export interface LineGroupAddAcquisitionPayload extends TransactionPayloadBase {
    tBan: string;
    requestDate: string
}

export interface LineAddAcquisitionPayload extends TransactionPayloadBase {
    tBan: string;
    requestDate: string
}

export interface SIMReissueTransactionPayload extends TransactionPayloadBase {
    /**
     * SIM番号
     */
    simNo: string;
    /**
     * カード種別ID
     */
    simType: string;
    /**
     * カード種別ID
     */
    cardTypeId: string;
    /**
     * 「eSIMプロファイル再ダウンロードオーダ」フラグ
     *
     * 変更前のEIDと変更後のEIDが同じの場合は`true`に要設定
     */
    isESIMProfileRedownload: boolean;
}

export interface OptionChangeTransactionPayload extends TransactionPayloadBase {
    /** NW暗証番号変更 */
    pinChange?: string;
    /** 国際ローミング利用限度額 */
    intlRoaming?: OptionUpdate;
    /** 留守番でんわ */
    voicemail?: OptionUpdate;
    /** キャッチホン */
    callWaiting?: OptionUpdate;
    /** 国際電話 */
    intlCall?: OptionUpdate;
    /** 転送でんわ */
    forwarding?: OptionUpdate;
    /** 国際着信転送 */
    intlForwarding?: OptionUpdate;
}

export interface LineDeleteTransactionPayload extends TransactionPayloadBase {
    /**
     * オーダー種別
     */
    orderType: string;
    /** MNP転出フラグ */
    mnpOutFlag: string;
    /** 予約日 (yyyyMMdd) */
    reserveDate: string;
}

export interface NWContractTransactionPayload extends TransactionPayloadBase {
    /** 変更前アクセス方式 */
    accessPre: string;
    /** 変更後アクセス方式 */
    access: string;
    /** カード種別ID */
    cardTypeId: string;
}

export interface FrontSoCancelPayload extends TransactionPayloadBase {
    /** SO-ID */
    serviceOrderIdKey: string;
    /** 予約オーダの受付ID */
    receiptId: string;
}

export interface LineGroupModifyAcquisitionPayload extends TransactionPayloadBase {
    // グループプロファイル番号
    lineGroupId: string,
    // 操作
    operation: string,
}

// Add property later
export interface SoCancelPayload extends TransactionPayloadBase {
    /** SO-ID */
    serviceOrderIdKey: string;
    /** 予約オーダの受付ID */
    receiptId: string;
}
// Add property later
export interface LinePlanAcquisitionPayload extends TransactionPayloadBase {
    // 申し込む料金プランを設定
    resalePlanIdT: string;
    // 希望日を設定
    reqDate: string;
    // 相殺OP
    op: boolean;
}

export interface CoreSwimmyLineEnablePayload extends TransactionPayloadBase {
    /** 同一MVNE */
    sameMvneFlag: boolean;
    /** カード種別(T番ID) */
    deviceTypeIdT: string;
    /** アクティベート実施日 */
    enableDate: string;
}

export interface PreLineAddPayload extends TransactionPayloadBase {
    custInfo: {
        /** 代理店コード */
        agencyCode: string;
        /**
         * お客様対応部門（担当）
         */
        customerService: string;
        /**
         * お客様対応部門電話番号
         */
        customerServicePhoneNumber: string;
        /**
         * お客様対応部門E-MAIL
         */
        customerServiceEmail: string;
        /**
         * 契約者名（カナ）
         */
        contractNameKana: string;
        /**
         * 契約者名
         */
        contractName: string;
    };
    /** フルMVNOフラグ */
    isFM: boolean;
    /** 予約日（yyyy/MM/dd 04:00形式） */
    reserveDate: string;
    /** 同一MVNE */
    isSameMvne: boolean;
    /** カード種別（提供商品） */
    cardTypeIdT: string;
    /** SIM番号 */
    simNo: string;
    /** SIMステータス (黒:0、半黒：1) */
    hankuro: boolean;
    /** SIMのみフラグ */
    simFlag: boolean;
    /** オーダ種別 */
    orderType: string;
    /** 送付先情報 */
    delivery: DeliveryAddress;
    /**
     * 再販料金プラン →
     * use *プランID（T番）* from `plans` table
     */
    pricePlanIdT: string;
    /** アクセス方式 (2文字コード`S[A-G]`) */
    accessType: string;
    /** 国際ローミング利用限度額ID (T番) */
    intlRoamingT?: string;
    /** 留守番でんわID (T番) */
    voicemailT?: string;
    /** キャッチホンID (T番) */
    callWaitingT?: string;
    /** 国際電話ID (T番) */
    intlCallT?: string;
    /** 転送でんわID (T番) */
    forwardingT?: string;
    /** 国際着信転送ID (T番) */
    intlForwardingT?: string;
    /** MNP転入オプションID (T番) (MNP_IN_TYPE_YES or SAME_MVNE only) */
    mnpAddOptionIdT?: string;
    /** MNP転出オプションID (T番) */
    mnpDelOptionIdT?: string;
    /**
     * - NO: 0
     * - YES: 1
     * - YES (SAME MVNE): 2
     */
    mnpInType: number;
}

// tslint:disable-next-line: no-empty-interface
export interface SameMvneHaishiPayload extends TransactionPayloadBase {}