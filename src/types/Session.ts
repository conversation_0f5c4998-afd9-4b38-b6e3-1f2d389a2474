/**
 * User roles
 *
 * 0 - スーパユーザ
 *
 * 1 - コムオペレータユーザ
 *
 * 2 - 代表ユーザ
 *
 * 3 - オペレータユーザ
 *
 * 4 - 閲覧ユーザ
 *
 * 999 - 店舗オペレータユーザ
 */
export type UserRole = 0 | 1 | 2 | 3 | 4 | 999;

export interface UserSession {
    internalId: string;
    plusId: string;
    name: string;
    maskPhoneNumber?: boolean;
    passwordExpired?: boolean;
}

export interface SessionData {
    roleId: UserRole;
    tenantId: string;
    tempoId: string;
    user: UserSession;
}
