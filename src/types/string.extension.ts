export {};

/**
 * String Extender
 */
declare global {
    interface String {
        equals(otherString?: string): boolean;
        /** alias for localCompare */
        compareTo(that: string): number;
    }
}

String.prototype.equals = function (otherString?: string): boolean {
    if (this === null || otherString === null) {
        return false;
    }
    return this === otherString;
};
String.prototype.compareTo = String.prototype.localeCompare;
