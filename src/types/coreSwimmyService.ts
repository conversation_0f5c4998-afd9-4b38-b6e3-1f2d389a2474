/**
 * API再送時のオプション設定
 */
export type ResendOptions = {
    /**
     * 同一MVNEフラグ （回線追加オーダのみ）
     *
     * `0` or `1
     */
    sameMvneFlag?: number;
};

type AppAttributeCode =
    | "M0000001"
    | "M0000002"
    | "M0000012"
    | "M0000013"
    | "M0000018"
    | "M0000022"; // define all possible values here

/**
 * 処理識別
 *
 * - 01：申込登録
 * - 02：チェックのみ
 * - 03：ワーニング続行
 */
type AppRequestInfo_ProcessType = "01" | "02" | "03";

/**
 * オーダ種別
 *
 * ■契約
 * - 02：契約新規
 * - 12：契約変更
 * - 14：契約訂正
 * - 04：契約廃止
 *
 * ■商品
 * - 01：商品新規
 * - 11：商品変更
 * - 13：商品訂正
 * - 03：商品廃止
 */
type OrderType = "02" | "12" | "14" | "04" | "01" | "11" | "13" | "03";

/**
 * 申込ステータス
 *
 * ■契約
 * - 10：オーダ登録
 * - 11：オーダ取消
 * - 15：オーダ確定
 * - 16：オーダ確定取り下げ
 * - 50：オーダ完了
 * - 60：竣工済（マスタ化済）
 *
 * ■商品
 * - 10：オーダ登録
 * - 11：オーダ取消
 * - 15：オーダ確定
 * - 16：オーダ確定取り下げ
 * - 30：カード納品ファイル出力
 * - 60：竣工済（マスタ化済）
 */
type AppStatus = "10" | "11" | "15" | "16" | "50" | "60" | "30";

/**
 * 申込商品の処理区分
 * - 00：変更なし
 * - 01：追加
 * - 02：変更
 * - 03：削除
 * - 04：親商品変更（初期開発では未使用）
 */
type AppPrdt_ProcessType = "00" | "01" | "02" | "03" | "04";

/**
 * 申込商品ステータス
 *
 * - 01：オーダ中
 * - 02：取消
 * - 03：竣工済み
 */
type AppPrdtStatus = "01" | "02" | "03";

/**
 * 商品種別コード
 *
 * - 0001：提供サービス
 * - 0002：提供商品
 * - 0003：再販料金プラン
 * - 0006：アカウントオプション
 * - 0007：契約情報
 * - 0009：工事手配
 * - 0010：商品手配
 * - 0011：記事
 * - 0013：提供eSIM商品
 * - 0014：プロファイル商品
 */
type PrdtTypeCode =
    | "0001"
    | "0002"
    | "0003"
    | "0006"
    | "0007"
    | "0009"
    | "0010"
    | "0011"
    | "0013"
    | "0014";

/**
 * （情報域）共通ヘッダ情報
 */
export interface CoreSwimmyTransactionRequestHeader {
    /**
     * 要求元システムＩＤ
     *
     * 「MAP:卸ポータル」固定
     */
    requestFromSystemId: "MAP";
    /**
     * 他システム送信日時
     *
     * yyyymmddhhmmss | SwimmyへのAPIリクエスト時間
     */
    otherSystemSendDatetime: string;
    /**
     * オペレータＩＤ
     */
    operatorId?: string;
    /**
     * オペレータグループID
     */
    operatorGroupId?: string;
}

/**
 * (情報域)申込基本詳細情報
 */
export interface AppBasicDtl {
    /**
     * 申込属性コード
     */
    appAttributeCode: AppAttributeCode;
    /**
     * 申込属性値
     */
    appAttributeValue: string;
}

/**
 * (情報域)申込商品詳細情報
 */
interface AppPrdtDtl {
    /**
     * 商品属性コード
     */
    prdtAttributeCode: string;
    /**
     * 商品属性値
     */
    prdtAttributeValue: string;
}

/**
 * (情報域)申込商品情報
 */
export interface AppPrdt {
    /**
     * アカウントID
     */
    accountId?: string;
    /**
     * アカウント商品ID
     */
    accountPrdtId?: string;
    /**
     * 申込商品ステータス
     */
    appPrdtStatus: AppPrdtStatus;
    /**
     * 申込商品取消起因
     */
    appPrdtCancelCause?: string;
    /**
     * 商品コード
     */
    prdtCode: string;
    /**
     * 処理種別コード
     */
    processType: AppPrdt_ProcessType;
    /**
     * 商品種別コード
     */
    prdtTypeCode: PrdtTypeCode;
    /**
     * 金額
     */
    price?: number;
    /**
     * 課金開始年月日
     */
    billingStartDate?: string;
    /**
     * 課金終了年月日
     */
    billingEndDate?: string;
    /**
     * 商品通番
     */
    prdtSerialno: number;
    /**
     * 新商品通番
     */
    parentPrdtSerialno?: number;
    /**
     * 申込商品詳細リスト
     */
    appPrdtDtlList?: AppPrdtDtl[];
}

/**
 * (情報域)申込基本
 */
export interface AppBasic {
    /**
     * 受付ＩＤ
     */
    receiptId?: string;
    /**
     * 入力元識別
     *
     * 「01:MVNO顧客」固定
     */
    inputFromType: "01";
    /**
     * 受付ID通番
     */
    receiptIdSerialno?: number;
    /**
     * オーダ種別
     */
    orderType: OrderType;
    /**
     * 申込ステータス
     */
    appStatus: AppStatus;
    /**
     * 申込年月日
     */
    appDate: string;
    /**
     * 開廃希望年月日
     */
    requestDate: string;
    /**
     * 販売チャネルコード
     */
    salesChannelCode?: string;
    /**
     * 販売チャネル名
     */
    salesChannelName?: string;
    /**
     * 入力元受付ID
     */
    inputFromReceiptId: string;
    /**
     * 申込基本詳細リスト
     */
    appBasicDtlList: AppBasicDtl[];
}

/**
 * 申込情報
 */
interface AppInfo {
    /** (情報域)申込基本 */
    appBasic: AppBasic;
    /** (情報域)申込商品リスト */
    appPrdtList: AppPrdt[];
}

/**
 * 申込情報（without AppBasicDtlList）
 */
interface AppInfoWithoutAppBasicDtl {
    /** (情報域)申込基本 */
    appBasic: Omit<AppBasic, "appBasicDtlList">;
    /** (情報域)申込商品リスト */
    appPrdtList: AppPrdt[];
}

/**
 * （情報域）申込要求情報
 */
interface AppRequestInfo {
    /**
     * 処理識別
     */
    processType: AppRequestInfo_ProcessType;
    /**
     * （情報域）申込情報リスト
     */
    appInfoList: AppInfo[];
}

/**
 * （情報域）申込要求情報
 */
interface AppRequestInfoWithoutAppPrdtList {
    /**
     * 処理識別
     */
    processType: AppRequestInfo_ProcessType;
    /**
     * （情報域）申込情報リスト
     */
    appInfoList: Omit<AppInfo, "appPrdtList">[];
}

/**
 * （情報域）申込要求情報
 */
interface AppRequestInfoWithoutList {
    /**
     * 処理識別
     */
    processType: AppRequestInfo_ProcessType;
    /**
     * （情報域）申込情報リスト
     */
    appInfoList: Omit<AppInfoWithoutAppBasicDtl, "appPrdtList">[];
}

/**
 * （情報域）共通ヘッダ情報
 */
interface CoreSwimmyTransactionResponseHeader
    extends CoreSwimmyTransactionRequestHeader {
    /**
     * サービス処理結果識別
     */
    serviceProcessResultType: string;
    /**
     * サービス処理結果詳細識別
     */
    serviceProcessResultDtlType?: string;
}

interface AppBasicResponse extends Partial<AppBasic> {
    /**
     * 入力下受付ID
     */
    inputFromReceiptId?: string;
    /**
     * 登録者名
     */
    registName?: string;
    /**
     * サービス開始年月日
     */
    serviceStartDate?: string;
    /**
     * （情報域）申込紐付
     */
    appTying?: {
        /**
         * 契約受付ID
         */
        contractReceiptId?: string;
    };
}

/**
 * （情報域）申込商品リンクリスト
 */
interface AppPrdtLink {
    /**
     * 親アカウント商品ID
     */
    parentAccountPrdtId: string;
    /**
     * 子アカウント商品ID
     */
    childAccountPrdtId: string;
}

/**
 * エラー情報
 */
interface ErrorInfo {
    /**
     * チェックID
     */
    checkId: string;
    /**
     * エラーレベル
     */
    errorLevel: string;
    /**
     * メッセージID
     */
    messageId: string;
    /**
     * メッセージ
     */
    message: string;
    /**
     * （情報域）フィールド情報リスト
     */
    fieldInfoList?: {
        /**
         * フィールド名
         */
        fieldName: string;
    }[];
    /**
     * （情報域）埋め込み情報リスト
     */
    embedInfoList?: {
        /**
         * 埋め込み情報値
         */
        embedInfoValue: string;
    }[];
}

/**
 * （情報域）申込応答情報
 */
interface AppResponseInfo {
    /**
     * 処理識別
     */
    processType: string;
    /**
     * 結果コード
     */
    resultCode: string;
    /**
     * 申込情報リスト
     */
    appInfoList: {
        /** (情報域)申込基本 */
        appBasic?: AppBasicResponse;
        /** (情報域)申込商品リスト */
        appPrdtList?: AppPrdt[];
        /** （情報域）申込商品リンクリスト */
        appPrdtLinkList?: AppPrdtLink[];
        /**
         * （情報域）申込エラー情報
         */
        appErrorInfo?: {
            /**
             * （情報域）エラー情報リスト
             */
            errorInfoList: ErrorInfo[];
        };
        /**
         * （情報域）チェックエラー情報
         */
        checkErrorInfo?: {
            /**
             * （情報域）エラー情報リスト
             */
            errorInfoList: ErrorInfo[];
        };
    }[];
}

/**
 * Request * Headerの共通情報
 */
interface CoreSwimmyTransactionRequestCommon {
    /** 共通ヘッダ情報 */
    commonHeaderInfo: CoreSwimmyTransactionRequestHeader;
}

/**
 * リクエストベース
 */
export interface CoreSwimmyTransactionRequest
    extends CoreSwimmyTransactionRequestCommon {
    /**
     * （情報域）申込要求情報
     */
    appRequestInfo:
        | AppRequestInfo
        | AppRequestInfoWithoutAppPrdtList
        | AppRequestInfoWithoutList;
}

export interface CoreSwimmyTransactionResponse {
    /** 共通ヘッダ情報 */
    commonHeaderInfo: CoreSwimmyTransactionResponseHeader;
    /**
     * （情報域）申込応答情報
     */
    appResponseInfo?: AppResponseInfo;
}

/**
 * 回線グループ追加チャージ(クーポン)容量・期間追加のリクエスト
 */
export interface CoreSwimmyLineGroupAddAcquisitionRequest
    extends CoreSwimmyTransactionRequest {
    /**
     * （情報域）申込要求情報
     */
    appRequestInfo: AppRequestInfo;
}

/**
 * SIM再発行のリクエスト
 */
export interface CoreSwimmyReissueRequest extends CoreSwimmyTransactionRequest {
    /**
     * （情報域）申込要求情報
     */
    appRequestInfo: AppRequestInfo;
}

/**
 * 回線追加チャージ(クーポン)容量・期間追加のリクエスト
 */
export interface CoreSwimmyLineAddAcquisitionRequest extends CoreSwimmyTransactionRequest {
    /**
     * （情報域）申込要求情報
     */
    appRequestInfo: AppRequestInfo;
}

/**
 * 回線オプション変更のリクエスト
 */
export interface CoreSwimmyOptionRequest extends CoreSwimmyTransactionRequest {
    /**
     * （情報域）申込要求情報
     */
    appRequestInfo: AppRequestInfo;
}

/**
 * 回線廃止のリクエスト
 */
export interface CoreSwimmyDelRequest extends CoreSwimmyTransactionRequest {
    /**
     * （情報域）申込要求情報
     */
    appRequestInfo: AppRequestInfoWithoutAppPrdtList;
}

/**
 * アクセス方式変更のリクエスト
 */
export interface CoreSwimmyNWContractRequest
    extends CoreSwimmyTransactionRequest {
    /**
     * （情報域）申込要求情報
     */
    appRequestInfo: AppRequestInfo;
}

export interface CoreSwimmyFrontSoCancelRequest
    extends CoreSwimmyTransactionRequest {
    /**
     * （情報域）申込要求情報
     */
    appRequestInfo: AppRequestInfoWithoutList;
}

export interface CoreSwimmySoCancelRequest
    extends CoreSwimmyTransactionRequest {
    /**
     * （情報域）申込要求情報
     */
    appRequestInfo: AppRequestInfoWithoutList;
}

export interface CoreSwimmyLineGroupModifyAcquisitionRequest
    extends CoreSwimmyTransactionRequest {
    /**
     * （情報域）申込要求情報
     */
    appRequestInfo: AppRequestInfoWithoutList;
}

export interface CoreSwimmyPreLineAddRequest
    extends CoreSwimmyTransactionRequest {
    /**
     * （情報域）申込要求情報
     */
    appRequestInfo: AppRequestInfo;
}

export interface SameMvneHaishiRequest extends CoreSwimmyTransactionRequest {
    /**
     * （情報域）申込要求情報
     */
    appRequestInfo: AppRequestInfoWithoutAppPrdtList;
}

/**
 * 黒化
 */
export interface CoreSwimmyLineEnableRequest
    extends CoreSwimmyTransactionRequest {
    /**
     * （情報域）申込要求情報
     */
    appRequestInfo: AppRequestInfo;
}

export interface CoreSwimmyPlanChangeRequest
    extends CoreSwimmyTransactionRequest {
    /**
     * （情報域）申込要求情報
     */
    appRequestInfo: AppRequestInfo;
}

/**
 * Just a sample response
 */
const ResponseSample = {
    commonHeaderInfo: {
        requestFromSystemId: "MAP",
        otherSystemSendDatetime: "20170101000000",
        operatorId: "SHIBA",
        operatorGroupId: "soukan_check",
        serviceProcessResultType: "0",
        serviceProcessResultDtlType: null,
    },
    appResponseInfo: {
        processType: "03",
        resultCode: "0001",
        appInfoList: [
            {
                appBasic: {
                    receiptId: "UK1706150000236",
                    inputFromType: "01",
                    receiptIdSerialno: 1,
                    orderType: "11",
                    appStatus: "15",
                    appDate: "20190826",
                    requestDate: "20190826",
                    salesChannelCode: "79100100",
                    salesChannelName: "金沢OCNサービスセンタ",
                    inputFromReceiptId: null,
                    registName: "ユーザ ３０００１",
                    serviceStartDate: null,
                    firstRequestFromSystemId: "MAP",
                    lastRequestFromSystemId: "MAP",
                    appBasicDtlList: [
                        {
                            appAttributeCode: "M0000001",
                            appAttributeValue: "A17000108",
                        },
                        {
                            appAttributeCode: "M0000004",
                            appAttributeValue: "20170529",
                        },
                        {
                            appAttributeCode: "M0000005",
                            appAttributeValue: "ユーザ ３０００１",
                        },
                        {
                            appAttributeCode: "M0000006",
                            appAttributeValue: "20170615",
                        },
                        {
                            appAttributeCode: "M0000007",
                            appAttributeValue: "ユーザ ３０００１",
                        },
                        {
                            appAttributeCode: "M0000013",
                            appAttributeValue: "N072061303",
                        },
                        {
                            appAttributeCode: "M0000002",
                            appAttributeValue: "012",
                        },
                        {
                            appAttributeCode: "M0000012",
                            appAttributeValue: "***********",
                        },
                    ],
                    appTying: {
                        contractReceiptId: "***************",
                    },
                },
                appContract: null,
                appPrdtList: [
                    {
                        accountId: "AC00000000082",
                        accountPrdtId: "**************************",
                        appPrdtStatus: "01",
                        appPrdtCancelCause: null,
                        prdtCode: "T1000001",
                        processType: "01",
                        prdtTypeCode: "0001",
                        price: "0",
                        billingStartDate: null,
                        billingEndDate: null,
                        prdtSerialno: 1,
                        parentPrdtSerialno: 0,
                        appPrdtDtlList: [
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "3",
                            },
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "0",
                            },
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "0",
                            },
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "1",
                            },
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "********",
                            },
                        ],
                    },
                    {
                        accountId: "AC00000000082",
                        accountPrdtId: "**************************",
                        appPrdtStatus: "01",
                        appPrdtCancelCause: null,
                        prdtCode: "T2000086",
                        processType: "01",
                        prdtTypeCode: "0002",
                        price: "0",
                        billingStartDate: null,
                        billingEndDate: null,
                        prdtSerialno: 2,
                        parentPrdtSerialno: 1,
                        appPrdtDtlList: [
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "***************",
                            },
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "***********",
                            },
                            {
                                prdtAttributeCode: "00000015",
                                prdtAttributeValue: "20131127",
                            },
                            {
                                prdtAttributeCode: "00000008",
                                prdtAttributeValue: "0",
                            },
                            {
                                prdtAttributeCode: "00000010",
                                prdtAttributeValue: "1",
                            },
                            {
                                prdtAttributeCode: "J1000001",
                                prdtAttributeValue: "1",
                            },
                            {
                                prdtAttributeCode: "J1000401",
                                prdtAttributeValue: "13101005001",
                            },
                            {
                                prdtAttributeCode: "J1000301",
                                prdtAttributeValue: "1000011",
                            },
                            {
                                prdtAttributeCode: "J1000501",
                                prdtAttributeValue: "１",
                            },
                            {
                                prdtAttributeCode: "J1000601",
                                prdtAttributeValue: "ビル",
                            },
                            {
                                prdtAttributeCode: "00000018",
                                prdtAttributeValue: "金",
                            },
                            {
                                prdtAttributeCode: "00000019",
                                prdtAttributeValue: "契約者部課名３",
                            },
                            {
                                prdtAttributeCode: "00000020",
                                prdtAttributeValue: "事務担当者氏名３",
                            },
                            {
                                prdtAttributeCode: "00000021",
                                prdtAttributeValue: "0123456789",
                            },
                            {
                                prdtAttributeCode: "00000016",
                                prdtAttributeValue: "20170530",
                            },
                            {
                                prdtAttributeCode: "00000017",
                                prdtAttributeValue: "4",
                            },
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "********",
                            },
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "********",
                            },
                        ],
                    },
                    {
                        accountId: "AC00000000082",
                        accountPrdtId: "**************************",
                        appPrdtStatus: "01",
                        appPrdtCancelCause: null,
                        prdtCode: "T5000001",
                        processType: "01",
                        prdtTypeCode: "0005",
                        price: "0",
                        billingStartDate: null,
                        billingEndDate: null,
                        prdtSerialno: 5,
                        parentPrdtSerialno: 1,
                        appPrdtDtlList: [
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "0",
                            },
                        ],
                    },
                    {
                        accountId: "AC00000000082",
                        accountPrdtId: "**************************",
                        appPrdtStatus: "01",
                        appPrdtCancelCause: null,
                        prdtCode: "T5000002",
                        processType: "01",
                        prdtTypeCode: "0005",
                        price: "0",
                        billingStartDate: null,
                        billingEndDate: null,
                        prdtSerialno: 6,
                        parentPrdtSerialno: 1,
                        appPrdtDtlList: [
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "1",
                            },
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "0",
                            },
                        ],
                    },
                    {
                        accountId: "AC00000000082",
                        accountPrdtId: "**************************",
                        appPrdtStatus: "01",
                        appPrdtCancelCause: null,
                        prdtCode: "T5000003",
                        processType: "01",
                        prdtTypeCode: "0005",
                        price: "0",
                        billingStartDate: null,
                        billingEndDate: null,
                        prdtSerialno: 7,
                        parentPrdtSerialno: 1,
                        appPrdtDtlList: [
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "0",
                            },
                        ],
                    },
                    {
                        accountId: "AC00000000082",
                        accountPrdtId: "**************************",
                        appPrdtStatus: "01",
                        appPrdtCancelCause: null,
                        prdtCode: "T5000004",
                        processType: "01",
                        prdtTypeCode: "0005",
                        price: "0",
                        billingStartDate: null,
                        billingEndDate: null,
                        prdtSerialno: 8,
                        parentPrdtSerialno: 1,
                        appPrdtDtlList: [
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "1",
                            },
                        ],
                    },
                    {
                        accountId: "AC00000000082",
                        accountPrdtId: "**************************",
                        appPrdtStatus: "01",
                        appPrdtCancelCause: null,
                        prdtCode: "T5000005",
                        processType: "01",
                        prdtTypeCode: "0005",
                        price: "0",
                        billingStartDate: null,
                        billingEndDate: null,
                        prdtSerialno: 9,
                        parentPrdtSerialno: 1,
                        appPrdtDtlList: [
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "0",
                            },
                        ],
                    },
                    {
                        accountId: "AC00000000082",
                        accountPrdtId: "**************************",
                        appPrdtStatus: "01",
                        appPrdtCancelCause: null,
                        prdtCode: "TA000001",
                        processType: "01",
                        prdtTypeCode: "0010",
                        price: "0",
                        billingStartDate: null,
                        billingEndDate: null,
                        prdtSerialno: 13,
                        parentPrdtSerialno: 1,
                        appPrdtDtlList: [
                            {
                                prdtAttributeCode: "S1000001",
                                prdtAttributeValue: "1",
                            },
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "",
                            },
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "",
                            },
                        ],
                    },
                    {
                        accountId: "AC00000000082",
                        accountPrdtId: "**************************",
                        appPrdtStatus: "01",
                        appPrdtCancelCause: null,
                        prdtCode: "T9000001",
                        processType: "01",
                        prdtTypeCode: "0009",
                        price: "0",
                        billingStartDate: null,
                        billingEndDate: null,
                        prdtSerialno: 14,
                        parentPrdtSerialno: 1,
                        appPrdtDtlList: [
                            {
                                prdtAttributeCode: "S1000001",
                                prdtAttributeValue: "1",
                            },
                            {
                                prdtAttributeCode: "S1000002",
                                prdtAttributeValue: "0",
                            },
                        ],
                    },
                    {
                        accountId: "AC00000000082",
                        accountPrdtId: "**************************",
                        appPrdtStatus: "01",
                        appPrdtCancelCause: null,
                        prdtCode: "T9000002",
                        processType: "01",
                        prdtTypeCode: "0009",
                        price: "0",
                        billingStartDate: null,
                        billingEndDate: null,
                        prdtSerialno: 15,
                        parentPrdtSerialno: 1,
                        appPrdtDtlList: [
                            {
                                prdtAttributeCode: "S1000001",
                                prdtAttributeValue: "1",
                            },
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "0",
                            },
                        ],
                    },
                    {
                        accountId: "AC00000000082",
                        accountPrdtId: "**************************",
                        appPrdtStatus: "01",
                        appPrdtCancelCause: null,
                        prdtCode: "T9000003",
                        processType: "01",
                        prdtTypeCode: "0009",
                        price: "0",
                        billingStartDate: null,
                        billingEndDate: null,
                        prdtSerialno: 16,
                        parentPrdtSerialno: 1,
                        appPrdtDtlList: [
                            {
                                prdtAttributeCode: "S1000003",
                                prdtAttributeValue: "0",
                            },
                            {
                                prdtAttributeCode: "S1000004",
                                prdtAttributeValue: "1",
                            },
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "0",
                            },
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "0",
                            },
                        ],
                    },
                    {
                        accountId: "AC00000000082",
                        accountPrdtId: "**************************",
                        appPrdtStatus: "01",
                        appPrdtCancelCause: null,
                        prdtCode: "T5000007",
                        processType: "01",
                        prdtTypeCode: "0005",
                        price: "0",
                        billingStartDate: null,
                        billingEndDate: null,
                        prdtSerialno: 19,
                        parentPrdtSerialno: 1,
                        appPrdtDtlList: [
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "0",
                            },
                        ],
                    },
                    {
                        accountId: "AC00000000082",
                        accountPrdtId: "**************************",
                        appPrdtStatus: "01",
                        appPrdtCancelCause: null,
                        prdtCode: "T3000518",
                        processType: "01",
                        prdtTypeCode: "0003",
                        price: "0",
                        billingStartDate: null,
                        billingEndDate: null,
                        prdtSerialno: 3,
                        parentPrdtSerialno: 1,
                        appPrdtDtlList: [
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "10",
                            },
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "2",
                            },
                        ],
                    },
                    {
                        accountId: "AC00000000082",
                        accountPrdtId: "**************************",
                        appPrdtStatus: "01",
                        appPrdtCancelCause: null,
                        prdtCode: "T4000001",
                        processType: "01",
                        prdtTypeCode: "0004",
                        price: "0",
                        billingStartDate: null,
                        billingEndDate: null,
                        prdtSerialno: 4,
                        parentPrdtSerialno: 1,
                        appPrdtDtlList: [
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "1",
                            },
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "********",
                            },
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "2100",
                            },
                            {
                                prdtAttributeCode: "00000028",
                                prdtAttributeValue: "2300",
                            },
                            {
                                prdtAttributeCode: "C0000001",
                                prdtAttributeValue: "d072061303",
                            },
                            {
                                prdtAttributeCode: "C0000002",
                                prdtAttributeValue: "ID99999",
                            },
                            {
                                prdtAttributeCode: "P0000001",
                                prdtAttributeValue: "pass",
                            },
                            {
                                prdtAttributeCode: "00000029",
                                prdtAttributeValue: "201.15.26.142",
                            },
                            {
                                prdtAttributeCode: "00000038",
                                prdtAttributeValue: "1",
                            },
                            {
                                prdtAttributeCode: "00000090",
                                prdtAttributeValue: "********2100",
                            },
                            {
                                prdtAttributeCode: "00000091",
                                prdtAttributeValue: "********2300",
                            },
                            {
                                prdtAttributeCode: "00000034",
                                prdtAttributeValue: "MF121",
                            },
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "***************",
                            },
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "***************",
                            },
                        ],
                    },
                    {
                        accountId: "AC00000000082",
                        accountPrdtId: "**************************",
                        appPrdtStatus: "01",
                        appPrdtCancelCause: null,
                        prdtCode: "T7000001",
                        processType: "01",
                        prdtTypeCode: "0007",
                        price: "0",
                        billingStartDate: null,
                        billingEndDate: null,
                        prdtSerialno: 12,
                        parentPrdtSerialno: 1,
                        appPrdtDtlList: [
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "12",
                            },
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "***********",
                            },
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "14521234",
                            },
                            {
                                prdtAttributeCode: "00000057",
                                prdtAttributeValue: "代理店３",
                            },
                            {
                                prdtAttributeCode: "00000059",
                                prdtAttributeValue: "NTTコム",
                            },
                            {
                                prdtAttributeCode: "00000060",
                                prdtAttributeValue: "お客様対応部門担当",
                            },
                            {
                                prdtAttributeCode: "00000061",
                                prdtAttributeValue: "0123456781",
                            },
                            {
                                prdtAttributeCode: "00000063",
                                prdtAttributeValue: "<EMAIL>",
                            },
                            {
                                prdtAttributeCode: "00000064",
                                prdtAttributeValue: "申込受付部門担当者",
                            },
                            {
                                prdtAttributeCode: "00000065",
                                prdtAttributeValue: "0123456789",
                            },
                            {
                                prdtAttributeCode: "00000067",
                                prdtAttributeValue: "<EMAIL>",
                            },
                            {
                                prdtAttributeCode: "00000082",
                                prdtAttributeValue: "ｷﾝ",
                            },
                            {
                                prdtAttributeCode: "00000081",
                                prdtAttributeValue: "金",
                            },
                            {
                                prdtAttributeCode: "00000083",
                                prdtAttributeValue: "契約者部課名３",
                            },
                            {
                                prdtAttributeCode: "J4000001",
                                prdtAttributeValue: "1",
                            },
                            {
                                prdtAttributeCode: "J4000401",
                                prdtAttributeValue: "13101005001",
                            },
                            {
                                prdtAttributeCode: "J4000301",
                                prdtAttributeValue: "1000011",
                            },
                            {
                                prdtAttributeCode: "J4000501",
                                prdtAttributeValue: "１",
                            },
                            {
                                prdtAttributeCode: "J4000601",
                                prdtAttributeValue: "ビル",
                            },
                            {
                                prdtAttributeCode: "00000084",
                                prdtAttributeValue: "事務担当者氏名３",
                            },
                            {
                                prdtAttributeCode: "00000085",
                                prdtAttributeValue: "0123456789",
                            },
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "<EMAIL>",
                            },
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "ご利用事業所名３",
                            },
                        ],
                    },
                    {
                        accountId: "AC00000000082",
                        accountPrdtId: "**************************",
                        appPrdtStatus: "01",
                        appPrdtCancelCause: null,
                        prdtCode: "TB000001",
                        processType: "01",
                        prdtTypeCode: "0011",
                        price: "0",
                        billingStartDate: null,
                        billingEndDate: null,
                        prdtSerialno: 17,
                        parentPrdtSerialno: 1,
                        appPrdtDtlList: [
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "カード納品記事欄",
                            },
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "記事欄１",
                            },
                            {
                                prdtAttributeCode: "00000073",
                                prdtAttributeValue: "記事欄２",
                            },
                            {
                                prdtAttributeCode: "00000074",
                                prdtAttributeValue: "記事欄３",
                            },
                            {
                                prdtAttributeCode: "00000075",
                                prdtAttributeValue: "予備１",
                            },
                            {
                                prdtAttributeCode: "00000076",
                                prdtAttributeValue: "予備２",
                            },
                            {
                                prdtAttributeCode: "00000077",
                                prdtAttributeValue: "予備３",
                            },
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "予備４",
                            },
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "予備５",
                            },
                        ],
                    },
                    {
                        accountId: "AC00000000082",
                        accountPrdtId: "**************************",
                        appPrdtStatus: "01",
                        appPrdtCancelCause: null,
                        prdtCode: "T9000004",
                        processType: "01",
                        prdtTypeCode: "0009",
                        price: null,
                        billingStartDate: null,
                        billingEndDate: null,
                        prdtSerialno: 901,
                        parentPrdtSerialno: 1,
                        appPrdtDtlList: [
                            {
                                prdtAttributeCode: "S1000001",
                                prdtAttributeValue: "0",
                            },
                        ],
                    },
                    {
                        accountId: "AC00000000082",
                        accountPrdtId: "**************************",
                        appPrdtStatus: "01",
                        appPrdtCancelCause: null,
                        prdtCode: "TA000002",
                        processType: "01",
                        prdtTypeCode: "0010",
                        price: null,
                        billingStartDate: null,
                        billingEndDate: null,
                        prdtSerialno: 902,
                        parentPrdtSerialno: 1,
                        appPrdtDtlList: [
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "",
                            },
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "",
                            },
                        ],
                    },
                ],
                appPrdtLinkList: [
                    {
                        parentAccountPrdtId: "**************************",
                        childAccountPrdtId: "**************************",
                    },
                    {
                        parentAccountPrdtId: "**************************",
                        childAccountPrdtId: "**************************",
                    },
                    {
                        parentAccountPrdtId: "**************************",
                        childAccountPrdtId: "**************************",
                    },
                    {
                        parentAccountPrdtId: "**************************",
                        childAccountPrdtId: "**************************",
                    },
                    {
                        parentAccountPrdtId: "**************************",
                        childAccountPrdtId: "**************************",
                    },
                    {
                        parentAccountPrdtId: "**************************",
                        childAccountPrdtId: "**************************",
                    },
                    {
                        parentAccountPrdtId: "**************************",
                        childAccountPrdtId: "**************************",
                    },
                    {
                        parentAccountPrdtId: "**************************",
                        childAccountPrdtId: "**************************",
                    },
                    {
                        parentAccountPrdtId: "**************************",
                        childAccountPrdtId: "**************************",
                    },
                    {
                        parentAccountPrdtId: "**************************",
                        childAccountPrdtId: "**************************",
                    },
                    {
                        parentAccountPrdtId: "**************************",
                        childAccountPrdtId: "**************************",
                    },
                    {
                        parentAccountPrdtId: "**************************",
                        childAccountPrdtId: "**************************",
                    },
                    {
                        parentAccountPrdtId: "**************************",
                        childAccountPrdtId: "**************************",
                    },
                    {
                        parentAccountPrdtId: "**************************",
                        childAccountPrdtId: "**************************",
                    },
                    {
                        parentAccountPrdtId: "**************************",
                        childAccountPrdtId: "**************************",
                    },
                    {
                        parentAccountPrdtId: "**************************",
                        childAccountPrdtId: "**************************",
                    },
                    {
                        parentAccountPrdtId: "**************************",
                        childAccountPrdtId: "**************************",
                    },
                ],
                appErrorInfo: {
                    errorInfoList: [],
                },
                checkErrorInfo: null,
            },
        ],
    },
};
