import ResponseHeader from "@/core/dto/ResponseHeader";
import { InvocationContext } from "@azure/functions";

export default interface ExtendedInvocationContext
    extends InvocationContext {
    /**
     * request.json() content
     *
     * since it is already consumed in BaseHandler, need to store it in context
     * so that it can be accessed by function handler later
     */
    jsonBody: any;

    /** result from 共通チェック */
    responseHeader: ResponseHeader;

    /**
     * Flag whether the request was sent by Function App or external source (via API Gateway)
     */
    isInternalRequest: boolean;
}
