// use type to avoid typos
type CoreMvnoConfigKey =
    | "ReservationDateExecutionUnits"
    | "ReservationsLimitDays"
    // | "ApiProcessIDServer"
    // | "AllowedSourceIpAddress"
    | "ServerConnectionLimit"
    // | "CancelReservationDisableDays"
    | "TpcSourceIpAddress"
    | "TpcDestIpAddress"
    | "TpcDestIpAddress2"
    | "TpcDestIpAddress3"
    | "TpcDestServiceName"
    | "TpcLiteRequestURI"
    // | "TpcTimeout"
    // | "SOAP"
    // | "TotalVolumeControlInJudgmentString"
    // | "TotalVolumeControlOutJudgmentString"
    // | "HeavyUserMonitorStatusUnregulatedString"
    // | "HeavyUserMonitorStatusRegulatedatLastString"
    // | "HeavyUserMonitorStatusRegulatedatMonthlyString"
    // | "HeavyUserMonitorStatusRegulatedatLastMonthString"
    // | "ApDbRetryMaxCnt"
    // | "ApDbRetryInterval"
    | "PreLineAddNgTime"
    | "GroupPlanChangeNgTime"
    | "PlanChangeNgTime";

/**
 * Map config key and environment variable (for local config)
 */
export const CoreMvnoLocalConfigMap: Record<CoreMvnoConfigKey, string> = {
    ReservationDateExecutionUnits: "CORE_MVNO_ReservationDateExecutionUnits",
    ReservationsLimitDays: "CORE_MVNO_ReservationsLimitDays",
    ServerConnectionLimit: "CORE_MVNO_ServerConnectionLimit",
    TpcSourceIpAddress: "CORE_MVNO_TpcSourceIpAddress",
    TpcDestIpAddress: "CORE_MVNO_TpcDestIpAddress",
    TpcDestIpAddress2: "CORE_MVNO_TpcDestIpAddress2",
    TpcDestIpAddress3: "CORE_MVNO_TpcDestIpAddress3",
    TpcDestServiceName: "CORE_MVNO_TpcDestServiceName",
    TpcLiteRequestURI: "CORE_MVNO_TpcLiteRequestURI",
    PreLineAddNgTime: "CORE_MVNO_PreLineAddNgTime",
    GroupPlanChangeNgTime: "CORE_MVNO_GroupPlanChangeNgTime",
    PlanChangeNgTime: "CORE_MVNO_PlanChangeNgTime",
};

export default CoreMvnoConfigKey;
