import { AxiosError } from "axios";

export interface SOAPExceptionOptions {
    cause?: Error;
    code?: string;
    details?: any;
}

export default class SOAPException extends Error {
    public code?: string;
    public details?: any;
    public cause?: Error;

    constructor(
        message: string | Error | AxiosError,
        options: SOAPExceptionOptions = {},
    ) {
        if (message instanceof Error) {
            super(message.message);
            this.name = message.name;
            this.stack = message.stack;
        } else {
            super(message);
        }
        // Set the prototype explicitly.
        // https://github.com/microsoft/TypeScript-wiki/blob/main/Breaking-Changes.md
        Object.setPrototypeOf(this, SOAPException.prototype);

        this.name = this.constructor.name; // Set the name property to the name of the custom error class
        this.code = options.code;
        this.details = options.details;
        this.cause = options.cause;

        // Preserve the stack trace
        // if (Error.captureStackTrace) {
        //     Error.captureStackTrace(this, this.constructor);
        // }
    }

    /**
     * Check if the error is an instance of SOAPException.
     * Because it can be thrown during request/response handling, also check if it is an instance of AxiosError
     * @param error
     * @returns
     */
    public static isSOAPException(
        error: any,
    ): error is SOAPException | AxiosError {
        return error instanceof SOAPException || error instanceof AxiosError;
    }

    /**
     * Check if the error is an instance of SOAPException.
     * Because it can be thrown during request/response handling, also check if it is an instance of AxiosError
     * @alias isSOAPException
     * @param error
     * @returns
     */
    public static isIt = SOAPException.isSOAPException;
}
