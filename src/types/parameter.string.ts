/**
 * Get correct string value from numeric parameter.
 * The specification requires that the field to be a string but need to handle cases where a number
 * is passed. If the value is not a number or not a string, then return as is.
 *
 * @example
 * getStringParameter("123") -> "123"
 * getStringParameter(100) -> "100"
 * getStringParameter("456abc") -> "456abc"
 * getStringParameter("") -> ""
 * getStringParameter(" ") -> " "
 * getStringParameter(undefined) -> undefined
 * getStringParameter(null) -> null
 * getStringParameter(NaN) -> null
 * getStringParameter(-99999999999) -> "-99999999999"
 * getStringParameter(99999999999) -> "99999999999"
 *
 * @param value The parameter value which is either string or number.
 * @returns The string value of the parameter.
 */
export function getStringParameter(value: string | number): string {
    if (value === undefined) {
        return undefined;
    }
    if (value === null) {
        return null;
    }
    if (typeof value === "number") {
        return String(value);
    }
    // if (typeof value !== "string") {
    //     throw new Error(`Invalid type for parameter: expected string or number, got ${typeof value}`);
    // }
    return value;
}
