import { isNone } from "@/utils";

export default class CoreSwimmyResponseHelper {
    /**
     * サービス処理結果識別と結果コードを取得
     */
    public static getResultCode(response: any) {
        const serviceProcessResultType: string =
            response?.commonHeaderInfo?.serviceProcessResultType;
        const resultCode: string = response?.appResponseInfo?.resultCode;
        return { resultCode, serviceProcessResultType };
    }

    /**
     * サービス処理結果詳細識別を取得
     */
    public static getProcessResultDtlType(response: any): string | undefined {
        return response?.commonHeaderInfo?.serviceProcessResultDtlType;
    }

    public static getErrorMessages(response: any) {
        const errorMessages: string[] = [];

        if (Array.isArray(response?.appResponseInfo?.appInfoList)) {
            for (const row of response.appResponseInfo.appInfoList) {
                if (
                    !isNone(row?.appErrorInfo) &&
                    Array.isArray(row?.appErrorInfo?.errorInfoList)
                ) {
                    for (const error of row.appErrorInfo.errorInfoList) {
                        if (!isNone(error?.message)) {
                            errorMessages.push(error.message);
                        }
                    }
                }
            }
        }

        return errorMessages;
    }
}
