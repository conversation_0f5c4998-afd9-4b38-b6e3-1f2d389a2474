import { HEADER_KEY } from "@/constants/headerKeys";
import { SessionData, UserRole, UserSession } from "@/types/Session";
import { InvocationContext, HttpRequest } from "@azure/functions";

export const getSessionData = (
    context: InvocationContext,
    req: HttpRequest,
): SessionData => {
    const roleId = parseInt(req.headers.get(HEADER_KEY.ROLE_ID), 10) as UserRole;
    const tenantId = req.headers.get(HEADER_KEY.TENANT_ID);
    /** @type {string} `tempoId` for tempo user (empty string for non tempo user) */
    const tempoId = req.headers.get(HEADER_KEY.TEMPO_ID) ?? "";

    const decodedUserName = req.headers.get(HEADER_KEY.USER_NAME)
        ? Buffer.from(
              req.headers.get(HEADER_KEY.USER_NAME) ?? "",
              "base64",
          ).toString("utf-8")
        : "";
    context.log(`decodedUserName: ${decodedUserName}`);
    const user: UserSession = {
        /** @type {string} user's `_id` */
        internalId: req.headers.get(HEADER_KEY.USER_ID) ?? "",
        /** @type {string} user's `plusId` */
        plusId: req.headers.get(HEADER_KEY.USER_PLUS_ID) ?? "",
        /** @type {string} user's `name` */
        name: decodedUserName,
        /** @type {boolean|undefined} undefined in case role is not tempo user */
        maskPhoneNumber: req.headers.get(HEADER_KEY.MASK_PHONE_NUMBER) === "true",
        /** @type {boolean|undefined} user's password is expired or not */
        passwordExpired: req.headers.get(HEADER_KEY.PASSWORD_EXPIRED) === "true",
    };

    return { roleId, tenantId, tempoId, user };
};

/**
 * Check whether the request is sent using function URL not via API Gateway.
 *
 * This is used to determine if the request is from the same function app or from an external source.
 * @param request request object
 * @returns {boolean} true if the request is from the same function app, false otherwise
 */
export const isRequestFromSelf = (request: HttpRequest): boolean => {
    const requestId = request.headers.get(HEADER_KEY.REQUEST_ID);
    return requestId === undefined || requestId === null || requestId === "";
}
