import { InvocationContext } from "@azure/functions";
import Sequelize from "sequelize";

/**
 * DBアクセスリトライ処理
 *
 * NOTE: Maximum number of calls will be `apDbRetryMaxCnt + 1`
 * @param context InvocationContext
 * @param caller 呼び出し元の関数名
 * @param fn クエリ実行関数
 * @param apDbRetryMaxCnt アプリケーション用DBリトライ最大回数
 * @param apDbRetryInterval プリケーション用DBリトライ間隔
 * @returns クエリ実行の結果
 */
export async function retryQuery<T>(
    context: InvocationContext,
    caller: string,
    fn: () => Promise<T>,
    apDbRetryMaxCnt: number,
    apDbRetryInterval: number,
): Promise<T> {
    // NOTE we don't have `PersistenceException` in JS so error not related to Sequelize will be thrown as is
    let retryCnt = 0;
    while (true) {
        try {
            const result = await fn();
            return result;
        } catch (ex) {
            if (isSQLException(ex)) {
                // DBアクセス時にエラー発生した場合
                if (retryCnt < apDbRetryMaxCnt) {
                    context.warn(`[retryQuery] ${caller} failed:`, ex);
                } else {
                    context.error(
                        `[retryQuery] ${caller} failed after ${retryCnt} retries:`,
                        [ex.name ?? "", ex.message ?? ""]
                            .filter((r) => r)
                            .join(": "),
                    );
                    throw ex;
                }
            } else {
                // not DB related error, no need to retry
                context.error(
                    `[retryQuery] ${caller} failed with non Sequelize error:`,
                    ex,
                );
                throw ex;
            }

            // リトライカウントをインクリメント
            retryCnt++;

            try {
                // 指定時間待機後にリトライ
                const sleepTime =
                    apDbRetryInterval * retryCnt * retryCnt * 1000;
                context.info(
                    `[retryQuery] ${caller} failed. Wait ${sleepTime} ms ... ${retryCnt}/${apDbRetryMaxCnt}`,
                );
                await new Promise((resolve) => setTimeout(resolve, sleepTime));
            } catch (e) {
                context.error(`[retryQuery] ${caller} failed to sleep:`, e);
            }
        }
    }
}

/**
 * Check if the error is database related error
 * @param error error object
 * @returns
 */
export function isSQLException(error: any): error is Sequelize.Error {
    return error instanceof Sequelize.Error;
}
