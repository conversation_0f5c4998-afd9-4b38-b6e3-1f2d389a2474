import CardEntity from "@/core/entity/CardEntity";
import { CustomerInfoEntity } from "@/core/entity/CustomerInfoEntity";
import LineOptionsEntity from "@/core/entity/LineOptionsEntity";

/**
 * T番の変換を行うヘルパークラス
 */
export default class TBanHelper {
    /**
     * NW暗証番号のT番オプションを取得する
     * @param nNo 顧客のN番
     */
    public static async getNWOptionTBan(nNo: string): Promise<string | null> {
        if (!nNo) return null;
        const customerInfo = await CustomerInfoEntity.findOne({
            where: {
                nnumber: nNo,
            },
        });
        if (customerInfo) {
            return customerInfo.nwOptionIdT;
        }
        return null;
    }

    /**
     * 回線オプションのAoxxx等からT番に変換する
     * @param lineOption Aで始まる回線オプション
     */
    public static async getLineOptionTBan(
        lineOption: string,
    ): Promise<string | null> {
        const option = await LineOptionsEntity.findOne({
            where: {
                lineOptionId: lineOption,
            },
        });
        if (option) {
            return option.lineOptionIdT;
        }
        return null;
    }

    /**
     * カード種別IDからT番を取得する
     * @param deviceTypeId カード種別ID
     */
    public static async getDeviceTypeTBan(
        deviceTypeId: string,
    ): Promise<string | null> {
        const cardInfo = await CardEntity.findOne({
            where: {
                deviceTypeId,
            },
        });
        if (cardInfo) {
            return cardInfo.deviceTypeIdT;
        }
        return null;
    }
}
