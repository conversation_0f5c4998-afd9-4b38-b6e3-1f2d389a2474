import ExtendedPortalInvocationContext from "@/types/ExtendedPortalInvocationContext";
import { UserRole } from "@/types/Session";
import { HttpRequest, HttpResponseInit } from "@azure/functions";

export default abstract class PortalFunctionHandlerBase {
    /**
     * only allowed roles can access this API
     */
    public static AllowedRoles: UserRole[] = [];

    /**
     * API handler which will be called after BasePortalHandler
     */
    public static async Handler(
        request: HttpRequest,
        context: ExtendedPortalInvocationContext,
    ): Promise<HttpResponseInit> {
        throw new Error("Method not implemented.");
    }
}