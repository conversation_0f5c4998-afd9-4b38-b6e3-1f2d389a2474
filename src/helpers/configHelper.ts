import AppConfig from "@/appconfig";
import CoreMvnoConfigKey from "@/types/coreMvnoConfig";

/**
 * Get the Core MVNO configuration value by key or return the default value
 * @param key
 * @param defaultValue
 * @returns
 */
export function tryGetConfig(
    key: CoreMvnoConfigKey,
    defaultValue: any = null,
): string {
    try {
        return AppConfig.getCoreMvnoConfig(key);
    } catch (e) {
        return defaultValue;
    }
}

/**
 * Get the Core MVNO configuration value by key
 * @param key
 * @returns
 */
export function getConfig(key: CoreMvnoConfigKey): string {
    return AppConfig.getCoreMvnoConfig(key);
}

/**
 * Get the Core MVNO configuration value by key as a number
 * @param key
 * @returns
 */
export function getConfigAsNumber(key: CoreMvnoConfigKey): number {
    return parseInt(getConfig(key), 10);
}
