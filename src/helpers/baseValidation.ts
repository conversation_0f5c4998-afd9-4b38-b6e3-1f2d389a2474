import Joi from "joi";

import BaseValidationResult from "@/types/baseValidationResult";
import { CORE_ERROR_MESSAGES } from "@/constants/coreErrorMessages";
import SOManagerInputDto from "@/core/dto/SOManagerInputDto";
import { isNone } from "@/utils";

// add other fields related to CoreV2 API if needed
const BaseRequestSchema = Joi.object({
    targetSoId: Joi.string().optional().messages({
        "string.base": CORE_ERROR_MESSAGES.TARGET_SOID_INVALID,
        "string.empty": CORE_ERROR_MESSAGES.TARGET_SOID_EMPTY,
        // "any.required": CORE_ERROR_MESSAGES.TARGET_SOID_EMPTY,
    }),
}).unknown();

/**
 * Validate request body for common fields (specific to CoreAPI v2)
 */
export default function validateBaseRequest<T extends SOManagerInputDto>(
    jsonBody: T,
): BaseValidationResult {
    const result: BaseValidationResult = {
        checkResult: true,
        ngReason: "",
    };

    if (isNone(jsonBody) || typeof jsonBody !== "object") {
        result.checkResult = false;
        result.ngReason = CORE_ERROR_MESSAGES.EMPTY_REQUEST_BODY;
        return result;
    }

    const { error } = BaseRequestSchema.validate(jsonBody);
    if (error) {
        result.checkResult = false;
        result.ngReason = error.details[0].message;
    }

    return result;
}
