import Joi from "joi";

import BaseValidationResult from "@/types/baseValidationResult";
import { CORE_ERROR_MESSAGES } from "@/constants/coreErrorMessages";
import SOManagerInputDto from "@/core/dto/SOManagerInputDto";
import { isNone } from "@/utils";

/**
 * Validate request body for common fields (specific to CoreAPI v2)
 */
export default function validateBaseRequest<T extends SOManagerInputDto>(
    jsonBody: T,
    schema: Joi.ObjectSchema,
): BaseValidationResult {
    const result: BaseValidationResult = {
        checkResult: true,
        ngReason: "",
    };

    if (isNone(jsonBody) || typeof jsonBody !== "object") {
        result.checkResult = false;
        result.ngReason = CORE_ERROR_MESSAGES.EMPTY_REQUEST_BODY;
        return result;
    }

    if (isNone(schema) || typeof schema !== "object") {
        result.checkResult = true;
        return result;      // nothing to validate
    }

    const { error } = schema.validate(jsonBody);
    if (error) {
        result.checkResult = false;
        result.ngReason = error.details[0].message;
    }

    return result;
}