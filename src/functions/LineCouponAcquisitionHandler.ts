import Check from "@/core/common/Check";
import FunctionExternalApiHandlerBase from "@/core/common/FunctionExternalApiHandler";
import Constants from "@/core/constant/Constants";
import ResultCdConstants from "@/core/constant/ResultCdConstants";
import LineCouponAcquisitionInputDto from "@/core/dto/LineCouponAcquisitionInputDto";
import LineCouponAcquisitionService from "@/core/service/impl/LineCouponAcquisitionService";
import ExtendedInvocationContext from "@/types/ExtendedInvocationContext";
import { HttpRequest, HttpResponseInit } from "@azure/functions";

export class LineCouponAcquisitionHandler extends FunctionExternalApiHandlerBase {
    public static Meta = {
        localFunctionType: "04",
        url: Constants.LINES_COUPON,
        getOrderType: (param: LineCouponAcquisitionInputDto) => Check.checkOrderType(
            param?.reserve_date,
            param?.reserve_flag,
            param?.reserve_soId
        ),
        processName: "回線クーポンオン・オフ",
    };
    public static Handler = async (
        request: HttpRequest,
        context: ExtendedInvocationContext,
    ): Promise<HttpResponseInit> => {
        const lineCouponAcquisitionService = new LineCouponAcquisitionService(
            request,
            context,
        );
        try {
            const param = context.jsonBody;
            const result = await lineCouponAcquisitionService.service(
                param,
                false,
            );
            return { jsonBody: result.jsonBody };
        } catch (error) {
            context.error(error);
            return { body: error };
        }
    };
}
