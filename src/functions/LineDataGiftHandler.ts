import FunctionExternalApiHandlerBase from "@/core/common/FunctionExternalApiHandler";
import Constants from "@/core/constant/Constants";
import { HttpRequest, HttpResponseInit } from "@azure/functions";
import ExtendedInvocationContext from "@/types/ExtendedInvocationContext";
import ResultCdConstants from "@/core/constant/ResultCdConstants";
import LineDataGiftServiceTx from "@/core/service/impl/LineDataGiftServiceTx";
import MvnoUtil from "@/core/common/MvnoUtil";
import LineDataGiftInputDto from "@/core/dto/LineDataGiftInputDto";
import MsgKeysConstants from "@/core/constant/MsgKeysConstants";

export class LineDataGiftHandler extends FunctionExternalApiHandlerBase {
    public static Meta = {
        localFunctionType: "19",
        url: Constants.LINE_DATAGIFT,
        getOrderType: (jsonBody: any) => null,          // soObject.setOrderType(null); at soManagement
        processName: "回線データ譲渡",
    }

    public static Handler = async (
        request: HttpRequest,
        context: ExtendedInvocationContext,
    ): Promise<HttpResponseInit> => {
        const lineDataGiftService = new LineDataGiftServiceTx(request, context);

        try {
            const param = context.jsonBody;
            const result = await lineDataGiftService.service(param);
            return {
                jsonBody: result.jsonBody,
            };
        } catch (error) {
            context.error("LineGroupModBucketAcquisitionHandler error:", error);
            context.responseHeader.processCode = ResultCdConstants.CODE_999999;
            return this.CommonCheckCatchErrorHandler(request, context, error);
        }
    }

    public static CommonCheckFailedHandler = async (
        request: HttpRequest,
        context: ExtendedInvocationContext,
        errorMessage: string,
    ): Promise<HttpResponseInit> => {
        const lineDataGiftService = new LineDataGiftServiceTx(request, context);
        const handleCode = context.responseHeader?.processCode ?? ResultCdConstants.CODE_999999;
        const apiProcessId = context.responseHeader?.apiProcessID ?? "";
        const param = context.jsonBody as LineDataGiftInputDto;
        const receivedDate = context.responseHeader?.receivedDate ?? MvnoUtil.getDateTimeNow();
        const functionType: string = param.requestHeader.functionType;
        const sourceLineNo: string = param.sourceLineNo;
        const destinationLineNo: string = param.destinationLineNo;

        await lineDataGiftService.soManagement(param, apiProcessId, handleCode, receivedDate, functionType,
            sourceLineNo, destinationLineNo);
        return lineDataGiftService.returnEdit(param, handleCode, apiProcessId, receivedDate);
    }

    public static CommonCheckCatchErrorHandler = async (
        request: HttpRequest,
        context: ExtendedInvocationContext,
        error: Error,
    ): Promise<HttpResponseInit> => {
        const lineDataGiftService = new LineDataGiftServiceTx(request, context);
        const param = context.jsonBody as LineDataGiftInputDto;
        const receivedDate = context.responseHeader?.receivedDate ?? MvnoUtil.getDateTimeNow();
        const apiProcessId = context.responseHeader?.apiProcessID ?? "";
        context.error(error, param.tenantId, param.requestHeader.sequenceNo, MsgKeysConstants.APCOME0401,
            error.message);
        const handleCode = ResultCdConstants.CODE_999999;
        return lineDataGiftService.returnEdit(param, handleCode, apiProcessId, receivedDate);
    }
}