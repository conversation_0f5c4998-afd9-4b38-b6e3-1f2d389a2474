import PortalFunctionHandlerBase from "@/helpers/PortalFunctionHandlerBase";
import { UserRole } from "@/types/Session";
import { MAWP_ROLE_ID } from "@/constants/userRoles";
import { HttpRequest, HttpResponseInit } from "@azure/functions";
import ExtendedPortalInvocationContext from "@/types/ExtendedPortalInvocationContext";
import Joi from "joi";
import { CommonError, getCommonErrorName } from "@/constants/commonError";
import { CoreSwimmyStatus } from "@/constants/coreSwimmy";
import { LinesEntity } from "@/core/entity/LinesEntity";
import { Op } from "sequelize";
import CoreServicesRestrictSetting from "@/models/servicesRestrictSetting";
import PlansEntity from "@/core/entity/PlansEntity";
import APILinesDAO from "@/core/dao/APILinesDAO";

interface UpdateParams {
    line_nos: string[],
    bb_uniba_flag: boolean,
    is_batch?: boolean
}

interface IParserResult {
    error: any
    updateParams: UpdateParams,
    newStatus: CoreSwimmyStatus
}

const changeKaisenBbUnibaSchema = Joi.object({
    line_nos: Joi.array().items(Joi.string().required()).when('is_batch', {
        is: true,
        then: Joi.array().min(1),
        otherwise: Joi.array().length(1),
    }).required(),
    bb_uniba_flag: Joi.string().valid("true", "false").required(),
    is_batch: Joi.boolean().strict().optional(),
});

export class ChangeKaisenBbUnibaHandler extends PortalFunctionHandlerBase {
    public static AllowedRoles: UserRole[] = [
        MAWP_ROLE_ID.SUPER_USER,
        MAWP_ROLE_ID.NTTCOM_OPERATOR_USER,
        MAWP_ROLE_ID.TENANT_DAIHYO_USER,
        MAWP_ROLE_ID.TENANT_OPERATOR_USER,
        MAWP_ROLE_ID.TENANT_ETSURAN_USER,
        MAWP_ROLE_ID.TEMPO_USER,
    ];

    public static Handler = async (
        request: HttpRequest,
        context: ExtendedPortalInvocationContext,
    ): Promise<HttpResponseInit> => {
        try {
            context.log("ChangeKaisenBbUnibaHandler START");

            const {
                error,
                updateParams,
                newStatus
            } = await this.validateAndParseChangeKaisenBbUniba(request, context);
            if (error) {
                return {
                    jsonBody: {
                        error,
                        error_msg: getCommonErrorName(error),
                        nttc_mvno_error_code: 'invalid parameters',
                        nttc_mvno_error_msg: null,
                    }
                };
            }

            return await this.processChangeKaisenBbUniba(
                request,
                context,
                updateParams,
                newStatus);
        } catch (e) {
            context.error('ChangeKaisenBbUnibaHandler - exception: ', e);
            return { body: e };
        }
    }

    private static validateAndParseChangeKaisenBbUniba = async (
        request: HttpRequest,
        context: ExtendedPortalInvocationContext,
    ): Promise<IParserResult> => {
        let body: any;
        try {
            body = await request.json();
        } catch(error) {
            throw Error("Request body is empty");
        }

        const { error } = changeKaisenBbUnibaSchema.validate(body);
        if (error) {
            context.error(error);
            return {
                error: CommonError.INVALID_PARAMETER,
                updateParams: null,
                newStatus: null
            };
        }

        return {
            error: null,
            updateParams: {
                line_nos: body.line_nos,
                bb_uniba_flag: body.bb_uniba_flag === "true",
                is_batch: body.is_batch,
            },
            newStatus: parseInt(body.statusCode, 10),
        };
    }

    private static processChangeKaisenBbUniba = async (
        request: HttpRequest,
        context: ExtendedPortalInvocationContext,
        updateParams: UpdateParams,
        newStatus: CoreSwimmyStatus
    ): Promise<HttpResponseInit> => {
        context.log('processChangeKaisenBbUniba START');
        const isBatch = updateParams.is_batch;
        const lineNos = updateParams.line_nos;

        const lines = await LinesEntity.findAll({
            where: {
                lineId: lineNos,
                lineStatus: { [Op.not]: "03" },
            }
        });

        if (lines.length !== lineNos.length) {
            context.log('Some line_nos not found ', JSON.stringify(lineNos));
            if (!isBatch) {
                return {
                    jsonBody: {
                        error: CommonError.INVALID_PARAMETER,
                        error_msg: getCommonErrorName(CommonError.INVALID_PARAMETER),
                        nttc_mvno_error_code: '回線が存在しない',
                        nttc_mvno_error_msg: '回線が存在しない',
                    }
                };
            } else {
                return {
                    jsonBody: {
                        error: CommonError.INVALID_PARAMETER,
                        result: lineNos.map(lineNo => {
                            const line = lines.find(l => l.lineId === lineNo);
                            return {
                                lineNo,
                                error: line ? CommonError.NO_ERROR : CommonError.INVALID_PARAMETER,
                                errorMessage: line ? "" : '回線が存在しない',
                            };
                        }),
                    }
                };
            }
        }

        const bbUnibaEnabledTenants = await CoreServicesRestrictSetting.getBbUnibaEnabledTenants();

        const apiLinesDao = new APILinesDAO(request, context as any);
        const tenant_ids = await apiLinesDao.getTenantIdByLineIds(lineNos);

        const tenants = Array.from(new Set(tenant_ids.map(tenant_ids => tenant_ids.tenant_id)));

        // Check if all tenants are enabled for BB Uniba
        const notEnabledTenants = tenants.filter(tid => !bbUnibaEnabledTenants.includes(tid));
        if (tenant_ids.length !== lineNos.length || notEnabledTenants.length > 0) {
            context.log('Some tenants are not enabled for BB Uniba: ', JSON.stringify(notEnabledTenants));
            if (!isBatch) {
                return {
                    jsonBody: {
                        error: CommonError.INVALID_PARAMETER,
                        error_msg: getCommonErrorName(CommonError.INVALID_PARAMETER),
                        nttc_mvno_error_code: 'BBユニバ変更できるテナントではないこと含んでる',
                        nttc_mvno_error_msg: null,
                    }
                };
            } else {
                return {
                    jsonBody: {
                        error: CommonError.INVALID_PARAMETER,
                        result: lineNos.map(lineNo => {
                            const tenant = tenant_ids.find(t => t.line_id === lineNo);
                            return {
                                lineNo,
                                error: tenant && !notEnabledTenants.includes(tenant.tenant_id) ? CommonError.NO_ERROR : CommonError.INVALID_PARAMETER,
                                errorMessage: tenant && !notEnabledTenants.includes(tenant.tenant_id) ? null : 'BBユニバ変更できるテナントではないこと含んでる',
                            };
                        }),
                    }
                };
            }
        }

        const resale_plan_ids = Array.from(new Set(lines.map(line => line.pricePlanId)));
        const plans = await PlansEntity.findAll({
            where: {
                pricePlanId: resale_plan_ids,
                planBbuniExFlag: { [Op.or]: [{ [Op.ne]: true }, { [Op.is]: null }] }
            }
        });
        if (plans.length !== resale_plan_ids.length) {
            context.log('Some plans are not eligible for BB Uniba: ', JSON.stringify(resale_plan_ids), plans);
            if (!isBatch) {
                return {
                    jsonBody: {
                        error: CommonError.INVALID_PARAMETER,
                        error_msg: getCommonErrorName(CommonError.INVALID_PARAMETER),
                        nttc_mvno_error_code: 'BBユニバ適用外プランあり',
                        nttc_mvno_error_msg: null,
                    }
                };
            } else {
                return {
                    jsonBody: {
                        error: CommonError.INVALID_PARAMETER,
                        result: lineNos.map(lineNo => {
                            const line = lines.find(l => l.lineId === lineNo);
                            const plan = line ? plans.find(p => p.pricePlanId === line.pricePlanId) : null;
                            return {
                                lineNo,
                                error: plan ? CommonError.NO_ERROR : CommonError.INVALID_PARAMETER,
                                errorMessage: plan ? "" : 'BBユニバ適用外プランあり',
                            };
                        }),
                    }
                };
            }
        }

        const res = await LinesEntity.update(
            {
                userBbuniExFlag: updateParams.bb_uniba_flag === true ? true : null
            },
            {
                where: {
                    lineId: lineNos,
                },
                returning: false,
            },
        );
        context.log(`Updated ${res[0]} lines with bb_uniba_flag=${updateParams.bb_uniba_flag}`);
        if (!isBatch) {
            return {
                jsonBody: {
                    error: CommonError.NO_ERROR,
                },
            };
        } else {
            return {
                jsonBody: {
                    error: CommonError.NO_ERROR,
                    result: lineNos.map(lineNo => ({
                        lineNo,
                        error: CommonError.NO_ERROR,
                        errorMessage: "",
                    })),
                },
            };
        }
    }
}