import { HttpRequest, HttpResponseInit } from "@azure/functions";
import PortalFunctionHandlerBase from "@/helpers/PortalFunctionHandlerBase";
import ExtendedPortalInvocationContext from "@/types/ExtendedPortalInvocationContext";
import { UserRole } from "@/types/Session";
import CoreSwimmyService from "@/services/coreSwimmyService";
import { CommonError } from "@/constants/commonError";

export class GetMaintenanceModeStatusHandler extends PortalFunctionHandlerBase {
    public static AllowedRoles: UserRole[] = [0, 1, 2, 3, 4];

    public static Handler = async (
        request: HttpRequest,
        context: ExtendedPortalInvocationContext,
    ): Promise<HttpResponseInit> => {
        try {
            context.log("GetMaintenanceModeStatus START");
            const coreSwimmyService = new CoreSwimmyService(request, context);
            const isMaintenance = await coreSwimmyService.isMaintenanceMode();
            context.log("GetMaintenanceModeStatus END: ", isMaintenance ? "on" : "off");
            return {
                jsonBody: {
                    error: CommonError.NO_ERROR,
                    result: {
                        maintenance_mode: isMaintenance ? "on" : "off",
                    },
                },
            };
        } catch (error) {
            context.error(error);
            return { body: error };
        }
    };
}
