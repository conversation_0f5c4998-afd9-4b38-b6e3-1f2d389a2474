import { HttpRequest, HttpResponseInit } from "@azure/functions";
import RESTCommon from "@/core/common/RESTCommon";
import ExtendedInvocationContext from "@/types/ExtendedInvocationContext";
import { TenantsEntity } from "@/core/entity/TenantsEntity";
import PortalFunctionHandlerBase from "@/helpers/PortalFunctionHandlerBase";
import { UserRole } from "@/types/Session";
import { MAWP_ROLE_ID } from "@/constants/userRoles";
import ExtendedPortalInvocationContext from "@/types/ExtendedPortalInvocationContext";


export class ResetTenantConnectionsHandler extends PortalFunctionHandlerBase {
    public static AllowedRoles: UserRole[] = [
        MAWP_ROLE_ID.SUPER_USER
    ];

    public static Handler = async (
        request: HttpRequest,
        context: ExtendedPortalInvocationContext,
    ): Promise<HttpResponseInit> => {
        try {
            context.log("ResetTenantConnectionsHandler started");
            let body: any;
            try {
                body = await request.json();
            } catch(error) {
                return {
                    status: 400,
                    jsonBody: { status: "Error", error: "Invalid JSON body" },
                }
            }
            const tenantIds = body.tenantId;
            const restCommon = new RESTCommon(request, context as any as ExtendedInvocationContext);

            if (!tenantIds) {
                return {
                    status: 400,
                    jsonBody: { status: "Error", error: "tenantId is required" },
                };
            }

            // Reset connections for array of tenant IDs
            for (const tenantId of tenantIds) {
                await restCommon.resetTenantConnectCount(tenantId);
            }
            return {
                status: 200,
                jsonBody: {
                    status: "OK",
                    message: `Connections for tenant ${tenantIds} have been reset.`
                },
            };
        } catch (error) {
            context.error("ResetTenantConnectionsHandler error: ", error);
            return {
                status: 500,
                jsonBody: {
                    status: "Error",
                    error: "Internal Server Error"
                },
            };
        }
    }
}