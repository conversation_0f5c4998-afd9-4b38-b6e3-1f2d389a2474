import { HttpRequest, HttpResponseInit } from "@azure/functions";
import PortalFunctionHandlerBase from "@/helpers/PortalFunctionHandlerBase";
import { SwimmyType } from "@/types/coreSwimmyApiLog";
import ExtendedPortalInvocationContext from "@/types/ExtendedPortalInvocationContext";
import { UserRole } from "@/types/Session";
import CoreSwimmyService from "@/services/coreSwimmyService";
import { CommonError, getCommonErrorName } from "@/constants/commonError";
import { MAWP_ROLE_ID } from "@/constants/userRoles";
import { CORE_SWIMMY_TYPES, CoreSwimmyStatus } from "@/constants/coreSwimmy";
import Joi from 'joi';
import { request } from "http";

interface IParserResult {
    error: any
    updateParams: [{requestOrderId: string, swimmyType: string}],
    newStatus: CoreSwimmyStatus
}

const coreSwimmyStatusValues = Object
                                .values(CoreSwimmyStatus)
                                .filter(value => typeof value === 'number');

const targetSchema = Joi.object({
    requestOrderId: Joi.string().alphanum().required(),
    swimmyType: Joi.string().valid(...Object.values(CORE_SWIMMY_TYPES)).required(),
});

const UpdateCoreSwimmyApiLogStatusSchema = Joi.object({
    targets: Joi.array().items(targetSchema).min(1).required(),
    statusCode: Joi.number().valid(...coreSwimmyStatusValues).required()
}).required();

export class UpdateCoreSwimmyApiLogStatusHandler extends PortalFunctionHandlerBase {
    public static AllowedRoles: UserRole[] = [
        MAWP_ROLE_ID.SUPER_USER,
        MAWP_ROLE_ID.NTTCOM_OPERATOR_USER
    ];

    public static Handler = async (
        request: HttpRequest,
        context: ExtendedPortalInvocationContext,
    ): Promise<HttpResponseInit> => {
        try {
            context.log("UpdateCoreSwimmyApiLogStatusHandler START");

            const {
                error,
                updateParams,
                newStatus
            } = await this.validateAndParseUpdateCoreSwimmyApiLogStatus(request);
            if (error) {
                return {
                    jsonBody: {
                        error,
                        error_msg: getCommonErrorName(error),
                        nttc_mvno_error_code: 'invalid parameters',
                        nttc_mvno_error_msg: null,
                    }
                };
            }

            const result = await this.processCoreUpdateSwimmyApiLogStatus(
                request,
                context,
                updateParams,
                newStatus);
            return {
                jsonBody: {
                    error: CommonError.NO_ERROR,
                    results: result
                },
            };

        } catch (error) {
            context.error('UpdateCoreSwimmyApiLogStatusHandler - exception: ', error);
            return { body: error };
        }
    }

    private static validateAndParseUpdateCoreSwimmyApiLogStatus = async (
        request: HttpRequest
    ): Promise<IParserResult> => {
        let body: any;
        try {
            body = await request.json();
        } catch(error) {
            throw Error("Request body is empty");
        }

        const { error } = UpdateCoreSwimmyApiLogStatusSchema.validate(body);
        if (error) {
            return {
                error: CommonError.TARGETS_INVALID,
                updateParams: null,
                newStatus: null
            };
        }

        return {
            error: null,
            updateParams: body.targets,
            newStatus: parseInt(body.statusCode, 10),
        };
    };

    private static processCoreUpdateSwimmyApiLogStatus = async (
        request: HttpRequest,
        context: ExtendedPortalInvocationContext,
        updateParams: [{requestOrderId: string, swimmyType: string}],
        newStatus: CoreSwimmyStatus
    ): Promise<object> => {
        context.log('processCoreUpdateSwimmyApiLogStatus START');

        const resultList = [];
        const coreSwimmyService = new CoreSwimmyService(request, context);
        await Promise.all(updateParams.map(async (obj) => {
            const updateOK = await coreSwimmyService.updateStatusForUI(
                context,
                obj.requestOrderId,
                obj.swimmyType as SwimmyType,
                newStatus,
                context.sessionData.user.plusId
            );
            resultList.push({
                requestOrderId: obj.requestOrderId,
                swimmyType: obj.swimmyType,
                updateOK
            });
        }));

        return resultList;
    };
}