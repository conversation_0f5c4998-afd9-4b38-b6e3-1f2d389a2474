import { HttpRequest, HttpResponseInit } from "@azure/functions";
import RESTCommon from "@/core/common/RESTCommon";
import ExtendedInvocationContext from "@/types/ExtendedInvocationContext";
import PortalFunctionHandlerBase from "@/helpers/PortalFunctionHandlerBase";
import { UserRole } from "@/types/Session";
import { MAWP_ROLE_ID } from "@/constants/userRoles";
import ExtendedPortalInvocationContext from "@/types/ExtendedPortalInvocationContext";
import { TenantsEntity } from "@/core/entity/TenantsEntity";
import { usePsql } from "@/database/psql";


export class GetTenantConnectionsHandler extends PortalFunctionHandlerBase {
    public static AllowedRoles: UserRole[] = [
        MAWP_ROLE_ID.SUPER_USER
    ];

    public static Handler = async (
        request: HttpRequest,
        context: ExtendedPortalInvocationContext,
    ): Promise<HttpResponseInit> => {
        try {
            context.log("GetTenantConnectionsHandler started");
            const tenantId = request.query.get("tenantId");
            const restCommon = new RESTCommon(request, context as any as ExtendedInvocationContext);
            if (!tenantId) {
                // return list of tenants
                context.log("No tenantId provided, returning list of tenants");
                const tenants = await TenantsEntity.findAll({
                    order: [["tenantId", "ASC"]],
                });
                const returnTenantList = [];
                const totalConnectionCount = await restCommon.getConCount();

                for (const tenant of tenants) {
                    const currentConnectionCount = await restCommon.getTenantConCount(tenant.tenantId);
                    returnTenantList.push({
                        tenantId: tenant.tenantId,
                        tenantName: tenant.tenantName,
                        tenantMaxConnection: tenant.tenantMaxConnection,
                        currentConnectionCount,
                    });
                }
                return {
                    status: 200,
                    jsonBody: {
                        tenants: returnTenantList,
                        totalConnectionCount,
                        tenantId: null,
                        currentConnections: null
                    },
                }
            }
            const count = await restCommon.getTenantConCount(tenantId);
            return {
                status: 200,
                jsonBody: {
                    tenantId,
                    currentConnections: count,
                    tenants: null,
                },
            };
        } catch (e) {
            context.error(e);
            return {
                status: 500,
                jsonBody: { error: "Internal Server Error", e },
            };
        }
    }
}