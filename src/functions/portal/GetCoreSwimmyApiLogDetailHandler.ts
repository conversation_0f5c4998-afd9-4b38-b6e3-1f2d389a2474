import { HttpRequest, HttpResponseInit } from "@azure/functions";
import mongoose from "mongoose";
import PortalFunctionHandlerBase from "@/helpers/PortalFunctionHandlerBase";
import ExtendedPortalInvocationContext from "@/types/ExtendedPortalInvocationContext";
import { UserRole } from "@/types/Session";
import CoreSwimmyService from "@/services/coreSwimmyService";
import { CommonError } from "@/constants/commonError";
import { CORE_SWIMMY_TYPES_TEXT, CORE_SWIMMY_STATUS_TEXT } from "@/constants/coreSwimmy";
import { MAWP_ROLE_ID } from "@/constants/userRoles";
import { isNone } from "@/utils";
import APICoreSwimmyDAO from "@/core/dao/APICoreSwimmyDAO";
import { usePsql } from "@/database/psql";

export class GetCoreSwimmyApiLogDetailHandler extends PortalFunctionHandlerBase {
    public static AllowedRoles: UserRole[] = [
        MAWP_ROLE_ID.SUPER_USER,
        MAWP_ROLE_ID.NTTCOM_OPERATOR_USER
    ];

    public static Handler = async (
        request: HttpRequest,
        context: ExtendedPortalInvocationContext,
    ): Promise<HttpResponseInit> => {
        try {
            context.log("GetCoreSwimmyApiLogDetailAPI START");
            const id = request.params.requestOrderId;
            if (isNone(id) || !mongoose.Types.ObjectId.isValid(id)) {
                return {
                    jsonBody: {
                        error: CommonError.BAD_REQUEST,
                        error_msg: 'invalid parameter',
                        nttc_mvno_error_code: CommonError.INVALID_PARAMETER,
                        nttc_mvno_error_msg: null,
                    }
                };
            }

            const result = await this.processGetCoreApiLogDetail(request, context, id);
            return {
                jsonBody: {
                    error: CommonError.NO_ERROR,
                    ...result
                },
            };

        } catch (error) {
            context.error('GetCoreSwimmyApiLogDetailAPI - exception: ', error);
            return { body: error };
        }
    };

    private static processGetCoreApiLogDetail = async (
        request: HttpRequest,
        context: ExtendedPortalInvocationContext,
        id: string
    ): Promise<object> => {
        context.log('processGetCoreApiLogDetail START ');

        const coreSwimmyService = new CoreSwimmyService(request, context);
        const logList = await coreSwimmyService.findById(id);
        if (logList) {
            const firstLog = logList;
            await usePsql();
            const dao = new APICoreSwimmyDAO(request, context);
            const tenantData = await dao.getTenantsEntity(firstLog.tenantId);
            const outputTenantName = !isNone(tenantData) ? tenantData.tenantName : '';
            return {
                id: firstLog.id,
                requestOrderId: firstLog.requestOrderId,
                soId: firstLog.soId ?? '',
                requestOrderType: firstLog.swimmyType ?? '',
                requestOrderName: !isNone(firstLog.swimmyType) ? CORE_SWIMMY_TYPES_TEXT[firstLog.swimmyType] : '',
                tenantId: firstLog.tenantId ?? '',
                tenantName: outputTenantName,
                tempoId: firstLog.tempoId ?? '',
                kaisenNo: firstLog.kaisenNo ?? '',
                status: !isNone(firstLog.status) ? CORE_SWIMMY_STATUS_TEXT[firstLog.status] : '',
                statusCode: !isNone(firstLog.status) ? firstLog.status : '',
                statusLog: firstLog.statusLog ?? {},
                requestParam: firstLog.requestParam ?? {},
                responseParam: firstLog.responseParam ?? {},
                responseLog: firstLog.responseLog ?? {},
                sentAt: firstLog.createdAt ?? 0,
                responseAt: firstLog.getResponseAt ?? 0,
            };
        } else {
            return {
                nttc_mvno_error_code: '9999',
                nttc_mvno_error_msg: 'エラーが発生しました。',
            };
        }
    };
}