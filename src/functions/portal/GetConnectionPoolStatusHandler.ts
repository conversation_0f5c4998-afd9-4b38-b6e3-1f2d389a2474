import { HttpRequest, HttpResponseInit } from "@azure/functions";
import PortalFunctionHandlerBase from "@/helpers/PortalFunctionHandlerBase";
import { UserRole } from "@/types/Session";
import { MAWP_ROLE_ID } from "@/constants/userRoles";
import ExtendedPortalInvocationContext from "@/types/ExtendedPortalInvocationContext";
import { getConnectionPoolStatus } from "@/database/psql";

export class GetConnectionPoolStatusHandler extends PortalFunctionHandlerBase {
    public static AllowedRoles: UserRole[] = [
        MAWP_ROLE_ID.SUPER_USER
    ];

    public static Handler = async (
        request: HttpRequest,
        context: ExtendedPortalInvocationContext,
    ): Promise<HttpResponseInit> => {
        try {
            context.log("GetConnectionPoolStatusHandler started");
            
            const poolStatus = getConnectionPoolStatus();
            
            return {
                status: 200,
                jsonBody: {
                    connectionPool: poolStatus,
                    timestamp: new Date().toISOString(),
                    message: "Connection pool status retrieved successfully"
                },
            };
        } catch (e) {
            context.error("GetConnectionPoolStatusHandler error:", e);
            return {
                status: 500,
                jsonBody: { 
                    error: "Internal Server Error", 
                    message: e.message || "Unknown error occurred"
                },
            };
        }
    };
}
