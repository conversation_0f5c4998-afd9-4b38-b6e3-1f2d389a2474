import PortalFunctionHandlerBase from "@/helpers/PortalFunctionHandlerBase";
import { UserRole } from "@/types/Session";
import { MAWP_ROLE_ID } from "@/constants/userRoles";
import { HttpRequest, HttpResponseInit, InvocationContext, Timer } from "@azure/functions";
import ExtendedPortalInvocationContext from "@/types/ExtendedPortalInvocationContext";
import AutoLineGroupModbucketHandler from "@/functions/AutoLineGroupModbucket";
import { ExecReservedSOHandler } from "@/functions/triggers/ExecReservedSOHandler";
import ReserveRewriteHandler from "@/functions/triggers/ReserveRewriteHandler";

export const availableCronJobs: {name: string, function: (timer: Timer, context: InvocationContext) => Promise<HttpResponseInit>}[] = [
    {
        name: "AutoLineGroupModbucketTimer",
        function: AutoLineGroupModbucketHandler
    },
    {
        name: "ExecReservedSOTimer",
        function: ExecReservedSOHandler.Handler
    },
    {
        name: "ReserveRewriteTimer",
        function: ReserveRewriteHandler.Handler
    },
];

export class RestartCronjobHandler extends PortalFunctionHandlerBase {
    public static AllowedRoles: UserRole[] = [
        MAWP_ROLE_ID.SUPER_USER
    ];

    public static Handler = async (
        request: HttpRequest,
        context: ExtendedPortalInvocationContext,
    ): Promise<HttpResponseInit> => {
        try {
            context.log("RestartCronjobHandler START");
            let body: any;
            try {
                body = await request.json();
            } catch(error) {
                return {
                    status: 400,
                    jsonBody: { status: "Error", error: "Invalid JSON body" },
                }
            }
            const cronJobName = body.cronJobName;

            if (!cronJobName) {
                return {
                    status: 400,
                    jsonBody: { status: "Error", error: "cronJobName is required" },
                };
            }
            const cronJob = availableCronJobs.find(job => job.name === cronJobName);
            if (!cronJob) {
                return {
                    status: 404,
                    jsonBody: { status: "Error", error: `Cron job ${cronJobName} not found` },
                };
            }
            context.log(`Restarting cron job: ${cronJobName}`);

            // Call the function associated with the cron job
            try {
                await cronJob.function(null, context);
            } catch (error) {
                context.error(`Error restarting cron job ${cronJobName}: `, error);
                return {
                    status: 500,
                    jsonBody: { status: "Error", error: `Failed to restart cron job ${cronJobName}` },
                };
            }

            return {
                status: 200,
                jsonBody: {
                    status: "OK",
                    message: "Cron job restarted successfully"
                },
            };
        } catch (error) {
            context.error('RestartCronjobHandler - exception: ', error);
            return { body: error };
        }
    }
}