import { HttpRequest, HttpResponseInit } from "@azure/functions";
import PortalFunctionHandlerBase from "@/helpers/PortalFunctionHandlerBase";
import ExtendedPortalInvocationContext from "@/types/ExtendedPortalInvocationContext";
import { UserRole } from "@/types/Session";
import CoreSwimmyService from "@/services/coreSwimmyService";
import { CommonError, getCommonErrorName } from "@/constants/commonError";
import { MAWP_ROLE_ID } from "@/constants/userRoles";
import Joi from 'joi';
import { zipArrays } from "@/utils";

interface IParserResult {
    error: any
    updateParams: [{id: string, options?: any}]
}

const targetSchema = Joi.object({
    id: Joi.string().alphanum().length(24).required(),
    options: Joi.object({
        sameMvneFlag: Joi.number().valid(0, 1).optional(),
    }).optional(),
});

const ResendCoreSwimmyTransactionSchema = Joi.object({
    targets: Joi.array().items(targetSchema).min(1).required()
}).required();

export class ResendCoreSwimmyTransactionHandler extends PortalFunctionHandlerBase {
    public static AllowedRoles: UserRole[] = [
        MAWP_ROLE_ID.SUPER_USER,
        MAWP_ROLE_ID.NTTCOM_OPERATOR_USER
    ];

    public static Handler = async (
        request: HttpRequest,
        context: ExtendedPortalInvocationContext,
    ): Promise<HttpResponseInit> => {
        try {
            context.log("ResendCoreSwimmyTransactionAPI START");

            const result = await this.processCoreResendSwimmyTransaction(request, context);
            if ('errorCode' in result) {
                context.log('ResendCoreSwimmyTransactionAPI invalid parameters ', result.errorCode);
                return {
                    jsonBody: {
                        error: CommonError.INVALID_USER,
                        error_msg: getCommonErrorName(CommonError.INVALID_USER),
                        nttc_mvno_error_code: 'invalid parameters',
                        nttc_mvno_error_msg: null,
                    }
                };
            }

            return {
                jsonBody: {
                    error: CommonError.NO_ERROR,
                    results: result
                },
            };

        } catch (error) {
            context.error('ResendCoreSwimmyTransactionAPI - exception: ', error);
            return { body: error };
        }
    }

    private static processCoreResendSwimmyTransaction = async (
        request: HttpRequest,
        context: ExtendedPortalInvocationContext
    ): Promise<object> => {
        context.log('processCoreResendSwimmyTransaction START');
        const { error, updateParams } = await this.validateAndParseResendCoreSwimmyTransactionRequest(request, context);
        if (error) {
            return { errorCode: error };
        }

        const coreSwimmyService = new CoreSwimmyService(request, context);
        const promises = await Promise.allSettled(updateParams.map(async (obj) => {
            const result = await coreSwimmyService.updateForResendRequest(obj.id, context.sessionData.user.plusId, obj.options);
            return {
                id: obj.id,
                resendOK: result
            }
        }));

        return zipArrays(promises, updateParams).map(([promise, obj]) => {
            if (promise.status === "fulfilled") {
                return promise.value;
            } else {
                return {
                    id: obj.id,
                    resendOK: false,
                };
            }
        });
    };

    private static validateAndParseResendCoreSwimmyTransactionRequest = async (
        request: HttpRequest,
        context: ExtendedPortalInvocationContext
    ): Promise<IParserResult> => {
        let body: any;
        try {
            body = await request.json();
        } catch(error) {
            throw Error("Request body is empty");
        }

        const { error } = ResendCoreSwimmyTransactionSchema.validate(body);
        if (error || !context?.sessionData?.user?.plusId) {
            return {
                error: CommonError.TARGETS_INVALID,
                updateParams: null
            };
        }

        return {
            error: null,
            updateParams: body.targets,
        };
    };
}
