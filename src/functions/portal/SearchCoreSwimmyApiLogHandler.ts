import { HttpRequest, HttpResponseInit } from "@azure/functions";
import PortalFunctionHandlerBase from "@/helpers/PortalFunctionHandlerBase";
import ExtendedPortalInvocationContext from "@/types/ExtendedPortalInvocationContext";
import { UserRole } from "@/types/Session";
import CoreSwimmyService from "@/services/coreSwimmyService";
import { CommonError, getCommonErrorName } from "@/constants/commonError";
import { CORE_SWIMMY_STATUS_TEXT, CORE_SWIMMY_TYPES, CORE_SWIMMY_TYPES_TEXT, CoreSwimmyStatus } from "@/constants/coreSwimmy";
import { MAWP_ROLE_ID } from "@/constants/userRoles";
import { ICoreSwimmySearchResult } from "@/models/coreSwimmyApiLog";
import { isNone } from "@/utils";
import Joi from 'joi';

interface ISearchCoreSwimmyParams {
    tenantIds: string[]
    orderTypes: string[]
    actionType: number
    status: string[]
    requestedDateTimeFrom: number
    requestedDateTimeTo: number
    sortBy: string
    skip: number
    limit: number
    isSortAsc: boolean
    searchType?: number
    searchValue?: string
}

interface IParserResult {
    errorCode: any
    params: ISearchCoreSwimmyParams
}

const SEARCH_TYPES = [0, 1];
const coreSwimmyStatusValues = Object
                                .values(CoreSwimmyStatus)
                                .filter(value => typeof value === 'number');

const SearchCoreSwimmySchema = Joi.object({
    searchType: Joi.number().valid(...SEARCH_TYPES),
    searchValue: Joi.string(),
    tenantIds: Joi.array().items(Joi.string()),
    orderTypes: Joi.array().items(Joi.string().valid(...Object.values(CORE_SWIMMY_TYPES))),
    status: Joi.array().items(Joi.number().valid(...coreSwimmyStatusValues)),
    actionType: Joi.number(),
    fromOrderDatetime: Joi.number(),
    toOrderDatetime: Joi.number(),
    sortBy: Joi.string(),
    skip: Joi.number(),
    limit: Joi.number().required(),
    sortOrder: Joi.string(),
}).required();

export class SearchCoreSwimmyApiLogHandler extends PortalFunctionHandlerBase {
    public static AllowedRoles: UserRole[] = [
        MAWP_ROLE_ID.SUPER_USER,
        MAWP_ROLE_ID.NTTCOM_OPERATOR_USER
    ];

    public static Handler = async (
        request: HttpRequest,
        context: ExtendedPortalInvocationContext,
    ): Promise<HttpResponseInit> => {
        try {
            context.log("SearchCoreSwimmyApiLogAPI START");
            const { errorCode, params } = await this.validateAndParseSearchRequest(request, context);
            if (errorCode) {
                context.log('SearchCoreSwimmyApiLogAPI invalid parameters ', errorCode);
                const error = CommonError.INVALID_USER;
                const errorMsg = getCommonErrorName(error);
                return {
                    jsonBody: {
                        error,
                        error_msg: errorMsg,
                        nttc_mvno_error_code: 'invalid parameters',
                        nttc_mvno_error_msg: null,
                    }
                };
            }

            const { result, count } = await this.processSearch(params, request, context);
            return {
                jsonBody: {
                    error: CommonError.NO_ERROR,
                    skip: params.skip ?? 0,
                    searchResultCount: count,
                    result
                },
            };
        } catch (error) {
            context.error('SearchCoreSwimmyApiLogAPI - exception: ', error);
            return { body: error };
        }
    };

    private static validateAndParseSearchRequest = async (
        request: HttpRequest,
        context: ExtendedPortalInvocationContext
    ): Promise<IParserResult> => {
        let body: any;
        try {
            body = await request.json();
        } catch(error) {
            throw Error("Request body is empty");
        }

        const { error } = SearchCoreSwimmySchema.validate(body);
        if (error) {
            context.log(error);
            return {
                errorCode: CommonError.INVALID_PARAMETER,
                params: null
            };
        }

        // no need?
        // if (isNone(body.search_value) && isNone(body.from_order_datetime) && isNone(body.to_order_datetime) &&
        //     isNone(body.tenant_ids) && isNone(body.status) && isNone(body.order_types)
        // ) {
        //     return {
        //         errorCode: CommonError.INVALID_PARAMETER,
        //         params: null
        //     }
        // }

        // const status = [];
        // const orderTypes = [];
        // if (!isNone(body.status)) {
        //     body.status.map((s) => status.push(CoreSwimmyStatus[s]));
        // }
        // if (!isNone(body.order_types)) {
        //     body.orderTypes.map((o) => orderTypes.push(CORE_SWIMMY_TYPES[o]));
        // }
        const params: ISearchCoreSwimmyParams = {
            tenantIds: body.tenantIds,
            orderTypes: body.orderTypes,
            actionType: body.actionType,
            status: body.status,
            requestedDateTimeFrom: parseInt(body.fromOrderDatetime, 10),
            requestedDateTimeTo: parseInt(body.toOrderDatetime, 10),
            sortBy: body.sortBy,
            skip: body.skip,
            limit: body.limit,
            isSortAsc: body.sortOrder === 'asc',
        };
        if (!isNone(body.searchType) && !isNone(body.searchValue)) {
            params.searchType = parseInt(body.searchType, 10);
            params.searchValue = body.searchValue;
        }
        if (!isNone(body.actionType)) {
            params.actionType = parseInt(body.actionType, 10);
        }
        return {
            params,
            errorCode: null
        };
    };

    private static processSearch = async (
        params: ISearchCoreSwimmyParams,
        request: HttpRequest,
        context: ExtendedPortalInvocationContext,
    ): Promise<ICoreSwimmySearchResult> => {
        const ands = [];
        switch (params.searchType) {
            case 0: // soId or requestOrderId で検索する
                ands.push({
                    $or: [
                        { requestOrderId: { $eq: params.searchValue } },
                        { soId: { $eq: params.searchValue } }
                    ]});
                break;
            case 1: // 回線番号で検索する
                ands.push({ kaisenNo: { $eq: params.searchValue } });
                break;
        }
        switch (params.actionType) {
            case 0:
                ands.push({ updateRetryUsers: { $exists: true } });
                break;
            case 1:
                ands.push({ updateStatusUsers: { $exists: true } });
                break;
            default:
                break;
        }
        if (!isNone(params.tenantIds)) {
            ands.push({ tenantId: { $in: params.tenantIds } });
        }
        if (!isNone(params.orderTypes)) {
            ands.push({ swimmyType: { $in: params.orderTypes } });
        }
        if (!isNone(params.status)) {
            ands.push({ status: { $in: params.status } });
        }
        if (!isNone(params.requestedDateTimeFrom) || !isNone(params.requestedDateTimeTo)) {
            const requestTime = [];
            if (!isNone(params.requestedDateTimeFrom)) {
                requestTime.push({ createdAt: { $gte: params.requestedDateTimeFrom } });
            }
            if (!isNone(params.requestedDateTimeTo)) {
                requestTime.push({ createdAt: { $lt: params.requestedDateTimeTo } });
            }
            ands.push({ $and: requestTime });
        }
        let query = {};
        if (ands.length > 0) {
            query = { $and: ands };
        }
        const sortAscendingInt = params.isSortAsc ? 1 : -1;
        let sort = {} as any;
        switch (params.sortBy) {
            case 'request_order_id':
                sort = { requestOrderId: sortAscendingInt };
                break;
            case 'order_type':
                sort = { swimmyType: sortAscendingInt };
                break;
            case 'tenant_id':
                sort = { tenantId: sortAscendingInt };
                break;
            case 'kaisen_no':
                sort = { kaisenNo: sortAscendingInt };
                break;
            default:
            case 'start_date':
                sort = { createdAt: sortAscendingInt };
                break;
        }
        const coreSwimmyService = new CoreSwimmyService(request, context);
        const {result, count} = await coreSwimmyService.search(query, sort, params.limit, params.skip);

        const searchResult = [];
        if (result.length > 0) {
            result.map((order) => {
                searchResult.push({
                    ...order,
                    statusName: !isNone(order.status) ? CORE_SWIMMY_STATUS_TEXT[order.status] : '',
                    swimmyTypeName: !isNone(order.swimmyType) ? CORE_SWIMMY_TYPES_TEXT[order.swimmyType] : '',
                });
            });
        }

        return {
            result: searchResult,
            count
        };
    };
}
