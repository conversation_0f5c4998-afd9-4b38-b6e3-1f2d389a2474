import { HttpRequest, HttpResponseInit } from "@azure/functions";
import PortalFunctionHandlerBase from "@/helpers/PortalFunctionHandlerBase";
import ExtendedPortalInvocationContext from "@/types/ExtendedPortalInvocationContext";
import { UserRole } from "@/types/Session";
import CoreSwimmyService from "@/services/coreSwimmyService";
import { CommonError } from "@/constants/commonError";
import { MAWP_ROLE_ID } from "@/constants/userRoles";
import Joi from 'joi';

const ToggleMaintenanceModeStatusSchema = Joi.object({
    switch: Joi.string().required().valid('on', 'off').messages({
        'string.base': CommonError.INVALID_PARAMETER,
        'any.required': CommonError.INVALID_PARAMETER,
        'string.empty': CommonError.INVALID_PARAMETER,
    }),
}).unknown();

export class ToggleMaintenanceModeStatus<PERSON>and<PERSON> extends PortalFunctionHandlerBase {
    public static AllowedRoles: UserRole[] = [
        MAWP_ROLE_ID.SUPER_USER,
        MAWP_ROLE_ID.NTTCOM_OPERATOR_USER,
        MAWP_ROLE_ID.TENANT_DAIHYO_USER,
        MAWP_ROLE_ID.TENANT_OPERATOR_USER,
        MAWP_ROLE_ID.TENANT_ETSURAN_USER,
    ];

    public static Handler = async (
        request: HttpRequest,
        context: ExtendedPortalInvocationContext,
    ): Promise<HttpResponseInit> => {
        try {
            context.log("ToggleMaintenanceModeStatus START");
            const { maintenanceMode } = await this.validateAndParseToggleMaintenanceModeStatusRequest(request);
            const coreSwimmyService = new CoreSwimmyService(request, context);
            const isMaintenance = await coreSwimmyService.toggleMaintenanceMode(maintenanceMode);
            context.error('CoreSwimmy-APIメンテナンス：', isMaintenance ? '「ON」 になりました。' : '「OFF」 になりました。');
            context.log("ToggleMaintenanceModeStatus END: ", isMaintenance ? "on" : "off");

            return {
                jsonBody: {
                    error: CommonError.NO_ERROR,
                    result: {
                        maintenance_mode: isMaintenance ? "on" : "off",
                    },
                },
            };
        } catch (error) {
            context.error(error);
            return { body: error };
        }
    };

    private static async validateAndParseToggleMaintenanceModeStatusRequest(request: HttpRequest): Promise<any> {
        let body: any;
        try{
            body = await request.json();
        } catch(error){
            throw Error("Request body is empty");
        }

        const { error } = ToggleMaintenanceModeStatusSchema.validate(body);
        if (error) {
            throw Error(error.details[0].message);
        }

        return {
            maintenanceMode: body.switch === 'on',
        };
    };
}
