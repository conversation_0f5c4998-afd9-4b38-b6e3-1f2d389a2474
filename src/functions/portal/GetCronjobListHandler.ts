import PortalFunctionHandlerBase from "@/helpers/PortalFunctionHandlerBase";
import { UserRole } from "@/types/Session";
import { MAWP_ROLE_ID } from "@/constants/userRoles";
import { HttpRequest, HttpResponseInit } from "@azure/functions";
import ExtendedPortalInvocationContext from "@/types/ExtendedPortalInvocationContext";
import { availableCronJobs } from "@/functions/portal/RestartCronjobHandler";

export class GetCronjobListHandler extends PortalFunctionHandlerBase {
    public static AllowedRoles: UserRole[] = [
        MAWP_ROLE_ID.SUPER_USER
    ];
    public static Handler = async (
        request: HttpRequest,
        context: ExtendedPortalInvocationContext,
    ): Promise<HttpResponseInit> => {
        try {
            context.log("GetCronjobListHandler START");
            return {
                status: 200,
                jsonBody: {
                    status: "OK",
                    cronjobList: availableCronJobs.map((cronjob) => {
                        return {
                            name: cronjob.name,
                            status: null            // Placeholder for status, as we don't have a way to check the status of the cron job right now
                        };
                    }),
                },
            };
        } catch (error) {
            context.error('GetCronjobListHandler - exception: ', error);
            return { body: error };
        }
    }
}