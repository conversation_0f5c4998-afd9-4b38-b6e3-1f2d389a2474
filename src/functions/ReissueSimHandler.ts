import { HttpRequest, HttpResponseInit } from "@azure/functions";
import ExtendedInvocationContext from "@/types/ExtendedInvocationContext";
import { SIMReissueTransactionPayload } from "@/types/coreSwimmyPayload";

import CoreSwimmyService from "@/services/coreSwimmyService";
import ReissueSimService from "@/core/service/impl/ReissueSimService";

import FunctionHandlerBase from "@/core/common/FunctionHandler";
import MvnoUtil from "@/core/common/MvnoUtil";
import Constants from "@/core/constant/Constants";
import ResultCdConstants from "@/core/constant/ResultCdConstants";

import ReissueSimInputDto from "@/core/dto/ReissueSimInputDto";
import ReissueSimOutputDto from "@/core/dto/ReissueSimOutputDto";

export class ReissueSimHandler extends FunctionHandlerBase {
    public static Meta = {
        localFunctionType: "53",
        url: Constants.LINE_SIM,
        getOrderType: (param: any) => null,
        processName: "SIM再発行",
    };

    public static Handler = async (
        request: HttpRequest,
        context: ExtendedInvocationContext,
    ): Promise<HttpResponseInit> => {
        const reissueSimService = new ReissueSimService(request, context);
        try {
            const param = context.jsonBody;
            const ipaddress = MvnoUtil.getClientIPAddress(request);
            const result = await reissueSimService.service(param, ipaddress);

            if (
                result.jsonBody.responseHeader.processCode ===
                ResultCdConstants.CODE_000000
            ) {
                if (result.additionalData.lineInfoUpdateError) {
                    context.warn(
                        "ReissueSimHandler: skip coreSwimmy call due to lineInfoUpdateError",
                    );
                } else {
                    await callCoreSwimmy(
                        request,
                        context,
                        param,
                        result,
                        ReissueSimHandler.Meta.getOrderType(param),
                    );
                }
            }

            return { jsonBody: result.jsonBody };
        } catch (error) {
            context.error(error);
            return { body: error };
        }
    };
}

/**
 * Prepare payload and call coreSwimmy service
 */
async function callCoreSwimmy(
    request: HttpRequest,
    context: ExtendedInvocationContext,
    param: ReissueSimInputDto,
    output: ReissueSimOutputDto,
    orderType: string | null,
) {
    if (output.additionalData.csvUnnecessary) {
        context.log(
            "ReissueSimHandler: skip coreSwimmy call due to csvUnnecessary",
        );
        return;
    }
    const payload: SIMReissueTransactionPayload = {
        receivedDate: context.responseHeader.receivedDate
            .substring(0, 10)
            .replace(/\//g, ""),
        frontSoId: param.targetSoId,
        coreSoId: context.responseHeader.apiProcessID,
        opfTenantId: param.tenantId,
        tenantId: param.targetTenantId,
        orderType,
        lineNo: param.lineNo,
        nNo: output.additionalData.nNo,
        simNo: param.sim_no,
        simType: param.sim_type,
        cardTypeId: param.cardTypeId,
        isESIMProfileRedownload: output.additionalData.isESIMProfileRedownload,
    };

    const coreSwimmy = new CoreSwimmyService(request, context);
    await coreSwimmy.registerSIMReissueTransaction(payload);
}
