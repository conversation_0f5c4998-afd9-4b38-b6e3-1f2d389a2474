import FunctionExternalApiHandlerBase from "@/core/common/FunctionExternalApiHandler";
import { HttpRequest } from "@azure/functions";
import ExtendedInvocationContext from "@/types/ExtendedInvocationContext";
import { HttpResponseInit } from "@azure/functions";
import LinePlanAcquisitionServiceTx from "@/core/service/impl/LinePlanAcquisitionServiceTx";
import Constants from "@/core/constant/Constants";
import LinePlanAcquisitionInputDto from "@/core/dto/LinePlanAcquisitionInputDto";
import LinePlanAcquisitionOutputDto from "@/core/dto/LinePlanAcquisitionOutputDto";
import ResultCdConstants from "@/core/constant/ResultCdConstants";
import { LinePlanAcquisitionPayload } from "@/types/coreSwimmyPayload";
import CoreSwimmyService from "@/services/coreSwimmyService";
import MvnoUtil from "@/core/common/MvnoUtil";
import Check from "@/core/common/Check";

export class LinePlanAcquisitionHandler extends FunctionExternalApiHandlerBase {
    public static Meta = {
        localFunctionType: "06",
        url: Constants.LINES_PLANCHARGE,
        getOrderType: (param: LinePlanAcquisitionInputDto) => Check.checkOrderType(
            param?.reserve_date,
            param?.reserve_flag,
            param?.reserve_soId,
        ),
        processName: "回線プラン変更",
    };

    public static Handler = async (
        request: HttpRequest,
        context: ExtendedInvocationContext,
    ): Promise<HttpResponseInit> => {
        const linePlanAcquisitionServiceTx = new LinePlanAcquisitionServiceTx(
            request,
            context,
        );
        try {
            const param = context.jsonBody;
            const ipaddress = MvnoUtil.getClientIPAddress(request);
            const result = await linePlanAcquisitionServiceTx.service(
                param,
                false,
                null, // ExecuteUserId
                null, // ExecuteTenantId
                ipaddress, // RemoteAddr
            );
            if (
                result.jsonBody.responseHeader.processCode ===
                ResultCdConstants.CODE_000000
            ) {
                await callCoreSwimmy(
                    request,
                    context,
                    param,
                    result,
                    LinePlanAcquisitionHandler.Meta.getOrderType(param),
                );
            }
            return { jsonBody: result.jsonBody };
        } catch (error) {
            context.error(error);
            return { body: error };
        }
    };
}

async function callCoreSwimmy(
    request: HttpRequest,
    context: ExtendedInvocationContext,
    param: LinePlanAcquisitionInputDto,
    output: LinePlanAcquisitionOutputDto,
    orderType: string | null,
) {
    if (output.additionalData.csvUnnecessary) {
        context.log(
            "LinePlanAcquisitionHandler: skip coreSwimmy call due to csvUnnecessary",
        );
        return;
    }

    const payload: LinePlanAcquisitionPayload = {
        receivedDate: context.responseHeader.receivedDate
            .substring(0, 10)
            .replace(/\//g, ""),
        frontSoId: param.targetSoId,
        coreSoId: context.responseHeader.apiProcessID,
        opfTenantId: param.tenantId,
        tenantId: param.tenantId,
        orderType,
        lineNo: param.lineNo,
        nNo: output.additionalData.nNo,
        resalePlanIdT: output.additionalData.resalePlanIdT,
        reqDate: output.additionalData.requestDate,
    };

    const coreSwimmy = new CoreSwimmyService(request, context);
    await coreSwimmy.registerLinePlanAcquisitionTransaction(payload);
}
