import { HttpRequest, HttpResponseInit } from "@azure/functions";
import ExtendedInvocationContext from "@/types/ExtendedInvocationContext";

import LineTerminalListAquisitionService from "@/core/service/impl/LineTerminalListAquisitionService";

import FunctionExternalApiHandlerBase from "@/core/common/FunctionExternalApiHandler";
import Constants from "@/core/constant/Constants";

export class LineTerminalListAquisitionHandler extends FunctionExternalApiHandlerBase {
    public static Meta = {
        localFunctionType: "07",
        url: Constants.LINES_LIST,
        getOrderType: (param: any) => null,
        processName: "回線リスト確認",
    };

    public static Handler = async (
        request: HttpRequest,
        context: ExtendedInvocationContext,
    ): Promise<HttpResponseInit> => {
        const lineTerminalListAquisitionService =
            new LineTerminalListAquisitionService(request, context);
        try {
            const param = context.jsonBody;
            const result = await lineTerminalListAquisitionService.service(
                param,
            );

            return {
                jsonBody: {
                    responseHeader: {
                        sequenceNo: result.responseHeader.sequenceNo,
                        processCode: result.responseHeader.processCode,
                        receivedDate: result.responseHeader.receivedDate,
                        apiProcessID: result.responseHeader.apiProcessID,
                    },
                    lineList: result.lineList,
                },
            };
        } catch (error) {
            context.error(error);
            return { body: error };
        }
    };
}
