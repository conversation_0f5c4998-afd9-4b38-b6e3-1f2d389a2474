import { HttpRequest, HttpResponseInit } from "@azure/functions";
import Check from "@/core/common/Check";
import FunctionExternalApiHandlerBase from "@/core/common/FunctionExternalApiHandler";
import Constants from "@/core/constant/Constants";
import MsgKeysConstants from "@/core/constant/MsgKeysConstants";
import ResultCdConstants from "@/core/constant/ResultCdConstants";
import CheckResultBean from "@/core/dto/CheckResultBean";
import LineGroupPlanChangeInputDto from "@/core/dto/LineGroupPlanChangeInputDto";
import LineGroupPlanChangeOutputDto from "@/core/dto/LineGroupPlanChangeOutputDto";
import LineGroupPlanChangeService from "@/core/service/impl/LineGroupPlanChangeService";
import ExtendedInvocationContext from "@/types/ExtendedInvocationContext";
import CustomLogger from "@/utils/logger";

export class LineGroupPlanChangeHandler extends FunctionExternalApiHandlerBase {
    public static Meta = {
        localFunctionType: "20",
        url: Constants.LINEGROUP_PLANCHARGE,
        getOrderType: (param: LineGroupPlanChangeInputDto) =>
            Check.checkOrderType(
                param.reserve_date,
                param.reserve_flag,
                param.reserve_soId,
            ),
        processName: "回線グループプラン変更",
    };

    public static Handler = async (
        request: HttpRequest,
        context: ExtendedInvocationContext,
    ): Promise<HttpResponseInit> => {
        const lineGroupPlanChangeService = new LineGroupPlanChangeService(
            request,
            context,
        );
        try {
            const param = context.jsonBody as LineGroupPlanChangeInputDto;
            const result = await lineGroupPlanChangeService.service(
                param,
                false,
                null, // request.remoteAddr
            );
            return {
                jsonBody: result.jsonBody,
            };
        } catch (error) {
            context.error("LineGroupPlanChangeHandler error:", error);
            context.responseHeader.processCode = ResultCdConstants.CODE_999999;
            return await this.CommonCheckCatchErrorHandler(
                request,
                context,
                error,
            );
        }
    };

    public static CommonCheckCatchErrorHandler = (
        request: HttpRequest,
        context: ExtendedInvocationContext,
        error: Error,
    ): Promise<HttpResponseInit> => {
        const logger = new CustomLogger(request, context);
        const param = context.jsonBody as LineGroupPlanChangeInputDto;
        // エラーログ出力
        logger.error(
            error,
            param?.tenantId,
            param?.requestHeader?.sequenceNo,
            MsgKeysConstants.APCOME0401,
            error?.message,
        );
        const dto: LineGroupPlanChangeOutputDto = {
            jsonBody: {
                responseHeader: {
                    ...context?.responseHeader,
                    processCode: ResultCdConstants.CODE_999999,
                },
            },
        };
        return Promise.resolve(dto);
    };

    public static CommonCheckFailedHandler = async (
        request: HttpRequest,
        context: ExtendedInvocationContext,
        errorMessage: string,
    ): Promise<HttpResponseInit> => {
        // call soManagement when 共通チェック failed
        context.log(
            "LineGroupPlanChangeHandler CommonCheckFailedHandler:",
            errorMessage,
        );
        const param = context.jsonBody as LineGroupPlanChangeInputDto;
        const lineGroupPlanChangeService = new LineGroupPlanChangeService(
            request,
            context,
        );
        const resultBean: CheckResultBean = {
            checkResult: true, // already passed 共通チェック in base handler
            processingCode: context?.responseHeader?.processCode,
            others: context?.responseHeader?.apiProcessID,
            errorMassage: "",
        };

        const orderType = LineGroupPlanChangeHandler.Meta.getOrderType(param);
        await lineGroupPlanChangeService.soManagement(
            param,
            resultBean,
            resultBean.processingCode,
            context?.responseHeader?.receivedDate,
            param?.requestHeader?.functionType,
            orderType,
            param?.reserve_date,
        );
        const dto: LineGroupPlanChangeOutputDto = {
            jsonBody: {
                responseHeader: {
                    ...context?.responseHeader,
                },
            },
        };
        return dto;
    };
}
