import { HttpRequest, HttpResponseInit } from "@azure/functions";
import ExtendedInvocationContext from "@/types/ExtendedInvocationContext";

import LineSuspendService from "@/core/service/impl/LineSuspendService";

import FunctionHandlerBase from "@/core/common/FunctionHandler";
import Constants from "@/core/constant/Constants";

export class LineSuspendHandler extends FunctionHandlerBase {
    public static Meta = {
        localFunctionType: "58",
        url: Constants.LINES_SUSPEND,
        getOrderType: (param: any) => null,
        processName: "利用中断/再開/サスペンド",
    };

    public static Handler = async (
        request: HttpRequest,
        context: ExtendedInvocationContext,
    ): Promise<HttpResponseInit> => {
        const lineSuspendService = new LineSuspendService(request, context);
        try {
            const param = context.jsonBody;
            const result = await lineSuspendService.service(param);

            return { jsonBody: result.jsonBody };
        } catch (error) {
            context.error(error);
            return { body: error };
        }
    };
}
