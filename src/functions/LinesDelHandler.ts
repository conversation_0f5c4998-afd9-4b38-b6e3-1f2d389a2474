import { HttpRequest, HttpResponseInit } from "@azure/functions";
import ExtendedInvocationContext from "@/types/ExtendedInvocationContext";
import { LineDeleteTransactionPayload } from "@/types/coreSwimmyPayload";

import APILinesDAO from "@/core/dao/APILinesDAO";
import LineDeleteInputDto from "@/core/dto/LineDeleteInputDto";
import LineDeleteOutputDto from "@/core/dto/LineDeleteOutputDto";

import CoreSwimmyService from "@/services/coreSwimmyService";
import LineDeleteService from "@/core/service/impl/LineDeleteService";

import MvnoUtil from "@/core/common/MvnoUtil";
import Constants from "@/core/constant/Constants";
import ResultCdConstants from "@/core/constant/ResultCdConstants";
import Check from "@/core/common/Check";
import CheckUtil from "@/core/common/CheckUtil";
import FunctionHandlerBase from "@/core/common/FunctionHandler";
import { fmtDate<PERSON>or<PERSON><PERSON><PERSON> } from "@/utils";

export class LinesDelHandler extends FunctionHandlerBase {
    public static Meta = {
        localFunctionType: "52",
        url: Constants.LINE_DEL,
        getOrderType: (param: LineDeleteInputDto) => {
            let reserveDate = param.reserve_date;
            if (!CheckUtil.checkIsNotNull(reserveDate)) {
                reserveDate += " 02:30";
            }
            return Check.checkOrderType(
                reserveDate,
                param.reserve_flag,
                param.reserve_soId,
            );
        },
        processName: "回線廃止機能",
    };

    public static Handler = async (
        request: HttpRequest,
        context: ExtendedInvocationContext,
    ): Promise<HttpResponseInit> => {
        // initialize psql data source if needed
        const apiLinesDAO = new APILinesDAO(request, context);
        const lineDeleteService = new LineDeleteService(request, context);
        try {
            // // get request body and parse it
            // const param = (await request.json()) as LineDeleteInputDto;
            const param = context.jsonBody;
            // lineDeleteService.service(null, null);
            const ipaddress = MvnoUtil.getClientIPAddress(request);
            const result = await lineDeleteService.service(param, ipaddress);

            // super.info(param.tenantId, param.requestHeader.sequenceNo,
            //     		MsgKeysConstants.APLDLI0002, PROCESS_NAME, lineDeleteService.getConCount(),
            //     		tenantId, await lineDeleteService.getTenantConCount(param.tenantId), strApiId);

            // REST送信信電文出力
            // RestLogSend.logOutput(MvnoUtil.getLocalIp(), request.getRemoteAddr(),
            // param.getRequestHeader().getSequenceNo(), result);

            if (
                result.jsonBody.responseHeader.processCode ===
                ResultCdConstants.CODE_000000
            ) {
                await callCoreSwimmy(
                    request,
                    context,
                    param,
                    result,
                    LinesDelHandler.Meta.getOrderType(param),
                );
            }

            return { jsonBody: result.jsonBody };
        } catch (error) {
            // console.error(error);
            context.error(error);
            return { body: error };
        }
    };
}

async function callCoreSwimmy(
    request: HttpRequest,
    context: ExtendedInvocationContext,
    param: LineDeleteInputDto,
    output: LineDeleteOutputDto,
    orderType: string | null,
) {
    if (output.additionalData.csvUnnecessary) {
        context.log(
            "LinesDelHandler: skip coreSwimmy call due to csvUnnecessary",
        );
        return;
    }
    const payload: LineDeleteTransactionPayload = {
        receivedDate: context.responseHeader.receivedDate
            .substring(0, 10)
            .replace(/\//g, ""),
        frontSoId: param.targetSoId,
        coreSoId: context.responseHeader.apiProcessID,
        opfTenantId: param.tenantId,
        tenantId: param.targetTenantId,
        orderType,
        lineNo: param.lineNo,
        nNo: output.additionalData.nNo,
        mnpOutFlag: param.mnpOutFlag,
        reserveDate: fmtDateForHaishi(param.reserve_date),
    };

    const coreSwimmy = new CoreSwimmyService(request, context);
    await coreSwimmy.registerLineDelTransaction(payload);
}
