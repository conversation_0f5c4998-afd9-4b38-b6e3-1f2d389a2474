import { HttpRequest, HttpResponseInit } from "@azure/functions";
import { retryQuery } from "@/helpers/queryHelper";
import AutoModBucketDao from "@/core/dao/AutoModBucketDao";
import { usePsql } from "@/database/psql";
import BatchTimesDao from "@/core/dao/BatchTimesDao";
import config from "config";
import MsgKeysConstants from "@/core/constant/MsgKeysConstants";
import CommonUtil from "@/core/common/CommonUtil";
import ResultCdConstants from "@/core/constant/ResultCdConstants";
import PortalFunctionHandlerBase from "@/helpers/PortalFunctionHandlerBase";
import { UserRole } from "@/types/Session";
import { MAWP_ROLE_ID } from "@/constants/userRoles";
import ExtendedPortalInvocationContext from "@/types/ExtendedPortalInvocationContext";
import { CommonError } from "@/constants/commonError";

export class ResetAutoApiBatchTimeHandler extends PortalFunctionHandlerBase {
    public static AllowedRoles: UserRole[] = [
        MAWP_ROLE_ID.SUPER_USER,
        MAWP_ROLE_ID.NTTCOM_OPERATOR_USER
    ];

    /** アプリケーション用DBリトライ最大回数 */
    public static apDbRetryMaxCnt = config.get<number>("mvno.ApDbRetryMaxCnt");
    /** アプリケーション用DBリトライ間隔 */
    public static apDbRetryInterval = config.get<number>("mvno.ApDbRetryInterval");

    public static Handler = async (
        request: HttpRequest,
        context: ExtendedPortalInvocationContext,
    ): Promise<HttpResponseInit> => {
        context.log("ResetAutoApiBatchTimeHandler START");
        try {
            const sequelize = await usePsql();
            const batchTime = await retryQuery(context,
                "getAutoApiBatchTimes",
                async () => {
                    return await BatchTimesDao.getAutoApiBatchTimes(context, sequelize);
                },
                this.apDbRetryMaxCnt,
                this.apDbRetryInterval,
            );

            if (batchTime !== null) {
                const preExecTimeStr: string = CommonUtil.convDateTimeToString(batchTime.execTime);
                // Reset the batch time to the current time
                await retryQuery(context,
                    "UpdateExecTime",
                    async () => {
                        return await BatchTimesDao.updAutoApiBatchTimes(context, sequelize, preExecTimeStr, preExecTimeStr);
                    },
                    this.apDbRetryMaxCnt,
                    this.apDbRetryInterval,
                );
            }

            context.log("ResetAutoApiBatchTimeHandler Ended");
            return {
                jsonBody: {
                    error: CommonError.NO_ERROR,
                },
            };
        } catch (error) {
            context.error('ResetAutoApiBatchTimeHandler - exception: ', error, error.body);
            return { body: error };
        }
    };
}