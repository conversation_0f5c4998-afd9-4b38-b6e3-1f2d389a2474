import FunctionExternalApiHandlerBase from "@/core/common/FunctionExternalApiHandler";
import Constants from "@/core/constant/Constants";
import { HttpRequest, HttpResponseInit } from "@azure/functions";
import ExtendedInvocationContext from "@/types/ExtendedInvocationContext";
import ResultCdConstants from "@/core/constant/ResultCdConstants";
import MvnoUtil from "@/core/common/MvnoUtil";
import SODetailInfoConsultService from "@/core/service/impl/SODetailInfoConsultService";
import ServiceOrdersEntity from "@/core/entity/ServiceOrdersEntity";

export class SODetailInfoConsultHandler extends FunctionExternalApiHandlerBase {
    public static Meta = {
        localFunctionType: "17",
        url: Constants.SODESC_CHARGE,
        getOrderType: (param: any) => null,
        processName: "SO詳細",
    };

    public static Handler = async (
        request: HttpRequest,
        context: ExtendedInvocationContext,
    ): Promise<HttpResponseInit> => {
        const soDetailInfoConsultService = new SODetailInfoConsultService(
            request,
            context,
        );

        try {
            const param = context.jsonBody;
            const result = await soDetailInfoConsultService.service(param);     // no need to split the result into different cases as typescript allows union types

            return { jsonBody: result.jsonBody };
        } catch (error) {
            context.error(error);
            context.responseHeader.processCode = ResultCdConstants.CODE_999999;
            return this.CommonCheckCatchErrorHandler(request, context, error);
        }
    }

    public static CommonCheckFailedHandler = async (
        request: HttpRequest,
        context: ExtendedInvocationContext,
        errorMessage: string,
    ): Promise<HttpResponseInit> => {
        const soDetailInfoConsultService = new SODetailInfoConsultService(
            request,
            context,
        );

        const receivedDate: string = context?.responseHeader?.receivedDate ?? MvnoUtil.getDateTimeNow();
        const handleCode = context.responseHeader?.processCode ?? ResultCdConstants.CODE_999999;
        const apiHandleId = context.responseHeader?.apiProcessID ?? "";
        const param = context.jsonBody;
        const tenantId = param?.tenantId;
        const sequenceNo: string = param.requestHeader.sequenceNo;
        const serviceOrdersEntity: ServiceOrdersEntity = ServiceOrdersEntity.build({
            serviceOrderId: null,
            orderDate: null,
            reserveDate: null,
            execDate: null,
            functionType: null
        });
        const tenantLevelList: string[] = null;
        const response = soDetailInfoConsultService.returnEdit(param, handleCode, apiHandleId, errorMessage, tenantId,
            sequenceNo, serviceOrdersEntity, tenantLevelList, receivedDate);
        return { jsonBody: response.jsonBody };
    }

    public static CommonCheckCatchErrorHandler = async (
        request: HttpRequest,
        context: ExtendedInvocationContext,
        error: Error,
    ): Promise<HttpResponseInit> => {
        const soDetailInfoConsultService = new SODetailInfoConsultService(
            request,
            context,
        );
        const receivedDate: string = context?.responseHeader?.receivedDate ?? MvnoUtil.getDateTimeNow();
        const handleCode = context.responseHeader?.processCode ?? ResultCdConstants.CODE_999999;
        const apiHandleId = context.responseHeader?.apiProcessID ?? "";
        const param = context.jsonBody;
        const tenantId = param?.tenantId;
        const sequenceNo: string = param.requestHeader.sequenceNo;
        const serviceOrdersEntity: ServiceOrdersEntity = ServiceOrdersEntity.build({
            serviceOrderId: null,
            orderDate: null,
            reserveDate: null,
            execDate: null,
            functionType: null
        });
        const tenantLevelList: string[] = null;
        const response = soDetailInfoConsultService.returnEdit(param, handleCode, apiHandleId, error.message, tenantId,
            sequenceNo, serviceOrdersEntity, tenantLevelList, receivedDate);
        return { jsonBody: response.jsonBody };
    }
}