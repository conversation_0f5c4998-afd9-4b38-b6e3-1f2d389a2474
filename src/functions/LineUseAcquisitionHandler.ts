import { HttpRequest, HttpResponseInit } from "@azure/functions";

import Constants from "@/core/constant/Constants";
import MsgKeysConstants from "@/core/constant/MsgKeysConstants";
import ResultCdConstants from "@/core/constant/ResultCdConstants";

import FunctionExternalApiHandlerBase from "@/core/common/FunctionExternalApiHandler";
import MvnoUtil from "@/core/common/MvnoUtil";

import LineUseAcquisitionInputDto from "@/core/dto/LineUseAcquisitionInputDto";
import ResponseHeader from "@/core/dto/ResponseHeader";
import { getEmpty_LineUseAcquisitionIpAddressOutput } from "@/core/dto/LineUseAcquisitionIpAddressOutputDto";

import LineUseAcquisitionService from "@/core/service/impl/LineUseAcquisitionService";

import ExtendedInvocationContext from "@/types/ExtendedInvocationContext";
import CustomLogger from "@/utils/logger";
import { getStringParameter } from "@/types/parameter.string";

export class LineUseAcquisitionHandler extends FunctionExternalApiHandlerBase {
    public static Meta = {
        localFunctionType: "02",
        url: Constants.LINES_TRAFFIC,
        getOrderType: (param: any) => null,
        processName: "回線運用情報参照",
    };

    public static Handler = async (
        request: HttpRequest,
        context: ExtendedInvocationContext,
    ): Promise<HttpResponseInit> => {
        const lineUseAcquisitionService = new LineUseAcquisitionService(
            request,
            context,
        );
        try {
            const param = context.jsonBody;
            const result = await lineUseAcquisitionService.service(param);
            return { jsonBody: result.jsonBody };
        } catch (error) {
            context.error(error);
            context.responseHeader.processCode = ResultCdConstants.CODE_999999;
            return this.CommonCheckCatchErrorHandler(request, context, error);
        }
    };

    public static CommonCheckFailedHandler = async (
        request: HttpRequest,
        context: ExtendedInvocationContext,
        errorMessage: string,
    ): Promise<HttpResponseInit> => {
        const lineUseAcquisitionService = new LineUseAcquisitionService(
            request,
            context,
        );
        const handleCode =
            context.responseHeader?.processCode ??
            ResultCdConstants.CODE_999999;
        const apiHandleId = context?.responseHeader?.apiProcessID ?? "";
        const param = context.jsonBody as LineUseAcquisitionInputDto;
        const version = getStringParameter(param?.version);
        const lineUsageStatus: string = null;
        const lineUseAcquisitionOutputDto =
            lineUseAcquisitionService.returnEdit(
                param,
                handleCode,
                apiHandleId,
                null,
                "",
                false,
                errorMessage,
                context.responseHeader?.receivedDate ??
                    MvnoUtil.getDateTimeNow(),
                null,
                null,
                "",
                null,
                null,
                null,
                null,
                version,
                lineUsageStatus,
            );
        const logger = new CustomLogger(request, context);
        logger.debug(
            param.tenantId,
            param?.requestHeader?.sequenceNo,
            MsgKeysConstants.APLTID0002,
            "LineUseAcquisitionService",
            "service",
        );
        return { jsonBody: lineUseAcquisitionOutputDto.jsonBody };
    };

    public static CommonCheckCatchErrorHandler = async (
        request: HttpRequest,
        context: ExtendedInvocationContext,
        error: Error,
    ): Promise<HttpResponseInit> => {
        context.log("CommonCheckCatchErrorHandler");
        const param = context.jsonBody as LineUseAcquisitionInputDto;
        // エラーログ出力
        const logger = new CustomLogger(request, context);
        logger.error(
            error,
            param?.tenantId,
            param?.requestHeader?.sequenceNo,
            MsgKeysConstants.APCOME0401,
            error?.message,
        );

        const responseHeader: ResponseHeader = {
            sequenceNo:
                context.responseHeader?.sequenceNo ??
                param?.requestHeader?.sequenceNo ??
                "",
            receivedDate:
                context.responseHeader?.receivedDate ??
                MvnoUtil.getDateTimeNow(),
            processCode: ResultCdConstants.CODE_999999,
            apiProcessID: context.responseHeader?.apiProcessID ?? "",
        };

        if ("1".equals(getStringParameter(param?.getIpAddress))) {
            const dtoIp =
                getEmpty_LineUseAcquisitionIpAddressOutput(responseHeader);
            return dtoIp;
        }

        // use returnEmptyPram for remaining cases
        const service = new LineUseAcquisitionService(request, context);
        const result = service.returnEmptyPram(
            false,
            getStringParameter(param?.addOption),
            getStringParameter(param?.version),
        );
        result.jsonBody.responseHeader = responseHeader;
        return result;
    };
}
