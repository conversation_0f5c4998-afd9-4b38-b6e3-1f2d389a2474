import Constants from "@/core/constant/Constants";
import { HttpRequest, HttpResponseInit } from "@azure/functions";
import ExtendedInvocationContext from "@/types/ExtendedInvocationContext";
import ResultCdConstants from "@/core/constant/ResultCdConstants";
import FunctionExternalApiHandlerBase from "@/core/common/FunctionExternalApiHandler";
import LineGroupUseAcquisitionService from "@/core/service/impl/LineGroupUseAcquisitionService";
import MvnoUtil from "@/core/common/MvnoUtil";
import { isSQLException } from "@/helpers/queryHelper";
import MsgKeysConstants from "@/core/constant/MsgKeysConstants";

export class LineGroupUseAcquisitionHandler extends FunctionExternalApiHandlerBase {
    public static Meta = {
        localFunctionType: "09",
        url: Constants.LINESGROUP_TRAFFIC,
        getOrderType: (param: any) => null,
        processName: "回線グループ運用情報参照",
    }

    public static Handler = async (
        request: HttpRequest,
        context: ExtendedInvocationContext
    ): Promise<HttpResponseInit> => {
        const lineGroupUserAcquisitionService = new LineGroupUseAcquisitionService(
            request,
            context
        );
        try {
            const param = context.jsonBody;
            const result = await lineGroupUserAcquisitionService.service(param);

            return { jsonBody: result.jsonBody };
        } catch (error) {
            context.error(error);
            context.responseHeader.processCode = ResultCdConstants.CODE_999999;
            return this.CommonCheckCatchErrorHandler(request, context, error);
        }
    }

    public static CommonCheckFailedHandler = async (
        request: HttpRequest,
        context: ExtendedInvocationContext,
        errorMessage: string,
    ): Promise<HttpResponseInit> => {
        const lineGroupUseAcquisition = new LineGroupUseAcquisitionService(
            request,
            context,
        );
        const cheatCode = context.responseHeader?.processCode ?? ResultCdConstants.CODE_999999;
        const apiProcessID = context.responseHeader?.apiProcessID ?? "";
        const param = context.jsonBody;
        const isPrivate = false;                // hardcode to false
        const receivedDate = context.responseHeader?.receivedDate ?? MvnoUtil.getDateTimeNow();
        const serviceModle: string = "";
        const trafficPreviousMonth: string = null;
        const trafficBeforehandMonth: string = null;
        const trafficPreviousMonthCouponOff: string = null;
        const trafficBeforehandMonthCouponOff: string = null;
        const document = null;
        const output = lineGroupUseAcquisition.returnEdit(param, cheatCode, apiProcessID, serviceModle,
            document, errorMessage, isPrivate, receivedDate,
            trafficPreviousMonth, trafficBeforehandMonth,
            trafficPreviousMonthCouponOff, trafficBeforehandMonthCouponOff);
        return output;
    }

    public static CommonCheckCatchErrorHandler = async (
        request: HttpRequest,
        context: ExtendedInvocationContext,
        error: Error,
    ): Promise<HttpResponseInit> => {
        const lineGroupUseAcquisition = new LineGroupUseAcquisitionService(
            request,
            context,
        );
        const param = context.jsonBody;
        const tenantId = param?.tenantId;
        let cheatCode: string;
        const functionType = param.requestHeader.functionType;
        if (isSQLException(error)) {
            cheatCode = context.responseHeader?.processCode ?? ResultCdConstants.CODE_999999;
            lineGroupUseAcquisition.generateErrorMessage(tenantId, param.requestHeader.sequenceNo, MsgKeysConstants.APCOME0402, functionType);
        } else {
            cheatCode = context.responseHeader?.processCode;
            lineGroupUseAcquisition.generateErrorMessage(tenantId, param.requestHeader.sequenceNo, MsgKeysConstants.APCOME0401, error);
        }
        const apiProcessID = context.responseHeader?.apiProcessID ?? "";
        const isPrivate = false;                // hardcode to false
        const receivedDate = context.responseHeader?.receivedDate ?? MvnoUtil.getDateTimeNow();
        const serviceModle: string = "";
        const trafficPreviousMonth: string = null;
        const trafficBeforehandMonth: string = null;
        const trafficPreviousMonthCouponOff: string = null;
        const trafficBeforehandMonthCouponOff: string = null;
        const document = null;
        const output = lineGroupUseAcquisition.returnEdit(param, cheatCode, apiProcessID, serviceModle,
            document, error.message, isPrivate, receivedDate,
            trafficPreviousMonth, trafficBeforehandMonth,
            trafficPreviousMonthCouponOff, trafficBeforehandMonthCouponOff);
        return output;
    }
}