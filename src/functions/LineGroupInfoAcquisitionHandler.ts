import { HttpRequest, HttpResponseInit } from "@azure/functions";
import ExtendedInvocationContext from "@/types/ExtendedInvocationContext";
import LineGroupInfoAcquisitionService from "@/core/service/impl/LineGroupInfoAcquisitionService";

import MvnoUtil from "@/core/common/MvnoUtil";
import Constants from "@/core/constant/Constants";
import ResultCdConstants from "@/core/constant/ResultCdConstants";
import FunctionExternalApiHandlerBase from "@/core/common/FunctionExternalApiHandler";
import MsgKeysConstants from "@/core/constant/MsgKeysConstants";
import LinesGroupInfoOutputDto from "@/core/dto/LinesGroupInfoOutputDto";
import { isSQLException } from "@/helpers/queryHelper";

export class LineGroupInfoAcquisitionHandler extends FunctionExternalApiHandlerBase {
    public static Meta = {
        localFunctionType: "08",
        url: Constants.LINESGROUP_INFO,
        getOrderType: (param: any) => null,
        processName: "回線基本情報参照機能",
    };

    public static Handler = async (
        request: HttpRequest,
        context: ExtendedInvocationContext,
    ): Promise<HttpResponseInit> => {
        const lineGroupInfoAcquisitionService = new LineGroupInfoAcquisitionService(
            request,
            context,
        );
        try {
            const param = context.jsonBody;
            const result = await lineGroupInfoAcquisitionService.service(param);

            return { jsonBody: result.jsonBody };
        } catch (error) {
            context.error(error);
            context.responseHeader.processCode = ResultCdConstants.CODE_999999;
            return this.CommonCheckCatchErrorHandler(request, context, error);
        }
    };

    public static CommonCheckFailedHandler = async (
        request: HttpRequest,
        context: ExtendedInvocationContext,
        errorMessage: string,
    ): Promise<HttpResponseInit> => {
        const lineGroupInfoAcquisitionService = new LineGroupInfoAcquisitionService(
            request,
            context,
        );
        const handleCode = context.responseHeader?.processCode ?? ResultCdConstants.CODE_999999;
        const apiProcessId = context.responseHeader?.apiProcessID ?? "";
        context.log(context.jsonBody.addOption);
        const param = context.jsonBody;
        const tenantId = param?.tenantId;
        const isPrivate = false;                // hardcode to false
        const receivedDate = context.responseHeader?.receivedDate ?? MvnoUtil.getDateTimeNow();
        const sequenceNo = context?.jsonBody?.requestHeader?.sequenceNo;
        const potalGroupPlanID = null;
        const potalGroupPlanName = null;
        // ロジック処理の処理;
        // ヘッダ情報を設定する
        const output = lineGroupInfoAcquisitionService.setHeaderInfo(sequenceNo, handleCode, apiProcessId, isPrivate, receivedDate);
        // 変数「処理コード」を判定し,ボディ情報（異常系）を設定する
        lineGroupInfoAcquisitionService.setBodyInfo(output, null, handleCode, potalGroupPlanID, potalGroupPlanName, isPrivate, errorMessage, tenantId, sequenceNo);
        lineGroupInfoAcquisitionService.generateDebugMessage(param.tenantId, param.requestHeader.sequenceNo,MsgKeysConstants.APGBID0002,
            'LineGroupInfoAcquisitionService', "service");
        // 返却値を返却し、処理終了
        return output;
    }

    public static CommonCheckCatchErrorHandler = async (
        request: HttpRequest,
        context: ExtendedInvocationContext,
        error: Error,
    ): Promise<HttpResponseInit> => {
        const lineGroupInfoAcquisitionService = new LineGroupInfoAcquisitionService(
            request,
            context,
        );
        const apiProcessId = context.responseHeader?.apiProcessID ?? "";
        context.log(context.jsonBody.addOption);
        const param = context.jsonBody;
        const tenantId = param?.tenantId;
        const isPrivate = false;                // hardcode to false
        const receivedDate = context.responseHeader?.receivedDate ?? MvnoUtil.getDateTimeNow();
        const potalGroupPlanID = null;
        const potalGroupPlanName = null;
        const functionType = param.requestHeader.functionType;
        // ロジック処理の処理;
        // ヘッダ情報を設定する
        let handleCode: string;
        if (isSQLException(error)) {
            handleCode = context.responseHeader?.processCode ?? ResultCdConstants.CODE_999999;
            lineGroupInfoAcquisitionService.generateErrorMessage(tenantId, param.requestHeader.sequenceNo, MsgKeysConstants.APCOME0402, functionType);
        } else {
            lineGroupInfoAcquisitionService.generateErrorMessage(tenantId, param.requestHeader.sequenceNo, MsgKeysConstants.APCOME0401, error);
            handleCode = ResultCdConstants.CODE_999999;
        }
        const output = lineGroupInfoAcquisitionService.setHeaderInfo(param.requestHeader.sequenceNo, handleCode, apiProcessId, isPrivate, receivedDate);
        // 変数「処理コード」を判定し,ボディ情報（異常系）を設定する
        lineGroupInfoAcquisitionService.setBodyInfo(output, null, handleCode, potalGroupPlanID, potalGroupPlanName, isPrivate, error.message, tenantId, param.requestHeader.sequenceNo);
        if (!isSQLException(error)) {
            lineGroupInfoAcquisitionService.generateErrorMessage(tenantId, param.requestHeader.sequenceNo, MsgKeysConstants.APGBID0002,
                'LineGroupInfoAcquisitionService', "service");
        }
        // 返却値を返却し、処理終了
        return output;
    }
}