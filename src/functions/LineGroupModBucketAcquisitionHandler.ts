import { HttpRequest, HttpResponseInit } from "@azure/functions";
import Constants from "@/core/constant/Constants";
import Check from "@/core/common/Check";
import FunctionExternalApiHandlerBase from "@/core/common/FunctionExternalApiHandler";
import MsgKeysConstants from "@/core/constant/MsgKeysConstants";
import ResultCdConstants from "@/core/constant/ResultCdConstants";
import LineGroupModBucketAcquisitionInputDto from "@/core/dto/LineGroupModBucketAcquisitionInputDto";
import LineGroupModBucketAcquisitionOutputDto from "@/core/dto/LineGroupModBucketAcquisitionOutputDto";
import LineGroupModBucketAcquisitionService from "@/core/service/impl/LineGroupModBucketAcquisitionService";
import ExtendedInvocationContext from "@/types/ExtendedInvocationContext";
import CustomLogger from "@/utils/logger";

export class LineGroupModBucketAcquisitionHandler extends FunctionExternalApiHandlerBase {
    public static Meta = {
        localFunctionType: "15",
        url: Constants.LINEGROUP_MOD_BUCKET,
        getOrderType: (param: LineGroupModBucketAcquisitionInputDto) =>
            Check.checkOrderType(
                param?.reserve_date,
                param?.reserve_flag,
                param?.reserve_soId,
            ),
        processName: "回線グループ基本容量変更",
    };

    public static Handler = async (
        request: HttpRequest,
        context: ExtendedInvocationContext,
    ): Promise<HttpResponseInit> => {
        const lineGroupModBucketAcquisitionService =
            new LineGroupModBucketAcquisitionService(request, context);
        try {
            const param =
                context.jsonBody as LineGroupModBucketAcquisitionInputDto;
            const result = await lineGroupModBucketAcquisitionService.service(
                param,
                false,
                null, // executeUserId
                null, // executeTenantId
                null, // remoteAddr
            );

            return {
                jsonBody: result.jsonBody,
            };
        } catch (error) {
            context.error("LineGroupModBucketAcquisitionHandler error:", error);
            context.responseHeader.processCode = ResultCdConstants.CODE_999999;
            return this.CommonCheckCatchErrorHandler(request, context, error);
        }
    };

    public static CommonCheckCatchErrorHandler = (
        request: HttpRequest,
        context: ExtendedInvocationContext,
        error: Error,
    ): Promise<HttpResponseInit> => {
        const logger = new CustomLogger(request, context);
        const param = context.jsonBody as LineGroupModBucketAcquisitionInputDto;
        // エラーログ出力
        logger.error(
            error,
            param?.tenantId,
            param?.requestHeader?.sequenceNo,
            MsgKeysConstants.APCOME0401,
            error?.message,
        );

        const dto: LineGroupModBucketAcquisitionOutputDto = {
            jsonBody: {
                responseHeader: {
                    ...context?.responseHeader,
                    processCode: ResultCdConstants.CODE_999999,
                },
                capacity: "",
            },
        };
        return Promise.resolve(dto);
    };

    public static CommonCheckFailedHandler = (
        request: HttpRequest,
        context: ExtendedInvocationContext,
        errorMessage: string,
    ): Promise<HttpResponseInit> => {
        const response: LineGroupModBucketAcquisitionOutputDto = {
            jsonBody: {
                responseHeader: context?.responseHeader,
                capacity: "",
            },
        };
        return Promise.resolve(response);
    };
}
