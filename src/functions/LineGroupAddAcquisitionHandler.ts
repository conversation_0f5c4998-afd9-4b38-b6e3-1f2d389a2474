import FunctionExternalApiHandlerBase from "@/core/common/FunctionExternalApiHandler";
import Constants from "@/core/constant/Constants";
import { HttpRequest, HttpResponseInit } from "@azure/functions";
import ExtendedInvocationContext from "@/types/ExtendedInvocationContext";
import ResultCdConstants from "@/core/constant/ResultCdConstants";
import LineGroupAddAcquisitionService from "@/core/service/impl/LineGroupAddAcquisitionService";
import MvnoUtil from "@/core/common/MvnoUtil";
import LineGroupAddAcquisitionInputDto from "@/core/dto/LineGroupAddAcquisitionInputDto";
import Check from "@/core/common/Check";
import LineGroupAddAcquisitionOutputDto from "@/core/dto/LineGroupAddAcquisitionOutputDto";
import { LineGroupAddAcquisitionPayload } from "@/types/coreSwimmyPayload";
import CoreSwimmyService from "@/services/coreSwimmyService";

export class LineGroupAddAcquisitionHandler extends FunctionExternalApiHandlerBase {
    public static Meta = {
        localFunctionType: "10",
        url: Constants.LINESGROUP_CHARGE,
        getOrderType: (param: LineGroupAddAcquisitionInputDto) =>
            Check.checkOrderType(
                param.reserve_date,
                param.reserve_flag,
                param.reserve_soId,
            ),
        processName: "回線グループ追加チャージ(クーポン)容量・期間追加",
    }

    public static Handler = async (
        request: HttpRequest,
        context: ExtendedInvocationContext,
    ): Promise<HttpResponseInit> => {
        const lineGroupAddAcquisitionService = new LineGroupAddAcquisitionService(
            request,
            context,
        );
        try {
            const param = context.jsonBody;
            const ipaddress = MvnoUtil.getClientIPAddress(request);
            const result: LineGroupAddAcquisitionOutputDto = await lineGroupAddAcquisitionService.service(param, ipaddress);

            if (
                result.jsonBody.responseHeader.processCode ===
                ResultCdConstants.CODE_000000 &&
                !result.additionalData.csvUnnecessary
            ) {
                await callCoreSwimmy(
                    request,
                    context,
                    param,
                    result,
                    LineGroupAddAcquisitionHandler.Meta.getOrderType(param),
                );
            }
            return { jsonBody: result.jsonBody };
        } catch (error) {
            context.error(error);
            context.responseHeader.processCode = ResultCdConstants.CODE_999999;
            return this.CommonCheckCatchErrorHandler(request, context, error);
        }
    }
}

async function callCoreSwimmy(
    request: HttpRequest,
    context: ExtendedInvocationContext,
    param: LineGroupAddAcquisitionInputDto,
    output: LineGroupAddAcquisitionOutputDto,
    orderType: string | null,
) {
    if (output.additionalData.csvUnnecessary) {
        context.log(
            "LineGroupAddAcquisitionHandler: skip coreSwimmy call due to csvUnnecessary",
        );
        return;
    }
    if (Constants.TYPE_5_TENANTS.includes(param.tenantId)) {
        context.log(
            "LineGroupAddAcquisitionHandler: skip coreSwimmy call due to RINK MOBILE",
        );
        return;
    }
    const payload: LineGroupAddAcquisitionPayload = {
        receivedDate: output.additionalData.orderReceivedDate
            .substring(0, 10)
            .replace(/\//g, ""),
        frontSoId: param.targetSoId,
        coreSoId: context.responseHeader.apiProcessID,
        opfTenantId: param.tenantId,
        tenantId: param.tenantId,
        orderType,
        lineNo: param.AccountingLineNo,
        nNo: output.additionalData.nNo,
        tBan: output.additionalData.tBan,
        requestDate: output.additionalData.requestDate,      // orderDate/targetDate
    };

    const coreSwimmy = new CoreSwimmyService(request, context);
    await coreSwimmy.registerLineGroupAddAcquisitionTransaction(payload);
}