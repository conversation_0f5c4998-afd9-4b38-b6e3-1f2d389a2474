import { HttpRequest, HttpResponseInit } from "@azure/functions";
import ExtendedInvocationContext from "@/types/ExtendedInvocationContext";
import { FrontSoCancelPayload } from "@/types/coreSwimmyPayload";

import CoreSwimmyService from "@/services/coreSwimmyService";
import FrontSoCancelService from "@/core/service/impl/FrontSoCancelService";

import FunctionHandlerBase from "@/core/common/FunctionHandler";
import Constants from "@/core/constant/Constants";
import ResultCdConstants from "@/core/constant/ResultCdConstants";

import FrontSoCancelInputDto from "@/core/dto/FrontSoCancelInputDto";
import FrontSoCancelOutputDto from "@/core/dto/FronSoCancelOutputDto";

export class FrontSoCancelHandler extends FunctionHandlerBase {
    public static Meta = {
        localFunctionType: "56",
        url: Constants.FRONT_SO_CANCEL,
        getOrderType: (param: any) => null,
        processName: "フロント用予約キャンセル",
    };

    public static Handler = async (
        request: HttpRequest,
        context: ExtendedInvocationContext,
    ): Promise<HttpResponseInit> => {
        const frontSoCancelService = new FrontSoCancelService(request, context);
        try {
            const param = context.jsonBody;
            const result = await frontSoCancelService.service(param);
            if (
                result.jsonBody.responseHeader.processCode ===
                ResultCdConstants.CODE_000000
            ) {
                await callCoreSwimmy(
                    request,
                    context,
                    param,
                    result,
                    FrontSoCancelHandler.Meta.getOrderType(param),
                );
            }

            return { jsonBody: result.jsonBody };
        } catch (error) {
            context.error(error);
            return { body: error };
        }
    };
}

async function callCoreSwimmy(
    request: HttpRequest,
    context: ExtendedInvocationContext,
    param: FrontSoCancelInputDto,
    output: FrontSoCancelOutputDto,
    orderType: string | null,
) {
    if (output.additionalData.csvUnnecessary) {
        context.log(
            "FrontSoCancelHandler: skip coreSwimmy call due to csvUnnecessary",
        );
        return;
    }
    const payload: FrontSoCancelPayload = {
        receivedDate: context.responseHeader.receivedDate
            .substring(0, 10)
            .replace(/\//g, ""),
        frontSoId: param.targetSoId,
        coreSoId: context.responseHeader.apiProcessID,
        opfTenantId: param.tenantId,
        tenantId: param.targetTenantId,
        orderType,
        lineNo: output.additionalData.lineNo,
        receiptId: output.additionalData.receiptId,
        serviceOrderIdKey: param.serviceOrderIdKey,
        nNo: output.additionalData.nNo,
    };

    const coreSwimmy = new CoreSwimmyService(request, context);
    await coreSwimmy.registerFrontSoCancelTransaction(payload);
}
