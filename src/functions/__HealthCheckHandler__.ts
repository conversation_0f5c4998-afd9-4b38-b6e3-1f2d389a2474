import {
    HttpRequest,
    HttpResponseInit,
    InvocationContext,
} from "@azure/functions";
// import { usePsql } from "@/core/psql";
import * as fs from "fs";
import * as path from "path";

import { useMongo } from "@/database/mongo";
export const __HealthCheckHandler__ = async (
    request: HttpRequest,
    context: InvocationContext,
): Promise<HttpResponseInit> => {
    try {
        // await usePsql();
        await useMongo(context);
        let versionHash = "";
        // let versionSharedHash = "";
        let buildDate = "";
        try {
            versionHash = fs.readFileSync(
                path.join(__dirname, "../../../__version__"),
                "utf8",
            );
            buildDate = fs.readFileSync(
                path.join(__dirname, "../../../__build_date__"),
                "utf8",
            );
        } catch (e) {
            versionHash = "No version file found";
            buildDate = "No build date file found";
        }
        const healthcheck = {
            uptime: process.uptime(),
            message: "OK",
            timestamp: Date.now(),
            ver: versionHash.trim().replace(/\r?\n|\r/g, ""),
            buildDate: buildDate.trim().replace(/\r?\n|\r/g, ""),
        };
        return { jsonBody: healthcheck };
    } catch (e) {
        return {
            status: 503,
            body: e,
        };
    }
};
