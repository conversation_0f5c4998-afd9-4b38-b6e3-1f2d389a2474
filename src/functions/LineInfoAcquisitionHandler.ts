import { HttpRequest, HttpResponseInit } from "@azure/functions";
import ExtendedInvocationContext from "@/types/ExtendedInvocationContext";
import LineInfoAcquisitionService from "@/core/service/impl/LineInfoAcquisitionService";

import MvnoUtil from "@/core/common/MvnoUtil";
import Constants from "@/core/constant/Constants";
import ResultCdConstants from "@/core/constant/ResultCdConstants";
import FunctionExternalApiHandlerBase from "@/core/common/FunctionExternalApiHandler";
import MsgKeysConstants from "@/core/constant/MsgKeysConstants";
import LineInfoAcquisitionBaseOutputDto from "@/core/dto/LineInfoAcquisitionBaseOutputDto";
import { retryQuery } from "@/helpers/queryHelper";

export class LineInfoAcquisitionHandler extends FunctionExternalApiHandlerBase {
    public static Meta = {
        localFunctionType: "01",
        url: Constants.LINES_INFO,
        getOrderType: (param: any) => null,
        processName: "回線基本情報参照機能",
    };

    public static Handler = async (
        request: HttpRequest,
        context: ExtendedInvocationContext,
    ): Promise<HttpResponseInit> => {
        const lineInfoAcquisitionService = new LineInfoAcquisitionService(
            request,
            context,
        );
        try {
            const param = context.jsonBody;
            const result = await lineInfoAcquisitionService.service(param);     // no need to split the result into different cases as typescript allows union types

            return { jsonBody: result.jsonBody };
        } catch (error) {
            context.error(error);
            context.responseHeader.processCode = ResultCdConstants.CODE_999999;
            return this.CommonCheckCatchErrorHandler(request, context, error);
        }
    };

    public static CommonCheckFailedHandler = async (
        request: HttpRequest,
        context: ExtendedInvocationContext,
        errorMessage: string,
    ): Promise<HttpResponseInit> => {
        const lineInfoAcquisitionService = new LineInfoAcquisitionService(
            request,
            context,
        );

        const cheatCode = context.responseHeader?.processCode ?? ResultCdConstants.CODE_999999;
        const apiProcessID = context.responseHeader?.apiProcessID ?? "";

        context.log(context.jsonBody.addOption);
        const param = context.jsonBody;
        const addOption = param?.addOption;
        const lineNo = param?.lineNo;
        const tenantId = param?.tenantId;
        const linesEntity = null;
        const linesGroupId = null;
        const lineOptionsNameList = null;
        const isPrivate = false;                // hardcode to false
        const receivedDate = context.responseHeader?.receivedDate ?? MvnoUtil.getDateTimeNow();

        let result: LineInfoAcquisitionBaseOutputDto;       // no need to split the result into different cases as typescript allows union types

        if (addOption === "1") {
            result = lineInfoAcquisitionService.returnEdit2(param, cheatCode, apiProcessID, addOption, lineNo, tenantId, linesEntity, linesGroupId, lineOptionsNameList, errorMessage, isPrivate, receivedDate);
        } else if (addOption === "9") {
            result = lineInfoAcquisitionService.returnEdit3(param, cheatCode, apiProcessID, addOption, lineNo, tenantId, linesEntity, linesGroupId, lineOptionsNameList, errorMessage, isPrivate, receivedDate);
        } else {
            result = lineInfoAcquisitionService.returnEdit1(param, cheatCode, apiProcessID, addOption, lineNo, tenantId, linesEntity, linesGroupId, lineOptionsNameList, errorMessage, isPrivate, receivedDate);
        }

        lineInfoAcquisitionService.generateDebugMessage(param.tenantId, param.requestHeader.sequenceNo, MsgKeysConstants.APLBID0002,
            'LineInfoAcquisitionService', "service");
        return result;
    };

    public static CommonCheckCatchErrorHandler = async (
        request: HttpRequest,
        context: ExtendedInvocationContext,
        error: Error,
    ): Promise<HttpResponseInit> => {
        const lineInfoAcquisitionService = new LineInfoAcquisitionService(
            request,
            context,
        );

        const cheatCode = context.responseHeader?.processCode ?? ResultCdConstants.CODE_999999;
        const apiProcessID = context.responseHeader?.apiProcessID ?? "";
        const param = context.jsonBody;
        const addOption = param?.addOption;
        const lineNo = param?.lineNo;
        const tenantId = param?.tenantId;
        const linesEntity = param?.linesEntity;
        const linesGroupId = param?.linesGroupId;
        const lineOptionsNameList = param?.lineOptionsNameList;
        const isPrivate = param?.isPrivate ?? false;
        const receivedDate = context.responseHeader?.receivedDate ?? MvnoUtil.getDateTimeNow();

        let result: LineInfoAcquisitionBaseOutputDto;       // no need to split the result into different cases as typescript allows union types

        if (addOption === "1") {
            result = lineInfoAcquisitionService.returnEdit2(param, cheatCode, apiProcessID, addOption, lineNo, tenantId, linesEntity, linesGroupId, lineOptionsNameList, error.message, isPrivate, receivedDate);
        } else if (addOption === "9") {
            result = lineInfoAcquisitionService.returnEdit3(param, cheatCode, apiProcessID, addOption, lineNo, tenantId, linesEntity, linesGroupId, lineOptionsNameList, error.message, isPrivate, receivedDate);
        } else {
            result = lineInfoAcquisitionService.returnEdit1(param, cheatCode, apiProcessID, addOption, lineNo, tenantId, linesEntity, linesGroupId, lineOptionsNameList, error.message, isPrivate, receivedDate);
        }

        lineInfoAcquisitionService.generateDebugMessage(param?.tenantId, param?.requestHeader?.sequenceNo, MsgKeysConstants.APLBID0002,
            'LineInfoAcquisitionService', "service");
        return result;
    }
}