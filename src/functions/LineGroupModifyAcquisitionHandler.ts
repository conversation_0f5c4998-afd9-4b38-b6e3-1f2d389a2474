import Check from "@/core/common/Check";
import FunctionExternalApiHandlerBase from "@/core/common/FunctionExternalApiHandler";
import MvnoUtil from "@/core/common/MvnoUtil";
import Constants from "@/core/constant/Constants";
import ResultCdConstants from "@/core/constant/ResultCdConstants";
import LineGroupModifyAcquisitionInputDto from "@/core/dto/LineGroupModifyAcquisitionInputDto";
import LineGroupModifyAcquisitionOutputDto from "@/core/dto/LineGroupModifyAcquisitionOutputDto";
import LineGroupModifyAcquisitionService from "@/core/service/impl/LineGroupModifyAcquisitionService";
import CoreSwimmyService from "@/services/coreSwimmyService";
import { LineGroupModifyAcquisitionPayload } from "@/types/coreSwimmyPayload";
import ExtendedInvocationContext from "@/types/ExtendedInvocationContext";
import { getStringParameter } from "@/types/parameter.string";
import { HttpRequest, HttpResponseInit } from "@azure/functions";

export class LineGroupModifyAcquisitionHandler extends FunctionExternalApiHandlerBase {
    public static Meta = {
        localFunctionType: "14",
        url: Constants.LINEGROUP_MODIFY,
        getOrderType: (param: LineGroupModifyAcquisitionInputDto) => Check.checkOrderType(
            param?.reserve_date,
            param?.reserve_flag,
            param?.reserve_soId,
        ),
        processName: "回線グループ所属回線変更機能",
    };
    public static Handler = async (
        request: HttpRequest,
        context: ExtendedInvocationContext,
    ): Promise<HttpResponseInit> => {
        const lineGroupModifyAcquisitionService =
            new LineGroupModifyAcquisitionService(request, context);

        try {
            const param =
                context.jsonBody as LineGroupModifyAcquisitionInputDto;
            const ipaddress = MvnoUtil.getClientIPAddress(request);
            const result = await lineGroupModifyAcquisitionService.service(
                param,
                false,
                null, // executeUserId
                null, // executeTenantId
                ipaddress, // remoteAddr
            );

            if (
                result.jsonBody.responseHeader.processCode ===
                ResultCdConstants.CODE_000000
            ) {
                await callCoreSwimmy(
                    request,
                    context,
                    param,
                    result,
                    LineGroupModifyAcquisitionHandler.Meta.getOrderType(param),
                );
            }
            return {
                jsonBody: result.jsonBody,
            };
        } catch (error) {
            context.error("error", error);
            return { body: error };
        }
    };
}

async function callCoreSwimmy(
    request: HttpRequest,
    context: ExtendedInvocationContext,
    param: LineGroupModifyAcquisitionInputDto,
    output: LineGroupModifyAcquisitionOutputDto,
    orderType: string | null,  // optional maybe
) {
    if (output.additionalData.csvUnnecessary) {
        context.log(
            "LineGroupModifyAcquisitionHandler: skip coreSwimmy call due to csvUnnecessary",
        );
        return;
    }
    const payload: LineGroupModifyAcquisitionPayload = {
        receivedDate: context.responseHeader.receivedDate
            .substring(0, 10)
            .replace(/\//g, ""),
        frontSoId: param.targetSoId,
        coreSoId: context.responseHeader.apiProcessID,
        opfTenantId: param.tenantId,
        tenantId: param.tenantId,
        orderType,
        lineNo: output.additionalData.lineNo,
        nNo: output.additionalData.nNo,
        lineGroupId: getStringParameter(param.lineGroupId),
        operation: getStringParameter(param.operation) === "1" ? "1": null,
    };

    const coreSwimmy = new CoreSwimmyService(request, context);
    await coreSwimmy.registerLineGroupModifyAcquisitionTransaction(payload);
}