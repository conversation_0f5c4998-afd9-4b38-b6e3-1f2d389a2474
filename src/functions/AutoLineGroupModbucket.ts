import { HttpResponseInit, InvocationContext, Timer } from "@azure/functions";
import MsgKeysConstants from "@/core/constant/MsgKeysConstants";
import AbstractMvnoBaseCommon from "@/core/common/AbstractMvnoBaseCommon";
import MessageProperties from "@/core/constant/MessageProperties";
import axios from "axios";
import LineGroupModBucketRestAPIResponse from "@/core/restclient/LineGroupModBucketRestAPIResponse";
import StorageTableService from "@/services/storageTableService";
import config from "config";
import CommonUtil from "@/core/common/CommonUtil";
import { retryQuery } from "@/helpers/queryHelper";
import { Sequelize } from "sequelize-typescript";
import { QueryTypes } from "sequelize";
import BatchTimesDao from "@/core/dao/BatchTimesDao";
import { usePsql } from "@/database/psql";
import AutoModBucketDao from "@/core/dao/AutoModBucketDao";
import { addDays, startOfDay } from "date-fns";
import AppConfig from "@/appconfig";
import RestAPIProperties from "@/core/restclient/RestAPIProperties";

type CheckDoubleExecResult = {
    alreadyRunning: boolean;
    error?: any;
    storageTable: StorageTableService;
    rowKey?: string;
}

/**
 * 回線グループ基本容量変更API定期実行機能<BR>
 *
 * @version 1.00 新規<BR>
 *          STEP20.0版対応<BR>
 */
export class AutoLineGroupModbucket extends AbstractMvnoBaseCommon{
    /**
     * 空文字
     */
    public static EMPTY: string = "";

    /**
     * メッセージプロパティファイル名
     */
    public static MESSAGE_FILE_NAME: string = "message.properties";

    /**
     * SGプロパティファイル名
     */
    public static SG_FILE_NAME: string = "/opt/mawp/AutoModbucketApi.properties";

    /**
     * 二重起動抑止用LOCKファイル
     */
    public static RUN_LOCK_FILE: string = "/opt/mawp/this.lock";

    /**
     * API名
     */
    public static API_NAME: string = "回線グループ基本容量変更API定期実行機能";

    /**
     * エンコード：UTF-8
     */
    public static ENCODE_UTF8: string = "UTF-8";

    /**
     * REST電文：送信番号
     */
    public static REST_SEQNO: string = "Y000000";

    /**
     * REST電文：送信元システムID
     */
    public static REST_SENDER_SYSID: string = "reca";

    /**
     *  返却値(正常)
     */
    private static SUCCESS: number = 0;

    /**
     *  返却値(異常)
     */
    private static FAIL: number = 1;

    /**
     * REST電文：機能種別(回線グループ基本容量計算API)
     */
    public static REST_FUNCTION_TYPE: string = "15";

    /**
     * REST電文：容量計算
     */
    public static REST_CALUCLATION: string = "1";


    /**
     * プロパティファイル和名：自動計算回線グループ保持期間
     */
    public static PROP_APIDATALIMIT: string = "自動計算回線グループ保持期間";

    /**
     * REST電文のファイル形式：JSON
     */
    public static REST_MEDIA_TYPE: string = "Content-Type: application/json";

    /**
     * REST通信：接続タイムアウト時間(ミリ秒)
     */
    public static RESTCLT_CON_TIMEOUT: number = 30000;

    /**
     * REST通信：受信タイムアウト時間(ミリ秒)
     */
    public static RESTCLT_READ_TIMEOUT: number = 3600000;

    /**
     * HTTPレスポンスコード 200：正常
     */
    public static HTTP_RESPONCE_OK: number = 200;

    // SystemPropertiesReaders
    /** DBサーバ仮想IPアドレス */
    public static DbServerVirtaulIpAddress = config.get<string>("mvno.DbServerVirtaulIpAddress");
    /** アプリケーション用DBユーザID */
    public static ApDbUserId = config.get<string>("mvno.ApDbUserId");
    /** アプリケーション用DBユーザPW */
    public static ApDbUserPw = config.get<string>("mvno.ApDbUserPw");
    /** アプリケーション用DB接続ポート */
    public static ApDbPort = config.get<string>("mvno.ApDbPort");
    /** アプリケーション用DB接続ドライバ */
    public static ApDbDriver = config.get<string>("mvno.ApDbDriver");
    /** バッチログ名 */
    public static AutoModbucketApiLogFile = config.get<string>("mvno.AutoModbucketApiLogFile");
    /** バッチログファイルパス */
    public static AutoModbucketApiLogFilePath = config.get<string>("mvno.AutoModbucketApiLogFilePath");
    /** 回線グループ基本容量変更APIベースURI */
    public static AutoModbucketApiBaseUri = config.get<string>("mvno.AutoModbucketApiBaseUri");
    /** 自動計算回線グループ保持期間 */
    public static AutoModbucketApiDataLimit = config.get<string>("mvno.AutoModbucketApiDataLimit");
    // STEP20.0版対応　追加　START
    /** アプリケーション用DBリトライ最大回数 */
    public static apDbRetryMaxCnt = config.get<number>("mvno.ApDbRetryMaxCnt");
    /** アプリケーション用DBリトライ間隔 */
    public static apDbRetryInterval = config.get<number>("mvno.ApDbRetryInterval");

    /**
     * メッセージリソースからメッセージを構築する
     *
     * NOTE: this function is a copy from `@/core/common/AbstractMvnoBaseCommon.ts`
     *
     * @param key
     * メッセージキー
     * @param params
     * メッセージパラメーター
     *
     * @return 構築されたメッセージ
     */
    private static getMessage(key: string, params: string[]): string {
        const template = MessageProperties[key];
        if (template && Array.isArray(params)) {
            let result = template;
            params.forEach((param, index) => {
                const regex = new RegExp(`\\{${index}\\}`, "g");
                result = result.replace(regex, param);
            });
            return result;
        }
        return key + params.join(",");
    }

    /**
     * 回線グループ基本容量変更API定期実行
     *
     * @param args 入力パラメータ
     */
    public static Handler = async (
        timer: Timer,
        context: InvocationContext,
    ): Promise<number> => {
        context.log("AutoLineGroupModbucket Handler:");
        const currentTime = new Date().toISOString();       // args[0]
        context.log("AutoLineGroupModbucket Handler:", currentTime);
        let returnCode: number = this.SUCCESS;

        try {
            returnCode = await this.execute(context, currentTime);
        } catch (e: any) {
            context.error(this.getMessage(MsgKeysConstants.MSGID_CRAGME0012, [e.message]), e);
            returnCode = this.FAIL;
        }

        if (returnCode === this.SUCCESS) {
            context.info(this.getMessage(MsgKeysConstants.MSGID_CRAGMI0004, [this.API_NAME]));
        } else {
            context.error(this.getMessage(MsgKeysConstants.MSGID_CRAGME0011, [this.API_NAME]));
        }

        return returnCode;
    };

    /**
     * ロジック処理実行
     *
     * @param executeTime 実行時間
     * @return 処理結果
     * @throws Exception 実行時例外
     */
    private static async execute(context: InvocationContext, executeTime: string): Promise<number> {
        const hashedPasswordMap = new Map<string, string>();

        // システム日付
        const strSystemDate = CommonUtil.getDateTimeNow();

        // システムプロパティ必須項目チェック
        try {
            this.requireCheck(context);
        } catch (e) {
            context.error(this.getMessage(MsgKeysConstants.MSGID_CRAGME0001, [e.message]), e);
            return this.FAIL;
        }

        // 二重起動チェック
        const isAlreadyRun: CheckDoubleExecResult = await this.isAlreadyRun(context);
        // 二重起動である場合
        if (isAlreadyRun.alreadyRunning) {
            context.error(this.getMessage(MsgKeysConstants.MSGID_CRAGME0002, []));
            return this.FAIL;
        }
        const pidStorageTable = isAlreadyRun.storageTable;
        const rowKey = isAlreadyRun.rowKey;
        if (!pidStorageTable) {
            // should not happen
            context.error("PIDストレージテーブルが取得できませんでした。");
            return this.FAIL;
        }

        // 開始メッセージ出力
        context.info(this.getMessage(MsgKeysConstants.MSGID_CRAGMI0001, [this.API_NAME]));

        try {
            const sequelize = await usePsql();
            const batchTime = await retryQuery(context,
                "getAutoApiBatchTimes",
                async () => {
                    return await BatchTimesDao.getAutoApiBatchTimes(context, sequelize);
                },
                this.apDbRetryMaxCnt,
                this.apDbRetryInterval,
            );
            if (batchTime === null) {
                context.error(this.getMessage(MsgKeysConstants.MSGID_CRAGME0003, []));
                return this.FAIL;
            }

            // 前回実施日時
            const preExecTime: Date = batchTime.preExecTime;
            // 今回実施日時
            const execTime: Date = batchTime.execTime;

            // 前回実施日時のフォーマットチェック
            if (!this.checkPreExecTimeFmt(context, preExecTime)) {
                context.error(this.getMessage(MsgKeysConstants.MSGID_CRAGME0004, []));
                return this.FAIL;
            }

            // 今回実施日時と前回実施日時が一致しているか
            if (execTime == null || preExecTime.getTime() !== execTime.getTime()) {
                context.error("execTime:" + execTime + " preExecTime:" + preExecTime);
                context.error(this.getMessage(MsgKeysConstants.MSGID_CRAGME0005, []));
                return this.FAIL;
            }

            // システム日時の取得
            const execTimeStr: string = strSystemDate;
            const preExecTimeStr: string = CommonUtil.convDateTimeToString(preExecTime);

            // 今回実施日時の更新
            await retryQuery(context,
                "UpdateExecTime",
                async () => {
                    await BatchTimesDao.updAutoApiBatchTimes(context, sequelize, null, execTimeStr);
                },
                this.apDbRetryMaxCnt,
                this.apDbRetryInterval,
            );

            // 処理対象回線グループ情報取得
            let targetGroupList = await retryQuery(context,
                "getTargetGroupList",
                async () => {
                    return await AutoModBucketDao.getAutoModBucketGroupList(context, sequelize, preExecTimeStr, execTimeStr);
                },
                this.apDbRetryMaxCnt,
                this.apDbRetryInterval,
            );

            if (targetGroupList == null || targetGroupList.length <= 0) {
                context.info(this.getMessage(MsgKeysConstants.MSGID_CRAGMI0002, []));
            } else {
                const deleteTargetList: { tenant_id: string, group_id: string }[] = [];

                for (const rec of targetGroupList) {
                    const tenantId = rec.tenant_id;
                    const groupId = rec.group_id;
                    // 取得項目のNULLチェック
                    if (tenantId === null || tenantId === "") {
                        context.warn(this.getMessage(MsgKeysConstants.MSGID_CRAGMW0001, [groupId]));
                        deleteTargetList.push({ tenant_id: tenantId, group_id: groupId });
                        continue;
                    }
                    // ハッシュ化パスワード取得
                    const hashedTenant = await retryQuery(context,
                        "getTenantHashedPassword",
                        async () => {
                            return await AutoModBucketDao.getTenantHashedPassword(context, sequelize, tenantId);
                        },
                        this.apDbRetryMaxCnt,
                        this.apDbRetryInterval,
                    );
                    // ハッシュ化パスワードチェック・格納
                    if (hashedTenant == null) {
                        context.error(this.getMessage(MsgKeysConstants.MSGID_CRAGME0007, [tenantId]));
                        return this.FAIL;
                    }
                    if (!hashedPasswordMap.has(tenantId)) {
                        // テナントハッシュ化パスワードマップにまだ格納されていないテナントIDなら格納
                        hashedPasswordMap.set(tenantId, hashedTenant);
                    }
                }
                // 削除対象に格納したオブジェクトを結果リストから削除
                targetGroupList = targetGroupList.filter(item =>
                    !deleteTargetList.some(deleteItem =>
                        deleteItem.tenant_id === item.tenant_id &&
                        deleteItem.group_id === item.group_id
                    )
                );

                for (const rec of targetGroupList) {
                    const tenantId = rec.tenant_id;
                    const groupId = rec.group_id;
                    // 送信番号ハッシュの取得
                    const hashedSeqNo = CommonUtil.digest(this.REST_SEQNO);

                    const restRequest = {
                        // リクエストヘッダの格納
                        requestHeader: {
                            // 送信番号の設定
                            sequenceNo: this.REST_SEQNO,
                            // 送信元IDの設定
                            senderSystemId: this.REST_SENDER_SYSID,
                            // API認証キーの設定;
                            apiKey: hashedPasswordMap.get(tenantId) + "$02$" + hashedSeqNo,
                            // 機能種別の設定
                            functionType: this.REST_FUNCTION_TYPE
                        },
                        // テナントIDの設定
                        tenantId,
                        // 回線グループIDの設定
                        lineGroupId: groupId,
                        // 容量計算の設定
                        calculation: this.REST_CALUCLATION
                    }

                    const restApiURLAddress = RestAPIProperties.getRestAPIUrlAddress();
                    const restApiURLPath = RestAPIProperties.getRestAPIUrlPath("15");
                    const autoModBucketApiBaseUri = restApiURLAddress + restApiURLPath;
                    // リクエスト電文(JSON)の生成
                    const restResponse = await this.sendREST(
                        context,
                        autoModBucketApiBaseUri,
                        'application/json',
                        JSON.stringify(restRequest)
                    );

                    context.log("rest sent", restResponse)

                    const apiId = restResponse.responseHeader.apiProcessID;
                    const processCode = restResponse.responseHeader.processCode;
                    const capacity = restResponse.capacity;

                    if (apiId !== null && apiId !== "") {
                        await retryQuery(context,
                            "UpdateServiceOrderExecUserId",
                            async () => {
                                await AutoModBucketDao.updServiceOrderExecUserId(context, sequelize, apiId);
                            },
                            this.apDbRetryMaxCnt,
                            this.apDbRetryInterval,
                        );
                    }

                    if ("000000" === processCode) {
                        context.info(
                            this.getMessage(
                                MsgKeysConstants.MSGID_CRAGMI0003,
                                [executeTime, groupId, tenantId, capacity, apiId]
                            )
                        );
                    } else {
                        context.info(
                            this.getMessage(
                                MsgKeysConstants.MSGID_CRAGMW0002,
                                [executeTime, groupId, tenantId, processCode, apiId]
                            )
                        );
                    }
                }
            }

            // テーブルのクリーンアップ
            // 自動計算回線グループ保持期間フォーマットチェック
            const apiLimit = this.AutoModbucketApiDataLimit;
            if (!this.checkApiLimitFmt(apiLimit)) {
                context.error(this.getMessage(MsgKeysConstants.MSGID_CRAGME0008,
                    [this.PROP_APIDATALIMIT, apiLimit]));
                return this.FAIL;
            }

            // 削除対象日付の設定
            let deleteDate = CommonUtil.convDateFormat(execTimeStr); // timestamp
            deleteDate = startOfDay(deleteDate);
            deleteDate = addDays(deleteDate, -Number(apiLimit));

            // 削除対象レコードの削除
            await retryQuery(context,
                "delAutoModBucketGroupList",
                async () => {
                    await AutoModBucketDao.delAutoModBucketGroupList(context, sequelize, deleteDate);
                },
                this.apDbRetryMaxCnt,
                this.apDbRetryInterval,
            );

            // 前回実施日時更新
            // 今回実施日時の更新
            await retryQuery(context,
                "updAutoApiBatchTimes",
                async () => {
                    await BatchTimesDao.updAutoApiBatchTimes(context, sequelize, execTimeStr, null);
                },
                this.apDbRetryMaxCnt,
                this.apDbRetryInterval,
            );

            return this.SUCCESS;
        } finally {
            try {
                await retryQuery(context,
                    "ClearPidStorageTable",
                    async () => {
                        await pidStorageTable.deleteEntity("AutoLineGroupModbucket", rowKey);
                    },
                    this.apDbRetryMaxCnt,
                    this.apDbRetryInterval,
                );
                context.info("処理を終了します。");
            } catch (e) {
                context.error("PIDストレージテーブルのクリアに失敗しました。", e);
            }
        }
    }

    /**
     * 二重起動抑止チェック
     *
     * @return true：二重起動である、false：二重起動ではない
     * @throws IOException ファイルアクセスエラー
     */
    private static async isAlreadyRun(context: InvocationContext): Promise<CheckDoubleExecResult> {
        const storageTable = new StorageTableService(
            context,
            "AutoLineGroupModbucketPid"
        );
        const tableClient = await storageTable.init();
        const result = await storageTable.getUniqueRowKeys();
        if (result.length > 0 && result[0]) {
            context.log("Already running");
            const lastRunISOString = result[0];
            if ((new Date().getTime() - new Date(lastRunISOString).getTime()) < 20 * 60 * 60 * 1000) {
                return {
                    alreadyRunning: true,
                    error: result,
                    storageTable: null,
                    rowKey: null
                };
            } else {
                context.log("Last run was more than 20 minutes ago, clearing the entity.");
                await storageTable.deleteEntity("AutoLineGroupModbucket", lastRunISOString);
            }
        }
        const pid = process.pid;
        const now = new Date();
        const rowKey = `${now.toISOString()}`;
        await tableClient.createEntity({
            partitionKey: "AutoLineGroupModbucket",
            rowKey,
            data: JSON.stringify({ pid, now }),
        });
        context.log(`Created entity with row key: ${rowKey}`);
        return {
            alreadyRunning: false,
            storageTable,
            rowKey
        };
    }

    /**
     * 自動計算回線グループ保持期間フォーマットチェック
     * @param apiLimit 自動計算回線グループ保持期間
     * @return true:OK false:NG
     */
    private static checkApiLimitFmt(apiLimit: string): boolean {
        const max = 99;
        try {
            const num = parseInt(apiLimit, 10);
            if (num < 0 || num > max) {
                return false;
            }
        } catch (e) {
            return false;
        }
        return true;
    }

    /**
     * REST電文送受信処理
     * @param uri 回線グループ基本容量APIベースURL
     * @param mediatype データ種別(JSON)
     * @param request RESTリクエスト電文
     * @return RESTレスポンス
     */
    private static async sendREST(context: InvocationContext, uri: string, mediatype: string, request: string): Promise<LineGroupModBucketRestAPIResponse> {
        // REST接続設定
        const config = {
            timeout: this.RESTCLT_CON_TIMEOUT,
        };
        try {
            context.debug(request);
            context.debug("uri:", uri);
            const response = await axios.post(uri, request, config);
            context.debug(response);
            if (response && response.status === this.HTTP_RESPONCE_OK) {
                return response.data;
            }
        } catch (e) {
            context.error(e);
            throw e;
        }
        return null;
    }

    private static requireCheck(context: InvocationContext): void {
        const list = {
            DbServerVirtaulIpAddress: this.DbServerVirtaulIpAddress,
            ApDbUserId: this.ApDbUserId,
            ApDbUserPw: this.ApDbUserPw,
            ApDbPort: this.ApDbPort,
            ApDbDriver: this.ApDbDriver,
            // AutoModbucketApiLogFile: this.AutoModbucketApiLogFile,           // no more log file
            // AutoModbucketApiLogFilePath: this.AutoModbucketApiLogFilePath,   // no more log file
            // AutoModbucketApiBaseUri: this.AutoModbucketApiBaseUri,           // changed to AppConfig
            AutoModbucketApiDataLimit: this.AutoModbucketApiDataLimit,
            ApDbRetryMaxCnt: this.apDbRetryMaxCnt,
            ApDbRetryInterval: this.apDbRetryInterval,
        }

        for (const [key, item] of Object.entries(list)) {
            if (item === null || item === undefined || item === "") {
                context.error(this.getMessage(MsgKeysConstants.MSGID_CRAGME0001, [key]));
                throw new Error("item is not found:" + item);
            }
        }

        const restApiURLAddress = RestAPIProperties.getRestAPIUrlAddress();
        if (restApiURLAddress == null) {
            context.error(this.getMessage(MsgKeysConstants.APSORE9999, ["url address does not exist."]));
            throw new Error("item is not found: restApiURLAddress");
        }
        const restApiURLPath = RestAPIProperties.getRestAPIUrlPath("15");
        if (restApiURLPath == null) {
            context.error(this.getMessage(MsgKeysConstants.APSORE9999, ["url path does not exist." + "15"]));
            return null;
        }
        const target = restApiURLAddress + restApiURLPath;
    }

    /**
     * 前回実施日時フォーマットチェック
     * @param time 前回実施日時
     * @return true:OK false:NG
     */
    private static checkPreExecTimeFmt(context: InvocationContext, time: Date): boolean {
        context.info(time.getTime());
        // NULLチェック
        if (time === null) {
            return false;
        }
        // 日付がシステム日時以前であることをチェック
        const nowTime = new Date();
        // for some reason, it seems time is in seconds (please check tests)
        if (time.getTime() > nowTime.getTime()) {
            return false;
        }
        // チェックOK
        return true;
    }
}

/**
 * 回線グループ基本容量変更API定期実行
 *
 * @param args 入力パラメータ
 */
const AutoLineGroupModbucketHandler = async (
    timer: Timer,
    context: InvocationContext,
): Promise<HttpResponseInit> => {
    context.log("AutoLineGroupModbucketHandler started:");

    const result = await AutoLineGroupModbucket.Handler(timer, context);

    context.log("AutoLineGroupModbucketHandler ended");

    return {
        status: 200,
        jsonBody: {
            result,
        },
    }
};

export default AutoLineGroupModbucketHandler;