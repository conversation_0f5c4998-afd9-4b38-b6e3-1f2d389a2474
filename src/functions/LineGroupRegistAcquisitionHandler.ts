import FunctionExternalApiHandlerBase from "@/core/common/FunctionExternalApiHandler";
import { HttpRequest, HttpResponseInit, trigger } from "@azure/functions";
import Constants from "@/core/constant/Constants";
import ExtendedInvocationContext from "@/types/ExtendedInvocationContext";
import LineGroupRegistAcquisitionServiceTx from "@/core/service/impl/LineGroupRegistAcquisitionServiceTx";
import BaseOutputDto from "@/core/dto/BaseOutputDto";
import LineGroupRegistAcquisitionOutputDto from "@/core/dto/LineGroupRegistAcquisitionOutputDto";
import ResponseHeader from "@/core/dto/ResponseHeader";
import ResultCdConstants from "@/core/constant/ResultCdConstants";
import MvnoUtil from "@/core/common/MvnoUtil";

export class LineGroupRegistAcquisitionHandler extends FunctionExternalApiHandlerBase {
    public static Meta = {
        localFunctionType: "13",
        url: Constants.LINEGROUP_REGIST,
        getOrderType: (param: any) => null,
        processName: "回線グループ作成廃棄機能",
    };

    public static Handler = async (
        request: HttpRequest,
        context: ExtendedInvocationContext,
    ): Promise<HttpResponseInit> => {
        const lineGroupRegistAcquisitionServiceTx =
            new LineGroupRegistAcquisitionServiceTx(request, context);
        try {
            const param = context.jsonBody;
            const result = await lineGroupRegistAcquisitionServiceTx.service(
                param,
            );

            return {
                jsonBody: {
                    responseHeader: {
                        sequenceNo: result.jsonBody.responseHeader.sequenceNo,
                        processCode: result.jsonBody.responseHeader.processCode,
                        receivedDate:
                            result.jsonBody.responseHeader.receivedDate,
                        apiProcessID:
                            result.jsonBody.responseHeader.apiProcessID,
                    },
                    lineGroupId: result.jsonBody.lineGroupId || "",
                },
            };
        } catch (error) {
            context.error(error);
            return { body: error };
        }
    };

    public static CommonCheckFailedHandler = async (
        request: HttpRequest,
        context: ExtendedInvocationContext,
        errorMessage: string,
    ): Promise<HttpResponseInit> => {
        // returnEdit
        const response: LineGroupRegistAcquisitionOutputDto = {
            jsonBody: {
                responseHeader: context.responseHeader,
                lineGroupId: "",
            },
        };
        return { jsonBody: response.jsonBody };
    };

    public static CommonCheckCatchErrorHandler = async (
        request: HttpRequest,
        context: ExtendedInvocationContext,
        error: Error,
    ): Promise<HttpResponseInit> => {
        context.error(error);
        const errorResponse: ResponseHeader = {
            processCode: ResultCdConstants.CODE_999999,
            apiProcessID: context?.responseHeader?.apiProcessID || "",
            sequenceNo: context?.jsonBody?.requestHeader?.sequenceNo || "",
            receivedDate: MvnoUtil.getDateTimeNow(),
        };
        return {
            jsonBody: {
                responseHeader: errorResponse,
                lineGroupId: "",
            },
        };
    }

}