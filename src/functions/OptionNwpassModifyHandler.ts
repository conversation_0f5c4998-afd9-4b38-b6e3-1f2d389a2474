import { HttpRequest, HttpResponseInit, trigger } from "@azure/functions";
import ExtendedInvocationContext from "@/types/ExtendedInvocationContext";
import { OptionChangeTransactionPayload } from "@/types/coreSwimmyPayload";

import CoreSwimmyService from "@/services/coreSwimmyService";
import OptionNwpassModifyService from "@/core/service/impl/OptionNwpassModifyService";

import FunctionHandlerBase from "@/core/common/FunctionHandler";
import Constants from "@/core/constant/Constants";
import ResultCdConstants from "@/core/constant/ResultCdConstants";
import OptionNwpassModifyInputDto from "@/core/dto/OptionNwpassModifyInputDto";
import OptionNwpassModifyOutputDto from "@/core/dto/OptionNwpassModifyOutputDto";

export class OptionNwpassModifyHandler extends FunctionHandlerBase {
    public static Meta = {
        localFunctionType: "54",
        url: Constants.LINE_LINEOP,
        getOrderType: (param: any) => null,
        processName: "回線オプション_NW暗証番号変更機能",
    };

    public static Handler = async (
        request: HttpRequest,
        context: ExtendedInvocationContext,
    ): Promise<HttpResponseInit> => {
        const optionNwpassModifyService = new OptionNwpassModifyService(
            request,
            context,
        );
        try {
            const param = context.jsonBody;
            const result = await optionNwpassModifyService.service(param);

            if (
                result.jsonBody.responseHeader.processCode ===
                ResultCdConstants.CODE_000000
            ) {
                await callCoreSwimmy(
                    request,
                    context,
                    param,
                    result,
                    OptionNwpassModifyHandler.Meta.getOrderType(param),
                );
            }

            return { jsonBody: result.jsonBody };
        } catch (error) {
            context.error(error);
            return { body: error };
        }
    };
}

async function callCoreSwimmy(
    request: HttpRequest,
    context: ExtendedInvocationContext,
    param: OptionNwpassModifyInputDto,
    output: OptionNwpassModifyOutputDto,
    orderType: string | null,
) {
    if (output.additionalData.csvUnnecessary) {
        context.log(
            "OptionNwpassModifyHandler: skip coreSwimmy call due to csvUnnecessary",
        );
        return;
    }

    const payload: OptionChangeTransactionPayload = {
        receivedDate: context.responseHeader.receivedDate
            .substring(0, 10)
            .replace(/\//g, ""),
        frontSoId: param.targetSoId,
        coreSoId: context.responseHeader.apiProcessID,
        opfTenantId: param.tenantId,
        tenantId: param.targetTenantId,
        orderType,
        lineNo: param.lineNo,
        nNo: output.additionalData.nNo,
        pinChange: param.pinChange,
        intlRoaming: param.intlRoaming,
        voicemail: param.voicemail,
        callWaiting: param.callWaiting,
        intlCall: param.intlCall,
        forwarding: param.forwarding,
        intlForwarding: param.intlForwarding,
    };

    const coreSwimmy = new CoreSwimmyService(request, context);
    await coreSwimmy.registerOptionChangeTransaction(payload);
}
