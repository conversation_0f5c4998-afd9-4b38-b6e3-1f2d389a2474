import { HttpRequest, HttpResponseInit } from "@azure/functions";
import Check from "@/core/common/Check";
import FunctionExternalApiHandlerBase from "@/core/common/FunctionExternalApiHandler";
import { FunctionMeta } from "@/core/common/FunctionHandler";
import Constants from "@/core/constant/Constants";
import MsgKeysConstants from "@/core/constant/MsgKeysConstants";
import ResultCdConstants from "@/core/constant/ResultCdConstants";
import LineStatusAcquisitionInputDto from "@/core/dto/LineStatusAcquisitionInputDto";
import LineStatusAcquisitionOutputDto from "@/core/dto/LineStatusAcquisitionOutputDto";
import LineStatusAcquisitionService from "@/core/service/impl/LineStatusAcquisitionService";
import ExtendedInvocationContext from "@/types/ExtendedInvocationContext";
import CustomLogger from "@/utils/logger";
import MvnoUtil from "@/core/common/MvnoUtil";

export default class LineStatusAcquisitionHandler extends FunctionExternalApiHandlerBase {
    public static Meta: FunctionMeta = {
        localFunctionType: "05",
        url: Constants.LINES_ACTIVATION,
        getOrderType: (param: LineStatusAcquisitionInputDto) =>
            Check.checkOrderType(
                param?.reserve_date,
                param?.reserve_flag,
                param?.reserve_soId,
            ),
        processName: "回線アクティベート/ディアクティベート",
    };

    public static Handler = async (
        request: HttpRequest,
        context: ExtendedInvocationContext,
    ): Promise<HttpResponseInit> => {
        const lineStatusAcquisitionService = new LineStatusAcquisitionService(
            request,
            context,
        );
        try {
            const param = context.jsonBody as LineStatusAcquisitionInputDto;
            const result = await lineStatusAcquisitionService.service(
                param,
                false,
                null, // executeUserId
                null, // executeTenantId
                null, // request.remoteAddress
            );

            return { jsonBody: result.jsonBody };
        } catch (error) {
            context.error(error);
            context.responseHeader.processCode = ResultCdConstants.CODE_999999;
            return LineStatusAcquisitionHandler.CommonCheckCatchErrorHandler(
                request,
                context,
                error,
            );
        }
    };

    public static CommonCheckCatchErrorHandler = async (
        request: HttpRequest,
        context: ExtendedInvocationContext,
        error: Error,
    ): Promise<HttpResponseInit> => {
        const logger = new CustomLogger(request, context);
        const param = context.jsonBody as LineStatusAcquisitionInputDto;
        // エラーログ出力
        logger.error(
            error,
            param?.tenantId,
            param?.requestHeader?.sequenceNo,
            MsgKeysConstants.APCOME0401,
            error?.message,
        );

        const dto: LineStatusAcquisitionOutputDto = {
            jsonBody: {
                responseHeader: {
                    ...context.responseHeader,
                    processCode: ResultCdConstants.CODE_999999,
                },
            },
        };
        return dto;
    };

    public static CommonCheckFailedHandler = async (
        request: HttpRequest,
        context: ExtendedInvocationContext,
        errorMessage: string,
    ): Promise<HttpResponseInit> => {
        const param = context.jsonBody as LineStatusAcquisitionInputDto;
        context.log(
            "LineStatusAcquisitionHandler.CommonCheckFailedHandler",
            errorMessage,
            JSON.stringify(param ?? {}),
        );
        const dto = {
            jsonBody: {
                responseHeader: {
                    sequenceNo: param?.requestHeader?.sequenceNo,
                    receivedDate: MvnoUtil.getDateTimeNow(),
                    // processCode: ResultCdConstants.CODE_999999,
                    // apiProcessID: context.responseHeader?.apiProcessID ?? "",
                    ...context.responseHeader,
                },
            },
        } satisfies LineStatusAcquisitionOutputDto;
        context.log(
            "LineStatusAcquisitionHandler.CommonCheckFailedHandler response",
            JSON.stringify(dto),
        );
        return dto;
    };
}
