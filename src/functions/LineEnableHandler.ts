import { HttpRequest, HttpResponseInit } from "@azure/functions";

import Constants from "@/core/constant/Constants";
import MsgKeysConstants from "@/core/constant/MsgKeysConstants";
import ResultCdConstants from "@/core/constant/ResultCdConstants";

import FunctionHandlerBase from "@/core/common/FunctionHandler";
import MvnoUtil from "@/core/common/MvnoUtil";

import LineEnableInputDto from "@/core/dto/LineEnableInputDto";
import LineEnableOutputDto from "@/core/dto/LineEnableOutputDto";
import ResponseHeader from "@/core/dto/ResponseHeader";

import CoreSwimmyService from "@/services/coreSwimmyService";
import LineEnableService from "@/core/service/impl/LineEnableService";

import ExtendedInvocationContext from "@/types/ExtendedInvocationContext";
import { CoreSwimmyLineEnablePayload } from "@/types/coreSwimmyPayload";

import { isNone } from "@/utils";
import CustomLogger from "@/utils/logger";

export class LineEnableHandler extends FunctionHandlerBase {
    public static Meta = {
        localFunctionType: "55",
        url: Constants.LINE_ENABLE,
        getOrderType: (param: LineEnableInputDto) => null,
        processName: "回線黒化",
    };

    public static Handler = async (
        request: HttpRequest,
        context: ExtendedInvocationContext,
    ): Promise<HttpResponseInit> => {
        const lineEnableService = new LineEnableService(request, context);
        const logger = new CustomLogger(request, context);
        const param = context.jsonBody as LineEnableInputDto;
        try {
            const ipaddress = MvnoUtil.getClientIPAddress(request);
            const result = await lineEnableService.service(param, ipaddress);

            if (
                result?.jsonBody?.responseHeader?.processCode ===
                ResultCdConstants.CODE_000000
            ) {
                await callCoreSwimmy(request, context, param, result);
            }
            return {
                jsonBody: result.jsonBody,
            };
        } catch (error) {
            logger.error(
                error as Error,
                param?.tenantId,
                param?.requestHeader?.sequenceNo,
                MsgKeysConstants.APCOME0401,
                error?.message,
            );
            let dto: ResponseHeader = context.responseHeader;
            if (!isNone(dto)) {
                dto.processCode = ResultCdConstants.CODE_999999;
            } else {
                dto = {
                    sequenceNo: param?.requestHeader?.sequenceNo ?? "",
                    processCode: ResultCdConstants.CODE_999999,
                    apiProcessID: "",
                    receivedDate: MvnoUtil.getDateTimeNow(),
                };
            }
            return {
                jsonBody: {
                    responseHeader: dto,
                },
            };
        }
    };
}

async function callCoreSwimmy(
    request: HttpRequest,
    context: ExtendedInvocationContext,
    param: LineEnableInputDto,
    output: LineEnableOutputDto,
): Promise<void> {
    context.log(
        "LineEnableHandler.callCoreSwimmy START",
        JSON.stringify(param),
        output.jsonBody,
        output.additionalData,
    );

    if (output.additionalData.csvUnnecessary) {
        context.log("LineEnableHandler.callCoreSwimmy 連携不要");
        return;
    }

    const payload: CoreSwimmyLineEnablePayload = {
        receivedDate: output.jsonBody.responseHeader.receivedDate
            .substring(0, 10)
            .replace(/\//g, ""),
        frontSoId: param.targetSoId,
        coreSoId: context.responseHeader.apiProcessID,
        opfTenantId: param.tenantId,
        tenantId: param.targetTenantId,
        orderType: null,
        lineNo: param.lineNo,
        nNo: output.additionalData.nNo,
        sameMvneFlag: param.sameMvneInFlag === "1",
        deviceTypeIdT: output.additionalData.deviceTypeIdT,
        enableDate: output.additionalData.enableDate,
    };

    const coreSwimmyService = new CoreSwimmyService(request, context);
    await coreSwimmyService.registerLineEnableTransaction(payload);
}
