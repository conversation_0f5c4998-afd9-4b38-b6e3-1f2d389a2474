import { HttpRequest, HttpResponseInit } from "@azure/functions";
import ExtendedInvocationContext from "@/types/ExtendedInvocationContext";
import CoreSwimmyService from "@/services/coreSwimmyService";
import FunctionExternalApiHandlerBase from "@/core/common/FunctionExternalApiHandler";
import Constants from "@/core/constant/Constants";
import ResultCdConstants from "@/core/constant/ResultCdConstants";
import SoCancelInputDto from "@/core/dto/SoCancelInputDto";
import SoCancelOutputDto from "@/core/dto/SoCancelOutputDto";
import SoCancelService from "@/core/service/impl/SoCancelService";
import { SoCancelPayload } from "@/types/coreSwimmyPayload";

export class SoCancelHandler extends FunctionExternalApiHandlerBase {
    public static Meta = {
        localFunctionType: "18",
        url: Constants.SO_CANCEL,
        getOrderType: (param: any) => null,
        processName: "予約キャンセル",
    };

    public static Handler = async (
        request: HttpRequest,
        context: ExtendedInvocationContext,
    ): Promise<HttpResponseInit> => {
        const soCancelService = new SoCancelService(request, context);
        try {
            const param = context.jsonBody;
            const result = await soCancelService.service(param);
            if (
                result.jsonBody.responseHeader.processCode ===
                ResultCdConstants.CODE_000000
            ) {
                await callCoreSwimmy(
                    request,
                    context,
                    param,
                    result,
                    SoCancelHandler.Meta.getOrderType(param),
                );
            }

            return { jsonBody: result.jsonBody };
        } catch (error) {
            context.error(error);
            return { body: error };
        }
    };
}

async function callCoreSwimmy(
    request: HttpRequest,
    context: ExtendedInvocationContext,
    param: SoCancelInputDto,
    output: SoCancelOutputDto,
    orderType: string | null,
) {
    if (output.additionalData.csvUnnecessary) {
        context.log(
            "SoCancelHandler: skip coreSwimmy call due to csvUnnecessary",
        );
        return;
    }
    const payload: SoCancelPayload = {
        receivedDate: context.responseHeader.receivedDate
            .substring(0, 10)
            .replace(/\//g, ""),
        frontSoId: param.targetSoId,
        coreSoId: context.responseHeader.apiProcessID,
        opfTenantId: "",
        tenantId: param.tenantId,
        orderType,
        lineNo: output.additionalData.lineNo,
        receiptId: output.additionalData.receiptId,
        serviceOrderIdKey: param.serviceOrderIdKey,
        nNo: output.additionalData.nNo,
    };

    const coreSwimmy = new CoreSwimmyService(request, context);
    await coreSwimmy.registerSoCancelTransaction(payload);
}