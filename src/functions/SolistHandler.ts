import { HttpRequest, HttpResponseInit } from "@azure/functions";
import ExtendedInvocationContext from "@/types/ExtendedInvocationContext";

import SolistService from "@/core/service/impl/SolistService";

import FunctionExternalApiHandlerBase from "@/core/common/FunctionExternalApiHandler";
import Constants from "@/core/constant/Constants";

export class SolistHandler extends FunctionExternalApiHandlerBase {
    public static Meta = {
        localFunctionType: "16",
        url: Constants.SOLIST_CHARGE,
        getOrderType: (param: any) => null,
        processName: "SO一覧",
    };

    public static Handler = async (
        request: HttpRequest,
        context: ExtendedInvocationContext,
    ): Promise<HttpResponseInit> => {
        const solistService = new SolistService(request, context);
        try {
            const param = context.jsonBody;
            const result = await solistService.service(param);

            return {
                jsonBody: {
                    ...result
                },
            };
        } catch (error) {
            context.error(error);
            return { body: error };
        }
    };
}
