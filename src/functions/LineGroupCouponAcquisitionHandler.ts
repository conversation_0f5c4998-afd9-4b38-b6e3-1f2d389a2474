import FunctionExternalApiHandlerBase from "@/core/common/FunctionExternalApiHandler";
import Constants from "@/core/constant/Constants";
import ExtendedInvocationContext from "@/types/ExtendedInvocationContext";
import { HttpRequest, HttpResponseInit } from "@azure/functions";
import LineGroupCouponAcquisitionService from "@/core/service/impl/LineGroupCouponAcquisitionService";
import LineGroupCouponAcquisitionInputDto from "@/core/dto/LineGroupCouponAcquisitionInputDto";
import Check from "@/core/common/Check";

export class LineGroupCouponAcquisitionHandler extends FunctionExternalApiHandlerBase {
    public static Meta = {
        localFunctionType: "12",
        url: Constants.LINESGROUP_COUPON,
        getOrderType: (param: LineGroupCouponAcquisitionInputDto) => Check.checkOrderType(
            param?.reserve_date,
            param?.reserve_flag,
            param?.reserve_soId,
        ),
        processName: "回線グループクーポンオン・オフ",
    };
    public static Handler = async (
        request: HttpRequest,
        context: ExtendedInvocationContext,
    ): Promise<HttpResponseInit> => {
        const lineGroupCouponAcquisitionService =
            new LineGroupCouponAcquisitionService(request, context);
        try {
            const param = context.jsonBody;
            const result = await lineGroupCouponAcquisitionService.service(
                param,
            );
            return { jsonBody: result.jsonBody };
        } catch (error) {
            context.error(error);
            return { body: error };
        }
    };
}