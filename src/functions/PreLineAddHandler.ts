import { HttpRequest, HttpResponseInit } from "@azure/functions";

import ExtendedInvocationContext from "@/types/ExtendedInvocationContext";
import {
    PreLineAddPayload,
    SameMvneHaishiPayload,
} from "@/types/coreSwimmyPayload";
import AppConfig from "@/appconfig";
import Constants from "@/core/constant/Constants";
import MsgKeysConstants from "@/core/constant/MsgKeysConstants";
import ResultCdConstants from "@/core/constant/ResultCdConstants";

import Check from "@/core/common/Check";
import CheckUtil from "@/core/common/CheckUtil";
import MvnoUtil from "@/core/common/MvnoUtil";
import FunctionHandlerBase from "@/core/common/FunctionHandler";

import PreLineAddInputDto from "@/core/dto/PreLineAddInputDto";
import PreLineAddOutputDto from "@/core/dto/PreLineAddOutputDto";
import PreLineAddService from "@/core/service/impl/PreLineAddService";
import CustomLogger from "@/utils/logger";
import { getNumberValue, isNone } from "@/utils";

import CoreSwimmyApiLog from "@/models/coreSwimmyApiLog";
import CoreSwimmyService from "@/services/coreSwimmyService";
import BaseOutputDto from "@/core/dto/BaseOutputDto";

export class PreLineAddHandler extends FunctionHandlerBase {
    public static Meta = {
        localFunctionType: "51",
        url: Constants.Line_preadd,
        getOrderType: (param: PreLineAddInputDto) => {
            let reserveDate = param.reserve_date;
            if (!CheckUtil.checkIsNotNull(reserveDate)) {
                reserveDate += " 04:00";
            }
            return Check.checkOrderType(
                reserveDate,
                param.reserve_flag,
                param.reserve_soId,
            );
        },
        processName: "仮登録回線追加",
    };

    public static Handler = async (
        request: HttpRequest,
        context: ExtendedInvocationContext,
    ): Promise<HttpResponseInit> => {
        const preLineAddService = new PreLineAddService(request, context);
        const logger = new CustomLogger(request, context);
        const param = context.jsonBody as PreLineAddInputDto;
        try {
            const ipaddress = MvnoUtil.getClientIPAddress(request);
            const result = await preLineAddService.service(param, ipaddress);

            if (
                [
                    ResultCdConstants.CODE_000000,
                    ResultCdConstants.CODE_510110,
                    ResultCdConstants.CODE_510301,
                    ResultCdConstants.CODE_510401,
                ].includes(result.jsonBody.responseHeader.processCode)
            ) {
                await callCoreSwimmy(request, context, param, result);
            }
            return {
                jsonBody: result.jsonBody,
            };
        } catch (error) {
            context.error(error);
            logger.error(
                error,
                param?.tenantId,
                param?.requestHeader?.sequenceNo,
                MsgKeysConstants.APCOME0401,
                error?.message,
            );
            // context.responseHeader.processCode = ResultCdConstants.CODE_999999;
            return {
                jsonBody: {
                    responseHeader: {
                        sequenceNo:
                            context?.responseHeader?.sequenceNo ??
                            param?.requestHeader?.sequenceNo ??
                            "",
                        receivedDate:
                            context?.responseHeader?.receivedDate ??
                            MvnoUtil.getDateTimeNow(),
                        processCode: ResultCdConstants.CODE_999999,
                        apiProcessID:
                            context?.responseHeader?.apiProcessID ?? "",
                    },
                },
            } satisfies BaseOutputDto;
        }
    };
}

async function callCoreSwimmy(
    request: HttpRequest,
    context: ExtendedInvocationContext,
    param: PreLineAddInputDto,
    output: PreLineAddOutputDto,
) {
    const data = output?.additionalData;

    context.log("PreLineAddHandler.callCoreSwimmy", {
        csvOutputKbn: data?.csvOutputKbn,
        abolitionCsvOutputKbn: data?.abolitionCsvOutputKbn,
        optionAddCsvOutputKbn: data?.optionAddCsvOutputKbn, // NOTE this is not used
    });
    if (data?.csvOutputKbn !== true) {
        context.log("PreLineAddHandler.callCoreSwimmy: API連携不要");
        return;
    }

    if (isNone(data)) {
        context.error(
            "PreLineAddHandler.callCoreSwimmy: data is None",
            context.responseHeader.apiProcessID,
        );
        return;
    }

    context.debug(
        "PreLineAddHandler.callCoreSwimmy: START",
        JSON.stringify({ param, output }),
    );

    const coreSwimmy = new CoreSwimmyService(request, context);
    /** 廃止's`.id` */
    let haishiOrderId: string = null;
    if (
        data.mnpInType === PreLineAddService.MNP_IN_TYPE_SAME_MVNE_YES &&
        data.abolitionCsvOutputKbn
    ) {
        // register 廃止回線 first and then create 回線新規 with 待機中 status
        // haishi (tenant A) -> preadd (tenant B)
        const haishiPayload: SameMvneHaishiPayload = {
            receivedDate: context.responseHeader.receivedDate
                .substring(0, 10)
                .replace(/\//g, ""),
            frontSoId: param.targetSoId,
            coreSoId: context.responseHeader.apiProcessID,
            opfTenantId: param.tenantId,
            tenantId: param.lineDelTenantId,
            lineNo: param.lineNo,
            nNo: data.nNo_haishi,
        };
        context.log(
            "PreLineAddHandler.callCoreSwimmy: registerSameMvneHaishiTransaction",
        );
        // NOTE: transaction is not sent to queue yet
        haishiOrderId = await coreSwimmy.registerSameMvneHaishiTransaction(
            haishiPayload,
            true, // don't send to queue
            true, // jikkouchuFlg=true: we will send to queue after preadd
        );
    }

    const payload: PreLineAddPayload = {
        receivedDate: context.responseHeader.receivedDate
            .substring(0, 10)
            .replace(/\//g, ""),
        frontSoId: param.targetSoId,
        coreSoId: context.responseHeader.apiProcessID,
        opfTenantId: param.tenantId,
        tenantId: param.targetTenantId, // preadd tenant B
        lineNo: param.lineNo,
        nNo: data.nNo, // tenant B nNo
        custInfo: {
            agencyCode: data.custInfo?.agencyCode,
            customerService: data.custInfo?.customerService,
            customerServicePhoneNumber:
                data.custInfo?.customerServicePhoneNumber,
            customerServiceEmail: data.custInfo?.customerServiceEmail,
            contractNameKana: data.custInfo?.contractorNameKana,
            contractName: data.custInfo?.contractorName,
        },
        isFM: data.isFullMvno,
        reserveDate: data.reserveDate,
        isSameMvne:
            data.mnpInType === PreLineAddService.MNP_IN_TYPE_SAME_MVNE_YES, // 2: 同一MVNE
        mnpInType: data.mnpInType,
        cardTypeIdT: data.cardInfo?.deviceTypeIdT,
        simNo: param.sim_no,
        hankuro: data.planInfo?.defaultSimFlag,
        simFlag: data.cardInfo?.simFlag,
        orderType: data.orderType,
        delivery: {
            postcode: data.custInfo?.addresseePostcode,
            prefecture: data.custInfo?.addresseePrefectures,
            city: data.custInfo?.addresseeCities,
            ooaza: data.custInfo?.addresseeDaiji,
            aza: data.custInfo?.addresseeJi,
            block: data.custInfo?.addresseeBlock,
            building: data.custInfo?.addresseeMansion,
            name: data.custInfo?.addresseeName,
            department: data.custInfo?.addresseeSectionName,
            contact: data.custInfo?.addresseeRepresentativeName,
            tel: data.custInfo?.addresseePhoneNumber,
        },
        pricePlanIdT: data.planInfo.planIdT,
        accessType: param.access,
        intlRoamingT: data.options?.intlRoamingT,
        voicemailT: data.options?.voicemailT,
        callWaitingT: data.options?.callWaitingT,
        intlCallT: data.options?.intlCallT,
        forwardingT: data.options?.forwardingT,
        intlForwardingT: data.options?.intlForwadingT,
        mnpAddOptionIdT: data.custInfo?.mnpAddOptionIdT,
        mnpDelOptionIdT: data.custInfo?.mnpDelOptionIdT,
    };
    context.log(
        "PreLineAddHandler.callCoreSwimmy: registerPreLineAddTransaction",
    );
    try {
        await coreSwimmy.registerPreLineAddTransaction(payload, haishiOrderId);
    } finally {
        // send haishi transaction to queue (jikkouchuFlg already set to true)
        if (haishiOrderId) {
            context.log(
                "PreLineAddHandler.callCoreSwimmy: sendSameMvneHaishiTransaction to queue",
                haishiOrderId,
                context.responseHeader.apiProcessID,
                param.targetSoId,
            );
            const haishidata = await CoreSwimmyApiLog.findById(haishiOrderId);
            if (haishidata) {
                let scheduleTime: number = null; // default: send immediately
                if (data.isSameMvneCancelRequestSent === true) {
                    // scheduled for 1 minute later if there was cancel API calls
                    const config = AppConfig.getCoreConfig(context);
                    const delaySeconds = getNumberValue(config.CORE_SWIMMY_API.MESSAGE_DELAY_SECONDS, 60);
                    scheduleTime = Math.floor(Date.now() / 1000) + delaySeconds;
                    context.log(
                        "PreLineAddHandler.callCoreSwimmy: send haishi as scheduled message:",
                        haishiOrderId,
                        scheduleTime,
                    );
                }
                await coreSwimmy.sendToServiceBus(haishidata, scheduleTime);
            } else {
                context.error(
                    "Could not find haishi transaction _id:",
                    haishiOrderId,
                );
            }
        }
    }
    context.debug("PreLineAddHandler.callCoreSwimmy: END");
}
