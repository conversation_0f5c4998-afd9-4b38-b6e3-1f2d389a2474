import { HttpRequest, HttpResponseInit } from "@azure/functions";
import ExtendedInvocationContext from "@/types/ExtendedInvocationContext";
import { NWContractTransactionPayload } from "@/types/coreSwimmyPayload";

import CoreSwimmyService from "@/services/coreSwimmyService";
import NetworkContractChangeService from "@/core/service/impl/NetworkContractChangeService";

import FunctionHandlerBase from "@/core/common/FunctionHandler";
import Constants from "@/core/constant/Constants";
import ResultCdConstants from "@/core/constant/ResultCdConstants";
import NetworkContractChangeInputDto from "@/core/dto/NetworkContractChangeInputDto";
import NetworkContractChangeOutputDto from "@/core/dto/NetworkContractChangeOutputDto";

export class NetworkContractChangeHandler extends FunctionHandlerBase {
    public static Meta = {
        localFunctionType: "60",
        url: Constants.LINES_ACCESS,
        getOrderType: (param: any) => null,
        processName: "NW契約変更",
    };

    public static Handler = async (
        request: HttpRequest,
        context: ExtendedInvocationContext,
    ): Promise<HttpResponseInit> => {
        const networkContractChangeService = new NetworkContractChangeService(
            request,
            context,
        );
        try {
            const param = context.jsonBody;
            const result = await networkContractChangeService.service(param);

            if (
                result.jsonBody.responseHeader.processCode ===
                ResultCdConstants.CODE_000000
            ) {
                await callCoreSwimmy(
                    request,
                    context,
                    param,
                    result,
                    NetworkContractChangeHandler.Meta.getOrderType(param),
                );
            }

            return { jsonBody: result.jsonBody };
        } catch (error) {
            context.error(error);
            return { body: error };
        }
    };
}

async function callCoreSwimmy(
    request: HttpRequest,
    context: ExtendedInvocationContext,
    param: NetworkContractChangeInputDto,
    output: NetworkContractChangeOutputDto,
    orderType: string | null,
) {
    if (output.additionalData.csvUnnecessary) {
        context.log(
            "NetworkContractChangeHandler: skip coreSwimmy call due to csvUnnecessary",
        );
        return;
    }

    const payload: NWContractTransactionPayload = {
        receivedDate: context.responseHeader.receivedDate
            .substring(0, 10)
            .replace(/\//g, ""),
        frontSoId: param.targetSoId,
        coreSoId: context.responseHeader.apiProcessID,
        opfTenantId: param.tenantId,
        tenantId: param.targetTenantId,
        orderType,
        lineNo: param.lineNo,
        nNo: output.additionalData.nNo,
        accessPre: param.access_pre,
        access: param.access,
        cardTypeId: param.cardTypeId,
    };

    const coreSwimmy = new CoreSwimmyService(request, context);
    await coreSwimmy.registerNWContractTransaction(payload);
}
