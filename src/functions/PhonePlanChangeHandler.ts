import FunctionHandlerBase from "@/core/common/FunctionHandler";
import Constants from "@/core/constant/Constants";
import LineDeleteInputDto from "@/core/dto/LineDeleteInputDto";
import CheckUtil from "@/core/common/CheckUtil";
import Check from "@/core/common/Check";
import { HttpRequest, HttpResponseInit } from "@azure/functions";
import ExtendedInvocationContext from "@/types/ExtendedInvocationContext";
import PhonePlanChangeService from "@/core/service/impl/PhonePlanChangeService";
import MvnoUtil from "@/core/common/MvnoUtil";
import ResultCdConstants from "@/core/constant/ResultCdConstants";
import FunctionExternalApiHandlerBase from "@/core/common/FunctionExternalApiHandler";

export class PhonePlanChangeHandler extends FunctionExternalApiHandlerBase {
    public static Meta = {
        localFunctionType: "59",
        url: Constants.LINES_PHONEPLAN,
        getOrderType: (param: any) => null,
        processName: "0035でんわプラン変更",
    };

    public static Handler = async (
        request: HttpRequest,
        context: ExtendedInvocationContext,
    ): Promise<HttpResponseInit> => {
        const phonePlanChangeService = new PhonePlanChangeService(request, context);
        const param = context.jsonBody;
        const result = await phonePlanChangeService.service(param);

        return { jsonBody: result.jsonBody };
    }
}