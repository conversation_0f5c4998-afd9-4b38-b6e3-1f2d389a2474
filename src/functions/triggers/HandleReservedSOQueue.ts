import { ServiceBusQueueHandler } from "@azure/functions";
import axios from "axios";
import ServiceOrdersEntity from "@/core/entity/ServiceOrdersEntity";
import ExtendedInvocationContext from "@/types/ExtendedInvocationContext";
import config from "config";
import RestAPIClient from "@/core/restclient/RestAPIClient";
import StorageTableService from "@/services/storageTableService";
import AbstractMvnoBaseCommon from "@/core/common/AbstractMvnoBaseCommon";
import MsgKeysConstants from "@/core/constant/MsgKeysConstants";
import { ExecReservedSOHandler } from "@/functions/triggers/ExecReservedSOHandler";
import RestAPIRequest from "@/core/restclient/RestAPIRequest";

// REST API接続時のコネクションタイムアウト値(秒)
const HTTP_CLIENT_CONNECTION_TIMEOUT = config.get<number>("mvno.httpClientConnectTimeout");

// REST API接続時の読み取りタイムアウト値(秒)
const HTTP_CLIENT_READ_TIMEOUT = config.get<number>("mvno.httpClientReadTimeout");

// REST APIからの正常戻りコード
const REST_API_NORMAL_PROCESSCODE = "000000";

/**
 * 実行キュー送信処理クラスコンストラクタ
 *
 * @param lockObject synchronized用ロックオブジェクト
 * @param requestMapList 検索された予約実行対象オーダ格納領域
 * @param threadNum REST API実行スレッド番号
 * @param workerBlockingFlg REST API実行workerスレッド動作管理配列
 * @param lastExecuteTime スレッドの最新処理開始時間
 * @param bq REST API実行スレッドが処理する要求キュー -> changed to Azure Service Bus Queue
 * @param lbq2 REST API実行スレッドが処理中のオーダリスト -> changed to Azure Table Storage
 * @param lbq3 REST API実行スレッドが処理を終了したオーダリスト -> changed to Azure Table Storage
 */
class ReservedSOQueueHandler extends AbstractMvnoBaseCommon{
    private service: StorageTableService;

    // Order status values
    public static readonly STATUS_PENDING = ExecReservedSOHandler.STATUS_PENDING;
    public static readonly STATUS_PROCESSING = ExecReservedSOHandler.STATUS_PROCESSING;
    public static readonly STATUS_COMPLETED = ExecReservedSOHandler.STATUS_COMPLETED;
    constructor(context: ExtendedInvocationContext) {
        super(null, context);
        this.context = context;
    }

    public getMessage(key: string, params: string[]): string {
        return super.getMessage(key, params);
    }

    async init() {
        this.service = new StorageTableService(this.context, "ReservedSO");
        await this.service.init();
        this.context.debug("worker started!");
    }
    /**
     * REST APIリクエストを実行する
     *
     * @param context ログ出力用のコンテキスト
     * @param serviceOrderId オーダID
     * @param restMessage REST API呼び出し電文
     * @returns REST APIの処理結果コード、またはnull（エラー時）
     */
    async executeRestAPIRequest(
        serviceOrderId: string,
        restMessage: RestAPIRequest
    ): Promise<string | null> {
        const currentThreadName = this.context.invocationId;
        if (!this.service) {
            await this.init();
        }
        try {
            this.context.info(super.getMessage(MsgKeysConstants.APSORI5401, [currentThreadName, serviceOrderId]));

            const restClient: RestAPIClient = new RestAPIClient(this.context,
                HTTP_CLIENT_CONNECTION_TIMEOUT, HTTP_CLIENT_READ_TIMEOUT);
            const processCode = await restClient.doRestAPIRequest(restMessage, serviceOrderId);

            if (processCode == null) {
                this.context.error(super.getMessage(MsgKeysConstants.APSORE9999,
                    ["REST API機能呼び出し中にエラーが発生しました:" + serviceOrderId]));
            } else if (processCode === REST_API_NORMAL_PROCESSCODE) {
                this.context.info(super.getMessage(MsgKeysConstants.APSORI5501,
                    [serviceOrderId, processCode]));
            }
            else {
                this.context.error(super.getMessage(MsgKeysConstants.APSORE5501,
                    [serviceOrderId, processCode]));
            }

            return processCode;
        } catch (e) {
            this.context.error(e.message);
            if (axios.isAxiosError(e)) {
                if (e.code === 'ECONNABORTED' || e.message.includes('timeout')) {
                    this.context.error(super.getMessage(MsgKeysConstants.APSORE5502,
                        [HTTP_CLIENT_READ_TIMEOUT.toString(),
                        currentThreadName,serviceOrderId]));
                } else {
                    this.context.error(super.getMessage(MsgKeysConstants.APSORE9999,
                        ["REST API機能呼び出し中にエラーが発生しました:" + serviceOrderId]));
                }
            } else {
                this.context.error(super.getMessage(MsgKeysConstants.APSORE9999,
                    ["REST API機能呼び出し中にエラーが発生しました:" + serviceOrderId]));
            }
            return null;
        }
    }

    /**
     * add to bq2
     *
     * @param serviceOrderId オーダID
     * @returns 成功した場合はtrue、失敗した場合はfalse
     */
    async markOrderAsProcessing(serviceOrderId: string): Promise<boolean> {
        if (!this.service) {
            await this.init();
        }
        try {
            // オーダを処理中としてマーク
            await this.service.createEntity({
                partitionKey: "orders",
                rowKey: serviceOrderId,
                status: ReservedSOQueueHandler.STATUS_PROCESSING,
                timestamp: new Date().toISOString()
            });

            return true;
        } catch (e) {
            this.context.error(`Failed to mark order ${serviceOrderId} as processing: ${e.message}`);
            return false;
        }
    }

    /**
     * オーダが既に処理中または完了しているか確認する
     *
     * @param serviceOrderId オーダID
     * @returns 処理中または完了している場合はtrue、そうでない場合はfalse
     */
    async isOrderProcessingOrCompleted(serviceOrderId: string): Promise<boolean> {
        try {
            try {
                const entity = await this.service.getEntity("orders", serviceOrderId) as any;
                // エンティティが存在し、ステータスが処理中または完了の場合
                if (entity && (entity.status === ReservedSOQueueHandler.STATUS_PROCESSING || entity.status === ReservedSOQueueHandler.STATUS_COMPLETED)) {
                    return true;
                }
                return false;
            } catch (e) {
                this.context.error(e.message);
                // エンティティが存在しない場合
                return false;
            }
        } catch (e) {
            this.context.error(`Error checking order status for ${serviceOrderId}: ${e.message}`);
            return false; // エラーの場合は安全のためfalseを返す
        }
    }

    /**
     * オーダを完了としてマークする
     *
     * @param serviceOrderId オーダID
     * @returns 成功した場合はtrue、失敗した場合はfalse
     */
    async markOrderAsCompleted(serviceOrderId: string): Promise<boolean> {
        if (!this.service) {
            await this.init();
        }
        try {
            // オーダを完了としてマーク
            await this.service.createEntity({
                partitionKey: "orders",
                rowKey: serviceOrderId,
                status: ReservedSOQueueHandler.STATUS_COMPLETED,
                timestamp: new Date().toISOString()
            });

            return true;
        } catch (e) {
            this.context.error(`Failed to mark order ${serviceOrderId} as completed: ${e.message}`);
            return false;
        }
    }
}

/**
 * ExecReservedSOConsumer
 */
const HandleReservedSOQueue: ServiceBusQueueHandler = async (
    message: unknown,
    context: ExtendedInvocationContext,
): Promise<void> => {
    context.log(`HandleReservedSOQueue for ${context.invocationId} starts at ${new Date().toISOString()}`);
    let serviceOrder: {serviceOrderId: string, restMessage: RestAPIRequest};
    const reservedSOHandler = new ReservedSOQueueHandler(context);
    await reservedSOHandler.init();
    try {
        // Parse the message, replacement of pollRequestQueue.
        serviceOrder = JSON.parse(JSON.stringify(message));
    } catch (e) {
        context.error("HandleReservedSOQueue error - failed to parse serviceOrder", e, message);
        throw e;
    }

    // Validate the message
    if (!serviceOrder || !serviceOrder.serviceOrderId) {
        // 基本的にここに入ることはないが念のため。
        context.error(reservedSOHandler.getMessage("APSORE9999",
            ["オーダ送信機能処理で、サービスオーダ情報が取得できませんでした。"]));
        return;
    }

    const serviceOrderId = serviceOrder.serviceOrderId;
    const restMessage = serviceOrder.restMessage;

    if (restMessage === null) {
        // 基本的にここに入ることはないが念のため。
        // DBのrestMessageカラムがnull?
        context.error(reservedSOHandler.getMessage("APSORE9999",
            ["オーダ送信機能処理で、REST API電文が取得できませんでした。"]));
        return;
    }

    // Prevent double execution
    const isAlreadyProcessing = await reservedSOHandler.isOrderProcessingOrCompleted(serviceOrderId);
    if (isAlreadyProcessing) {
        context.info(`Order ${serviceOrderId} is already being processed or completed. Skipping.`);
        return;
    }

    // 当該サービスオーダIDを実行中とする
    const marked = await reservedSOHandler.markOrderAsProcessing(serviceOrderId);
    if (!marked) {
        context.error(`Failed to mark order ${serviceOrderId} as processing. Skipping.`);
        return;
    }

    try {
        //
        // REST API呼び出し処理
        //
        context.info(`Executing REST API for order ${serviceOrderId}`);
        await reservedSOHandler.executeRestAPIRequest(serviceOrderId, restMessage);
    } catch (e) {
        context.error(`Error during REST API execution for order ${serviceOrderId}: ${e.message}`);
    } finally {
        // REST API呼び出しが失敗しても成功してもオーダ情報は実行中キューから終了キューへ移動
        // 2つのキューに同じ値が入っている瞬間があるが、Producerでの新規キュー作成時の除外判定は
        // 以下のどちらかのキューに入っているかなので問題ない
        await reservedSOHandler.markOrderAsCompleted(serviceOrderId);
        context.info(`HandleReservedSOQueue for ${context.invocationId} with Order ${serviceOrderId} processing completed at ${new Date().toISOString()}`);
    }
};

export default HandleReservedSOQueue;
