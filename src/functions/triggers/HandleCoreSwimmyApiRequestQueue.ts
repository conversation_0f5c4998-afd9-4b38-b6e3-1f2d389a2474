import { Invocation<PERSON>ontext, ServiceBus<PERSON><PERSON>ue<PERSON><PERSON><PERSON> } from "@azure/functions";

import AppConfig from "@/appconfig";
import { CORE_SWIMMY_RESPONSE, CoreSwimmyStatus } from "@/constants/coreSwimmy";
import { useMongo } from "@/database/mongo";

import CoreSwimmyApiLog, { ICoreSwimmyApiLog } from "@/models/coreSwimmyApiLog";
import CoreSwimmyApiService from "@/services/coreSwimmyApiService";
import CoreSwimmyService from "@/services/coreSwimmyService";
import { getNumberValue, isNone } from "@/utils";
import CoreSwimmyResponseHelper from "@/helpers/coreSwimmyResponseHelper";

/**
 * Call Swimmy Core API and update response & status
 */
async function execCoreSwimmyAPI(
    context: InvocationContext,
    apiLog: ICoreSwimmyApiLog,
) {
    try {
        context.log(
            "execCoreSwimmyAPI started:",
            apiLog.requestOrderId,
            apiLog.swimmyType,
            apiLog.isChainedTransaction,
        );
        const coreSwimmyApiService = new CoreSwimmyApiService(context);

        const result = await coreSwimmyApiService.sendToCoreSwimmyApi(apiLog);
        if (result.isOk && isNone(result.data)) {
            // if response is empty, throw error
            throw new Error("execCoreSwimmyAPI empty response");
        }
        // resultCode and serviceProcessResultType both must be OK
        const { resultCode, serviceProcessResultType } =
            CoreSwimmyResponseHelper.getResultCode(result.data);

        context.log("execCoreSwimmyAPI response code", {
            resultCode,
            serviceProcessResultType,
        });

        let newStatus = CoreSwimmyStatus.STOPPING_FOR_ERROR;
        let ngReason: string = null;

        if (
            result.isOk &&
            resultCode === CORE_SWIMMY_RESPONSE.RESULT_CODE.OK &&
            serviceProcessResultType ===
                CORE_SWIMMY_RESPONSE.SERVICE_PROCESS_RESULT_TYPE.OK
        ) {
            newStatus = CoreSwimmyStatus.TOUROKU_IRAI_RESULT_OK;
        } else {
            // get error message if possible
            if (result.statusCode === 409) {
                ngReason = "排他制御エラー";
            } else if (result.statusCode === 500 || result.statusCode === 503) {
                ngReason = `${result.statusCode}: システムエラー`;
            } else {
                newStatus = CoreSwimmyStatus.TOUROKU_IRAI_RESULT_NG;
                // 400 error: get message from response object
                if (!isNone(result.data)) {
                    const resultDtlType =
                        CoreSwimmyResponseHelper.getProcessResultDtlType(
                            result.data,
                        );
                    const errorMessages =
                        CoreSwimmyResponseHelper.getErrorMessages(result.data);
                    // TODO confirm which error message to use
                    const errorText = [];
                    if (errorMessages.length > 0) {
                        // just use the first one ?
                        errorText.push(errorMessages[0]);
                    }
                    if (!isNone(resultDtlType)) {
                        errorText.push(`(${resultDtlType})`);
                    }
                    ngReason = errorText.join(" ");
                } else {
                    ngReason = `${result.statusCode}: 不明なエラー`;
                }
            }
        }

        if (!isNone(result.data)) {
            await CoreSwimmyApiLog.updateStatusAndResponse(
                context,
                apiLog.requestOrderId,
                apiLog.swimmyType,
                newStatus,
                result.data,
                ngReason,
                result.statusCode,
            );
        } else {
            await CoreSwimmyApiLog.updateStatus(
                context,
                apiLog.requestOrderId,
                apiLog.swimmyType,
                newStatus,
                ngReason,
                null, // updateStatusUsers
                result.statusCode,
            );
        }

        context.log("execCoreSwimmyAPI result:", newStatus, ngReason);
        // for 同一MVNE transactions (廃止->追加, activate 追加 only if 廃止 was successful)
        // for other transactions: activate without checking current result
        if (
            newStatus === CoreSwimmyStatus.TOUROKU_IRAI_RESULT_OK ||
            apiLog.isChainedTransaction !== true
        ) {
            // `needToActiveTheseIDs` may be updated after message sent so need to get fresh data
            const updatedLog = await CoreSwimmyApiLog.findByOrderIdAndType(
                apiLog.requestOrderId,
                apiLog.swimmyType,
            );
            context.log(
                "execCoreSwimmyAPI activateTransaction:",
                updatedLog?.needToActiveTheseIDs,
            );

            if (
                updatedLog &&
                Array.isArray(updatedLog.needToActiveTheseIDs) &&
                updatedLog.needToActiveTheseIDs.length > 0
            ) {
                const config = AppConfig.getCoreConfig(context);
                const delaySeconds = getNumberValue(
                    config.CORE_SWIMMY_API.MESSAGE_DELAY_SECONDS,
                    60,
                );
                const nextScheduleTime = Math.floor(Date.now() / 1000) + delaySeconds;

                // usually this array only has 1 ID but just in case handle multiple IDs
                const coreSwimmyService = new CoreSwimmyService(null, context);
                const activateResult = await Promise.all(
                    updatedLog.needToActiveTheseIDs.map(async (id) => {
                        return coreSwimmyService.activateTransaction(
                            context,
                            id,
                            nextScheduleTime,
                        );
                    }),
                );
                if (activateResult.some((r) => !r)) {
                    const zipped = updatedLog.needToActiveTheseIDs.map(
                        (id, i) => ({
                            id,
                            result: activateResult[i],
                        }),
                    );
                    context.error(
                        "execCoreSwimmyAPI activateTransaction failed:",
                        zipped,
                    );
                }
            }
        }
    } catch (error) {
        context.error("execCoreSwimmyAPI error", error);
        await CoreSwimmyApiLog.updateStatus(
            context,
            apiLog.requestOrderId,
            apiLog.swimmyType,
            CoreSwimmyStatus.STOPPING_FOR_ERROR,
            "不明なエラー",
        );
    } finally {
        await CoreSwimmyApiLog.updateJikkouchuFlg(
            apiLog.requestOrderId,
            apiLog.swimmyType,
            false,
        );
    }
}

const HandleCoreSwimmyApiRequestQueue: ServiceBusQueueHandler = async (
    message: unknown,
    context: InvocationContext,
): Promise<void> => {
    context.log("HandleCoreSwimmyApiRequestQueue start");
    try {
        const apiLog =
            typeof message === "object"
                ? message
                : JSON.parse(JSON.stringify(message));
        // if apiLog is empty or does not have requestOrderId throw error
        if (isNone(apiLog) || isNone(apiLog?.requestOrderId)) {
            throw new Error(
                "HandleCoreSwimmyApiRequestQueue: apiLog is empty: " +
                    JSON.stringify(apiLog),
            );
        }

        await useMongo(context);
        await execCoreSwimmyAPI(context, apiLog);
    } catch (error) {
        context.error("HandleCoreSwimmyApiRequestQueue error", error);
    }
};

export default HandleCoreSwimmyApiRequestQueue;
