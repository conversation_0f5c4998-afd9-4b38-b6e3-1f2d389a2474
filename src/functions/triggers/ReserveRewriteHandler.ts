import { HttpRequest, HttpResponseInit, InvocationContext, Timer } from "@azure/functions";
import AppConfig from "@/appconfig";
import { Sequelize } from "sequelize-typescript";
import { dirname } from "path";
import StorageTableService from "@/services/storageTableService";
import { retryQuery } from "@/helpers/queryHelper";
import { QueryTypes } from "sequelize";
import config from "config";

const select_sql = `select service_order_id from service_orders
 where order_status = '予約中' and function_type = '03'
  and tenant_id in ('KIC000', 'TST000')
   and reserve_date = CURRENT_DATE + interval '4 hours' order by 1 `;

const update_sql = `update service_orders
 set reserve_date = CURRENT_DATE + interval '45 minutes'
 where order_status = '予約中'
 and function_type = '03'
 and tenant_id in ('KIC000', 'TST000')
 and reserve_date = CURRENT_DATE + interval '4 hours'`;

const DB_CONNECTION_STRING = process.env.DATABASE_CORE_POSTGRESQL_CONNECTION_STRING;

type CheckDoubleExecResult = {
    alreadyRunning: boolean;
    error?: any;
    storageTable: StorageTableService;
    rowKey?: string;
}

export default class ReserveRewriteHandler {
    /**
     * DBアクセスリトライ回数
     */
    private apDbRetryMaxCnt = config.get<number>("mvno.ApDbRetryMaxCnt");

    /**
     * DBアクセスリトライ間隔
     */
    private apDbRetryInterval = config.get<number>("mvno.ApDbRetryInterval");
    private context: InvocationContext;
    constructor(context: InvocationContext) {
        this.context = context;
    }

    private async useCorePsql(): Promise<Sequelize> {
        const dbConfig = await AppConfig.getDBConfig(null);
        const psqlDBURI = dbConfig.DATABASE_CORE_POSTGRESQL_CONNECTION_STRING;
        const sequelize = new Sequelize(psqlDBURI, {
            pool: {
                max: 5,
                min: 0,
                idle: 0,
                acquire: 3000,
            },
            models: [dirname(__dirname) + "/core/entity"],
            define: {
                timestamps: false,
            },
            dialectOptions: {
                useUTC: false, // for reading from database
            },
            timezone: "+09:00", // for writing to database
            // logging: false,
        });
        await sequelize.authenticate();
        return sequelize;
    }

    private async checkDoubleExec(
    ): Promise<CheckDoubleExecResult> {
        const storageTable = new StorageTableService(
            this.context,
            "MntScriptPid"
        );
        const tableClient = await storageTable.init();
        const result = await storageTable.getUniqueRowKeys();
        if (result.length > 0 && result[0]) {
            this.context.log("Already running");
            const lastRunISOString = result[0];
            if ((new Date().getTime() - new Date(lastRunISOString).getTime()) < 30 * 60 * 1000) {
                return {
                    alreadyRunning: true,
                    error: result,
                    storageTable: null,
                    rowKey: null
                };
            } else {
                this.context.log("Last run was more than 30 minutes ago, clearing the entity.");
                await storageTable.deleteEntity("ReserveRewrite", lastRunISOString);
            }
        }
        const pid = process.pid;
        const now = new Date();
        const rowKey = `${now.toISOString()}`;
        await tableClient.createEntity({
            partitionKey: "ReserveRewrite",
            rowKey,
            data: JSON.stringify({ pid, now }),
        });
        this.context.log(`Created entity with row key: ${rowKey}`);
        return {
            alreadyRunning: false,
            storageTable,
            rowKey
        };
    }

    public async execReserveRewrite (
    ) {
        const isRunning = await this.checkDoubleExec();
        if (isRunning.alreadyRunning) {
            this.context.error("二重起動を検知したため起動を抑止します。");
            this.context.debug(`Already running: ${JSON.stringify(isRunning.error)}`);
            return;
        }
        const pidStorageTable = isRunning.storageTable;
        const rowKey = isRunning.rowKey;
        if (!pidStorageTable) {
            this.context.error("PIDストレージテーブルが取得できませんでした。");
            return;
        }

        this.context.log("予約日時書き換え処理を開始します。");

        const sequelize = await this.useCorePsql();
        try {
            let id_list: string[] = [];
            try {
                id_list = await retryQuery(
                    this.context,
                    "SelectReserveRewrite",
                    async () => {
                        const result = await sequelize.query(select_sql, {
                            type: QueryTypes.SELECT,
                            logging: false,
                        });
                        return result.map((row: any) => String(row.service_order_id));
                    },
                    this.apDbRetryMaxCnt,
                    this.apDbRetryInterval,
                );
            } catch (e) {
                this.context.error("予約日時書き換え対象オーダの検索に失敗しました。", e);
                throw e;
            }
            if (id_list.length === 0) {
                this.context.log("書き換え対象のオーダがありません。");
                return;
            }
            this.context.log("書き換え対象のオーダIDは次の通りです。");
            this.context.log(id_list);
            try {
                await retryQuery(
                    this.context,
                    "UpdateReserveRewrite",
                    async () => {
                        await sequelize.query(update_sql, {
                            type: QueryTypes.UPDATE,
                            logging: false,
                        });
                    },
                    this.apDbRetryMaxCnt,
                    this.apDbRetryInterval,
                );
            } catch (e) {
                this.context.error("予約日時書き換え対象オーダの更新に失敗しました。", e);
                throw e;
            }
            return;
        } catch (e) {
            throw e;
        } finally {
            await sequelize.close();
            try {
                await retryQuery(this.context,
                    "ClearPidStorageTable",
                    async () => {
                        await pidStorageTable.deleteEntity("ReserveRewrite", rowKey);
                    },
                    this.apDbRetryMaxCnt,
                    this.apDbRetryInterval,
                );
                this.context.info("処理を終了します。");
            } catch (e) {
                this.context.error("PIDストレージテーブルのクリアに失敗しました。", e);
            }
        }
    }

    /**
     * Handler function for the Azure Function
     * @param context InvocationContext
     */
    public static Handler = async (
        requestOrTimer: HttpRequest | Timer,
        context: InvocationContext,
    ): Promise<HttpResponseInit | null> => {
        context.log("ReserveRewriteHandler start");
        try {
            const handler = new ReserveRewriteHandler(context);
            await handler.execReserveRewrite();
            context.log("ReserveRewriteHandler end");
            return {
                status: 200,
            }
        } catch (error) {
            context.error("ReserveRewriteHandler error", error);
            return {
                status: 500,
                jsonBody: {
                    error
                },
            }
        }
    }
}