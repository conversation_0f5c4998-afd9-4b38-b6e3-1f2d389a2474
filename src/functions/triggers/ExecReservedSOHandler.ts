import { HttpResponseInit, Timer } from "@azure/functions";
import { Op } from "sequelize";
import { usePsql } from "@/database/psql";
import StorageTableService from "@/services/storageTableService";
import AppConfig from "@/appconfig";
import enqueueMessage from "@/services/servicebus";
import ExtendedInvocationContext from "@/types/ExtendedInvocationContext";
import { retryQuery } from "@/helpers/queryHelper";
import ServiceOrdersEntity from "@/core/entity/ServiceOrdersEntity";
import config from "config";
import MsgKeysConstants from "@/core/constant/MsgKeysConstants";
import AbstractMvnoBaseCommon from "@/core/common/AbstractMvnoBaseCommon";
import { ExecReservedSOProperties } from "@/utils/ExecReservedSOProperties";
import { CreateDeleteEntityAction } from "@azure/data-tables";
import { WhereOptions } from "sequelize/types/model";

type CheckDoubleExecResult = {
    alreadyRunning: boolean;
    error?: any;
    storageTable: StorageTableService;
    rowKey?: string;
};

/**
 * ExecReservedSOProducer
 */
export class ExecReservedSOHandler extends AbstractMvnoBaseCommon{
    /**
     * DBアクセスリトライ回数
     */
    private apDbRetryMaxCnt = config.get<number>("mvno.ApDbRetryMaxCnt");

    /**
     * DBアクセスリトライ間隔
     */
    private apDbRetryInterval = config.get<number>("mvno.ApDbRetryInterval");

    private MAX_REQUEST_QUEUE_SIZE = config.get<number>("mvno.maxRequestQueueSize");

    private execReservedSOProperties: ExecReservedSOProperties;

    private tcpRegulationFlg: boolean = false;

    public static readonly STATUS_PENDING = "pending";
    public static readonly STATUS_PROCESSING = "processing";
    public static readonly STATUS_COMPLETED = "completed";

    constructor(context: ExtendedInvocationContext) {
        super(null, context);
        this.execReservedSOProperties = new ExecReservedSOProperties(this.context);
    }

    /**
     * Uses Azure Storage Table to prevent multiple running instances
     */
    private async checkDoubleExec(): Promise<CheckDoubleExecResult> {
        const pidStorageTable = new StorageTableService(
            this.context,
            "ExecReservedSOPid"
        );
        const pidStorageTableClient = await pidStorageTable.init();
        const result = await pidStorageTable.getUniqueRowKeys();

        if (result.length > 0 && result[0]) {
            this.context.log("Already running");
            const lastRunISOString = result[0];
            // If the last run was more than 5 minutes ago, consider it ended
            if ((new Date().getTime() - new Date(lastRunISOString).getTime()) < 5 * 60 * 1000) {
                return {
                    alreadyRunning: true,
                    error: result,
                    storageTable: null,
                    rowKey: null
                };
            } else {
                this.context.log("Last run was more than 5 minutes ago, clearing the entity.");
                await pidStorageTable.deleteEntity("ExecReservedSO", lastRunISOString);
            }
        }

        // Create a new entity to track this run
        const pid = process.pid;
        const now = new Date();
        const rowKey = `${now.toISOString()}`;
        await pidStorageTableClient.createEntity({
            partitionKey: "ExecReservedSO",
            rowKey,
            data: JSON.stringify({ pid, now }),
        });

        this.context.log(`Created entity with row key: ${rowKey}`);
        return {
            alreadyRunning: false,
            storageTable: pidStorageTable,
            rowKey
        };
    }

    /**
     * Get service orders that are ready to be executed
     */
    private async getReservedServiceOrders(limit: number): Promise<ServiceOrdersEntity[]> {
        try {
            const results = await retryQuery(
                this.context,
                "getReservedServiceOrders",
                async () => {
                    const whereClause: WhereOptions<ServiceOrdersEntity> = {
                        orderStatus: "予約中",
                        reserveDate: {
                            [Op.lte]: new Date().toISOString(),
                        }
                    };

                    const coreConfig = AppConfig.getCoreConfig(this.context);

                    if (!coreConfig.V1_HAS_STOPPED_RUNNING) {
                        whereClause.serviceOrderId = {
                            [Op.like]: `${this.execReservedSOProperties.getApiProcessIDServer()}%`,
                        };
                    }
                    return await ServiceOrdersEntity.findAll({
                        where: whereClause,
                        order: [["reserve_date", "ASC"], ["service_order_id", "ASC"]],
                        limit
                    }) as ServiceOrdersEntity[];
                },
                this.apDbRetryMaxCnt,
                this.apDbRetryInterval,
            );

            this.context.log(`Found ${results.length} service orders to execute`);
            return results;
        } catch (error) {
            this.context.error(super.getMessage(MsgKeysConstants.APSORE9999,
                ["予約済みSO検索処理でデータベースアクセスエラーが発生しました。"]));
            throw error;
        }
    }

    /**
     * @returns Object containing counts of processed and skipped orders
     */
    private async sendToServiceBus(serviceOrders: ServiceOrdersEntity[], bq1: {partitionKey: string, rowKey: string, status: string}[], bq2: {partitionKey: string, rowKey: string, status: string}[], reservedSOStorageTable: StorageTableService): Promise<number> {
        const coreConfig = AppConfig.getCoreConfig(this.context);
        const connectionString = coreConfig.RESERVED_SO.SERVICE_BUS_CONNECTION_STRING;
        const queueName = coreConfig.RESERVED_SO.SERVICE_BUS_QUEUE_NAME;

        this.context.log(`Found ${serviceOrders.length} service orders to potentially send to Service Bus queue`);

        let processedCount = 0;

        for (const order of serviceOrders) {
            try {
                // Check if the order is already being processed
                const isProcessing = bq1.some(item => item.rowKey === order.serviceOrderId) || bq2.some(item => item.rowKey === order.serviceOrderId);
                if (isProcessing) {
                    this.context.log(`Service order ${order.serviceOrderId} is already being processed or completed. Skipping.`);
                    continue;
                }
                // set order status to pending
                await reservedSOStorageTable.createEntity({
                    partitionKey: "orders",
                    rowKey: order.serviceOrderId,
                    status: ExecReservedSOHandler.STATUS_PENDING,
                    timestamp: new Date().toISOString()
                });

                await enqueueMessage(
                    this.context as ExtendedInvocationContext,
                    connectionString,
                    queueName,
                    order.serviceOrderId,
                    order
                );
                this.context.log(`Successfully sent service order ${order.serviceOrderId} to Service Bus`);
                processedCount++;
            } catch (error) {
                this.context.error(super.getMessage(MsgKeysConstants.APSORE9999, [`オーダの積み込み処理に失敗しました。SOID=${order.serviceOrderId}`]), error);
                // Don't count in either category
            }
        }

        return processedCount;
    }

    public async execReservedSO(): Promise<void> {
        // Check if another instance is already running
        const isRunning = await this.checkDoubleExec();
        this.context.log("isRunning", isRunning);
        if (isRunning.alreadyRunning) {
            this.context.warn(super.getMessage(MsgKeysConstants.APSORW0001, []));      // should not error as now is using things like cronjob instead
            this.context.debug(`Already running: ${JSON.stringify(isRunning.error)}`);
            return;
        }

        const pidStorageTable = isRunning.storageTable;
        const rowKey = isRunning.rowKey;

        if (!pidStorageTable) {
            this.context.error(super.getMessage(MsgKeysConstants.APSORE9999,
                ["予約実行機能の処理で予期しないエラーが発生しました。"]));
            this.context.info(super.getMessage(MsgKeysConstants.APSORI1002, []));
            return;
        }
        try {
            // プロパティチェック
            if (!await this.checkProperties()) {
                this.context.error(super.getMessage(MsgKeysConstants.APSORE0002,
                    ["プロパティチェックでエラーが発生しました。"]));
                return;
            }

            this.context.log("予約実行処理を開始します。");
            // ExecResevedSOProducer start

            // TCP規制時間チェック
            if (this.isTcpRegulation()) {
                // 規制中
                this.context.debug("TCP out of service.");
                if (!this.tcpRegulationFlg) {
                    // これまで規制中でなかった場合は規制開始メッセージを表示する
                    this.context.info(super.getMessage(MsgKeysConstants.APSORI0401,
                        [this.execReservedSOProperties.getTpcRegulationTime()]));
                    this.tcpRegulationFlg=true;
                }
                return;
            }
            else {
                // 規制中ではない
                if (this.tcpRegulationFlg) {
                    // これまで規制中であった場合は規制終了メッセージを表示する
                    this.context.info(super.getMessage(MsgKeysConstants.APSORI0402
                        ,[this.execReservedSOProperties.getTpcRegulationTime()]));
                    this.tcpRegulationFlg=false;
                }
            }
            // clear bq3
            const tableName = "ReservedSO";
            const reservedSOStorageTable = new StorageTableService(this.context, tableName);
            const tableClient = await reservedSOStorageTable.init();

            const entities: {partitionKey: string, rowKey: string, status: string}[] = [];
            for await (const entity of await reservedSOStorageTable.listEntitiesByPartitionKey("orders")) {
                entities.push(entity);
            }

            const bq1 = entities.filter((entity) => entity.status === ExecReservedSOHandler.STATUS_PENDING);
            const bq2 = entities.filter((entity) => entity.status === ExecReservedSOHandler.STATUS_PROCESSING);
            const bq3 = entities.filter((entity) => entity.status === ExecReservedSOHandler.STATUS_COMPLETED);
            // 実行済みリストクリア
            // 実行済みリストに入っていても、REST APIで実行されていれば次の検索処理では検索されないし、
            // REST APIで処理する前にエラーで終わっていて次の検索処理で検索されれば再実行しないといけないので、
            // 新しいオーダ作成時の除外確認で実行済みリストは不要であり、クリアしても問題ない
            const currentBqSize = bq1.length;
            const currentBq2Size = bq2.length;
            const currentBq3Size = bq3.length;
            this.context.log(`Current Queue3:`+bq3);
            let deleteBq3Batches: CreateDeleteEntityAction[] = [];
            for (const entity of bq3) {
                deleteBq3Batches.push([
                    "delete",
                    {
                        partitionKey: entity.partitionKey,
                        rowKey: entity.rowKey,
                    },
                ]);
                if (deleteBq3Batches.length === 100) {
                    await tableClient.submitTransaction(deleteBq3Batches);
                    deleteBq3Batches = [];
                }
            }
            if (deleteBq3Batches.length > 0) {
                await tableClient.submitTransaction(deleteBq3Batches);
            }
            if (currentBq3Size > 0) {
                this.context.info(super.getMessage(MsgKeysConstants.APSORI0301,
                    [String(currentBqSize), String(currentBq2Size), String(currentBq3Size)]));
            }

            const remainingSize = this.MAX_REQUEST_QUEUE_SIZE - currentBqSize - currentBq2Size;
            let serviceOrders: ServiceOrdersEntity[];
            try {
                // データベースから予約オーダを検索
                serviceOrders = await this.getReservedServiceOrders(remainingSize);
                // 検索件数ログ出力
                this.context.info(super.getMessage(MsgKeysConstants.APSORI0501,
                    [serviceOrders.length.toString()]));
            } catch (error) {
                this.context.error(error.message, error);
                this.context.error(super.getMessage(MsgKeysConstants.APSORE9999,
                    ["Database Access Error. Process continued."]));
                this.context.error(super.getMessage(MsgKeysConstants.APSORE5001, ["0"]));
                return;
            }

            if (serviceOrders.length > 0) {
                const processedCount = await this.sendToServiceBus(serviceOrders, bq1, bq2, reservedSOStorageTable);
                if (processedCount > 0) {
                    this.context.info(super.getMessage(MsgKeysConstants.APSORI0701, []));
                }
            }
        } finally {
            // Clear the entity from the storage table, allowing another instance to run
            if (pidStorageTable && rowKey) {
                await pidStorageTable.deleteEntity("ExecReservedSO", rowKey);
            }
        }
    }

    // in ExecReservedSOMain
    private async checkProperties(): Promise<boolean> {
        let chkVal: string;
        // 必須項目チェック
        const chkResStr = this.execReservedSOProperties.requireCheck();
        if (chkResStr !== "") {
            this.context.error(super.getMessage(MsgKeysConstants.APSORE0098, [chkResStr]));
            return false;
        }

        /* TPC接続不可時間チェック */
        chkVal = this.execReservedSOProperties.getTpcRegulationTime();
        if (!this.execReservedSOProperties.chkTpcRegulationTime()) {
            this.context.error(super.getMessage(MsgKeysConstants.APSORE0003, [chkVal]));
            return false;
        }
        this.context.info(super.getMessage(MsgKeysConstants.APSORI0002,[chkVal]));

        /* API処理IDサーバ識別チェック */
        chkVal = this.execReservedSOProperties.getApiProcessIDServer();
        this.context.debug("ApiProcessIDServer:"+chkVal);
        if (this.execReservedSOProperties.chkApiProcessIDServer()) {
            this.context.info(super.getMessage(MsgKeysConstants.APSORI0004,[chkVal]));
        } else {
            this.context.error(super.getMessage(MsgKeysConstants.APSORI0004, [chkVal]));
            return false;
        }

        /* TPC最大同時接続数チェック */
        chkVal = this.execReservedSOProperties.getTpcMaxConnections().toString();
        this.context.debug("TpcMaxConnections:"+chkVal);
        if (this.execReservedSOProperties.chkTpcMaxConnections()) {
            this.context.info(super.getMessage(MsgKeysConstants.APSORI0006,[chkVal]));
        } else {
            this.context.error(super.getMessage(MsgKeysConstants.APSORE0007, [chkVal]));
            return false;
        }

        /* 実行キュー積み込み処理間隔チェック */
        chkVal = this.execReservedSOProperties.getProducerPeriod().toString();
        this.context.debug("ProducerPeriod:"+chkVal);
        if (this.execReservedSOProperties.chkProducerPeriod()) {
            this.context.info(super.getMessage(MsgKeysConstants.APSORI0101,[chkVal]));
        } else {
            this.context.error(super.getMessage(MsgKeysConstants.APSORE0101,[chkVal]));
            return false;
        }

        /* 実行キュー送信処理間隔(ミリ秒)チェック */
        chkVal = this.execReservedSOProperties.getWorkerPeriod().toString();
        this.context.debug("WorkerPeriod:"+chkVal);
        if (this.execReservedSOProperties.chkWorkerPeriod()) {
            this.context.info(super.getMessage(MsgKeysConstants.APSORI0102,[chkVal]));
        } else {
            this.context.error(super.getMessage(MsgKeysConstants.APSORE0102,[chkVal]));
            return false;
        }

        return true;
    }

    private isTcpRegulation(): boolean {
        // このスレッドは1多重でしか動かないので、SimpleDateFormatのsynchronizeは実施しない
        const tpcRegulationStart: string =
            this.execReservedSOProperties.getTpcRegulationStart();
        const tpcRegulationEnd: string =
            this.execReservedSOProperties.getTpcRegulationEnd();
        try {
            const currentDt = new Date();
            const startDt = new Date(currentDt.getFullYear(), currentDt.getMonth(), currentDt.getDate(), Number(tpcRegulationStart.split(":")[0]), Number(tpcRegulationStart.split(":")[1]), Number(tpcRegulationStart.split(":")[2]));
            const endDt = new Date(currentDt.getFullYear(), currentDt.getMonth(), currentDt.getDate(), Number(tpcRegulationEnd.split(":")[0]), Number(tpcRegulationEnd.split(":")[1]), Number(tpcRegulationEnd.split(":")[2]));
            if ((startDt.getTime() <= currentDt.getTime()) && (currentDt.getTime() <= endDt.getTime())) {
                this.context.info("tpc regulation: true");
                return true;
            } else {
                this.context.info("tpc regulation: false");
                return false;
            }
        } catch (e: any) {
            this.context.error(e.message, e);
            // 基本的にここに入ることはないはず。
            // 仮にはいった場合は念のためtrueを返却。
            return true;
        }
    }

    public static Handler = async (
        timer: Timer,
        context: ExtendedInvocationContext,
    ): Promise<HttpResponseInit | null> => {
        context.log("ExecReservedSOHandler start");

        try {
            await usePsql();
            const handler = new ExecReservedSOHandler(context);
            await handler.execReservedSO();
            context.log("ExecReservedSOHandler end");
            return {
                status: 200,
            };
        } catch (error) {
            context.error("ExecReservedSOHandler error", error);
            return {
                status: 500,
                jsonBody: {
                    error
                },
            };
        }
    };
}
