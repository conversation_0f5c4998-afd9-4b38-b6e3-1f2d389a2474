import FunctionExternalApiHandlerBase from "@/core/common/FunctionExternalApiHandler";
import Constants from "@/core/constant/Constants";
import { HttpRequest, HttpResponseInit } from "@azure/functions";
import ExtendedInvocationContext from "@/types/ExtendedInvocationContext";
import ResultCdConstants from "@/core/constant/ResultCdConstants";
import LineAddAcquisitionService from "@/core/service/impl/LineAddAcquisitionService";
import MvnoUtil from "@/core/common/MvnoUtil";
import LineAddAcquisitionInputDto from "@/core/dto/LineAddAcquisitionInputDto";
import Check from "@/core/common/Check";
import LineAddAcquisitionOutputDto from "@/core/dto/LineAddAcquisitionOutputDto";
import { LineAddAcquisitionPayload } from "@/types/coreSwimmyPayload";
import CoreSwimmyService from "@/services/coreSwimmyService";

export class LineAddAcquisitionHandler extends
    FunctionExternalApiHandlerBase {
    public static Meta = {
        localFunctionType: "03",
        url: Constants.LINES_CHARGE,
        getOrderType: (param: LineAddAcquisitionInputDto) => {
            return Check.checkOrderType(
                param.reserve_date,
                param.reserve_flag,
                param.reserve_soId,
            );
        },
        processName: "回線追加チャージ(クーポン)容量・期間追加",
    }

    public static Handler = async (
        request: HttpRequest,
        context: ExtendedInvocationContext,
    ): Promise<HttpResponseInit> => {
        const lineGroupAddAcquisitionService = new LineAddAcquisitionService(
            request,
            context,
        );
        try {
            const param = context.jsonBody;
            const ipaddress = MvnoUtil.getClientIPAddress(request);
            const result: LineAddAcquisitionOutputDto = await lineGroupAddAcquisitionService.service(param, ipaddress);

            context.log("LineAddAcquisitionHandler result", JSON.stringify(result));

            if (
                result.jsonBody.responseHeader.processCode ===
                ResultCdConstants.CODE_000000
            ) {
                await callCoreSwimmy(
                    request,
                    context,
                    param,
                    result,
                    LineAddAcquisitionHandler.Meta.getOrderType(param),
                );
            }
            return { jsonBody: result.jsonBody };
        } catch (error) {
            context.error(error);
            context.responseHeader.processCode = ResultCdConstants.CODE_999999;
            return this.CommonCheckCatchErrorHandler(request, context, error);
        }
    }
}

async function callCoreSwimmy(
    request: HttpRequest,
    context: ExtendedInvocationContext,
    param: LineAddAcquisitionInputDto,
    output: LineAddAcquisitionOutputDto,
    orderType: string | null,
) {
    if (output.additionalData.csvUnnecessary) {
        context.log(
            "LineAddAcquisitionHandler: skip coreSwimmy call due to csvUnnecessary",
        );
        return;
    }
    if (Constants.TYPE_5_TENANTS.includes(param.tenantId)) {
        context.log(
            "LineAddAcquisitionHandler: skip coreSwimmy call due to RINKモバイル",
        );
        return;
    }
    const payload: LineAddAcquisitionPayload = {
        receivedDate: context.responseHeader.receivedDate
            .substring(0, 10)
            .replace(/\//g, ""),
        frontSoId: param.targetSoId,
        coreSoId: context.responseHeader.apiProcessID,
        opfTenantId: param.tenantId,
        tenantId: param.tenantId,
        orderType,
        lineNo: param.lineNo,
        nNo: output.additionalData.nNo,
        tBan: output.additionalData.tBan,
        requestDate: output.additionalData.requestDate,
    };

    const coreSwimmy = new CoreSwimmyService(request, context);
    await coreSwimmy.registerLineAddAcquisitionTransaction(payload);
}