/* tslint:disable:no-string-literal */
import AppConfig from "@/appconfig";
import { dirname } from "path";
import { Sequelize } from "sequelize-typescript";

let sequelize: Sequelize | null = null;

async function loadPsql(): Promise<Sequelize> {
    if (sequelize) {
        return sequelize;
    }
    const dbConfig = await AppConfig.getDBConfig(null);
    const psqlDBURI = dbConfig["DATABASE_CORE_POSTGRESQL_CONNECTION_STRING"];
    // const psqlDBURI = "postgresql://localhost:5432/mawp";
    sequelize = new Sequelize(psqlDBURI, {
        pool: {
            /*
             * Increased max connections to handle concurrent requests better
             * Ideally you want to choose a max` number where this holds true:
             * max * EXPECTED_MAX_CONCURRENT_LAMBDA_INVOCATIONS < MAX_ALLOWED_DATABASE_CONNECTIONS * 0.8
             */
            max: 15,
            /*
             * Keep minimum connections to reduce connection churn
             */
            min: 2,
            /*
             * Allow connections to stay idle for 30 seconds before cleanup
             * This reduces connection churn for frequent operations
             */
            idle: 30000,
            // Increased timeout to handle complex transactions like LinePlanAcquisitionServiceTx
            acquire: 10000,
            /*
             * Ensures the connection pool attempts to be cleaned up automatically on the next Lambda
             * function invocation, if the previous invocation timed out.
             */
            evict: 60000, // 1 minute eviction timeout
        },
        models: [dirname(__dirname) + "/core/entity"],
        define: {
            timestamps: false,
        },
        dialectOptions: {
            useUTC: false, // for reading from database
        },
        timezone: "+09:00", // for writing to database
        logging: false, // disable PSQL query logging
        // Add connection monitoring and better error handling
        retry: {
            max: 3,
            match: [
                /ConnectionError/,
                /ConnectionRefusedError/,
                /ConnectionTimedOutError/,
                /TimeoutError/,
                /SequelizeConnectionError/,
                /SequelizeConnectionRefusedError/,
                /SequelizeConnectionTimedOutError/,
                /SequelizeHostNotFoundError/,
                /SequelizeHostNotReachableError/,
                /SequelizeInvalidConnectionError/,
                /SequelizeConnectionAcquireTimeoutError/
            ]
        }
    });

    // Add connection pool event listeners for monitoring
    sequelize.connectionManager.pool.on('acquire', (connection) => {
        console.log(`Connection acquired: ${connection.uuid || 'unknown'}`);
    });

    sequelize.connectionManager.pool.on('release', (connection) => {
        console.log(`Connection released: ${connection.uuid || 'unknown'}`);
    });

    sequelize.connectionManager.pool.on('remove', (connection) => {
        console.log(`Connection removed: ${connection.uuid || 'unknown'}`);
    });

    sequelize.connectionManager.pool.on('createError', (error) => {
        console.error('Connection pool create error:', error);
    });

    sequelize.connectionManager.pool.on('acquireError', (error) => {
        console.error('Connection pool acquire error:', error);
    });

    // or `sequelize.sync()`
    await sequelize.authenticate();

    return sequelize;
}

export const usePsql = async () => {
    return await loadPsql();
};

/**
 * Get connection pool status for monitoring
 */
export const getConnectionPoolStatus = () => {
    if (!sequelize) {
        return { error: 'Sequelize not initialized' };
    }

    const pool = sequelize.connectionManager.pool;
    return {
        size: pool.size,
        available: pool.available,
        using: pool.using,
        waiting: pool.waiting,
        max: pool.options.max,
        min: pool.options.min,
        idle: pool.options.idle,
        acquire: pool.options.acquire,
        evict: pool.options.evict
    };
};

/**
 * Check if the error is a lock_not_available error
 * @param error error object
 */
export const isLockNotAvailableError = (error: any) => {
    // https://www.postgresql.org/docs/9.5/errcodes-appendix.html
    // last condition can be removed if we don't want to check the error code (more general)
    return (
        typeof error === "object" &&
        error !== null &&
        error?.name === "SequelizeDatabaseError" &&
        error?.parent?.code === "55P03" // lock_not_available
    );
};
