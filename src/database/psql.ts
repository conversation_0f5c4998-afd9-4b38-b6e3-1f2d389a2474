/* tslint:disable:no-string-literal */
import AppConfig from "@/appconfig";
import { dirname } from "path";
import { Sequelize } from "sequelize-typescript";

let sequelize: Sequelize | null = null;

async function loadPsql(): Promise<Sequelize> {
    if (sequelize) {
        return sequelize;
    }
    const dbConfig = await AppConfig.getDBConfig(null);
    const psqlDBURI = dbConfig["DATABASE_CORE_POSTGRESQL_CONNECTION_STRING"];
    // const psqlDBURI = "postgresql://localhost:5432/mawp";
    sequelize = new Sequelize(psqlDBURI, {
        pool: {
            /*
             * Ideally you want to choose a max` number where this holds true:
             * max * EXPECTED_MAX_CONCURRENT_LAMBDA_INVOCATIONS < MAX_ALLOWED_DATABASE_CONNECTIONS * 0.8
             */
            max: 5,
            /*
             * Set this value to 0 so connection pool eviction logic eventually cleans up all connections
             * in the event of a Lambda function timeout.
             */
            min: 0,
            /*
             * Set this value to 0 so connections are eligible for cleanup immediately after they're
             * returned to the pool.
             */
            idle: 0,
            // Choose a small enough value that fails fast if a connection takes too long to be established.
            acquire: 3000,
            /*
             * Ensures the connection pool attempts to be cleaned up automatically on the next Lambda
             * function invocation, if the previous invocation timed out.
             */
            // evict: CURRENT_LAMBDA_FUNCTION_TIMEOUT
        },
        models: [dirname(__dirname) + "/core/entity"],
        define: {
            timestamps: false,
        },
        dialectOptions: {
            useUTC: false, // for reading from database
        },
        timezone: "+09:00", // for writing to database
        logging: false, // disable PSQL query logging
    });

    // or `sequelize.sync()`
    await sequelize.authenticate();

    return sequelize;
}

export const usePsql = async () => {
    return await loadPsql();
};

/**
 * Check if the error is a lock_not_available error
 * @param error error object
 */
export const isLockNotAvailableError = (error: any) => {
    // https://www.postgresql.org/docs/9.5/errcodes-appendix.html
    // last condition can be removed if we don't want to check the error code (more general)
    return (
        typeof error === "object" &&
        error !== null &&
        error?.name === "SequelizeDatabaseError" &&
        error?.parent?.code === "55P03" // lock_not_available
    );
};
