import { InvocationContext } from "@azure/functions";
import AppConfig from "../appconfig";
import mongoose from "mongoose";

let db: typeof mongoose = null;

async function loadMongoose(context: InvocationContext) {
    if (!db) {
        context.info("Connecting to mongoDB");
        try {
            const dbConfig = await AppConfig.getDBConfig(context);
            const connectionString = dbConfig[
                "DATABASE_MONGODB_CONNECTION_STRING" // tslint:disable-line: no-string-literal
            ]
                .concat(
                    AppConfig.getCoreConfig(context).DB_NAME.CORE_API_SERVICE,
                )
                // tslint:disable-next-line: no-string-literal
                .concat(dbConfig["DATABASE_MONGODB_CONNECTION_OPTIONS"] || "");
            db = await mongoose.connect(connectionString);

            context.info("Connect mongoDB successfully");
        } catch (error) {
            context.error("Failed to connect mongoDB", error);
        }
    }
}

export const useMongo = async (context: InvocationContext) => {
    return await loadMongoose(context);
};

export const disconnectMongo = async (context: InvocationContext) => {
    if (db) {
        await db.disconnect();
        db = null;
    }
};
