import { InvocationContext } from "@azure/functions";
import { Redis, RedisOptions } from "ioredis";
import AppConfig from "../appconfig";

let redisClient: Redis = null;

async function loadRedis(context: InvocationContext) {
    if (redisClient) {
        return redisClient;
    }

    const dbConfig = await AppConfig.getDBConfig(context);
    const redisConfig: RedisOptions = {
        port: +dbConfig.DATABASE_REDIS_PORT,
        host: dbConfig.DATABASE_REDIS_HOSTNAME,
        password: dbConfig.DATABASE_REDIS_ACCESS_KEY,
    };
    redisClient = new Redis(redisConfig);

    redisClient.on("error", (error) => {
        if (error.name === "ECONNRESET") {
            context.warn("Connection to Redis timed out.", error.message);
        } else if (error.name === "ECONNREFUSED") {
            context.warn("Connection to Redis refused!", error.message); // should severityLevel be error ?
        } else {
            context.warn("Redis client error:", error);
        }
    });

    redisClient.on("reconnecting", (err: any) => {
        if (redisClient.status === "reconnecting")
            context.info("Reconnecting to Redis...");
        else context.warn("Error reconnecting to Redis:", err);
    });

    redisClient.on("connect", (err: any) => {
        if (!err) context.info("Connected to Redis!");
    });

    return redisClient;
}

export const useRedis = async (context: InvocationContext) => {
    return await loadRedis(context);
};
