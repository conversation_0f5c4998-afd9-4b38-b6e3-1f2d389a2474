import axios, { AxiosInstance } from "axios";
import crypto from "crypto";
import https from "https";
import md2 from "js-md2";
import { HttpRequest } from "@azure/functions";

import BaseService from "./baseService";
import ExtendedInvocationContext from "@/types/ExtendedInvocationContext";
import Constants from "@/core/constant/Constants";
import { getCoreSoCancelMockResponse } from "./mockdata";
import SoCancelInputDto from "@/core/dto/SoCancelInputDto";
import SOManagerInputDto from "@/core/dto/SOManagerInputDto";
import RequestHeader from "@/core/dto/RequestHeader";
import { TenantsEntity } from "@/core/entity/TenantsEntity";
import ResponseHeader from "@/core/dto/ResponseHeader";

const allowLegacyRenegotiationforNodeJsOptions = {
    httpsAgent: new https.Agent({
        secureOptions: crypto.constants.SSL_OP_LEGACY_SERVER_CONNECT,
    }),
};

/**
 * Service class to send request to Core API (self)
 */
export default class CoreAPIService extends BaseService {
    private client: AxiosInstance;
    private cachedTenant: TenantsEntity;

    public constructor(
        request: HttpRequest,
        context: ExtendedInvocationContext,
    ) {
        super(request, context);
    }

    /**
     * Send SO予約キャンセル request to Core API (self)
     * @param tenantId service order's tenant ID
     * @param serviceOrderIdKey service order ID (AP~)
     * @param csvUnnecessaryFlag Swimmy連携 flag (default: "1")
     */
    public async sendSoCancelRequest(
        tenantId: string,
        serviceOrderIdKey: string,
        csvUnnecessaryFlag: "1" | "0",
    ) {
        this.context.log(
            "CoreAPIService:sendSoCancelRequest start:",
            tenantId,
            serviceOrderIdKey,
            csvUnnecessaryFlag,
        );
        const request: SoCancelInputDto = {
            requestHeader: await this.getTenantRequestHeader(tenantId, "18"),
            tenantId,
            serviceOrderIdKey,
            csvUnnecessaryFlag,
            reserve_flag: undefined,
            reserve_soId: undefined,
        };

        this.context.debug(
            "CoreAPIService:sendSoCancelRequest request:",
            JSON.stringify(request),
        );

        const result = await this.getClient().post(
            Constants.SO_CANCEL,
            request,
        );

        let isOk = true;
        let processCode = "";
        let apiProcessID = "";
        if (result.status !== 200) {
            isOk = false;
            this.context.log(
                "CoreAPIService:sendSoCancelRequest error:",
                result.status,
                result.statusText,
                JSON.stringify(result.data),
            );
        } else {
            const response = result?.data?.responseHeader as ResponseHeader;
            processCode = response?.processCode || "";
            apiProcessID = response?.apiProcessID || "";
            isOk = processCode === "000000";
        }
        this.context.log(
            "CoreAPIService:sendSoCancelRequest result:",
            isOk,
            processCode,
            apiProcessID,
        );
        return { isOk, processCode, apiProcessID };
    }

    private getClient() {
        if (this.client) {
            return this.client;
        }
        this.client = axios.create({
            ...allowLegacyRenegotiationforNodeJsOptions,
            // NOTE reuse reserved SO API endpoint since it is Core API endpoint
            baseURL: this.coreConfig.RESERVED_SO.API_ENDPOINT,
            headers: {
                "Content-Type": "application/json",
                Accept: "application/json",
            },
        });

        this.mockResponse();

        return this.client;
    }

    private mockResponse() {
        if (this.coreConfig.API_SERVICE.RUN_WITH_MOCK) {
            this.context.log("CoreAPIService: using mock");
            const MockAdapter = require("axios-mock-adapter");
            const mockInstance = new MockAdapter(this.client);

            mockInstance
                .onPost(Constants.SO_CANCEL)
                .reply(200, getCoreSoCancelMockResponse());
        }
    }

    public async getTenantRequestHeader(
        tenantId: string,
        functionType: string,
    ): Promise<RequestHeader> {
        // create request header with API key from DB
        const tenant = await this.getTenant(tenantId);
        if (!tenant) {
            throw new Error(`Tenant not found: ${tenantId}`);
        }
        // CoreService is using `G` prefix (GUI?); so perhaps CoreAPI should use `C` prefix (Core?)
        const seqNoStr =
            "C" +
            (Math.floor(Math.random() * 999998) + 1)
                .toString()
                .padStart(6, "0");
        const digest = md2(seqNoStr);
        const apiKey = tenant.hashedPassword + "$02$" + digest;
        return {
            sequenceNo: seqNoStr,
            senderSystemId: "0001",
            apiKey,
            functionType,
        };
    }

    private async getTenant(tenantId: string): Promise<TenantsEntity> {
        if (this.cachedTenant && this.cachedTenant?.tenantId === tenantId) {
            return this.cachedTenant;
        }
        this.cachedTenant = await TenantsEntity.findOne({
            where: { tenantId },
        });
        return this.cachedTenant;
    }
}
