import { InvocationContext } from "@azure/functions";

import {
    NotificationCacheKey,
    NotificationType,
} from "@/constants/notificationType";
import { useRedis } from "@/database/redis";
import { getBooleanValue } from "@/utils";
import NotificationService from "@/services/notificationService";

/**
 * Get whether Core Swimmy has error (from Redis cache)
 */
export const getHasCoreSwimmyError = async (
    context: InvocationContext,
): Promise<boolean> => {
    const redisClient = await useRedis(context);
    const hasError = await redisClient.get(
        NotificationCacheKey.HAS_CORE_SWIMMY_ERROR_KEY,
    );
    if (hasError === undefined || hasError === null) {
        await setHasCoreSwimmyError(context, false);
        return false;
    }
    return getBooleanValue(hasError);
};

/**
 * Set error flag for Core Swimmy's Redis cache
 * and send notification to SignalR
 * @param context
 * @param hasError
 */
export const setHasCoreSwimmyError = async (
    context: InvocationContext,
    hasError: boolean,
) => {
    const redisClient = await useRedis(context);
    await redisClient.set(
        NotificationCacheKey.HAS_CORE_SWIMMY_ERROR_KEY,
        String(hasError), // will be 'true' or 'false'
    );
    await NotificationService.sendMessage(
        context,
        NotificationType.HAS_CORE_SWIMMY_ERROR,
        hasError,
    );
};
