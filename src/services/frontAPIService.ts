import axios, { AxiosInstance } from "axios";
import crypto from "crypto";
import https from "https";
import md2 from "js-md2";
import { HttpRequest } from "@azure/functions";

import BaseService from "./baseService";
import {
    FRONT_EXTERNAL_API,
    FRONT_EXTERNAL_API_FUNCTION_TYPE,
} from "@/constants/frontApi";
import RequestHeader from "@/core/dto/RequestHeader";
import SOManagerInputDto from "@/core/dto/SOManagerInputDto";
import { TenantsEntity } from "@/core/entity/TenantsEntity";
import {
    FrontExternalApiBaseResponse,
    FrontExternalApiRequestHeader,
    FrontExternalSoCancelRequest,
} from "@/types/frontExternalApi";
import ExtendedInvocationContext from "@/types/ExtendedInvocationContext";
import { getFrontExternalCancelMockResponse } from "./mockdata";

const CODE_OK = "000000";

const allowLegacyRenegotiationforNodeJsOptions = {
    httpsAgent: new https.Agent({
        secureOptions: crypto.constants.SSL_OP_LEGACY_SERVER_CONNECT,
    }),
};

/**
 * フロント外部APIサービス
 */
export default class FrontAPIService extends BaseService {
    private externalApiInstance: AxiosInstance = null;
    private parsedJsonBody: SOManagerInputDto;

    private cachedTenant: TenantsEntity;

    public constructor(
        request: HttpRequest,
        context: ExtendedInvocationContext,
    ) {
        super(request, context);
        this.parsedJsonBody = context.jsonBody as SOManagerInputDto;
    }

    /**
     * Send SO予約キャンセル request to フロント外部API (skip CoreAPI call)
     * @param tenantId
     * @param soId
     */
    public async sendSoCancelRequest(tenantId: string, soId: string) {
        this.context.log(
            "FrontService:sendSoCancelRequest start:",
            tenantId,
            soId,
        );
        const request: FrontExternalSoCancelRequest = {
            requestHeader: await this.getTenantRequestHeader(
                tenantId,
                FRONT_EXTERNAL_API_FUNCTION_TYPE.SO_CANCEL,
            ),
            tenantId,
            soId,
            // skipCore: "1", // no need to skip Core API call (we need to call FrontSoCancel API)
        };

        this.context.debug(
            "FrontService:sendSoCancelRequest request:",
            JSON.stringify(request),
        );

        const result = await this.getExternalApiClient().post(
            FRONT_EXTERNAL_API.SO_CANCEL,
            request,
        );
        let isOk = true;
        let apiProcessID = "";
        let processCode = "";
        if (result.status !== 200) {
            isOk = false;
            this.context.log(
                "FrontService:sendSoCancelRequest error:",
                result.status,
                result.statusText,
                JSON.stringify(result.data),
            );
        } else {
            const response = result.data as FrontExternalApiBaseResponse;
            processCode = response?.responseHeader?.processCode;
            isOk = processCode === CODE_OK;
            apiProcessID = response?.responseHeader?.apiProcessID || "";
        }
        this.context.log(
            "FrontService:sendSoCancelRequest end:",
            isOk,
            processCode,
            apiProcessID,
        );
        return { isOk, processCode, apiProcessID };
    }

    private getExternalApiClient() {
        if (this.externalApiInstance) {
            return this.externalApiInstance;
        }

        this.externalApiInstance = axios.create({
            ...allowLegacyRenegotiationforNodeJsOptions,
            baseURL: this.coreConfig.API_SERVICE.FRONT_EXTERNAL_API,
            headers: {
                "Content-Type": "application/json",
                Accept: "application/json",
            },
        });

        this.mockExternalApiResponse();

        return this.externalApiInstance;
    }

    private mockExternalApiResponse() {
        if (this.coreConfig.API_SERVICE.RUN_WITH_MOCK) {
            this.context.log("FrontAPIService: using mock for external API");
            const MockAdapter = require("axios-mock-adapter");
            const mockInstance = new MockAdapter(this.externalApiInstance);

            mockInstance
                .onPost(FRONT_EXTERNAL_API.SO_CANCEL)
                .reply(200, getFrontExternalCancelMockResponse());
        }
    }

    public async getTenantRequestHeader(
        tenantId: string,
        functionType: string,
    ): Promise<RequestHeader> {
        // create request header with API key from DB
        const tenant = await this.getTenant(tenantId);
        if (!tenant) {
            throw new Error(`Tenant not found: ${tenantId}`);
        }
        // CoreService is using `G` prefix (GUI?); so perhaps CoreAPI should use `C` prefix (Core?)
        const seqNoStr =
            "C" +
            (Math.floor(Math.random() * 999998) + 1)
                .toString()
                .padStart(6, "0");
        const digest = md2(seqNoStr);
        const apiKey = tenant.hashedPassword + "$02$" + digest;
        return {
            sequenceNo: seqNoStr,
            senderSystemId: "0001",
            apiKey,
            functionType,
        };
    }

    private async getTenant(tenantId: string): Promise<TenantsEntity> {
        if (this.cachedTenant && this.cachedTenant?.tenantId === tenantId) {
            return this.cachedTenant;
        }
        this.cachedTenant = await TenantsEntity.findOne({
            where: { tenantId },
        });
        return this.cachedTenant;
    }
}
