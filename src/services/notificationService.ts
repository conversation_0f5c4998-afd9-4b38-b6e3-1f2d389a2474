import { InvocationContext } from "@azure/functions";
import { ServiceBusClient } from "@azure/service-bus";

import AppConfig from "@/appconfig";
import { NotificationType } from "@/constants/notificationType";

/**
 * Class for enqueue notification message to service bus
 */
export default class NotificationService {
    /**
     * Send payload to service bus which later will be broadcasted by SignalR service
     * @param context
     * @param type NotificationType constant
     * @param message payload
     */
    public static async sendMessage(
        context: InvocationContext,
        type: NotificationType,
        message: any,
    ) {
        try {
            const coreConfig = AppConfig.getCoreConfig(context);
            const client = new ServiceBusClient(
                coreConfig.NOTIFICATION.SERVICE_BUS_CONNECTION_STRING,
            );
            const sender = client.createSender(
                coreConfig.NOTIFICATION.SERVICE_BUS_QUEUE_NAME,
            );
            context.log("SignalR service sendMessage:", { type, message });
            await sender.sendMessages([{ body: { type, message } }]);
            await sender.close();
        } catch (error) {
            context.error("SignalR service sendMessage - error:", error);
        }
    }
}
