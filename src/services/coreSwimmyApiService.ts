import axios, { AxiosInstance } from "axios";
import crypto from "crypto";
import https from "https";
import MockAdapter from "axios-mock-adapter";

import { InvocationContext } from "@azure/functions";
import {
    ICoreSwimmyApiLog,
    ICoreSwimmyApiLogDocument,
} from "@/models/coreSwimmyApiLog";
import { isNone } from "@/utils";

import BaseService from "./baseService";
import {
    checkPayloadForMock,
    getMockResponse,
    getReissueMockResponse,
} from "./mockdata";

/**
 * HTTP status codes that should not throw errors
 */
const NG_STATUS = [500, 503, 400, 409];

const allowLegacyRenegotiationforNodeJsOptions = {
    httpsAgent: new https.Agent({
        secureOptions: crypto.constants.SSL_OP_LEGACY_SERVER_CONNECT,
    }),
};

export type SendToCoreSwimmyResult = {
    isOk: boolean;
    statusCode: number;
    data: any;
};

/**
 * Service class for sending CoreSwimmyApiLog to Core Swimmy API server
 */
export default class CoreSwimmyApiService extends BaseService {
    private coreSwimmyInstance: AxiosInstance = null;
    public constructor(context: InvocationContext) {
        super(null, context);
    }

    public async sendToCoreSwimmyApi(
        apiLog: ICoreSwimmyApiLog | ICoreSwimmyApiLogDocument,
    ): Promise<SendToCoreSwimmyResult> {
        this.context.log(
            "sendToCoreSwimmyApi start",
            apiLog.swimmyType,
            apiLog.requestOrderId,
        );
        this.context.log("json body", JSON.stringify(apiLog.requestParam));

        // if requestParam is empty, throw error
        if (
            isNone(apiLog.requestParam) ||
            typeof apiLog.requestParam !== "object"
        ) {
            throw new Error(
                `sendToCoreSwimmyApi: ${apiLog.requestOrderId} requestParam is empty`,
            );
        }

        const result = await this.getCoreSwimmyClient().post(
            this.coreConfig.CORE_SWIMMY_API.ENDPOINT_PATH,
            apiLog.requestParam,
            {
                validateStatus: (status) => {
                    // 400: NG (validation error)
                    return (
                        (status >= 200 && status < 300) ||
                        NG_STATUS.includes(status)
                    );
                },
            },
        );
        let isOk = true;
        if (result.status !== 200) {
            // throw new Error(
            //     "sendToCoreSwimmyApi: " +
            //         apiLog.requestOrderId +
            //         " failed with status: " +
            //         result.status,
            // );
            isOk = false;
            this.context.error(
                "sendToCoreSwimmyApi (" +
                    apiLog.requestOrderId +
                    ") failed with status: " +
                    result.status,
                JSON.stringify(result.data), // include response content in error log
            );
        }
        // context.log('Core executeAPICall: ' + executeAPIName + ' response: ', data);
        this.context.log(
            "sendToCoreSwimmyApi response",
            JSON.stringify(result.data),
        );
        return {
            isOk,
            statusCode: result.status,
            data: result.data,
        };
    }

    private getCoreSwimmyClient() {
        if (this.coreSwimmyInstance) {
            return this.coreSwimmyInstance;
        }
        this.coreSwimmyInstance = axios.create({
            ...allowLegacyRenegotiationforNodeJsOptions,
            baseURL: this.coreConfig.CORE_SWIMMY_API.ENDPOINT_ORIGIN,
            headers: {
                "Content-Type": "application/json",
                Accept: "application/json",
            },
        });

        this.mockCoreSwimmyAPI();

        return this.coreSwimmyInstance;
    }

    private mockCoreSwimmyAPI() {
        if (this.coreConfig.CORE_SWIMMY_API.RUN_WITH_MOCK) {
            // const MockAdapter = require("axios-mock-adapter");
            const mockInstance = new MockAdapter(this.coreSwimmyInstance);

            // mock response
            mockInstance
                .onPost(this.coreConfig.CORE_SWIMMY_API.ENDPOINT_PATH)
                // .reply(200, getReissueMockResponse());
                .reply((config) => {
                    const payload = JSON.parse(config.data);
                    const check = checkPayloadForMock(payload);
                    // receiptId will be generated randomly
                    const response = getMockResponse(
                        false,
                        check.orderType || "004", // order type (********) or default to SIM reissue
                        check.inputFromReceiptId || null, // PF0 or AP0 from request
                        null,
                        null,
                    );
                    return [200, response];
                });

            // TODO add more mock for other API endpoints

            // --- for testing error handling ---
            // return HTTP 503 システムエラー
            mockInstance.onPost("/test/http-503").reply(503);
            // return HTTP 500 システムエラー
            mockInstance.onPost("/test/http-500").reply(500);
            // return HTTP 500 排他制御エラー
            mockInstance.onPost("/test/http-409").reply(409);
            // return HTTP 400 Bad Request
            mockInstance
                .onPost("/test/http-400")
                .reply(
                    400,
                    getReissueMockResponse(true, "S02", [
                        "電話番号が不正",
                        "テストエラーメッセージ",
                    ]),
                );
            // return HTTP 502 (Axios will throw error)
            mockInstance.onPost("/test/http-error").reply(502);
            // return HTTP 200 but with error code in response object
            mockInstance
                .onPost("/test/resp-error")
                .reply(200, getReissueMockResponse(true, "S00"));

            mockInstance.onPost("/test/resp-empty").reply(200, undefined);

            mockInstance.onPost("/test/throw-error").reply(async () => {
                throw new Error("mock error");
            });
        }
    }
}
