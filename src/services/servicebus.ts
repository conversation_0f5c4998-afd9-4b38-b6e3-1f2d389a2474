import ExtendedInvocationContext from "@/types/ExtendedInvocationContext";
import { ServiceBusClient } from "@azure/service-bus";


/**
 * Send a message to a Service Bus queue
 * @param context
 * @param connectionString
 * @param queueName
 * @param id
 * @param payload
 * @param scheduleTime unix timestamp in seconds to schedule the message, if null it will be sent immediately
 */
export default async function enqueueMessage(
    context: ExtendedInvocationContext,
    connectionString: string,
    queueName: string,
    id: string,
    payload: any,
    scheduleTime: number = null,
) {
    context.log(`sendToServiceBus: sending ${id}`);
    let sbClient: ServiceBusClient;
    try {
        sbClient = new ServiceBusClient(connectionString);
        const sender = sbClient.createSender(queueName);
        // exclude previous response object from the payload to reduce size
        delete payload?.responseParam;
        delete payload?.responseLog;
        await sender.sendMessages([
            {
                body: payload,
                scheduledEnqueueTimeUtc: scheduleTime ? new Date(scheduleTime * 1000) : undefined,
            },
        ]);
        await sender.close();
        context.log("sendToServiceBus: message sent", id);
    } catch (err) {
        context.error("sendToServiceBus error", err);
        // TODO throw error??
    } finally {
        if (sbClient) {
            await sbClient.close();
        }
    }
}
