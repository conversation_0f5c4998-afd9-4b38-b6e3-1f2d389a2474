import { InvocationContext, HttpRequest } from "@azure/functions";

import AppConfig from "@/appconfig";
import ExtendedInvocationContext from "@/types/ExtendedInvocationContext";
import { CoreConfig } from "@/types/config";

export default class BaseService {
    protected context: ExtendedInvocationContext | InvocationContext;
    protected request: HttpRequest;
    protected coreConfig: CoreConfig;
    public constructor(
        request: HttpRequest,
        context: ExtendedInvocationContext | InvocationContext,
    ) {
        this.context = context;
        this.request = request;
        this.coreConfig = AppConfig.getCoreConfig(context);
    }
}
