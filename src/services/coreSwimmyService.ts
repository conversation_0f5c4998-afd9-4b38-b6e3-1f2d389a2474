import { InvocationContext } from "@azure/functions";
import { ServiceBusAdministrationClient } from "@azure/service-bus";

import MsgKeysConstants from "@/core/constant/MsgKeysConstants";
import Constants from "@/core/constant/Constants";
import { ICoreSwimmySearchResult } from "@/models/coreSwimmyApiLog";
import { CORE_SWIMMY_TYPES, CoreSwimmyStatus, RESEND_SAME_MVNE_FLAG_TYPES } from "@/constants/coreSwimmy";

import APILinesDAO from "@/core/dao/APILinesDAO";
import OptionUpdate from "@/core/dto/OptionUpdate";
import CoreSwimmyApiLog, {
    ICoreSwimmyApiLogDocument,
    ICoreSwimmyResendQuery,
    ICoreSwimmyResendUpdate,
} from "@/models/coreSwimmyApiLog";

import { SwimmyType } from "@/types/coreSwimmyApiLog";
import {
    CoreSwimmyTransactionRequestHeader,
    CoreSwimmyReissueRequest,
    CoreSwimmyOptionRequest,
    CoreSwimmyDelRequest,
    CoreSwimmyNWContractRequest,
    CoreSwimmyFrontSoCancelRequest,
    CoreSwimmyPreLineAddRequest,
    AppBasicDtl,
    AppPrdt,
    SameMvneHaishiRequest,
    CoreSwimmyLineEnableRequest,
    CoreSwimmyLineGroupAddAcquisitionRequest,
    CoreSwimmyLineGroupModifyAcquisitionRequest,
    CoreSwimmyLineAddAcquisitionRequest,
    ResendOptions,
    CoreSwimmySoCancelRequest,
    CoreSwimmyPlanChangeRequest,
} from "@/types/coreSwimmyService";
import {
    CoreSwimmyLineEnablePayload,
    FrontSoCancelPayload,
    LineAddAcquisitionPayload,
    LineDeleteTransactionPayload,
    LinePlanAcquisitionPayload,
    LineGroupAddAcquisitionPayload,
    LineGroupModifyAcquisitionPayload,
    NWContractTransactionPayload,
    OptionChangeTransactionPayload,
    PreLineAddPayload,
    SameMvneHaishiPayload,
    SIMReissueTransactionPayload,
    SoCancelPayload,
    TransactionPayloadBase,
} from "@/types/coreSwimmyPayload";
import ExtendedInvocationContext from "@/types/ExtendedInvocationContext";

import { usePsql } from "@/database/psql";

import StringHelper from "@/helpers/stringHelper";
import TBanHelper from "@/helpers/tBanHelper";
import { getArrayValue, getNumberValue, getScheduleTimeForSwimmyOrder, getSystemDatetime, isNone, removeEmptyProperties } from "@/utils";
import CustomLogger from "@/utils/logger";

import BaseService from "./baseService";
import enqueueMessage from "./servicebus";
import ExtendedPortalInvocationContext from "@/types/ExtendedPortalInvocationContext";
import { SimTypes } from "@/constants/simConstants";
import PreLineAddService from "@/core/service/impl/PreLineAddService";
import AppConfig from "@/appconfig";

/**
 * Service class for sending CoreSwimmyApiLog to Service Bus queue
 */
export default class CoreSwimmyService extends BaseService {
    private apiLinesDao = new APILinesDAO(
        this.request,
        this.context as ExtendedInvocationContext,
    );
    private logger = new CustomLogger(
        this.request,
        this.context as ExtendedInvocationContext,
    );

    private sequenceNo: string =
        (this.context as ExtendedInvocationContext)?.jsonBody?.requestHeader
            ?.sequenceNo ?? "";

    /**
     * Send CoreSwimmyApiLog to Service Bus queue.
     * If `status` is not 未登録依頼 or `jikkouchuFlg` is not true, it will not be sent.
     * @param sender name of function that is calling this function
     * @param coreSwimmyApiLog payload
     * @param requestScheduleTime unix timestamp in seconds to schedule the message.
     * if `null`, will use `coreSwimmyApiLog.scheduledAt` to send as scheduled or immediate message
     */
    public async sendToServiceBus(coreSwimmyApiLog: ICoreSwimmyApiLogDocument, requestScheduleTime: number = null): Promise<void> {
        // if it is a 待機中 api log (jikkouchuFlg=false), don't send to SB
        if (
            coreSwimmyApiLog.jikkouchuFlg &&
            coreSwimmyApiLog.status ===
                CoreSwimmyStatus.TOUROKU_IRAI_NOT_YET_SENT
        ) {
            let scheduleTime: number = null;
            if (!isNone(requestScheduleTime)) {
                scheduleTime = requestScheduleTime;
            } else if (!isNone(coreSwimmyApiLog.scheduledAt)) {
                scheduleTime = coreSwimmyApiLog.scheduledAt;
            }
            this.context.log(
                "sendToServiceBus:",
                coreSwimmyApiLog.requestOrderId,
                coreSwimmyApiLog.swimmyType,
                coreSwimmyApiLog.id,
                requestScheduleTime,
                scheduleTime,
            );
            await enqueueMessage(
                this.context as ExtendedInvocationContext,
                this.coreConfig.CORE_SWIMMY_API.POLLING
                    .SERVICE_BUS_CONNECTION_STRING,
                this.coreConfig.CORE_SWIMMY_API.POLLING.SERVICE_BUS_QUEUE_NAME,
                coreSwimmyApiLog.requestOrderId,
                coreSwimmyApiLog.toObject(),
                scheduleTime,
            );
        } else {
            this.context.log(
                "sendToServiceBus skipped:",
                coreSwimmyApiLog.requestOrderId,
                coreSwimmyApiLog.swimmyType,
                coreSwimmyApiLog.id,
                coreSwimmyApiLog.jikkouchuFlg,
                coreSwimmyApiLog.status,
                requestScheduleTime,
            );
        }
    }

    /**
     * （情報域）共通ヘッダ情報を生成する
     *
     * @returns CoreSwimmyTransactionRequestHeader
     */
    private generateCommonHeaderInfo(): CoreSwimmyTransactionRequestHeader {
        return {
            requestFromSystemId: "MAP",
            otherSystemSendDatetime: getSystemDatetime(),
            operatorId: null,
            operatorGroupId: null,
        };
    }

    private async createLineGroupAddAcquisitionTransactionRequest(
        payload: LineGroupAddAcquisitionPayload,
    ): Promise<CoreSwimmyLineGroupAddAcquisitionRequest> {
        return removeEmptyProperties({
            commonHeaderInfo: this.generateCommonHeaderInfo(),
            appRequestInfo: {
                processType: "03", // 「03:ワーニング突破」固定
                appInfoList: [
                    {
                        appBasic: {
                            receiptId: null,
                            inputFromType: "01", // 「01:MVNO顧客」固定
                            receiptIdSerialno: null,
                            orderType: "11", // 「11:商品変更」固定
                            appStatus: "15", // 「15:オーダ確定」固定
                            appDate: payload.receivedDate, // 申込日を設定(yyyymmdd)
                            requestDate: payload.requestDate, // 希望日を設定(yyyymmdd)
                            salesChannelCode: null, // Swimmyにて補填
                            salesChannelName: null, // Swimmyにて補填
                            registName: null, // Swimmyにて補填
                            inputFromReceiptId: payload.frontSoId, // 卸フロントのSO-ID（PF～）
                            appBasicDtlList: [
                                {
                                    appAttributeCode: "********", // レコード種別
                                    appAttributeValue: "001" // 「001:回線追加」固定
                                },
                                {
                                    appAttributeCode: "M0000012", // 「M0000012:発信番号」固定
                                    appAttributeValue: payload.lineNo, // 対象の回線番号を設定
                                },
                                {
                                    appAttributeCode: "M0000013", // 契約番号（N番号）
                                    appAttributeValue: payload.nNo, // 紐づくN番を設定
                                }
                            ]
                        },
                        appPrdtList: [
                            {
                                accountId: null,
                                accountPrdtId: null,
                                appPrdtStatus: "01", // 「01：オーダ中」固定
                                appPrdtCancelCause: null,
                                prdtCode: payload.tBan, // 申し込むオプションプランを設定
                                processType: "01", // 「01：追加」固定
                                prdtTypeCode: "0006", // 「0006:アカウントオプション」
                                price: null,
                                billingStartDate: null,
                                billingEndDate: null,
                                prdtSerialno: "1", // １回線申込内でユニークな通番を設定
                                parentPrdtSerialno: null,
                                appPrdtDtlList: [
                                    {
                                        prdtAttributeCode: "********", // 適用年月日
                                        prdtAttributeValue: payload.requestDate, // 利用開始年月日
                                    }
                                ]
                            }
                        ]
                    }
                ],
            }
        });
    }

    private async createLineAddAcquisitionTransactionRequest(
        payload: LineAddAcquisitionPayload,
    ): Promise<CoreSwimmyLineAddAcquisitionRequest> {
        return removeEmptyProperties({
            commonHeaderInfo: this.generateCommonHeaderInfo(),
            appRequestInfo: {
                processType: "03", // 「03:ワーニング突破」固定
                appInfoList: [
                    {
                        appBasic: {
                            receiptId: null,
                            inputFromType: "01", // 「01:MVNO顧客」固定
                            receiptIdSerialno: null,
                            orderType: "11", // 「11:商品変更」固定
                            appStatus: "15", // 「15:オーダ確定」固定
                            appDate: payload.receivedDate, // 申込日を設定(yyyymmdd)
                            requestDate: payload.requestDate, // 希望日を設定(yyyymmdd)
                            salesChannelCode: null, // Swimmyにて補填
                            salesChannelName: null, // Swimmyにて補填
                            registName: null, // Swimmyにて補填
                            inputFromReceiptId: payload.frontSoId, // 卸フロントのSO-ID（PF～）
                            appBasicDtlList: [
                                {
                                    appAttributeCode: "********", // レコード種別
                                    appAttributeValue: "001" // 「001:回線追加」固定
                                },
                                {
                                    appAttributeCode: "M0000012", // 「M0000012:発信番号」固定
                                    appAttributeValue: payload.lineNo, // 対象の回線番号を設定
                                },
                                {
                                    appAttributeCode: "M0000013", // 契約番号（N番号）
                                    appAttributeValue: payload.nNo, // 紐づくN番を設定
                                }
                            ]
                        },
                        appPrdtList: [
                            {
                                accountId: null,
                                accountPrdtId: null,
                                appPrdtStatus: "01", // 「01：オーダ中」固定
                                appPrdtCancelCause: null,
                                prdtCode: payload.tBan, // 申し込むオプションプランを設定
                                processType: "01", // 「01：追加」固定
                                prdtTypeCode: "0006", // 「0006:アカウントオプション」
                                price: null,
                                billingStartDate: null,
                                billingEndDate: null,
                                prdtSerialno: "1", // １回線申込内でユニークな通番を設定
                                parentPrdtSerialno: null,
                                appPrdtDtlList: [
                                    {
                                        prdtAttributeCode: "********", // 適用年月日
                                        prdtAttributeValue: payload.requestDate, // 利用開始年月日
                                    }
                                ]
                            }
                        ]
                    }
                ],
            }
        });
    }

    private async createSIMReissueTransactionRequest(
        payload: SIMReissueTransactionPayload,
    ): Promise<CoreSwimmyReissueRequest> {
        await usePsql();
        const cardTypeIdT = await TBanHelper.getDeviceTypeTBan(
            payload.cardTypeId,
        );
        if (isNone(cardTypeIdT)) {
            throw new Error(
                `createSIMReissueTransactionRequest: cardTypeIdT not found for ${payload.cardTypeId}`,
            );
        }

        const result: CoreSwimmyReissueRequest = {
            commonHeaderInfo: this.generateCommonHeaderInfo(),
            appRequestInfo: {
                processType: "03",
                appInfoList: [
                    {
                        appBasic: {
                            receiptId: null,
                            inputFromType: "01",
                            receiptIdSerialno: null,
                            orderType: "11",
                            appStatus: "15",
                            appDate: payload.receivedDate,
                            requestDate: payload.receivedDate,
                            salesChannelCode: null, // Swimmyにて補填
                            salesChannelName: null, // Swimmyにて補填
                            inputFromReceiptId: payload.frontSoId, // 卸フロントのSO-ID（PF～）
                            appBasicDtlList: [
                                {
                                    appAttributeCode: "********", // 「********:レコード種別」固定
                                    appAttributeValue: "004", // 「004:端末、認証情報設定変更」固定
                                },
                                {
                                    appAttributeCode: "M0000012", // 「M0000012:発信番号」固定
                                    appAttributeValue: payload.lineNo,
                                },
                                {
                                    appAttributeCode: "M0000013", // 「M0000013:契約番号（N番号）」固定
                                    appAttributeValue: payload.nNo, // 紐づくN番を設定
                                },
                            ],
                        },
                        appPrdtList: [],
                    },
                ],
            },
        };

        if (
            payload.simType === SimTypes.ESIM &&
            payload.isESIMProfileRedownload
        ) {
            // eSIMプロファイル再ダウンロードオーダ→eSIMプロファイル再ＤＬフラグを設定、0002:提供商品以下は設定なし
            result.appRequestInfo.appInfoList[0].appBasic.appBasicDtlList.push({
                appAttributeCode: "********", // 「********:eSIMプロファイル再DLフラグ」固定
                appAttributeValue: "1", // eSIMプロファイル再DLの場合、「1」を設定
            });
        } else {
            // SIM再発行 or eSIM再発行→0002:提供商品以下を設定
            result.appRequestInfo.appInfoList[0].appPrdtList.push({
                accountId: null,
                accountPrdtId: null,
                appPrdtStatus: "01",
                appPrdtCancelCause: null,
                prdtCode: cardTypeIdT, // 変更後のカード種別（提供商品）を設定（T番）
                processType: "01",
                prdtTypeCode: "0002", // 「0002:提供商品」固定
                price: null,
                billingStartDate: null,
                billingEndDate: null,
                prdtSerialno: 1,
                parentPrdtSerialno: null,
                appPrdtDtlList: [
                    {
                        prdtAttributeCode: "********", // [********:SIM番号／EID」固定
                        prdtAttributeValue: payload.simNo, // 変更後のSIM番号／EIDを設定
                    },
                ],
            });
        }

        return removeEmptyProperties(result);
    }
    private async createOptionChangeTransactionRequest(
        payload: OptionChangeTransactionPayload,
    ): Promise<CoreSwimmyOptionRequest> {
        // for populating appPrdtList
        const prodTypes: { prdtCode: string; processType: "01" | "03" }[] = [];

        const addToArrayIfNeeded = (option?: OptionUpdate) => {
            if (option) {
                // 1:追加・2:変更・3:削除
                // 2:変更 will have two records: 03:削除 and 01:追加
                if (option.changeCode === "2" || option.changeCode === "3") {
                    // 2:変更・3:削除 → 03:削除
                    prodTypes.push({
                        prdtCode: option.beforeOpId,
                        processType: "03",
                    });
                }
                if (option.changeCode === "1" || option.changeCode === "2") {
                    // 1:追加・2:変更 → 01:追加
                    prodTypes.push({
                        prdtCode: option.afterOpId,
                        processType: "01",
                    });
                }
            }
        };

        addToArrayIfNeeded(payload.intlRoaming);
        addToArrayIfNeeded(payload.intlCall);
        addToArrayIfNeeded(payload.callWaiting);
        addToArrayIfNeeded(payload.voicemail);
        addToArrayIfNeeded(payload.forwarding);
        addToArrayIfNeeded(payload.intlForwarding);

        // prdtCode: ★Aoxxx等からT番に変換する
        const prdtCodes = prodTypes.map((row) => row.prdtCode);
        // prdtCodes should not have empty ones
        if (prdtCodes.some((code) => !code)) {
            this.context.info(prodTypes);
            throw new Error(
                `createOptionChangeTransactionRequest: prdtCodes contains empty value ${prdtCodes}`,
            );
        }
        const prdtCodesT = await Promise.all(
            prdtCodes.map((code) => TBanHelper.getLineOptionTBan(code)),
        );
        for (let i = 0; i < prdtCodesT.length; i++) {
            prodTypes[i].prdtCode = prdtCodesT[i];
            // throw if empty ??
            if (isNone(prodTypes[i].prdtCode)) {
                throw new Error(
                    `createOptionChangeTransactionRequest: (${i}) prdtCodeT not found for ${prdtCodes[i]}`,
                );
            }
        }

        // process only if pinChange is 1 (変更あり)
        if (payload.pinChange === "1") {
            let nwOptionIdT: string = null;
            // get customer nw change option id
            const customerInfo = await this.apiLinesDao.getLineCustomerInfo(
                payload.lineNo,
            );
            if (isNone(customerInfo)) {
                // try to find NWOption directly using N番
                nwOptionIdT = await TBanHelper.getNWOptionTBan(payload.nNo);
            } else {
                nwOptionIdT = customerInfo.nwOptionIdT;
            }
            if (isNone(nwOptionIdT)) {
                this.logger.error(
                    payload.opfTenantId,
                    this.sequenceNo,
                    MsgKeysConstants.APONME0001,
                    payload.lineNo,
                    payload.pinChange,
                );
                // if customer info is not found, this code should never be reached (see OptionNwPassModifyService.checkCsvOutput)
                // throw new Error(
                //     `createOptionChangeTransactionRequest: customerInfo not found for ${payload.lineNo} ${payload.nNo}`,
                // );
                this.context.error(
                    "createOptionChangeTransactionRequest: nwOptionIdT not found for ", payload?.nNo, payload?.lineNo
                )
            } else {
                prodTypes.push({
                    prdtCode: nwOptionIdT,
                    processType: "01",
                });
            }
        }

        return {
            commonHeaderInfo: this.generateCommonHeaderInfo(),
            appRequestInfo: {
                processType: "03",
                appInfoList: [
                    {
                        appBasic: {
                            receiptId: null,
                            inputFromType: "01",
                            receiptIdSerialno: null,
                            orderType: "11",
                            appStatus: "15",
                            appDate: payload.receivedDate,
                            requestDate: payload.receivedDate,
                            salesChannelCode: null, // NOTE Swimmyにて補填
                            salesChannelName: null, // NOTE Swimmyにて補填
                            inputFromReceiptId: payload.frontSoId, // 卸フロントのSO-ID（PF～）
                            appBasicDtlList: [
                                {
                                    appAttributeCode: "********", // 「********:レコード種別」固定
                                    appAttributeValue: "006", // 「006:オプション変更」固定
                                },
                                {
                                    appAttributeCode: "M0000012", // 「M0000012:発信番号」固定
                                    appAttributeValue: payload.lineNo, // 対象の発信番号を設定
                                },
                                {
                                    appAttributeCode: "M0000013", // 「M0000013:契約番号（N番号）」固定
                                    appAttributeValue: payload.nNo, // 紐づくN番を設定
                                },
                            ],
                        },
                        appPrdtList: prodTypes.map((row, i) => ({
                            accountId: null,
                            accountPrdtId: null,
                            appPrdtStatus: "01",
                            appPrdtCancelCause: null,
                            prdtCode: row.prdtCode, // 商品コード（T番）
                            processType: row.processType,
                            prdtTypeCode: "0006", // 「0006:アカウントオプション」固定
                            price: null,
                            billingStartDate: null,
                            billingEndDate: null,
                            prdtSerialno: i + 1, // 変更する数ごとに指定する必要あり
                            parentPrdtSerialno: null,
                        })),
                    },
                ],
            },
        };
    }

    private createLineDelTransactionRequest(
        payload: LineDeleteTransactionPayload,
    ): CoreSwimmyDelRequest {
        const reserveDate = Constants.ORDER_TYPE_1.equals(payload.orderType)
            ? payload.reserveDate
            : payload.receivedDate;
        return {
            commonHeaderInfo: this.generateCommonHeaderInfo(),
            appRequestInfo: {
                processType: "03",
                appInfoList: [
                    {
                        appBasic: {
                            receiptId: null,
                            inputFromType: "01",
                            receiptIdSerialno: null,
                            orderType: "03", // 「03:商品廃止」固定
                            appStatus: "15",
                            appDate: payload.receivedDate,
                            requestDate: reserveDate,
                            salesChannelCode: null,
                            salesChannelName: null,
                            inputFromReceiptId: payload.frontSoId, // 卸フロントのSO-ID（PF～）
                            appBasicDtlList: [
                                {
                                    appAttributeCode: "********",
                                    appAttributeValue: "008", // 「008:回線廃止」固定
                                },
                                {
                                    appAttributeCode: "M0000012", // 「M0000012:発信番号」固定
                                    appAttributeValue: payload.lineNo, // 対象の発信番号を設定
                                },
                                {
                                    appAttributeCode: "M0000013", // 「M0000013:契約番号（N番号）」固定
                                    appAttributeValue: payload.nNo, // 紐づくN番を設定
                                },
                            ],
                        },
                    },
                ],
            },
        };
    }

    private createSoCancelTransactionRequest(payload:SoCancelPayload):CoreSwimmySoCancelRequest{
        if (isNone(payload.receiptId)) {
            throw new Error(
                "createSoCancelTransactionRequest: receiptId is required",
            );
        }
        return removeEmptyProperties({
            commonHeaderInfo: this.generateCommonHeaderInfo(),
            appRequestInfo: {
                processType: "03",
                appInfoList: [
                    {
                        appBasic: {
                            receiptId: payload.receiptId,
                            inputFromType: "01", // 「01:MVNO顧客」固定
                            receiptIdSerialno: null,
                            orderType: "03", // 「03:商品廃止」固定
                            appStatus: "11", // 「11:オーダ取消」固定
                            appDate: null,
                            requestDate: null,
                            salesChannelCode: null,
                            salesChannelName: null,
                        },
                    },
                ],
            },
        } as CoreSwimmySoCancelRequest);
    }

    private createLinePlanAcquisitionTransactionRequest(
        payload: LinePlanAcquisitionPayload,
    ): CoreSwimmyPlanChangeRequest {
        const result =  removeEmptyProperties({
            commonHeaderInfo: this.generateCommonHeaderInfo(),
            appRequestInfo: {
                processType: "03", // 「03:ワーニング突破」固定
                appInfoList: [
                    {
                        appBasic: {
                            receiptId: null,
                            inputFromType: "01", // 「01:MVNO顧客」固定
                            receiptIdSerialno: null,
                            orderType: "11", // 「11：商品変更」固定
                            appStatus: "15", // 「15:オーダ確定」固定
                            appDate: payload.receivedDate,
                            requestDate: payload.reqDate,
                            salesChannelCode: null,
                            salesChannelName: null,
                            // registName: null,
                            inputFromReceiptId: payload.frontSoId, // ポータル受付番号を設定(卸フロントのSO-ID（PF～）)

                            appBasicDtlList: [
                                {
                                    appAttributeCode: "********", // レコード種別
                                    appAttributeValue: "002", // 「002：総量規制設定変更」固定
                                },
                                {
                                    appAttributeCode: "M0000012", // 「M0000012:発信番号」固定
                                    appAttributeValue: payload.lineNo, // 対象の回線番号を設定
                                },
                                {
                                    appAttributeCode: "M0000013", // 契約番号（N番号）
                                    appAttributeValue: payload.nNo, // 紐づくN番を設定
                                },
                            ],
                        },
                        appPrdtList: [
                            {
                                accountId: null,
                                accountPrdtId: null,
                                appPrdtStatus: "01", // 「01：オーダ中」固定
                                appPrdtCancelCause: null,
                                prdtCode: payload.resalePlanIdT, // plansテーブルのT番
                                processType: "01", // 「01：追加」固定
                                prdtTypeCode: "0003", // 「0003:再販料金プラン」
                                price: null,
                                billingStartDate: null,
                                billingEndDate: null,
                                prdtSerialno: 1, // １回線申込内でユニークな通番を設定
                                parentPrdtSerialno: null,
                            },
                            {
                                accountId: null,
                                accountPrdtId: null,
                                appPrdtStatus: "01", // 「01：オーダ中」固定
                                appPrdtCancelCause: null,
                                prdtCode: "T9000002", //  商品コード「T9000002:総量規制ファイル」固定値
                                processType: "01", // 「01：追加」固定
                                prdtTypeCode: "0009", // 「0009：工事手配」
                                price: null,
                                billingStartDate: null,
                                billingEndDate: null,
                                prdtSerialno: 2, // １回線申込内でユニークな通番を設定
                                parentPrdtSerialno: null,
                                appPrdtDtlList: [
                                    {
                                        prdtAttributeCode: "S1000001", // 工事要否
                                        prdtAttributeValue: "0", // 総量規制ファイル出力要否「0（否）」
                                    },
                                ],
                            },
                            {
                                accountId: null,
                                accountPrdtId: null,
                                appPrdtStatus: "01", // 「01：オーダ中」固定
                                appPrdtCancelCause: null,
                                prdtCode: "T9000003", // 商品コード「T9000003:総量規制設定」固定値
                                processType: "01", // 「01：追加」固定
                                prdtTypeCode: "0009", // 「0009：工事手配」
                                price: null,
                                billingStartDate: null,
                                billingEndDate: null,
                                prdtSerialno: 3, // １回線申込内でユニークな通番を設定
                                parentPrdtSerialno: null,
                                appPrdtDtlList: [
                                    {
                                        prdtAttributeCode: "S1000003", // 総量規制契約コース設定
                                        prdtAttributeValue: "0", // 契約コース変更要否「0（否）」固定
                                    },
                                    {
                                        prdtAttributeCode: "********", // 総量規制回線グループ設定
                                        prdtAttributeValue: "0", // 回線グループ所属設定要否「0（否）」固定
                                    },
                                ],
                            },
                        ],
                    },
                ],
            },
        } satisfies CoreSwimmyPlanChangeRequest);
        if (!isNone(payload.op)) { //payload.swimmy or user_bb_flag
            if (payload.op) {
                result.appRequestInfo.appInfoList[0].appPrdtList.push({
                    appPrdtStatus: "01",                    // 「01:オーダ中」固定
                    prdtCode: "TG000001",                   // 申し込むオプションの商品コードを設定
                    processType: "01",                      // 「01：追加」固定
                    prdtTypeCode: "0016",                   // 「0016：BBユニバ情報」固定
                    prdtSerialno: "serialNo++",               // １回線申込内でユニークな通番を設定
                    appPrdtDtlList: [
                        {
                            prdtAttributeCode: "********",  // 適用年月日
                            prdtAttributeValue: payload.receivedDate,    // BBユニバ相殺の適用開始日を設定 ※基本的には開廃希望年月日と同じ日が設定される想定
                        },
                    ],
                });
            } else {
                result.appRequestInfo.appInfoList[0].appPrdtList.push({
                    appPrdtStatus: "01",                    // 「01:オーダ中」固定
                    prdtCode: "TG000001",                   // 申し込むオプションの商品コードを設定
                    processType: "01",                      // 「03：削除」固定
                    prdtTypeCode: "0016",                   // 「0016：BBユニバ情報」固定
                    prdtSerialno: "serialNo++",               // １回線申込内でユニークな通番を設定
                    appPrdtDtlList: [
                        {
                            prdtAttributeCode: "00000043",  // 削除年月日
                            prdtAttributeValue: payload.receivedDate,    // BBユニバ相殺の適用終了日を設定 ※適用終了日-1日が相殺期間となる。※追加時は読み飛ばし
                        },
                    ],
                });
            }
        }
        return result;
    }

    private async createNWContractTransactionRequest(
        payload: NWContractTransactionPayload,
    ): Promise<CoreSwimmyNWContractRequest> {
        // SIM種別変換
        const accessMap = {
            // 3G
            SA: "1",
            SB: "1",
            // LTE
            SC: "2",
            SD: "2",
            SE: "2",
            // 5G(NSA)
            SH: "3",
            SJ: "3",
        };
        // get T番 of cardTypeId
        await usePsql();
        const cardTypeIdT = await TBanHelper.getDeviceTypeTBan(
            payload.cardTypeId,
        );
        if (isNone(cardTypeIdT)) {
            throw new Error(
                `createNWContractTransactionRequest: cardTypeIdT not found for ${payload.cardTypeId}`,
            );
        }
        return removeEmptyProperties({
            commonHeaderInfo: this.generateCommonHeaderInfo(),
            appRequestInfo: {
                processType: "03",
                appInfoList: [
                    {
                        appBasic: {
                            receiptId: null,
                            inputFromType: "01",
                            receiptIdSerialno: null,
                            orderType: "11", // 「11:商品変更」固定
                            appStatus: "15",
                            appDate: payload.receivedDate,
                            requestDate: payload.receivedDate,
                            salesChannelCode: null,
                            salesChannelName: null,
                            inputFromReceiptId: payload.frontSoId, // 卸フロントのSO-ID（PF～）
                            appBasicDtlList: [
                                {
                                    appAttributeCode: "********", // 「********:レコード種別」固定
                                    appAttributeValue: "009", // 「009:アクセス方式変更」固定
                                },
                                {
                                    appAttributeCode: "M0000012", // 「M0000012:発信番号」固定
                                    appAttributeValue: payload.lineNo, // 対象の発信番号を設定
                                },
                                {
                                    appAttributeCode: "M0000013", // 「M0000013:契約番号（N番号）」固定
                                    appAttributeValue: payload.nNo, // 紐づくN番を設定
                                },
                            ],
                        },
                        appPrdtList: [
                            {
                                // 「0002:提供商品」の申込情報域
                                accountId: null,
                                accountPrdtId: null,
                                appPrdtStatus: "01",
                                appPrdtCancelCause: null,
                                prdtCode: cardTypeIdT, // 変更後のカード（提供商品）を設定（T番）
                                processType: "01", // 「01:追加」固定
                                prdtTypeCode: "0002", // 「0002:提供商品」固定
                                price: null,
                                billingStartDate: null,
                                billingEndDate: null,
                                prdtSerialno: 1,
                                parentPrdtSerialno: null,
                            },
                            {
                                // 「0003:再販料金プラン」の申込情報域
                                accountId: null,
                                accountPrdtId: null,
                                appPrdtStatus: "01",
                                appPrdtCancelCause: null,
                                prdtCode: null,
                                processType: "02", // 「02:変更」固定
                                prdtTypeCode: "0003", // 「0003:再販料金プラン」固定
                                price: null,
                                billingStartDate: null,
                                billingEndDate: null,
                                prdtSerialno: 2, // 「2」固定
                                parentPrdtSerialno: null,
                                appPrdtDtlList: [
                                    {
                                        prdtAttributeCode: "********", // 「********:アクセス方式」固定
                                        prdtAttributeValue:
                                            accessMap[payload.access] || "", // 変更後のアクセス方式を設定
                                    },
                                ],
                            },
                        ],
                    },
                ],
            },
        } as CoreSwimmyNWContractRequest);
    }

    private createFrontSoCancelTransactionRequest(
        payload: FrontSoCancelPayload,
    ): CoreSwimmyFrontSoCancelRequest {
        if (isNone(payload.receiptId)) {
            throw new Error(
                "createFrontSoCancelTransactionRequest: receiptId is required",
            );
        }
        return removeEmptyProperties({
            commonHeaderInfo: this.generateCommonHeaderInfo(),
            appRequestInfo: {
                processType: "03",
                appInfoList: [
                    {
                        appBasic: {
                            receiptId: payload.receiptId,
                            inputFromType: "01", // 「01:MVNO顧客」固定
                            receiptIdSerialno: null,
                            orderType: "03", // 「03:商品廃止」固定
                            appStatus: "11", // 「11:オーダ取消」固定
                            appDate: null,
                            requestDate: null,
                            salesChannelCode: null,
                            salesChannelName: null,
                        },
                    },
                ],
            },
        } as CoreSwimmyFrontSoCancelRequest);
    }

    private createLineGroupModifyAcquisitionTransactionRequest(
        payload: LineGroupModifyAcquisitionPayload,
    ): CoreSwimmyLineGroupModifyAcquisitionRequest {
        return removeEmptyProperties({
            commonHeaderInfo: this.generateCommonHeaderInfo(),
            appRequestInfo: {
                processType: "03", // 「03:ワーニング突破」固定
                appInfoList: [
                    {
                        appBasic: {
                            receiptId: null,
                            inputFromType: "01",   // 「01:MVNO顧客」固定
                            receiptIdSerialno: null,
                            orderType: "11",       // 「11：商品変更」固定
                            appStatus: "15",       // 「15:オーダ確定」固定
                            appDate: payload.receivedDate,
                            requestDate: payload.receivedDate,
                            salesChannelCode: null,
                            salesChannelName: null,
                            registName: null,
                            inputFromReceiptId: payload.frontSoId, // ポータル受付番号を設定(卸フロントのSO-ID（PF～）)

                            appBasicDtlList: [
                                {
                                    appAttributeCode: "********",  // レコード種別
                                    appAttributeValue: "002",     // 「002：総量規制設定変更」固定
                                },
                                {
                                    appAttributeCode: "M0000012",      // 「M0000012:発信番号」固定
                                    appAttributeValue: payload.lineNo, // 対象の回線番号を設定
                                },
                                ...(payload.nNo ?
                                    [
                                        {
                                            appAttributeCode: "M0000013",    // 契約番号（N番号）
                                            appAttributeValue: payload.nNo,  // 紐づくN番を設定
                                        },
                                    ] :
                                        []
                                    )
                            ]

                        },
                        appPrdtList: [
                            {
                                accountId:null,
                                accountPrdtId:null,
                                appPrdtStatus:"01",       // 「01：オーダ中」固定
                                appPrdtCancelCause:null,
                                prdtCode: null,
                                processType: "02",        // 「02：変更」固定
                                prdtTypeCode: "0003",     // 「0003:再販料金プラン」
                                price:null,
                                billingStartDate:null,
                                billingEndDate:null,
                                prdtSerialno:"3",         // １回線申込内でユニークな通番を設定
                                parentPrdtSerialno:null,
                                appPrdtDtlList:[
                                    {
                                        prdtAttributeCode: "********",           // グループ種別
                                        prdtAttributeValue: payload.operation, // null if グループ削除
                                    },
                                    {
                                        prdtAttributeCode: "********",           // グループプロファイル番号
                                        prdtAttributeValue: payload.operation ? payload.lineGroupId : null, // null if グループ削除
                                    }
                                ],
                            },
                            {
                                accountId: null,
                                accountPrdtId: null,
                                appPrdtStatus: "01",        // 「01：オーダ中」固定
                                appPrdtCancelCause: null,
                                prdtCode: "T9000002",       //  商品コード「T9000002:総量規制ファイル」固定値
                                processType: "01",          // 「01：追加」固定
                                prdtTypeCode: "0009",       // 「0009：工事手配」
                                price: null,
                                billingStartDate: null,
                                billingEndDate: null,
                                prdtSerialno: "9",          // １回線申込内でユニークな通番を設定
                                parentPrdtSerialno: null,
                                appPrdtDtlList: [
                                    {
                                        prdtAttributeCode: "S1000001", // 工事要否
                                        prdtAttributeValue: "0",       // 総量規制ファイル出力要否「0（否）」
                                    },
                                ],
                            },
                            {
                                accountId: null,
                                accountPrdtId: null,
                                appPrdtStatus: "01",              // 「01：オーダ中」固定
                                appPrdtCancelCause: null,
                                prdtCode: "T9000003",             // 商品コード「T9000003:総量規制設定」固定値
                                processType: "01",                // 「01：追加」固定
                                prdtTypeCode: "0009",             // 「0009：工事手配」
                                price: null,
                                billingStartDate: null,
                                billingEndDate: null,
                                prdtSerialno: "9",          // １回線申込内でユニークな通番を設定
                                parentPrdtSerialno: null,
                                appPrdtDtlList: [
                                    {
                                        prdtAttributeCode : "S1000003", // 総量規制契約コース設定
                                        prdtAttributeValue: 0,          // 契約コース変更要否「0（否）」固定
                                    },
                                    {
                                        prdtAttributeCode : "********", // 総量規制回線グループ設定
                                        prdtAttributeValue: 0,          // 回線グループ所属設定要否「0（否）」固定
                                    }
                                ]
                            }
                        ]
                    },
                ],
            },
        });
    }


    private async createPreLineAddLiteTransactionRequest(
        payload: PreLineAddPayload,
    ): Promise<CoreSwimmyPreLineAddRequest> {
        /** 通番 */
        let serialNo = 4;
        /** yyyyMMdd */
        const sysDate = payload.receivedDate;

        /** yyyyMMdd */
        let startDate: string = null;
        if (Constants.ORDER_TYPE_0 === payload.orderType) {
            startDate = sysDate;
        } else {
            // yyyyMMdd形式に変換
            startDate = payload.reserveDate.replace(/\//g, "").substring(0, 8);
        }

        // 同一MVNE間MNP転入の場合、同一MVNEフラグを設定
        const appBasicDtl: AppBasicDtl[] = [];
        if (payload.isSameMvne) {
            appBasicDtl.push({
                appAttributeCode: "********", // 同一MVNEフラグ
                appAttributeValue: "1", // 同一MVNEの場合、「1」を設定
            });
        }
        /**
         * 端末所有区分
         *
         * - 「1」お買上げ（新規購入）
         * - 「3」お買上げ（再利用）
         * - 「2」レンタル
         * - 「9」お客様自営
         *
         * SIMのみの場合
         * "お客様自営"固定
         * SIMのみ以外の場合
         * "お買上げ（新規購入）"固定
         */
        const deviceOwnership = payload.simFlag ? "9" : "1";
        await usePsql();
        const cardTypeIdT = payload.cardTypeIdT;
        if (isNone(cardTypeIdT)) {
            throw new Error(
                `createPreLineAddLiteTransactionRequest: cardTypeIdT is empty`,
            );
        }

        /**
         * アクセス方式設定
         * 統合プラン（アクセス方式変更可否が「1:可」）の場合は必須
         * - 1: 3G
         * - 2: LTE
         * - 3: 5G-NSA
         */
        let accessType = "";
        if (["SA", "SB"].includes(payload.accessType)) {
            accessType = "1";
        } else if (["SC", "SD", "SE"].includes(payload.accessType)) {
            accessType = "2";
        } else if (["SH", "SJ"].includes(payload.accessType)) {
            accessType = "3";
        }
        const accessTypePrdtDtl = accessType
            ? [
                  {
                      prdtAttributeCode: "********", // アクセス方式
                      prdtAttributeValue: accessType,
                  },
              ]
            : [];

        /**
         * 「0006：アカウントオプション」list
         */
        const optAppPrdtList: AppPrdt[] = [];

        // 「0006：アカウントオプション」の申込情報域
        const lineOptions = [
            payload.intlRoamingT,
            payload.voicemailT,
            payload.callWaitingT,
            payload.intlCallT,
            payload.forwardingT,
            payload.intlForwardingT,
        ].filter((o) => o);
        if (
            payload.mnpInType === PreLineAddService.MNP_IN_TYPE_YES ||
            payload.mnpInType === PreLineAddService.MNP_IN_TYPE_SAME_MVNE_YES
        ) {
            lineOptions.push(payload.mnpAddOptionIdT);
        }
        if (lineOptions.length > 0) {
            for (const optionId of lineOptions) {
                optAppPrdtList.push({
                    appPrdtStatus: "01", // 「01:オーダ中」固定
                    prdtCode: optionId, // 申し込むオプションの商品コードを設定
                    processType: "01", // 「01:追加」固定
                    prdtTypeCode: "0006", // 「0006:アカウントオプション」固定
                    prdtSerialno: serialNo, // １回線申込内でユニークな通番を設定
                    appPrdtDtlList: [],
                });
                serialNo++;
            }
        }

        return {
            commonHeaderInfo: this.generateCommonHeaderInfo(),
            appRequestInfo: {
                processType: "03", // 「03:ワーニング突破」固定
                appInfoList: [
                    {
                        appBasic: {
                            inputFromType: "01", // 「01:MVNO顧客」固定
                            orderType: "01", // 「01:商品新規」固定
                            appStatus: "15", // 「15:オーダ確定」固定
                            appDate: sysDate,
                            requestDate: startDate,
                            salesChannelCode:
                                payload?.custInfo?.agencyCode ?? null, // 代理店コード
                            salesChannelName: isNone(
                                payload?.custInfo?.agencyCode,
                            )
                                ? null
                                : "*", // 代理店名
                            // registName: "", // Swimmyにて補填
                            inputFromReceiptId: payload.frontSoId, // 卸フロントのSO-ID（PF～）
                            appBasicDtlList: [
                                {
                                    appAttributeCode: "********", // 「********:レコード種別」固定
                                    appAttributeValue: "007", // 「007:回線追加」固定
                                },
                                {
                                    appAttributeCode: "M0000013", // 「M0000013:契約番号（N番号）」固定
                                    appAttributeValue: payload.nNo, // 紐づくN番を設定
                                },
                                ...appBasicDtl,
                            ],
                        },
                        appPrdtList: [
                            {
                                // 「0001:提供サービス」の申込情報域
                                appPrdtStatus: "01", // 「01:オーダ中」固定
                                prdtCode: "T1000001", // ＮＴＴＣｏｍモバイル（ｄ）／（ＧＭ２Ｍ）
                                processType: "01", // 「01:追加」固定ＮＴＴＣｏｍモバイル（ｄ）／（ＧＭ２Ｍ）
                                prdtTypeCode: "0001", // 「0001:提供サービス」固定
                                prdtSerialno: 1, // ユニーク通番
                                appPrdtDtlList: [
                                    {
                                        prdtAttributeCode: "00000001", // 提供形態
                                        prdtAttributeValue: "4", // 「4」他事業者向け固定
                                    },
                                    {
                                        prdtAttributeCode: "00000002", // 端末所有区分
                                        prdtAttributeValue: deviceOwnership,
                                    },
                                    {
                                        prdtAttributeCode: "00000003", // 事業者間譲渡有無
                                        prdtAttributeValue: "0", // "無:0"固定
                                    },
                                    {
                                        prdtAttributeCode: "00000092", // 譲渡有無
                                        prdtAttributeValue: "0", // "無:0"固定
                                    },
                                ],
                            },
                            {
                                // 「0002:提供商品」の申込情報域
                                appPrdtStatus: "01", // 「01:オーダ中」固定
                                prdtCode: cardTypeIdT, // 変更後のカード種別（提供商品）を設定
                                processType: "01", // 「01:追加」固定
                                prdtTypeCode: "0002", // 「0002:提供商品」固定
                                prdtSerialno: 2, // 1回線申込内でユニークな通番を設定
                                appPrdtDtlList: [
                                    {
                                        prdtAttributeCode: "********", // SIM番号／EID
                                        prdtAttributeValue: payload.simNo,
                                    },
                                    {
                                        prdtAttributeCode: "00000008", // SIM種別
                                        prdtAttributeValue:
                                            payload.hankuro === true
                                                ? "1"
                                                : "0",
                                    },
                                    {
                                        prdtAttributeCode: "00000010", // 回線番号自動選定有無
                                        prdtAttributeValue: "0", // 「0:無」固定
                                    },
                                    {
                                        prdtAttributeCode: "00000011", // 発信回線番号
                                        prdtAttributeValue: payload.lineNo,
                                    },
                                    {
                                        prdtAttributeCode: "00000016", // 端末／SIM送付希望日
                                        prdtAttributeValue: startDate,
                                    },
                                    {
                                        prdtAttributeCode: "J1000001", // 端末／SIM送付先住所コード値有無
                                        prdtAttributeValue: "0", // 「0:無し」
                                    },
                                    {
                                        prdtAttributeCode: "J1000301", // 端末／SIM送付先郵便番号
                                        prdtAttributeValue:
                                            payload.delivery.postcode,
                                    },
                                    {
                                        prdtAttributeCode: "J1000801", // 端末／SIM送付先都道府県
                                        prdtAttributeValue:
                                            payload.delivery.prefecture,
                                    },
                                    {
                                        prdtAttributeCode: "J1000802", // 端末／SIM送付先市区町村
                                        prdtAttributeValue:
                                            payload.delivery.city,
                                    },
                                    {
                                        prdtAttributeCode: "00000018", // 端末／SIM送付先氏名
                                        prdtAttributeValue:
                                            payload.delivery.name,
                                    },
                                ],
                            },
                            {
                                // 「0003:再販料金プラン」の申込情報域
                                appPrdtStatus: "01", // 「01:オーダ中」固定
                                prdtCode: payload.pricePlanIdT, // 申し込む料金プランを設定
                                processType: "01", // 「01:追加」固定
                                prdtTypeCode: "0003", // 「0003:再販料金プラン」固定
                                prdtSerialno: 3,
                                appPrdtDtlList: [
                                    {
                                        prdtAttributeCode: "00000022", // POI情報
                                        prdtAttributeValue: "1", // 「1:不要」固定
                                    },
                                    ...accessTypePrdtDtl, // アクセス方式設定
                                ],
                            },
                            ...optAppPrdtList,
                            {
                                // 「0007：契約情報」の申込情報域 ??
                                appPrdtStatus: "01", // 「01:オーダ中」固定
                                prdtCode: "T7000001", // 申込むオプションの商品コードを設定
                                processType: "01", // 「01:追加」固定
                                prdtTypeCode: "0007", // 「0007:契約情報」固定
                                prdtSerialno: serialNo++, // １回線申込内でユニークな通番を設定
                                appPrdtDtlList: [
                                    {
                                        prdtAttributeCode: "00000056", // 代理店コード
                                        prdtAttributeValue:
                                            payload.custInfo?.agencyCode,
                                    },
                                    {
                                        prdtAttributeCode: "00000057", // 代理店名
                                        prdtAttributeValue: "*", // 「*」固定
                                    },
                                    {
                                        prdtAttributeCode: "00000059", // 販売チャンネル
                                        prdtAttributeValue: "NTTコム", // 「NTTコム」固定
                                    },
                                    {
                                        prdtAttributeCode: "00000060", // お客様対応部門（担当）
                                        prdtAttributeValue:
                                            payload.custInfo?.customerService,
                                    },
                                    {
                                        prdtAttributeCode: "00000061", // お客様対応部門電話番号
                                        prdtAttributeValue:
                                            StringHelper.normalizePhoneNum(
                                                payload.custInfo
                                                    ?.customerServicePhoneNumber,
                                            ),
                                    },
                                    {
                                        prdtAttributeCode: "00000063", // お客様対応部門E-MAIL
                                        prdtAttributeValue:
                                            payload.custInfo
                                                ?.customerServiceEmail,
                                    },
                                    {
                                        prdtAttributeCode: "00000082", // 契約者名（ﾌﾘｶﾞﾅ）
                                        prdtAttributeValue:
                                            payload.custInfo?.contractNameKana,
                                    },
                                    {
                                        prdtAttributeCode: "00000081", // 契約者名
                                        prdtAttributeValue:
                                            payload.custInfo?.contractName,
                                    },
                                    {
                                        prdtAttributeCode: "J4000001", // 契約者住所コード値有無
                                        prdtAttributeValue: "0", // 「0:無し」固定
                                    },
                                    {
                                        prdtAttributeCode: "J4000301", // 契約者郵便番号
                                        prdtAttributeValue:
                                            payload.delivery.postcode,
                                    },
                                    {
                                        prdtAttributeCode: "J4000801", // 契約者都道府県
                                        prdtAttributeValue:
                                            payload.delivery.prefecture,
                                    },
                                    {
                                        prdtAttributeCode: "J4000802", // 契約者市区町村
                                        prdtAttributeValue:
                                            payload.delivery.city,
                                    },
                                    {
                                        prdtAttributeCode: "J4000804", // 契約者字丁目
                                        prdtAttributeValue:
                                            payload.delivery.aza,
                                    },
                                    {
                                        prdtAttributeCode: "J4000501", // 契約者番地
                                        prdtAttributeValue:
                                            payload.delivery.block,
                                    },
                                    {
                                        prdtAttributeCode: "00000084", // 契約者担当者氏名
                                        prdtAttributeValue:
                                            payload.delivery.contact,
                                    },
                                    {
                                        prdtAttributeCode: "00000085", // 事務担当者電話番号
                                        prdtAttributeValue:
                                            StringHelper.normalizePhoneNum(
                                                payload.delivery.tel,
                                            ),
                                    },
                                ],
                            },
                            {
                                // 「0009：工事手配」の申込情報域
                                appPrdtStatus: "01", // 「01:オーダ中」固定
                                prdtCode: "T9000002", // 申込むオプションの商品コードを設定
                                processType: "01", // 「01:追加」固定
                                prdtTypeCode: "0009", // 「0009:工事手配」
                                prdtSerialno: serialNo++, // １回線申込内でユニークな通番を設定
                                appPrdtDtlList: [
                                    {
                                        prdtAttributeCode: "S1000001", // 工事要否
                                        prdtAttributeValue: "0", // 総量規制ファイル出力要否「0（否）」固定
                                    },
                                ],
                            },
                            {
                                // 「0010：物品手配」の申込情報域
                                appPrdtStatus: "01", // 「01:オーダ中」固定
                                prdtCode: "TA000001", // 申込むオプションの商品コードを設定
                                processType: "01", // 「01:追加」固定
                                prdtTypeCode: "0010", // 「0010:物品手配」
                                prdtSerialno: serialNo++, // １回線申込内でユニークな通番を設定
                                appPrdtDtlList: [
                                    {
                                        prdtAttributeCode: "S1000001", // 工事要否
                                        prdtAttributeValue: "0", // 『0:否』固定
                                    },
                                ],
                            },
                            // NOTE 0011：記事 seems to be optional
                        ],
                    },
                ],
            },
        };
    }

    private async createPreLineAddFullTransactionRequest(
        payload: PreLineAddPayload,
    ): Promise<CoreSwimmyPreLineAddRequest> {
        /** 通番 */
        let serialNo = 5;
        /** yyyyMMdd */
        const sysDate = payload.receivedDate;

        /** yyyyMMdd */
        let startDate: string = null;
        if (Constants.ORDER_TYPE_0 === payload.orderType) {
            startDate = sysDate;
        } else {
            // yyyyMMdd形式に変換
            startDate = payload.reserveDate.replace(/\//g, "").substring(0, 8);
        }

        /**
         * 端末所有区分
         *
         * - 「1」お買上げ（新規購入）
         * - 「3」お買上げ（再利用）
         * - 「2」レンタル
         * - 「9」お客様自営
         *
         * SIMのみの場合
         * "お客様自営"固定
         * SIMのみ以外の場合
         * "お買上げ（新規購入）"固定
         */
        const deviceOwnership = payload.simFlag ? "9" : "1";
        await usePsql();
        const cardTypeIdT = payload.cardTypeIdT;
        if (isNone(cardTypeIdT)) {
            throw new Error(
                `createPreLineAddFullTransactionRequest: cardTypeIdT is empty`,
            );
        }

        /**
         * 「0006：アカウントオプション」list
         */
        const optAppPrdtList: AppPrdt[] = [];

        // 「0006：アカウントオプション」の申込情報域
        const lineOptions = [
            payload.intlRoamingT,
            payload.voicemailT,
            payload.callWaitingT,
            payload.intlCallT,
            payload.forwardingT,
            payload.intlForwardingT,
        ].filter((o) => o);
        if (
            payload.mnpInType === PreLineAddService.MNP_IN_TYPE_YES ||
            payload.mnpInType === PreLineAddService.MNP_IN_TYPE_SAME_MVNE_YES
        ) {
            lineOptions.push(payload.mnpAddOptionIdT);
        }
        if (lineOptions.length > 0) {
            for (const optionId of lineOptions) {
                optAppPrdtList.push({
                    appPrdtStatus: "01", // 「01:オーダ中」固定
                    prdtCode: optionId, // 申し込むオプションの商品コードを設定
                    processType: "01", // 「01:追加」固定
                    prdtTypeCode: "0006", // 「0006:アカウントオプション」固定
                    prdtSerialno: serialNo, // １回線申込内でユニークな通番を設定
                    appPrdtDtlList: [],
                });
                serialNo++;
            }
        }

        return {
            commonHeaderInfo: this.generateCommonHeaderInfo(),
            appRequestInfo: {
                processType: "03", // 「03:ワーニング突破」固定
                appInfoList: [
                    {
                        appBasic: {
                            inputFromType: "01", // 「01:MVNO顧客」固定
                            orderType: "01", // 「01:商品新規」固定
                            appStatus: "15", // 「15:オーダ確定」固定
                            appDate: sysDate, // REST APIで受付けた際のシステム日付
                            requestDate: sysDate, // REST APIで受付けた際のシステム日付
                            salesChannelCode:
                                payload.custInfo?.agencyCode ?? null, // 代理店コード
                            salesChannelName: isNone(
                                payload.custInfo?.agencyCode,
                            )
                                ? null
                                : "*", // 代理店名
                            inputFromReceiptId: payload.frontSoId, // 卸フロントのSO-ID（PF～）
                            appBasicDtlList: [
                                {
                                    appAttributeCode: "********", // 「********:レコード種別」固定
                                    appAttributeValue: "007", // 「007:回線追加」固定
                                },
                                {
                                    appAttributeCode: "M0000013", // 「M0000013:契約番号（N番号）」固定
                                    appAttributeValue: payload.nNo, // 紐づくN番を設定
                                },
                            ],
                        },
                        appPrdtList: [
                            {
                                // 「0001：提供サービス」の申込情報域
                                appPrdtStatus: "01", // 「01：オーダ中」固定
                                prdtCode: "T1000001", // ＮＴＴＣｏｍモバイル（ｄ）／（ＧＭ２Ｍ）
                                processType: "01", // 「01：追加」固定
                                prdtTypeCode: "0001", // 「0001：提供サービス」固定
                                prdtSerialno: 1, // ユニーク通番
                                appPrdtDtlList: [
                                    {
                                        prdtAttributeCode: "00000001", // 提供形態
                                        prdtAttributeValue: "4", // 「4」他事業者向け固定
                                    },
                                    {
                                        prdtAttributeCode: "00000002", // 端末所有区分
                                        prdtAttributeValue: deviceOwnership,
                                    },
                                    {
                                        prdtAttributeCode: "00000003", // 事業者間譲渡有無
                                        prdtAttributeValue: "0", // "無:0"固定
                                    },
                                    {
                                        prdtAttributeCode: "00000092", // 譲渡有無
                                        prdtAttributeValue: "0", // "無:0"固定
                                    },
                                    {
                                        prdtAttributeCode: "00000108", // フルMVNO識別
                                        prdtAttributeValue: "1", // 「1:フルMVNO」を設定
                                    },
                                ],
                            },
                            {
                                // 「0013：提供eSIM商品」の申込情報域
                                appPrdtStatus: "01", // 「01：オーダ中」固定
                                prdtCode: cardTypeIdT, // カード種別T番
                                processType: "01", // 「01：追加」固定
                                prdtTypeCode: "0013", // 「0013：提供eSIM商品」
                                prdtSerialno: 2, // ユニーク通番
                                appPrdtDtlList: [
                                    {
                                        prdtAttributeCode: "********", // SIM番号
                                        prdtAttributeValue: payload.simNo, // MVNO顧客の番号払い出しを使用しない場合は必要だが、必須チェックはしていない
                                    },
                                    {
                                        prdtAttributeCode: "00000008",
                                        prdtAttributeValue:
                                            payload.hankuro === true
                                                ? "1"
                                                : "0", // SIM種別
                                    },
                                    {
                                        prdtAttributeCode: "00000010", // 回線番号自動選定有無
                                        prdtAttributeValue: "0", // 「0:無」固定
                                    },
                                    {
                                        prdtAttributeCode: "00000016", // 端末／SIM送付希望日
                                        prdtAttributeValue: startDate,
                                    },
                                    {
                                        prdtAttributeCode: "J1000001", // 端末／SIM送付先住所コード値有無
                                        prdtAttributeValue: "0", // 「0:無し」
                                    },
                                    {
                                        prdtAttributeCode: "J1000301", // 端末／SIM送付先郵便番号
                                        prdtAttributeValue:
                                            payload.delivery.postcode,
                                    },
                                    {
                                        prdtAttributeCode: "J1000801", // 端末／SIM送付先都道府県
                                        prdtAttributeValue:
                                            payload.delivery.prefecture,
                                    },
                                    {
                                        prdtAttributeCode: "J1000802", // 端末／SIM送付先市区町村
                                        prdtAttributeValue:
                                            payload.delivery.city,
                                    },
                                    {
                                        prdtAttributeCode: "00000018", // 端末／SIM送付先氏名
                                        prdtAttributeValue:
                                            payload.delivery.name,
                                    },
                                    {
                                        prdtAttributeCode: "00000102", // eSIM識別
                                        prdtAttributeValue: "01", // 01：データ 固定
                                    },
                                ],
                            },
                            {
                                // 「0014：プロファイル商品」の申込情報域
                                appPrdtStatus: "01", // 「01：オーダ中」固定
                                prdtCode: "TE000001", // プロファイル商品
                                processType: "01", // 「01：追加」固定
                                prdtTypeCode: "0014", // 「0014:プロファイル商品」
                                prdtSerialno: 3, // ユニーク通番
                                appPrdtDtlList: [
                                    {
                                        prdtAttributeCode: "00000011", // 発信回線番号
                                        prdtAttributeValue: payload.lineNo,
                                    },
                                ],
                            },
                            {
                                // 「0003:再販料金プラン」の申込情報域
                                appPrdtStatus: "01", // 「01：オーダ中」固定
                                prdtCode: payload.pricePlanIdT, // 申し込む料金プランを設定
                                processType: "01", // 「01：追加」固定
                                prdtTypeCode: "0003", // 「0003：再販料金プラン」固定
                                prdtSerialno: 4, // ユニーク通番
                                appPrdtDtlList: [
                                    {
                                        prdtAttributeCode: "00000022", // POI情報
                                        prdtAttributeValue: "1", // 「1:不要」固定
                                    },
                                ],
                            },
                            ...optAppPrdtList, // 「0006：アカウントオプション」の申込情報域
                            {
                                // 「0007：契約情報」の申込情報域 ??
                                appPrdtStatus: "01", // 「01：オーダ中」固定
                                prdtCode: "T7000001", // 申込むオプションの商品コードを設定
                                processType: "01", // 「01：追加」固定
                                prdtTypeCode: "0007", // 「0007：契約情報」固定
                                prdtSerialno: serialNo++, // ユニーク通番
                                appPrdtDtlList: [
                                    {
                                        prdtAttributeCode: "00000056", // 代理店コード
                                        prdtAttributeValue:
                                            payload.custInfo.agencyCode,
                                    },
                                    {
                                        prdtAttributeCode: "00000057", // 代理店名
                                        prdtAttributeValue: "*", // 「*」固定
                                    },
                                    {
                                        prdtAttributeCode: "00000059", // 販売チャンネル
                                        prdtAttributeValue: "NTTコム", // 「NTTコム」固定
                                    },
                                    {
                                        prdtAttributeCode: "00000060", // お客様対応部門（担当）
                                        prdtAttributeValue:
                                            payload.custInfo.customerService,
                                    },
                                    {
                                        prdtAttributeCode: "00000061", // お客様対応部門電話番号
                                        prdtAttributeValue:
                                            StringHelper.normalizePhoneNum(
                                                payload.custInfo
                                                    .customerServicePhoneNumber,
                                            ),
                                    },
                                    {
                                        prdtAttributeCode: "00000063", // お客様対応部門E-MAIL
                                        prdtAttributeValue:
                                            payload.custInfo
                                                .customerServiceEmail,
                                    },
                                    {
                                        prdtAttributeCode: "00000082", // 契約者名（ﾌﾘｶﾞﾅ）
                                        prdtAttributeValue:
                                            payload.custInfo.contractNameKana,
                                    },
                                    {
                                        prdtAttributeCode: "00000081", // 契約者名
                                        prdtAttributeValue:
                                            payload.custInfo.contractName,
                                    },
                                    {
                                        prdtAttributeCode: "J4000001", // 契約者住所コード値有無
                                        prdtAttributeValue: "0", // 「0:無し」固定
                                    },
                                    {
                                        prdtAttributeCode: "J4000301", // 契約者郵便番号
                                        prdtAttributeValue:
                                            payload.delivery.postcode,
                                    },
                                    {
                                        prdtAttributeCode: "J4000801", // 契約者都道府県
                                        prdtAttributeValue:
                                            payload.delivery.prefecture,
                                    },
                                    {
                                        prdtAttributeCode: "J4000802", // 契約者市区町村
                                        prdtAttributeValue:
                                            payload.delivery.city,
                                    },
                                    {
                                        prdtAttributeCode: "J4000804", // 契約者字丁目
                                        prdtAttributeValue:
                                            payload.delivery.aza,
                                    },
                                    {
                                        prdtAttributeCode: "J4000501", // 契約者番地
                                        prdtAttributeValue:
                                            payload.delivery.block,
                                    },
                                    {
                                        prdtAttributeCode: "00000084", // 契約者担当者氏名
                                        prdtAttributeValue:
                                            payload.delivery.contact,
                                    },
                                    {
                                        prdtAttributeCode: "00000085", // 事務担当者電話番号
                                        prdtAttributeValue:
                                            StringHelper.normalizePhoneNum(
                                                payload.delivery.tel,
                                            ),
                                    },
                                ],
                            },
                            {
                                // 「0009：工事手配」の申込情報域
                                appPrdtStatus: "01", // 「01：オーダ中」固定
                                prdtCode: "T9000002", // 申込むオプションの商品コードを設定
                                processType: "01", // 「01：追加」固定
                                prdtTypeCode: "0009", // 「0009：工事手配」
                                prdtSerialno: serialNo++, // ユニーク通番
                                appPrdtDtlList: [
                                    {
                                        prdtAttributeCode: "S1000001", // 工事要否
                                        prdtAttributeValue: "0", // 総量規制ファイル出力要否「0（否）」固定
                                    },
                                ],
                            },
                            {
                                // 「0010：物品手配」の申込情報域
                                appPrdtStatus: "01", // 「01：オーダ中」固定
                                prdtCode: "TA000001", // 申込むオプションの商品コードを設定
                                processType: "01", // 「01：追加」固定
                                prdtTypeCode: "0010", // 「0010：物品手配」
                                prdtSerialno: serialNo++, // ユニーク通番
                                appPrdtDtlList: [
                                    {
                                        prdtAttributeCode: "S1000001", // 工事要否
                                        prdtAttributeValue: "0", // 『0:否』固定
                                    },
                                ],
                            },
                            // NOTE 0011：記事 seems to be optional
                        ],
                    },
                ],
            },
        };
    }

    private createSameMvneHaishiTransactionRequest(
        payload: SameMvneHaishiPayload,
    ): SameMvneHaishiRequest {
        return {
            commonHeaderInfo: this.generateCommonHeaderInfo(),
            appRequestInfo: {
                processType: "03", // 「03:ワーニング突破」固定
                appInfoList: [
                    {
                        appBasic: {
                            inputFromType: "01", // 「01:MVNO顧客」固定
                            orderType: "03", // 「03:商品廃止」固定
                            appStatus: "15", // 「15:オーダ確定」固定
                            appDate: payload.receivedDate, // REST APIで受付けた際のシステム日付
                            requestDate: payload.receivedDate, // REST APIで受付けた際のシステム日付
                            inputFromReceiptId: payload.frontSoId, // 卸フロントのSO-ID（PF～）
                            appBasicDtlList: [
                                {
                                    appAttributeCode: "********", // 「********:レコード種別」固定
                                    appAttributeValue: "008", // 「008:回線廃止」固定
                                },
                                {
                                    appAttributeCode: "M0000012", // 「M0000012:発信番号」固定
                                    appAttributeValue: payload.lineNo, // 電文中の回線番号
                                },
                                {
                                    appAttributeCode: "M0000013", // 「M0000013:契約番号（N番号）」固定
                                    appAttributeValue: payload.nNo, // 紐づくN番を設定
                                },
                                {
                                    appAttributeCode: "********", // 同一MVNEフラグ
                                    appAttributeValue: "1", // 同一MVNEフラグ
                                },
                            ],
                        },
                    },
                ],
            },
        };
    }

    private createLineEnableTransactionRequest(
        payload: CoreSwimmyLineEnablePayload,
    ): CoreSwimmyLineEnableRequest {
        const appBasicDtlList = [];
        if (payload.sameMvneFlag) {
            appBasicDtlList.push({
                appAttributeCode: "********",
                appAttributeValue: "1",
            });
        }

        // NOTE `enableDate = param.chargeDate ?? receivedDate` already handled in LineEnableService
        const startDate = payload.enableDate;
        return {
            commonHeaderInfo: this.generateCommonHeaderInfo(),
            appRequestInfo: {
                processType: "03", // 「03:ワーニング突破」固定
                appInfoList: [
                    {
                        appBasic: {
                            inputFromType: "01", // 「01:MVNO顧客」固定
                            orderType: "11", // 「11:商品変更」固定
                            appStatus: "15", // 「15:オーダ確定」固定
                            appDate: startDate, // 申込日を設定(yyyymmdd)
                            requestDate: startDate, // 希望日を設定(yyyymmdd)
                            inputFromReceiptId: payload.frontSoId, // 卸フロントのSO-ID（PF～）
                            appBasicDtlList: [
                                {
                                    appAttributeCode: "********", // 「********:レコード種別」固定
                                    appAttributeValue: "005", // 「005:SIMステータス変更」固定
                                },
                                {
                                    appAttributeCode: "M0000012", // 「M0000012:発信番号」固定
                                    appAttributeValue: payload.lineNo, // 対象の発信番号を設定
                                },
                                {
                                    appAttributeCode: "M0000013", // 「M0000013:契約番号（N番号）」固定
                                    appAttributeValue: payload.nNo, // 紐づくN番を設定
                                },
                                ...appBasicDtlList, // 同一MVNEフラグ
                            ],
                        },
                        appPrdtList: [
                            {
                                appPrdtStatus: "01", // 「01：オーダ中」固定
                                prdtCode: payload.deviceTypeIdT, // 申し込む提供商品を設定
                                processType: "02", // 「02：変更」固定
                                prdtTypeCode: "0002", // 「0002：提供商品」固定
                                prdtSerialno: 1,
                                appPrdtDtlList: [
                                    {
                                        prdtAttributeCode: "00000008", // SIMステータス
                                        prdtAttributeValue: "0", // 「0:黒」
                                    },
                                    {
                                        prdtAttributeCode: "00000009", // アクティベート実施日
                                        prdtAttributeValue: startDate, // 利用開始年月日を設定
                                    },
                                ],
                            },
                        ],
                    },
                ],
            },
        };
    }

    /**
     * 回線グループ追加チャージ(クーポン)容量・期間追加トランザクションのリクエストを生成する
     */
    public async registerLineGroupAddAcquisitionTransaction(
        payload: LineGroupAddAcquisitionPayload,
    ) {
        try {
            this.updateFrontSoIdIfNeeded(payload);
            const requestBody = await this.createLineGroupAddAcquisitionTransactionRequest(
                payload,
            );

            const coreSwimmyApiLog = await CoreSwimmyApiLog.createSwimmyApiLog({
                tenantId: payload.tenantId,
                tempoId: "",
                kaisenNo: payload.lineNo,
                swimmyType: "001",
                soId: payload.frontSoId,
                requestOrderId: payload.coreSoId,
                requestParam: requestBody,
                status: CoreSwimmyStatus.TOUROKU_IRAI_NOT_YET_SENT,
                jikkouchuFlg: true, // set true because we're (possibly) sending to SB
            });
            await this.sendToServiceBus(coreSwimmyApiLog);
        } catch (e: any) {
            this.context.info(
                "registerLineGroupAddAcquisitionTransaction payload",
                JSON.stringify(payload),
            );
            this.context.error("registerLineGroupAddAcquisitionTransaction error", e);
        }
    }

    /**
     * SIM再発行トランザクションを登録する
     */
    public async registerSIMReissueTransaction(
        payload: SIMReissueTransactionPayload,
    ) {
        try {
            this.updateFrontSoIdIfNeeded(payload);
            const requestBody = await this.createSIMReissueTransactionRequest(
                payload,
            );

            const coreSwimmyApiLog = await CoreSwimmyApiLog.createSwimmyApiLog({
                tenantId: payload.tenantId,
                tempoId: "",
                kaisenNo: payload.lineNo,
                swimmyType: "004",
                soId: payload.frontSoId,
                requestOrderId: payload.coreSoId,
                requestParam: requestBody,
                status: CoreSwimmyStatus.TOUROKU_IRAI_NOT_YET_SENT,
                jikkouchuFlg: true, // set true because we're (possibly) sending to SB
            });
            await this.sendToServiceBus(coreSwimmyApiLog);
        } catch (e: any) {
            this.context.info(
                "registerSIMReissueTransaction payload",
                JSON.stringify(payload),
            );
            this.context.error("registerSIMReissueTransaction error", e);
        }
    }

    /**
     * オプション変更トランザクションを登録する
     */
    public async registerOptionChangeTransaction(
        payload: OptionChangeTransactionPayload,
    ) {
        try {
            this.updateFrontSoIdIfNeeded(payload);
            const requestBody = await this.createOptionChangeTransactionRequest(
                payload,
            );
            const coreSwimmyApiLog = await CoreSwimmyApiLog.createSwimmyApiLog({
                tenantId: payload.tenantId,
                tempoId: "",
                kaisenNo: payload.lineNo,
                swimmyType: "006",
                soId: payload.frontSoId,
                requestOrderId: payload.coreSoId,
                requestParam: requestBody,
                status: CoreSwimmyStatus.TOUROKU_IRAI_NOT_YET_SENT,
                jikkouchuFlg: true, // set true because we're (possibly) sending to SB
            });
            await this.sendToServiceBus(coreSwimmyApiLog);
        } catch (e: any) {
            this.context.info(
                "registerOptionChangeTransaction payload",
                JSON.stringify(payload),
            );
            this.context.error("registerOptionChangeTransaction error", e);
        }
    }

    /**
     * 回線廃止トランザクションを登録する
     */
    public async registerLineDelTransaction(
        payload: LineDeleteTransactionPayload,
    ) {
        try {
            this.updateFrontSoIdIfNeeded(payload);
            const requestBody = this.createLineDelTransactionRequest(payload);
            const coreSwimmyApiLog = await CoreSwimmyApiLog.createSwimmyApiLog({
                tenantId: payload.tenantId,
                tempoId: "",
                kaisenNo: payload.lineNo,
                swimmyType: "008",
                soId: payload.frontSoId,
                requestOrderId: payload.coreSoId,
                requestParam: requestBody,
                status: CoreSwimmyStatus.TOUROKU_IRAI_NOT_YET_SENT,
                jikkouchuFlg: true, // set true because we're (possibly) sending to SB
            });
            await this.sendToServiceBus(coreSwimmyApiLog);
        } catch (e: any) {
            this.context.info(
                "registerLineDelTransaction payload",
                JSON.stringify(payload),
            );
            this.context.error("registerLineDelTransaction error", e);
        }
    }

    /**
     * 予約キャンセルトランザクションを登録する
     */
    public async registerSoCancelTransaction(payload:SoCancelPayload){
        try{
            this.updateFrontSoIdIfNeeded(payload);
            const requestBody = this.createSoCancelTransactionRequest(payload)
            const coreSwimmyApiLog = await CoreSwimmyApiLog.createSwimmyApiLog({
                tenantId: payload.tenantId,
                tempoId: "",
                kaisenNo: payload.lineNo,
                swimmyType: "000",
                soId: payload.frontSoId,
                requestOrderId: payload.coreSoId,
                requestParam: requestBody,
                status: CoreSwimmyStatus.TOUROKU_IRAI_NOT_YET_SENT,
                jikkouchuFlg: true, // set true because we're (possibly) sending to SB
            });
            await this.sendToServiceBus(coreSwimmyApiLog);
        }catch(e:any){
            this.context.info(
                "registerSoCancelTransaction payload",
                JSON.stringify(payload),
            );
            this.context.error("registerSoCancelTransaction error", e);
        }
    }

    /**
     * 回線追加チャージ(クーポン)容量・期間追加トランザクションのリクエストを生成する
     */
    public async registerLineAddAcquisitionTransaction(
        payload: LineAddAcquisitionPayload,
    ) {
        try {
            this.updateFrontSoIdIfNeeded(payload);
            const requestBody = await this.createLineAddAcquisitionTransactionRequest(
                payload,
            );

            const coreSwimmyApiLog = await CoreSwimmyApiLog.createSwimmyApiLog({
                tenantId: payload.tenantId,
                tempoId: "",
                kaisenNo: payload.lineNo,
                swimmyType: "001",
                soId: payload.frontSoId,
                requestOrderId: payload.coreSoId,
                requestParam: requestBody,
                status: CoreSwimmyStatus.TOUROKU_IRAI_NOT_YET_SENT,
                jikkouchuFlg: true, // set true because we're (possibly) sending to SB
            });
            await this.sendToServiceBus(coreSwimmyApiLog);
        } catch (e: any) {
            this.context.info(
                "registerLineGroupAddAcquisitionTransaction payload",
                JSON.stringify(payload),
            );
            this.context.error("registerLineGroupAddAcquisitionTransaction error", e);
        }
    }

    /**
     * 回線プラン変更トランザクションを登録する
     */
    public async registerLinePlanAcquisitionTransaction(payload:LinePlanAcquisitionPayload){
        try{
            this.updateFrontSoIdIfNeeded(payload);
            const requestBody = this.createLinePlanAcquisitionTransactionRequest(payload)
            const coreSwimmyApiLog = await CoreSwimmyApiLog.createSwimmyApiLog({
                tenantId: payload.tenantId,
                tempoId: "",
                kaisenNo: payload.lineNo,
                swimmyType: "002",
                soId: payload.frontSoId,
                requestOrderId: payload.coreSoId,
                requestParam: requestBody,
                status: CoreSwimmyStatus.TOUROKU_IRAI_NOT_YET_SENT,
                jikkouchuFlg: true, // set true because we're (possibly) sending to SB
            });
            await this.sendToServiceBus(coreSwimmyApiLog);
        }catch(e:any){
            this.context.info(
                "registerLinePlanAcquisitionTransaction payload",
                JSON.stringify(payload),
            );
            this.context.error("registerLinePlanAcquisitionTransaction error", e);
        }
    }

    /**
     * NW契約変更トランザクションを登録する
     */
    public async registerNWContractTransaction(
        payload: NWContractTransactionPayload,
    ) {
        try {
            this.updateFrontSoIdIfNeeded(payload);
            const requestBody = await this.createNWContractTransactionRequest(
                payload,
            );
            const coreSwimmyApiLog = await CoreSwimmyApiLog.createSwimmyApiLog({
                tenantId: payload.tenantId,
                tempoId: "",
                kaisenNo: payload.lineNo,
                swimmyType: "009",
                soId: payload.frontSoId,
                requestOrderId: payload.coreSoId,
                requestParam: requestBody,
                status: CoreSwimmyStatus.TOUROKU_IRAI_NOT_YET_SENT,
                jikkouchuFlg: true, // set true because we're (possibly) sending to SB
            });
            await this.sendToServiceBus(coreSwimmyApiLog);
        } catch (e: any) {
            this.context.info(
                "registerNWContractTransaction payload",
                JSON.stringify(payload),
            );
            this.context.error("registerNWContractTransaction error", e);
        }
    }

    /**
     * フロント用予約キャンセルトランザクションを登録する
     */
    public async registerFrontSoCancelTransaction(
        payload: FrontSoCancelPayload,
    ) {
        try {
            this.updateFrontSoIdIfNeeded(payload);
            const requestBody =
                this.createFrontSoCancelTransactionRequest(payload);
            const coreSwimmyApiLog = await CoreSwimmyApiLog.createSwimmyApiLog({
                tenantId: payload.tenantId,
                tempoId: "",
                kaisenNo: payload.lineNo,
                swimmyType: "000",
                soId: payload.frontSoId,
                requestOrderId: payload.coreSoId,
                requestParam: requestBody,
                status: CoreSwimmyStatus.TOUROKU_IRAI_NOT_YET_SENT,
                jikkouchuFlg: true, // set true because we're (possibly) sending to SB
            });
            await this.sendToServiceBus(coreSwimmyApiLog);
        } catch (e: any) {
            this.context.info(
                "registerFrontSoCancelTransaction payload",
                JSON.stringify(payload),
            );
            this.context.error("registerFrontSoCancelTransaction error", e);
        }
    }

    /**
     * 回線グループ所属回線変更機能トランザクションを登録する
     */
    public async registerLineGroupModifyAcquisitionTransaction(
        payload: LineGroupModifyAcquisitionPayload,
    ) {
        try {
            this.updateFrontSoIdIfNeeded(payload);
            const requestBody =
                this.createLineGroupModifyAcquisitionTransactionRequest(payload);
            const coreSwimmyApiLog = await CoreSwimmyApiLog.createSwimmyApiLog({
                tenantId: payload.tenantId,
                tempoId: "",
                kaisenNo: payload.lineNo,
                swimmyType: "002",
                soId: payload.frontSoId,
                requestOrderId: payload.coreSoId,
                requestParam: requestBody,
                status: CoreSwimmyStatus.TOUROKU_IRAI_NOT_YET_SENT,
                jikkouchuFlg: true, // set true because we're (possibly) sending to SB
            });
            await this.sendToServiceBus(coreSwimmyApiLog);
        } catch (e: any) {
            this.context.info(
                "registerLineGroupModifyAcquisitionTransaction payload",
                JSON.stringify(payload),
            );
            this.context.error("registerLineGroupModifyAcquisitionTransaction error", e);
        }
    }

    /**
     * 回線廃止（同一MVNE）トランザクションを登録する
     * @param payload
     * @param skipSendToServiceBus don't send to service bus (for waiting 回線追加 being created)
     * @param jikkouchuFlg (default: `true`)
     * @returns トランザクションID
     */
    public async registerSameMvneHaishiTransaction(
        payload: SameMvneHaishiPayload,
        skipSendToServiceBus: boolean = false,
        jikkouchuFlg: boolean = true,
    ): Promise<string> {
        try {
            this.updateFrontSoIdIfNeeded(payload);
            const requestBody =
                this.createSameMvneHaishiTransactionRequest(payload);
            const coreSwimmyApiLog = await CoreSwimmyApiLog.createSwimmyApiLog(
                {
                    tenantId: payload.tenantId,
                    tempoId: "",
                    kaisenNo: payload.lineNo,
                    swimmyType: "008",
                    soId: payload.frontSoId,
                    requestOrderId: payload.coreSoId,
                    requestParam: requestBody,
                    status: CoreSwimmyStatus.TOUROKU_IRAI_NOT_YET_SENT,
                    jikkouchuFlg,
                    isChainedTransaction: true, // part of 廃止→新規 chain
                },
                { skipWaitingCheck: true }, // since this is head of 廃止→新規, no need
            );
            if (!skipSendToServiceBus) {
                await this.sendToServiceBus(coreSwimmyApiLog);
            }
            return coreSwimmyApiLog.id;
        } catch (e: any) {
            this.context.info(
                "registerSameMvneHaishiTransaction payload",
                JSON.stringify(payload),
            );
            this.context.error("registerSameMvneHaishiTransaction error", e);
            throw e;
        }
    }

    /**
     * 仮登録回線追加トランザクションを登録する
     * @param payload
     * @param haishiId 同一MVNEの場合は廃止トランザクションIDを指定する
     */
    public async registerPreLineAddTransaction(
        payload: PreLineAddPayload,
        haishiId: string = null,
    ) {
        try {
            this.updateFrontSoIdIfNeeded(payload);
            const requestBody = payload.isFM
                ? await this.createPreLineAddFullTransactionRequest(payload)
                : await this.createPreLineAddLiteTransactionRequest(payload);
            const coreSwimmyApiLog = await CoreSwimmyApiLog.createSwimmyApiLog(
                {
                    tenantId: payload.tenantId,
                    tempoId: "",
                    kaisenNo: payload.lineNo,
                    swimmyType: "007",
                    soId: payload.frontSoId,
                    requestOrderId: payload.coreSoId,
                    requestParam: requestBody,
                    status: CoreSwimmyStatus.TOUROKU_IRAI_NOT_YET_SENT,
                    jikkouchuFlg: true, // set true because we're (possibly) sending to SB
                },
                {
                    setWaitingForId: haishiId,
                },
            );
            await this.sendToServiceBus(coreSwimmyApiLog);
        } catch (e) {
            this.context.info(
                "registerPreLineAddTransaction payload",
                JSON.stringify(payload),
            );
            this.context.error("registerPreLineAddTransaction error", e);
        }
    }

    public async registerLineEnableTransaction(
        payload: CoreSwimmyLineEnablePayload,
    ) {
        try {
            this.updateFrontSoIdIfNeeded(payload);
            const requestBody =
                this.createLineEnableTransactionRequest(payload);
            const coreSwimmyApiLog = await CoreSwimmyApiLog.createSwimmyApiLog({
                tenantId: payload.tenantId,
                tempoId: "",
                kaisenNo: payload.lineNo,
                swimmyType: "005",
                soId: payload.frontSoId,
                requestOrderId: payload.coreSoId,
                requestParam: requestBody,
                status: CoreSwimmyStatus.TOUROKU_IRAI_NOT_YET_SENT,
                jikkouchuFlg: true, // set true because we're (possibly) sending to SB
            });
            await this.sendToServiceBus(coreSwimmyApiLog);
        } catch (e) {
            this.context.info(
                "registerLineEnableTransaction payload",
                JSON.stringify(payload),
            );
            this.context.error("registerLineEnableTransaction error", e);
        }
    }

    private async getServiceBusAdministrationClient(): Promise<ServiceBusAdministrationClient> {
        const serviceBusAdministrationClient = this.coreConfig.CORE_SWIMMY_API
            .POLLING.SERVICE_BUS_CONNECTION_STRING
            ? new ServiceBusAdministrationClient(
                  this.coreConfig.CORE_SWIMMY_API.POLLING.SERVICE_BUS_CONNECTION_STRING,
              )
            : null;
        if (!serviceBusAdministrationClient) {
            this.context.warn("serviceBusAdministrationClient is null");
            return null;
        }
        return serviceBusAdministrationClient;
    }

    public async isMaintenanceMode(): Promise<boolean> {
        const serviceBusAdministrationClient =
            await this.getServiceBusAdministrationClient();
        if (!serviceBusAdministrationClient) {
            return false;
        }
        const apiRequestsQueue = await serviceBusAdministrationClient.getQueue(
            this.coreConfig.CORE_SWIMMY_API.POLLING.SERVICE_BUS_QUEUE_NAME,
        );
        const isMaintenance = !["Active"].includes(apiRequestsQueue.status);
        return isMaintenance;
    }

    public async toggleMaintenanceMode(
        isMaintenance: boolean,
    ): Promise<boolean> {
        const serviceBusAdministrationClient =
            await this.getServiceBusAdministrationClient();
        if (!serviceBusAdministrationClient) {
            return false;
        }
        const apiRequestsQueue = await serviceBusAdministrationClient.getQueue(
            this.coreConfig.CORE_SWIMMY_API.POLLING.SERVICE_BUS_QUEUE_NAME,
        );
        apiRequestsQueue.status = isMaintenance ? "ReceiveDisabled" : "Active";
        await serviceBusAdministrationClient.updateQueue(apiRequestsQueue);
        return !["Active"].includes(apiRequestsQueue.status);
    }

    public async search(
        query: object,
        sort: number,
        limit: number,
        skip: number,
    ): Promise<ICoreSwimmySearchResult> {
        return await CoreSwimmyApiLog.search(query, sort, limit, skip);
    }

    public async findById(id: string): Promise<ICoreSwimmyApiLogDocument> {
        return await CoreSwimmyApiLog.findById(id);
    }

    public async updateForResendRequest(
        id: string,
        userPlusId: string,
        resendOptions?: ResendOptions,
    ): Promise<boolean> {
        const status = CoreSwimmyStatus.TOUROKU_IRAI_NOT_YET_SENT;
        const statusTime = Date.now();

        const query: ICoreSwimmyResendQuery = {
            _id: id,
            status: { $ne: status },
            jikkouchuFlg: false,
        };

        const updateObj: ICoreSwimmyResendUpdate = {
            status,
            statusTime,
            jikkouchuFlg: true,
            [`statusLog.${statusTime}`]: status,
            [`updateRetryUsers.${statusTime}`]: userPlusId,
        };

        this.context.log(
            "CoreSwimmyService updateForResendRequest START ",
            id,
            userPlusId,
            JSON.stringify(resendOptions),
        );
        const originalDocument = await CoreSwimmyApiLog.findById(id).lean();

        if (!isNone(resendOptions)) {
            const newRequestParam = this.updateRequestParam(
                id,
                originalDocument.swimmyType,
                resendOptions,
                originalDocument.requestParam,
            );
            if (!isNone(newRequestParam)) {
                updateObj.requestParam = newRequestParam;
            }
        }

        let result = null;
        try {
            result = await CoreSwimmyApiLog.updateResendRequest(
                query,
                updateObj,
            );
            if (result) {
                await this.sendToServiceBus(result);
            } else {
                this.context.warn("CoreSwimmyService updateForResendRequest: not found", id, query);
            }
        } catch (error) {
            // revert if failed
            await CoreSwimmyApiLog.updateOne({ _id: id }, originalDocument);
            this.context.error(
                "CoreSwimmyService updateForResendRequest: error",
                error,
            );
            result = null;
        }
        return result !== null;
    }

    public async updateStatusForUI(
        context: ExtendedPortalInvocationContext,
        requestOrderId: string,
        swimmyType: SwimmyType,
        status: CoreSwimmyStatus,
        userPlusId: string,
    ): Promise<boolean> {
        let success = false;
        try {
            const result = await CoreSwimmyApiLog.updateStatus(
                context,
                requestOrderId,
                swimmyType,
                status,
                null,
                userPlusId,
            );
            success = result.modifiedCount > 0;
        } catch (error) {
            success = false;
        }
        return success;
    }

    /**
     * Send 待機中 transaction to service bus
     * @param context
     * @param id string version of ObjectId (`.id`)
     * @param requestScheduleTime epoch seconds to schedule the message, null for default value (60 seconds later)
     * @returns true if successfully sent, false otherwise
     */
    public async activateTransaction(
        context: InvocationContext,
        id: string,
        requestScheduleTime: number = null,
    ): Promise<boolean> {
        context.log("CoreSwimmyService.activateTransaction start:", id, requestScheduleTime);
        let success = false;
        try {
            const payload = await CoreSwimmyApiLog.activateWaitingTransaction(
                id,
            );
            if (isNone(payload)) {
                // if not found or status is not 待機中 then the transaction will not be sent
                context.log(
                    "CoreSwimmyService.activateTransaction: could not find by ID:",
                    id,
                );
            } else {
                let scheduleTime: number = null;
                // if it is 007 (追加) type and is 同一MVNE, need to send as scheduled message in
                // custom timing
                if (payload.swimmyType === CORE_SWIMMY_TYPES.KAISEN_TSUIKA && payload.isChainedTransaction === true) {
                    const configMinute = getNumberValue(
                        this.coreConfig.CORE_SWIMMY_API.MASKING_ONHOLD_MINUTE,
                        15,
                    );
                    const configHours = getArrayValue<number>(
                        this.coreConfig.CORE_SWIMMY_API
                            .MASKING_ONHOLD_SKIP_HOURS,
                        [],
                    );
                    scheduleTime = getScheduleTimeForSwimmyOrder(
                        Math.floor(Date.now() / 1000),
                        configMinute,
                        configHours,
                    );
                    context.log(
                        "CoreSwimmyService.activateTransaction: send as scheduled message",
                        id,
                        new Date(scheduleTime * 1000).toISOString(),
                        JSON.stringify({
                            scheduleTime,
                            configMinute,
                            configHours,
                        }),
                    );
                } else {
                    // schedule now + some delay to avoid immediate processing
                    if (isNone(requestScheduleTime)) {
                        const config = AppConfig.getCoreConfig(null);
                        const defaultDelay = getNumberValue(config.CORE_SWIMMY_API.MESSAGE_DELAY_SECONDS);
                        scheduleTime = Math.floor(Date.now() / 1000) + defaultDelay;
                    } else {
                        scheduleTime = requestScheduleTime;
                    }
                    context.log(
                        "CoreSwimmyService.activateTransaction: send as scheduled message",
                        id,
                        new Date(scheduleTime * 1000).toISOString(),
                        JSON.stringify({
                            scheduleTime,
                            requestScheduleTime,
                        }),
                    );
                }
                await this.sendToServiceBus(payload, scheduleTime);
            }
            success = true; // either way, we consider this a success
        } catch (e) {
            context.error("CoreSwimmyService.activateTransaction:", e);
        }
        context.log("CoreSwimmyService.activateTransaction end:", id, success);
        return success;
    }

    /**
     * Update request param for resend options
     * @param swimmyType
     * @param resendOptions
     * @param requestParam
     * @returns
     */
    private updateRequestParam(
        id: string,
        swimmyType: SwimmyType,
        resendOptions: ResendOptions,
        requestParam: any,
    ): any | null {
        this.context.log(
            "handleResendOptions",
            id,
            swimmyType,
            JSON.stringify(resendOptions),
        );
        if (isNone(resendOptions) || isNone(requestParam)) return null;

        if (RESEND_SAME_MVNE_FLAG_TYPES.includes(swimmyType) && resendOptions.sameMvneFlag === 1) {
            const newParam = structuredClone(
                requestParam,
            ) as unknown as CoreSwimmyPreLineAddRequest;
            // if it is full mvno, do nothing
            const fullMvnoCheck =
                newParam?.appRequestInfo?.appInfoList[0]?.appPrdtList[0]?.appPrdtDtlList?.find(
                    (r) => r.prdtAttributeCode === "00000108",
                );
            // フルMVNO識別
            if (fullMvnoCheck && fullMvnoCheck.prdtAttributeValue === "1") {
                this.context.log(
                    `handleResendOptions: ${id} [${swimmyType}/同一MVNE] skip full mvno`,
                );
                return null;
            }

            // add 同一MVNEフラグ if not exist
            const sameMvneFlag =
                newParam?.appRequestInfo?.appInfoList[0]?.appBasic?.appBasicDtlList?.find(
                    (r) => r.appAttributeCode === "********",
                );
            if (isNone(sameMvneFlag)) {
                this.context.log(
                    `handleResendOptions: ${id} [${swimmyType}/同一MVNE] add sameMvneFlag`,
                );
                newParam?.appRequestInfo?.appInfoList[0]?.appBasic?.appBasicDtlList?.push(
                    {
                        appAttributeCode: "********", // 同一MVNEフラグ
                        appAttributeValue: "1", // 同一MVNEの場合、「1」を設定
                    },
                );
                return newParam;
            }
            this.context.log(
                `handleResendOptions: ${id} [${swimmyType}/同一MVNE] sameMvneFlag already set`,
            );
            return null;
        }

        this.context.log(
            `handleResendOptions: ${id} no suitable swimmyType/resendOptions found`,
        );
        return null;
    }

    /**
     * Update frontSoId in payload if it is not set
     * @param payload
     */
    private updateFrontSoIdIfNeeded<T extends TransactionPayloadBase>(
        payload: T,
    ): void {
        if (isNone(payload?.frontSoId)) {
            this.context.warn(
                "updateFrontSoId: frontSoId is null or undefined",
                payload?.coreSoId,
            );
            payload.frontSoId = payload?.coreSoId;
        }
        if (isNone(payload?.frontSoId)) {
            this.context.error(
                "updateFrontSoId: frontSoId is still null or undefined",
                payload?.coreSoId,
            );
            throw new Error("frontSoId is required but not set in payload");
        }
    }
}
