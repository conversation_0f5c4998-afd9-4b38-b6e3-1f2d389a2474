import { TableClient, AzureNamedKeyCredential, ListTableEntitiesOptions } from "@azure/data-tables";
import BaseService from "./baseService";
import AppConfig from "@/appconfig";
import { InvocationContext } from "@azure/functions";

type TableKeyProcess =
    "MntScriptPid" | "ExecReservedSOPid" | "ReservedSO" | "AutoLineGroupModbucketPid";

export default class StorageTableService {
    private tableClient = null as TableClient | null;
    private tableKeyProcess = null as TableKeyProcess | null;
    context: InvocationContext;

    constructor(
        context: InvocationContext,
        tableKeyProcess: TableKeyProcess,
    ) {
        this.context = context;
        this.tableKeyProcess = tableKeyProcess;
    }

    public async init(): Promise<TableClient> {
        if (this.tableClient) {
            return this.tableClient;
        }
        const storageConfig = await AppConfig.getStorageConfig(this.context);
        this.context.debug("storageConfig", storageConfig);
        const STORAGE_ACCOUNT = storageConfig.STORAGE_MOS_STORAGE_ACCOUNT;
        const STORAGE_KEY = storageConfig.STORAGE_MOS_STORAGE_KEY;
        const TABLE_NAME =
            this.tableKeyProcess;
        if (storageConfig.STORAGE_CONNECTION_STRING === "UseDevelopmentStorage=true") {
            this.context.log("Using development storage");
            this.tableClient = TableClient.fromConnectionString(
                "UseDevelopmentStorage=true",
                TABLE_NAME
            );
        } else {
            this.tableClient = new TableClient(
                `https://${STORAGE_ACCOUNT}.table.core.windows.net`,
                TABLE_NAME,
                new AzureNamedKeyCredential(STORAGE_ACCOUNT, STORAGE_KEY),
            );
        }
        // since createTable is not returns error if table already exists, we can safely call it
        this.context.log("Creating table: " + TABLE_NAME);
        await this.tableClient.createTable();
        this.context.log("Table created: " + TABLE_NAME);
        return this.tableClient;
    }

    private wrapFunction = async <T>(func: () => Promise<T>): Promise<T> => {
        if (!this.tableClient) {
            throw new Error("Table not initialized");
        }
        return func();
    };

    // delete table
    public clear = async (): Promise<void> => {
        return this.wrapFunction(() => this.tableClient?.deleteTable());
    };

    // get unique row keys
    public getUniqueRowKeys = async (): Promise<string[]> => {
        return this.wrapFunction(async () => {
            const entities = this.tableClient?.listEntities();
            if (!entities) {
                return [];
            }
            const keys = new Set<string>();
            for await (const entity of entities) {
                keys.add(entity.rowKey);
            }
            return Array.from(keys);
        });
    };

    public listEntitiesByPartitionKey = async (
        partitionKey: string,
    ): Promise<any> => {
        return this.wrapFunction(async () => {
            return this.tableClient?.listEntities({
                queryOptions: { filter: `PartitionKey eq '${partitionKey}'` },
            });
        });
    };

    public submitTransaction = async (batch: any[]): Promise<void> => {
        return this.wrapFunction(async () => {
            await this.tableClient?.submitTransaction(batch);
        });
    };

    public listEntitiesByPartitionKeyPaged = <
        T extends object = Record<string, unknown>,
    >(
        partitionKey: string,
        maxPageSize: number,
    ) => {
        // not wrapped because it is not a Promise
        if (!this.tableClient) {
            throw new Error("Table not initialized");
        }
        return this.tableClient
            ?.listEntities<T>({
                queryOptions: {
                    filter: `PartitionKey eq '${partitionKey}'`,
                },
            })
            .byPage({ maxPageSize });
    };

    public listEntities =
    async (object: ListTableEntitiesOptions) => {
        return this.wrapFunction(async () => {
            return this.tableClient?.listEntities(object);
        });
    };

    public deleteEntity = async (
        partitionKey: string,
        rowKey: string,
    ): Promise<void> => {
        return this.wrapFunction(async () => {
            await this.tableClient?.deleteEntity(partitionKey, rowKey);
        });
    }

    public createEntity = async <T extends object>(
        entity: { partitionKey: string; rowKey: string } & T
    ): Promise<void> => {
        try {
            // Attempt to delete the entity if it already exists. Also delete cost 0.
            await this.tableClient?.deleteEntity(entity.partitionKey, entity.rowKey);
        } catch (error: any) {
            // Ignore error if the entity does not exist
            if (error.statusCode !== 404) {
                throw error;
            }
        }
        return this.wrapFunction(async () => {
            await this.tableClient?.createEntity(entity);
        });
    }

    public getEntity = async <T extends object>(
        partitionKey: string,
        rowKey: string
    ): Promise<T> => {
        return this.wrapFunction(async () => {
            return await this.tableClient?.getEntity(partitionKey, rowKey) as T;
        });
    }
}
