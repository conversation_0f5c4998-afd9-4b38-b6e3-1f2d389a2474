import { InvocationContext, ServiceBusQueueHandler } from "@azure/functions";
import AppConfig from "@/appconfig";

/**
 *
 * @param queueHandler Service Bus Queue handler function
 * @returns void
 */
export const BaseQueueHandler =
    (queueHandler: ServiceBusQueueHandler) =>
    async (message: unknown, context: InvocationContext): Promise<void> => {
        try {
            context.log("BaseQueueHandler start", context.functionName);
            context.log("BaseQueueHandler metadata", {
                sequenceNumber: context.triggerMetadata?.sequenceNumber,
                messageId: context.triggerMetadata?.messageId,
                deliveryCount: context.triggerMetadata?.deliveryCount,
                enqueuedTime: context.triggerMetadata?.enqueuedTime,
            });

            // load core mvno config?
            await AppConfig.loadCoreMvnoConfig(context);

            // run queue handler
            await queueHandler(message, context);
        } catch (error) {
            context.error("BaseQueueHandler error", error);
        }
    };
