import { InvocationContext, Timer<PERSON><PERSON><PERSON> } from "@azure/functions";
import { useMongo } from "@/database/mongo";
import { Timer } from "@azure/functions/types/timer";
import { usePsql } from "@/database/psql";

/**
 * Base timer handler (middleware) for Timer
 *
 * This middleware will log metadata of the message and run the queue handler
 *
 * @param timerHandler Timer handler function
 * @returns void
 */
const BaseTimerHandler =
    (timerHandler: TimerHandler) =>
        async (myTimer: Timer, context: InvocationContext): Promise<void> => {
            try {
                context.log("BaseTimerHandler start", context.functionName);
                context.log("BaseTimerHandler metadata", {
                    sequenceNumber: context.triggerMetadata?.sequenceNumber,
                    messageId: context.triggerMetadata?.messageId,
                    deliveryCount: context.triggerMetadata?.deliveryCount,
                    enqueuedTime: context.triggerMetadata?.enqueuedTime,
                });
                await useMongo(context);
                await usePsql();
                // run queue handler
                await timer<PERSON><PERSON>ler(myTimer, context);
            } catch (error) {
                context.error("BaseTimerHandler error", error);
            }
        };

export default BaseTimerHandler;