import { HttpRequest, HttpResponseInit } from "@azure/functions";
import AppConfig from "@/appconfig";
import { useMongo } from "@/database/mongo";
import PortalFunctionHandlerBase from "@/helpers/PortalFunctionHandlerBase";
import ExtendedPortalInvocationContext from "@/types/ExtendedPortalInvocationContext";
import { getSessionData } from "@/helpers/baseHelper";
import { isNone } from "@/utils";
import { usePsql } from "@/database/psql";

/**
 * Run common validation before passing request to API handler
 * @param apiHandler API handler class
 * @returns response
 */
export const BasePortalHandler =
    <H extends typeof PortalFunctionHandlerBase>(apiHandler: H) =>
    async (
        request: HttpRequest,
        context: ExtendedPortalInvocationContext,
    ): Promise<HttpResponseInit> => {
        let response: HttpResponseInit;
        try {
            context.log("BasePortalHandler");
            // log request body
            context.log("request body: ", request.body);

            const sessionData = getSessionData(context, request);
            const { roleId, tenantId, user } = sessionData;
            context.log("sessionData: ", sessionData);

            if (isNone(roleId) || isNone(tenantId) || isNone(user)) {
                context.log(
                    "BasePortalHandler Unauthorized",
                    roleId,
                    tenantId,
                    user,
                );
                return {
                    status: 401,
                    body: "Unauthorized",
                };
            }

            if (
                apiHandler.AllowedRoles.length > 0 &&
                !apiHandler.AllowedRoles.includes(roleId)
            ) {
                context.log(
                    "BasePortalHandler Forbidden",
                    roleId,
                    tenantId,
                    user,
                );
                return {
                    status: 403,
                    body: "Forbidden",
                };
            }

            context.sessionData = sessionData;

            // load psql
            await usePsql();
            // load mongoose
            await useMongo(context);
            // load core mvno config
            await AppConfig.loadCoreMvnoConfig(context);

            response = await apiHandler.Handler(request, context);
            context.log("BasePortalHandler finished");
            return response;
        } catch (error) {
            context.error(error);
            return;
        }
    };
