import SOManagerInputDto from "@/core/dto/SOManagerInputDto";
import { HttpRequest, HttpResponseInit } from "@azure/functions";
import AppConfig from "@/appconfig";
import ExtendedInvocationContext from "@/types/ExtendedInvocationContext";
import RESTCommon from "@/core/common/RESTCommon";
import MvnoUtil from "@/core/common/MvnoUtil";
import BaseOutputDto from "@/core/dto/BaseOutputDto";
import FunctionHandlerBase from "@/core/common/FunctionHandler";
import Constants from "@/core/constant/Constants";
import ResponseHeader from "@/core/dto/ResponseHeader";
import ResultCdConstants from "@/core/constant/ResultCdConstants";
import { usePsql } from "@/database/psql";
import { useMongo } from "@/database/mongo";
import validateBaseRequest from "@/helpers/baseValidation";
import { isRequestFromSelf } from "@/helpers/baseHelper";

/**
 * Run common validation before passing request to API handler
 * @param apiHandler API handler class
 * @returns response
 */
export const BaseHandler =
    <T extends SOManagerInputDto, H extends typeof FunctionHandlerBase>(
        apiHandler: H,
    ) =>
    async (
        request: HttpRequest,
        context: ExtendedInvocationContext,
    ): Promise<HttpResponseInit> => {
        // define variables used in `finally` block
        let orderType = "";
        let response: HttpResponseInit;
        let restCommon: RESTCommon;
        try {
            context.log("BaseHandler");
            // log request body
            context.log("request body: ", request.body);

            // load mongoose
            await useMongo(context);
            // load core mvno config
            await AppConfig.loadCoreMvnoConfig(context);

            // NOTE consume json here for common check and store it in context before passing it to handler
            let jsonBody: T = null;
            try {
                jsonBody = (await request.json()) as T;
                context.jsonBody = jsonBody;
                context.log("BaseHandler jsonBody: ", JSON.stringify(jsonBody));
            } catch (error) {
                // don't output error when json body is invalid
                context.warn("Invalid JSON body: ", error);
                return {
                    status: 200,
                    jsonBody: {
                        responseHeader: {
                            processCode: ResultCdConstants.CODE_999999,
                            apiProcessID: "",
                            sequenceNo: "",
                            receivedDate: MvnoUtil.getDateTimeNow(),
                        }
                    }
                };
            }

            // if json body is empty, return error
            if (
                !jsonBody ||
                jsonBody === undefined ||
                jsonBody === null ||
                Object.keys(jsonBody).length === 0
            ) {
                throw new Error("Request body is empty");
            }

            // save order type
            orderType = apiHandler.Meta.getOrderType(jsonBody);

            // 共通チェック
            const clientIPAddress = MvnoUtil.getClientIPAddress(request);
            restCommon = new RESTCommon(request, context);
            // connect to DB if needed
            await usePsql();
            const resultBean = await restCommon.checkRestCommon(
                jsonBody.requestHeader.sequenceNo,
                jsonBody.requestHeader.senderSystemId,
                jsonBody.requestHeader.apiKey,
                jsonBody.requestHeader.functionType,
                jsonBody.tenantId,
                apiHandler.Meta.localFunctionType,
                apiHandler.Meta.url,
                orderType,
                jsonBody.reserve_soId,
                clientIPAddress,
            );

            // NOTE reuse this responseHeader in service implementation (core/service/impl/...Service.ts)
            context.responseHeader = {
                processCode: resultBean.processingCode,
                apiProcessID: resultBean.others,
                sequenceNo: jsonBody.requestHeader.sequenceNo,
                receivedDate: MvnoUtil.getDateTimeNow(),
            };
            context.isInternalRequest = isRequestFromSelf(request);
            if (context.isInternalRequest) {
                context.log(
                    "BaseHandler: Request is from internal function app",
                );
            }

            let isValidationOk = resultBean.checkResult;

            // if restCommon validation ok, run base validation
            if (isValidationOk) {
                const baseBean = validateBaseRequest(jsonBody);
                if (!baseBean.checkResult) {
                    // log warn for validation error
                    context.warn(
                        "BaseHandler validation failed:",
                        baseBean.ngReason,
                    );
                    context.responseHeader.processCode =
                        ResultCdConstants.CODE_999999;
                    isValidationOk = false;
                }
            }

            if (!isValidationOk) {
                context.log(
                    "BaseHandler isValidationOk=false :",
                    context.responseHeader,
                );
                // returnEdit
                const response: BaseOutputDto = {
                    jsonBody: { responseHeader: context.responseHeader },
                };
                return { jsonBody: response.jsonBody };
            }

            response = await apiHandler.Handler(request, context);
            context.log("BaseHandler finished:", apiHandler.Meta.processName);
            return response;
        } catch (error) {
            context.error(error);
            const errorResponse: ResponseHeader = {
                processCode: ResultCdConstants.CODE_999999,
                apiProcessID: "",
                sequenceNo: context?.jsonBody?.requestHeader?.sequenceNo,
                receivedDate: MvnoUtil.getDateTimeNow(),
            };
            return {
                jsonBody: {
                    responseHeader: errorResponse,
                },
            };
        } finally {
            // 同時接続数の減算処理
            const processCode =
                response?.jsonBody?.responseHeader?.processCode ??
                context?.responseHeader?.processCode;

            // console.warn(">> finally processCode:", processCode);
            if (!restCommon) {
                // error happened even before common check
                context.log("restCommon is not initialized");
            } else if (orderType !== Constants.ORDER_TYPE_2) {
                // 予約実行の場合は、減算処理は行わない。
                await Promise.allSettled([
                    restCommon.decTenantConnectCount(
                        context.jsonBody.tenantId,
                        processCode,
                    ),
                    restCommon.modConCount(false),
                ]);
            }
        }
    };
