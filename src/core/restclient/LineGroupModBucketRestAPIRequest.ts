import RestAPIRequestBase from './RestAPIRequestBase';

/**
 * REST APIリクエスト JSON処理用実装クラス(回線グループ基本容量変更API)<BR>
 *
 * @version 1.00 新規<BR>
 */
export default class LineGroupModBucketRestAPIRequest extends RestAPIRequestBase {
    /** テナントID	 */
    public tenantId: string;
    /** 回線グループID */
    public lineGroupId: string;
    /** 容量指定 */
    public capacity: string;
    /** 容量計算 */
    public calculation: string;
    /** 予約日 */
    public reserve_date: string;

    /* (非 Javadoc)
     * @see java.lang.Object#toString()
     */
    public toString(): string {
        return "LineGroupModBucketRestAPIRequest [tenantId=" + this.tenantId
            + ", lineGroupId=" + this.lineGroupId + ", capacity=" + this.capacity + ", calculation=" + this.calculation
            + ", reserve_date=" + this.reserve_date + "]";
    }
}
