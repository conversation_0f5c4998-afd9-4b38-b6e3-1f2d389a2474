import Constants from "../constant/Constants";

import AppConfig from "@/appconfig";

const functionType: Record<string, string> = {
    "03": Constants.LINES_CHARGE,           // 回線追加チャージ（クーポン）容量・期間追加
    "04": Constants.LINES_COUPON,           // 回線クーポンON/OFF
    "05": Constants.LINES_ACTIVATION,       // 回線アクティベート/ディアクティベート
    "06": Constants.LINES_PLANCHARGE,       // 回線プラン変更
    "10": Constants.LINESGROUP_CHARGE,      // 回線グループ追加チャージ（クーポン）容量・期間追加
    "12": Constants.LINESGROUP_COUPON,      // 回線グループクーポンON/OFF
    "14": Constants.LINEGROUP_MODIFY,       // 回線グループ所属回線変更
    "15": Constants.LINEGROUP_MOD_BUCKET,   // 回線グループ基本容量変更
    "51": Constants.Line_preadd,            // 仮登録回線追加
    "52": Constants.LINE_DEL,               // 回線廃止
    "20": Constants.LINEGROUP_PLANCHARGE,   // 回線グループプラン変更
};

const requestClass: Record<string, string> = {
    "03": "LinesChargeRestAPIRequest",
    "04": "LinesCouponRestAPIRequest",
    "05": "LinesActivationRestAPIRequest",
    "06": "LinesPlanRestAPIRequest",
    "10": "LinesGroupChargeRestAPIRequest",
    "12": "LinesGroupCouponRestAPIRequest",
    "14": "LinesGroupLineGroup_modifyRestAPIRequest",
    "15": "LinesGroupLineGroup_mod_bucketRestAPIRequest",
    "51": "PreLineAddRestAPIRequest",
    "52": "LinesDeleteRestAPIRequest",
    "20": "LinesGroupPlanRestAPIRequest",
}

export default class RestAPIProperties {
    public static getRestAPIUrlAddress(): string {
        return AppConfig.getCoreConfig(null).RESERVED_SO.API_ENDPOINT;
    }

    public static getRestAPIUrlPath(funcTypeNo: string): string {
        return functionType[funcTypeNo];
    }

    public static getRestAPIRequestClass(funcTypeNo: string): any {
        return requestClass[funcTypeNo];
    }
}

