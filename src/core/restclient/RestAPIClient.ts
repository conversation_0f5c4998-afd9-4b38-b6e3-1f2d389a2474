import RestAPIRequest from "@/core/restclient/RestAPIRequest";
import RestAPIRequestForRO from "@/core/restclient/RestAPIRequestForRO";
import RestAPIResponse from "@/core/restclient/RestAPIResponse";
import { InvocationContext } from "@azure/functions";
import MessageProperties from "@/core/constant/MessageProperties";
import RestAPIProperties from "@/core/restclient/RestAPIProperties";
import MsgKeysConstants from "@/core/constant/MsgKeysConstants";
import axios, { AxiosResponse } from "axios";

export default class RestAPIClient {
    private context: InvocationContext;
    private static readonly REST_API_NORMAL_PROCESSCODE = "000000";
    private readonly connTimeout = 30 * 1000;
    private readonly readTimeout = 3600 * 1000;

    /**
     * REST API実行クラスコンストラクタ
     */
    constructor(context: InvocationContext)
    /**
     * REST API実行クラスコンストラクタ(タイムアウト時間指定)
     *
     * @param connTimeoutSec
     * @param readTimeoutSec
     */
    constructor(context: InvocationContext, connTimeoutSec: number, readTimeoutSec: number)
    constructor(context: InvocationContext, connTimeoutSec?: number, readTimeoutSec?: number) {
        this.context = context;
        if (connTimeoutSec) {
            this.connTimeout = connTimeoutSec * 1000;
        }
        if (readTimeoutSec) {
            this.readTimeout = readTimeoutSec * 1000;
        }
    }

    /**
     * メッセージリソースからメッセージを構築する
     *
     * NOTE: this function is a copy from `@/core/common/AbstractMvnoBaseCommon.ts`
     *
     * @param key
     * メッセージキー
     * @param params
     * メッセージパラメーター
     *
     * @return 構築されたメッセージ
     */
    private getMessage(key: string, params: string[]): string {
        // return key + params.join(",");
        const template = MessageProperties[key];
        if (template && Array.isArray(params)) {
            let result = template;
            params.forEach((param, index) => {
                const regex = new RegExp(`\\{${index}\\}`, "g");
                result = result.replace(regex, param);
            });
            return result;
        }
        return key + params.join(",");
    }

    /**
     * REST APIへのPOSTリクエストを実行。
     *
     * @param target REST APIへの接続情報
     * @param apiReq REST APIへの送信電文情報
     * @param apiResCls レスポンスクラス型
     * @return REST APIから受信したレスポンス情報
     * @throws SocketTimeoutException postリクエスト実行時の読み取りタイムアウト発生時の例外
     * @throws Exception REST APIへのリクエスト発行時に発生した例外
     */
    public async callRestApi(
        target: string,
        apiReq: RestAPIRequest,
    ): Promise<RestAPIResponse> {
        this.context.log("callRestApi", target, apiReq);
        let resEntity: RestAPIResponse = null;
        let response: AxiosResponse<RestAPIResponse, any>;
        try {
            response = await axios.post<RestAPIResponse>(target, {
                ...apiReq,
            });
            this.context.debug("response", response);
            if (response !== null && response.status === 200) {
                resEntity = response.data;
                this.context.debug(resEntity);
                return resEntity;
            }
        } catch (error: any) {
            // ここは呼び出しもとで出しているはずなので、debugで
            this.context.error(error.message, error);
            throw error;
        }
    }

    /**
     * REST APIリクエストの送信準備および送信メソッドの呼び出しを行う。
     *
     * @param jsonStr REST APIへ送信するリクエスト情報文字列
     * @param serviceOrderID 予約実行するオーダID
     * @return REST APIから返却された処理コード
     * @throws SocketTimeoutException postリクエスト実行時の読み取りタイムアウ例外
     * @throws Exception REST APIへのリクエスト発行時に発生した例外
     */
    public async doRestAPIRequest(
        jsonStr: RestAPIRequest,
        serviceOrderID: string,
    ): Promise<string> {
        this.context.log("doRestAPIRequest", jsonStr, serviceOrderID);
        let apiReqHeader: RestAPIRequest = null;
        let apiReqFull: RestAPIRequest = null;
        let apiRes: RestAPIResponse = null;
        let functionType: string = null;
        let cl: string = null;

        try {
            // 機能種別取得のため、ヘッダ情報だけ取得
            apiReqHeader = jsonStr;
            // 機能種別の取得
            functionType = apiReqHeader.requestHeader.functionType;
            this.context.debug("Function: " + functionType);
            if (functionType == null) {
                this.context.error(this.getMessage(MsgKeysConstants.APSORE9999, ["Cannot get FunctionType."]));
                return null;
            }
            // 機能種別から処理用クラス名を取得
            cl = RestAPIProperties.getRestAPIRequestClass(functionType);
            this.context.debug("RequestClass: " + cl);
            if (cl !== null) {
                // リクエストがRestAPIRequestForROAbstractのサブクラスであれば、SO-IDと予約実行フラグをセットする。
                apiReqFull = jsonStr;
                // have already checked that cl is not null, indicating it is one of the class we wanted
                (apiReqFull as RestAPIRequestForRO).reserve_soId = serviceOrderID;
                (apiReqFull as RestAPIRequestForRO).reserve_flag = true;
            } else {
                this.context.error(this.getMessage(MsgKeysConstants.APSORE9999, ["Cannot get Request Class." + functionType]));
                return null;
            }
        } catch (e: any) {
            this.context.error(e.message, e);
            throw e;
        }

        const restApiURLAddress = RestAPIProperties.getRestAPIUrlAddress();
        if (restApiURLAddress == null) {
            this.context.error(this.getMessage(MsgKeysConstants.APSORE9999, ["url address does not exist."]));
            return null;
        }
        const restApiURLPath = RestAPIProperties.getRestAPIUrlPath(functionType);
        if (restApiURLPath == null) {
            this.context.error(this.getMessage(MsgKeysConstants.APSORE9999, ["url path does not exist." + functionType]));
            return null;
        }
        const target = restApiURLAddress + restApiURLPath;
        this.context.debug("target", target);
        this.context.debug("apiReqFull", apiReqFull);
        // REST API呼び出処理(処理応答情報はヘッダに限定)
        if (cl !== null) {
            apiRes = await this.callRestApi(target, apiReqFull);
        }
        this.context.debug("apiRes", apiRes);
        if (apiRes == null) {
            return null;
        }
        const processCode = apiRes.responseHeader.processCode;
        if (processCode == null) {
            return null;
        }
        if (processCode === RestAPIClient.REST_API_NORMAL_PROCESSCODE) {
            // 正常応答を受信した
            this.context.debug("Normal End:" + processCode);
        } else {
            // 正常応答が受信できなかった
            this.context.debug("ABNormal End:" + processCode);
        }
        return processCode;
    }
}