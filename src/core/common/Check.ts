import { isAfter } from "date-fns/isAfter";
import { compareAsc } from "date-fns/compareAsc";
import { isValid, parse, format, subDays, getDaysInMonth, addMonths, lastDayOfMonth } from "date-fns";
import { BlockList } from "net";
import Constants from "../constant/Constants";
import CheckUtil from "./CheckUtil";
import MvnoUtil from "@/core/common/MvnoUtil";
import TpcDataCheckResultDto from "@/core/dto/TpcDataCheckResultDto";
import "@/types/string.extension";
import APISoDAO from "../dao/APISoDAO";
export default class Check {
    /** 定数 「0」 */
    private static CONST_ZERO = "0";
    /** 定数 「ALL」 */
    private static CONST_ALL = "ALL";
    /** STEP1.2b版対応 追加 START */
    /**
     * オーダ種別チェックを行う
     * @param reserve_date 予約日
     * @param reserve_flg 予約フラグ
     * @param so_id ＳＯ－ＩＤ
     * @return オーダ種別
     */
    public static checkOrderType(
        reserve_date: string,
        reserve_flg: boolean,
        so_id: string,
    ): string {
        // 変数「予約日チェック結果」
        // 変数「ＳＯ－ＩＤチェック結果」
        // 変数「オーダ種別」
        let reserveDateResult = true;
        let soIdResult = true;
        let orderType = null;

        // 入力パラメータに対して、予約日チェックを行う
        if (CheckUtil.checkIsNotNull(reserve_date)) {
            reserveDateResult = false;
        }
        // 入力パラメータに対して、ＳＯ－ＩＤチェックを行う
        if (CheckUtil.checkIsNotNull(so_id)) {
            soIdResult = false;
        }

        // １．４ オーダ種別チェック
        // 変数「予約日チェック結果」 = false 且つ
        // 入力パラメータ．「予約フラグ」= false 且つ
        // 変数「ＳＯ－ＩＤチェック結果」 = false
        if (!reserveDateResult && !reserve_flg && !soIdResult) {
            // 変数「オーダ種別」 = '0'（即時オーダ）
            orderType = Constants.ORDER_TYPE_0;
            // 変数「予約日チェック結果」 = true 且つ
            // 入力パラメータ．「予約フラグ」 = false 且つ
            // 変数「ＳＯ－ＩＤチェック結果」 = false
        } else if (reserveDateResult && !reserve_flg && !soIdResult) {
            // 変数「オーダ種別」 = '1'（即予約前オーダ）
            orderType = Constants.ORDER_TYPE_1;
            // 変数「予約日チェック結果」 = true 且つ
            // 入力パラメータ．「予約フラグ」 = true 且つ
            // 変数「ＳＯ－ＩＤチェック結果」 = true
        } else if (reserveDateResult && reserve_flg && soIdResult) {
            // 変数「オーダ種別」 = '2'（予約実行オーダ）
            orderType = Constants.ORDER_TYPE_2;
        } else {
            // '9'を返却し、処理終了
            orderType = Constants.ORDER_TYPE_9;
        }
        return orderType;
    }

    /**
     * @deprecated use `context.isInternalRequest instead`
     *
     * 予約実行可否チェックを行う
     * @param localAddr 送信元のIPアドレス
     * @param ap_ipAdrressList APサーバの複数IPアドレス
     * @return 予約実行可否チェック結果
     */
    public static checkReserve(
        ipAdrress: string,
        ap_ipAdrressList: string[],
    ): boolean {
        // localhost/127.0.0.1も対象とする
        if (ipAdrress === "127.0.0.1") {
            return true;
            // 送信元のIPアドレスがAPサーバで複数IPアドレスに存在するか、存在しない場合
        } else if (!ap_ipAdrressList.includes(ipAdrress)) {
            return false;
        }
        return true;
    }

    /**
     * 予約日と投入日の相関チェックを行う
     * @param order_date 投入日
     * @param reserve_date 予約日
     * @return 予約日と投入日の相関チェック結果
     */
    public static checkReserveDateOrderDate(
        order_date: string,
        reserve_date: string,
    ): boolean {
        try {
            if (isAfter(new Date(reserve_date), new Date(order_date))) {
                return true;
            } else {
                return false;
            }
        } catch (error: any) {
            return false;
        }
    }

    /**
     * 予約実行日時単位フォーマットチェックを行う
     * @param reservationDateExecutionUnits 予約実行日時単位
     * @return 予約実行日時単位フォーマットチェック結果
     */
    public static checkReservationDateExecutionUnitsFmt(
        reservationDateExecutionUnits: string,
    ): boolean {
        // 必須チェック
        if (CheckUtil.checkIsNotNull(reservationDateExecutionUnits)) {
            return false;
            // 桁数チェック
        } else if (
            !CheckUtil.checkLength(reservationDateExecutionUnits, 2, false)
        ) {
            return false;
            // 数値項目チェック
        } else if (!CheckUtil.checkIsNum(reservationDateExecutionUnits)) {
            return false;
        }
        // １．２ 予約実行日時単位チェック
        // 入力パラメータ．「予約実行日時単位」=0 あるいは
        // （0 < 入力パラメータ．「予約実行日時単位」 ≦ 60 且つ 60 mod 入力パラメータ．「予約実行日時単位」＝ 0）
        const data = parseInt(reservationDateExecutionUnits, 10);
        if (data === 0 || (data > 0 && data <= 60 && 60 % data === 0)) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 予約実行日時単位と予約日相関チェックを行う
     * @param reservationDateExecutionUnits 予約実行日時単位
     * @param reserve_date 予約日
     * @return 予約日と投入日の相関チェック結果
     */
    public static checkReservationDateExecutionUnits(
        reservationDateExecutionUnits: string,
        reserve_date: string,
    ): boolean {
        // 入力パラメータ．「予約日」の分
        const minites = reserve_date?.substring(14);
        // 入力パラメータ．「予約実行日時単位」=0 あるいは 入力パラメータ．「予約実行日時単位」=60
        if (
            parseInt(reservationDateExecutionUnits, 10) === 0 ||
            parseInt(reservationDateExecutionUnits, 10) === 60
        ) {
            // 入力パラメータ．「予約日」の分＝00
            if (minites === "00") {
                return true;
            } else {
                return false;
            }
        } else {
            // オーダ種別チェック
            // 入力パラメータ．「予約日」の分 mod  SG「予約実行日時単位」＝0
            if (
                parseInt(minites, 10) %
                    parseInt(reservationDateExecutionUnits, 10) ===
                0
            ) {
                return true;
            } else {
                return false;
            }
        }
    }

    /**
     * 予約可能制限日数フォーマットチェックを行う
     * @param ｒeservationsLimitDays 予約可能制限日数
     * @return 予約可能制限日数フォーマットチェック結果
     */
    public static checkReservationsLimitDaysFmt(reservationsLimitDays: string) {
        // 必須チェック
        if (CheckUtil.checkIsNotNull(reservationsLimitDays)) {
            return false;
            // 桁数チェック
        } else if (!CheckUtil.checkLength(reservationsLimitDays, 3, false)) {
            return false;
            // 数値項目チェック
        } else if (!CheckUtil.checkIsNum(reservationsLimitDays)) {
            return false;
        }
        return true;
    }

    /**
     * TODO: wrtie unit test for this method
     * 予約可能制限日数範囲チェックを行う
     * @param reservationsLimitDays 予約可能制限日数
     * @param reserveDate 予約日
     * @return 予約可能制限日数フォーマットチェック結果
     */
    public static checkReservationsLimitDays(
        reservationsLimitDays: string,
        reserveDate: string,
    ) {
        // 入力パラメータに対して、予約可能制限日数を予約可能制限日数後の日付を算出
        const time: Date = CheckUtil.addDay(
            new Date(),
            parseInt(reservationsLimitDays, 10),
        );

        // システム日付≦入力パラメータ．「予約日」≦変数「予約可能終了日」
        if (
            compareAsc(new Date(), new Date(reserveDate)) <= 0 &&
            compareAsc(new Date(reserveDate), time) <= 0
        ) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * IPアドレスフォーマットチェック
     * @param ipAddress IPアドレス
     * @return チェックOK：true、チェックNG:false
     */
    public static checkIpAddressFmt(ipAddress: string): boolean {
        const checkPattern = /^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$/;
        if (!checkPattern.test(ipAddress)) {
            return false;
        }
        const octets = ipAddress.split(".");
        for (const octet of octets) {
            if (+octet > 255) {
                return false;
            }
        }
        return true;
    }

    /**
     * IPアドレス許可チェック
     * @param targetIpAddress チェック対象IPアドレス
     * @param permitIpAddress 許可IPアドレス
     * @param subnetMask サブネットマスク
     * @return 許可されている：true、許可されていない：false
     */
    public static checkIpAddressPermit(
        targetIpAddress: string,
        permitIpAddress: string,
        subnetMask: number,
    ) {
        if (subnetMask === null || isNaN(subnetMask)) {
            return permitIpAddress === targetIpAddress;
        } else {
            const blockList = new BlockList();
            blockList.addSubnet(permitIpAddress, subnetMask, "ipv4");
            return blockList.check(targetIpAddress);
        }
    }

    /**
     * テナントIDの必須チェック、英数字チェック、桁数チェックを行う
     * @param tenatId テナントID
     * @return テナントIDフォーマットチェック結果
     */
    public static checkTenatIdFmt(tenatId: string): boolean {
        // 「テナントID」の必須入力チェック
        if (CheckUtil.checkIsNotNull(tenatId)) {
            return false;
            // 「テナントID」の英数字チェック
        } else if (!CheckUtil.checkIsSemiangleAlphanumic(tenatId)) {
            return false;
            // 「テナントID」の桁数10チェック
        } else if (!CheckUtil.checkLength(tenatId, 10, false)) {
            return false;
        }
        return true;
    }

    /**
     * 回線番号の必須チェック、桁数チェック、数値チェックを行う
     * @param lineNo 回線番号
     * @return 回線番号フォーマットチェック結果
     */
    public static checkLineNo(lineNo: string): boolean {
        // 「回線番号」の必須入力チェック
        if (CheckUtil.checkIsNotNull(lineNo)) {
            return false;
            // STEP17.0版対応 変更 START
            // 「回線番号」の桁数11または桁数14チェック
        } else if (
            !(
                CheckUtil.checkLength(lineNo, 11, true) ||
                CheckUtil.checkLength(lineNo, 14, true)
            )
        ) {
            // STEP17.0版対応 変更 END
            return false;
            // 「回線番号」の数値項目チェック
        } else if (!CheckUtil.checkIsNum(lineNo)) {
            return false;
        }
        return true;
    }

    /**
     * SO-IDのフォーマットチェックを実施する
     * @param serviceOrderIdKey サービスオーダID
     * @return チェック結果
     */
    public static checkSoId(serviceOrderIdKey: string): boolean {
        // 必須チェック
        if (CheckUtil.checkIsNotNull(serviceOrderIdKey)) {
            return false;
        }
        // 桁数チェック
        if (!CheckUtil.checkLength(serviceOrderIdKey, 15, false)) {
            return false;
        }
        // 半角英数チェック
        if (!CheckUtil.checkIsSemiangleAlphanumic(serviceOrderIdKey)) {
            return false;
        }

        return true;
    }

    /**
     * 譲渡データ量の必須チェック、桁数チェック、数値チェックを行う
     * @param giftData 譲渡データ量
     * @param sourceLineNo 譲渡元回線番号
     * @param destinationLineNo 譲渡先回線番号
     * @return チェック結果
     */
    public static checkGiftData(
        giftData: string,
        sourceLineNo: string,
        destinationLineNo: string,
    ): boolean {
        // 必須チェック
        if (CheckUtil.checkIsNotNull(giftData)) {
            return false;
        }
        // STEP10.0版対応 追加 START
        // 譲渡元回線番号もしくは譲渡先回線番号が「99999999999」(9が11桁)の時に
        // マイナス値(「-」＋9桁以下の文字列)を許容
        if (sourceLineNo.equals("99999999999") || destinationLineNo.equals("99999999999")) {
            // データ譲渡量の先頭が"-"の場合
            if (giftData.startsWith("-")) {
                // 2文字目以降の値を取得
                giftData = giftData.substring(1);
            }
        }
        // STEP10.0版対応 追加 END
        // 桁数チェック
        if (!CheckUtil.checkLength(giftData, 9, false)) {
            return false;
        }
        // 数値項目チェック
        if (!CheckUtil.checkIsNum(giftData)) {
            return false;
        }
        return true;
    }

    /**
     * 基準日と同年同月の月末日を取得する
     * @param baseTime 基準日(yyyy/MM/dd)
     * @return 基準日付
     * @throws Exception 想定外エラー
     */
    public static getEndOfMonth(baseTime: string): string{

        // 変数「基準日付」に、入力パラメータ．「基準日」をCalendar型に変換して設定
        const baseDate: Date = this.parseStrToCal(baseTime);

        // 変数「月末日」 に変数「基準日付」．「月末日」を設定
        const endOfMonth: number = getDaysInMonth(baseDate);

        // 変数「基準日付」．「日(dd)」に変数「月末日」を設定
        baseDate.setDate(endOfMonth);

        // 変数「基準日付」を文字列(yyyy/MM/dd)に変換
        const ret = format(baseDate, "yyyy/MM/dd");

        return ret;
    }

    /**
     * 基準日と同年同月の月初日を取得する
     * @param baseTime 基準日(yyyy/MM/dd)
     * @return 基準日付
     * @throws Exception 想定外エラー
     */
    public static getFirstOfMonth(baseTime: string): string {

        // 変数「基準日付」に、入力パラメータ．「基準日」をCalendar型に変換して設定
        const baseDate: Date = this.parseStrToCal(baseTime);

        // 変数「月初日」 に変数「基準日付」．「月初日」を設定
        // int firstOfMonth = baseDate.getActualMinimum(Calendar.DAY_OF_MONTH);
        const firstOfMonth: number = 1;

        // 変数「基準日付」．「日(dd)」に変数「月初日」を設定
        baseDate.setDate(firstOfMonth);

        // 変数「基準日付」を文字列(yyyy/MM/dd)に変換
        const ret = format(baseDate, "yyyy/MM/dd");

        return ret;
    }

    /**
     * 対象日が月末日であるかを判定する
     * @param targetTime 対象日(yyyy/MM/dd)
     * @param baseTime 基準日(yyyy/MM/dd)
     * @return チェック結果
     * @throws Exception 想定外エラー
     */
    public static checkIsEndOfMonth(targetTime: string, baseTime: string): boolean {
        // 月末日の取得
        const endOfMonth: string = this.getEndOfMonth(baseTime);

        // 対象日と月末日の比較
        if (endOfMonth.equals(targetTime)) {
            return true;
        }

        return false;
    }

    /**
     * 対象日が月初日であるかを判定する
     * @param targetTime 対象日(yyyy/MM/dd)
     * @return チェック結果
     * @throws Exception 想定外エラー
     */
    public static checkIsFirstOfMonth(targetTime: string): boolean {
        // 月初日の取得
        const firstOfMonth: string = this.getFirstOfMonth(targetTime);

        // 対象日と月初日の比較
        if (firstOfMonth.equals(targetTime)) {
            return true;
        }

        return false;
    }

    /**
     * チェック項目が指定した最大桁数(符号除く)以内の負の数値(半角数値)であるかどうかチェックする
     * @param checkString チェック項目
     * @param maxLength 最大桁数
     * @return チェック結果
     */
    public static checkIsNegativeNum(checkString: string, maxLength: number): boolean {
        // 正規表現パターン
        const pattern: string = "^-[0-9]{1," + maxLength + "}$";

        // 入力パラメータ．「チェック項目」が変数「正規表現パターン」にマッチする
        return new RegExp(pattern).test(checkString);
    }

    /**
     * データ譲渡不可時間帯のフォーマットチェックを行う
     * @param dataGiftNgTime データ譲渡不可時間帯
     * @return チェック結果
     */
    public static checkDataGiftNgTimeFormat(dataGiftNgTime: string): boolean {
        // 入力パラメータに対して、データ譲渡不可時間帯必須チェックを行う
        if (CheckUtil.checkIsNotNull(dataGiftNgTime)) {
            return false;
        }

        // データ譲渡不可時間帯のフォーマットチェック
        // 入力パラメータ．「データ譲渡不可時間帯」 = "ALL"の場合
        if (Check.CONST_ALL.equals(dataGiftNgTime)) {
            return true;
        }
        // フォーマットが「H:mm-H:mm」以外の場合
        if (dataGiftNgTime.split("-").length !== 2) {
            return false;
        }

        // 変数「開始時刻」 = 入力パラメータ．「プラン変更不可時間帯」の”-”前の文字列
        const startTime: string = dataGiftNgTime.split("-")[0];
        // 変数「終了時刻」 = 入力パラメータ．「プラン変更不可時間帯」の”-”後の文字列
        const endTime: string = dataGiftNgTime.split("-")[1];

        // 開始時刻チェックを行う
        if (!CheckUtil.checkTimeFmt(startTime, "H:mm")) {
            return false;
        }

        // 終了時刻チェックを行う
        if (!CheckUtil.checkTimeFmt(endTime, "H:mm")) {
            return false;
        }

        // 開始時刻と終了時刻を比較する
        if (parseInt(startTime.replace(":", ""), 10) < parseInt(endTime.replace(":", ""), 10)) {
            return true;
        }

        return false;
    }

    /**
     * データ譲渡が可能な時間帯であるかをチェックする
     * @param targetTime 基準日時(yyyy/MM/dd HH:mm:ss)
     * @param dataGiftNgTime データ譲渡不可時間帯(「H:mm-H:mm」形式または「ALL」)
     * @param dataGiftNgFlag データ譲渡不可フラグ
     * @return チェック結果
     * @throws Exception 想定外エラー
     */
    public static checkDataGiftNgTime(targetTime: string, dataGiftNgTime: string, dataGiftNgFlag: string): boolean
    {
        // Date型に変換
        const tagDate = MvnoUtil.convDateFormat(targetTime);

        // 月初日かどうかの判定
        const firstOfMonthFlg = this.checkIsFirstOfMonth(format(tagDate, "yyyy/MM/dd"));

        // 入力パラメータ．「データ譲渡不可時間帯」 = "ALL"の場合
        if (Check.CONST_ALL.equals(dataGiftNgTime)) {
            // 入力パラメータ．「データ譲渡不可フラグ」 の値で判定
            if ("1".equals(dataGiftNgFlag)) {
                return false;
            } else {
                // 変数「月初フラグ」 の値で判定
                if (firstOfMonthFlg) {
                    return false;
                }
                return true;
            }
            // 入力パラメータ．「データ譲渡不可時間帯」 = "ALL"以外の場合
        } else {
            // 入力パラメータ．「データ譲渡不可フラグ」と変数「月初フラグ」 の値で判定
            if ("0".equals(dataGiftNgFlag) && !firstOfMonthFlg) {
                return true;
            }
            // 時間帯範囲チェックを実施する
            if (this.checkTimeSpanOutHM(format(tagDate, "H:mm"), dataGiftNgTime)) {
                return true;
            }
            return false;
        }
    }

    /**
     * データ譲渡TPC取得情報のフォーマットをチェックする
     * @param basicCap 基本bucket容量
     * @param basicUse 基本bucket使用量
     * @param giftCap1 譲渡bucket1容量
     * @param giftExpDate1 譲渡bucket1有効期限
     * @param giftCap0 譲渡bucket0容量
     * @param giftExpDate0 譲渡bucket0有効期限
     * @return 結果Map
     */
    // public static TpcDataCheckResultDto checkLineGiftTpcData(String basicCap, String basicUse,
    // String giftCap1, String giftExpDate1, String giftCap0, String giftExpDate0) {

    public static checkLineGiftTpcData(
        basicCap: string,
        basicUse: string,
        giftCap1: string,
        giftExpDate1: string,
        giftCap0: string,
        giftExpDate0: string,
    ) {
        // データ譲渡TPC取得情報のフォーマットチェック結果Dtoクラス
        const tpcDataResult = new TpcDataCheckResultDto();

        // 基本bucket容量フォーマットチェック
        // 基本bucket容量のフォーマットチェックを行う
        // STEP10.0版対応 追加 START
        // 基本bucket容量フォーマットチェックの実施要否を判定
        if (basicCap != null &&  !"".equals(basicCap)) {
            // STEP10.0版対応 追加 END
            if (!CheckUtil.checkLength(basicCap, 20, false) || !CheckUtil.checkIsNum(basicCap)) {
                // 変数「結果Map」．「チェック結果」 = false を設定
                tpcDataResult.setResult(false);

                // 変数「結果Map」．「変数名」 = ”基本bucket容量” を設定
                tpcDataResult.setErrName("基本bucket容量");

                // 変数「結果Map」．「変数値」 = 入力パラメータ．「基本bucket容量」 を設定
                tpcDataResult.setErrVal(basicCap);

                // 変数「結果Map」を返却し、処理終了
                return tpcDataResult;
            }
        }

        // 基本bucket使用量フォーマットチェック
        // 基本bucket使用量のフォーマットチェックを行う
        // STEP10.0版対応 追加 START
        // 基本bucket使用量フォーマットチェックの実施要否を判定
        if (basicUse != null &&  !"".equals(basicUse)) {
            // STEP10.0版対応 追加 END
            if (!CheckUtil.checkLength(basicUse, 20, false) || !CheckUtil.checkIsNum(basicUse)) {
                // 変数「結果Map」．「チェック結果」 = false を設定
                tpcDataResult.setResult(false);

                // 変数「結果Map」．「変数名」 = ”基本bucket使用量” を設定
                tpcDataResult.setErrName("基本bucket使用量");

                // 変数「結果Map」．「変数値」 = 入力パラメータ．「基本bucket使用量」 を設定
                tpcDataResult.setErrVal(basicUse);

                // 変数「結果Map」を返却し、処理終了
                return tpcDataResult;
            }
        }

        // 譲渡bucket1容量フォーマットチェック
        // 譲渡bucket1容量が0であればチェックOKとする
        // STEP10.0版対応 追加 START
        // 譲渡bucket1容量フォーマットチェックの実施要否を判定
        if (giftCap1 != null &&  !"".equals(giftCap1)) {
            // STEP10.0版対応 追加 END
            if (!"0".equals(giftCap1)) {
                // 譲渡bucket1容量のフォーマットチェックを行う
                if (!this.checkIsNegativeNum(giftCap1, 15)) {
                    // 変数「結果Map」．「チェック結果」 = false を設定
                    tpcDataResult.setResult(false);

                    // 変数「結果Map」．「変数名」 = ”譲渡bucket1容量” を設定
                    tpcDataResult.setErrName("譲渡bucket1容量");

                    // 変数「結果Map」．「変数値」 = 入力パラメータ．「譲渡bucket1容量」 を設定
                    tpcDataResult.setErrVal(giftCap1);

                    // 変数「結果Map」を返却し、処理終了
                    return tpcDataResult;
                }
            }
        }

        // 譲渡bucket1有効期限フォーマットチェック
        // 譲渡bucket1有効期限フォーマットチェックの実施要否を判定
        if (giftExpDate1 != null &&  !"".equals(giftExpDate1)) {
            // 譲渡bucket1有効期限のフォーマットチェックを行う
            if (!CheckUtil.checkDateFmt(giftExpDate1, "yyyy/MM/dd")) {
                // 変数「結果Map」．「チェック結果」 = false を設定
                tpcDataResult.setResult(false);

                // 変数「結果Map」．「変数名」 = ”譲渡bucket1有効期限” を設定
                tpcDataResult.setErrName("譲渡bucket1有効期限");

                // 変数「結果Map」．「変数値」 = 入力パラメータ．「譲渡bucket1有効期限」 を設定
                tpcDataResult.setErrVal(giftExpDate1);

                // 変数「結果Map」を返却し、処理終了
                return tpcDataResult;
            }
        }

        // 譲渡bucket0容量フォーマットチェック
        // 譲渡bucket0容量フォーマットチェックの実施要否を判定
        if (giftCap0 != null &&  !"".equals(giftCap0)) {
            // 譲渡bucket0容量のフォーマットチェックを行う
            if (!CheckUtil.checkLength(giftCap0, 15, false) || !CheckUtil.checkIsNum(giftCap0)) {
                // 変数「結果Map」．「チェック結果」 = false を設定
                tpcDataResult.setResult(false);

                // 変数「結果Map」．「変数名」 = ”譲渡bucket0容量” を設定
                tpcDataResult.setErrName("譲渡bucket0容量");

                // 変数「結果Map」．「変数値」 = 入力パラメータ．「譲渡bucket0容量」 を設定
                tpcDataResult.setErrVal(giftCap0);

                // 変数「結果Map」を返却し、処理終了
                return tpcDataResult;
            }
        }

        // 譲渡bucket0有効期限フォーマットチェック
        // 譲渡bucket0有効期限フォーマットチェックの実施要否を判定
        if (giftExpDate0 != null &&  !"".equals(giftExpDate0)) {
            // 譲渡bucket0有効期限のフォーマットチェックを行う
            if (!CheckUtil.checkDateFmt(giftExpDate0, "yyyy/MM/dd")) {
                // 変数「結果Map」．「チェック結果」 = false を設定
                tpcDataResult.setResult(false);

                // 変数「結果Map」．「変数名」 = ”譲渡bucket0有効期限” を設定
                tpcDataResult.setErrName("譲渡bucket0有効期限");

                // 変数「結果Map」．「変数値」 = 入力パラメータ．「譲渡bucket0有効期限」 を設定
                tpcDataResult.setErrVal(giftExpDate0);

                // 変数「結果Map」を返却し、処理終了
                return tpcDataResult;
            }
        }

        // チェックOK
        // 変数「結果Map」．「チェック結果」 = true を設定
        tpcDataResult.setResult(true);

        // 変数「結果Map」を返却し、処理終了
        return tpcDataResult;
    }

    /**
     * String型⇒Calendar型への変換処理
     * @param str 変換前日付文字列
     * @return Calendar型オブジェクト
     * @throws ParseException 変換エラー
     */
    private static parseStrToCal(str: string): Date {
        return parse(str, "yyyy/MM/dd", new Date());
    }

    /**
     * 対象SO所属テナントIDの必須チェック、英数字チェック、桁数チェックを行う
     * @param tenatId テナントID
     * @return テナントIDフォーマットチェック結果
     */
    public static checkTargetTenatIdFmt(tenatId: string): boolean {
        // 「テナントID」の必須入力チェック
        if (CheckUtil.checkIsNotNull(tenatId)) {
            return false;
            // 「テナントID」の英数字チェック
        } else if (!CheckUtil.checkIsSemiangleAlphanumic(tenatId)) {
            return false;
            // 「テナントID」の桁数10チェック
        } else if (!CheckUtil.checkRangeLength(tenatId, 6, 10)) {
            return false;
        }
        return true;
    }

    /**
     * キャンセル不可期間フォーマットチェックを行う
     * @param ｃancelReservationDisableDays キャンセル不可期間
     * @return キャンセル不可期間フォーマットチェック結果
     */
    public static checkCancelReservationDisableDaysFmt(
        cancelReservationDisableDays: string,
    ): boolean {
        // 必須チェック
        if (CheckUtil.checkIsNotNull(cancelReservationDisableDays)) {
            return false;
            // 桁数チェック
        } else if (
            !CheckUtil.checkLength(cancelReservationDisableDays, 3, false)
        ) {
            return false;
            // 数値項目チェック
        } else if (!CheckUtil.checkIsNum(cancelReservationDisableDays)) {
            return false;
        }
        return true;
    }

    /** STEP2.0a版対応　追加　START */
    /**
     * 容量追加コードの必須チェック、桁数チェック、数値チェックを行う
     * @param capacityCode 容量追加コード
     * @return 容量追加コードフォーマットチェック結果
     */
    public static checkCsvCapacityCodeFmt(capacityCode: string): boolean {
        // 「容量追加コード」の必須入力チェック
        if (CheckUtil.checkIsNotNull(capacityCode)) {
            return false;
            // 「容量追加コード」の桁数5チェック
        } else if (!CheckUtil.checkLength(capacityCode, 5, true)) {
            return false;
            // 「容量追加コード」の半角英数字チェック
        } else if (!CheckUtil.checkIsSemiangleAlphanumic(capacityCode)) {
            return false;
        }
        return true;
    }

    public static checkOptionPlanTBanFmt(tban: string): boolean {
        // 「T番」の必須入力チェック
        if (CheckUtil.checkIsNotNull(tban)) {
            return false;
            // 「T番」の桁数8チェック
        } else if (!CheckUtil.checkLength(tban, 8, true)) {
            return false;
            // 「T番」の頭文字チェック
        } else if (tban.charAt(0) !== "T") {
            return false;
            // 「T番」の左から2桁目から取得した文字数値項目チェック
        } else if (!CheckUtil.checkIsNum(tban.substring(1))) {
            return false;
        }
        return true;
    }

    /**
     * キャンセル不可期間範囲チェックを行う
     * @param order_date キャンセル不可期間
     * @param reserve_date 予約日
     * @param order_date 投入日
     * @return キャンセル不可期間範囲チェック結果
     */
    public static checkCancelReservationDisableDays(
        cancelReservationDisableDays: string,
        reserve_date: string,
        order_date: string,
    ): boolean {
        try {
            // 予約日(時刻は切り捨てる)
            const reserveDateDay = reserve_date.substring(0, 11);
            const reserveDate = reserveDateDay + "22:00:00";
            // Date orderDateFormat = dateFormat.parse(order_date);
            const orderDateFormat = parse(
                order_date,
                "yyyy/MM/dd HH:mm:ss",
                new Date(),
            );
            if (!isValid(orderDateFormat)) {
                return false;
            }
            const day = +cancelReservationDisableDays;
            // 変数「キャンセル不可終了日」
            const reserveDateFormat = CheckUtil.addDay(
                parse(reserveDate, "yyyy/MM/dd HH:mm:ss", new Date()),
                -day,
            );
            if (!isValid(reserveDateFormat)) {
                return false;
            }
            // if (day == 0 || orderDateFormat.compareTo(reserveDateFormat) <=0) {
            if (
                day === 0 ||
                compareAsc(orderDateFormat, reserveDateFormat) <= 0
            ) {
                return true;
            } else {
                return false;
            }
        } catch (e: any) {
            return false;
        }
    }

    /**
     * N番フォーマットチェック
     * N番の桁数チェック、頭文字、数値チェックを行う
     * @param nNumber N番
     * @return N番フォーマットチェック結果
     */
    public static checkNNumberFmt(nNumber: string): boolean {
        // 「N番」の桁数11チェック
        if (!CheckUtil.checkLength(nNumber, 10, true)) {
            return false;
            // 「N番」頭文字=「N」のチェック
        } else if (nNumber.charAt(0) !== "N") {
            return false;
            // 「N番」の左から2桁目から取得した文字数値項目チェック
        } else if (!CheckUtil.checkIsNum(nNumber.substring(1))) {
            return false;
        }
        return true;
    }

    /**
     * 卸ポータルプランIDの桁数チェック、数値チェックを行う
     * @param {string|number} potalPlanID - 卸ポータルプランID
     * @return {boolean} 卸ポータルプランIDフォーマットチェック結果
     */
    public static checkPotalPlanID(potalPlanID: string): boolean {
        // 「卸ポータルプランID」の桁数5チェック
        if (!CheckUtil.checkLength(potalPlanID, 5, true)) {
            return false;
            // 「卸ポータルプランID」の数値項目チェック
        } else if (!CheckUtil.checkIsNum(potalPlanID)) {
            return false;
        }
        return true;
    }

    /**
     * 英数字型のID必須チェック、英数字チェック、桁数チェックを行う
     * @param id ID
     * @param length 桁数
     * @param koteikahenFlg 固定長／可変長フラグ
     * @return 英数字型のIDチェック結果
     */
    public static checkStrIdFmt(
        id: string,
        length: number,
        koteikahenFlg: boolean,
    ): boolean {
        // 「ID」の必須入力チェック
        if (CheckUtil.checkIsNotNull(id)) {
            return false;
            // 「ID」の桁数チェック
        } else if (!CheckUtil.checkLength(id, length, koteikahenFlg)) {
            return false;
            /** #451対応 削除 START */
            //        //「ID」の英数字チェック
            //        } else if (!CheckUtil.checkIsSemiangleAlphanumic(id)) {
            //            return false;
            /** #451対応 削除 END */
        }
        return true;
    }

    /**
     * 予約日フォーマットチェックを行う
     * @param {string} reserveDate - 予約日
     * @return {boolean} 予約日フォーマットチェック結果
     */
    public static checkReserveDateFmt(reserveDate: string): boolean {
        // 必須チェック
        if (CheckUtil.checkIsNotNull(reserveDate)) {
            return false;
            // 桁数チェック
        } else if (!CheckUtil.checkLength(reserveDate, 16, true)) {
            return false;
            // 日付フォーマットチェック
        } else if (!CheckUtil.checkDateFmt(reserveDate, "yyyy/MM/dd HH:mm")) {
            return false;
        }
        return true;
    }

    /**
     * 時間帯（h:mm-h:mm）フォーマットチェックを行う
     * @param {string} timeSpan - 時間帯（h:mm-h:mm）
     * @return {boolean} プラン変更不可時間帯フォーマットチェック結果
     */
    public static checkTimeSpanHMFmt(timeSpan: string): boolean {
        // 変数「開始時刻」
        let startTime = null;
        // 変数「終了時刻」
        let endTime = null;

        // 必須チェック
        if (CheckUtil.checkIsNotNull(timeSpan)) {
            return false;
        }

        // 時間帯を「-」で分割
        const splitTime = timeSpan.split("-");

        // フォーマット「h:mm-h:mm」チェック
        if (splitTime.length !== 2) {
            return false;
        }

        // 変数「開始時刻」 = 入力パラメータ．「時間帯」の”-”前の文字列
        startTime = splitTime[0];
        // 変数「終了時刻」 = 入力パラメータ．「時間帯」の”-”後の文字列
        endTime = splitTime[1];

        // // 開始時刻チェックを行う
        if (!CheckUtil.checkTimeFmt(startTime, "HH:mm")) {
            return false;
        }

        // 終了時刻チェックを行う
        if (!CheckUtil.checkTimeFmt(endTime, "HH:mm")) {
            return false;
        }

        // 開始時刻と終了時刻を比較する
        if (
            parseInt(startTime.replace(":", ""), 10) <=
            parseInt(endTime.replace(":", ""), 10)
        ) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 時間帯（h:mm-h:mm）範囲外チェックを行う
     * @param {string} timeHM - 基準時刻（hh:mm）
     * @param {string} targetTime - 時間帯（h:mm-h:mm）
     * @return {boolean} 時間帯範囲チェック結果 true--範囲外、false--範囲内
     */
    public static checkTimeSpanOutHM(
        timeHM: string,
        targetTime: string,
    ): boolean {
        // 時間帯を「-」で分割
        const splitTime = targetTime.split("-");
        // 変数「開始時刻」
        const startTime = splitTime[0];
        // 変数「終了時刻」
        const endTime = splitTime[1];

        // 時間帯の範囲チェック
        // パラメータ「時刻」＜変数「開始時刻」
        // 変数「開始時刻」=変数「終了時刻」の場合はチェック対象外
        if (
            parseInt(startTime.replace(":", ""), 10) ===
            parseInt(endTime.replace(":", ""), 10)
        ) {
            return true;
        }
        if (
            parseInt(timeHM.replace(":", ""), 10) <
            parseInt(startTime.replace(":", ""), 10)
        ) {
            return true;
            // パラメータ「時刻」＞＝変数「終了時刻」
        } else if (
            parseInt(timeHM.replace(":", ""), 10) >=
            parseInt(endTime.replace(":", ""), 10)
        ) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 当日からX日前の年月日取得
     * @param {number} preXDay - X日
     * @return {string} 当日のX日前年月日
     */
    public static getPreXDay(preXDay: number): string {
        // サーバからシステム日付を取得
        const sysdate = new Date();
        const lastdate = subDays(sysdate, preXDay);
        return format(lastdate, "yyyyMMdd");
    }

    /**
     * 卸ポータルグループプランIDフォーマットチェック(省略可)
     * @param potalGroupPlanID 卸ポータルグループプランID
     * @return 卸ポータルグループプランIDフォーマットチェック結果
     */
    public static checkGroupPlanIDFmt(potalGroupPlanID: string): boolean {
        // 「卸ポータルグループプランID」の桁数5チェック
        if (!CheckUtil.checkLength(potalGroupPlanID, 5, true)) {
            return false;
            // 「卸ポータルグループプランID」の数値項目チェック
        } else if (!CheckUtil.checkIsNum(potalGroupPlanID)) {
            return false;
        }
        return true;
    }

    /**
     * ポリシーIDフォーマットチェック
     * @param policyId ポリシーID
     * @return ポリシーIDフォーマットチェック結果
     */
    public static checkPolicyId(policyId: string): boolean {
        // 「policyId」の桁数1チェック
        if (!CheckUtil.checkLength(policyId, 3, true)) {
            return false;
            // 「policyId」の数値項目チェック
        } else if (!CheckUtil.checkIsNum(policyId)) {
            return false;
        }
        return true;
    }

    /**
     * 月数後末日取得
     * @param termValidityMonth 有効期限月数
     * @return 月数後末日
     */
    public static getMonthEndDay(termValidityMonth: string): string {
        // サーバからシステム日付を取得
        const sysdate = new Date();
        let lastdate: Date;
        const dateFormat = "yyMMdd";
        // 有効期限月数
        let termValidityDate: string;
        if (Check.CONST_ZERO.equals(termValidityMonth)) {
            lastdate = lastDayOfMonth(sysdate);
            termValidityDate = format(lastdate, dateFormat).toString();
            return termValidityDate;
        } else {
            // 有効期限月数
            const tVdityMonth: number = parseInt(termValidityMonth, 10);
            const calendar = addMonths(sysdate, tVdityMonth);
            lastdate = lastDayOfMonth(calendar);
            termValidityDate = format(lastdate, dateFormat).toString();
        }
        return termValidityDate;
    }

    /**
     * システム日付の当月の月末日を取得
     * @param date システム日付
     * @return システム日付の当月の月末日
     */         // there is exact same function in date-fns, no need to implement as it is private.
    // private static lastDayOfMonth(date: Date): Date {
    // }

    /**
     * 回線グループIDの必須チェック、桁数チェック、数値チェックを行う
     * @param {type} lineGroupId 回線グループID
     * @return {type} 回線グループIDフォーマットチェック結果
     */
    public static checkLineGroupId(lineGroupId: string): boolean {
        // 「回線グループID」の数値項目チェック
        if (CheckUtil.checkIsNotNull(lineGroupId)) {
            return false;
            /** STEP4.0版対応 変更 START */
            // 「回線番号」の桁数9チェック
        } else if (!CheckUtil.checkLength(lineGroupId, 9, false)) {
            /** STEP4.0版対応 変更 END */
            return false;
            // 「回線番号」の数値項目チェック
        } else if (!CheckUtil.checkIsNum(lineGroupId)) {
            return false;
        }
        return true;
    }

    /**
     * 卸ポータルグループプランIDフォーマットチェック
     * @param potalGroupPlanID 卸ポータルグループプランID
     * @return 卸ポータルグループプランIDフォーマットチェック結果
     */
    public static checkPlanIDFmt(potalGroupPlanID: string): boolean {
        // 「卸ポータルグループプランID」の数値項目チェック
        if (CheckUtil.checkIsNotNull(potalGroupPlanID)) {
            return false;
            // 「卸ポータルグループプランID」の桁数5チェック
        } else if (!CheckUtil.checkLength(potalGroupPlanID, 5, true)) {
            return false;
            // 「卸ポータルグループプランID」の数値項目チェック
        } else if (!CheckUtil.checkIsNum(potalGroupPlanID)) {
            return false;
        }
        return true;
    }

    /**
     * クーポンOFF時の通信量要否のフォーマットチェックを行う
     * @param {boolean} trafficOfCouponOffFlag - クーポンOFF時の通信量要否
     * @return {boolean} クーポンOFF時の通信量要否フォーマットチェック結果
     */
    public static checkTrafficOfCouponOffFlag(
        trafficOfCouponOffFlag: string,
    ): boolean {
        return trafficOfCouponOffFlag === "1" || trafficOfCouponOffFlag === "0";
    }

    /**
     * プラン変更不可時間帯フォーマットチェックを行う
     * @param planChangeNgTime プラン変更不可時間帯
     * @return プラン変更不可時間帯フォーマットチェック結果
     */
    public static checkPlanChangeNgTimeFmt(planChangeNgTime: string): boolean {
        // 変数「開始時刻」
        let startTime: string = null;
        // 変数「終了時刻」
        let endTime: string = null;

        // 入力パラメータに対して、プラン変更不可時間帯必須チェックを行う
        if (CheckUtil.checkIsNotNull(planChangeNgTime)) {
            return false;
        }

        // フォーマットが「h:mm-h:mm」である場合
        if (planChangeNgTime.split("-").length !== 2) {
            return false;
        }

        // 変数「開始時刻」 = 入力パラメータ．「プラン変更不可時間帯」の”-”前の文字列
        startTime = planChangeNgTime.split("-")[0];
        // 変数「終了時刻」 = 入力パラメータ．「プラン変更不可時間帯」の”-”後の文字列
        endTime = planChangeNgTime.split("-")[1];

        // 開始時刻チェックを行う
        if (!CheckUtil.checkTimeFmt(startTime, "HH:mm")) {
            return false;
        }

        // 終了時刻チェックを行う
        if (!CheckUtil.checkTimeFmt(endTime, "HH:mm")) {
            return false;
        }

        // 開始時刻と終了時刻を比較する
        if (
            parseInt(startTime.replace(":", ""), 10) <
            parseInt(endTime.replace(":", ""), 10)
        ) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * プラン変更不可時間帯範囲チェックを行う
     * @param planChangeNgTime プラン変更不可時間帯
     * @return プラン変更不可時間帯範囲チェック結果
     */
    public static checkPlanChangeNgTime(
        time: string,
        planChangeNgTime: string,
    ): boolean {
        // 変数「開始時刻」
        const startTime = planChangeNgTime.split("-")[0];
        // 変数「終了時刻」
        const endTime = planChangeNgTime.split("-")[1];
        // 変数「時刻」
        const timeHM = time.substring(11, 16);

        // プラン変更不可時間帯の範囲チェック
        // 変数「時刻」＜変数「開始時刻」
        if (
            parseInt(timeHM.replace(":", ""), 10) <
            parseInt(startTime.replace(":", ""), 10)
        ) {
            return true;
            // 変数「時刻」＞＝変数「終了時刻」
        } else if (
            parseInt(timeHM.replace(":", ""), 10) >=
            parseInt(endTime.replace(":", ""), 10)
        ) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * クーポンON OFF状態フォーマットチェック
     * @param couponOnOff クーポンON OFF状態
     * @return クーポンON OFF状態フォーマットチェック結果
     */
    public static checkCouponOnOff(couponOnOff: string): boolean{
        // 「クーポンON OFF」の数値項目チェック
		if (CheckUtil.checkIsNotNull(couponOnOff)) {
			return false;
			// 「クーポンON OFF」の桁数1チェック
		} else if (!CheckUtil.checkLength(couponOnOff, 1, true)) {
			return false;
			// 「クーポンON OFF」の数値項目チェック
		} else if (!CheckUtil.checkIsNum(couponOnOff)) {
			return false;
			// （1:ON/0:OFF）
		} else if (!"1".equals(couponOnOff) && !"0".equals(couponOnOff)) {
			return false;
		}
		return true;
    }

    /**
     * CSVプランIDフォーマットチェックを行う
     * @param planId プランID
     * @return CSVプランIDフォーマットチェック結果
     */
    public static checkCsvplanIdFmt(planId: string): boolean {
        // 「プランID」の必須入力チェック
		if (CheckUtil.checkIsNotNull(planId)) {
			return false;
			// 「プランID」の桁数5チェック
		} else if (!CheckUtil.checkLength(planId, 5, false)) {
			return false;
			// 「プランID」の数値項目チェック
		} else if (!CheckUtil.checkIsNum(planId)) {
			return false;
		}
		return true;
    }
    /**
     * 予約中オーダあるかチェックを行う
     * @param lineNo 回線番号
     * @return 予約中オーダあるかチェック結果
     * @throws SQLException
     */
    public static async checkReserveOrder(lineNo: string, aPISoDAO: APISoDAO): Promise<boolean> {
        // 変数「予約中オーダ」
		let reserveOrderList: string[] = null;
        try{
            // 予約中オーダがあるかチェックを行う
            reserveOrderList = await aPISoDAO.getReserveOrder(lineNo);
        }catch(e){
            throw e;
        }

        // 変数「予約中オーダ」より判定する
		// 変数「予約中オーダ」の件数<>0
        if (reserveOrderList.length > 0) {
            return false;
        }
        return true;
    }

    /**
     * CSV料金プランIDフォーマットチェックを行う
     * @param planId 料金プランID
     * @return CSV料金プランIDフォーマットチェック結果
     */
    public static checkCsvResalePlanIdFmt(planId: string): boolean {
        // 「料金プランID」の必須入力チェック
		if (CheckUtil.checkIsNotNull(planId)) {
			return false;
			// 「料金プランID」の桁数5チェック
		} else if (!CheckUtil.checkLength(planId, 5, false)) {
			return false;
			// 「料金プランID」の半角英数字チェック
		} else if (!CheckUtil.checkIsSemiangleAlphanumic(planId)) {
			return false;
		}
		return true;
    }
}
