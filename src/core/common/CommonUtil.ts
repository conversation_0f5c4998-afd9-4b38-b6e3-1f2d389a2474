import * as crypto from 'crypto';
import md2 from "js-md2";
import StringUtils from './StringUtils';
import { format } from "date-fns";


/**
 * CommonUtilクラス<BR>
 *
 * @version 1.00 新規<BR>
 */
export default class CommonUtil {

    /**
     * 文字列（半角英字大文字）
     */
	private static readonly STR_ABC_L = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    /**
     * 文字列（半角英字小文字）
     */
	private static readonly STR_ABC_S = "abcdefghijklmnopqrstuvwxyz";

    /**
     * 文字列（半角数値）
     */
	private static readonly STR_NUMBER  = "0123456789";

    /**
     * システム日時を取得（yyyy/MM/dd HH:mm:ss）
     *
     * @return String　フォーマット後日時
     */
    public static getDateTimeNow(): string {
		return format(new Date(), "yyyy/MM/dd HH:mm:ss");
    }

    /**
     * データ型変換
     *
     * @param strDate 日時文字列
     * @return timeStamp
     */
    public static convDateFormat(strDate: string): Date | null {
        if (StringUtils.isBlank(strDate)) {
            return null;
        }
		try {
			const date = new Date(strDate);
			if (isNaN(date.getTime())) {
				return null;
			}
			return date;
		} catch (e) {
			return null;
		}
    }

    /**
     * データ型変換（Date型⇒String型）
     *
     * @param date 日付情報
     * @return String型(yyyy/MM/dd 00:00:00形式)
     */
	public static convDateToString(date: Date | null): string | null {
		if (date == null) {
			return null;
		}
		const str = format(date, "yyyy/MM/dd 00:00:00");
		return str;
	}

    /**
     * データ型変換（Date型⇒String型）
     *
     * @param date 日付情報
     * @return String型(yyyy/MM/dd HH:mm:ss形式)
     */
	public static convDateTimeToString(date: Date | null): string | null {
		if (date == null) {
			return null;
		}
		const str = format(date, "yyyy/MM/dd HH:mm:ss");
		return str;
	}


	/**
	 * 文字種のチェック<BR>
	 *
	 * ＜文字種＞英数字：「a-zA-Z0-9」であれば、OKとし、trueを返却する。<BR>
	 * それ以外、falseを返却する。<BR>
	 *
	 * @param str  チェック対象文字列
	 * @return 判定結果
	 */
	public static chkStrType(str: string | null): boolean {
		if (str == null) {
			return false;
		}
		const checkItemType = [CommonUtil.STR_ABC_L, CommonUtil.STR_ABC_S, CommonUtil.STR_NUMBER];
		if (!CommonUtil.propertiesCheck(str, checkItemType)) {
			return false;
		}
		return true;
	}

	/**
	 * 文字列属性チェック
	 *
	 * @param inputStr チェック対象
	 * @param inputCheckStr チェックアイテム
	 * @return 判定結果
	 */
	private static propertiesCheck(inputStr: string, inputCheckStr: string[]): boolean {
		let checkStr = "";

		if (inputCheckStr != null && inputCheckStr.length > 0) {
			for (const tmpString of inputCheckStr) {
				checkStr = checkStr + tmpString;
			}
		}

		// チェックアイテムが対象文字列に含まれているかをチェック
		let index = 0;
		for (let i = 0; i < checkStr.length; i++) {
			index = 0;
			// 対象文字列の検索
			while (index !== -1) {
				index = inputStr.indexOf(checkStr.charAt(i));
				if (index !== -1) { // 対象文字が見つかった。
					if (index > 0) {
						// 削除対象位置が>0の場合
						inputStr = inputStr.substring(0, index) + inputStr.substring(index + 1);
					} else if (index === 0) {
						// 削除対象位置が=0の場合
						inputStr = inputStr.substring(1);
					} else {
						// 削除対象位置が無効の場合は処理エラー
						throw new Error("StringIndexOutOfBoundsException");
					}
				}
			}
		}
		return inputStr.length === 0;
	}

    /**
     * 文字列のハッシュ化
     * @param sequenceNo ハッシュ化対象
     * @return ハッシュ化後文字列
     * @throws NoSuchAlgorithmException 処理例外
     */
	public static digest(sequenceNo: string): string {
		try {
			const seqByte = Buffer.from(md2(sequenceNo), 'hex');			// md2 string -> byte
			let sb = "";
			const shiftByte = 4;
			const padHex = 0x0f;

			// byte -> string
			for (let i = 0; i < seqByte.length; i++) {
				// tslint:disable-next-line:no-bitwise
				sb += ((seqByte[i] >> shiftByte) & padHex).toString(16); // 上位ニブルを1文字へ
				// tslint:disable-next-line:no-bitwise
				sb += (seqByte[i] & padHex).toString(16); // 下位ニブルを1文字へ
			}

			return sb;
		} catch (e) {
			throw new Error("NoSuchAlgorithmException: " + e.message);
		}
		// === return md2(sequenceNo);
	}
}
