export default class StringUtils {
    /**
     * Implementation of Java `String.isEmpty` method.
     * Checks if a String is empty ("") or null.
     * Should return false for spaces only string.
     */
    public static isEmpty(str: string | null | undefined): boolean {
        return !str || str.length === 0;
    }

    public static isNotEmpty(str: string): boolean {
        return !this.isEmpty(str);
    }

    /**
     * Implementation of Java `String.isBlank` method.
     * Checks if a String is whitespace, empty ("") or null.
     * Trim the string first before checking if it is blank.
     */
    public static isBlank(str: string | null | undefined): boolean {
        return !str || str.trim().length === 0;
    }

    public static isNotBlank(str: string): boolean {
        return !this.isBlank(str);
    }

    /**
     * Implementation of Java `StringUtils.trimToEmpty` method.
     * Trim the string and return an empty string if the input is null or undefined.
     */
    public static trimToEmpty(str: string | null | undefined): string {
        return (str || "").trim();
    }

    /**
     * Implementation of Java `StringUtils.equals` method.
     * Compare two strings, return true if they represent the same sequence of characters
     * (*case-sensitive*).
     *
     * **NOTE**: since `undefined` does not exist in Java, `undefined` will be treated as `null`.
     * Two `null`s are considered to be equal.
     * @param str1
     * @param str2
     */
    public static equals(
        str1: string | null | undefined,
        str2: string | null | undefined,
    ): boolean {
        return (
            str1 === str2 ||
            ((str1 === undefined || str1 === null) &&
                (str2 === undefined || str2 === null))
        );
    }
}
