import APICommonDAO from "@/core/dao/APICommonDAO";
import { isSQLException } from "@/helpers/queryHelper";
import AbstractMvnoBaseCommon from "./AbstractMvnoBaseCommon";
import { LinesEntity } from "../entity/LinesEntity";
import LineTenantsEntity from "../entity/LineTenantsEntity";
import { TenantNnumbersEntity } from "../entity/TenantNnumbersEntity";
import { TenantsEntity } from "../entity/TenantsEntity";
import StringUtils from "./StringUtils";
import MvnoUtil from "./MvnoUtil";

/**
 * テナント管理クラス。<BR>
 * <pre>
 * 回線はテナントに所属するかを判断する
 * </pre>
 */
export default class TenantManage extends AbstractMvnoBaseCommon {
    // NOTE extended AbstractMvnoBaseCommon for initializing APICommonDAO

    /**
     * API共通DAO
     */
    private aPICommonDAO: APICommonDAO = new APICommonDAO(
        this.request,
        this.context,
    );

    /**
     * 回線はテナントに所属するかの判断機能である。
     * <pre>
     * 下記三つ判断をすると、結果が出る。一つ目結果（true：OK、false：NG）二つ目結果（代表N番）
     * ・回線テナント判定
     * ・代表N番判定
     * ・再販料金プラン判定
     * </pre>
     * @param lineId 回線ID
     * @param tenantId テナントID
     * @return boolean true：OK、false：NG;代表N番
     */
    public async doCheck(
        lineId: string,
        tenantId: string,
    ): Promise<[boolean, string]> {
        this.context.debug("TenantManage.doCheck", { lineId, tenantId });
        // NOTE changed first element type to boolean instead of string
        // because checking boolean value from string in JS is error-prone
        const checkResult: [boolean, string] = [false, ""];

        // テナントIDチェック
        // テナントIDが有効であることを確認し、併せて社内フラグを取得する。
        const tenantsEntity = await this.aPICommonDAO.getTenantsEntity(
            tenantId,
        );
        // テナントID、社内フラグが取得できない場合は処理を中断して、falseを返却する。
        if (tenantsEntity === null) {
            this.context.debug("TenantManage.doCheck tenant not found [ng]");
            checkResult[0] = false;
            return checkResult;
        }

        let linesEntity: LinesEntity = null;
        let nNumberList: string[] = [];

        // 回線テナント判定
        // テナントIDが回線テナントに存在することを確認する
        const recordCount = await this.aPICommonDAO.getLineTenantCount(
            tenantId,
            lineId,
        );
        // 1件取得できた場合
        if (1 === recordCount) {
            // 回線IDが回線情報に存在することを確認する。
            const linesEntity = await this.aPICommonDAO.getLineID1(lineId);
            // 取得できた場合
            if (linesEntity !== null) {
                this.context.debug("TenantManage.doCheck line_tenants count = 1, lineEntity found [ok]");
                checkResult[0] = true;
                // 代表N番に値が設定されていない場合、代表N番はnullとする。
                // checkResult[1] = "".equals(StringUtils.trimToEmpty(linesEntity.getNnumber())) ? null : linesEntity.getNnumber();
                checkResult[1] =
                    StringUtils.trimToEmpty(linesEntity.nnumber) === ""
                        ? null
                        : linesEntity.nnumber;
                return checkResult;
            }
        }

        // 代表N番判定
        // テナントIDでテナントN番を取得する。
        nNumberList = await this.aPICommonDAO.getNNumber(tenantId);
        // 1件以上取得できた場合
        if (nNumberList.length > 0) {
            // 回線IDとN番が回線情報に存在することを確認する。
            linesEntity = await this.aPICommonDAO.getLineID2(
                lineId,
                nNumberList,
            );
            // 取得できた場合
            if (linesEntity !== null) {
                this.context.debug("TenantManage.doCheck tenant nnumber found & lineEntity found [ok]");
                checkResult[0] = true;
                // 代表N番に値が設定されていない場合、代表N番はnullとする。
                // checkResult[1] = "".equals(StringUtils.trimToEmpty(linesEntity.getNnumber())) ? null : linesEntity.getNnumber();
                checkResult[1] =
                    StringUtils.trimToEmpty(linesEntity.nnumber) === ""
                        ? null
                        : linesEntity.nnumber;
                return checkResult;
            }
        }

        // 社内フラグfalseの場合
        if (!tenantsEntity.office) {
            this.context.debug("TenantManage.doCheck tenant shanai flag=false [ng]");
            checkResult[0] = false;
            return checkResult;
        }

        // 再販料金プラン判定
        // 回線情報から再販料金プランを取得する。
        linesEntity = await this.aPICommonDAO.getResalePlanID(lineId);
        if (linesEntity === null) {
            this.context.debug("TenantManage.doCheck resale plan not found [ng]");
            checkResult[0] = false;
            return checkResult;
        } else {
            // プランからプランIDを取得する。
            const planIdList = await this.aPICommonDAO.getPlanID(
                linesEntity.pricePlanId,
            );
            if (planIdList.length !== 1) {
                this.context.debug(
                    "TenantManage.doCheck planIdList length is not 1 [ng]",
                    linesEntity?.pricePlanId,
                );
                checkResult[0] = false;
                return checkResult;
            } else {
                // テナントプランからテナントIDを取得する。
                const tenantIdList = await this.aPICommonDAO.getTenantID2(
                    +planIdList[0],
                    tenantId,
                );
                if (tenantIdList.length !== 1) {
                    this.context.debug(
                        "TenantManage.doCheck tenant-plan relation not found [ng]",
                        tenantId,
                        +planIdList[0],
                    );
                    checkResult[0] = false;
                    return checkResult;
                }
            }
            this.context.debug("TenantManage.doCheck use nnumber from lines (default=empty) [ok]");
            // 代表N番に値が設定されていない場合、代表N番はnullとする。
            // checkResult[1] = "".equals(StringUtils.trimToEmpty(linesEntity.getNnumber())) ? null : linesEntity.getNnumber();
            checkResult[1] =
                StringUtils.trimToEmpty(linesEntity.nnumber) === ""
                    ? null
                    : linesEntity.nnumber;
        }
        checkResult[0] = true;
        return checkResult;
    }

    /**
     * 指定された回線が一意のテナントに所属するかを判定する。
     * <pre>
     * 下記三つ判断をすると、結果が出る。
     * 一つ目結果（true：OK、false：NG）二つ目結果（テナントID）
     * ・回線テナント判定
     * ・代表N番判定
     * ・再販料金プラン判定
     * </pre>
     * @param lineId 回線ID
     * @return boolean true：OK、false：NG;テナントID
     */
    public async getCheckedTenantId(
        lineId: string,
    ): Promise<[boolean, string]> {
        // NOTE changed first element type to boolean instead of string
        // because checking boolean value from string in JS is error-prone

        // 変数「回線テナント」を定義する
        let lineTenantsEntityList: LineTenantsEntity[] = [];
        // 変数「回線情報」を定義する
        let linesEntity: LinesEntity = null;
        // 変数「テナントN番リスト」を定義する
        let tenantNnumbersEntityList: TenantNnumbersEntity[] = [];
        // 変数「プランIDリスト」を定義する
        let planIdList: string[] = [];
        // 変数「テナントプランリスト」を定義する
        let tenantIdList: string[] = [];
        // 変数「テナント」を定義する
        let tenantsEntity: TenantsEntity = null;

        // // 処理結果返却
        const checkResult: [boolean, string] = [false, ""];

        // STEP20.0版対応　変更　START
        try {
            // // 回線テナント判定
            // // テナントIDが回線テナントに存在することを確認する。
            // lineTenantsEntityList = aPICommonDAO.getLineTenantsEntity(lineId);
            lineTenantsEntityList =
                await this.aPICommonDAO.getLineTenantsEntity(lineId);
            // 1件取得できた場合
            if (
                Array.isArray(lineTenantsEntityList) &&
                lineTenantsEntityList.length === 1
            ) {
                // 回線IDが回線情報に存在することを確認する。
                // 	linesEntity = aPICommonDAO.getLineID1(lineId);
                linesEntity = await this.aPICommonDAO.getLineID1(lineId);
                // 取得できた場合
                if (linesEntity !== null) {
                    checkResult[0] = true;
                    // テナントIDを設定する。
                    checkResult[1] = lineTenantsEntityList[0].tenantId;
                    return checkResult;
                }
            }

            // 代表N番判定
            // 回線IDで代表N番を取得する。
            // linesEntity = aPICommonDAO.getLineID1(lineId);
            linesEntity = await this.aPICommonDAO.getLineID1(lineId);
            // 1件以上取得できた場合
            if (linesEntity !== null) {
                // 代表N番でテナントN番を取得する。
                // 	tenantNnumbersEntityList = aPICommonDAO.getTenantNnumbersEntity(linesEntity.getNnumber());
                tenantNnumbersEntityList =
                    await this.aPICommonDAO.getTenantNnumbersEntity(
                        linesEntity.nnumber,
                    );
                // 取得できた場合
                if (
                    tenantNnumbersEntityList !== null &&
                    tenantNnumbersEntityList.length === 1
                ) {
                    checkResult[0] = true;
                    // テナントIDを設定する。

                    checkResult[1] = tenantNnumbersEntityList[0].tenantId;
                    return checkResult;
                }
            }

            // 再販料金プラン判定
            // 回線情報から再販料金プランを取得する。
            linesEntity = await this.aPICommonDAO.getResalePlanID(lineId);
            if (linesEntity !== null) {
                // プランからプランIDを取得する。
                planIdList = await this.aPICommonDAO.getPlanID(
                    linesEntity.planId,
                );

                if (planIdList !== null && planIdList.length === 1) {
                    // テナントプランからテナントIDを取得する。
                    tenantIdList =
                        await this.aPICommonDAO.getTenantIdFromTenantPlans(
                            +planIdList[0],
                        );
                    if (tenantIdList !== null && tenantIdList.length === 1) {
                        // テナントIDから社内フラグを取得する。
                        tenantsEntity =
                            await this.aPICommonDAO.getTenantsEntity(
                                tenantIdList[0],
                            );
                        if (tenantsEntity !== null && tenantsEntity.office) {
                            checkResult[0] = true;
                            // テナントIDを設定する。
                            checkResult[1] = tenantsEntity.tenantId;
                            return checkResult;
                        }
                    }
                }
            }
        } catch (e) {
            if (isSQLException(e)) {
                // / DBアクセスエラーとなった場合
                checkResult[0] = false;
                return checkResult;
            }
            throw e; // don't catch other errors
        }

        checkResult[0] = false;
        return checkResult;
    }

    /**
     * 複数存在するTPCのうち、どのTPCにSOAP電文を発行するのかを判定する。
     * @param tenantId テナントID
     * @return boolean true：TPC情報、false：NG
     * @throws SQLException
     */
    public async checkTpcConnection(
        tenantId: string,
    ): Promise<[boolean, string, string]> {
        // NOTE changed first element type to boolean instead of string
        // because checking boolean value from string in JS is error-prone
        // 変数「テナント」を定義する
        let tenantsEntity: TenantsEntity = null;
        // 処理結果返却
        const checkResult: [boolean, string, string] = [false, "", ""];

        try {
            // 指定されたテナントIDをもとに、TPC情報を取得する。
            tenantsEntity = await this.aPICommonDAO.getTenantsEntity(tenantId);
            // TPC情報が取得できない場合は処理を中断して、falseを返却する。
            if (
                tenantsEntity === null ||
                StringUtils.isEmpty(tenantsEntity.tpc)
            ) {
                checkResult[0] = false;
                return checkResult;
            }
        } catch (e) {
            throw e;
        }

        // TPC情報を取得できた場合はtrueとTPC情報を返却する。
        checkResult[0] = true;
        checkResult[1] = tenantsEntity.tpc;
        checkResult[2] = tenantsEntity.tpc2;

        return checkResult;
    }
}
