import APICommonDAO from "../dao/APICommonDAO";
import AbstractMvnoBaseCommon from "./AbstractMvnoBaseCommon";

/**
 * API共通処理クラス。<BR>
 * <pre>
 * REST-APIで共通して使用する
 * </pre>
 */
export default class ApiCommon extends AbstractMvnoBaseCommon {
    private apiCommonDAO = new APICommonDAO(this.request, this.context);

    /**
     * 対象回線に対する廃止オーダが投入されていないかをチェックする
     * @param lineNo 回線番号
     * @param targetDate 基準日
     * @return 回線廃止オーダチェック結果 true：投入されていない false：投入されている
     */
    public async checkAbolishSo(
        lineNo: string,
        targetDate: string,
    ): Promise<boolean> {
        // NOTE step21: since SQLException will be thrown anyway, no need for try-catch
        const abolitionOrderList =
            await this.apiCommonDAO.getCommonAbolitionOrder(lineNo, targetDate);
        return abolitionOrderList.length === 0;
    }

    /**
     * 対象回線に対する利用状態がサスペンド中でないかチェックする
     * @param lineNo 回線番号
     * @return サスペンド中チェック結果
     *           true：ライトMVNO回線 もしくは、サスペンド中以外の回線
     *           false：サスペンド中の回線
     * @throws SQLException
     */
    public async checkLineSuspend(lineNo: string): Promise<boolean> {
        // NOTE try/catch SQLException will be thrown anyway so not needed
        // 変数「該当回線情報リスト」
        let lineOrderList: number[] = [];

        // 利用状態のチェックを行う
        lineOrderList = await this.apiCommonDAO.getLineUsageStatus(lineNo);

        return lineOrderList.length === 0;
    }
}
