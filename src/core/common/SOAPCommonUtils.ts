import * as xmlbuilder from "xmlbuilder2";
import xpath from "xpath";
import { D<PERSON><PERSON>ars<PERSON> } from "xmldom";

import SOAPException from "@/types/soapException";

import Constants from "../constant/Constants";
import ParameterName from "../dto/ParameterName";

import MvnoUtil from "./MvnoUtil";
import { isNone } from "@/utils";

const CONST_SOAP_1_1 = "SOAP1.1";

type BuildMessageConfig = {
    /** SOAP基準 */
    soapProtocol: string;
    /** SOAP通信時の送信元APサーバのIPアドレス */
    sourceIpAddress: string;
};

/**
 * Create SOAP message in XML format
 *
 * // NOTE: *this fn is used internally by `SOAPCommon.ts`, don't use it directly*
 * @param operationName オペレーション名称
 * @param operationTypeName オペレーション種別名称
 * @param profileName プロファイル名称
 * @param parameterNameList パラメータ名称リスト
 * @param messageConfig SOAPバージョンと送信元APサーバのIPアドレス
 * @returns
 */
export function buildMessageXML(
    operationName: string,
    operationTypeName: string,
    profileName: string,
    parameterNameList: ParameterName[],
    messageConfig: BuildMessageConfig,
) {
    // SOAP基準を判定、1.1である場合、1.1を使う、1.1以外である場合、１．２を使う
    const namespace =
        messageConfig.soapProtocol === CONST_SOAP_1_1
            ? "http://schemas.xmlsoap.org/soap/envelope/"
            : "http://www.w3.org/2003/05/soap-envelope";

    const obj = {
        "soapenv:Envelope": {
            "@xmlns:soapenv": namespace,
            "soapenv:Body": {
                [operationName]: {
                    SourceIPaddr: MvnoUtil.addSpaceAfterStr(
                        messageConfig.sourceIpAddress,
                        0,
                    ),
                    OperationType: {
                        "@name": operationTypeName,
                        Profile: "",
                        Parameter: [],
                    },
                },
            },
        },
    };
    if (messageConfig.soapProtocol === CONST_SOAP_1_1) {
        obj["soapenv:Envelope"]["@xmlns:ns1"] = "http://ws.apache.org/axis2";
    }

    if (
        Constants.PROFILEVIEW === operationTypeName ||
        Constants.SERVICEPROFILEREQUEST === operationName
    ) {
        obj["soapenv:Envelope"]["soapenv:Body"][
            operationName
        ].OperationType.Profile = MvnoUtil.addSpaceAfterStr(profileName, 0);
    } else {
        delete obj["soapenv:Envelope"]["soapenv:Body"][operationName]
            .OperationType.Profile;
    }

    /** Parameter */
    if (!Array.isArray(parameterNameList) || parameterNameList.length === 0) {
        delete obj["soapenv:Envelope"]["soapenv:Body"][operationName]
            .OperationType.Parameter;
    }
    for (const param of parameterNameList) {
        obj["soapenv:Envelope"]["soapenv:Body"][
            operationName
        ].OperationType.Parameter.push({
            "@name": param.getName(),
            "#": param.getValue(),
        });
    }

    return convertToXML(obj);
}

/**
 * Create SOAP message in XML format
 *
 * // NOTE: *this fn is used internally by `SOAPCommonCoupon.ts`, don't use it directly*
 * @param operationName オペレーション名称
 * @param operationTypeName1 オペレーション種別名称
 * @param profileName1 プロファイル名称
 * @param parameterNameList1 パラメータ名称リスト
 * @param operationTypeName2 オペレーション種別名称
 * @param profileName2 プロファイル名称
 * @param parameterNameList2 パラメータ名称リスト
 * @param messageConfig SOAPバージョンと送信元APサーバのIPアドレス
 * @returns
 */
export function buildMessageXMLCoupon(
    operationName: string,
    operationTypeName1: string,
    profileName1: string,
    parameterNameList1: ParameterName[],
    operationTypeName2: string,
    profileName2: string,
    parameterNameList2: ParameterName[],
    messageConfig: BuildMessageConfig,
) {
    // SOAP基準を判定、1.1である場合、1.1を使う、1.1以外である場合、１．２を使う
    const namespace =
        messageConfig.soapProtocol === CONST_SOAP_1_1
            ? "http://schemas.xmlsoap.org/soap/envelope/"
            : "http://www.w3.org/2003/05/soap-envelope";

    const obj = {
        "soapenv:Envelope": {
            "@xmlns:soapenv": namespace,
            "soapenv:Body": {
                [operationName]: {
                    SourceIPaddr: MvnoUtil.addSpaceAfterStr(
                        messageConfig.sourceIpAddress,
                        0,
                    ),
                    OperationType: [{
                        "@name": operationTypeName1,
                        Profile: "",
                        Parameter: [],
                    },{
                        "@name": operationTypeName2,
                        Profile: "",
                        Parameter: [],
                    }],
                },
            },
        },
    };
    if (messageConfig.soapProtocol === CONST_SOAP_1_1) {
        obj["soapenv:Envelope"]["@xmlns:ns1"] = "http://ws.apache.org/axis2";
    }

    if (
        Constants.PROFILEVIEW === operationTypeName1 ||
        Constants.SERVICEPROFILEREQUEST === operationName
    ) {
        obj["soapenv:Envelope"]["soapenv:Body"][
            operationName
            ].OperationType[0].Profile = MvnoUtil.addSpaceAfterStr(profileName1, 0);
    } else {
        delete obj["soapenv:Envelope"]["soapenv:Body"][operationName]
            .OperationType[0].Profile;
    }

    if (
        Constants.PROFILEVIEW === operationTypeName2 ||
        Constants.SERVICEPROFILEREQUEST === operationName
    ) {
        obj["soapenv:Envelope"]["soapenv:Body"][
            operationName
            ].OperationType[1].Profile = MvnoUtil.addSpaceAfterStr(profileName2, 0);
    } else {
        delete obj["soapenv:Envelope"]["soapenv:Body"][operationName]
            .OperationType[1].Profile;
    }

    /** Parameter */
    if (!Array.isArray(parameterNameList1) || parameterNameList1.length === 0) {
        delete obj["soapenv:Envelope"]["soapenv:Body"][operationName]
            .OperationType[0].Parameter;
    }
    if (!Array.isArray(parameterNameList2) || parameterNameList2.length === 0) {
        delete obj["soapenv:Envelope"]["soapenv:Body"][operationName]
            .OperationType[1].Parameter;
    }
    for (const param of parameterNameList1) {
        obj["soapenv:Envelope"]["soapenv:Body"][
            operationName
            ].OperationType[0].Parameter.push({
            "@name": param.getName(),
            "#": param.getValue(),
        });
    }
    for (const param of parameterNameList2) {
        obj["soapenv:Envelope"]["soapenv:Body"][
            operationName
            ].OperationType[1].Parameter.push({
            "@name": param.getName(),
            "#": param.getValue(),
        });
    }

    return convertToXML(obj);
}

/**
 * Parse XML string to Document
 * @param data XML-like string
 * @param mime content type
 */
export function readXML(data: string, mime = "application/xml") {
    const parser = new DOMParser({
        errorHandler: (e) => {
            throw new SOAPException(e);
        },
    });
    const doc = parser.parseFromString(data, mime);
    return doc;
}

/**
 * Convert JS object to XML
 * @param obj
 * @returns
 */
export function convertToXML(obj: object): string {
    try {
        return xmlbuilder.convert(obj, {
            headless: true,
            format: "xml",
        });
    } catch (e) {
        throw new SOAPException(e);
    }
}
