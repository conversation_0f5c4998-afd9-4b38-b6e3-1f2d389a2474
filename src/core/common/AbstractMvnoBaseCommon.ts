import ExtendedInvocationContext from "@/types/ExtendedInvocationContext";
import { InvocationContext, HttpRequest } from "@azure/functions";
import MessageProperties from "@/core/constant/MessageProperties";
import MsgKeysConstants from "@/core/constant/MsgKeysConstants";

export default class AbstractMvnoBaseCommon<
    TContext extends InvocationContext = ExtendedInvocationContext,
> {
    // /** ログレベル：DEBUG */
    // protected static final String LOG_DEBUG = "DEBUG";
    protected static readonly LOG_DEBUG = "DEBUG";
    // /** ログレベル：INFO */
    // protected static final String LOG_INFO = "INFO";
    protected static readonly LOG_INFO = "INFO";
    // /** ログレベル：WARN */
    // protected static final String LOG_WARN = "WARN";
    protected static readonly LOG_WARN = "WARN";
    // /** ログレベル：ERROR */
    // protected static final String LOG_ERROR = "ERROR";
    protected static readonly LOG_ERROR = "ERROR";
    // /** ログレベル：FATAL */
    // protected static final String LOG_FATAL = "FATAL";
    protected static readonly LOG_FATAL = "FATAL";

    protected context: TContext;
    protected request: HttpRequest;

    public constructor(request: HttpRequest, context: TContext) {
        this.context = context;
        this.request = request;
    }

    /**
     * DEBUGログの出力
     *
     * @param msg
     * メッセージ
     */
    protected debug(
        tenantId: string,
        sequenceNo: string,
        msgKey: string,
        ...params: any[]
    ): void {
        // DO NOT LOG IF msgKey is 1 of the following keys
        if (
            [
                MsgKeysConstants.APCOMD0001,
                MsgKeysConstants.APCOMD0002,
                MsgKeysConstants.MPRCOD0001,
                MsgKeysConstants.MPRCOD0002,
                MsgKeysConstants.APLTID0001,
                MsgKeysConstants.APLCPD0001,
                MsgKeysConstants.APLBID0001,
                MsgKeysConstants.APLBID0002,
                MsgKeysConstants.APLAFD0002,
                MsgKeysConstants.APLAFD0001,
            ].includes(msgKey)
        ) {
            return;
        }

        // do not log from these APIs
        if ([
            "LineUseAcquisitionAPI",
            "LineGroupCouponAcquisitionAPI",
        ].includes(this.context.functionName)) {
            return;
        }

        const msg = this.createMessage(
            AbstractMvnoBaseCommon.LOG_DEBUG,
            tenantId,
            sequenceNo,
            msgKey,
            params,
        );
        this.context.debug(msg);
    }

    /**
     * ERRORログの出力
     *
     * @param tenantId
     * @param sequenceNo
     * @param msgKey
     * @param params
     */
    protected error(
        tenantId: string,
        sequenceNo: string,
        msgKey: string,
        ...params: any[]
    ): void;
    /**
     * ERRORログの出力
     *
     * @param e
     * @param tenantId
     * @param sequenceNo
     * @param msgKey
     * @param params
     */
    protected error(
        e: Error,
        tenantId: string,
        sequenceNo: string,
        msgKey: string,
        ...params: any[]
    ): void;

    // Single implementation
    protected error(
        arg1: string | Error,
        arg2: string,
        arg3: string,
        arg4: string,
        ...params: any[]
    ): void {
        if (typeof arg1 === "string") {
            // Handle the first overload
            const tenantId = arg1;
            const sequenceNo = arg2;
            const msgKey = arg3;
            if (arg4) {
                params.unshift(arg4);
            }
            const msg = this.createMessage(
                AbstractMvnoBaseCommon.LOG_ERROR,
                tenantId,
                sequenceNo,
                msgKey,
                params,
            );
            this.context.error(msg);
        } else {
            // Handle the second overload
            const e = arg1;
            const tenantId = arg2;
            const sequenceNo = arg3;
            const msgKey = arg4;
            const msg = this.createMessage(
                AbstractMvnoBaseCommon.LOG_ERROR,
                tenantId,
                sequenceNo,
                msgKey,
                params,
            );
            this.context.error(msg, e);
        }
    }

    /**
     * WARNログの出力
     *
     * @param e
     * @param tenantId
     * @param sequenceNo
     * @param msgKey
     * @param params
     */
    protected warn(
        e: Error,
        tenantId: string,
        sequenceNo: string,
        msgKey: string,
        ...params: any[]
    ): void;

    /**
     * WARNログの出力
     *
     * @param tenantId
     * @param sequenceNo
     * @param msgKey
     * @param params
     */
    protected warn(
        tenantId: string,
        sequenceNo: string,
        msgKey: string,
        ...params: any[]
    ): void;

    // implementation
    protected warn(
        arg1: string | Error,
        arg2: string,
        arg3: string,
        arg4: string,
        ...params: any[]
    ): void {
        if (typeof arg1 === "string") {
            if (arg4) {
                params.unshift(arg4);
            }
            const msg = this.createMessage(
                AbstractMvnoBaseCommon.LOG_WARN,
                arg1, // tenantId,
                arg2, // sequenceNo,
                arg3, // msgKey,
                params,
            );
            this.context.warn(msg);
        } else {
            const msg = this.createMessage(
                AbstractMvnoBaseCommon.LOG_WARN,
                arg2, // tenantId,
                arg3, // sequenceNo,
                arg4, // msgKey,
                params,
            );
            this.context.warn(msg, arg1);
        }
    }

    /**
     * ログ出力用のメッセージ作成
     *
     * @param logLevel
     * @param tenantId
     * @param sequenceNo
     * @param msgKey
     * @param params
     * @return
     */
    protected createMessage(
        logLevel: string,
        tenantId: string,
        sequenceNo: string,
        msgKey: string,
        params: any[],
    ): string {
        // String msg = this.getMessage(msgKey, params);
        const msg = this.getMessage(msgKey, params);
        return `${this.getHostName()} ${logLevel} ${tenantId} ${sequenceNo} ${msgKey} ${msg}`;
    }

    /**
     * TODO: need to implement this method later
     * メッセージリソースからメッセージを構築する
     *
     * @param key
     * メッセージキー
     * @param params
     * メッセージパラメーター
     *
     * @return 構築されたメッセージ
     */
    protected getMessage(key: string, params: string[]): string {
        // return key + params.join(",");
        const template = MessageProperties[key];
        if (template && Array.isArray(params)) {
            let result = template;
            params.forEach((param, index) => {
                const regex = new RegExp(`\\{${index}\\}`, "g");
                result = result.replace(regex, param);
            });
            return result;
        }
        return key + params.join(",");
    }

    /**
     * hostname取得
     * @return
     */
    private getHostName(): string {
        return this.request.headers.get("host");
    }

    /**
     * クラス名取得
     * @return
     * クラス名
     */
    protected getClassName(): string {
        return this.constructor.name;
    }

    // implementation of ntt.mawp.log.*
    private loggerInfo(...args: string[]) {
        // do not log from these APIs
        if ([
            "LineUseAcquisitionAPI",
            "LineGroupCouponAcquisitionAPI",
        ].includes(this.context.functionName)) {
            return;
        }
        this.context.info(args.join(" "));
    }

    /**
     * SOAP送信ログ出力を行う。
     * @param ipAddrFrom 送信元IP
     * @param ipAddrTo 送信先IP
     * @param id 電文識別ID
     * @param message メッセージ
     */
    protected soapLogSend(
        ipAddrFrom: string,
        ipAddrTo: string,
        id: string,
        message: string,
    ) {
        this.loggerInfo(ipAddrFrom, ipAddrTo, "INFO", id, message);
    }

    /**
     * SOAP受信ログ出力を行う。
     * @param ipAddrFrom 送信元IP
     * @param ipAddrTo 送信先IP
     * @param id 電文識別ID
     * @param message メッセージ
     */
    protected soapLogReceive(
        ipAddrFrom: string,
        ipAddrTo: string,
        id: string,
        message: string,
    ) {
        this.loggerInfo(ipAddrFrom, ipAddrTo, "INFO", id, message);
    }
}
