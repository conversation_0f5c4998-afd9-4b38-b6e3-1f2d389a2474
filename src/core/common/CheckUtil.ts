import { add, format, isValid, parse } from "date-fns";
import os from "os";
import MVNOUtil from "./MvnoUtil";
import MessageProperties from "../constant/MessageProperties";
import MsgKeysConstants from "../constant/MsgKeysConstants";

const SEPARATOR = " ";

export default class CheckUtil {
    /**
     * TODO: this function name looks weird. It should be renamed to something more descriptive.
     * チェック項目がｎｕｌｌ、""ではないかチェック処理である。<BR>
     *
     * @param checkString　チェック項目
     * @return true--チェック項目がｎｕｌｌ、""です、false--チェック項目がｎｕｌｌ、""ではない
     */
    public static checkIsNotNull(checkString: string): boolean {
        if (checkString === null || checkString === undefined) {
            /** チェック項目のnullチェック */
            return true;
        } else if (checkString === "") {
            /** チェック項目の空白チェック */
            return true;
        } else {
            return false;
        }
    }

    /**
     * 桁数チェック。<BR>
     *
     * <PRE>
     * 1、チェック項目に対して、桁数を満たすかどうかをチェックする
     * ２、固定長である場合、チェック項目の桁数がパラメータの桁数と同じであること
     * ３、可変長である場合、チェック項目の桁数がパラメータの桁数の以下であること
     * </PRE>
     *
     * @param checkString　チェック項目
     * @param length　桁数
     * @param koteikahenFlg　固定長／可変長フラグ
     * @return true--チェック条件に合う、false--チェック条件に合わない
     */
    public static checkLength(
        checkString: string | any,
        length: number,
        koteikahenFlg: boolean,
    ): boolean {
        if (checkString === null || checkString === undefined) {
            return false;
        }
        /** チェック項目の桁数取得 */
        const checkStringLen =
            typeof checkString === "string"
                ? checkString.length
                : checkString.toString().length;
        if (koteikahenFlg && checkStringLen === length) {
            return true;
        } else if (!koteikahenFlg && checkStringLen <= length) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * チェック項目は数字であるかどうかチェックする。<BR>
     *
     * @param checkString　チェック項目
     * @return true--チェック項目が数字、""です、false--チェック項目が数字ではない
     */
    public static checkIsNum(checkString: string): boolean {
        const stringified =
            typeof checkString === "string"
                ? checkString
                : checkString === null || checkString === undefined
                ? ""
                : (checkString as any).toString();
        // const result = stringified.match("[0-9]+");
        // NOTE updated regex to match the entire string
        const result = stringified.match("^[0-9]+$");
        return result !== null;
    }

    /**
     * 日付フォーマットチェック<BR>
     *
     * @param forMartDate　入力パラメータ
     * @param parten 入力パラメータ
     * @return 日付フォーマットチェック結果
     */
    public static checkDateFmt(forMartDate: string, parten: string): boolean {
        return isValid(parse(forMartDate, parten, new Date()));
    }

    /**
     * 予約可能制限日数後の日付を算出<BR>
     *
     * @param forMartDate　サーバシステム日付
     * @param parten 入力パラメータ
     * @return 日付フォーマットチェック結果
     */
    public static addDay(forMartDate: Date, days: number): Date {
        return add(forMartDate, { days });
    }

    /** STEP1.3版対応　追加　START */
    /**
     * 入力値が半角英数字かのを判断する
     * @param input 入力値
     * @return true:入力値が半角英数字である、false:入力値が半角英数字ではない
     */
    public static checkIsSemiangleAlphanumic(input: string): boolean {
        let result = false;
        result = /^[0-9a-zA-Z]*$/.test(input);
        return result;
    }
    /** STEP1.3版対応　追加　END */

    /** STEP1.3版仕様変更対応　追加　START */
    /**
     * 桁数範囲チェック。<BR>
     *
     * <PRE>
     * 1、チェック項目に対して、桁数を満たすかどうかをチェックする
     * ２、固定長である場合、チェック項目の桁数がパラメータの桁数と同じであること
     * ３、可変長である場合、チェック項目の桁数がパラメータの桁数の以下であること
     * </PRE>
     *
     * @param checkString　チェック項目
     * @param startlength　最小桁数
     * @param endlength　最大桁数
     * @return true--チェック条件に合う、false--チェック条件に合わない
     */
    public static checkRangeLength(
        checkString: string,
        startlength: number,
        endlength: number,
    ): boolean {
        if (checkString === null || checkString === undefined) {
            return false;
        }
        /** チェック項目の桁数取得 */
        const checkStringLen = checkString.length;
        /** 最小桁数から最大桁数までチェックを行う */
        if (checkStringLen >= startlength && checkStringLen <= endlength) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 時刻フォーマットチェック<BR>
     *
     * @param forMartTime　入力パラメータ
     * @param parten 入力パラメータ
     * @return 時刻フォーマットチェック結果
     */
    public static checkTimeFmt(forMartTime: string, parten: string): boolean {
        return isValid(parse(forMartTime, parten, new Date()));
    }

    /**
     * チェック項目がｎｕｌｌではないかチェック処理である。<BR>
     *
     * @param check　チェック項目
     * @return true--チェック項目がｎｕｌｌです、false--チェック項目がｎｕｌｌではない
     */
    public static checkIsNull(check: any): boolean {
        if (check === null || check === undefined) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 返却値編集チェックする。<BR>
     *
     * @param prams　入力パラメータ
     * @param length 桁数
     * @return 返却値編集値
     */
    public static returnPrams(prams: string, length: number): string {
        let resultPrams = "";
        if (!CheckUtil.checkIsNotNull(prams)) {
            resultPrams = MVNOUtil.addSpaceAfterStr(prams, length);
        } else {
            resultPrams = MVNOUtil.addSpaceAfterStr(null, 0);
        }
        return resultPrams;
    }

    /**
     * 容量切れ時間編集。<BR>
     *
     * @param forMartdynDate　入力パラメータ
     * @return 容量切れ時間編集値
     */
    public static forMartdynDate(forMartdynDate: string): string {
        if (forMartdynDate !== "") {
            if (forMartdynDate.length === 13) {
                forMartdynDate = forMartdynDate + ":00";
                return forMartdynDate;
            }
        }
        return forMartdynDate;
    }

    /**
     * 容量切れ時間編集。<BR>
     *
     * @param fmartDate　入力パラメータ (???yyMMdd)
     * @return 容量切れ時間編集値 (yyyy/MM/dd)
     */
    public static forMartDate(fmartDate: string): string {
        if (typeof fmartDate === "string" && fmartDate.length >= 9) {
            let date: Date;
            let xmlStr = "";
            xmlStr = "20" + fmartDate.substring(3, 9);
            // let strfmartDate = "";
            const dateFormat = "yyyyMMdd";
            const returnDateFormat = "yyyy/MM/dd";
            try {
                date = parse(xmlStr, dateFormat, new Date());
                return format(date, returnDateFormat);
            } catch (e) {
                CheckUtil.error(
                    e,
                    "",
                    "",
                    MsgKeysConstants.APCOME0401,
                    e.message,
                );
                return "";
            }
        } else {
            return "";
        }
    }

    /**
     * ERRORログの出力
     *
     * @param e
     * @param tenantId
     * @param sequenceNo
     * @param msgKey
     * @param params
     */
    private static error(
        e: Error,
        tenantId: string,
        sequenceNo: string,
        msgKey: string,
        ...params: any[]
    ): void {
        const msg = CheckUtil.createMessage(
            "ERROR",
            tenantId,
            sequenceNo,
            msgKey,
            params,
        );
        // NOTE we don't have `context` here so use console.error instead
        // tslint:disable-next-line:no-console
        console.error(msg, e);
    }

    /**
     * ログ出力用のメッセージ作成
     *
     * @param logLevel
     * @param tenantId
     * @param sequenceNo
     * @param msgKey
     * @param params
     * @return
     */
    private static createMessage(
        logLevel: string,
        tenantId: string,
        sequenceNo: string,
        msgKey: string,
        params: any[],
    ): string {
        const msg = CheckUtil.getMessage(msgKey, params);
        return (
            CheckUtil.getHostName() +
            SEPARATOR +
            logLevel +
            SEPARATOR +
            tenantId +
            SEPARATOR +
            sequenceNo +
            SEPARATOR +
            msgKey +
            SEPARATOR +
            msg
        );
    }

    /**
     * メッセージリソースからメッセージを構築する
     *
     * NOTE: this function is a copy from `@/core/common/AbstractMvnoBaseCommon.ts`
     *
     * @param key
     * メッセージキー
     * @param params
     * メッセージパラメーター
     *
     * @return 構築されたメッセージ
     */
    private static getMessage(key: string, params: string[]): string {
        // return key + params.join(",");
        const template = MessageProperties[key];
        if (template && Array.isArray(params)) {
            let result = template;
            params.forEach((param, index) => {
                const regex = new RegExp(`\\{${index}\\}`, "g");
                result = result.replace(regex, param);
            });
            return result;
        }
        return key + params.join(",");
    }

    /**
     * hostname取得
     * @return
     */
    private static getHostName(): string {
        return os.hostname();
    }
}
