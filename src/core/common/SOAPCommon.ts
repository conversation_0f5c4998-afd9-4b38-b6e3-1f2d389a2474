import axios, { AxiosError, AxiosInstance } from "axios";
import config from "config";
import xpath from "xpath";

import SOAPException from "@/types/soapException";
import CoreMvnoConfigKey from "@/types/coreMvnoConfig";

import AbstractMvnoBaseCommon from "./AbstractMvnoBaseCommon";
import * as SOAPCommonUtils from "./SOAPCommonUtils";
import MvnoUtil from "./MvnoUtil";
import StringUtils from "./StringUtils";

import AppConfig from "@/appconfig";
import ParameterName from "../dto/ParameterName";
import SOAPCommonOutputDto from "../dto/SOAPCommonOutputDto";

import MsgKeysConstants from "../constant/MsgKeysConstants";
import ResultCdConstants from "../constant/ResultCdConstants";
import Constants from "../constant/Constants";

import { getConfig, tryGetConfig } from "@/helpers/configHelper";
import { isNone } from "@/utils";

export default class SOAPCommon extends AbstractMvnoBaseCommon {
    /** Axiosインスタンス */
    private static client: AxiosInstance;

    /** SOAP送信アドレス */
    private tpcLiteRequestURI = getConfig("TpcLiteRequestURI");

    /** SOAP送信アドレス */
    private tpcDestServiceName = getConfig("TpcDestServiceName");

    /** SOAPタイムアウト */
    private tpcTimeout = config.get<string>("mvno.TpcTimeout");

    /** SOAP通信時の送信元APサーバのIPアドレス */
    private tpcSourceIpAddress = getConfig("TpcSourceIpAddress");

    /** SOAP基準 */
    private soap = config.get<string>("mvno.SOAP");

    /** XML電文返信結果 */
    private static CONST_RESULT = "NG";

    /** SOAP接続の判定 */
    private static CONST_CONNRESULT = "OK";

    /** HTTPヘッダのレスポンスコード */
    private static CONST_RESPONSECODE = 200;

    /** SOAP基準1.1 */
    private static CONST_SOAP_1_1 = "SOAP1.1";

    /** SOAPオペレーション名称 */
    private static CONST_OPERATIONNAME = "serviceProfileRequestLite";

    /**
     * SOAP APIを呼び出す。
     * @param operationName オペレーション名称
     * @param operationTypeName オペレーション種別名称
     * @param profileName プロファイル名称
     * @param parameterNameList パラメータ名称リスト
     * @param tenantId テナントID
     * @param sequenceNo 送信番号
     * @param tpcDestInfo TPC情報
     * @param primaryFlg プライマリフラグ
     * @returns Document 応答電文情報
     */
    public async callWebSoapApi(
        operationName: string,
        operationTypeName: string,
        profileName: string,
        parameterNameList: ParameterName[],
        tenantId: string,
        sequenceNo: string,
        tpcDestInfo: string,
        primaryFlg: boolean = true,
    ): Promise<SOAPCommonOutputDto> {
        const sOAPCommonOutputDto = new SOAPCommonOutputDto();

        const paraStr: string[] = [];
        paraStr.push("callWebSoapApi [operationName=");
        paraStr.push(operationName);
        paraStr.push(", operationTypeName=");
        paraStr.push(operationTypeName);
        paraStr.push(", profileName");
        paraStr.push(profileName);
        paraStr.push(", parameterNameList[");
        for (let i = 0; i < parameterNameList.length; i++) {
            paraStr.push("name=");
            paraStr.push(parameterNameList.at(i).getName());
            paraStr.push(", value=");
            paraStr.push(parameterNameList.at(i).getValue());
        }
        paraStr.push("] tenantId=");
        paraStr.push(tenantId);
        paraStr.push(", sequenceNo");
        paraStr.push(sequenceNo);
        paraStr.push("]");

        this.debug(
            tenantId,
            sequenceNo,
            MsgKeysConstants.MPSCOD0001,
            this.getClassName(),
            "callWebSoapApi",
            paraStr.join(""),
        );

        /** 変数「処理コード」初期化 */
        let handleCode = ResultCdConstants.CODE_000000;
        /** 返信電文 */
        let doc: Document = null; // 初期化

        // 電文チェック
        if (
            Constants.PROFILEVIEW === operationTypeName &&
            isNone(profileName)
        ) {
            if (primaryFlg) {
                this.error(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.MPSCOE0001,
                    "プライマリTPC",
                );
            } else {
                this.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.MPSCOE0001,
                    "セカンダリTPC",
                );
            }
            sOAPCommonOutputDto.setDoc(doc);
            super.debug(
                tenantId,
                sequenceNo,
                MsgKeysConstants.MPSCOD0002,
                this.getClassName(),
                "callWebSoapApi",
            );
            return sOAPCommonOutputDto;
        }

        // TPC送信先IPアドレスを取得
        // const tpcDestIpAddress = config.get<string>(`mvno.${tpcDestInfo}`);
        const tpcDestIpAddress = tryGetConfig(
            tpcDestInfo as CoreMvnoConfigKey,
            null,
        );
        if (StringUtils.isEmpty(tpcDestIpAddress)) {
            // 入力パラメータ.TPC情報のキーが設定ファイルに存在しない
            if (primaryFlg) {
                this.error(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.MPSCOE0005,
                    tpcDestInfo,
                    "プライマリTPC",
                );
            } else {
                this.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.MPSCOE0005,
                    tpcDestInfo,
                    "セカンダリTPC",
                );
            }
            handleCode = ResultCdConstants.CODE_000951;
            sOAPCommonOutputDto.setProcessCode(handleCode);
            sOAPCommonOutputDto.setDoc(doc);
            sOAPCommonOutputDto.setError(true);
            this.debug(
                tenantId,
                sequenceNo,
                MsgKeysConstants.MPSCOD0002,
                this.getClassName(),
                "callWebSoapApi",
            );
            return sOAPCommonOutputDto;
        }

        // ログ付与用の電文識別ID(タイムスタンプ + " " + 数値10桁)を取得
        const strTelegramId = MvnoUtil.getLogApiIdentId();
        /** SOAPメッセージ */
        const soapMessage = SOAPCommonUtils.buildMessageXML(
            operationName,
            operationTypeName,
            profileName,
            parameterNameList,
            {
                soapProtocol: this.soap,
                sourceIpAddress: this.tpcSourceIpAddress,
            },
        );
        const sendMessage = this.getMessage(MsgKeysConstants.MPSCOI0001, [
            soapMessage,
        ]);
        this.soapLogSend(
            this.tpcSourceIpAddress,
            tpcDestIpAddress,
            strTelegramId,
            sendMessage,
        );

        /** SOAP送信アドレス */
        let soapApiUrl: string;
        if (SOAPCommon.CONST_OPERATIONNAME === operationName) {
            // soapApiUrl = tpcLiteRequestURI;
            soapApiUrl = this.tpcLiteRequestURI.replace(
                "${IpAddr}",
                tpcDestIpAddress,
            );
        } else {
            // soapApiUrl = tpcDestServiceName;
            soapApiUrl = this.tpcDestServiceName.replace(
                "${IpAddr}",
                tpcDestIpAddress,
            );
        }

        // SOAP送信
        try {
            if (!SOAPCommon.client) {
                SOAPCommon.client = axios.create({
                    timeout: parseInt(this.tpcTimeout, 10),
                });
                this.runWithMock(SOAPCommon.client);
            }

            this.context.log("Sending SOAP message to", soapApiUrl);
            let contentType = "application/soap+xml"; // SOAP 1.2
            if (this.soap === SOAPCommon.CONST_SOAP_1_1) {
                contentType = "text/xml"; // SOAP 1.1
            }
            const response = await SOAPCommon.client.post(
                soapApiUrl,
                soapMessage,
                {
                    headers: {
                        "Content-Type": contentType,
                    },
                },
            );

            const headerInfo = Object.entries(response.headers || {}).map(
                ([key, val]) => {
                    return key + ":" + val.toString();
                },
            );
            headerInfo.unshift(response.status.toString());

            // HTTPヘッダログ出力
            const headerMessage = this.getMessage(MsgKeysConstants.MPSCOI0002, [
                headerInfo.join("."),
            ]);
            this.soapLogReceive(
                this.tpcSourceIpAddress,
                tpcDestIpAddress,
                strTelegramId,
                headerMessage,
            );

            // 返信田文ログ出力
            const receiveMessage = this.getMessage(
                MsgKeysConstants.MPSCOI0003,
                [response.data],
            );
            this.soapLogReceive(
                this.tpcSourceIpAddress,
                tpcDestIpAddress,
                strTelegramId,
                receiveMessage,
            );

            doc = SOAPCommonUtils.readXML(response.data);
            const responseCode = response.status;

            if (responseCode !== SOAPCommon.CONST_RESPONSECODE) {
                handleCode = ResultCdConstants.CODE_001101;
                if (primaryFlg) {
                    this.error(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.MPSCOE0004,
                        responseCode,
                        "プライマリTPC",
                    );
                } else {
                    this.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.MPSCOE0004,
                        responseCode,
                        "セカンダリTPC",
                    );
                }
                sOAPCommonOutputDto.setProcessCode(handleCode);
                sOAPCommonOutputDto.setDoc(doc);
                sOAPCommonOutputDto.setError(true);
                this.debug(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.MPSCOD0002,
                    this.getClassName(),
                    "callWebSoapApi",
                );
                return sOAPCommonOutputDto;
            }

            // 電文ボディ結果を判定する
            const resultStr = this.getNodeContent(doc, "//Result");
            if (SOAPCommon.CONST_RESULT === resultStr) {
                handleCode = ResultCdConstants.CODE_001102;
                sOAPCommonOutputDto.setProcessCode(handleCode);
                sOAPCommonOutputDto.setDoc(doc);
                this.debug(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.MPSCOD0002,
                    this.getClassName(),
                    "callWebSoapApi",
                );
                return sOAPCommonOutputDto;
            }
        } catch (e) {
            if (
                axios.isAxiosError(e) &&
                (e.code === AxiosError.ETIMEDOUT ||
                    e.code === AxiosError.ECONNABORTED)
            ) {
                // タイムアウト時間を経過してもTPCからの応答がない場合
                this.context.log("SOAP request failed:", e.message);
                const timeOut = +this.tpcTimeout;
                const timeOutLog = String(timeOut / 1000);
                if (primaryFlg) {
                    this.error(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.MPSCOE0003,
                        timeOutLog,
                        "プライマリTPC",
                    );
                } else {
                    this.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.MPSCOE0003,
                        timeOutLog,
                        "セカンダリTPC",
                    );
                }
                sOAPCommonOutputDto.setDoc(doc);
                this.debug(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.MPSCOD0002,
                    this.getClassName(),
                    "callWebSoapApi",
                );
                throw e;
            } else if (SOAPException.isSOAPException(e)) {
                // NOTE this include SOAPException from Axios error which is not a timeout or from error during XML parsing
                if (primaryFlg) {
                    this.error(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.MPSCOE0002,
                        soapApiUrl,
                        "プライマリTPC",
                    );
                } else {
                    this.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.MPSCOE0002,
                        soapApiUrl,
                        "セカンダリTPC",
                    );
                }
                sOAPCommonOutputDto.setDoc(doc);
                this.debug(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.MPSCOD0002,
                    this.getClassName(),
                    "callWebSoapApi",
                );
                throw e; // NOTE rethrow the error
                // return sOAPCommonOutputDto; // NOTE return was commented out in original code
            }
            // NOTE original code only catches SOAPException so other errors should be rethrown
            throw e;
        }
        // return
        sOAPCommonOutputDto.setProcessCode(handleCode);
        sOAPCommonOutputDto.setDoc(doc);
        this.debug(
            tenantId,
            sequenceNo,
            MsgKeysConstants.MPSCOD0002,
            this.getClassName(),
            "callWebSoapApi",
        );
        return sOAPCommonOutputDto;
    }

    /**
     * xml内容から指定されるxpathにより、値を取得する。
     *
     * @param doc
     *            xmlのDocument
     * @param xpathStr
     *            照会xpath
     * @throws Error
     *             　異常
     * @return 結果値
     */
    // Note: XPathExpressionException only occurs if xpathStr is invalid, while in typescript, only null xpathStr would cause an error
    // The error thrown by null doc in the original code is not catched, so it will be thrown as well
    public getNodeContent(doc: Document, xpathStr: string) {
        if (!doc) {
            throw new SOAPException("");
        }
        try {
            const root = doc.documentElement;
            const select = xpath.useNamespaces({ ns: root.namespaceURI });
            const result = select(xpathStr, root);
            if (Array.isArray(result) && result.length > 0) {
                return result[0].textContent;
            } else {
                return "";
            }
        } catch (e) {
            return "";
        }
    }

    /**
     * xml内容から指定されるxpathにより、タグ存在チェック。
     *
     * @param doc xmlのDocument
     * @param xpathStr 照会xpath
     * @throws Error 異常
     * @return 結果値
     */
    // Note: XPathExpressionException only occurs if xpathStr is invalid, while in typescript, only null xpathStr would cause an error
    // The error thrown by null doc in the original code is not catched, so it will be thrown as well
    public CheckNodeExist(doc: Document, xpathStr: string) {
        if (!doc) {
            throw new SOAPException("");
        }
        try {
            const root = doc.documentElement;
            const select = xpath.useNamespaces({ ns: root.namespaceURI });
            const result = select(xpathStr, root);
            return Array.isArray(result) && result.length > 0;
        } catch (e) {
            return false;
        }
    }

    private runWithMock(instance: AxiosInstance) {
        const coreConfig = AppConfig.getCoreConfig(this.context);
        if (
            !coreConfig.CORE_TPC_API.RUN_WITH_MOCK ||
            process.env.NODE_ENV === "test" // if running with jest, no need to mock (already using nock)
        ) {
            return;
        }
        this.context.log("Running with mock response");

        const result = "OK";
        const response = `
            <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
                xmlns:ns1="http://ws.apache.org/axis2">
                <soapenv:Body>
                    <Result>${result}</Result>
                    <RunWithMock>1</RunWithMock>
                </soapenv:Body>
            </soapenv:Envelope>
            `.trim();

        const MockAdapter = require("axios-mock-adapter");
        const mockApi = new MockAdapter(instance);

        mockApi.onPost().reply(200, response, { "Content-Type": "text/xml" });
    }
}
