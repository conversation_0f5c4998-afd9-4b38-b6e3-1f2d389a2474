import { HttpRequest } from "@azure/functions";
import { getTime, parse, addMonths, addDays, isValid, differenceInDays } from "date-fns";
import { format } from "date-fns/format";
import { ja } from "date-fns/locale";

export default class MvnoUtil {

    /**
     * 文字列（半角英字大文字）
     */
    private static STR_ABC_L: string = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    /**
     * 文字列（半角英字大文字）
     */
    private static STR_ABC_S: string = "abcdefghijklmnopqrstuvwxyz";

    /**
     * 文字列（半角数値）
     */
    private static STR_NUMBER: string  = "0123456789";

    /**
     * システム日時を取得（yyyy/MM/dd HH:mm:ss）
     *
     * @return String フォーマット後日時
     */
    public static getDateTimeNow(): string {
        return format(new Date(), "yyyy/MM/dd HH:mm:ss", { locale: ja });
    }

    /**
     * システム日時を取得（yyyy/MM/dd HH:mm:ss.SSS）
     *
     * @return String フォーマット後日時
     */
    public static getDateTime(): string {
        return format(new Date(), "yyyy/MM/dd HH:mm:ss.SSS", { locale: ja });
    }

    /** #385 追加 START */
    /**
     * TODO: Need to implement this method to get the azure function IP
     * APサーバの複数IP取得
     * @return List<String>
     * @throws Exception
     */
    public static getLocalIpList(): string[] {
        return [];
    }

    /**
     * リスト内容が出力する
     * @return List<String>⇒String
     * @throws Exception
     */
    public static getOutputList(list: string[]): string {
        let result = "";
        for (let i = 0; i < list.length; i++) {
            result = result.concat(list[i]).concat(", ");
        }
        if (list.length > 0) {
            result = result.substring(0, result.length - 2);
        }
        return result;
    }

    /**
     * Get client IP Address
     * original function: `request.getRemoteAddr()`
     * @param request
     * @returns
     */
    public static getClientIPAddress(request: HttpRequest): string {
        const raw =
            request.headers.get("x-forwarded-for") ||
            request.headers.get("x-client-ip") ||
            "";
        // split by comma and remove port number if exists
        const ipAddress = raw.split(",").map((ip) => ip.trim().split(":")[0]);
        // return the first IP address
        return ipAddress.length > 0 ? ipAddress[0] : null;
    }

    /**
     * 文字列の後ろに半角スペースを埋め処理
     *
     * @param input 対象
     * @param strLength ””を付ける桁数
     * @return String ””を付けた文字列
     */
    public static addSpaceAfterStr(input: any, strLength: number) {
        let str: string;
        if (input === null || input === undefined) {
            str = "";
        } else {
            // NOTE if input is object then it will return '[object Object]'
            // but all calls to this function seems to be with string or null
            str = String(input);
        }
        return str;
    }

    /**
     * データ型変換
     * convert date string to unix timestamp (millisecond)
     * @param strDate
     * @return
     */
    public static convDateFormat(strDate: string): number {
        return getTime(parse(strDate, "yyyy/MM/dd HH:mm:ss", new Date()));
    }

    /**
     * REST電文を取得する
     * @param <T>
     * @throws Exception
     */
    public static getRestMessage<T>(dto: T): string {
        const excludeFields = [
            "internal_flag",
            "reserve_flag",
            "executeUserId",
            "executeTenantId",
            "reserve_soId",
        ];
        return JSON.stringify(
            Object.keys(dto).reduce((acc, key) => {
                if (!excludeFields.includes(key)) {
                    acc[key] = dto[key];
                }
                return acc;
            }, {}),
        );
    }

    public static addZeroBeforeStr(str: string, strLength: number) {
        if (str === null || str === undefined) {
            return str;
        }
        return str.padStart(strLength, "0");
    }

    /**
     * APIを一意に識別できるログ付与用のIDを生成する。
     *
     * @return ログ付与用 API識別ID値(タイムスタンプ + " " + 数値10桁)
     */
    public static getLogApiIdentId(): string {
        const minVal = 1;
        const maxVal = 10000000000;
        // ※指定された最小値から境界までの範囲 (最小値は含むが、境界は含まない)
        const lngRandom =
            Math.floor(Math.random() * (maxVal - minVal)) + minVal;

        // API識別ID値(タイムスタンプ + " " + 数値10桁(乱数))を返却
        return (
            MvnoUtil.getDateTime() +
            " " +
            lngRandom.toString().padStart(10, "0")
        );
    }

    /** STEP1.2b版対応 追加 START */
    /**
     * システム日付の月と年を取得する
     * @param days
     * @return String
     * @throws Exception
     */

    public static getTrafficPreviousMonth(days: number): string[] {
        const delDate = addMonths(new Date(), days);
        return [format(delDate, "yyyy"), format(delDate, "MM").replace(/^0/, "")];
    }

    /**
     * 日付フォーマットチェック<BR>
     *
     * 正しい日付である場合、TRUEを返却する。<BR>
     * それ以外の場合、FALSEを返却する。<BR>
     *
     * @param date チェック対象日付文字列(YYYYMMDD)
     * @return
     */
    public static checkDateFmt(date: string): boolean {
        return isValid(parse(date, "yyyy/MM/dd", new Date()));
    }

    /**
     * 日期比較チェック<BR>
     *
     * 完了日（始期） <=完了日（終期）  return true;<BR>
     * その他 return false;<BR>
     *
     * @param str1 完了日（始期）
     * @param str2 完了日（終期）
     * @return
     */
    public static checkIsDataComparison(str1: string, str2: string): boolean {
        if (this.checkDateFmt(str1) === false || this.checkDateFmt(str2) === false) {   // original code catch (Exception e) part. Date-fns doesn't throw exception even could not parse the date.
            return false;
        }
        return differenceInDays(parse(str1, "yyyy/MM/dd", new Date()), parse(str2, "yyyy/MM/dd", new Date())) <= 0;
    }

    /**
     * 検索期間の算出（直近30日分）
     * @param days
     * @return String
     * @throws Exception
     */
    public static getRecent30Day(days: string): string {
        return format(addDays(new Date(), Number("-" + days)), "yyyy/MM/dd");
    }

    /**
     * 文字長の範囲チェック<BR>
     *
     * ＜文字長＞最小桁数～最大桁数Byteであれば、OKとし、trueを返却する。<BR>
     * それ以外、falseを返却する。<BR>
     *
     * @param str チェック対象文字列
     * @param min 最小桁数
     * @param max 最大桁数
     * @return
     */
    public static chkLenRange(str: string | null, min: number, max: number): boolean {
        if (str === null) {
            return false;
        } else if (str.length < min || str.length > max) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * 文字列属性チェック
     * @param inputStr
     * @param inputCheckStr
     * @return
     */
    public static propertiesCheck(inputStr: string, inputCheckStr: string[]): boolean {
        let checkStr = "";

        if (inputCheckStr != null && inputCheckStr.length > 0) {
            for (const tmpString of inputCheckStr) {
                checkStr = checkStr + tmpString;
            }
        }

        // チェックアイテムが対象文字列に含まれているかをチェック
        let index = 0;
        for (let i = 0; i < checkStr.length; i++) {
            index = 0;
            // 対象文字列の検索
            while (index !== -1) {
                index = inputStr.indexOf(checkStr.charAt(i));
                if (index !== -1) { // 対象文字が見つかった。
                    if (index > 0) {
                        // 削除対象位置が>0の場合
                        inputStr = inputStr.substring(0, index) + inputStr.substring(index + 1);
                    } else if (index === 0) {
                        // 削除対象位置が=0の場合
                        inputStr = inputStr.substring(1);
                    } else {
                        // 削除対象位置が無効の場合は処理エラー
                        throw new Error("StringIndexOutOfBoundsException");
                    }
                }
            }
        }

        if (inputStr.length === 0) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 文字種のチェック1<BR>
     *
     * ＜文字種＞英数字：「a-zA-Z0-9」であれば、OKとし、trueを返却する。<BR>
     * それ以外、falseを返却する。<BR>
     * @param str  チェック対象文字列
     * @return
     */
    public static chkStrType1(str: string): boolean {
        if (str === null) {
            return false;
        }
        const checkItemType:string[] = [this.STR_ABC_L,this.STR_ABC_S,this.STR_NUMBER]

		if (!this.propertiesCheck(str,checkItemType)) {
			return false;
		}
		return true;
	}
}
