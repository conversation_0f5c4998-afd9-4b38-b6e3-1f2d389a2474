import md2 from "js-md2";
import config from "config";
import { Transaction } from "sequelize";

import CheckResultBean from "@/core/dto/CheckResultBean";
import Constants from "@/core/constant/Constants";
import ResultCdConstants from "@/core/constant/ResultCdConstants";
import APICommonDAO from "@/core/dao/APICommonDAO";
import MsgKeysConstants from "@/core/constant/MsgKeysConstants";
import MvnoConstants from "@/core/constant/MvnoConstants";
import { TenantsEntity } from "@/core/entity/TenantsEntity";
import { isSQLException, retryQuery } from "@/helpers/queryHelper";
import AbstractMvnoBaseCommon from "./AbstractMvnoBaseCommon";
import MvnoUtil from "./MvnoUtil";
import CheckUtil from "./CheckUtil";
import Check from "./Check";
import AppConfig from "../../appconfig";
import { useRedis } from "../../database/redis";
import { isLockNotAvailableError, usePsql } from "@/database/psql";
import { getConfig, getConfigAsNumber } from "@/helpers/configHelper";

export default class RESTCommon extends AbstractMvnoBaseCommon {
    /**
     * シーケンス編集用文字列
     */
    private apiProcessID = config.get<string>("mvno.ApiProcessIDServer");

    /**
     * API認証キー生成用文字列
     */
    private static STR_API_KEY = "$02$";

    // /**
    //  * 接続許可IPアドレス (not used)
    //  */
    // private allowedSourceIpAddress = getConfig("AllowedSourceIpAddress");

    /**
     * APサーバ同時接続上限数
     */
    private connectionMax = getConfigAsNumber("ServerConnectionLimit");

    protected apiCommonDAO: APICommonDAO = new APICommonDAO(
        this.request,
        this.context,
    );

    /**
     * DBアクセスリトライ回数
     */
    protected apDbRetryMaxCnt = config.get<number>("mvno.ApDbRetryMaxCnt");

    /**
     * DBアクセスリトライ間隔
     */
    protected apDbRetryInterval = config.get<number>("mvno.ApDbRetryInterval");

    /**
     * REST共通チェック(外部用)
     *
     * NOTE: Changed to static so that it can be used from BaseHandler
     * @param sequenceNo
     * @param senderSystemId
     * @param apiKey
     * @param functionType
     * @param tenantId
     * @param localFunctionType
     * @param url
     * @param orderType
     * @param reserveSoId
     * @param sourceIpAddress IPアドレス
     * @return
     */
    public async checkRestCommon(
        sequenceNo: string,
        senderSystemId: string,
        apiKey: string,
        functionType: string,
        tenantId: string,
        localFunctionType: string,
        url: string,
        orderType: string,
        reserveSoId: string,
        sourceIpAddress: string,
    ) {
        // super.debug(
        //     tenantId,
        //     sequenceNo,
        //     MsgKeysConstants.MPRCOD0001,
        //     this.getClassName(),
        //     "checkRestCommon",
        //     "sequenceNo=" +
        //         sequenceNo +
        //         ",senderSystemId=" +
        //         senderSystemId +
        //         ",apiKey=" +
        //         apiKey +
        //         ",functionType=" +
        //         functionType +
        //         ",tenantId=" +
        //         tenantId,
        // );

        const checkResultBean: CheckResultBean = {
            checkResult: false,
            processingCode: "",
            others: "",
            errorMassage: "",
        };

        /* #493対応 追加 START */
        // 予約前オーダ　或いは　即時オーダの場合
        if (Constants.ORDER_TYPE_2 !== orderType) {
            // 同時接続数チェックを行う
            const resultChkConCnt: boolean = await this.checkConnectCoun(
                tenantId,
                sequenceNo,
            );

            // 「同時接続数チェック結果」より判定する
            if (!resultChkConCnt) {
                // 「チェック結果」にfalseを設定する
                checkResultBean.checkResult = false;
                // 「処理コード」に「000931」を設定する
                checkResultBean.processingCode = ResultCdConstants.CODE_000931;
                super.debug(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.MPRCOD0002,
                    this.getClassName(),
                    "checkRestCommon",
                );
                return checkResultBean;
            }
            /** STEP5.0対応　追加　START */
            // テナント毎同時接続数チェックを行う
            // CheckResultBean tenantConResult = checkTenantConnectCount(sequenceNo, tenantId);
            const tenantConResult = await this.checkTenantConnectCount(
                sequenceNo,
                tenantId,
                functionType,
            );
            // チェック結果より判定
            if (!tenantConResult.checkResult) {
                // 「チェック結果」にfalseを設定
                checkResultBean.checkResult = false;
                // 処理コードを設定
                checkResultBean.processingCode = tenantConResult.processingCode;
                return checkResultBean;
            }
            /** STEP5.0対応　追加　END */
        }
        /* #493対応 追加 END */

        /** STEP1.2b版対応　修正　START */
        // 予約前オーダ　或いは　即時オーダの場合
        if (Constants.ORDER_TYPE_2 !== orderType) {
            // API処理ID払い出しを行う
            let apiID = "";
            try {
                // APIシーケンスを取得する
                apiID = await this.getAPIID();
            } catch (e: any) {
                // STEP20.0版対応　変更　START
                if (isSQLException(e)) {
                    // 「チェック結果」にfalseを設定する
                    checkResultBean.checkResult = false;
                    // 「処理コード」に「999999」を設定する
                    checkResultBean.processingCode =
                        ResultCdConstants.CODE_999999;
                    super.error(
                        e,
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APCOME0402,
                        functionType,
                    );
                    return checkResultBean;
                    // STEP20.0版対応　変更　END
                }

                // 「チェック結果」にfalseを設定する
                checkResultBean.checkResult = false;
                // 「処理コード」に「999999」を設定する
                checkResultBean.processingCode = ResultCdConstants.CODE_999999;

                super.error(
                    e,
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APCOME0401,
                    e,
                );
                super.debug(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.MPRCOD0002,
                    this.getClassName(),
                    "checkRestCommon",
                );
                return checkResultBean;
            } finally {
                checkResultBean.others = apiID;
            }
        } else {
            checkResultBean.others = reserveSoId;
        }
        /** STEP1.2b版対応　修正　END */

        // 入力パラメータに対して、フォーマットチェックを行う
        let resultChkHedFmt = false;

        resultChkHedFmt = this.checkHeaderFormat(
            sequenceNo,
            senderSystemId,
            apiKey,
            functionType,
            tenantId,
        );

        // 変数「ヘッダフォーマットチェック結果」より判定する
        if (!resultChkHedFmt) {
            // 「チェック結果」にfalseを設定する
            checkResultBean.checkResult = false;
            // 「処理コード」に「000921」を設定する
            checkResultBean.processingCode = ResultCdConstants.CODE_000921;
            super.debug(
                tenantId,
                sequenceNo,
                MsgKeysConstants.MPRCOD0002,
                this.getClassName(),
                "checkRestCommon",
            );
            return checkResultBean;
        }

        // URL毎の機能種別のチェック
        if (localFunctionType !== functionType) {
            super.warn(
                tenantId,
                sequenceNo,
                MsgKeysConstants.APCOMW0102,
                functionType,
                url,
            );
            // 「チェック結果」にfalseを設定する
            checkResultBean.checkResult = false;
            // 「処理コード」に「000921」を設定する
            checkResultBean.processingCode = ResultCdConstants.CODE_000922;
            super.debug(
                tenantId,
                sequenceNo,
                MsgKeysConstants.MPRCOD0002,
                this.getClassName(),
                "checkRestCommon",
            );
            return checkResultBean;
        }

        // NOTE commented out in original code
        /* #493対応 削除 START */
        /* STEP1.2b版対応　追加　START */
        // 予約前オーダ　或いは　即時オーダの場合
        // if (!Constants.ORDER_TYPE_2.equals(orderType)) {
        /* STEP1.2b版対応　追加　END */
        // 同時接続数チェックを行う
        // boolean resultChkConCnt = checkConnectCoun(tenantId, sequenceNo);
        //
        // 「同時接続数チェック結果」より判定する
        // if (!resultChkConCnt) {
        //
        // 「チェック結果」にfalseを設定する
        // checkResultBean.setCheckResult(false);
        // 「処理コード」に「000931」を設定する
        //         checkResultBean.setProcessingCode(ResultCdConstants.CODE_000931);
        //         super.debug(tenantId, sequenceNo,MsgKeysConstants.MPRCOD0002, this.getClass().getName(), "checkRestCommon");
        //         return checkResultBean;
        //     }
        // }
        /* #493対応 削除 END */

        // 認証チェックを行う
        let resultChkAut = ResultCdConstants.CODE_000000;
        try {
            resultChkAut = await this.checkAuthentication(
                sequenceNo,
                apiKey,
                tenantId,
            );
        } catch (e: any) {
            // / STEP20.0版対応　変更　START
            if (isSQLException(e)) {
                // 「チェック結果」にfalseを設定する
                checkResultBean.checkResult = false;
                // 「処理コード」に「999999」を設定する
                checkResultBean.processingCode = ResultCdConstants.CODE_999999;
                super.error(
                    e,
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APCOME0402,
                    functionType,
                );
                return checkResultBean;
                // STEP20.0版対応　変更　END
            }

            // 「チェック結果」にfalseを設定する
            checkResultBean.checkResult = false;
            // 「処理コード」に「999999」を設定する
            checkResultBean.processingCode = ResultCdConstants.CODE_999999;

            super.error(
                e,
                tenantId,
                sequenceNo,
                MsgKeysConstants.APCOME0401,
                e,
            );
            super.debug(
                tenantId,
                sequenceNo,
                MsgKeysConstants.MPRCOD0002,
                this.getClassName(),
                "checkRestCommon",
            );
            return checkResultBean;
        }

        // 変数「認証チェック結果」より判定する
        if (resultChkAut !== ResultCdConstants.CODE_000000) {
            // 「チェック結果」にfalseを設定する
            checkResultBean.checkResult = false;
            // 「処理コード」に「返却コード」を設定する
            checkResultBean.processingCode = resultChkAut;
            super.debug(
                tenantId,
                sequenceNo,
                MsgKeysConstants.MPRCOD0002,
                this.getClassName(),
                "checkRestCommon",
            );
            return checkResultBean;
        }

        let ipAddressChkResult = ResultCdConstants.CODE_000000;
        try {
            /** STEP5.0対応　追加　START */
            // IPアドレス許可チェックを行う
            ipAddressChkResult = await this.checkSourceIpAddress(
                tenantId,
                sourceIpAddress,
                sequenceNo,
            );
        } catch (e: any) {
            if (isSQLException(e)) {
                // 「チェック結果」にfalseを設定する
                checkResultBean.checkResult = false;
                // 「処理コード」に「999999」を設定する
                checkResultBean.processingCode = ResultCdConstants.CODE_999999;
                super.error(
                    e,
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APCOME0402,
                    functionType,
                );
                return checkResultBean;
            }
            throw e;
        }

        // チェック結果判定
        if (ipAddressChkResult !== ResultCdConstants.CODE_000000) {
            // 「チェック結果」にfalseを設定する
            checkResultBean.checkResult = false;
            // 「処理コード」に「返却コード」を設定する
            checkResultBean.processingCode = ipAddressChkResult;
            super.debug(
                tenantId,
                sequenceNo,
                MsgKeysConstants.MPRCOD0002,
                this.getClassName(),
                "checkRestCommon",
            );
            return checkResultBean;
        }
        /** STEP5.0対応　追加　END */

        // 返却値編集
        checkResultBean.checkResult = true;
        checkResultBean.processingCode = ResultCdConstants.CODE_000000;

        // super.debug(tenantId, sequenceNo, MsgKeysConstants.MPRCOD0002, this.getClass().getName(), "checkRestCommon");

        return checkResultBean;
    }

    /**
     * 同時接続数チェック
     *
     * @param tenantId
     * @param sequenceNo
     * @return
     */
    private async checkConnectCoun(tenantId: string, sequenceNo: string) {
        // super.debug(
        //     tenantId,
        //     sequenceNo,
        //     MsgKeysConstants.APCOMD0001,
        //     this.getClassName(),
        //     "checkConnectCoun",
        //     "tenantId=" + tenantId + ",sequenceNo=" + sequenceNo,
        // );
        // /* #493対応 MOD START */
        // // 接続数増加
        // increase and get current connection count within single transaction
        const checkConCount = await this.modConCount(true);
        // /* #493対応 MOD END */

        // // 同時接続数チェックを行う
        // /* #493対応 MOD START */
        // const checkConCount = await this.getConCount();
        if (checkConCount > this.connectionMax) {
            super.warn(tenantId, sequenceNo, "APCOMW0201", checkConCount);
            return false;
        }

        // /* #493対応 MOD END */
        // super.debug(
        //     tenantId,
        //     sequenceNo,
        //     MsgKeysConstants.APCOMD0002,
        //     this.getClassName(),
        //     "checkConnectCoun",
        // );

        return true;
    }

    /**
     * 同時接続数加減算処理
     * 同時接続数を、排他制御した上で加算・減算する
     * @param isAdd true:加算 false：減算
     * @returns 現在の同時接続数 (加算・減算後)
     */
    public async modConCount(isAdd: boolean = false): Promise<number> {
        const now = Math.floor(Date.now() / 1000);
        const windowStart =
            now - AppConfig.getCoreConfig(this.context).THROTTLING.WINDOW_SIZE;

        const redisClient = await useRedis(this.context);
        const multi = redisClient.multi();
        if (isAdd) {
            // Add new connection to the list
            multi.zadd("conCount", now, this.context.invocationId);
            // Set the key's TTL to the window size (timeout)
            // This will automatically remove the key after the window size and
            // reset connection count to 0 when no new connections are made
            multi.expire(
                "conCount",
                AppConfig.getCoreConfig(this.context).THROTTLING.WINDOW_SIZE,
            );
        } else {
            // Remove requests that are longer than timeout (optional cleanup)
            multi.zremrangebyscore("conCount", "-inf", windowStart.toString());
            // remove current connection from the list
            multi.zrem("conCount", this.context.invocationId);
        }
        multi.zcount("conCount", windowStart.toString(), "+inf");
        return new Promise((resolve, reject) => {
            // tslint:disable-next-line:no-floating-promises
            multi.exec((err, replies) => {
                if (err) {
                    reject(err);
                } else {
                    // return current connection count from zcount (response format is [null, count])
                    // this.context.warn(">> current count", +replies[2][1]);
                    resolve(+replies[2][1]);
                }
            });
        });
    }

    /**
     * 同時接続数参照処理
     * 現在の同時接続数を外部から参照する
     */
    public async getConCount(): Promise<number> {
        const redisClient = await useRedis(this.context);

        // calculate window size
        const now = Math.floor(Date.now() / 1000);
        const windowStart =
            now - AppConfig.getCoreConfig(this.context).THROTTLING.WINDOW_SIZE;

        // count all conCount entries within the window size (handle `zremrangebyscore` failure)
        return new Promise((resolve, reject) => {
            // tslint:disable-next-line:no-floating-promises
            redisClient.zcount(
                "conCount",
                windowStart.toString(),
                "+inf",
                (err, reply) => {
                    // this.context.warn(">> getConCount", reply);
                    if (err) {
                        reject(err);
                    } else {
                        resolve(reply);
                    }
                },
            );
        });

        // count all conCount entries (assuming `zremrangebyscore` won't fail, faster implementation)
        // return new Promise((resolve, reject) => {
        //     redisClient.zcard("conCount", (err, reply) => {
        //         if (err) {
        //             reject(err);
        //         } else {
        //             resolve(reply);
        //         }
        //     });
        // });
    }

    /**
     * テナント毎同時接続数チェックを行う
     * @param sequenceNo 送信番号
     * @param tenantId テナントID
     * @param functionType 機能種別
     * @return 結果Bean
     */
    // private CheckResultBean checkTenantConnectCount(String sequenceNo, String tenantId) {
    private async checkTenantConnectCount(
        sequenceNo: string,
        tenantId: string,
        functionType: string,
    ) {
        // NOTE may need to? update tenant logic to match all connection count logic (more strict check)
        // super.debug(
        //     tenantId,
        //     sequenceNo,
        //     MsgKeysConstants.APCOMD0001,
        //     this.getClassName(),
        //     "checkTenantConnectCount",
        //     "tenantId=" + tenantId + ",sequenceNo=" + sequenceNo,
        // );
        const result: CheckResultBean = {
            checkResult: false,
            processingCode: "",
            others: "",
            errorMassage: "",
        };

        // 	//テナント情報取得
        let tenant: TenantsEntity = null;
        try {
            tenant = await this.apiCommonDAO.getTenantsEntity(tenantId);
            // 	//テナント情報の有無を判定
            if (tenant === null) {
                super.error(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APCOME0211,
                    tenantId,
                );
                result.checkResult = false;
                result.processingCode = ResultCdConstants.CODE_000934;
                result.others = "テナント毎の同時接続数判定に失敗しました。";
                return result;
            }
        } catch (e) {
            if (isSQLException(e)) {
                super.error(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APCOME0211,
                    tenantId,
                );
                super.error(
                    e,
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APCOME0402,
                    functionType,
                );
                result.checkResult = false;
                result.processingCode = ResultCdConstants.CODE_000934;
                result.others = "DBアクセスリトライ回数に失敗しました。";
                return result;
            }
            throw e;
        }
        // テナント同時接続数チェック・加算を実施
        // テナントの現同時接続数取得
        const count = await this.getTenantConCount(tenantId);
        // 接続数比較
        // if (tenant.getTenantMaxConnection().compareTo(count) <= 0) {
        if (count >= tenant.tenantMaxConnection) {
            super.warn(
                tenantId,
                sequenceNo,
                MsgKeysConstants.APCOMW0213,
                tenantId,
                count,
            );
            result.checkResult = false;
            result.processingCode = ResultCdConstants.CODE_000936;
            result.others = "テナント毎の同時接続数を超過しました。";
            return result;
        }
        // 同時接続数加算
        // tenantConCount.put(tenantId, count + 1);
        await this.tenantConCountHelper(tenantId, true);

        // super.debug(
        //     tenantId,
        //     sequenceNo,
        //     MsgKeysConstants.APCOMD0002,
        //     this.getClassName(),
        //     "checkTenantConnectCount",
        // );
        result.checkResult = true;
        return result;
    }

    /**
     * Helper method to increment / decrement connection count of a tenant (added during refactoring)
     *
     * use similar logic as modConCount but with tenant specific key
     * @param tenantId
     * @param isAdd
     * @returns number of latest connection
     */
    private async tenantConCountHelper(
        tenantId: string,
        isAdd: boolean = false,
    ): Promise<void> {
        const tenantKey = `conCount:${tenantId}`;
        const now = Math.floor(Date.now() / 1000);
        const windowStart =
            now - AppConfig.getCoreConfig(this.context).THROTTLING.WINDOW_SIZE;

        const redisClient = await useRedis(this.context);

        const multi = redisClient.multi();
        if (isAdd) {
            // Add new connection to the list
            multi.zadd(tenantKey, now, this.context.invocationId);
            // Set the key's TTL to the window size (timeout)
            // This will automatically remove the key after the window size and
            // reset connection count to 0 when no new connections are made
            multi.expire(
                tenantKey,
                AppConfig.getCoreConfig(this.context).THROTTLING.WINDOW_SIZE,
            );
        } else {
            // Remove requests that are longer than timeout (optional cleanup)
            multi.zremrangebyscore(tenantKey, "-inf", windowStart.toString());
            // remove current connection from the list
            multi.zrem(tenantKey, this.context.invocationId);
        }
        return new Promise((resolve, reject) => {
            // tslint:disable-next-line:no-floating-promises
            multi.exec((err, replies) => {
                if (err) {
                    reject(err);
                } else {
                    resolve();
                }
            });
        });
    }

    /**
     * 指定したテナントIDのテナント毎同時接続数を取得する
     * @param tenantId テナントID
     * @return テナント毎同時接続数
     */
    public async getTenantConCount(tenantId: string): Promise<number> {
        // NOTE this is a copy of getConCount but with tenant specific key
        if (!tenantId) {
            throw new Error("Tenant ID is required");
        }
        const tenantKey = `conCount:${tenantId}`;
        const redisClient = await useRedis(this.context);

        // calculate window size
        const now = Math.floor(Date.now() / 1000);
        const windowStart =
            now - AppConfig.getCoreConfig(this.context).THROTTLING.WINDOW_SIZE;

        // count all tenantKey entries within the window size (handle `zremrangebyscore` failure)
        return new Promise((resolve, reject) => {
            // tslint:disable-next-line:no-floating-promises
            redisClient.zcount(
                tenantKey,
                windowStart.toString(),
                "+inf",
                (err, reply) => {
                    // this.context.warn(">> getTenantConCount", reply);
                    if (err) {
                        reject(err);
                    } else {
                        resolve(reply);
                    }
                },
            );
        });

        // count all tenantKey entries (assuming `zremrangebyscore` won't fail, faster implementation)
        // return new Promise((resolve, reject) => {
        //     redisClient.zcard(tenantKey, (err, reply) => {
        //         if (err) {
        //             reject(err);
        //         } else {
        //             resolve(reply);
        //         }
        //     });
        // });
    }

    /**
     * テナント同時接続数の減算
     * @param tenantId テナントID
     * @param processCode 処理コード
     */
    public async decTenantConnectCount(
        tenantId: string,
        processCode: string,
    ): Promise<void> {
        const excList = [
            ResultCdConstants.CODE_000931,
            ResultCdConstants.CODE_000934,
            ResultCdConstants.CODE_000936,
        ];
        if (!excList.includes(processCode)) {
            await this.tenantConCountHelper(tenantId, false);
        }
    }

    /**
     * テナント同時接続数のリセット
     * @param tenantId
     */
    public async resetTenantConnectCount(
        tenantId: string,
    ): Promise<void> {
        // Reset tenant connection count
        const redisClient = await useRedis(this.context);
        const tenantKey = `conCount:${tenantId}`;

        // Get all connections for the tenant
        const connections = await redisClient.zrange(tenantKey, 0, -1);
        if (connections.length > 0) {
            // Remove all connections for the tenant
            await redisClient.zrem(tenantKey, ...connections);
        }
    }

    /**
     * API処理ID払い出し
     *
     * @return
     */
    private async getAPIID() {
        // super.debug(
        //     "",
        //     "",
        //     MsgKeysConstants.APCOMD0001,
        //     this.getClassName(),
        //     "getAPIID",
        // );

        // シーケンスを取得する
        const sequence = await this.apiCommonDAO.getSequence();

        // シーケンス編集
        /** STEP1.3版対応　変更　START */
        const seq = this.apiProcessID + MvnoUtil.addZeroBeforeStr(sequence, 12);
        /** STEP1.3版対応　変更　END */

        // super.debug(
        //     "",
        //     "",
        //     MsgKeysConstants.APCOMD0002,
        //     this.getClassName(),
        //     "getAPIID",
        // );

        return seq;
    }

    /**
     * ヘッダフォーマットチェック
     *
     * @param sequenceNo
     * @param senderSystemId
     * @param apiKey
     * @param functionType
     * @return
     */
    private checkHeaderFormat(
        sequenceNo: string,
        senderSystemId: string,
        apiKey: string,
        functionType: string,
        tenantId: string,
    ) {
        // super.debug(
        //     tenantId,
        //     sequenceNo,
        //     MsgKeysConstants.APCOMD0001,
        //     this.getClassName(),
        //     "checkHeaderFormat",
        //     "sequenceNo=" +
        //         sequenceNo +
        //         ",senderSystemId=" +
        //         senderSystemId +
        //         ",apiKey=" +
        //         apiKey +
        //         ",functionType=" +
        //         functionType +
        //         ",tenantId=" +
        //         tenantId,
        // );

        // 送信番号必須チェック
        if (CheckUtil.checkIsNotNull(sequenceNo)) {
            super.warn(
                tenantId,
                sequenceNo,
                MsgKeysConstants.APCOMW0101,
                "sequenceNo",
                sequenceNo,
            );
            return false;
        }

        // 送信番号桁数チェック
        if (!CheckUtil.checkLength(sequenceNo, 7, true)) {
            super.warn(
                tenantId,
                sequenceNo,
                MsgKeysConstants.APCOMW0101,
                "sequenceNo",
                sequenceNo,
            );
            return false;
        }

        /** STEP1.3版対応　追加　START */
        // 半角英数字チェック
        if (!CheckUtil.checkIsSemiangleAlphanumic(sequenceNo)) {
            super.warn(
                tenantId,
                sequenceNo,
                MsgKeysConstants.APCOMW0101,
                "sequenceNo",
                sequenceNo,
            );
            return false;
        }
        /** STEP1.3版対応　追加　END */

        // 送信元システムID必須チェック
        if (CheckUtil.checkIsNotNull(senderSystemId)) {
            super.warn(
                tenantId,
                sequenceNo,
                MsgKeysConstants.APCOMW0101,
                "senderSystemId",
                senderSystemId,
            );
            return false;
        }

        // 送信元システムID桁数チェック
        if (!CheckUtil.checkLength(senderSystemId, 4, true)) {
            super.warn(
                tenantId,
                sequenceNo,
                MsgKeysConstants.APCOMW0101,
                "senderSystemId",
                senderSystemId,
            );
            return false;
        }

        /** STEP1.3版対応　追加　START */
        // 半角英数字チェック
        if (!CheckUtil.checkIsSemiangleAlphanumic(senderSystemId)) {
            super.warn(
                tenantId,
                sequenceNo,
                MsgKeysConstants.APCOMW0101,
                "senderSystemId",
                senderSystemId,
            );
            return false;
        }
        /** STEP1.3版対応　追加　END */

        /** STEP1.2a版対応　変更　START */
        // if (!privateFlag) {
        // API認証キー必須チェック
        if (CheckUtil.checkIsNotNull(apiKey)) {
            super.warn(
                tenantId,
                sequenceNo,
                MsgKeysConstants.APCOMW0101,
                "apiKey",
                apiKey,
            );
            return false;
        }

        // API認証キー桁数チェック
        if (!CheckUtil.checkLength(apiKey, 72, true)) {
            super.warn(
                tenantId,
                sequenceNo,
                MsgKeysConstants.APCOMW0101,
                "apiKey",
                apiKey,
            );
            return false;
        }

        // NOTE commented out in original code
        /** STEP1.3版対応　追加　START */
        // 半角英数字チェック
        // if (!CheckUtil.checkIsSemiangleAlphanumic(apiKey)) {
        // super.warn(tenantId, sequenceNo, MsgKeysConstants.APCOMW0101, "apiKey", apiKey);
        // return false;
        // }
        /** STEP1.3版対応　追加　END */

        // 機能種別必須チェック
        if (CheckUtil.checkIsNotNull(functionType)) {
            super.warn(
                tenantId,
                sequenceNo,
                MsgKeysConstants.APCOMW0101,
                "functionType",
                functionType,
            );
            return false;
        }

        // 機能種別桁数チェック
        if (!CheckUtil.checkLength(functionType, 2, true)) {
            super.warn(
                tenantId,
                sequenceNo,
                MsgKeysConstants.APCOMW0101,
                "functionType",
                functionType,
            );
            return false;
        }

        // 機能種別数値チェック
        if (!CheckUtil.checkIsNum(functionType)) {
            super.warn(
                tenantId,
                sequenceNo,
                MsgKeysConstants.APCOMW0101,
                "functionType",
                functionType,
            );
            return false;
        }

        // 機能種別範囲チェック
        // Integer.parseInt(functionType) == 0
        if (!Number.isInteger(+functionType) || +functionType === 0) {
            super.warn(
                tenantId,
                sequenceNo,
                MsgKeysConstants.APCOMW0101,
                "functionType",
                functionType,
            );
            return false;
        }

        // テナントID必須チェック
        if (CheckUtil.checkIsNotNull(tenantId)) {
            super.warn(
                tenantId,
                sequenceNo,
                MsgKeysConstants.APCOMW0101,
                "tenantId",
                tenantId,
            );
            return false;
        }

        // テナントID桁数チェック
        /** STEP1.3版仕様変更対応　変更　START */
        if (!CheckUtil.checkRangeLength(tenantId, 6, 10)) {
            /** STEP1.3版仕様変更対応　変更　END */
            super.warn(
                tenantId,
                sequenceNo,
                MsgKeysConstants.APCOMW0101,
                "tenantId",
                tenantId,
            );
            return false;
        }

        /** STEP1.3版対応　追加　START */
        // 半角英数字チェック
        if (!CheckUtil.checkIsSemiangleAlphanumic(tenantId)) {
            super.warn(
                tenantId,
                sequenceNo,
                MsgKeysConstants.APCOMW0101,
                "tenantId",
                tenantId,
            );
            return false;
        }
        /** STEP1.3版対応　追加　END */

        super.debug(
            tenantId,
            sequenceNo,
            MsgKeysConstants.APCOMD0002,
            this.getClassName(),
            "checkHeaderFormat",
        );

        return true;
    }

    /**
     * 認証チェック
     *
     * @param sequenceNo
     * @param apiKey
     * @param tenantId
     * @return
     * @/throws NoSuchAlgorithmException
     */
    private async checkAuthentication(
        sequenceNo: string,
        apiKey: string,
        tenantId: string,
    ) {
        // super.debug(
        //     tenantId,
        //     sequenceNo,
        //     MsgKeysConstants.APCOMD0001,
        //     this.getClassName(),
        //     "checkAuthentication",
        //     "sequenceNo=" + sequenceNo + ",apiKey=" + apiKey + ",tenantId=",
        //     tenantId,
        // );

        // テナントIDよりハッシュ化パスワード取得を行う
        const tenantsEntity = await this.apiCommonDAO.getTenants(tenantId);
        if (tenantsEntity === null || "" === tenantsEntity.hashedPassword) {
            super.warn(
                tenantId,
                sequenceNo,
                MsgKeysConstants.APCOMW0301,
                tenantId,
            );
            return ResultCdConstants.CODE_000941;
        }

        const hashPassword = tenantsEntity.hashedPassword;

        // MD2で送信番号のハッシュ化を行う
        const hashSeq = this.digest(sequenceNo);

        if (hashSeq === null) {
            return ResultCdConstants.CODE_999999;
        }

        // 「API認証キー（生成）」を作成する
        const apiKeyNew = hashPassword + RESTCommon.STR_API_KEY + hashSeq;

        if (apiKeyNew !== apiKey) {
            super.warn(
                tenantId,
                sequenceNo,
                MsgKeysConstants.APCOMW0302,
                apiKey,
                apiKeyNew,
            );

            return ResultCdConstants.CODE_000942;
        }

        super.debug(
            tenantId,
            sequenceNo,
            MsgKeysConstants.APCOMD0002,
            this.getClassName(),
            "checkAuthentication",
        );

        return ResultCdConstants.CODE_000000;
    }

    /**
     * ハッシュ化処理を行う
     *
     * @param sequenceNo
     * @return
     */
    private digest(sequenceNo: string) {
        // super.debug(
        //     "",
        //     sequenceNo,
        //     MsgKeysConstants.APCOMD0001,
        //     this.getClassName(),
        //     "digest",
        //     "sequenceNo=" + sequenceNo,
        // );

        // MessageDigest messageDigest = MessageDigest.getInstance("MD2");
        // byte[] seqByte = messageDigest.digest(sequenceNo.getBytes());
        // messageDigest.reset();
        // StringBuffer sb = new StringBuffer();
        // for(int i = 0; i < seqByte.length; i++) {
        //     sb.append(Integer.toHexString((seqByte[i] >> 4) & 0x0f)); // 上位ニブルを1文字へ
        //     sb.append(Integer.toHexString( seqByte[i] & 0x0f)); // 下位ニブルを1文字へ
        // }
        // String seqNo = new String(sb);
        const seqNo = md2(sequenceNo) as string;
        super.debug(
            "",
            sequenceNo,
            MsgKeysConstants.APCOMD0002,
            this.getClassName(),
            "digest",
        );

        return seqNo;
    }

    /**
     * IPアドレス許可チェック
     * @param tenantId テナントID
     * @param sourceIpAddress 接続元IPアドレス
     * @param sequenceNo 送信番号
     * @return 処理コード
     */
    public async checkSourceIpAddress(
        tenantId: string,
        sourceIpAddress: string,
        sequenceNo: string,
    ) {
        // NOTE no need to check after refactoring
        return ResultCdConstants.CODE_000000;
        // // IPアドレスリスト取得
        // const ipaddressList = await this.apiCommonDAO.getIpAddressList(
        //     tenantId,
        // );
        // // 0件ならば処理終了
        // if (ipaddressList.length === 0) {
        //     return ResultCdConstants.CODE_000000;
        // }
        // // IPアドレスフォーマットチェック
        // for (const ipaddress of ipaddressList) {
        //     if (!Check.checkIpAddressFmt(ipaddress.ipAddress)) {
        //         super.error(
        //             tenantId,
        //             sequenceNo,
        //             MsgKeysConstants.APCOME0311,
        //             tenantId,
        //             ipaddress.ipAddress,
        //             ipaddress.subnetMask,
        //         );
        //         return ResultCdConstants.CODE_000946;
        //     }
        //     if (
        //         ipaddress.subnetMask < Constants.SUBNET_MASK_MIN ||
        //         ipaddress.subnetMask > Constants.SUBNET_MASK_MAX
        //     ) {
        //         super.error(
        //             tenantId,
        //             sequenceNo,
        //             MsgKeysConstants.APCOME0311,
        //             tenantId,
        //             ipaddress.ipAddress,
        //             ipaddress.subnetMask,
        //         );
        //         return ResultCdConstants.CODE_000946;
        //     }
        // }
        // // バッチ処理接続元IPアドレスの取得
        // let allowedIpAddrList: string[] = [];
        // if (CheckUtil.checkIsNotNull(this.allowedSourceIpAddress)) {
        //     super.error(
        //         tenantId,
        //         sequenceNo,
        //         MsgKeysConstants.APCOME0312,
        //         this.allowedSourceIpAddress,
        //     );
        //     return ResultCdConstants.CODE_000947;
        // } else {
        //     allowedIpAddrList = this.allowedSourceIpAddress.split(",");
        // }
        // // リスト件数判定
        // if (allowedIpAddrList.length > Constants.ALLOWED_IP_MAX_CNT) {
        //     super.error(
        //         tenantId,
        //         sequenceNo,
        //         MsgKeysConstants.APCOME0312,
        //         this.allowedSourceIpAddress,
        //     );
        //     return ResultCdConstants.CODE_000947;
        // }
        // // バッチ処理接続元IPアドレスのフォーマットチェック
        // for (const allowedIpAddr of allowedIpAddrList) {
        //     if (!Check.checkIpAddressFmt(allowedIpAddr)) {
        //         super.error(
        //             tenantId,
        //             sequenceNo,
        //             MsgKeysConstants.APCOME0312,
        //             this.allowedSourceIpAddress,
        //         );
        //         return ResultCdConstants.CODE_000947;
        //     }
        // }
        // // IPアドレス許可チェック
        // for (const ipaddress of ipaddressList) {
        //     if (
        //         Check.checkIpAddressPermit(
        //             sourceIpAddress,
        //             ipaddress.ipAddress,
        //             ipaddress.subnetMask,
        //         )
        //     ) {
        //         return ResultCdConstants.CODE_000000;
        //     }
        // }
        // // バッチIPアドレス許可チェック
        // for (const allowedIpAddr of allowedIpAddrList) {
        //     if (
        //         Check.checkIpAddressPermit(sourceIpAddress, allowedIpAddr, null)
        //     ) {
        //         return ResultCdConstants.CODE_000000;
        //     }
        // }
        // // 許可されていないIPアドレスの場合
        // super.warn(
        //     tenantId,
        //     sequenceNo,
        //     MsgKeysConstants.APCOMW0313,
        //     tenantId,
        //     sourceIpAddress,
        // );
        // return ResultCdConstants.CODE_000948;
    }

    /**
     * 自動計算回線グループ情報追加処理
     *
     * @param soId SOID
     * @param functionType 機能種別
     * @param processCode 処理コード
     * @param lineId 回線ID
     * @param groupId 回線グループID
     * @param tenantId テナントID
     * @param sequenceNo 送信番号
     * @param orderType オーダ種別
     * @throws Exception 例外
     */
    public async addAutoModbucketLineGroup(
        soId: string,
        functionType: string,
        processCode: string,
        lineId: string,
        groupId: string,
        tenantId: string,
        sequenceNo: string,
        orderType: string,
    ): Promise<void> {
        await this.addAutoModbucketLineGroupWithReturn(
            soId,
            functionType,
            processCode,
            lineId,
            groupId,
            tenantId,
            sequenceNo,
            orderType,
            -1,
        );
    }

    public async addAutoModbucketLineGroupWithReturn(
        soId: string,
        functionType: string,
        processCode: string,
        lineId: string,
        groupId: string,
        tenantId: string,
        sequenceNo: string,
        orderType: string,
        mnpInFlg: number,
    ): Promise<boolean> {
        // REST API共通処理が呼び出されたシステム日付
        const systemDateTime = MvnoUtil.getDateTimeNow();
        // 変数「対象回線グループIDリスト」を定義する
        let lineGroupIdList: string[] = [];
        // 変数「対象回線グループID」を定義する
        let lineGroupId: string = null;

        // STEP13.0対応　追加　START
        // 変数「対象回線IDリスト」を定義する
        let lineIdList: string[] = [];
        // STEP13.0対応　追加　END

        // STEP13.0対応　変更　START
        // STEP15.0対応　変更　START
        // 機能種別を判定する
        if (
            !"06".equals(functionType) &&
            !"14".equals(functionType) &&
            !"51".equals(functionType) &&
            !"20".equals(functionType) &&
            !"58".equals(functionType)
        ) {
            // STEP15.0対応　変更　END
            // STEP13.0対応　変更　END
            return false;
        }

        // オーダ種別を判定する
        if (
            !Constants.ORDER_TYPE_0.equals(orderType) &&
            !Constants.ORDER_TYPE_2.equals(orderType)
        ) {
            return false;
        }

        // 処理コードの値を判定する
        if (!"000000".equals(processCode)) {
            // 処理コードが「000000」以外の場合

            if ("51".equals(functionType)) {
                // API個別機能が「“51”：仮登録回線追加」の場合
                if (
                    !(
                        MvnoConstants.MNP_IN_TYPE_SAME_MVNE_YES === mnpInFlg &&
                        Constants.ORDER_TYPE_0.equals(orderType) &&
                        (ResultCdConstants.CODE_510110.equals(processCode) ||
                            ResultCdConstants.CODE_510301.equals(processCode) ||
                            ResultCdConstants.CODE_510401.equals(processCode))
                    )
                ) {
                    return false;
                }
                // MNP転入フラグ："2"(同一MVNE転入)であり、即時オーダであり、処理コード：「510110」or「510301」or「510401」の場合は処理を継続
            } else {
                // API個別機能が「“51”：仮登録回線追加」以外の場合
                return false;
            }
        }

        // 機能種別に応じた回線グループ情報を取得する
        // 回線プラン変更の場合
        if ("06".equals(functionType)) {
            try {
                // 電文の回線番号がグループに所属しているか確認する
                lineGroupIdList = await this.apiCommonDAO.getGroupIdList(
                    lineId,
                );
            } catch (e) {
                // DBアクセスリトライエラーの場合
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    super.error(
                        e,
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APCOME0402,
                        functionType,
                    );
                    return false;
                }
                throw e;
            }

            // 変数「対象回線グループIDリスト」の値で判定する
            if (lineGroupIdList.length <= 0) {
                return false;
            }

            lineGroupId = lineGroupIdList[0];
        } else if ("14".equals(functionType)) {
            let tenantsEntity: TenantsEntity = null;
            try {
                // 電文のテナントIDに紐付くテナント種別を取得する
                tenantsEntity = await this.apiCommonDAO.getTenants(tenantId);
            } catch (e) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    super.error(
                        e,
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APCOME0402,
                        functionType,
                    );
                    return false;
                }
                throw e;
            }

            // 変数「対象回線テナント情報」の値で判定する
            if (1 !== tenantsEntity.tenantType) {
                lineGroupId = groupId;
            } else {
                return false;
            }
            // STEP13.0対応　変更　START
        } else if ("51".equals(functionType)) {
            // STEP13.0対応　変更　END
            // 仮登録回線追加の場合

            // MNP転入フラグ："2"(同一MVNE転入)以外の場合
            if (MvnoConstants.MNP_IN_TYPE_SAME_MVNE_YES !== mnpInFlg) {
                return false;
            }

            try {
                // 電文の回線番号がグループに所属しているか確認する
                lineGroupIdList = await this.apiCommonDAO.getGroupIdList(
                    lineId,
                );
            } catch (e) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    super.error(
                        e,
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APCOME0402,
                        functionType,
                    );
                    return false;
                }
                throw e;
            }

            // 変数「対象回線グループIDリスト」の値で判定する
            if (lineGroupIdList.length <= 0) {
                return false;
            }
            lineGroupId = lineGroupIdList[0];
            // STEP13.0対応　変更　START
            // STEP15.0対応　変更　START
        } else if ("20".equals(functionType)) {
            // STEP15.0対応　変更　END
            // 回線グループプラン変更の場合

            try {
                // 電文のグループIDに回線が所属しているか確認
                lineIdList = await this.apiCommonDAO.getLineLineGroupToLineId(
                    groupId,
                );
            } catch (e) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    super.error(
                        e,
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APCOME0402,
                        functionType,
                    );
                    return false;
                }
                throw e;
            }

            // 変数「対象回線IDリスト」の値で判定する
            if (lineIdList.length <= 0) {
                return false;
            }

            // 登録内容の設定
            lineId = lineIdList[0]; // 回線ID
            lineGroupId = groupId; // 電文のグループID

            // STEP15.0対応　変更　START
        } else {
            // 利用中断／再開／サスペンドの場合

            // 回線がグループに所属しているかの確認はAPIで行っているため、ここでは行わない

            // 登録内容の設定
            lineGroupId = groupId; // 電文のグループID
        }
        // STEP15.0対応　変更　END
        // STEP13.0対応　変更　END

        // 自動計算回線グループ情報登録
        // 回線グループ情報を登録する
        try {
            await this.apiCommonDAO.insertAutoModBucketLineGroup(
                lineId,
                lineGroupId,
                soId,
                systemDateTime,
            );
        } catch (e) {
            if (isSQLException(e)) {
                // DBアクセスリトライエラーの場合
                super.error(
                    e,
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APCOME0402,
                    functionType,
                );
                return false;
            }
            super.error(
                e,
                tenantId,
                sequenceNo,
                MsgKeysConstants.MPRCOE0301,
                soId,
            );
            return false;
        }
        return true;
    }

    /**
     * REST共通チェック(外部用)
     *
     * NOTE: Changed to static so that it can be used from BaseHandler
     * @param sequenceNo
     * @param senderSystemId
     * @param apiKey
     * @param functionType
     * @param tenantId
     * @param localFunctionType
     * @param url
     * @param orderType
     * @param reserveSoId
     * @return
     */
    public async checkRestCommonInside(
        sequenceNo: string,
        senderSystemId: string,
        apiKey: string,
        functionType: string,
        tenantId: string,
        localFunctionType: string,
        url: string,
        orderType: string,
        reserveSoId: string,
    ) {
        super.debug(
            tenantId,
            sequenceNo,
            MsgKeysConstants.MPRCOD0001,
            this.getClassName(),
            "checkRestCommonInside",
            "sequenceNo=" +
                sequenceNo +
                ",senderSystemId=" +
                senderSystemId +
                ",apiKey=" +
                apiKey +
                ",functionType=" +
                functionType +
                ",tenantId=" +
                tenantId,
        );

        const checkResultBean: CheckResultBean = {
            checkResult: false,
            processingCode: "",
            others: "",
            errorMassage: "",
        };

        /* #494対応　追加　START */
        // 予約前オーダ　或いは　即時オーダの場合
        if (Constants.ORDER_TYPE_2 !== orderType) {
            // 同時接続数チェックを行う
            const resultChkConCnt: boolean = await this.checkConnectCoun(
                tenantId,
                sequenceNo,
            );

            // 「同時接続数チェック結果」より判定する
            if (!resultChkConCnt) {
                // 「チェック結果」にfalseを設定する
                checkResultBean.checkResult = false;
                // 「処理コード」に「000931」を設定する
                checkResultBean.processingCode = ResultCdConstants.CODE_000931;
                super.debug(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.MPRCOD0002,
                    this.getClassName(),
                    "checkRestCommonInside",
                );
                checkResultBean.errorMassage = "API同時接続数を超過しました。";
                return checkResultBean;
            }
            /** STEP5.0対応　追加　START */
            // テナント毎同時接続数チェックを行う
            const tenantConResult = await this.checkTenantConnectCount(
                sequenceNo,
                tenantId,
                functionType,
            );
            // チェック結果より判定
            if (!tenantConResult.checkResult) {
                // 「チェック結果」にfalseを設定
                checkResultBean.checkResult = false;
                // 処理コードを設定
                checkResultBean.processingCode = tenantConResult.processingCode;
                // エラーメッセージを設定
                checkResultBean.errorMassage = tenantConResult.others;
                return checkResultBean;
            }
            /** STEP5.0対応　追加　END */
        }
        /* #493対応 追加 END */
        /** #372　修正	　START */
        // 予約前オーダ　或いは　即時オーダの場合
        if (Constants.ORDER_TYPE_2 !== orderType) {
            // API処理ID払い出しを行う
            let apiID = "";
            try {
                // APIシーケンスを取得する
                apiID = await this.getAPIID();
            } catch (e: any) {
                // 「チェック結果」にfalseを設定する
                checkResultBean.checkResult = false;
                // 「処理コード」に「999999」を設定する
                checkResultBean.processingCode = ResultCdConstants.CODE_999999;

                super.error(
                    e,
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APCOME0401,
                    e,
                );
                super.debug(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.MPRCOD0002,
                    this.getClassName(),
                    "checkRestCommonInside",
                );
                return checkResultBean;
            } finally {
                checkResultBean.others = apiID;
            }
        } else {
            checkResultBean.others = reserveSoId;
        }
        /** #372　修正　END */
        // 入力パラメータに対して、フォーマットチェックを行う
        let resultChkHedFmt = false;

        resultChkHedFmt = this.checkHeaderFormat(
            sequenceNo,
            senderSystemId,
            apiKey,
            functionType,
            tenantId,
        );

        // 変数「ヘッダフォーマットチェック結果」より判定する
        if (!resultChkHedFmt) {
            // 「チェック結果」にfalseを設定する
            checkResultBean.checkResult = false;
            // 「処理コード」に「000921」を設定する
            checkResultBean.processingCode = ResultCdConstants.CODE_000921;
            super.debug(
                tenantId,
                sequenceNo,
                MsgKeysConstants.MPRCOD0002,
                this.getClassName(),
                "checkRestCommonInside",
            );
            checkResultBean.errorMassage =
                "フォーマットチェックエラーが発生しました。";
            return checkResultBean;
        }

        // URL毎の機能種別のチェック
        if (localFunctionType !== functionType) {
            super.warn(
                tenantId,
                sequenceNo,
                MsgKeysConstants.APCOMW0102,
                functionType,
                url,
            );
            // 「チェック結果」にfalseを設定する
            checkResultBean.checkResult = false;
            // 「処理コード」に「000921」を設定する
            checkResultBean.processingCode = ResultCdConstants.CODE_000922;
            super.debug(
                tenantId,
                sequenceNo,
                MsgKeysConstants.MPRCOD0002,
                this.getClassName(),
                "checkRestCommonInside",
            );
            checkResultBean.errorMassage =
                "フォーマットチェックエラーが発生しました。";
            return checkResultBean;
        }

        // NOTE commented out in original code
        /* #494対応 削除 START */
        // /** #372　修正　START */
        // // 予約前オーダ　或いは　即時オーダの場合
        // if (!Constants.ORDER_TYPE_2.equals(orderType)) {
        //     // 同時接続数チェックを行う
        //     boolean resultChkConCnt = checkConnectCoun(tenantId, sequenceNo);

        //     // 「同時接続数チェック結果」より判定する
        //     if (!resultChkConCnt) {

        //         // 「チェック結果」にfalseを設定する
        //         checkResultBean.setCheckResult(false);
        //         // 「処理コード」に「000931」を設定する
        //         checkResultBean.setProcessingCode(ResultCdConstants.CODE_000931);
        //         super.debug(tenantId, sequenceNo,MsgKeysConstants.MPRCOD0002, this.getClass().getName(), "checkRestCommonInside");
        //         checkResultBean.setErrorMassage("API同時接続数を超過しました。");
        //         return checkResultBean;
        //     }
        // }
        // /** #372　修正　END */
        /* #494対応　削除　END */
        // 返却値編集
        checkResultBean.checkResult = true;
        checkResultBean.processingCode = ResultCdConstants.CODE_000000;

        super.debug(
            tenantId,
            sequenceNo,
            MsgKeysConstants.MPRCOD0002,
            this.getClassName(),
            "checkRestCommonInside",
        );

        return checkResultBean;
    }

    /**
     * 廃止元回線の回線回線グループ削除
     * @param soId SOID
     * @param tenantId テナントID
     * @param sequenceNo 送信番号
     * @param functionType 機能ID
     * @param lineId 回線ID
     */
    public async deleteAbolitionOrgLineLineGroups(
        soId: string,
        tenantId: string,
        sequenceNo: string,
        lineId: string,
        functionType: string,
    ): Promise<void> {
        let linelineGroupCntList = 0;
        let isLocked = false;
        let isSuccess = false;
        let tx: Transaction = null;
        let canRollback = false; // added this since rollback twice will throw error
        try {
            const sequelize = await usePsql();
            await retryQuery(
                this.context,
                "deleteAbolitionOrgLineLineGroups",
                async () => {
                    // DBに接続、トランザクションの開始
                    tx = await sequelize.transaction();
                    canRollback = true;
                    // 廃止元回線の回線回線グループ削除処理
                    try {
                        // REST電文の回線番号がグループに所属しているか確認する。
                        linelineGroupCntList =
                            await this.apiCommonDAO.getLineLineGroupsCount(
                                lineId,
                                tx,
                            );
                    } catch (e) {
                        throw e;
                    }
                    // 変数「対象回線回線グループ件数」の値で判定する
                    if (linelineGroupCntList === 0) {
                        // 0件の場合は処理を中断
                        return;
                    }

                    // 該当レコードの行ロックを取得した後、レコードの削除を行う。
                    for (
                        let linelockTime = 0;
                        linelockTime < 5;
                        linelockTime++
                    ) {
                        try {
                            // 回線回線グループ情報テーブルのロックを取得する
                            const lineNos =
                                await this.apiCommonDAO.getLineLineGroupsLock(
                                    lineId,
                                    tx,
                                );
                            if (lineNos.length > 0) {
                                // 行ロックが取得できた
                                isLocked = true;
                            }
                            break;
                        } catch (e) {
                            if (isLockNotAvailableError(e)) {
                                // ロック取得に失敗した場合
                                if (linelockTime === 4) {
                                    // 最大リトライ回数を超えたらエラーで処理中断
                                    // ログ出力
                                    super.error(
                                        e as Error,
                                        tenantId,
                                        sequenceNo,
                                        MsgKeysConstants.MPRCOE0401,
                                        soId,
                                    );
                                    return;
                                }
                                await tx.rollback();
                                canRollback = false;
                                try {
                                    // 最大待ちミリ秒数分、待機する
                                    await new Promise((resolve) => {
                                        setTimeout(resolve, 1000);
                                    });
                                } catch (e) {
                                    // 処理不要
                                }
                                // 行ロックを取得できなかった場合、行ロックを再取得
                                tx = await sequelize.transaction();
                                canRollback = true;
                                continue;
                            } else if (isSQLException(e)) {
                                // DBアクセス時にエラー発生した場合
                                throw e;
                            } else {
                                // ログ出力
                                super.error(
                                    e as Error,
                                    tenantId,
                                    sequenceNo,
                                    MsgKeysConstants.MPRCOE0401,
                                    soId,
                                );
                                return;
                            }
                        }
                    }

                    // ロックが取得できた場合
                    if (isLocked) {
                        try {
                            // 回線回線グループ情報テーブルをロック取得できた値で削除
                            await this.apiCommonDAO.deleteLineLineGroups(
                                lineId,
                                tx,
                            );
                            // 例外が発生しなければ、正常に更新できたとみなす
                            isSuccess = true;
                        } catch (e) {
                            if (isSQLException(e)) {
                                throw e;
                            } else {
                                // ログ出力
                                super.error(
                                    e as Error,
                                    tenantId,
                                    sequenceNo,
                                    MsgKeysConstants.MPRCOE0401,
                                    soId,
                                );
                                return;
                            }
                        }
                    }
                    // break; in original code = stop retry logic
                    return;
                },
                this.apDbRetryMaxCnt,
                this.apDbRetryInterval,
            );
        } catch (e) {
            super.error(
                e as Error,
                tenantId,
                sequenceNo,
                MsgKeysConstants.MPRCOE0401,
                soId,
            );
            return;
        } finally {
            if (tx) {
                if (isSuccess) {
                    // 正常に更新できた場合は、コミットする
                    await tx.commit();
                } else if (canRollback) {
                    // エラーが発生した場合はロールバックする
                    await tx.rollback();
                }
            }
        }
    }
}
