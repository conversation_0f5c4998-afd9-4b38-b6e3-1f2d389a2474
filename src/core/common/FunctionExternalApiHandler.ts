import ExtendedInvocationContext from "@/types/ExtendedInvocationContext";
import { HttpRequest, HttpResponseInit } from "@azure/functions";
import BaseOutputDto from "@/core/dto/BaseOutputDto";
import FunctionHandlerBase from "@/core/common/FunctionHandler";
import ResponseHeader from "@/core/dto/ResponseHeader";
import ResultCdConstants from "@/core/constant/ResultCdConstants";
import MvnoUtil from "@/core/common/MvnoUtil";
import CheckResultBean from "@/core/dto/CheckResultBean";
import BaseValidationResult from "@/types/baseValidationResult";
import { ObjectSchema } from "joi";
// import { getConfigAsNumber } from "@/helpers/configHelper";

export default abstract class FunctionExternalApiHandlerBase extends FunctionHandlerBase {
    /**
     * DBアクセスリトライ回数
     */
    // protected static apDbRetryMaxCnt = getConfigAsNumber("ApDbRetryMaxCnt");

    /**
     * DBアクセスリトライ間隔
     */
    // protected static apDbRetryInterval = getConfigAsNumber("ApDbRetryInterval");

    public static CommonCheckFailedHandler = async (
        request: HttpRequest,
        context: ExtendedInvocationContext,
        errorMessage: string,
    ): Promise<HttpResponseInit> => {
        // returnEdit
        const response: BaseOutputDto = {
            jsonBody: { responseHeader: context.responseHeader },
        };
        return { jsonBody: response.jsonBody };
    };

    public static CommonCheckCatchErrorHandler = async (
        request: HttpRequest,
        context: ExtendedInvocationContext,
        error: Error,
    ): Promise<HttpResponseInit> => {
        context.error(error);
        const errorResponse: ResponseHeader = {
            processCode: ResultCdConstants.CODE_999999,
            apiProcessID: context?.responseHeader?.apiProcessID || "",
            sequenceNo: context?.jsonBody?.requestHeader?.sequenceNo,
            receivedDate: MvnoUtil.getDateTimeNow(),
        };
        return {
            jsonBody: {
                responseHeader: errorResponse,
            },
        };
    }

    public static schema: ObjectSchema = null;
}