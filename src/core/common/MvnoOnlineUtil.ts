import * as dateFns from "date-fns";
import StringUtils from "@/core/common/StringUtils";

export default class MvnoOnlineUtil {
    /**
     * 日付フォーマット変更(Dateから)<BR>
     *
     * @param date
     * @param format
     * @return
     */
    public static dateFormat(date: Date, format: string): string;
    /**
     * 日付フォーマット変更(Stringから)<BR>
     *
     * @param str   (yyyyMMdd or yyyyMMddHHmmss)               // note: null will be treated as Date object, thus calling dateFormat(Date, format)
     * @param format
     * @return
     */
    // tslint:disable-next-line:unified-signatures
    public static dateFormat(str: string, format: string): string;
    /**
     * 日付フォーマット
     * <pre>
     * 1:yyyy/MM/dd:HH:mm(MMとddが1桁可能) → yyyy/MM/dd HH:mm(MMとddが2桁)
     * 2:yyyy/MM/dd(MMとddが1桁可能)       → yyyy/MM/dd(MMとddが2桁)
     * </pre>
     * @param input 日付文字列
     * @return フォーマットした日付文字列
     */
    public static dateFormat(input: string): string;

    public static dateFormat(arg1: Date | string | null, format?: string): string {
        if (format === undefined) {                             // dateFormat(string)
            if (arg1 !== null && arg1 !== undefined && arg1 instanceof Date) {
                throw new Error("Invalid argument: arg1");      // Does not have dateFormat(Date) overload
            }
            let result = "";
            let input = arg1 as string;
            if (!StringUtils.isBlank(input)) {
                if (input.match("\\d{4}\\/\\d{1,2}\\/\\d{1,2}\\:\\d{1,2}\\:\\d{1,2}")) {
                    const format = "yyyy/MM/dd HH:mm";
                    input = input.replace("\\", "/");
                    const datePart = input.substring(0, input.indexOf(":")).split("/");
                    const timePart = input.substring(input.indexOf(":") + 1, input.length).split(":");
                    const cal = new Date();
                    cal.setFullYear(parseInt(datePart[0], 10));
                    cal.setMonth(parseInt(datePart[1], 10) - 1);
                    cal.setDate(parseInt(datePart[2], 10));
                    cal.setHours(parseInt(timePart[0], 10));
                    cal.setMinutes(parseInt(timePart[1], 10));
                    result = dateFns.format(
                        cal,
                        format,
                    );
                } else if (input.match("\\d{4}\\/\\d{1,2}\\/\\d{1,2}")) {
                    const format = "yyyy/MM/dd";
                    input = input.replace("\\", "/");
                    const datePart = input.split("/");
                    const cal = new Date();
                    cal.setFullYear(parseInt(datePart[0], 10));
                    cal.setMonth(parseInt(datePart[1], 10) - 1);
                    cal.setDate(parseInt(datePart[2], 10));
                    result = dateFns.format(
                        cal,
                        format,
                    );
                }
            }
            return result;
        } else {
            if (arg1 === null || arg1 === undefined) {          // note: null will be treated as Date object, thus calling dateFormat(Date, format)
                return "";
            } else if (arg1 instanceof Date) {                  // dateFormat(Date, format)
                const date = arg1 as Date;
                return dateFns.format(
                    date,
                    format,
                );
            } else {                                            // dateFormat(String, format)
                // date is string
                const str = arg1 as string;
                if (StringUtils.isEmpty(str)) {
                    return str;
                }
                if (str.length !== 8 && str.length !== 14) {
                    return str;
                }
                let returnStr = format;
                returnStr = returnStr.replace("yyyy", str.substring(0, 4));
                returnStr = returnStr.replace("MM", str.substring(4, 6));
                returnStr = returnStr.replace("dd", str.substring(6, 8));
                if (str.length === 14) {
                    returnStr = returnStr.replace("HH", str.substring(8, 10));
                    returnStr = returnStr.replace("mm", str.substring(10, 12));
                    returnStr = returnStr.replace("ss", str.substring(12, 14));
                } else {
                    returnStr = returnStr.replace("HH", "00");
                    returnStr = returnStr.replace("mm", "00");
                    returnStr = returnStr.replace("ss", "00");
                }
                return returnStr;
            }
        }
    }
}