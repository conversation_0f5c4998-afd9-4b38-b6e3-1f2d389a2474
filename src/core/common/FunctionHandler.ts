import ExtendedInvocationContext from "@/types/ExtendedInvocationContext";
import { HttpRequest, HttpResponseInit } from "@azure/functions";

export default abstract class FunctionHandlerBase {
    /**
     * API properties which will be used in BaseHandler (checkRestCommon validation)
     */
    public static Meta: FunctionMeta;

    /**
     * API handler which will be called after BaseHandler
     */
    public static async Handler(
        request: HttpRequest,
        context: ExtendedInvocationContext,
    ): Promise<HttpResponseInit> {
        throw new Error("Method not implemented.");
    }
}

export interface FunctionMeta {
    /** function number in xx format */
    localFunctionType: string;
    /** url of the function (from Constants.ts) */
    url: string;
    /** get order type from request body */
    getOrderType: (param: any) => string;
    /** name of the API */
    processName: string;
}
