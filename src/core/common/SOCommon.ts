import APICommonDAO from "@/core/dao/APICommonDAO";
import APISoDAO from "@/core/dao/APISoDAO";
import SOObject from "@/core/dto/SOObject";
import ServiceOrdersEntity from "@/core/entity/ServiceOrdersEntity";
import AbstractMvnoBaseCommon from "./AbstractMvnoBaseCommon";
import TenantManage from "./TenantManage";
import CheckUtil from "./CheckUtil";
import Constants from "../constant/Constants";
import { isValid, parse } from "date-fns";
import StringUtils from "./StringUtils";
import "@/types/string.extension";
import { isNone } from "@/utils";

export default class SOCommon extends AbstractMvnoBaseCommon {
    /**
     * APISoDAO
     */
    private aPISoDAO: APISoDAO = new APISoDAO(this.request, this.context);

    /**
     * API共通DAO
     */
    private aPICommonDAO: APICommonDAO = new APICommonDAO(
        this.request,
        this.context,
    );

    /** STEP3.0版対応　追加　START */
    /**
     * テナント管理
     */
    private tenantManage: TenantManage = new TenantManage(
        this.request,
        this.context,
    );
    /** STEP3.0版対応　追加　END */

    /**
     * SO管理
     * @param input SO管理オブジェクト
     */
    public async soCommon(input: SOObject) {
        this.context.log("SOCommon start", input.getServiceOrderId());
        /** 358　追加　START */
        // 入力パラメータチェック
        // テナントIDチェック
        if (
            StringUtils.isEmpty(input.getTenantId()) ||
            !CheckUtil.checkRangeLength(input.getTenantId(), 6, 10) ||
            !CheckUtil.checkIsSemiangleAlphanumic(input.getTenantId())
        ) {
            this.context.log("SOCommon tenantId check", input.getTenantId());
            return;
        }

        // 回線IDDチェック
        if (
            !CheckUtil.checkIsNotNull(input.getLineId()) &&
            // STEP17.0版対応　変更　START
            (!CheckUtil.checkRangeLength(input.getLineId(), 1, 14) ||
                // STEP17.0版対応　変更　END
                !CheckUtil.checkIsSemiangleAlphanumic(input.getLineId()))
        ) {
            this.context.log("SOCommon lineId check", input.getLineId());
            return;
        }
        // 回線グループIDDチェック
        /** STEP4.0版対応　変更　START */
        if (
            !CheckUtil.checkIsNotNull(input.getLineGroupId()) &&
            (!CheckUtil.checkRangeLength(input.getLineGroupId(), 1, 9) ||
                !CheckUtil.checkIsSemiangleAlphanumic(input.getLineGroupId()))
        ) {
            this.context.log(
                "SOCommon lineGroupId check",
                input.getLineGroupId(),
            );
            return;
        }
        /** STEP4.0版対応　変更　END */
        // 予約日チェック
        if (
            !CheckUtil.checkIsNotNull(input.getReserveDate()) &&
            (!CheckUtil.checkLength(input.getReserveDate(), 16, true) ||
                !CheckUtil.checkDateFmt(
                    input.getReserveDate(),
                    "yyyy/MM/dd HH:mm",
                ))
        ) {
            this.context.log(
                "SOCommon reserveDate check",
                input.getReserveDate(),
            );
            return;
        }
        // オプションプランIDチェック
        if (
            !CheckUtil.checkIsNotNull(input.getOptionPlanId()) &&
            !CheckUtil.checkIsNum(input.getOptionPlanId())
        ) {
            this.context.log(
                "SOCommon optionPlanId check",
                input.getOptionPlanId(),
            );
            return;
        }
        /** 358　追加　END */
        // 変数「種別」を定義する
        // NOTE `type` is reserved keyword, so using `type_` instead
        let type_: string = "";
        // 変数「オーダステータス」を定義する
        let orderStatus: string = "";
        // 変数「内容詳細」を定義する
        let detail: string = "";
        // 変数「オプションプラン説明」を定義する
        let optionPlanExplain: string = "";
        /** STEP4.0版対応　追加　START */
        // 変数「判定種別」を定義する
        let decisionType: string = "";
        /** STEP4.0版対応　追加　END */
        // 変数「種別」設定
        type_ = this.getType(input);
        // 変数「オーダステータス」設定
        orderStatus = this.getOrderStatus(input);
        // オプションプラン説明取得
        // オプションプランIDを判定する
        this.context.log("SOCommon optionPlanId/functionType", {
            optionPlanId: input.getOptionPlanId(),
            functionType: input.getFunctionType(),
        });
        if (
            StringUtils.isNotEmpty(input.getOptionPlanId()) &&
            ("03".equals(input.getFunctionType()) ||
                "04".equals(input.getFunctionType()))
        ) {
            // 回線オプションプラン説明を取得する
            optionPlanExplain = await this.aPISoDAO.getOptionPlans(
                +input.getOptionPlanId(),
            );
        } else if (
            StringUtils.isNotEmpty(input.getOptionPlanId()) &&
            ("10".equals(input.getFunctionType()) ||
                "12".equals(input.getFunctionType()))
        ) {
            // 回線オプションプラン説明を取得する
            optionPlanExplain = await this.aPISoDAO.getGruopOptionPlans(
                +input.getOptionPlanId(),
            );
        } else {
            optionPlanExplain = null;
        }

        // 内容詳細設定
        detail = this.getDetail(input, optionPlanExplain);
        /** STEP4.0版対応　追加　START */
        // 判定種別設定
        decisionType = null;
        if ("51".equals(input.getFunctionType())) {
            // 51：仮登録回線追加の場合
            if (
                !(
                    "0".equals(input.getMnpInFlag()) ||
                    "2".equals(input.getMnpInFlag())
                )
            ) {
                // 0 or 2以外の場合（null、tag無しも含む）
                decisionType = "3";
            } else if ("2".equals(input.getMnpInFlag())) {
                // 2：同一MVNE転入の場合
                decisionType = "4";
            } else if ("0".equals(input.getMnpInFlag())) {
                // 0：MNP転入の場合
                decisionType = "2";
            }
        } else if (
            "52".equals(input.getFunctionType()) &&
            "1".equals(input.getMnpOutFlag())
        ) {
            decisionType = "1";
        } else if (
            "52".equals(input.getFunctionType()) &&
            !"1".equals(input.getMnpOutFlag())
        ) {
            decisionType = "0";
        }

        /** STEP4.0版対応　追加　END */
        /** STEP1.2b版対応　修正　START */
        // 予約実行オーダ　或いは　予約キャンセル呼び出し場合
        if (Constants.ORDER_TYPE_2.equals(input.getOrderType())) {
            /** STEP4.0版対応　変更　START */
            // SO管理テーブルにデータ更新
            this.context.log(
                "SOCommon updServiceOrders",
                input.getServiceOrderId(),
                decisionType,
            );
            await this.updServiceOrders(input, orderStatus, detail);
            /** STEP4.0版対応　変更　END */
        } else {
            this.context.log("SOCommon orderType", input.getOrderType());
            // SO管理テーブルにデータ挿入
            /** STEP4.0版対応　変更　START */
            /**** #295対応  変更　START*******/
            if (
                StringUtils.isNotEmpty(input.getServiceOrderId()) &&
                StringUtils.isNotEmpty(type_)
            ) {
                this.context.log(
                    "SOCommon insertServiceOrders",
                    input.getServiceOrderId(),
                    decisionType,
                );
                await this.insertServiceOrders(
                    input,
                    type_,
                    orderStatus,
                    detail,
                    decisionType,
                );
            }
            /**** #295対応  変更　END*******/
            /** STEP4.0版対応　変更　END */
        }
        /** STEP1.2b版対応　修正　END */
    }

    /**
     * 機能種別、操作区分より、変数「種別」の設定値を取得する。
     * @param input SO管理オブジェクト
     * @return type 種別
     */
    private getType(input: SOObject): string {
        //     String type = "";
        let type_ = "";

        // 変数「種別」設定
        if ("03".equals(input.getFunctionType())) {
            type_ = "クーポン追加";
        } else if ("04".equals(input.getFunctionType())) {
            if ("1".equals(input.getOperationDivision())) {
                type_ = "クーポンON";
            } else if ("0".equals(input.getOperationDivision())) {
                type_ = "クーポンOFF";
            }
        } else if ("05".equals(input.getFunctionType())) {
            if ("1".equals(input.getOperationDivision())) {
                type_ = "アクティベート";
            } else if ("0".equals(input.getOperationDivision())) {
                type_ = "ディアクティベート";
            }
        } else if ("06".equals(input.getFunctionType())) {
            type_ = "プラン変更";
        } else if ("10".equals(input.getFunctionType())) {
            type_ = "回線グループクーポン追加";
        } else if ("12".equals(input.getFunctionType())) {
            if ("1".equals(input.getOperationDivision())) {
                type_ = "回線グループクーポンON";
            } else if ("0".equals(input.getOperationDivision())) {
                type_ = "回線グループクーポンOFF";
            }
        } else if ("13".equals(input.getFunctionType())) {
            if ("1".equals(input.getOperationDivision())) {
                type_ = "回線グループ作成";
            } else if ("0".equals(input.getOperationDivision())) {
                type_ = "回線グループ廃止";
            }
        } else if ("14".equals(input.getFunctionType())) {
            if ("1".equals(input.getOperationDivision())) {
                type_ = "回線グループ所属回線変更(割当)";
            } else if ("0".equals(input.getOperationDivision())) {
                type_ = "回線グループ所属回線変更(割当解除)";
            }
        } else if ("15".equals(input.getFunctionType())) {
            type_ = "回線グループ基本容量変更";
            /** STEP4.0d版対応　追加　START */
        } else if ("19".equals(input.getFunctionType())) {
            type_ = "データ譲渡";
            /** STEP4.0d版対応　追加　END */
            // STEP13.0版対応　追加　START
        } else if ("20".equals(input.getFunctionType())) {
            type_ = "回線グループプラン変更";
            // STEP13.0版対応　追加　END
            /** STEP3.0版対応　追加　START */
        } else if ("51".equals(input.getFunctionType())) {
            type_ = "仮登録回線追加";
            /** STEP3.0版対応　追加　END */
            /** STEP4.0版対応　追加　START */
        } else if ("52".equals(input.getFunctionType())) {
            type_ = "回線廃止";
        } else if ("54".equals(input.getFunctionType())) {
            type_ = "回線オプション_NW暗証番号変更";
        } else if ("55".equals(input.getFunctionType())) {
            type_ = "回線黒化";
            /** STEP4.0版対応　追加　END */
            /** STEP5.0対応　追加　START */
        } else if ("53".equals(input.getFunctionType())) {
            type_ = "回線SIM再発行";
            /** STEP5.0対応　追加　END */
            // STEP12.0版対応　追加　START */
        } else if ("57".equals(input.getFunctionType())) {
            if ("1".equals(input.getOperationDivision())) {
                type_ = "回線プラン追加";
            } else if ("0".equals(input.getOperationDivision())) {
                type_ = "回線プラン削除";
            }
            // STEP12.0版対応　追加　END */
            // STEP15.0版対応　追加　START */
        } else if ("58".equals(input.getFunctionType())) {
            if ("0".equals(input.getOperationDivision())) {
                type_ = "利用状態変更（利用中）";
            } else if ("1".equals(input.getOperationDivision())) {
                type_ = "利用状態変更（利用中断中）";
            } else if ("2".equals(input.getOperationDivision())) {
                type_ = "利用状態変更（サスペンド中）";
            }
            // STEP15.0版対応　追加　END */
            // STEP21.0版対応　追加　START
        } else if ("59".equals(input.getFunctionType())) {
            type_ = "0035でんわプラン変更";
            // STEP21.0版対応　追加　END
            // STEP17.0版対応　追加　START
        } else if ("60".equals(input.getFunctionType())) {
            type_ = "NW契約変更";
            // STEP17.0版対応　追加　START
        }

        return type_;
    }

    /**
     * 機能種別、操作区分より、変数「オーダステータス」の設定値を取得する
     * @param input SO管理オブジェクト
     * @return orderStatus オーダステータス
     */
    private getOrderStatus(input: SOObject): string {
        let orderStatus = "";
        /** STEP1.2b版対応　修正　START */
        // 予約実行オーダの場合
        if (Constants.ORDER_TYPE_2.equals(input.getOrderType())) {
            // 処理コード000000の場合
            if ("000000".equals(input.getOrderStatus())) {
                orderStatus = "完了";
            } else {
                orderStatus = "失敗";
            }
        } else {
            // 予約前オーダの場合
            if (Constants.ORDER_TYPE_1.equals(input.getOrderType())) {
                // 処理コード000000の場合
                if ("000000".equals(input.getOrderStatus())) {
                    orderStatus = "予約中";
                } else {
                    orderStatus = "失敗";
                }
                // 即時オーダの場合
            } else {
                // 処理コード000000の場合
                if ("000000".equals(input.getOrderStatus())) {
                    orderStatus = "完了";
                } else {
                    orderStatus = "失敗";
                }
            }
        }
        /** STEP1.2b版対応　修正　START */
        return orderStatus;
    }

    /**
     * 変数「内容詳細」の設定値を取得する
     * @param input SO管理オブジェクト
     * @param optionPlanExplain 変数「オプションプラン説明」
     * @return 内容詳細
     */
    private getDetail(input: SOObject, optionPlanExplain: string): string {
        let detail = "";
        // 変数「文字列リスト」を定義する
        const wordList: string[] = [];

        // 1.主属性を変数「文字列リスト」へ追加する
        if (
            "03".equals(input.getFunctionType()) ||
            "04".equals(input.getFunctionType()) ||
            "05".equals(input.getFunctionType()) ||
            "06".equals(input.getFunctionType()) ||
            /** STEP4.0d版対応　追加　START */
            "19".equals(input.getFunctionType()) ||
            /** STEP4.0d版対応　追加　END */
            /** STEP3.0版対応　追加　START */
            "51".equals(input.getFunctionType()) ||
            /** STEP3.0版対応　追加　END */
            /** STEP4.0版対応　追加　START */
            "52".equals(input.getFunctionType()) ||
            /** STEP5.0対応　追加　START */
            "53".equals(input.getFunctionType()) ||
            /** STEP5.0対応　追加　END */
            "54".equals(input.getFunctionType()) ||
            "55".equals(input.getFunctionType()) ||
            /** STEP4.0版対応　追加　END */
            // STEP15.0版対応　追加　START
            "58".equals(input.getFunctionType()) ||
            // STEP15.0版対応　追加　END
            // STEP17.0版対応　追加　START
            "60".equals(input.getFunctionType())
        ) {
            // STEP17.0版対応　追加　END
            // 変数「文字列リスト」に入力パラメータ．「SO管理オブジェクト」．「回線ID」を追加する
            wordList.push(input.getLineId());
        } else if (
            "10".equals(input.getFunctionType()) ||
            "12".equals(input.getFunctionType()) ||
            "13".equals(input.getFunctionType()) ||
            "14".equals(input.getFunctionType()) ||
            "15".equals(input.getFunctionType()) ||
            // STEP13.0版対応　追加　START
            "20".equals(input.getFunctionType())
        ) {
            // STEP13.0版対応　追加　START
            // 変数「文字列リスト」に入力パラメータ．「SO管理オブジェクト」．「回線グループID」を追加する
            wordList.push(input.getLineGroupId());
        }

        // 2.オプションIDを変数「文字列リスト」へ追加する
        if (
            "03".equals(input.getFunctionType()) ||
            "04".equals(input.getFunctionType()) ||
            "10".equals(input.getFunctionType()) ||
            "12".equals(input.getFunctionType())
        ) {
            // 変数「文字列リスト」に入力パラメータ．「SO管理オブジェクト」．「回線ID」を追加する
            wordList.push(input.getOptionPlanId());
        }

        // 3.オプション説明を変数「文字列リスト」へ追加する
        if (
            StringUtils.isNotEmpty(optionPlanExplain) &&
            ("03".equals(input.getFunctionType()) ||
                "04".equals(input.getFunctionType()) ||
                "10".equals(input.getFunctionType()) ||
                "12".equals(input.getFunctionType()))
        ) {
            // 変数「文字列リスト」に変数「オプションプラン説明」を追加する
            wordList.push(optionPlanExplain);
        }

        // 4.実施内容を変数「文字列リスト」へ追加する
        if (
            "05".equals(input.getFunctionType()) &&
            "050202".equals(input.getOrderStatus())
        ) {
            if ("1".equals(input.getOperationDivision())) {
                // 変数「文字列リスト」に「アクティベート済」を追加する
                wordList.push("アクティベート済");
            } else if ("0".equals(input.getOperationDivision())) {
                // 変数「文字列リスト」に「ディアクティベート済」を追加する
                wordList.push("ディアクティベート済");
            }
        } else if ("06".equals(input.getFunctionType())) {
            // 変数「文字列リスト」に入力パラメータ．「SO管理オブジェクト」．「変更前プランID」＋「、」
            // ＋入力パラメータ．「SO管理オブジェクト」．「変更後プランID」を追加する
            if (CheckUtil.checkIsNotNull(input.getChangeOldplanId())) {
                input.setChangeOldplanId("");
            }
            if (CheckUtil.checkIsNotNull(input.getChangeNewplanId())) {
                input.setChangeNewplanId("");
            }
            wordList.push(
                input.getChangeOldplanId() + "、" + input.getChangeNewplanId(),
            );
        } else if ("14".equals(input.getFunctionType())) {
            // 変数「文字列リスト」に入力パラメータ．「SO管理オブジェクト」．「回線所属変更回線ID」を追加する
            wordList.push(input.getChangePlanId());
        } else if ("15".equals(input.getFunctionType())) {
            // 変数「文字列リスト」に入力パラメータ．「SO管理オブジェクト」．「基本容量」を追加する
            wordList.push(input.getBasicCapacity());
            /** STEP4.0d版対応　追加　START */
        } else if ("19".equals(input.getFunctionType())) {
            // 変数「文字列リスト」に入力パラメータ．「SO管理オブジェクト」．「譲渡先回線番号」＋「、」
            // ＋入力パラメータ．「SO管理オブジェクト」．「譲渡データ量」を追加する
            if (CheckUtil.checkIsNotNull(input.getDestinationLineNo())) {
                input.setDestinationLineNo("");
            }
            if (CheckUtil.checkIsNotNull(input.getGiftData())) {
                input.setGiftData("");
            }
            wordList.push(
                input.getDestinationLineNo() + "、" + input.getGiftData(),
            );
            /** STEP4.0d版対応　追加　END */
            // STEP13.0版対応　追加　START
        } else if ("20".equals(input.getFunctionType())) {
            // 変数「文字列リスト」に入力パラメータ．「SO管理オブジェクト」．「変更前卸ポータルグループプランID」＋「、」
            // ＋入力パラメータ．「SO管理オブジェクト」．「変更後卸ポータルグループプランID」を追加する
            if (CheckUtil.checkIsNotNull(input.getChangeOldGroupPlanId())) {
                input.setChangeOldGroupPlanId("");
            }
            if (CheckUtil.checkIsNotNull(input.getChangeNewGroupPlanId())) {
                input.setChangeNewGroupPlanId("");
            }
            wordList.push(
                input.getChangeOldGroupPlanId() +
                    "、" +
                    input.getChangeNewGroupPlanId(),
            );
            // STEP15.0版対応　追加　START
        } else if ("58".equals(input.getFunctionType())) {
            // 利用中断／再開／サスペンド機能の場合
            if ("0".equals(input.getOperationDivision())) {
                // 変数「文字列リスト」に「利用中」を追加する
                wordList.push("利用中");
            } else if ("1".equals(input.getOperationDivision())) {
                // 変数「文字列リスト」に「利用中断中」を追加する
                wordList.push("利用中断中");
            } else if ("2".equals(input.getOperationDivision())) {
                // 変数「文字列リスト」に「サスペンド中」を追加する
                wordList.push("サスペンド中");
            }
            // STEP15.0版対応　追加　END
            // STEP21.0版対応　追加　START
        } else if ("59".equals(input.getFunctionType())) {
            // 0035でんわプラン変更の場合
            // 変数「文字列リスト」に変更後0035でんわプランIDを追加する
            wordList.push(input.getChangeNewVoicePlanId());
            // STEP21.0版対応　追加　END
            // STEP17.0版対応　追加　START
        } else if ("60".equals(input.getFunctionType())) {
            // NW契約変更機能の場合
            // 変数「文字列リスト」に入力パラメータ．「SO管理オブジェクト」．「変更前アクセス方式」＋「、」
            // ＋入力パラメータ．「SO管理オブジェクト」．「変更後アクセス方式」を追加する
            if (CheckUtil.checkIsNotNull(input.getChangeOldAccess())) {
                input.setChangeOldAccess("");
            }
            if (CheckUtil.checkIsNotNull(input.getChangeNewAccess())) {
                input.setChangeNewAccess("");
            }
            wordList.push(
                input.getChangeOldAccess() + "、" + input.getChangeNewAccess(),
            );
        }
        // STEP17.0版対応　追加　END
        // STEP13.0版対応　追加　END

        // 5.処理コードを変数「文字列リスト」へ追加する
        if (
            (!"000000".equals(input.getOrderStatus()) &&
                ("03".equals(input.getFunctionType()) ||
                    "04".equals(input.getFunctionType()) ||
                    "06".equals(input.getFunctionType()) ||
                    "10".equals(input.getFunctionType()) ||
                    "12".equals(input.getFunctionType()) ||
                    "13".equals(input.getFunctionType()) ||
                    "14".equals(input.getFunctionType()) ||
                    "15".equals(input.getFunctionType()) ||
                    /** STEP4.0d版対応　追加　START */
                    "19".equals(input.getFunctionType()) ||
                    /** STEP4.0d版対応　追加　END */
                    // STEP13.0版対応　追加　START */
                    "20".equals(input.getFunctionType()) ||
                    // STEP13.0版対応　追加　END */
                    /** STEP3.0版対応　追加　START */
                    "51".equals(input.getFunctionType()) ||
                    /** STEP3.0版対応　追加　END */
                    /** STEP4.0版対応　追加　START */
                    "52".equals(input.getFunctionType()) ||
                    /** STEP5.0対応　追加　START */
                    "53".equals(input.getFunctionType()) ||
                    /** STEP5.0対応　追加　END */
                    "54".equals(input.getFunctionType()) ||
                    "55".equals(input.getFunctionType()) ||
                    /** STEP4.0版対応　追加　END */
                    // STEP12.0版対応　追加　START */
                    "57".equals(input.getFunctionType()) ||
                    // STEP12.0版対応　追加　END */
                    // STEP15.0版対応　追加　START */
                    "58".equals(input.getFunctionType()) ||
                    // STEP15.0版対応　追加　END */
                    // STEP21.0版対応　追加　START */
                    "59".equals(input.getFunctionType()) ||
                    // STEP21.0版対応　追加　END */
                    // STEP17.0版対応　追加　START */
                    "60".equals(input.getFunctionType()))) ||
            // STEP17.0版対応　追加　END */
            (!"000000".equals(input.getOrderStatus()) &&
                !"050202".equals(input.getOrderStatus()) &&
                "05".equals(input.getFunctionType()))
        ) {
            // 変数「文字列リスト」に入力パラメータ．「SO管理オブジェクト」．「オーダステータス」を追加する
            wordList.push(input.getOrderStatus());
        }

        for (let i = 0; i < wordList.length; i++) {
            if (!StringUtils.isNotEmpty(wordList[i])) {
                wordList.splice(i, 1);
                i--;
            }
        }

        // 文字列連接共通を呼び出し、その返却値を変数「内容詳細」に設定する
        detail = wordList.join(" ");
        if ("".equals(detail)) {
            detail = null;
        }
        return detail;
    }

    /** STEP4.0版対応　変更　START */
    /** STEP1.2b版対応　追加　START */
    /**
     * SO管理テーブルにデータ更新
     *
     * @param input SO管理オブジェクト
     * @param orderStatus 変数「オーダステータス」
     * @param detail 変数「内容詳細」
     */
    // private void updServiceOrders(SOObject input, String orderStatus, String detail) {
    // 	aPISoDAO.updServiceOrders(toDate(input.getExecDate()), orderStatus, input.getServiceOrderId(), detail);
    // }
    private async updServiceOrders(
        input: SOObject,
        orderStatus: string,
        detail: string,
    ) {
        await this.aPISoDAO.updServiceOrders(
            this.toDate(input.getExecDate()),
            orderStatus,
            input.getServiceOrderId(),
            detail,
        );
    }
    /** STEP4.0版対応　変更　END */
    /** STEP1.2b版対応　追加　START */

    /** STEP4.0版対応　変更　START */
    /**
     * SO管理テーブルにデータ挿入
     * @param input SO管理オブジェクト
     * @param type 変数「種別」
     * @param orderStatus 変数「オーダステータス」
     * @param detail 変数「内容詳細」
     * @param decisionType 変数「判定種別」
     */
    private async insertServiceOrders(
        input: SOObject,
        type_: string,
        orderStatus: string,
        detail: string,
        decisionType: string,
    ) {
        /** STEP4.0版対応　変更　END */
        const soEntity = new ServiceOrdersEntity();
        // サービスオーダID
        soEntity.serviceOrderId = input.getServiceOrderId();
        // 投入日
        soEntity.orderDate = this.toDate(input.getOrderDate());
        // 完了日
        soEntity.execDate = this.toDate(input.getExecDate());
        /** STEP1.2b版対応　追加　START */
        // 予約日
        //     soEntity.setReserveDate(toDate(input.getReserveDate()));
        soEntity.reserveDate = this.toDate(input.getReserveDate());
        /** STEP2.0a版対応　追加　START */
        // 機能種別
        soEntity.functionType = input.getFunctionType();
        /** STEP2.0a版対応　追加　END */
        /** STEP1.2b版対応　追加　END */
        // 種別
        soEntity.orderType = type_;
        // オーダステータス
        soEntity.orderStatus = orderStatus;
        // 回線ID
        soEntity.lineId = input.getLineId();
        /** #324対応　修正 START */
        /** #344対応　修正 START */
        if (input.isLineGroupCreateFlag()) {
            // 所属テナントID
            //         soEntity.setTenantId(input.getTenantId());
            soEntity.tenantId = input.getTenantId();
        } else {
            // 所属テナントIDの取得
            //         String tenantId = getTenantId(input);
            const tenantId = await this.getTenantId(input);

            // 所属テナントID
            if (StringUtils.isEmpty(tenantId)) {
                if (input.isInternalFlag()) {
                    soEntity.tenantId = input.getExecuteTenantId();
                } else {
                    soEntity.tenantId = input.getTenantId();
                }
            } else {
                soEntity.tenantId = tenantId;
            }
        }
        /** #344対応　修正 END */
        /** #324対応　修正 END */
        // 実行ユーザID
        //     soEntity.setExecUserId(input.getExecuteUserId());
        soEntity.execUserId = input.getExecuteUserId();
        // 実行テナントID
        soEntity.execTenantId = input.getExecuteTenantId();
        // 内容詳細
        soEntity.content = detail;
        /** STEP1.2b版対応　追加　START */
        // 内部呼出し
        soEntity.internalFlag = input.isInternalFlag();
        // REST電文
        soEntity.restMessage = input.getRestMessage();
        /** STEP1.2b版対応　追加　END */
        /** STEP4.0版対応　追加　START */
        // 判定種別
        soEntity.decisionType = decisionType;
        /** STEP4.0版対応　追加　END */
        // STEP13.0版対応　追加　START
        if ("20".equals(input.getFunctionType())) {
            // 回線グループプラン変更の場合、回線グループIDを設定
            // 電文の回線グループIDが9桁以下の場合のみ
            if (CheckUtil.checkLength(input.getLineGroupId(), 9, false)) {
                soEntity.groupId = input.getLineGroupId();
            }
        }
        // STEP13.0版対応　追加　END
        await this.aPISoDAO.insertServiceOrders(soEntity);
    }

    /**
     * 文字列からjava.util.Dateに変換する処理。
     * @param strDate 文字列日付
     * @return java.util.Date
     */
    private toDate(strDate: string): Date {
        const pattern = [
            "yyyyMM",
            "yyyy/MM",
            "yyyy-MM",
            "yyyyMMdd",
            "yyyy/MM/dd",
            "yyyy-MM-dd",
            "yyyyMMddHHmmss",
            "yyyy-MM-dd HH:mm:ss",
            /** STEP1.2b版対応　追加　START */
            "yyyy/MM/dd HH:mm",
            /** STEP1.2b版対応　追加　END */
            "yyyy/MM/dd HH:mm:ss",
            "yyyyMMddHHmmsssss",
            "yyyy-MM-dd HH:mm:ss.SSS",
            "yyyy/MM/dd HH:mm:ss.SSS",
        ];

        // return DateUtils.parseDate(strDate, pattern);
        if (typeof strDate !== "string" || strDate.length === 0) {
            return null;
        }
        for (const pat of pattern) {
            const date = parse(strDate, pat, new Date());
            if (isValid(date)) {
                return date;
            }
        }
        return null;
    }

    /**
     * 回線グループ所属テナント情報の取得する
     * @param input SO管理オブジェクト
     * @return 回線所属テナント
     */
    private async getTenantId(input: SOObject): Promise<string> {
        if (!input || StringUtils.isEmpty(input.getFunctionType())) {
            return null;
        }

        let listKeyTenantId = "";
        if (input.isInternalFlag()) {
            listKeyTenantId = input.getExecuteTenantId();
        } else {
            listKeyTenantId = input.getTenantId();
        }

        // 自テナントを含む子テナントを取得する。
        const tenantIdList = await this.aPISoDAO.getTenantLevel(
            listKeyTenantId,
        );

        if (!tenantIdList || tenantIdList.length === 0) {
            return null;
        }

        let tenantId = null;

        // 	// 回線グループに対するオーダの場合
        if (
            "10".equals(input.getFunctionType()) ||
            "12".equals(input.getFunctionType()) ||
            "13".equals(input.getFunctionType()) ||
            "14".equals(input.getFunctionType()) ||
            "15".equals(input.getFunctionType()) ||
            // STEP13.0版対応　追加　START
            "20".equals(input.getFunctionType())
        ) {
            // STEP13.0版対応　追加　END
            // 所属テナントIDを取得する
            // LineGroupsEntity lineGroupsEntity = aPICommonDAO.getLineGroupsEntity(input.getLineGroupId());
            const lineGroupsEntity = isNone(input.getLineGroupId())
                ? null
                : await this.aPICommonDAO.getLineGroupsEntity(
                      input.getLineGroupId(),
                  );
            if (lineGroupsEntity == null) {
                return null;
            } else if (StringUtils.isEmpty(lineGroupsEntity.tenantId)) {
                return null;
            } else if (!lineGroupsEntity.tenantId.equals(input.getTenantId())) {
                return null;
            } else if (lineGroupsEntity.status !== 1) {
                return null;
            } else if (lineGroupsEntity.planId == null) {
                return null;
            } else if (!tenantIdList.includes(lineGroupsEntity.tenantId)) {
                return null;
            } else {
                tenantId = lineGroupsEntity.tenantId;
            }
            // 単回線に対するオーダの場合
        } else {
            // 所属テナントIDを取得する
            tenantId = this.getBelongTenantId(input.getLineId(), tenantIdList);
        }
        return tenantId;
    }

    /**
     * 所属テナントを取得する。
     *
     * @param lineId
     * @return
     */
    private async getBelongTenantId(
        lineId: string,
        tenantIdList: string[],
    ): Promise<string> {
        // テナントID
        let tenantId = null;

        /** STEP3.0版対応　追加　START */
        // 回線テナントから所属テナントを取得する。
        // String[] lineTenants = tenantManage.getCheckedTenantId(lineId);
        const lineTenants = await this.tenantManage.getCheckedTenantId(lineId);

        if (lineTenants == null) {
            return null;
        }

        if (!lineTenants[0]) {
            return null;
        } else {
            tenantId = lineTenants[1].trim();
            if (tenantId && tenantIdList.includes(tenantId)) {
                return tenantId;
            } else {
                return null;
            }
        }
    }
}
