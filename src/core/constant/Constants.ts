export default abstract class Constants {
    /** オペレーション種別名称_ProfileView */
    public static readonly PROFILEVIEW = "ProfileView";
    /** オペレーション名称_serviceProfileQuery */
    public static readonly SERVICEPROFILEQUERY = "serviceProfileQuery";
    /** オペレーション名称_serviceProfileQuery */
    public static readonly SERVICEPROFILEREQUEST = "serviceProfileRequest";
    /** オペレーション名称_serviceProfileQuery */
    public static readonly SERVICEPROFILEREQUESTLITE =
        "serviceProfileRequestLite";
    /** 回線基本情報取得 */
    public static readonly LINES_INFO = "/MVNO/api/V100/Lines/info";
    /** 回線運用情報参照 */
    public static readonly LINES_TRAFFIC = "/MVNO/api/V100/Lines/traffic";
    /** 回線追加チャージ（クーポン）容量・期間追加 */
    public static readonly LINES_CHARGE = "/MVNO/api/V100/Lines/charge";
    /** 回線クーポンON/OFF */
    public static readonly LINES_COUPON = "/MVNO/api/V100/Lines/coupon";
    /** 回線アクティベート/ディアクティベート */
    public static readonly LINES_ACTIVATION = "/MVNO/api/V100/Lines/activation";
    /** 回線リスト確認 */
    public static readonly LINES_LIST = "/MVNO/api/V100/Lines/list";
    /** 回線グループ基本情報取得 */
    public static readonly LINESGROUP_INFO = "/MVNO/api/V100/LinesGroup/info";
    /** 回線グループ運用情報参照 */
    public static readonly LINESGROUP_TRAFFIC =
        "/MVNO/api/V100/LinesGroup/traffic";
    /** 回線グループ追加チャージ（クーポン）容量・期間追加 */
    public static readonly LINESGROUP_CHARGE =
        "/MVNO/api/V100/LinesGroup/charge";
    /** 回線グループクーポンON/OFF */
    public static readonly LINESGROUP_COUPON =
        "/MVNO/api/V100/LinesGroup/coupon";
    /** 回線グループ基本容量変更 */
    public static readonly LINEGROUP_MOD_BUCKET =
        "/MVNO/api/V100/LinesGroup/LineGroup_mod_bucket";
    /** 回線グループ所属回線変更 */
    public static readonly LINEGROUP_MODIFY =
        "/MVNO/api/V100/LinesGroup/LineGroup_modify";
    /** 回線グループ新規作成/廃止 */
    public static readonly LINEGROUP_REGIST =
        "/MVNO/api/V100/LinesGroup/LineGroup_regist";

    /** STEP1.2a版対応 追加 START */
    /** SO_SO一覧情報参照 */
    public static readonly SOLIST_CHARGE = "/MVNO/api/V100/solist";
    /** SO_SO詳細情報参照 */
    public static readonly SODESC_CHARGE = "/MVNO/api/V100/sodesc";
    /** 回線プラン変更 */
    public static readonly LINES_PLANCHARGE = "/MVNO/api/V100/Lines/plan";
    /** STEP1.2a版対応 追加 END */

    /** STEP1.2b版対応 追加 START */
    /** 即時オーダ */
    public static readonly ORDER_TYPE_0 = "0";
    /** 予約前オーダ */
    public static readonly ORDER_TYPE_1 = "1";
    /** 予約実行オーダ */
    public static readonly ORDER_TYPE_2 = "2";
    /** 予想以外オーダ */
    public static readonly ORDER_TYPE_9 = "9";
    /** SOキャンセル */
    public static readonly SO_CANCEL = "/MVNO/api/V100/so_cancel";
    /** STEP1.2b版対応 追加 END */

    /** STEP3.0版対応 追加 START */
    /** 仮登録回線追加 */
    public static readonly Line_preadd = "/MVNO/api/V100/Lines/preadd";
    /** STEP3.0版対応 追加 END */

    /** STEP4.0版対応 追加 START */
    /** 回線黒化 */
    public static readonly LINE_ENABLE = "/MVNO/api/V100/Lines/enable";
    /** 回線廃止 */
    public static readonly LINE_DEL = "/MVNO/api/V100/Lines/del";
    /** フロント用予約キャンセル */
    public static readonly FRONT_SO_CANCEL = "/MVNO/api/V100/so_frontcancel";
    /** 回線オプション_NW暗証番号変更 */
    public static readonly LINE_LINEOP = "/MVNO/api/V100/Lines/lineop";
    /** システム自動実行 */
    public static readonly SYSTEM_AUTOMATIC_EXECUTION = "システム自動実行";
    /** STEP4.0版対応 追加 END */
    /** STEP4.0d版対応 追加 START */
    /** 回線データ譲渡 */
    public static readonly LINE_DATAGIFT = "/MVNO/api/V100/Lines/gift";
    /** SQLステータスコード 一意制約エラー */
    public static readonly SQLSTATE_UNIQUE_CONSTRAINT_VIOLATION = "23505";
    /** STEP4.0d版対応 追加 END */

    /** STEP5.0対応 追加 START */
    /** SIM再発行 */
    public static readonly LINE_SIM = "/MVNO/api/V100/Lines/sim";

    /** フロントAPI用テナントID */
    public static readonly FRONT_API_TENANT_ID = "OPF000";

    /** IPアドレス サブネットマスク上限値 */
    public static readonly SUBNET_MASK_MAX: number = 32;

    /** IPアドレス サブネットマスク下限値 */
    public static readonly SUBNET_MASK_MIN: number = 24;

    /** SG「接続許可IPアドレス」最大件数
     */
    public static readonly ALLOWED_IP_MAX_CNT: number = 6;
    /** STEP5.0対応 追加 END */

    // STEP12.0版対応 追加 START
    /** 回線プラン追加／削除 */
    public static readonly LINE_PLAN_ADD_DEL = "/MVNO/api/V100/plan_add_del";
    // STEP12.0版対応 追加 END

    // STEP13.0版対応 追加 START
    /** 回線グループプラン変更 */
    public static readonly LINEGROUP_PLANCHARGE =
        "/MVNO/api/V100/LinesGroup/plan";
    // STEP13.0版対応 追加 END

    // STEP15.0版対応 追加 START
    /** 利用中断／再開／サスペンド */
    public static readonly LINES_SUSPEND = "/MVNO/api/V100/Lines/suspend";
    // STEP15.0版対応 追加 END

    // STEP17.0版対応 追加 START
    /** NW契約変更 */
    public static readonly LINES_ACCESS = "/MVNO/api/V100/Lines/access";
    // STEP17.0版対応 追加 END

    // STEP21.0版対応 追加 START
    /** 0035でんわプラン変更 */
    public static readonly LINES_PHONEPLAN = "/MVNO/api/V100/Lines/voice";
    // STEP21.0版対応 追加 END

    // STEP30 START
    public static readonly TYPE_5_TENANTS = ["RKM000"];
    // STEP30 END
}
