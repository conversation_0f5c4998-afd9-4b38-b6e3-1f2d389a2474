/**
 * added during refactoring: moved public static properties from PreLineService.ts to avoid circular deps
 */
export default abstract class MvnoConstants {
    // MNP転入フラグ
    /** MNP転入でない */
    public static readonly MNP_IN_TYPE_NO = 0;
    /** MNP転入 */
    public static readonly MNP_IN_TYPE_YES = 1;
    /** 同一MVNE転入 */
    public static readonly MNP_IN_TYPE_SAME_MVNE_YES = 2;

    // 対応NW
    /** 3G */
    public static readonly NETWORK_IN_TYPE_3G = "3G";
    /** LTE */
    public static readonly NETWORK_IN_TYPE_LTE = "LTE";
    /** 5G */
    public static readonly NETWORK_IN_TYPE_5G = "5G(NSA)";
}
