export default abstract class ResultCdConstants {
    /**
     * <strong>正常終了</strong>
     */
    public static readonly CODE_000000 = "000000";

    /**
     * <strong>予期せぬエラー</strong>
     */
    public static readonly CODE_999999 = "999999";

    /**
     * <strong>同時接続数チェックエラー</strong>
     */
    public static readonly CODE_000931 = "000931";

    /**
     * <strong>ハッシュ化パスワード取得エラー</strong>
     */
    public static readonly CODE_000941 = "000941";

    /**
     * <strong>API認証キーマッチングエラー</strong>
     */
    public static readonly CODE_000942 = "000942";

    /**
     * <strong>ヘッダフォーマットチェックエラー</strong>
     */
    public static readonly CODE_000921 = "000921";

    /**
     * <strong>ヘッダフォーマットチェックエラー</strong>
     */
    public static readonly CODE_000922 = "000922";

    /** STEP3.1版対応　追加　START */
    /**
     * <strong>設定ファイルにキーが存在しないエラー</strong>
     */
    public static readonly CODE_000951 = "000951";
    /** STEP3.1版対応　追加　END */

    /**
     * <strong>卸ポータルプランIDフォーマットチェックエラー</strong>
     */
    public static readonly CODE_020101 = "020101";

    /**
     * <strong>社内テナント種別チェックエラー</strong>
     */
    public static readonly CODE_020102 = "020102";

    /**
     * <strong>卸ポータルプランIDチェックエラー</strong>
     */
    public static readonly CODE_020103 = "020103";

    /**
     * <strong>回線テナント所属チェックエラー</strong>
     */
    public static readonly CODE_020104 = "020104";

    /**
     * <strong>プランID所属チェックエラー</strong>
     */
    public static readonly CODE_020105 = "020105";

    /**
     * <strong>サービスパターン結果エラー</strong>
     */
    public static readonly CODE_020201 = "020201";

    // STEP20.0版対応　追加　START
    /**
     * <strong>前月通信量取得エラー</strong>
     */
    public static readonly CODE_020202 = "020202";

    /**
     * <strong>前々月通信量取得エラー</strong>
     */
    public static readonly CODE_020203 = "020203";
    // STEP20.0版対応　追加　START

    /**
     * <strong>サービスパターン結果エラー</strong>
     */
    public static readonly CODE_020401 = "020401";

    /**
     * <strong>回線番号フォーマットチェックエラー</strong>
     */
    public static readonly CODE_030101 = "030101";

    /**
     * <strong>検索テナントID桁数チェック エラー    　　検索テナントID英数字チェックチェック</strong>
     */
    public static readonly CODE_160101 = "160101";
    /**
     * <strong>　変数「回線番号必須チェック結果」 エラー  </strong>
     */
    public static readonly CODE_160301 = "160301";
    /**
     * <strong>　変数「申込日（始期）フォーマットチェック結果」 エラー    変数「申込日（終期）フォーマットチェック結果」エラー
     * 			変数「完了日（始期）フォーマットチェック結果」エラー　　変数「完了日（終期）フォーマットチェック結果」エラー</strong>
     */
    public static readonly CODE_160201 = "160201";
    /**
     * <strong>　変数「申込日比較チェック結果」 エラー </strong>
     */
    public static readonly CODE_160202 = "160202";
    /**
     * <strong>変数「テナント階層情報」 　 エラー </strong>
     */
    public static readonly CODE_160501 = "160501";
    /**
     * <strong>テナントIDチェック  エラー </strong>
     */
    public static readonly CODE_160601 = "160601";
    /**
     * <strong>DBエラーが発生する場合  エラー </strong>
     */
    public static readonly CODE_160702 = "160702";
    /**
     * <strong>変数「SO一覧情報」の件数が0件の場合  エラー </strong>
     */
    public static readonly CODE_160701 = "160701";

    /**
     * <strong>回線テナント所属チェック</strong>
     */
    public static readonly CODE_030102 = "030102";

    /**
     * <strong>テナントIDより判定エラー</strong>
     */
    public static readonly CODE_030103 = "030103";

    /**
     * <strong>プランID取得結果エラー</strong>
     */
    public static readonly CODE_030104 = "030104";

    /**
     * <strong>プラン情報取得結果エラー</strong>
     */
    public static readonly CODE_030201 = "030201";

    /**
     * <strong>更新サービスパターンのフォーマットチェックエラー</strong>
     */
    public static readonly CODE_030202 = "030202";

    /**
     * <strong>追加容量情報結果より判定エラー</strong>
     */
    public static readonly CODE_030301 = "030301";

    /**
     * <strong>追加容量のフォーマットチェックエラー</strong>
     */
    public static readonly CODE_030302 = "030302";

    /**
     * <strong>追加容量情報結果より判定エラー</strong>
     */
    public static readonly CODE_030303 = "030303";

    /**
     * <strong>追加容量のフォーマットチェックエラー</strong>
     */
    public static readonly CODE_030304 = "030304";

    /**
     * <strong>追加容量情報結果より判定エラー</strong>
     */
    public static readonly CODE_030305 = "030305";

    /**
     * <strong>追加容量のフォーマットチェックエラー</strong>
     */
    public static readonly CODE_030306 = "030306";

    /**
     * <strong>有効期限結果を判定エラー</strong>
     */
    public static readonly CODE_030307 = "030307";

    /**
     * <strong>容量追加結果を判定エラー</strong>
     */
    public static readonly CODE_030401 = "030401";

    /**
     * <strong>回線番号フォーマットチェックのエラー</strong>
     */
    public static readonly CODE_010101 = "010101";

    /**
     * <strong>回線テナント所属チェックのエラー</strong>
     */
    public static readonly CODE_010102 = "010102";

    /**
     * <strong>社内テナントIDチェックのエラー</strong>
     */
    public static readonly CODE_010103 = "010103";

    /**
     * <strong>　回線基本情報を取得のエラー</strong>
     */
    public static readonly CODE_010201 = "010201";

    /**
     *  * <strong>回線グループのチェックエラー</strong>
     */
    public static readonly CODE_090101 = "090101";

    /**
     *  * <strong>回線番号の判断エラー</strong>
     */
    public static readonly CODE_090102 = "090102";

    /**
     *  <strong>　卸ポータルグループプランID所属チェックエラー</strong>
     */
    public static readonly CODE_090103 = "090103";

    /**
     *  <strong>回線クループテーブルより該当回線のエラー</strong>
     */
    public static readonly CONST_090201 = "090201";

    /**
     *  <strong>SOAP APIにより、回線クループ運用情報を取得のエラー</strong>
     */
    public static readonly CODE_090401 = "090401";

    /**
     * <strong>電文作成エラー</strong>
     */
    public static readonly CODE_001201 = "001201";

    /**
     * <strong>エラー060101</strong>
     */
    public static readonly CODE_060101 = "060101";

    /** STEP1.2a版対応　追加　START */

    /**
     * <strong>エラー060102</strong>
     */
    public static readonly CODE_060102 = "060102";

    /**
     * <strong>エラー060401</strong>
     */
    public static readonly CODE_060401 = "060401";

    /**
     * <strong>エラー060501</strong>
     */
    public static readonly CODE_060501 = "060501";

    /**
     * <strong>エラー060601</strong>
     */
    public static readonly CODE_060601 = "060601";

    /**
     * <strong>エラー060701</strong>
     */
    public static readonly CODE_060701 = "060701";

    /**
     * <strong>エラー060801</strong>
     */
    public static readonly CODE_060801 = "060801";

    /**
     * <strong>エラー060901</strong>
     */
    public static readonly CODE_060901 = "060901";

    /**
     * <strong>エラー061101</strong>
     */
    public static readonly CODE_061101 = "061101";

    /**
     * <strong>エラー061201</strong>
     */
    public static readonly CODE_061201 = "061201";

    /** STEP1.2a版対応　追加　END */
    /**
     * <strong>エラー060201</strong>
     */
    public static readonly CODE_060201 = "060201";

    /**
     * <strong>エラー060101回線グループIDフォーマットチェック</strong>
     */
    public static readonly CODE_080101 = "080101";

    /** STEP1.3版対応　追加　START */
    /**
     * <strong>エラー080102社内テナント種別チェック</strong>
     */
    public static readonly CODE_080102 = "080102";

    /**
     * <strong>エラー080202変数「回線グループ基本情報リスト」を判定する</strong>
     */
    public static readonly CODE_080202 = "080202";
    /** STEP1.3版対応　追加　END */

    /**
     * <strong>エラー060201「回線グループ基本情報リスト」を判定する</strong>
     */
    public static readonly CODE_080201 = "080201";

    /**
     * <strong>電文ヘッダ結果200以外エラー</strong>
     */
    public static readonly CODE_001101 = "001101";

    /**
     * <strong>電文ヘッダ結果ResultがNGエラー</strong>
     */
    public static readonly CODE_001102 = "001102";

    /**
     * <strong>エラー050101回線番号フォーマットチェック結果」より判定する</strong>
     */
    public static readonly CODE_050101 = "050101";

    /**
     * <strong>エラー050102「回線テナント所属チェック結果」より判定する</strong>
     */
    public static readonly CODE_050102 = "050102";

    /**
     * <strong>エラー050201「回線データ情報結果」を判定する
     */
    public static readonly CODE_050201 = "050201";

    /**
     * <strong>エラー050202「アクティベート状況」より判定する
     */
    public static readonly CODE_050202 = "050202";

    /**
     * <strong>エラー050201変数「データ更新結果」を判定する
     */
    public static readonly CODE_050401 = "050401";

    // STEP18.0版対応　追加　START
    /**
     * <strong>エラー050601変数「PCRFデータ更新結果」を判定する
     */
    public static readonly CODE_050601 = "050601";
    // STEP18.0版対応　追加　END

    /**
     * <strong>エラー100101変数「回線グループID」を判定する
     */
    public static readonly CODE_100101 = "100101";

    /**
     * <strong>エラー100102「テナントID」を判定する
     */
    public static readonly CODE_100102 = "100102";
    /**
     * <strong>エラー100103変数「プランID」を判定する
     */
    public static readonly CODE_100103 = "100103";
    /**
     * <strong>エラー100201変数「プラン情報取得結果」を判定する
     */
    public static readonly CODE_100201 = "100201";
    /**
     * <strong>エラー100104変数「ポリシーIDチェック結果」を判定する
     */
    public static readonly CODE_100104 = "100104";
    /**
     * <strong>エラー100301変数「追加容量情報結果」を判定する
     */
    public static readonly CODE_100301 = "100301";
    /**
     * <strong>エラー100302変数「追加容量」を判定する
     */
    public static readonly CODE_100302 = "100302";
    /**
     * <strong>エラー100401変数「容量追加結果」を判定する
     */
    public static readonly CODE_100401 = "100401";
    /**
     * <strong>エラー100303変数「追加容量情報結果」を判定する
     */
    public static readonly CODE_100303 = "100303";
    /**
     * <strong>エラー100304変数「追加容量」を判定する
     */
    public static readonly CODE_100304 = "100304";
    /**
     * <strong>回線グループクーポンオン・オフ用エラー</strong>
     */
    public static readonly CODE_120101 = "120101";
    /**
     * <strong>回線グループクーポンオン・オフ用エラー</strong>
     */
    public static readonly CODE_120102 = "120102";
    /**
     * <strong>回線グループクーポンオン・オフ用エラー</strong>
     */
    public static readonly CODE_120103 = "120103";
    /**
     * <strong>回線グループクーポンオン・オフ用エラー</strong>
     */
    public static readonly CODE_120201 = "120201";
    /**
     * <strong>回線グループクーポンオン・オフ用エラー</strong>
     */
    public static readonly CODE_120202 = "120202";
    /**
     * <strong>回線グループクーポンオン・オフ用エラー</strong>
     */
    public static readonly CODE_120401 = "120401";

    // STEP18.0版対応　追加　START
    /**
     * <strong>回線グループクーポンオン・オフ用エラー</strong>
     */
    public static readonly CODE_120501 = "120501";
    // STEP18.0版対応　追加　END

    /**
     * <strong>「回線番号リスト」を判定する</strong>
     */
    public static readonly CODE_070201 = "070201";

    /**
     * <strong>代表N番相関チェック</strong>
     */
    public static readonly CODE_070101 = "070101";
    /**
     * <strong>回線クーポンオン・オフ用エラー</strong>
     */
    public static readonly CODE_040101 = "040101";
    /**
     * <strong>回線クーポンオン・オフ用エラー</strong>
     */
    public static readonly CODE_040102 = "040102";
    /**
     * <strong>回線クーポンオン・オフ用エラー</strong>
     */
    public static readonly CODE_040103 = "040103";
    /**
     * <strong>回線クーポンオン・オフ用エラー</strong>
     */
    public static readonly CODE_040104 = "040104";
    /**
     * <strong>回線クーポンオン・オフ用エラー</strong>
     */
    public static readonly CODE_040201 = "040201";
    /**
     * <strong>回線クーポンオン・オフ用エラー</strong>
     */
    public static readonly CODE_040202 = "040202";
    /**
     * <strong>回線クーポンオン・オフ用エラー</strong>
     */
    public static readonly CODE_040401 = "040401";

    // STEP18.0版対応　変更　START
    /**
     * <strong>回線クーポンオン・オフ用エラー</strong>
     */
    public static readonly CODE_040501 = "040501";
    // STEP18.0版対応　変更　END

    /**
     * <strong>オプションプランID利用チェック 用エラー</strong>
     */
    public static readonly CODE_040105 = "040105";

    /**
     * <strong>回線番号利用チェック 用エラー</strong>
     */
    public static readonly CONST_090202 = "090202";

    /** STEP1.3版対応　追加　START */
    /**
     * <strong>回線グループ再利用不可期間チェックエラー</strong>
     */
    public static readonly CODE_130001 = "130001";

    /**
     * <strong>新規/廃止フラグフォーマットチェック</strong>
     */
    public static readonly CODE_130002 = "130002";

    /**
     * <strong>回線グループプランID必須チェックエラー</strong>
     */
    public static readonly CODE_130003 = "130003";

    /**
     * <strong>回線グループIDのフォーマットチェックエラー</strong>
     */
    public static readonly CODE_130004 = "130004";

    /**
     * <strong>「社内テナント種別」エラー</strong>
     */
    public static readonly CODE_130005 = "130005";

    /**
     * <strong>「社内テナント種別」エラー</strong>
     */
    public static readonly CODE_130006 = "130006";

    /** STEP3.0版対応　追加　START */
    /**
     * <strong>「社内テナント種別」エラー</strong>
     */
    public static readonly CODE_130007 = "130007";

    /**
     * <strong>「社内テナント種別」エラー</strong>
     */
    public static readonly CODE_130008 = "130008";
    /** STEP3.0版対応　追加　END */

    /**
     * <strong>回線グループ新規結果エラー</strong>
     */
    public static readonly CODE_130105 = "130105";

    /**
     * <strong>回線グループ新規結果エラー</strong>
     */
    public static readonly CODE_130106 = "130106";

    // STEP18.0版対応　追加　START
    /**
     * <strong>回線グループ新規結果エラー</strong>
     */
    public static readonly CODE_130109 = "130109";
    // STEP18.0版対応　追加　END

    /**
     * <strong>回線グループプランＩＤ取得結果エラー</strong>
     */
    public static readonly CODE_130101 = "130101";

    /**
     * <strong>サービスプランＩＤ取得結果エラー</strong>
     */
    public static readonly CODE_130102 = "130102";

    /**
     * <strong>回線グループＩＤリスト取得結果エラー</strong>
     */
    public static readonly CODE_130103 = "130103";

    /**
     * <strong>回線グループＩＤロック結果エラー</strong>
     */
    public static readonly CODE_130104 = "130104";

    /**
     * <strong>回線グループ所属回線変更.回線番号フォーマットチェック結果のエラーコード</strong>
     */
    public static readonly CODE_140001 = "140001";

    /**
     * <strong>回線グループ所属回線変更.社内テナント種別チェックのエラーコード</strong>
     */
    public static readonly CODE_140002 = "140002";

    /**
     * <strong>回線グループ所属回線変更.社内テナント種別チェックのエラーコード</strong>
     */
    public static readonly CODE_140003 = "140003";

    /**
     * <strong>回線グループ所属回線変更.回線グループＩＤが回線グループ管理に存在するか判定するのエラーコード</strong>
     */
    public static readonly CODE_140004 = "140004";

    /**
     * <strong>回線グループ所属回線変更.回線グループＩＤ行ロック取得のエラーコード</strong>
     */
    public static readonly CODE_140005 = "140005";

    /**
     * <strong>回線グループ所属回線変更.回線ＩＤ確認のエラーコード</strong>
     */
    public static readonly CODE_140100 = "140100";

    /**
     * <strong>回線グループ所属回線変更.「再販料金プラン」を取得する処理のエラーコード</strong>
     */
    public static readonly CODE_140101 = "140101";

    /**
     * <strong>回線グループ所属回線変更.「再販料金プラン」を取得する処理のエラーコード</strong>
     */
    public static readonly CODE_140103 = "140103";

    /**
     * <strong>回線グループ所属回線変更.回線ＩＤ、回線グループＩＤを挿入処理のエラーコード</strong>
     */
    public static readonly CODE_140104 = "140104";

    // STEP18.0版対応　追加　START
    /**
     * <strong>PCRF関連のエラーコード</strong>
     */
    public static readonly CODE_141801 = "141801";
    // STEP18.0版対応　追加　END

    /**
     * <strong>回線グループ所属回線変更.回線回線グループテーブルから「回線ＩＤ」を取得する処理のエラーコード</strong>
     */
    public static readonly CODE_140201 = "140201";

    /**
     * <strong>回線グループ所属回線変更.SOAP APIにより、グループを割当解除する処理のエラーコード</strong>
     */
    public static readonly CODE_140202 = "140202";

    /**
     * <strong>回線グループ所属回線変更.回線回線グループテーブルを削除する処理のエラーコード</strong>
     */
    public static readonly CODE_140203 = "140203";

    // STEP18.0版対応　追加　START
    /**
     * <strong>PCRF関連のエラーコード</strong>
     */
    public static readonly CODE_145901 = "145901";
    // STEP18.0版対応　追加　END

    /**
     * <strong>プロパティ取得エラー</strong>
     */
    public static readonly CODE_150101 = "150101";

    /**
     * <strong>チェック項目のフォーマットエラー</strong>
     */
    public static readonly CODE_150102 = "150102";

    /**
     * <strong>社内テナント種別取得エラー</strong>
     */
    public static readonly CODE_150103 = "150103";

    /**
     * <strong>社内テナント種別は１であるエラー</strong>
     */
    public static readonly CODE_150104 = "150104";

    /**
     * <strong>回線グループ管理取得エラーまたはフォーマットチェックエラー</strong>
     */
    public static readonly CODE_150201 = "150201";

    /**
     * <strong>回線IDが取得できない</strong>
     */
    public static readonly CODE_150301 = "150301";

    /**
     * <strong>再販料金プラン取得エラー</strong>
     */
    public static readonly CODE_150302 = "150302";

    /**
     * <strong>基本容量とプランID取得エラー</strong>
     */
    public static readonly CODE_150303 = "150303";

    /**
     * <strong>テナントID取得エラーまたは取得テナントIDが電文中のテナントIDと不一致</strong>
     */
    public static readonly CODE_150304 = "150304";

    /**
     * <strong>容量は104857600を超えた</strong>
     */
    public static readonly CODE_150305 = "150305";

    /**
     * <strong>ResultがNGである場合（現在容量取得電文）</strong>
     */
    public static readonly CODE_150501 = "150501";

    /**
     * <strong>cap_polnumの値が空（NULL）の場合</strong>
     */
    public static readonly CODE_150601 = "150601";

    /**
     * <strong>ResultがNGの場合（容量変更電文）</strong>
     */
    public static readonly CODE_150801 = "150801";

    /**
     * <strong>回線グループ管理テーブル更新エラー</strong>
     */
    public static readonly CODE_130107 = "130107";

    /**
     * <strong>「回線グループＩＤ情報」=null</strong>
     */
    public static readonly CODE_130201 = "130201";

    /**
     * <strong>回線グループＩＤの行ロック失敗</strong>
     */
    public static readonly CODE_130202 = "130202";

    /**
     * <strong>テナントＩＤ確認エラー</strong>
     */
    public static readonly CODE_130203 = "130203";

    /**
     * <strong>ステータス確認エラー</strong>
     */
    public static readonly CODE_130204 = "130204";

    /**
     * <strong>「回線ＩＤ数」を取得するエラー</strong>
     */
    public static readonly CODE_130205 = "130205";

    /**
     * <strong>SOAPのAPIを接続するエラー</strong>
     */
    public static readonly CODE_130206 = "130206";

    /**
     * <strong>回線グループ管理テーブルを更新するエラー</strong>
     */
    public static readonly CODE_130207 = "130207";
    /** STEP1.3版対応　追加　END */

    // STEP18.0版対応　追加　START
    /**
     * <strong>PCRF関連のエラー</strong>
     */
    public static readonly CODE_130209 = "130209";
    // STEP18.0版対応　追加　END

    /** STEP1.2a版対応　追加　START */

    /**
     * <strong>パラメータ「SO-ID」のフォーマットが不正です</strong>
     */
    public static readonly CODE_170101 = "170101";

    /**
     * <strong>テナント階層情報が取得失敗</strong>
     */
    public static readonly CODE_170201 = "170201";

    /**
     * <strong>SO詳細情報の取得に失敗</strong>
     */
    public static readonly CODE_170301 = "170301";

    /**
     * <strong>SO詳細情報の取得するエラー</strong>
     */
    public static readonly CODE_170302 = "170302";

    /** STEP1.2a版対応　追加　END */

    /**
     * #149対応で追加
     * <strong>回線グループID確認エラー</strong>
     */
    public static readonly CONST_090203 = "090203";

    // STEP20.0版対応　追加　START
    /**
     * <strong>前月回線グループ通信量取得エラー</strong>
     */
    public static readonly CONST_090204 = "090204";

    /**
     * <strong>前々月回線グループ通信量取得エラー</strong>
     */
    public static readonly CONST_090205 = "090205";
    // STEP20.0版対応　追加　END

    /** STEP1.2b版対応　追加　START */
    /**
     * <strong>エラー050105「オーダ種別」より判定する</strong>
     */
    public static readonly CODE_050105 = "050105";
    /**
     * <strong>エラー050106「予約日と投入日の相関チェック結果」より判定する</strong>
     */
    public static readonly CODE_050106 = "050106";
    /**
     * <strong>エラー050107「予約実行日時単位フォーマットチェック結果」より判定する</strong>
     */
    public static readonly CODE_050107 = "050107";
    /**
     * <strong>エラー050108「予約実行日時単位と予約日相関チェック結果」より判定する</strong>
     */
    public static readonly CODE_050108 = "050108";
    /**
     * <strong>エラー050109「予約可能制限日数フォーマットチェック結果」より判定する</strong>
     */
    public static readonly CODE_050109 = "050109";
    /**
     * <strong>エラー050110「予約可能制限日数範囲チェック結果」より判定する</strong>
     */
    public static readonly CODE_050110 = "050110";

    /**
     * <strong>エラー150105「オーダ種別」より判定する</strong>
     */
    public static readonly CODE_150105 = "150105";

    /**
     * <strong>エラー150106「予約日と投入日の相関チェック結果」より判定する</strong>
     */
    public static readonly CODE_150106 = "150106";

    /**
     * <strong>エラー150107「予約実行日時単位フォーマットチェック結果」より判定する</strong>
     */
    public static readonly CODE_150107 = "150107";

    /**
     * <strong>エラー150108「予約実行日時単位と予約日相関チェック結果」より判定する</strong>
     */
    public static readonly CODE_150108 = "150108";

    /**
     * <strong>エラー150109「予約可能制限日数フォーマットチェック結果」より判定する</strong>
     */
    public static readonly CODE_150109 = "150109";

    /**
     * <strong>エラー150110「予約可能制限日数範囲チェック結果」より判定する</strong>
     */
    public static readonly CODE_150110 = "150110";

    /**
     * <strong>エラー180101「テナントIDフォーマットチェック結果」より判定する</strong>
     */
    public static readonly CODE_180101 = "180101";

    /**
     * <strong>エラー180201「予約情報」より判定する</strong>
     */
    public static readonly CODE_180201 = "180201";

    /**
     * <strong>エラー180202「予約情報」より判定する</strong>
     */
    public static readonly CODE_180202 = "180202";

    /**
     * <strong>エラー180203「予約情報」から卸ポータルグループプランIDを取得できない場合</strong>
     */
    public static readonly CODE_180203 = "180203";

    /**
     * <strong>エラー180204「予約情報」から回線グループIDを取得できない場合</strong>
     */
    public static readonly CODE_180204 = "180204";

    /**
     * <strong>エラー180205　内部呼出しフラグの有無を判断する</strong>
     */
    public static readonly CODE_180205 = "180205";

    /**
     * <strong>エラー180301「キャンセル不可期間フォーマットチェック結果」を判定する</strong>
     */
    public static readonly CODE_180301 = "180301";

    /**
     * <strong>エラー180302「キャンセル不可期間範囲チェック結果」を判定する</strong>
     */
    public static readonly CODE_180302 = "180302";

    /**
     * <strong>エラー180401予約キャンセルの実行日時をチェックする</strong>
     */
    public static readonly CODE_180401 = "180401";

    /**
     * <strong>エラー180501「テナントID」判定する</strong>
     */
    public static readonly CODE_180501 = "180501";

    /**
     * <strong>エラー180502「テナントID」判定する</strong>
     */
    public static readonly CODE_180502 = "180502";

    /**
     * <strong>エラー180503「テナントID」判定する</strong>
     */
    public static readonly CODE_180503 = "180503";

    /**
     * <strong>エラー180504「テナントID」判定する</strong>
     */
    public static readonly CODE_180504 = "180504";

    /**
     * <strong>エラー180505「回線グループ管理情報」より判定する</strong>
     */
    public static readonly CODE_180505 = "180505";

    /**
     * <strong>エラー180506「テナントID」判定する</strong>
     */
    public static readonly CODE_180506 = "180506";

    /**
     * <strong>エラー060105 「オーダ種別」より判定する</strong>
     */
    public static readonly CODE_060105 = "060105";

    /**
     * <strong>エラー060106 「プラン変更不可時間帯フォーマットチェック結果」より判定する</strong>
     */
    public static readonly CODE_060106 = "060106";

    /**
     * <strong>エラー060107 「プラン変更不可時間帯範囲チェック結果」より判定する</strong>
     */
    public static readonly CODE_060107 = "060107";

    /**
     * <strong>エラー060108 「予約チェック結果」より判定する</strong>
     */
    public static readonly CODE_060108 = "060108";

    /**
     * <strong>エラー060109 「プラン変更不可時間帯範囲チェック結果」より判定する</strong>
     */
    public static readonly CODE_060109 = "060109";

    /**
     * <strong>エラー060110 「予約日と投入日相関チェック結果」より判定する</strong>
     */
    public static readonly CODE_060110 = "060110";

    /**
     * <strong>エラー060111 「予約実行日時単位フォーマットチェック結果」より判定する</strong>
     */
    public static readonly CODE_060111 = "060111";

    /**
     * <strong>エラー060112 「予約実行日時単位と予約日相関チェックチェック結果」より判定する</strong>
     */
    public static readonly CODE_060112 = "060112";

    /**
     * <strong>エラー060113 「予約可能制限日数フォーマットチェックチェック結果」より判定する</strong>
     */
    public static readonly CODE_060113 = "060113";

    /**
     * <strong>エラー060114 「予約可能制限日数範囲チェック結果」より判定する</strong>
     */
    public static readonly CODE_060114 = "060114";

    /**
     * <strong>エラー060131 [プラン変更タイミング]より判定する</strong>
     */
    public static readonly CODE_060131 = "060131";

    /**
     * <strong>エラー060132 [プラン変更タイミング]より判定する</strong>
     */
    public static readonly CODE_060132 = "060132";

    /**
     * <strong>エラー100105 「オーダ種別」より判定する</strong>
     */
    public static readonly CODE_100105 = "100105";

    /**
     * <strong>エラー100106 「予約日と投入日の相関チェック結果」より判定する</strong>
     */
    public static readonly CODE_100106 = "100106";

    /**
     * <strong>エラー100107 「予約実行日時単位フォーマットチェック結果」より判定する</strong>
     */
    public static readonly CODE_100107 = "100107";

    /**
     * <strong>エラー100108 「予約実行日時単位と予約日相関チェック結果」より判定する</strong>
     */
    public static readonly CODE_100108 = "100108";

    /**
     * <strong>エラー100109 「予約可能制限日数フォーマットチェック結果」より判定する</strong>
     */
    public static readonly CODE_100109 = "100109";

    /**
     * <strong>エラー100110 「予約可能制限日数範囲チェック結果」より判定する</strong>
     */
    public static readonly CODE_100110 = "100110";

    /**
     * <strong>エラー120104 「オーダ種別」より判定する</strong>
     */
    public static readonly CODE_120104 = "120104";

    /**
     * <strong>エラー120105 「予約日と投入日相関チェック結果」より判定する</strong>
     */
    public static readonly CODE_120105 = "120105";

    /**
     * <strong>エラー120106 「予約実行日時単位フォーマットチェック結果」より判定する</strong>
     */
    public static readonly CODE_120106 = "120106";

    /**
     * <strong>エラー120107 「予約実行日時単位と予約日相関チェックチェック結果」より判定する</strong>
     */
    public static readonly CODE_120107 = "120107";

    /**
     * <strong>エラー120108 「予約可能制限日数フォーマットチェック結果」より判定する</strong>
     */
    public static readonly CODE_120108 = "120108";

    /**
     * <strong>エラー120109 「予約可能制限日数範囲チェック結果」より判定する</strong>
     */
    public static readonly CODE_120109 = "120109";

    /**
     * <strong>エラー140006 「オーダ種別」より判定する</strong>
     */
    public static readonly CODE_140006 = "140006";

    /**
     * <strong>エラー140007 「予約日と投入日の相関チェック結果」より判定する</strong>
     */
    public static readonly CODE_140007 = "140007";

    /**
     * <strong>エラー140008 「予約実行日時単位フォーマットチェック結果」より判定する</strong>
     */
    public static readonly CODE_140008 = "140008";

    /**
     * <strong>エラー140009 「予約実行日時単位と予約日相関チェックチェック」より判定する</strong>
     */
    public static readonly CODE_140009 = "140009";

    /**
     * <strong>エラー140010 「予約可能制限日数フォーマットチェック」より判定する</strong>
     */
    public static readonly CODE_140010 = "140010";

    /**
     * <strong>エラー140011 「予約可能制限日数範囲チェック結果」より判定する</strong>
     */
    public static readonly CODE_140011 = "140011";

    /**
     * <strong>エラー030105 「オーダ種別」より判定する</strong>
     */
    public static readonly CODE_030105 = "030105";

    /**
     * <strong>エラー030106 「予約日と投入日の相関チェック結果」より判定する</strong>
     */
    public static readonly CODE_030106 = "030106";

    /**
     * <strong>エラー030107 「予約実行日時単位フォーマットチェック結果」より判定する</strong>
     */
    public static readonly CODE_030107 = "030107";

    /**
     * <strong>エラー030108 「予約実行日時単位と予約日相関チェック結果」より判定する</strong>
     */
    public static readonly CODE_030108 = "030108";

    /**
     * <strong>エラー030109 「予約可能制限日数フォーマットチェック結果」より判定する</strong>
     */
    public static readonly CODE_030109 = "030109";

    /**
     * <strong>エラー030110 「予約可能制限日数範囲チェック結果」より判定する</strong>
     */
    public static readonly CODE_030110 = "030110";

    /**
     * <strong>エラー040111 「オーダ種別」より判定する</strong>
     */
    public static readonly CODE_040111 = "040111";

    /**
     * <strong>エラー040106 「予約日と投入日の相関チェック結果」より判定する</strong>
     */
    public static readonly CODE_040106 = "040106";

    /**
     * <strong>エラー040107 「予約実行日時単位フォーマットチェック結果」より判定する</strong>
     */
    public static readonly CODE_040107 = "040107";

    /**
     * <strong>エラー040108 「予約実行日時単位と予約日相関チェック結果」より判定する</strong>
     */
    public static readonly CODE_040108 = "040108";

    /**
     * <strong>エラー040109 「予約可能制限日数フォーマットチェック結果」より判定する</strong>
     */
    public static readonly CODE_040109 = "040109";

    /**
     * <strong>エラー040110 「予約可能制限日数範囲チェック結果」より判定する</strong>
     */
    public static readonly CODE_040110 = "040110";
    /** STEP1.2b版対応　追加　END */

    /** STEP2.0a版対応　追加　START */
    /**
     * <strong>エラー061302 「回線グループチェックチェック結果」より判定する</strong>
     */
    public static readonly CODE_061302 = "061302";

    /**
     * <strong>エラー061401 「プランン変更２回目結果」より判定する</strong>
     */
    public static readonly CODE_061401 = "061401";

    // STEP18.0版対応　追加　START
    /**
     * <strong>エラー061501 PCRFへの連携エラー</strong>
     */
    public static readonly CODE_061501 = "061501";
    // STEP18.0版対応　追加　END

    // STEP14.0版対応　追加　START
    /**
     * <strong>エラー061701 回線回線グループ更新に失敗</strong>
     */
    public static readonly CODE_061701 = "061701";
    // STEP14.0版対応　追加　END

    /** #378 対応  削除 START */
    //    /**
    //     * <strong>エラー061603 「プランン変更２回目結果」より判定する</strong>
    //    */
    //     public static readonly CODE_061603 = "061603";
    //
    //    /**
    //     * <strong>エラー061605 「料金プランID」より判定する</strong>
    //    */
    //     public static readonly CODE_061605 = "061605";
    //
    //    /**
    //     * <strong>エラー061605 「「プラン種別」より判定する</strong>
    //    */
    //     public static readonly CODE_061606 = "061606";
    //
    //    /**
    //     * <strong>エラー030602 代表N番を判定する</strong>
    //     */
    //     public static readonly CODE_030602 = "030602";
    //
    //    /**
    //     * <strong>エラー030604 変数「社内テナント種別」より判定する</strong>
    //     */
    //     public static readonly CODE_030604 = "030604";
    //
    //    /**
    //     * <strong>エラー030605 変数「容量追加コード」より判定する</strong>
    //     */
    //     public static readonly CODE_030605 = "030605";
    //
    //    /**
    //     * <strong>エラー180602 変数「関係チェック結果CSV」より判定する</strong>
    //     */
    //     public static readonly CODE_180602 = "180602";
    //
    //    /**
    //     * <strong>エラー180603 変数「社内フラグ」より判定する</strong>
    //     */
    //     public static readonly CODE_180603 = "180603";
    //
    //    /**
    //     * <strong>エラー141603 変数「代表N番」より判定する</strong>
    //     */
    //     public static readonly CODE_141603 = "141603";
    //
    //    /**
    //     * <strong>エラー141605 変数「プランID」より判定する</strong>
    //     */
    //     public static readonly CODE_141605 = "141605";
    //
    //    /**
    //     * <strong>エラー141606 変数「プラン種別」より判定する</strong>
    //     */
    //     public static readonly CODE_141606 = "141606";
    //
    //    /**
    //     * <strong>エラー145603 変数「代表N番」より判定する</strong>
    //     */
    //     public static readonly CODE_145603 = "145603";
    //
    //    /**
    //     * <strong>エラー145605 変数「プランID」より判定する</strong>
    //     */
    //     public static readonly CODE_145605 = "145605";
    //
    //    /**
    //     * <strong>エラー145606 変数「プラン種別」より判定する</strong>
    //     */
    //     public static readonly CODE_145606 = "145606";
    /** #378 対応  削除 END */
    /** STEP2.0a版対応　追加　END */

    /**
     * <strong>エラー510101 相関チェック、フォーマットチェックエラー</strong>
     */
    public static readonly CODE_510101 = "510101";
    /**
     * <strong>エラー510102 仮登録回線追加受付不可時間、フォーマットチェックエラー</strong>
     */
    public static readonly CODE_510102 = "510102";
    /**
     * <strong>エラー510103 仮登録回線追加受付不可時間エラー</strong>
     */
    public static readonly CODE_510103 = "510103";
    /**
     * <strong>エラー510105 既設回線存在判定エラー</strong>
     */
    public static readonly CODE_510105 = "510105";
    /**
     * <strong>エラー510106 料金プラン使用可能判定エラー</strong>
     */
    public static readonly CODE_510106 = "510106";
    /**
     * <strong>エラー510107 音声SIM判定エラー</strong>
     */
    public static readonly CODE_510107 = "510107";
    /**
     * <strong>エラー510108 回線オプション使用可能判定エラー</strong>
     */
    public static readonly CODE_510108 = "510108";
    /**
     * <strong>エラー510111 回線オプション使用可能判定エラー</strong>
     */
    public static readonly CODE_510111 = "510111";
    /**
     * <strong>エラー510109 テナントN番有効判定チェックエラー</strong>
     */
    public static readonly CODE_510109 = "510109";
    /**
     * <strong>エラー510110 電文用パラメータ取得チェックエラー</strong>
     */
    public static readonly CODE_510110 = "510110";
    /**
     * <strong>エラー510301 SOAP-API電文送受信チェックエラー</strong>
     */
    public static readonly CODE_510301 = "510301";
    /**
     * <strong>エラー510401 仮登録回線情報登録処理チェックエラー</strong>
     */
    public static readonly CODE_510401 = "510401";

    // STEP18.0版対応　追加　START
    /**
     * <strong>エラー510701 電文用パラメータ取得チェックエラー</strong>
     */
    public static readonly CODE_510701 = "510701";
    // STEP18.0版対応　追加　END

    /** STEP4.0版対応　削除　START */
    //    /**
    //     * <strong>エラー060151 仮登録回線チェックエラー</strong>
    //    */
    //     public static readonly CODE_060151 = "060151";
    /** STEP4.0版対応　削除　END */
    /** STEP3.1版対応　追加　START */
    /**
     * <strong>エラー020301 TPC情報取得結果チェックエラー</strong>
     */
    public static readonly CODE_020301 = "020301";
    /**
     * <strong>エラー020310 TPC情報取得結果チェックエラー</strong>
     */
    public static readonly CODE_030310 = "030310";
    /**
     * <strong>エラー040310 TPC情報取得結果チェックエラー</strong>
     */
    public static readonly CODE_040310 = "040310";

    /**
     * <strong>エラー060300 変更後回線プラン情報取得失敗</strong>
     */
    public static readonly CODE_060300 = "060300";

    /**
     * <strong>エラー060301 回線グループ情報取得失敗</strong>
     */
    public static readonly CODE_060301 = "060301";

    /**
     * <strong>エラー060302 グループ更新サービスパターン取得失敗</strong>
     */
    public static readonly CODE_060302 = "060302";

    /**
     * <strong>エラー061102 TPC情報取得結果チェックエラー</strong>
     */
    public static readonly CODE_061102 = "061102";

    /**
     * <strong>エラー050301 TPC情報取得結果チェックエラー</strong>
     */
    public static readonly CODE_050301 = "050301";

    /**
     * <strong>エラー100111 社内テナント種別取得エラー</strong>
     */
    public static readonly CODE_100111 = "100111";

    /**
     * <strong>エラー100112 グループテナント所属判定エラー</strong>
     */
    public static readonly CODE_100112 = "100112";

    /**
     * <strong>エラー100310 TPC情報取得結果チェックエラー</strong>
     */
    public static readonly CODE_100310 = "100310";

    /**
     * <strong>エラー100017 テナント種別取得エラー</strong>
     */
    public static readonly CODE_100017 = "100017";

    /**
     * <strong>エラー100018 グループテナント所属判定エラー</strong>
     */
    public static readonly CODE_100018 = "100018";

    /**
     * <strong>エラー120301 TPC情報取得結果チェックエラー</strong>
     */
    public static readonly CODE_120301 = "120301";

    /**
     * <strong>エラー510115 TPC情報取得結果チェックエラー</strong>
     */
    public static readonly CODE_510115 = "510115";

    /**
     * <strong>エラー150401 TPC情報取得結果チェックエラー</strong>
     */
    public static readonly CODE_150401 = "150401";

    /**
     * <strong>エラー130108 TPC情報取得結果チェックエラー</strong>
     */
    public static readonly CODE_130108 = "130108";

    /**
     * <strong>エラー130208 TPC情報取得結果チェックエラー</strong>
     */
    public static readonly CODE_130208 = "130208";

    /**
     * <strong>エラー090301 TPC情報取得結果チェックエラー</strong>
     */
    public static readonly CODE_090301 = "090301";

    /**
     * <strong>エラー141111 TPC情報取得結果チェックエラー</strong>
     */
    public static readonly CODE_141111 = "141111";

    /**
     * <strong>エラー140204 TPC情報取得結果チェックエラー</strong>
     */
    public static readonly CODE_140204 = "140204";

    /** STEP3.1版対応　追加　END */

    /** STEP4.0版対応　追加　START */

    /**
     * <strong>エラー550101 テナントIDチェックエラー</strong>
     */
    public static readonly CODE_550101 = "550101";

    /**
     * <strong>エラー550102 変数「対象回線廃止確認結果」より判定する</strong>
     */
    public static readonly CODE_550102 = "550102";

    /**
     * <strong>エラー550103 変数「関係チェック結果」より判定する</strong>
     */
    public static readonly CODE_550103 = "550103";

    /**
     * <strong>エラー550104 変数「対象回線廃止オーダチェック結果」より判定する</strong>
     */
    public static readonly CODE_550104 = "550104";

    /**
     * <strong>エラー550201 テーブル登録・更新の判定する</strong>
     */
    public static readonly CODE_550201 = "550201";

    /**
     * <strong>エラー020106 卸ポータルプランIDの判断を行う</strong>
     */
    public static readonly CODE_020106 = "020106";

    /**
     * <strong>エラー030111 変数「判定用テナント情報」より判定する</strong>
     */
    public static readonly CODE_030111 = "030111";

    /**
     * <strong>エラー030112 変数「回線利用プランID」より判定する</strong>
     */
    public static readonly CODE_030112 = "030112";

    /**
     * <strong>エラー030113 変数「廃止オーダ受付中判定結果」より判定する</strong>
     */
    public static readonly CODE_030113 = "030113";

    // STEP15.0版対応　追加　START
    /**
     * <strong>エラー030114 対象回線の利用状態より判定する</strong>
     */
    public static readonly CODE_030114 = "030114";
    // STEP15.0版対応　追加　END

    /**
     * <strong>エラー520112 変数「オーダ種別」より判定する</strong>
     */
    public static readonly CODE_520112 = "520112";

    /**
     * <strong>エラー520101 テナントIDの値をチェックし、リクエストが卸ポータルフロントからのものかを判断する</strong>
     */
    public static readonly CODE_520101 = "520101";

    /**
     * <strong>エラー520102 変数「対象回線情報」より判定する</strong>
     */
    public static readonly CODE_520102 = "520102";

    /**
     * <strong>エラー520103 変数「対象回線廃止オーダチェック結果」より判定する</strong>
     */
    public static readonly CODE_520103 = "520103";

    /**
     * <strong>エラー520104 変数「関係チェック結果」より判定する</strong>
     */
    public static readonly CODE_520104 = "520104";

    /**
     * <strong>エラー520105 半黒フラグの値を判定する</strong>
     */
    public static readonly CODE_520105 = "520105";

    /**
     * <strong>エラー520106 変数「廃止SO存在チェック結果」より判定する</strong>
     */
    public static readonly CODE_520106 = "520106";

    /**
     * <strong>エラー520107 変数「予約日と投入日の相関チェック結果」より判定する</strong>
     */
    public static readonly CODE_520107 = "520107";

    /**
     * <strong>エラー520108 変数「予約実行日時単位フォーマットチェック結果」より判定する</strong>
     */
    public static readonly CODE_520108 = "520108";

    /**
     * <strong>エラー520109 変数「予約実行日時単位と予約日相関チェック結果」より判定する</strong>
     */
    public static readonly CODE_520109 = "520109";

    /**
     * <strong>エラー520110 変数「予約可能制限日数フォーマットチェック結果」より判定する</strong>
     */
    public static readonly CODE_520110 = "520110";

    /**
     * <strong>エラー520111 変数「予約可能制限日数範囲チェック結果」より判定する</strong>
     */
    public static readonly CODE_520111 = "520111";

    /**
     * <strong>エラー520114 電文のMNP転出フラグを判定する</strong>
     */
    public static readonly CODE_520114 = "520114";

    /**
     * <strong>入力パラメータ フォーマットチェックエラー</strong>
     */
    public static readonly CODE_560101 = "560101";

    /**
     * <strong>予約情報 取得エラー</strong>
     */
    public static readonly CODE_560201 = "560201";

    /**
     * <strong>予約情報．REST電文取得エラー</strong>
     */
    public static readonly CODE_560202 = "560202";

    /**
     * <strong>システムプロパティ．「キャンセル不可期間」 フォーマットチェックエラー</strong>
     */
    public static readonly CODE_560301 = "560301";

    /**
     * <strong>システムプロパティ．「キャンセル不可期間」 範囲チェックエラー</strong>
     */
    public static readonly CODE_560302 = "560302";

    /**
     * <strong>予約キャンセル 実行日時チェックエラー</strong>
     */
    public static readonly CODE_560401 = "560401";

    /**
     * <strong>テナントID一致判定エラー</strong>
     */
    public static readonly CODE_560501 = "560501";

    /**
     * <strong>エラー540101 入力パラメータ．「テナントID」の値で判定</strong>
     */
    public static readonly CODE_540101 = "540101";

    /**
     * <strong>エラー540102 変数「対象回線廃止確認結果」より判定する</strong>
     */
    public static readonly CODE_540102 = "540102";

    /**
     * <strong>エラー540103 変数「関係チェック結果」より判定する</strong>
     */
    public static readonly CODE_540103 = "540103";

    /**
     * <strong>エラー540104 変数「対象回線廃止オーダチェック結果」より判定する</strong>
     */
    public static readonly CODE_540104 = "540104";

    /**
     * <strong>エラー540201 テーブル登録・更新の判定する</strong>
     */
    public static readonly CODE_540201 = "540201";

    /**
     * <strong>エラー100113 課金対象回線番号必須チェックエラー</strong>
     */
    public static readonly CODE_100113 = "100113";

    /**
     * <strong>エラー100114 課金対象回線のテナント所属判定エラー</strong>
     */
    public static readonly CODE_100114 = "100114";

    /**
     * <strong>エラー100115 課金対象回線の回線グループ所属判定エラー</strong>
     */
    public static readonly CODE_100115 = "100115";

    /**
     * <strong>エラー100116 グループプランID利用チェックエラー</strong>
     */
    public static readonly CODE_100116 = "100116";

    /**
     * <strong>エラー100117 課金対象回線の廃止オーダが受付中チェックエラー</strong>
     */
    public static readonly CODE_100117 = "100117";

    // STEP15.0版対応　追加　START
    /**
     * <strong>エラー100118 課金対象回線の利用状態より判定する</strong>
     */
    public static readonly CODE_100118 = "100118";
    // STEP15.0版対応　追加　END

    /**
     * <strong>グループプランID利用チェックエラー</strong>
     */
    public static readonly CODE_120110 = "120110";

    /**
     * <strong>回線の回線グループ所属判定エラー</strong>
     */
    public static readonly CODE_120111 = "120111";

    /**
     * <strong>回線の廃止オーダ受付中チェックエラー</strong>
     */
    public static readonly CODE_120112 = "120112";

    // STEP15.0版対応　追加　START
    /**
     * <strong>エラー120113 対象回線の利用状態より判定する</strong>
     */
    public static readonly CODE_120113 = "120113";
    // STEP15.0版対応　追加　END

    /**
     * <strong>エラー060133 変数「廃止オーダ受付中判定結果」より判定する</strong>
     */
    public static readonly CODE_060133 = "060133";

    /**
     * <strong>エラー060134 変数「回線情報（半黒判定）」を判定する</strong>
     */
    public static readonly CODE_060134 = "060134";

    // STEP17.0版対応　追加　START
    /**
     * <strong>エラー060135 回線の契約種別(5G専用プラン使用可否)を判定する</strong>
     */
    public static readonly CODE_060135 = "060135";
    // STEP17.0版対応　追加　END

    /**
     * <strong>エラー050103 変数「廃止オーダ受付中判定結果」より判定する</strong>
     */
    public static readonly CODE_050103 = "050103";

    // STEP15.0版対応　追加　START
    /**
     * <strong>エラー050104 対象回線の利用状態より判定する</strong>
     */
    public static readonly CODE_050104 = "050104";
    // STEP15.0版対応　追加　END

    /**
     * <strong>グループプランIDの利用チェックエラー</strong>
     */
    public static readonly CODE_090104 = "090104";

    /**
     * <strong>エラー040112 社内テナント種別を取得する</strong>
     */
    public static readonly CODE_040112 = "040112";

    /**
     * <strong>エラー040113 変数「回線利用プランID」より判定する</strong>
     */
    public static readonly CODE_040113 = "040113";

    /**
     * <strong>エラー040114 変数「廃止オーダ受付中判定結果」より判定する</strong>
     */
    public static readonly CODE_040114 = "040114";

    // STEP15.0版対応　追加　START
    /**
     * <strong>エラー040115 対象回線の利用状態より判定する</strong>
     */
    public static readonly CODE_040115 = "040115";
    // STEP15.0版対応　追加　END

    /** STEP4.0版対応　追加　END */
    /** STEP4.0d版対応　追加　START */

    /**
     * <strong>エラー190001 変数「譲渡元回線番号チェック結果」より判定する    変数「譲渡先回線番号チェック結果」より判定する
     * 			変数「譲渡データ量チェック結果」より判定する</strong>
     */
    public static readonly CODE_190001 = "190001";

    /**
     * <strong>エラー190002 データ譲渡不可フラグのフォーマットチェック</strong>
     */
    public static readonly CODE_190002 = "190002";

    /**
     * <strong>エラー190003 変数「データ譲渡不可時間帯フォーマットチェック結果」より判定する</strong>
     */
    public static readonly CODE_190003 = "190003";

    /**
     * <strong>エラー190004 変数「データ譲渡不可時間帯範囲チェック結果」より判定する</strong>
     */
    public static readonly CODE_190004 = "190004";

    /**
     * <strong>エラー190100 変数「譲渡元回線関係チェック結果」より判定する</strong>
     */
    public static readonly CODE_190100 = "190100";

    /**
     * <strong>エラー190101 変数「譲渡先回線関係チェック結果」より判定する</strong>
     */
    public static readonly CODE_190101 = "190101";

    /**
     * <strong>エラー190102 変数「譲渡元回線廃止オーダチェック結果」より判定する</strong>
     */
    public static readonly CODE_190102 = "190102";

    /**
     * <strong>エラー190103 変数「譲渡先回線廃止オーダチェック結果」より判定する</strong>
     */
    public static readonly CODE_190103 = "190103";

    /**
     * <strong>エラー190104 変数「登録済譲渡元回線ID」の有無を判定する</strong>
     */
    public static readonly CODE_190104 = "190104";

    /**
     * <strong>エラー190105 変数「譲渡元回線ID登録結果」の値を判定する</strong>
     */
    public static readonly CODE_190105 = "190105";

    /**
     * <strong>エラー190106 変数「登録済譲渡先回線ID」の有無を判定する</strong>
     */
    public static readonly CODE_190106 = "190106";

    /**
     * <strong>エラー190107 変数「譲渡先回線ID登録結果」の値を判定する</strong>
     */
    public static readonly CODE_190107 = "190107";

    /**
     * <strong>エラー190108 変数「TPC情報取得結果」より判定する</strong>
     */
    public static readonly CODE_190108 = "190108";

    /**
     * <strong>エラー190109 変数「譲渡元回線TPCデータ」より判定する</strong>
     */
    public static readonly CODE_190109 = "190109";

    /**
     * <strong>エラー190110 変数「譲渡先回線TPCデータ」より判定する</strong>
     */
    public static readonly CODE_190110 = "190110";

    /**
     * <strong>エラー190111 変数「データ譲渡TPC取得情報フォーマットチェック結果」より判定する</strong>
     */
    public static readonly CODE_190111 = "190111";

    /**
     * <strong>エラー190112 残容量と譲渡量を比較する</strong>
     */
    public static readonly CODE_190112 = "190112";

    /**
     * <strong>エラー190113 変数「データ更新結果」より判定する</strong>
     */
    public static readonly CODE_190113 = "190113";

    /**
     * <strong>エラー190114 削除結果を判定する</strong>
     */
    public static readonly CODE_190114 = "190114";

    // STEP15.0版対応　追加　START
    /**
     * <strong>エラー190115 対象譲渡元回線の利用状態より判定する</strong>
     */
    public static readonly CODE_190115 = "190115";

    /**
     * <strong>エラー190116 対象譲渡先回線の利用状態より判定する</strong>
     */
    public static readonly CODE_190116 = "190116";
    // STEP15.0版対応　追加　END

    /**
     * <strong>エラー190201 変数「譲渡元回線関係チェック結果」より判定する</strong>
     */
    public static readonly CODE_190201 = "190201";

    /**
     * <strong>エラー190202 変数「譲渡元回線廃止オーダチェック結果」より判定する</strong>
     */
    public static readonly CODE_190202 = "190202";

    /**
     * <strong>エラー190203 変数「登録済譲渡元回線ID」の有無を判定する</strong>
     */
    public static readonly CODE_190203 = "190203";

    /**
     * <strong>エラー190204 変数「譲渡元回線ID登録結果」の値を判定する</strong>
     */
    public static readonly CODE_190204 = "190204";

    /**
     * <strong>エラー190205 変数「TPC情報取得結果」より判定する</strong>
     */
    public static readonly CODE_190205 = "190205";

    /**
     * <strong>エラー190206 変数「譲渡元回線TPCデータ」より判定する</strong>
     */
    public static readonly CODE_190206 = "190206";

    /**
     * <strong>エラー190207 変数「データ譲渡TPC取得情報フォーマットチェック結果」より判定する</strong>
     */
    public static readonly CODE_190207 = "190207";

    /**
     * <strong>エラー190208 残容量と譲渡量を比較する</strong>
     */
    public static readonly CODE_190208 = "190208";

    /**
     * <strong>エラー190209 変数「データ更新結果」より判定する</strong>
     */
    public static readonly CODE_190209 = "190209";

    /**
     * <strong>エラー190210 削除結果を判定する</strong>
     */
    public static readonly CODE_190210 = "190210";

    /** STEP4.0d版対応　追加　END */

    // STEP15.0版対応　追加　START
    /**
     * <strong>エラー190211 対象譲渡元回線の利用状態より判定する</strong>
     */
    public static readonly CODE_190211 = "190211";
    // STEP15.0版対応　追加　END

    /** STEP5.0対応　追加　START */
    /**
     * <strong>エラー510112 変数「オーダ種別」より判定する</strong>
     */
    public static readonly CODE_510112 = "510112";

    // STEP15.0版対応　追加　START
    /**
     * <strong>エラー510113 変数「フルMVNO時のフォーマットチェック結果」より判定する</strong>
     */
    public static readonly CODE_510113 = "510113";
    // STEP15.0版対応　追加　END

    // STEP21.0版対応　追加　START
    /**
     * <strong>エラー510114 変数「0035でんわ情報取得」より判定する</strong>
     */
    public static readonly CODE_510114 = "510114";
    // STEP21.0版対応　追加　END

    /**
     * <strong>エラー510501 変数「予約日と投入日の相関チェック結果」より判定する</strong>
     */
    public static readonly CODE_510501 = "510501";

    /**
     * <strong>エラー510502 変数「予約実行日時単位フォーマットチェック結果」より判定する</strong>
     */
    public static readonly CODE_510502 = "510502";

    /**
     * <strong>エラー510503 変数「予約実行日時単位と予約日相関チェック結果」より判定する</strong>
     */
    public static readonly CODE_510503 = "510503";

    /**
     * <strong>エラー510504 変数「予約可能制限日数フォーマットチェック結果」より判定する</strong>
     */
    public static readonly CODE_510504 = "510504";

    /**
     * <strong>エラー510505 変数「予約可能制限日数範囲チェック結果」より判定する</strong>
     */
    public static readonly CODE_510505 = "510505";

    /**
     * <strong>エラー530101 テナントIDチェックエラー</strong>
     */
    public static readonly CODE_530101 = "530101";

    /**
     * <strong>エラー530102 変数「対象回線廃止確認結果」より判定する</strong>
     */
    public static readonly CODE_530102 = "530102";

    /**
     * <strong>エラー530103 変数「関係チェック結果」より判定する</strong>
     */
    public static readonly CODE_530103 = "530103";

    /**
     * <strong>エラー530104 変数「対象回線廃止オーダチェック結果」より判定する</strong>
     */
    public static readonly CODE_530104 = "530104";

    /**
     * <strong>エラー560203 変数「機能種別」より判定する</strong>
     */
    public static readonly CODE_560203 = "560203";

    /** STEP5.0対応　追加　END */

    /** STEP5.0対応　追加　START */
    /**
     * <strong>エラー000934 テナント最大接続数の取得</strong>
     */
    public static readonly CODE_000934 = "000934";
    /**
     * <strong>エラー000936 テナント接続数の判定</strong>
     */
    public static readonly CODE_000936 = "000936";

    /**
     * <strong>エラー000946 接続許可IPアドレスのフォーマットチェック</strong>
     */
    public static readonly CODE_000946 = "000946";
    /**
     * <strong>エラー000947 バッチ機能IPアドレスのフォーマットチェック</strong>
     */
    public static readonly CODE_000947 = "000947";
    /**
     * <strong>エラー000948 IPアドレス許可チェック</strong>
     */
    public static readonly CODE_000948 = "000948";

    /** STEP5.0対応　追加　END */

    // STEP7.0版対応　追加　START
    /**
     * <strong>エラー520115 SO管理テーブルの情報取得処理エラー</strong>
     */
    public static readonly CODE_520115 = "520115";

    /**
     * <strong>エラー520116 SO管理テーブルのオーダステータス更新処理エラー</strong>
     */
    public static readonly CODE_520116 = "520116";

    /**
     * <strong>エラー510601 回線廃止先TPC情報取得結果チェックエラー</strong>
     */
    public static readonly CODE_510601 = "510601";
    /**
     * <strong>エラー510602 回線廃止SOAP-API電文送受信チェックエラー</strong>
     */
    public static readonly CODE_510602 = "510602";
    /**
     * <strong>エラー510603 回線情報更新処理エラー</strong>
     */
    public static readonly CODE_510603 = "510603";
    /**
     * <strong>エラー510604 廃止回線登録処理エラー</strong>
     */
    public static readonly CODE_510604 = "510604";
    /**
     * <strong>エラー510605 通信量クリア処理エラー</strong>
     */
    public static readonly CODE_510605 = "510605";
    /**
     * <strong>エラー510606 キャンセル対象SOの検索処理エラー</strong>
     */
    public static readonly CODE_510606 = "510606";
    /**
     * <strong>エラー510607 SO管理ステータス変更処理エラー</strong>
     */
    public static readonly CODE_510607 = "510607";
    // STEP7.0版対応　追加　END

    // STEP18.0版対応　追加　START
    /**
     * <strong>エラー510608 PCRF連携処理エラー</strong>
     */
    public static readonly CODE_510608 = "510608";
    // STEP18.0版対応　追加　END

    // STEP11.0版対応　追加　START
    /**
     * <strong>エラー020107 卸ポータルグループプランID入力チェックエラー</strong>
     */
    public static readonly CODE_020107 = "020107";
    /**
     * <strong>エラー020108 テナントグループプラン所属チェックエラー</strong>
     */
    public static readonly CODE_020108 = "020108";
    /**
     * <strong>エラー020501 卸ポータルグループプランID所属チェックエラー</strong>
     */
    public static readonly CODE_020501 = "020501";
    /**
     * <strong>エラー020502 サービスグループパターン結果エラー</strong>
     */
    public static readonly CODE_020502 = "020502";

    // STEP20.0版対応　追加　START
    /**
     * <strong>エラー020503 前月グループ通信量取得エラー</strong>
     */
    public static readonly CODE_020503 = "020503";

    /**
     * <strong>エラー020502 前々月グループ通信量取得エラー</strong>
     */
    public static readonly CODE_020504 = "020504";
    // STEP20.0版対応　追加　END

    // STEP11.0版対応　追加　END

    // STEP12.0版対応　追加　START
    /**
     * <strong>エラー570001 電文パラメータフォーマットエラー 追加/削除共通</strong>
     */
    public static readonly CODE_570001 = "570001";
    /**
     * <strong>エラー570002 電文パラメータフォーマットエラー 追加時</strong>
     */
    public static readonly CODE_570002 = "570002";
    /**
     * <strong>エラー570003 電文パラメータフォーマットエラー 削除時</strong>
     */
    public static readonly CODE_570003 = "570003";
    /**
     * <strong>エラー570004 対象所属テナント存在確認エラー</strong>
     */
    public static readonly CODE_570004 = "570004";
    /**
     * <strong>エラー570101 プランテンプレートID存在確認エラー</strong>
     */
    public static readonly CODE_570101 = "570101";
    /**
     * <strong>エラー570102 プランテンプレートIDと対象所属テナントIDの使用可能確認エラー</strong>
     */
    public static readonly CODE_570102 = "570102";
    /**
     * <strong>エラー570103 プランテンプレートの必須項目入力エラー</strong>
     */
    public static readonly CODE_570103 = "570103";
    /**
     * <strong>エラー570104 カスタマイズ数上限　確認エラー</strong>
     */
    public static readonly CODE_570104 = "570104";
    /**
     * <strong>エラー570105 TPC接続先判定エラー</strong>
     */
    public static readonly CODE_570105 = "570105";
    /**
     * <strong>エラー570106 IDリスト取得エラー</strong>
     */
    public static readonly CODE_570106 = "570106";
    /**
     * <strong>エラー570107 ID行ロック取得エラー</strong>
     */
    public static readonly CODE_570107 = "570107";
    /**
     * <strong>エラー570108 オンデマンドプラン管理　更新エラー</strong>
     */
    public static readonly CODE_570108 = "570108";
    /**
     * <strong>エラー570109 カスタマイズ情報の連携 TPC送受信エラー</strong>
     */
    public static readonly CODE_570109 = "570109";
    /**
     * <strong>エラー570110 APNフィルタリング情報の取得 TPC送受信エラー</strong>
     */
    public static readonly CODE_570110 = "570110";
    /**
     * <strong>エラー570111 APNフィルタリング情報の連携 TPC送受信エラー</strong>
     */
    public static readonly CODE_570111 = "570111";
    /**
     * <strong>エラー570112 プランテーブル登録エラー</strong>
     */
    public static readonly CODE_570112 = "570112";
    /**
     * <strong>エラー570113 プランオプションプランテーブル登録エラー</strong>
     */
    public static readonly CODE_570113 = "570113";
    /**
     * <strong>エラー570114 テナントプランテーブル登録エラー</strong>
     */
    public static readonly CODE_570114 = "570114";
    /**
     * <strong>エラー570115 テナントプラン回線オプションテーブル登録エラー</strong>
     */
    public static readonly CODE_570115 = "570115";
    /**
     * <strong>エラー570116 行ロックテーブル　ステータス更新エラー</strong>
     */
    public static readonly CODE_570116 = "570116";
    /**
     * <strong>エラー570201 カスタマイズID取得エラー</strong>
     */
    public static readonly CODE_570201 = "570201";
    /**
     * <strong>エラー570202 プランID取得エラー</strong>
     */
    public static readonly CODE_570202 = "570202";
    /**
     * <strong>エラー570203 回線情報適用済みエラー</strong>
     */
    public static readonly CODE_570203 = "570203";
    /**
     * <strong>エラー570204 予約SO確認エラー</strong>
     */
    public static readonly CODE_570204 = "570204";
    /**
     * <strong>エラー570205 カスタマイズID行ロック取得エラー</strong>
     */
    public static readonly CODE_570205 = "570205";
    /**
     * <strong>エラー570206 サービスプランID行ロック取得エラー</strong>
     */
    public static readonly CODE_570206 = "570206";
    /**
     * <strong>エラー570207 TPC接続先判定エラー</strong>
     */
    public static readonly CODE_570207 = "570207";
    /**
     * <strong>エラー570208 APNフィルタリング情報の取得 TPC送受信エラー</strong>
     */
    public static readonly CODE_570208 = "570208";
    /**
     * <strong>エラー570209 APNフィルタリング情報の連携 TPC送受信エラー</strong>
     */
    public static readonly CODE_570209 = "570209";
    /**
     * <strong>エラー570210 プランオプションプランテーブル削除エラー</strong>
     */
    public static readonly CODE_570210 = "570210";
    /**
     * <strong>エラー570211 テナントプランテーブル削除エラー</strong>
     */
    public static readonly CODE_570211 = "570211";
    /**
     * <strong>エラー570212 テナントプラン回線オプションテーブル削除エラー</strong>
     */
    public static readonly CODE_570212 = "570212";
    /**
     * <strong>エラー570213 プランテーブル削除エラー</strong>
     */
    public static readonly CODE_570213 = "570213";
    /**
     * <strong>エラー570214 行ロックテーブル　ステータス更新エラー</strong>
     */
    public static readonly CODE_570214 = "570214";
    // STEP12.0版対応　追加　END

    // STEP13.0版対応　追加　START
    /**
     * <strong>エラー200101 オーダ種別判定エラー</strong>
     */
    public static readonly CODE_200101 = "200101";

    /**
     * <strong>エラー200102 電文パラメータフォーマットエラー</strong>
     */
    public static readonly CODE_200102 = "200102";

    /**
     * <strong>エラー200103 「グループプラン変更不可時間帯」フォーマットエラー</strong>
     */
    public static readonly CODE_200103 = "200103";

    /**
     * <strong>エラー200104 「グループプラン変更不可時間帯」判定エラー</strong>
     */
    public static readonly CODE_200104 = "200104";

    /**
     * <strong>エラー200105 予約判定エラー</strong>
     */
    public static readonly CODE_200105 = "200105";

    /**
     * <strong>エラー200106 「グループプラン変更不可時間帯」フォーマットエラー　予約時</strong>
     */
    public static readonly CODE_200106 = "200106";

    /**
     * <strong>エラー200107 「グループプラン変更不可時間帯」判定エラー　予約時</strong>
     */
    public static readonly CODE_200107 = "200107";

    /**
     * <strong>エラー200108 予約日と投入日の相関チェックエラー</strong>
     */
    public static readonly CODE_200108 = "200108";

    /**
     * <strong>エラー200109 「予約実行日時単位」フォーマットエラー</strong>
     */
    public static readonly CODE_200109 = "200109";

    /**
     * <strong>エラー200110 「予約実行日時単位」判定エラー</strong>
     */
    public static readonly CODE_200110 = "200110";

    /**
     * <strong>エラー200111 「予約可能制限日数」フォーマットエラー</strong>
     */
    public static readonly CODE_200111 = "200111";

    /**
     * <strong>エラー200112 「予約可能制限日数」判定エラー</strong>
     */
    public static readonly CODE_200112 = "200112";

    /**
     * <strong>エラー200201 社内テナント種別取得エラー</strong>
     */
    public static readonly CODE_200201 = "200201";

    /**
     * <strong>エラー200301 回線グループID行ロック取得エラー</strong>
     */
    public static readonly CODE_200301 = "200301";

    /**
     * <strong>エラー200401 回線グループテナント所属チェックエラー</strong>
     */
    public static readonly CODE_200401 = "200401";

    /**
     * <strong>エラー200501 変更前卸ポータルグループプランIDチェックエラー</strong>
     */
    public static readonly CODE_200501 = "200501";

    /**
     * <strong>エラー200601 変更後卸ポータルグループプランIDチェックエラー</strong>
     */
    public static readonly CODE_200601 = "200601";

    /**
     * <strong>エラー200701 変更後卸ポータルグループプランIDテナント所属チェックエラー</strong>
     */
    public static readonly CODE_200701 = "200701";

    /**
     * <strong>エラー200801 接続先TPC取得エラー</strong>
     */
    public static readonly CODE_200801 = "200801";

    /**
     * <strong>エラー200901 SOAP-API電文送受信エラー</strong>
     */
    public static readonly CODE_200901 = "200901";

    /**
     * <strong>エラー201001 回線グループ管理更新エラー</strong>
     */
    public static readonly CODE_201001 = "201001";
    // STEP13.0版対応　追加　END

    // STEP18.0版対応　追加　START
    /**
     * <strong>エラー201101 PCRF連携エラー</strong>
     */
    public static readonly CODE_201101 = "201101";
    // STEP18.0版対応　追加　END

    // STEP15.0版対応　追加　START
    /**
     * <strong>エラー580101 フォーマットチェックエラー</strong>
     */
    public static readonly CODE_580101 = "580101";

    /**
     * <strong>エラー580102 回線廃止済チェックエラー</strong>
     */
    public static readonly CODE_580102 = "580102";

    /**
     * <strong>エラー580103 回線テナント所属チェックエラー</strong>
     */
    public static readonly CODE_580103 = "580103";

    /**
     * <strong>エラー580105 フルMVNOプランチェックエラー</strong>
     */
    public static readonly CODE_580105 = "580105";

    /**
     * <strong>エラー580201 回線情報更新エラー</strong>
     */
    public static readonly CODE_580201 = "580201";

    /**
     * <strong>エラー580301 回線グループ情報更新エラー</strong>
     */
    public static readonly CODE_580301 = "580301";
    // STEP15.0版対応　追加　END

    // STEP21.0版対応　追加　START
    /**
     * <strong>エラー590101 フロントリクエストチェックエラー</strong>
     */
    public static readonly CODE_590101 = "590101";

    /**
     * <strong>エラー590102 回線廃止済チェックエラー</strong>
     */
    public static readonly CODE_590102 = "590102";

    /**
     * <strong>エラー590103 回線テナント所属チェックエラー</strong>
     */
    public static readonly CODE_590103 = "590103";

    /**
     * <strong>エラー590104 0035でんわプランチェックエラー</strong>
     */
    public static readonly CODE_590104 = "590104";

    /**
     * <strong>エラー590201 回線行ロック取得エラー</strong>
     */
    public static readonly CODE_590201 = "590201";

    /**
     * <strong>エラー590301 回線情報更新エラー</strong>
     */
    public static readonly CODE_590301 = "590301";
    // STEP21.0版対応　追加　END

    // STEP17.0版対応　追加　START
    /**
     * <strong>エラー600101 フロントリクエストチェックエラー</strong>
     */
    public static readonly CODE_600101 = "600101";

    /**
     * <strong>エラー600102 回線廃止済チェックエラー</strong>
     */
    public static readonly CODE_600102 = "600102";

    /**
     * <strong>エラー600103 回線テナント所属チェックエラー</strong>
     */
    public static readonly CODE_600103 = "600103";

    /**
     * <strong>エラー600104 回線廃止予約チェックエラー</strong>
     */
    public static readonly CODE_600104 = "600104";

    /**
     * <strong>エラー600201 回線行ロック取得エラー</strong>
     */
    public static readonly CODE_600201 = "600201";

    /**
     * <strong>エラー600301 回線情報更新エラー</strong>
     */
    public static readonly CODE_600301 = "600301";
    // STEP17.0版対応　追加　END

    // STEP18.0版対応　追加　END
    /**
     * <strong>リクエストデータエラー</strong>
     */
    public static readonly CODE_001301 = "001301";

    /**
     * <strong>SG値取得エラー</strong>
     */
    public static readonly CODE_001302 = "001302";

    /**
     * <strong>リクエスト送信エラー</strong>
     */
    public static readonly CODE_001303 = "001303";

    /**
     * <strong>リクエストタイムアウトエラー</strong>
     */
    public static readonly CODE_001304 = "001304";

    /**
     * <strong>HTTPレスポンスエラー</strong>
     */
    public static readonly CODE_001305 = "001305";
    // STEP18.0版対応　追加　END
    /**
     * コンストラクタ
     */
}
