export default abstract class MsgKeysConstants {
    /**
     * <strong>電文パラメータフォーマットエラー{0}[{1}]</strong>
     */
    public static readonly APLBIW0001 = "APLBIW0001";

    /**
     * <strong>回線番号[{0}]がテナント[{1}]に所属しない</strong>
     */
    public static readonly APLBIW0002 = "APLBIW0002";

    /**
     * <strong>テナントＩＤ[{0}]は社内ユーザではない</strong>
     */
    public static readonly APLBIW0003 = "APLBIW0003";

    /**
     * <strong>回線情報取得エラー</strong>
     */
    public static readonly APLBIW0004 = "APLBIW0004";

    /**
     * <strong>電文パラメータフォーマットエラー{0}[{1}]</strong>
     */
    public static readonly APLTIW0001 = "APLTIW0001";

    /**
     * <strong>社内テナント種別を取得できないー</strong>
     */
    public static readonly APLTIW0002 = "APLTIW0002";

    /**
     * <strong>社内テナント種別はC-OCNなので、プランＩＤは省略できません。</strong>
     */
    public static readonly APLTIW0003 = "APLTIW0003";

    /**
     * <strong>回線番号[{0}]がテナント[{1}]に所属しない</strong>
     */
    public static readonly APLTIW0004 = "APLTIW0004";

    /**
     * <strong>テナント[{0}]が利用できないプランID[{1}]</strong>
     */
    public static readonly APLTIW0005 = "APLTIW0005";

    /**
     * <strong>参照サービスパターン取得エラー</strong>
     */
    public static readonly APLTIW0006 = "APLTIW0006";

    /**
     * <strong>回線運用情報取得エラー、エラー内容は[{0}]</strong>
     */
    public static readonly APLTIW0007 = "APLTIW0007";

    /**
     * <strong>電文パラメータフォーマットエラー{0}[{1}]</strong>
     */
    public static readonly APGBIW0001 = "APGBIW0001";

    /**
     * <strong>回線番号リスト取得エラー</strong>
     */
    public static readonly APGBIW0002 = "APGBIW0002";

    /** STEP1.3版対応　追加　START */
    /**
     * <strong>回線グループ管理情報取得エラー</strong>
     */
    public static readonly APGBIW0003 = "APGBIW0003";

    /**
     * <strong>回線グループ基本情報取得エラー</strong>
     */
    public static readonly APGBIW0004 = "APGBIW0004";

    /**
     * <strong>社内テナント種別取得エラー</strong>
     */
    public static readonly APGBIW0005 = "APGBIW0005";
    /** STEP1.3版対応　追加　END */

    /**
     * <strong>電文パラメータフォーマットエラー{0}[{1}]</strong>
     */
    public static readonly APGTIW0001 = "APGTIW0001";

    /**
     * <strong>社内テナント種別はC-OCNなので、回線番号は省略できません。</strong>
     */
    public static readonly APGTIW0002 = "APGTIW0002";

    /**
     * <strong>プランID[{0}]がテナントID[{1}]に所属しない</strong>
     */
    public static readonly APGTIW0003 = "APGTIW0003";

    /**
     * <strong>参照サービスパターン取得エラー</strong>
     */
    public static readonly APGTIW0004 = "APGTIW0004";

    /**
     * <strong>回線グループ運用情報取得エラー、エラー内容は[{0}]</strong>
     */
    public static readonly APGTIW0005 = "APGTIW0005";

    /**
     * <strong>電文パラメータフォーマットエラー{0}[{1}]</strong>
     */
    public static readonly APLCFW0001 = "APLCFW0001";

    /**
     * <strong>【APSOL】SO一覧　電文パラメータフォーマットエラー{0}[{1}]</strong>
     */
    public static readonly APSOLW0001 = "APSOLW0001";
    /**
     * <strong>電文パラメータフォーマットエラー。{0}:[{1}] </strong>
     */
    public static readonly APSOLW0003 = "APSOLW0003";
    /**
     * <strong>日付チェックエラー。{0}:[{1}]</strong>
     */
    public static readonly APSOLW0002 = "APSOLW0002";
    /**
     * <strong> 変数「テナント階層情報」 　 エラー </strong>
     */
    public static readonly APSOLW0004 = "APSOLW0004";
    /**
     * <strong> テナントIDチェック  エラー </strong>
     */
    public static readonly APSOLW0005 = "APSOLW0005";
    /**
     * <strong> DBエラーが発生する場合  エラー </strong>
     */
    public static readonly APSOLW0006 = "APSOLW0006";

    /**
     * <strong>回線番号[{0}]がテナント[{1}]に所属しない</strong>
     */
    public static readonly APLCFW0002 = "APLCFW0002";

    /**
     * <strong>プランID[{0}]がテナントID[{1}]に所属しない</strong>
     */
    public static readonly APLCFW0003 = "APLCFW0003";

    /**
     * <strong>オプションプランID[{0}]が回線の利用しているプランで利用できない</strong>
     */
    public static readonly APLCFW0004 = "APLCFW0004";

    /**
     * <strong>更新サービスパターンポリシーIDとポリシーIDをプランから取得できない</strong>
     */
    public static readonly APLCFW0005 = "APLCFW0005";

    /**
     * <strong>フォーマットエラー[{0}][{1}]</strong>
     */
    public static readonly APLCFW0006 = "APLCFW0006";

    /**
     * <strong>SO投入失敗、エラー内容は[{0}]</strong>
     */
    public static readonly APLCFW0007 = "APLCFW0007";

    /**
     * <strong>オプションプランパラメータ取得エラー[{0}]</strong>
     */
    public static readonly APLCFW0008 = "APLCFW0008";

    /**
     * <strong>SO投入失敗[{0}]</strong>
     */
    public static readonly APLCFW0010 = "APLCFW0010";

    /** STEP1.2a版対応　追加　START */

    /**
     * <strong>{0}APIにエラーが発生しました。</strong>
     */
    public static readonly APCOMW0001 = "APCOMW0001";

    /**
     * <strong>パラメータ「SO-ID」のフォーマットが不正です。（必須、英数字15桁以下であること）</strong>
     */
    public static readonly APSODW0001 = "APSODW0001";

    /**
     * <strong>テナント階層情報が取得できません。</strong>
     */
    public static readonly APSODW0002 = "APSODW0002";

    /**
     * <strong>SO詳細情報の取得に失敗しました。</strong>
     */
    public static readonly APSODW0003 = "APSODW0003";

    /**
     * <strong>処理を開始します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3}</strong>
     */
    public static readonly APSOLI0001 = "APSOLI0001";
    /**
     * <strong>処理を終了します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3}</strong>
     */
    public static readonly APSOLI0002 = "APSOLI0002";
    /**
     * <strong>処理を開始します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3}</strong>
     */
    public static readonly APSODI0001 = "APSODI0001";

    /**
     * <strong>処理を終了します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3}</strong>
     */
    public static readonly APSODI0002 = "APSODI0002";
    /**
     * <strong>システムプロパティファイルを取得できない。</strong>
     */
    public static readonly SWOCIW0001 = "SWOCIW0001";
    /**
     * <strong>処理を開始します。 処理名:{0}</strong>
     */
    public static readonly SWOCII0001 = "SWOCII0001";
    /**
     * <strong>処理を終了します。 処理名:{0}</strong>
     */
    public static readonly SWOCII0002 = "SWOCII0002";
    /**
     * <strong>代表N番[{0}]のフォーマットチェックエラー。</strong>
     */
    public static readonly SWOCIW0002 = "SWOCIW0002";
    /**
     * <strong>受信開始時刻：[{0}]　URL：[{1}]</strong>
     */
    public static readonly SWOCII0003 = "SWOCII0003";
    /**
     * <strong>受信完了時刻：[{0}]　標準APIレスポンス：[{1}]</strong>
     */
    public static readonly SWOCII0004 = "SWOCII0004";
    /**
     * <strong>標準API受信でタイムアウトが発生しました。タイムアウト値: {0}秒</strong>
     */
    public static readonly SWOCIW0003 = "SWOCIW0003";
    /**
     * <strong>標準API受信でエラーが発生しました。</strong>
     */
    public static readonly SWOCIW0004 = "SWOCIW0004";
    /**
     * <strong>オプションサービス情報を取得できません。オプションコード：{0}</strong>
     */
    public static readonly SWOCIW0006 = "SWOCIW0006";
    /**
     * <strong>予期せぬエラーが発生した。</strong>
     */
    public static readonly SWOCIW0005 = "SWOCIW0005";

    /** STEP1.2a版対応　追加　END */

    /**
     * <strong>電文パラメータフォーマットエラー{0}[{1}]</strong>
     */
    public static readonly APLCPW0001 = "APLCPW0001";

    /**
     * <strong>回線番号[{0}]がテナント[{1}]に所属しない</strong>
     */
    public static readonly APLCPW0002 = "APLCPW0002";

    /**
     * <strong>プランID[{0}]がテナントID[{1}]に所属しない</strong>
     */
    public static readonly APLCPW0003 = "APLCPW0003";

    /**
     * <strong>回線グループIDを持っているため、対象外</strong>
     */
    public static readonly APLCPW0004 = "APLCPW0004";

    /**
     * <strong>更新サービスパターンとポリシーIDをプランから取得できない</strong>
     */
    public static readonly APLCPW0005 = "APLCPW0005";

    /**
     * <strong>フォーマットエラー[{0}][{1}]</strong>
     */
    public static readonly APLCPW0006 = "APLCPW0006";

    /**
     * <strong>SO投入失敗</strong>
     */
    public static readonly APLCPW0007 = "APLCPW0007";

    /**
     * <strong>オプションプランID[{0}]が回線の利用しているプランで利用できない</strong>
     */
    public static readonly APLCPW0008 = "APLCPW0008";

    /**
     * <strong>電文パラメータフォーマットエラー{0}[{1}]</strong>
     */
    public static readonly APLAFW0001 = "APLAFW0001";

    /**
     * <strong>回線番号[{0}]がテナント[{1}]に所属しない</strong>
     */
    public static readonly APLAFW0002 = "APLAFW0002";

    /**
     * <strong>回線グループIDを持っているため、対象外</strong>
     */
    public static readonly APLAFW0003 = "APLAFW0003";

    /**
     * <strong>SO投入失敗、エラー内容は[{0}]</strong>
     */
    public static readonly APLAFW0004 = "APLAFW0004";

    /**
     * <strong>現在の状況はディアクティベートのため、変更できない</strong>
     */
    public static readonly APLAFW0005 = "APLAFW0005";

    /**
     * <strong>現在の状況はアクティベートのため、変更できない</strong>
     */
    public static readonly APLAFW0006 = "APLAFW0006";

    /**
     * <strong>SO投入失敗</strong>
     */
    public static readonly APLAFW0007 = "APLAFW0007";

    /**
     * <strong>電文パラメータフォーマットエラー{0}[{1}]</strong>
     */
    public static readonly APLLCW0001 = "APLLCW0001";

    /**
     * <strong>回線番号リストは取得できない</strong>
     */
    public static readonly APLLCW0002 = "APLLCW0002";

    /**
     * <strong>電文パラメータフォーマットエラー{0}[{1}]</strong>
     */
    public static readonly APGCFW0001 = "APGCFW0001";

    /**
     * <strong>グループプランID[{0}]がテナント[{1}]に所属しない</strong>
     */
    public static readonly APGCFW0002 = "APGCFW0002";

    /**
     * <strong>オプションプランID[{0}]がグループの利用しているプランで利用できない</strong>
     */
    public static readonly APGCFW0003 = "APGCFW0003";

    /**
     * <strong>更新サービスパターンとポリシーIDをプランから取得できない</strong>
     */
    public static readonly APGCFW0004 = "APGCFW0004";

    /**
     * <strong>SO投入失敗、エラー内容は[{0}]</strong>
     */
    public static readonly APGCFW0005 = "APGCFW0005";

    /**
     * <strong>オプションプランのパラメータを取得できない</strong>
     */
    public static readonly APGCFW0006 = "APGCFW0006";

    /**
     * <strong>フォーマットエラー[{0}][{1}]</strong>
     */
    public static readonly APGCFW0007 = "APGCFW0007";

    /**
     * <strong>SO投入失敗</strong>
     */
    public static readonly APGCFW0008 = "APGCFW0008";

    /**
     * <strong>電文パラメータフォーマットエラー{0}[{1}]</strong>
     */
    public static readonly APCGPW0001 = "APCGPW0001";

    /**
     * <strong>更新サービスパターンをプランから取得できない</strong>
     */
    public static readonly APCGPW0002 = "APCGPW0002";

    /**
     * <strong>更新サービスパターン取得エラー</strong>
     */
    public static readonly APCGPW0003 = "APCGPW0003";

    /**
     * <strong>SO投入失敗</strong>
     */
    public static readonly APCGPW0004 = "APCGPW0004";

    /**
     * <strong>REST受信 形式チェックエラー[{0}][{1}]</strong>
     */
    public static readonly MPRCOE0101 = "MPRCOE0101";

    /**
     * <strong>REST送信 応答データ作成エラー[{0}][{1}]</strong>
     */
    public static readonly MPRCOE0201 = "MPRCOE0201";

    /**
     * <strong>REST送信 フォーマット変換エラー[{0}][{1}]</strong>
     */
    public static readonly MPRCOE0202 = "MPRCOE0202";

    /**
     * <strong>SOAP受信 HTTP受信エラー[{0}]</strong>
     */
    public static readonly MPRCOE0301 = "MPRCOE0301";

    /**
     * <strong>SOAP受信 Result判定エラー[{0}]</strong>
     */
    public static readonly MPRCOE0302 = "MPRCOE0302";

    /**
     * <strong>回線回線グループの削除に失敗。サービスオーダID：{0}</strong>
     */
    public static readonly MPRCOE0401 = "MPRCOE0401";

    /**
     * <strong>クエストデータのフォーマットチェックエラーです。[tag={0}, value={1}]</strong>
     */
    public static readonly APCOMW0101 = "APCOMW0101";

    /**
     * <strong>機能種別とURLが一致しません。[機能種別={0}, URL={1}]</strong>
     */
    public static readonly APCOMW0102 = "APCOMW0102";

    /**
     * <strong>最大同時接続数を超過しました。[現在の接続数={0}]</strong>
     */
    public static readonly APCOMW0201 = "APCOMW0201";

    /**
     * <strong>該当のテナントIDがテナントテーブルに存在しません。[テナントID={0}]</strong>
     */
    public static readonly APCOMW0301 = "APCOMW0301";

    /**
     * <strong>API認証キーが一致しません。[電文のAPI認証キー={0}, システムで求めたAPI認証キー={1}]</strong>
     */
    public static readonly APCOMW0302 = "APCOMW0302";

    /**
     * <strong>処理開始 [{0}:{1}] {2}</strong>
     */
    public static readonly APCOMD0001 = "APCOMD0001";

    /**
     * <strong>処理終了 [{0}:{1}]</strong>
     */
    public static readonly APCOMD0002 = "APCOMD0002";

    /**
     * <strong>予期せぬエラー [{0}]</strong>
     */
    public static readonly APCOME0401 = "APCOME0401";

    /**
     * <strong>DBアクセス時にリトライ回数分エラーとなりました。機能種別：[{0}]</strong>
     */
    public static readonly APCOME0402 = "APCOME0402";

    /**
     * <strong>システムプロパティファイル取得エラー</strong>
     */
    public static readonly MPCOME0201 = "MPCOME0201";

    /**
     * <strong>プロパティファイルフォーマットチェックエラー[{0}][{1}]</strong>
     */
    public static readonly MPSPME0002 = "MPSPME0002";

    /**
     * <strong>REST API受信電文ログ出力エラー[{0}]</strong>
     */
    public static readonly MPCOME0301 = "MPCOME0301";

    /**
     * <strong>REST API送信電文ログ出力エラー[{0}]</strong>
     */
    public static readonly MPCOME0302 = "MPCOME0302";

    /**
     * <strong>SOAP API受信電文ログ出力エラー[{0}]</strong>
     */
    public static readonly MPCOME0311 = "MPCOME0311";

    /**
     * <strong>SOAP API送信電文ログ出力エラー[{0}]</strong>
     */
    public static readonly MPCOME0312 = "MPCOME0312";

    /**
     * <strong>標準 API受信電文ログ出力エラー[{0}]</strong>
     */
    public static readonly MPCOME0321 = "MPCOME0321";

    /**
     * <strong>標準 API送信電文ログ出力エラー[{0}]</strong>
     */
    public static readonly MPCOME0322 = "MPCOME0322";

    /**
     * <strong>[{0}]</strong>
     */
    public static readonly APCGPW0006 = "APCGPW0006";

    /**
     * <strong>[{0}]</strong>
     */
    public static readonly APCGPW0005 = "APCGPW0005";

    /**
     * <strong>処理開始 [{0}:{1}] {2}</strong>
     */
    public static readonly MPRCOD0001 = "MPRCOD0001";

    /**
     * <strong>処理終了 [{0}:{1}]</strong>
     */
    public static readonly MPRCOD0002 = "MPRCOD0002";

    /**
     * <strong>処理開始 [{0}:{1}] {2}</strong>
     */
    public static readonly MPSCOD0001 = "MPSCOD0001";

    /**
     * <strong>処理終了 [{0}:{1}]</strong>
     */
    public static readonly MPSCOD0002 = "MPSCOD0002";

    /**
     * <strong>処理開始 [{0}:{1}]</strong>
     */
    public static readonly MPSPMD0001 = "MPSPMD0001";

    /**
     * <strong>処理終了 [{0}:{1}]</strong>
     */
    public static readonly MPSPMD0002 = "MPSPMD0002";

    /**
     * <strong>処理を開始します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3}</strong>
     */
    public static readonly APLBII0001 = "APLBII0001";

    /**
     * <strong>処理を終了します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3}</strong>
     */
    public static readonly APLBII0002 = "APLBII0002";

    /**
     * <strong>処理開始 [{0}:{1}] {2}</strong>
     */
    public static readonly APLBID0001 = "APLBID0001";

    /**
     * <strong>処理終了 [{0}:{1}]</strong>
     */
    public static readonly APLBID0002 = "APLBID0002";

    /**
     * <strong>処理を開始します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3}</strong>
     */
    public static readonly APLTII0001 = "APLTII0001";

    /**
     * <strong>処理を終了します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3}</strong>
     */
    public static readonly APLTII0002 = "APLTII0002";

    /**
     * <strong>処理開始 [{0}:{1}] {2}</strong>
     */
    public static readonly APLTID0001 = "APLTID0001";

    /**
     * <strong>処理終了 [{0}:{1}]</strong>
     */
    public static readonly APLTID0002 = "APLTID0002";

    /**
     * <strong>処理を開始します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3}</strong>
     */
    public static readonly APGBII0001 = "APGBII0001";

    /**
     * <strong>処理を終了します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3}</strong>
     */
    public static readonly APGBII0002 = "APGBII0002";

    /**
     * <strong>処理開始 [{0}:{1}] {2}</strong>
     */
    public static readonly APGBID0001 = "APGBID0001";

    /**
     * <strong>処理終了 [{0}:{1}]</strong>
     */
    public static readonly APGBID0002 = "APGBID0002";

    /**
     * <strong>処理を開始します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3}</strong>
     */
    public static readonly APGTII0001 = "APGTII0001";

    /**
     * <strong>処理を終了します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3}</strong>
     */
    public static readonly APGTII0002 = "APGTII0002";

    /**
     * <strong>処理開始 [{0}:{1}] {2}</strong>
     */
    public static readonly APGTID0001 = "APGTID0001";

    /**
     * <strong>処理終了 [{0}:{1}]</strong>
     */
    public static readonly APGTID0002 = "APGTID0002";

    /**
     * <strong>処理を開始します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3}</strong>
     */
    public static readonly APLCFI0001 = "APLCFI0001";

    /**
     * <strong>処理を終了します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3}</strong>
     */
    public static readonly APLCFI0002 = "APLCFI0002";

    /**
     * <strong>処理開始 [{0}:{1}] {2}</strong>
     */
    public static readonly APLCFD0001 = "APLCFD0001";

    /**
     * <strong>処理終了 [{0}:{1}]</strong>
     */
    public static readonly APLCFD0002 = "APLCFD0002";

    /**
     * <strong>処理開始 [{0}:{1}] {2}</strong>
     */
    public static readonly APSOLD0001 = "APSOLD0001";

    /**
     * <strong>処理終了 [{0}:{1}]</strong>
     */
    public static readonly APSOLD0002 = "APSOLD0002";

    /**
     * <strong>処理開始 [{0}:{1}] {2}</strong>
     */
    public static readonly APSODD0001 = "APSODD0001";

    /**
     * <strong>処理終了 [{0}:{1}]</strong>
     */
    public static readonly APSODD0002 = "APSODD0002";

    /**
     * <strong>処理を開始します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3}</strong>
     */
    public static readonly APLCPI0001 = "APLCPI0001";

    /**
     * <strong>処理を終了します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3}</strong>
     */
    public static readonly APLCPI0002 = "APLCPI0002";

    /**
     * <strong>処理開始 [{0}:{1}] {2}</strong>
     */
    public static readonly APLCPD0001 = "APLCPD0001";

    /**
     * <strong>処理終了 [{0}:{1}]</strong>
     */
    public static readonly APLCPD0002 = "APLCPD0002";

    /**
     * <strong>処理を開始します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3}</strong>
     */
    public static readonly APLAFI0001 = "APLAFI0001";

    /**
     * <strong>処理を終了します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3}</strong>
     */
    public static readonly APLAFI0002 = "APLAFI0002";

    /**
     * <strong>処理開始 [{0}:{1}] {2}</strong>
     */
    public static readonly APLAFD0001 = "APLAFD0001";

    /**
     * <strong>処理終了 [{0}:{1}]</strong>
     */
    public static readonly APLAFD0002 = "APLAFD0002";

    /**
     * <strong>処理を開始します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3}</strong>
     */
    public static readonly APLLCI0001 = "APLLCI0001";

    /**
     * <strong>処理を終了します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3}</strong>
     */
    public static readonly APLLCI0002 = "APLLCI0002";

    /**
     * <strong>処理開始 [{0}:{1}] {2}</strong>
     */
    public static readonly APLLCD0001 = "APLLCD0001";

    /**
     * <strong>処理終了 [{0}:{1}]</strong>
     */
    public static readonly APLLCD0002 = "APLLCD0002";

    /**
     * <strong>処理を開始します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3}</strong>
     */
    public static readonly APGCFI0001 = "APGCFI0001";

    /**
     * <strong>処理を終了します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3}</strong>
     */
    public static readonly APGCFI0002 = "APGCFI0002";

    /**
     * <strong>処理開始 [{0}:{1}] {2}</strong>
     */
    public static readonly APGCFD0001 = "APGCFD0001";

    /**
     * <strong>処理終了 [{0}:{1}]</strong>
     */
    public static readonly APGCFD0002 = "APGCFD0002";

    /**
     * <strong>処理を開始します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3}</strong>
     */
    public static readonly APCGPI0001 = "APCGPI0001";

    /**
     * <strong>処理を終了します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3}</strong>
     */
    public static readonly APCGPI0002 = "APCGPI0002";

    /**
     * <strong>処理開始 [{0}:{1}] {2}</strong>
     */
    public static readonly APCGPD0001 = "APCGPD0001";

    /**
     * <strong>処理終了 [{0}:{1}]</strong>
     */
    public static readonly APCGPD0002 = "APCGPD0002";

    /**
     * <strong>処理を開始します。 処理名:{0}</strong>
     */
    public static readonly SWLOAI0001 = "SWLOAI0001";

    /**
     * <strong>処理を終了します。 処理名:{0}</strong>
     */
    public static readonly SWLOAI0002 = "SWLOAI0002";

    /**
     * <strong>処理開始 [{0}:{1}] {2}</strong>
     */
    public static readonly SWLOAD0001 = "SWLOAD0001";

    /**
     * <strong>処理終了 [{0}:{1}]</strong>
     */
    public static readonly SWLOAD0002 = "SWLOAD0002";

    /**
     * <strong>SOAP接続の判定エラー [{0}]</strong>
     */
    public static readonly MPSCOE0002 = "MPSCOE0002";

    /**
     * <strong>タイムアウト時間経過エラー [{0}]</strong>
     */
    public static readonly MPSCOE0003 = "MPSCOE0003";

    /**
     * <strong>HTTPヘッダのレスポンスコード=200 以外エラー [{0}]</strong>
     */
    public static readonly MPSCOE0004 = "MPSCOE0004";

    /**
     * <strong>電文作成エラー</strong>
     */
    public static readonly MPSCOE0001 = "MPSCOE0001";

    /**
     * <strong>SOAPの請求電文作INFO</strong>
     */
    public static readonly MPSCOI0001 = "MPSCOI0001";

    /**
     * <strong>SOAPの請求電文HTTPヘッダ情報</strong>
     */
    public static readonly MPSCOI0002 = "MPSCOI0002";

    /**
     * <strong>SOAPボディ電文情報</strong>
     */
    public static readonly MPSCOI0003 = "MPSCOI0003";

    /**
     * <strong>変数「回線番号」結果</strong>
     */
    public static readonly APGTIW0006 = "APGTIW0006";

    /** STEP1.3版対応　追加　START */
    /**
     * <strong>プロパティファイルには回線グループ再利用不可期間（日数）は数字ではない。</strong>
     */
    public static readonly APGRGW0000 = "APGRGW0000";

    /**
     * <strong>電文パラメータフォーマットエラー</strong>
     */
    public static readonly APGRGW0001 = "APGRGW0001";

    /**
     * <strong>「社内テナント種別」エラー</strong>
     */
    public static readonly APGRGW0002 = "APGRGW0002";

    /**
     * <strong>「社内テナント種別」エラー</strong>
     */
    public static readonly APGRGW0003 = "APGRGW0003";

    /**
     * <strong>回線グループ新規結果エラー</strong>
     */
    public static readonly APGRGW0105 = "APGRGW0105";

    // STEP18.0版対応　追加　START
    /**
     * <strong>セカンダリTPCへのSO投入失敗。エラー内容[{0}]</strong>
     */
    public static readonly APGRGW0106 = "APGRGW0106";
    // STEP18.0版対応　追加　END

    /**
     * <strong>回線グループプランＩＤ取得結果エラー</strong>
     */
    public static readonly APGRGW0101 = "APGRGW0101";

    /**
     * <strong>サービスプランＩＤ取得結果エラー</strong>
     */
    public static readonly APGRGW0102 = "APGRGW0102";

    /**
     * <strong>回線グループＩＤリスト取得結果エラー</strong>
     */
    public static readonly APGRGW0103 = "APGRGW0103";

    /**
     * <strong>回線グループＩＤロック結果エラー</strong>
     */
    public static readonly APGRGW0104 = "APGRGW0104";

    /**
     * <strong>プロパティファイルには設定値がみつかりませんでした。{0}</strong>
     */
    public static readonly APGMBW0000 = "APGMBW0000";

    /**
     * <strong>電文パラメータフォーマットエラー。{0}:[{1}]</strong>
     */
    public static readonly APGMBW0001 = "APGMBW0001";

    /**
     * <strong>社内テナント種別が取得できない。</strong>
     */
    public static readonly APGMBW0002 = "APGMBW0002";

    /**
     * <strong>社内テナント種別はC-OCNため、処理中断。</strong>
     */
    public static readonly APGMBW0003 = "APGMBW0003";

    /**
     * <strong>回線グループID取得エラー</strong>
     */
    public static readonly APGMBW0005 = "APGMBW0005";

    /**
     * <strong>容量計算エラー</strong>
     */
    public static readonly APGMBW0006 = "APGMBW0006";

    /**
     * <strong>TPCにSO投入失敗。</strong>
     */
    public static readonly APGMBW0007 = "APGMBW0007";

    /**
     * <strong>適用帯域(QoS)条件/ポリシープロファイル番号取得エラー</strong>
     */
    public static readonly APGMBW0008 = "APGMBW0008";

    /**
     * <strong>TPCにSO投入失敗。</strong>
     */
    public static readonly APGMBW0009 = "APGMBW0009";

    /**
     * <strong>処理開始 [{0}:{1}] {2}</strong>
     */
    public static readonly APGMBD0001 = "APGMBD0001";

    /**
     * <strong>処理終了 [{0}:{1}]</strong>
     */
    public static readonly APGMBD0002 = "APGMBD0002";

    /**
     * <strong>処理を開始します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3}</strong>
     */
    public static readonly APGMBI0001 = "APGMBI0001";

    /**
     * <strong>処理を終了します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3}</strong>
     */
    public static readonly APGMBI0002 = "APGMBI0002";

    /**
     * <strong>回線グループ管理テーブル更新エラー</strong>
     */
    public static readonly APGRGE0107 = "APGRGE0107";

    /**
     * <strong>「回線グループＩＤ情報」=null</strong>
     */
    public static readonly APGRGW0201 = "APGRGW0201";

    /**
     * <strong>回線グループＩＤの行ロック失敗</strong>
     */
    public static readonly APGRGW0202 = "APGRGW0202";

    /**
     * <strong>テナントＩＤ確認エラー</strong>
     */
    public static readonly APGRGW0203 = "APGRGW0203";

    /**
     * <strong>ステータス確認エラー</strong>
     */
    public static readonly APGRGW0204 = "APGRGW0204";

    /**
     * <strong>「回線ＩＤ数」を取得するエラー</strong>
     */
    public static readonly APGRGW0205 = "APGRGW0205";

    /**
     * <strong>SOAPのAPIを接続するエラー</strong>
     */
    public static readonly APGRGW0206 = "APGRGW0206";

    // STEP18.0版対応　追加　START
    /**
     * <strong>PCRFへのSO投入失敗。エラー内容[{0}]</strong>
     */
    public static readonly APGRGW0207 = "APGRGW0207";
    // STEP18.0版対応　追加　END

    /**
     * <strong>回線グループ管理テーブルを更新するエラー</strong>
     */
    public static readonly APGRGE0207 = "APGRGE0207";

    /**
     * <strong>処理開始 [{0}:{1}] {2}</strong>
     */
    public static readonly APGRGD0001 = "APGRGD0001";

    /**
     * <strong>処理終了 [{0}:{1}]</strong>
     */
    public static readonly APGRGD0002 = "APGRGD0002";

    /**
     * <strong>処理を開始します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3}</strong>
     */
    public static readonly APGRGI0001 = "APGRGI0001";

    /**
     * <strong>処理を終了します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3}</strong>
     */
    public static readonly APGRGI0002 = "APGRGI0002";

    /**
     * <strong>電文パラメータフォーマットエラー。{0}:[{1}]</strong>
     */
    public static readonly APGMDW0001 = "APGMDW0001";

    /**
     * <strong>社内テナント種別が取得できない。</strong>
     */
    public static readonly APGMDW0002 = "APGMDW0002";

    /**
     * <strong>社内テナント種別はC-OCNため、処理中断。</strong>
     */
    public static readonly APGMDW0003 = "APGMDW0003";

    /**
     * <strong>電文で指定された回線グループ{0}は、テナント{1}に所属していません。</strong>
     */
    public static readonly APGMDW0004 = "APGMDW0004";

    /**
     * <strong>回線グループ管理テーブルにロックかける失敗。</strong>
     */
    public static readonly APGMDW0005 = "APGMDW0005";

    /**
     * <strong>回線グループID確認エラー</strong>
     */
    public static readonly APGMDW0100 = "APGMDW0100";

    /**
     * <strong>更新サービスパターンチェックエラー</strong>
     */
    public static readonly APGMDW0101 = "APGMDW0101";

    /**
     * <strong>TPCにSO投入失敗。</strong>
     */
    public static readonly APGMDW0103 = "APGMDW0103";

    /**
     * <strong>回線回線グループテーブル挿入失敗。</strong>
     */
    public static readonly APGMDE0014 = "APGMDE0014";

    /**
     * <strong>回線IDを取得できない。</strong>
     */
    public static readonly APGMDW0201 = "APGMDW0201";

    /**
     * <strong>TPCにSO投入失敗。</strong>
     */
    public static readonly APGMDW0202 = "APGMDW0202";

    /**
     * <strong>回線回線グループテーブル削除失敗。</strong>
     */
    public static readonly APGMDW0203 = "APGMDW0203";

    /**
     * <strong>処理開始 [{0}:{1}] {2}</strong>
     */
    public static readonly APGMDD0001 = "APGMDD0001";

    /**
     * <strong>処理終了 [{0}:{1}]</strong>
     */
    public static readonly APGMDD0002 = "APGMDD0002";

    /**
     * <strong>処理を開始します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3}</strong>
     */
    public static readonly APGMDI0001 = "APGMDI0001";

    /**
     * <strong>処理を終了します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3}</strong>
     */
    public static readonly APGMDI0002 = "APGMDI0002";

    /** STEP1.3版対応　追加　END */

    /** STEP1.2a版対応　追加　START */

    /**
     * <strong>電文パラメータフォーマットエラー。{0}:[{1}]</strong>
     */
    public static readonly APLPCW0001 = "APLPCW0001";

    /**
     * <strong>社内テナント種別が取得できない。</strong>
     */
    public static readonly APLPCW0002 = "APLPCW0002";

    /**
     * <strong>回線番号[{0}]を取得できません。</strong>
     */
    public static readonly APLPCW0003 = "APLPCW0003";

    /**
     * <strong>回線番号[{0}]がテナントID[{1}]に所属しません。</strong>
     */
    public static readonly APLPCW0004 = "APLPCW0004";

    /**
     * <strong>変更前プラン情報のチェックにエラーが発生しました。プランID：{0}</strong>
     */
    public static readonly APLPCW0005 = "APLPCW0005";

    /**
     * <strong>変更後プラン情報のチェックにエラーが発生しました。プランID：{0}</strong>
     */
    public static readonly APLPCW0006 = "APLPCW0006";

    /**
     * <strong>プラン変更条件チェックにエラーが発生しました。</strong>
     */
    public static readonly APLPCW0007 = "APLPCW0007";

    /**
     * <strong>プラン変更実施回数チェックにエラーが発生しました。</strong>
     */
    public static readonly APLPCW0008 = "APLPCW0008";

    /**
     * <strong>サービスプランIDが取得できない。</strong>
     */
    public static readonly APLPCW0009 = "APLPCW0009";

    /**
     * <strong>TPCにSO投入失敗。</strong>
     */
    public static readonly APLPCW0010 = "APLPCW0010";

    /**
     * <strong>回線情報の更新が失敗しました。</strong>
     */
    public static readonly APLPCW0011 = "APLPCW0011";

    /**
     * <strong>処理を開始します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3}</strong>
     */
    public static readonly APLPCI0001 = "APLPCI0001";

    /**
     * <strong>処理を終了します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3}</strong>
     */
    public static readonly APLPCI0002 = "APLPCI0002";

    /**
     * <strong>処理開始 [{0}:{1}] {2}</strong>
     */
    public static readonly APLPCD0001 = "APLPCD0001";

    /**
     * <strong>処理終了 [{0}:{1}]</strong>
     */
    public static readonly APLPCD0002 = "APLPCD0002";

    /** STEP1.2a版対応　追加　END */

    /** STEP1.2b版対応　追加　START */
    /**
     * <strong>リアルタイムオーダ、予約前オーダ、予約実行オーダのどのオーダなのか判定できない。</strong>
     */
    public static readonly APLAFW0011 = "APLAFW0011";
    /**
     * <strong>予約前オーダの予約日において、過去日時が指定された。予約日：{0}＜システム時刻：{1}</strong>
     */
    public static readonly APLAFW0012 = "APLAFW0012";
    /**
     * <strong>SG「予約実行日時単位」フォーマットエラー：「{0}」</strong>
     */
    public static readonly APLAFW0013 = "APLAFW0013";
    /**
     * <strong>予約前オーダの予約日において、SG「予約実行日時単位」に許容されない日時が指定された。{0}：{1}</strong>
     */
    public static readonly APLAFW0014 = "APLAFW0014";
    /**
     * <strong>SG「予約可能制限日数」フォーマットエラー：「{0}」</strong>
     */
    public static readonly APLAFW0015 = "APLAFW0015";
    /**
     * <strong>予約前オーダの予約日において、SG「予約可能制限日数」の範囲外の日時が指定された。{0}：{1}</strong>
     */
    public static readonly APLAFW0016 = "APLAFW0016";

    /**
     * <strong>リアルタイムオーダ、予約前オーダ、予約実行オーダのどのオーダなのか判定できない。</strong>
     */
    public static readonly APLPCW0012 = "APLPCW0012";

    /**
     * <strong>SG「プラン変更不可時間帯」に許容されない時間帯が指定された。{0}</strong>
     */
    public static readonly APLPCW0013 = "APLPCW0013";

    /**
     * <strong>プラン変更オーダを、SG「プラン変更不可時間帯」に許容されない時間帯に処理しようとした。{0}</strong>
     */
    public static readonly APLPCW0014 = "APLPCW0014";

    /**
     * <strong>当該オーダの回線IDに対して既に予約が入っていた。回線ID：{0}</strong>
     */
    public static readonly APLPCW0015 = "APLPCW0015";

    /**
     * <strong>「プラン変更タイミング」判定において、「月初」指定なのに、「月初」以外の日に実行しようとした。{0}</strong>
     */
    public static readonly APLPCW0031 = "APLPCW0031";

    /**
     * <strong>予約前オーダの予約日において、SG「予約実行日時単位」に許容されない日時が指定された。{0}：{1}</strong>
     */
    public static readonly APLPCW0019 = "APLPCW0019";

    /**
     * <strong>予約前オーダの予約日において、過去日時が指定された。予約日：{0}＜システム時刻：{1}</strong>
     */
    public static readonly APLPCW0017 = "APLPCW0017";

    /**
     * <strong>予約前オーダの予約日において、SG「予約実行日時単位」に許容されないフォーマットが指定された。{0}</strong>
     */
    public static readonly APLPCW0018 = "APLPCW0018";

    /**
     * <strong>予約前オーダの予約日において、SG「予約可能制限日数」に許容されないフォーマットが指定された。{0}</strong>
     */
    public static readonly APLPCW0020 = "APLPCW0020";

    /**
     * <strong>予約前オーダの予約日において、SG「予約可能制限日数」の範囲外の日時が指定された。{0}：{1}</strong>
     */
    public static readonly APLPCW0021 = "APLPCW0021";

    // STEP18.0版対応　追加　START
    /**
     * <strong>セカンダリTPCへのSO投入失敗。エラー内容[{0}]</strong>
     */
    public static readonly APLPCW0022 = "APLPCW0022";
    // STEP18.0版対応　追加　END

    /**
     * <strong>予約前オーダの「プラン変更タイミング」判定において、「月初」指定なのに、「月初」以外の予約日が指定された。{0}</strong>
     */
    public static readonly APLPCW0032 = "APLPCW0032";

    /**
     * <strong>リアルタイムオーダ、予約前オーダ、予約実行オーダのどのオーダなのか判定できない。</strong>
     */
    public static readonly APGCFW0011 = "APGCFW0011";

    /**
     * <strong>予約前オーダの予約日において、過去日時が指定された。予約日：{0}＜システム時刻：{1}</strong>
     */
    public static readonly APGCFW0012 = "APGCFW0012";

    /**
     * <strong>SG「予約実行日時単位」フォーマットエラー：「{0}」</strong>
     */
    public static readonly APGCFW0013 = "APGCFW0013";

    /**
     * <strong>予約前オーダの予約日において、SG「予約実行日時単位」に許容されない日時が指定された。{0}：{1}</strong>
     */
    public static readonly APGCFW0014 = "APGCFW0014";

    /**
     * <strong>SG「予約可能制限日数」フォーマットエラー：「{0}」</strong>
     */
    public static readonly APGCFW0015 = "APGCFW0015";

    /**
     * <strong>予約前オーダの予約日において、SG「予約可能制限日数」の範囲外の日時が指定された。{0}：{1}</strong>
     */
    public static readonly APGCFW0016 = "APGCFW0016";

    /**
     * <strong>リアルタイムオーダ、予約前オーダ、予約実行オーダのどのオーダなのか判定できない。</strong>
     */
    public static readonly APCGPW0011 = "APCGPW0011";

    /**
     * <strong>予約雨オーダの予約日において、過去日時が指定された。予約日：{0}＜システム時刻：{1}</strong>
     */
    public static readonly APCGPW0012 = "APCGPW0012";

    /**
     * <strong>SG「予約実行日時単位」フォーマットエラー：「{0}」</strong>
     */
    public static readonly APCGPW0013 = "APCGPW0013";

    /**
     * <strong>予約前オーダの予約日において、SG「予約実行日時単位」に許容されない日時が指定された。{0}：{1}</strong>
     */
    public static readonly APCGPW0014 = "APCGPW0014";

    /**
     * <strong>SG「予約可能制限日数」フォーマットエラー：「{0}」</strong>
     */
    public static readonly APCGPW0015 = "APCGPW0015";

    /**
     * <strong>予約前オーダの予約日において、SG「予約可能制限日数」の範囲外の日時が指定された。{0}：{1}</strong>
     */
    public static readonly APCGPW0016 = "APCGPW0016";

    /**
     * <strong>リアルタイムオーダ、予約前オーダ、予約実行オーダのどのオーダなのか判定できない。</strong>
     */
    public static readonly APGMDW0011 = "APGMDW0011";

    /**
     * <strong>予約前オーダの予約日において、過去日時が指定された。予約日：{0}＜システム時刻：{1}</strong>
     */
    public static readonly APGMDW0012 = "APGMDW0012";

    /**
     * <strong>SG「予約実行日時単位」フォーマットエラー：「{0}」</strong>
     */
    public static readonly APGMDW0013 = "APGMDW0013";

    /**
     * <strong>予約前オーダの予約日において、SG「予約実行日時単位」に許容されない日時が指定された。{0}：{1}</strong>
     */
    public static readonly APGMDW0014 = "APGMDW0014";

    /**
     * <strong>SG「予約可能制限日数」フォーマットエラー：「{0}」</strong>
     */
    public static readonly APGMDW0015 = "APGMDW0015";

    /**
     * <strong>予約前オーダの予約日において、SG「予約可能制限日数」の範囲外の日時が指定された。{0}：{1}</strong>
     */
    public static readonly APGMDW0016 = "APGMDW0016";

    /**
     * <strong>プラン変更の予約前オーダの予約日において、SG「プラン変更不可時間帯」に許容されない時間帯が指定された。SG「プラン変更不可時間帯」：{0}、予約日：{1}</strong>
     */
    public static readonly APLPCW0016 = "APLPCW0016";

    /**
     * <strong>リアルタイムオーダ、予約前オーダ、予約実行オーダのどのオーダなのか判定できない。</strong>
     */
    public static readonly APLCFW0011 = "APLCFW0011";

    /**
     * <strong>予約前オーダの予約日において、過去日時が指定された。予約日：{0}＜システム時刻：{1}</strong>
     */
    public static readonly APLCFW0012 = "APLCFW0012";

    /**
     * <strong>SG「予約実行日時単位」フォーマットエラー：「{0}」</strong>
     */
    public static readonly APLCFW0013 = "APLCFW0013";

    /**
     * <strong>予約前オーダの予約日において、SG「予約実行日時単位」に許容されない日時が指定された。{0}：{1}</strong>
     */
    public static readonly APLCFW0014 = "APLCFW0014";

    /**
     * <strong>SG「予約可能制限日数」フォーマットエラー：「{0}」</strong>
     */
    public static readonly APLCFW0015 = "APLCFW0015";

    /**
     * <strong>予約前オーダの予約日において、SG「予約可能制限日数」の範囲外の日時が指定された。{0}：{1}</strong>
     */
    public static readonly APLCFW0016 = "APLCFW0016";

    /**
     * <strong>処理開始 [{0}:{1}] {2}</strong>
     */
    public static readonly APSOCD0001 = "APSOCD0001";

    /**
     * <strong>処理終了 [{0}:{1}]</strong>
     */
    public static readonly APSOCD0002 = "APSOCD0002";

    /**
     * <strong>処理を開始します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3}</strong>
     */
    public static readonly APSOCI0001 = "APSOCI0001";

    /**
     * <strong>処理を終了します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3}</strong>
     */
    public static readonly APSOCI0002 = "APSOCI0002";

    /**
     * <strong>電文パラメータフォーマットエラー。SO-ID:{0} {1}:[{2}]</strong>
     */
    public static readonly APSOCW0001 = "APSOCW0001";

    /**
     * <strong>予約キャンセル対象の情報がSO管理から取得できない。SO-ID:{0} テナントID:{1}</strong>
     */
    public static readonly APSOCW0201 = "APSOCW0201";

    /**
     * <strong>予約キャンセル対象の情報がSO管理のREST電文から取得できない。SO-ID:{0} チェック項目名:{1}</strong>
     */
    public static readonly APSOCW0202 = "APSOCW0202";

    /**
     * <strong>予約キャンセル対象の情報がSO管理のREST電文から取得できない。SO-ID:{0} チェック項目名:{1}</strong>
     */
    public static readonly APSOCW0203 = "APSOCW0203";

    /**
     * <strong>予約キャンセル対象の情報がSO管理のREST電文から取得できない。SO-ID:{0} チェック項目名:{1}</strong>
     */
    public static readonly APSOCW0204 = "APSOCW0204";

    /**
     * <strong>SG「キャンセル不可期間」フォーマットエラー:「{0}」</strong>
     */
    public static readonly APSOCW0301 = "APSOCW0301";

    /**
     * <strong>SG「キャンセル不可期間」の期間中に予約キャンセルを受信しました。SO-ID:{0} SG「キャンセル不可期間」:{1} システム時刻:{2}</strong>
     */
    public static readonly APSOCW0302 = "APSOCW0302";

    /**
     * <strong>予約キャンセルにおいて、過去の予約をキャンセルしようとしました。SO-ID:{0} システム時刻:{1} 予約日:{2}</strong>
     */
    public static readonly APSOCW0401 = "APSOCW0401";

    /**
     * <strong>予約キャンセルにおいて、回線IDが自テナントに所属していませんでした。SO-ID:{0} 機能種別:{1} 回線ID:{2}</strong>
     */
    public static readonly APSOCW0501 = "APSOCW0501";

    /**
     * <strong>予約キャンセルにおいて、回線IDが自テナント配下の何れかのテナントにも所属していませんでした。SO-ID:{0} 機能種別:{1} 回線ID:{2}</strong>
     */
    public static readonly APSOCW0502 = "APSOCW0502";

    /**
     * <strong>予約キャンセルにおいて、グループプランIDが自テナントに所属していませんでした。SO-ID:{0} 機能種別:{1} グループプランID:{2}</strong>
     */
    public static readonly APSOCW0503 = "APSOCW0503";

    /**
     * <strong>予約キャンセルにおいて、グループプランIDが自テナント配下の何れかのテナントにもに所属していませんでした。SO-ID:{0} 機能種別:{1} グループプランID:{2}</strong>
     */
    public static readonly APSOCW0504 = "APSOCW0504";

    /**
     * <strong>予約キャンセルにおいて、回線グループIDが自テナントに所属していませんでした。SO-ID:{0} 機能種別:{1} 回線グループID:{2}</strong>
     */
    public static readonly APSOCW0505 = "APSOCW0505";

    /**
     * <strong>予約キャンセルにおいて、回線グループIDが自テナント配下の何れかのテナントにもに所属していませんでした。SO-ID:{0} 機能種別:{1} 回線グループID:{2}</strong>
     */
    public static readonly APSOCW0506 = "APSOCW0506";

    /**
     * <strong>リアルタイムオーダ、予約前オーダ、予約実行オーダのどのオーダなのか判定できない。</strong>
     */
    public static readonly APGMBW0011 = "APGMBW0011";

    /**
     * <strong>予約前オーダの予約日において、過去日時が指定された。予約日：{0}＜システム時刻：{1}</strong>
     */
    public static readonly APGMBW0012 = "APGMBW0012";

    /**
     * <strong>SG「予約実行日時単位」フォーマットエラー：「{0}」</strong>
     */
    public static readonly APGMBW0013 = "APGMBW0013";

    /**
     * <strong>予約前オーダの予約日において、SG「予約実行日時単位」に許容されない日時が指定された。{0}：{1}</strong>
     */
    public static readonly APGMBW0014 = "APGMBW0014";

    /**
     * <strong>SG「予約可能制限日数」フォーマットエラー：「{0}」</strong>
     */
    public static readonly APGMBW0015 = "APGMBW0015";

    /**
     * <strong>予約前オーダの予約日において、SG「予約可能制限日数」の範囲外の日時が指定された。{0}：{1}</strong>
     */
    public static readonly APGMBW0016 = "APGMBW0016";

    // STEP18.0版対応　追加　START
    /**
     * <strong>PCRFへのSO投入失敗。エラー内容[{0}]</strong>
     */
    public static readonly APGMBW0017 = "APGMBW0017";
    // STEP18.0版対応　追加　START

    /**
     * <strong>リアルタイムオーダ、予約前オーダ、予約実行オーダのどのオーダなのか判定できない。</strong>
     */
    public static readonly APLCPW0011 = "APLCPW0011";

    /**
     * <strong>予約前オーダの予約日において、過去日時が指定された。予約日：{0}＜システム時刻：{1}</strong>
     */
    public static readonly APLCPW0012 = "APLCPW0012";

    /**
     * <strong>SG「予約実行日時単位」フォーマットエラー：「{0}」</strong>
     */
    public static readonly APLCPW0013 = "APLCPW0013";

    /**
     * <strong>予約前オーダの予約日において、SG「予約実行日時単位」に許容されない日時が指定された。{0}：{1}</strong>
     */
    public static readonly APLCPW0014 = "APLCPW0014";

    /**
     * <strong>SG「予約可能制限日数」フォーマットエラー：「{0}」</strong>
     */
    public static readonly APLCPW0015 = "APLCPW0015";

    /**
     * <strong>予約前オーダの予約日において、SG「予約可能制限日数」の範囲外の日時が指定された。{0}：{1}</strong>
     */
    public static readonly APLCPW0016 = "APLCPW0016";

    /** STEP1.2b版対応　追加　END */

    /** STEP2.0a版対応　追加　START */
    /**
     * <strong>グループに所属しているため、プラン変更できない。回線番号：{0}</strong>
     */
    public static readonly APLPCW1302 = "APLPCW1302";

    /**
     * <strong>TPCにSO投入失敗。</strong>
     */
    public static readonly APLPCW1401 = "APLPCW1401";

    // STEP22.0版対応　追加　START
    /**
     * <strong>セカンダリTPCへのSO投入失敗。エラー内容[{0}]</strong>
     */
    public static readonly APLPCW1402 = "APLPCW1402";
    // STEP22.0版対応　追加　END

    /**
     * <strong>電文で指定されたテナントIDがテナント所属判定で無効と判断されました。テナントID:{0}</strong>
     */
    public static readonly APLPCE1603 = "APLPCE1603";

    /**
     * <strong>電文の変更後卸ポータルプランIDがプランに所属していませんでした。プランID:{0}</strong>
     */
    public static readonly APLPCE1605 = "APLPCE1605";

    /**
     * <strong>電文の変更前卸ポータルプランIDがプランに所属していませんでした。プランID:{0}</strong>
     */
    public static readonly APLPCE1606 = "APLPCE1606";

    /**
     * <strong>電文で指定されたテナントIDがテナント所属判定で無効と判断されました。テナントID:{0}</strong>
     */
    public static readonly APLCFE0602 = "APLCFE0602";

    /**  2.0aFIX以降のBD変更　 削除 　START */
    // /**
    //  * <strong>電文のテナントIDがテナントに所属していませんでした。テナントID:{0}</strong>
    //  */
    // public static readonly APLCFE0604 = "APLCFE0604";
    /**  2.0aFIX以降のBD変更　 削除 　END */

    /**
     * <strong>電文の卸ポータルオプションプランIDがオプションプランに所属していませんでした。オプションプランID:{0}</strong>
     */
    public static readonly APLCFE0605 = "APLCFE0605";

    /**
     * <strong>予約キャンセルにおいて、予約時の回線ID、テナントIDがテナントに所属していませんでした。回線ID:{0} テナントID:{1}</strong>
     */
    public static readonly APSOCE0602 = "APSOCE0602";

    /**
     * <strong>予約キャンセルにおいて、予約キャンセルリクエスト電文で指定されたテナントIDがテナントに存在しない。テナントID：{0}</strong>
     */
    public static readonly APSOCE0603 = "APSOCE0603";

    /**
     * <strong>電文で指定されたテナントIDがテナント所属判定で無効と判断されました。テナントID:{0}</strong>
     */
    public static readonly APGMDE1603 = "APGMDE1603";

    /**
     * <strong>電文の回線グループIDが回線グループ管理に所属していませんでした。回線グループID:{0}</strong>
     */
    public static readonly APGMDE1605 = "APGMDE1605";

    /**
     * <strong>プランIDがグループプランに所属していませんでした。プランID:{0}</strong>
     */
    public static readonly APGMDE1606 = "APGMDE1606";

    /**
     * <strong>電文で指定されたテナントIDがテナント所属判定で無効と判断されました。テナントID:{0}</strong>
     */
    public static readonly APGMDE5603 = "APGMDE5603";

    /**
     * <strong>電文の回線グループIDが回線グループ管理に所属していませんでした。回線グループID:{0}</strong>
     */
    public static readonly APGMDE5605 = "APGMDE5605";

    /**
     * <strong>プランIDがグループプランに所属していませんでした。プランID:{0}</strong>
     */
    public static readonly APGMDE5606 = "APGMDE5606";

    /** STEP2.0a版対応　追加　END */

    /** STEP3.0版対応　追加　START */
    /**
     * <strong>処理を開始します。</strong>
     */
    public static readonly CRPLDI0001 = "CRPLDI0001";
    /**
     * <strong>処理を終了します。</strong>
     */
    public static readonly CRPLDI0002 = "CRPLDI0002";
    /**
     * <strong>二重起動を検知したので終了します。</strong>
     */
    public static readonly CRPLDE0001 = "CRPLDE0001";
    /**
     * <strong>システムプロパティ値の取得に失敗しました。項目:{0}</strong>
     */
    public static readonly CRPLDE0002 = "CRPLDE0002";
    /**
     * <strong>システムプロパティ値が不正です。項目：{0}　値：{1}</strong>
     */
    public static readonly CRPLDE0003 = "CRPLDE0003";
    /**
     * <strong>マスタ化遅れ回線を検知しました。基準日{0}　件数：{1}　回線ID：{2}</strong>
     */
    public static readonly CRPLDW0001 = "CRPLDW0001";
    /**
     * <strong>電文パラメータフォーマットエラー。{0}:[{1}]</strong>
     */
    public static readonly APPLAW0001 = "APPLAW0001";
    /**
     * <strong>テナント情報の取得に失敗しました。テナントID：{0}</strong>
     */
    public static readonly APPLAW0002 = "APPLAW0002";
    /**
     * <strong>プラン情報の取得に失敗しました。サービスプランID：{0}</strong>
     */
    public static readonly APPLAW0003 = "APPLAW0003";
    /**
     * <strong>回線番号[{0}]が既に有効な状態で存在します。</strong>
     */
    public static readonly APPLAW0005 = "APPLAW0005";
    /**
     * <strong>テナント[{0}]が利用できないプランID[{1}]です。</strong>
     */
    public static readonly APPLAW0006 = "APPLAW0006";
    /**
     * <strong>テナント[{0}]のプラン[{1}]が利用できない回線オプションID[{2}]です。</strong>
     */
    public static readonly APPLAW0007 = "APPLAW0007";
    /**
     * <strong>プランID[{0}]は音声プランではありません。</strong>
     */
    public static readonly APPLAW0008 = "APPLAW0008";
    /**
     * <strong>テナント[{0}]に紐づくN番[{1}]が存在しません。</strong>
     */
    public static readonly APPLAW0009 = "APPLAW0009";
    /**
     * <strong>SO投入失敗、エラー内容は[{0}]です。</strong>
     */
    public static readonly APPLAW0010 = "APPLAW0010";
    /**
     * <strong>回線番号[{0}]の更新が失敗しました。</strong>
     */
    public static readonly APPLAW0011 = "APPLAW0011";
    /**
     * <strong>テナント[{0}]は存在しない、もしくはステータスがfalseなテナントです。</strong>
     */
    public static readonly APPLAW0012 = "APPLAW0012";
    /**
     * <strong>テナント[{0}]に紐づく帯域卸フラグが存在しません。</strong>
     */
    public static readonly APPLAW0013 = "APPLAW0013";
    /**
     * <strong>指定された回線オプション[{0}]は、テナント[{1}]では使用できません。</strong>
     */
    public static readonly APPLAW0014 = "APPLAW0014";
    /** #457対応　変更　START */
    /**
     * <strong>回線オプションID[{0}]に紐づく回線オプション情報が存在しません。</strong>
     */
    public static readonly APPLAW0016 = "APPLAW0016";
    /** #457対応　変更　END */
    /**
     * <strong>処理を開始します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3}</strong>
     */
    public static readonly APPLAI0001 = "APPLAI0001";
    /**
     * <strong>処理を終了します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3}</strong>
     */
    public static readonly APPLAI0002 = "APPLAI0002";
    /**
     * <strong>処理開始 [{0}:{1}] {2}</strong>
     */
    public static readonly APPLAD0001 = "APPLAD0001";
    /**
     * <strong>処理終了 [{0}:{1}]</strong>
     */
    public static readonly APPLAD0002 = "APPLAD0002";
    /**
     * <strong>SG「仮登録回線受付不可時間」フォーマットエラー：「{0}」</strong>
     */
    public static readonly APPLAE0001 = "APPLAE0001";
    /**
     * <strong>現在「仮登録回線受付不可時間」です。受付不可時間帯:{0}</strong>
     */
    public static readonly APPLAE0002 = "APPLAE0002";
    /**
     * <strong>指定された回線グループID{0}は既に存在している。</strong>
     */
    public static readonly APGRGW0004 = "APGRGW0004";
    /**
     * <strong>回線グループID：{0}の妥当性エラー。</strong>
     */
    public static readonly APGRGW0005 = "APGRGW0005";
    /** STEP4.0版対応　削除　START */
    /**
     * <strong>仮登録回線に対してプラン変更リクエストがありました。回線番号：{0}</strong>
     */
    // public static readonly APLPCW0033 = "APLPCW0033";
    /** STEP4.0版対応　削除　END */
    /** STEP3.0版対応　追加　END */

    /** STEP3.1版対応　追加　START */
    /**
     * <strong>テナント{0}の接続先TPC情報が取得できません。</strong>
     */
    public static readonly APLTIW0008 = "APLTIW0008";
    /**
     * <strong>テナント{0}の接続先TPC情報が取得できません。</strong>
     */
    public static readonly APLCFW0020 = "APLCFW0020";
    /**
     * <strong>テナント{0}の接続先TPC情報が取得できません。</strong>
     */
    public static readonly APLCPW0010 = "APLCPW0010";

    /**
     * <strong>テナント{0}の接続先TPC情報が取得できません。</strong>
     */
    public static readonly APLPCW0030 = "APLPCW0030";

    /**
     * <strong>変更後プランがグループに所属可能なプランではありません。回線番号：{0}、プラン：{1}</strong>
     */
    public static readonly APLPCW1303 = "APLPCW1303";

    /**
     * <strong>テナント{0}の接続先TPC情報が取得できません。</strong>
     */
    public static readonly APLAFW0010 = "APLAFW0010";

    /**
     * <strong>電文で指定された回線グループ{0}は、テナント{1}に所属していません。</strong>
     */
    public static readonly APGCFW0112 = "APGCFW0112";

    /**
     * <strong>テナント{0}の社内テナント種別が取得できません。</strong>
     */
    public static readonly APGCFW0111 = "APGCFW0111";

    /**
     * <strong>テナント{0}の接続先TPC情報が取得できません。</strong>
     */
    public static readonly APGCFW0010 = "APGCFW0010";

    /**
     * <strong>テナント{0}の社内テナント種別が取得できません。</strong>
     */
    public static readonly APCGPW0017 = "APCGPW0017";

    /**
     * <strong>電文で指定された回線グループ{0}は、テナント{1}に所属していません。</strong>
     */
    public static readonly APCGPW0018 = "APCGPW0018";

    /**
     * <strong>テナント{0}の接続先TPC情報が取得できません。</strong>
     */
    public static readonly APCGPW0007 = "APCGPW0007";

    /** #457対応　変更　START */
    /**
     * <strong>テナント{0}の接続先TPC情報が取得できません。</strong>
     */
    public static readonly APPLAW0015 = "APPLAW0015";
    /** #457対応　変更　END */

    /**
     * <strong>テナント{0}の接続先TPC情報が取得できません。</strong>
     */
    public static readonly APGMBW0010 = "APGMBW0010";

    /**
     * <strong>テナント{0}の接続先TPC情報が取得できません。</strong>
     */
    public static readonly APGRGW0108 = "APGRGW0108";

    /**
     * <strong>テナント{0}の接続先TPC情報が取得できません。</strong>
     */
    public static readonly APGRGW0208 = "APGRGW0208";

    /**
     * <strong>電文で指定された回線グループ{0}は、テナント{1}に所属していません。</strong>
     */
    public static readonly APGTIW0007 = "APGTIW0007";

    /**
     * <strong>テナント{0}の接続先TPC情報が取得できません。</strong>
     */
    public static readonly APGTIW0008 = "APGTIW0008";

    /**
     * <strong>テナント{0}の接続先TPC情報が取得できません。</strong>
     */
    public static readonly APGMDW0104 = "APGMDW0104";

    /**
     * <strong>テナント{0}の接続先TPC情報が取得できません。</strong>
     */
    public static readonly APGMDW0204 = "APGMDW0204";

    // STEP18.0版対応　追加　START
    /**
     * <strong>PCRFへのSO投入失敗。エラー内容[{0}]</strong>
     */
    public static readonly APGMDW0205 = "APGMDW0205";
    // STEP18.0版対応　追加　END

    /**
     * <strong>TPCサーバIPアドレス：SG「{0}」が取得できません。</strong>
     */
    public static readonly MPSCOE0005 = "MPSCOE0005";

    /** STEP3.1版対応　追加　END */

    /** STEP4.0版対応　追加　START */

    /**
     * <strong>カード用途の取得に失敗しました。N番：{0}</strong>
     */
    public static readonly APPLAE0017 = "APPLAE0017";

    /**
     * <strong>指定されたカード種別ID[{0}]はカード用途[{1}]で使用できる種別ではありません。</strong>
     */
    public static readonly APPLAE0018 = "APPLAE0018";

    /**
     * <strong>指定されたプランID[{0}]はカード用途[{1}]で使用できるプランではありません。</strong>
     */
    public static readonly APPLAE0019 = "APPLAE0019";

    /**
     * <strong>指定されたプランID[{0}]はカード種別[{1}]で使用できるプランではありません。</strong>
     */
    public static readonly APPLAE0020 = "APPLAE0020";

    /**
     * <strong>お客様基本情報の取得に失敗しました。N番：{0} MNP転入フラグ：{1}</strong>
     */
    public static readonly APPLAE0003 = "APPLAE0003";

    /**
     * <strong>回線番号[{0}]は既に廃止されています。</strong>
     */
    public static readonly APLENW0001 = "APLENW0001";

    /**
     * <strong>回線番号[{0}]はテナントID[{1}]に所属していません。</strong>
     */
    public static readonly APLENW0002 = "APLENW0002";

    /**
     * <strong>回線番号[{0}]はすでに廃止SOを受付中です。</strong>
     */
    public static readonly APLENW0003 = "APLENW0003";

    /**
     * <strong>回線情報の更新に失敗しました。回線番号：[{0}]</strong>
     */
    public static readonly APLENW0004 = "APLENW0004";

    /**
     * <strong>卸ポータルフロントからのリクエストではありません。テナントID：{0}</strong>
     */
    public static readonly APLENW0005 = "APLENW0005";

    /**
     * <strong>回線とテナントの所属判定時に、N番を取得できませんでした。回線ID：{0} テナントID：{1}</strong>
     */
    public static readonly APLENE0001 = "APLENE0001";

    /**
     * <strong>処理を開始します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3}</strong>
     */
    public static readonly APLENI0001 = "APLENI0001";

    /**
     * <strong>処理を終了します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3}</strong>
     */
    public static readonly APLENI0002 = "APLENI0002";

    /**
     * <strong>処理開始 [{0}:{1}] {2}</strong>
     */
    public static readonly APLEND0001 = "APLEND0001";

    /**
     * <strong>処理終了 [{0}:{1}]</strong>
     */
    public static readonly APLEND0002 = "APLEND0002";

    /**
     * <strong>社内テナント種別がC-OCN以外なので、プランIDは指定できません。</strong>
     */
    public static readonly APLTIW0009 = "APLTIW0009";

    /**
     * <strong>社内テナント種別が取得できない。</strong>
     */
    public static readonly APLCFW0017 = "APLCFW0017";

    /**
     * <strong>プランID[{0}]が回線の利用しているプランでない</strong>
     */
    public static readonly APLCFW0018 = "APLCFW0018";

    /**
     * <strong>回線番号[{0}]はすでに廃止SOを受付中です。</strong>
     */
    public static readonly APLCFW0019 = "APLCFW0019";

    // STEP18.0版対応　追加　START
    /**
     * <strong>セカンダリTPCへのSO投入失敗。エラー内容[{0}]</strong>
     */
    public static readonly APLCFW0022 = "APLCFW0022";
    // STEP18.0版対応　追加　END

    // STEP15.0版対応　追加　START
    /**
     * <strong>回線番号[{0}]の利用状態がサスペンド中のため利用できません。</strong>
     */
    public static readonly APLCFW0021 = "APLCFW0021";
    // STEP15.0版対応　追加　END

    /**
     * <strong>リアルタイムオーダ、予約前オーダ、予約実行オーダのどのオーダなのか判定できない。</strong>
     */
    public static readonly APLDLW0011 = "APLDLW0011";

    /**
     * <strong>卸ポータルフロントからのリクエストではありません。テナントID：{0}</strong>
     */
    public static readonly APLDLW0012 = "APLDLW0012";

    /**
     * <strong>回線番号[{0}]は既に廃止されています。</strong>
     */
    public static readonly APLDLW0001 = "APLDLW0001";

    /**
     * <strong>回線番号[{0}]は既に廃止オーダを受付中です。</strong>
     */
    public static readonly APLDLW0002 = "APLDLW0002";

    /**
     * <strong>回線番号[{0}]はテナントID[{1}]に所属していません。</strong>
     */
    public static readonly APLDLW0003 = "APLDLW0003";

    /**
     * <strong>回線番号[{0}]は黒化されていないSIMです。半黒SIMは廃止できません。</strong>
     */
    public static readonly APLDLW0004 = "APLDLW0004";

    /**
     * <strong>処理を開始します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3}</strong>
     */
    public static readonly APLDLI0001 = "APLDLI0001";

    /**
     * <strong>処理を終了します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3}</strong>
     */
    public static readonly APLDLI0002 = "APLDLI0002";

    /**
     * <strong>回線番号[{0}]はSOを予約中のため、廃止できません。</strong>
     */
    public static readonly APLDLW0005 = "APLDLW0005";

    /**
     * <strong>予約前オーダの予約日において、過去日時が指定された。予約日：{0}＜システム時刻：{1}</strong>
     */
    public static readonly APLDLW0006 = "APLDLW0006";

    /**
     * <strong>SG「予約実行日時単位」フォーマットエラー：「{0}」</strong>
     */
    public static readonly APLDLW0007 = "APLDLW0007";

    /**
     * <strong>予約前オーダの予約日において、SG「予約実行日時単位」に許容されない日時が指定された。{0}：{1}</strong>
     */
    public static readonly APLDLW0008 = "APLDLW0008";

    /**
     * <strong>SG「予約可能制限日数」フォーマットエラー：「{0}」</strong>
     */
    public static readonly APLDLW0009 = "APLDLW0009";

    /**
     * <strong>予約前オーダの予約日において、SG「予約可能制限日数」の範囲外の日時が指定された。{0}：{1}</strong>
     */
    public static readonly APLDLW0010 = "APLDLW0010";

    /**
     * <strong>MNP転出による回線廃止は、予約日指定不可です。回線ID：{0} 予約日：{1}</strong>
     */
    public static readonly APLDLW0013 = "APLDLW0013";

    /**
     * <strong>回線とテナントの所属判定時に、N番を取得できませんでした。回線ID：{0} テナントID：{1}</strong>
     */
    public static readonly APLDLE0002 = "APLDLE0002";

    /**
     * <strong>処理開始 [{0}:{1}] {2}</strong>
     */
    public static readonly APLDLD0001 = "APLDLD0001";

    /**
     * <strong>処理終了 [{0}:{1}]</strong>
     */
    public static readonly APLDLD0002 = "APLDLD0002";

    /**
     * <strong>お客様基本情報の取得に失敗しました。回線ID：{0} MNP転出フラグ：{1}</strong>
     */
    public static readonly APLDLE0001 = "APLDLE0001";

    /**
     * <strong>電文パラメータフォーマットエラー。SO-ID:{0} {1}:[{2}]</strong>
     */
    public static readonly APFOCW0001 = "APFOCW0001";

    /**
     * <strong>予約キャンセル対象の情報がSO管理から取得できない。SO-ID:{0} チェック項目名:{1}</strong>
     */
    public static readonly APFOCW0201 = "APFOCW0201";

    /**
     * <strong>予約キャンセル対象の情報がSO管理のREST電文から取得できない。SO-ID:{0} チェック項目名:{1}</strong>
     */
    public static readonly APFOCW0202 = "APFOCW0202";

    /**
     * <strong>SG「キャンセル不可期間」フォーマットエラー:「{0}」</strong>
     */
    public static readonly APFOCW0301 = "APFOCW0301";

    /**
     * <strong>SG「キャンセル不可期間」の期間中に予約キャンセルを受信しました。SO-ID:{0} SG「キャンセル不可期間」:{1} システム時刻:{2}</strong>
     */
    public static readonly APFOCW0302 = "APFOCW0302";

    /**
     * <strong>予約キャンセルにおいて、過去の予約をキャンセルしようとしました。SO-ID:{0} システム時刻:{1} 予約日:{2}</strong>
     */
    public static readonly APFOCW0401 = "APFOCW0401";

    /**
     * <strong>予約キャンセルにおいて、指定された所属テナントIDがSO管理情報の所属テナントIDと一致しませんでした。SO-ID:{0} 電文中の所属テナントID:{1} SO管理の所属テナントID:{2}</strong>
     */
    public static readonly APFOCW0501 = "APFOCW0501";

    /**
     * <strong>予約キャンセルにおいて、予約時の回線ID、テナントIDがテナントに所属していませんでした。SO-ID:{0} 回線ID:{1} テナントID:{2}</strong>
     */
    public static readonly APFOCE0602 = "APFOCE0602";

    /**
     * <strong>処理を開始します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3}</strong>
     */
    public static readonly APFOCI0001 = "APFOCI0001";

    /**
     * <strong>処理を終了します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3}</strong>
     */
    public static readonly APFOCI0002 = "APFOCI0002";

    /**
     * <strong>回線番号[{0}]は既に廃止されています。</strong>
     */
    public static readonly APONMW0001 = "APONMW0001";

    /**
     * <strong>回線番号[{0}]はテナントID[{1}]に所属していません。</strong>
     */
    public static readonly APONMW0002 = "APONMW0002";

    /**
     * <strong>回線番号[{0}]はすでに廃止SOを受付中です。</strong>
     */
    public static readonly APONMW0003 = "APONMW0003";

    /**
     * <strong>回線情報の更新に失敗しました。回線番号：[{0}]</strong>
     */
    public static readonly APONMW0004 = "APONMW0004";

    /**
     * <strong>卸ポータルフロントからのリクエストではありません。テナントID：{0}</strong>
     */
    public static readonly APONMW0005 = "APONMW0005";

    /**
     * <strong>お客様基本情報の取得に失敗しました。回線ID：{0} NW暗証番号変更：{1}</strong>
     */
    public static readonly APONME0001 = "APONME0001";

    /**
     * <strong>回線とテナントの所属判定時に、N番を取得できませんでした。回線ID：{0} テナントID：{1}</strong>
     */
    public static readonly APONME0002 = "APONME0002";

    /**
     * <strong>処理を開始します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3}</strong>
     */
    public static readonly APONMI0001 = "APONMI0001";

    /**
     * <strong>処理を終了します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3}</strong>
     */
    public static readonly APONMI0002 = "APONMI0002";

    /**
     * <strong>処理開始 [{0}:{1}] {2}</strong>
     */
    public static readonly APONMD0001 = "APONMD0001";

    /**
     * <strong>処理終了 [{0}:{1}]</strong>
     */
    public static readonly APONMD0002 = "APONMD0002";

    /**
     * <strong>社内テナント種別が事業者卸なので、課金対象回線番号は省略できません。テナントID：{0}</strong>
     */
    public static readonly APGCFW0017 = "APGCFW0017";

    /**
     * <strong>電文で指定されたテナントIDと課金対象回線番号の組み合わせが回線テナントに存在しない。テナントID：{0}、課金対象回線番号：{1}</strong>
     */
    public static readonly APGCFW0018 = "APGCFW0018";

    /**
     * <strong>電文で指定された回線グループIDと課金対象回線番号の組み合わせが回線回線グループに存在しない。もしくは課金対象回線番号が廃止されている。回線グループID：{0}、課金対象回線番号：{1}</strong>
     */
    public static readonly APGCFW0019 = "APGCFW0019";

    /**
     * <strong>グループプランID[{0}]がグループの利用しているプランでない。</strong>
     */
    public static readonly APGCFW0020 = "APGCFW0020";

    /**
     * <strong>電文で指定されたテナントIDがテナント所属判定で無効と判断されました。テナントID:{0}</strong>
     */
    public static readonly APGCFE0602 = "APGCFE0602";

    /**
     * <strong>容量追加コードがnullまたはフォーマットエラーです。オプションプランID:{0}</strong>
     */
    public static readonly APGCFE0605 = "APGCFE0605";

    /**
     * <strong>回線番号[{0}]はすでに廃止SOを受付中です。</strong>
     */
    public static readonly APGCFW0021 = "APGCFW0021";

    // STEP15.0版対応　追加　START
    /**
     * <strong>回線番号[{0}]の利用状態がサスペンド中のため利用できません。</strong>
     */
    public static readonly APGCFW0022 = "APGCFW0022";
    // STEP15.0版対応　追加　END

    // STEP18.0版対応　追加　START
    /**
     * <strong>セカンダリTPCへのSO投入失敗。エラー内容[{0}]</strong>
     */
    public static readonly APGCFW0023 = "APGCFW0023";
    // STEP18.0版対応　追加　END

    /**
     * <strong>回線番号[{0}]はすでに廃止SOを受付中です。</strong>
     */
    public static readonly APLPCW0034 = "APLPCW0034";

    /**
     * <strong>回線番号[{0}]のSIMステータスが半黒です。</strong>
     */
    public static readonly APLPCW0035 = "APLPCW0035";

    // STEP14.0版対応　追加　START
    /**
     * <strong>回線回線グループの更新に失敗しました。</strong>
     */
    public static readonly APLPCW0036 = "APLPCW0036";
    // STEP14.0版対応　追加　END

    // STEP17.0版対応　追加　START
    /**
     * <strong>回線番号[{0}]は5G回線ではないため5G専用プランに変更できません。</strong>
     */
    public static readonly APLPCW0037 = "APLPCW0037";
    // STEP17.0版対応　追加　END

    /**
     * <strong>グループプランID[{0}]がグループの利用しているプランでない。</strong>
     */
    public static readonly APCGPW0020 = "APCGPW0020";

    /**
     * <strong>電文で指定された回線グループIDと回線番号の組み合わせが回線回線グループに存在しない。もしくは回線番号が廃止されている。回線グループID：{0}、回線番号：{1}</strong>
     */
    public static readonly APCGPW0021 = "APCGPW0021";

    // STEP15.0版対応　追加　START
    /**
     * <strong>回線番号[{0}]の利用状態がサスペンド中のため利用できません。</strong>
     */
    public static readonly APCGPW0022 = "APCGPW0022";
    // STEP15.0版対応　追加　END

    // STEP18.0版対応　追加　START
    /**
     * <strong>セカンダリTPCへのSO投入失敗。エラー内容[{0}]</strong>
     */
    public static readonly APCGPW0023 = "APCGPW0023";
    // STEP18.0版対応　追加　END

    /**
     * <strong>回線番号[{0}]はすでに廃止SOを受付中です。</strong>
     */
    public static readonly APCGPW0019 = "APCGPW0019";

    /**
     * <strong>回線番号[{0}]はすでに廃止SOを受付中です。</strong>
     */
    public static readonly APLAFW0017 = "APLAFW0017";

    // STEP15.0版対応　追加　START
    /**
     * <strong>回線番号[{0}]の利用状態がサスペンド中のため利用できません。</strong>
     */
    public static readonly APLAFW0018 = "APLAFW0018";
    // STEP15.0版対応　追加　END

    // STEP18.0版対応　追加　START
    /**
     * <strong>セカンダリTPCへのSO投入失敗。エラー内容[{0}]</strong>
     */
    public static readonly APLAFW0019 = "APLAFW0019";
    // STEP18.0版対応　追加　END

    /**
     * <strong>グループプランID[{0}]がグループの利用しているプランでない。</strong>
     */
    public static readonly APGTIW0009 = "APGTIW0009";

    // STEP18.0版対応　追加　START
    /**
     * <strong>回線グループ運用情報(PCRF)取得エラー、エラー内容は[{0}]</strong>
     */
    public static readonly APGTIW0010 = "APGTIW0010";
    // STEP18.0版対応　追加　END

    /**
     * <strong>回線番号[{0}]はすでに廃止SOを受付中です。</strong>
     */
    public static readonly APGMDW0105 = "APGMDW0105";

    // STEP18.0版対応　追加　START
    /**
     * <strong>セカンダリTPCへのSO投入失敗。エラー内容[{0}]</strong>
     */
    public static readonly APGMDW0106 = "APGMDW0106";
    // STEP18.0版対応　追加　END

    /**
     * <strong>社内テナント種別が取得できない。</strong>
     */
    public static readonly APLCPW0017 = "APLCPW0017";

    /**
     * <strong>プランID[{0}]が回線の利用しているプランでない</strong>
     */
    public static readonly APLCPW0018 = "APLCPW0018";

    /**
     * <strong>回線番号[{0}]はすでに廃止SOを受付中です。</strong>
     */
    public static readonly APLCPW0019 = "APLCPW0019";

    // STEP15.0版対応　追加　START
    /**
     * <strong>回線番号[{0}]の利用状態がサスペンド中のため利用できません。</strong>
     */
    public static readonly APLCPW0020 = "APLCPW0020";
    // STEP15.0版対応　追加　END

    // STEP18.0版対応　追加　START
    /**
     * <strong>セカンダリTPCへのSO投入失敗。エラー内容[{0}]</strong>
     */
    public static readonly APLCPW0021 = "APLCPW0021";
    // STEP18.0版対応　追加　END

    /** STEP4.0版対応　追加　END */
    /** STEP4.0d版対応　追加　START */
    /**
     * <strong>電文パラメータフォーマットエラー。{0}:[{1}]</strong>
     */
    public static readonly APLDGW0001 = "APLDGW0001";

    /**
     * <strong>SG「データ譲渡不可フラグ」に許容されないフォーマットが指定された。{0}</strong>
     */
    public static readonly APLDGW0002 = "APLDGW0002";

    /**
     * <strong>SG「データ譲渡不可時間帯」に許容されない時間帯が指定された。{0}</strong>
     */
    public static readonly APLDGW0003 = "APLDGW0003";

    /**
     * <strong>データ譲渡オーダを、SG「データ譲渡不可フラグ[{0}]」、「データ譲渡不可時間帯[{1}]」に許容されない時間帯に処理しようとした。</strong>
     */
    public static readonly APLDGW0004 = "APLDGW0004";

    /**
     * <strong>電文で指定された譲渡元回線番号とテナントIDの関係が、テナント所属判定で無効と判断されました。譲渡元回線番号:{0}、テナントID:{1}</strong>
     */
    public static readonly APLDGW0101 = "APLDGW0101";

    /**
     * <strong>電文で指定された譲渡先回線番号とテナントIDの関係が、テナント所属判定で無効と判断されました。譲渡先回線番号:{0}、テナントID:{1}</strong>
     */
    public static readonly APLDGW0102 = "APLDGW0102";

    /**
     * <strong>電文で指定された譲渡元回線番号[{0}]はすでに廃止SOを受付中です。</strong>
     */
    public static readonly APLDGW0103 = "APLDGW0103";

    /**
     * <strong>電文で指定された譲渡先回線番号[{0}]はすでに廃止SOを受付中です。</strong>
     */
    public static readonly APLDGW0104 = "APLDGW0104";

    /**
     * <strong>電文で指定された譲渡元回線番号[{0}]はデータ譲渡SOを受付中です。</strong>
     */
    public static readonly APLDGW0105 = "APLDGW0105";

    /**
     * <strong>電文で指定された譲渡先回線番号[{0}]はデータ譲渡SOを受付中です。</strong>
     */
    public static readonly APLDGW0106 = "APLDGW0106";

    /**
     * <strong>データ譲渡回線テーブルへの譲渡元回線番号[{0}]の追加に失敗しました。</strong>
     */
    public static readonly APLDGW0107 = "APLDGW0107";

    /**
     * <strong>データ譲渡回線テーブルへの譲渡先回線番号[{0}]の追加に失敗しました。</strong>
     */
    public static readonly APLDGW0108 = "APLDGW0108";

    /**
     * <strong>テナント{0}の接続先TPC情報が取得できません。</strong>
     */
    public static readonly APLDGW0109 = "APLDGW0109";

    /**
     * <strong>譲渡元回線番号[{0}]の情報取得処理にて、TPCにSO投入失敗しました。エラー内容は[{1}]です。</strong>
     */
    public static readonly APLDGW0110 = "APLDGW0110";

    /**
     * <strong>譲渡先回線番号[{0}]の情報取得処理にて、TPCにSO投入失敗しました。エラー内容は[{1}]です。</strong>
     */
    public static readonly APLDGW0111 = "APLDGW0111";

    /**
     * <strong>取得項目フォーマットエラー。{0}:[{1}]</strong>
     */
    public static readonly APLDGW0112 = "APLDGW0112";

    /**
     * <strong>譲渡データ量[{0}(byte)]が、譲渡元回線の残bucket容量[{1}(byte)]を上回ってます。</strong>
     */
    public static readonly APLDGW0113 = "APLDGW0113";

    /**
     * <strong>譲渡先回線[{0}]の加算・譲渡元回線[{1}]の減算処理にて、TPCにSO投入失敗しました。エラー内容は[{2}]です。</strong>
     */
    public static readonly APLDGW0114 = "APLDGW0114";

    /**
     * <strong>データ譲渡回線テーブルからの譲渡元回線[{0}]・譲渡先回線[{1}]の削除に失敗しました。</strong>
     */
    public static readonly APLDGW0115 = "APLDGW0115";

    // STEP15.0版対応　追加　START
    /**
     * <strong>電文で指定された譲渡元回線番号[{0}]は利用状態がサスペンド中のため利用できません。</strong>
     */
    public static readonly APLDGW0116 = "APLDGW0116";

    /**
     * <strong>電文で指定された譲渡先回線番号[{0}]は利用状態がサスペンド中のため利用できません。</strong>
     */
    public static readonly APLDGW0117 = "APLDGW0117";
    // STEP15.0版対応　追加　END

    // STEP18.0版対応　追加　START
    /**
     * <strong>譲渡先回線[{0}]の加算・譲渡元回線[{1}]の減算処理にて、セカンダリTPCにSO投入失敗しました。エラー内容は[{2}]です。</strong>
     */
    public static readonly APLDGW0118 = "APLDGW0118";
    // STEP18.0版対応　追加　END

    /**
     * <strong>電文で指定された譲渡元回線番号とテナントIDの関係が、テナント所属判定で無効と判断されました。譲渡元回線番号:{0}、テナントID:{1}</strong>
     */
    public static readonly APLDGW0201 = "APLDGW0201";

    /**
     * <strong>電文で指定された譲渡元回線番号[{0}]はすでに廃止SOを受付中です。</strong>
     */
    public static readonly APLDGW0202 = "APLDGW0202";

    /**
     * <strong>電文で指定された譲渡元回線番号[{0}]はデータ譲渡SOを受付中です。</strong>
     */
    public static readonly APLDGW0203 = "APLDGW0203";

    /**
     * <strong>データ譲渡回線テーブルへの譲渡元回線番号[{0}]の追加に失敗しました。</strong>
     */
    public static readonly APLDGW0204 = "APLDGW0204";

    /**
     * <strong>テナント{0}の接続先TPC情報が取得できません。</strong>
     */
    public static readonly APLDGW0205 = "APLDGW0205";

    /**
     * <strong>譲渡元回線番号[{0}]の情報取得処理にて、TPCにSO投入失敗しました。エラー内容は[{1}]です。</strong>
     */
    public static readonly APLDGW0206 = "APLDGW0206";

    /**
     * <strong>取得項目フォーマットエラー。{0}:[{1}]</strong>
     */
    public static readonly APLDGW0207 = "APLDGW0207";

    /**
     * <strong>譲渡データ量[{0}(byte)]が、譲渡元回線の残bucket容量[{1}(byte)]を上回ってます。</strong>
     */
    public static readonly APLDGW0208 = "APLDGW0208";

    /**
     * <strong>譲渡元回線[{0}]の減算処理にて、TPCにSO投入失敗しました。エラー内容は[{1}]です。</strong>
     */
    public static readonly APLDGW0209 = "APLDGW0209";

    /**
     * <strong>データ譲渡回線テーブルからの譲渡元回線[{0}]の削除に失敗しました。</strong>
     */
    public static readonly APLDGW0210 = "APLDGW0210";

    // STEP15.0版対応　追加　START
    /**
     * <strong>電文で指定された譲渡元回線番号[{0}]は利用状態がサスペンド中のため利用できません。</strong>
     */
    public static readonly APLDGW0211 = "APLDGW0211";
    // STEP15.0版対応　追加　END

    // STEP18.0版対応　追加　START
    /**
     * <strong>譲渡元回線[{0}]の減算処理にて、セカンダリTPCにSO投入失敗しました。エラー内容は[{1}]です。</strong>
     */
    public static readonly APLDGW0212 = "APLDGW0212";
    // STEP18.0版対応　追加　END

    /**
     * <strong>処理を開始します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3}</strong>
     */
    public static readonly APLDGI0001 = "APLDGI0001";

    /**
     * <strong>処理を終了します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3}</strong>
     */
    public static readonly APLDGI0002 = "APLDGI0002";

    /** STEP4.0d版対応　追加　END */

    /** STEP5.0対応　追加　START */

    /**
     * <strong>回線番号[{0}]は既に廃止されています。</strong>
     */
    public static readonly APRESW0001 = "APRESW0001";

    /**
     * <strong>回線番号[{0}]はテナントID[{1}]に所属していません。</strong>
     */
    public static readonly APRESW0002 = "APRESW0002";

    /**
     * <strong>回線番号[{0}]はすでに廃止SOを受付中です。</strong>
     */
    public static readonly APRESW0003 = "APRESW0003";

    /**
     * <strong>カード種別名の取得に失敗しました。カード種別ID：[{0}]</strong>
     */
    public static readonly APRESW0004 = "APRESW0004";

    /**
     * <strong>回線情報の更新に失敗しました。回線番号：[{0}]</strong>
     */
    public static readonly APRESW0005 = "APRESW0005";

    /**
     * <strong>卸ポータルフロントからのリクエストではありません。テナントID：{0}</strong>
     */
    public static readonly APRESW0006 = "APRESW0006";

    /**
     * <strong>回線とテナントの所属判定時に、N番を取得できませんでした。回線ID：{0} テナントID：{1}</strong>
     */
    public static readonly APRESE0001 = "APRESE0001";

    /**
     * <strong>処理を開始します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3}</strong>
     */
    public static readonly APRESI0001 = "APRESI0001";

    /**
     * <strong>処理を終了します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3}</strong>
     */
    public static readonly APRESI0002 = "APRESI0002";

    /**
     * <strong>処理開始 [{0}:{1}] {2}</strong>
     */
    public static readonly APRESD0001 = "APRESD0001";

    /**
     * <strong>処理終了 [{0}:{1}]</strong>
     */
    public static readonly APRESD0002 = "APRESD0002";

    /**
     * <strong>テナントの同時接続数判定に失敗しました。[テナントID={0}]</strong>
     */
    public static readonly APCOME0211 = "APCOME0211";

    /**
     * <strong>テナントの最大同時接続数を超過しました。[テナントID={0}][現在の接続数={1}]</strong>
     */
    public static readonly APCOMW0213 = "APCOMW0213";

    /**
     * <strong>リアルタイムオーダ、予約前オーダ、予約実行オーダのどのオーダなのか判定できない。</strong>
     */
    public static readonly APPLAW0020 = "APPLAW0020";

    /**
     * <strong>予約前オーダの予約日において、過去日時が指定された。予約日：{0}＜システム時刻：{1}</strong>
     */
    public static readonly APPLAW0021 = "APPLAW0021";

    /**
     * <strong>SG「予約実行日時単位」フォーマットエラー：「{0}」</strong>
     */
    public static readonly APPLAW0022 = "APPLAW0022";

    /**
     * <strong>予約前オーダの予約日において、SG「予約実行日時単位」に許容されない日時が指定された。{0}：{1}</strong>
     */
    public static readonly APPLAW0023 = "APPLAW0023";

    /**
     * <strong>SG「予約可能制限日数」フォーマットエラー：「{0}」</strong>
     */
    public static readonly APPLAW0024 = "APPLAW0024";

    /**
     * <strong>予約前オーダの予約日において、SG「予約可能制限日数」の範囲外の日時が指定された。{0}：{1}</strong>
     */
    public static readonly APPLAW0025 = "APPLAW0025";

    /**
     * <strong>指定された回線オプション[{0}]は、代表N番[{1}]では使用できません。</strong>
     */
    public static readonly APPLAW0026 = "APPLAW0026";

    /**
     * <strong>指定された回線オプション[{0}]は、代表N番[{1}]では使用できません。</strong>
     */
    public static readonly APPLAW0027 = "APPLAW0027";

    /**
     * <strong>指定された回線ID[{0}]は、予約オーダ[SO-ID:{1}]で既に開通予定です。</strong>
     */
    public static readonly APPLAW0028 = "APPLAW0028";

    /**
     * <strong>テナント[{0}]は存在しない、もしくはステータスがfalseなテナントです。</strong>
     */
    public static readonly APPLAW0029 = "APPLAW0029";

    /**
     * <strong>指定されたSO-IDの機能種別は予約キャンセル不可です。SO-ID:{0} 機能種別:{1}</strong>
     */
    public static readonly APFOCW0203 = "APFOCW0203";

    /**
     * <strong>テナントで許容するIPアドレス情報のフォーマットが不正です。[テナントID={0}][IPアドレス={1}][サブネットマスク={2}]</strong>
     */
    public static readonly APCOME0311 = "APCOME0311";

    /**
     * <strong>SG「接続許可IPアドレス」フォーマットエラー：「{0}」</strong>
     */
    public static readonly APCOME0312 = "APCOME0312";

    /**
     * <strong>許可されていない接続元からのアクセスです。[テナントID={0}][接続元IPアドレス={1}]</strong>
     */
    public static readonly APCOMW0313 = "APCOMW0313";
    /** STEP5.0対応　追加　END */

    // STEP7.0版対応　追加　START
    /**
     * <strong>回線廃止テナント[{0}]の接続先TPC情報が取得できません。</strong>
     */
    public static readonly APPLAW0030 = "APPLAW0030";

    /**
     * <strong>SO投入失敗、エラー内容は[{0}]です。</strong>
     */
    public static readonly APPLAW0031 = "APPLAW0031";

    /**
     * <strong>回線情報の更新に失敗しました。回線番号：[{0}]</strong>
     */
    public static readonly APPLAW0032 = "APPLAW0032";

    /**
     * <strong>廃止回線情報テーブル格納時にエラーが発生しました。回線番号：{0}</strong>
     */
    public static readonly APPLAW0033 = "APPLAW0033";

    /**
     * <strong>回線通信量のロックに失敗しました。回線番号：{0} 年月：{1}</strong>
     */
    public static readonly APPLAW0034 = "APPLAW0034";

    /**
     * <strong>回線通信量の排他制御のタイムアウトが発生しました。回線番号：{0} 年月：{1}</strong>
     */
    public static readonly APPLAW0035 = "APPLAW0035";

    /**
     * <strong>回線通信量の更新に失敗しました。回線番号：{0} 年月：{1}</strong>
     */
    public static readonly APPLAW0036 = "APPLAW0036";

    /**
     * <strong>SO管理情報の取得に失敗しました。回線番号：{0}</strong>
     */
    public static readonly APPLAW0037 = "APPLAW0037";

    /**
     * <strong>SO管理情報の更新に失敗しました。回線番号：{0} サービスオーダID：{1}</strong>
     */
    public static readonly APPLAW0038 = "APPLAW0038";

    // STEP21.0版対応　追加　START
    /**
     * <strong>セカンダリTPCへのSO投入失敗。エラー内容[{0}]</strong>
     */
    public static readonly APPLAW0039 = "APPLAW0039";

    /**
     * <strong>セカンダリTPCへのSO投入失敗。エラー内容[{0}]</strong>
     */
    public static readonly APPLAW0040 = "APPLAW0040";
    // STEP21.0版対応　追加　END

    /**
     * <strong>お客様基本情報の取得に失敗しました。回線番号：{0} MNP転入フラグ：{1}</strong>
     */
    public static readonly APPLAE0004 = "APPLAE0004";

    /**
     * <strong>0035でんわ情報の取得に失敗しました。テナントID：[{0}]</strong>
     */
    public static readonly APPLAE0005 = "APPLAE0005";

    /**
     * <strong>SO管理情報の取得に失敗しました。回線番号：{0}</strong>
     */
    public static readonly APLDLW0015 = "APLDLW0015";

    /**
     * <strong>SO管理情報の更新に失敗しました。回線番号：{0} サービスオーダID：{1}</strong>
     */
    public static readonly APLDLW0016 = "APLDLW0016";
    // STEP7.0版対応　追加　END

    // STEP11.0版対応　追加　START
    /**
     * <strong>追加オプション[4]なので、卸ポータルグループプランIDは省略できません。</strong>
     */
    public static readonly APLTIW0010 = "APLTIW0010";
    /**
     * <strong>グループプランID[{0}]がテナントID[{1}]に所属しない。</strong>
     */
    public static readonly APLTIW0011 = "APLTIW0011";
    /**
     * <strong>グループプランID[{0}]がグループの利用しているプランでない。</strong>
     */
    public static readonly APLTIW0012 = "APLTIW0012";
    /**
     * <strong>グループ参照サービスパターン取得エラー</strong>
     */
    public static readonly APLTIW0013 = "APLTIW0013";
    // STEP11.0版対応　追加　END

    // STEP18.0版対応　追加　START
    /**
     * <strong>回線運用情報(PCRF)取得エラー、エラー内容は[{0}]</strong>
     */
    public static readonly APLTIW0014 = "APLTIW0014";
    // STEP18.0版対応　追加　END

    // STEP12.0版対応　追加　START
    /**
     * <strong>処理を開始します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}</strong>
     */
    public static readonly APPADI0001 = "APPADI0001";

    /**
     * <strong>処理を終了します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}</strong>
     */
    public static readonly APPADI0002 = "APPADI0002";

    /**
     * <strong>電文パラメータフォーマットエラー。{0}:[{1}]</strong>
     */
    public static readonly APPADW0001 = "APPADW0001";

    /**
     * <strong>電文で指定された対象所属テナントIDがテナントに存在しません。テナントID:{0}</strong>
     */
    public static readonly APPADW0002 = "APPADW0002";

    /**
     * <strong>電文で指定されたテンプレートIDがテンプレート管理に存在しません。テンプレートID:{0}</strong>
     */
    public static readonly APPADW0003 = "APPADW0003";

    /**
     * <strong>テナント[{0}]が利用できないテンプレートID[{1}]です。</strong>
     */
    public static readonly APPADW0004 = "APPADW0004";

    /**
     * <strong>カスタマイズ可能項目に値が設定されておりません。項目名：[{0}]</strong>
     */
    public static readonly APPADW0005 = "APPADW0005";

    /**
     * <strong>カスタマイズ可能な上限数[{0}]を超えています。</strong>
     */
    public static readonly APPADW0006 = "APPADW0006";

    /**
     * <strong>テーブル[{0}]からID[{1}]が取得できません。</strong>
     */
    public static readonly APPADW0007 = "APPADW0007";

    /**
     * <strong>テーブル[{0}]のロック取得に失敗しました。</strong>
     */
    public static readonly APPADW0008 = "APPADW0008";

    /**
     * <strong>テナント{0}の接続先TPC情報が取得できません。</strong>
     */
    public static readonly APPADW0009 = "APPADW0009";

    /**
     * <strong>SO投入失敗、エラー内容は[{0}]です。</strong>
     */
    public static readonly APPADW0010 = "APPADW0010";

    /**
     * <strong>電文で指定されたカスタマイズIDが{0}に存在しません。カスタマイズID:{1}</strong>
     */
    public static readonly APPADW0011 = "APPADW0011";

    /**
     * <strong>電文で指定された対象所属テナントIDと削除対象プランのテナントIDが一致しないため、削除できません。</strong>
     */
    public static readonly APPADW0012 = "APPADW0012";

    /**
     * <strong>削除対象プラン[{0}]が回線情報に適用済みのため、削除できません。</strong>
     */
    public static readonly APPADW0013 = "APPADW0013";

    /**
     * <strong>削除対象プラン[{0}]が機能種別[{1}]にて予約中のため、削除できません。</strong>
     */
    public static readonly APPADW0014 = "APPADW0014";

    /**
     * <strong>テーブル[{0}]の更新に失敗しました。</strong>
     */
    public static readonly APPADE0001 = "APPADE0001";

    /**
     * <strong>テーブル[{0}]の登録に失敗しました。</strong>
     */
    public static readonly APPADE0002 = "APPADE0002";

    /**
     * <strong>テーブル[{0}]の削除に失敗しました。</strong>
     */
    public static readonly APPADE0003 = "APPADE0003";
    // STEP12.0版対応　追加　END

    // STEP13.0版対応　追加　START
    /**
     * <strong>処理を開始します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}</strong>
     */
    public static readonly APGPCI0001 = "APGPCI0001";

    /**
     * <strong>処理を終了します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}</strong>
     */
    public static readonly APGPCI0002 = "APGPCI0002";

    /**
     * <strong>リアルタイムオーダ、予約前オーダ、予約実行オーダのどのオーダなのか判定できない。</strong>
     */
    public static readonly APGPCW0001 = "APGPCW0001";

    /**
     * <strong>電文パラメータフォーマットエラー。{0}:[{1}]</strong>
     */
    public static readonly APGPCW0002 = "APGPCW0002";

    /**
     * <strong>SG「グループプラン変更不可時間帯」に許容されない時間帯が指定された。{0}</strong>
     */
    public static readonly APGPCW0003 = "APGPCW0003";

    /**
     * <strong>グループプラン変更オーダを、SG「グループプラン変更不可時間帯」に許容されない時間帯に処理しようとした。{0}</strong>
     */
    public static readonly APGPCW0004 = "APGPCW0004";

    /**
     * <strong>当該オーダの回線グループIDに対して既に予約が入っていた。回線グループID：{0}</strong>
     */
    public static readonly APGPCW0005 = "APGPCW0005";

    /**
     * <strong>予約前オーダの予約日において、過去日時が指定された。予約日：{0}＜システム時刻：{1}</strong>
     */
    public static readonly APGPCW0006 = "APGPCW0006";

    /**
     * <strong>予約前オーダの予約日において、SG「予約実行日時単位」に許容されないフォーマットが指定された。{0}</strong>
     */
    public static readonly APGPCW0007 = "APGPCW0007";

    /**
     * <strong>予約前オーダの予約日において、SG「予約実行日時単位」に許容されない日時が指定された。{0}：{1}</strong>
     */
    public static readonly APGPCW0008 = "APGPCW0008";

    /**
     * <strong>予約前オーダの予約日において、SG「予約可能制限日数」に許容されないフォーマットが指定された。{0}</strong>
     */
    public static readonly APGPCW0009 = "APGPCW0009";

    /**
     * <strong>予約前オーダの予約日において、SG「予約可能制限日数」の範囲外の日時が指定された。{0}：{1}</strong>
     */
    public static readonly APGPCW0010 = "APGPCW0010";

    /**
     * <strong>社内テナント種別が取得できない。</strong>
     */
    public static readonly APGPCW0011 = "APGPCW0011";

    /**
     * <strong>回線グループID[{0}]を取得できません。</strong>
     */
    public static readonly APGPCW0012 = "APGPCW0012";

    /**
     * <strong>回線グループ{0}は、テナント{1}に所属していません。</strong>
     */
    public static readonly APGPCW0013 = "APGPCW0013";

    /**
     * <strong>変更前卸ポータルグループプランID[{0}]がプランID[{1}]と一致しません。</strong>
     */
    public static readonly APGPCW0014 = "APGPCW0014";

    /**
     * <strong>変更後卸ポータルグループプランID[{0}]のサービスプランIDが取得できません。</strong>
     */
    public static readonly APGPCW0015 = "APGPCW0015";

    /**
     * <strong>変更後卸ポータルグループプランID[{0}]はテナントID[{1}]で利用できません。</strong>
     */
    public static readonly APGPCW0016 = "APGPCW0016";

    /**
     * <strong>テナント{0}の接続先TPC情報が取得できません。</strong>
     */
    public static readonly APGPCW0017 = "APGPCW0017";

    /**
     * <strong>SO投入失敗、エラー内容は[{0}]です。</strong>
     */
    public static readonly APGPCW0018 = "APGPCW0018";

    // STEP18.0版対応　追加　START
    /**
     * <strong>セカンダリTPCにSO投入失敗しました。エラー内容は[{0}]です。</strong>
     */
    public static readonly APGPCW0019 = "APGPCW0019";
    // STEP18.0版対応　追加　END

    /**
     * <strong>回線グループ管理の更新に失敗しました。</strong>
     */
    public static readonly APGPCE0001 = "APGPCE0001";
    // STEP13.0版対応　追加　END

    // STEP15.0版対応　追加　START
    /**
     * <strong>処理を開始します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}</strong>
     */
    public static readonly APIRSI0001 = "APIRSI0001";

    /**
     * <strong>処理を終了します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}</strong>
     */
    public static readonly APIRSI0002 = "APIRSI0002";

    /**
     * <strong>電文パラメータフォーマットエラー。{0}:[{1}]</strong>
     */
    public static readonly APIRSW0001 = "APIRSW0001";

    /**
     * <strong>回線番号[{0}]は既に廃止されています。</strong>
     */
    public static readonly APIRSW0002 = "APIRSW0002";

    /**
     * <strong>回線番号[{0}]はテナントID[{1}]に所属していません。</strong>
     */
    public static readonly APIRSW0003 = "APIRSW0003";

    /**
     * <strong>回線番号[{0}]はフルMVNOのプラン[{1}]ではありません。</strong>
     */
    public static readonly APIRSW0004 = "APIRSW0004";

    /**
     * <strong>回線情報の更新に失敗しました。回線番号：[{0}]</strong>
     */
    public static readonly APIRSE0001 = "APIRSE0001";

    /**
     * <strong>回線回線グループ情報の更新に失敗しました。回線番号：[{0}]、回線グループID：[{1}]</strong>
     */
    public static readonly APIRSE0002 = "APIRSE0002";
    // STEP15.0版対応　追加　END

    // STEP17.0版対応　追加　START
    /**
     * <strong>処理を開始します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}</strong>
     */
    public static readonly APNCCI0001 = "APNCCI0001";

    /**
     * <strong>処理を終了します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}</strong>
     */
    public static readonly APNCCI0002 = "APNCCI0002";

    /**
     * <strong>卸ポータルフロントからのリクエストではありません。テナントID：{0}</strong>
     */
    public static readonly APNCCW0001 = "APNCCW0001";

    /**
     * <strong>回線番号[{0}]は既に廃止されています。</strong>
     */
    public static readonly APNCCW0002 = "APNCCW0002";

    /**
     * <strong>回線番号[{0}]はテナントID[{1}]に所属していません。</strong>
     */
    public static readonly APNCCW0003 = "APNCCW0003";

    /**
     * <strong>回線番号[{0}]はすでに廃止SOを受付中です。</strong>
     */
    public static readonly APNCCW0004 = "APNCCW0004";

    /**
     * <strong>回線情報のロック取得に失敗しました。回線番号:[{0}]</strong>
     */
    public static readonly APNCCW0005 = "APNCCW0005";

    /**
     * <strong>カード種別名の取得に失敗しました。カード種別ID：[{0}]</strong>
     */
    public static readonly APNCCW0006 = "APNCCW0006";

    /**
     * <strong>回線情報の更新に失敗しました。回線番号：[{0}]</strong>
     */
    public static readonly APNCCE0001 = "APNCCE0001";

    /**
     * <strong>回線とテナントの所属判定時に、N番を取得できませんでした。回線ID：{0} テナントID：{1}</strong>
     */
    public static readonly APNCCE0002 = "APNCCE0002";
    // STEP17.0版対応　追加　END

    // STEP21.0版対応　追加　START
    /**
     * <strong>処理を開始します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}</strong>
     */
    public static readonly APPPCI0001 = "APPPCI0001";

    /**
     * <strong>処理を終了します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}</strong>
     */
    public static readonly APPPCI0002 = "APPPCI0002";

    /**
     * <strong>卸ポータルフロントからのリクエストではありません。テナントID：{0}</strong>
     */
    public static readonly APPPCW0001 = "APPPCW0001";

    /**
     * <strong>回線番号[{0}]は既に廃止されています。</strong>
     */
    public static readonly APPPCW0002 = "APPPCW0002";

    /**
     * <strong>回線番号[{0}]はテナントID[{1}]に所属していません。</strong>
     */
    public static readonly APPPCW0003 = "APPPCW0003";

    /**
     * <strong>0035でんわプラン名の取得に失敗しました。テナントID：{0} 0035でんわプランID：{1}</strong>
     */
    public static readonly APPPCW0004 = "APPPCW0004";

    /**
     * <strong>回線情報のロック取得に失敗しました。回線番号:[{0}]</strong>
     */
    public static readonly APPPCW0005 = "APPPCW0005";

    /**
     * <strong>回線情報の更新に失敗しました。回線番号：[{0}]</strong>
     */
    public static readonly APPPCE0001 = "APPPCE0001";

    /**
     * メッセージID：CRAGME0001
     */
    public static readonly MSGID_CRAGME0001 = "CRAGME0001";

    /**
     * メッセージID：CRAGME0002
     */
    public static readonly MSGID_CRAGME0002 = "CRAGME0002";

    /**
     * メッセージID：CRAGME0003
     */
    public static readonly MSGID_CRAGME0003 = "CRAGME0003";

    /**
     * メッセージID：CRAGME0004
     */
    public static readonly MSGID_CRAGME0004 = "CRAGME0004";

    /**
     * メッセージID：CRAGME0005
     */
    public static readonly MSGID_CRAGME0005 = "CRAGME0005";

    /**
     * メッセージID：CRAGME0006
     */
    public static readonly MSGID_CRAGME0006 = "CRAGME0006";

    /**
     * メッセージID：CRAGME0007
     */
    public static readonly MSGID_CRAGME0007 = "CRAGME0007";

    /**
     * メッセージID：CRAGME0008
     */
    public static readonly MSGID_CRAGME0008 = "CRAGME0008";

    /**
     * メッセージID：CRAGME0009
     */
    public static readonly MSGID_CRAGME0009 = "CRAGME0009";

    /**
     * メッセージID：CRAGME0010
     */
    public static readonly MSGID_CRAGME0010 = "CRAGME0010";

    /**
     * メッセージID：CRAGME0011
     */
    public static readonly MSGID_CRAGME0011 = "CRAGME0011";

    /**
     * メッセージID：CRAGME0012
     */
    public static readonly MSGID_CRAGME0012 = "CRAGME0012";

    // STEP20.0版対応　追加　START
    /**
     * メッセージID：CRAGME0013
     */
    public static readonly MSGID_CRAGME0013 = "CRAGME0013";
    // STEP20.0版対応　追加　START

    /**
     * メッセージID：CRAGMI0001
     */
    public static readonly MSGID_CRAGMI0001 = "CRAGMI0001";

    /**
     * メッセージID：CRAGMI0002
     */
    public static readonly MSGID_CRAGMI0002 = "CRAGMI0002";

    /**
     * メッセージID：CRAGMI0003
     */
    public static readonly MSGID_CRAGMI0003 = "CRAGMI0003";

    /**
     * メッセージID：CRAGMW0001
     */
    public static readonly MSGID_CRAGMW0001 = "CRAGMW0001";

    /**
     * メッセージID：CRAGMW0002
     */
    public static readonly MSGID_CRAGMW0002 = "CRAGMW0002";

    /**
     * メッセージID：CRAGMI0004
     */
    public static readonly MSGID_CRAGMI0004 = "CRAGMI0004";
    // STEP21.0版対応　追加　END

    // #【APSOR】予約実行機能
    /**
     * <strong>二重起動を検知したので終了しました。実行プロセスID:{0}</strong>
     */
    public static readonly APSORW0001 = "APSORW0001"
    /**
     * <strong>SG「TPC接続不可時間帯」 -> {0}</strong
     */
    public static readonly APSORI0002 = "APSORI0002"
    /**
     * <strong>読み込んだSG「TPC接続不可時間帯」の値が妥当でないため、予約実行機能を終了します。 -> {0}</strong
     */
    public static readonly APSORE0003 = "APSORE0003"
    /**
     * <strong>SG「API処理IDサーバ識別」 -> {0}</strong
     */
    public static readonly APSORI0004 = "APSORI0004"
    /**
     * <strong>読み込んだSG「API処理IDサーバ識別」の値が妥当でないため、予約実行機能を終了します。 -> {0}</strong
     */
    public static readonly APSORE0005 = "APSORE0005"
    /**
     * <strong>SG「TPC最大同時接続数」 -> {0}</strong
     */
    public static readonly APSORI0006 = "APSORI0006"
    /**
     * <strong>読み込んだSG「TPC最大同時接続数」の値が妥当でないため、予約実行機能を終了します。 -> {0}</strong
     */
    public static readonly APSORE0007 = "APSORE0007"
    /**
     * <strong>予約実行機能を開始しました。</strong>
     */
    public static readonly APSORI0008 = "APSORI0008"
    /**
     * <strong>実行キュー積み込み機能: 終了通知を送信しました。</strong>
     */
    public static readonly APSORI0201 = "APSORI0201"
    /**
     * <strong>実行済みオーダを削除しました。未実行:{0}件 実行中:{1}件 実行済:{2}件</strong>
     */
    public static readonly APSORI0301 = "APSORI0301"
    /**
     * <strong>TPC接続不可時間帯に入ったので予約オーダの積み込みを抑止します。接続不可時間帯:{0}</strong>
     */
    public static readonly APSORI0401 = "APSORI0401"
    /**
     * <strong>TPC接続可時間帯に入ったので予約オーダの積み込みを開始します。接続不可時間帯:{0}</strong>
     */
    public static readonly APSORI0402 = "APSORI0402"
    /**
     * <strong>SO管理からデータを取得しました。取得件数:{0}件</strong>
     */
    public static readonly APSORI0501 = "APSORI0501"
    /**
     * <strong>予約中オーダを未実行として積込みました。積込み件数:{0}件</strong>
     */
    public static readonly APSORI0601 = "APSORI0601"
    /**
     * <strong>実行キュー積み込み機能:開始通知を送信しました。</strong>
     */
    public static readonly APSORI0701 = "APSORI0701"
    /**
     * <strong>実行キュー積み込み機能:実行キュー送信機能が異常です。</strong>
     */
    public static readonly APSORE0702 = "APSORE0702"
    /**
     * <strong>予約実行機能を終了しました。</strong>
     */
    public static readonly APSORI1002 = "APSORI1002"
    /**
     * <strong>SO管理からのデータ取得が失敗しました。</strong>
     */
    public static readonly APSORE5001 = "APSORE5001"
    /**
     * <strong>実行キュー送信機能の処理を開始します。</strong>
     */
    public static readonly APSORI5101 = "APSORI5101"
    /**
     * <strong>予約中オーダを実行します。並列実行識別子:{0} SO-ID:{1}</strong>
     */
    public static readonly APSORI5401 = "APSORI5401"
    /**
     * <strong>予約中オーダが完了しました。並列実行識別子:{0} SO-ID:{1} 処理コード:{2}</strong>
     */
    public static readonly APSORI5501 = "APSORI5501"
    /**
     * <strong>予約中オーダが完了しました。並列実行識別子:{0} SO-ID:{1} 処理コード:{2}</strong>
     */
    public static readonly APSORE5501 = "APSORE5501"
    /**
     * <strong>{0}秒を経過したオーダを強制切断しました。並列実行識別子:{1} SO-ID:{2}</strong>
     */
    public static readonly APSORE5502 = "APSORE5502"
    /**
     * <strong>実行キュー送信機能: 終了通知を受信しました。</strong>
     */
    public static readonly APSORI5701 = "APSORI5701"
    /**
     * <strong>プロパティファイル中の項目にエラーがありました。プロパティファイル:{0}</strong>
     */
    public static readonly APSORE0002 = "APSORE0002"
    /**
     * <strong>プロパティファイルに必須項目が指定されていません。項目: {0}</strong>
     */
    public static readonly APSORE0098 = "APSORE0098"
    /**
     * <strong>プロパティファイルの読み込みに失敗しました。プロパティファイル:{0}</strong>
     */
    public static readonly APSORE0099 = "APSORE0099"
    /**
     * <strong>実行キュー送信機能の終了を待ちます。</strong>
     */
    public static readonly APSORI1001 = "APSORI1001"
    /**
     * <strong>SG「実行キュー積み込み処理間隔」 -> {0}</strong
     */
    public static readonly APSORI0101 = "APSORI0101"
    /**
     * <strong>読み込んだSG「実行キュー積み込み処理間隔」の値が妥当でないため、予約実行機能を終了します。 -> {0}</strong
     */
    public static readonly APSORE0101 = "APSORE0101"
    /**
     * <strong>SG「実行キュー送信処理間隔」 -> {0}</strong
     */
    public static readonly APSORI0102 = "APSORI0102"
    /**
     * <strong>読み込んだSG「実行キュー送信処理間隔」の値が妥当でないため、予約実行機能を終了します。 -> {0}</strong
     */
    public static readonly APSORE0102 = "APSORE0102"
    /**
     * <strong>実行キュー積み込み処理: 次の実行まで待機します。</strong>
     */
    public static readonly APSORI0901 = "APSORI0901"
    /**
     * <strong>実行中のスレッドでエラーが発生した可能性があります。[{0}]</strong>
     */
    public static readonly APSORE9998 = "APSORE9998"
    /**
     * <strong>予期せぬエラー [{0}]</strong>
     */
    public static readonly APSORE9999 = "APSORE9999"
}
