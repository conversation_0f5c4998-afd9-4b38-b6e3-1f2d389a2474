/** file: 1.war/src/message.properties + 1.war/src/messageonline.properties */
const MessageProperties = {
    // #【APLBI】回線基本情報参照機能
    APLBIW0001: "電文パラメータフォーマットエラー。{0}:[{1}]",
    APLBIW0002: "回線番号[{0}]がテナント[{1}]に所属しない",
    APLBIW0003: "テナントID[{0}]は社内ユーザではない",
    APLBIW0004: "回線情報取得エラー",
    APLBII0001:
        "処理を開始します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}",
    APLBII0002:
        "処理を終了します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}",
    APLBID0001: "処理開始 [{0}:{1}] {2}",
    APLBID0002: "処理終了 [{0}:{1}]",

    // #【APLTI】回線運用状況参照機能
    APLTIW0001: "電文パラメータフォーマットエラー。{0}:[{1}]",
    APLTIW0002: "社内テナント種別を取得できない",
    APLTIW0003: "社内テナント種別はC-OCNなので、プランIDは省略できません。",
    APLTIW0004: "回線番号[{0}]がテナント[{1}]に所属しない",
    APLTIW0005: "テナント[{0}]が利用できないプランID[{1}]",
    APLTIW0006: "参照サービスパターン取得エラー",
    APLTIW0007: "回線運用情報取得エラー、エラー内容は[{0}]",
    APLTIW0008: "テナント{0}の接続先TPC情報が取得できません。",
    APLTII0001:
        "処理を開始します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}",
    APLTII0002:
        "処理を終了します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}",
    APLTID0001: "処理開始 [{0}:{1}] {2}",
    APLTID0002: "処理終了 [{0}:{1}]",
    APLTIW0009: "社内テナント種別がC-OCN以外なので、プランIDは指定できません。",
    APLTIW0010:
        "追加オプション[4]なので、卸ポータルグループプランIDは省略できません。",
    APLTIW0011: "グループプランID[{0}]がテナントID[{1}]に所属しない",
    APLTIW0012: "グループプランID[{0}]がグループの利用しているプランでない。",
    APLTIW0013: "グループ参照サービスパターン取得エラー",

    // #【APGBI】回線グループ基本情報参照機能
    APGBIW0001: "電文パラメータフォーマットエラー。{0}:[{1}]",
    APGBIW0002: "回線番号リスト取得エラー",
    APGBIW0003:
        "回線グループID[{0}]は、テナント[{1}]に所属していない、もしくは割り当て済みではない回線グループです。",
    APGBIW0004: "回線番号リスト取得エラー",
    APGBIW0005: "社内テナント種別が取得できない。",
    APGBII0001:
        "処理を開始します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}",
    APGBII0002:
        "処理を終了します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}",
    APGBID0001: "処理開始 [{0}:{1}] {2}",
    APGBID0002: "処理終了 [{0}:{1}]",

    // #【APGTI】回線グループ運用状況参照機能
    APGTIW0001: "電文パラメータフォーマットエラー。{0}:[{1}]",
    APGTIW0002: "社内テナント種別はC-OCNなので、回線番号は省略できません。",
    APGTIW0003: "プランID[{0}]がテナントID[{1}]に所属しない",
    APGTIW0004: "参照サービスパターン取得エラー",
    APGTIW0005: "回線グループ運用情報取得エラー、エラー内容は[{0}]",
    APGTIW0006: "回線情報取得エラー",
    APGTII0001:
        "処理を開始します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}",
    APGTII0002:
        "処理を終了します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}",
    APGTID0001: "処理開始 [{0}:{1}] {2}",
    APGTID0002: "処理終了 [{0}:{1}]",
    APGTIW0007:
        "電文で指定された回線グループ{0}は、テナント{1}に所属していません。",
    APGTIW0008: "テナント{0}の接続先TPC情報が取得できません。",
    APGTIW0009: "グループプランID[{0}]がグループの利用しているプランでない。",

    // #【APLCF】回線追加チャージ(クーポン)容量追加・期間追加機能
    APLCFW0001: "電文パラメータフォーマットエラー。{0}:[{1}]",
    APLCFW0002: "回線番号[{0}]がテナント[{1}]に所属しない",
    APLCFW0003: "プランID[{0}]がテナントID[{1}]に所属しない",
    APLCFW0004:
        "オプションプランID[{0}]が回線の利用しているプランで利用できない",
    APLCFW0005:
        "更新サービスパターンポリシーIDとポリシーIDをプランから取得できない",
    APLCFW0006: "フォーマットエラー[{0}][{1}]",
    APLCFW0007: "SO投入失敗、エラー内容は[{0}]",
    APLCFW0008: "オプションプランパラメータ取得エラー[{0}]",
    APLCFW0010: "SO投入失敗[{0}]",
    APLCFW0020: "テナント{0}の接続先TPC情報が取得できません。",
    APLCFW0021: "回線番号[{0}]の利用状態がサスペンド中のため利用できません。",
    APLCFI0001:
        "処理を開始します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}",
    APLCFI0002:
        "処理を終了します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}",
    APLCFD0001: "処理開始 [{0}:{1}] {2}",
    APLCFD0002: "処理終了 [{0}:{1}]",
    APLCFE0602:
        "電文で指定されたテナントIDがテナント所属判定で無効と判断されました。テナントID:{0}",
    APLCFE0605: "電文の卸ポータルオプションプランIDがオプションプランに所属していませんでした。オプションプランID:{0}",
    APLCFW0017: "社内テナント種別が取得できない。",
    APLCFW0018: "プランID[{0}]が回線の利用しているプランでない",
    APLCFW0019: "回線番号[{0}]はすでに廃止SOを受付中です。",
    APLCFW0022: "セカンダリTPCへのSO投入失敗。エラー内容[{0}]",

    // #【APLCP】回線クーポンオン・オフ機能
    APLCPW0001: "電文パラメータフォーマットエラー。{0}:[{1}]",
    APLCPW0002: "回線番号[{0}]がテナント[{1}]に所属しない",
    APLCPW0003: "プランID[{0}]がテナントID[{1}]に所属しない",
    APLCPW0004: "回線グループIDを持っているため、対象外",
    APLCPW0005: "更新サービスパターンとポリシーIDをプランから取得できない",
    APLCPW0006: "フォーマットエラー。[{0}]:[{1}]",
    APLCPW0007: "SO投入失敗",
    APLCPW0008:
        "オプションプランID[{0}]が回線の利用しているプランで利用できない",
    APLCPW0010: "テナント{0}の接続先TPC情報が取得できません。",
    APLCPW0020: "回線番号[{0}]の利用状態がサスペンド中のため利用できません。",
    APLCPW0021: "セカンダリTPCへのSO投入失敗。エラー内容[{0}]",
    APLCPI0001:
        "処理を開始します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}",
    APLCPI0002:
        "処理を終了します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}",
    APLCPD0001: "処理開始 [{0}:{1}] {2}",
    APLCPD0002: "処理終了 [{0}:{1}]",

    // #【APLAF】回線アクティベート・ディアクティベート機能
    APLAFW0001: "電文パラメータフォーマットエラー。{0}:[{1}]",
    APLAFW0002: "回線番号[{0}]がテナント[{1}]に所属しない",
    APLAFW0003: "回線グループIDを持っているため、対象外",
    APLAFW0004: "SO投入失敗、エラー内容は[{0}]",
    APLAFW0005: "現在の状況はディアクティベートのため、変更できない",
    APLAFW0006: "現在の状況はアクティベートのため、変更できない",
    APLAFW0007: "SO投入失敗",
    APLAFW0010: "テナント{0}の接続先TPC情報が取得できません。",
    APLAFW0018: "回線番号[{0}]の利用状態がサスペンド中のため利用できません。",
    APLAFW0019: "セカンダリTPCへのSO投入失敗。エラー内容[{0}]",
    APLAFI0001:
        "処理を開始します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}",
    APLAFI0002:
        "処理を終了します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}",
    APLAFD0001: "処理開始 [{0}:{1}] {2}",
    APLAFD0002: "処理終了 [{0}:{1}]",

    // #【APLLC】回線リスト確認
    APLLCW0001: "電文パラメータフォーマットエラー。{0}:[{1}]",
    APLLCW0002: "回線番号リストは取得できない",
    APLLCI0001:
        "処理を開始します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}",
    APLLCI0002:
        "処理を終了します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}",
    APLLCD0001: "処理開始 [{0}:{1}] {2}",
    APLLCD0002: "処理終了 [{0}:{1}]",

    // #【APGCF】回線グループ追加チャージ(クーポン)容量追加・期間追加機能
    APGCFW0001: "電文パラメータフォーマットエラー。{0}:[{1}]",
    APGCFW0002: "グループプランID[{0}]がテナント[{1}]に所属しない",
    APGCFW0003:
        "オプションプランID[{0}]がグループの利用しているプランで利用できない",
    APGCFW0004: "更新サービスパターンとポリシーIDをプランから取得できない",
    APGCFW0005: "SO投入失敗、エラー内容は[{0}]",
    APGCFW0006: "オプションプランのパラメータを取得できない",
    APGCFW0007: "フォーマットエラー[{0}][{1}]",
    APGCFW0008: "SO投入失敗",
    APGCFW0022: "回線番号[{0}]の利用状態がサスペンド中のため利用できません。",
    APGCFW0023: "セカンダリTPCへのSO投入失敗。エラー内容[{0}]",
    APGCFI0001:
        "処理を開始します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}",
    APGCFI0002:
        "処理を終了します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}",
    APGCFD0001: "処理開始 [{0}:{1}] {2}",
    APGCFD0002: "処理終了 [{0}:{1}]",
    APGCFW0111: "テナント{0}の社内テナント種別が取得できません。",
    APGCFW0112:
        "電文で指定された回線グループ{0}は、テナント{1}に所属していません。",
    APGCFW0010: "テナント{0}の接続先TPC情報が取得できません。",

    // #【APCGP】回線グループクーポンオン・オフ機能
    APCGPW0001: "電文パラメータフォーマットエラー。{0}:[{1}]",
    APCGPW0002: "更新サービスパターンをプランから取得できない",
    APCGPW0003: "更新サービスパターン取得エラー",
    APCGPW0004: "SO投入失敗",
    APCGPW0005: "フォーマットエラー[{0}][{1}]",
    APCGPW0006:
        "オプションプランID[{0}]がグループの利用しているプランで利用できない",
    APCGPI0001:
        "処理を開始します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}",
    APCGPI0002:
        "処理を終了します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}",
    APCGPD0001: "処理開始 [{0}:{1}] {2}",
    APCGPD0002: "処理終了 [{0}:{1}]",
    APCGPW0018:
        "電文で指定された回線グループ{0}は、テナント{1}に所属していません。",
    APCGPW0007: "テナント{0}の接続先TPC情報が取得できません。",
    APCGPW0017: "テナント{0}の社内テナント種別が取得できません。",
    APCGPW0020: "グループプランID[{0}]がグループの利用しているプランでない。",
    APCGPW0021:
        "電文で指定された回線グループIDと回線番号の組み合わせが回線回線グループに存在しない。もしくは回線番号が廃止されている。回線グループID：{0}、回線番号：{1}",
    APCGPW0022: "回線番号[{0}]の利用状態がサスペンド中のため利用できません。",
    APCGPW0019: "回線番号[{0}]はすでに廃止SOを受付中です。",
    APCGPW0023: "セカンダリTPCへのSO投入失敗。エラー内容[{0}]",

    // #REST受信 形式チェックエラー[{0}]
    REST受信: "形式チェックエラー tenantId{0}[{1}]",

    // #【APCOM】共通機能
    APCOMW0101:
        "リクエストデータのフォーマットチェックエラーです。[tag={0}, value={1}]",
    APCOMW0102: "機能種別とURLが一致しません。[機能種別={0}, URL={1}]",
    APCOMW0201: "最大同時接続数を超過しました。[現在の接続数={0}]",
    APCOMW0301:
        "該当のテナントIDがテナントテーブルに存在しません。[テナントID={0}]",
    APCOMW0302:
        "API認証キーが一致しません。[電文のAPI認証キー={0}, システムで求めたAPI認証キー={1}]",
    APCOMD0001: "処理開始 [{0}:{1}] {2}",
    APCOMD0002: "処理終了 [{0}:{1}]",
    APCOME0401: "予期せぬエラー [{0}]",
    APCOME0402:
        "DBアクセス時にリトライ回数分エラーとなりました。機能種別：[{0}]",
    APCOME0211: "テナントの同時接続数判定に失敗しました。[テナントID={0}]",
    APCOMW0213:
        "テナントの最大同時接続数を超過しました。[テナントID={0}][現在の接続数={1}]",

    // #【MPSPM】システムプロパティ取得機能
    MPSPME0001: "システムプロパティファイルが読み込めません。ファイル名:{0}{1}",
    MPSPME0002:
        "次のプロパティ項目に値が設定されていません。プロパティ項目名:{0}",

    // #【MPSCO】SOAP共通機能
    MPSCOE0004: "HTTPレスポンス異常 [{0}] 実行TPC：{1}",
    MPSCOI0001: "{0}",
    MPSCOI0002: "{0}",
    MPSCOI0003: "{0}",
    MPSCOE0001: "リクエストデータが不正です。実行TPC：{0}",
    MPSCOE0002: "TPCとの接続が確立できません。URI: {0} 実行TPC：{1}",
    MPSCOE0003:
        "TPCとのタイムアウトが発生しました。タイムアウト値: {0}秒 実行TPC：{1}",
    MPSCOD0001: "処理開始 [{0}:{1}] {2}",
    MPSCOD0002: "処理終了 [{0}:{1}]",
    MPSCOE0005: "TPCサーバIPアドレス：SG「{0}」が取得できません。実行TPC：{1}",

    // #【MPRCO】REST共通機能
    MPRCOD0001: "処理開始 [{0}:{1}] {2}",
    MPRCOD0002: "処理終了 [{0}:{1}]",
    MPRCOE0201: "ファイル出力エラー[出力先ディレクトリ名:{0} ファイル名:{1}]",
    MPRCOI0202:
        "CSVファイルを出力しました。[出力先ディレクトリ名:{0} ファイル名:{1}]",
    MPRCOE0301:
        "自動計算回線グループへの回線グループ情報追加に失敗。サービスオーダID：{0}",
    MPRCOE0401: "回線回線グループの削除に失敗。サービスオーダID：{0}",
    APCOME0311:
        "テナントで許容するIPアドレス情報のフォーマットが不正です。[テナントID={0}][IPアドレス={1}][サブネットマスク={2}]",
    APCOME0312: "SG「接続許可IPアドレス」フォーマットエラー：「{0}」",
    APCOMW0313:
        "許可されていない接続元からのアクセスです。[テナントID={0}][接続元IPアドレス={1}]",

    // #【APGRG】回線グループ新規作成
    APGRGW0000:
        "プロパティファイルには回線グループ再利用不可期間（日数）は数字ではない。",
    APGRGW0001: "電文パラメータフォーマットエラー。{0}:[{1}]",
    APGRGW0002: "社内テナント種別が取得できない。",
    APGRGW0003:
        "社内テナント種別がC-OCNの場合は、廃止リクエストは受け付けない。",
    APGRGW0004: "指定された回線グループID{0}は既に存在している。",
    APGRGW0005: "回線グループID：{0}の妥当性エラー。",
    APGRGW0101: "回線グループプランIDが取得できない。",
    APGRGW0102: "サービスプランIDが取得できない。",
    APGRGW0103: "回線グループIDを取得できない。",
    APGRGW0104: "回線グループ管理テーブルにロックかける失敗。",
    APGRGW0105: "TPCにSO投入失敗。",
    APGRGW0106: "セカンダリTPCへのSO投入失敗。エラー内容[{0}]",
    APGRGE0107: "回線グループ管理テーブル更新失敗。",
    APGRGW0201: "回線グループIDが取得できない。",
    APGRGW0202: "回線グループ管理テーブルにロックかける失敗。",
    APGRGW0203: "テナントIDが一致してないため、処理中断。",
    APGRGW0204: "ステータス確認失敗。",
    APGRGW0205: "当該回線グループに所属する回線があるため、廃止できない。",
    APGRGW0206: "TPCにSO投入失敗。",
    APGRGW0207: "セカンダリTPCへのSO投入失敗。エラー内容[{0}]",
    APGRGE0207: "回線グループ管理テーブル更新失敗。",
    APGRGW0108: "テナント{0}の接続先TPC情報が取得できません。",
    APGRGW0208: "テナント{0}の接続先TPC情報が取得できません。",
    APGRGD0001: "処理開始 [{0}:{1}] {2}",
    APGRGD0002: "処理終了 [{0}:{1}]",
    APGRGI0001:
        "処理を開始します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}",
    APGRGI0002:
        "処理を終了します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}",

    // #【APGMB】回線グループ基本容量変更
    APGMBW0000: "プロパティファイルには設定値がみつかりませんでした。{0}",
    APGMBW0001: "電文パラメータフォーマットエラー。{0}:[{1}]",
    APGMBW0002: "社内テナント種別が取得できない。",
    APGMBW0003: "社内テナント種別はC-OCNため、処理中断。",
    APGMBW0005: "回線グループID取得エラー",
    APGMBW0006: "容量計算エラー",
    APGMBW0007: "TPCにSO投入失敗。",
    APGMBW0008: "適用帯域(QoS)条件/ポリシープロファイル番号取得エラー",
    APGMBW0009: "TPCにSO投入失敗。",
    APGMBD0001: "処理開始 [{0}:{1}] {2}",
    APGMBD0002: "処理終了 [{0}:{1}]",
    APGMBI0001:
        "処理を開始します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}",
    APGMBI0002:
        "処理を終了します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}",

    // #【APGMD】回線グループ所属回線変更
    APGMDW0001: "電文パラメータフォーマットエラー。{0}:[{1}]",
    APGMDW0002: "社内テナント種別、社内フラグが取得できない。",
    APGMDW0003: "社内テナント種別はC-OCNため、処理中断。",
    APGMDW0004:
        "電文で指定された回線グループ{0}は、テナント{1}に所属していません。",
    APGMDW0005: "回線グループ管理テーブルにロックかける失敗。",
    APGMDW0100: "回線ID確認エラー [{0}]",
    APGMDW0101: "更新サービスパターンチェックエラー",
    APGMDW0103: "TPCにSO投入失敗。",
    APGMDE0014: "回線回線グループテーブル挿入失敗。",
    APGMDW0201: "回線IDを取得できない。",
    APGMDW0202: "TPCにSO投入失敗。",
    APGMDW0203: "回線回線グループテーブル削除失敗。",
    APGMDD0001: "処理開始 [{0}:{1}] {2}",
    APGMDD0002: "処理終了 [{0}:{1}]",
    APGMDI0001:
        "処理を開始します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}",
    APGMDI0002:
        "処理を終了します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}",
    APGMDW0104: "テナント{0}の接続先TPC情報が取得できません。",
    APGMDW0204: "テナント{0}の接続先TPC情報が取得できません。",
    APGMDW0205: "セカンダリTPCへのSO投入失敗。エラー内容[{0}]",
    APGMDW0105: "回線番号[{0}]はすでに廃止SOを受付中です。",
    APGMDW0106: "セカンダリTPCへのSO投入失敗。エラー内容[{0}]",

    // #【MTDSO】SO管理レコード削除機能
    MTDSOW0001:
        "[DeleteSORecord.sh] MTDSOW0001:SO管理のレコード削除が既に実行中です。",
    MTDSOE0001: "システムプロパティファイルを取得できない。",
    MTDSOE0002: "DBと接続できません。",
    MTDSOE0003: "DB操作が失敗しました。",
    MTDSOI0001: "処理を開始します。 処理名:{0}",
    MTDSOI0002:
        "SO管理テーブルに、完了日は{0}までのレコードを削除しました、削除件数は{1}件です。",
    MTDSOI0003: "処理を終了します。 処理名:{0}",

    // #【MTDPW】パスワードレコード削除機能
    MTDPWW0001:
        "[DeletePWRecord.sh] MTDPWW0001:パスワードのレコード削除が既に実行中です。",
    MTDPWI0001: "処理を開始します。 処理名:{0}",
    MTDPWI0002:
        "パスワードテーブルに過去3世代を超過したレコードを削除しました、削除件数は{0}件です。",
    MTDPWI0003: "処理を終了します。 処理名:{0}",
    MTDPWE0001: "システムプロパティファイルを取得できない。",
    MTDPWE0002: "DBと接続できません。",
    MTDPWE0003: "DB操作が失敗しました。",

    // #【APLPC】回線プラン変更
    APLPCW0013:
        "SG「プラン変更不可時間帯」に許容されない時間帯が指定された。{0}",
    APLPCW0014:
        "プラン変更オーダを、SG「プラン変更不可時間帯」に許容されない時間帯に処理しようとした。{0}",
    APLPCW0015: "当該オーダの回線IDに対して既に予約が入っていた。回線ID：{0}",
    APLPCW0031:
        "「プラン変更タイミング」判定において、「月初」指定なのに、「月初」以外の日に実行しようとした。{0}",
    APLPCW0019:
        "予約前オーダの予約日において、SG「予約実行日時単位」に許容されない日時が指定された。{0}：{1}",
    APLPCW0017:
        "予約前オーダの予約日において、過去日時が指定された。予約日：{0}＜システム時刻：{1}",
    APLPCW0018:
        "予約前オーダの予約日において、SG「予約実行日時単位」に許容されないフォーマットが指定された。{0}",
    APLPCW0020:
        "予約前オーダの予約日において、SG「予約可能制限日数」に許容されないフォーマットが指定された。{0}",
    APLPCW0021:
        "予約前オーダの予約日において、SG「予約可能制限日数」の範囲外の日時が指定された。{0}：{1}",
    APLPCW0022: "セカンダリTPCへのSO投入失敗。エラー内容[{0}]",
    APLPCW0032:
        "予約前オーダの「プラン変更タイミング」判定において、「月初」指定なのに、「月初」以外の予約日が指定された。{0}",
    APLPCW0012:
        "リアルタイムオーダ、予約前オーダ、予約実行オーダのどのオーダなのか判定できない。",
    APLPCW0016:
        "プラン変更の予約前オーダの予約日において、SG「プラン変更不可時間帯」に許容されない時間帯が指定された。SG「プラン変更不可時間帯」：{0}、予約日：{1}",
    APLPCW1303:
        "変更後プランがグループに所属可能なプランではありません。回線番号：{0}、プラン：{1}",
    APLPCW0030: "テナント{0}の接続先TPC情報が取得できません。",
    APLPCW0034: "回線番号[{0}]はすでに廃止SOを受付中です。",
    APLPCW0035: "回線番号[{0}]のSIMステータスが半黒です。",
    APLPCW0036: "回線回線グループの更新に失敗しました。",
    APLPCW0037:
        "回線番号[{0}]は5G回線ではないため5G専用プランに変更できません。",

    // #【APGCF】回線グループ追加チャージ(クーポン)容量追加・期間追加機能
    APGCFW0012:
        "予約前オーダの予約日において、過去日時が指定された。予約日：{0}＜システム時刻：{1}",
    APGCFW0013: "SG「予約実行日時単位」フォーマットエラー：「{0}」",
    APGCFW0014:
        "予約前オーダの予約日において、SG「予約実行日時単位」に許容されない日時が指定された。{0}：{1}",
    APGCFW0015: "SG「予約可能制限日数」フォーマットエラー：「{0}」",
    APGCFW0016:
        "予約前オーダの予約日において、SG「予約可能制限日数」の範囲外の日時が指定された。{0}：{1}",
    APGCFW0011:
        "リアルタイムオーダ、予約前オーダ、予約実行オーダのどのオーダなのか判定できない。",
    APGCFW0017:
        "社内テナント種別が事業者卸なので、課金対象回線番号は省略できません。テナントID：{0}",
    APGCFW0018:
        "電文で指定されたテナントIDと課金対象回線番号の組み合わせが回線テナントに存在しない。テナントID：{0}、課金対象回線番号：{1}",
    APGCFW0019:
        "電文で指定された回線グループIDと課金対象回線番号の組み合わせが回線回線グループに存在しない。もしくは課金対象回線番号が廃止されている。回線グループID：{0}、課金対象回線番号：{1}",
    APGCFW0020: "グループプランID[{0}]がグループの利用しているプランでない。",
    APGCFE0602:
        "電文で指定されたテナントIDがテナント所属判定で無効と判断されました。テナントID:{0}",
    APGCFE0605:
        "容量追加コードがnullまたはフォーマットエラーです。オプションプランID:{0}",
    APGCFW0021: "回線番号[{0}]はすでに廃止SOを受付中です。",

    // #【APCGP】回線グループクーポンオン・オフ機能
    APCGPW0012:
        "予約前オーダの予約日において、過去日時が指定された。予約日：{0}＜システム時刻：{1}",
    APCGPW0013: "SG「予約実行日時単位」フォーマットエラー：「{0}」",
    APCGPW0014:
        "予約前オーダの予約日において、SG「予約実行日時単位」に許容されない日時が指定された。{0}：{1}",
    APCGPW0015: "SG「予約可能制限日数」フォーマットエラー：「{0}」",
    APCGPW0016:
        "予約前オーダの予約日において、SG「予約可能制限日数」の範囲外の日時が指定された。{0}：{1}",
    APCGPW0011:
        "リアルタイムオーダ、予約前オーダ、予約実行オーダのどのオーダなのか判定できない。",

    // #【APGMD】回線グループ所属回線変更
    APGMDW0012:
        "予約前オーダの予約日において、過去日時が指定された。予約日：{0}＜システム時刻：{1}",
    APGMDW0013: "SG「予約実行日時単位」フォーマットエラー：「{0}」",
    APGMDW0014:
        "予約前オーダの予約日において、SG「予約実行日時単位」に許容されない日時が指定された。{0}：{1}",
    APGMDW0015: "SG「予約可能制限日数」フォーマットエラー：「{0}」",
    APGMDW0016:
        "予約前オーダの予約日において、SG「予約可能制限日数」の範囲外の日時が指定された。{0}：{1}",
    APGMDW0011:
        "リアルタイムオーダ、予約前オーダ、予約実行オーダのどのオーダなのか判定できない。",

    // #【APLAF】回線アクティベート・ディアクティベート機能
    APLAFW0011:
        "リアルタイムオーダ、予約前オーダ、予約実行オーダのどのオーダなのか判定できない。",
    APLAFW0012:
        "予約前オーダの予約日において、過去日時が指定された。予約日：{0}＜システム時刻：{1}",
    APLAFW0013: "SG「予約実行日時単位」フォーマットエラー：「{0}」",
    APLAFW0014:
        "予約前オーダの予約日において、SG「予約実行日時単位」に許容されない日時が指定された。{0}：{1}",
    APLAFW0015: "SG「予約可能制限日数」フォーマットエラー：「{0}」",
    APLAFW0016:
        "予約前オーダの予約日において、SG「予約可能制限日数」の範囲外の日時が指定された。{0}：{1}",
    APLAFW0017: "回線番号[{0}]はすでに廃止SOを受付中です。",

    // #【APGMB】回線グループ基本容量変更
    APGMBW0011:
        "リアルタイムオーダ、予約前オーダ、予約実行オーダのどのオーダなのか判定できない。",
    APGMBW0012:
        "予約前オーダの予約日において、過去日時が指定された。予約日：{0}＜システム時刻：{1}",
    APGMBW0013: "SG「予約実行日時単位」フォーマットエラー：「{0}」",
    APGMBW0014:
        "予約前オーダの予約日において、SG「予約実行日時単位」に許容されない日時が指定された。{0}：{1}",
    APGMBW0015: "SG「予約可能制限日数」フォーマットエラー：「{0}」",
    APGMBW0016:
        "予約前オーダの予約日において、SG「予約可能制限日数」の範囲外の日時が指定された。{0}：{1}",
    APGMBW0010: "テナント{0}の接続先TPC情報が取得できません。",
    APGMBW0017: "セカンダリTPCへのSO投入失敗。エラー内容[{0}]",

    // #【APLCF】回線追加チャージ(クーポン)容量追加・期間追加機能
    APLCFW0011:
        "リアルタイムオーダ、予約前オーダ、予約実行オーダのどのオーダなのか判定できない。",
    APLCFW0012:
        "予約前オーダの予約日において、過去日時が指定された。予約日：{0}＜システム時刻：{1}",
    APLCFW0013: "SG「予約実行日時単位」フォーマットエラー：「{0}」",
    APLCFW0014:
        "予約前オーダの予約日において、SG「予約実行日時単位」に許容されない日時が指定された。{0}：{1}",
    APLCFW0015: "SG「予約可能制限日数」フォーマットエラー：「{0}」",
    APLCFW0016:
        "予約前オーダの予約日において、SG「予約可能制限日数」の範囲外の日時が指定された。{0}：{1}",

    // #【APLCP】回線クーポンオン・オフ機能
    APLCPW0011:
        "リアルタイムオーダ、予約前オーダ、予約実行オーダのどのオーダなのか判定できない。",
    APLCPW0012:
        "予約前オーダの予約日において、過去日時が指定された。予約日：{0}＜システム時刻：{1}",
    APLCPW0013: "SG「予約実行日時単位」フォーマットエラー：「{0}」",
    APLCPW0014:
        "予約前オーダの予約日において、SG「予約実行日時単位」に許容されない日時が指定された。{0}：{1}",
    APLCPW0015: "SG「予約可能制限日数」フォーマットエラー：「{0}」",
    APLCPW0016:
        "予約前オーダの予約日において、SG「予約可能制限日数」の範囲外の日時が指定された。{0}：{1}",
    APLCPW0017: "社内テナント種別が取得できない。",
    APLCPW0018: "プランID[{0}]が回線の利用しているプランでない",
    APLCPW0019: "回線番号[{0}]はすでに廃止SOを受付中です。",

    // #【APSOC】予約キャンセル
    APSOCW0001: "電文パラメータフォーマットエラー。SO-ID:{0} {1}:[{2}]",
    APSOCW0201:
        "予約キャンセル対象の情報がSO管理から取得できない。SO-ID:{0} テナントID:{1}",
    APSOCW0202:
        "予約キャンセル対象の情報がSO管理のREST電文から取得できない。SO-ID:{0} チェック項目名:{1}",
    APSOCW0203:
        "予約キャンセル対象の情報がSO管理のREST電文から取得できない。SO-ID:{0} チェック項目名:{1}",
    APSOCW0204:
        "予約キャンセル対象の情報がSO管理のREST電文から取得できない。SO-ID:{0} チェック項目名:{1}",
    APSOCW0301: "SG「キャンセル不可期間」フォーマットエラー:「{0}」",
    APSOCW0302:
        "SG「キャンセル不可期間」の期間中に予約キャンセルを受信しました。SO-ID:{0} SG「キャンセル不可期間」:{1} システム時刻:{2}",
    APSOCW0401:
        "予約キャンセルにおいて、過去の予約をキャンセルしようとしました。SO-ID:{0} システム時刻:{1} 予約日:{2}",
    APSOCW0501:
        "予約キャンセルにおいて、回線IDが自テナントに所属していませんでした。SO-ID:{0} 機能種別:{1} 回線ID:{2}",
    APSOCW0502:
        "予約キャンセルにおいて、回線IDが自テナント配下の何れかのテナントにも所属していませんでした。SO-ID:{0} 機能種別:{1} 回線ID:{2}",
    APSOCW0503:
        "予約キャンセルにおいて、グループプランIDが自テナントに所属していませんでした。SO-ID:{0} 機能種別:{1} グループプランID:{2}",
    APSOCW0504:
        "予約キャンセルにおいて、グループプランIDが自テナント配下の何れかのテナントにもに所属していませんでした。SO-ID:{0} 機能種別:{1} グループプランID:{2}",
    APSOCW0505:
        "予約キャンセルにおいて、回線グループIDが自テナントに所属していませんでした。SO-ID:{0} 機能種別:{1} 回線グループID:{2}",
    APSOCW0506:
        "予約キャンセルにおいて、回線グループIDが自テナント配下の何れかのテナントにもに所属していませんでした。SO-ID:{0} 機能種別:{1} 回線グループID:{2}",
    APSOCI0001:
        "処理を開始します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}",
    APSOCI0002:
        "処理を終了します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}",
    APSOCD0001: "処理開始 [{0}:{1}] {2}",
    APSOCD0002: "処理終了 [{0}:{1}]",

    // #【APLPC】回線プラン変更
    APLPCW1302: "グループに所属しているため、プラン変更できない。回線番号：{0}",
    APLPCW1401: "TPCにSO投入失敗。",
    APLPCW1402: "セカンダリTPCへのSO投入失敗。エラー内容[{0}]",
    APLPCI0001:
        "処理を開始します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}",
    APLPCI0002:
        "処理を終了します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}",
    APLPCE1602:
        "電文のテナントIDがテナントに所属していませんでした。テナントID:{0}",
    APLPCE1603:
        "電文で指定されたテナントIDがテナント所属判定で無効と判断されました。テナントID:{0}",
    APLPCE1605:
        "電文の変更後卸ポータルプランIDがプランに所属していませんでした。プランID:{0}",
    APLPCE1606:
        "電文の変更前卸ポータルプランIDがプランに所属していませんでした。プランID:{0}",

    // #【MPRCO】REST共通機能
    // MPRCOE0201: "ファイル出力エラー[出力先ディレクトリ名:{0} ファイル名:{1}]",
    // MPRCOI0202: "CSVファイルを出力しました。[出力先ディレクトリ名:{0} ファイル名:{1}]",

    // #【APLCF】回線追加チャージ(クーポン)容量追加・期間追加機能
    // APLCFE0602: "電文で指定されたテナントIDがテナント所属判定で無効と判断されました。テナントID:{0}",
    // APLCFE0605: "容量追加コードがnullまたはフォーマットエラーです。",

    // #【APSOC】予約キャンセル
    APSOCE0602:
        "予約キャンセルにおいて、予約時の回線ID、テナントIDがテナントに所属していませんでした。回線ID:{0} テナントID:{1}",
    APSOCE0603:
        "予約キャンセルにおいて、予約キャンセルリクエスト電文で指定されたテナントIDがテナントに存在しない。テナントID：{0}",

    // #【APGMD】回線グループ所属回線変更
    APGMDE1603:
        "電文で指定されたテナントIDがテナント所属判定で無効と判断されました。テナントID:{0}",
    APGMDE1605:
        "電文の回線グループIDが回線グループ管理に所属していませんでした。回線グループID:{0}",
    APGMDE1606:
        "プランIDがグループプランに所属していませんでした。プランID:{0}",
    APGMDE5603:
        "電文で指定されたテナントIDがテナント所属判定で無効と判断されました。テナントID:{0}",
    APGMDE5605:
        "電文の回線グループIDが回線グループ管理に所属していませんでした。回線グループID:{0}",
    APGMDE5606:
        "プランIDがグループプランに所属していませんでした。プランID:{0}",

    // #【CRPLD】マスタ化遅れ回線検知機能
    CRPLDI0001: "処理を開始します。",
    CRPLDI0002: "処理を終了します。",
    CRPLDE0001: "二重起動を検知したので終了します。",
    CRPLDE0002: "システムプロパティ値の取得に失敗しました。項目:{0}",
    CRPLDE0003: "システムプロパティ値が不正です。項目：{0}　値：{1}",
    CRPLDW0001:
        "マスタ化遅れ回線を検知しました。基準日{0}　件数：{1}　回線ID：{2}",

    // #【APPLA】仮登録回線追加機能
    APPLAW0001: "電文パラメータフォーマットエラー。{0}:[{1}]",
    APPLAW0002: "テナント情報の取得に失敗しました。テナントID：{0}",
    APPLAW0003: "プラン情報の取得に失敗しました。サービスプランID：{0}",
    APPLAW0005: "回線番号[{0}]が既に有効な状態で存在します。",
    APPLAW0006: "テナント[{0}]が利用できないプランID[{1}]です。",
    APPLAW0007:
        "テナント[{0}]のプラン[{1}]が利用できない回線オプションID[{2}]です。",
    APPLAW0008: "プランID[{0}]は音声プランではありません。",
    APPLAW0009: "テナント[{0}]に紐づくN番[{1}]が存在しません。",
    APPLAW0010: "SO投入失敗、エラー内容は[{0}]です。",
    APPLAW0011:
        "回線番号[{0}]の更新に必要な情報の取得、もしくは更新に失敗しました。",
    APPLAW0012:
        "テナント[{0}]は存在しない、もしくはステータスがfalseなテナントです。",
    APPLAW0013: "テナント[{0}]に紐づく帯域卸フラグが存在しません。",
    APPLAW0014:
        "指定された回線オプション[{0}]は、テナント[{1}]では使用できません。",
    APPLAW0015: "テナント{0}の接続先TPC情報が取得できません。",
    APPLAW0016:
        "回線オプションID[{0}]に紐づく回線オプション情報が存在しません。",
    APPLAW0020:
        "リアルタイムオーダ、予約前オーダ、予約実行オーダのどのオーダなのか判定できない。",
    APPLAW0021:
        "予約前オーダの予約日において、過去日時が指定された。予約日：{0}＜システム時刻：{1}",
    APPLAW0022: "SG「予約実行日時単位」フォーマットエラー：「{0}」",
    APPLAW0023:
        "予約前オーダの予約日において、SG「予約実行日時単位」に許容されない日時が指定された。{0}：{1}",
    APPLAW0024: "SG「予約可能制限日数」フォーマットエラー：「{0}」",
    APPLAW0025:
        "予約前オーダの予約日において、SG「予約可能制限日数」の範囲外の日時が指定された。{0}：{1}",
    APPLAW0026:
        "指定された回線オプション[{0}]は、代表N番[{1}]では使用できません。",
    APPLAW0027:
        "指定された回線オプション[{0}]は、代表N番[{1}]では使用できません。",
    APPLAW0028:
        "指定された回線ID[{0}]は、予約オーダ[SO-ID:{1}]で既に開通予定です。",
    APPLAW0029:
        "テナント[{0}]は存在しない、もしくはステータスがfalseなテナントです。",
    APPLAW0030: "回線廃止テナント[{0}]の接続先TPC情報が取得できません。",
    APPLAW0031: "SO投入失敗、エラー内容は[{0}]です。",
    APPLAW0032: "回線情報の更新に失敗しました。回線番号：[{0}]",
    APPLAW0033:
        "廃止回線情報テーブル格納時にエラーが発生しました。回線番号：{0}",
    APPLAW0034: "回線通信量のロックに失敗しました。回線番号：{0} 年月日：{1}",
    APPLAW0035:
        "回線通信量の排他制御のタイムアウトが発生しました。回線番号：{0} 年月日：{1}",
    APPLAW0036: "回線通信量の更新に失敗しました。回線番号：{0} 年月日：{1}",
    APPLAW0037: "SO管理情報の取得に失敗しました。回線番号：{0}",
    APPLAW0038:
        "SO管理情報の更新に失敗しました。回線番号：{0} サービスオーダID：{1}",
    APPLAW0039: "セカンダリTPCへのSO投入失敗。エラー内容[{0}]",
    APPLAW0040: "セカンダリTPCへのSO投入失敗。エラー内容[{0}]",
    APPLAI0001:
        "処理を開始します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}",
    APPLAI0002:
        "処理を終了します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}",
    APPLAD0001: "処理開始 [{0}:{1}] {2}",
    APPLAD0002: "処理終了 [{0}:{1}]",
    APPLAE0001: "SG「仮登録回線受付不可時間」フォーマットエラー：「{0}」",
    APPLAE0002: "現在「仮登録回線受付不可時間」です。受付不可時間帯:{0}",
    APPLAE0003:
        "お客様基本情報の取得に失敗しました。N番：{0} MNP転入フラグ：{1}",
    APPLAE0004:
        "お客様基本情報の取得に失敗しました。回線番号：{0} MNP転入フラグ：{1}",
    APPLAE0005: "0035でんわ情報の取得に失敗しました。テナントID：[{0}]",
    APPLAE0017: "カード用途の取得に失敗しました。N番：{0}",
    APPLAE0018:
        "指定されたカード種別ID[{0}]はカード用途[{1}]で使用できる種別ではありません。",
    APPLAE0019:
        "指定されたプランID[{0}]はカード用途[{1}]で使用できるプランではありません。",
    APPLAE0020:
        "指定されたプランID[{0}]はカード種別[{1}]で使用できるプランではありません。",

    // #【APLEN】回線黒化
    APLENW0001: "回線番号[{0}]は既に廃止されています。",
    APLENW0002: "回線番号[{0}]はテナントID[{1}]に所属していません。",
    APLENW0003: "回線番号[{0}]はすでに廃止SOを受付中です。",
    APLENW0004: "回線情報の更新に失敗しました。回線番号：[{0}]",
    APLENW0005:
        "卸ポータルフロントからのリクエストではありません。テナントID：{0}",
    APLENE0001:
        "回線とテナントの所属判定時に、N番を取得できませんでした。回線ID：{0} テナントID：{1}",
    APLENI0001:
        "処理を開始します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}",
    APLENI0002:
        "処理を終了します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}",
    APLEND0001: "処理開始 [{0}:{1}] {2}",
    APLEND0002: "処理終了 [{0}:{1}]",

    // #【APLDL】回線廃止
    APLDLW0001: "回線番号[{0}]は既に廃止されています。",
    APLDLW0002: "回線番号[{0}]は既に廃止オーダを受付中です。",
    APLDLW0003: "回線番号[{0}]はテナントID[{1}]に所属していません。",
    APLDLW0004:
        "回線番号[{0}]は黒化されていないSIMです。半黒SIMは廃止できません。",
    APLDLW0005: "回線番号[{0}]はSOを予約中のため、廃止できません。",
    APLDLW0006:
        "予約前オーダの予約日において、過去日時が指定された。予約日：{0}＜システム時刻：{1}",
    APLDLW0007: "SG「予約実行日時単位」フォーマットエラー：「{0}」",
    APLDLW0008:
        "予約前オーダの予約日において、SG「予約実行日時単位」に許容されない日時が指定された。{0}：{1}",
    APLDLW0009: "SG「予約可能制限日数」フォーマットエラー：「{0}」",
    APLDLW0010:
        "予約前オーダの予約日において、SG「予約可能制限日数」の範囲外の日時が指定された。{0}：{1}",
    APLDLW0011:
        "リアルタイムオーダ、予約前オーダ、予約実行オーダのどのオーダなのか判定できない。",
    APLDLW0012:
        "卸ポータルフロントからのリクエストではありません。テナントID：{0}",
    APLDLW0013:
        "MNP転出による回線廃止は、予約日指定不可です。回線ID：{0} 予約日：{1}",
    APLDLW0014:
        '電文のMNP転出フラグに"1"(MNP転出である)が設定されています。回線ID：{0}',
    APLDLW0015: "SO管理情報の取得に失敗しました。回線番号：{0}",
    APLDLW0016:
        "SO管理情報の更新に失敗しました。回線番号：{0} サービスオーダID：{1}",
    APLDLE0001:
        "お客様基本情報の取得に失敗しました。回線ID：{0} MNP転出フラグ：{1}",
    APLDLE0002:
        "回線とテナントの所属判定時に、N番を取得できませんでした。回線ID：{0} テナントID：{1}",
    APLDLI0001:
        "処理を開始します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}",
    APLDLI0002:
        "処理を終了します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}",
    APLDLD0001: "処理開始 [{0}:{1}] {2}",
    APLDLD0002: "処理終了 [{0}:{1}]",

    // #【APFOC】卸ポータルフロント向け予約キャンセル
    APFOCW0001: "電文パラメータフォーマットエラー。SO-ID:{0} {1}:[{2}]",
    APFOCW0201:
        "予約キャンセル対象の情報がSO管理から取得できない。SO-ID:{0} チェック項目名:{1}",
    APFOCW0202:
        "予約キャンセル対象の情報がSO管理のREST電文から取得できない。SO-ID:{0} チェック項目名:{1}",
    APFOCW0203:
        "指定されたSO-IDの機能種別は予約キャンセル不可です。SO-ID:{0} 機能種別:{1}",
    APFOCW0301: "SG「キャンセル不可期間」フォーマットエラー:「{0}」",
    APFOCW0302:
        "SG「キャンセル不可期間」の期間中に予約キャンセルを受信しました。SO-ID:{0} SG「キャンセル不可期間」:{1} システム時刻:{2}",
    APFOCW0401:
        "予約キャンセルにおいて、過去の予約をキャンセルしようとしました。SO-ID:{0} システム時刻:{1} 予約日:{2}",
    APFOCW0501:
        "予約キャンセルにおいて、指定された所属テナントIDがSO管理情報の所属テナントIDと一致しませんでした。SO-ID:{0} 電文中の所属テナントID:{1} SO管理の所属テナントID:{2}",
    APFOCE0602:
        "予約キャンセルにおいて、予約時の回線ID、テナントIDがテナントに所属していませんでした。SO-ID:{0} 回線ID:{1} テナントID:{2}",
    APFOCI0001:
        "処理を開始します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}",
    APFOCI0002:
        "処理を終了します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}",

    // #【APONM】回線オプション_NW暗証番号変更
    APONMW0001: "回線番号[{0}]は既に廃止されています。",
    APONMW0002: "回線番号[{0}]はテナントID[{1}]に所属していません。",
    APONMW0003: "回線番号[{0}]はすでに廃止SOを受付中です。",
    APONMW0004: "回線情報の更新に失敗しました。回線番号：[{0}]",
    APONMW0005:
        "卸ポータルフロントからのリクエストではありません。テナントID：{0}",
    APONME0001:
        "お客様基本情報の取得に失敗しました。回線ID：{0} NW暗証番号変更：{1}",
    APONME0002:
        "回線とテナントの所属判定時に、N番を取得できませんでした。回線ID：{0} テナントID：{1}",
    APONMI0001:
        "処理を開始します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}",
    APONMI0002:
        "処理を終了します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}",
    APONMD0001: "処理開始 [{0}:{1}] {2}",
    APONMD0002: "処理終了 [{0}:{1}]",

    // #【APLDG】回線データ譲渡機能
    APLDGW0001: "電文パラメータフォーマットエラー。{0}:[{1}]",
    APLDGW0002:
        "SG「データ譲渡不可フラグ」に許容されないフォーマットが指定された。{0}",
    APLDGW0003:
        "SG「データ譲渡不可時間帯」に許容されない時間帯が指定された。{0}",
    APLDGW0004:
        "データ譲渡オーダを、SG「データ譲渡不可フラグ[{0}]」、「データ譲渡不可時間帯[{1}]」に許容されない時間帯に処理しようとした。",
    APLDGW0101:
        "電文で指定された譲渡元回線番号とテナントIDの関係が、テナント所属判定で無効と判断されました。譲渡元回線番号:{0}、テナントID:{1}",
    APLDGW0102:
        "電文で指定された譲渡先回線番号とテナントIDの関係が、テナント所属判定で無効と判断されました。譲渡先回線番号:{0}、テナントID:{1}",
    APLDGW0103:
        "電文で指定された譲渡元回線番号[{0}]はすでに廃止SOを受付中です。",
    APLDGW0104:
        "電文で指定された譲渡先回線番号[{0}]はすでに廃止SOを受付中です。",
    APLDGW0105:
        "電文で指定された譲渡元回線番号[{0}]はデータ譲渡SOを受付中です。",
    APLDGW0106:
        "電文で指定された譲渡先回線番号[{0}]はデータ譲渡SOを受付中です。",
    APLDGW0107:
        "データ譲渡回線テーブルへの譲渡元回線番号[{0}]の追加に失敗しました。",
    APLDGW0108:
        "データ譲渡回線テーブルへの譲渡先回線番号[{0}]の追加に失敗しました。",
    APLDGW0109: "テナント{0}の接続先TPC情報が取得できません。",
    APLDGW0110:
        "譲渡元回線番号[{0}]の情報取得処理にて、TPCにSO投入失敗しました。エラー内容は[{1}]です。",
    APLDGW0111:
        "譲渡先回線番号[{0}]の情報取得処理にて、TPCにSO投入失敗しました。エラー内容は[{1}]です。",
    APLDGW0112: "取得項目フォーマットエラー。{0}:[{1}]",
    APLDGW0113:
        "譲渡データ量[{0}(byte)]が、譲渡元回線の残bucket容量[{1}(byte)]を上回ってます。",
    APLDGW0114:
        "譲渡先回線[{0}]の加算・譲渡元回線[{1}]の減算処理にて、TPCにSO投入失敗しました。エラー内容は[{2}]です。",
    APLDGW0115:
        "データ譲渡回線テーブルからの譲渡元回線[{0}]・譲渡先回線[{1}]の削除に失敗しました。",
    APLDGW0116:
        "電文で指定された譲渡元回線番号[{0}]は利用状態がサスペンド中のため利用できません。",
    APLDGW0117:
        "電文で指定された譲渡先回線番号[{0}]は利用状態がサスペンド中のため利用できません。",
    APLDGW0118:
        "譲渡先回線[{0}]の加算・譲渡元回線[{1}]の減算処理にて、セカンダリTPCにSO投入失敗しました。エラー内容は[{2}]です。",
    APLDGW0201:
        "電文で指定された譲渡元回線番号とテナントIDの関係が、テナント所属判定で無効と判断されました。譲渡元回線番号:{0}、テナントID:{1}",
    APLDGW0202:
        "電文で指定された譲渡元回線番号[{0}]はすでに廃止SOを受付中です。",
    APLDGW0203:
        "電文で指定された譲渡元回線番号[{0}]はデータ譲渡SOを受付中です。",
    APLDGW0204:
        "データ譲渡回線テーブルへの譲渡元回線番号[{0}]の追加に失敗しました。",
    APLDGW0205: "テナント{0}の接続先TPC情報が取得できません。",
    APLDGW0206:
        "譲渡元回線番号[{0}]の情報取得処理にて、TPCにSO投入失敗しました。エラー内容は[{1}]です。",
    APLDGW0207: "取得項目フォーマットエラー。{0}:[{1}]",
    APLDGW0208:
        "譲渡データ量[{0}(byte)]が、譲渡元回線の残bucket容量[{1}(byte)]を上回ってます。",
    APLDGW0209:
        "譲渡元回線[{0}]の減算処理にて、TPCにSO投入失敗しました。エラー内容は[{1}]です。",
    APLDGW0210:
        "データ譲渡回線テーブルからの譲渡元回線[{0}]の削除に失敗しました。",
    APLDGW0211:
        "電文で指定された譲渡元回線番号[{0}]は利用状態がサスペンド中のため利用できません。",
    APLDGW0212:
        "譲渡元回線[{0}]の減算処理にて、セカンダリTPCにSO投入失敗しました。エラー内容は[{1}]です。",
    APLDGI0001:
        "処理を開始します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}",
    APLDGI0002:
        "処理を終了します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}",

    // #【APRES】SIM再発行機能
    APRESW0001: "回線番号[{0}]は既に廃止されています。",
    APRESW0002: "回線番号[{0}]はテナントID[{1}]に所属していません。",
    APRESW0003: "回線番号[{0}]はすでに廃止SOを受付中です。",
    APRESW0004: "カード種別名の取得に失敗しました。カード種別ID：[{0}]",
    APRESW0005: "回線情報の更新に失敗しました。回線番号：[{0}]",
    APRESW0006:
        "卸ポータルフロントからのリクエストではありません。テナントID：{0}",
    APRESE0001:
        "回線とテナントの所属判定時に、N番を取得できませんでした。回線ID：{0} テナントID：{1}",
    APRESI0001:
        "処理を開始します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}",
    APRESI0002:
        "処理を終了します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}",
    APRESD0001: "処理開始 [{0}:{1}] {2}",
    APRESD0002: "処理終了 [{0}:{1}]",

    // #【APPAD】回線プラン追加／削除
    APPADI0001:
        "処理を開始します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}",
    APPADI0002:
        "処理を終了します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}",
    APPADW0001: "電文パラメータフォーマットエラー。{0}:[{1}]",
    APPADW0002:
        "電文で指定された対象所属テナントIDがテナントに存在しません。テナントID:{0}",
    APPADW0003:
        "電文で指定されたテンプレートIDがテンプレート管理に存在しません。テンプレートID:{0}",
    APPADW0004: "テナント[{0}]が利用できないテンプレートID[{1}]です。",
    APPADW0005: "カスタマイズ可能項目に値が設定されておりません。項目名：[{0}]",
    APPADW0006: "カスタマイズ可能な上限数[{0}]を超えています。",
    APPADW0007: "テーブル[{0}]からID[{1}]が取得できません。",
    APPADW0008: "テーブル[{0}]のロック取得に失敗しました。",
    APPADE0001: "テーブル[{0}]の更新に失敗しました。",
    APPADW0009: "テナント{0}の接続先TPC情報が取得できません。",
    APPADW0010: "SO投入失敗、エラー内容は[{0}]です。",
    APPADE0002: "テーブル[{0}]の登録に失敗しました。",
    APPADW0011:
        "電文で指定されたカスタマイズIDが{0}に存在しません。カスタマイズID:{1}",
    APPADW0012:
        "電文で指定された対象所属テナントIDと削除対象プランのテナントIDが一致しないため、削除できません。",
    APPADW0013:
        "削除対象プラン[{0}]が回線情報に適用済みのため、削除できません。",
    APPADW0014:
        "削除対象プラン[{0}]が機能種別[{1}]にて予約中のため、削除できません。",
    APPADE0003: "テーブル[{0}]の削除に失敗しました。",

    // #【APGPC】回線グループプラン変更機能
    APGPCI0001:
        "処理を開始します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}",
    APGPCI0002:
        "処理を終了します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}",
    APGPCW0001:
        "リアルタイムオーダ、予約前オーダ、予約実行オーダのどのオーダなのか判定できない。",
    APGPCW0002: "電文パラメータフォーマットエラー。{0}:[{1}]",
    APGPCW0003:
        "SG「グループプラン変更不可時間帯」に許容されない時間帯が指定された。{0}",
    APGPCW0004:
        "グループプラン変更オーダを、SG「グループプラン変更不可時間帯」に許容されない時間帯に処理しようとした。{0}",
    APGPCW0005:
        "当該オーダの回線グループIDに対して既に予約が入っていた。回線グループID：{0}",
    APGPCW0006:
        "予約前オーダの予約日において、過去日時が指定された。予約日：{0}＜システム時刻：{1}",
    APGPCW0007:
        "予約前オーダの予約日において、SG「予約実行日時単位」に許容されないフォーマットが指定された。{0}",
    APGPCW0008:
        "予約前オーダの予約日において、SG「予約実行日時単位」に許容されない日時が指定された。{0}：{1}",
    APGPCW0009:
        "予約前オーダの予約日において、SG「予約可能制限日数」に許容されないフォーマットが指定された。{0}",
    APGPCW0010:
        "予約前オーダの予約日において、SG「予約可能制限日数」の範囲外の日時が指定された。{0}：{1}",
    APGPCW0011: "社内テナント種別が取得できない。",
    APGPCW0012: "回線グループID[{0}]を取得できません。",
    APGPCW0013: "回線グループ{0}は、テナント{1}に所属していません。",
    APGPCW0014:
        "変更前卸ポータルグループプランID[{0}]がプランID[{1}]と一致しません。",
    APGPCW0015:
        "変更後卸ポータルグループプランID[{0}]のサービスプランIDが取得できません。",
    APGPCW0016:
        "変更後卸ポータルグループプランID[{0}]はテナントID[{1}]で利用できません。",
    APGPCW0017: "テナント{0}の接続先TPC情報が取得できません。",
    APGPCW0018: "SO投入失敗、エラー内容は[{0}]です。",
    APGPCW0019: "セカンダリTPCへのSO投入失敗。エラー内容[{0}]",
    APGPCE0001: "回線グループ管理の更新に失敗しました。",

    // #【APIRS】利用中断／再開／サスペンド機能
    APIRSI0001:
        "処理を開始します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}",
    APIRSI0002:
        "処理を終了します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}",
    APIRSW0001: "電文パラメータフォーマットエラー。{0}:[{1}]",
    APIRSW0002: "回線番号[{0}]は既に廃止されています。",
    APIRSW0003: "回線番号[{0}]はテナントID[{1}]に所属していません。",
    APIRSW0004: "回線番号[{0}]はフルMVNOのプラン[{1}]ではありません。",
    APIRSE0001: "回線情報の更新に失敗しました。回線番号：[{0}]",
    APIRSE0002:
        "回線回線グループ情報の更新に失敗しました。回線番号：[{0}]、回線グループID：[{1}]",

    // #【APNCC】NW契約変更
    APNCCI0001:
        "処理を開始します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}",
    APNCCI0002:
        "処理を終了します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}",
    APNCCW0001:
        "卸ポータルフロントからのリクエストではありません。テナントID：{0}",
    APNCCW0002: "回線番号[{0}]は既に廃止されています。",
    APNCCW0003: "回線番号[{0}]はテナントID[{1}]に所属していません。",
    APNCCW0004: "回線番号[{0}]はすでに廃止SOを受付中です。",
    APNCCW0005: "回線情報のロック取得に失敗しました。回線番号:[{0}]",
    APNCCW0006: "カード種別名の取得に失敗しました。カード種別ID：[{0}]",
    APNCCE0001: "回線情報の更新に失敗しました。回線番号：[{0}]",
    APNCCE0002:
        "回線とテナントの所属判定時に、N番を取得できませんでした。回線ID：{0} テナントID：{1}",

    // #【APPPC】0035でんわプラン変更
    APPPCI0001:
        "処理を開始します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}",
    APPPCI0002:
        "処理を終了します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}",
    APPPCW0001:
        "卸ポータルフロントからのリクエストではありません。テナントID：{0}",
    APPPCW0002: "回線番号[{0}]は既に廃止されています。",
    APPPCW0003: "回線番号[{0}]はテナントID[{1}]に所属していません。",
    APPPCW0004:
        "0035でんわプラン名の取得に失敗しました。テナントID：{0} 0035でんわプランID：{1}",
    APPPCW0005: "回線情報のロック取得に失敗しました。回線番号:[{0}]",
    APPPCE0001: "回線情報の更新に失敗しました。回線番号：[{0}]",

    // #【CRAGM】回線グループ基本容量変更API定期実行機能
    CRAGME0001:"システムプロパティ値の取得に失敗しました。項目:{0}",
    CRAGME0002:"二重起動を検知したので終了します。",
    CRAGME0003:"API定期実行バッチ処理日時取得でエラーが発生しました。",
    CRAGME0004:"前回の差分取得時刻の取得に失敗しました。",
    CRAGME0005:"前回の実行は終了していないか異常終了しています。",
    CRAGME0006:"今回実施日時の設定ができません。",
    CRAGME0007:"テナントIDに紐付くハッシュ化パスワードの取得に失敗しました。回線グループID：{0}",
    CRAGME0008:"フォーマットエラー[{0}][{1}]",
    CRAGME0009:"廃止回線グループテーブルのクリーンアップができません。",
    CRAGME0010:"前回実施日時の設定ができません。",
    CRAGME0011:"{0}は異常終了しました。",
    CRAGME0012:"予期せぬエラー [{0}]",
    CRAGME0013:"DBアクセス時にリトライ回数分エラーとなりました。[{0}]",
    CRAGMW0001:"回線グループIDに紐付くテナントID、プランIDの取得に失敗しました。回線グループID：{0}",
    CRAGMW0002:"回線グループ基本容量変更API実行 結果：異常応答[処理日時：{0} 回線グループID：{1} テナントID：{2} 処理コード：{3} API処理ID：{4}",
    CRAGMI0001:"処理を開始します。 処理名:{0}",
    CRAGMI0002:"処理対象回線グループ情報の検索結果が0件でした。",
    CRAGMI0003:"回線グループ基本容量変更API実行 結果：正常応答[処理日時：{0} 回線グループID：{1} テナントID：{2} 容量：{3} API処理ID：{4}]",
    CRAGMI0004:"{0}は正常終了しました。",

    // #【APSOR】予約実行機能
    APSORW0001: "二重起動を検知したので終了しました。実行プロセスID:{0}",
    APSORI0002: "SG「TPC接続不可時間帯」 -> {0}",
    APSORE0003: "読み込んだSG「TPC接続不可時間帯」の値が妥当でないため、予約実行機能を終了します。 -> {0}",
    APSORI0004: "SG「API処理IDサーバ識別」 -> {0}",
    APSORE0005: "読み込んだSG「API処理IDサーバ識別」の値が妥当でないため、予約実行機能を終了します。 -> {0}",
    APSORI0006: "SG「TPC最大同時接続数」 -> {0}",
    APSORE0007: "読み込んだSG「TPC最大同時接続数」の値が妥当でないため、予約実行機能を終了します。 -> {0}",
    APSORI0008: "予約実行機能を開始しました。",
    APSORI0201: "実行キュー積み込み機能: 終了通知を送信しました。",
    APSORI0301: "実行済みオーダを削除しました。未実行:{0}件 実行中:{1}件 実行済:{2}件",
    APSORI0401: "TPC接続不可時間帯に入ったので予約オーダの積み込みを抑止します。接続不可時間帯:{0}",
    APSORI0402: "TPC接続可時間帯に入ったので予約オーダの積み込みを開始します。接続不可時間帯:{0}",
    APSORI0501: "SO管理からデータを取得しました。取得件数:{0}件",
    APSORI0601: "予約中オーダを未実行として積込みました。積込み件数:{0}件",
    APSORI0701: "実行キュー積み込み機能:開始通知を送信しました。",
    APSORE0702: "実行キュー積み込み機能:実行キュー送信機能が異常です。",
    APSORI1002: "予約実行機能を終了しました。",
    APSORE5001: "SO管理からのデータ取得が失敗しました。",
    APSORI5101: "実行キュー送信機能の処理を開始します。",
    APSORI5401: "予約中オーダを実行します。並列実行識別子:{0} SO-ID:{1}",
    APSORI5501: "予約中オーダが完了しました。並列実行識別子:{0} SO-ID:{1} 処理コード:{2}",
    APSORE5501: "予約中オーダが完了しました。並列実行識別子:{0} SO-ID:{1} 処理コード:{2}",
    APSORE5502: "{0}秒を経過したオーダを強制切断しました。並列実行識別子:{1} SO-ID:{2}",
    APSORI5701: "実行キュー送信機能: 終了通知を受信しました。",
    APSORE0002: "プロパティファイル中の項目にエラーがありました。プロパティファイル:{0}",
    APSORE0098: "プロパティファイルに必須項目が指定されていません。項目: {0}",
    APSORE0099: "プロパティファイルの読み込みに失敗しました。プロパティファイル:{0}",
    APSORI1001: "実行キュー送信機能の終了を待ちます。",
    APSORI0101: "SG「実行キュー積み込み処理間隔」 -> {0}",
    APSORE0101: "読み込んだSG「実行キュー積み込み処理間隔」の値が妥当でないため、予約実行機能を終了します。 -> {0}",
    APSORI0102: "SG「実行キュー送信処理間隔」 -> {0}",
    APSORE0102: "読み込んだSG「実行キュー送信処理間隔」の値が妥当でないため、予約実行機能を終了します。 -> {0}",
    APSORI0901: "実行キュー積み込み処理: 次の実行まで待機します。",
    APSORE9998: "実行中のスレッドでエラーが発生した可能性があります。[{0}]",
    APSORE9999: "予期せぬエラー [{0}]",

    // from messageonline.properties
    GUCOMG0001: "セッションの更新に失敗しました。（GUCOMG0001）",
    GUCOMG0002: "ユーザ権限がありません。（GUCOMG0002）",
    GUCOMG0003: "DBのアクセスに失敗しました。（GUCOMG0003）",
    GUCOMG0004: "想定外エラーが発生しました。（GUCOMG0004）",
    GUULOG0001: "テナントID、ログインIDまたはパスワードの組み合わせが間違ってます。（GUULOG0001）",
    GUULOG0002: "テナントIDは無効です。（GUULOG0002）",
    GUULOG0003: "テナントID、ログインIDまたはパスワードの組み合わせが間違ってます。（GUULOG0003）",
    GUULOG0004: "ユーザIDは無効です。（GUULOG0004）",
    GUULOG0005: "アカウントステータスの更新に失敗しました。（GUULOG0005）",
    GUULOG0006: "パスワードの有効期限が切れました。（GUULOG0006）",
    GUULOG0008: "接続元IPアドレス判定に失敗しました。（GUULOG0008）",
    GUULOG0009: "許可されていないアクセスです。（GUULOG0009）",
    GUPCHG0001: "現在のパスワードが違います。（GUPCHG0001）",
    GUPCHG0002: "新パスワードを8桁以上16桁以下の範囲で入力して下さい。（GUPCHG0002）",
    GUPCHG0003: "新パスワードは半角英数字「a-zA-Z0-9」または記号「!”#$%&’()=~|@[;:],./’'{+*'}<>?_」を使って、8桁以上16桁以下で入力して下さい。（GUPCHG0003）",
    GUPCHG0004: "過去３世代パスワードは再利用できません。（GUPCHG0004）",
    GUPCHG0005: "パスワードがパスワード(再入力)と一致しません。（GUPCHG0005）",
    GUPCHG0006: "パスワード変更対象のユーザ情報の取得に失敗しました。（GUPCHG0006）",
    GUPCHG0007: "パスワードを変更しました。",
    GUSOLG0001: "キーワード入力欄の入力値を1桁以上11桁以下の範囲で入力して下さい。（GUSOLG0001）",
    GUSOLG0002: "キーワード入力欄の入力値が半角数字ではありません。（GUSOLG0002）",
    GUSOLG0003: "キーワード入力欄の入力値を1桁以上15桁以下の範囲で入力して下さい。（GUSOLG0003）",
    GUSOLG0004: "キーワード入力欄の入力値が半角英数字ではありません。（GUSOLG0004）",
    GUSOLG0005: "投入日の開始日が未入力です。（GUSOLG0005）",
    GUSOLG0006: "投入日の終了日が未入力です。（GUSOLG0006）",
    GUSOLG0007: "投入日の開始日のフォーマットがyyyymmddではありません。（GUSOLG0007）",
    GUSOLG0008: "投入日の終了日のフォーマットがyyyymmddではありません。（GUSOLG0008）",
    GUSOLG0009: "投入日期間指定の終了日が開始日より過去の日付です。（GUSOLG0009）",
    GUSOLG0010: "完了日の開始日が未入力です。（GUSOLG0010）",
    GUSOLG0011: "完了日の終了日が未入力です。（GUSOLG0011）",
    GUSOLG0012: "完了日の開始日のフォーマットがyyyymmddではありません。（GUSOLG0012）",
    GUSOLG0013: "完了日の終了日のフォーマットがyyyymmddではありません。（GUSOLG0013）",
    GUSOLG0014: "完了日期間指定の終了日が開始日より過去の日付です。（GUSOLG0014）",
    GUSOLG0015: "テナントが存在しません。（GUSOLG0015）",
    GUSOLG0016: "SO一覧情報の取得に失敗しました。（GUSOLG0016）",
    GUSOLG0017: "検索表示件数が検索表示上限件数を超過しています。[検索表示件数 = {0} 検索表示上限件数 = {1}]",
    GUSODG0001: "SO詳細情報の取得に失敗しました。（GUSODG0001）",
    GULPDG0001: "回線プラン基本情報の取得に失敗しました。（GULPDG0001）",
    GULPDG0002: "回線所属テナント情報の取得に失敗しました。（GULPDG0002）",
    GULDEG0001: "回線番号のフォーマットが不正です。（必須、半角数字11桁であること）（GULDEG0001）",
    GULDEG0002: "回線情報取得に失敗しました。（GULDEG0002）",
    GULDEG0003: "現在の利用状況の取得に失敗しました。（GULDEG0003）",
    GULDEG0004: "自テナントのステータスがfalseです。（GULDEG0004）",
    GUUCHG0001: "変更対象テナントレベルの取得に失敗しました。（GUUCHG0001）",
    GUUCHG0002: "変更対象テナントIDは6桁以上10桁以下の範囲で入力して下さい。（GUUCHG0002）",
    GUUCHG0003: "変更対象テナントIDは半角英数字「a-zA-Z0-9」で入力して下さい。（GUUCHG0003）",
    GUUCHG0004: "変更対象テナントIDが存在しません。（GUUCHG0004）",
    GUUCHG0005: "変更対象ユーザ種別が存在しません。（GUUCHG0005）",
    GUUCHG0006: "変更対象ユーザIDは3桁以上16桁以下の範囲で入力して下さい。（GUUCHG0006）",
    GUUCHG0007: "変更対象ユーザIDは半角英数字「a-zA-Z0-9」または記号「-_.」「@(先頭は除く)」の文字列ではありません。（GUUCHG0007）",
    GUUCHG0008: "変更対象ユーザIDが存在しません。（GUUCHG0008）",
    GUUCHG0009: "変更対象ユーザ名は21文字以下で入力して下さい。（GUUCHG0009）",
    GUUCHG0010: "変更対象新パスワードは8桁以上16桁以下の範囲で入力して下さい。（GUUCHG0010）",
    GUUCHG0011: "変更対象新パスワードは半角英数字「a-zA-Z0-9」または記号「!”#$%&’()=~|@[;:],./’'{+*'}<>?_」を使って入力して下さい。（GUUCHG0011）",
    GUUCHG0012: "過去３世代パスワードは再利用できません。（GUUCHG0012）",
    GUUCHG0013: "パスワードがパスワード(再入力)と一致しません。（GUUCHG0013）",
    GUUCHG0014: "ログインユーザ種別の取得に失敗しました。（GUUCHG0014）",
    GUUCHG0015: "ログインユーザ種別は「スーパーユーザ」もしくは「代表ユーザ」でないため、操作できません。（GUUCHG0015）",
    GUUCHG0016: "ログインユーザテナントレベルの取得に失敗しました。（GUUCHG0016）",
    GUUCHG0017: "「変更対象ユーザID：{0}」を変更することができません。ログインユーザ種別：{1}変更対象ユーザ種別：{2}（GUUCHG0017）",
    GUUCHG0018: "ユーザ情報の変更に成功しました。",
    GULSUG0001: "キーワード入力欄の入力値が半角数字ではありません。（GULSUG0001）",
    GULSUG0002: "キーワード入力欄の入力値を1桁以上11桁以下の範囲で入力して下さい。（GULSUG0002）",
    GULSUG0003: "開通日の開始日が未入力です。（GULSUG0003）",
    GULSUG0004: "開通日の終了日が未入力です。（GULSUG0004）",
    GULSUG0005: "開通日の開始日のフォーマットがyyyymmddではありません。（GULSUG0005）",
    GULSUG0006: "開通日の終了日のフォーマットがyyyymmddではありません。（GULSUG0006）",
    GULSUG0007: "開通日期間指定の終了日が開始日より過去の日付です。（GULSUG0007）",
    GULSUG0008: "テナントのステータスがfalseです。（GULSUG0008）",
    GULSUG0009: "検索表示件数が検索表示上限件数を超過しています。[検索表示件数 = {0} 検索表示上限件数 = {1}]",
    GULSUG0010: "検索対象回線が不正です。（GULSUG0010）",
    GUUDEG0001: "削除対象ユーザ種別の取得に失敗しました。（GUUDEG0001）",
    GUUDEG0002: "削除対象テナントレベルの取得に失敗しました。（GUUDEG0002）",
    GUUDEG0003: "削除対象ユーザIDが存在しません。（GUUDEG0003）",
    GUUDEG0004: "ログインユーザ種別の取得に失敗しました。（GUUDEG0004）",
    GUUDEG0005: "ログインユーザ種別が「スーパーユーザ」もしくは「代表ユーザ」でないため、操作できません。（GUUDEG0005）",
    GUUDEG0006: "ログインユーザテナントレベルの取得に失敗しました。（GUUDEG0006）",
    GUUDEG0007: "「削除対象ユーザID：{0}」を削除することができません。ログインユーザ種別：{1} 削除対象ユーザ種別：{2}（GUUDEG0007）",
    GUUDEG0008: "削除対象ユーザをDBから削除に失敗しました。（GUUDEG0008）",
    GUUDEG0009: "ユーザ情報の削除に成功しました。",
    GUUADG0001: "ログインユーザ種別の取得に失敗しました。（GUUADG0001）",
    GUUADG0002: "ログインユーザ種別が「スーパーユーザ」もしくは「代表ユーザ」でないため、操作できません。（GUUADG0002）",
    GUUADG0003: "追加対象テナントIDを6桁以上10桁以下の範囲で入力して下さい。（GUUADG0003）",
    GUUADG0004: "追加対象テナントIDは半角英数字「a-zA-Z0-9」で入力して下さい。（GUUADG0004）",
    GUUADG0005: "追加対象テナントIDが存在しません。（GUUADG0005）",
    GUUADG0006: "追加対象ユーザ種別が存在しません。（GUUADG0006）",
    GUUADG0007: "追加対象ユーザIDを3桁以上16桁以下の範囲で入力して下さい。（GUUADG0007）",
    GUUADG0008: "追加対象ユーザIDは半角英数字「a-zA-Z0-9」または記号「-_.」「@(先頭は除く)」を使って入力して下さい。（GUUADG0008）",
    GUUADG0009: "追加対象ユーザIDが既に存在しています。（GUUADG0009）",
    GUUADG0010: "追加対象ユーザ名は21文字以下で入力して下さい。（GUUADG0010）",
    GUUADG0011: "追加対象パスワードを8桁以上16桁以下の範囲で入力して下さい。（GUUADG0011）",
    GUUADG0012: "追加対象パスワードは半角英数字「a-zA-Z0-9」または記号「!”#$%&’()=~|@[;:],./’'{+*'}<>?_」を使って入力して下さい。（GUUADG0012）",
    GUUADG0013: "追加対象パスワードにユーザIDが含まれています。（GUUADG0013）",
    GUUADG0014: "パスワードがパスワード(再入力)と一致しません。（GUUADG0014）",
    GUUADG0015: "追加対象ユーザ種別は「代表ユーザ」ですが、追加対象テナント内に「代表ユーザ」が既に存在します。（GUUADG0015）",
    GUUADG0016: "ログインユーザテナントレベルの取得に失敗しました。（GUUADG0016）",
    GUUADG0017: "「追加対象ユーザID：{0}」を追加することができません。ログインユーザ種別：{1}追加対象ユーザ種別：{2}（GUUADG0017）",
    GUUADG0018: "ユーザ情報の新規登録に成功しました。",
    GUAING0001: "ログインユーザ種別の取得に失敗しました。（GUAING0001）",
    GUAING0002: "テナント情報リストの取得に失敗しました。（GUAING0002）",
    GUAING0003: "対象テナント所属N番がDB上に1件も存在しません。（GUAING0003）",
    GUAING0004: "契約情報が取得できません。（GUAING0004）",
    GUUSUG0001: "ログインユーザ種別の取得に失敗しました。（GUUSUG0001）",
    GUUSUG0002: "ログインユーザ種別は「スーパーユーザ」もしくは「代表ユーザ」でないため、操作できません。（GUUSUG0002）",
    GUSICG0101: "オプションプラン情報の取得に失敗しました。（GUSICG0101）",
    GUSICG0102: "回線所属テナント情報の取得に失敗しました。（GUSICG0102）",
    GUSICG0103: "回線クーポン追加が成功しました。",
    GUSICG0401: "現回線プラン情報取得に失敗しました。（GUSICG0401）",
    GUSICG0402: "変更先回線プラン情報取得に失敗しました。（GUSICG0402）",
    GUSICG0501: "オーダ種別の取得に失敗しました。（GUSICG0501）",
    GUSICG0503: "予約キャンセルに成功しました。",


    APCOMW0001: "{0}でエラーが発生しました。",
    SWOCIW0001: "システムプロパティファイルを取得できない。",
    SWOCIW0002: "代表N番[{0}]のフォーマットチェックエラー。",
    SWOCII0001: "処理を開始します。 処理名:{0}",
    SWOCII0002: "処理を終了します。 処理名:{0}",
    SWOCII0003: "受信開始時刻：[{0}]　URL：[{1}]",
    SWOCII0004: "受信完了時刻：[{0}]　標準APIレスポンス：[{1}]",
    SWOCIW0003: "標準API受信でタイムアウトが発生しました。タイムアウト値: {0}秒",
    SWOCIW0004: "標準API受信でエラーが発生しました。",
    SWOCIW0005: "予期せぬエラーが発生した。",
    SWOCIW0006: "オプションサービス情報を取得できません。オプションコード：{0}",
    APSODI0001: "処理を開始します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}",
    APSODI0002: "処理を終了します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}",
    APSODD0001: "処理開始 [{0}:{1}] {2}",
    APSODD0002: "処理終了 [{0}:{1}]",
    APSODW0001: "パラメータ「SO-ID」のフォーマットが不正です。（必須、英数字15桁以下であること）",
    APSODW0002: "テナント階層情報が取得できません。",
    APSODW0003: "SO詳細情報の取得に失敗しました。",
    APLPCW0001: "電文パラメータフォーマットエラー。{0}:[{1}]",
    APLPCW0002: "社内テナント種別、社内フラグが取得できない。",
    APLPCW0003: "回線番号[{0}]を取得できません。",
    APLPCW0004: "回線番号[{0}]がテナントID[{1}]に所属しません。",
    APLPCW0005: "変更前プラン情報のチェックにエラーが発生しました。プランID：{0}",
    APLPCW0006: "変更後プラン情報のチェックにエラーが発生しました。プランID：{0}",
    APLPCW0007: "プラン変更条件チェックにエラーが発生しました。",
    APLPCW0008: "プラン変更実施回数チェックにエラーが発生しました。",
    APLPCW0009: "サービスプランIDが取得できない。",
    APLPCW0010: "TPCにSO投入失敗。",
    APLPCW0011: "回線情報の更新が失敗しました。",
    // APLPCI0001: "処理を開始します。 処理名:{0}",
    // APLPCI0002: "処理を終了します。 処理名:{0}",
    APLPCD0001: "処理開始 [{0}:{1}] {2}",
    APLPCD0002: "処理終了 [{0}:{1}]",
    APSOLI0001: "処理を開始します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}",
    APSOLI0002: "処理を終了します。 処理名:{0} 同時接続数: {1} テナントID:{2} テナント毎同時接続数: {3} API識別ID:{4}",
    APSOLD0001: "処理開始 [{0}:{1}] {2}",
    APSOLD0002: "処理終了 [{0}:{1}]",
    APSOLW0001: "電文パラメータフォーマットエラー。{0}:[{1}]",
    APSOLW0002: "日付チェックエラー。{0}:[{1}]",
    APSOLW0003: "電文パラメータフォーマットエラー。{0}:[{1}]",
    APSOLW0004: "テナント階層情報が取得できない。",
    APSOLW0005: "テナント階層情報に含まれない。",
    APSOLW0006: "SO情報リストの取得が失敗しました。",
    GULSUW0001: "キーワード入力欄の入力値が半角数字ではありません。",
    GULSUW0002: "キーワード入力欄の入力値を1桁以上11桁以下の範囲で入力して下さい。",
    GULSUW0003: "開通日の開始日が未入力です。",
    GULSUW0004: "開通日の終了日が未入力です。",
    GULSUW0005: "開通日の開始日のフォーマットがyyyymmddではありません。[開通日の開始日 = {0}]",
    GULSUW0006: "開通日の終了日のフォーマットがyyyymmddではありません。[開通日の終了日 = {0}]",
    GULSUW0007: "開通日期間指定の終了日が開始日より過去の日付です。[開始日 = {0} 終了日 = {1}]",
    GULSUW0008: "テナントのステータスがfalseです。 [テナントID = {0}]",
    GULSUW0010: "検索対象回線が不正です。[検索対象回線 = {0}]",
    GUAINW0001: "ログインユーザ種別の取得に失敗しました。[ユーザNo = {0}]",
    GUAINW0002: "テナント情報リストの取得に失敗しました。[ユーザID = {0} テナントID = {1}　ユーザ種別 = {2}]",
    GUAINW0003: "対象テナント所属N番がDB上に1件も存在しません。",
    GUAINW0004: "契約情報が取得できません。[代表N番 = {0} テナントID = {1}]",
    GUCOME0001: "セッションの更新に失敗しました。",
    GUCOME0002: "ユーザ権限がありません。[ユーザID = {0}]",
    GUCOME0003: "DBのアクセスに失敗しました。",
    GUCOME0004: "想定外エラーが発生しました。",
    GUULOW0001: "テナントID、ログインIDまたはパスワードの組み合わせが間違ってます。[ユーザID = {0} テナントID = {1}]",
    GUULOW0002: "テナントIDは無効です。[テナントID = {0}]",
    GUULOW0003: "テナントID、ログインIDまたはパスワードの組み合わせが間違ってます。[ユーザID = {0} テナントID = {1}]",
    GUULOW0004: "ユーザIDは無効です。[ユーザID = {0} テナントID = {1}]",
    GUULOW0005: "アカウントステータスの更新に失敗しました。 [ユーザID = {0} テナントID = {1}]",
    GUULOW0006: "パスワードの有効期限が切れました。[ユーザID = {0} テナントID = {1}]",
    GUULOE0008: "テナントで許容するIPアドレス情報のフォーマットが不正です。[テナントID={0}][IPアドレス={1}][サブネットマスク={2}]",
    GUULOW0009: "許可されていない接続元からのアクセスです。[テナントID={0}][接続元IPアドレス={1}]",
    GUPCHW0001: "現在のパスワードが違います。",
    GUPCHW0002: "新パスワードを8桁以上16桁以下の範囲入力して下さい。",
    GUPCHW0003: "新パスワードは半角英数字「a-zA-Z0-9」または記号「!”#$%&’()=~|@[;:],./’'{+*'}<>?_」を使って、8桁以上16桁以下で入力して下さい。",
    GUPCHW0004: "過去３世代パスワードは再利用できません。",
    GUPCHW0005: "パスワードがパスワード(再入力)と一致しません。",
    GUPCHW0006: "パスワード変更対象のユーザ情報の取得に失敗しました。[ユーザID = {0} テナントID = {1}]",
    GUPCHI0007: "パスワードを変更しました。[ユーザID = {0} テナントID = {1}]",
    GUSOLW0001: "キーワード入力欄の入力値を1桁以上11桁以下の範囲で入力して下さい。",
    GUSOLW0002: "キーワード入力欄の入力値が半角数字ではありません。",
    GUSOLW0003: "キーワード入力欄の入力値を1桁以上15桁以下の範囲で入力して下さい。",
    GUSOLW0004: "キーワード入力欄の入力値が半角英数字ではありません。",
    GUSOLW0005: "投入日の開始日が未入力です。",
    GUSOLW0006: "投入日の終了日が未入力です。",
    GUSOLW0007: "投入日の開始日のフォーマットがyyyymmddではありません。[投入日の開始日 = {0}]",
    GUSOLW0008: "投入日の終了日のフォーマットがyyyymmddではありません。[投入日の終了日 = {0}]",
    GUSOLW0009: "投入日期間指定の終了日が開始日より過去の日付です。[開始日 = {0} 終了日 = {1}]",
    GUSOLW0010: "完了日の開始日が未入力です。",
    GUSOLW0011: "完了日の終了日が未入力です。",
    GUSOLW0012: "完了日の開始日のフォーマットがyyyymmddではありません。[完了日の開始日 = {0}]",
    GUSOLW0013: "完了日の終了日のフォーマットがyyyymmddではありません。[完了日の終了日 = {0}]",
    GUSOLW0014: "完了日期間指定の終了日が開始日より過去の日付です。[開始日 = {0} 終了日 = {1}]",
    GUSOLW0015: "テナントが存在しません。[テナントID = {0}]",
    GUSOLW0016: "SO一覧情報の取得に失敗しました。[API処理コード = {0}]",
    GUSODW0001: "SO詳細情報の取得に失敗しました。[SO-ID = {0}]",
    GULPDW0001: "回線プラン基本情報の取得に失敗しました。[回線プランID = {0}]",
    GULPDW0002: "回線所属テナント情報の取得に失敗しました。[回線プランID = {0}]",
    GULDEW0001: "回線番号のフォーマットが不正です。（必須、半角数字11桁であること） [回線ID = {0}]",
    GULDEW0002: "回線情報取得に失敗しました。 [回線ID = {0}]",
    GULDEW0003: "現在の利用状況の取得に失敗しました。",
    GULDEW0004: "自テナントのステータスがfalseです。[テナントID = {0}]",
    GUUCHW0001: "変更対象テナントレベルの取得に失敗しました。[変更対象テナントID = {0}]",
    GUUCHW0002: "変更対象テナントIDは6桁以上10桁以下の範囲で入力して下さい。[変更対象テナントID = {0}]",
    GUUCHW0003: "変更対象テナントIDは半角英数字「a-zA-Z0-9」で入力して下さい。[変更対象テナントID = {0}]",
    GUUCHW0004: "変更対象テナントIDが存在しません。[変更対象テナントID = {0}]",
    GUUCHW0005: "変更対象ユーザ種別が存在しません。[変更対象ユーザ種別 = {0}]",
    GUUCHW0006: "変更対象ユーザIDは3桁以上16桁以下の範囲で入力して下さい。[変更対象ユーザID = {0}]",
    GUUCHW0007: "変更対象ユーザIDは半角英数字「a-zA-Z0-9」または記号「-_.」「@(先頭は除く)」の文字列ではありません。[変更対象ユーザID = {0}]",
    GUUCHW0008: "変更対象ユーザIDが存在しません。[変更対象テナントID = {0} 変更対象ユーザID = {1}]",
    GUUCHW0009: "変更対象ユーザ名は21文字以下で入力して下さい。[変更対象ユーザ名 = {0}]",
    GUUCHW0010: "変更対象新パスワードは8桁以上16桁以下の範囲で入力して下さい。",
    GUUCHW0011: "変更対象新パスワードは半角英数字「a-zA-Z0-9」または記号「!”#$%&’()=~|@[;:],./’'{+*'}<>?_」を使って入力して下さい。",
    GUUCHW0012: "過去３世代パスワードは再利用できません。",
    GUUCHW0013: "パスワードがパスワード(再入力)と一致しません。",
    GUUCHW0014: "ログインユーザ種別の取得に失敗しました。[ユーザNo = {0}]",
    GUUCHW0015: "ログインユーザ種別は「スーパーユーザ」もしくは「代表ユーザ」でないため、操作できません。[ユーザNo = {0} ユーザ種別 = {1}]",
    GUUCHW0016: "ログインユーザテナントレベルの取得に失敗しました。[ユーザNo = {0}]",
    GUUCHW0017: "「変更対象ユーザID：{0}」を変更することができません。ログインユーザ種別：{1}変更対象ユーザ種別：{2}",
    GUUDEW0001: "削除対象ユーザ種別の取得に失敗しました。[削除対象テナントID = {0} 削除対象ユーザID = {1}]",
    GUUDEW0002: "削除対象テナントレベルの取得に失敗しました。[削除対象テナントID = {0}]",
    GUUDEW0003: "削除対象ユーザIDが存在しません。[削除対象テナントID = {0} 削除対象ユーザID = {1}]",
    GUUDEW0004: "ログインユーザ種別の取得に失敗しました。[ユーザNo = {0}]",
    GUUDEW0005: "ログインユーザ種別は「スーパーユーザ」もしくは「代表ユーザ」でないため、操作できません。[ユーザNo = {0} ユーザ種別 = {1}]",
    GUUDEW0006: "ログインユーザテナントレベルの取得に失敗しました。[ユーザNo = {0}]",
    GUUDEW0007: "「削除対象ユーザID：{0}」を削除することができません。ログインユーザ種別：{1}削除対象ユーザ種別：{2}",
    GUUDEW0008: "削除対象ユーザをDBから削除に失敗しました。[削除対象テナントID = {0} 削除対象ユーザID = {1}]",
    GUUADW0001: "ログインユーザ種別の取得に失敗しました。[ユーザNo = {0}]",
    GUUADW0002: "ログインユーザ種別は「スーパーユーザ」もしくは「代表ユーザ」でないため、操作できません。[ユーザNo = {0} ユーザ種別 = {1}]",
    GUUADW0003: "追加対象テナントIDを6桁以上10桁以下の範囲で入力して下さい。[追加対象テナントID = {0}]",
    GUUADW0004: "追加対象テナントIDは半角英数字「a-zA-Z0-9」で入力して下さい。[追加対象テナントID = {0}]",
    GUUADW0005: "追加対象テナントIDが存在しません。[追加対象テナントID = {0}]",
    GUUADW0006: "追加対象ユーザ種別が存在しません。[追加対象ユーザ種別 = {0}]",
    GUUADW0007: "追加対象ユーザIDを3桁以上16桁以下の範囲で入力して下さい。",
    GUUADW0008: "追加対象ユーザIDは半角英数字「a-zA-Z0-9」または記号「-_.」「@(先頭は除く)」を使って入力して下さい。",
    GUUADW0009: "追加対象ユーザIDが既に存在しています。[追加対象テナントID = {0} 追加対象ユーザID = {1}]",
    GUUADW0010: "追加対象ユーザ名は21文字以下で入力して下さい。",
    GUUADW0011: "追加対象パスワードを8桁以上16桁以下の範囲で入力して下さい。",
    GUUADW0012: "追加対象パスワードは半角英数字「a-zA-Z0-9」または記号「!”#$%&’()=~|@[;:],./’'{+*'}<>?_」を使って入力して下さい。",
    GUUADW0013: "追加対象パスワードにユーザIDが含まれています。",
    GUUADW0014: "パスワードがパスワード(再入力)と一致しません。",
    GUUADW0015: "追加対象ユーザ種別は「代表ユーザ」ですが、追加対象テナント内に「代表ユーザ」が既に存在します。[追加対象テナントID = {0} 追加対象ユーザID = {1} 追加対象ユーザ種別 = {2}]",
    GUUADW0016: "ログインユーザテナントレベルの取得に失敗しました。[ユーザNo = {0}]",
    GUUADW0017: "「追加対象ユーザID：{0}」を追加することができません。ログインユーザ種別：{1}追加対象ユーザ種別：{2}",
    GUUSUE0001: "ログインユーザ種別の取得に失敗しました。[ユーザNo = {0}]",
    GUUSUE0002: "ログインユーザ種別は「スーパーユーザ」もしくは「代表ユーザ」でないため、操作できません。[ユーザNo = {0} ユーザ種別 = {1}]",
    GUSICG0201: "回線所属テナント情報の取得に失敗しました。 （GUSICG0201）",
    GUSICG0202: "回線クーポンオン・オフが正常に終了しました。",
    GUSICG0301: "回線所属テナント情報の取得に失敗しました。 （GUSICG0301）",
    GUSICG0302: "SO実行が正常に終了しました。",
    GUSICG0303: "SO実行が失敗しました。（GUSICG0303）",
    GUSICG0403: "回線所属テナント情報の取得に失敗しました。 （GUSICG0403）",
    GUSICG0404: "回線プラン変更が正常に終了しました。",
    GUSICW0101: "オプションプラン情報の取得に失敗しました。「オプションプランID = {0}」",
    GUSICW0102: "回線所属テナント情報の取得に失敗しました。「回線プランID = {0}」",
    GUSICW0201: "回線所属テナント情報の取得に失敗しました。「回線プランID = {0}」",
    GUSICW0301: "回線所属テナント情報の取得に失敗しました。「回線プランID = {0}」",
    GUSICW0401: "現回線プラン情報取得に失敗敗しました。「現回線プランID = {0}」",
    GUSICW0402: "変更先回線プラン情報取得に失敗しました。「変更先回線プランID = {0}」",
    GUSICW0403: "回線所属テナント情報の取得に失敗しました。「回線プランID = {0}」",
    GUULOI0001: "ログインに成功しました。 [ユーザID = {0} テナントID = {1}]",
    GUULOI0002: "ログイン画面を表示しました。",
    GUCOMI0001: "ホーム画面を表示しました。",
    GUCOMI0002: "ログアウトに成功しました。",
    GUPCHI0001: "パスワード変更画面を表示しました。",
    GUSLII0001: "ヘッダ画面を表示しました。",
    GUSLII0002: "回線検索モード要求 [回線ID = {0}]",
    GUSLII0003: "回線検索モード応答 [件数 = {0}]",
    GUSSOI0001: "SO検索モード要求 [SOID = {0}]",
    GUSSOI0002: "SO検索モード応答 [件数 = {0}]",
    GULSUI0001: "回線一覧画面を表示しました。",
    GULDEI0001: "回線詳細画面を表示しました。",
    GULDEI0002: "回線詳細要求 [回線ID= {0}]",
    GULPDI0001: "回線プラン詳細画面を表示しました。",
    GULPDI0002: "回線プラン要求 [回線プラン名= {0}]",
    GUSICI0001: "SO投入画面を表示しました。",
    GUSICI0002: "SO投入を行いました。",
    GUSICI0003: "アクティベート/ディアクティベート投入画面を表示しました。",
    GUSICI0004: "アクティベート/ディアクティベート投入を行いました。",
    GUSIRI0001: "アクティベート/ディアクティベート投入結果画面を表示しました。",
    GUSOLI0001: "SO一覧画面を表示しました。",
    GUSODI0001: "SO詳細画面を表示しました。",
    GUSODI0002: "SO詳細要求 [SOID= {0}]",
    GUAINI0001: "契約情報画面を表示しました。[テナントID = {0} N番 = {1}]",
    GUUSUI0001: "ユーザ一覧画面を表示しました。",
    GUUADI0001: "ユーザ追加画面を表示しました。",
    GUUADI0002: "ユーザが追加されました。[ユーザID = {0} テナントID = {1}]",
    GUUCHI0001: "ユーザ変更画面を表示しました。",
    GUUCHI0002: "ユーザが変更されました。[ユーザID = {0} テナントID = {1}]",
    GUUDEI0001: "ユーザ削除画面を表示しました。",
    GUUDEI0002: "ユーザが削除されました。[削除対象{0}]",
    GUSICG0405: "プラン情報タイミング取得に失敗しました。 （GUSICG0405）",
    GUSICW0405: "プラン情報タイミング取得に失敗しました。「変更先回線プランID = {0}」",
    GUSICW0501: "オーダ種別の取得に失敗しました。「SO-ID = {0}」",
    GUDLSI0001: "廃止回線検索モード要求 [回線ID = {0}]",
    GUDLSI0002: "廃止回線検索モード応答 [件数 = {0}]",
    GUDLSI0003: "回線一覧（廃止）画面を表示しました。",
    GUDLSW0001: "キーワード入力欄の入力値が半角数字ではありません。",
    GUDLSW0002: "キーワード入力欄の入力値を1桁以上11桁以下の範囲で入力して下さい。",
    GUDLSW0003: "開通日の開始日が未入力です。",
    GUDLSW0004: "開通日の終了日が未入力です。",
    GUDLSW0005: "開通日の開始日のフォーマットがyyyymmddではありません。[開通日の開始日 = {0}]",
    GUDLSW0006: "開通日の終了日のフォーマットがyyyymmddではありません。[開通日の終了日 = {0}]",
    GUDLSW0007: "開通日期間指定の終了日が開始日より過去の日付です。[開始日 = {0} 終了日 = {1}]",
    GUDLSW0008: "テナントのステータスがfalseです。 [テナントID = {0}]",
    GUDLSW0010: "検索対象回線が不正です。[検索対象回線 = {0}]",
    GUDLSG0001: "キーワード入力欄の入力値が半角数字ではありません。（GUDLSG0001）",
    GUDLSG0002: "キーワード入力欄の入力値を1桁以上11桁以下の範囲で入力して下さい。（GUDLSG0002）",
    GUDLSG0003: "開通日の開始日が未入力です。（GUDLSG0003）",
    GUDLSG0004: "開通日の終了日が未入力です。（GUDLSG0004）",
    GUDLSG0005: "開通日の開始日のフォーマットがyyyymmddではありません。（GUDLSG0005）",
    GUDLSG0006: "開通日の終了日のフォーマットがyyyymmddではありません。（GUDLSG0006）",
    GUDLSG0007: "開通日期間指定の終了日が開始日より過去の日付です。（GUDLSG0007）",
    GUDLSG0008: "テナントのステータスがfalseです。（GUDLSG0008）",
    GUDLSG0010: "検索対象回線が不正です。",
    GUDLSG0009: "検索表示件数が検索表示上限件数を超過しています。[検索表示件数 = {0} 検索表示上限件数 = {1}]",
    GUDLDG0001: "回線番号のフォーマットが不正です。（必須、半角数字11桁であること）（GUDLDG0001）",
    GUDLDG0002: "created_atのフォーマットが不正です。（必須、時刻であること）（GUDLDG0002）",
    GUDLDG0003: "廃止回線情報を一意に取得できませんでした。（GUDLDG0003）",
    GUDLDG0004: "自テナントのステータスがfalseです。(GUDLDG0004)",
    GUDLDI0001: "廃止回線詳細要求 [回線ID= {0}]",
    GUDLDI0002: "回線詳細（廃止）画面を表示しました。",
    GUDLDW0001: "回線番号のフォーマットが不正です。（必須、半角数字11桁であること） [回線ID = {0}]",
    GUDLDW0002: "created_atのフォーマットが不正です。（必須、時刻であること） [created_at = {0}]",
    GUDLDW0003: "廃止回線情報を一意に取得できない。 [回線ID = {0} created_at = {1} 取得した廃止回線情報の件数 = {2}]",
    GUDLDW0004: "テナントのステータスがfalseです。[テナントID = {0}]",
};

export default MessageProperties;
