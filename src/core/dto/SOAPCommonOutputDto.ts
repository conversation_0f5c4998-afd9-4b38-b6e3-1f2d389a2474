export default class SOAPCommonOutputDto {
    /** 返信DOC */
    private doc: Document;

    /** 処理コード */
    private processCode: string;

    /** 200以外エラーフラグ */
    private _isError: boolean = false;

    public getDoc(): Document {
        return this.doc;
    }

    public setDoc(doc: Document): void {
        this.doc = doc;
    }

    public getProcessCode(): string {
        return this.processCode;
    }

    public setProcessCode(processCode: string): void {
        this.processCode = processCode;
    }

    public isError(): boolean {
        return this._isError;
    }

    public setError(isError: boolean): void {
        this._isError = isError;
    }
}
