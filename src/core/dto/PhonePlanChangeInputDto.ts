import SOManagerInputDto from "./SOManagerInputDto";

export default interface PhonePlanChangeInputDto extends SOManagerInputDto {
    // /** 入力情報共通 */
    // private RequestHeader requestHeader;
    // requestHeader: RequestHeader; // NOTE moved to SOManagerInputDto
    // /** テナントID */
    // private String tenantId;
    // tenantId: string; // NOTE moved to SOManagerInputDto
    // /** 対象回線の所属テナントID */
    // private String targetTenantId;
    targetTenantId: string;
    // /** 回線番号 */
    // private String lineNo;
    lineNo: string;
    // /** 0035でんわプランID */
    // private String voicePlanId;
    voicePlanId: string;
}
