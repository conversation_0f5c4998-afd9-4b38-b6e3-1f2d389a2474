import LineInfoAcquisitionCase1InsideOutputDto from "./LineInfoAcquisitionCase1InsideOutputDto";
import LineInfoAcquisitionCase1OutputDto from "./LineInfoAcquisitionCase1OutputDto";
import LineInfoAcquisitionCase1VersionOutputDto from "./LineInfoAcquisitionCase1VersionOutputDto";
import LineInfoAcquisitionCase1Version2OutputDto from "./LineInfoAcquisitionCase1Version2OutputDto";
import LineInfoAcquisitionCase2InsideOutputDto from "./LineInfoAcquisitionCase2InsideOutputDto";
import LineInfoAcquisitionCase2OutputDto from "./LineInfoAcquisitionCase2OutputDto";
import LineInfoAcquisitionCase2VersionOutputDto from "./LineInfoAcquisitionCase2VersionOutputDto";
import LineInfoAcquisitionCase2Version2OutputDto from "./LineInfoAcquisitionCase2Version2OutputDto";
import LineInfoAcquisitionCase3InsideOutputDto from "./LineInfoAcquisitionCase3InsideOutputDto";
import LineInfoAcquisitionCase3OutputDto from "./LineInfoAcquisitionCase3OutputDto";
import LineInfoAcquisitionCase3VersionOutputDto from "./LineInfoAcquisitionCase3VersionOutputDto";
import LineInfoAcquisitionCase3Version2OutputDto from "./LineInfoAcquisitionCase3Version2OutputDto";

type LineInfoAcquisitionBaseOutputDto =
LineInfoAcquisitionCase1InsideOutputDto |
LineInfoAcquisitionCase2InsideOutputDto |
LineInfoAcquisitionCase3InsideOutputDto |
LineInfoAcquisitionCase1OutputDto |
LineInfoAcquisitionCase2OutputDto |
LineInfoAcquisitionCase3OutputDto |
LineInfoAcquisitionCase1VersionOutputDto |
LineInfoAcquisitionCase2VersionOutputDto |
LineInfoAcquisitionCase3VersionOutputDto |
LineInfoAcquisitionCase1Version2OutputDto |
LineInfoAcquisitionCase2Version2OutputDto |
LineInfoAcquisitionCase3Version2OutputDto;

export default LineInfoAcquisitionBaseOutputDto;