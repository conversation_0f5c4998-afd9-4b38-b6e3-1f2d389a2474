import SOManagerInputDto from "./SOManagerInputDto";

export default interface LineGroupPlanChangeInputDto extends SOManagerInputDto {
    /** テナントID */
    // private String tenantId;
    tenantId: string;

    /** 回線グループID */
    // private String lineGroupId;
    lineGroupId: string;

    /** 変更前卸ポータルグループプランID */
    // private String potalGroupPlanID_pre;
    potalGroupPlanID_pre: string;

    /** 変更後卸ポータルグループプランID */
    // private String potalGroupPlanID;
    potalGroupPlanID: string;

    /** 予約日 */
    // private String reserve_date;
    reserve_date: string;
}
