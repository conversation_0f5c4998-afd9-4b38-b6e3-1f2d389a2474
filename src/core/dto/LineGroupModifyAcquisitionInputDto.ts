import SOManagerInputDto from "./SOManagerInputDto";

export default interface LineGroupModifyAcquisitionInputDto extends SOManagerInputDto {
    /** テナントID */
    tenantId: string;

    /** 回線番号 */
    lineNo: string;

    /** 操作  */
    operation: string;

    /** 回線グループID */
    lineGroupId: string;

    /** 回線総量制限サービス可否 */
    totalVolumeControlFlag: string;

    /** 予約日 */
    reserve_date: string;

    /** MVNO顧客CSV連携不要フラグ */
    csvUnnecessaryFlag: string;
}