import SOManagerInputDto from "./SOManagerInputDto";

export default interface LineSuspendInputDto extends SOManagerInputDto {
    /** 入力情報共通 */
    // RequestHeader requestHeader;

    /** テナントID */
    // private String tenantId;

    /** 対象回線の所属テナントID */
    // private String targetTenantId;
    targetTenantId: string;

    /** 回線番号 */
    // private String lineNo;
    lineNo: string;

    /** 利用状態変更フラグ */
    // private String suspendFlag;
    suspendFlag: string;
}
