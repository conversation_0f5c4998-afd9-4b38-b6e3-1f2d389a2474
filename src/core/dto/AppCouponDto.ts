/**
 * アプリクーポンDtoクラス
 */
export default interface AppCouponDto {
    /** アプリクーポン容量 */
    aplCouponCapacity: string; // = ""

    /** アプリクーポン使用量 */
    aplCouponUse: string; // = ""

    /** アプリクーポン残容量 */
    aplCouponRemainCapacity: string; // = ""

    /** アプリクーポン有効期限 */
    aplCouponTermValidity: string; // = ""
}

/**
 * アプリクーポンDtoの初期値
 */
export function getEmpty_AppCoupon(): AppCouponDto {
    return {
        aplCouponCapacity: "",
        aplCouponUse: "",
        aplCouponRemainCapacity: "",
        aplCouponTermValidity: "",
    };
}
