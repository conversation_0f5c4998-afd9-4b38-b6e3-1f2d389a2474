export default interface AdditionalCoupon4Group {
    /** 追加クーポン4(グループ)容量 */
    additionalCoupon4Capacity: string; // = "";

    /** 追加クーポン4(グループ)使用量 */
    additionalCoupon4Use: string; // = "";
    // STEP16.0版対応　追加　END

    /** 追加クーポン4(グループ)残容量 */
    additionalCoupon4RemainCapacity: string; // = "";

    /** 追加クーポン4(グループ)有効期限 */
    additionalCoupon4TermValidity: string; // = "";
}

/**
 * 追加クーポン4(グループ)Dtoの初期値
 */
export function getEmpty_AdditionalCoupon4Group(): AdditionalCoupon4Group {
    return {
        additionalCoupon4Capacity: "",
        additionalCoupon4Use: "",
        additionalCoupon4RemainCapacity: "",
        additionalCoupon4TermValidity: "",
    };
}
