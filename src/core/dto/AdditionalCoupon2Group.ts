export default interface AdditionalCoupon2Group {
    /** 追加クーポン2(グループ)容量 */
    additionalCoupon2Capacity: string; // = "";

    /** 追加クーポン2(グループ)使用量 */
    additionalCoupon2Use: string; // = "";
    // STEP16.0版対応　追加　END

    /** 追加クーポン2(グループ)残容量 */
    additionalCoupon2RemainCapacity: string; // = "";

    /** 追加クーポン2(グループ)有効期限 */
    additionalCoupon2TermValidity: string; // = "";
}

/**
 * 追加クーポン2(グループ)Dtoの初期値
 */
export function getEmpty_AdditionalCoupon2Group(): AdditionalCoupon2Group {
    return {
        additionalCoupon2Capacity: "",
        additionalCoupon2Use: "",
        additionalCoupon2RemainCapacity: "",
        additionalCoupon2TermValidity: "",
    };
}