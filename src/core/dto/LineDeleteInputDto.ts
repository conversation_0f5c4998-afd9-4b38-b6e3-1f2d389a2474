import SOManagerInputDto from "./SOManagerInputDto";

export default interface LineDeleteInputDto extends SOManagerInputDto {
    // /** 入力情報共通 */
    // private RequestHeader requestHeader;
    // requestHeader: RequestHeader; // NOTE moved to SOManagerInputDto
    // /** テナントID */
    // private String tenantId;
    // tenantId: string; // NOTE moved to SOManagerInputDto
    // /** 対象回線の所属テナントID */
    // private String targetTenantId;
    targetTenantId: string;
    // /** 回線番号 */
    // private String lineNo;
    lineNo: string;
    // /** MNP転出フラグ */
    // private String mnpOutFlag;
    mnpOutFlag: string;
    // /** 予約日 */
    // private String reserve_date;
    reserve_date: string;
    // /** MVNO顧客CSV連携不要フラグ */
    // private String csvUnnecessaryFlag;
    csvUnnecessaryFlag: string;
}
