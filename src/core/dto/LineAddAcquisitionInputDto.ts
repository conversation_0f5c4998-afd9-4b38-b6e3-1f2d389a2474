import SOManagerInputDto from "./SOManagerInputDto";

export default interface LineAddAcquisitionInputDto
    extends SOManagerInputDto {
    /** 卸ポータルプランID */
    // private String potalPlanID;
    potalPlanID: string;

    /** 回線番号 */
    // private String lineNo;
    lineNo: string;

    /** オプションプランID */
    // private String optionPlanId;
    optionPlanId: string;

    /** STEP1.2b版対応　追加　START */
    /** 予約日 */
    // private String reserve_date;
    reserve_date: string;
    /** STEP1.2b版対応　追加　END */
}