import OptionUpdate from "./OptionUpdate";
import SOManagerInputDto from "./SOManagerInputDto";

export default interface OptionNwpassModifyInputDto extends SOManagerInputDto {
    /** 対象回線の所属テナントID */
    // private String targetTenantId;
    targetTenantId: string;

    /** 回線番号 */
    // private String lineNo;
    lineNo: string;

    /** NW暗証番号変更 */
    // private String pinChange;
    pinChange: string;

    /** 国際ローミング利用限度額 */
    // private OptionUpdate intlRoaming;
    intlRoaming: OptionUpdate;

    /** 留守番でんわ */
    // private OptionUpdate voicemail;
    voicemail: OptionUpdate;

    /** キャッチホン */
    // private OptionUpdate callWaiting;
    callWaiting: OptionUpdate;

    /** 国際電話 */
    // private OptionUpdate intlCall;
    intlCall: OptionUpdate;

    /** 転送でんわ */
    // private OptionUpdate forwarding;
    forwarding: OptionUpdate;

    /** 国際着信転送 */
    // private OptionUpdate intlForwarding;
    intlForwarding: OptionUpdate;

    /** MVNO顧客CSV連携不要フラグ */
    // private String csvUnnecessaryFlag;
    csvUnnecessaryFlag: string;
}
