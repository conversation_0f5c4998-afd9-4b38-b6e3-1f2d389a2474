import GiftCoupon1Dto from "./GiftCoupon1Dto";
import GiftCoupon2Dto from "./GiftCoupon2Dto";

export default interface LineUseInfoGiftCouponDto {
    /** 譲渡クーポン1 */
    giftCoupon1: GiftCoupon1Dto;

    /** 譲渡クーポン2 */
    giftCoupon2: GiftCoupon2Dto;
}

/**
 * LineUseInfoGiftCouponDtoの初期値
 */
export function getEmpty_LineUseInfoGiftCoupon(): LineUseInfoGiftCouponDto {
    return {
        giftCoupon1: null,
        giftCoupon2: null,
    };
}
