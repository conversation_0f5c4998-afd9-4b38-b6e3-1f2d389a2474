import SOManagerInputDto from "@/core/dto/SOManagerInputDto";

export default interface LineGroupAddAcquisitionInputDto extends SOManagerInputDto {
    /** テナントID */
    // private String tenantId;

    /** 回線グループID */
    lineGroupId: string;

    /** 卸ポータルグループプランID */
    // private String potalGroupPlanID;
    potalGroupPlanID: string;

    /** 卸ポータルオプショングループプランID */
    optionGroupPlanId: string;

    /** STEP4.0版対応　追加　START */
    /** 課金対象回線番号 */
    // @JsonProperty("AccountingLineNo")
    AccountingLineNo: string;

    /** STEP4.0版対応　追加　END */

    /** STEP1.2b版対応　追加　START */
    /** 予約日 */
    reserve_date: string;
}