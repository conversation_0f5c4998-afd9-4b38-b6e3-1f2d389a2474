
import SOManagerInputDto from "./SOManagerInputDto";

export default interface PreLineAddInputDto extends SOManagerInputDto {
    /** 入力情報共通 */
    // RequestHeader requestHeader;

    /** テナントID */
    // tenantId;

    /** 回線追加先テナントID */
    targetTenantId: string;

    /** 代表N番 */
    nBan: string;

    /** 回線番号 */
    lineNo: string;

    /** 卸ポータルプランID */
    potalPlanID: string;

    /** 国際ローミング利用限度額ID */
    id_intlRoaming: string;

    /** 留守番でんわID */
    id_voicemail: string;

    /** キャッチホンID */
    id_callWaiting: string;

    /** 国際電話ID */
    id_intlCall: string;

    /** 転送でんわID */
    id_forwarding: string;

    /** 国際着信転送ID */
    id_intlForwarding: string;

    /** SIM番号(DN番号) */
    sim_no: string;

    /** SIM種別 */
    sim_type: string;

    // STEP17.0版対応　追加　START
    /** アクセス方式 */
    access: string;
    // STEP17.0版対応　追加　END

    /** STEP4.0版対応　追加　START */

    /** カード種別ID */
    cardTypeId: string;

    /** PSID */
    psid: string;

    /** MNP転入フラグ */
    mnpInFlag: string;

    /** MVNO顧客CSV連携不要フラグ */
    csvUnnecessaryFlag: string;

    /** STEP4.0版対応　追加　END */

    /** STEP5.0版対応　追加　START */
    /** 予約日 */
    reserve_date: string;

    /** STEP5.0版対応　追加　END */


    // STEP7.0版対応　追加　START
    /** 回線廃止テナントID */
    lineDelTenantId: string;
    // STEP7.0版対応　追加　END

    // STEP15.0版対応　追加　START
    /** IMSI */
    imsi: string;
    /** PUK1 */
    puk1: string;
    /** PUK2 */
    puk2: string;
    /** 利用状態 */
    lineStatus: string;
    // STEP15.0版対応　追加　END
}
