import BaseOutputDto from "./BaseOutputDto";
import ResponseHeader from "./ResponseHeader";

export default interface SODetailInfoConsultOutputDto extends BaseOutputDto {
    jsonBody: {
        responseHeader: ResponseHeader;
        /** SO-ID */
        serviceOrderId: string;
        /** 投入日 */
        order_date: string;
        /** 予約日 */
        reserve_date: string;
        /** 完了日 */
        exec_date: string;
        /** 種別 */
        order_type: string;
        /** ステータス */
        status: string;
        /** 回線番号 */
        lineNo: string;
        /** 内容詳細 */
        content: string;
    };
}