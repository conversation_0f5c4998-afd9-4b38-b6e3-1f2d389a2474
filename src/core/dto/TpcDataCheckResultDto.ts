
export default class TpcDataCheckResultDto {
    /** チェック結果 */
    checkResult: boolean;

    /** 変数名 */
    name: string;

    /** 変数値 */
    value: string;

    /**
     * @return チェック結果
     */
    public getResult(): boolean {
        return this.checkResult;
    }

    /**
     * @param checkResult チェック結果
     */
    public setResult(checkResult: boolean): void {
        this.checkResult = checkResult;
    }

    /**
     * @return 変数名
     */
    public getErrName(): string {
        return this.name;
    }

    /**
     * @param name 変数名
     */
    public setErrName(name: string) {
        this.name = name;
    }

    /**
     * @return 変数値
     */
    public getErrVal(): string {
        return this.value;
    }

    /**
     * @param value 変数値
     */
    public setErrVal(value: string): void {
        this.value = value;
    }
}