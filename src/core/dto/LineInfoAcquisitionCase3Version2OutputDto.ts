import BaseOutputDto from "./BaseOutputDto";
import GroupA3Dto from "./GroupA3Dto";
import GroupBDto from "./GroupBDto";
import GroupCDto from "./GroupCDto";
import ResponseHeader from "./ResponseHeader";

export default interface LineInfoAcquisitionCase3Version2OutputDto extends BaseOutputDto {
    jsonBody: {
        responseHeader: ResponseHeader;
        groupA: GroupA3Dto;
        groupB: GroupBDto;
        groupC: GroupCDto;
    };
}