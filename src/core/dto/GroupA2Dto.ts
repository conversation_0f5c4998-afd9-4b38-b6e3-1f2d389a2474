export default interface GroupA2Dto {
    /** 回線番号 */
    lineNo: string;

    /** テナントID */
    tenantId: string;

    /** 代表N番 */
    nBan: string;

    /** 卸ポータルプランID */
    potalPlanID: string;

    /** 卸ポータルプラン名 */
    potalPlanName: string;

    /** 回線グループID */
    lineGroupID: string;

    /** カード種別名 */
    cardClassificationName: string;

    /** 回線利用開始年月日 */
    lineUseStartDate: string;

    /** 国際ローミング利用限度額 */
    interationalRoamingCreditLine: string;

    /** 契約種別 */
    contractType: string;

    /** 留守番でんわ */
    voicemail: string;

    /** キャッチホン */
    callWaiting: string;

    /** 国際電話 */
    intlCall: string;

    /** 転送でんわ */
    forwarding: string;

    /** 国際着信転送 */
    intlForwarding: string;

    /** 回線登録状態 */
    lineRegStatus: string;

    /** SIMステータス */
    simStatus: string;

    /** 黒化日 */
    activateDate: string;

    /** SIM番号(DN番号) */
    simNo: string;

    /** IMSI */
    imsi: string;

    /** PUK1 */
    puk1: string;

    /** PUK2 */
    puk2: string;
}