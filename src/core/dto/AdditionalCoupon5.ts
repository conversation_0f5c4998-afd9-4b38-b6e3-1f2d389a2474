export default interface AdditionalCoupon5 {
    /** 追加クーポン5残容量 */
    // private String additionalCoupon5RemainCapacity= "";
    additionalCoupon5RemainCapacity: string;

    /** 追加クーポン5有効期限 */
    // private String additionalCoupon5TermValidity= "";
    additionalCoupon5TermValidity: string;
}

/**
 * 追加クーポン5の初期値
 */
export function getEmpty_AdditionalCoupon5(): AdditionalCoupon5 {
    return {
        additionalCoupon5RemainCapacity: "",
        additionalCoupon5TermValidity: "",
    };
}
