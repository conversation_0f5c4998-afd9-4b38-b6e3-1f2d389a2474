export default interface AdditionalCoupon3 {
    /** 追加クーポン3残容量 */
    // private String additionalCoupon3RemainCapacity= "";
    additionalCoupon3RemainCapacity: string;

    /** 追加クーポン3有効期限 */
    // private String additionalCoupon3TermValidity= "";
    additionalCoupon3TermValidity: string;
}

/**
 * 追加クーポン3の初期値
 */
export function getEmpty_AdditionalCoupon3(): AdditionalCoupon3 {
    return {
        additionalCoupon3RemainCapacity: "",
        additionalCoupon3TermValidity: "",
    };
}
