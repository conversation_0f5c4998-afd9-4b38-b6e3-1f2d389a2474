/**
 *  SOAPから返却電文の構造クラス。
 */
export default class ParameterName {
    name: string;
    value: string;

    // simple constructor
    public static new(name: string, value: string) {
        const instance = new ParameterName();
        instance.name = name;
        instance.value = value;
        return instance;
    }

    // getter/setter
    public getName(): string {
        return this.name;
    }

    public setName(name: string): void {
        this.name = name;
    }

    public getValue(): string {
        return this.value;
    }

    public setValue(value: string): void {
        this.value = value;
    }
}
