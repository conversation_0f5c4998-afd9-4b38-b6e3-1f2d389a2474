import AppCoupon1Dto from "./AppCoupon1Dto";
import AppCoupon2 from "./AppCoupon2Dto";
import AppCoupon3Dto from "./AppCoupon3Dto";
import AppCoupon4Dto from "./AppCoupon4Dto";
import AppCoupon5Dto from "./AppCoupon5Dto";
import AppCoupon6Dto from "./AppCoupon6Dto";

export default interface LineUseInfoAppCouponDto {
    /** アプリクーポン1 */
    aplCoupon1: AppCoupon1Dto;
    /** アプリクーポン2 */
    aplCoupon2: AppCoupon2;
    /** アプリクーポン3 */
    aplCoupon3: AppCoupon3Dto;
    /** アプリクーポン4 */
    aplCoupon4: AppCoupon4Dto;
    /** アプリクーポン5 */
    aplCoupon5: AppCoupon5Dto;
    /** アプリクーポン6 */
    aplCoupon6: AppCoupon6Dto;
}

/**
 * LineUseInfoAppCouponDtoの初期値
 */
export function getEmpty_LineUseInfoAppCoupon(): LineUseInfoAppCouponDto {
    return {
        aplCoupon1: null,
        aplCoupon2: null,
        aplCoupon3: null,
        aplCoupon4: null,
        aplCoupon5: null,
        aplCoupon6: null,
    };
}
