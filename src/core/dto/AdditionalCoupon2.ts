export default interface AdditionalCoupon2 {
    /** 追加クーポン2残容量 */
    // private String additionalCoupon2RemainCapacity= "";
    additionalCoupon2RemainCapacity: string;

    /** 追加クーポン2有効期限 */
    // private String additionalCoupon2TermValidity= "";
    additionalCoupon2TermValidity: string;
}

/**
 * 追加クーポン2の初期値
 */
export function getEmpty_AdditionalCoupon2(): AdditionalCoupon2 {
    return {
        additionalCoupon2RemainCapacity: "",
        additionalCoupon2TermValidity: "",
    };
}
