import LineInfoAcquisitionCase2InsideOutputDto from "./LineInfoAcquisitionCase2InsideOutputDto";
import LineInfoAcquisitionCase2OutputDto from "./LineInfoAcquisitionCase2OutputDto";
import LineInfoAcquisitionCase2VersionOutputDto from "./LineInfoAcquisitionCase2VersionOutputDto";
import LineInfoAcquisitionCase2Version2OutputDto from "./LineInfoAcquisitionCase2Version2OutputDto";

type LineInfoAcquisitionCase2BaseOutputDto =
LineInfoAcquisitionCase2InsideOutputDto |
LineInfoAcquisitionCase2OutputDto |
LineInfoAcquisitionCase2VersionOutputDto |
LineInfoAcquisitionCase2Version2OutputDto

export default LineInfoAcquisitionCase2BaseOutputDto;