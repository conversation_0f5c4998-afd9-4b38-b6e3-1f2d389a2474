import SOManagerInputDto from "./SOManagerInputDto";

export default interface NetworkContractChangeInputDto
    extends SOManagerInputDto {
    /** 対象回線の所属テナントID */
    // private String targetTenantId;
    targetTenantId: string;

    /** 回線ID */
    // private String lineNo;
    lineNo: string;

    /** 変更前アクセス方式 */
    // private String access_pre;
    access_pre: string;

    /** 変更後アクセス方式 */
    // private String access;
    access: string;

    /** 変更後カード種別ID */
    // private String cardTypeId;
    cardTypeId: string;

    /** MVNO顧客CSV連携不要フラグ */
    // private String csvUnnecessaryFlag;
    csvUnnecessaryFlag: string;
}
