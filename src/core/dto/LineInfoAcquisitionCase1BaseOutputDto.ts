import LineInfoAcquisitionCase1InsideOutputDto from "./LineInfoAcquisitionCase1InsideOutputDto";
import LineInfoAcquisitionCase1OutputDto from "./LineInfoAcquisitionCase1OutputDto";
import LineInfoAcquisitionCase1VersionOutputDto from "./LineInfoAcquisitionCase1VersionOutputDto";
import LineInfoAcquisitionCase1Version2OutputDto from "./LineInfoAcquisitionCase1Version2OutputDto";

type LineInfoAcquisitionCase1BaseOutputDto =
LineInfoAcquisitionCase1InsideOutputDto |
LineInfoAcquisitionCase1OutputDto |
LineInfoAcquisitionCase1VersionOutputDto |
LineInfoAcquisitionCase1Version2OutputDto;

export default LineInfoAcquisitionCase1BaseOutputDto;