import ResponseHeader from "./ResponseHeader";

export default interface BaseOutputDto {
    //  /** ヘッダー情報 */
    jsonBody: {
        responseHeader: ResponseHeader;
    };
    /**
     * db query result, etc. which will be used in handler function
     */
    additionalData?: {
        /** N番 */
        nNo: string;
        /**
         * 連携不要フラグ
         */
        csvUnnecessary: boolean;
    };
}
