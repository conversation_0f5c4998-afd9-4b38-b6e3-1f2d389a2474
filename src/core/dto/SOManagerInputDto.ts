import RequestHeader from "./RequestHeader";

export default interface SOManagerInputDto {
    /**
     * 卸ポータルフロントの対象SOID
     */
    targetSoId?: string;

    // /** 内部呼出しフラグ */
    // private boolean internal_flag;
    // internal_flag: boolean;

    // /** 予約フラグ(予約実行呼出し専用) */
    // private boolean reserve_flag;
    reserve_flag: boolean;

    // /** 実行ユーザID(内部呼出し専用) */
    // private String executeUserId;
    // executeUserId: string;

    // /** 実行テナントID(内部呼出し専用) */
    // private String executeTenantId;
    // executeTenantId: string;

    // /** SO-ID(予約実行呼出し専用) */
    // private String reserve_soId;
    reserve_soId: string;

    // 「内部呼出し」は使用しておりません、移行は不要
    //  https://mobilus.backlog.jp/view/MVNO_C_F_P-7#comment-237276611Ï

    // NOTE added fields from SOManagerInputDto implementation
    // /** 入力情報共通 */
    // private RequestHeader requestHeader;
    requestHeader: RequestHeader;
    // /** テナントID */
    // private String tenantId;
    tenantId: string;
}
