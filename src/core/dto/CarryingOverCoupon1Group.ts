export default interface CarryingOverCoupon1Group {
    /** 繰越クーポン1(グループ)容量 */
    carryingOverCoupon1Capacity: string; // = "";

    /** 繰越クーポン1(グループ)使用量 */
    carryingOverCoupon1Use: string; // = "";
    // STEP16.0版対応　追加　END

    /** 繰越クーポン1(グループ)残容量 */
    carryingOverCoupon1RemainCapacity: string; // = "";

    /** 繰越クーポン1(グループ)有効期限 */
    carryingOverCoupon1TermValidity: string; // = "";
}

/**
 * 繰越クーポン1(グループ)Dtoの初期値
 */
export function getEmpty_CarryingOverCoupon1Group(): CarryingOverCoupon1Group {
    return {
        carryingOverCoupon1Capacity: "",
        carryingOverCoupon1Use: "",
        carryingOverCoupon1RemainCapacity: "",
        carryingOverCoupon1TermValidity: "",
    };
}
