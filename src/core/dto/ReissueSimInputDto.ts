import SOManagerInputDto from "./SOManagerInputDto";

export default interface ReissueSimInputDto extends SOManagerInputDto {
    /** 対象回線の所属テナントID */
    // private String targetTenantId;
    targetTenantId: string;

    /** 回線番号 */
    // private String lineNo;
    lineNo: string;

    /** SIM番号 */
    // private String sim_no;
    sim_no: string;

    /** SIM種別 */
    // private String sim_type;
    sim_type: string;

    /** カード種別ID */
    // private String cardTypeId;
    cardTypeId: string;

    /** MVNO顧客CSV連携不要フラグ */
    // private String csvUnnecessaryFlag;
    csvUnnecessaryFlag: string;
}
