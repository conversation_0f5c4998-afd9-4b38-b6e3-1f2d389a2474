export default interface CarryingOverCoupon2 {
    /** 繰越クーポン2残容量 */
    // private String carryingOverCoupon2RemainCapacity= "";
    carryingOverCoupon2RemainCapacity: string;

    /** 繰越クーポン2有効期限 */
    // private String carryingOverCoupon2TermValidity= "";
    carryingOverCoupon2TermValidity: string;
}

/**
 * 繰越クーポン2の初期値
 */
export function getEmpty_CarryingOverCoupon2(): CarryingOverCoupon2 {
    return {
        carryingOverCoupon2RemainCapacity: "",
        carryingOverCoupon2TermValidity: "",
    };
}
