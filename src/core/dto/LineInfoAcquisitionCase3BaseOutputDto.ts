import LineInfoAcquisitionCase3InsideOutputDto from "./LineInfoAcquisitionCase3InsideOutputDto";
import LineInfoAcquisitionCase3OutputDto from "./LineInfoAcquisitionCase3OutputDto";
import LineInfoAcquisitionCase3VersionOutputDto from "./LineInfoAcquisitionCase3VersionOutputDto";
import LineInfoAcquisitionCase3Version2OutputDto from "./LineInfoAcquisitionCase3Version2OutputDto";

type LineInfoAcquisitionCase3BaseOutputDto =
LineInfoAcquisitionCase3InsideOutputDto |
LineInfoAcquisitionCase3OutputDto |
LineInfoAcquisitionCase3VersionOutputDto |
LineInfoAcquisitionCase3Version2OutputDto;

export default LineInfoAcquisitionCase3BaseOutputDto;