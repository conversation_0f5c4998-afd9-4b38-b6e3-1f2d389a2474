export default class SOObject {
    /** サービスオーダID */
    private serviceOrderId: string;

    /** 申込日 */
    private orderDate: string;

    /** 完了日 */
    private execDate: string;
    /** STEP1.2b版対応　追加　START */
    /** 予約日 */
    private reserveDate: string;

    /** オーダ種別 */
    private orderType: string;
    /** STEP1.2b版対応　追加　END */
    /** オーダステータス */
    private orderStatus: string;

    /** 回線ID */
    private lineId: string;

    /** 回線グループID */
    private lineGroupId: string;

    /** 所属テナントID */
    private tenantId: string;

    /** 実行ユーザID */
    private executeUserId: string;

    /** 実行テナントID */
    private executeTenantId: string;

    /** 機能種別 */
    private functionType: string;

    /** 操作区分 */
    private operationDivision: string;

    /** 変更前プランID */
    private changeOldplanId: string;

    /** 変更後プランID */
    private changeNewplanId: string;

    /** 回線所属変更回線ID */
    private changePlanId: string;

    /** 基本容量 */
    private basicCapacity: string;

    /** オプションプランID */
    private optionPlanId: string;

    /** STEP4.0版対応　追加　START */
    /** MNP転出フラグ */
    private mnpOutFlag: string = null;
    /** STEP4.0版対応　追加　END */

    /** STEP4.0d版対応　追加　START */
    /** 譲渡先回線番号 */
    private destinationLineNo: string = null;

    /** 譲渡データ量 */
    private giftData: string = null;
    /** STEP4.0d版対応　追加　END */

    /** STEP1.2b版対応　追加　START */
    /** 内部呼び出し */
    private internalFlag: boolean;

    /** REST電文 */
    private restMessage: string;
    /** STEP1.2b版対応　追加　END */

    /** #344対応　追加 START */
    // 回線グループ新規作成フラグ
    private lineGroupCreateFlag: boolean = false;

    // STEP7.0版対応　追加　START
    /** MNP転入フラグ */
    private mnpInFlag: string = null;
    // STEP7.0版対応　追加　END

    // STEP13.0版対応　追加　START
    /** 変更前卸ポータルグループプランID */
    private changeOldGroupPlanId: string;

    /** 変更後卸ポータルグループプランID */
    private changeNewGroupPlanId: string;
    // STEP13.0版対応　追加　END

    // STEP17.0版対応　追加　START
    /** 変更前アクセス方式 */
    private changeOldAccess: string;

    /** 変更後アクセス方式 */
    private changeNewAccess: string;
    // STEP17.0版対応　追加　END

    // STEP21.0版対応　追加　START
    /** 変更後0035でんわプランID */
    private changeNewVoicePlanId: string;
    // STEP21.0版対応　追加　END

    // java code start
    /**
     * @return the lineGroupCreateFlag
     */
    public isLineGroupCreateFlag() {
        return this.lineGroupCreateFlag;
    }

    /**
     * @param lineGroupCreateFlag the lineGroupCreateFlag to set
     */
    public setLineGroupCreateFlag(lineGroupCreateFlag: boolean) {
        this.lineGroupCreateFlag = lineGroupCreateFlag;
    }
    /** #344対応　追加 END */

    /**
     * @return サービスオーダID
     */
    public getServiceOrderId(): string {
        return this.serviceOrderId;
    }

    /**
     * @param serviceOrderId サービスオーダID
     */
    public setServiceOrderId(serviceOrderId: string) {
        this.serviceOrderId = serviceOrderId;
    }

    /**
     * @return 申込日
     */
    public getOrderDate(): string {
        return this.orderDate;
    }

    /**
     * @param orderDate 申込日
     */
    public setOrderDate(orderDate: string) {
        this.orderDate = orderDate;
    }

    /**
     * @return 完了日
     */
    public getExecDate(): string {
        return this.execDate;
    }

    /**
     * @param execDate 完了日
     */
    public setExecDate(execDate: string) {
        this.execDate = execDate;
    }
    /** STEP1.2b版対応　追加　START */
    /**
     * @return 予約日
     */
    public getReserveDate(): string {
        return this.reserveDate;
    }

    /**
     * @param reserveDdate 予約日
     */
    public setReserveDate(reserveDate: string) {
        this.reserveDate = reserveDate;
    }

    /**
     * @return オーダ種別
     */
    public getOrderType(): string {
        return this.orderType;
    }

    /**
     * @param orderType オーダ種別
     */
    public setOrderType(orderType: string) {
        this.orderType = orderType;
    }
    /** STEP1.2b版対応　追加　END */
    /**
     * @return オーダステータス
     */
    public getOrderStatus(): string {
        return this.orderStatus;
    }

    /**
     * @param orderStatus オーダステータス
     */
    public setOrderStatus(orderStatus: string) {
        this.orderStatus = orderStatus;
    }

    /**
     * @return 回線ID
     */
    public getLineId(): string {
        return this.lineId;
    }

    /**
     * @param lineId 回線ID
     */
    public setLineId(lineId: string) {
        this.lineId = lineId;
    }

    /**
     * @return 回線グループID
     */
    public getLineGroupId(): string {
        return this.lineGroupId;
    }

    /**
     * @param lineGroupId 回線グループID
     */
    public setLineGroupId(lineGroupId: string) {
        this.lineGroupId = lineGroupId;
    }

    /**
     * @return 所属テナントID
     */
    public getTenantId(): string {
        return this.tenantId;
    }

    /**
     * @param tenantId 所属テナントID
     */
    public setTenantId(tenantId: string) {
        this.tenantId = tenantId;
    }

    /**
     * @return 実行ユーザID
     */
    public getExecuteUserId(): string {
        return this.executeUserId;
    }

    /**
     * @param executeUserId 実行ユーザID
     */
    public setExecuteUserId(executeUserId: string) {
        this.executeUserId = executeUserId;
    }

    /**
     * @return 実行テナントID
     */
    public getExecuteTenantId(): string {
        return this.executeTenantId;
    }

    /**
     * @param executeTenantId 実行テナントID
     */
    public setExecuteTenantId(executeTenantId: string) {
        this.executeTenantId = executeTenantId;
    }

    /**
     * @return 機能種別
     */
    public getFunctionType(): string {
        return this.functionType;
    }

    /**
     * @param functionType 機能種別
     */
    public setFunctionType(functionType: string) {
        this.functionType = functionType;
    }

    /**
     * @return 操作区分
     */
    public getOperationDivision(): string {
        return this.operationDivision;
    }

    /**
     * @param operationDivision 操作区分
     */
    public setOperationDivision(operationDivision: string) {
        this.operationDivision = operationDivision;
    }

    /**
     * @return 変更前プランID
     */
    public getChangeOldplanId(): string {
        return this.changeOldplanId;
    }

    /**
     * @param changeOldplanId 変更前プランID
     */
    public setChangeOldplanId(changeOldplanId: string) {
        this.changeOldplanId = changeOldplanId;
    }

    /**
     * @return 変更後プランID
     */
    public getChangeNewplanId(): string {
        return this.changeNewplanId;
    }

    /**
     * @param changeNewplanId 変更後プランID
     */
    public setChangeNewplanId(changeNewplanId: string) {
        this.changeNewplanId = changeNewplanId;
    }

    /**
     * @return 回線所属変更回線ID
     */
    public getChangePlanId(): string {
        return this.changePlanId;
    }

    /**
     * @param changePlanId 回線所属変更回線ID
     */
    public setChangePlanId(changePlanId: string) {
        this.changePlanId = changePlanId;
    }

    /**
     * @return 基本容量
     */
    public getBasicCapacity(): string {
        return this.basicCapacity;
    }

    /**
     * @param basicCapacity 基本容量
     */
    public setBasicCapacity(basicCapacity: string) {
        this.basicCapacity = basicCapacity;
    }

    /**
     * @return オプションプランID
     */
    public getOptionPlanId(): string {
        return this.optionPlanId;
    }

    /**
     * @param optionPlanId オプションプランID
     */
    public setOptionPlanId(optionPlanId: string) {
        this.optionPlanId = optionPlanId;
    }

    /** STEP4.0版対応　追加　START */
    /**
     * @return MNP転出フラグ
     */
    public getMnpOutFlag(): string {
        return this.mnpOutFlag;
    }

    /**
     * @param mnpOutFlag MNP転出フラグ
     */
    public setMnpOutFlag(mnpOutFlag: string) {
        this.mnpOutFlag = mnpOutFlag;
    }
    /** STEP4.0版対応　追加　END */

    /** STEP4.0d版対応　追加　START */
    /**
     * @return 譲渡先回線番号
     */
    public getDestinationLineNo(): string {
        return this.destinationLineNo;
    }

    /**
     * @param destinationLineNo 譲渡先回線番号
     */
    public setDestinationLineNo(destinationLineNo: string) {
        this.destinationLineNo = destinationLineNo;
    }

    /**
     * @return 譲渡データ量
     */
    public getGiftData(): string {
        return this.giftData;
    }

    /**
     * @param giftData 譲渡データ量
     */
    public setGiftData(giftData: string) {
        this.giftData = giftData;
    }
    /** STEP4.0d版対応　追加　END */

    /** STEP1.2b版対応　追加　START */
    /**
     * @return the internalFlag
     */
    public isInternalFlag(): boolean {
        return this.internalFlag;
    }

    /**
     * @param internalFlag the internalFlag to set
     */
    public setInternalFlag(internalFlag: boolean) {
        this.internalFlag = internalFlag;
    }

    /**
     * @return the restMessage
     */
    public getRestMessage(): string {
        return this.restMessage;
    }

    /**
     * @param restMessage the restMessage to set
     */
    public setRestMessage(restMessage: string) {
        this.restMessage = restMessage;
    }
    /** STEP1.2b版対応　追加　END */

    // STEP7.0版対応　追加　START
    /**
     * @return MNP転入フラグ
     */
    public getMnpInFlag(): string {
        return this.mnpInFlag;
    }

    /**
     * @param mnpInFlag MNP転入フラグ
     */
    public setMnpInFlag(mnpInFlag: string) {
        this.mnpInFlag = mnpInFlag;
    }
    // STEP7.0版対応　追加　END

    // STEP13.0版対応　追加　START
    /**
     * @return 変更前卸ポータルグループプランID
     */
    public getChangeOldGroupPlanId(): string {
        return this.changeOldGroupPlanId;
    }

    /**
     * @param changeOldGroupPlanId 変更前卸ポータルグループプランID
     */
    public setChangeOldGroupPlanId(changeOldGroupPlanId: string) {
        this.changeOldGroupPlanId = changeOldGroupPlanId;
    }

    /**
     * @return 変更後卸ポータルグループプランID
     */
    public getChangeNewGroupPlanId(): string {
        return this.changeNewGroupPlanId;
    }

    /**
     * @param changeNewGroupPlanId 変更後卸ポータルグループプランID
     */
    public setChangeNewGroupPlanId(changeNewGroupPlanId: string) {
        this.changeNewGroupPlanId = changeNewGroupPlanId;
    }
    // STEP13.0版対応　追加　END

    // STEP17.0版対応　追加　START
    /**
     * @return 変更前アクセス方式
     */
    public getChangeOldAccess(): string {
        return this.changeOldAccess;
    }

    /**
     * @param changeOldAccess 変更前アクセス方式
     */
    public setChangeOldAccess(changeOldAccess: string) {
        this.changeOldAccess = changeOldAccess;
    }

    /**
     * @return 変更後アクセス方式
     */
    public getChangeNewAccess(): string {
        return this.changeNewAccess;
    }

    /**
     * @param changeNewAccess 変更後アクセス方式
     */
    public setChangeNewAccess(changeNewAccess: string) {
        this.changeNewAccess = changeNewAccess;
    }
    // STEP17.0版対応　追加　END
    // STEP21.0版対応　追加　START
    /**
     * @return 0035でんわプランID
     */
    public getChangeNewVoicePlanId() {
        return this.changeNewVoicePlanId;
    }

    /**
     * @param changeNewVoicePlanId 0035でんわプランID
     */
    public setChangeNewVoicePlanId(changeNewVoicePlanId: string) {
        this.changeNewVoicePlanId = changeNewVoicePlanId;
    }

    // STEP21.0版対応　追加　END
    // java code end
}
