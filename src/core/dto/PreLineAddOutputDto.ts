import BaseOutputDto from "./BaseOutputDto";

import CardEntity from "../entity/CardEntity";
import { CustomerInfoEntity } from "../entity/CustomerInfoEntity";
import PlansEntity from "../entity/PlansEntity";

/**
 * 回線オプション（T番）情報
 */
export interface LineOptionsTBan {
    /** 国際ローミング利用限度額ID */
    intlRoamingT?: string;
    /** 留守番でんわ */
    voicemailT?: string;
    /** キャッチホン */
    callWaitingT?: string;
    /** 国際電話 */
    intlCallT?: string;
    /** 転送でんわ */
    forwardingT?: string;
    /** 国際着信転送 */
    intlForwadingT?: string;
}

export default interface PreLineAddOutputDto extends BaseOutputDto {
    additionalData?: {
        nNo: string;
        csvUnnecessary: boolean;
        /** 回線廃止用のN番（同一MVNE転入の場合） */
        nNo_haishi: string;
        /** CSV作成要否 */
        csvOutputKbn: boolean;
        /** オプション追加CSVの作成要否 */
        optionAddCsvOutputKbn: boolean;
        /** 回線廃止CSVの作成要否 */
        abolitionCsvOutputKbn: boolean;
        /** お客様基本情報 */
        custInfo: CustomerInfoEntity;
        /** プラン情報 */
        planInfo: PlansEntity;
        /** カード情報 */
        cardInfo: CardEntity;
        /** mnp転入フラグ */
        mnpInType: number;
        /** 予約日 yyyy/MM/dd 04:00 */
        reserveDate: string;
        /** オーダ種別 */
        orderType: string;
        /** REST APIで受付けた際のシステム日付(yyyy/MM/dd) */
        receivedDate: string;
        /** フルMVNOフラグ */
        isFullMvno: boolean;
        /** 回線オプション */
        options: LineOptionsTBan;
        /** 同一MVNEのキャンセルAPI送信済みフラグ */
        isSameMvneCancelRequestSent?: boolean;
    };
}
