import BaseOutputDto from "./BaseOutputDto";

// tslint:disable-next-line:no-empty-interface
export default interface LinePlanAcquisitionOutputDto extends BaseOutputDto {
    additionalData: {
        /** N番 */
        nNo: string;
        /** CSV作成要否 */
        csvOutputKbn: string;
        /** 連携不要フラグ */
        csvUnnecessary: boolean;
        /** 予約の回線番号 */
        lineNo: string;
        // 申し込む料金プラン(T番)
        resalePlanIdT: string;
        // 初期SIMフラグ
        shokiSIMFlag: string;
        // 希望日
        requestDate: string;
        // OP相殺
        op: boolean;
    };
}