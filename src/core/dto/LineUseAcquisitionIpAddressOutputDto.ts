import BaseOutputDto from "./BaseOutputDto";
import ResponseHeader from "./ResponseHeader";

export default interface LineUseAcquisitionIpAddressOutputDto
    extends BaseOutputDto {
    jsonBody: {
        /** レスポンスヘッダ */
        responseHeader: ResponseHeader;
        /** 端末IPアドレス */
        terminalIpAddress: string; // = "";
    };
}

/**
 * LineUseAcquisitionIpAddressOutputDtoの初期値
 */
export function getEmpty_LineUseAcquisitionIpAddressOutput(
    responseHeader: ResponseHeader,
): LineUseAcquisitionIpAddressOutputDto {
    return {
        jsonBody: {
            responseHeader,
            terminalIpAddress: "",
        },
    };
}
