export default interface AdditionalCoupon3Group {
    /** 追加クーポン3(グループ)容量 */
    additionalCoupon3Capacity: string; // = "";

    /** 追加クーポン3(グループ)使用量 */
    additionalCoupon3Use: string; // = "";
    // STEP16.0版対応　追加　END

    /** 追加クーポン3(グループ)残容量 */
    additionalCoupon3RemainCapacity: string; // = "";

    /** 追加クーポン3(グループ)有効期限 */
    additionalCoupon3TermValidity: string; // = "";
}

/**
 * 追加クーポン3(グループ)Dtoの初期値
 */
export function getEmpty_AdditionalCoupon3Group(): AdditionalCoupon3Group {
    return {
        additionalCoupon3Capacity: "",
        additionalCoupon3Use: "",
        additionalCoupon3RemainCapacity: "",
        additionalCoupon3TermValidity: "",
    };
}
