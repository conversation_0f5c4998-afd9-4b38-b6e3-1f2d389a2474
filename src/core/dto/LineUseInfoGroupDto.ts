import AdditionalCoupon1Group from "./AdditionalCoupon1Group";
import AdditionalCoupon2Group from "./AdditionalCoupon2Group";
import AdditionalCoupon3Group from "./AdditionalCoupon3Group";
import AdditionalCoupon4Group from "./AdditionalCoupon4Group";
import AdditionalCoupon5Group from "./AdditionalCoupon5Group";
import AppCoupon1GroupDto from "./AppCoupon1GroupDto";
import AppCoupon2GroupDto from "./AppCoupon2GroupDto";
import AppCoupon3GroupDto from "./AppCoupon3GroupDto";
import AppCoupon4GroupDto from "./AppCoupon4GroupDto";
import AppCoupon5GroupDto from "./AppCoupon5GroupDto";
import AppCoupon6GroupDto from "./AppCoupon6GroupDto";
import CarryingOverCoupon1Group from "./CarryingOverCoupon1Group";
import CarryingOverCoupon2Group from "./CarryingOverCoupon2Group";

export default interface LineUseInfoGroupDto {
    /** 当日の通信量（グループ） */
    trafficOneDayGroup: string; // = "";

    /** 1日前の通信量（グループ） */
    traffic1dayAgoGroup: string; // = "";

    /** 2日前の通信量（グループ） */
    traffic2daysAgoGroup: string; // = "";

    /** 3日前の通信量（グループ） */
    traffic3daysAgoGroup: string; // = "";

    /** 4日前の通信量（グループ） */
    traffic4daysAgoGroup: string; // = "";

    /** 5日前の通信量（グループ） */
    traffic5daysAgoGroup: string; // = "";

    /** 6日前の通信量（グループ） */
    traffic6daysAgoGroup: string; // = "";

    /** 7日前の通信量（グループ） */
    traffic7daysAgoGroup: string; // = "";

    /** 8日前の通信量（グループ） */
    traffic8daysAgoGroup: string; // = "";

    /** 9日前の通信量（グループ） */
    traffic9daysAgoGroup: string; // = "";

    /** 10日前の通信量（グループ） */
    traffic10daysAgoGroup: string; // = "";

    /** 11日前の通信量（グループ） */
    traffic11daysAgoGroup: string; // = "";

    /** 12日前の通信量（グループ） */
    traffic12daysAgoGroup: string; // = "";

    /** 13日前の通信量（グループ） */
    traffic13daysAgoGroup: string; // = "";

    /** 14日前の通信量（グループ） */
    traffic14daysAgoGroup: string; // = "";

    /** 15日前の通信量（グループ） */
    traffic15daysAgoGroup: string; // = "";

    /** 16日前の通信量（グループ） */
    traffic16daysAgoGroup: string; // = "";

    /** 17日前の通信量（グループ） */
    traffic17daysAgoGroup: string; // = "";

    /** 18日前の通信量（グループ） */
    traffic18daysAgoGroup: string; // = "";

    /** 19日前の通信量（グループ） */
    traffic19daysAgoGroup: string; // = "";

    /** 20日前の通信量（グループ） */
    traffic20daysAgoGroup: string; // = "";

    /** 21日前の通信量（グループ） */
    traffic21daysAgoGroup: string; // = "";

    /** 22日前の通信量（グループ） */
    traffic22daysAgoGroup: string; // = "";

    /** 23日前の通信量（グループ） */
    traffic23daysAgoGroup: string; // = "";

    /** 24日前の通信量（グループ） */
    traffic24daysAgoGroup: string; // = "";

    /** 25日前の通信量（グループ） */
    traffic25daysAgoGroup: string; // = "";

    /** 26日前の通信量（グループ） */
    traffic26daysAgoGroup: string; // = "";

    /** 27日前の通信量（グループ） */
    traffic27daysAgoGroup: string; // = "";

    /** 28日前の通信量（グループ） */
    traffic28daysAgoGroup: string; // = "";

    /** 29日前の通信量（グループ） */
    traffic29daysAgoGroup: string; // = "";

    /** 30日前の通信量（グループ） */
    traffic30daysAgoGroup: string; // = "";

    /** 当月の通信量（グループ） */
    trafficThisMonthGroup: string; // = "";

    /** 前月の通信量（グループ） */
    trafficPreviousMonthGroup: string; // = "";

    /** 前々月の通信量（グループ） */
    trafficBeforehandMonthGroup: string; // = "";

    /** 基本クーポン残容量（グループ） */
    basicCouponRemainsGroup: string; // = "";

    // STEP16.0版対応　追加　START
    /** 基本クーポン容量（グループ） */
    basicCouponCapacityGroup: string; // = "";

    /** 基本クーポン使用量（グループ） */
    basicCouponUseGroup: string; // = "";
    // STEP16.0版対応　追加　END

    /** 基本クーポン有効期限（グループ） */
    basicCouponTermValidityGroup: string; // = "";

    /** クーポン容量切れ時間（グループ） */
    couponPieceTimeGroup: string; // = "";

    /** 繰越クーポン1（グループ） */
    carryingOverCoupon1Group: CarryingOverCoupon1Group; // = null;

    /** 繰越クーポン2（グループ） */
    carryingOverCoupon2Group: CarryingOverCoupon2Group; // = null;

    /** 追加クーポン1（グループ） */
    additionalCoupon1Group: AdditionalCoupon1Group; // = null;

    /** 追加クーポン2（グループ） */
    additionalCoupon2Group: AdditionalCoupon2Group; // = null;

    /** 追加クーポン3（グループ） */
    additionalCoupon3Group: AdditionalCoupon3Group; // = null;

    /** 追加クーポン4（グループ） */
    additionalCoupon4Group: AdditionalCoupon4Group; // = null;

    /** 追加クーポン5（グループ） */
    additionalCoupon5Group: AdditionalCoupon5Group; // = null;

    // STEP16.0版対応　追加　START
    /** アプリクーポン1（グループ） */
    aplCoupon1Group: AppCoupon1GroupDto; // = null;

    /** アプリクーポン2（グループ） */
    aplCoupon2Group: AppCoupon2GroupDto; // = null;

    /** アプリクーポン3（グループ） */
    aplCoupon3Group: AppCoupon3GroupDto; // = null;

    /** アプリクーポン4（グループ） */
    aplCoupon4Group: AppCoupon4GroupDto; // = null;

    /** アプリクーポン5（グループ） */
    aplCoupon5Group: AppCoupon5GroupDto; // = null;

    /** アプリクーポン6（グループ） */
    aplCoupon6Group: AppCoupon6GroupDto; // = null;
    // STEP16.0版対応　追加　END

    /** クーポンON/OFF状態（グループ） */
    couponOnOffGroup: string; // = "";

    /** ヘビーユーザ監視規制状態（グループ） */
    heavyUserMonitorStatusGroup: string; // = "";

    /** ヘビーユーザ監視規制開始時間（月間）（グループ） */
    heavyUserMonitorStartTimeMonthGroup: string; // = "";

    /** ヘビーユーザ監視規制開始時間（直近）（グループ） */
    heavyUserMonitorStartTimeLatestGroup: string; // = "";

    /** 適用サービス状態（規制理由）（グループ） */
    regulationCauseGroup: string; // = "";

    /** 総量規制状態（グループ） */
    totalVolumeControlStatusGroup: string; // = "";

    /** 当日の通信量(クーポンOFF時)（グループ） */
    trafficOneDay_CouponOffGroup: string; // = "";

    /** 1日前の通信量(クーポンOFF時)（グループ） */
    traffic1dayAgo_CouponOffGroup: string; // = "";

    /** 2日前の通信量(クーポンOFF時)（グループ） */
    traffic2daysAgo_CouponOffGroup: string; // = "";

    /** 3日前の通信量(クーポンOFF時)（グループ） */
    traffic3daysAgo_CouponOffGroup: string; // = "";

    /** 4日前の通信量(クーポンOFF時)（グループ） */
    traffic4daysAgo_CouponOffGroup: string; // = "";

    /** 5日前の通信量(クーポンOFF時)（グループ） */
    traffic5daysAgo_CouponOffGroup: string; // = "";

    /** 6日前の通信量(クーポンOFF時)（グループ） */
    traffic6daysAgo_CouponOffGroup: string; // = "";

    /** 7日前の通信量(クーポンOFF時)（グループ） */
    traffic7daysAgo_CouponOffGroup: string; // = "";

    /** 8日前の通信量(クーポンOFF時)（グループ） */
    traffic8daysAgo_CouponOffGroup: string; // = "";

    /** 9日前の通信量(クーポンOFF時)（グループ） */
    traffic9daysAgo_CouponOffGroup: string; // = "";

    /** 10日前の通信量(クーポンOFF時)（グループ） */
    traffic10daysAgo_CouponOffGroup: string; // = "";

    /** 11日前の通信量(クーポンOFF時)（グループ） */
    traffic11daysAgo_CouponOffGroup: string; // = "";

    /** 12日前の通信量(クーポンOFF時)（グループ） */
    traffic12daysAgo_CouponOffGroup: string; // = "";

    /** 13日前の通信量(クーポンOFF時)（グループ） */
    traffic13daysAgo_CouponOffGroup: string; // = "";

    /** 14日前の通信量(クーポンOFF時)（グループ） */
    traffic14daysAgo_CouponOffGroup: string; // = "";

    /** 15日前の通信量(クーポンOFF時)（グループ） */
    traffic15daysAgo_CouponOffGroup: string; // = "";

    /** 16日前の通信量(クーポンOFF時)（グループ） */
    traffic16daysAgo_CouponOffGroup: string; // = "";

    /** 17日前の通信量(クーポンOFF時)（グループ） */
    traffic17daysAgo_CouponOffGroup: string; // = "";

    /** 18日前の通信量(クーポンOFF時)（グループ） */
    traffic18daysAgo_CouponOffGroup: string; // = "";

    /** 19日前の通信量(クーポンOFF時)（グループ） */
    traffic19daysAgo_CouponOffGroup: string; // = "";

    /** 20日前の通信量(クーポンOFF時)（グループ） */
    traffic20daysAgo_CouponOffGroup: string; // = "";

    /** 21日前の通信量(クーポンOFF時)（グループ） */
    traffic21daysAgo_CouponOffGroup: string; // = "";

    /** 22日前の通信量(クーポンOFF時)（グループ） */
    traffic22daysAgo_CouponOffGroup: string; // = "";

    /** 23日前の通信量(クーポンOFF時)（グループ） */
    traffic23daysAgo_CouponOffGroup: string; // = "";

    /** 24日前の通信量(クーポンOFF時)（グループ） */
    traffic24daysAgo_CouponOffGroup: string; // = "";

    /** 25日前の通信量(クーポンOFF時)（グループ） */
    traffic25daysAgo_CouponOffGroup: string; // = "";

    /** 26日前の通信量(クーポンOFF時)（グループ） */
    traffic26daysAgo_CouponOffGroup: string; // = "";

    /** 27日前の通信量(クーポンOFF時)（グループ） */
    traffic27daysAgo_CouponOffGroup: string; // = "";

    /** 28日前の通信量(クーポンOFF時)（グループ） */
    traffic28daysAgo_CouponOffGroup: string; // = "";

    /** 29日前の通信量(クーポンOFF時)（グループ） */
    traffic29daysAgo_CouponOffGroup: string; // = "";

    /** 30日前の通信量(クーポンOFF時)（グループ） */
    traffic30daysAgo_CouponOffGroup: string; // = "";

    /** 当月の通信量(クーポンOFF時)（グループ） */
    trafficThisMonth_CouponOffGroup: string; // = "";

    /** 前月の通信量(クーポンOFF時)（グループ） */
    trafficPreviousMonth_CouponOffGroup: string; // = "";

    /** 前々月の通信量(クーポンOFF時)（グループ） */
    trafficBeforehandMonth_CouponOffGroup: string; // = "";
}

/**
 * LineUseInfoGroupDtoの初期値
 */
export function getEmpty_LineUseInfoGroup(): LineUseInfoGroupDto {
    return {
        trafficOneDayGroup: "",
        traffic1dayAgoGroup: "",
        traffic2daysAgoGroup: "",
        traffic3daysAgoGroup: "",
        traffic4daysAgoGroup: "",
        traffic5daysAgoGroup: "",
        traffic6daysAgoGroup: "",
        traffic7daysAgoGroup: "",
        traffic8daysAgoGroup: "",
        traffic9daysAgoGroup: "",
        traffic10daysAgoGroup: "",
        traffic11daysAgoGroup: "",
        traffic12daysAgoGroup: "",
        traffic13daysAgoGroup: "",
        traffic14daysAgoGroup: "",
        traffic15daysAgoGroup: "",
        traffic16daysAgoGroup: "",
        traffic17daysAgoGroup: "",
        traffic18daysAgoGroup: "",
        traffic19daysAgoGroup: "",
        traffic20daysAgoGroup: "",
        traffic21daysAgoGroup: "",
        traffic22daysAgoGroup: "",
        traffic23daysAgoGroup: "",
        traffic24daysAgoGroup: "",
        traffic25daysAgoGroup: "",
        traffic26daysAgoGroup: "",
        traffic27daysAgoGroup: "",
        traffic28daysAgoGroup: "",
        traffic29daysAgoGroup: "",
        traffic30daysAgoGroup: "",
        trafficThisMonthGroup: "",
        trafficPreviousMonthGroup: "",
        trafficBeforehandMonthGroup: "",
        basicCouponRemainsGroup: "",
        basicCouponCapacityGroup: "",
        basicCouponUseGroup: "",
        basicCouponTermValidityGroup: "",
        couponPieceTimeGroup: "",
        carryingOverCoupon1Group: null,
        carryingOverCoupon2Group: null,
        additionalCoupon1Group: null,
        additionalCoupon2Group: null,
        additionalCoupon3Group: null,
        additionalCoupon4Group: null,
        additionalCoupon5Group: null,
        aplCoupon1Group: null,
        aplCoupon2Group: null,
        aplCoupon3Group: null,
        aplCoupon4Group: null,
        aplCoupon5Group: null,
        aplCoupon6Group: null,
        couponOnOffGroup: "",
        heavyUserMonitorStatusGroup: "",
        heavyUserMonitorStartTimeMonthGroup: "",
        heavyUserMonitorStartTimeLatestGroup: "",
        regulationCauseGroup: "",
        totalVolumeControlStatusGroup: "",
        trafficOneDay_CouponOffGroup: "",
        traffic1dayAgo_CouponOffGroup: "",
        traffic2daysAgo_CouponOffGroup: "",
        traffic3daysAgo_CouponOffGroup: "",
        traffic4daysAgo_CouponOffGroup: "",
        traffic5daysAgo_CouponOffGroup: "",
        traffic6daysAgo_CouponOffGroup: "",
        traffic7daysAgo_CouponOffGroup: "",
        traffic8daysAgo_CouponOffGroup: "",
        traffic9daysAgo_CouponOffGroup: "",
        traffic10daysAgo_CouponOffGroup: "",
        traffic11daysAgo_CouponOffGroup: "",
        traffic12daysAgo_CouponOffGroup: "",
        traffic13daysAgo_CouponOffGroup: "",
        traffic14daysAgo_CouponOffGroup: "",
        traffic15daysAgo_CouponOffGroup: "",
        traffic16daysAgo_CouponOffGroup: "",
        traffic17daysAgo_CouponOffGroup: "",
        traffic18daysAgo_CouponOffGroup: "",
        traffic19daysAgo_CouponOffGroup: "",
        traffic20daysAgo_CouponOffGroup: "",
        traffic21daysAgo_CouponOffGroup: "",
        traffic22daysAgo_CouponOffGroup: "",
        traffic23daysAgo_CouponOffGroup: "",
        traffic24daysAgo_CouponOffGroup: "",
        traffic25daysAgo_CouponOffGroup: "",
        traffic26daysAgo_CouponOffGroup: "",
        traffic27daysAgo_CouponOffGroup: "",
        traffic28daysAgo_CouponOffGroup: "",
        traffic29daysAgo_CouponOffGroup: "",
        traffic30daysAgo_CouponOffGroup: "",
        trafficThisMonth_CouponOffGroup: "",
        trafficPreviousMonth_CouponOffGroup: "",
        trafficBeforehandMonth_CouponOffGroup: "",
    };
}
