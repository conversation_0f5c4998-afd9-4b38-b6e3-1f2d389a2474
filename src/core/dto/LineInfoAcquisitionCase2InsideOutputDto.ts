import BaseOutputDto from "./BaseOutputDto";
import GroupADto from "./GroupADto";
import GroupBDto from "./GroupBDto";
import ResponseHeader from "./ResponseHeader";

export default interface LineInfoAcquisitionCase2InsideOutputDto extends BaseOutputDto {
    jsonBody: {
        responseHeader: ResponseHeader;
        groupA: GroupADto;
        groupB: GroupBDto;
        errorMessage: string;
    };
}