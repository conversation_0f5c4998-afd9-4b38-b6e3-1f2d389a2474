import BaseOutputDto from "./BaseOutputDto";
import GroupA2Dto from "./GroupA2Dto";
import GroupBDto from "./GroupBDto";
import GroupCDto from "./GroupCDto";
import ResponseHeader from "./ResponseHeader";

export default interface LineInfoAcquisitionCase3VersionOutputDto extends BaseOutputDto {
    jsonBody: {
        responseHeader: ResponseHeader;
        groupA: GroupA2Dto;
        groupB: GroupBDto;
        groupC: GroupCDto;
    };
}