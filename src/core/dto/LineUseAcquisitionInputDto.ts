import SOManagerInputDto from "./SOManagerInputDto";

export default interface LineUseAcquisitionInputDto extends SOManagerInputDto {
    /** 回線番号 */
    lineNo: string;

    /** 卸ポータルプランID */
    potalPlanID: string;

    /** 卸ポータルグループプランID */
    potalGroupPlanID: string;

    /** 端末IPアドレス取得要否 */
    getIpAddress: string;

    /** 追加オプション */
    addOption: string;

    /** バージョン */
    version: string;
}
