import BaseOutputDto from "./BaseOutputDto";
import GroupADto from "./GroupADto";
import GroupBDto from "./GroupBDto";
import GroupCDto from "./GroupCDto";
import ResponseHeader from "./ResponseHeader";

export default interface LineInfoAcquisitionCase3OutputDto extends BaseOutputDto {
    jsonBody: {
        responseHeader: ResponseHeader;
        groupA: GroupADto;
        groupB: GroupBDto;
        groupC: GroupCDto;
    };
}