export default interface AdditionalCoupon1Group {
    /** 追加クーポン1(グループ)容量 */
    additionalCoupon1Capacity: string; // = "";

    /** 追加クーポン1(グループ)使用量 */
    additionalCoupon1Use: string; // = "";
    // STEP16.0版対応　追加　END

    /** 追加クーポン1(グループ)残容量 */
    additionalCoupon1RemainCapacity: string; // = "";

    /** 追加クーポン1(グループ)有効期限 */
    additionalCoupon1TermValidity: string; // = "";
}

/**
 * 追加クーポン1(グループ)Dtoの初期値
 */
export function getEmpty_AdditionalCoupon1Group(): AdditionalCoupon1Group {
    return {
        additionalCoupon1Capacity: "",
        additionalCoupon1Use: "",
        additionalCoupon1RemainCapacity: "",
        additionalCoupon1TermValidity: "",
    };
}
