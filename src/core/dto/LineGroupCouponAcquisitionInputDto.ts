import SOManagerInputDto from "./SOManagerInputDto";

export default interface LineGroupCouponAcquisitionInputDto
    extends SOManagerInputDto {
    /** 回線グループID */
    lineGroupId: string;
    /** 卸ポータルグループプランID */
    potalGroupPlanID: string;
    /** 卸ポータルオプショングループプランID */
    optionGroupPlanId: string;
    /** クーポンON/OFF状態 */
    couponOnOff: string;
    /** 回線番号 */
    lineNo: string;
    /** 予約日 */
    reserve_date: string;
}