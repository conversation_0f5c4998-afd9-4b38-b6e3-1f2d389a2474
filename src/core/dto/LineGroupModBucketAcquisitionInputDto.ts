import SOManagerInputDto from "./SOManagerInputDto";

export default interface LineGroupModBucketAcquisitionInputDto
    extends SOManagerInputDto {
    /** テナントID */
    // private String tenantId;
    tenantId: string;

    /** 回線グループID */
    // private String lineGroupId;
    lineGroupId: string;

    /** 容量指定 */
    // private String capacity;
    capacity: string;

    /** 容量計算 */
    // private String calculation;
    calculation: string;

    /** 予約日 */
    // private String reserve_date;
    reserve_date: string;
}
