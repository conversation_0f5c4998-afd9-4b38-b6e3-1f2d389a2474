export default interface CarryingOverCoupon1 {
    /** 繰越クーポン1残容量 */
    // private String carryingOverCoupon1RemainCapacity= "";
    carryingOverCoupon1RemainCapacity: string;

    /** 繰越クーポン1有効期限 */
    // private String carryingOverCoupon1TermValidity= "";
    carryingOverCoupon1TermValidity: string;
}

/**
 * 繰越クーポン1の初期値
 */
export function getEmpty_CarryingOverCoupon1(): CarryingOverCoupon1 {
    return {
        carryingOverCoupon1RemainCapacity: "",
        carryingOverCoupon1TermValidity: "",
    };
}
