export default interface AdditionalCoupon4 {
    /** 追加クーポン4残容量 */
    // private String additionalCoupon4RemainCapacity= "";
    additionalCoupon4RemainCapacity: string;

    /** 追加クーポン4有効期限 */
    // private String additionalCoupon4TermValidity= "";
    additionalCoupon4TermValidity: string;
}

/**
 * 追加クーポン4の初期値
 */
export function getEmpty_AdditionalCoupon4(): AdditionalCoupon4 {
    return {
        additionalCoupon4RemainCapacity: "",
        additionalCoupon4TermValidity: "",
    };
}
