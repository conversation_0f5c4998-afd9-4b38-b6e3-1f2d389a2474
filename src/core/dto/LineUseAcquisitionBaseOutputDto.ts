import LineUseAcquisitionAddOpiton1OutputDto from "./LineUseAcquisitionAddOpiton1OutputDto";
import LineUseAcquisitionAddOpiton1VersionOutputDto from "./LineUseAcquisitionAddOpiton1VersionOutputDto";
import LineUseAcquisitionAddOpiton2OutputDto from "./LineUseAcquisitionAddOpiton2OutputDto";
import LineUseAcquisitionAddOpiton2VersionOutputDto from "./LineUseAcquisitionAddOpiton2VersionOutputDto";
import LineUseAcquisitionAddOpiton3OutputDto from "./LineUseAcquisitionAddOpiton3OutputDto";
import LineUseAcquisitionAddOpiton3VersionOutputDto from "./LineUseAcquisitionAddOpiton3VersionOutputDto";
import LineUseAcquisitionAddOpiton4OutputDto from "./LineUseAcquisitionAddOpiton4Dto";
import LineUseAcquisitionAddOpiton4VersionOutputDto from "./LineUseAcquisitionAddOpiton4VersionOutputDto";
import LineUseAcquisitionIpAddressOutputDto from "./LineUseAcquisitionIpAddressOutputDto";
import LineUseAcquisitionOutputDto from "./LineUseAcquisitionOutputDto";
import LineUseAcquisitionVersionOutputDto from "./LineUseAcquisitionVersionOutputDto";

type LineUseAcquisitionBaseOutputDto =
    | LineUseAcquisitionAddOpiton1OutputDto
    | LineUseAcquisitionAddOpiton1VersionOutputDto
    | LineUseAcquisitionAddOpiton2OutputDto
    | LineUseAcquisitionAddOpiton2VersionOutputDto
    | LineUseAcquisitionAddOpiton3OutputDto
    | LineUseAcquisitionAddOpiton3VersionOutputDto
    | LineUseAcquisitionAddOpiton4OutputDto
    | LineUseAcquisitionAddOpiton4VersionOutputDto
    | LineUseAcquisitionIpAddressOutputDto
    | LineUseAcquisitionOutputDto
    | LineUseAcquisitionVersionOutputDto;

export default LineUseAcquisitionBaseOutputDto;
