import ResponseHeader from "@/core/dto/ResponseHeader";
import CarryingOverCoupon1 from "@/core/dto/CarryingOverCoupon1";
import CarryingOverCoupon2 from "@/core/dto/CarryingOvercoupon2";
import AdditionalCoupon1 from "@/core/dto/AdditionalCoupon1";
import AdditionalCoupon2 from "@/core/dto/AdditionalCoupon2";
import AdditionalCoupon3 from "@/core/dto/AdditionalCoupon3";
import AdditionalCoupon4 from "@/core/dto/AdditionalCoupon4";
import AdditionalCoupon5 from "@/core/dto/AdditionalCoupon5";

export default interface LineGroupUseAcquisitionInsideOutputDto {
    jsonBody: {
        responseHeader: ResponseHeader;
        trafficOneDay: string;
        traffic1dayAgo: string;
        traffic2daysAgo: string;
        traffic3daysAgo: string;
        traffic4daysAgo: string;
        traffic5daysAgo: string;
        traffic6daysAgo: string;
        traffic7daysAgo: string;
        traffic8daysAgo: string;
        traffic9daysAgo: string;
        traffic10daysAgo: string;
        traffic11daysAgo: string;
        traffic12daysAgo: string;
        traffic13daysAgo: string;
        traffic14daysAgo: string;
        traffic15daysAgo: string;
        traffic16daysAgo: string;
        traffic17daysAgo: string;
        traffic18daysAgo: string;
        traffic19daysAgo: string;
        traffic20daysAgo: string;
        traffic21daysAgo: string;
        traffic22daysAgo: string;
        traffic23daysAgo: string;
        traffic24daysAgo: string;
        traffic25daysAgo: string;
        traffic26daysAgo: string;
        traffic27daysAgo: string;
        traffic28daysAgo: string;
        traffic29daysAgo: string;
        traffic30daysAgo: string;
        trafficThisMonth: string;
        trafficPreviousMonth: string;
        trafficBeforehandMonth: string;
        basicCouponRemains: string;
        basicCouponTermValidity: string;
        couponPieceTime: string;
        carryingOverCoupon1: CarryingOverCoupon1;
        carryingOverCoupon2: CarryingOverCoupon2;
        additionalCoupon1: AdditionalCoupon1;
        additionalCoupon2: AdditionalCoupon2;
        additionalCoupon3: AdditionalCoupon3;
        additionalCoupon4: AdditionalCoupon4;
        additionalCoupon5: AdditionalCoupon5;
        couponOnOff: string;
        heavyUserMonitorStatus: string;
        heavyUserMonitorStartTimeMonth: string;
        heavyUserMonitorStartTimeLatest: string;
        regulationCause: string;
        totalVolumeControlStatus: string;
        errorMessage: string;
    }
}