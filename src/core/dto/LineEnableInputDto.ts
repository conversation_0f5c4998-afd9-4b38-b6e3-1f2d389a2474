import SOManagerInputDto from "./SOManagerInputDto";

/**
 * 回線黒化機能Dtoクラス
 */
export default interface LineEnableInputDto extends SOManagerInputDto {
    /** テナントID */
    // tenantId: string;

    /** 対象回線の所属テナントID */
    targetTenantId: string;

    /** 回線番号 */
    lineNo: string;

    /** 課金開始日 */
    chargeDate: string;

    /** MVNO顧客CSV連携不要フラグ */
    csvUnnecessaryFlag: string;

    /** 同一MVNE転入フラグ */
    sameMvneInFlag: string;
}
