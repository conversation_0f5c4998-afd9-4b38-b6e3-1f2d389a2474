export default interface AdditionalCoupon1 {
    /** 追加クーポン1残容量 */
    // private String additionalCoupon1RemainCapacity= "";
    additionalCoupon1RemainCapacity: string;

    /** 追加クーポン1有効期限 */
    // private String additionalCoupon1TermValidity= "";
    additionalCoupon1TermValidity: string;
}

/**
 * 追加クーポン1の初期値
 */
export function getEmpty_AdditionalCoupon1(): AdditionalCoupon1 {
    return {
        additionalCoupon1RemainCapacity: "",
        additionalCoupon1TermValidity: "",
    };
}
