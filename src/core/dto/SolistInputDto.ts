import RequestHeader from "./RequestHeader";

export default interface SolistInputDto {
    /** ヘッダー情報 */
    requestHeader: RequestHeader;
    /** テナントID */
    tenantId: string;
    /** 回線番号 */
    lineNoKey?: string;
    /** SO-ID */
    serviceOrderIdKey?: string;
    /** 検索テナントID */
    tenantIdKey?: string;
    /** ステータス */
    orderStatusKey?: string;
    /** 種別 */
    orderTypeKey?: string;
    /** 投入日(始期) */
    orderDateBeginKey?: string;
    /** 投入日(終期) */
    orderDateEndKey?: string;
    /** 完了日(始期) */
    execDateBeginKey?: string;
    /** 完了日(終期) */
    execDateEndKey?: string;
}
