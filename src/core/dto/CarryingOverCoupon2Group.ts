export default interface CarryingOverCoupon2Group {
    /** 繰越クーポン2(グループ)容量 */
    carryingOverCoupon2Capacity: string; // = ""

    /** 繰越クーポン2(グループ)使用量 */
    carryingOverCoupon2Use: string; // = ""
    // STEP16.0版対応　追加　END

    /** 繰越クーポン2(グループ)残容量 */
    carryingOverCoupon2RemainCapacity: string; // = ""

    /** 繰越クーポン2(グループ)有効期限 */
    carryingOverCoupon2TermValidity: string; // = ""
}

/**
 * 繰越クーポン2(グループ)Dtoの初期値
 */
export function getEmpty_CarryingOverCoupon2Group(): CarryingOverCoupon2Group {
    return {
        carryingOverCoupon2Capacity: "",
        carryingOverCoupon2Use: "",
        carryingOverCoupon2RemainCapacity: "",
        carryingOverCoupon2TermValidity: "",
    };
}
