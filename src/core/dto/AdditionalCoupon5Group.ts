export default interface AdditionalCoupon5Group {
    /** 追加クーポン5(グループ)容量 */
    additionalCoupon5Capacity: string; // = "";

    /** 追加クーポン5(グループ)使用量 */
    additionalCoupon5Use: string; // = "";
    // STEP16.0版対応　追加　END

    /** 追加クーポン5(グループ)残容量 */
    additionalCoupon5RemainCapacity: string; // = "";

    /** 追加クーポン5(グループ)有効期限 */
    additionalCoupon5TermValidity: string; // = "";
}

/**
 * 追加クーポン5(グループ)Dtoの初期値
 */
export function getEmpty_AdditionalCoupon5Group(): AdditionalCoupon5Group {
    return {
        additionalCoupon5Capacity: "",
        additionalCoupon5Use: "",
        additionalCoupon5RemainCapacity: "",
        additionalCoupon5TermValidity: "",
    };
}
