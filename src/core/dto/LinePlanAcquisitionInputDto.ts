import SOManagerInputDto from "./SOManagerInputDto";

export default interface LinePlanAcquisitionInputDto extends SOManagerInputDto {
    /** テナントID */
    // private String tenantId;

    /** 回線番号 */
    lineNo: string;

    /** 変更前卸ポータルプランID */
    potalPlanID_pre: string;

    /** 変更後卸ポータルプランID */
    potalPlanID: string;

    /** 予約日 */
    reserve_date: string;

    /** MVNO顧客CSV連携不要フラグ */
    csvUnnecessaryFlag: string;
}