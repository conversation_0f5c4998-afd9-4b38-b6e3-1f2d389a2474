import SOManagerInputDto from "@/core/dto/SOManagerInputDto";

export default interface LineGroupUseAcquisitionInputDto extends SOManagerInputDto {
    /** 回線グループID */
    // private String lineGroupId;
    lineGroupId: string;

    /** 卸ポータルグループプランID */
    // private String potalGroupPlanID;
    potalGroupPlanID: string;

    /** 回線番号 */
    // private String lineNo;
    lineNo: string;

    /** STEP4.0版対応　追加　START */
    /** クーポンOFF時の通信量要否 */
    // private String trafficOfCouponOffFlag;
    trafficOfCouponOffFlag: string;
    /** STEP4.0版対応　追加　END */
}