import BaseOutputDto from "@/core/dto/BaseOutputDto";
import LineList from "@/core/dto/LineList";
import ResponseHeader from "@/core/dto/ResponseHeader";

/**
 * 回線グループの基本情報参照アウトプットDtoクラス。 <BR>
 * <PRE>
 *
 * </PRE>
 * <AUTHOR> yong.tan 2014/02/11
 * @version 1.00 新規<BR>
 */
export default interface LinesGroupInfoOutputDto extends BaseOutputDto {
    jsonBody: {
        /** レスポンスヘーダ */
        responseHeader: ResponseHeader;
        /** 卸ポータルグループプランID */
        potalGroupPlanID: string;
        /** 卸ポータルグループプラン名 */
        potalGroupPlanName: string;
        /** 回線番号リスト */
        lineList: LineList[];
    };
}