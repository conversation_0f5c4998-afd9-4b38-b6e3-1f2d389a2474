import SOManagerInputDto from "@/core/dto/SOManagerInputDto";

export default interface LineDataGiftInputDto
    extends SOManagerInputDto {

    /** テナントID */
    // private String tenantId;
    tenantId: string;

    /** 譲渡元回線番号 */
    // private String sourceLineNo;
    sourceLineNo: string;

    /** 譲渡先回線番号 */
    // private String destinationLineNo;
    destinationLineNo: string;

    /** 譲渡データ量 */
    // private String giftData;
    giftData: string;
}