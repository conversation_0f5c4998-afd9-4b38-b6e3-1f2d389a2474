export default interface GiftCoupon1Dto {
    /** 譲渡クーポン1容量 */
    giftCoupon1Capacity: string; // = "";

    /** 譲渡クーポン1使用量 */
    giftCoupon1Use: string; // = "";

    /** 譲渡クーポン1残容量 */
    giftCoupon1RemainCapacity: string; // = "";

    /** 譲渡クーポン1有効期限 */
    giftCoupon1TermValidity: string; // = "";
}

/**
 * 譲渡クーポン1Dtoの初期値
 */
export function getEmpty_GiftCoupon1(): GiftCoupon1Dto {
    return {
        giftCoupon1Capacity: "",
        giftCoupon1Use: "",
        giftCoupon1RemainCapacity: "",
        giftCoupon1TermValidity: "",
    };
}
