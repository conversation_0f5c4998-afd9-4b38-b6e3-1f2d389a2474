import TenantManage from "@/core/common/TenantManage";
import APICommonDAO from "@/core/dao/APICommonDAO";
import APILinesDAO from "@/core/dao/APILinesDAO";
import RESTCommon from "@/core/common/RESTCommon";
import StringUtils from "@/core/common/StringUtils";
import LineInfoAcquisitionInputDto from "@/core/dto/LineInfoAcquisitionInputDto";
import LineInfoAcquisitionBaseOutputDto from "@/core/dto/LineInfoAcquisitionBaseOutputDto";
import LineInfoAcquisitionCase1InsideOutputDto from "@/core/dto/LineInfoAcquisitionCase1InsideOutputDto";
import LineInfoAcquisitionCase2InsideOutputDto from "@/core/dto/LineInfoAcquisitionCase2InsideOutputDto";
import LineInfoAcquisitionCase3InsideOutputDto from "@/core/dto/LineInfoAcquisitionCase3InsideOutputDto";
import LineInfoAcquisitionCase1OutputDto from "@/core/dto/LineInfoAcquisitionCase1OutputDto";
import LineInfoAcquisitionCase2OutputDto from "@/core/dto/LineInfoAcquisitionCase2OutputDto";
import LineInfoAcquisitionCase3OutputDto from "@/core/dto/LineInfoAcquisitionCase3OutputDto";
import LineInfoAcquisitionCase1VersionOutputDto from "@/core/dto/LineInfoAcquisitionCase1VersionOutputDto";
import LineInfoAcquisitionCase2VersionOutputDto from "@/core/dto/LineInfoAcquisitionCase2VersionOutputDto";
import LineInfoAcquisitionCase3VersionOutputDto from "@/core/dto/LineInfoAcquisitionCase3VersionOutputDto";
import LineInfoAcquisitionCase1Version2OutputDto from "@/core/dto/LineInfoAcquisitionCase1Version2OutputDto";
import LineInfoAcquisitionCase2Version2OutputDto from "@/core/dto/LineInfoAcquisitionCase2Version2OutputDto";
import LineInfoAcquisitionCase3Version2OutputDto from "@/core/dto/LineInfoAcquisitionCase3Version2OutputDto";
import { isSQLException } from "@/helpers/queryHelper";

// union types for cleaner code
import LineInfoAcquisitionCase1BaseOutputDto from "@/core/dto/LineInfoAcquisitionCase1BaseOutputDto";
import LineInfoAcquisitionCase2BaseOutputDto from "@/core/dto/LineInfoAcquisitionCase2BaseOutputDto";
import LineInfoAcquisitionCase3BaseOutputDto from "@/core/dto/LineInfoAcquisitionCase3BaseOutputDto";
import ResponseHeader from "@/core/dto/ResponseHeader";

import { IFService } from "../IFService";
import GroupADto from "@/core/dto/GroupADto";
import GroupBDto from "@/core/dto/GroupBDto";
import GroupCDto from "@/core/dto/GroupCDto";
import MvnoUtil from "@/core/common/MvnoUtil";
import { LinesEntity } from "@/core/entity/LinesEntity";
import PlansEntity from "@/core/entity/PlansEntity";
import CheckUtil from "@/core/common/CheckUtil";
import { format } from "date-fns";
import ResultCdConstants from "@/core/constant/ResultCdConstants";
import MsgKeysConstants from "@/core/constant/MsgKeysConstants";
import GroupA2Dto from "@/core/dto/GroupA2Dto";
import GroupA3Dto from "@/core/dto/GroupA3Dto";
import Check from "@/core/common/Check";
import { TenantsEntity } from "@/core/entity/TenantsEntity";
import { getStringParameter } from "@/types/parameter.string";

export default class LineInfoAcquisitionService
    extends RESTCommon
    implements IFService<LineInfoAcquisitionInputDto, LineInfoAcquisitionBaseOutputDto> {

    // 定数　「回線番号」
    private static readonly LINE_NO = "回線番号";
    // 定数　「追加オプション」
    private static readonly ADD_OPTION = "追加オプション";
    // 定数　「1」
    private static readonly CONST_SHANAITENANTID_1 = "1";
    // 定数　「9」
    private static readonly CONST_SHANAITENANTID_9 = "9";

    // 定数　「回線オプション数」
    private static readonly CONST_LINE_OPTION_NUMBER = 6;

    /**
     * API回線DAO
     */
    private apiLinesDao = new APILinesDAO(this.request, this.context);

    /**
     * API共通DAO
     */
    private apiCommonDao = new APICommonDAO(this.request, this.context);

    /**
     * テナント管理
     */
    private tenantManage = new TenantManage(this.request, this.context);

    /**
     * 回線基本情報参照機能<BR>
     * <PRE>
     * REST APIにより、受信したパラメータの回線番号をキーにして回線基本情報を取得する。
     * モバイルアクセス回線の回線グループ運用情報を提供する。
     * </PRE>
     * @param  param 回線基本運用情報参照取得インプット
     * @param clientIPAddress クライアントIP // NOTE added during refactoring
     * @param params 可変長さ引数(内部から呼出す時に使用するパラメータ)
     * @return LineInfoAcquisitionOutputDto 回線基本情報参照取得アウトプット
     */
    async service(param: LineInfoAcquisitionInputDto, ...params: any[]): Promise<LineInfoAcquisitionBaseOutputDto> {
        const receivedDate: string = MvnoUtil.getDateTimeNow();
        let cheatCode: string = ResultCdConstants.CODE_000000;
        const {
            requestHeader: { sequenceNo, senderSystemId, apiKey, functionType },
            tenantId,
            lineNo,
            /** SO-ID(予約実行呼出し専用) */
            reserve_soId: reserveSoId,
            /** 予約フラグ */
            reserve_flag: reserveFlg,
        } = param;
        const addOption = getStringParameter(param.addOption);
        const version = getStringParameter(param.version);

        const errorMessage: string = null;
        let case1OutputDto: LineInfoAcquisitionCase1BaseOutputDto = null;
        let case2OutputDto: LineInfoAcquisitionCase2BaseOutputDto = null;
        let case3OutputDto: LineInfoAcquisitionCase3BaseOutputDto = null;
        let linesEntity: LinesEntity = null;
        let plansList: PlansEntity[] = [];
        const apiProcessID = this.context.responseHeader?.apiProcessID ?? "";
        let linesGroupId: string = null;
        let lineOptionsNameList: string[] = null;
        const isPrivate = false;                                // NOTE: removed from service, so assume not to return error message

        try {
            // 業務チェック処理
            // １　回線番号フォーマットチェック
            if (!Check.checkLineNo(lineNo)) {
                cheatCode = ResultCdConstants.CODE_010101;
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APLBIW0001, LineInfoAcquisitionService.LINE_NO, lineNo);
                if (addOption === "1") {
                    case2OutputDto =
                            this.returnEdit2(param, cheatCode, apiProcessID, addOption, lineNo, tenantId, linesEntity, linesGroupId, lineOptionsNameList, errorMessage, isPrivate, receivedDate);
                } else if (addOption === "9") {
                    case3OutputDto =
                            this.returnEdit3(param, cheatCode, apiProcessID, addOption, lineNo, tenantId, linesEntity, linesGroupId, lineOptionsNameList, errorMessage, isPrivate, receivedDate);
                } else {
                    case1OutputDto =
                            this.returnEdit1(param, cheatCode, apiProcessID, addOption, lineNo, tenantId, linesEntity, linesGroupId, lineOptionsNameList, errorMessage, isPrivate, receivedDate);
                }
                super.debug(param.tenantId,param.requestHeader.sequenceNo,MsgKeysConstants.APLBID0002,
                    this.getClassName(), "service");
                if (addOption === "1") {
                    return case2OutputDto;
                } else if (addOption === "9") {
                    return case3OutputDto;
                } else {
                    return case1OutputDto;
                }
            }
            // ２　追加オプションチェック
            if (!CheckUtil.checkIsNotNull(addOption)) {
                if (LineInfoAcquisitionService.CONST_SHANAITENANTID_1 !== addOption) {
                    if (LineInfoAcquisitionService.CONST_SHANAITENANTID_9 !== addOption) {
                        cheatCode = ResultCdConstants.CODE_010101;
                        super.warn(tenantId, sequenceNo, MsgKeysConstants.APLBIW0001, LineInfoAcquisitionService.ADD_OPTION, addOption);

                        case1OutputDto =
                             this.returnEdit1(param, cheatCode, apiProcessID, addOption, lineNo, tenantId, linesEntity, linesGroupId, lineOptionsNameList, errorMessage, isPrivate, receivedDate);

                        super.debug(param.tenantId,param.requestHeader.sequenceNo,MsgKeysConstants.APLBID0002,
                                this.getClassName(), "service");
                        return case1OutputDto;
                    }
                }
            }
            // バージョンチェック
            if (!CheckUtil.checkIsNotNull(version)) {
                if (version !== "1" && version !== "2") {
                    cheatCode = ResultCdConstants.CODE_010101;
                    super.warn(tenantId, sequenceNo, MsgKeysConstants.APLBIW0001, "バージョン", version);
                    if (addOption === "1") {
                        case2OutputDto = this.returnEdit2(param, cheatCode, apiProcessID, addOption, lineNo, tenantId,
                                                     linesEntity, linesGroupId, lineOptionsNameList, errorMessage,
                                                     isPrivate, receivedDate);
                    } else if (addOption === "9") {
                        case3OutputDto = this.returnEdit3(param, cheatCode, apiProcessID, addOption, lineNo, tenantId,
                                                     linesEntity, linesGroupId, lineOptionsNameList, errorMessage,
                                                     isPrivate, receivedDate);
                    } else {
                        case1OutputDto = this.returnEdit1(param, cheatCode, apiProcessID, addOption, lineNo, tenantId,
                                                     linesEntity, linesGroupId, lineOptionsNameList, errorMessage,
                                                     isPrivate, receivedDate);
                    }
                    if (addOption === "1") {
                        return case2OutputDto;
                    } else if (addOption === "9") {
                        return case3OutputDto;
                    } else {
                        return case1OutputDto;
                    }
                }
            }

            /** STEP1.2a版　仕様変更（161～163）　削除　START */
            // Lots of commented code, 回線テナント所属チェック
            /** STEP1.2a版　仕様変更（161～163）　削除　END */

            // 回線IDと対象回線の所属テナントIDの関係をチェックする
            let tenantCheckResult: [boolean, string];
            try {
                tenantCheckResult = await this.tenantManage.doCheck(param.lineNo, param.tenantId);
            } catch (e) {
                if (isSQLException(e)) {
                    cheatCode = ResultCdConstants.CODE_010102;
                }
                throw e;
            }

            if (!tenantCheckResult[0]) {
                cheatCode = ResultCdConstants.CODE_010102;
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APLBIW0002, lineNo, tenantId);
                if (addOption === "1") {
                    case2OutputDto = this.returnEdit2(param, cheatCode, apiProcessID, addOption,lineNo, tenantId, linesEntity, linesGroupId, lineOptionsNameList, errorMessage, isPrivate, receivedDate);
                } else if (addOption === "9") {
                    case3OutputDto = this.returnEdit3(param, cheatCode, apiProcessID, addOption,lineNo, tenantId, linesEntity, linesGroupId, lineOptionsNameList, errorMessage, isPrivate, receivedDate);
                } else {
                    case1OutputDto = this.returnEdit1(param, cheatCode, apiProcessID, addOption,lineNo, tenantId, linesEntity, linesGroupId, lineOptionsNameList, errorMessage, isPrivate, receivedDate);
                }
                super.debug(param.tenantId,param.requestHeader.sequenceNo,MsgKeysConstants.APLBID0002,
                        this.getClassName(), "service");
                if (addOption === "1") {
                    return case2OutputDto;
                } else if (addOption === "9") {
                    return case3OutputDto;
                } else {
                    return case1OutputDto;
                }
            }

            // ４　社内テナントIDチェック
            if (LineInfoAcquisitionService.CONST_SHANAITENANTID_9 === addOption) {
                // 変数「テナント情報」．「社内フラグ」判断
                let tenantsEntity: TenantsEntity = null;
                try {
                    tenantsEntity = await this.apiCommonDao.getTenants(tenantId);
                } catch (e: any) {
                    if (isSQLException(e)) {
                        // DBアクセスリトライエラーの場合
                        cheatCode = ResultCdConstants.CODE_010103;
                    }
                    throw e;
                }
                let flag = false;
                flag = tenantsEntity.office;
                if (!flag) {
                    cheatCode = ResultCdConstants.CODE_010103;
                    super.warn(tenantId, sequenceNo, MsgKeysConstants.APLBIW0003, tenantId);
                    case3OutputDto
                        = this.returnEdit3(param, cheatCode, apiProcessID, addOption, lineNo, tenantId, linesEntity,
                                      linesGroupId, lineOptionsNameList, errorMessage, isPrivate, receivedDate);
                    super.debug(param.tenantId, param.requestHeader.sequenceNo,
                                MsgKeysConstants.APLBID0002, this.getClassName(), "service");
                    return case3OutputDto;
                }
            }

            // ロジック処理
            // １　回線基本情報取得
            try {
                linesEntity = await this.apiLinesDao.getLineInfo(lineNo);
            } catch (e: any) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    cheatCode = ResultCdConstants.CODE_010201;
                }
                throw e;
            }

            // 変数「回線基本情報」=null
            if (linesEntity === null || linesEntity === undefined) {
                cheatCode = ResultCdConstants.CODE_010201;
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APLBIW0004);
                if (addOption === "1") {
                    case2OutputDto = this.returnEdit2(param, cheatCode, apiProcessID, addOption, lineNo, tenantId, linesEntity, linesGroupId, lineOptionsNameList, errorMessage, isPrivate, receivedDate);
                } else if (addOption === "9") {
                    case3OutputDto = this.returnEdit3(param, cheatCode, apiProcessID, addOption, lineNo, tenantId, linesEntity, linesGroupId, lineOptionsNameList, errorMessage, isPrivate, receivedDate);
                } else {
                    case1OutputDto = this.returnEdit1(param, cheatCode, apiProcessID, addOption, lineNo, tenantId, linesEntity, linesGroupId, lineOptionsNameList, errorMessage, isPrivate, receivedDate);
                }
                super.debug(param.tenantId,param.requestHeader.sequenceNo,MsgKeysConstants.APLBID0002,
                        this.getClassName(), "service");
                if (addOption === "1") {
                    return case2OutputDto;
                } else if (addOption === "9") {
                    return case3OutputDto;
                } else {
                    return case1OutputDto;
                }
            }

            // 回線情報の再販料金プランチェック
            if (!CheckUtil.checkIsNotNull(linesEntity.pricePlanId)) {
                // STEP20.0版対応　変更　START
                try {
                    plansList = await this.apiLinesDao.getPlanInfo(linesEntity.pricePlanId);
                    // プラン情報がなしと複数である場合
                    if (plansList === null || plansList === undefined || plansList.length === 0) {
                        linesEntity.planName = "";
                    } else if (plansList.length !== 1) {
                        cheatCode = ResultCdConstants.CODE_010201;
                        super.warn(tenantId, sequenceNo, MsgKeysConstants.APLBIW0004);
                        if (addOption === "1") {
                            case2OutputDto
                                = this.returnEdit2(param, cheatCode, apiProcessID, addOption, lineNo, tenantId,
                                              linesEntity, linesGroupId, lineOptionsNameList, errorMessage,
                                              isPrivate, receivedDate);
                        } else if (addOption === "9") {
                            case3OutputDto
                                = this.returnEdit3(param, cheatCode, apiProcessID, addOption, lineNo, tenantId,
                                              linesEntity, linesGroupId, lineOptionsNameList, errorMessage,
                                              isPrivate, receivedDate);
                        } else {
                            case1OutputDto
                                = this.returnEdit1(param, cheatCode, apiProcessID, addOption, lineNo, tenantId,
                                              linesEntity, linesGroupId, lineOptionsNameList, errorMessage,
                                              isPrivate, receivedDate);
                        }
                        super.debug(param.tenantId, param.requestHeader.sequenceNo,
                                     MsgKeysConstants.APLBID0002, this.getClassName(), "service");
                        if (addOption === "1") {
                            return case2OutputDto;
                        } else if (addOption === "9") {
                            return case3OutputDto;
                        } else {
                            return case1OutputDto;
                        }
                    } else {
                        linesEntity.planId = String(plansList[0].planId);
                        linesEntity.planName = plansList[0].planName;
                    }
                } catch (e: any) {
                    // DBアクセスリトライエラーの場合
                    // プラン情報をなしとする
                    if (isSQLException(e)) {
                        linesEntity.planName = "";
                    } else {
                        throw e;
                    }
                }
            }

            try {
                linesGroupId = await this.apiLinesDao.getLinesGroupId(linesEntity.lineId);
                // 回線グループIDがNULLの場合、回線グループIDを""に設定する
                if (linesGroupId === null || linesGroupId === undefined) {
                    linesGroupId = "";
                }
            } catch (e: any) {
                // DBアクセスリトライエラーの場合
                // 回線グループIDをなしとする
                if (isSQLException(e)) {
                    linesGroupId = "";
                } else {
                    throw e;
                }
            }

            try {
                // 回線オプション名取得
                lineOptionsNameList = await this.apiLinesDao.getLineOptionsName(lineNo);
                if (lineOptionsNameList === null || lineOptionsNameList === undefined || lineOptionsNameList.length === 0) {
                    cheatCode = ResultCdConstants.CODE_010201;
                    super.warn(tenantId, sequenceNo, MsgKeysConstants.APLBIW0004);
                }
            } catch (e: any) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    cheatCode = ResultCdConstants.CODE_010201;
                } else {
                    throw e;
                }
                super.error(e, tenantId, sequenceNo, MsgKeysConstants.APCOME0402, functionType);
            }

            if (addOption === "1") {
                case2OutputDto = this.returnEdit2(param, cheatCode, apiProcessID, addOption, lineNo, tenantId, linesEntity, linesGroupId, lineOptionsNameList, errorMessage, isPrivate, receivedDate);
            } else if (addOption === "9") {
                case3OutputDto = this.returnEdit3(param, cheatCode, apiProcessID, addOption, lineNo, tenantId, linesEntity, linesGroupId, lineOptionsNameList, errorMessage, isPrivate, receivedDate);
            } else {
                case1OutputDto = this.returnEdit1(param, cheatCode, apiProcessID, addOption, lineNo, tenantId, linesEntity, linesGroupId, lineOptionsNameList, errorMessage, isPrivate, receivedDate);
            }
            super.debug(param.tenantId, param.requestHeader.sequenceNo, MsgKeysConstants.APLBID0002,
                     this.getClassName(), "service");

            if (addOption === "1") {
                return case2OutputDto;
            } else if (addOption === "9") {
                return case3OutputDto;
            } else {
                return case1OutputDto;
            }

        } catch (error: any) {
            if (!isSQLException(error)) {
                cheatCode = ResultCdConstants.CODE_999999;
            }
            super.error(
                tenantId,
                sequenceNo,
                MsgKeysConstants.APCOME0401,
                error.message,
            );
            if (addOption === "1") {
                case2OutputDto = this.returnEdit2(param, cheatCode, apiProcessID, addOption, lineNo, tenantId, linesEntity, linesGroupId, lineOptionsNameList, errorMessage, isPrivate, receivedDate);
            } else if (addOption === "9") {
                case3OutputDto = this.returnEdit3(param, cheatCode, apiProcessID, addOption, lineNo, tenantId, linesEntity, linesGroupId, lineOptionsNameList, errorMessage, isPrivate, receivedDate);
            } else {
                case1OutputDto = this.returnEdit1(param, cheatCode, apiProcessID, addOption, lineNo, tenantId, linesEntity, linesGroupId, lineOptionsNameList, errorMessage, isPrivate, receivedDate);
            }

            super.debug(tenantId, sequenceNo, MsgKeysConstants.APLBID0002,
                "LineInfoAcquisitionService", "service");

            if (addOption === "1") {
                return case2OutputDto;
            } else if (addOption === "9") {
                return case3OutputDto;
            }
            return case1OutputDto;
        }       // no need to super.decTenantConnectCount and super.modConCount as they already done inside base handler
    }

    /**
     *  回線の基本情報参照CASE1
     * @param pram 回線の基本情報参照Dto
     * @param rtncheatCode 変数「処理コード」
     * @param rtnapiProcessID 変数 「API処理ID」
     * @param strAddOption 変数 「追加オプション」
     * @param strLineNO 変数 「回線番号」
     * @param strTenantID 変数 「テナントID」
     * @param retninesEntity 変数 「回線情報エンティティ」
     * @param linesGroupId 変数「回線グループID」
     * @param lineOptionsNameLsit 変数「回線オプション名リスト」
     * @param errorMessage 変数「エラーメッセージ」
     * @param isPrivate 内部呼出フラグ
     * @param receivedDate REST API共通処理が呼び出されたシステム日付
     * @return 回線基本情報取得アウトプット
     */
    public returnEdit1(pram: LineInfoAcquisitionInputDto, rtncheatCode: string,
                       rtnapiProcessID: string, strAddOption: string, strLineNO: string, strTenantID: string, retninesEntity: LinesEntity,
                       linesGroupId: string, lineOptionsNameLsit: string[], errorMessage: string, isPrivate: boolean, receivedDate: string):
        LineInfoAcquisitionCase1BaseOutputDto {

        const responseHeader: ResponseHeader = {
            sequenceNo: pram.requestHeader.sequenceNo,
            receivedDate,
            processCode: rtncheatCode,
            apiProcessID: rtnapiProcessID,
        };

        // 内部呼出フラグ = true
        if (isPrivate) {
            const returnPram: LineInfoAcquisitionCase1InsideOutputDto = {
                jsonBody: {
                    responseHeader,
                    groupA: null,
                    errorMessage: "",
                },
            };

            // 変数「処理コード」<>000000である場合
            if (rtncheatCode !== ResultCdConstants.CODE_000000) {
                if (StringUtils.isEmpty(errorMessage)) {
                    returnPram.jsonBody.errorMessage = super.getMessage(MsgKeysConstants.APCOMW0001, ["回線基本情報参照"]);
                } else {
                    returnPram.jsonBody.errorMessage = errorMessage;
                }
                returnPram.jsonBody.groupA = this.getEmptyGroupADto();
                return returnPram;
            } else {
                returnPram.jsonBody.errorMessage = "";
                if (retninesEntity === null || retninesEntity === undefined) {
                    returnPram.jsonBody.groupA = this.getEmptyGroupADto();
                    return returnPram;
                }
                returnPram.jsonBody.groupA = this.returnGroupADto(strLineNO, strTenantID, retninesEntity, linesGroupId, lineOptionsNameLsit);
            }
            return returnPram;
        } else {
            const emptyGroupADto = this.getEmptyGroupADto();
            // 変数「処理コード」<>000000である場合
            if (rtncheatCode !== ResultCdConstants.CODE_000000) {
                if (getStringParameter(pram.version) === "1") {
                    const groupA2Dto: GroupA2Dto = {
                        ...emptyGroupADto,
                        simNo: MvnoUtil.addSpaceAfterStr(null, 0),
                        imsi: MvnoUtil.addSpaceAfterStr(null, 0),
                        puk1: MvnoUtil.addSpaceAfterStr(null, 0),
                        puk2: MvnoUtil.addSpaceAfterStr(null, 0),
                    };
                    const returnPramVersion: LineInfoAcquisitionCase1VersionOutputDto = {
                        jsonBody: {
                            responseHeader,
                            groupA: groupA2Dto,
                        },
                    };
                    return returnPramVersion;
                } else if (getStringParameter(pram.version) === "2") {
                    const groupA3Dto: GroupA3Dto = {
                        ...emptyGroupADto,
                        voicePlanId: MvnoUtil.addSpaceAfterStr(null, 0),
                        voicePlanName: MvnoUtil.addSpaceAfterStr(null, 0),
                    };
                    const returnPramVersion2: LineInfoAcquisitionCase1Version2OutputDto = {
                        jsonBody: {
                            responseHeader,
                            groupA: groupA3Dto,
                        },
                    };
                    return returnPramVersion2;
                } else {
                    const groupADto: GroupADto = emptyGroupADto;
                    const returnPram: LineInfoAcquisitionCase1OutputDto = {
                        jsonBody: {
                            responseHeader,
                            groupA: groupADto,
                        },
                    };
                    return returnPram;
                }
            } else {
                // 変数 「回線情報エンティティ」 = null
                if (retninesEntity === null || retninesEntity === undefined) {
                    if (getStringParameter(pram.version) === "1") {
                        const groupA2Dto: GroupA2Dto = {
                            ...emptyGroupADto,
                            simNo: MvnoUtil.addSpaceAfterStr(null, 0),
                            imsi: MvnoUtil.addSpaceAfterStr(null, 0),
                            puk1: MvnoUtil.addSpaceAfterStr(null, 0),
                            puk2: MvnoUtil.addSpaceAfterStr(null, 0),
                        };
                        const returnPramVersion: LineInfoAcquisitionCase1VersionOutputDto = {
                            jsonBody: {
                                responseHeader,
                                groupA: groupA2Dto,
                            },
                        };
                        return returnPramVersion;
                    } else if (getStringParameter(pram.version) === "2") {
                        const groupA3Dto: GroupA3Dto = {
                            ...emptyGroupADto,
                            voicePlanId: MvnoUtil.addSpaceAfterStr(null, 0),
                            voicePlanName: MvnoUtil.addSpaceAfterStr(null, 0),
                        };
                        const returnPramVersion2: LineInfoAcquisitionCase1Version2OutputDto = {
                            jsonBody: {
                                responseHeader,
                                groupA: groupA3Dto,
                            },
                        };
                        return returnPramVersion2;
                    }
                    const groupADto: GroupADto = emptyGroupADto;
                    const returnPram: LineInfoAcquisitionCase1OutputDto = {
                        jsonBody: {
                            responseHeader,
                            groupA: groupADto,
                        },
                    };
                    return returnPram;
                }
                const groupADto = this.returnGroupADto(strLineNO, strTenantID, retninesEntity, linesGroupId, lineOptionsNameLsit);
                if (getStringParameter(pram.version) === "1") {
                    const groupA2Dto: GroupA2Dto = {
                        ...groupADto,
                        simNo: CheckUtil.returnPrams(retninesEntity.simNumber, 0),
                        imsi: CheckUtil.returnPrams(retninesEntity.imsi, 0),
                        puk1: CheckUtil.returnPrams(retninesEntity.puk1, 0),
                        puk2: CheckUtil.returnPrams(retninesEntity.puk2, 0),
                    };
                    const returnPramVersion: LineInfoAcquisitionCase1VersionOutputDto = {
                        jsonBody: {
                            responseHeader,
                            groupA: groupA2Dto,
                        },
                    };
                    return returnPramVersion;
                } else if (getStringParameter(pram.version) === "2") {
                    const groupA3Dto: GroupA3Dto = {
                        ...groupADto,
                        voicePlanId: CheckUtil.returnPrams(retninesEntity.voicePlanId, 0),
                        voicePlanName: CheckUtil.returnPrams(retninesEntity.voicePlanName, 0),
                    };
                    const returnPramVersion2: LineInfoAcquisitionCase1Version2OutputDto = {
                        jsonBody: {
                            responseHeader,
                            groupA: groupA3Dto,
                        },
                    };
                    return returnPramVersion2;
                } else {
                    const returnPram: LineInfoAcquisitionCase1OutputDto = {
                        jsonBody: {
                            responseHeader,
                            groupA: groupADto,
                        },
                    };
                    return returnPram;
                }
            }
        }
    }

    /**
     *  回線の基本情報参照CASE2
     * @param pram 回線の基本情報参照Dto
     * @param rtncheatCode 変数 「API処理ID」
     * @param rtnapiProcessID 変数 「API処理ID」
     * @param strAddOption 変数 「追加オプション」
     * @param strLineNO 変数 「回線番号」
     * @param strTenantID 変数 「テナントID」
     * @param retninesEntity 変数 「回線情報エンティティ」
     * @param linesGroupId 変数「回線グループID」
     * @param lineOptionsNameLsit 変数「回線オプション名リスト」
     * @param receivedDate REST API共通処理が呼び出されたシステム日付
     * @return 回線基本情報取得アウトプット
     */
    public returnEdit2(pram: LineInfoAcquisitionInputDto, rtncheatCode: string,
                       rtnapiProcessID: string, strAddOption: string, strLineNO: string, strTenantID: string, retninesEntity: LinesEntity,
                       linesGroupId: string, lineOptionsNameLsit: string[], errorMessage: string, isPrivate: boolean, receivedDate: string):
        LineInfoAcquisitionCase2BaseOutputDto {
        const responseHeader: ResponseHeader = {
            sequenceNo: pram.requestHeader.sequenceNo,
            receivedDate,
            processCode: rtncheatCode,
            apiProcessID: rtnapiProcessID,
        };

        if (isPrivate) {
            // 回線の基本情報参照Case2Dtoクラス(内部)を定義する
            const returnPram: LineInfoAcquisitionCase2InsideOutputDto = {
                jsonBody: {
                    // ヘッダを設定する
                    responseHeader,
                    groupA: null,
                    groupB: null,
                    errorMessage: "",
                },
            };
            // 変数「処理コード」<>000000である場合
            if (rtncheatCode !== ResultCdConstants.CODE_000000) {
                // 変数「エラーメッセージ」=空白の場合
                if (StringUtils.isEmpty(errorMessage)) {
                    // エラーメッセージを設定する
                    returnPram.jsonBody.errorMessage = super.getMessage(MsgKeysConstants.APCOMW0001, ["回線基本情報参照"]);
                } else {
                    // エラーメッセージを設定する
                    returnPram.jsonBody.errorMessage = errorMessage;
                }
                returnPram.jsonBody.groupA = this.getEmptyGroupADto();
                returnPram.jsonBody.groupB = this.getEmptyGroupBDto();
                return returnPram;
            } else {
                // エラーメッセージを設定する
                returnPram.jsonBody.errorMessage = "";
                // 変数 「回線情報エンティティ」 = null
                if (retninesEntity === null || retninesEntity === undefined) {
                    returnPram.jsonBody.groupA = this.getEmptyGroupADto();
                    returnPram.jsonBody.groupB = this.getEmptyGroupBDto();
                    return returnPram;
                }
                // 変換編集などをしたデータをDTOに格納する
                returnPram.jsonBody.groupA = this.returnGroupADto(strLineNO, strTenantID, retninesEntity, linesGroupId, lineOptionsNameLsit);
                returnPram.jsonBody.groupB = this.returnGroupBDto(retninesEntity);
            }
            return returnPram;
        } else {
            const emptyGroupADto = this.getEmptyGroupADto();
            const emptyGroupBDto = this.getEmptyGroupBDto();

            // 変数「処理コード」<>000000である場合
            if (rtncheatCode !== ResultCdConstants.CODE_000000) {
                const groupBDto = emptyGroupBDto;
                if (getStringParameter(pram.version) === "1") {
                    const groupA2Dto: GroupA2Dto = {
                        ...emptyGroupADto,
                        simNo: MvnoUtil.addSpaceAfterStr(null, 0),
                        imsi: MvnoUtil.addSpaceAfterStr(null, 0),
                        puk1: MvnoUtil.addSpaceAfterStr(null, 0),
                        puk2: MvnoUtil.addSpaceAfterStr(null, 0),
                    };
                    const returnPramVersion: LineInfoAcquisitionCase2VersionOutputDto = {
                        jsonBody: {
                            responseHeader,
                            groupA: groupA2Dto,
                            groupB: groupBDto,
                        },
                    };
                    return returnPramVersion;
                } else if (getStringParameter(pram.version) === "2") {
                    const groupA3Dto: GroupA3Dto = {
                        ...emptyGroupADto,
                        voicePlanId: MvnoUtil.addSpaceAfterStr(null, 0),
                        voicePlanName: MvnoUtil.addSpaceAfterStr(null, 0),
                    };
                    const returnPramVersion2: LineInfoAcquisitionCase2Version2OutputDto = {
                        jsonBody: {
                            responseHeader,
                            groupA: groupA3Dto,
                            groupB: groupBDto,
                        },
                    };
                    return returnPramVersion2;
                } else {
                    const returnPram: LineInfoAcquisitionCase2OutputDto = {
                        jsonBody: {
                            responseHeader,
                            groupA: emptyGroupADto,
                            groupB: groupBDto,
                        },
                    };
                    return returnPram;
                }
            } else {
                if (retninesEntity === null || retninesEntity === undefined) {
                    const groupBDto = emptyGroupBDto;
                    if (getStringParameter(pram.version) === "1") {
                        const groupA2Dto: GroupA2Dto = {
                            ...emptyGroupADto,
                            simNo: MvnoUtil.addSpaceAfterStr(null, 0),
                            imsi: MvnoUtil.addSpaceAfterStr(null, 0),
                            puk1: MvnoUtil.addSpaceAfterStr(null, 0),
                            puk2: MvnoUtil.addSpaceAfterStr(null, 0),
                        };
                        const returnPramVersion: LineInfoAcquisitionCase2VersionOutputDto = {
                            jsonBody: {
                                responseHeader,
                                groupA: groupA2Dto,
                                groupB: groupBDto,
                            },
                        };
                        return returnPramVersion;
                    } else if (getStringParameter(pram.version) === "2") {
                        const groupA3Dto: GroupA3Dto = {
                            ...emptyGroupADto,
                            voicePlanId: MvnoUtil.addSpaceAfterStr(null, 0),
                            voicePlanName: MvnoUtil.addSpaceAfterStr(null, 0),
                        };
                        const returnPramVersion2: LineInfoAcquisitionCase2Version2OutputDto = {
                            jsonBody: {
                                responseHeader,
                                groupA: groupA3Dto,
                                groupB: groupBDto,
                            },
                        };
                        return returnPramVersion2;
                    }
                    const groupADto = emptyGroupADto;
                    const returnPram: LineInfoAcquisitionCase2OutputDto = {
                        jsonBody: {
                            responseHeader,
                            groupA: groupADto,
                            groupB: groupBDto,
                        },
                    };
                    return returnPram;
                }
                const groupADto = this.returnGroupADto(strLineNO, strTenantID, retninesEntity, linesGroupId, lineOptionsNameLsit);
                const groupBDto = this.returnGroupBDto(retninesEntity);
                if (getStringParameter(pram.version) === "1") {
                    const groupA2Dto: GroupA2Dto = {
                        ...groupADto,
                        simNo: CheckUtil.returnPrams(retninesEntity.simNumber, 0),
                        imsi: CheckUtil.returnPrams(retninesEntity.imsi, 0),
                        puk1: CheckUtil.returnPrams(retninesEntity.puk1, 0),
                        puk2: CheckUtil.returnPrams(retninesEntity.puk2, 0),
                    };
                    const returnPramVersion: LineInfoAcquisitionCase2VersionOutputDto = {
                        jsonBody: {
                            responseHeader,
                            groupA: groupA2Dto,
                            groupB: groupBDto,
                        },
                    };
                    return returnPramVersion;
                } else if (getStringParameter(pram.version) === "2") {
                    const groupA3Dto: GroupA3Dto = {
                        ...groupADto,
                        voicePlanId: CheckUtil.returnPrams(retninesEntity.voicePlanId, 0),
                        voicePlanName: CheckUtil.returnPrams(retninesEntity.voicePlanName, 0),
                    };
                    const returnPramVersion2: LineInfoAcquisitionCase2Version2OutputDto = {
                        jsonBody: {
                            responseHeader,
                            groupA: groupA3Dto,
                            groupB: groupBDto,
                        },
                    };
                    return returnPramVersion2;
                }
                const returnPram: LineInfoAcquisitionCase2OutputDto = {
                    jsonBody: {
                        responseHeader,
                        groupA: groupADto,
                        groupB: groupBDto,
                    },
                };
                return returnPram;
            }
        }
    }

    /**
     *  回線の基本情報参照CASE3
     * @param  pram 回線の基本情報参照Dto
     * @param  rtncheatCode 変数 「API処理ID」
     * @param  rtnapiProcessID 変数 「API処理ID」
     * @param  strAddOption 変数 「追加オプション」
     * @param  strLineNO 変数 「回線番号」
     * @param  strTenantID 変数 「テナントID」
     * @param  retninesEntity 変数 「回線情報エンティティ」
     * @param  linesGroupId 変数「回線グループID」
     * @param lineOptionsNameLsit 変数「回線オプション名リスト」
     * @param receivedDate REST API共通処理が呼び出されたシステム日付
     * @return 回線基本情報取得アウトプット
     */
    public returnEdit3(pram: LineInfoAcquisitionInputDto, rtncheatCode: string,
                       rtnapiProcessID: string, strAddOption: string, strLineNO: string, strTenantID: string, retninesEntity: LinesEntity,
                       linesGroupId: string, lineOptionsNameLsit: string[], errorMessage: string, isPrivate: boolean, receivedDate: string):
        LineInfoAcquisitionCase3BaseOutputDto {
        const responseHeader: ResponseHeader = {
            sequenceNo: pram.requestHeader.sequenceNo,
            receivedDate,
            processCode: rtncheatCode,
            apiProcessID: rtnapiProcessID,
        };

        if (isPrivate) {
            // 回線の基本情報参照Case3Dtoクラス(内部)を定義する
            const returnPram: LineInfoAcquisitionCase3InsideOutputDto = {
                jsonBody: {
                    // ヘッダを設定する
                    responseHeader,
                    groupA: null,
                    groupB: null,
                    groupC: null,
                    errorMessage: "",
                },
            };
            // 変数「処理コード」<>000000である場合
            if (rtncheatCode !== ResultCdConstants.CODE_000000) {
                // 変数「エラーメッセージ」=空白の場合
                if (StringUtils.isEmpty(errorMessage)) {
                    // エラーメッセージを設定する
                    returnPram.jsonBody.errorMessage = super.getMessage(MsgKeysConstants.APCOMW0001, ["回線基本情報参照"]);
                } else {
                    // エラーメッセージを設定する
                    returnPram.jsonBody.errorMessage = errorMessage;
                }
                returnPram.jsonBody.groupA = this.getEmptyGroupADto();
                returnPram.jsonBody.groupB = this.getEmptyGroupBDto();
                returnPram.jsonBody.groupC = this.getEmptyGroupCDto();
                return returnPram;
            } else {
                // エラーメッセージを設定する
                returnPram.jsonBody.errorMessage = "";
                // 変数 「回線情報エンティティ」 = null
                if (retninesEntity === null || retninesEntity === undefined) {
                    returnPram.jsonBody.groupA = this.getEmptyGroupADto();
                    returnPram.jsonBody.groupB = this.getEmptyGroupBDto();
                    returnPram.jsonBody.groupC = this.getEmptyGroupCDto();
                    return returnPram;
                }
                // 変換編集などをしたデータをDTOに格納する
                returnPram.jsonBody.groupA = this.returnGroupADto(strLineNO, strTenantID, retninesEntity, linesGroupId, lineOptionsNameLsit);
                returnPram.jsonBody.groupB = this.returnGroupBDto(retninesEntity);
                returnPram.jsonBody.groupC = this.returnGroupCDto(retninesEntity);
            }
            return returnPram;
        } else {
            const emptyGroupADto = this.getEmptyGroupADto();
            const emptyGroupBDto = this.getEmptyGroupBDto();
            const emptyGroupCDto = this.getEmptyGroupCDto();

            // 変数「処理コード」<>000000である場合
            if (rtncheatCode !== ResultCdConstants.CODE_000000) {
                const groupBDto = emptyGroupBDto;
                const groupCDto = emptyGroupCDto;
                if (getStringParameter(pram.version) === "1") {
                    const groupA2Dto: GroupA2Dto = {
                        ...emptyGroupADto,
                        simNo: MvnoUtil.addSpaceAfterStr(null, 0),
                        imsi: MvnoUtil.addSpaceAfterStr(null, 0),
                        puk1: MvnoUtil.addSpaceAfterStr(null, 0),
                        puk2: MvnoUtil.addSpaceAfterStr(null, 0),
                    };
                    const returnPramVersion: LineInfoAcquisitionCase3VersionOutputDto = {
                        jsonBody: {
                            responseHeader,
                            groupA: groupA2Dto,
                            groupB: groupBDto,
                            groupC: groupCDto,
                        },
                    };
                    return returnPramVersion;
                } else if (getStringParameter(pram.version) === "2") {
                    const groupA3Dto: GroupA3Dto = {
                        ...emptyGroupADto,
                        voicePlanId: MvnoUtil.addSpaceAfterStr(null, 0),
                        voicePlanName: MvnoUtil.addSpaceAfterStr(null, 0),
                    };
                    const returnPramVersion2: LineInfoAcquisitionCase3Version2OutputDto = {
                        jsonBody: {
                            responseHeader,
                            groupA: groupA3Dto,
                            groupB: groupBDto,
                            groupC: groupCDto,
                        },
                    };
                    return returnPramVersion2;
                } else {
                    const returnPram: LineInfoAcquisitionCase3OutputDto = {
                        jsonBody: {
                            responseHeader,
                            groupA: emptyGroupADto,
                            groupB: groupBDto,
                            groupC: groupCDto,
                        },
                    };
                    return returnPram;
                }
            } else {
                if (retninesEntity === null || retninesEntity === undefined) {
                    const groupBDto = emptyGroupBDto;
                    const groupCDto = emptyGroupCDto;
                    if (getStringParameter(pram.version) === "1") {
                        const groupA2Dto: GroupA2Dto = {
                            ...emptyGroupADto,
                            simNo: MvnoUtil.addSpaceAfterStr(null, 0),
                            imsi: MvnoUtil.addSpaceAfterStr(null, 0),
                            puk1: MvnoUtil.addSpaceAfterStr(null, 0),
                            puk2: MvnoUtil.addSpaceAfterStr(null, 0),
                        };
                        const returnPramVersion: LineInfoAcquisitionCase3VersionOutputDto = {
                            jsonBody: {
                                responseHeader,
                                groupA: groupA2Dto,
                                groupB: groupBDto,
                                groupC: groupCDto,
                            },
                        };
                        return returnPramVersion;
                    } else if (getStringParameter(pram.version) === "2") {
                        const groupA3Dto: GroupA3Dto = {
                            ...emptyGroupADto,
                            voicePlanId: MvnoUtil.addSpaceAfterStr(null, 0),
                            voicePlanName: MvnoUtil.addSpaceAfterStr(null, 0),
                        };
                        const returnPramVersion2: LineInfoAcquisitionCase3Version2OutputDto = {
                            jsonBody: {
                                responseHeader,
                                groupA: groupA3Dto,
                                groupB: groupBDto,
                                groupC: groupCDto,
                            },
                        };
                        return returnPramVersion2;
                    }
                    const groupADto = emptyGroupADto;
                    const returnPram: LineInfoAcquisitionCase3OutputDto = {
                        jsonBody: {
                            responseHeader,
                            groupA: groupADto,
                            groupB: groupBDto,
                            groupC: groupCDto,
                        },
                    };
                    return returnPram;
                }
                const groupADto = this.returnGroupADto(strLineNO, strTenantID, retninesEntity, linesGroupId, lineOptionsNameLsit);
                const groupBDto = this.returnGroupBDto(retninesEntity);
                const groupCDto = this.returnGroupCDto(retninesEntity);
                if (getStringParameter(pram.version) === "1") {
                    const groupA2Dto: GroupA2Dto = {
                        ...groupADto,
                        simNo: CheckUtil.returnPrams(retninesEntity.simNumber, 0),
                        imsi: CheckUtil.returnPrams(retninesEntity.imsi, 0),
                        puk1: CheckUtil.returnPrams(retninesEntity.puk1, 0),
                        puk2: CheckUtil.returnPrams(retninesEntity.puk2, 0),
                    };
                    const returnPramVersion: LineInfoAcquisitionCase3VersionOutputDto = {
                        jsonBody: {
                            responseHeader,
                            groupA: groupA2Dto,
                            groupB: groupBDto,
                            groupC: groupCDto,
                        },
                    };
                    return returnPramVersion;
                } else if (getStringParameter(pram.version) === "2") {
                    const groupA3Dto: GroupA3Dto = {
                        ...groupADto,
                        voicePlanId: CheckUtil.returnPrams(retninesEntity.voicePlanId, 0),
                        voicePlanName: CheckUtil.returnPrams(retninesEntity.voicePlanName, 0),
                    };
                    const returnPramVersion2: LineInfoAcquisitionCase3Version2OutputDto = {
                        jsonBody: {
                            responseHeader,
                            groupA: groupA3Dto,
                            groupB: groupBDto,
                            groupC: groupCDto,
                        },
                    };
                    return returnPramVersion2;
                }
                const returnPram: LineInfoAcquisitionCase3OutputDto = {
                    jsonBody: {
                        responseHeader,
                        groupA: groupADto,
                        groupB: groupBDto,
                        groupC: groupCDto,
                    },
                };
                return returnPram;
            }
        }
    }

    /**
     * グループADto取得
     * @param retnEntityA 回線情報テーブル
     * @param linesGroupId 変数「回線グループID」
     * @return groupADto グループA
     */
    private returnGroupADto(lineNo: string, tenantID: string, retnEntityA: LinesEntity,
                            lineGroupID: string, lineOptionsNameLsit: string[]): GroupADto {

        const dateFormat = "yyyy/MM/dd";
        // グループA
        const groupADto: GroupADto = this.getEmptyGroupADto();
        // 回線番号
        groupADto.lineNo = CheckUtil.returnPrams(lineNo, 11);
        // テナントID
        groupADto.tenantId = CheckUtil.returnPrams(tenantID, 6);
        // 代表N番
        groupADto.nBan = CheckUtil.returnPrams(retnEntityA.nnumber, 10);
        // 卸ポータルプランID
        if (retnEntityA.planId === "0") {
            groupADto.potalPlanID = "";
        } else {
            groupADto.potalPlanID = CheckUtil.returnPrams(retnEntityA.planId, 5);
        }
        // 卸ポータルプラン名
        groupADto.potalPlanName = CheckUtil.returnPrams(retnEntityA.planName, 0);
        // 回線グループID
        groupADto.lineGroupID = lineGroupID;
        // カード種別名
        groupADto.cardClassificationName = CheckUtil.returnPrams(retnEntityA.simType, 0);
        // 回線利用開始年月日
        let strDate = MvnoUtil.addSpaceAfterStr(null, 0);
        if (retnEntityA.lineActDate !== null && retnEntityA.lineActDate !== undefined) {
            strDate = format(retnEntityA.lineActDate, dateFormat);
            groupADto.lineUseStartDate = MvnoUtil.addSpaceAfterStr(strDate, 10);
        } else {
            groupADto.lineUseStartDate = strDate;
        }
        // オプション名称設定
        if (lineOptionsNameLsit !== null && lineOptionsNameLsit !== undefined && lineOptionsNameLsit.length === LineInfoAcquisitionService.CONST_LINE_OPTION_NUMBER) {
            // 国際ローミング利用限度額
            groupADto.interationalRoamingCreditLine = CheckUtil.returnPrams(lineOptionsNameLsit[0], 0);
            // 留守番でんわ
            groupADto.voicemail = CheckUtil.returnPrams(lineOptionsNameLsit[1], 0);
            // キャッチホン
            groupADto.callWaiting = CheckUtil.returnPrams(lineOptionsNameLsit[2], 0);
            // 国際電話
            groupADto.intlCall = CheckUtil.returnPrams(lineOptionsNameLsit[3], 0);
            // 転送でんわ
            groupADto.forwarding = CheckUtil.returnPrams(lineOptionsNameLsit[4], 0);
            // 国際着信転送
            groupADto.intlForwarding = CheckUtil.returnPrams(lineOptionsNameLsit[5], 0);
        }
        // 契約種別
        groupADto.contractType = CheckUtil.returnPrams(retnEntityA.contractType, 0);
        // 回線登録状態
        let lineStatus = "0";
        if (retnEntityA.tempRegist === true) {
            lineStatus = "1";
        }
        groupADto.lineRegStatus = lineStatus;
        // SIMステータス (半黒フラグ true：1 false：0)
        if (retnEntityA.simFlag === true) {
            groupADto.simStatus = "1";
        } else {
            groupADto.simStatus = "0";
        }

        // 黒化日
        if (retnEntityA.activateDate !== null && retnEntityA.activateDate !== undefined) {
            const activateDate = format(retnEntityA.activateDate, dateFormat);
            groupADto.activateDate = MvnoUtil.addSpaceAfterStr(activateDate, 10);
        } else {
            groupADto.activateDate = MvnoUtil.addSpaceAfterStr(null, 0);
        }

        return groupADto;
    }

    /**
     * グループBDto取得
     *
     * @param retnEntityB 回線情報テーブル
     * @param strAddOption 追加オプション
     * @return グループB
     */
    private returnGroupBDto(retnEntityB: LinesEntity): GroupBDto {
        let strStartDate = "";
        if (retnEntityB.startDate === null || retnEntityB.startDate === undefined) {
            strStartDate = MvnoUtil.addSpaceAfterStr(null, 0);
        } else {
            strStartDate = MvnoUtil.addSpaceAfterStr(format(retnEntityB.startDate, "yyyy/MM/dd"), 10);
        }
        return {
            // SIM番号(DN番号)
            simNo: CheckUtil.returnPrams(retnEntityB.simNumber, 0),
            // 端末種別名
            terminalClassificationName: CheckUtil.returnPrams(retnEntityB.modelType, 0),
            // 製造番号（IMEI）
            imei: CheckUtil.returnPrams(retnEntityB.imei, 0),
            // 端末利用開始年月日
            terminalUseStartDate: strStartDate,
        };
    }

    /**
     * グループCDto取得
     *
     * @param retnEntityC 回線情報テーブル
     * @param strAddOption 追加オプション
     * @return グループC
     */
    private returnGroupCDto(retnEntityC: LinesEntity): GroupCDto {
        return {
            // ドメイン
            domain: CheckUtil.returnPrams(retnEntityC.domain, 0),
            // 認証ID
            attestationId: CheckUtil.returnPrams(retnEntityC.authId, 0),
            // 国内用IP　ACT東
            kokunaiyoIpACTeast: CheckUtil.returnPrams(retnEntityC.actEastIp, 0),
            // 国内用IP　ACT西
            kokunaiyoIpACTwest: CheckUtil.returnPrams(retnEntityC.actWestIp, 0),
            // 国内用IP　SBY
            kokunaiyoIpACTSBY: CheckUtil.returnPrams(retnEntityC.sbyIp, 0),
            // 国際用固定IPアドレス
            interationalIpAddress: CheckUtil.returnPrams(retnEntityC.fixedIp, 0),
            // 料金プランID
            planId: CheckUtil.returnPrams(retnEntityC.pricePlanId, 0),
            // 料金プラン名
            planName: CheckUtil.returnPrams(retnEntityC.pricePlanName, 0),
            // POI情報
            poiInfomation: CheckUtil.returnPrams(retnEntityC.poi, 0),
            // 認証パターン
            authPattern: CheckUtil.returnPrams(retnEntityC.authPattern, 0),
            // 端末機種名
            deviceModelName: CheckUtil.returnPrams(retnEntityC.deviceModelName, 0),
            // 端末製造番号
            imeisv_2: CheckUtil.returnPrams(retnEntityC.imeisv_2, 0),
            // 端末製造番号(予備)
            imeisv_3: CheckUtil.returnPrams(retnEntityC.imeisv_3, 0),
            // メモ欄
            notes: CheckUtil.returnPrams(retnEntityC.notes, 0),
        };
    }

    /**
     * GroupADto対象を作成し、すべて属性に""を設定する
     * @return GroupADto
     */
    private getEmptyGroupADto(): GroupADto {
        return {
            // 回線番号の11桁半角スペースを設定する
            lineNo: MvnoUtil.addSpaceAfterStr(null, 0),
            // テナントIDの6桁半角スペースを設定する
            tenantId: MvnoUtil.addSpaceAfterStr(null, 0),
            // 代表N番の10桁半角スペースを設定する
            nBan: MvnoUtil.addSpaceAfterStr(null, 0),
            // 卸ポータルプランIDの5桁半角スペースを設定する
            potalPlanID: MvnoUtil.addSpaceAfterStr(null, 0),
            // 卸ポータルプラン名
            potalPlanName: MvnoUtil.addSpaceAfterStr(null, 0),
            // 回線グループID
            lineGroupID: MvnoUtil.addSpaceAfterStr(null, 0),
            // カード種別名
            cardClassificationName: MvnoUtil.addSpaceAfterStr(null, 0),
            // 回線利用開始年月日の10桁半角スペースを設定する
            lineUseStartDate: MvnoUtil.addSpaceAfterStr(null, 0),
            // 国際ローミング利用限度額
            interationalRoamingCreditLine: MvnoUtil.addSpaceAfterStr(null, 0),
            // 契約種別
            contractType: MvnoUtil.addSpaceAfterStr(null, 0),
            // SIMステータス
            simStatus: MvnoUtil.addSpaceAfterStr(null, 0),
            // 黒化日
            activateDate: MvnoUtil.addSpaceAfterStr(null, 0),

            voicemail: "",                                                      // NOTE: default value is empty string
            callWaiting: "",
            intlCall: "",
            forwarding: "",
            intlForwarding: "",
            lineRegStatus: "",
        };
    }

    /**
     * GroupBDto対象を作成し、すべて属性に""を設定する
     * @return GroupBDto
     */
    private getEmptyGroupBDto(): GroupBDto {
        return {
            // SIM番号(DN番号)
            simNo: MvnoUtil.addSpaceAfterStr(null, 0),
            // 端末種別名
            terminalClassificationName: MvnoUtil.addSpaceAfterStr(null, 0),
            // 製造番号（IMEI）
            imei: MvnoUtil.addSpaceAfterStr(null, 0),
            // 端末利用開始年月日の10桁半角スペースを設定する
            terminalUseStartDate: MvnoUtil.addSpaceAfterStr(null, 0),
        };
    }

    /**
     * GroupCDto対象を作成し、すべて属性に""を設定する
     * @return GroupCDto
     */
    private getEmptyGroupCDto(): GroupCDto {
        return {
            // ドメイン
            domain: MvnoUtil.addSpaceAfterStr(null, 0),
            // 認証ID
            attestationId: MvnoUtil.addSpaceAfterStr(null, 0),
            // 国内用IP　ACT東
            kokunaiyoIpACTeast: MvnoUtil.addSpaceAfterStr(null, 0),
            // 国内用IP　ACT西
            kokunaiyoIpACTwest: MvnoUtil.addSpaceAfterStr(null, 0),
            // 国内用IP　SBY
            kokunaiyoIpACTSBY: MvnoUtil.addSpaceAfterStr(null, 0),
            // 国際用固定IPアドレス
            interationalIpAddress: MvnoUtil.addSpaceAfterStr(null, 0),
            // 料金プランID
            planId: MvnoUtil.addSpaceAfterStr(null, 0),
            // 料金プラン名
            planName: MvnoUtil.addSpaceAfterStr(null, 0),
            // POI情報
            poiInfomation: MvnoUtil.addSpaceAfterStr(null, 0),

            // 認証パターン
            authPattern: MvnoUtil.addSpaceAfterStr(null, 0),
            // 端末機種名
            deviceModelName: MvnoUtil.addSpaceAfterStr(null, 0),
            // 端末製造番号
            imeisv_2: MvnoUtil.addSpaceAfterStr(null, 0),
            // 端末製造番号(予備)
            imeisv_3: MvnoUtil.addSpaceAfterStr(null, 0),
            // メモ欄
            notes: MvnoUtil.addSpaceAfterStr(null, 0),
        };
    }

    public generateDebugMessage(tenantId: string,
                                sequenceNo: string,
                                msgKey: string,
                                ...params: any[]): void
    {
        super.debug(tenantId, sequenceNo, msgKey, ...params);
        return;
    }
}