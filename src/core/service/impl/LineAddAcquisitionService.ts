import RESTCommon from "@/core/common/RESTCommon";
import { IFService } from "@/core/service/IFService";
import LineAddAcquisitionInputDto from "@/core/dto/LineAddAcquisitionInputDto";
import LineAddAcquisitionOutputDto from "@/core/dto/LineAddAcquisitionOutputDto";
import TenantManage from "@/core/common/TenantManage";
import APICommonDAO from "@/core/dao/APICommonDAO";
import APILinesDAO from "@/core/dao/APILinesDAO";
import ApiCommon from "@/core/common/ApiCommon";
import SOAPCommon from "@/core/common/SOAPCommon";
import SOCommon from "@/core/common/SOCommon";
import config from "config";
import MsgKeysConstants from "@/core/constant/MsgKeysConstants";
import ResultCdConstants from "@/core/constant/ResultCdConstants";
import MvnoUtil from "@/core/common/MvnoUtil";
import SOAPCommonOutputDto from "@/core/dto/SOAPCommonOutputDto";
import CheckResultBean from "@/core/dto/CheckResultBean";
import Check from "@/core/common/Check";
import Constants from "@/core/constant/Constants";
import ResponseHeader from "@/core/dto/ResponseHeader";
import { TenantsEntity } from "@/core/entity/TenantsEntity";
import { isSQLException } from "@/helpers/queryHelper";
import CheckUtil from "@/core/common/CheckUtil";
import SOObject from "@/core/dto/SOObject";
import SOAPException from "@/types/soapException";
import StringUtils from "@/core/common/StringUtils";
import ParameterName from "@/core/dto/ParameterName";
import TenantPlansEntity from "@/core/entity/TenantPlansEntity";
import PlansEntity from "@/core/entity/PlansEntity";
import ServiceOrdersEntity from "@/core/entity/ServiceOrdersEntity";
import OptionPlanParametersEntity from "@/core/entity/OptionPlanParametersEntity";
import { addDays, format, parse } from "date-fns";
import OptionPlansEntity from "@/core/entity/OptionPlansEntity";
import { getConfig } from "@/helpers/configHelper";
import { getStringParameter } from "@/types/parameter.string";

export default class LineAddAcquisitionService
    extends RESTCommon
    implements IFService<LineAddAcquisitionInputDto, LineAddAcquisitionOutputDto> {
    /** 定数 「回線番号」 */
    private static readonly CONST_LINENO: string = "回線番号";
    /** 定数 「卸ポータルプランID」 */
    private static readonly CONST_POTALGROUPPLANID: string = "卸ポータルプランID";
    /** 定数 「オプションプランID」 */
    private static readonly CONST_EXECUTEMIN: string = "オプションプランID";
    // /** 定数 「社内テナント種別は１（C-OCN）」 */
    // private static readonly CONST_SHANAITENANTID: string = "1";
    /** 定数 「更新サービスパターン」 */
    private static readonly CONST_KOUSHISERVICEPATTERN: string = "更新サービスパターン";
    /** 定数 「ポリシーID」 */
    private static readonly CONST_PORISHIID: string = "ポリシーID";
    /** 定数 「パラメータキー」 */
    private static readonly CONST_PARAMKEY_ADD_BUCKET: string = "add_bucket";

    /** １．１版対応 追加 START */
    private static readonly CONST_PARAMKEY_ADDBD_BUCKET: string = "addbd_bucket";
    /** １．１版対応 追加 END */

    /** 定数 「パラメータキー」 */
    private static readonly CONST_PARAMKEY_EXPIRATION_DATE: string = "expiration_date";
    /** 定数 「パラメータキー」 */
    private static readonly CONST_PARAMKEY_EXP_DATE: string = "exp_date";
    /** 定数 「追加容量」 */
    private static readonly CONST_ADDITIONALCOUPONREMAINCAPACITY: string = "追加容量";
    /** 定数 「有効期限（月数）」 */
    private static readonly CONST_ADDITIONALCOUPONTERMVALIDITY_MON: string = "有効期限（月数）";
    /** 定数 「有効期限（日数）」 */
    private static readonly CONST_ADDITIONALCOUPONTERMVALIDITY_DD: string = "有効期限（日数）";
    /** XML電文返信結果 */
    private static readonly CONST_RESULT: string = "NG";
    /** 更新サービスパターン結果1 */
    private static readonly CONST_SERVICEPATTERN_1: string = "1";
    /** 更新サービスパターン結果2 */
    private static readonly CONST_SERVICEPATTERN_2: string = "2";
    /** 更新サービスパターン結果3 */
    private static readonly CONST_SERVICEPATTERN_3: string = "3";
    /** 更新サービスパターン結果4 */
    private static readonly CONST_SERVICEPATTERN_4: string = "4";
    /** 更新サービスパターン結果5 */
    private static readonly CONST_SERVICEPATTERN_5: string = "5";
    /** 有効期限（日数）上限値 */
    private static readonly CONST_VALIDITYNICHISU_LOWER: number = 999;
    /** 有効期限（日数）下限値 */
    private static readonly CONST_VALIDITYNICHISU_UPPER: number = 0;
    /** 有効期限（月数）上限値 */
    private static readonly CONST_VALIDITYGETSUSU_LOWER: BigInt = BigInt(99);
    /** 有効期限（月数）下限値 */
    private static readonly CONST_VALIDITYGETSUSU_UPPER: BigInt = BigInt(0);
    /**
     * API共通DAO
     */
    // @Autowired
    private aPICommonDAO: APICommonDAO = new APICommonDAO(
        this.request,
        this.context
    );

    /**
     * SOAP API連携機能初期化
     */
    // @Autowired
    private sOAPCommon: SOAPCommon = new SOAPCommon(
        this.request,
        this.context
    );

    /** STEP1.2a版対応 追加 START */
    /**
     * SO管理共通DAO
     */
    // @Autowired
    private soCommon: SOCommon = new SOCommon(
        this.request,
        this.context
    );
    /** STEP1.2a版対応 追加 END */

    /** STEP2.0a版対応 追加 START */
    // @Autowired
    // private restCsvCommon: RestCsvCommon;
    /**
     * API回線DAO
     */
    // @Autowired
    private apiLinesDao: APILinesDAO = new APILinesDAO(
        this.request,
        this.context
    );
    /** STEP2.0a版対応 追加 END */

    /** STEP4.0版対応　追加　START */
    /** API共通処理 */
    // @Autowired
    private apiCommon: ApiCommon = new ApiCommon(
        this.request,
        this.context
    );
    /** STEP4.0版対応　追加　END */

    /**
     * 追加容量上限値
     */
    // @Value("#{mvno[AddBucketUpperLimit]}")
    private addBucketUpperLimit: string = config.get<string>("mvno.AddBucketUpperLimit");

    /**
     * 追加容量下限値
     */
    // @Value("#{mvno[AddBucketLowerLimit]}")
    private addBucketLowerLimit: string = config.get<string>("mvno.AddBucketLowerLimit");

    /** STEP1.2a版 仕様変更（161～163） 追加 START */
    private tenantManage: TenantManage = new TenantManage(
        this.request,
        this.context
    );
    /** STEP1.2a版 仕様変更（161～163） 追加 END */

    /** STEP1.2b版対応 追加 START */
    /**
     * 予約実行日時単位(予約機能)
     */
    // @Value("#{mvno[ReservationDateExecutionUnits]}")
    private reservationDateExecutionUnits: string = getConfig("ReservationDateExecutionUnits");

    /**
     * 予約可能制限日数(予約機能)
     */
    // @Value("#{mvno[ReservationsLimitDays]}")
    private reservationsLimitDays: string = getConfig("ReservationsLimitDays");
    /** STEP1.2b版対応 追加 END */

    /**
     * 回線追加チャージ(クーポン)容量追加・期間追加機能。<BR>
     *
     * <PRE>
     * SOAP APIにより、受信したパラメータの回線番号に容量と期間の更新、追加処理を行う
     * </PRE>
     *
     * @param param 回線追加チャージ(クーポン)容量追加・期間追加機能インプット
     * @param isPrivate 内部呼出フラグ
     * @param params 可変長さ引数(内部から呼出す時に使用するパラメータ)
     * @return LineAddAcquisitionOutputDto 回線追加チャージ(クーポン)容量追加・期間追加機能アウトプット
     * @throws Exception
     */
    async service(
        param: LineAddAcquisitionInputDto,
        clientIPAddress: string,
        ...params: any
    ) : Promise<LineAddAcquisitionOutputDto> {
        const isPrivate = false;

        super.debug(param.tenantId, param.requestHeader.sequenceNo,
            MsgKeysConstants.APLCFD0001, this.getClassName(), "service", JSON.stringify(param));
        // REST API共通処理が呼び出されたシステム日付
        const receivedDate: string = MvnoUtil.getDateTimeNow();
        /** 変数「処理コード」初期化 */
        let handleCode: string = ResultCdConstants.CODE_000000;
        /** 送信番号取得 */
        const sequenceNo: string = param.requestHeader.sequenceNo;
        /** 送信元システムID取得 */
        const senderSystemId: string = param.requestHeader.senderSystemId;
        /** API認証キー取得 */
        const apiKey: string = param.requestHeader.apiKey;
        /** 機能種別取得 */
        const functionType: string = param.requestHeader.functionType;
        /** テナントID取得 */
        const tenantId: string = param.tenantId;
        /** 卸ポータルプランID取得 */
        const potalPlanID: string = getStringParameter(param.potalPlanID) ?? "";
        /** 回線番号取得 */
        const lineNo: string = param.lineNo;
        /** 卸ポータルオプションプランID取得 */
        const optionPlanId: string = getStringParameter(param.optionPlanId);
        /** STEP4.0版対応　追加　START */
        /** 基準日 */
        let targetDate: string = null;
        /** 予約オーダ投入日 */
        let orderDate: string = null;
        /** STEP4.0版対応　追加　END */
        /** STEP1.2a版対応　変更　START */
        // 回線基本情報取得アウトプット初期化
        let lineAddAcquisitionOutputDto: LineAddAcquisitionOutputDto = null;
        /** STEP1.2a版対応　変更　END */
        /** STEP1.2b版対応　追加　START */
        // 予約日
        const reserveDate: string = param.reserve_date;
        // 予約フラグ
        const reserveFlg: boolean = param.reserve_flag;
        // ＳＯ－ＩＤ
        const reserveSoId: string = param.reserve_soId;
        // 変数「オーダ種別」初期化
        const orderType: string = Check.checkOrderType(reserveDate, reserveFlg, reserveSoId);
        /** STEP1.2b版対応　追加　END */

        /** STEP1.2a版対応　追加　START */
        // 変数「実行ユーザID」を定義する
        const executeUserId: string = null;
        // 変数「実行テナントID」を定義する
        const executeTenantId: string = null;

        /** STEP2.0版対応　追加　START */
        // 変数「CSV作成要否」
        let csvOutputKbn: string = null;
        // 変数「T番」
        let optionPlanTBan: string = null;
        // 変数「ファイル名通番」
        let fileNameNo: string = null;
        /** STEP2.0版対応　追加　END */

        // 変数「エラーメッセージ」
        const errorMessage: string = null;
        /** STEP1.2a版対応　追加　START */
        // 回線運用データ情報結果初期化
        // Document document = null;
        let document: Document = null;

        // 共通チェック
        /** STEP1.2a版対応　変更　START */
        const resultBean: CheckResultBean = {
            checkResult: true, // already passed 共通チェック in base handler
            processingCode: this.context.responseHeader.processCode,
            others: this.context.responseHeader.apiProcessID,
            errorMassage: "",
        };


        // 変数「REST共通チェック結果」
        const commCheckResult: boolean = resultBean.checkResult;
        // 変数「処理コード」
        handleCode = resultBean.processingCode;
        // 変数「API処理ID」
        const apiHandleId: string = resultBean.others;

        try {
            /** STEP1.2b版対応　追加　START */
            // 業務チェック
            // オーダ種別チェック
            // 変数「オーダ種別」より判定する
            if (Constants.ORDER_TYPE_9.equals(orderType)) {
                handleCode = ResultCdConstants.CODE_030105;
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APLCFW0011);
                // 返却値編集
                lineAddAcquisitionOutputDto = this.returnEdit(param, handleCode, apiHandleId, isPrivate, errorMessage, receivedDate);
                return lineAddAcquisitionOutputDto;
            }
            // 予約実行オーダの場合
            if (Constants.ORDER_TYPE_2.equals(orderType)) {
                // APサーバの複数IP取得
                const localIpList: string[] = MvnoUtil.getLocalIpList();
                // 予約実行可否チェック
                if (!this.context.isInternalRequest) {
                    // ログ出力
                    super.error(tenantId, sequenceNo, MsgKeysConstants.APCOME0401, "予約実行できませんでした。クライアントIP:" + clientIPAddress.toString() + ", APサーバーIP：" + MvnoUtil.getOutputList(localIpList));
                    // 変数「処理コード」設定
                    handleCode = ResultCdConstants.CODE_999999;
                    // 返却値編集
                    lineAddAcquisitionOutputDto = this.returnEdit(param, handleCode, apiHandleId, isPrivate, errorMessage, receivedDate);
                    return lineAddAcquisitionOutputDto;
                }
            }
            /** STEP1.2b版対応　追加　END */
            // 回線番号フォーマットチェック
            if (Check.checkLineNo(lineNo)) {
                // 卸ポータルプランIDフォーマットチェック
                if (!Check.checkPlanIDFmt(potalPlanID)) {
                    handleCode = ResultCdConstants.CODE_030101;
                    super.warn(tenantId, sequenceNo, MsgKeysConstants.APLCFW0001, LineAddAcquisitionService.CONST_POTALGROUPPLANID,
                        potalPlanID);
                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId, handleCode, receivedDate, orderType, reserveDate, isPrivate);
                    // 返却値編集
                    lineAddAcquisitionOutputDto = this.returnEdit(param, handleCode, apiHandleId, isPrivate, errorMessage, receivedDate);
                    return lineAddAcquisitionOutputDto;

                }
                // 卸ポータルオプションプランIDフォーマットチェック
                if (!Check.checkPlanIDFmt(optionPlanId)) {
                    handleCode = ResultCdConstants.CODE_030101;
                    super.warn(tenantId, sequenceNo, MsgKeysConstants.APLCFW0001, LineAddAcquisitionService.CONST_EXECUTEMIN, optionPlanId);
                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId, handleCode, receivedDate, orderType, reserveDate, isPrivate);
                    // 返却値編集
                    lineAddAcquisitionOutputDto = this.returnEdit(param, handleCode, apiHandleId, isPrivate, errorMessage, receivedDate);
                    return lineAddAcquisitionOutputDto;
                }
                /** STEP1.2a版　仕様変更（161～163）　削除　START */
                // ... 以下省略
                /** STEP1.2a版　仕様変更（161～163）　削除　END */

                /** STEP1.2b版対応　追加　START */
                // 予約前オーダ,予約実行オーダ
                if (Constants.ORDER_TYPE_1.equals(orderType) || Constants.ORDER_TYPE_2.equals(orderType)) {
                    // ４　予約日フォーマットチェック(予約前オーダ,予約実行オーダ)
                    if (!Check.checkReserveDateFmt(reserveDate)) {
                        handleCode = ResultCdConstants.CODE_030101;
                        super.warn(tenantId, sequenceNo, MsgKeysConstants.APLCFW0001, "予約日", reserveDate);
                        // SO管理共通パラメータ設定
                        await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId, handleCode, receivedDate, orderType, reserveDate, isPrivate);
                        // 返却値編集
                        lineAddAcquisitionOutputDto = this.returnEdit(param, handleCode, apiHandleId, isPrivate, errorMessage, receivedDate);
                        return lineAddAcquisitionOutputDto;
                    }
                }

                // 予約前オーダ
                if (Constants.ORDER_TYPE_1.equals(orderType)) {
                    // ５　予約日と投入日相関チェック
                    if (!Check.checkReserveDateOrderDate(receivedDate, reserveDate)) {
                        handleCode = ResultCdConstants.CODE_030106;
                        super.warn(tenantId, sequenceNo, MsgKeysConstants.APLCFW0012, reserveDate, receivedDate);
                        // SO管理共通パラメータ設定
                        await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId, handleCode, receivedDate, orderType, reserveDate, isPrivate);
                        // 返却値編集
                        lineAddAcquisitionOutputDto = this.returnEdit(param, handleCode, apiHandleId, isPrivate, errorMessage, receivedDate);
                        return lineAddAcquisitionOutputDto;
                    }

                    // ６　予約実行日時単位フォーマットチェック
                    if (!Check.checkReservationDateExecutionUnitsFmt(this.reservationDateExecutionUnits)) {
                        handleCode = ResultCdConstants.CODE_030107;
                        super.warn(tenantId, sequenceNo, MsgKeysConstants.APLCFW0013, this.reservationDateExecutionUnits);
                        // SO管理共通パラメータ設定
                        await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId, handleCode, receivedDate, orderType, reserveDate, isPrivate);
                        // 返却値編集
                        lineAddAcquisitionOutputDto = this.returnEdit(param, handleCode, apiHandleId, isPrivate, errorMessage, receivedDate);
                        return lineAddAcquisitionOutputDto;
                    }

                    // ７　予約実行日時単位と予約日相関チェックチェック
                    if (!Check.checkReservationDateExecutionUnits(this.reservationDateExecutionUnits, reserveDate)) {
                        handleCode = ResultCdConstants.CODE_030108;
                        super.warn(tenantId, sequenceNo, MsgKeysConstants.APLCFW0014, this.reservationDateExecutionUnits, reserveDate);
                        // SO管理共通パラメータ設定
                        await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId, handleCode, receivedDate, orderType, reserveDate, isPrivate);
                        // 返却値編集
                        lineAddAcquisitionOutputDto = this.returnEdit(param, handleCode, apiHandleId, isPrivate, errorMessage, receivedDate);
                        return lineAddAcquisitionOutputDto;
                    }

                    // ８　予約可能制限日数フォーマットチェック
                    if (!Check.checkReservationsLimitDaysFmt(this.reservationsLimitDays)) {
                        handleCode = ResultCdConstants.CODE_030109;
                        super.warn(tenantId, sequenceNo, MsgKeysConstants.APLCFW0015, this.reservationsLimitDays);
                        // SO管理共通パラメータ設定
                        await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId, handleCode, receivedDate, orderType, reserveDate, isPrivate);
                        // 返却値編集
                        lineAddAcquisitionOutputDto = this.returnEdit(param, handleCode, apiHandleId, isPrivate, errorMessage, receivedDate);
                        return lineAddAcquisitionOutputDto;
                    }

                    // ９　予約可能制限日数範囲チェック
                    if (!Check.checkReservationsLimitDays(this.reservationsLimitDays, reserveDate)) {
                        handleCode = ResultCdConstants.CODE_030110;
                        super.warn(tenantId, sequenceNo, MsgKeysConstants.APLCFW0016, this.reservationsLimitDays, reserveDate);
                        // SO管理共通パラメータ設定
                        await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId, handleCode, receivedDate, orderType, reserveDate, isPrivate);
                        // 返却値編集
                        lineAddAcquisitionOutputDto = this.returnEdit(param, handleCode, apiHandleId, isPrivate, errorMessage, receivedDate);
                        return lineAddAcquisitionOutputDto;
                    }
                }
                /** STEP1.2b版対応　追加　END */

                /** STEP1.2a版　仕様変更（161～163）　追加　START */

                /** STEP2.0a版対応　変更　START */
                // STEP20.0版対応　変更　START
                let checkResult: [boolean, string] = null;
                try {
                    checkResult = await this.tenantManage.doCheck(param.lineNo, param.tenantId);
                } catch (e: any) {
                    // DBアクセスリトライエラーの場合
                    if (isSQLException(e)) {
                        handleCode = ResultCdConstants.CODE_030102;
                    }
                    throw e;
                }
                // STEP20.0版対応　変更　END
                let nNo: string;
                if (!checkResult[0]) {
                    handleCode = ResultCdConstants.CODE_030102;
                    super.warn(tenantId, sequenceNo, MsgKeysConstants.APLCFW0002, param.lineNo, param.tenantId);
                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId, handleCode, receivedDate, orderType, reserveDate, isPrivate);
                    // 返却値編集
                    lineAddAcquisitionOutputDto = this.returnEdit(param, handleCode, apiHandleId, isPrivate, errorMessage, receivedDate);
                    return lineAddAcquisitionOutputDto;
                } else {
                    nNo = checkResult[1];
                }
                /** STEP1.2a版　仕様変更（161～163）　追加　END */
                // STEP20.0版対応　変更　START
                let tenantIdIdList: TenantPlansEntity[] = null;
                try {
                    // プランID所属チェック
                    tenantIdIdList = await this.aPICommonDAO.getTenantPlans(potalPlanID, tenantId);
                } catch (e: any) {
                    // DBアクセスリトライエラーの場合
                    if (isSQLException(e)) {
                        handleCode = ResultCdConstants.CODE_030103;
                    }
                    throw e;
                }
                // STEP20.0版対応　変更　END
                if (tenantIdIdList == null || tenantIdIdList.length === 0) {
                    handleCode = ResultCdConstants.CODE_030103;
                    super.warn(tenantId, sequenceNo, MsgKeysConstants.APLCFW0003, potalPlanID, tenantId);
                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId, handleCode, receivedDate, orderType, reserveDate, isPrivate);
                    // 返却値編集
                    lineAddAcquisitionOutputDto = this.returnEdit(param, handleCode, apiHandleId, isPrivate, errorMessage, receivedDate);
                    return lineAddAcquisitionOutputDto;
                }

                /** STEP4.0対応　追加　START */
                // STEP20.0版対応　変更　START
                let tenantDecisionInfo: TenantsEntity = null;
                try {
                    // 卸ポータルプランID利用チェック
                    tenantDecisionInfo = await this.aPICommonDAO.getTenants(tenantId);
                } catch (e: any) {
                    // DBアクセスリトライエラーの場合
                    if (isSQLException(e)) {
                        handleCode = ResultCdConstants.CODE_030111;
                    }
                    throw e;
                }
                // STEP20.0版対応　変更　END
                // 変数「判定用テナント情報」より判定する
                if (tenantDecisionInfo != null) {
                    if (1 !== tenantDecisionInfo.tenantType) {
                        // STEP20.0版対応　変更　START
                        let lineUsePlanId: string = null;
                        try {
                            // プランIDを取得する
                            lineUsePlanId = await this.apiLinesDao.getLineUsingPlan(lineNo);
                        } catch (e: any) {
                            if (isSQLException(e)) {
                                // DBアクセスリトライエラーの場合
                            handleCode = ResultCdConstants.CODE_030112;
                            }
                            throw e;
                        }
                        // STEP20.0版対応　変更　END
                        // 変数「回線利用プランID」より判定する
                        if (!potalPlanID.equals(lineUsePlanId)) {
                            handleCode = ResultCdConstants.CODE_030112;
                            super.warn(tenantId, sequenceNo, MsgKeysConstants.APLCFW0018, potalPlanID);
                            // SO管理共通パラメータ設定
                            await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId, handleCode,
                                receivedDate, orderType, reserveDate, isPrivate);
                            // 返却値編集
                            lineAddAcquisitionOutputDto = this.returnEdit(param, handleCode, apiHandleId,
                                isPrivate, errorMessage, receivedDate);
                            return lineAddAcquisitionOutputDto;
                        }
                    }
                } else {
                    handleCode = ResultCdConstants.CODE_030111;
                    super.warn(tenantId, sequenceNo, MsgKeysConstants.APLCFW0017);
                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId, handleCode,
                        receivedDate, orderType, reserveDate, isPrivate);
                    // 返却値編集
                    lineAddAcquisitionOutputDto = this.returnEdit(param, handleCode, apiHandleId, isPrivate,
                        errorMessage, receivedDate);
                    return lineAddAcquisitionOutputDto;
                }

                /** STEP4.0対応　追加　END */

                /** STEP4.0対応　変更　START */
                // STEP20.0版対応　変更　START
                let optionPlansInfo: string = null;
                try {
                    // オプションプランID利用チェック
                    optionPlansInfo
                        = await this.aPICommonDAO.getCheckedPlanOptionPlans(optionPlanId, potalPlanID, "03");
                } catch (e: any) {
                    if (isSQLException(e)) {
                        // DBアクセスリトライエラーの場合
                    handleCode = ResultCdConstants.CODE_030104;
                    }
                    throw e;
                }
                // STEP20.0版対応　変更　END
                if (!potalPlanID.equals(optionPlansInfo)) {
                    handleCode = ResultCdConstants.CODE_030104;
                    super.warn(tenantId, sequenceNo, MsgKeysConstants.APLCFW0004, optionPlanId);
                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId, handleCode,
                        receivedDate, orderType, reserveDate, isPrivate);
                    // 返却値編集
                    lineAddAcquisitionOutputDto = this.returnEdit(param, handleCode, apiHandleId,
                        isPrivate, errorMessage, receivedDate);
                    return lineAddAcquisitionOutputDto;
                }
                /** STEP4.0対応　変更　END */

                /** STEP4.0対応　追加　START */

                if (Constants.ORDER_TYPE_0.equals(orderType)) {
                    targetDate = receivedDate.substring(0, 10).replace(/\//g, "");
                } else {
                    targetDate = reserveDate.substring(0, 10).replace(/\//g, "");
                }
                // STEP20.0版対応　変更　START
                let checkAbolishSoResult: boolean;
                try {
                    // 廃止オーダ受付中チェック
                    checkAbolishSoResult = await this.apiCommon.checkAbolishSo(lineNo, targetDate);
                } catch (e: any) {
                    if (isSQLException(e)) {
                        // DBアクセスリトライエラーの場合
                    handleCode = ResultCdConstants.CODE_030113;
                    }
                    throw e;
                }
                // STEP20.0版対応　変更　END
                // 変数「廃止オーダ受付中判定結果」より判定する
                if (!checkAbolishSoResult) {
                    handleCode = ResultCdConstants.CODE_030113;
                    super.warn(tenantId, sequenceNo, MsgKeysConstants.APLCFW0019, lineNo);
                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId, handleCode,
                        receivedDate, orderType, reserveDate, isPrivate);
                    // 返却値編集
                    lineAddAcquisitionOutputDto = this.returnEdit(param, handleCode, apiHandleId,
                        isPrivate, errorMessage, receivedDate);
                    return lineAddAcquisitionOutputDto;
                }
                /** STEP4.0対応　追加　END */

                // STEP19.0版対応　変更　START
                if (2 !== tenantDecisionInfo.tenantType
                    && 3 !== tenantDecisionInfo.tenantType
                    && 4 !== tenantDecisionInfo.tenantType
                    && 5 !== tenantDecisionInfo.tenantType) {
                    // B-OCN、UNOモバイル、ICM, RINKモバイル（社内テナント種別が２、３、４, 5）以外の場合にチェックを行う

                    // STEP20.0版対応　変更　START
                    let checkLineUsageStatusResult: boolean;
                    try {
                        // STEP15.0版対応　追加　START
                        // 利用状態(サスペンド中)のチェック
                        checkLineUsageStatusResult = await this.apiCommon.checkLineSuspend(lineNo);
                    } catch (e: any) {
                        if (isSQLException(e)) {
                            // DBアクセスリトライエラーの場合
                        handleCode = ResultCdConstants.CODE_030114;
                        }
                        throw e;
                    }
                    // STEP20.0版対応　変更　END
                    // 変数「利用状態判定結果」より判定する
                    if (!checkLineUsageStatusResult) {
                        handleCode = ResultCdConstants.CODE_030114;
                        super.warn(tenantId, sequenceNo, MsgKeysConstants.APLCFW0021, lineNo);
                        // SO管理共通パラメータ設定
                        await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId, handleCode,
                            receivedDate, orderType, reserveDate, isPrivate);
                        // 返却値編集
                        lineAddAcquisitionOutputDto = this.returnEdit(param, handleCode, apiHandleId,
                            isPrivate, errorMessage, receivedDate);
                        return lineAddAcquisitionOutputDto;
                    }
                    // STEP15.0版対応　追加　END
                }
                // STEP19.0版対応　変更　END

                // 更新サービスパターンとポリシーIDを取得する
                // 更新サービスパターン
                let koushiServicePattern: number = 0;
                // ポリシーID
                let porishiId: number = 0;
                // STEP20.0版対応　変更　START
                let plansList: PlansEntity[] = null;
                try {
                    plansList = await this.aPICommonDAO.getPlans(potalPlanID);
                } catch (e: any) {
                    if (isSQLException(e)) {
                        // DBアクセスリトライエラーの場合
                        handleCode = ResultCdConstants.CODE_030201;
                    }
                    throw e;
                }
                // STEP20.0版対応　変更　END
                if (plansList == null || plansList.length === 0) {
                    handleCode = ResultCdConstants.CODE_030201;
                    super.warn(tenantId, sequenceNo, MsgKeysConstants.APLCFW0005);
                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId, handleCode, receivedDate, orderType, reserveDate, isPrivate);
                    // 返却値編集
                    lineAddAcquisitionOutputDto = this.returnEdit(param, handleCode, apiHandleId, isPrivate, errorMessage, receivedDate);
                    return lineAddAcquisitionOutputDto;
                } else {
                    // 更新サービスパターン
                    if (CheckUtil.checkIsNull(plansList[0].tpcServicePattern)) {
                        handleCode = ResultCdConstants.CODE_030202;
                        super.warn(tenantId, sequenceNo, MsgKeysConstants.APLCFW0006, LineAddAcquisitionService.CONST_KOUSHISERVICEPATTERN + "（null）",
                            plansList[0].tpcServicePattern);
                        // SO管理共通パラメータ設定
                        await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId, handleCode, receivedDate, orderType, reserveDate, isPrivate);
                        // 返却値編集
                        lineAddAcquisitionOutputDto = this.returnEdit(param, handleCode, apiHandleId, isPrivate, errorMessage, receivedDate);
                        return lineAddAcquisitionOutputDto;
                    } else {
                        koushiServicePattern = plansList[0].tpcServicePattern;
                    }

                    // ポリシーID
                    if (CheckUtil.checkIsNull(plansList[0].policyId)) {
                        handleCode = ResultCdConstants.CODE_030202;
                        super.warn(tenantId, sequenceNo, MsgKeysConstants.APLCFW0006, LineAddAcquisitionService.CONST_PORISHIID + "（null）", plansList[0].policyId);
                        // SO管理共通パラメータ設定
                        await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId, handleCode, receivedDate, orderType, reserveDate, isPrivate);
                        // 返却値編集
                        lineAddAcquisitionOutputDto = this.returnEdit(param, handleCode, apiHandleId, isPrivate, errorMessage, receivedDate);
                        return lineAddAcquisitionOutputDto;
                    } else {
                        porishiId = plansList[0].policyId;
                    }
                }

                // 更新サービスパターンのフォーマットチェック
                if (!(koushiServicePattern >= 1 && koushiServicePattern <= 5)) {
                    handleCode = ResultCdConstants.CODE_030202;
                    super.warn(tenantId, sequenceNo, MsgKeysConstants.APLCFW0006, LineAddAcquisitionService.CONST_KOUSHISERVICEPATTERN + "（1〜5ではありません）",
                        koushiServicePattern);
                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId, handleCode, receivedDate, orderType, reserveDate, isPrivate);
                    // 返却値編集
                    lineAddAcquisitionOutputDto = this.returnEdit(param, handleCode, apiHandleId, isPrivate, errorMessage, receivedDate);
                    return lineAddAcquisitionOutputDto;
                }
                // ポリシーID のフォーマットチェック
                const porishiIdStr: string = porishiId.toString(10);
                if(!Check.checkPolicyId(porishiIdStr)){
                    handleCode = ResultCdConstants.CODE_030202;
                    super.warn(tenantId, sequenceNo, MsgKeysConstants.APLCFW0006, LineAddAcquisitionService.CONST_PORISHIID + "（フォーマットは正しくありません）", porishiId);
                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId, handleCode, receivedDate, orderType, reserveDate, isPrivate);
                    // 返却値編集
                    lineAddAcquisitionOutputDto = this.returnEdit(param, handleCode, apiHandleId, isPrivate, errorMessage, receivedDate);
                    return lineAddAcquisitionOutputDto;
                }

                /** STEP1.2b版対応　変更　START */
                // ２．３　オーダ種別より判断する
                if (Constants.ORDER_TYPE_0.equals(orderType) || Constants.ORDER_TYPE_2.equals(orderType)) {
                    // SO電文作成

                    /** STEP3.1版対応　追加　START */
                    // STEP20.0版対応　変更　START
                    let tpcConnectionResults: [boolean, string, string] = null;
                    try {
                        // TPC情報を取得する。
                        tpcConnectionResults = await this.tenantManage.checkTpcConnection(param.tenantId);
                    } catch (e: any) {
                        if (isSQLException(e)) {
                            // DBアクセスリトライエラーの場合
                            handleCode = ResultCdConstants.CODE_030310;
                        }
                        throw e;
                    }
                    // STEP20.0版対応　変更　END
                    // 変数「TPC情報取得結果」より判断する
                    if (!tpcConnectionResults[0]) {
                        handleCode = ResultCdConstants.CODE_030310;
                        super.warn(tenantId, sequenceNo, MsgKeysConstants.APLCFW0020, param.tenantId);
                        // SO管理共通パラメータ設定
                        await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId, handleCode, receivedDate, orderType, reserveDate, isPrivate);
                        // 返却値編集
                        lineAddAcquisitionOutputDto = this.returnEdit(param, handleCode, apiHandleId, isPrivate, errorMessage, receivedDate);
                        return lineAddAcquisitionOutputDto;
                    }
                    /** STEP3.1版対応　追加　END */

                    /** １．１版対応　変更　START */
                    let optionPlanParameters: OptionPlanParametersEntity = null;
                    // STEP20.0版対応　変更　START
                    try {
                        if (koushiServicePattern === 1 || koushiServicePattern === 3 || koushiServicePattern === 2) {
                            optionPlanParameters
                                = await this.aPICommonDAO.getOptionPlanParameters(optionPlanId, LineAddAcquisitionService.CONST_PARAMKEY_ADD_BUCKET);
                        } else if (koushiServicePattern === 4) {
                            optionPlanParameters
                                = await this.aPICommonDAO.getOptionPlanParameters(optionPlanId, LineAddAcquisitionService.CONST_PARAMKEY_ADDBD_BUCKET);
                        }
                        /** １．１版対応　変更　END */
                    } catch (e: any) {
                        if (isSQLException(e)) {
                            // DBアクセスリトライエラーの場合
                            const koushiServicePt: string = koushiServicePattern.toString();
                            if (LineAddAcquisitionService.CONST_SERVICEPATTERN_1.equals(koushiServicePt)
                                || LineAddAcquisitionService.CONST_SERVICEPATTERN_3.equals(koushiServicePt)
                                || LineAddAcquisitionService.CONST_SERVICEPATTERN_4.equals(koushiServicePt)) {
                                // 「月次プラン」、「容量プリペイド」、「月次プラン7G」の場合
                                handleCode = ResultCdConstants.CODE_030301;
                            } else if (LineAddAcquisitionService.CONST_SERVICEPATTERN_2.equals(koushiServicePt)) {
                                // 「日次プラン」 の場合
                                handleCode = ResultCdConstants.CODE_030303;
                            }
                        }
                        throw e;
                    }
                    // STEP20.0版対応　変更　END

                    // 変数「追加容量情報結果」のパラメータ値
                    let additionalCouponRemainCapacity: string = "";
                    if(optionPlanParameters != null){
                        additionalCouponRemainCapacity = optionPlanParameters.value;
                    }
                    let additionalCouponRemainCapacityLong: BigInt = BigInt(0);
                    if(!CheckUtil.checkIsNotNull(additionalCouponRemainCapacity)){
                        additionalCouponRemainCapacityLong = BigInt(additionalCouponRemainCapacity);
                    }
                    // 加容量のフォーマットチェック
                    const addBucketLowerLimitLong: BigInt = BigInt(this.addBucketLowerLimit);
                    const addBucketUpperLimitLong: BigInt = BigInt(this.addBucketUpperLimit);

                    const koushiServicePt: string = koushiServicePattern.toString();
                    if (LineAddAcquisitionService.CONST_SERVICEPATTERN_1.equals(koushiServicePt) || LineAddAcquisitionService.CONST_SERVICEPATTERN_3.equals(koushiServicePt) || LineAddAcquisitionService.CONST_SERVICEPATTERN_4.equals(koushiServicePt)) {
                        if (CheckUtil.checkIsNotNull(additionalCouponRemainCapacity)) {
                            handleCode = ResultCdConstants.CODE_030301;
                            super.warn(tenantId, sequenceNo, MsgKeysConstants.APLCFW0008, "追加容量情報結果");
                            // SO管理共通パラメータ設定
                            await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId, handleCode, receivedDate, orderType, reserveDate, isPrivate);
                            // 返却値編集
                            lineAddAcquisitionOutputDto = this.returnEdit(param, handleCode, apiHandleId, isPrivate, errorMessage, receivedDate);
                            return lineAddAcquisitionOutputDto;
                        }

                        // 有効期限月数結果取得
                        // STEP21.0版対応　変更　START
                        let additionalCouponTermValidity: string = null;
                        // STEP20.0版対応　変更　START
                        let additionalCouponTermValidityList: OptionPlanParametersEntity = null;
                        let additionalCouponTermValidityNichisu: string = null;
                        let additionalCouponTermValidityNichisuList: OptionPlanParametersEntity = null;
                        // STEP21.0版対応　変更　END
                        try {
                            additionalCouponTermValidityList
                                = await this.aPICommonDAO.getOptionPlanParameters(optionPlanId,
                                LineAddAcquisitionService.CONST_PARAMKEY_EXPIRATION_DATE);
                            if (additionalCouponTermValidityList === null
                                || "".equals(additionalCouponTermValidityList.value)) {
                                // STEP20.0版対応　変更　START
                                // 有効期限（月数）が取得できなかった場合
                                additionalCouponTermValidityNichisuList
                                    = await this.aPICommonDAO.getOptionPlanParameters(optionPlanId,
                                    LineAddAcquisitionService.CONST_PARAMKEY_EXP_DATE);
                                if (additionalCouponTermValidityNichisuList === null
                                    || "".equals(additionalCouponTermValidityNichisuList.value)) {
                                    // 有効期限（日数）も取得できなかった場合
                                    handleCode = ResultCdConstants.CODE_030301;
                                    super.warn(tenantId, sequenceNo, MsgKeysConstants.APLCFW0008, "有効期限（月数）");
                                    /** STEP1.2a版対応　追加　START */
                                    /** STEP1.2b版対応　変更　START */
                                    // SO管理共通パラメータ設定
                                    await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId,
                                        handleCode, receivedDate, orderType, reserveDate, isPrivate);
                                    /** STEP1.2b版対応　変更　START */
                                    /** STEP1.2a版対応　追加　END */
                                    // 返却値編集
                                    /** STEP1.2a版対応　変更　START */
                                    lineAddAcquisitionOutputDto
                                        = this.returnEdit(param, handleCode, apiHandleId, isPrivate, errorMessage,
                                        receivedDate);
                                    /** STEP1.2a版対応　変更　END */
                                    /** STEP1.2b版対応　削除　START */
                                    // super.conCount--;
                                    // super.debug(tenantId, sequenceNo, MsgKeysConstants.APLCFD0002,
                                    //             this.getClass().getName(), "service");
                                    /** STEP1.2b版対応　削除　END */
                                    return lineAddAcquisitionOutputDto;
                                } else {
                                    additionalCouponTermValidityNichisu
                                        = additionalCouponTermValidityNichisuList.value;
                                }
                                // STEP20.0版対応　変更　END
                            } else {
                                additionalCouponTermValidity = additionalCouponTermValidityList.value;
                            }
                        } catch (e: any) {
                            if (isSQLException(e)) {
                                // DBアクセスリトライエラーの場合
                                handleCode = ResultCdConstants.CODE_030301;
                            }
                            throw e;
                        }
                        // STEP20.0版対応　変更　END

                        // 加容量のフォーマットチェック
                        if(!(additionalCouponRemainCapacityLong >= addBucketLowerLimitLong && additionalCouponRemainCapacityLong <= addBucketUpperLimitLong) ){
                            handleCode = ResultCdConstants.CODE_030302;
                            super.warn(tenantId, sequenceNo, MsgKeysConstants.APLCFW0006,
                                LineAddAcquisitionService.CONST_ADDITIONALCOUPONREMAINCAPACITY　+ "（サービスパターン=1/3/4）", additionalCouponRemainCapacity);
                            // SO管理共通パラメータ設定
                            await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId, handleCode, receivedDate, orderType, reserveDate, isPrivate);
                            // 返却値編集
                            lineAddAcquisitionOutputDto = this.returnEdit(param, handleCode, apiHandleId, isPrivate, errorMessage, receivedDate);
                            return lineAddAcquisitionOutputDto;
                        }
                        // STEP21.0版対応　変更　START
                        // 有効期限（月数）のフォーマットチェック
                        let expiration_date: string = null;
                        if (additionalCouponTermValidity != null) {
                            const additionalCouponTermValidityInt: BigInt = BigInt(additionalCouponTermValidity);

                            if (!(additionalCouponTermValidityInt >= LineAddAcquisitionService.CONST_VALIDITYGETSUSU_UPPER
                                && additionalCouponTermValidityInt <= LineAddAcquisitionService.CONST_VALIDITYGETSUSU_LOWER)) {
                                handleCode = ResultCdConstants.CODE_030302;
                                super.warn(tenantId, sequenceNo, MsgKeysConstants.APLCFW0006,
                                    LineAddAcquisitionService.CONST_ADDITIONALCOUPONTERMVALIDITY_MON + "（サービスパターン=1/3/4）", additionalCouponTermValidity);
                                // SO管理共通パラメータ設定
                                await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId, handleCode,
                                    receivedDate, orderType, reserveDate, isPrivate);
                                // 返却値編集
                                lineAddAcquisitionOutputDto = this.returnEdit(param, handleCode, apiHandleId,
                                    isPrivate, errorMessage, receivedDate);
                                return lineAddAcquisitionOutputDto;
                            }
                            // 有効期限日付取得
                            expiration_date = Check.getMonthEndDay(additionalCouponTermValidity);
                        }

                        // 有効期限（日数）のフォーマットチェック
                        if (additionalCouponTermValidityNichisu != null) {
                            const additionalCouponTermValidityNichiSu: number
                                = parseInt(additionalCouponTermValidityNichisu, 10);
                            if (!(additionalCouponTermValidityNichiSu >= LineAddAcquisitionService.CONST_VALIDITYNICHISU_UPPER
                                && additionalCouponTermValidityNichiSu <= LineAddAcquisitionService.CONST_VALIDITYNICHISU_LOWER)) {
                                handleCode = ResultCdConstants.CODE_030302;
                                super.warn(tenantId, sequenceNo, MsgKeysConstants.APLCFW0006,
                                    LineAddAcquisitionService.CONST_ADDITIONALCOUPONTERMVALIDITY_DD　+ "（サービスパターン=1/3/4）",
                                    additionalCouponTermValidityNichisu);
                                // SO管理共通パラメータ設定
                                await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId, handleCode,
                                    receivedDate, orderType, reserveDate, isPrivate);
                                // 返却値編集
                                lineAddAcquisitionOutputDto = this.returnEdit(param, handleCode, apiHandleId,
                                    isPrivate, errorMessage, receivedDate);
                                return lineAddAcquisitionOutputDto;
                            }
                            // 有効期限日付取得
                            const expirationDate = new Date();
                            expirationDate.setDate(expirationDate.getDate() + additionalCouponTermValidityNichiSu);
                            expiration_date = format(expirationDate, "yyMMdd");
                        }
                        // STEP20.0版対応　変更　END

                        // SOAP APIにより、オプションサービスに対する容量を追加する
                        // パラメータ設定
                        /** １．１版対応　変更　START */
                        let sOAPCommonOutputDto: SOAPCommonOutputDto = null;
                        // STEP22.0版対応　変更　START
                        const paraList: ParameterName[] = [];
                        // STEP22.0版対応　変更　END
                        if (koushiServicePattern === 1 || koushiServicePattern === 3) {
                            const phoneNumber: ParameterName = new ParameterName();
                            phoneNumber.setName("phone_number");
                            phoneNumber.setValue(lineNo);
                            paraList.push(phoneNumber);
                            const policyNumber: ParameterName = new ParameterName();
                            policyNumber.setName("policy_number");
                            const porishiStr: string = porishiId.toString();
                            policyNumber.setValue(porishiStr);
                            paraList.push(policyNumber);
                            const addBucket: ParameterName = new ParameterName();
                            addBucket.setName("add_bucket");
                            addBucket.setValue(additionalCouponRemainCapacity);
                            paraList.push(addBucket);
                            const expirationDate: ParameterName = new ParameterName();
                            expirationDate.setName("expiration_date");
                            expirationDate.setValue(expiration_date);
                            paraList.push(expirationDate);

                            /** STEP3.1版対応　変更　START */
                            sOAPCommonOutputDto = await this.sOAPCommon.callWebSoapApi(
                                "serviceProfileRequestLite", "Mod", "", paraList, tenantId, sequenceNo, tpcConnectionResults[1]);
                            /** STEP3.1版対応　変更　END */

                        } else if (koushiServicePattern===4) {

                            const phoneNumber: ParameterName = new ParameterName();
                            phoneNumber.setName("phone_number");
                            phoneNumber.setValue(lineNo);
                            paraList.push(phoneNumber);
                            const policyNumber: ParameterName = new ParameterName();
                            policyNumber.setName("policy_number");
                            const porishiStr: string = porishiId.toString();
                            policyNumber.setValue(porishiStr);
                            paraList.push(policyNumber);
                            const addBucket: ParameterName = new ParameterName();
                            addBucket.setName("addbd_bucket");
                            addBucket.setValue(additionalCouponRemainCapacity);
                            paraList.push(addBucket);
                            const expirationDate: ParameterName = new ParameterName();
                            expirationDate.setName("expiration_date");
                            expirationDate.setValue(expiration_date);
                            paraList.push(expirationDate);

                            /** STEP3.1版対応　変更　START */
                            sOAPCommonOutputDto = await this.sOAPCommon.callWebSoapApi(
                                "serviceProfileRequestLite", "Mod", "", paraList, tenantId, sequenceNo, tpcConnectionResults[1]);
                        }
                        /** STEP3.1版対応　変更　END */

                        /** STEP3.1版対応　追加　START */
                        if (ResultCdConstants.CODE_000951.equals(sOAPCommonOutputDto.getProcessCode())) {
                            // SG取得がNG
                            handleCode = ResultCdConstants.CODE_030401;
                            super.warn(tenantId, sequenceNo, MsgKeysConstants.APLCFW0010, "（SG取得NG）（サービスパターン=1/3/4）");
                            await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId, handleCode, receivedDate, orderType, reserveDate, isPrivate);
                            lineAddAcquisitionOutputDto = this.returnEdit(param, handleCode, apiHandleId, isPrivate, errorMessage, receivedDate);
                            return lineAddAcquisitionOutputDto;
                        }
                        /** STEP3.1版対応　追加　END */

                        /** １．１版対応　変更　END */
                        document = sOAPCommonOutputDto.getDoc();
                        let resultStr: string = this.sOAPCommon.getNodeContent(document, "//Result");
                        if (sOAPCommonOutputDto.isError() || LineAddAcquisitionService.CONST_RESULT.equals(resultStr)) {
                            handleCode = ResultCdConstants.CODE_030401;
                            super.warn(tenantId, sequenceNo, MsgKeysConstants.APLCFW0010, "（エラーまたはTPCからのResultがNG）（サービスパターン=1/3/4）");
                            // SO管理共通パラメータ設定
                            await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId, handleCode, receivedDate, orderType, reserveDate, isPrivate);
                            // 返却値編集
                            lineAddAcquisitionOutputDto = this.returnEdit(param, handleCode, apiHandleId, isPrivate, errorMessage, receivedDate);
                            return lineAddAcquisitionOutputDto;
                        }

                        // STEP22.0版対応　追加　START
                        // セカンダリTPCへSOAP連携
                        if (tpcConnectionResults[2] != null) {
                            // セカンダリTPCが設定されている場合は以下の処理を行う(電文はプライマリと同じものを使用)
                            try {
                                sOAPCommonOutputDto
                                    = await this.sOAPCommon.callWebSoapApi("serviceProfileRequestLite", "Mod", "", paraList,
                                    tenantId, sequenceNo, tpcConnectionResults[2],
                                    false);

                                if (ResultCdConstants.CODE_000951.equals(sOAPCommonOutputDto.getProcessCode())) {
                                    // SG取得でNGの場合
                                    super.warn(tenantId, sequenceNo, MsgKeysConstants.APLCFW0022, "SG取得NG（サービスパターン=1/3/4）");
                                } else {
                                    document = sOAPCommonOutputDto.getDoc();
                                    resultStr = this.sOAPCommon.getNodeContent(document, "//Result");
                                    if (sOAPCommonOutputDto.isError() || LineAddAcquisitionService.CONST_RESULT.equals(resultStr)) {
                                        // TPCからのResultがNGの場合
                                        const errInfo: string = this.sOAPCommon.getNodeContent(document, "//ErrorInfomation");
                                        super.warn(tenantId, sequenceNo, MsgKeysConstants.APLCFW0022, errInfo + "（サービスパターン=1/3/4）");
                                    }
                                }
                            } catch (e: any) {
                                if (SOAPException.isSOAPException(e)) {
                                    super.warn(e, tenantId, sequenceNo, MsgKeysConstants.APLCFW0022, e.toString() + "（サービスパターン=1/3/4）");
                                } else {
                                    throw e;
                                }
                            }
                        }
                        // STEP22.0版対応　追加　END

                    } else if (LineAddAcquisitionService.CONST_SERVICEPATTERN_2.equals(koushiServicePt)) {
                        // 「追加容量情報結果」より判定する
                        if (CheckUtil.checkIsNotNull(additionalCouponRemainCapacity)) {
                            handleCode = ResultCdConstants.CODE_030303;
                            super.warn(tenantId, sequenceNo, MsgKeysConstants.APLCFW0008, "追加容量情報結果");
                            // SO管理共通パラメータ設定
                            await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId, handleCode, receivedDate, orderType, reserveDate, isPrivate);
                            // 返却値編集
                            lineAddAcquisitionOutputDto = this.returnEdit(param, handleCode, apiHandleId, isPrivate, errorMessage, receivedDate);
                            return lineAddAcquisitionOutputDto;
                        }
                        // 変数「追加容量情報結果」のパラメータ値
                        // 加容量のフォーマットチェック
                        if(!(additionalCouponRemainCapacityLong >= addBucketLowerLimitLong && additionalCouponRemainCapacityLong <= addBucketUpperLimitLong) ){
                            handleCode = ResultCdConstants.CODE_030304;
                            super.warn(tenantId, sequenceNo, MsgKeysConstants.APLCFW0006,
                                LineAddAcquisitionService.CONST_ADDITIONALCOUPONREMAINCAPACITY + "（サービスパターン=2）", additionalCouponRemainCapacity);
                            // SO管理共通パラメータ設定
                            await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId, handleCode, receivedDate, orderType, reserveDate, isPrivate);
                            // 返却値編集
                            lineAddAcquisitionOutputDto = this.returnEdit(param, handleCode, apiHandleId, isPrivate, errorMessage, receivedDate);
                            return lineAddAcquisitionOutputDto;
                        }
                        // SOAP APIにより、オプションサービスに対する容量を追加する
                        // パラメータ設定
                        const paraList: ParameterName[] = [];
                        const phoneNumber: ParameterName = new ParameterName();
                        phoneNumber.setName("phone_number");
                        phoneNumber.setValue(lineNo);
                        paraList.push(phoneNumber);
                        const policyNumber: ParameterName = new ParameterName();
                        policyNumber.setName("policy_number");
                        const porishiStr: string = porishiId.toString();
                        policyNumber.setValue(porishiStr);
                        paraList.push(policyNumber);
                        const addBucket: ParameterName = new ParameterName();
                        addBucket.setName("add_bucket");
                        addBucket.setValue(additionalCouponRemainCapacity);
                        paraList.push(addBucket);
                        const expirationDate: ParameterName = new ParameterName();
                        expirationDate.setName("term_days");
                        expirationDate.setValue("0");
                        paraList.push(expirationDate);
                        /** STEP3.1版対応　変更　START */
                        let sOAPCommonOutputDto: SOAPCommonOutputDto = await this.sOAPCommon.callWebSoapApi(
                            "serviceProfileRequestLite", "Mod", "", paraList, tenantId, sequenceNo, tpcConnectionResults[1]);
                        /** STEP3.1版対応　変更　END */

                        /** STEP3.1版対応　追加　START */
                        if (ResultCdConstants.CODE_000951.equals(sOAPCommonOutputDto.getProcessCode())) {
                            // SG取得がNG
                            handleCode = ResultCdConstants.CODE_030401;
                            super.warn(tenantId, sequenceNo, MsgKeysConstants.APLCFW0010, "（SG取得NG）（サービスパターン=2）");
                            await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId, handleCode, receivedDate, orderType, reserveDate, isPrivate);
                            lineAddAcquisitionOutputDto = this.returnEdit(param, handleCode, apiHandleId, isPrivate, errorMessage, receivedDate);
                            return lineAddAcquisitionOutputDto;
                        }
                        /** STEP3.1版対応　追加　END */

                        document = sOAPCommonOutputDto.getDoc();
                        let resultStr: string = this.sOAPCommon.getNodeContent(document, "//Result");
                        if (LineAddAcquisitionService.CONST_RESULT.equals(resultStr)) {
                            handleCode = ResultCdConstants.CODE_030401;
                            super.warn(tenantId, sequenceNo, MsgKeysConstants.APLCFW0010, "（エラーまたはTPCからのResultがNG）（サービスパターン=2）");
                            // SO管理共通パラメータ設定
                            await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId, handleCode, receivedDate, orderType, reserveDate, isPrivate);
                            // 返却値編集
                            lineAddAcquisitionOutputDto = this.returnEdit(param, handleCode, apiHandleId, isPrivate, errorMessage, receivedDate);
                            return lineAddAcquisitionOutputDto;
                        }

                        // STEP22.0版対応　追加　START
                        // セカンダリTPCへ連携
                        if (tpcConnectionResults[2] != null) {
                            // セカンダリTPCが設定されている場合は以下の処理を行う(電文はプライマリと同じものを使用)
                            try {
                                sOAPCommonOutputDto
                                    = await this.sOAPCommon.callWebSoapApi("serviceProfileRequestLite", "Mod", "", paraList,
                                    tenantId, sequenceNo, tpcConnectionResults[2],
                                    false);

                                if (ResultCdConstants.CODE_000951.equals(sOAPCommonOutputDto.getProcessCode())) {
                                    // SG取得でNGの場合
                                    super.warn(tenantId, sequenceNo, MsgKeysConstants.APLCFW0022, "SG取得NG（サービスパターン=2）");
                                } else {
                                    // TPCからのResultがNGの場合
                                    document = sOAPCommonOutputDto.getDoc();
                                    resultStr = this.sOAPCommon.getNodeContent(document, "//Result");
                                    if (sOAPCommonOutputDto.isError() || LineAddAcquisitionService.CONST_RESULT.equals(resultStr)) {
                                        const errInfo: string = this.sOAPCommon.getNodeContent(document, "//ErrorInfomation");
                                        super.warn(tenantId, sequenceNo, MsgKeysConstants.APLCFW0022, errInfo + "（サービスパターン=2）");
                                    }
                                }
                            } catch (e: any) {
                                if (SOAPException.isSOAPException(e)) {
                                    super.warn(e, tenantId, sequenceNo, MsgKeysConstants.APLCFW0022, e.toString() + "（サービスパターン=2）");
                                } else {
                                    throw e;
                                }
                            }
                        }
                        // STEP22.0版対応　追加　END

                    } else if (LineAddAcquisitionService.CONST_SERVICEPATTERN_5.equals(koushiServicePt)) {

                        // STEP20.0版対応　変更　START
                        // 有効期限（日数）結果取得
                        let additionalCouponTermValidity: string = "";
                        let additionalCouponTermValidityNichisu: OptionPlanParametersEntity = null;
                        try {
                            additionalCouponTermValidityNichisu
                                = await this.aPICommonDAO.getOptionPlanParameters(optionPlanId, LineAddAcquisitionService.CONST_PARAMKEY_EXP_DATE);
                            if (additionalCouponTermValidityNichisu === null
                                || "".equals(additionalCouponTermValidityNichisu.value)) {
                                handleCode = ResultCdConstants.CODE_030305;
                                super.warn(tenantId, sequenceNo, MsgKeysConstants.APLCFW0008, "有効期限（日数）");
                                // SO管理共通パラメータ設定
                                await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId, handleCode,
                                    receivedDate, orderType, reserveDate, isPrivate);
                                // 返却値編集
                                lineAddAcquisitionOutputDto
                                    = this.returnEdit(param, handleCode, apiHandleId, isPrivate, errorMessage,
                                    receivedDate);
                                return lineAddAcquisitionOutputDto;
                            } else {
                                additionalCouponTermValidity = additionalCouponTermValidityNichisu.value;
                            }
                        } catch (e: any) {
                            if (isSQLException(e)) {
                                // DBアクセスリトライエラーの場合
                                handleCode = ResultCdConstants.CODE_030305;
                            }
                            throw e;
                        }
                        // STEP20.0版対応　変更　END

                        // 有効期限（日数）のフォーマットチェック
                        const additionalCouponTermValidityNichiSu: number = parseInt(additionalCouponTermValidity, 10);
                        if (!(additionalCouponTermValidityNichiSu >= LineAddAcquisitionService.CONST_VALIDITYNICHISU_UPPER
                            && additionalCouponTermValidityNichiSu <= LineAddAcquisitionService.CONST_VALIDITYNICHISU_LOWER)) {
                            super.warn(tenantId, sequenceNo, MsgKeysConstants.APLCFW0006,
                                LineAddAcquisitionService.CONST_ADDITIONALCOUPONTERMVALIDITY_DD + "（サービスパターン=5）", additionalCouponTermValidity);
                            handleCode = ResultCdConstants.CODE_030306;
                            // SO管理共通パラメータ設定
                            await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId, handleCode, receivedDate, orderType, reserveDate, isPrivate);
                            // 返却値編集
                            lineAddAcquisitionOutputDto = this.returnEdit(param, handleCode, apiHandleId, isPrivate, errorMessage, receivedDate);
                            return lineAddAcquisitionOutputDto;
                        }

                        // SOAP APIにより、オプションサービスに対する容量を追加する
                        // パラメータ設定
                        const paraList: ParameterName[] = [];
                        const pronum: ParameterName = new ParameterName();
                        pronum.setName("pronum");
                        pronum.setValue("102" + MvnoUtil.addZeroBeforeStr(lineNo, 15));
                        paraList.push(pronum);
                        /** STEP3.1版対応　変更　START */
                        let sOAPCommonOutputDto: SOAPCommonOutputDto = await this.sOAPCommon.callWebSoapApi("serviceProfileQuery",
                            "ProfileView", "Service", paraList, tenantId, sequenceNo, tpcConnectionResults[1]);
                        /** STEP3.1版対応　変更　END */

                        /** STEP3.1版対応　追加　START */
                        if (ResultCdConstants.CODE_000951.equals(sOAPCommonOutputDto.getProcessCode())) {
                            // SG取得がNG
                            handleCode = ResultCdConstants.CODE_030307;
                            super.warn(tenantId, sequenceNo, MsgKeysConstants.APLCFW0007);
                            await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId, handleCode, receivedDate, orderType, reserveDate, isPrivate);
                            lineAddAcquisitionOutputDto = this.returnEdit(param, handleCode, apiHandleId, isPrivate, errorMessage, receivedDate);
                            return lineAddAcquisitionOutputDto;
                        }
                        /** STEP3.1版対応　追加　END */

                        document = sOAPCommonOutputDto.getDoc();
                        let resultStr: string = this.sOAPCommon.getNodeContent(document, "//Result");
                        if (LineAddAcquisitionService.CONST_RESULT.equals(resultStr)) {
                            handleCode = ResultCdConstants.CODE_030307;
                            super.warn(tenantId, sequenceNo, MsgKeysConstants.APLCFW0007,
                                this.sOAPCommon.getNodeContent(document, "//ErrorInfomation"));
                            // SO管理共通パラメータ設定
                            await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId, handleCode, receivedDate, orderType, reserveDate, isPrivate);
                            // 返却値編集
                            lineAddAcquisitionOutputDto = this.returnEdit(param, handleCode, apiHandleId, isPrivate, errorMessage, receivedDate);
                            return lineAddAcquisitionOutputDto;
                        }

                        // 変更前有効期限
                        const exp_dateFmt: string = this.sOAPCommon.getNodeContent(document,
                            "//ServiceProfileInfomation/exp_date");
                        // 変更後有効期限計算
                        let henkougoyukoukigen: string = "";
                        if(!CheckUtil.checkIsNotNull(exp_dateFmt)){
                            const henkoumaeyukoukigen: string = CheckUtil.forMartDate(exp_dateFmt);
                            const df = "yyyy/MM/dd";
                            const systemDate: string = format(new Date(), df);

                            const sdf = "yyyy/MM/dd";
                            let dt: Date;
                            if (henkoumaeyukoukigen.compareTo(systemDate) <= 0) {
                                dt = parse(systemDate, sdf, new Date());
                            } else {
                                dt = parse(henkoumaeyukoukigen, sdf, new Date());
                            }
                            // システム日付、または変更前有効期限＋変数「有効期限（日数）」
                            const rightNow = addDays(dt, additionalCouponTermValidityNichiSu);
                            const reDate: string = format(rightNow, sdf);
                            const henkougoyukoukigenBf: string = "407" + reDate.substring(2);
                            henkougoyukoukigen = henkougoyukoukigenBf.replace(/\//g, "");
                        }

                        // SOAP APIにより、オプションサービスに対する容量を追加する
                        // パラメータ設定
                        const paraNmList: ParameterName[] = [];
                        const phoneNumber: ParameterName = new ParameterName();
                        phoneNumber.setName("phone_number");
                        phoneNumber.setValue(lineNo);
                        paraNmList.push(phoneNumber);
                        const policyNumber: ParameterName = new ParameterName();
                        policyNumber.setName("policy_number");
                        const porishiStr: string = porishiId.toString();
                        policyNumber.setValue(porishiStr);
                        paraNmList.push(policyNumber);
                        const expDate: ParameterName = new ParameterName();
                        expDate.setName("exp_date");
                        expDate.setValue(henkougoyukoukigen);
                        paraNmList.push(expDate);
                        /** STEP3.1版対応　変更　START */
                        sOAPCommonOutputDto = await this.sOAPCommon.callWebSoapApi("serviceProfileRequestLite", "Mod", "",
                            paraNmList, tenantId, sequenceNo, tpcConnectionResults[1]);
                        /** STEP3.1版対応　変更　END */

                        /** STEP3.1版対応　追加　START */
                        if (ResultCdConstants.CODE_000951.equals(sOAPCommonOutputDto.getProcessCode())) {
                            // SG取得がNG
                            handleCode = ResultCdConstants.CODE_030401;
                            super.warn(tenantId, sequenceNo, MsgKeysConstants.APLCFW0010, "（SG取得NG）（サービスパターン=5）");
                            await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId, handleCode, receivedDate, orderType, reserveDate, isPrivate);
                            lineAddAcquisitionOutputDto = this.returnEdit(param, handleCode, apiHandleId, isPrivate, errorMessage, receivedDate);
                            return lineAddAcquisitionOutputDto;
                        }
                        /** STEP3.1版対応　追加　END */

                        document = sOAPCommonOutputDto.getDoc();
                        const resultXml: string = this.sOAPCommon.getNodeContent(document, "//Result");
                        if (LineAddAcquisitionService.CONST_RESULT.equals(resultXml)) {
                            handleCode = ResultCdConstants.CODE_030401;
                            super.warn(tenantId, sequenceNo, MsgKeysConstants.APLCFW0010, "（エラーまたはTPCからのResultがNG）（サービスパターン=5）");
                            // SO管理共通パラメータ設定
                            await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId, handleCode, receivedDate, orderType, reserveDate, isPrivate);
                            // 返却値編集
                            lineAddAcquisitionOutputDto = this.returnEdit(param, handleCode, apiHandleId, isPrivate, errorMessage, receivedDate);
                            return lineAddAcquisitionOutputDto;
                        }

                        // STEP22.0版対応　追加　START
                        // セカンダリTPCへ連携
                        if (tpcConnectionResults[2] != null) {
                            // セカンダリTPCが設定されている場合は以下の処理を行う(電文はプライマリと同じものを使用)
                            try {
                                sOAPCommonOutputDto
                                    = await this.sOAPCommon.callWebSoapApi("serviceProfileRequestLite", "Mod", "", paraNmList,
                                    tenantId, sequenceNo, tpcConnectionResults[2],
                                    false);

                                if (ResultCdConstants.CODE_000951.equals(sOAPCommonOutputDto.getProcessCode())) {
                                    // SG取得でNGの場合
                                    super.warn(tenantId, sequenceNo, MsgKeysConstants.APLCFW0022, "SG取得NG（サービスパターン=5）");
                                } else {
                                    // TPCからのResultがNGの場合
                                    document = sOAPCommonOutputDto.getDoc();
                                    resultStr = this.sOAPCommon.getNodeContent(document, "//Result");
                                    if (sOAPCommonOutputDto.isError() || LineAddAcquisitionService.CONST_RESULT.equals(resultStr)) {
                                        const errInfo: string = this.sOAPCommon.getNodeContent(document, "//ErrorInfomation");
                                        super.warn(tenantId, sequenceNo, MsgKeysConstants.APLCFW0022, errInfo + "（サービスパターン=5）");
                                    }
                                }
                            } catch (e: any) {
                                if (SOAPException.isSOAPException(e)) {
                                    super.warn(e, tenantId, sequenceNo, MsgKeysConstants.APLCFW0022, e.toString() + "（サービスパターン=5）");
                                } else {
                                    throw e;
                                }
                            }
                        }
                        // STEP22.0版対応　追加　END
                    }
                }

                /** STEP2.0a版対応　追加　START */
                /** STEP4.0版対応　変更　START */
                if (Constants.ORDER_TYPE_0.equals(orderType) || Constants.ORDER_TYPE_2.equals(orderType)) {
                    /** STEP4.0版対応　変更　END */
                    // 代表N番を判定する
                    if (StringUtils.isEmpty(checkResult[1])) {
                        // 変数「CSV作成要否」を0で処理する
                        csvOutputKbn = "0";
                        /** #378 対応  変更 START */
                        handleCode = ResultCdConstants.CODE_000000;
                        super.error(tenantId, sequenceNo, MsgKeysConstants.APLCFE0602, param.tenantId);
                        // SO管理共通パラメータ設定
                        await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId, handleCode, receivedDate, orderType, reserveDate, isPrivate);
                        // 返却値編集
                        lineAddAcquisitionOutputDto = this.returnEdit(param, handleCode, apiHandleId, isPrivate, errorMessage, receivedDate);
                        return lineAddAcquisitionOutputDto;
                        /** #378 対応  変更 END */
                    }
                    // STEP20.0版対応　変更　START
                    let wholeFlag: boolean[] = null;
                    try {
                        // 4.2.1 変数「帯域卸フラグ」取得
                        wholeFlag = await this.apiLinesDao.getWholeFlag(tenantId, checkResult[1]);
                    } catch (e) {
                        if (isSQLException(e)) {
                            // DBアクセスリトライエラーの場合
                            // 変数「帯域卸フラグ」にnullを設定
                            handleCode = ResultCdConstants.CODE_000000;
                            super.error(e, tenantId, sequenceNo, MsgKeysConstants.APCOME0402, functionType + " getWholeFlag");
                            wholeFlag = null;
                        } else {
                            throw e;
                        }
                    }
                    // STEP20.0版対応　変更　END
                    // 取得件数が0件あるいば変数「帯域卸フラグ」がnullあるいば変数「帯域卸フラグ」がTRUE以外の場合
                    /** #377 対応　変更 　START */
                    if (wholeFlag === null || wholeFlag.length === 0 || wholeFlag[0] === null || !wholeFlag[0]) {
                        /** #377 対応　変更 　END */
                        let tenantTypeCheck: string = "1";
                        // 即時オーダの場合
                        if (Constants.ORDER_TYPE_0.equals(orderType)) {
                            // STEP20.0版対応　変更　START
                            let tenantsEntity: TenantsEntity = null;
                            try {
                                // 4.3.1 変数「社内テナント情報」取得
                                tenantsEntity = await this.aPICommonDAO.getTenants(tenantId);
                                /**  2.0aFIX以降のBD変更 削除 　START */
                                // //変数「社内テナント情報」=null
                                // if (tenantsEntity === =null) {
                                //     // 変数「CSV作成要否」を0で処理する
                                //     csvOutputKbn = "0";
                                //     /** #378 対応  変更 START */
                                //     handleCode = ResultCdConstants.CODE_000000;
                                //     /** #378 対応  変更 END */
                                //     super.error(tenantId, sequenceNo, MsgKeysConstants.APLCFE0604, param.getTenantId());
                                //    // SO管理共通パラメータ設定
                                //     await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId, handleCode, receivedDate, orderType, reserveDate, isPrivate);
                                //     // 返却値編集
                                //     lineAddAcquisitionOutputDto = this.returnEdit(param, handleCode, apiHandleId, isPrivate, errorMessage, receivedDate);
                                //     return lineAddAcquisitionOutputDto;
                                // }
                                /**  2.0aFIX以降のBD変更  削除 　END */
                                if (tenantsEntity != null
                                    && tenantsEntity.tenantType != null
                                    && (tenantsEntity.tenantType === 1
                                        || tenantsEntity.tenantType === 2
                                        || tenantsEntity.tenantType === 3
                                        || tenantsEntity.tenantType === 5)) {
                                    tenantTypeCheck = "0";
                                }
                            } catch (e: any) {
                                if(isSQLException(e)) {
                                    // DBアクセスリトライエラーの場合
                                    // 変数「社内テナント種別」を0で処理する
                                    handleCode = ResultCdConstants.CODE_000000;
                                    super.error(e, tenantId, sequenceNo, MsgKeysConstants.APCOME0402, functionType + " getTenants");
                                    tenantTypeCheck = "0";
                                } else {
                                    throw e;
                                }
                            }
                            // STEP20.0版対応　変更　END
                        }
                        // 変数「社内テナント情報」．「社内テナント種別」が、(1または2または3または5)以外の場合、あるいは、nullの場合
                        if ("1".equals(tenantTypeCheck)) {
                            // STEP20.0版対応　変更　START
                            try {
                                // added code for getting optionPlanTBan
                                optionPlanTBan = await this.aPICommonDAO.getOptionPlanTBan(optionPlanId);
                                if (optionPlanTBan == null) {
                                    handleCode = ResultCdConstants.CODE_000000;
                                    super.error(tenantId, sequenceNo, MsgKeysConstants.APLCFE0605, optionPlanId + " （T番取得失敗）");

                                    // SO管理共通パラメータ設定
                                    await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId,
                                        handleCode, receivedDate, orderType, reserveDate, isPrivate);
                                    // 返却値編集
                                    lineAddAcquisitionOutputDto
                                        = this.returnEdit(param, handleCode, apiHandleId, isPrivate, errorMessage,
                                        receivedDate);
                                    return lineAddAcquisitionOutputDto;
                                } else {
                                    // 4.4.3 変数「容量追加コード」のCSV容量追加コードフォーマットチェック
                                    if (!Check.checkOptionPlanTBanFmt(StringUtils.trimToEmpty(optionPlanTBan))) {
                                        this.context.error(optionPlanTBan);
                                        // 変数「CSV作成要否」を0で処理する
                                        csvOutputKbn = "0";
                                        /** #378 対応  変更 START */
                                        handleCode = ResultCdConstants.CODE_000000;
                                        /** #378 対応  変更 END */
                                        super.error(tenantId, sequenceNo, MsgKeysConstants.APLCFE0605,
                                            getStringParameter(param.optionPlanId) + " (T番フォーマットNG)");
                                        // SO管理共通パラメータ設定
                                        await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId,
                                            handleCode, receivedDate, orderType, reserveDate,
                                            isPrivate);
                                        // 返却値編集
                                        lineAddAcquisitionOutputDto
                                            = this.returnEdit(param, handleCode, apiHandleId, isPrivate,
                                            errorMessage, receivedDate);
                                        return lineAddAcquisitionOutputDto;
                                    } else {
                                        /** STEP4.0版対応　追加　START */
                                        // （予約実行時）予約時のシステム時刻を取得する
                                        if (Constants.ORDER_TYPE_2.equals(orderType)) {
                                            const soData: ServiceOrdersEntity = await this.aPICommonDAO.getServiceOrder(reserveSoId);
                                            orderDate = format(soData.orderDate, "yyyy/MM/dd HH:mm:ss");
                                        }
                                        /** STEP4.0版対応　追加　END */
                                        // 変数「CSV作成要否」を1で処理する
                                        csvOutputKbn = "1";
                                    }
                                }
                            } catch (e: any) {
                                if (isSQLException(e)) {
                                    // DBアクセスリトライエラーの場合
                                    // 変数「CSV作成要否」を0で処理する
                                    csvOutputKbn = "0";
                                    handleCode = ResultCdConstants.CODE_000000;
                                    super.error(e, tenantId, sequenceNo, MsgKeysConstants.APCOME0402, functionType + " T番取得");
                                    // SO管理共通パラメータ設定
                                    await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId, handleCode,
                                        receivedDate, orderType, reserveDate, isPrivate);
                                    // 返却値編集
                                    lineAddAcquisitionOutputDto
                                        = this.returnEdit(param, handleCode, apiHandleId, isPrivate, errorMessage,
                                        receivedDate);
                                    return lineAddAcquisitionOutputDto;
                                } else {
                                    throw e;
                                }
                            }
                            // STEP20.0版対応　変更　END
                        } else {
                            // 変数「CSV作成要否」を0で処理する
                            csvOutputKbn = "0";
                        }
                    } else {
                        // 変数「CSV作成要否」を0で処理する
                        csvOutputKbn = "0";
                    }
                    // 変数「帯域卸フラグ」がTRUEの場合、
                    // ４．５．１　変数「CSV作成要否」より判定する
                    // 変数「CSV作成要否」＝“1” の場合
                    if ("1".equals(csvOutputKbn)) {
                        /** STEP4.0版対応　追加　START */
                        let apiId: string;
                        if (Constants.ORDER_TYPE_0.equals(orderType)) {
                            apiId = apiHandleId;
                        } else {
                            apiId = reserveSoId;
                        }
                        /** STEP4.0版対応　追加　END */
                        /** STEP4.0版対応　変更　START */
                        // 変数「ファイル名通番」＝共通の１から取得された変数「API処理ID」の下12桁
                        if (apiHandleId.length > 12) {
                            fileNameNo = apiId.substring(apiId.length - 12);
                        } else {
                            fileNameNo = apiId;
                        }
                    }
                } else {
                    // 変数「CSV作成要否」を0で処理する
                    csvOutputKbn = "0";
                }
                // SO管理共通パラメータ設定
                await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId, handleCode, receivedDate, orderType, reserveDate, isPrivate);
                // 返却値編集
                if (csvOutputKbn === "0" || !(ResultCdConstants.CODE_000000.equals(handleCode))) {
                    lineAddAcquisitionOutputDto = this.returnEdit(param, handleCode, apiHandleId, isPrivate, errorMessage, receivedDate);
                } else {
                    if (Constants.ORDER_TYPE_0.equals(orderType)) {
                        lineAddAcquisitionOutputDto = this.returnEdit(param, handleCode, apiHandleId, isPrivate, errorMessage, receivedDate, nNo, csvOutputKbn,
                            receivedDate,
                            optionPlanTBan);
                    } else if (Constants.ORDER_TYPE_2.equals(orderType)) {
                        lineAddAcquisitionOutputDto = this.returnEdit(param, handleCode, apiHandleId, isPrivate, errorMessage, orderDate, nNo, csvOutputKbn,
                            reserveDate, optionPlanTBan);
                    }
                }
                return lineAddAcquisitionOutputDto;
            } else {
                handleCode = ResultCdConstants.CODE_030101;
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APLCFW0001, LineAddAcquisitionService.CONST_LINENO, lineNo);
                // SO管理共通パラメータ設定
                await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId, handleCode, receivedDate, orderType, reserveDate, isPrivate);
                // 返却値編集
                lineAddAcquisitionOutputDto = this.returnEdit(param, handleCode, apiHandleId, isPrivate, errorMessage, receivedDate);
                return lineAddAcquisitionOutputDto;
            }
        } catch (e: any) {
            if (isSQLException(e)) {
                // DBアクセスリトライエラーの場合
                // リターンコード値は各エラー時にて設定済み
                super.error(e, tenantId, sequenceNo, MsgKeysConstants.APCOME0402, functionType + " other");
                // SO管理共通パラメータ設定
                await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId, handleCode, receivedDate,
                    orderType, reserveDate, isPrivate);
                // 返却値編集
                lineAddAcquisitionOutputDto
                    = this.returnEdit(param, handleCode, apiHandleId, isPrivate, errorMessage, receivedDate);
                return lineAddAcquisitionOutputDto;
                // STEP20.0版対応　追加　END
            } else if (SOAPException.isSOAPException(e)) {
                super.error(e, tenantId, sequenceNo, MsgKeysConstants.APCOMW0001, "回線追加チャージ(クーポン)容量・期間追加");
                handleCode = ResultCdConstants.CODE_030401;
                // SO管理共通パラメータ設定
                await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId, handleCode, receivedDate, orderType, reserveDate, isPrivate);
                // 返却値編集
                lineAddAcquisitionOutputDto = this.returnEdit(param, handleCode, apiHandleId, isPrivate, errorMessage, receivedDate);
                return lineAddAcquisitionOutputDto;
            } else {
                handleCode = ResultCdConstants.CODE_999999;
                super.error(e, param.tenantId, param.requestHeader.sequenceNo, MsgKeysConstants.APCOME0401, e.message);
                // SO管理共通パラメータ設定
                await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId, handleCode, receivedDate, orderType, reserveDate, isPrivate);
                // 返却値編集
                lineAddAcquisitionOutputDto = this.returnEdit(param, handleCode, apiHandleId, isPrivate, errorMessage, receivedDate);
                return lineAddAcquisitionOutputDto;
                /** STEP1.2b版対応　追加　START */
            }
        } finally {
            super.debug(tenantId, sequenceNo, MsgKeysConstants.APLCFD0002, this.getClassName(), "service");
        }
    }

    /**
     * 返却値編集。<BR>
     *
     * @param pram 回線追加チャージ(クーポン)容量追加・期間追加機能インプット
     * @param rtnHandleCode 処理コード
     * @param rtnapiHandleId API処理ID
     * @param isPrivate 内部呼出フラグ
     * @param errorMessage エラーメッセージ
     * @param receivedDate REST API共通処理が呼び出されたシステム日付
     * @return LineAddAcquisitionOutputDto 回線追加チャージ(クーポン)容量追加・期間追加機能アウトプット
     */
    /** STEP1.2a版対応 変更 START */
    private returnEdit(
        pram: LineAddAcquisitionInputDto,
        rtnHandleCode: string,
        /** STEP1.2a版対応 変更 END */
        rtnapiHandleId: string,
        /** STEP1.2a版対応　追加　START */
        isPrivate: boolean,
        errorMessage: string,
        /** STEP1.2a版対応　追加　END */
        receivedDate: string,
    ) : LineAddAcquisitionOutputDto;
    private returnEdit(
        pram: LineAddAcquisitionInputDto,
        rtnHandleCode: string,
        /** STEP1.2a版対応 変更 END */
        rtnapiHandleId: string,
        /** STEP1.2a版対応　追加　START */
        isPrivate: boolean,
        errorMessage: string,
        /** STEP1.2a版対応　追加　END */
        receivedDate: string,
        nNo: string,
        csvOutputKbn: string,
        requestDate: string,
        optionPlanTBan: string
    ) : LineAddAcquisitionOutputDto;
    private returnEdit(
        pram: LineAddAcquisitionInputDto,
        rtnHandleCode: string,
        /** STEP1.2a版対応 変更 END */
        rtnapiHandleId: string,
        /** STEP1.2a版対応　追加　START */
        isPrivate: boolean,
        errorMessage: string,
        /** STEP1.2a版対応　追加　END */
        receivedDate: string,
        nNo: string = null,
        csvOutputKbn: string = "0",
        requestDate: string = null,
        optionPlanTBan: string = null
    ) : LineAddAcquisitionOutputDto {
        const responseHeader: ResponseHeader = {
            sequenceNo: pram.requestHeader.sequenceNo,
            receivedDate,
            processCode: rtnHandleCode,
            apiProcessID: rtnapiHandleId,
        };

        const returnPram: LineAddAcquisitionOutputDto = {
            jsonBody: {
                responseHeader
            },
            additionalData: {
                csvOutputKbn,
                nNo,
                csvUnnecessary: csvOutputKbn === "0",
                requestDate: requestDate ? requestDate.substring(0, 10).replace(/[-/]/g, "") : null,
                tBan: optionPlanTBan
            },
        };

        return returnPram;
    }

    /** STEP1.2a版対応 追加 START */

    /** STEP1.2b版対応 変更 START */
    /**
     * SO管理共通パラメータ設定
     *
     * @param param
     * @param resultBean
     * @param executeUserId
     * @param executeTenantId
     * @param receivedDate REST API共通処理が呼び出されたシステム日付
     * @param orderType
     * @param reserveDate
     * @param isPrivate
     * @throws Exception
     */
    private async soManagementCommon
    (
        param: LineAddAcquisitionInputDto,
        resultBean: CheckResultBean,
        executeUserId: string,
        executeTenantId: string,
        code: string,
        receivedDate: string,
        orderType: string,
        reserveDate: string,
        isPrivate: boolean
    ) {
        // SO管理共通を呼び出す
        const soObject: SOObject = new SOObject();

        // サービスオーダID
        soObject.setServiceOrderId(resultBean.others);
        // 申込日
        soObject.setOrderDate(receivedDate);
        // 予約日
        // 変数「オーダ種別」は「予約前オーダ」の場合
        if (Constants.ORDER_TYPE_1.equals(orderType)) {
            soObject.setReserveDate(reserveDate);
        } else {
            soObject.setReserveDate(null);
        }
        // オーダ種別
        soObject.setOrderType(orderType);
        // 完了日
        // 変数「オーダ種別」は「即時オーダ」の場合
        if (Constants.ORDER_TYPE_0.equals(orderType)
            || Constants.ORDER_TYPE_2.equals(orderType)) {
            soObject.setExecDate(MvnoUtil.getDateTimeNow());
        } else {
            soObject.setExecDate(null);
        }
        // オーダステータス
        soObject.setOrderStatus(code);
        // 回線ID
        soObject.setLineId(param.lineNo);
        // 回線グループID
        soObject.setLineGroupId("");
        // 所属テナントID
        soObject.setTenantId(param.tenantId);
        // 実行ユーザID
        soObject.setExecuteUserId(executeUserId);
        // 実行テナントID
        soObject.setExecuteTenantId(executeTenantId);
        // 機能種別
        soObject.setFunctionType(param.requestHeader.functionType);
        // 操作区分
        soObject.setOperationDivision("");
        // 変更前プランID
        soObject.setChangeOldplanId("");
        // 変更後プランID
        soObject.setChangeNewplanId("");
        // 回線所属変更回線ID
        soObject.setChangePlanId("");
        // 基本容量
        soObject.setBasicCapacity("");
        // オプションプランID
        soObject.setOptionPlanId(getStringParameter(param.optionPlanId));
        // 内部呼び出し
        soObject.setInternalFlag(isPrivate);
        // REST電文
        // 変数「オーダ種別」は「予約前オーダ」の場合
        if (Constants.ORDER_TYPE_1.equals(orderType)) {
            soObject.setRestMessage(MvnoUtil.getRestMessage(param));
        } else {
            soObject.setRestMessage(null);
        }

        await this.soCommon.soCommon(soObject);
    }
}