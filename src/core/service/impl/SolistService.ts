import { format } from "date-fns";
import MsgKeysConstants from "@/core/constant/MsgKeysConstants";
import ResultCdConstants from "@/core/constant/ResultCdConstants";

import CheckUtil from "@/core/common/CheckUtil";
import Check from "@/core/common/Check";
import MvnoUtil from "@/core/common/MvnoUtil";
import RESTCommon from "@/core/common/RESTCommon";
import StringUtils from "@/core/common/StringUtils";

import APISoDAO from "@/core/dao/APISoDAO";
import SolistInputDto from "@/core/dto/SolistInputDto";
import SolistOutputDto from "@/core/dto/SolistOutputDto";
import ResponseHeader from "@/core/dto/ResponseHeader";

import { isSQLException } from "@/helpers/queryHelper";
import { IFService } from "../IFService";
import { TenantsEntity } from "@/core/entity/TenantsEntity";
import ServiceOrdersEntity from "@/core/entity/ServiceOrdersEntity";
import { toInt } from "validator";
import SoList from "@/core/dto/Solist";
import MvnoOnlineUtil from "@/core/common/MvnoOnlineUtil";

/** 定数　「検索テナントID」 */
const CONST_TENANTIDKEY = "検索テナントID";
/** 定数　「SO-ID」 */
const CONST_SERVICEORDERIDKEY = "SO-ID";
/** 定数　「回線ID」 */
const CONST_LINENO = "回線ID";
/** 定数　「ステータス」 */
const CONST_ORDERSTATUSKEY = "ステータス";
/** 定数　「投入日（始期）、投入日（終期）」 */
const CONST_ORDERDATEBEGINKEY_ORDERDATEENDKEY =
    "投入日（始期）、投入日（終期）";
/** 定数　「完了日（始期）、完了日（終期）」 */
const CONST_EXECDATEBEGINKEY_EXECDATEENDKEY = "完了日（始期）、完了日（終期）";
/** 定数　「種別」 */
const CONST_ORDERTYPEKEY = "種別";
/** 定数　「投入日（始期）」 */
const CONST_ORDERDATEBEGINKEY = "投入日（始期）";
/** 定数　「投入日（終期）」 */
const CONST_ORDERDATEENDKEY = "投入日（終期）";
/** 定数　「完了日（始期）」 */
const CONST_EXECDATEBEGINKEY = "完了日（始期）";
/** 定数　「完了日（終期）」 */
const CONST_EXECDATEENDKEY = "完了日（終期）";
/** 定数　「回線ID、SO-ID」 */
const CONST_LINENO_SERVICEORDERIDKEY = "回線ID、SO-ID";

export default class SolistService
    extends RESTCommon
    implements IFService<SolistInputDto, SolistOutputDto>
{
    /**
     * APISoDAO
     */
    private apiSoDao = new APISoDAO(this.request, this.context);

    /**
     * API設計書_SO_SO一覧情報参照機能。<BR>
     *
     * <PRE>
     * SOAP APIにより、受信したパラメータの回線番号に容量と期間の更新、追加処理を行う
     * </PRE>
     *
     * @param param API設計書_SO_SO一覧情報参照機能インプット
     * @param privateFlag trueの場合、内部呼び出し。falseの場合、外部呼出し
     * @return SolistOutputDto API設計書_SO_SO一覧情報参照機能アウトプット
     */
    public async service(
        param: SolistInputDto,
        ...params: any[]
    ): Promise<SolistOutputDto> {
        this.context.log("SolistService.service start", JSON.stringify(param));
        // REST API共通処理が呼び出されたシステム日付
        const receivedDate = this.context.responseHeader.receivedDate;
        // 変数「処理コード」初期化
        let handleCode = ResultCdConstants.CODE_000000;

        // 送信番号取得
        const sequenceNo = param.requestHeader.sequenceNo;
        // 機能種別取得
        const functionType = param.requestHeader.functionType;
        // テナントID取得
        const tenantId = param.tenantId;

        // SO_SO一覧情報参照機能基本情報取得アウトプット初期化
        let solistOutputDto = null as SolistOutputDto;

        // 変数「API処理ID」
        const apiHandleId = this.context.responseHeader.apiProcessID;

        try {
            // 検索テナントID フォーマットチェック
            // 検索テナントID 取得
            const tenantIdKey = param.tenantIdKey;
            if (
                !CheckUtil.checkIsNotNull(tenantIdKey) &&
                !Check.checkTenatIdFmt(tenantIdKey)
            ) {
                this.context.log(
                    "SolistService: tenantId is invalid",
                    tenantIdKey,
                );
                // 処理ログを出力する
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APSOLW0001,
                    CONST_TENANTIDKEY,
                    tenantIdKey,
                );
                // 変数「処理コード」に「160101」を設定する
                handleCode = ResultCdConstants.CODE_160101;
                // 返却値を編集する
                return this.returnEdit(
                    param,
                    handleCode,
                    apiHandleId,
                    receivedDate,
                );
            }

            // SO-IDチェック
            // SO-ID 取得
            const serviceOrderIdKey = param.serviceOrderIdKey;
            // SO-IDの必須チェック
            // 変数「SO-ID必須チェック結果」
            const soIdCheckResult = CheckUtil.checkIsNotNull(serviceOrderIdKey);
            if (!soIdCheckResult) {
                // SO-IDの桁数チェック
                if (!CheckUtil.checkLength(serviceOrderIdKey, 15, false)) {
                    this.context.log(
                        "SolistService: serviceOrderIdKey is invalid (1)",
                        serviceOrderIdKey,
                    );
                    // 処理ログを出力する
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APSOLW0001,
                        CONST_SERVICEORDERIDKEY,
                        serviceOrderIdKey,
                    );
                    // 変数「処理コード」に「160101」を設定する
                    handleCode = ResultCdConstants.CODE_160101;
                    // 返却値を編集する
                    return this.returnEdit(
                        param,
                        handleCode,
                        apiHandleId,
                        receivedDate,
                    );
                }
                // SO-IDの英数字チェック
                if (!CheckUtil.checkIsSemiangleAlphanumic(serviceOrderIdKey)) {
                    this.context.log(
                        "SolistService: serviceOrderIdKey is invalid (2)",
                        serviceOrderIdKey,
                    );
                    // 処理ログを出力する
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APSOLW0001,
                        CONST_SERVICEORDERIDKEY,
                        serviceOrderIdKey,
                    );
                    // 変数「処理コード」に「160101」を設定する
                    handleCode = ResultCdConstants.CODE_160101;
                    // 返却値を編集する
                    return this.returnEdit(
                        param,
                        handleCode,
                        apiHandleId,
                        receivedDate,
                    );
                }
            }

            // 回線番号フォーマットチェック
            // 回線番号取得
            const lineNo = param.lineNoKey;
            // 回線番号の必須チェック
            // 変数「回線番号必須チェック結果」
            const lineNoResult = CheckUtil.checkIsNotNull(lineNo);
            if (!soIdCheckResult && !lineNoResult) {
                this.context.log(
                    "SolistService: serviceOrderIdKey and lineNo are both invalid",
                    serviceOrderIdKey,
                    lineNo,
                );
                // 処理ログを出力する
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APSOLW0003,
                    CONST_LINENO_SERVICEORDERIDKEY,
                    lineNo + "、" + serviceOrderIdKey,
                );
                // 変数「処理コード」に「160301」を設定する
                handleCode = ResultCdConstants.CODE_160301;
                // 返却値を編集する
                return this.returnEdit(
                    param,
                    handleCode,
                    apiHandleId,
                    receivedDate,
                );
            } else if (soIdCheckResult && !lineNoResult) {
                // 回線番号の桁数チェック
                // STEP17.0版対応　変更　START
                if (!CheckUtil.checkLength(lineNo, 14, false)) {
                    this.context.log(
                        "SolistService: lineNo is invalid (1)",
                        lineNo,
                    );
                    // STEP17.0版対応　変更　END
                    // 処理ログを出力する
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APSOLW0001,
                        CONST_LINENO,
                        lineNo,
                    );
                    // 変数「処理コード」に「160101」を設定する
                    handleCode = ResultCdConstants.CODE_160101;
                    // 返却値を編集する
                    return this.returnEdit(
                        param,
                        handleCode,
                        apiHandleId,
                        receivedDate,
                    );
                }
                // 回線番号の数値チェック
                if (!CheckUtil.checkIsNum(lineNo)) {
                    this.context.log(
                        "SolistService: lineNo is invalid (2)",
                        lineNo,
                    );
                    // 処理ログを出力する
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APSOLW0001,
                        CONST_LINENO,
                        lineNo,
                    );
                    // 変数「処理コード」に「160101」を設定する
                    handleCode = ResultCdConstants.CODE_160101;
                    // 返却値を編集する
                    return this.returnEdit(
                        param,
                        handleCode,
                        apiHandleId,
                        receivedDate,
                    );
                }
            }

            // ステータスチェック
            // ステータス  取得
            const orderStatusKey = param.orderStatusKey;
            // ステータスの必須チェック
            if (!CheckUtil.checkIsNotNull(orderStatusKey)) {
                // ステータス値チェックを行う
                if (
                    !"完了".equals(orderStatusKey) &&
                    !"失敗".equals(orderStatusKey) &&
                    /** STEP1.2b版対応　追加　START */
                    !"予約中".equals(orderStatusKey) &&
                    !"キャンセル済み".equals(orderStatusKey)
                ) {
                    this.context.log(
                        "SolistService: orderStatusKey is invalid",
                        orderStatusKey,
                    );
                    /** STEP1.2b版対応　追加　END */
                    // 処理ログを出力する
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APSOLW0001,
                        CONST_ORDERSTATUSKEY,
                        orderStatusKey,
                    );
                    // 変数「処理コード」に「160101」を設定する
                    handleCode = ResultCdConstants.CODE_160101;
                    // 返却値を編集する
                    return this.returnEdit(
                        param,
                        handleCode,
                        apiHandleId,
                        receivedDate,
                    );
                }
            }

            // 投入日フォーマットチェック
            // 投入日(始期) 取得
            let orderDateBeginKey = param.orderDateBeginKey;
            // 投入日(終期) 取得
            let orderDateEndKey = param.orderDateEndKey;
            // 投入日（始期）の必須チェック
            // 変数「投入日（始期）必須チェック結果」
            const orderDateBeginKeyResult =
                CheckUtil.checkIsNotNull(orderDateBeginKey);
            // 投入日（終期）の必須チェック
            // 変数「投入日（終期）必須チェック結果」
            const orderDateEndKeyResult =
                CheckUtil.checkIsNotNull(orderDateEndKey);
            // 変数「投入日（始期）必須チェック結果」=true　AND 変数「投入日（終期）必須チェック結果」=true
            if (!orderDateBeginKeyResult && !orderDateEndKeyResult) {
                // 投入日（始期）のフォーマットチェック
                if (!MvnoUtil.checkDateFmt(orderDateBeginKey)) {
                    this.context.log(
                        "SolistService: orderDateBeginKey is invalid",
                        orderDateBeginKey,
                    );
                    // 処理ログを出力する
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APSOLW0002,
                        CONST_ORDERDATEBEGINKEY,
                        orderDateBeginKey,
                    );
                    // 変数「処理コード」に「160201」を設定する
                    handleCode = ResultCdConstants.CODE_160201;
                    // 返却値を編集する
                    return this.returnEdit(
                        param,
                        handleCode,
                        apiHandleId,
                        receivedDate,
                    );
                }

                // 投入日（終期）のフォーマットチェック
                if (!MvnoUtil.checkDateFmt(orderDateEndKey)) {
                    this.context.log(
                        "SolistService: orderDateEndKey is invalid",
                        orderDateEndKey,
                    );
                    // 処理ログを出力する
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APSOLW0002,
                        CONST_ORDERDATEENDKEY,
                        orderDateEndKey,
                    );
                    // 変数「処理コード」に「160201」を設定する
                    handleCode = ResultCdConstants.CODE_160201;
                    // 返却値を編集する
                    return this.returnEdit(
                        param,
                        handleCode,
                        apiHandleId,
                        receivedDate,
                    );
                }

                // 投入日（始期）と投入日（終期）の比較チェック
                if (
                    !MvnoUtil.checkIsDataComparison(
                        orderDateBeginKey,
                        orderDateEndKey,
                    )
                ) {
                    this.context.log(
                        "SolistService: orderDateBeginKey and orderDateEndKey comparison failed",
                        orderDateBeginKey,
                        orderDateEndKey,
                    );
                    // 処理ログを出力する
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APSOLW0002,
                        CONST_ORDERDATEBEGINKEY_ORDERDATEENDKEY,
                        orderDateBeginKey + "、" + orderDateEndKey,
                    );
                    // 変数「処理コード」に「160202」を設定する
                    handleCode = ResultCdConstants.CODE_160202;
                    // 返却値を編集する
                    return this.returnEdit(
                        param,
                        handleCode,
                        apiHandleId,
                        receivedDate,
                    );
                }
                // 変数「投入日（始期）必須チェック結果」=false　AND 変数「投入日（終期）必須チェック結果」=false
            } else if (orderDateBeginKeyResult && orderDateEndKeyResult) {
                // 完了日フォーマットチェックを行う
                // 上記以外
                this.context.log(
                    "SolistService: orderDateBeginKey and orderDateEndKey are both OK",
                    orderDateBeginKey,
                    orderDateEndKey,
                );
            } else {
                this.context.log(
                    "SolistService: orderDateBeginKey and orderDateEndKey are invalid",
                    orderDateBeginKey,
                    orderDateEndKey,
                );
                // 処理ログを出力する
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APSOLW0001,
                    CONST_ORDERDATEBEGINKEY_ORDERDATEENDKEY,
                    orderDateBeginKey + "、" + orderDateEndKey,
                );
                // 変数「処理コード」に「160101」を設定する
                handleCode = ResultCdConstants.CODE_160101;
                // 返却値を編集する
                return this.returnEdit(
                    param,
                    handleCode,
                    apiHandleId,
                    receivedDate,
                );
            }

            // 完了日フォーマットチェックを行う
            // 完了日(始期) 取得
            const execDateBeginKey = param.execDateBeginKey;
            // 完了日(終期)　取得
            const execDateEndKey = param.execDateEndKey;
            // 完了日（始期）の必須チェック
            // 変数「完了日（始期）必須チェック結果」
            const execDateBeginKeyResult =
                CheckUtil.checkIsNotNull(execDateBeginKey);
            // 変数「完了日（終期）必須チェック結果」
            const execDateEndKeyResult =
                CheckUtil.checkIsNotNull(execDateEndKey);
            // 変数「完了日（始期）必須チェック結果」=true　AND 変数「完了日（終期）必須チェック結果」=true
            if (!execDateBeginKeyResult && !execDateEndKeyResult) {
                // 完了日（始期）のフォーマットチェック
                if (!MvnoUtil.checkDateFmt(execDateBeginKey)) {
                    this.context.log(
                        "SolistService: execDateBeginKey is invalid",
                        execDateBeginKey,
                    );
                    // 処理ログを出力する
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APSOLW0002,
                        CONST_EXECDATEBEGINKEY,
                        execDateBeginKey,
                    );
                    // 変数「処理コード」に「160201」を設定する
                    handleCode = ResultCdConstants.CODE_160201;
                    // 返却値を編集する
                    return this.returnEdit(
                        param,
                        handleCode,
                        apiHandleId,
                        receivedDate,
                    );
                }
                // 完了日（終期）のフォーマットチェック
                if (!MvnoUtil.checkDateFmt(execDateEndKey)) {
                    this.context.log(
                        "SolistService: execDateEndKey is invalid",
                        execDateEndKey,
                    );
                    // 処理ログを出力する
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APSOLW0002,
                        CONST_EXECDATEENDKEY,
                        execDateEndKey,
                    );
                    // 変数「処理コード」に「160201」を設定する
                    handleCode = ResultCdConstants.CODE_160201;
                    // 返却値を編集する
                    return this.returnEdit(
                        param,
                        handleCode,
                        apiHandleId,
                        receivedDate,
                    );
                }
                // 完了日（始期）と完了日（終期）の比較チェック
                if (
                    !MvnoUtil.checkIsDataComparison(
                        execDateBeginKey,
                        execDateEndKey,
                    )
                ) {
                    this.context.log(
                        "SolistService: execDateBeginKey and execDateEndKey comparison failed",
                        execDateBeginKey,
                        execDateEndKey,
                    );
                    // 処理ログを出力する
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APSOLW0002,
                        CONST_EXECDATEBEGINKEY_EXECDATEENDKEY,
                        execDateBeginKey + "、" + execDateEndKey,
                    );
                    // 変数「処理コード」に「160202」を設定する
                    handleCode = ResultCdConstants.CODE_160202;
                    // 返却値を編集する
                    return this.returnEdit(
                        param,
                        handleCode,
                        apiHandleId,
                        receivedDate,
                    );
                }
                // 変数「完了日（始期）必須チェック結果」=false　AND 変数「完了日（終期）必須チェック結果」=false
            } else if (execDateBeginKeyResult && execDateEndKeyResult) {
                // 種別チェックを行う
                // 上記以外
                this.context.log(
                    "SolistService: execDateBeginKey and execDateEndKey are both OK",
                    execDateBeginKey,
                    execDateEndKey,
                );
            } else {
                this.context.log(
                    "SolistService: execDateBeginKey and execDateEndKey are invalid",
                    execDateBeginKey,
                    execDateEndKey,
                );
                // 処理ログを出力する
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APSOLW0001,
                    CONST_EXECDATEBEGINKEY_EXECDATEENDKEY,
                    execDateBeginKey + "、" + execDateEndKey,
                );
                // 変数「処理コード」に「160101」を設定する
                handleCode = ResultCdConstants.CODE_160101;
                // 返却値を編集する
                return this.returnEdit(
                    param,
                    handleCode,
                    apiHandleId,
                    receivedDate,
                );
            }

            // 種別チェックを行う
            // 種別を取得する
            const orderTypeKey = param.orderTypeKey;
            // 種別の必須チェック
            if (!CheckUtil.checkIsNotNull(orderTypeKey)) {
                // 種別値チェックを行う
                if (
                    orderTypeKey !== "クーポン追加" &&
                    orderTypeKey !== "クーポンON" &&
                    orderTypeKey !== "クーポンOFF" &&
                    orderTypeKey !== "アクティベート" &&
                    orderTypeKey !== "ディアクティベート" &&
                    orderTypeKey !== "プラン変更" &&
                    orderTypeKey !== "回線グループクーポン追加" &&
                    orderTypeKey !== "回線グループクーポンON" &&
                    orderTypeKey !== "回線グループクーポンOFF" &&
                    orderTypeKey !== "回線グループ作成" &&
                    orderTypeKey !== "回線グループ廃止" &&
                    orderTypeKey !== "回線グループ所属回線変更(割当)" &&
                    orderTypeKey !== "回線グループ所属回線変更(割当解除)" &&
                    orderTypeKey !== "回線グループ基本容量変更" &&
                    // STEP13.0版対応　追加　START
                    /** STEP5.0対応　追加　START */
                    orderTypeKey !== "データ譲渡" &&
                    orderTypeKey !== "回線グループプラン変更"
                    /** STEP5.0対応　追加　END */
                    // STEP13.0版対応　追加　END
                ) {
                    this.context.log(
                        "SolistService: orderTypeKey is invalid",
                        orderTypeKey,
                    );
                    // 処理ログを出力する
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APSOLW0001,
                        CONST_ORDERTYPEKEY,
                        orderTypeKey,
                    );
                    // 変数「処理コード」に「160101」を設定する
                    handleCode = ResultCdConstants.CODE_160101;
                    // 返却値を編集する
                    return this.returnEdit(
                        param,
                        handleCode,
                        apiHandleId,
                        receivedDate,
                    );
                }
            }

            // 投入日の直近30日分を取得する
            if (
                StringUtils.isEmpty(param.lineNoKey) &&
                StringUtils.isEmpty(param.serviceOrderIdKey) &&
                StringUtils.isEmpty(param.tenantIdKey) &&
                StringUtils.isEmpty(param.orderStatusKey) &&
                StringUtils.isEmpty(param.orderTypeKey) &&
                StringUtils.isEmpty(param.orderDateBeginKey) &&
                StringUtils.isEmpty(param.orderDateEndKey) &&
                StringUtils.isEmpty(param.execDateBeginKey) &&
                StringUtils.isEmpty(param.execDateEndKey)
            ) {
                // 開始日付を算出
                orderDateBeginKey = MvnoUtil.getRecent30Day("30") + "000000";
                // 終了日付を算出
                orderDateEndKey = format(new Date(), "yyyy/MM/ddHHmmss");
                this.context.log(
                    "SolistService: getRecent30Day",
                    orderDateBeginKey,
                    orderDateEndKey,
                );
            }

            // ロジック処理
            // テナント階層情報取得
            // 変数「テナント階層情報」 取得
            // STEP20.0版対応　変更　START
            let tenantsEntityList = null as TenantsEntity[];
            try {
                tenantsEntityList = await this.apiSoDao.getTenantsList(
                    tenantId,
                );
            } catch (e: any) {
                // DBアクセスリトライエラーの場合
                if (isSQLException(e)) {
                    handleCode = ResultCdConstants.CODE_160501;
                }
                throw e;
            }
            // STEP20.0版対応　変更　END
            // 変数「テナント階層情報」の件数が0件の場合
            if (tenantsEntityList.length === 0) {
                this.context.log(
                    "SolistService: tenantsEntityList is empty",
                    tenantId,
                );
                // 処理ログを出力する
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APSOLW0004);
                // 変数「処理コード」に「160501」を設定する
                handleCode = ResultCdConstants.CODE_160501;
                // 返却値を編集する
                return this.returnEdit(
                    param,
                    handleCode,
                    apiHandleId,
                    receivedDate,
                );
            }

            // テナントIDリストを作成する
            let tenantsIdList = [] as string[];

            // テナントIDチェックフラグ
            let tenantIdCheckFlag = true;
            // テナント階層情報をループして、比較する
            for (const tenantsEntity of tenantsEntityList) {
                tenantsIdList.push(tenantsEntity.tenantId);
                // 変数「テナント階層情報」．「idx」．「テナントID」＝入力パラメータ．「検索テナントID」
                if (tenantsEntity.tenantId === tenantIdKey) {
                    tenantIdCheckFlag = false;
                }
            }

            // テナント階層情報は毎に取得したテナントIDとパラメターのテナントIDは一致しない場合
            if (!CheckUtil.checkIsNotNull(tenantIdKey) && tenantIdCheckFlag) {
                this.context.log(
                    "SolistService: tenantIdKey check flag is invalid",
                    tenantIdKey,
                    tenantIdCheckFlag
                );
                // 処理ログを出力する
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APSOLW0005);
                // 変数「処理コード」に「160601」を設定する
                handleCode = ResultCdConstants.CODE_160601;
                // 返却値を編集する
                return this.returnEdit(
                    param,
                    handleCode,
                    apiHandleId,
                    receivedDate,
                );
            }

            try {
                /* #214 対応 追加 START */
                // 詳細検索のテナントIDを指定する場合、検索条件にする。
                if (StringUtils.isNotEmpty(tenantIdKey)) {
                    tenantsIdList = [];
                    tenantsIdList.push(tenantIdKey);
                }
                /* #214 対応 追加 END */
                // 表示件数上限数
                // SO一覧情報を取得する
                let searchCount = 0;
                if (
                    params !== null &&
                    params.length > 0 &&
                    params[0] !== null
                ) {
                    searchCount = parseInt(params[0], 10);
                }
                // STEP20.0版対応　変更　START
                let serviceOrdersList = null as ServiceOrdersEntity[];
                try {
                    serviceOrdersList = await this.apiSoDao.getSoList(
                        tenantsIdList,
                        serviceOrderIdKey,
                        lineNo,
                        orderStatusKey,
                        orderDateBeginKey,
                        orderDateEndKey,
                        execDateBeginKey,
                        execDateEndKey,
                        orderTypeKey,
                        false,
                        searchCount,
                    );
                } catch (e: any) {
                    // DBアクセスリトライエラーの場合
                    if (isSQLException(e)) {
                        handleCode = ResultCdConstants.CODE_160702;
                    }
                    throw e;
                }
                // STEP20.0版対応　変更　END
                // 変数「SO一覧情報」の件数が0件の場合
                if (serviceOrdersList.length === 0) {
                    this.context.log(
                        "SolistService: serviceOrdersList is empty",
                        serviceOrdersList,
                    );
                    // 処理ログを出力する
                    // super.warn(tenantId, sequenceNo, MsgKeysConstants.APSOLW0006);
                    // 変数「処理コード」に「000000」を設定する
                    handleCode = ResultCdConstants.CODE_000000;
                    // 返却値を編集する
                    return this.returnEdit(
                        param,
                        handleCode,
                        apiHandleId,
                        receivedDate,
                    );
                } else {
                    /* #230 対応 変更 START */
                    let tempSoList = {} as { [soId: string]: SoList };
                    for (const serviceOrdersEntity of serviceOrdersList) {
                        const soList = {} as SoList;
                        // サービスオーダーIDリストを設定する
                        /* #337 対応 変更 START */
                        const soId = StringUtils.trimToEmpty(
                            serviceOrdersEntity.serviceOrderId,
                        );
                        soList.order_date = MvnoOnlineUtil.dateFormat(
                            serviceOrdersEntity.orderDate,
                            "yyyy/MM/dd HH:mm:ss",
                        );
                        /** STEP1.2b版対応　追加　START */
                        soList.reserve_date = MvnoOnlineUtil.dateFormat(
                            serviceOrdersEntity.reserveDate,
                            "yyyy/MM/dd HH:mm:ss",
                        );
                        /** STEP1.2b版対応　追加　END */
                        soList.exec_date = MvnoOnlineUtil.dateFormat(
                            serviceOrdersEntity.execDate,
                            "yyyy/MM/dd HH:mm:ss",
                        );
                        soList.order_type = StringUtils.trimToEmpty(
                            serviceOrdersEntity.orderType,
                        );
                        soList.order_status = StringUtils.trimToEmpty(
                            serviceOrdersEntity.orderStatus,
                        );
                        soList.line_id = StringUtils.trimToEmpty(
                            serviceOrdersEntity.lineId,
                        );
                        /* #337 対応 変更 END */
                        tempSoList[soId] = soList;
                    }
                    /* #230 対応 変更 END */
                    // 返却値を編集する
                    solistOutputDto = this.returnEdit(
                        param,
                        handleCode,
                        apiHandleId,
                        receivedDate,
                    );
                    tempSoList = Object.fromEntries(Object.entries(tempSoList).sort());
                    solistOutputDto = {...solistOutputDto, ...tempSoList};
                    // ボディ情報（正常系）を設定する
                    return solistOutputDto;
                }
                // STEP20.0版対応　追加　START
            } catch (e: any) {
                this.context.warn(tenantId, sequenceNo, MsgKeysConstants.APSOLW0006, e);
                // DBアクセスリトライエラーの場合
                if (isSQLException(e)) {
                    throw e;
                }
                // STEP20.0版対応　追加　END
                // 処理ログを出力する
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APSOLW0006);
                // 変数「処理コード」に「160702」を設定する
                handleCode = ResultCdConstants.CODE_160702;
                // 返却値を編集する
                return this.returnEdit(
                    param,
                    handleCode,
                    apiHandleId,
                    receivedDate,
                );
            }
        } catch (e: any) {
            // DBアクセスリトライエラーの場合
            // リターンコード値は各エラー時にて設定済み
            if (isSQLException(e)) {
                super.error(
                    e,
                    param.tenantId,
                    param.requestHeader.sequenceNo,
                    MsgKeysConstants.APCOME0402,
                    functionType,
                );
                // 返却値を編集する
                return this.returnEdit(
                    param,
                    handleCode,
                    apiHandleId,
                    receivedDate,
                );
            }
            // 処理ログを出力する
            super.error(
                e,
                param.tenantId,
                param.requestHeader.sequenceNo,
                MsgKeysConstants.APCOME0401,
                e.message,
            );
            // 変数「処理コード」に「999999」を設定する
            handleCode = ResultCdConstants.CODE_999999;
            // 返却値を編集する
            return this.returnEdit(
                param,
                handleCode,
                apiHandleId,
                receivedDate,
            );
        } finally {
            /* #493対応 MOD END */
            super.debug(
                tenantId,
                sequenceNo,
                MsgKeysConstants.APSOLD0002,
                this.getClassName(),
                "service",
            );
        }
    }

    /**
     * 返却値を編集する。<BR>
     *
     * @param pram API設計書_SO_SO一覧情報参照機能インプット
     * @param rtnHandleCode 処理コード
     * @param rtnapiHandleId API処理ID
     * @param privateFlag trueの場合、内部呼び出し。falseの場合、外部呼出し
     * @param receivedDate REST API共通処理が呼び出されたシステム日付
     * @return SolistOutputDto API設計書_SO_SO一覧情報参照機能アウトプット
     */
    private returnEdit(
        pram: SolistInputDto,
        rtnHandleCode: string,
        rtnapiHandleId: string,
        receivedDate: string,
    ): SolistOutputDto {
        // SO一覧出力情報 を生成する
        const solistOutputDto = {} as SolistOutputDto;

        // ヘッダ情報を設定する
        const responseHeader = {} as ResponseHeader;

        // 送信番号取得
        responseHeader.sequenceNo = pram.requestHeader.sequenceNo;
        // 受信日時
        responseHeader.receivedDate = receivedDate;
        // 処理コード
        responseHeader.processCode = rtnHandleCode;
        // API処理ID
        responseHeader.apiProcessID = rtnapiHandleId;

        solistOutputDto.responseHeader = responseHeader;

        // 内部呼び出す場合
        return solistOutputDto;
    }
}
