import config from "config";
import { IFService } from "../IFService";
import { Transaction } from "sequelize";
import LineGroupRegistAcquisitionOutputDto from "@/core/dto/LineGroupRegistAcquisitionOutputDto";
import LineGroupRegistAcquisitionInputDto from "@/core/dto/LineGroupRegistAcquisitionInputDto";
import RESTCommon from "@/core/common/RESTCommon";
import CheckUtil from "@/core/common/CheckUtil";
import SOObject from "@/core/dto/SOObject";
import SOCommon from "@/core/common/SOCommon";
import APICommonDAO from "@/core/dao/APICommonDAO";
import APILinesGroupDAO from "@/core/dao/ApiLinesGroupDAO";
import SOAPCommon from "@/core/common/SOAPCommon";
import TenantManage from "@/core/common/TenantManage";
import MvnoUtil from "@/core/common/MvnoUtil";
import ResponseHeader from "@/core/dto/ResponseHeader";
import MsgKeysConstants from "@/core/constant/MsgKeysConstants";
import ResultCdConstants from "@/core/constant/ResultCdConstants";
import Check from "@/core/common/Check";
import { TenantsEntity } from "@/core/entity/TenantsEntity";
import LineGroupsEntity from "@/core/entity/LineGroupsEntity";
import TenantGroupPlansEntity from "@/core/entity/TenantGroupPlansEntity";
import { GroupPlansEntity } from "@/core/entity/GroupPlansEntity";
import { retryQuery } from "@/helpers/queryHelper";
import { usePsql } from "@/database/psql";
import StringUtils from "@/core/common/StringUtils";
import SOAPCommonOutputDto from "@/core/dto/SOAPCommonOutputDto";
import { isSQLException } from "@/helpers/queryHelper";
import SOAPException from "@/types/soapException";
import ParameterName from "@/core/dto/ParameterName";
import { isLockNotAvailableError } from "@/database/psql";
import { getStringParameter } from "@/types/parameter.string";
export default class LineGroupRegistAcquisitionServiceTx
    extends RESTCommon
    implements
        IFService<
            LineGroupRegistAcquisitionInputDto,
            LineGroupRegistAcquisitionOutputDto
        >
{
    /** 定数 「回線グループID」 */
    private static readonly CONST_LINEGROUPID = "回線グループID";

    /** 定数 「新規／廃止フラグ」 */
    private static readonly CONST_REGISTFLAG = "新規／廃止フラグ";

    /** 定数 「卸ポータルグループプランID」 */
    private static readonly CONST_LINEGROUPPLANID = "回線グループプランID";

    /** 定数 「卸ポータルグループプランID」 */
    private static readonly CONST_LINEGROUPKIKAN = "回線グループ再利用不可期間";

    /** API共通DAO */
    private apiCommonDao = new APICommonDAO(this.request, this.context);

    /** 回線グループDAO */
    private apiLinesGroupDAO = new APILinesGroupDAO(this.request, this.context);

    /** SOAP共通 */
    private soapCommon = new SOAPCommon(this.request, this.context);

    /**
     * SO管理共通DAO
     */
    private soCommon = new SOCommon(this.request, this.context);

    private tenantManage = new TenantManage(this.request, this.context);

    /** 回線グループ再利用不可期間 */
    private lineGroupUnusableTime: string = config.get<string>(
        "mvno.GroupIDReuseTerm",
    );

    /**
     * 回線グループ新規作成/廃止機能。<BR>
     *
     * <PRE>
     * B-OCN・UNOポータルおよび卸事業者からの回線グループの新設規および廃止リクエストに対し、回線グループの新規／廃止を提供する。
     * </PRE>
     */

    async service(
        param: LineGroupRegistAcquisitionInputDto,
        ...obj: any[]
    ): Promise<LineGroupRegistAcquisitionOutputDto> {
        // デバッグログ出力
        super.debug(
            param.tenantId,
            param.requestHeader.sequenceNo,
            MsgKeysConstants.APGRGD0001,
            this.getClassName(),
            "service",
            JSON.stringify(param),
        );

        // REST API共通処理が呼び出されたシステム日付
        const receivedDate = MvnoUtil.getDateTimeNow();

        // 出力対象生成
        const responseHeader = this.context.responseHeader;
        responseHeader.sequenceNo = param.requestHeader.sequenceNo;
        responseHeader.receivedDate = receivedDate;
        const outputDto: LineGroupRegistAcquisitionOutputDto = {
            jsonBody: { responseHeader, lineGroupId: getStringParameter(param.lineGroupId) },
        };

        // 処理コードを初期化する
        responseHeader.processCode = ResultCdConstants.CODE_000000;
        outputDto.jsonBody.lineGroupId = "";

        try {
            const object = await this.biz(
                param,
                outputDto,
                responseHeader,
                receivedDate,
            );

            super.debug(
                param.tenantId,
                param.requestHeader.sequenceNo,
                MsgKeysConstants.APGRGD0002,
                this.getClassName(),
                "service",
            );
            return object;
        } catch (e) {
            responseHeader.processCode = ResultCdConstants.CODE_999999;
            super.error(
                e as Error,
                param.tenantId,
                param.requestHeader.sequenceNo,
                MsgKeysConstants.APCOME0401,
                (e as Error).message,
            );
            super.debug(
                param.tenantId,
                param.requestHeader.sequenceNo,
                MsgKeysConstants.APGRGD0002,
                this.getClassName(),
                "service",
            );
            return this.returnEdit(outputDto);
        }
    }

    private async biz(
        param: LineGroupRegistAcquisitionInputDto,
        output: LineGroupRegistAcquisitionOutputDto,
        responseHeader: ResponseHeader,
        receivedDate: string,
        ...params: any[]
    ): Promise<LineGroupRegistAcquisitionOutputDto> {
        const {
            requestHeader: { sequenceNo, functionType },
            tenantId,
        } = param;
        const lineGroupId = getStringParameter(param.lineGroupId);
        const registFlag = getStringParameter(param.registFlag);
        const potalGroupPlanID = getStringParameter(param.potalGroupPlanID);

        // 変数「エラーメッセージ」を定義する
        const errorMessage: string = null;
        // 変数「実行ユーザID」を定義する
        const executeUserId: string = null;
        // 変数「実行テナントID」を定義する
        const executeTenantId: string = null;
        // 変数「処理コード」初期化
        let handleCode = ResultCdConstants.CODE_000000;
        const sequelize = await usePsql();
        // トランザクション開始
        let tx: Transaction = null;
        // the variable being used to address open transaction
        let cancelTx: boolean = false;
        try {
            // 回線グループ再利用不可期間の必須チェック
            if (CheckUtil.checkIsNotNull(this.lineGroupUnusableTime)) {
                super.warn(
                    param.tenantId,
                    sequenceNo,
                    MsgKeysConstants.APGRGW0000,
                );

                // SO管理共通パラメータ設定

                await this.soManagementCommon(
                    param,
                    responseHeader.apiProcessID,
                    executeUserId,
                    executeTenantId,
                    ResultCdConstants.CODE_130001,
                    receivedDate,
                    lineGroupId,
                );

                // 返却値を設定し、返却
                responseHeader.processCode = ResultCdConstants.CODE_130001;

                super.debug(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APGRGD0002,
                    this.getClassName(),
                    "service",
                );

                return this.returnEdit(output);
            }

            // 回線グループ再利用不可期間の桁数チェック
            if (!CheckUtil.checkLength(this.lineGroupUnusableTime, 3, false)) {
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APGRGW0000);

                // SO管理共通パラメータ設定
                await this.soManagementCommon(
                    param,
                    responseHeader.apiProcessID,
                    executeUserId,
                    executeTenantId,
                    ResultCdConstants.CODE_130001,
                    receivedDate,
                    lineGroupId,
                );

                // 返却値を設定し、返却
                responseHeader.processCode = ResultCdConstants.CODE_130001;

                super.debug(
                    param.tenantId,
                    sequenceNo,
                    MsgKeysConstants.APGRGD0002,
                    this.getClassName(),
                    "service",
                    JSON.stringify(param),
                );
                return this.returnEdit(output);
            }

            // 回線グループ再利用不可期間の数字チェック
            if (!CheckUtil.checkIsNum(this.lineGroupUnusableTime)) {
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APGRGW0000);

                // SO管理共通パラメータ設定
                await this.soManagementCommon(
                    param,
                    responseHeader.apiProcessID,
                    executeUserId,
                    executeTenantId,
                    ResultCdConstants.CODE_130001,
                    receivedDate,
                    lineGroupId,
                );

                // 返却値を設定し、返却
                responseHeader.processCode = ResultCdConstants.CODE_130001;

                super.debug(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APGRGD0002,
                    this.getClassName(),
                    "service",
                );

                return this.returnEdit(output);
            }

            // 新規/廃止フラグフォーマットチェック
            // 新規の場合
            if (registFlag === "1") {
                // 回線グループプランIDの必須チェック
                if (CheckUtil.checkIsNotNull(potalGroupPlanID)) {
                    super.warn(
                        param.tenantId,
                        sequenceNo,
                        MsgKeysConstants.APGRGW0001,
                        LineGroupRegistAcquisitionServiceTx.CONST_LINEGROUPPLANID,
                        potalGroupPlanID,
                    );

                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(
                        param,
                        responseHeader.apiProcessID,
                        executeUserId,
                        executeTenantId,
                        ResultCdConstants.CODE_130003,
                        receivedDate,
                        lineGroupId,
                    );

                    // 返却値を設定し、返却
                    responseHeader.processCode = ResultCdConstants.CODE_130003;

                    super.debug(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APGRGD0002,
                        this.getClassName(),
                        "service",
                    );

                    return this.returnEdit(output);
                }

                // 回線グループプランIDの桁数チェック
                if (!CheckUtil.checkLength(potalGroupPlanID, 5, true)) {
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APGRGW0001,
                        LineGroupRegistAcquisitionServiceTx.CONST_LINEGROUPPLANID,
                        potalGroupPlanID,
                    );

                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(
                        param,
                        responseHeader.apiProcessID,
                        executeUserId,
                        executeTenantId,
                        ResultCdConstants.CODE_130003,
                        receivedDate,
                        lineGroupId,
                    );

                    // 返却値を設定し、返却
                    responseHeader.processCode = ResultCdConstants.CODE_130003;

                    super.debug(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APGRGD0002,
                        this.getClassName(),
                        "service",
                    );

                    return this.returnEdit(output);
                }

                // 回線グループプランIDの数字チェック
                if (!CheckUtil.checkIsNum(potalGroupPlanID)) {
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APGRGW0001,
                        LineGroupRegistAcquisitionServiceTx.CONST_LINEGROUPPLANID,
                        potalGroupPlanID,
                    );

                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(
                        param,
                        responseHeader.apiProcessID,
                        executeUserId,
                        executeTenantId,
                        ResultCdConstants.CODE_130003,
                        receivedDate,
                        lineGroupId,
                    );

                    // 返却値を設定し、返却
                    responseHeader.processCode = ResultCdConstants.CODE_130003;

                    super.debug(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APGRGD0002,
                        this.getClassName(),
                        "service",
                    );

                    return this.returnEdit(output);
                }

                // 回線グループIDの必須チェック
                if (!CheckUtil.checkIsNotNull(lineGroupId)) {
                    // 回線グループIDのフォーマットチェックを行う
                    if (!Check.checkLineGroupId(lineGroupId)) {
                        super.warn(
                            tenantId,
                            sequenceNo,
                            MsgKeysConstants.APGRGW0001,
                            LineGroupRegistAcquisitionServiceTx.CONST_LINEGROUPID,
                            lineGroupId,
                        );
                        // SO管理共通パラメータ設定
                        await this.soManagementCommon(
                            param,
                            responseHeader.apiProcessID,
                            executeUserId,
                            executeTenantId,
                            ResultCdConstants.CODE_130003,
                            receivedDate,
                            lineGroupId,
                        );
                        // 返却値を設定し、返却
                        responseHeader.processCode =
                            ResultCdConstants.CODE_130003;

                        super.debug(
                            param.tenantId,
                            sequenceNo,
                            MsgKeysConstants.APGRGW0001,
                            this.getClassName(),
                            "service",
                        );
                        return this.returnEdit(output);
                    }
                }

                // 廃棄の場合
            } else if (registFlag === "0") {
                // 回線グループプランIDのフォーマットチェックを行う
                if (!CheckUtil.checkIsNotNull(potalGroupPlanID)) {
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APGRGW0001,
                        LineGroupRegistAcquisitionServiceTx.CONST_LINEGROUPPLANID,
                        potalGroupPlanID,
                    );

                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(
                        param,
                        responseHeader.apiProcessID,
                        executeUserId,
                        executeTenantId,
                        ResultCdConstants.CODE_130004,
                        receivedDate,
                        lineGroupId,
                    );

                    // 返却値を設定し、返却
                    responseHeader.processCode = ResultCdConstants.CODE_130004;

                    super.debug(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APGRGD0002,
                        this.getClassName(),
                        "service",
                    );

                    return this.returnEdit(output);
                }

                // 回線グループIDのフォーマットチェックを行う
                if (!Check.checkLineGroupId(lineGroupId)) {
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APGRGW0001,
                        LineGroupRegistAcquisitionServiceTx.CONST_LINEGROUPID,
                        lineGroupId,
                    );

                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(
                        param,
                        responseHeader.apiProcessID,
                        executeUserId,
                        executeTenantId,
                        ResultCdConstants.CODE_130004,
                        receivedDate,
                        lineGroupId,
                    );

                    // 返却値を設定し、返却
                    responseHeader.processCode = ResultCdConstants.CODE_130004;

                    super.debug(
                        param.tenantId,
                        sequenceNo,
                        MsgKeysConstants.APGRGD0002,
                        this.getClassName(),
                        "service",
                    );

                    return this.returnEdit(output);
                }
                //  新規・廃棄以外の場合
            } else {
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APGRGW0001,
                    LineGroupRegistAcquisitionServiceTx.CONST_REGISTFLAG,
                    registFlag,
                );

                // SO管理共通パラメータ設定
                await this.soManagementCommon(
                    param,
                    responseHeader.apiProcessID,
                    executeUserId,
                    executeTenantId,
                    ResultCdConstants.CODE_130002,
                    receivedDate,
                    lineGroupId,
                );

                // 返却値を設定し、返却
                responseHeader.processCode = ResultCdConstants.CODE_130002;

                super.debug(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APGRGD0002,
                    this.getClassName(),
                    "service",
                );

                return this.returnEdit(output);
            }

            // 社内テナント種別チェック
            let tenantsEntity: TenantsEntity = null;

            try {
                await retryQuery(
                    this.context,
                    "LineGroupRegistService.getTenants",
                    async () => {
                        if (!tx) {
                            tx = await sequelize.transaction();
                        }
                        tenantsEntity = await this.apiCommonDao.getTenants(
                            tenantId,
                            tx,
                        );
                    },
                    this.apDbRetryMaxCnt,
                    this.apDbRetryInterval,
                );
            } catch (e) {
                super.error(
                    e,
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APCOME0402,
                    functionType,
                );

                // SO管理共通パラメータ設定
                await this.soManagementCommon(
                    param,
                    responseHeader.apiProcessID,
                    executeUserId,
                    executeTenantId,
                    ResultCdConstants.CODE_130005,
                    receivedDate,
                    getStringParameter(param.lineGroupId),
                );
                // 返却値を設定し、返却
                responseHeader.processCode = ResultCdConstants.CODE_130005;
                cancelTx = true;
                return this.returnEdit(output);
            }
            if (tenantsEntity == null) {
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APGRGW0002);
                // SO管理共通パラメータ設定
                await this.soManagementCommon(
                    param,
                    responseHeader.apiProcessID,
                    executeUserId,
                    executeTenantId,
                    ResultCdConstants.CODE_130005,
                    receivedDate,
                    getStringParameter(param.lineGroupId),
                );
                // 返却値を設定し、返却
                responseHeader.processCode = ResultCdConstants.CODE_130005;

                super.debug(
                    param.tenantId,
                    sequenceNo,
                    MsgKeysConstants.APGRGD0002,
                    this.getClassName(),
                    "service",
                );
                cancelTx = true;
                return this.returnEdit(output);
            } else if (
                "0".equals(getStringParameter(param.registFlag)) &&
                tenantsEntity !== null &&
                tenantsEntity.tenantType === 1
            ) {
                // 廃止、かつC-OCNの場合はエラー
                super.warn(
                    param.tenantId,
                    sequenceNo,
                    MsgKeysConstants.APGRGW0003,
                );

                // SO管理共通パラメータ設定
                await this.soManagementCommon(
                    param,
                    responseHeader.apiProcessID,
                    executeUserId,
                    executeTenantId,
                    ResultCdConstants.CODE_130006,
                    receivedDate,
                    getStringParameter(param.lineGroupId),
                );
                // 返却値を設定し、返却

                responseHeader.processCode = ResultCdConstants.CODE_130006;

                super.debug(
                    param.tenantId,
                    sequenceNo,
                    MsgKeysConstants.APGRGD0002,
                    this.getClassName(),
                    "service",
                );
                cancelTx = true;
                return this.returnEdit(output);
            } else if (
                getStringParameter(param.registFlag) === "1" &&
                (tenantsEntity === null || tenantsEntity.tenantType !== 1) &&
                !CheckUtil.checkIsNotNull(getStringParameter(param.lineGroupId))
            ) {
                // 新規、かつC-OCNの場合、回線グループIDが指定されていればエラー
                super.warn(
                    param.tenantId,
                    sequenceNo,
                    MsgKeysConstants.APGRGW0005,
                    getStringParameter(param.lineGroupId),
                );
                // SO管理共通パラメータ設定
                await this.soManagementCommon(
                    param,
                    responseHeader.apiProcessID,
                    executeUserId,
                    executeTenantId,
                    ResultCdConstants.CODE_130007,
                    receivedDate,
                    getStringParameter(param.lineGroupId),
                );
                // 返却値を設定し、返却
                responseHeader.processCode = ResultCdConstants.CODE_130007;

                super.debug(
                    param.tenantId,
                    sequenceNo,
                    MsgKeysConstants.APGRGD0002,
                    this.getClassName(),
                    "service",
                );
                cancelTx = true;
                return this.returnEdit(output);
            } else if (
                tenantsEntity.tenantType !== null &&
                tenantsEntity.tenantType === 1 &&
                CheckUtil.checkIsNotNull(getStringParameter(param.lineGroupId))
            ) {
                super.warn(
                    param.tenantId,
                    sequenceNo,
                    MsgKeysConstants.APGRGW0005,
                    getStringParameter(param.lineGroupId),
                );
                // SO管理共通パラメータ設定
                await this.soManagementCommon(
                    param,
                    responseHeader.apiProcessID,
                    executeUserId,
                    executeTenantId,
                    ResultCdConstants.CODE_130007,
                    receivedDate,
                    getStringParameter(param.lineGroupId),
                );
                // 返却値を設定し、返却
                responseHeader.processCode = ResultCdConstants.CODE_130007;

                super.debug(
                    param.tenantId,
                    sequenceNo,
                    MsgKeysConstants.APGRGD0002,
                    this.getClassName(),
                    "service",
                );
                cancelTx = true;
                return this.returnEdit(output);
            }

            // 回線グループ管理テーブルから「回線グループＩＤ情報」を取得する
            if (
                tenantsEntity.tenantType !== null &&
                tenantsEntity.tenantType === 1
            ) {
                let lineGroupsEntity: LineGroupsEntity = null;
                try {
                    await retryQuery(
                        this.context,
                        "LineGroupRegistService.getLineGroupsInfo",
                        async () => {
                            if (!tx) {
                                tx = await sequelize.transaction();
                            }
                            lineGroupsEntity =
                                await this.apiLinesGroupDAO.getLineGroupsInfo(
                                    getStringParameter(param.lineGroupId),
                                    tx,
                                );
                        },
                        this.apDbRetryMaxCnt,
                        this.apDbRetryInterval,
                    );
                } catch (e) {
                    super.error(
                        e,
                        param.tenantId,
                        sequenceNo,
                        MsgKeysConstants.APCOME0402,
                        functionType,
                    );
                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(
                        param,
                        responseHeader.apiProcessID,
                        executeUserId,
                        executeTenantId,
                        ResultCdConstants.CODE_130008,
                        receivedDate,
                        getStringParameter(param.lineGroupId),
                    );
                    // 返却値を設定し、返却
                    responseHeader.processCode = ResultCdConstants.CODE_130008;
                    cancelTx = true;
                    return this.returnEdit(output);
                }

                if (lineGroupsEntity != null) {
                    super.warn(
                        param.tenantId,
                        sequenceNo,
                        MsgKeysConstants.APGRGW0004,
                        getStringParameter(param.lineGroupId),
                    );
                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(
                        param,
                        responseHeader.apiProcessID,
                        executeUserId,
                        executeTenantId,
                        ResultCdConstants.CODE_130008,
                        receivedDate,
                        getStringParameter(param.lineGroupId),
                    );
                    // 返却値を設定し、返却
                    responseHeader.processCode = ResultCdConstants.CODE_130008;

                    super.debug(
                        param.tenantId,
                        sequenceNo,
                        MsgKeysConstants.APGRGD0002,
                        this.getClassName(),
                        "service",
                    );
                    cancelTx = true;
                    return this.returnEdit(output);
                }
            }

            // 入力パラメータ．「新規/廃止フラグ」=1（新規）の場合、下記の処理を実行する
            if (getStringParameter(param.registFlag) === "1") {
                // 回線グループプランＩＤを取得する
                let tenantGroupPlansEntity: TenantGroupPlansEntity = null;
                try {
                    await retryQuery(
                        this.context,
                        "LineGroupRegistService.getGroupPlanId",
                        async () => {
                            if (!tx) {
                                tx = await sequelize.transaction();
                            }
                            tenantGroupPlansEntity =
                                await this.apiLinesGroupDAO.getGroupPlanId(
                                    tenantId,
                                    parseInt(potalGroupPlanID, 10),
                                    tx,
                                );
                        },
                        this.apDbRetryMaxCnt,
                        this.apDbRetryInterval,
                    );
                } catch (e) {
                    cancelTx = true;
                    super.error(
                        e,
                        param.tenantId,
                        sequenceNo,
                        MsgKeysConstants.APCOME0402,
                        functionType,
                    );

                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(
                        param,
                        responseHeader.apiProcessID,
                        executeUserId,
                        executeTenantId,
                        ResultCdConstants.CODE_130101,
                        receivedDate,
                        getStringParameter(param.lineGroupId),
                    );
                    // 返却値を設定し、返却
                    responseHeader.processCode = ResultCdConstants.CODE_130101;

                    return this.returnEdit(output);
                }

                if (tenantGroupPlansEntity === null) {
                    super.warn(
                        param.tenantId,
                        sequenceNo,
                        MsgKeysConstants.APGRGW0101,
                        getStringParameter(param.potalGroupPlanID),
                    );
                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(
                        param,
                        responseHeader.apiProcessID,
                        executeUserId,
                        executeTenantId,
                        ResultCdConstants.CODE_130101,
                        receivedDate,
                        getStringParameter(param.lineGroupId),
                    );
                    // 返却値を設定し、返却
                    responseHeader.processCode = ResultCdConstants.CODE_130101;

                    super.debug(
                        param.tenantId,
                        sequenceNo,
                        MsgKeysConstants.APGRGD0002,
                        this.getClassName(),
                        "service",
                    );
                    cancelTx = true;
                    return this.returnEdit(output);
                }

                // サービスプランＩＤを取得する
                let groupPlansEntity: GroupPlansEntity = null;

                try {
                    await retryQuery(
                        this.context,
                        "LineGroupRegistService.getServicePlanId",
                        async () => {
                            if (!tx) {
                                tx = await sequelize.transaction();
                            }
                            groupPlansEntity =
                                await this.apiLinesGroupDAO.getServicePlanId(
                                    parseInt(potalGroupPlanID, 10),
                                    tx,
                                );
                        },
                        this.apDbRetryMaxCnt,
                        this.apDbRetryInterval,
                    );
                } catch (e) {
                    cancelTx = true;
                    // DBアクセス時にエラー発生した場合
                    // ログ出力
                    super.error(
                        e,
                        param.tenantId,
                        sequenceNo,
                        MsgKeysConstants.APCOME0402,
                        functionType,
                    );
                    // SO管理共通パラメータ設定

                    await this.soManagementCommon(
                        param,
                        responseHeader.apiProcessID,
                        executeUserId,
                        executeTenantId,
                        ResultCdConstants.CODE_130102,
                        receivedDate,
                        getStringParameter(param.lineGroupId),
                    );
                    // 返却値を設定し、返却
                    responseHeader.processCode = ResultCdConstants.CODE_130102;
                    return this.returnEdit(output);
                }

                if (
                    groupPlansEntity == null ||
                    groupPlansEntity.servicePlanId == null ||
                    "".equals(groupPlansEntity.servicePlanId.toString())
                ) {
                    super.warn(
                        param.tenantId,
                        sequenceNo,
                        MsgKeysConstants.APGRGW0102,
                        getStringParameter(param.potalGroupPlanID),
                    );

                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(
                        param,
                        responseHeader.apiProcessID,
                        executeUserId,
                        executeTenantId,
                        ResultCdConstants.CODE_130102,
                        receivedDate,
                        getStringParameter(param.lineGroupId),
                    );

                    // 返却値を設定し、返却
                    responseHeader.processCode = ResultCdConstants.CODE_130102;

                    super.debug(
                        param.tenantId,
                        sequenceNo,
                        MsgKeysConstants.APGRGD0002,
                        this.getClassName(),
                        "service",
                    );
                    cancelTx = true;
                    return this.returnEdit(output);
                }

                // 回線グループＩＤリストを取得する
                let groupIdLock: string = "";
                let lineGroupIdList: string[] = null;
                // 社内テナント種別チェック
                if (
                    tenantsEntity.tenantType === null ||
                    tenantsEntity.tenantType !== 1
                ) {
                    try {
                        const result = await retryQuery(
                            this.context,
                            "LineGroupRegistService.getLinesGroupIdList",
                            async () => {
                                try {
                                    if (!tx) {
                                        tx = await sequelize.transaction();
                                    }
                                    lineGroupIdList =
                                        await this.apiLinesGroupDAO.getLinesGroupIdList(
                                            new Date(),
                                            this.lineGroupUnusableTime,
                                            tx,
                                        );
                                } catch (e) {
                                    await tx.rollback();
                                    tx = null;
                                    if (isSQLException(e)) {
                                        throw e;
                                    } else {
                                        super.warn(
                                            param.tenantId,
                                            sequenceNo,
                                            MsgKeysConstants.APGRGW0104,
                                        );
                                        // SO管理共通パラメータ設定
                                        await this.soManagementCommon(
                                            param,
                                            responseHeader.apiProcessID,
                                            executeUserId,
                                            executeTenantId,
                                            ResultCdConstants.CODE_130104,
                                            receivedDate,
                                            lineGroupId,
                                        );
                                        // 返却値を設定し、返却
                                        responseHeader.processCode =
                                            ResultCdConstants.CODE_130104;
                                        return this.returnEdit(output);
                                    }
                                }
                            },
                            this.apDbRetryMaxCnt,
                            this.apDbRetryInterval,
                        );
                        if (result) {
                            return result;
                        }
                    } catch (e) {
                        if (isSQLException(e)) {
                            // ログ出力
                            super.error(
                                e,
                                param.tenantId,
                                sequenceNo,
                                MsgKeysConstants.APCOME0402,
                                functionType,
                            );
                            // SO管理共通パラメータ設定

                            await this.soManagementCommon(
                                param,
                                responseHeader.apiProcessID,
                                executeUserId,
                                executeTenantId,
                                ResultCdConstants.CODE_130103,
                                receivedDate,
                                lineGroupId,
                            );
                            // 返却値を設定し、返却
                            responseHeader.processCode =
                                ResultCdConstants.CODE_130103;
                            return this.returnEdit(output);
                        } else {
                            throw e;
                        }
                    }

                    if (
                        lineGroupIdList === null ||
                        lineGroupIdList.length === 0
                    ) {
                        super.warn(
                            param.tenantId,
                            sequenceNo,
                            MsgKeysConstants.APGRGD0002,
                            getStringParameter(param.potalGroupPlanID),
                        );

                        // SO管理共通パラメータ設定
                        await this.soManagementCommon(
                            param,
                            responseHeader.apiProcessID,
                            executeUserId,
                            executeTenantId,
                            ResultCdConstants.CODE_130103,
                            receivedDate,
                            lineGroupId,
                        );
                        // 返却値を設定し、返却
                        responseHeader.processCode =
                            ResultCdConstants.CODE_130103;
                        cancelTx = true;
                        return this.returnEdit(output);
                    }
                }

                const tpcResultsFlag: boolean = false;
                try {
                    const res = await retryQuery(
                        this.context,
                        "LineGroupRegistService.tpcResultsFlag[registFlag=1]",
                        async () => {
                            if (!tx) {
                                tx = await sequelize.transaction();
                            }
                            // トランザクション再発行
                            if (
                                tenantsEntity.tenantType === null ||
                                tenantsEntity.tenantType !== 1
                            ) {
                                // 回線グループＩＤ行ロック取得
                                let lineGroups: LineGroupsEntity = null;
                                groupIdLock = null;
                                // 更新対象groupId
                                for (const lineGroupId of lineGroupIdList) {
                                    try {
                                        lineGroups =
                                            await this.apiLinesGroupDAO.getLineGroupsLockForNew(
                                                lineGroupId,
                                                tx,
                                            );
                                    } catch (e) {
                                        if (isLockNotAvailableError(e)) {
                                            await tx.rollback();
                                            tx = await sequelize.transaction();
                                            continue;
                                        }
                                        if (isSQLException(e)) {
                                            handleCode =
                                                ResultCdConstants.CODE_130104;
                                            throw e;
                                        }
                                        await tx.rollback();
                                        tx = null;
                                        super.warn(
                                            param.tenantId,
                                            sequenceNo,
                                            MsgKeysConstants.APGRGW0104,
                                        );

                                        await this.soManagementCommon(
                                            param,
                                            responseHeader.apiProcessID,
                                            executeTenantId,
                                            executeUserId,
                                            ResultCdConstants.CODE_130104,
                                            receivedDate,
                                            getStringParameter(param.lineGroupId),
                                        );

                                        // 返却値を設定し、返却
                                        responseHeader.processCode =
                                            ResultCdConstants.CODE_130104;
                                        return this.returnEdit(output);
                                    }

                                    if (lineGroups === null) {
                                        await tx.rollback();
                                        tx = null;
                                        // ログ出力
                                        super.warn(
                                            param.tenantId,
                                            sequenceNo,
                                            MsgKeysConstants.APGRGW0104,
                                        );
                                        // SO管理共通パラメータ設定
                                        await this.soManagementCommon(
                                            param,
                                            responseHeader.apiProcessID,
                                            executeTenantId,
                                            executeUserId,
                                            ResultCdConstants.CODE_130104,
                                            receivedDate,
                                            getStringParameter(param.lineGroupId),
                                        );
                                        // 返却値を設定し、返却
                                        responseHeader.processCode =
                                            ResultCdConstants.CODE_130104;
                                        return this.returnEdit(output);
                                    }

                                    if (
                                        lineGroups != null &&
                                        lineGroups.status === 0
                                    ) {
                                        groupIdLock = lineGroups.groupId;
                                        break;
                                    }
                                }

                                if (StringUtils.isEmpty(groupIdLock)) {
                                    // SO管理共通パラメータ設定
                                    await this.soManagementCommon(
                                        param,
                                        responseHeader.apiProcessID,
                                        executeTenantId,
                                        executeUserId,
                                        ResultCdConstants.CODE_130104,
                                        receivedDate,
                                        getStringParameter(param.lineGroupId),
                                    );
                                    super.warn(
                                        param.tenantId,
                                        sequenceNo,
                                        MsgKeysConstants.APGRGW0104,
                                        getStringParameter(param.potalGroupPlanID),
                                    );
                                    responseHeader.processCode =
                                        ResultCdConstants.CODE_130104;
                                    return this.returnEdit(output);
                                }
                            }

                            // TPC電文作成した、SOAPのAPIを接続する
                            let groupId: string = null;

                            if (!tpcResultsFlag) {
                                // TPC連携をまだ行っていない場合
                                // TPC情報を取得する。
                                let tpcConnectionResults: [
                                    boolean,
                                    string,
                                    string,
                                ] = null;

                                try {
                                    tpcConnectionResults =
                                        await this.tenantManage.checkTpcConnection(
                                            tenantId,
                                        );
                                } catch (e) {
                                    if (isSQLException(e)) {
                                        handleCode =
                                            ResultCdConstants.CODE_130108;
                                    }
                                    throw e;
                                }

                                // 変数「TPC情報取得結果」より判断する
                                if (!tpcConnectionResults[0]) {
                                    super.warn(
                                        param.tenantId,
                                        sequenceNo,
                                        MsgKeysConstants.APGRGW0108,
                                        getStringParameter(param.potalGroupPlanID),
                                    );
                                    responseHeader.processCode =
                                        ResultCdConstants.CODE_130108;
                                    return this.returnEdit(output);
                                }

                                let soapCommonOutputDto =
                                    new SOAPCommonOutputDto();

                                // TPC電文作成
                                if (
                                    tenantsEntity.tenantType !== null &&
                                    tenantsEntity.tenantType === 1
                                ) {
                                    groupId = getStringParameter(param.lineGroupId);
                                } else {
                                    groupId = groupIdLock;
                                }

                                const paramArray1: string[][] = [
                                    ["group_number", groupId],
                                    [
                                        "policy_number",
                                        groupPlansEntity.servicePlanId,
                                    ],
                                ];
                                try {
                                    // SOAP共通を呼び出す
                                    soapCommonOutputDto =
                                        await this.getSoapResult(
                                            "serviceProfileRequestLite",
                                            "Add",
                                            "",
                                            paramArray1,
                                            param.tenantId,
                                            sequenceNo,
                                            tpcConnectionResults[1],
                                            true,
                                        );
                                } catch (e) {
                                    this.context.log(
                                        "soapCommonOutputDto error",
                                        e,
                                    );
                                    if (SOAPException.isSOAPException(e)) {
                                        super.warn(
                                            param.tenantId,
                                            sequenceNo,
                                            MsgKeysConstants.APGRGW0105,
                                            getStringParameter(param.potalGroupPlanID),
                                        );

                                        // SO管理共通パラメータ設定
                                        await this.soManagementCommon(
                                            param,
                                            responseHeader.apiProcessID,
                                            executeTenantId,
                                            executeUserId,
                                            ResultCdConstants.CODE_130106,
                                            receivedDate,
                                            getStringParameter(param.lineGroupId),
                                        );
                                        responseHeader.processCode =
                                            ResultCdConstants.CODE_130106;
                                        super.debug(
                                            param.tenantId,
                                            sequenceNo,
                                            MsgKeysConstants.APGRGD0002,
                                            this.getClassName(),
                                            "service",
                                        );
                                        return this.returnEdit(output);
                                    }
                                    throw e;
                                }

                                if (
                                    soapCommonOutputDto.isError() ||
                                    this.soapCommon
                                        .getNodeContent(
                                            soapCommonOutputDto.getDoc(),
                                            "//Result",
                                        )
                                        .equals("NG")
                                ) {
                                    if (
                                        soapCommonOutputDto.isError() ||
                                        this.soapCommon
                                            .getNodeContent(
                                                soapCommonOutputDto.getDoc(),
                                                "//ErrorInfomation",
                                            )
                                            .substring(0, 4)
                                            .equals("c407")
                                    ) {
                                        // 二重登録、解除エラーの場合
                                        // 社内テナント種別チェック
                                        if (
                                            tenantsEntity.tenantType === null ||
                                            tenantsEntity.tenantType !== 1
                                        ) {
                                            // 回線グループ管理テーブル更新
                                            try {
                                                await this.apiLinesGroupDAO.updLineGroupStatus(
                                                    groupIdLock,
                                                    2,
                                                    new Date(),
                                                    tx,
                                                );
                                                await tx.commit();
                                                tx = null;
                                            } catch (e) {
                                                if (isSQLException(e)) {
                                                    // DBアクセスリトライエラーの場合
                                                    handleCode =
                                                        ResultCdConstants.CODE_130105;
                                                    throw e;
                                                }
                                                super.error(
                                                    param.tenantId,
                                                    sequenceNo,
                                                    MsgKeysConstants.APGRGE0107,
                                                );
                                                // SO管理共通パラメータ設定
                                                await this.soManagementCommon(
                                                    param,
                                                    responseHeader.apiProcessID,
                                                    executeUserId,
                                                    executeTenantId,
                                                    ResultCdConstants.CODE_130105,
                                                    receivedDate,
                                                    lineGroupId,
                                                );
                                                responseHeader.processCode =
                                                    ResultCdConstants.CODE_130105;
                                                super.debug(
                                                    param.tenantId,
                                                    sequenceNo,
                                                    MsgKeysConstants.APGRGD0002,
                                                    this.getClassName(),
                                                    "service",
                                                );
                                                return this.returnEdit(output);
                                            }
                                        }
                                        // ログ出力
                                        super.warn(
                                            param.tenantId,
                                            sequenceNo,
                                            MsgKeysConstants.APGRGW0105,
                                        );

                                        // SO管理共通パラメータ設定
                                        await this.soManagementCommon(
                                            param,
                                            responseHeader.apiProcessID,
                                            executeUserId,
                                            executeTenantId,
                                            ResultCdConstants.CODE_130105,
                                            receivedDate,
                                            lineGroupId,
                                        );
                                        responseHeader.processCode =
                                            ResultCdConstants.CODE_130105;
                                        super.debug(
                                            param.tenantId,
                                            sequenceNo,
                                            MsgKeysConstants.APGRGD0002,
                                            this.getClassName(),
                                            "service",
                                        );
                                        return this.returnEdit(output);
                                    } else {
                                        super.warn(
                                            param.tenantId,
                                            sequenceNo,
                                            MsgKeysConstants.APGRGW0105,
                                            getStringParameter(param.potalGroupPlanID),
                                        );
                                        responseHeader.processCode =
                                            ResultCdConstants.CODE_130106;
                                        // responseHeader.processCode = ResultCdConstants.CODE_999999
                                        super.debug(
                                            param.tenantId,
                                            sequenceNo,
                                            MsgKeysConstants.APGRGD0002,
                                            this.getClassName(),
                                            "service",
                                        );
                                        return this.returnEdit(output);
                                    }
                                }

                                // セカンダリTPCへSOAP連携を行う
                                if (tpcConnectionResults[2] != null) {
                                    // セカンダリTPCが設定されている場合に連携
                                    try {
                                        // SOAP共通を呼び出す
                                        soapCommonOutputDto =
                                            await this.getSoapResult(
                                                "serviceProfileRequestLite",
                                                "Add",
                                                "",
                                                paramArray1,
                                                param.tenantId,
                                                sequenceNo,
                                                tpcConnectionResults[2],
                                                true,
                                            );
                                        if (
                                            soapCommonOutputDto.getProcessCode() ===
                                            ResultCdConstants.CODE_000951
                                        ) {
                                            super.warn(
                                                param.tenantId,
                                                sequenceNo,
                                                MsgKeysConstants.APGRGW0106,
                                                "SG取得NG",
                                            );
                                        } else if (
                                            soapCommonOutputDto.isError() ||
                                            this.soapCommon.getNodeContent(
                                                soapCommonOutputDto.getDoc(),
                                                "//Result",
                                            ) === "NG"
                                        ) {
                                            // SOAP結果が"NG"の場合
                                            const errInfo: string =
                                                this.soapCommon.getNodeContent(
                                                    soapCommonOutputDto.getDoc(),
                                                    "//ErrorInfomation",
                                                );
                                            super.warn(
                                                param.tenantId,
                                                sequenceNo,
                                                MsgKeysConstants.APGRGW0106,
                                                errInfo,
                                            );
                                        }
                                    } catch (e) {
                                        if (SOAPException.isSOAPException(e)) {
                                            super.warn(
                                                param.tenantId,
                                                sequenceNo,
                                                MsgKeysConstants.APGRGW0106,
                                                getStringParameter(param.potalGroupPlanID),
                                            );
                                        }
                                        throw e;
                                    }
                                }
                            }

                            // 回線グループ管理テーブル更新
                            // 社内テナント種別チェック
                            if (
                                tenantsEntity.tenantType === null ||
                                tenantsEntity.tenantType !== 1
                            ) {
                                try {
                                    await this.apiLinesGroupDAO.updLineGroups(
                                        groupIdLock,
                                        1,
                                        tenantId,
                                        tenantGroupPlansEntity.planId,
                                        new Date(),
                                        tx,
                                    );
                                } catch (e) {
                                    if (isSQLException(e)) {
                                        // DBアクセスリトライエラーの場合
                                        handleCode =
                                            ResultCdConstants.CODE_130107;
                                        throw e;
                                    }
                                    super.error(
                                        param.tenantId,
                                        sequenceNo,
                                        MsgKeysConstants.APGRGE0107,
                                    );

                                    // SO管理共通パラメータ設定
                                    await this.soManagementCommon(
                                        param,
                                        responseHeader.apiProcessID,
                                        executeTenantId,
                                        executeUserId,
                                        ResultCdConstants.CODE_130107,
                                        receivedDate,
                                        getStringParameter(param.lineGroupId),
                                    );

                                    // 返却値を設定し、返却
                                    responseHeader.processCode =
                                        ResultCdConstants.CODE_130107;
                                    super.debug(
                                        param.tenantId,
                                        sequenceNo,
                                        MsgKeysConstants.APGRGD0002,
                                        this.getClassName(),
                                        "service",
                                    );
                                    return this.returnEdit(output);
                                }
                            }

                            output.jsonBody.lineGroupId = groupId;

                            // トランザクション終了
                            if (tx !== null) {
                                await tx.commit();
                                tx = null;
                            }
                            return null;
                        },
                        this.apDbRetryMaxCnt,
                        this.apDbRetryInterval,
                    );
                    if (res !== null) {
                        return res;
                    }
                } catch (ex) {
                    super.error(
                        ex,
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APCOME0402,
                        functionType,
                    );
                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(
                        param,
                        responseHeader.apiProcessID,
                        executeUserId,
                        executeTenantId,
                        handleCode,
                        receivedDate,
                        lineGroupId,
                    );
                    // 返却値を設定し、返却
                    responseHeader.processCode = handleCode;
                    return this.returnEdit(output);
                } finally {
                    if (tx !== null) {
                        this.context.warn(
                            "LineGroupRegistAcquisitionServiceTx.service transaction is still open, rolling back...",
                        );
                        try {
                            await tx.rollback();
                            this.context.warn(
                                "LineGroupRegistAcquisitionServiceTx.service rollback successful",
                            );
                        } catch (e) {
                            this.context.error(
                                "LineGroupRegistAcquisitionServiceTx.service error while rolling back transaction",
                                e,
                            );
                        }
                    }
                }
            }

            // 入力パラメータ．「新規/廃止フラグ」=0（廃止）の場合、下記の処理を実行する
            if (getStringParameter(param.registFlag) === "0") {
                // 回線グループ管理テーブルから「回線グループＩＤ」を取得する
                let lineGroupsEntity: LineGroupsEntity = null;

                try {
                    await retryQuery(
                        this.context,
                        "LineGroupRegistService.getLineGroupsInfo",
                        async () => {
                            if (!tx) {
                                tx = await sequelize.transaction();
                            }
                            lineGroupsEntity =
                                await this.apiLinesGroupDAO.getLineGroupsInfo(
                                    lineGroupId,
                                    tx,
                                );
                        },
                        this.apDbRetryMaxCnt,
                        this.apDbRetryInterval,
                    );
                } catch (e) {
                    cancelTx = true;
                    // DBアクセス時にエラー発生した場合
                    // 指定回数すべて失敗した場合はエラーとする
                    super.error(
                        e,
                        param.tenantId,
                        sequenceNo,
                        MsgKeysConstants.APCOME0402,
                        functionType,
                    );
                    // SO管理共通パラメータ設定

                    await this.soManagementCommon(
                        param,
                        responseHeader.apiProcessID,
                        executeUserId,
                        executeTenantId,
                        ResultCdConstants.CODE_130201,
                        receivedDate,
                        getStringParameter(param.lineGroupId),
                    );
                    // 返却値を設定し、返却
                    responseHeader.processCode = ResultCdConstants.CODE_130201;
                    return this.returnEdit(output);
                }

                if (lineGroupsEntity == null) {
                    super.warn(
                        param.tenantId,
                        sequenceNo,
                        MsgKeysConstants.APGRGW0201,
                    );
                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(
                        param,
                        responseHeader.apiProcessID,
                        executeUserId,
                        executeTenantId,
                        ResultCdConstants.CODE_130201,
                        receivedDate,
                        getStringParameter(param.lineGroupId),
                    );
                    // 返却値を設定し、返却
                    responseHeader.processCode = ResultCdConstants.CODE_130201;
                    cancelTx = true;
                    return this.returnEdit(output);
                }

                const tpcResultsFlag: boolean = false;
                try {
                    const retryResult = await retryQuery(
                        this.context,
                        "LineGroupRegistService.tpcResultsFlag[registFlag=0]",
                        async () => {
                            if (!tx) {
                                tx = await sequelize.transaction();
                            }
                            // 回線グループＩＤの行ロックを取得する
                            let lineGroupsEntity1: LineGroupsEntity = null;

                            try {
                                lineGroupsEntity1 =
                                    await this.apiLinesGroupDAO.getLineGroupsLock(
                                        lineGroupId,
                                        tx,
                                    );
                            } catch (e) {
                                if (isLockNotAvailableError(e)) {
                                    super.warn(
                                        param.tenantId,
                                        sequenceNo,
                                        MsgKeysConstants.APGRGW0202,
                                    );

                                    // SO管理共通パラメータ設定
                                    await this.soManagementCommon(
                                        param,
                                        responseHeader.apiProcessID,
                                        executeUserId,
                                        executeTenantId,
                                        ResultCdConstants.CODE_130202,
                                        receivedDate,
                                        getStringParameter(param.lineGroupId),
                                    );
                                    // 返却値を設定し、返却
                                    responseHeader.processCode =
                                        ResultCdConstants.CODE_130202;
                                    return this.returnEdit(output);
                                }
                                if (isSQLException(e)) {
                                    handleCode = ResultCdConstants.CODE_130202;
                                    throw e;
                                }
                                throw e;
                            }

                            if (lineGroupsEntity1 == null) {
                                super.warn(
                                    param.tenantId,
                                    sequenceNo,
                                    MsgKeysConstants.APGRGW0202,
                                );
                                // SO管理共通パラメータ設定
                                await this.soManagementCommon(
                                    param,
                                    responseHeader.apiProcessID,
                                    executeUserId,
                                    executeTenantId,
                                    ResultCdConstants.CODE_130202,
                                    receivedDate,
                                    getStringParameter(param.lineGroupId),
                                );
                                // 返却値を設定し、返却
                                responseHeader.processCode =
                                    ResultCdConstants.CODE_130202;
                                super.debug(
                                    param.tenantId,
                                    sequenceNo,
                                    MsgKeysConstants.APGRGD0002,
                                    this.getClassName(),
                                    "service",
                                );

                                return this.returnEdit(output);
                            }

                            // テナントＩＤ確認
                            if (param.tenantId !== lineGroupsEntity1.tenantId) {
                                super.warn(
                                    param.tenantId,
                                    sequenceNo,
                                    MsgKeysConstants.APGRGW0203,
                                );

                                // SO管理共通パラメータ設定
                                await this.soManagementCommon(
                                    param,
                                    responseHeader.apiProcessID,
                                    executeUserId,
                                    executeTenantId,
                                    ResultCdConstants.CODE_130203,
                                    receivedDate,
                                    getStringParameter(param.lineGroupId),
                                );
                                // 返却値を設定し、返却
                                responseHeader.processCode =
                                    ResultCdConstants.CODE_130203;
                                super.debug(
                                    param.tenantId,
                                    sequenceNo,
                                    MsgKeysConstants.APGRGD0002,
                                    this.getClassName(),
                                    "service",
                                );
                                return this.returnEdit(output);
                            }

                            // ステータス確認
                            if (lineGroupsEntity1.status !== 1) {
                                super.warn(
                                    param.tenantId,
                                    sequenceNo,
                                    MsgKeysConstants.APGRGW0204,
                                );
                                // SO管理共通パラメータ設定
                                await this.soManagementCommon(
                                    param,
                                    responseHeader.apiProcessID,
                                    executeUserId,
                                    executeTenantId,
                                    ResultCdConstants.CODE_130204,
                                    receivedDate,
                                    getStringParameter(param.lineGroupId),
                                );
                                // 返却値を設定し、返却
                                responseHeader.processCode =
                                    ResultCdConstants.CODE_130204;
                                super.debug(
                                    param.tenantId,
                                    sequenceNo,
                                    MsgKeysConstants.APGRGD0002,
                                    this.getClassName(),
                                    "service",
                                );
                                return this.returnEdit(output);
                            }

                            // 回線回線グループテーブルから「回線ＩＤ数」を取得する
                            let lineIdCnt: number = null;

                            try {
                                lineIdCnt =
                                    await this.apiLinesGroupDAO.getLineIdCount(
                                        lineGroupId,
                                        tx,
                                    );
                            } catch (e) {
                                // DBアクセス時にエラー発生した場合
                                if (isSQLException(e)) {
                                    // DBアクセスリトライエラーの場合
                                    handleCode = ResultCdConstants.CODE_130205;
                                }
                                throw e;
                            }

                            if (lineIdCnt !== 0) {
                                super.warn(
                                    param.tenantId,
                                    sequenceNo,
                                    MsgKeysConstants.APGRGW0205,
                                );
                                // SO管理共通パラメータ設定
                                await this.soManagementCommon(
                                    param,
                                    responseHeader.apiProcessID,
                                    executeUserId,
                                    executeTenantId,
                                    ResultCdConstants.CODE_130205,
                                    receivedDate,
                                    getStringParameter(param.lineGroupId),
                                );
                                // 返却値を設定し、返却
                                responseHeader.processCode =
                                    ResultCdConstants.CODE_130205;
                                super.debug(
                                    param.tenantId,
                                    sequenceNo,
                                    MsgKeysConstants.APGRGD0002,
                                    this.getClassName(),
                                    "service",
                                );
                                return this.returnEdit(output);
                            }

                            // ＴＰＣ電文作成した、SOAPのAPIを接続する
                            if (!tpcResultsFlag) {
                                // TPC情報を取得する。
                                let tpcConnectionResults: [
                                    boolean,
                                    string,
                                    string,
                                ] = null;
                                try {
                                    tpcConnectionResults =
                                        await this.tenantManage.checkTpcConnection(
                                            tenantId,
                                        );
                                } catch (ex) {
                                    if (isSQLException(ex)) {
                                        // DBアクセス時にエラー発生した場合
                                        handleCode =
                                            ResultCdConstants.CODE_130208;
                                    }
                                    throw ex;
                                }

                                // 変数「TPC情報取得結果」より判断する
                                if (!tpcConnectionResults[0]) {
                                    super.warn(
                                        param.tenantId,
                                        sequenceNo,
                                        MsgKeysConstants.APGRGW0108,
                                        getStringParameter(param.potalGroupPlanID),
                                    );
                                    responseHeader.processCode =
                                        ResultCdConstants.CODE_130208;
                                    return this.returnEdit(output);
                                }

                                let soapCommonOutputDto =
                                    new SOAPCommonOutputDto();
                                const paramArray1: string[][] = [
                                    ["group_number", getStringParameter(param.lineGroupId)],
                                ];

                                try {
                                    // SOAP共通を呼び出す
                                    soapCommonOutputDto =
                                        await this.getSoapResult(
                                            "serviceProfileRequestLite",
                                            "Del",
                                            "",
                                            paramArray1,
                                            param.tenantId,
                                            sequenceNo,
                                            tpcConnectionResults[1],
                                            true,
                                        );
                                } catch (e) {
                                    this.context.log(
                                        "soapCommonOutputDto error",
                                        e,
                                    );
                                    if (SOAPException.isSOAPException(e)) {
                                        super.warn(
                                            param.tenantId,
                                            sequenceNo,
                                            MsgKeysConstants.APGRGW0206,
                                            getStringParameter(param.potalGroupPlanID),
                                        );
                                        responseHeader.processCode =
                                            ResultCdConstants.CODE_130206;
                                        super.debug(
                                            param.tenantId,
                                            sequenceNo,
                                            MsgKeysConstants.APGRGW0206,
                                            this.getClassName(),
                                            "service",
                                        );
                                        return this.returnEdit(output);
                                    }
                                    throw e;
                                }

                                if (
                                    soapCommonOutputDto.isError() ||
                                    this.soapCommon
                                        .getNodeContent(
                                            soapCommonOutputDto.getDoc(),
                                            "//Result",
                                        )
                                        .equals("NG")
                                ) {
                                    super.warn(
                                        param.tenantId,
                                        sequenceNo,
                                        MsgKeysConstants.APGRGW0206,
                                        getStringParameter(param.potalGroupPlanID),
                                    );
                                    responseHeader.processCode =
                                        ResultCdConstants.CODE_130206;
                                    super.debug(
                                        param.tenantId,
                                        sequenceNo,
                                        MsgKeysConstants.APGRGD0002,
                                        this.getClassName(),
                                        "service",
                                    );
                                    return this.returnEdit(output);
                                }

                                // セカンダリTPCへSOAP連携を行う
                                if (tpcConnectionResults[2] != null) {
                                    // セカンダリTPCが設定されている場合に連携
                                    try {
                                        // SOAP共通を呼び出す
                                        soapCommonOutputDto =
                                            await this.getSoapResult(
                                                "serviceProfileRequestLite",
                                                "Del",
                                                "",
                                                paramArray1,
                                                param.tenantId,
                                                sequenceNo,
                                                tpcConnectionResults[2],
                                                false,
                                            );
                                        if (
                                            soapCommonOutputDto.getProcessCode() ===
                                            ResultCdConstants.CODE_000951
                                        ) {
                                            super.warn(
                                                param.tenantId,
                                                sequenceNo,
                                                MsgKeysConstants.APGRGW0207,
                                                "SG取得NG",
                                            );
                                        } else if (
                                            soapCommonOutputDto.isError() ||
                                            this.soapCommon.getNodeContent(
                                                soapCommonOutputDto.getDoc(),
                                                "//Result",
                                            ) === "NG"
                                        ) {
                                            // SOAP結果が"NG"の場合
                                            const errInfo: string =
                                                this.soapCommon.getNodeContent(
                                                    soapCommonOutputDto.getDoc(),
                                                    "//ErrorInfomation",
                                                );
                                            super.warn(
                                                param.tenantId,
                                                sequenceNo,
                                                MsgKeysConstants.APGRGW0207,
                                                errInfo,
                                            );
                                        }
                                    } catch (e) {
                                        if (SOAPException.isSOAPException(e)) {
                                            super.warn(
                                                param.tenantId,
                                                sequenceNo,
                                                MsgKeysConstants.APGRGW0207,
                                                getStringParameter(param.potalGroupPlanID),
                                            );
                                        }
                                        throw e;
                                    }
                                }

                                // 回線グループ管理テーブルを更新する
                                try {
                                    await this.apiLinesGroupDAO.updLineGroupsForNull(
                                        lineGroupId,
                                        0,
                                        new Date(),
                                        tx,
                                    );
                                } catch (e) {
                                    if (isSQLException(e)) {
                                        handleCode =
                                            ResultCdConstants.CODE_130207;
                                        throw e;
                                    }
                                    super.error(
                                        e,
                                        param.tenantId,
                                        sequenceNo,
                                        MsgKeysConstants.APGRGE0207,
                                    );

                                    // SO管理共通パラメータ設定
                                    await this.soManagementCommon(
                                        param,
                                        responseHeader.apiProcessID,
                                        executeUserId,
                                        executeTenantId,
                                        ResultCdConstants.CODE_130207,
                                        receivedDate,
                                        getStringParameter(param.lineGroupId),
                                    );
                                    // 返却値を設定し、返却
                                    responseHeader.processCode =
                                        ResultCdConstants.CODE_130207;
                                    super.debug(
                                        param.tenantId,
                                        sequenceNo,
                                        MsgKeysConstants.APGRGD0002,
                                        this.getClassName(),
                                        "service",
                                    );
                                    return this.returnEdit(output);
                                }

                                output.jsonBody.lineGroupId = lineGroupId;

                                // トランザクション終了
                                await tx.commit();
                                tx = null;
                            }
                            return null;
                        },
                        this.apDbRetryMaxCnt,
                        this.apDbRetryInterval,
                    );
                    if (retryResult !== null) {
                        return retryResult;
                    }
                } catch (e) {
                    // ログ出力
                    super.error(
                        e,
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APCOME0402,
                        functionType,
                    );

                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(
                        param,
                        responseHeader.apiProcessID,
                        executeUserId,
                        executeTenantId,
                        handleCode,
                        receivedDate,
                        lineGroupId,
                    );

                    // 返却値を設定し、返却
                    responseHeader.processCode = handleCode;
                    return this.returnEdit(output);
                } finally {
                    if (tx !== null) {
                        this.context.warn(
                            "LineGroupRegistAcquisitionServiceTx.service transaction is still open, rolling back...",
                        );
                        try {
                            await tx.rollback();
                            this.context.warn(
                                "LineGroupRegistAcquisitionServiceTx.service rollback successful",
                            );
                        } catch (e) {
                            this.context.error(
                                "LineGroupRegistAcquisitionServiceTx.service error while rolling back transaction",
                                e,
                            );
                        }
                    }
                }
            }

            // SO管理共通パラメータ設定
            await this.soManagementCommon(
                param,
                responseHeader.apiProcessID,
                executeUserId,
                executeTenantId,
                responseHeader.processCode,
                receivedDate,
                getStringParameter(param.lineGroupId),
            );

            return this.returnEdit(output);
        } finally {
            if (cancelTx && tx) {
                await tx.rollback();
                tx = null;
            }
        }
    }

    /**
     * SO管理共通パラメータ設定
     *
     * @param param
     * @param apiProcessID
     * @param executeUserId
     * @param executeTenantId
     * @param receivedDate REST API共通処理が呼び出されたシステム日付
     * @param lineGroupId
     * @throws Exception
     */

    private async soManagementCommon(
        param: LineGroupRegistAcquisitionInputDto,
        apiProcessID: string,
        executeUserId: string,
        executeTenantId: string,
        code: string,
        receivedDate: string,
        lineGroupId: string,
    ): Promise<void> {
        const soObject = new SOObject();

        // サービスオーダID
        soObject.setServiceOrderId(apiProcessID);
        // 申込日
        soObject.setOrderDate(receivedDate);
        // 完了日
        soObject.setExecDate(MvnoUtil.getDateTimeNow());
        // オーダステータス
        soObject.setOrderStatus(code);
        // 回線ID
        soObject.setLineId(null);
        // 回線グループID
        soObject.setLineGroupId(lineGroupId);
        // 所属テナントID
        soObject.setTenantId(param.tenantId);
        // 実行ユーザID
        soObject.setExecuteUserId(executeUserId);
        // 実行テナントID
        soObject.setExecuteTenantId(executeTenantId);
        // 機能種別
        soObject.setFunctionType(param.requestHeader.functionType);
        // 操作区分
        soObject.setOperationDivision(getStringParameter(param.registFlag));
        // 変更前プランID
        soObject.setChangeOldplanId("");
        // 変更後プランID
        soObject.setChangeNewplanId("");
        // 回線所属変更回線ID
        soObject.setChangePlanId("");
        // 基本容量
        soObject.setBasicCapacity("");
        // オプションプランID
        soObject.setOptionPlanId("");

        soObject.setLineGroupCreateFlag(true);

        await this.soCommon.soCommon(soObject);
    }

    /**
     *  回線グループ新規作成/廃止機能返却値編集
     *
     * @param outputDto 回線グループ新規作成/廃止機能OutputDto
     * @param isPrivate 変数 「内部呼出フラグ」
     * @param errorMessage 変数 「エラーメッセージ」
     * @return  回線グループ新規作成/廃止機能OutputDto
     */
    private returnEdit(
        outputDto: LineGroupRegistAcquisitionOutputDto,
    ): LineGroupRegistAcquisitionOutputDto {
        return outputDto;
    }

    /**
     * SOAP共通実行結果を取得する。
     *
     * @param operationName
     * @param operationTypeName
     * @param profileName
     * @param parameterNameArray
     * @param tenantId
     * @param sequenceNo
     * @param tpcGettingResults
     * @param primaryFlg
     * @return SOAPCommonOutputDto
     * @throws Exception
     */
    private async getSoapResult(
        operationName: string,
        operationTypeName: string,
        profileName: string,
        parameterNameArray: string[][],
        tenantId: string,
        sequenceNo: string,
        tpcDestInfo: string,
        primaryFlg: boolean,
    ): Promise<SOAPCommonOutputDto> {
        let soapCommonOutputDto = new SOAPCommonOutputDto();
        const paraList: ParameterName[] = [];
        for (let i = 0; i < parameterNameArray.length; i++) {
            const parameterName = new ParameterName();
            parameterName.setName(parameterNameArray[i][0]);
            parameterName.setValue(parameterNameArray[i][1]);
            paraList.push(parameterName);
        }

        soapCommonOutputDto = await this.soapCommon.callWebSoapApi(
            operationName,
            operationTypeName,
            profileName,
            paraList,
            tenantId,
            sequenceNo,
            tpcDestInfo,
            primaryFlg,
        );

        return soapCommonOutputDto;
    }
}
