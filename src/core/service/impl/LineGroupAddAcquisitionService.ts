import RESTCommon from "@/core/common/RESTCommon";
import { IFService } from "@/core/service/IFService";
import LineGroupAddAcquisitionInputDto from "@/core/dto/LineGroupAddAcquisitionInputDto";
import LineGroupAddAcquisitionOutputDto from "@/core/dto/LineGroupAddAcquisitionOutputDto";
import TenantManage from "@/core/common/TenantManage";
import APILinesGroupDAO from "@/core/dao/ApiLinesGroupDAO";
import APICommonDAO from "@/core/dao/APICommonDAO";
import APILinesDAO from "@/core/dao/APILinesDAO";
import ApiCommon from "@/core/common/ApiCommon";
import SOAPCommon from "@/core/common/SOAPCommon";
import SOCommon from "@/core/common/SOCommon";
import config from "config";
import MsgKeysConstants from "@/core/constant/MsgKeysConstants";
import ResultCdConstants from "@/core/constant/ResultCdConstants";
import MvnoUtil from "@/core/common/MvnoUtil";
import BigNumber from "bignumber.js";
import SOAPCommonOutputDto from "@/core/dto/SOAPCommonOutputDto";
import CheckResultBean from "@/core/dto/CheckResultBean";
import { GroupOptionPlanParametersEntity } from "@/core/entity/GroupOptionPlanParametersEntity";
import Check from "@/core/common/Check";
import Constants from "@/core/constant/Constants";
import ResponseHeader from "@/core/dto/ResponseHeader";
import { TenantsEntity } from "@/core/entity/TenantsEntity";
import { isSQLException } from "@/helpers/queryHelper";
import CheckUtil from "@/core/common/CheckUtil";
import SOObject from "@/core/dto/SOObject";
import LineGroupsEntity from "@/core/entity/LineGroupsEntity";
import SOAPException from "@/types/soapException";
import LineLineGroupsEntity from "@/core/entity/LineLineGroupsEntity";
import TenantGroupPlansEntity from "@/core/entity/TenantGroupPlansEntity";
import { GroupPlansEntity } from "@/core/entity/GroupPlansEntity";
import GroupOptionPlansEntity from "@/core/entity/GroupOptionPlansEntity";
import StringUtils from "@/core/common/StringUtils";
import ParameterName from "@/core/dto/ParameterName";
import OptionPlansEntity from "@/core/entity/OptionPlansEntity";
import ServiceOrdersEntity from "@/core/entity/ServiceOrdersEntity";
import { format, parse } from "date-fns";
import { getConfig } from "@/helpers/configHelper";
import { getStringParameter } from "@/types/parameter.string";

export default class LineGroupAddAcquisitionService
    extends RESTCommon
    implements
        IFService<LineGroupAddAcquisitionInputDto, LineGroupAddAcquisitionOutputDto>
{

    /** 定数　「回線グループID」 */
    private static readonly LINE_GROUPID: string = "回線グループID";
    /** 定数　「グループプランID」 */
    private static readonly POTAL_GROUP_PLANID: string = "グループプランID";
    /** 定数　「グループオプションプランID」 */
    private static readonly OPTION_GROUP_PLANID: string = "グループオプションプランID";
    /** 定数　「更新サービスパターン」 */
    private static readonly SERVICE_PATTERN: string = "更新サービスパターン";
    /** 定数　「ポリシーID」 */
    private static readonly POLICY_ID: string = "ポリシーID";
    /** 定数　「追加容量」 */
    private static readonly ADD_ACQUISITION: string = "追加容量";
    /** 定数　「有効期限（月数）」 */
    private static readonly EXPIRATIONT_MOTHS: string = "有効期限（月数）";
    /** STEP4.0版対応　追加　START */
    /** 定数　課金対象回線番号 */
    private static readonly ACCOUNTING_LINE_NO: string = "課金対象回線番号";
    /** STEP4.0版対応　追加　END */
    /** 定数　「1」 */
    private static readonly CONST_1: number = 1;
    /** 定数　「2」 */
    private static readonly CONST_2: number = 2;
    /** 定数　「4」 */
    private static readonly CONST_4: number = 4;
    /** 定数　「0」 */
    private static readonly CONST_0: number = 0;
    /** 定数　「99」 */
    private static readonly CONST_99: number = 99;
    /** 「Result」結果がNG */
    private static readonly RESULT_NG: string = "NG";

    /** STEP4.0版対応　追加　START */
    /** 定数 マップkey CSV出力要否 */
    private static readonly KEY_CSV_OUT_KBN: string = "csvOutKbn";
    /** 定数 マップkey 予約オーダ投入日時 */
    private static readonly KEY_RESERVE_ORDER_DATE: string = "reserveOrderDate";
    /** 定数 マップkey グループオプションプラン情報 */
    private static readonly KEY_GROUP_OPTION_PLANS: string = "groupOptionPlans";
    /** 定数　「1」 */
    private static readonly TYPE_1: number = 1;
    /** 定数　「2」 */
    private static readonly TYPE_2: number = 2;
    /** 定数　「3」 */
    private static readonly TYPE_3: number = 3;

    private static readonly TYPE_5: number = 5;
    /** STEP4.0版対応　追加　END */


    /**
     * API共通DAODAO
     */
    private apiCommonDao: APICommonDAO = new APICommonDAO(
        this.request,
        this.context
    );


    /** STEP3.1版対応　追加　START */
    /**
     * API回線グループDAO
     */
    private apiLinesGroupDao: APILinesGroupDAO = new APILinesGroupDAO(
        this.request,
        this.context
    );

    /**
     * テナント管理機能
     */
    private tenantManage: TenantManage = new TenantManage(
        this.request,
        this.context
    );

    /** STEP3.1版対応　追加　END */

    /** STEP4.0版対応　追加　START */
    /**
     * API回線DAO
     */
    private apiLinesDao: APILinesDAO = new APILinesDAO(
        this.request,
        this.context
    );

    /**
     * CSV連携共通機能
     */
    // private restCsvCommon: RestCsvCommon;

    /**
     * API共通処理
     */
    private apiCommon: ApiCommon = new ApiCommon(
        this.request,
        this.context
    );
    /** STEP4.0版対応　追加　END */

    /**
     * SOAP API連携機能初期化
     */

    private sOAPCommon: SOAPCommon = new SOAPCommon(
        this.request,
        this.context
    );

    /** STEP1.2a版対応　追加　START */
    /**
     * SO管理共通DAO
     */
    private soCommon: SOCommon = new SOCommon(
        this.request,
        this.context
    );
    /** STEP1.2a版対応　追加　END */

    // @Value("#{mvno[APServerConnectSOAPAPIURL]}")
    // private soapApiUrl: string = config.get<string>("mvno.APServerConnectSOAPAPIURL");

    /**
     * 追加容量上限値
     */
    // @Value("#{mvno[AddBucketUpperLimit]}")
    private addBucketUpperLimit: string = config.get<string>("mvno.AddBucketUpperLimit");

    /**
     * 追加容量下限値
     */
    // @Value("#{mvno[AddBucketLowerLimit]}")
    private addBucketLowerLimit: string = config.get<string>("mvno.AddBucketLowerLimit");

    /** STEP1.2b版対応　追加　START */
    /**
     * 予約実行日時単位(予約機能)
     */
    // @Value("#{mvno[ReservationDateExecutionUnits]}")
    private reservationDateExecutionUnits: string = getConfig("ReservationDateExecutionUnits");

    /**
     * 予約可能制限日数(予約機能)
     */
    // @Value("#{mvno[ReservationsLimitDays]}")
    private reservationsLimitDays: string = getConfig("ReservationsLimitDays");
    /** STEP1.2b版対応　追加　END */

    /**
     * 回線グループ追加チャージ(クーポン)容量追加・期間追加機能。<BR>
     *
     * <PRE>
     * REST APIにより、受信したパラメータの回線グループ追加チャージ(クーポン)容量追加・期間追加機能を取得する。
     * 回線グループ追加チャージ(クーポン)容量追加・期間追加機能を提供する。
     * </PRE>
     *
     * @param param
     *            回線グループ追加チャージ(クーポン)容量追加・期間追加機能取得インプット
     * @param isPrivate
     *            内部呼出フラグ
     * @param params
     *            可変長さ引数(内部から呼出す時に使用するパラメータ)
     * @return LineGroupAddAcquisitionOutputDto
     *         回線グループ追加チャージ(クーポン)容量追加・期間追加機能アウトプット
     * @throws Exception
     */
    async service(
        param: LineGroupAddAcquisitionInputDto,
        clientIPAddress: string,
        ...params: any
    ) : Promise<LineGroupAddAcquisitionOutputDto> {
        const isPrivate = false;
        super.debug(param.tenantId, param.requestHeader.sequenceNo, MsgKeysConstants.APGCFD0001,
            this.getClassName(), "service", JSON.stringify(param));

        // REST API共通処理が呼び出されたシステム日付
        const receivedDate: string = MvnoUtil.getDateTimeNow();
        // 変数「処理コード」初期化
        let cheatCode: string = ResultCdConstants.CODE_000000;
        // 送信番号取得
        const sequenceNo: string = param.requestHeader.sequenceNo;
        // 送信元システムID取得
        const senderSystemId: string = param.requestHeader.senderSystemId;
        // API認証キー取得
        const apiKey: string = param.requestHeader.apiKey;
        // 機能種別取得
        const functionType: string = param.requestHeader.functionType;
        // テナントID
        const tenantId: string = param.tenantId;
        // 回線グループID
        const lineGroupId: string = getStringParameter(param.lineGroupId);
        // 卸ポータルグループプランID
        const potalGroupPlanID: string = getStringParameter(param.potalGroupPlanID);
        // 卸ポータルオプショングループプランID
        const optionGroupPlanId: string = getStringParameter(param.optionGroupPlanId);
        /** STEP4.0版対応　追加　START */
        // 課金対象回線番号
        const accountingLineNo: string = param.AccountingLineNo;
        // 回線テナント所属判定結果
        let checkResult: [boolean, string];
        // ファイル名通番 */
        // let fileNameNo: string = null;
        // CSVファイル項目数 */
        // const csvColNum = 11;
        // CSVファイル出力データ */
        // let csvData: string[] = new Array(csvColNum).fill("");
        // CSVファイル出力要否区分
        // let csvOutKbn: string = "0";
        /** STEP4.0版対応　追加　END */

        const apiProcessID: string = this.context.responseHeader.apiProcessID;
        // 変数「更新サービスパターン」
        let strServicePattern: number = 0;
        // 変数「ポリシーID」
        let strPolicyId: number = 0;
        // 変数「有効期限（日数）」
        let expirationtDay: string = "";
        // 変数「有効期限（月数）」
        let expirationtMoth: string = "";
        // 変数「有効期限（月数）」
        let expirationtMoths: number;
        // 変数「追加容量」
        let addAcquisition: string = "";
        // 変数「追加容量」
        let addAcquisitions: BigNumber = new BigNumber(0);
        // 加容量のフォーマットチェック
        const addBucketLowerLimitLong = new BigNumber(this.addBucketLowerLimit);
        const addBucketUpperLimitLong = new BigNumber(this.addBucketUpperLimit);
        // 回線運用データ情報結果初期化
        let document: Document = null;
        // SOAP API連携機能初期化
        let sOAPCommonOutputDto: SOAPCommonOutputDto = null;
        // 「追加容量情報結果」設定
        let groupOptionPlanParametersEntity: GroupOptionPlanParametersEntity = null;

        let lineGroupAddAcquisitionOutputDto: LineGroupAddAcquisitionOutputDto;

        let nNo: string;

        /** STEP1.2a版対応　追加　START */
        const resultBean: CheckResultBean = {
            checkResult: true, // already passed 共通チェック in base handler
            processingCode: this.context.responseHeader.processCode,
            others: this.context.responseHeader.apiProcessID,
            errorMassage: "",
        };
        // always null for isPrivate = false
        // 変数「実行ユーザID」を定義する
        const executeUserId: string = null;
        // 変数「実行テナントID」を定義する
        const executeTenantId: string = null;
        // 変数「エラーメッセージ」
        const errorMessage: string = null;
        /** STEP1.2a版対応　追加　END */

        /** STEP1.2b版対応　追加　START */
            // 予約日
        const reserveDate: string = param.reserve_date;
        // 予約フラグ
        const reserveFlg: boolean = param.reserve_flag;
        // ＳＯ－ＩＤ
        const reserveSoId: string = param.reserve_soId;
        // 変数「オーダ種別」
        const orderType: string = Check.checkOrderType(reserveDate, reserveFlg, reserveSoId);
        /** STEP1.2b版対応　追加　END */

        try {
            // 業務チェック
            // オーダ種別チェック
            // 変数「オーダ種別」より判定する
            if (Constants.ORDER_TYPE_9.equals(orderType)) {
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APGCFW0011);
                // 変数「処理コード」設定
                cheatCode = ResultCdConstants.CODE_100105;
                // 返却値編集
                lineGroupAddAcquisitionOutputDto = this.returnEdit(param, cheatCode, apiProcessID, isPrivate, errorMessage, receivedDate);
                return lineGroupAddAcquisitionOutputDto;
            }

            /** STEP3.1対応　追加　START */
                // 回線グループとテナントIDの所属判定を実施する
                // テナント情報取得
                // STEP20.0版対応　変更　START
            let tenantsInfo: TenantsEntity = null;
            try {
                tenantsInfo = await this.apiCommonDao.getTenants(tenantId);
            } catch (e: any) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    cheatCode = ResultCdConstants.CODE_100111;
                }
                throw e;
            }
            // STEP20.0版対応　変更　END
            if (tenantsInfo == null) {
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APGCFW0111, tenantId);
                // 変数「処理コード」設定
                cheatCode = ResultCdConstants.CODE_100111;
                // SO管理共通パラメータ設定
                await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId,cheatCode, receivedDate, orderType, reserveDate, isPrivate);
                // 返却値編集
                lineGroupAddAcquisitionOutputDto = this.returnEdit(param, cheatCode, apiProcessID, isPrivate, errorMessage, receivedDate);
                return lineGroupAddAcquisitionOutputDto;
            }

            // 回線グループIDが、回線グループ管理に存在するか否か確認する。
            // STEP20.0版対応　変更　START
            let lineGroupsInfo: LineGroupsEntity = null;
            try {
                lineGroupsInfo = await this.apiLinesGroupDao.getLineGroupsInfo(lineGroupId);
            } catch (e: any) {
                // DBアクセスリトライエラーの場合
                if (isSQLException(e)) {
                    cheatCode = ResultCdConstants.CODE_100112;
                }
                throw e;
            }
            // STEP20.0版対応　変更　END
            if ( tenantsInfo.tenantType === null || tenantsInfo.tenantType !== 1) {
                // テナント種別が1以外の場合
                // 回線グループ情報が存在しなければエラー
                if (lineGroupsInfo == null) {
                    super.warn(tenantId, sequenceNo, MsgKeysConstants.APGCFW0112, lineGroupId, tenantId);
                    // 変数「処理コード」設定
                    cheatCode = ResultCdConstants.CODE_100112;
                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId,cheatCode, receivedDate, orderType, reserveDate, isPrivate);
                    // 返却値編集
                    lineGroupAddAcquisitionOutputDto = this.returnEdit(param, cheatCode, apiProcessID, isPrivate, errorMessage, receivedDate);
                    return lineGroupAddAcquisitionOutputDto;
                }
                // 回線グループ情報のテナントIDと電文のテナントIDが一致しなければエラー
                if (!tenantId.equals(lineGroupsInfo.tenantId)) {
                    super.warn(tenantId, sequenceNo, MsgKeysConstants.APGCFW0112, lineGroupId, tenantId);
                    // 変数「処理コード」設定
                    cheatCode = ResultCdConstants.CODE_100112;
                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId,cheatCode, receivedDate, orderType, reserveDate, isPrivate);
                    // 返却値編集
                    lineGroupAddAcquisitionOutputDto = this.returnEdit(param, cheatCode, apiProcessID, isPrivate, errorMessage, receivedDate);
                    return lineGroupAddAcquisitionOutputDto;
                }
                // 回線グループ情報のステータスを判定
                if (lineGroupsInfo.status !== 1) {
                    super.warn(tenantId, sequenceNo, MsgKeysConstants.APGCFW0112, lineGroupId, tenantId);
                    // 変数「処理コード」設定
                    cheatCode = ResultCdConstants.CODE_100112;
                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId,cheatCode, receivedDate, orderType, reserveDate, isPrivate);
                    // 返却値編集
                    lineGroupAddAcquisitionOutputDto = this.returnEdit(param, cheatCode, apiProcessID, isPrivate, errorMessage, receivedDate);
                    return lineGroupAddAcquisitionOutputDto;
                }
                // 回線グループ情報にプランIDが存在しない場合エラー
                if (lineGroupsInfo.planId == null) {
                    super.warn(tenantId, sequenceNo, MsgKeysConstants.APGCFW0112, lineGroupId, tenantId);
                    // 変数「処理コード」設定
                    cheatCode = ResultCdConstants.CODE_100112;
                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId,cheatCode, receivedDate, orderType, reserveDate, isPrivate);
                    // 返却値編集
                    lineGroupAddAcquisitionOutputDto = this.returnEdit(param, cheatCode, apiProcessID, isPrivate, errorMessage, receivedDate);
                    return lineGroupAddAcquisitionOutputDto;
                }
            } else {
                // テナント種別が１(C-OCN)の場合、回線グループ情報が存在すればエラー
                if (lineGroupsInfo != null) {
                    super.warn(tenantId, sequenceNo, MsgKeysConstants.APGCFW0112, lineGroupId, tenantId);
                    // 変数「処理コード」設定
                    cheatCode = ResultCdConstants.CODE_100112;
                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId,cheatCode, receivedDate, orderType, reserveDate, isPrivate);
                    // 返却値編集
                    lineGroupAddAcquisitionOutputDto = this.returnEdit(param, cheatCode, apiProcessID, isPrivate, errorMessage, receivedDate);
                    return lineGroupAddAcquisitionOutputDto;
                }
            }
            /** STEP3.1対応　追加　END */

            // 予約実行オーダの場合
            if (Constants.ORDER_TYPE_2.equals(orderType)) {
                // APサーバの複数IP取得
                const localIpList: string[] = MvnoUtil.getLocalIpList();
                // 予約実行可否チェック
                if (!this.context.isInternalRequest) {
                    // ログ出力
                    super.error(tenantId, sequenceNo, MsgKeysConstants.APCOME0401, "予約実行できませんでした。クライアントIP:" + clientIPAddress + ", APサーバーIP：" + MvnoUtil.getOutputList(localIpList));
                    // 変数「処理コード」設定
                    cheatCode = ResultCdConstants.CODE_999999;
                    // 返却値編集
                    lineGroupAddAcquisitionOutputDto = this.returnEdit(param, cheatCode, apiProcessID, isPrivate, errorMessage, receivedDate);
                    return lineGroupAddAcquisitionOutputDto;
                }
            }
            /** STEP1.2b版対応　追加　END */

            // １　回線グループIDフォーマットチェック
            if (!Check.checkLineGroupId(lineGroupId)) {
                cheatCode = ResultCdConstants.CODE_100101;
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APGCFW0001, LineGroupAddAcquisitionService.LINE_GROUPID, lineGroupId);
                // SO管理共通パラメータ設定
                await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId,cheatCode, receivedDate, orderType, reserveDate, isPrivate);
                lineGroupAddAcquisitionOutputDto = this.returnEdit(param, cheatCode, apiProcessID, isPrivate, errorMessage, receivedDate);
                return lineGroupAddAcquisitionOutputDto;
            }
            // ２　卸ポータルグループプランIDフォーマットチェック
            if (!Check.checkPlanIDFmt(potalGroupPlanID)) {
                cheatCode = ResultCdConstants.CODE_100101;
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APGCFW0001, LineGroupAddAcquisitionService.POTAL_GROUP_PLANID, potalGroupPlanID);
                // SO管理共通パラメータ設定
                await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId,cheatCode, receivedDate, orderType, reserveDate, isPrivate);
                lineGroupAddAcquisitionOutputDto = this.returnEdit(param, cheatCode, apiProcessID, isPrivate, errorMessage, receivedDate);
                return lineGroupAddAcquisitionOutputDto;
            }
            // ３　卸ポータルオプショングループプランIDフォーマットチェック
            if (!Check.checkPlanIDFmt(optionGroupPlanId)) {
                cheatCode = ResultCdConstants.CODE_100101;
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APGCFW0001, LineGroupAddAcquisitionService.OPTION_GROUP_PLANID, optionGroupPlanId);
                // SO管理共通パラメータ設定
                await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId, cheatCode, receivedDate, orderType, reserveDate, isPrivate);
                lineGroupAddAcquisitionOutputDto = this.returnEdit(param, cheatCode, apiProcessID, isPrivate, errorMessage, receivedDate);
                return lineGroupAddAcquisitionOutputDto;
            }
            /** STEP4.0版対応　追加　START */
            // 課金対象回線番号フォーマットチェック
            if (!CheckUtil.checkIsNotNull(accountingLineNo)) {
                if (!Check.checkLineNo(accountingLineNo)) {
                    cheatCode = ResultCdConstants.CODE_100101;
                    super.warn(tenantId, sequenceNo, MsgKeysConstants.APGCFW0001, LineGroupAddAcquisitionService.ACCOUNTING_LINE_NO,
                        accountingLineNo);
                    await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId,
                        cheatCode, receivedDate, orderType, reserveDate, isPrivate);
                    lineGroupAddAcquisitionOutputDto = this.returnEdit(param, cheatCode,
                        apiProcessID, isPrivate, errorMessage, receivedDate);
                    return lineGroupAddAcquisitionOutputDto;
                }
            }
            /** STEP4.0版対応　追加　END */
            /** STEP1.2b版対応　追加　START */
            if (Constants.ORDER_TYPE_1.equals(orderType) || Constants.ORDER_TYPE_2.equals(orderType)) {
                // 予約日フォーマットチェック
                if (!Check.checkReserveDateFmt(reserveDate)) {
                    cheatCode = ResultCdConstants.CODE_100101;
                    super.warn(tenantId, sequenceNo, MsgKeysConstants.APGCFW0001, "予約日", reserveDate);
                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId,cheatCode, receivedDate, orderType, reserveDate, isPrivate);
                    lineGroupAddAcquisitionOutputDto = this.returnEdit(param, cheatCode, apiProcessID, isPrivate, errorMessage, receivedDate);
                    return lineGroupAddAcquisitionOutputDto;
                }
            }

            if (Constants.ORDER_TYPE_1.equals(orderType)) {
                // 予約前オーダ:５　予約日と投入日相関チェック
                if (!Check.checkReserveDateOrderDate(receivedDate, reserveDate)) {
                    cheatCode = ResultCdConstants.CODE_100106;
                    super.warn(tenantId, sequenceNo, MsgKeysConstants.APGCFW0012, reserveDate, receivedDate);
                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId,cheatCode, receivedDate, orderType, reserveDate, isPrivate);
                    lineGroupAddAcquisitionOutputDto = this.returnEdit(param, cheatCode, apiProcessID, isPrivate, errorMessage, receivedDate);
                    return lineGroupAddAcquisitionOutputDto;
                }

                // 予約前オーダ:６　予約実行日時単位フォーマットチェック
                if (!Check.checkReservationDateExecutionUnitsFmt(this.reservationDateExecutionUnits)) {
                    cheatCode = ResultCdConstants.CODE_100107;
                    super.warn(tenantId, sequenceNo, MsgKeysConstants.APGCFW0013, this.reservationDateExecutionUnits);
                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId,cheatCode, receivedDate, orderType, reserveDate, isPrivate);
                    lineGroupAddAcquisitionOutputDto = this.returnEdit(param, cheatCode, apiProcessID, isPrivate, errorMessage, receivedDate);
                    return lineGroupAddAcquisitionOutputDto;
                }

                // 予約前オーダ:７　予約実行日時単位と予約日相関チェックチェック
                if (!Check.checkReservationDateExecutionUnits(this.reservationDateExecutionUnits, reserveDate)) {
                    cheatCode = ResultCdConstants.CODE_100108;
                    super.warn(tenantId, sequenceNo, MsgKeysConstants.APGCFW0014, this.reservationDateExecutionUnits, reserveDate);
                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId,cheatCode, receivedDate, orderType, reserveDate, isPrivate);
                    lineGroupAddAcquisitionOutputDto = this.returnEdit(param, cheatCode, apiProcessID, isPrivate, errorMessage, receivedDate);
                    return lineGroupAddAcquisitionOutputDto;
                }

                // ８　予約可能制限日数フォーマットチェック
                if (!Check.checkReservationsLimitDaysFmt(this.reservationsLimitDays)) {
                    cheatCode = ResultCdConstants.CODE_100109;
                    super.warn(tenantId, sequenceNo, MsgKeysConstants.APGCFW0015, this.reservationsLimitDays);
                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId,cheatCode, receivedDate, orderType, reserveDate, isPrivate);
                    lineGroupAddAcquisitionOutputDto = this.returnEdit(param, cheatCode, apiProcessID, isPrivate, errorMessage, receivedDate);
                    return lineGroupAddAcquisitionOutputDto;
                }

                // ９　予約可能制限日数範囲チェック
                if (!Check.checkReservationsLimitDays(this.reservationsLimitDays, reserveDate)) {
                    cheatCode = ResultCdConstants.CODE_100110;
                    super.warn(tenantId, sequenceNo, MsgKeysConstants.APGCFW0016, this.reservationsLimitDays, reserveDate);
                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId,cheatCode, receivedDate, orderType, reserveDate, isPrivate);
                    lineGroupAddAcquisitionOutputDto = this.returnEdit(param, cheatCode, apiProcessID, isPrivate, errorMessage, receivedDate);
                    return lineGroupAddAcquisitionOutputDto;
                }
            }
            /** STEP1.2b版対応　追加　END */

            /** STEP4.0版対応　追加　START */
            // 課金対象回線番号必須チェック
            if (LineGroupAddAcquisitionService.TYPE_1 !== tenantsInfo.tenantType
                && LineGroupAddAcquisitionService.TYPE_2 !== tenantsInfo.tenantType
                && LineGroupAddAcquisitionService.TYPE_3 !== tenantsInfo.tenantType
                && LineGroupAddAcquisitionService.TYPE_5 !== tenantsInfo.tenantType) {
                if (CheckUtil.checkIsNotNull(accountingLineNo)) {
                    cheatCode = ResultCdConstants.CODE_100113;
                    super.warn(tenantId, sequenceNo, MsgKeysConstants.APGCFW0017, tenantId);
                    await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId, cheatCode,
                        receivedDate, orderType, reserveDate, isPrivate);
                    lineGroupAddAcquisitionOutputDto = this.returnEdit(param, cheatCode, apiProcessID,
                        isPrivate, errorMessage, receivedDate);
                    return lineGroupAddAcquisitionOutputDto;
                }
            }

            // 課金対象回線のテナント所属判定
            if (!CheckUtil.checkIsNotNull(accountingLineNo)) {
                // STEP20.0版対応　変更　START
                try {
                    checkResult = await this.tenantManage.doCheck(accountingLineNo, tenantId);
                } catch (e: any) {
                    if (isSQLException(e)) {
                        // DBアクセスリトライエラーの場合
                        cheatCode = ResultCdConstants.CODE_100114;
                    }
                    throw e;
                }
                // STEP20.0版対応　変更　END
                if (!checkResult[0]) {
                    cheatCode = ResultCdConstants.CODE_100114;
                    super.warn(tenantId, sequenceNo, MsgKeysConstants.APGCFW0018, tenantId, accountingLineNo);
                    await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId,
                        cheatCode, receivedDate, orderType, reserveDate, isPrivate);
                    lineGroupAddAcquisitionOutputDto = this.returnEdit(param, cheatCode,
                        apiProcessID, isPrivate, errorMessage, receivedDate);
                    return lineGroupAddAcquisitionOutputDto;
                } else {
                    nNo = checkResult[1]; // save N番 for success return later
                }
            }

            // 課金対象回線の回線グループ所属判定
            if (LineGroupAddAcquisitionService.TYPE_1 !== tenantsInfo.tenantType && !CheckUtil.checkIsNotNull(accountingLineNo)) {
                // STEP20.0版対応　変更　START
                let linesGroupInfo: LineLineGroupsEntity = null;
                try {
                    linesGroupInfo = await this.apiLinesGroupDao.getLinesGroupId(accountingLineNo);
                } catch (e: any) {
                    if (isSQLException(e)) {
                        // DBアクセスリトライエラーの場合
                        cheatCode = ResultCdConstants.CODE_100115;
                    }
                    throw e;
                }
                // STEP20.0版対応　変更　END
                if (linesGroupInfo == null || !lineGroupId.equals(linesGroupInfo.groupId)) {
                    cheatCode = ResultCdConstants.CODE_100115;
                    super.warn(tenantId, sequenceNo, MsgKeysConstants.APGCFW0019, lineGroupId, accountingLineNo);
                    await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId,
                        cheatCode, receivedDate, orderType, reserveDate, isPrivate);
                    lineGroupAddAcquisitionOutputDto = this.returnEdit(param, cheatCode, apiProcessID,
                        isPrivate, errorMessage, receivedDate);
                    return lineGroupAddAcquisitionOutputDto;
                }
            }
            /** STEP4.0版対応　追加　END */

            // ４　グループプランID所属チェック
            // STEP20.0版対応　変更　START
            let tenatGroupEntity: TenantGroupPlansEntity = null;
            try {
                tenatGroupEntity = await this.apiCommonDao.getTenantGroupPlans(potalGroupPlanID, tenantId);
            } catch (e: any) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    cheatCode = ResultCdConstants.CODE_100102;
                }
                throw e;
            }
            // STEP20.0版対応　変更　END
            if (tenatGroupEntity == null) {
                cheatCode = ResultCdConstants.CODE_100102;
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APGCFW0002, potalGroupPlanID, tenantId);
                // SO管理共通パラメータ設定
                await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId, cheatCode, receivedDate, orderType, reserveDate, isPrivate);
                lineGroupAddAcquisitionOutputDto = this.returnEdit(param, cheatCode, apiProcessID, isPrivate, errorMessage, receivedDate);
                return lineGroupAddAcquisitionOutputDto;
            }

            /** STEP4.0版対応　追加　START */
            // グループプランID利用チェック
            if (LineGroupAddAcquisitionService.TYPE_1 !== tenantsInfo.tenantType) {
                // STEP20.0版対応　変更　START
                let groupUsePlanId: string = null;
                try {
                    groupUsePlanId = await this.apiLinesGroupDao.getUsingGroupPlans(potalGroupPlanID, lineGroupId);
                } catch (e: any) {
                    if (isSQLException(e)) {
                        // DBアクセスリトライエラーの場合
                        cheatCode = ResultCdConstants.CODE_100116;
                    }
                    throw e;
                }
                // STEP20.0版対応　変更　END
                if (CheckUtil.checkIsNotNull(groupUsePlanId)) {
                    cheatCode = ResultCdConstants.CODE_100116;
                    super.warn(tenantId, sequenceNo, MsgKeysConstants.APGCFW0020, potalGroupPlanID);
                    await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId,
                        cheatCode, receivedDate, orderType, reserveDate, isPrivate);
                    lineGroupAddAcquisitionOutputDto = this.returnEdit(param, cheatCode, apiProcessID,
                        isPrivate, errorMessage, receivedDate);
                    return lineGroupAddAcquisitionOutputDto;
                }
            }
            /** STEP4.0版対応　追加　END */

            // グループオプションプランID所属チェック
            /** STEP4.0版対応　変更　START */
            // STEP20.0版対応　変更　START
            let groupPlanOptionPlansInfo: string = null;
            try {
                groupPlanOptionPlansInfo
                    = await this.apiCommonDao.getCheckedGroupPlanOptionPlans(optionGroupPlanId, potalGroupPlanID, "10");
            } catch (e: any) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    cheatCode = ResultCdConstants.CODE_100103;
                }
                throw e;
            }
            // STEP20.0版対応　変更　END
            if (CheckUtil.checkIsNotNull(groupPlanOptionPlansInfo)) {
                cheatCode = ResultCdConstants.CODE_100103;
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APGCFW0003, optionGroupPlanId);
                // SO管理共通パラメータ設定
                await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId,cheatCode, receivedDate, orderType, reserveDate, isPrivate);
                lineGroupAddAcquisitionOutputDto = this.returnEdit(param, cheatCode, apiProcessID, isPrivate, errorMessage, receivedDate);
                return lineGroupAddAcquisitionOutputDto;
            }
            // ロジック処理
            // １　更新サービスパターンとポリシーIDを取得する
            // STEP20.0版対応　変更　START
            let groupPlansEntity: GroupPlansEntity = null;
            try {
                groupPlansEntity = await this.apiCommonDao.getGroupPlans(potalGroupPlanID);
            } catch (e: any) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    cheatCode = ResultCdConstants.CODE_100201;
                }
                throw e;
            }
            // STEP20.0版対応　変更　END
            if (groupPlansEntity == null) {
                cheatCode = ResultCdConstants.CODE_100201;
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APGCFW0004);
                // SO管理共通パラメータ設定
                await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId,cheatCode, receivedDate, orderType, reserveDate, isPrivate);
                lineGroupAddAcquisitionOutputDto = this.returnEdit(param, cheatCode, apiProcessID, isPrivate, errorMessage, receivedDate);
                return lineGroupAddAcquisitionOutputDto;
            }
            // STEP12.0b版対応　追加　START
            // STEP20.0版対応　変更　START
            let groupOptionPlans: GroupOptionPlansEntity = null;
            try {
                groupOptionPlans = await this.apiLinesGroupDao.getGroupOptionPlan(optionGroupPlanId);
            } catch (e: any) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    cheatCode = ResultCdConstants.CODE_100201;
                }
                throw e;
            }
            // STEP20.0版対応　変更　END
            if (groupOptionPlans == null) {
                cheatCode = ResultCdConstants.CODE_100201;
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APGCFW0004);
                // SO管理共通パラメータ設定
                await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId,cheatCode, receivedDate, orderType, reserveDate, isPrivate);
                lineGroupAddAcquisitionOutputDto = this.returnEdit(param, cheatCode, apiProcessID, isPrivate, errorMessage, receivedDate);
                return lineGroupAddAcquisitionOutputDto;
            }
            // STEP12.0b版対応　追加　END
            // 変数「更新サービスパターン」設定値
            // STEP12.0b版対応　変更　START
            if (CheckUtil.checkIsNull(groupOptionPlans.tpcServicePattern)) {
                // STEP12.0b版対応　変更　END
                cheatCode = ResultCdConstants.CODE_100104;
                // STEP12.0b版対応　変更　START
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APGCFW0007, LineGroupAddAcquisitionService.SERVICE_PATTERN, groupOptionPlans.tpcServicePattern);
                // STEP12.0b版対応　変更　END
                // SO管理共通パラメータ設定
                await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId,cheatCode, receivedDate, orderType, reserveDate, isPrivate);
                lineGroupAddAcquisitionOutputDto = this.returnEdit(param, cheatCode, apiProcessID, isPrivate, errorMessage, receivedDate);
                return lineGroupAddAcquisitionOutputDto;
            } else {
                // STEP12.0b版対応　変更　START
                strServicePattern = groupOptionPlans.tpcServicePattern;
                // STEP12.0b版対応　変更　END
            }

            // 変数「ポリシーID」設定値
            if (CheckUtil.checkIsNull(groupPlansEntity.policyId)) {
                cheatCode = ResultCdConstants.CODE_100104;
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APGCFW0007, LineGroupAddAcquisitionService.POLICY_ID, groupPlansEntity.policyId);
                // SO管理共通パラメータ設定
                await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId,cheatCode, receivedDate, orderType, reserveDate, isPrivate);
                lineGroupAddAcquisitionOutputDto = this.returnEdit(param, cheatCode, apiProcessID, isPrivate, errorMessage, receivedDate);
                return lineGroupAddAcquisitionOutputDto;
            } else {
                strPolicyId = groupPlansEntity.policyId;
            }

            // ２　更新サービスパターンとポリシーIDのフォーマットチェック
            if (strServicePattern !== LineGroupAddAcquisitionService.CONST_1) {
                if (strServicePattern !== LineGroupAddAcquisitionService.CONST_2) {
                    if (strServicePattern !== LineGroupAddAcquisitionService.CONST_4) {
                        cheatCode = ResultCdConstants.CODE_100104;
                        super.warn(tenantId, sequenceNo, MsgKeysConstants.APGCFW0007, LineGroupAddAcquisitionService.SERVICE_PATTERN, strServicePattern);
                        await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId,cheatCode, receivedDate, orderType, reserveDate, isPrivate);
                        lineGroupAddAcquisitionOutputDto = this.returnEdit(param, cheatCode, apiProcessID, isPrivate, errorMessage, receivedDate);
                        return lineGroupAddAcquisitionOutputDto;
                    }
                }
            }

            // ２．２　ポリシーID のフォーマットチェックを行う
            if (!Check.checkPolicyId(strPolicyId.toString())) {
                cheatCode = ResultCdConstants.CODE_100104;
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APGCFW0007, LineGroupAddAcquisitionService.POLICY_ID, strPolicyId);
                await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId,cheatCode, receivedDate, orderType, reserveDate, isPrivate);
                lineGroupAddAcquisitionOutputDto = this.returnEdit(param, cheatCode, apiProcessID, isPrivate, errorMessage, receivedDate);
                return lineGroupAddAcquisitionOutputDto;
            }

            /** STEP1.2b版対応　変更　START */
            /** STEP4.0版対応　追加　START */
            // 課金対象回線の廃止オーダが受付中でないか
            if (!CheckUtil.checkIsNotNull(accountingLineNo)) {
                let resultAbolSo: boolean;
                // STEP20.0版対応　変更　START
                try {
                    if (Constants.ORDER_TYPE_0.equals(orderType)) {
                        resultAbolSo = await this.apiCommon.checkAbolishSo(accountingLineNo, receivedDate);
                    } else {
                        resultAbolSo = await this.apiCommon.checkAbolishSo(accountingLineNo, reserveDate);
                    }
                } catch (e: any) {
                    if (isSQLException(e)) {
                        // DBアクセスリトライエラーの場合
                        cheatCode = ResultCdConstants.CODE_100117;
                    }
                    throw e;
                }
                // STEP20.0版対応　変更　END
                if (!resultAbolSo) {
                    cheatCode = ResultCdConstants.CODE_100117;
                    super.warn(tenantId, sequenceNo, MsgKeysConstants.APGCFW0021, accountingLineNo);
                    await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId,
                        cheatCode, receivedDate, orderType, reserveDate, isPrivate);
                    lineGroupAddAcquisitionOutputDto = this.returnEdit(param, cheatCode, apiProcessID,
                        isPrivate, errorMessage, receivedDate);
                    return lineGroupAddAcquisitionOutputDto;
                }
            }

            // STEP15.0版対応　追加　START
            // 課金対象回線の利用状態がサスペンド中でないか
            if (!CheckUtil.checkIsNotNull(accountingLineNo)) {
                // STEP22.0版対応　変更　START
                if (2 !== tenantsInfo.tenantType
                    && 3 !== tenantsInfo.tenantType
                    && 4 !== tenantsInfo.tenantType
                    && 5 !== tenantsInfo.tenantType) {
                    // B-OCN、UNOモバイル、ICM, RINKモバイル（社内テナント種別が２、３、４, 5）以外の場合にチェックを行う

                    // STEP20.0版対応　変更　START
                    let checkLineUsageStatusResult: boolean;
                    try {
                        checkLineUsageStatusResult = await this.apiCommon.checkLineSuspend(accountingLineNo);
                    } catch (e: any) {
                        if (isSQLException(e)) {
                            // DBアクセスリトライエラーの場合
                            cheatCode = ResultCdConstants.CODE_100118;
                        }
                        throw e;
                    }
                    // STEP20.0版対応　変更　END
                    // 変数「利用状態判定結果」より判定する
                    if (!checkLineUsageStatusResult) {
                        // 変数「処理コード」設定
                        cheatCode = ResultCdConstants.CODE_100118;
                        super.warn(tenantId, sequenceNo, MsgKeysConstants.APGCFW0022, accountingLineNo);
                        // SO管理共通パラメータ設定
                        await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId,
                            cheatCode, receivedDate, orderType, reserveDate, isPrivate);
                        // 返却値編集
                        lineGroupAddAcquisitionOutputDto = this.returnEdit(param, cheatCode, apiProcessID,
                            isPrivate, errorMessage, receivedDate);
                        return lineGroupAddAcquisitionOutputDto;
                    }
                }
                // STEP22.0版対応　変更　END
            }
            // STEP15.0版対応　追加　END

            /** STEP4.0版対応　追加　END */
            // 変数「オーダ種別」=即時オーダ　あるいは　予約実行オーダ 処理３へ
            // 変数「オーダ種別」=予約前オーダ 処理４へ
            if (Constants.ORDER_TYPE_0.equals(orderType) || Constants.ORDER_TYPE_2.equals(orderType)) {
                // SO電文作成

                /** STEP3.1版対応　追加　START */
                // TPC情報を取得する
                // STEP20.0版対応　変更　START
                let tpcConnectionResults: [boolean, string, string] = null;
                try {
                    tpcConnectionResults = await this.tenantManage.checkTpcConnection(tenantId);
                } catch (e: any) {
                    if (isSQLException(e)) {
                        // DBアクセスリトライエラーの場合
                        cheatCode = ResultCdConstants.CODE_100310;
                    }
                    throw e;
                }
                // STEP20.0版対応　変更　END
                // 変数「TPC情報取得結果」より判断する
                if(!tpcConnectionResults[0]){
                    super.warn(tenantId, sequenceNo, MsgKeysConstants.APGCFW0010, tenantId);
                    // 変数「処理コード」設定
                    cheatCode = ResultCdConstants.CODE_100310;
                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId,cheatCode, receivedDate, orderType, reserveDate, isPrivate);
                    // 返却値編集
                    lineGroupAddAcquisitionOutputDto = this.returnEdit(param, cheatCode, apiProcessID, isPrivate, errorMessage, receivedDate);
                    return lineGroupAddAcquisitionOutputDto;
                }
                /** STEP3.1版対応　追加　END */

                // 「追加容量情報結果」設定
                /** １．１版対応　変更　START */
                // STEP20.0版対応　変更　START
                try {
                    if (strServicePattern === LineGroupAddAcquisitionService.CONST_1 || strServicePattern === LineGroupAddAcquisitionService.CONST_2) {
                        groupOptionPlanParametersEntity
                            = await this.apiCommonDao.getGroupOptionPlanParameters(optionGroupPlanId, "add_bucket");
                    } else if (strServicePattern === LineGroupAddAcquisitionService.CONST_4) {
                        groupOptionPlanParametersEntity
                            = await this.apiCommonDao.getGroupOptionPlanParameters(optionGroupPlanId, "addbd_bucket");
                    }
                } catch (e: any) {
                    if (isSQLException(e)) {
                        // DBアクセスリトライエラーの場合
                        cheatCode = ResultCdConstants.CODE_100301;
                    }
                    throw e;
                }
                // STEP20.0版対応　変更　END
                /** １．１版対応　変更　END */
                // 変数「追加容量」
                // ３．１．１　変数設定
                if (groupOptionPlanParametersEntity != null) {
                    addAcquisition = groupOptionPlanParametersEntity.value;
                }

                // ３．２　変数「更新サービスパターン」＝1、又は4の場合、下記処理を行う
                if (strServicePattern === LineGroupAddAcquisitionService.CONST_1 || strServicePattern === LineGroupAddAcquisitionService.CONST_4) {
                    // ３．２．１　変数「追加容量情報結果」より判定する
                    if (groupOptionPlanParametersEntity == null) {
                        cheatCode = ResultCdConstants.CODE_100301;
                        super.warn(tenantId, sequenceNo, MsgKeysConstants.APGCFW0006);
                        // SO管理共通パラメータ設定
                        await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId,cheatCode, receivedDate, orderType, reserveDate, isPrivate);
                        lineGroupAddAcquisitionOutputDto = this.returnEdit(param, cheatCode, apiProcessID, isPrivate, errorMessage, receivedDate);
                        return lineGroupAddAcquisitionOutputDto;
                    }

                    // ３．２．２　グループオプションプランパラメータテーブルに「パラメータ値」を取得した、有効期限（月数）を設定する
                    // 変数「有効期限月数情報結果」
                    // /「追加容量情報結果」設定
                    // STEP20.0版対応　変更　START
                    try {
                        groupOptionPlanParametersEntity
                            = await this.apiCommonDao.getGroupOptionPlanParameters(optionGroupPlanId, "expiration_date");
                    } catch (e: any) {
                        if (isSQLException(e)) {
                            // DBアクセスリトライエラーの場合
                            cheatCode = ResultCdConstants.CODE_100301;
                        }
                        throw e;
                    }

                    // STEP20.0版対応　変更　END
                    // データ取得できない場合
                    if (groupOptionPlanParametersEntity == null || StringUtils.isEmpty(groupOptionPlanParametersEntity.value)) {
                        cheatCode = ResultCdConstants.CODE_100301;
                        super.warn(tenantId, sequenceNo, MsgKeysConstants.APGCFW0006);
                        // SO管理共通パラメータ設定
                        await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId,cheatCode, receivedDate, orderType, reserveDate, isPrivate);
                        lineGroupAddAcquisitionOutputDto = this.returnEdit(param, cheatCode, apiProcessID, isPrivate, errorMessage, receivedDate);
                        return lineGroupAddAcquisitionOutputDto;
                    }
                    // b．　変数 「有効期限（月数）」設定
                    expirationtMoth = groupOptionPlanParametersEntity.value;
                    if(StringUtils.isNotEmpty(addAcquisition)){
                        addAcquisitions = new BigNumber(addAcquisition);
                    }
                    // ３．２．３　テーブル値フォーマットチェック
                    if (!(addAcquisitions.gte(addBucketLowerLimitLong) && addAcquisitions.lte(addBucketUpperLimitLong))) {
                        cheatCode = ResultCdConstants.CODE_100302;
                        super.warn(tenantId, sequenceNo, MsgKeysConstants.APGCFW0007, LineGroupAddAcquisitionService.ADD_ACQUISITION, addAcquisitions);
                        // SO管理共通パラメータ設定
                        await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId,cheatCode, receivedDate, orderType, reserveDate, isPrivate);
                        lineGroupAddAcquisitionOutputDto = this.returnEdit(param, cheatCode, apiProcessID, isPrivate, errorMessage, receivedDate);
                        return lineGroupAddAcquisitionOutputDto;
                    }

                    expirationtMoths = parseInt(expirationtMoth, 10);
                    // b．　有効期限（月数）のフォーマットチェックを行う
                    if (expirationtMoths < LineGroupAddAcquisitionService.CONST_0 || expirationtMoths > LineGroupAddAcquisitionService.CONST_99) {
                        cheatCode = ResultCdConstants.CODE_100302;
                        super.warn(tenantId, sequenceNo, MsgKeysConstants.APGCFW0007, LineGroupAddAcquisitionService.EXPIRATIONT_MOTHS, expirationtMoths);
                        // SO管理共通パラメータ設定
                        await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId,cheatCode, receivedDate, orderType, reserveDate, isPrivate);
                        lineGroupAddAcquisitionOutputDto = this.returnEdit(param, cheatCode, apiProcessID, isPrivate, errorMessage, receivedDate);
                        return lineGroupAddAcquisitionOutputDto;
                    }

                    // ３．２．４　有効期限日付取得
                    // 変数「有効期限（月数）」
                    expirationtDay = Check.getMonthEndDay(expirationtMoth);

                    // ３．２．５　SOAP APIにより、容量を追加する
                    // １　アクティベート状況取得
                    /** １．１版対応　変更　START */
                    // STEP22.0版対応　変更　START
                    const paraList: ParameterName[] = [];
                    // STEP22.0版対応　変更　END
                    if (strServicePattern === LineGroupAddAcquisitionService.CONST_1) {
                        const groupNumber: ParameterName = new ParameterName();
                        groupNumber.setName("group_number");
                        groupNumber.setValue(lineGroupId);
                        paraList.push(groupNumber);
                        const policyNumber: ParameterName = new ParameterName();
                        policyNumber.setName("policy_number");
                        const porishiStr: string = strPolicyId.toString();
                        policyNumber.setValue(porishiStr);
                        paraList.push(policyNumber);
                        const addBucket: ParameterName = new ParameterName();
                        addBucket.setName("add_bucket");
                        addBucket.setValue(addAcquisition);
                        paraList.push(addBucket);
                        const expirationDate: ParameterName = new ParameterName();
                        expirationDate.setName("expiration_date");
                        expirationDate.setValue(expirationtDay);
                        paraList.push(expirationDate);

                        /**  STEP3.1 変更　START */
                        sOAPCommonOutputDto = await this.sOAPCommon.callWebSoapApi(
                            "serviceProfileRequestLite", "Mod", "",
                            paraList, tenantId, sequenceNo, tpcConnectionResults[1]);
                        /**  STEP3.1 変更　END */
                    } else if (strServicePattern === LineGroupAddAcquisitionService.CONST_4) {
                        const groupNumber: ParameterName = new ParameterName();
                        groupNumber.setName("group_number");
                        groupNumber.setValue(lineGroupId);
                        paraList.push(groupNumber);
                        const policyNumber: ParameterName = new ParameterName();
                        policyNumber.setName("policy_number");
                        const porishiStr: string = strPolicyId.toString();
                        policyNumber.setValue(porishiStr);
                        paraList.push(policyNumber);
                        const addBucket: ParameterName = new ParameterName();
                        addBucket.setName("addbd_bucket");
                        addBucket.setValue(addAcquisition);
                        paraList.push(addBucket);
                        const expirationDate: ParameterName = new ParameterName();
                        expirationDate.setName("expiration_date");
                        expirationDate.setValue(expirationtDay);
                        paraList.push(expirationDate);

                        /** STEP3.1版対応　変更　START */
                        sOAPCommonOutputDto = await this.sOAPCommon.callWebSoapApi(
                            "serviceProfileRequestLite", "Mod", "",
                            paraList, tenantId, sequenceNo, tpcConnectionResults[1]);
                        /** STEP3.1版対応　変更　END */
                    }
                    /** １．１版対応　変更　END */

                    /** STEP3.1版対応　追加　START */
                    if (ResultCdConstants.CODE_000951.equals(sOAPCommonOutputDto.getProcessCode())) {
                        // SG取得がNG
                        cheatCode = ResultCdConstants.CODE_100401;
                        super.warn(tenantId, sequenceNo, MsgKeysConstants.APGCFW0008);
                        // SO管理共通パラメータ設定
                        await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId,cheatCode, receivedDate, orderType, reserveDate, isPrivate);
                        lineGroupAddAcquisitionOutputDto = this.returnEdit(param, cheatCode, apiProcessID, isPrivate, errorMessage, receivedDate);
                        return lineGroupAddAcquisitionOutputDto;
                    }
                    /** STEP3.1版対応　追加　END */

                    document = sOAPCommonOutputDto.getDoc();
                    let resultStr: string = this.sOAPCommon.getNodeContent(document, "//Result");

                    // a．　変数「容量追加結果」を判定する
                    if (sOAPCommonOutputDto.isError() || LineGroupAddAcquisitionService.RESULT_NG.equals(resultStr)) {
                        cheatCode = ResultCdConstants.CODE_100401;
                        super.warn(tenantId, sequenceNo, MsgKeysConstants.APGCFW0008);
                        // SO管理共通パラメータ設定
                        await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId,cheatCode, receivedDate, orderType, reserveDate, isPrivate);
                        lineGroupAddAcquisitionOutputDto = this.returnEdit(param, cheatCode, apiProcessID, isPrivate, errorMessage, receivedDate);
                        return lineGroupAddAcquisitionOutputDto;
                    }

                    // STEP22.0版対応　追加　START
                    // セカンダリTPCへSOAP連携を行う
                    if (tpcConnectionResults[2] != null) {
                        // セカンダリTPCが設定されている場合に連携
                        try {
                            sOAPCommonOutputDto
                                = await this.sOAPCommon.callWebSoapApi("serviceProfileRequestLite", "Mod", "", paraList,
                                tenantId, sequenceNo, tpcConnectionResults[2], false);

                            if (ResultCdConstants.CODE_000951.equals(sOAPCommonOutputDto.getProcessCode())) {
                                // SG取得がNG
                                super.warn(tenantId, sequenceNo, MsgKeysConstants.APGCFW0023, "SG取得NG");
                            } else {
                                document = sOAPCommonOutputDto.getDoc();
                                resultStr = this.sOAPCommon.getNodeContent(document, "//Result");
                                if (sOAPCommonOutputDto.isError() || LineGroupAddAcquisitionService.RESULT_NG.equals(resultStr)) {
                                    // TPCからのResultでNGの場合
                                    // ログ出力
                                    const errInfo: string = this.sOAPCommon.getNodeContent(sOAPCommonOutputDto.getDoc(),
                                        "//ErrorInfomation");
                                    super.warn(tenantId, sequenceNo, MsgKeysConstants.APGCFW0023, errInfo);
                                }
                            }
                        } catch (e) {
                            if (SOAPException.isSOAPException(e)) {
                                // ログ出力
                                super.warn(e.message, tenantId, sequenceNo, MsgKeysConstants.APGCFW0023, e.toString());
                            } else {
                                throw e;
                            }
                        }
                    }
                    // STEP22.0版対応　追加　END

                    /** STEP4.0版対応　追加　START */
                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId,cheatCode, receivedDate, orderType, reserveDate, isPrivate);
                    let orderDate: string = null;
                    if (Constants.ORDER_TYPE_2.equals(orderType)) {
                        try {
                            const soData: ServiceOrdersEntity = await this.apiCommonDao.getServiceOrder(reserveSoId);
                            orderDate = format(soData.orderDate, "yyyy/MM/dd HH:mm:ss");
                        } catch (e: any) {
                            if (isSQLException(e)) {
                                // DBアクセスリトライエラーの場合
                                // 変数「CSV作成要否」を0で処理する
                                cheatCode = ResultCdConstants.CODE_000000;
                                super.error(e, tenantId, sequenceNo, MsgKeysConstants.APCOME0402, functionType);
                                // SO管理共通パラメータ設定
                                await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId, cheatCode, receivedDate, orderType, reserveDate, isPrivate);
                                // 返却値編集
                                lineGroupAddAcquisitionOutputDto = this.returnEdit(param, cheatCode, apiProcessID, isPrivate, errorMessage, receivedDate);
                                return lineGroupAddAcquisitionOutputDto;
                            } else {
                                throw e;
                            }
                        }
                    }

                    let csvData = null;
                    if (Constants.ORDER_TYPE_0.equals(orderType) || Constants.ORDER_TYPE_2.equals(orderType)) {
                        csvData = await this.checkCsvOutKbn(param, nNo, orderType, tenantsInfo.tenantType);
                    }
                    // only make csv for 0/2
                    if (Constants.ORDER_TYPE_0.equals(orderType)) {
                        lineGroupAddAcquisitionOutputDto = this.returnEdit(param, cheatCode, apiProcessID, isPrivate, errorMessage, receivedDate, receivedDate, nNo, csvData[LineGroupAddAcquisitionService.KEY_CSV_OUT_KBN], receivedDate, groupOptionPlans);
                    } else if (Constants.ORDER_TYPE_2.equals(orderType)) {
                        lineGroupAddAcquisitionOutputDto = this.returnEdit(param, cheatCode, apiProcessID, isPrivate, errorMessage, receivedDate, orderDate, nNo, csvData[LineGroupAddAcquisitionService.KEY_CSV_OUT_KBN], reserveDate, groupOptionPlans);
                    } else {
                        // not to make csv - so not to make call to swimmy
                        lineGroupAddAcquisitionOutputDto = this.returnEdit(param, cheatCode, apiProcessID, isPrivate, errorMessage, receivedDate);
                    }
                    /** STEP4.0版対応　追加　END */

                    return lineGroupAddAcquisitionOutputDto;

                    // ３．３　変数「更新サービスパターン」＝2の場合、下記処理を行う
                } else if (strServicePattern === LineGroupAddAcquisitionService.CONST_2) {
                    if (groupOptionPlanParametersEntity == null) {
                        cheatCode = ResultCdConstants.CODE_100303;
                        super.warn(tenantId, sequenceNo, MsgKeysConstants.APGCFW0006);
                        // SO管理共通パラメータ設定
                        await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId,cheatCode, receivedDate, orderType, reserveDate, isPrivate);
                        lineGroupAddAcquisitionOutputDto = this.returnEdit(param, cheatCode, apiProcessID, isPrivate, errorMessage, receivedDate);
                        return lineGroupAddAcquisitionOutputDto;
                    }

                    // ３．２．２　追加容量フォーマットチェック
                    addAcquisitions = new BigNumber(addAcquisition);
                    if (!(addAcquisitions.gte(addBucketLowerLimitLong) && addAcquisitions.lte(addBucketUpperLimitLong))) {
                        cheatCode = ResultCdConstants.CODE_100304;
                        super.warn(tenantId, sequenceNo, MsgKeysConstants.APGCFW0007, LineGroupAddAcquisitionService.ADD_ACQUISITION, addAcquisitions);
                        await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId,cheatCode, receivedDate, orderType, reserveDate, isPrivate);
                        lineGroupAddAcquisitionOutputDto = this.returnEdit(param, cheatCode, apiProcessID, isPrivate, errorMessage, receivedDate);
                        return lineGroupAddAcquisitionOutputDto;
                    }

                    // １　アクティベート状況取得
                    const paramsList: ParameterName[] = [];
                    const groupNumber: ParameterName = new ParameterName();
                    groupNumber.setName("group_number");
                    groupNumber.setValue(lineGroupId);
                    paramsList.push(groupNumber);

                    const policyNumber: ParameterName = new ParameterName();
                    policyNumber.setName("policy_number");
                    const porishiStr: string = strPolicyId.toString();
                    policyNumber.setValue(porishiStr);
                    paramsList.push(policyNumber);
                    const addBucket: ParameterName = new ParameterName();
                    addBucket.setName("add_bucket");
                    addBucket.setValue(addAcquisition);
                    paramsList.push(addBucket);
                    const termDays: ParameterName = new ParameterName();
                    termDays.setName("term_days");
                    const strZero: string = LineGroupAddAcquisitionService.CONST_0.toString();
                    termDays.setValue(strZero);
                    paramsList.push(termDays);
                    /** STEP3.1版対応　変更　START */
                    sOAPCommonOutputDto = await this.sOAPCommon.callWebSoapApi(
                        "serviceProfileRequestLite", "Mod", "",
                        paramsList, tenantId, sequenceNo, tpcConnectionResults[1]);
                    /** STEP3.1版対応　変更　END */

                    /** STEP3.1版対応　追加　START */
                    if (ResultCdConstants.CODE_000951.equals(sOAPCommonOutputDto.getProcessCode())) {
                        // SG取得がNG
                        cheatCode = ResultCdConstants.CODE_100401;
                        super.warn(tenantId, sequenceNo, MsgKeysConstants.APGCFW0008);
                        // SO管理共通パラメータ設定
                        await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId,cheatCode, receivedDate, orderType, reserveDate, isPrivate);
                        lineGroupAddAcquisitionOutputDto = this.returnEdit(param, cheatCode, apiProcessID, isPrivate, errorMessage, receivedDate);
                        return lineGroupAddAcquisitionOutputDto;
                    }
                    /** STEP3.1版対応　追加　END */

                    document = sOAPCommonOutputDto.getDoc();
                    let resultStrs: string = this.sOAPCommon.getNodeContent(document, "//Result");

                    // a．　変数「容量追加結果」を判定する
                    if (LineGroupAddAcquisitionService.RESULT_NG.equals(resultStrs)) {
                        cheatCode = ResultCdConstants.CODE_100401;
                        super.warn(tenantId, sequenceNo, MsgKeysConstants.APGCFW0008);
                        await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId,cheatCode, receivedDate, orderType, reserveDate, isPrivate);
                        lineGroupAddAcquisitionOutputDto = this.returnEdit(param, cheatCode, apiProcessID, isPrivate, errorMessage, receivedDate);
                        return lineGroupAddAcquisitionOutputDto;
                    }

                    // STEP22.0版対応　追加　START
                    // セカンダリTPCへSOAP連携を行う
                    if (tpcConnectionResults[2] != null) {
                        // セカンダリTPCが設定されている場合に連携
                        try {
                            sOAPCommonOutputDto
                                = await this.sOAPCommon.callWebSoapApi("serviceProfileRequestLite", "Mod", "", paramsList,
                                tenantId, sequenceNo, tpcConnectionResults[2], false);

                            if (ResultCdConstants.CODE_000951.equals(sOAPCommonOutputDto.getProcessCode())) {
                                // SG取得がNG
                                super.warn(tenantId, sequenceNo, MsgKeysConstants.APGCFW0023, "SG取得NG");
                            } else {
                                document = sOAPCommonOutputDto.getDoc();
                                resultStrs = this.sOAPCommon.getNodeContent(document, "//Result");
                                if (sOAPCommonOutputDto.isError() || LineGroupAddAcquisitionService.RESULT_NG.equals(resultStrs)) {
                                    // TPCからのResultでNGの場合
                                    // ログ出力
                                    const errInfo: string = this.sOAPCommon.getNodeContent(sOAPCommonOutputDto.getDoc(),
                                        "//ErrorInfomation");
                                    super.warn(tenantId, sequenceNo, MsgKeysConstants.APGCFW0023, errInfo);
                                }
                            }
                        } catch (e) {
                            if (SOAPException.isSOAPException(e)) {
                                // ログ出力
                                super.warn(e.message, tenantId, sequenceNo, MsgKeysConstants.APGCFW0023, e.toString());
                            } else {
                                throw e;
                            }
                        }
                    }
                    // STEP22.0版対応　追加　END

                    /** STEP4.0版対応　追加　START */
                    let orderDate: string = null;
                    if (Constants.ORDER_TYPE_2.equals(orderType)) {
                        try {
                            const soData: ServiceOrdersEntity = await this.apiCommonDao.getServiceOrder(reserveSoId);
                            orderDate = format(soData.orderDate, "yyyy/MM/dd HH:mm:ss");
                        } catch (e: any) {
                            if (isSQLException(e)) {
                                // DBアクセスリトライエラーの場合
                                // 変数「CSV作成要否」を0で処理する
                                cheatCode = ResultCdConstants.CODE_000000;
                                super.error(e, tenantId, sequenceNo, MsgKeysConstants.APCOME0402, functionType);
                                // SO管理共通パラメータ設定
                                await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId, cheatCode, receivedDate, orderType, reserveDate, isPrivate);
                                // 返却値編集
                                lineGroupAddAcquisitionOutputDto = this.returnEdit(param, cheatCode, apiProcessID, isPrivate, errorMessage, receivedDate);
                                return lineGroupAddAcquisitionOutputDto;
                            } else {
                                throw e;
                            }
                        }
                    }
                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId, cheatCode, receivedDate, orderType, reserveDate, isPrivate);
                    let csvData = null;
                    if (Constants.ORDER_TYPE_0.equals(orderType) || Constants.ORDER_TYPE_2.equals(orderType)) {
                        csvData = await this.checkCsvOutKbn(param, nNo, orderType, tenantsInfo.tenantType);
                    }
                    // only exist for 0/2
                    if (Constants.ORDER_TYPE_0.equals(orderType)) {
                        lineGroupAddAcquisitionOutputDto = this.returnEdit(param, cheatCode, apiProcessID, isPrivate, errorMessage, receivedDate, receivedDate, nNo, csvData[LineGroupAddAcquisitionService.KEY_CSV_OUT_KBN], receivedDate, groupOptionPlans);
                    } else if (Constants.ORDER_TYPE_2.equals(orderType)) {
                        lineGroupAddAcquisitionOutputDto = this.returnEdit(param, cheatCode, apiProcessID, isPrivate, errorMessage, receivedDate, orderDate, nNo, csvData[LineGroupAddAcquisitionService.KEY_CSV_OUT_KBN], reserveDate, groupOptionPlans);
                    } else {
                        // should not exist
                        lineGroupAddAcquisitionOutputDto = this.returnEdit(param, cheatCode, apiProcessID, isPrivate, errorMessage, receivedDate);
                    }
                    /** STEP4.0版対応　追加　END */
                    return lineGroupAddAcquisitionOutputDto;
                }
            }
            /** STEP1.2b版対応　変更　END */
        } catch (e) {
            if (isSQLException(e)) {
                // DBアクセスリトライエラーの場合
                // リターンコード値は各エラー時にて設定済み
                super.error(e.message, param.tenantId, param.requestHeader.sequenceNo,
                    MsgKeysConstants.APCOME0402, functionType);
                // SO管理共通パラメータ設定
                await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId, cheatCode, receivedDate,
                    orderType, reserveDate, isPrivate);
                lineGroupAddAcquisitionOutputDto
                    = this.returnEdit(param, cheatCode, apiProcessID, isPrivate, errorMessage, receivedDate);
                return lineGroupAddAcquisitionOutputDto;
                // STEP20.0版対応　追加　END
            } else if (SOAPException.isSOAPException(e)) {
                this.context.error(e, e.message);
                cheatCode = ResultCdConstants.CODE_100401;
                // SO管理共通パラメータ設定
                await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId,cheatCode, receivedDate, orderType, reserveDate, isPrivate);
                lineGroupAddAcquisitionOutputDto = this.returnEdit(param, cheatCode,apiProcessID, isPrivate, errorMessage, receivedDate);
                return lineGroupAddAcquisitionOutputDto;
            } else {
                cheatCode = ResultCdConstants.CODE_999999;
                super.error(e.message, param.tenantId, param.requestHeader.sequenceNo, MsgKeysConstants.APCOME0401, e.message);
                this.context.error(e, e.message);
                // SO管理共通パラメータ設定
                await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId,cheatCode, receivedDate, orderType, reserveDate, isPrivate);
                // 返却値編集
                lineGroupAddAcquisitionOutputDto = this.returnEdit(param, cheatCode, apiProcessID, isPrivate, errorMessage, receivedDate);
                return lineGroupAddAcquisitionOutputDto;
            }
        } finally {
            // 処理コードが「000000」ならばCSV出力
            // if (ResultCdConstants.CODE_000000.equals(cheatCode)) {
            //     restCsvCommon.csvOutput(tenantId, sequenceNo, functionType, csvOutKbn, fileNameNo, csvData, null);
            // }
            super.debug(param.tenantId, param.requestHeader.sequenceNo, MsgKeysConstants.APGCFD0002,
                this.getClassName(), "service");
        }
        // SO管理共通パラメータ設定
        await this.soManagementCommon(param, resultBean, executeUserId, executeTenantId,cheatCode, receivedDate, orderType, reserveDate, isPrivate);
        // 返却値編集
        lineGroupAddAcquisitionOutputDto = this.returnEdit(param, cheatCode, apiProcessID, isPrivate, errorMessage, receivedDate);
        /** STEP1.2b版対応　追加　END */
        return lineGroupAddAcquisitionOutputDto;
    }

    /**
     * CSV出力要否判定
     * @param param 入力パラメータ
     * @param nnumber N番
     * @param orderType オーダ種別
     * @param tenantType 社内テナント種別
     * @return CSV出力要否判定結果
     */
    private async checkCsvOutKbn(param: LineGroupAddAcquisitionInputDto, nnumber: string,
                           orderType: string, tenantType: number):
        Promise<{ // @ts-ignore
        [LineGroupAddAcquisitionService.KEY_RESERVE_ORDER_DATE]: string, [LineGroupAddAcquisitionService.KEY_GROUP_OPTION_PLANS]: GroupOptionPlansEntity, [LineGroupAddAcquisitionService.KEY_CSV_OUT_KBN]: string}>
    {
        const accountingLineNo = param.AccountingLineNo;
        const tenantId = param.tenantId;
        const sequenceNo = param.requestHeader.sequenceNo;
        const optionGroupPlanId = param.optionGroupPlanId;
        const soId = param.reserve_soId;
        const result = {};

        // 入力パラメータ．「課金対象回線番号」が設定されているか確認する
        if (!CheckUtil.checkIsNotNull(accountingLineNo)) {
            // N番の有無を判定する
            if (CheckUtil.checkIsNotNull(nnumber)) {
                super.error(tenantId, sequenceNo, MsgKeysConstants.APGCFE0602, tenantId);
                return {[LineGroupAddAcquisitionService.KEY_CSV_OUT_KBN]: "0"};
            }
        }
        // 帯域卸ユーザかを判定する
        let wholeFlagList: boolean[] = [];
        if (!CheckUtil.checkIsNotNull(nnumber)) {
            // STEP20.0版対応　変更　START
            try {
                wholeFlagList = await this.apiLinesDao.getWholeFlag(tenantId, nnumber);
            } catch (e) {
                // DBアクセスリトライエラーの場合
                super.error(e, param.tenantId, param.requestHeader.sequenceNo,
                    MsgKeysConstants.APCOME0402, param.requestHeader.functionType);
                return {[LineGroupAddAcquisitionService.KEY_CSV_OUT_KBN]: "0"};
            }
            // STEP20.0版対応　変更　END
        }
        if (wholeFlagList.length === 0 || (wholeFlagList[0] != null && wholeFlagList[0])) {
            return {[LineGroupAddAcquisitionService.KEY_CSV_OUT_KBN]: "0"};
        }
        // テナント種別より判定する
        if (LineGroupAddAcquisitionService.TYPE_1 === tenantType || LineGroupAddAcquisitionService.TYPE_2 === tenantType || LineGroupAddAcquisitionService.TYPE_3 === tenantType) {
            return {[LineGroupAddAcquisitionService.KEY_CSV_OUT_KBN]: "0"};
        }

        // 容量追加コードを取得する
        // STEP20.0版対応　変更　START
        let groupOptionPlans: GroupOptionPlansEntity = null;
        try {
            groupOptionPlans = await this.apiLinesGroupDao.getGroupOptionPlan(optionGroupPlanId);
        } catch (e) {
        // DBアクセスリトライエラーの場合
        super.error(e, param.tenantId, param.requestHeader.sequenceNo,
            MsgKeysConstants.APCOME0402, param.requestHeader.functionType);
            return {[LineGroupAddAcquisitionService.KEY_CSV_OUT_KBN]: "0"};
        }
        // STEP20.0版対応　変更　END
        if (groupOptionPlans == null) {
            super.error(tenantId, sequenceNo, MsgKeysConstants.APGCFE0605, optionGroupPlanId);
            return {[LineGroupAddAcquisitionService.KEY_CSV_OUT_KBN]: "0"};
        }
        let capacityCode = groupOptionPlans.capacityCode;

        if(!CheckUtil.checkIsNotNull(capacityCode)) {
            capacityCode = capacityCode.trim();
        }

        if (!Check.checkCsvCapacityCodeFmt(capacityCode)) {
            super.error(tenantId, sequenceNo, MsgKeysConstants.APGCFE0605, optionGroupPlanId);
            return {[LineGroupAddAcquisitionService.KEY_CSV_OUT_KBN]: "0"};
        }

        // （予約実行時）予約時のシステム時刻を取得する
        if (Constants.ORDER_TYPE_2.equals(orderType)) {
            // STEP20.0版対応　変更　START
            let soData: ServiceOrdersEntity = null;
            try {
                soData = await this.apiCommonDao.getServiceOrder(soId);
            } catch (e) {
                // DBアクセスリトライエラーの場合
                super.error(e, param.tenantId, param.requestHeader.sequenceNo,
                    MsgKeysConstants.APCOME0402, param.requestHeader.functionType);
                return {[LineGroupAddAcquisitionService.KEY_CSV_OUT_KBN]: "0"};
            }
            // STEP20.0版対応　変更　END
            result[LineGroupAddAcquisitionService.KEY_RESERVE_ORDER_DATE] = format(soData.orderDate, "yyyy/MM/dd HH:mm:ss");
        }
        result[LineGroupAddAcquisitionService.KEY_GROUP_OPTION_PLANS] = groupOptionPlans;
        result[LineGroupAddAcquisitionService.KEY_CSV_OUT_KBN] = "1";

        return result;
    }

    /**
     * 回線グループ_回線グループ追加チャージ（クーポン）容量・期間追加返却値編集
     *
     * @param inputParam
     *            回線グループ_回線グループ追加チャージ（クーポン）容量・期間追加InputDto
     * @param rtncheatCode
     *            変数 「API処理ID」
     * @param rtnapiProcessID
     *            変数 「API処理ID」
     * @param isPrivate
     *            変数 「内部呼出フラグ」
     * @param errorMessage
     *            変数 「エラーメッセージ」
     * @param receivedDate REST API共通処理が呼び出されたシステム日付
     * @return 回線グループ_回線グループ追加チャージ（クーポン）容量・期間追加OutputDto
     */
    public returnEdit(
        inputParam: LineGroupAddAcquisitionInputDto,
        rtncheatCode: string,
        rtnapiProcessID: string,
        isPrivate: boolean,         // isPrivate = false
        errorMessage: string,       // not needed as isPrivate = false
        receivedDate: string) : LineGroupAddAcquisitionOutputDto

    /**
     * 回線グループ_回線グループ追加チャージ（クーポン）容量・期間追加返却値編集
     *
     * @param inputParam
     *            回線グループ_回線グループ追加チャージ（クーポン）容量・期間追加InputDto
     * @param rtncheatCode
     *            変数 「API処理ID」
     * @param rtnapiProcessID
     *            変数 「API処理ID」
     * @param isPrivate
     *            変数 「内部呼出フラグ」
     * @param errorMessage
     *            変数 「エラーメッセージ」
     * @param receivedDate REST API共通処理が呼び出されたシステム日付
     * @param orderReceivedDate 申込年月日
     * @param nNo N番
     * @param csvOutputKbn CSV出力区分
     * @param requestDate 即時オーダの場合 REST APIで受付けた際のシステム日付, 予約実行オーダの場合 電文の「予約日」
     * @param optionPlan オプションプラン - T番
     * @return 回線グループ_回線グループ追加チャージ（クーポン）容量・期間追加OutputDto
     */
    public returnEdit(
        inputParam: LineGroupAddAcquisitionInputDto,
        rtncheatCode: string,
        rtnapiProcessID: string,
        isPrivate: boolean,         // isPrivate = false
        errorMessage: string,       // not needed as isPrivate = false
        receivedDate: string,
        orderReceivedDate: string,
        nNo: string,
        csvOutputKbn: string,
        requestDate: string,
        optionPlan: GroupOptionPlansEntity
    ) : LineGroupAddAcquisitionOutputDto

    public returnEdit(
        inputParam: LineGroupAddAcquisitionInputDto,
        rtncheatCode: string,
        rtnapiProcessID: string,
        isPrivate: boolean,         // isPrivate = false
        errorMessage: string,       // not needed as isPrivate = false
        receivedDate: string,
        orderReceivedDate: string = null,
        nNo: string = null,
        csvOutputKbn: string = "0",
        requestDate: string = null,
        optionPlan: GroupOptionPlansEntity = null
    ) : LineGroupAddAcquisitionOutputDto {
        const responseHeader: ResponseHeader = {
            sequenceNo: inputParam.requestHeader.sequenceNo,
            receivedDate,
            processCode: rtncheatCode,
            apiProcessID: rtnapiProcessID,
        };
        const returnPram: LineGroupAddAcquisitionOutputDto = {
            jsonBody: {
                responseHeader
            },
            additionalData: {
                orderReceivedDate,
                csvOutputKbn,
                nNo,
                csvUnnecessary: csvOutputKbn === "0",
                requestDate: requestDate ? requestDate.substring(0, 10).replace(/[-/]/g, "") : null,
                tBan: optionPlan?.optionPlanIdT
            },
        };
        return returnPram;
    }

    /** STEP1.2a版対応　追加　START */
    /** STEP1.2b版対応　変更　START */
    /**
     * SO管理共通パラメータ設定
     *
     * @param param
     * @param resultBean
     * @param executeUserId
     * @param executeTenantId
     * @param receivedDate REST API共通処理が呼び出されたシステム日付
     * @param orderType
     * @param reserveDate
     * @param isPrivate
     * @throws Exception
     */
    private async soManagementCommon(
        param: LineGroupAddAcquisitionInputDto,
        resultBean: CheckResultBean,
        executeUserId: string,
        executeTenantId: string,
        cheatCode: string,
        receivedDate: string,
        orderType: string,
        reserveDate: string,
        isPrivate: boolean
    ) {
        // SO管理共通を呼び出す
        const soObject = new SOObject();
        /** STEP4.0d版対応　追加　START */
            // 課金対象回線番号が設定されている場合は登録する
        let so_accountingLineNo: string;
        if (CheckUtil.checkIsNotNull(param.AccountingLineNo)) {
            so_accountingLineNo = null;
        } else {
            so_accountingLineNo = param.AccountingLineNo;
        }
        /** STEP4.0d版対応　追加　END */

        // サービスオーダID
        soObject.setServiceOrderId(resultBean.others);
        // 申込日
        soObject.setOrderDate(receivedDate);
        // 予約日
        // 変数「オーダ種別」は「予約前オーダ」の場合
        if (Constants.ORDER_TYPE_1.equals(orderType)) {
            soObject.setReserveDate(reserveDate);
        } else {
            soObject.setReserveDate(null);
        }
        // オーダ種別
        soObject.setOrderType(orderType);
        // 完了日
        // 変数「オーダ種別」は「即時オーダ」の場合
        if (Constants.ORDER_TYPE_0.equals(orderType)
            || Constants.ORDER_TYPE_2.equals(orderType)) {
            soObject.setExecDate(MvnoUtil.getDateTimeNow());
        } else {
            soObject.setExecDate(null);
        }
        // オーダステータス
        soObject.setOrderStatus(cheatCode);
        /** STEP4.0対応 変更 START */
        /* #337 対応 変更 START */
        // 回線ID(課金対象回線番号)
        // 予約実行オーダの場合は登録しない
        if (Constants.ORDER_TYPE_2.equals(orderType)) {
            soObject.setLineId(null);
        } else {
            soObject.setLineId(so_accountingLineNo);
        }
        /* #337 対応 変更 END */
        /** STEP4.0対応 変更 END */
        // 回線グループID
        soObject.setLineGroupId(getStringParameter(param.lineGroupId));
        // 所属テナントID
        soObject.setTenantId(param.tenantId);
        // 実行ユーザID
        soObject.setExecuteUserId(executeUserId);
        // 実行テナントID
        soObject.setExecuteTenantId(executeTenantId);
        // 機能種別
        soObject.setFunctionType(param.requestHeader.functionType);
        // 操作区分
        soObject.setOperationDivision("");
        // 変更前プランID
        soObject.setChangeOldplanId("");
        // 変更後プランID
        soObject.setChangeNewplanId("");
        // 回線所属変更回線ID
        soObject.setChangePlanId("");
        // 基本容量
        soObject.setBasicCapacity("");
        // オプションプランID
        soObject.setOptionPlanId(getStringParameter(param.optionGroupPlanId));
        // 内部呼び出し
        soObject.setInternalFlag(isPrivate);
        // REST電文
        // 変数「オーダ種別」は「予約前オーダ」の場合
        if (Constants.ORDER_TYPE_1.equals(orderType)) {
            soObject.setRestMessage(MvnoUtil.getRestMessage(param));
        } else {
            soObject.setRestMessage(null);
        }
        await this.soCommon.soCommon(soObject);
    }
}