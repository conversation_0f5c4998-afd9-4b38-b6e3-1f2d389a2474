import RESTCommon from "@/core/common/RESTCommon";
import { IFService } from "@/core/service/IFService";
import LineDataGiftInputDto from "@/core/dto/LineDataGiftInputDto";
import LineDataGiftOutputDto from "@/core/dto/LineDataGiftOutputDto";
import StringUtils from "@/core/common/StringUtils";
import TenantManage from "@/core/common/TenantManage";
import Api<PERSON>ommon from "@/core/common/ApiCommon";
import SOAPCommon from "@/core/common/SOAPCommon";
import SOAPCommonCoupon from "@/core/common/SOAPCommonCoupon";
import SOCommon from "@/core/common/SOCommon";
import APILinesDAO from "@/core/dao/APILinesDAO";
import config from "config";
import ResultCdConstants from "@/core/constant/ResultCdConstants";
import MvnoUtil from "@/core/common/MvnoUtil";
import Check from "@/core/common/Check";
import MsgKeysConstants from "@/core/constant/MsgKeysConstants";
import { parse } from "date-fns";
import { format } from "date-fns/format";
import { isSQLException, retryQuery } from "@/helpers/queryHelper";
import { Transaction } from "sequelize";
import { usePsql } from "@/database/psql";
import SOObject from "@/core/dto/SOObject";
import ParameterName from "@/core/dto/ParameterName";
import SOAPCommonOutputDto from "@/core/dto/SOAPCommonOutputDto";
import CheckUtil from "@/core/common/CheckUtil";
import SOAPException from "@/types/soapException";
import TpcDataCheckResultDto from "@/core/dto/TpcDataCheckResultDto";
import BigNumber from "bignumber.js";
import "@/types/string.extension";
import { getStringParameter } from "@/types/parameter.string";

export default class LineDataGiftServiceTx
    extends RESTCommon
    implements IFService<LineDataGiftInputDto, LineDataGiftOutputDto>
{
    // STEP10.0版対応　追加　START
    // 定数 「99999999999（他社回線用回線番号）」
    private static readonly THIRD_PARTY_LINENO = "99999999999";
    // STEP10.0版対応　追加　END

    /**
     * テナント管理クラス
     */
    private tenantManage = new TenantManage(this.request, this.context);

    /**
     * API共通処理
     */
    private apiCommon = new ApiCommon(this.request, this.context);

    /**
     * SOAP共通
     */
    private soapCommon = new SOAPCommon(this.request, this.context);


    /**
     * SOAP共通(複数オーダ)
     */
    private soapCommonCoupon = new SOAPCommonCoupon(this.request, this.context);

    /**
     * SO管理共通DAO
     */
    private soCommon = new SOCommon(this.request, this.context);


    /**
     * API回線DAO
     */
    private apiLinesDAO = new APILinesDAO(this.request, this.context);

    /** データ譲渡不可フラグ */
    private giftNgFlag = config.get<string>("mvno.GiftNgFlag")

    /** データ譲渡不可時間帯 */
    private giftNgTime = config.get<string>("mvno.GiftNgTime")

    async service(
        param: LineDataGiftInputDto
    ): Promise<LineDataGiftOutputDto> {
        /** 送信番号取得 */
        const sequenceNo: string = param.requestHeader.sequenceNo;
        /** 送信元システムID取得 */
        const senderSystemId: string = param.requestHeader.senderSystemId;
        /** API認証キー取得 */
        const apiKey: string = param.requestHeader.apiKey;
        /** 機能種別取得 */
        const functionType: string = param.requestHeader.functionType;
        /** テナントID取得 */
        const tenantId: string = param.tenantId;
        /** 譲渡元回線番号取得 */
        const sourceLineNo: string = param.sourceLineNo;
        /** 譲渡先回線番号取得 */
        const destinationLineNo: string = param.destinationLineNo;
        /** 譲渡データ量取得 */
        const giftData: string = getStringParameter(param.giftData);

        /** 変数「処理コード」初期化 */
        let handleCode: string = ResultCdConstants.CODE_000000;

        /** 変数「システム時刻」取得 */
        const receivedDate: string = this.context.responseHeader.receivedDate;

        /** API処理Id */
        const apiHandleId = this.context.responseHeader.apiProcessID;

        try {
            // 譲渡元回線番号のフォーマットチェック
            if (!Check.checkLineNo(sourceLineNo)) {
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APLDGW0001, "譲渡元回線番号", sourceLineNo);
                handleCode = ResultCdConstants.CODE_190001;
                // SO管理共通
                await this.soManagement(param, apiHandleId, handleCode, receivedDate, functionType,
                    sourceLineNo, destinationLineNo);
                // 返却値編集
                return this.returnEdit(param, handleCode, apiHandleId, receivedDate);
            }

            // 譲渡先回線番号のフォーマットチェック
            if (!Check.checkLineNo(destinationLineNo)) {
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APLDGW0001, "譲渡先回線番号", destinationLineNo);
                handleCode = ResultCdConstants.CODE_190001;
                // SO管理共通
                await this.soManagement(param, apiHandleId, handleCode, receivedDate, functionType,
                    sourceLineNo, destinationLineNo);
                // 返却値編集
                return this.returnEdit(param, handleCode, apiHandleId, receivedDate);
            }

            // 譲渡データ量のフォーマットチェック
            // STEP10.0版対応　変更　START
            if (!Check.checkGiftData(giftData, sourceLineNo, destinationLineNo)) {
                // STEP10.0版対応　変更　END
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APLDGW0001, "譲渡データ量", giftData);
                handleCode = ResultCdConstants.CODE_190001;
                // SO管理共通
                await this.soManagement(param, apiHandleId, handleCode, receivedDate, functionType,
                    sourceLineNo, destinationLineNo);
                // 返却値編集
                return this.returnEdit(param, handleCode, apiHandleId, receivedDate);
            }

            // データ譲渡不可フラグのフォーマットチェック
            if (!"1".equals(this.giftNgFlag) && !"0".equals(this.giftNgFlag)) {
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APLDGW0002, this.giftNgFlag);
                handleCode = ResultCdConstants.CODE_190002;
                // SO管理共通
                await this.soManagement(param, apiHandleId, handleCode, receivedDate, functionType,
                    sourceLineNo, destinationLineNo);
                // 返却値編集
                return this.returnEdit(param, handleCode, apiHandleId, receivedDate);
            }

            // データ譲渡不可時間帯フォーマットチェック
            if (!Check.checkDataGiftNgTimeFormat(this.giftNgTime)) {
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APLDGW0003, this.giftNgTime);
                handleCode = ResultCdConstants.CODE_190003;
                // SO管理共通
                await this.soManagement(param, apiHandleId, handleCode, receivedDate, functionType,
                    sourceLineNo, destinationLineNo);
                // 返却値編集
                return this.returnEdit(param, handleCode, apiHandleId, receivedDate);
            }

            // データ譲渡不可時間帯範囲チェック
            if (!Check.checkDataGiftNgTime(this.dateFmt(receivedDate, "yyyy/MM/dd HH:mm:ss"), this.giftNgTime, this.giftNgFlag)) {
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APLDGW0004, this.giftNgFlag, this.giftNgTime);
                handleCode = ResultCdConstants.CODE_190004;
                // SO管理共通
                await this.soManagement(param, apiHandleId, handleCode, receivedDate, functionType,
                    sourceLineNo, destinationLineNo);
                // 返却値編集
                return this.returnEdit(param, handleCode, apiHandleId, receivedDate);
            }

            // 譲渡元回線番号と譲渡先回線番号が一致しているか確認する
            if (!sourceLineNo.equals(destinationLineNo)) {
                // 容量の譲渡 ※譲渡元回線番号と譲渡先回線番号が一致しない場合
                /** STEP5.0対応　変更　START */
                const returnParam = await this.doGift(param, receivedDate, apiHandleId, functionType);
                handleCode = returnParam.jsonBody.responseHeader.processCode;
                return returnParam;
                /** STEP5.0対応　変更　END */
            } else {
                // 容量の減算 ※譲渡元回線番号と譲渡先回線番号が一致する場合
                /** STEP5.0対応　変更　START */
                const returnParam = await this.doSubtract(param, receivedDate, apiHandleId, functionType);
                handleCode = returnParam.jsonBody.responseHeader.processCode;
                return returnParam;
                /** STEP5.0対応　変更　END */
            }
        } catch (e) {
            super.error(e, param.tenantId, param.requestHeader.sequenceNo, MsgKeysConstants.APCOME0401,
                e.message);
            handleCode = ResultCdConstants.CODE_999999;
            return this.returnEdit(param, handleCode, apiHandleId, receivedDate);
        }
    }

    /**
     * 容量の譲渡<BR>
     * @param param 回線データ譲渡機能インプット
     * @param apiHandleId API処理ID
     * @param receivedDate REST API共通処理が呼び出されたシステム日付
     * @param functionType 機能種別
     * @return 処理結果
     * @throws Exception 想定外エラー
     */
    private async doGift(
        param: LineDataGiftInputDto,
        receivedDate: string,
        apiHandleId: string,
        functionType: string
    ): Promise<LineDataGiftOutputDto> {
        /** 送信番号取得 */
        const sequenceNo: string = param.requestHeader.sequenceNo;
        /** テナントID取得 */
        const tenantId: string = param.tenantId;
        /** 譲渡元回線番号取得 */
        const sourceLineNo: string = param.sourceLineNo;
        /** 譲渡先回線番号取得 */
        const destinationLineNo: string = param.destinationLineNo;
        /** 譲渡データ量取得 */
        const giftData: string = getStringParameter(param.giftData);
        /** 変数「処理コード」初期化 */
        let handleCode: string = ResultCdConstants.CODE_000000;

        const sequelize = await usePsql();

        // 容量の譲渡 ※譲渡元回線番号と譲渡先回線番号が一致しない場合

        // 回線テナント所属判定

        // 電文中の譲渡元回線番号とテナントIDの関係が有効であることを確認する。
        // 変数「譲渡元回線関係チェック結果」より判定する
        // STEP10.0版対応　追加　START
        // 譲渡元回線番号が「99999999999」(9が11桁)の時は処理を行わない
        if (!sourceLineNo.equals(LineDataGiftServiceTx.THIRD_PARTY_LINENO)) {
            // STEP10.0版対応　追加　END
            // STEP20.0版対応　変更　START
            let tenantCheckResult: [boolean, string] = null;
            try {
                tenantCheckResult = await this.tenantManage.doCheck(sourceLineNo, tenantId);
            } catch (e) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    super.error(e, tenantId, sequenceNo, MsgKeysConstants.APCOME0402, functionType);
                    handleCode = ResultCdConstants.CODE_190100;
                    // SO管理共通
                    await this.soManagement(param, apiHandleId, handleCode, receivedDate, functionType,
                        sourceLineNo, destinationLineNo);
                    // 返却値編集
                    return this.returnEdit(param, handleCode, apiHandleId, receivedDate);
                }
                throw e;
            }
            if (!tenantCheckResult[0]) {
                // STEP20.0版対応　変更　START
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APLDGW0101, sourceLineNo, tenantId);
                handleCode = ResultCdConstants.CODE_190100;
                // SO管理共通
                await this.soManagement(param, apiHandleId, handleCode, receivedDate, functionType,
                    sourceLineNo, destinationLineNo);
                // 返却値編集
                return this.returnEdit(param, handleCode, apiHandleId, receivedDate);
            }
        }

        // 電文中の譲渡先回線番号とテナントIDの関係が有効であることを確認する。
        // STEP10.0版対応　追加　START
        // 譲渡先回線番号が「99999999999」(9が11桁)の時は処理を行わない
        if (!destinationLineNo.equals(LineDataGiftServiceTx.THIRD_PARTY_LINENO)) {
            // STEP10.0版対応　追加　END
            // STEP20.0版対応　変更　START
            let tenantCheckResult: [boolean, string] = null;
            try {
                tenantCheckResult = await this.tenantManage.doCheck(destinationLineNo, tenantId);
            } catch (e) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    super.error(e, tenantId, sequenceNo, MsgKeysConstants.APCOME0402, functionType);
                    handleCode = ResultCdConstants.CODE_190101;
                    // SO管理共通
                    await this.soManagement(param, apiHandleId, handleCode, receivedDate, functionType,
                        sourceLineNo, destinationLineNo);
                    // 返却値編集
                    return this.returnEdit(param, handleCode, apiHandleId, receivedDate);
                }
                throw e;
            }
            if (!tenantCheckResult[0]) {
                // STEP20.0版対応　変更　END
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APLDGW0102, destinationLineNo, tenantId);
                handleCode = ResultCdConstants.CODE_190101;
                // SO管理共通
                await this.soManagement(param, apiHandleId, handleCode, receivedDate, functionType, sourceLineNo, destinationLineNo);
                // 返却値編集
                return this.returnEdit(param, handleCode, apiHandleId, receivedDate);
            }
        }
        // 廃止オーダチェック

        // 譲渡元回線番号の廃止オーダが受付中でないかを確認する。
        // 変数「譲渡元回線廃止オーダチェック結果」より判定する
        // STEP10.0版対応　追加　START
        // 譲渡元回線番号が「99999999999」(9が11桁)の時は処理を行わない
        if (!sourceLineNo.equals(LineDataGiftServiceTx.THIRD_PARTY_LINENO)) {
            // STEP10.0版対応　追加　END
            // STEP20.0版対応　変更　START
            let checkAbolishSoResult: boolean;
            try {
                checkAbolishSoResult = await this.apiCommon.checkAbolishSo(sourceLineNo, this.dateFmt(receivedDate, "yyyyMMdd"));
            } catch (e) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    super.error(e, tenantId, sequenceNo, MsgKeysConstants.APCOME0402, functionType);
                    handleCode = ResultCdConstants.CODE_190102;
                    // SO管理共通
                    await this.soManagement(param, apiHandleId, handleCode, receivedDate, functionType,
                        sourceLineNo, destinationLineNo);
                    // 返却値編集
                    return this.returnEdit(param, handleCode, apiHandleId, receivedDate);
                }
                throw e;
            }
            if (!checkAbolishSoResult) {
                // STEP20.0版対応　変更　END
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APLDGW0103, sourceLineNo);
                handleCode = ResultCdConstants.CODE_190102;
                // SO管理共通
                await this.soManagement(param, apiHandleId, handleCode, receivedDate, functionType, sourceLineNo, destinationLineNo);
                // 返却値編集
                return this.returnEdit(param, handleCode, apiHandleId, receivedDate);
            }
        }
        // 譲渡先回線番号の廃止オーダが受付中でないかを確認する。
        // 変数「譲渡先回線廃止オーダチェック結果」より判定する
        // STEP10.0版対応　追加　START
        // 譲渡先回線番号が「99999999999」(9が11桁)の時は処理を行わない
        if (!destinationLineNo.equals(LineDataGiftServiceTx.THIRD_PARTY_LINENO)) {
            // STEP10.0版対応　追加　END
            // STEP20.0版対応　変更　START
            let checkAbolishSoResult: boolean;
            try {
                checkAbolishSoResult = await this.apiCommon.checkAbolishSo(destinationLineNo, this.dateFmt(receivedDate, "yyyyMMdd"));
            } catch (e) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    super.error(e, tenantId, sequenceNo, MsgKeysConstants.APCOME0402, functionType);
                    handleCode = ResultCdConstants.CODE_190103;
                    // SO管理共通
                    await this.soManagement(param, apiHandleId, handleCode, receivedDate, functionType,
                        sourceLineNo, destinationLineNo);
                    // 返却値編集
                    return this.returnEdit(param, handleCode, apiHandleId, receivedDate);
                }
                throw e;
            }
            if (!checkAbolishSoResult) {
                // STEP20.0版対応　変更　END
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APLDGW0104, destinationLineNo);
                handleCode = ResultCdConstants.CODE_190103;
                // SO管理共通
                await this.soManagement(param, apiHandleId, handleCode, receivedDate, functionType, sourceLineNo, destinationLineNo);
                // 返却値編集
                return this.returnEdit(param, handleCode, apiHandleId, receivedDate);
            }
        }

        // STEP15.0版対応　追加　START
        // 利用状態チェック

        // 譲渡元回線番号の利用状態がサスペンド中でないかを確認する。
        // 譲渡元回線番号が「99999999999」(9が11桁)の時は処理を行わない
        if (!sourceLineNo.equals(LineDataGiftServiceTx.THIRD_PARTY_LINENO)) {
            // 変数「利用状態判定結果」より判定する
            // STEP20.0版対応　変更　START
            let checkLineSuspendResult: boolean;
            try {
                checkLineSuspendResult = await this.apiCommon.checkLineSuspend(sourceLineNo);
            } catch (e) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    super.error(e, tenantId, sequenceNo, MsgKeysConstants.APCOME0402, functionType);
                    handleCode = ResultCdConstants.CODE_190115;
                    // SO管理共通
                    await this.soManagement(param, apiHandleId, handleCode, receivedDate, functionType,
                        sourceLineNo, destinationLineNo);
                    // 返却値編集
                    return this.returnEdit(param, handleCode, apiHandleId, receivedDate);
                }
                throw e;
            }
            if (!checkLineSuspendResult) {
                // STEP20.0版対応　変更　END
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APLDGW0116, sourceLineNo);
                handleCode = ResultCdConstants.CODE_190115;
                // SO管理共通
                await this.soManagement(param, apiHandleId, handleCode, receivedDate, functionType,
                    sourceLineNo, destinationLineNo);
                // 返却値編集
                return this.returnEdit(param, handleCode, apiHandleId, receivedDate);
            }
        }
        // 譲渡先回線番号の利用状態がサスペンド中でないかを確認する。
        // 譲渡先回線番号が「99999999999」(9が11桁)の時は処理を行わない
        if (!destinationLineNo.equals(LineDataGiftServiceTx.THIRD_PARTY_LINENO)) {
            // 変数「利用状態判定結果」より判定する
            // STEP20.0版対応　変更　START
            let checkLineSuspendResult;
            try {
                checkLineSuspendResult = await this.apiCommon.checkLineSuspend(destinationLineNo);
            } catch (e) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    super.error(e, tenantId, sequenceNo, MsgKeysConstants.APCOME0402, functionType);
                    handleCode = ResultCdConstants.CODE_190116;
                    // SO管理共通
                    await this.soManagement(param, apiHandleId, handleCode, receivedDate, functionType,
                        sourceLineNo, destinationLineNo);
                    // 返却値編集
                    return this.returnEdit(param, handleCode, apiHandleId, receivedDate);
                }
                throw e;
            }
            if (!checkLineSuspendResult) {
                // STEP20.0版対応　変更　END
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APLDGW0117, destinationLineNo);
                handleCode = ResultCdConstants.CODE_190116;
                // SO管理共通
                await this.soManagement(param, apiHandleId, handleCode, receivedDate, functionType,
                    sourceLineNo, destinationLineNo);
                // 返却値編集
                return this.returnEdit(param, handleCode, apiHandleId, receivedDate);
            }
        }
        // STEP15.0版対応　追加　END

        // STEP20.0版対応　変更　START
        const retryResult = await retryQuery(
            this.context,
            "doGift",
            async() => {
                let tx: Transaction = await sequelize.transaction();
                try {
                    // STEP10.0版対応　追加　START
                    // 譲渡元回線番号が「99999999999」(9が11桁)の時は処理を行わない
                    if (!sourceLineNo.equals(LineDataGiftServiceTx.THIRD_PARTY_LINENO)) {
                        // STEP10.0版対応　追加　END
                        // データ譲渡回線テーブルに譲渡元回線番号が存在するか確認する
                        let dupSrcLineId: string;
                        try {
                            dupSrcLineId = await this.apiLinesDAO.getGiftLine(sourceLineNo, tx);
                        } catch (e) {
                            if (isSQLException(e)) {
                                tx = null;
                                handleCode = ResultCdConstants.CODE_190104;
                            }
                            throw e;
                        }
                        // 変数「登録済譲渡元回線ID」の有無を判定する
                        if (!CheckUtil.checkIsNotNull(dupSrcLineId)) {
                            await tx.rollback();
                            super.warn(tenantId, sequenceNo, MsgKeysConstants.APLDGW0105, sourceLineNo);
                            handleCode = ResultCdConstants.CODE_190104;
                            // SO管理共通
                            await this.soManagement(param, apiHandleId, handleCode, receivedDate, functionType,
                                sourceLineNo, destinationLineNo);

                            return this.returnEdit(param, handleCode, apiHandleId, receivedDate);
                        }
                        // データ譲渡回線テーブルに対して譲渡元回線IDを追加する
                        let insGiftSrcLineDup: boolean;
                        try {
                            insGiftSrcLineDup = await this.apiLinesDAO.insertGiftLineNotDup(sourceLineNo, apiHandleId,
                                MvnoUtil.convDateFormat(this.dateFmt(receivedDate, "yyyy/MM/dd HH:mm:ss")),
                                tx);
                        } catch (e) {
                            if (isSQLException(e)) {
                                tx = null;
                                handleCode = ResultCdConstants.CODE_190105;
                            }
                            throw e;
                        }

                        if (!insGiftSrcLineDup) {
                            await tx.rollback();
                            super.warn(tenantId, sequenceNo, MsgKeysConstants.APLDGW0107, sourceLineNo);
                            handleCode = ResultCdConstants.CODE_190105;
                            // SO管理共通
                            await this.soManagement(param, apiHandleId, handleCode, receivedDate, functionType,
                                sourceLineNo, destinationLineNo);

                            return this.returnEdit(param, handleCode, apiHandleId, receivedDate);
                        }
                    }

                    // STEP10.0版対応　追加　START
                    // 譲渡先回線番号が「99999999999」(9が11桁)の時は処理を行わない
                    if (!destinationLineNo.equals(LineDataGiftServiceTx.THIRD_PARTY_LINENO)) {
                    // STEP10.0版対応　追加　END
                        // データ譲渡回線テーブルに譲渡先回線番号が存在するか確認する
                        let dupDestLineId: string;
                        try {
                            dupDestLineId = await this.apiLinesDAO.getGiftLine(destinationLineNo, tx);
                        } catch (e) {
                            if (isSQLException(e)) {
                                tx = null;
                                handleCode = ResultCdConstants.CODE_190106;
                            }
                            throw e;
                        }
                        // 変数「登録済譲渡先回線ID」の有無を判定する
                        if (!CheckUtil.checkIsNotNull(dupDestLineId)) {
                            await tx.rollback();
                            super.warn(tenantId, sequenceNo, MsgKeysConstants.APLDGW0106, destinationLineNo);
                            handleCode = ResultCdConstants.CODE_190106;
                            // SO管理共通
                            await this.soManagement(param, apiHandleId, handleCode, receivedDate, functionType,
                                sourceLineNo, destinationLineNo);

                            return this.returnEdit(param, handleCode, apiHandleId, receivedDate);
                        }
                        // データ譲渡回線テーブルに対して譲渡先回線IDを追加する
                        let insGiftDestLineDup: boolean;
                        try {
                            insGiftDestLineDup = await this.apiLinesDAO.insertGiftLineNotDup(destinationLineNo, apiHandleId,
                                MvnoUtil.convDateFormat(receivedDate),
                                tx);
                        } catch (e) {
                            if (isSQLException(e)) {
                                tx = null;
                                handleCode = ResultCdConstants.CODE_190107;
                            }
                            throw e;
                        }

                        if (!insGiftDestLineDup) {
                            await tx.rollback();
                            super.warn(tenantId, sequenceNo, MsgKeysConstants.APLDGW0108, destinationLineNo);
                            handleCode = ResultCdConstants.CODE_190107;
                            // SO管理共通
                            await this.soManagement(param, apiHandleId, handleCode, receivedDate, functionType,
                                sourceLineNo, destinationLineNo);

                            return this.returnEdit(param, handleCode, apiHandleId, receivedDate);
                        }
                    }
                    // トランザクション終了
                    await tx.commit();

                    // リトライしたタイミングでエラーコードを設定しているので
                    // リトライ後に正常となった場合は正常コードに戻す
                    handleCode = ResultCdConstants.CODE_000000;
                } catch (e) {
                    if (isSQLException(e)) {
                        throw e;
                    }
                    super.error(e, param.tenantId, param.requestHeader.sequenceNo,
                        MsgKeysConstants.APCOME0401, e.message);
                    handleCode = ResultCdConstants.CODE_999999;
                    if (tx) {       // actually should not happen as tx = null only if SQLException
                        await tx.rollback();
                    }
                    // SO管理共通
                    await this.soManagement(param, apiHandleId, handleCode, receivedDate, functionType,
                        sourceLineNo, destinationLineNo);

                    return this.returnEdit(param, handleCode, apiHandleId, receivedDate);
                } finally {
                    if (tx) {
                        tx = null;
                    }
                }
            },
            this.apDbRetryMaxCnt,
            this.apDbRetryInterval
        );

        if (retryResult) {
            return retryResult;
        }

        try {
            let tpcInfo: [boolean, string, string];
            try {
                tpcInfo = await this.tenantManage.checkTpcConnection(tenantId);
            } catch (e) {
                if (isSQLException(e)) {
                    handleCode = ResultCdConstants.CODE_190108;
                }
                throw e;
            }
            if (!tpcInfo[0]) {
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APLDGW0109, tenantId);
                handleCode = ResultCdConstants.CODE_190108;
                // 排他制御用データ削除
                handleCode = await this.deleteLockRecord(apiHandleId, handleCode, param);
                // SO管理共通
                await this.soManagement(param, apiHandleId, handleCode, receivedDate, functionType,
                    sourceLineNo, destinationLineNo);
                // 返却値編集
                return this.returnEdit(param, handleCode, apiHandleId, receivedDate);
            }
            // SOAP-API電文送受信（譲渡元回線番号）
            // SOAP  APIにより、譲渡元回線のTPCデータを取得する

            // STEP10.0版対応　追加　START
            let basicCapStr: string = "0";
            let basicUseStr: string = "0";
            let giftCap1Str: string = "0";
            let giftExpDate1: string = "";
            // 譲渡元回線番号が「99999999999」(9が11桁)の時は処理を行わない
            if (!sourceLineNo.equals(LineDataGiftServiceTx.THIRD_PARTY_LINENO)) {
                // STEP10.0版対応　追加　END
                let srcLineTpcInfo: SOAPCommonOutputDto;
                try {
                    srcLineTpcInfo = await this.callSoapAllView(sourceLineNo, tenantId, sequenceNo, tpcInfo[1]);
                } catch (e) {
                    if (SOAPException.isSOAPException(e)) {
                        super.warn(tenantId, sequenceNo, MsgKeysConstants.APLDGW0110, sourceLineNo, "");
                        handleCode = ResultCdConstants.CODE_190109;
                        // 排他制御用データ削除
                        handleCode = await this.deleteLockRecord(apiHandleId, handleCode, param);
                        // SO管理共通
                        await this.soManagement(param, apiHandleId, handleCode, receivedDate, functionType,
                            sourceLineNo, destinationLineNo);
                        // 返却値編集
                        return this.returnEdit(param, handleCode, apiHandleId, receivedDate);
                    }
                    throw e;
                }
                // 変数「譲渡元回線TPCデータ」より判定する
                const srcLineDoc = srcLineTpcInfo.getDoc();
                // 結果がNG
                if (!ResultCdConstants.CODE_000000.equals(srcLineTpcInfo.getProcessCode())) {
                    let errInfo = "";
                    if (srcLineDoc != null) {
                        errInfo = this.soapCommon.getNodeContent(srcLineDoc, "//ErrorInfomation");
                    }
                    super.warn(tenantId, sequenceNo, MsgKeysConstants.APLDGW0110, sourceLineNo, errInfo);
                    handleCode = ResultCdConstants.CODE_190109;
                    // 排他制御用データ削除
                    handleCode = await this.deleteLockRecord(apiHandleId, handleCode, param);
                    // SO管理共通
                    await this.soManagement(param, apiHandleId, handleCode, receivedDate, functionType,
                        sourceLineNo, destinationLineNo);
                    // 返却値編集
                    return this.returnEdit(param, handleCode, apiHandleId, receivedDate);
                }
                // 変数「譲渡元回線TPCデータ」より、データを取得し設定する
                // STEP10.0版対応　変更　START
                basicCapStr = this.soapCommon.getNodeContent(
                    srcLineDoc, "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_month_info/dyn_cap");
                basicUseStr = this.soapCommon.getNodeContent(
                    srcLineDoc, "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_month_info/dyn_use");
                giftCap1Str = this.soapCommon.getNodeContent(
                    srcLineDoc, "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_tms_info_1/dyn_cap");
                giftExpDate1 = this.soapCommon.getNodeContent(
                    srcLineDoc, "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_tms_info_1/dyn_exp_date");
                // STEP10.0版対応　変更　END

                // 取得した値が空文字(SOAPCommonにてnullは空に変換されている)の場合は0を設定する
                if ("".equals(basicCapStr)) {
                    basicCapStr = "0";
                }
                if ("".equals(basicUseStr)) {
                    basicUseStr = "0";
                }
                if ("".equals(giftCap1Str)) {
                    giftCap1Str = "0";
                }
            }
            // SOAP-API電文送受信（譲渡先回線番号）
            // SOAP  APIにより、譲渡先回線のTPCデータを取得する
            // STEP10.0版対応　追加　START
            let giftCap0Str = "0";
            let giftExpDate0 = "";

            // 譲渡先回線番号が「99999999999」(9が11桁)の時は処理を行わない
            if (!destinationLineNo.equals(LineDataGiftServiceTx.THIRD_PARTY_LINENO)) {
                let destLineTpcInfo: SOAPCommonOutputDto;
                try {
                    destLineTpcInfo = await this.callSoapAllView(
                        destinationLineNo, tenantId, sequenceNo, tpcInfo[1]);
                } catch (e) {
                    if (SOAPException.isSOAPException(e)) {
                        super.warn(tenantId, sequenceNo, MsgKeysConstants.APLDGW0111, destinationLineNo, "");
                        handleCode = ResultCdConstants.CODE_190110;
                        // 排他制御用データ削除
                        handleCode = await this.deleteLockRecord(apiHandleId, handleCode, param);
                        // SO管理共通
                        await this.soManagement(param, apiHandleId, handleCode, receivedDate, functionType,
                            sourceLineNo, destinationLineNo);
                        // 返却値編集
                        return this.returnEdit(param, handleCode, apiHandleId, receivedDate);
                    }
                    throw e;
                }
                // 変数「譲渡先回線TPCデータ」より判定する
                const destLineDoc = destLineTpcInfo.getDoc();
                // 結果がNG
                if (!ResultCdConstants.CODE_000000.equals(destLineTpcInfo.getProcessCode())) {
                    let errInfo = "";
                    if (destLineDoc != null) {
                        errInfo = this.soapCommon.getNodeContent(destLineDoc, "//ErrorInfomation");
                    }
                    super.warn(tenantId, sequenceNo, MsgKeysConstants.APLDGW0111, destinationLineNo, errInfo);
                    handleCode = ResultCdConstants.CODE_190110;
                    // 排他制御用データ削除
                    handleCode = await this.deleteLockRecord(apiHandleId, handleCode, param);
                    // SO管理共通
                    await this.soManagement(param, apiHandleId, handleCode, receivedDate, functionType,
                        sourceLineNo, destinationLineNo);
                    // 返却値編集
                    return this.returnEdit(param, handleCode, apiHandleId, receivedDate);
                }

                // 変数「譲渡先回線TPCデータ」より、データを取得し設定する
                // STEP10.0版対応　変更　START
                giftCap0Str = this.soapCommon.getNodeContent(
                    destLineDoc, "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_tms_info_0/dyn_cap");
                giftExpDate0 = this.soapCommon.getNodeContent(
                    destLineDoc, "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_tms_info_0/dyn_exp_date");
                // STEP10.0版対応　変更　END

                // 取得した値が空文字(SOAPCommonにてnullは空に変換されている)の場合は0を設定する
                if ("".equals(giftCap0Str)) {
                    giftCap0Str = "0";
                }
            }

            // データ取得項目のフォーマットを確認する。
            // STEP10.0版対応　変更　START
            // 譲渡元回線番号もしくは譲渡先回線番号が「99999999999」(9が11桁)の時に確認するフォーマットを変更
            let tpcDataResult: TpcDataCheckResultDto = null;

            if (sourceLineNo.equals(LineDataGiftServiceTx.THIRD_PARTY_LINENO)) {
                tpcDataResult = Check.checkLineGiftTpcData(
                    null, null, null, null, giftCap0Str, giftExpDate0);

            } else if (destinationLineNo.equals(LineDataGiftServiceTx.THIRD_PARTY_LINENO)) {
                tpcDataResult = Check.checkLineGiftTpcData(
                    basicCapStr, basicUseStr, giftCap1Str, giftExpDate1, null, null);

            } else {
                tpcDataResult = Check.checkLineGiftTpcData(
                    basicCapStr, basicUseStr, giftCap1Str, giftExpDate1, giftCap0Str, giftExpDate0);
            }
            // STEP10.0版対応　変更　END

            // 変数「データ譲渡TPC取得情報フォーマットチェック結果」より判定する
            if (!tpcDataResult.getResult()) {
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APLDGW0112,
                    tpcDataResult.getErrName(), tpcDataResult.getErrVal());
                handleCode = ResultCdConstants.CODE_190111;
                // 排他制御用データ削除
                handleCode = await this.deleteLockRecord(apiHandleId, handleCode, param);
                // SO管理共通
                await this.soManagement(param, apiHandleId, handleCode, receivedDate, functionType,
                    sourceLineNo, destinationLineNo);
                // 返却値編集
                return this.returnEdit(param, handleCode, apiHandleId, receivedDate);
            }

            // 取得データを数値に変換する
            const basicCap = new BigNumber(basicCapStr);            // bigDecimal equivalent
            const basicUse = new BigNumber(basicUseStr);
            const giftCap0 = new BigNumber(giftCap0Str);
            const giftCap1 = new BigNumber(giftCap1Str);

            // 譲渡元回線残容量チェック
            // 譲渡bucket1有効期限が月末であるかを確認する

            // STEP10.0版対応　追加　START
            // データ譲渡量をByte単位に変換する
            const bytes = new BigNumber(1024);
            const giftDataByte = (new BigNumber(giftData)).times(bytes).times(bytes);
            // STEP10.0版対応　追加　END

            // STEP10.0版対応　追加　START
            let isEndOfMonthBucket1 = false;
            // 譲渡元回線番号が「99999999999」(9が11桁)の時は処理を行わない
            if (!sourceLineNo.equals(LineDataGiftServiceTx.THIRD_PARTY_LINENO)) {

                // 譲渡bucket1有効期限がシステム時刻の月末か確認
                isEndOfMonthBucket1 = Check.checkIsEndOfMonth(giftExpDate1, this.dateFmt(receivedDate, "yyyy/MM/dd"));

                // データ譲渡量がマイナス値(「-」＋9桁以下の文字列)の時は処理を行わない
                if (!(new RegExp("-[0-9]+")).test(giftData)) {
                    // STEP10.0版対応　追加　END
                    let srcRemain: BigNumber = null;

                    // 譲渡元bucket残容量を算出する
                    if (isEndOfMonthBucket1) {
                        srcRemain = basicCap.minus(basicUse).plus(giftCap1);
                    } else {
                        srcRemain = basicCap.minus(basicUse);
                    }

                    // 残容量と譲渡量を比較する
                    if (srcRemain.comparedTo(giftDataByte) < 0) {
                        super.warn(tenantId, sequenceNo, MsgKeysConstants.APLDGW0113,
                            giftDataByte.toString(), srcRemain.toString());
                        handleCode = ResultCdConstants.CODE_190112;
                        // 排他制御用データ削除
                        handleCode = await this.deleteLockRecord(apiHandleId, handleCode, param);
                        // SO管理共通
                        await this.soManagement(param, apiHandleId, handleCode, receivedDate, functionType,
                            sourceLineNo, destinationLineNo);
                        // 返却値編集
                        return this.returnEdit(param, handleCode, apiHandleId, receivedDate);
                    }
                }
            }

            // 更新値計算

            // STEP10.0版対応　追加　START
            const bdZero: BigNumber = BigNumber("0");
            // STEP10.0版対応　追加　END

            // 譲渡元回線の更新値を計算する
            let updateSrcBucket: BigNumber = null;
            // STEP10.0版対応　追加　START
            // 譲渡元回線番号が「99999999999」(9が11桁)の時は処理を行わない
            if (!sourceLineNo.equals(LineDataGiftServiceTx.THIRD_PARTY_LINENO)) {
                // STEP10.0版対応　追加　END
                if (isEndOfMonthBucket1) {
                    updateSrcBucket = (giftCap1.minus(giftDataByte)).dividedToIntegerBy(bytes.pow(2));
                } else {
                    updateSrcBucket = giftDataByte.negated().dividedToIntegerBy(bytes.pow(2));
                }
                // STEP10.0版対応　追加　START
            } else {
                updateSrcBucket = bdZero;
                // STEP10.0版対応　追加　END
            }
            let updateDestBucket: BigNumber = null;
            // STEP10.0版対応　追加　START
            // 譲渡先回線番号が「99999999999」(9が11桁)の時は処理を行わない
            if (!destinationLineNo.equals(LineDataGiftServiceTx.THIRD_PARTY_LINENO)) {
                // STEP10.0版対応　追加　END
                // 譲渡bucket0有効期限が月末であるかを確認する
                const isEndOfMonthBucket0: boolean = Check.checkIsEndOfMonth(giftExpDate0, this.dateFmt(receivedDate, "yyyy/MM/dd"));

                // 譲渡先回線の更新値を計算する
                if (isEndOfMonthBucket0) {
                    updateDestBucket = (giftCap0.plus(giftDataByte)).dividedToIntegerBy(bytes.pow(2));
                } else {
                    updateDestBucket = giftDataByte.dividedToIntegerBy(bytes.pow(2));
                }
                // STEP10.0版対応　追加　START
            } else  {
                updateDestBucket = bdZero;
                // STEP10.0版対応　追加　END
            }

            // 月末日の取得
            let endOfMonth: string = Check.getEndOfMonth(this.dateFmt(receivedDate, "yyyy/MM/dd"));

            // STEP10.0版対応　追加　START
            // 譲渡bucket容量更新値（譲渡先回線）が0以下の場合に「1」を設定
            // 譲渡bucket容量更新値（譲渡先回線）が0以下の場合に「2000/01/01」を設定
            if (!destinationLineNo.equals(LineDataGiftServiceTx.THIRD_PARTY_LINENO)) {
                if (bdZero.comparedTo(updateDestBucket) >= 0) {
                    updateDestBucket = new BigNumber(1);
                    endOfMonth = "2000/01/01";
                }
                // 譲渡bucket容量更新値（譲渡元回線）が0以上の場合に「-1」を設定
                // 譲渡bucket容量更新値（譲渡元回線）が0以上の場合に「2000/01/01」を設定
            } else if (!sourceLineNo.equals(LineDataGiftServiceTx.THIRD_PARTY_LINENO)) {
                if (bdZero.comparedTo(updateSrcBucket) <= 0) {
                    updateSrcBucket = new BigNumber(-1);
                    endOfMonth = "2000/01/01";
                }
            }
            // SOAP電文の送信
            let giftSoapResult: SOAPCommonOutputDto;
            try {
                // STEP22.0版対応　変更　START
                giftSoapResult
                    = await this.callSoapGift(sourceLineNo, destinationLineNo, updateSrcBucket.toString(),
                    updateDestBucket.toString(), tenantId, sequenceNo, tpcInfo[1], endOfMonth, true);
                // STEP22.0版対応　変更　END
            } catch (e) {
                if (SOAPException.isSOAPException(e)) {
                    super.warn(tenantId, sequenceNo, MsgKeysConstants.APLDGW0114, destinationLineNo, sourceLineNo, "");
                    handleCode = ResultCdConstants.CODE_190113;
                    // 排他制御用データ削除
                    handleCode = await this.deleteLockRecord(apiHandleId, handleCode, param);
                    // SO管理共通
                    await this.soManagement(param, apiHandleId, handleCode, receivedDate, functionType,
                        sourceLineNo, destinationLineNo);
                    // 返却値編集
                    return this.returnEdit(param, handleCode, apiHandleId, receivedDate);
                }
                throw e;
            }
            // 変数「データ更新結果」より判定する
            let giftResultDoc: Document = giftSoapResult.getDoc();
            if (!ResultCdConstants.CODE_000000.equals(giftSoapResult.getProcessCode())) {
                let errInfo: string = "";
                if (giftResultDoc != null) {
                    errInfo = this.soapCommon.getNodeContent(giftResultDoc, "//ErrorInfomation");
                }
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APLDGW0114, destinationLineNo, sourceLineNo, errInfo);
                handleCode = ResultCdConstants.CODE_190113;
                // 排他制御用データ削除
                handleCode = await this.deleteLockRecord(apiHandleId, handleCode, param);
                // SO管理共通
                await this.soManagement(param, apiHandleId, handleCode, receivedDate, functionType,
                    sourceLineNo, destinationLineNo);
                // 返却値編集
                return this.returnEdit(param, handleCode, apiHandleId, receivedDate);
            }
            // STEP22.0版対応　追加　START
            // セカンダリTPCへSOAP連携
            if (tpcInfo[2] != null) {
                // セカンダリTPCが設定されている場合は以下の処理を行う(電文はプライマリと同じものを使用)
                try {
                    giftSoapResult
                        = await this.callSoapGift(sourceLineNo, destinationLineNo, updateSrcBucket.toString(),
                        updateDestBucket.toString(), tenantId, sequenceNo, tpcInfo[2],
                        endOfMonth, false);

                    // 変数「データ更新結果」より判定する
                    giftResultDoc = giftSoapResult.getDoc();
                    if (!ResultCdConstants.CODE_000000.equals(giftSoapResult.getProcessCode())) {
                        let errInfo: string = "";
                        if (ResultCdConstants.CODE_000951.equals(giftSoapResult.getProcessCode())) {
                            // SG取得でNGの場合
                            errInfo = "SG取得NG";
                        } else {
                            errInfo = this.soapCommon.getNodeContent(giftResultDoc, "//ErrorInfomation");
                        }
                        // ログ出力
                        super.warn(tenantId, sequenceNo, MsgKeysConstants.APLDGW0118, destinationLineNo,
                            sourceLineNo, errInfo);
                    }
                } catch (e) {
                    if (SOAPException.isSOAPException(e)) {
                        // ログ出力
                        super.warn(e, tenantId, sequenceNo, MsgKeysConstants.APLDGW0118, destinationLineNo,
                            sourceLineNo, e.toString());
                    } else {
                        throw e;
                    }
                }
            }
            // STEP22.0版対応　追加　END

            // 正常終了
            // 排他制御用データ削除
            handleCode = await this.deleteLockRecord(apiHandleId, handleCode, param);
            // SO管理共通
            await this.soManagement(param, apiHandleId, handleCode, receivedDate, functionType, sourceLineNo, destinationLineNo);
            // 返却値編集
            return this.returnEdit(param, handleCode, apiHandleId, receivedDate);
            // STEP20.0版対応　追加　START
        } catch (e) {
            if (isSQLException(e)) {
                // DBアクセスリトライエラーの場合
                // リターンコード値は各エラー時にて設定済み
                super.error(e, tenantId, sequenceNo, MsgKeysConstants.APCOME0402, functionType);
                // 排他制御用データ削除
                handleCode = await this.deleteLockRecord(apiHandleId, handleCode, param);
                // SO管理共通
                await this.soManagement(param, apiHandleId, handleCode, receivedDate, functionType, sourceLineNo, destinationLineNo);
                // 返却値編集
                return this.returnEdit(param, handleCode, apiHandleId, receivedDate);
            } else {
                super.error(e, param.tenantId, param.requestHeader.sequenceNo, MsgKeysConstants.APCOME0401,
                    e.message);
                handleCode = ResultCdConstants.CODE_999999;
                // 排他制御用データ削除
                handleCode = await this.deleteLockRecord(apiHandleId, handleCode, param);
                // SO管理共通
                await this.soManagement(param, apiHandleId, handleCode, receivedDate, functionType, sourceLineNo, destinationLineNo);
                // 返却値編集
                return this.returnEdit(param, handleCode, apiHandleId, receivedDate);
            }
        }
    }

    /**
     * 容量の減算<BR>
     * @param param 回線データ譲渡機能インプット
     * @param apiHandleId API処理ID
     * @param receivedDate REST API共通処理が呼び出されたシステム日付
     * @param functionType 機能種別
     * @return 処理結果
     * @throws Exception 想定外エラー
     */
    private async doSubtract(
        param: LineDataGiftInputDto,
        receivedDate: string,
        apiHandleId: string,
        functionType: string
    ): Promise<LineDataGiftOutputDto> {
        /** 送信番号取得 */
        const sequenceNo: string = param.requestHeader.sequenceNo;
        /** テナントID取得 */
        const tenantId: string = param.tenantId;
        /** 譲渡元回線番号取得 */
        const sourceLineNo: string = param.sourceLineNo;
        /** 譲渡先回線番号取得 */
        const destinationLineNo: string = param.destinationLineNo;
        /** 譲渡データ量取得 */
        const giftData: string = getStringParameter(param.giftData);
        /** 変数「処理コード」初期化 */
        let handleCode: string = ResultCdConstants.CODE_000000;

        // 電文中の譲渡元回線番号とテナントIDの関係が有効であることを確認する。
        // 変数「譲渡元回線関係チェック結果」より判定する
        // STEP20.0版対応　変更　START
        let tenantCheckResult: [boolean, string] = null;
        try {
            tenantCheckResult = await this.tenantManage.doCheck(sourceLineNo, tenantId);
        } catch (e) {
            if (isSQLException(e)) {
                // DBアクセスリトライエラーの場合
                super.error(e, tenantId, sequenceNo, MsgKeysConstants.APCOME0402, functionType);
                handleCode = ResultCdConstants.CODE_190201;
                // SO管理共通
                await this.soManagement(param, apiHandleId, handleCode, receivedDate, functionType,
                    sourceLineNo, destinationLineNo);
                // 返却値編集
                return this.returnEdit(param, handleCode, apiHandleId, receivedDate);
            }
            throw e;
        }
        if (!tenantCheckResult[0]) {
            // STEP20.0版対応　変更　END
            super.warn(tenantId, sequenceNo, MsgKeysConstants.APLDGW0201, sourceLineNo, tenantId);
            handleCode = ResultCdConstants.CODE_190201;
            // SO管理共通
            await this.soManagement(param, apiHandleId, handleCode, receivedDate, functionType, sourceLineNo, destinationLineNo);
            // 返却値編集
            return this.returnEdit(param, handleCode, apiHandleId, receivedDate);
        }

        // 廃止オーダチェック

        // 譲渡元回線番号の廃止オーダが受付中でないかを確認する。
        // 変数「譲渡元回線廃止オーダチェック結果」より判定する
        // STEP20.0版対応　変更　START
        let checkAbolishSoResult: boolean;
        try {
            checkAbolishSoResult = await this.apiCommon.checkAbolishSo(sourceLineNo, this.dateFmt(receivedDate, "yyyyMMdd"));
        } catch (e) {
            if (isSQLException(e)) {
                // DBアクセスリトライエラーの場合
                super.error(e, tenantId, sequenceNo, MsgKeysConstants.APCOME0402, functionType);
                handleCode = ResultCdConstants.CODE_190202;
                // SO管理共通
                await this.soManagement(param, apiHandleId, handleCode, receivedDate, functionType,
                    sourceLineNo, destinationLineNo);
                // 返却値編集
                return this.returnEdit(param, handleCode, apiHandleId, receivedDate);
            }
            throw e;
        }
        if (!checkAbolishSoResult) {
            // STEP20.0版対応　変更　END
            super.warn(tenantId, sequenceNo, MsgKeysConstants.APLDGW0202, sourceLineNo);
            handleCode = ResultCdConstants.CODE_190202;
            // SO管理共通
            await this.soManagement(param, apiHandleId, handleCode, receivedDate, functionType, sourceLineNo, destinationLineNo);
            // 返却値編集
            return this.returnEdit(param, handleCode, apiHandleId, receivedDate);
        }

        // STEP15.0版対応　追加　START
        // 利用状態チェック

        // 譲渡元回線番号の利用状態がサスペンド中でないかを確認する。
        // 変数「利用状態判定結果」より判定する
        // STEP20.0版対応　変更　START
        let checkLineSuspendResult: boolean;
        try {
            checkLineSuspendResult = await this.apiCommon.checkLineSuspend(sourceLineNo);
        } catch (e) {
            if (isSQLException(e)) {
                // DBアクセスリトライエラーの場合
                super.error(e, tenantId, sequenceNo, MsgKeysConstants.APCOME0402, functionType);
                handleCode = ResultCdConstants.CODE_190211;
                // SO管理共通
                await this.soManagement(param, apiHandleId, handleCode, receivedDate, functionType,
                    sourceLineNo, destinationLineNo);
                // 返却値編集
                return this.returnEdit(param, handleCode, apiHandleId, receivedDate);
            }
            throw e;
        }
        if (!checkLineSuspendResult) {
            // STEP20.0版対応　変更　END
            super.warn(tenantId, sequenceNo, MsgKeysConstants.APLDGW0211, sourceLineNo);
            handleCode = ResultCdConstants.CODE_190211;
            // SO管理共通
            await this.soManagement(param, apiHandleId, handleCode, receivedDate, functionType, sourceLineNo, destinationLineNo);
            // 返却値編集
            return this.returnEdit(param, handleCode, apiHandleId, receivedDate);
        }
        // STEP15.0版対応　追加　END

        // STEP20.0版対応　変更　START
        const retryResult = await retryQuery(
            this.context,
            "doSubtract",
            async() => {
                const sequelize = await usePsql();
                let tx = await sequelize.transaction();
                try {
                    // データ譲渡回線テーブルに譲渡元回線番号が存在するか確認する
                    let dupSrcLineId: string = null;
                    try {
                        dupSrcLineId = await this.apiLinesDAO.getGiftLine(sourceLineNo, tx);
                    } catch (e) {
                        if (isSQLException(e)) {
                            // DBアクセスリトライエラーの場合
                            tx = null;
                            handleCode = ResultCdConstants.CODE_190203;
                        }
                        throw e;
                    }
                    // 変数「登録済譲渡元回線ID」の有無を判定する
                    if (!CheckUtil.checkIsNotNull(dupSrcLineId)) {
                        await tx.rollback();
                        tx = null;
                        super.warn(tenantId, sequenceNo, MsgKeysConstants.APLDGW0203, sourceLineNo);
                        handleCode = ResultCdConstants.CODE_190203;
                        // SO管理共通
                        await this.soManagement(param, apiHandleId, handleCode, receivedDate, functionType,
                            sourceLineNo, destinationLineNo);
                        // 返却値編集
                        return this.returnEdit(param, handleCode, apiHandleId, receivedDate);
                    }

                    // データ譲渡回線テーブルに対して譲渡元回線IDを追加する
                    let insGiftSrcLineDup: boolean;
                    try {
                        insGiftSrcLineDup
                            = await this.apiLinesDAO.insertGiftLineNotDup(sourceLineNo, apiHandleId,
                            MvnoUtil.convDateFormat(receivedDate), tx);
                    } catch (e) {
                        if (isSQLException(e)) {
                            // DBアクセスリトライエラーの場合
                            tx = null;
                            handleCode = ResultCdConstants.CODE_190204;
                        }
                        throw e;
                    }
                    if (!insGiftSrcLineDup) {
                        await tx.rollback();
                        tx = null;
                        super.warn(tenantId, sequenceNo, MsgKeysConstants.APLDGW0204, sourceLineNo);
                        handleCode = ResultCdConstants.CODE_190204;
                        // SO管理共通
                        await this.soManagement(param, apiHandleId, handleCode, receivedDate, functionType,
                            sourceLineNo, destinationLineNo);
                        // 返却値編集
                        return this.returnEdit(param, handleCode, apiHandleId, receivedDate);
                    }
                    // トランザクション終了
                    await tx.commit();
                    tx = null;

                    // リトライしたタイミングでエラーコードを設定しているので
                    // リトライ後に正常となった場合は正常コードに戻す
                    handleCode = ResultCdConstants.CODE_000000;
                } catch (e: any) {
                    if (isSQLException(e)) {
                        throw e;
                    }
                    super.error(e, param.tenantId, param.requestHeader.sequenceNo, MsgKeysConstants.APCOME0401,
                        e.message);
                    handleCode = ResultCdConstants.CODE_999999;
                    if (tx) {
                        await tx.rollback();
                    }
                    // SO管理共通
                    await this.soManagement(param, apiHandleId, handleCode, receivedDate, functionType, sourceLineNo, destinationLineNo);
                    // 返却値編集
                    return this.returnEdit(param, handleCode, apiHandleId, receivedDate);
                }
            },
            this.apDbRetryMaxCnt,
            this.apDbRetryInterval
        )
        if (retryResult) {
            return retryResult;
        }

        try {
            // TPC情報を取得する
            // STEP20.0版対応　変更　START
            let tpcInfo: [boolean, string, string] = null;
            try {
                tpcInfo = await this.tenantManage.checkTpcConnection(tenantId);
            } catch (e) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    handleCode = ResultCdConstants.CODE_190205;
                }
                throw e;
            }
            // STEP20.0版対応　変更　END
            if (!tpcInfo[0]) {
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APLDGW0205, tenantId);
                handleCode = ResultCdConstants.CODE_190205;
                // 排他制御用データ削除
                handleCode = await this.deleteLockRecord(apiHandleId, handleCode, param);
                // SO管理共通
                await this.soManagement(param, apiHandleId, handleCode, receivedDate, functionType,
                    sourceLineNo, destinationLineNo);
                // 返却値編集
                return this.returnEdit(param, handleCode, apiHandleId, receivedDate);
            }
            // SOAP-API電文送受信（譲渡元回線番号）
            // SOAP  APIにより、譲渡元回線のTPCデータを取得する
            let srcLineTpcInfo: SOAPCommonOutputDto;
            try {
                srcLineTpcInfo = await this.callSoapAllView(sourceLineNo, tenantId, sequenceNo, tpcInfo[1]);
            } catch (e) {
                if (SOAPException.isSOAPException(e)) {
                    super.warn(tenantId, sequenceNo, MsgKeysConstants.APLDGW0206, sourceLineNo, "");
                    handleCode = ResultCdConstants.CODE_190206;
                    // 排他制御用データ削除
                    handleCode = await this.deleteLockRecord(apiHandleId, handleCode, param);
                    // SO管理共通
                    await this.soManagement(param, apiHandleId, handleCode, receivedDate, functionType,
                        sourceLineNo, destinationLineNo);
                    // 返却値編集
                    return this.returnEdit(param, handleCode, apiHandleId, receivedDate);
                }
                throw e;
            }
            // 変数「譲渡元回線TPCデータ」より判定する
            const srcLineDoc: Document = srcLineTpcInfo.getDoc();

            if (!ResultCdConstants.CODE_000000.equals(srcLineTpcInfo.getProcessCode())) {
                let errInfo: string = "";
                if (srcLineDoc != null) {
                    errInfo = this.soapCommon.getNodeContent(srcLineDoc, "//ErrorInfomation");
                }
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APLDGW0206, sourceLineNo, errInfo);
                handleCode = ResultCdConstants.CODE_190206;
                // 排他制御用データ削除
                handleCode = await this.deleteLockRecord(apiHandleId, handleCode, param);
                // SO管理共通
                await this.soManagement(param, apiHandleId, handleCode, receivedDate, functionType,
                    sourceLineNo, destinationLineNo);
                // 返却値編集
                return this.returnEdit(param, handleCode, apiHandleId, receivedDate);
            }
            // 変数「譲渡元回線TPCデータ」より、データを取得し設定する
            let basicCapStr: string = this.soapCommon.getNodeContent(
                srcLineDoc, "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_month_info/dyn_cap");
            let basicUseStr: string = this.soapCommon.getNodeContent(
                srcLineDoc, "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_month_info/dyn_use");
            let giftCap1Str: string = this.soapCommon.getNodeContent(
                srcLineDoc, "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_tms_info_1/dyn_cap");
            const giftExpDate1: string = this.soapCommon.getNodeContent(
                srcLineDoc, "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_tms_info_1/dyn_exp_date");

            // 取得した値が空文字(SOAPCommonにてnullは空に変換されている)の場合は0を設定する
            if ("".equals(basicCapStr)) {
                basicCapStr = "0";
            }
            if ("".equals(basicUseStr)) {
                basicUseStr = "0";
            }
            if ("".equals(giftCap1Str)) {
                giftCap1Str = "0";
            }
            // データ取得項目のフォーマットを確認する。
            const tpcDataResult: TpcDataCheckResultDto = Check.checkLineGiftTpcData(
                basicCapStr, basicUseStr, giftCap1Str, giftExpDate1, null, null);

            // 変数「データ譲渡TPC取得情報フォーマットチェック結果」より判定する
            if (!tpcDataResult.getResult()) {
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APLDGW0207,
                    tpcDataResult.getErrName(), tpcDataResult.getErrVal());
                handleCode = ResultCdConstants.CODE_190207;
                // 排他制御用データ削除
                handleCode = await this.deleteLockRecord(apiHandleId, handleCode, param);
                // SO管理共通
                await this.soManagement(param, apiHandleId, handleCode, receivedDate, functionType,
                    sourceLineNo, destinationLineNo);
                // 返却値編集
                return this.returnEdit(param, handleCode, apiHandleId, receivedDate);
            }

            // 取得データを数値に変換する
            const basicCap = new BigNumber(basicCapStr);
            const basicUse = new BigNumber(basicUseStr);
            const giftCap1 = new BigNumber(giftCap1Str);

            // 譲渡元回線残容量チェック
            // 譲渡bucket1有効期限が月末であるかを確認する
            let srcRemain: BigNumber = null;
            const isEndOfMonthBucket1: boolean = Check.checkIsEndOfMonth(giftExpDate1, this.dateFmt(receivedDate, "yyyy/MM/dd"));
            // 譲渡元bucket残容量を算出する
            if (isEndOfMonthBucket1) {
                srcRemain = basicCap.minus(basicUse).plus(giftCap1);
            } else {
                srcRemain = basicCap.minus(basicUse);
            }
            // データ譲渡量をByte単位に変換する
            const bytes: BigNumber = new BigNumber(1024);
            const giftDataByte: BigNumber = (new BigNumber(giftData)).times(bytes).times(bytes);

            // 残容量と譲渡量を比較する
            if (srcRemain.comparedTo(giftDataByte) < 0) {
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APLDGW0208,
                    giftDataByte.toString(), srcRemain.toString());
                handleCode = ResultCdConstants.CODE_190208;
                // 排他制御用データ削除
                handleCode = await this.deleteLockRecord(apiHandleId, handleCode, param);
                // SO管理共通
                await this.soManagement(param, apiHandleId, handleCode, receivedDate, functionType,
                    sourceLineNo, destinationLineNo);
                // 返却値編集
                return this.returnEdit(param, handleCode, apiHandleId, receivedDate);
            }

            // 更新値計算
            // 譲渡元回線の更新値を計算する
            let updateSrcBucket: BigNumber = null;
            if (isEndOfMonthBucket1) {
                updateSrcBucket = (giftCap1.minus(giftDataByte)).dividedToIntegerBy(bytes.pow(2));
            } else {
                updateSrcBucket = giftDataByte.negated().dividedToIntegerBy(bytes.pow(2));
            }

            // 月末日の取得
            const endOfMonth: string = Check.getEndOfMonth(this.dateFmt(receivedDate, "yyyy/MM/dd"));

            // SOAP電文の送信
            let giftSoapResult: SOAPCommonOutputDto;
            try {
                // STEP22.0版対応　変更　START
                giftSoapResult
                    = await this.callSoapSubtract(sourceLineNo, updateSrcBucket.toString(), tenantId, sequenceNo, tpcInfo[1],
                    endOfMonth, true);
                // STEP22.0版対応　変更　START
            } catch (e) {
                if (SOAPException.isSOAPException(e)) {
                    super.warn(tenantId, sequenceNo, MsgKeysConstants.APLDGW0209, sourceLineNo, "");
                    handleCode = ResultCdConstants.CODE_190209;
                    // 排他制御用データ削除
                    handleCode = await this.deleteLockRecord(apiHandleId, handleCode, param);
                    // SO管理共通
                    await this.soManagement(param, apiHandleId, handleCode, receivedDate, functionType,
                        sourceLineNo, destinationLineNo);
                    // 返却値編集
                    return this.returnEdit(param, handleCode, apiHandleId, receivedDate);
                }
                throw e;
            }
            // 変数「データ更新結果」より判定する
            let giftResultDoc: Document = giftSoapResult.getDoc();
            if (!ResultCdConstants.CODE_000000.equals(giftSoapResult.getProcessCode())) {
                let errInfo: string = "";
                if (giftResultDoc != null) {
                    errInfo = this.soapCommon.getNodeContent(giftResultDoc, "//ErrorInfomation");
                }
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APLDGW0209, sourceLineNo, errInfo);
                handleCode = ResultCdConstants.CODE_190209;
                // 排他制御用データ削除
                handleCode = await this.deleteLockRecord(apiHandleId, handleCode, param);
                // SO管理共通
                await this.soManagement(param, apiHandleId, handleCode, receivedDate, functionType,
                    sourceLineNo, destinationLineNo);
                // 返却値編集
                return this.returnEdit(param, handleCode, apiHandleId, receivedDate);
            }

            // STEP22.0版対応　追加　START
            // セカンダリTPCへSOAP連携
            if (tpcInfo[2] != null) {
                try {
                    giftSoapResult
                        = await this.callSoapSubtract(sourceLineNo, updateSrcBucket.toString(), tenantId, sequenceNo, tpcInfo[2],
                        endOfMonth, false);

                    // 変数「データ更新結果」より判定する
                    giftResultDoc = giftSoapResult.getDoc();
                    if (!ResultCdConstants.CODE_000000.equals(giftSoapResult.getProcessCode())) {
                        let errInfo: string = "";
                        if (ResultCdConstants.CODE_000951.equals(giftSoapResult.getProcessCode())) {
                            // SG取得でNGの場合
                            errInfo = "SG取得NG";
                        } else {
                            errInfo = this.soapCommon.getNodeContent(giftResultDoc, "//ErrorInfomation");
                        }
                        // ログ出力
                        super.warn(tenantId, sequenceNo, MsgKeysConstants.APLDGW0212, sourceLineNo, errInfo);
                    }
                } catch (e) {
                    if (SOAPException.isSOAPException(e)) {
                        // ログ出力
                        super.warn(e, tenantId, sequenceNo, MsgKeysConstants.APLDGW0212, sourceLineNo, e.toString());
                    } else {
                        throw e;
                    }
                }
            }
            // STEP22.0版対応　追加　END

            // 正常終了
            // 排他制御用データ削除
            handleCode = await this.deleteLockRecord(apiHandleId, handleCode, param);
            // SO管理共通
            await this.soManagement(param, apiHandleId, handleCode, receivedDate, functionType, sourceLineNo, destinationLineNo);
            // 返却値編集
            return this.returnEdit(param, handleCode, apiHandleId, receivedDate);

            // STEP20.0版対応　追加　START
        } catch (e) {
            if (isSQLException(e)) {
                // DBアクセスリトライエラーの場合
                // リターンコード値は各エラー時にて設定済み
                super.error(e, tenantId, sequenceNo, MsgKeysConstants.APCOME0402, functionType);
                // 排他制御用データ削除
                handleCode = await this.deleteLockRecord(apiHandleId, handleCode, param);
                // SO管理共通
                await this.soManagement(param, apiHandleId, handleCode, receivedDate, functionType, sourceLineNo, destinationLineNo);
                // 返却値編集
                return this.returnEdit(param, handleCode, apiHandleId, receivedDate);
            } else {
                super.error(e, param.tenantId, param.requestHeader.sequenceNo, MsgKeysConstants.APCOME0401,
                    e.message);
                handleCode = ResultCdConstants.CODE_999999;
                // 排他制御用データ削除
                handleCode = await this.deleteLockRecord(apiHandleId, handleCode, param);
                // SO管理共通
                await this.soManagement(param, apiHandleId, handleCode, receivedDate, functionType, sourceLineNo, destinationLineNo);
                // 返却値編集
                return this.returnEdit(param, handleCode, apiHandleId, receivedDate);
            }
        }
    }

    /**
     * SOAP送受信処理(AllView)
     * @param lineNo 回線番号
     * @param tenantId テナントID
     * @param sequenceNo 送信番号
     * @param tpcConInfo TPC接続情報
     * @return SOAPレスポンスDto
     * @throws Exception 実行時エラー
     */
    private async callSoapAllView(
        lineNo: string,
        tenantId: string,
        sequenceNo: string,
        tpcConInfo: string
    ): Promise<SOAPCommonOutputDto> {
        const paraList: ParameterName[] = [];
        const parameterName = new ParameterName();
        parameterName.name = "msisdn";
        const lineNoNew = "81" + lineNo.substring(1);
        parameterName.value = lineNoNew;
        paraList.push(parameterName);
        return this.soapCommon.callWebSoapApi("serviceProfileQuery", "AllView", "",
            paraList, tenantId, sequenceNo, tpcConInfo);
    }

    /**
     * SOAP送受信処理(容量の譲渡)。
     *
     * @param sourceLineNo 譲渡元回線番号
     * @param destLineNo 譲渡先回線番号
     * @param sourceCap 譲渡先更新容量
     * @param destCap 譲渡元更新容量
     * @param tenantId テナントID
     * @param sequenceNo 送信番号
     * @param tpcConInfo TPC接続情報
     * @param endOfMonth 月末日
     * @param primaryFlg プライマリフラグ
     * @return SOAPレスポンスDto
     * @throws Exception 実行時エラー
     */
    // STEP22.0版対応　変更　START
    private async callSoapGift(
        sourceLineNo: string,
        destLineNo: string,
        sourceCap: string,
        destCap: string,
        tenantId: string,
        sequenceNo: string,
        tpcConInfo: string,
        endOfMonth: string,
        primaryFlg: boolean
    ) {
        // STEP22.0版対応　変更　END

        // STEP10.0版対応　追加　START
        let retSoapApiResult: SOAPCommonOutputDto;
        // STEP10.0版対応　追加　END
        const eom = parse(endOfMonth, "yyyy/MM/dd", new Date());
        const expDate = format(eom, "'407'yyMMdd");

        const paramNameList1: ParameterName[] = [];
        // STEP10.0版対応　追加　START
        if (!destLineNo.equals(LineDataGiftServiceTx.THIRD_PARTY_LINENO)) {
        // STEP10.0版対応　追加　END
            const param1 = new ParameterName();
            param1.name = "pronum";
            // STEP17.0版対応　変更　START
            param1.value = "203" + MvnoUtil.addZeroBeforeStr(destLineNo, 15);
            // STEP17.0版対応　変更　END
            const param2 = new ParameterName();
            param2.name = "cap_gft0";
            param2.value = destCap;
            const param3 = new ParameterName();
            param3.name = "exp_date0";
            param3.value = expDate;
            paramNameList1.push(param1);
            paramNameList1.push(param2);
            paramNameList1.push(param3);
        }

        const paramNameList2: ParameterName[] = [];
        // STEP10.0版対応　追加　START
        if (!sourceLineNo.equals(LineDataGiftServiceTx.THIRD_PARTY_LINENO)) {
        // STEP10.0版対応　追加　END
            const param4 = new ParameterName();
            param4.name = "pronum";
            // STEP17.0版対応　変更　START
            param4.value = "203" + MvnoUtil.addZeroBeforeStr(sourceLineNo, 15);
            // STEP17.0版対応　変更　END
            const param5 = new ParameterName();
            param5.name = "cap_gft1";
            param5.value = sourceCap;
            const param6 = new ParameterName();
            param6.name = "exp_date1";
            param6.value = expDate;
            paramNameList2.push(param4);
            paramNameList2.push(param5);
            paramNameList2.push(param6);
        }

        // STEP10.0版対応　変更　START
        if (!destLineNo.equals(LineDataGiftServiceTx.THIRD_PARTY_LINENO) && !sourceLineNo.equals(LineDataGiftServiceTx.THIRD_PARTY_LINENO)) {
            // STEP22.0版対応　変更　START
            retSoapApiResult = await this.soapCommonCoupon.callWebSoapApi("serviceProfileRequest", "Mod", "Service",
                paramNameList1, "Mod", "Service", paramNameList2, tenantId,
                sequenceNo, tpcConInfo, primaryFlg);
            // STEP22.0版対応　変更　END
        } else if (!destLineNo.equals(LineDataGiftServiceTx.THIRD_PARTY_LINENO)) {
            // STEP22.0版対応　変更　START
            retSoapApiResult = await this.soapCommon.callWebSoapApi("serviceProfileRequest", "Mod", "Service",
                paramNameList1, tenantId, sequenceNo, tpcConInfo, primaryFlg);
            // STEP22.0版対応　変更　END
        } else {
            // STEP22.0版対応　変更　START
            retSoapApiResult = await this.soapCommon.callWebSoapApi("serviceProfileRequest", "Mod", "Service",
                paramNameList2, tenantId, sequenceNo, tpcConInfo, primaryFlg);
            // STEP22.0版対応　変更　END
        }
        return retSoapApiResult;
        // STEP10.0版対応　変更　END
    }

    /**
     * SOAP送受信処理(容量の減算)。
     *
     * @param sourceLineNo 送信元回線番号
     * @param sourceCap 送信元更新容量
     * @param tenantId テナントID
     * @param sequenceNo 送信番号
     * @param tpcConInfo TPC接続情報
     * @param endOfMonth 月末日
     * @param primaryFlg プライマリフラグ
     * @return SOAPレスポンスDTO
     * @throws Exception 実行時例外
     */
    // STEP22.0版対応　変更　START
    private async callSoapSubtract(
        sourceLineNo: string,
        sourceCap: string,
        tenantId: string,
        sequenceNo: string,
        tpcConInfo: string,
        endOfMonth: string,
        primaryFlg: boolean
    ) {
        // STEP22.0版対応　変更　END
        const eom = parse(endOfMonth, "yyyy/MM/dd", new Date());
        const expDate = format(eom, "'407'yyMMdd");

        const paramNameList: ParameterName[] = [];
        const param1 = new ParameterName();
        param1.name = "pronum";
        // STEP17.0版対応　変更　START
        param1.value = "203" + MvnoUtil.addZeroBeforeStr(sourceLineNo, 15);
        // STEP17.0版対応　変更　END
        const param2 = new ParameterName();
        param2.name = "cap_gft1";
        param2.value = sourceCap;
        const param3 = new ParameterName();
        param3.name = "exp_date1";
        param3.value = expDate;
        paramNameList.push(param1);
        paramNameList.push(param2);
        paramNameList.push(param3);

        // STEP22.0版対応　変更　START
        return this.soapCommon.callWebSoapApi("serviceProfileRequest", "Mod", "Service", paramNameList,
            tenantId, sequenceNo, tpcConInfo, primaryFlg);
        // STEP22.0版対応　変更　END
    }

    /**
     * 回線データ譲渡機能返却値編集
     *
     * @param param 回線データ譲渡機能インプット
     * @param handleCode 処理コード
     * @param apiHandleId API処理ID
     * @param receivedDate REST API共通処理が呼び出されたシステム日付
     * @return LineDataGiftOutputDto 回線データ譲渡機能アウトプット
     * @throws ParseException フォーマットエラー
     */
    public returnEdit(
        param: LineDataGiftInputDto,
        handleCode: string,
        apiHandleId: string,
        receivedDate: string
    ): LineDataGiftOutputDto {
        return {
            jsonBody: {
                responseHeader: {
                    // 送信番号
                    sequenceNo: param.requestHeader.sequenceNo,
                    // 受信日時
                    receivedDate: this.dateFmt(receivedDate, "yyyy/MM/dd HH:mm:ss"),
                    // 処理コード
                    processCode: handleCode,
                    // API処理ID
                    apiProcessID: apiHandleId,
                },
            },
        };
    }

    /**
     * SO管理共通パラメータ設定
     *
     * @param param パラメータ
     * @param apiProcessID API処理ID  // NOTE changed from: resultBean チェック結果 (`resultBean.getOthers()`)
     * @param code オーダステータス
     * @param receivedDate REST API共通処理が呼び出されたシステム日付
     * @param functionType 機能種別
     * @param srcLineNo 譲渡元回線番号
     * @param destLineNo 譲渡先回線番号
     * @throws Exception 例外
     */
    public async soManagement(
        param: LineDataGiftInputDto,
        apiProcessID: string,
        code: string,
        receivedDate: string,
        functionType: string,
        srcLineNo: string,
        destLineNo: string
    ) {
        // SO管理共通を呼び出す
        const soObject = new SOObject();

        // サービスオーダID
        soObject.setServiceOrderId(apiProcessID);
        // 投入日
        soObject.setOrderDate(this.dateFmt(receivedDate, "yyyy/MM/dd HH:mm:ss"));
        // 予約日
        soObject.setReserveDate(null);
        // オーダ種別
        soObject.setOrderType(null);
        // 完了日
        soObject.setExecDate(MvnoUtil.getDateTimeNow());
        // オーダステータス
        soObject.setOrderStatus(code);
        // 回線ID
        soObject.setLineId(param.sourceLineNo);
        // 回線グループID
        soObject.setLineGroupId(null);
        // 所属テナントID
        soObject.setTenantId(param.tenantId);
        // 実行ユーザID
        soObject.setExecuteUserId(null);
        // 実行テナントID
        soObject.setExecuteTenantId(null);
        // 機能種別
        soObject.setFunctionType(functionType);
        // 操作区分
        soObject.setOperationDivision("");
        // 変更前プランID
        soObject.setChangeOldplanId("");
        // 変更後プランID
        soObject.setChangeNewplanId("");
        // 回線所属変更回線ID
        soObject.setChangePlanId("");
        // 基本容量
        soObject.setBasicCapacity("");
        // オプションプランID
        soObject.setOptionPlanId("");
        // 譲渡先回線番号
        soObject.setDestinationLineNo(destLineNo);
        // 譲渡データ量
        soObject.setGiftData(getStringParameter(param.giftData));
        // REST電文
        soObject.setRestMessage(null);

        await this.soCommon.soCommon(soObject);
    }

    /**
     * 排他制御用データ削除処理
     * @param apiHandleId api処理ID
     * @param handleCode 処理コード
     * @param param 入力パラメータ
     * @return 削除に成功：引数の処理コード 失敗：削除失敗時の処理コード
     */
    private async deleteLockRecord(apiHandleId: string, handleCode: string, param: LineDataGiftInputDto) {
        const sequelize = await usePsql();
        const tenantId: string = param.tenantId;
        const sequenceNo: string = param.requestHeader.sequenceNo;
        const sourceLineNo: string = param.sourceLineNo;
        const destinationLineNo: string = param.destinationLineNo;

        // 排他制御用データ削除
        return await retryQuery(
            this.context,
            "LineDataGiftServiceTx.deleteLockRecord",
            async () => {
                try {
                    let tx = await sequelize.transaction();
                    try {
                        await this.apiLinesDAO.deleteGiftLine(apiHandleId, tx);
                        await tx.commit();
                        tx = null;
                        return handleCode;
                    } catch (e) {
                        if (isSQLException(e)) {
                            // DBアクセスリトライエラーの場合
                            throw e;
                        } else {
                            if (!sourceLineNo.equals(destinationLineNo)) {
                                super.warn(e, tenantId, sequenceNo, MsgKeysConstants.APLDGW0115,
                                    sourceLineNo, destinationLineNo);
                                return ResultCdConstants.CODE_190114;
                            } else {
                                super.warn(e, tenantId, sequenceNo, MsgKeysConstants.APLDGW0210,
                                    sourceLineNo);
                                return ResultCdConstants.CODE_190210;
                            }
                        }
                    } finally {
                        if (tx) {
                            await tx.rollback();
                        }
                    }
                } catch (ex) {
                    if (isSQLException(ex)) {
                        if (!sourceLineNo.equals(destinationLineNo)) {
                            return ResultCdConstants.CODE_190114;
                        } else {
                            return ResultCdConstants.CODE_190210;
                        }
                    }
                    throw ex;
                }
            },
            this.apDbRetryMaxCnt,
            this.apDbRetryInterval
        );
    }


    /**
     * システム日付のフォーマット変換
     * @param sysDate システム時刻
     * @param formatString フォーマット形式
     * @return フォーマット済み文字列
     * @throws ParseException フォーマットエラー
     */
    private dateFmt(sysDate: string, formatString: string): string {
        const parsedDate = parse(sysDate, "yyyy/MM/dd HH:mm:ss", new Date());           // trim .SSS as this.context.responseHeader.receivedDate does not have milliseconds
        return format(parsedDate, formatString);
    }
}