import RESTCommon from "@/core/common/RESTCommon";
import { IFService } from "@/core/service/IFService";
import SODetailInfoConsultInputDto from "@/core/dto/SODetailInfoConsultInputDto";
import SODetailInfoConsultOutputDto from "@/core/dto/SODetailInfoConsultOutputDto";
import APISoDAO from "@/core/dao/APISoDAO";
import MvnoUtil from "@/core/common/MvnoUtil";
import ResultCdConstants from "@/core/constant/ResultCdConstants";
import MsgKeysConstants from "@/core/constant/MsgKeysConstants";
import ServiceOrdersEntity from "@/core/entity/ServiceOrdersEntity";
import CheckUtil from "@/core/common/CheckUtil";
import { isSQLException } from "@/helpers/queryHelper";
import ResponseHeader from "@/core/dto/ResponseHeader";
import StringUtils from "@/core/common/StringUtils";
import { format } from "date-fns";

export default class SODetailInfoConsultService
    extends R<PERSON>TCommon
    implements IFService<SODetailInfoConsultInputDto, SODetailInfoConsultOutputDto> {

    /**
     * API共通DAO
     */
    private aPISoDAO = new APISoDAO(this.request, this.context);

    /**
     * SO詳細情報参照。<BR>
     *
     * <PRE>
     * REST APIにより、受信したパラメータのSO詳細情報参照を取得する。
     * </PRE>
     * @param param SO詳細情報参照Dto
     * @param isPrivate
     *            内部呼出フラグ
     * @param params
     *            可変長さ引数(内部から呼出す時に使用するパラメータ)
     * @return SODetailInfoConsultOutputDto SO詳細情報参照Dtoクラス
     * @throws Exception
     */
    async service(param: SODetailInfoConsultInputDto, ...params: any[]): Promise<SODetailInfoConsultOutputDto> {
        super.debug(param.tenantId, param.requestHeader.sequenceNo,
            MsgKeysConstants.APSODD0001, this.getClassName(), "service", JSON.stringify(param));
        // REST API共通処理が呼び出されたシステム日付
        const receivedDate: string = this.context?.responseHeader?.receivedDate ?? MvnoUtil.getDateTimeNow();
        /** 変数「処理コード」初期化 */
        let handleCode: string = ResultCdConstants.CODE_000000;
        /** 送信番号取得 */
        const sequenceNo: string = param.requestHeader.sequenceNo;
        /** 送信元システムID取得 */
        const senderSystemId: string = param.requestHeader.senderSystemId;
        /** API認証キー取得 */
        const apiKey: string = param.requestHeader.apiKey;
        /** 機能種別取得 */
        const functionType: string = param.requestHeader.functionType;
        /** テナントID取得 */
        const tenantId: string = param.tenantId;
        /** SO-ID */
        const serviceOrderIdKey: string = param.serviceOrderIdKey;
        /** SO詳細情報初期化 */
        let soDetailInfoConsultOutputDto: SODetailInfoConsultOutputDto;
        /** SO管理テーブル情報初期化 */
        let serviceOrdersEntity: ServiceOrdersEntity = null;
        /** テナント階層情報を取得 */
        let tenantLevelList: string[] = null;
        // 変数「API処理ID」
        const apiHandleId = this.context.responseHeader.apiProcessID;
        // 変数「エラーメッセージ」を定義する
        const errorMessage: string = null;

        try {
            // SO-IDの必須チェック
            if (CheckUtil.checkIsNotNull(serviceOrderIdKey)) {
                this.context.log(
                    "SODetailInfoConsultService: serviceOrderIdKey invalid (1)",
                    serviceOrderIdKey,
                );
                handleCode = ResultCdConstants.CODE_170101;
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APSODW0001);
                // 返却値編集
                soDetailInfoConsultOutputDto = this.returnEdit(param, handleCode, apiHandleId, errorMessage, tenantId,
                    sequenceNo, serviceOrdersEntity, tenantLevelList, receivedDate);
                return soDetailInfoConsultOutputDto;
            }
            // SO-IDの桁数チェック
            if (!CheckUtil.checkLength(serviceOrderIdKey, 15, false)) {
                this.context.log(
                    "SODetailInfoConsultService: serviceOrderIdKey invalid (2)",
                    serviceOrderIdKey,
                );
                handleCode = ResultCdConstants.CODE_170101;
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APSODW0001);
                // 返却値編集
                soDetailInfoConsultOutputDto = this.returnEdit(param, handleCode, apiHandleId, errorMessage, tenantId,
                    sequenceNo, serviceOrdersEntity, tenantLevelList, receivedDate);
                return soDetailInfoConsultOutputDto;
            }
            // SO-IDの英数字チェック
            if (!CheckUtil.checkIsSemiangleAlphanumic(serviceOrderIdKey)) {
                this.context.log(
                    "SODetailInfoConsultService: serviceOrderIdKey invalid (3)",
                    serviceOrderIdKey,
                );
                handleCode = ResultCdConstants.CODE_170101;
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APSODW0001);
                // 返却値編集
                soDetailInfoConsultOutputDto = this.returnEdit(param, handleCode, apiHandleId, errorMessage, tenantId,
                    sequenceNo, serviceOrdersEntity, tenantLevelList, receivedDate);
                return soDetailInfoConsultOutputDto;
            }
            // テナント階層情報を取得
            // STEP20.0版対応　変更　START
            try {
                tenantLevelList = await this.aPISoDAO.getTenantLevel(tenantId);
            } catch (e: any) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    handleCode = ResultCdConstants.CODE_170201;
                }
                throw e;
            }
            // STEP20.0版対応　変更　END
            if (tenantLevelList == null || tenantLevelList.length === 0) {
                handleCode = ResultCdConstants.CODE_170201;
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APSODW0002);
                // 返却値編集
                soDetailInfoConsultOutputDto = this.returnEdit(param, handleCode, apiHandleId, errorMessage, tenantId,
                    sequenceNo, serviceOrdersEntity, tenantLevelList, receivedDate);
                return soDetailInfoConsultOutputDto;
            }
            // SO詳細情報取得
            try {
                serviceOrdersEntity = await this.aPISoDAO.getSOInfo(serviceOrderIdKey, tenantLevelList);
                if (serviceOrdersEntity == null) {
                    this.context.log(
                        "SODetailInfoConsultService: serviceOrdersEntity not found",
                        serviceOrderIdKey,
                    );
                    handleCode = ResultCdConstants.CODE_170301;
                    super.warn(tenantId, sequenceNo, MsgKeysConstants.APSODW0003);
                    // 返却値編集
                    soDetailInfoConsultOutputDto = this.returnEdit(param, handleCode, apiHandleId, errorMessage, tenantId,
                        sequenceNo, serviceOrdersEntity, tenantLevelList, receivedDate);
                    return soDetailInfoConsultOutputDto;
                } else {
                    this.context.log(
                        "SODetailInfoConsultService: serviceOrdersEntity found",
                        serviceOrderIdKey,
                    );
                    // 返却値編集
                    soDetailInfoConsultOutputDto = this.returnEdit(param, handleCode, apiHandleId, errorMessage, tenantId,
                        sequenceNo, serviceOrdersEntity, tenantLevelList, receivedDate);
                    return soDetailInfoConsultOutputDto;
                }
                // 異常が発生する場合
                // STEP20.0版対応　追加　START
            } catch (e: any) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    handleCode = ResultCdConstants.CODE_170302;
                    throw e;
                    // STEP20.0版対応　追加　END
                } else {
                    handleCode = ResultCdConstants.CODE_170302;
                    super.warn(tenantId, sequenceNo, MsgKeysConstants.APSODW0003);
                    // 返却値編集
                    soDetailInfoConsultOutputDto = this.returnEdit(param, handleCode, apiHandleId, errorMessage, tenantId,
                        sequenceNo, serviceOrdersEntity, tenantLevelList, receivedDate);
                    return soDetailInfoConsultOutputDto;
                }
            }
        } catch (e: any) {
            if (isSQLException(e)) {
                // DBアクセスリトライエラーの場合
                // リターンコード値は各エラー時にて設定済み
                super.error(e, tenantId, sequenceNo, MsgKeysConstants.APCOME0402, functionType);
                // 返却値編集
                soDetailInfoConsultOutputDto
                    = this.returnEdit(param, handleCode, apiHandleId, errorMessage, tenantId,
                    sequenceNo, serviceOrdersEntity, tenantLevelList, receivedDate);
                return soDetailInfoConsultOutputDto;
                // STEP20.0版対応　追加　END
            } else {
                handleCode = ResultCdConstants.CODE_999999;
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APCOME0401, e.message);
                // 返却値編集
                soDetailInfoConsultOutputDto = this.returnEdit(param, handleCode, apiHandleId, errorMessage, tenantId,
                    sequenceNo, serviceOrdersEntity, tenantLevelList, receivedDate);
                return soDetailInfoConsultOutputDto;
            }
        }
    }

    /**
     * 返却値編集。<BR>
     *
     * @param pram SO詳細情報
     * @param rtnHandleCode 処理コード
     * @param rtnapiHandleId API処理ID
     * @param errorMessage エラーメッセージ
     * @param tenantId テナントID
     * @param isPrivate 内部呼出
     * @param sequenceNo 送信番号
     * @param serviceOrdersEntity SO管理テーブ
     * @param tenantLevelList テナント階層情報
     * @param receivedDate REST API共通処理が呼び出されたシステム日付
     * @return SODetailInfoConsultOutputDto SO詳細情報
     */
    public returnEdit(pram: SODetailInfoConsultInputDto, rtnHandleCode: string, rtnapiHandleId: string,
                      errorMessage: string, tenantId: string, sequenceNo: string, serviceOrdersEntity: ServiceOrdersEntity,
                      tenantLevelList: string[], receivedDate: string): SODetailInfoConsultOutputDto {
        const responseHeader: ResponseHeader = {
            sequenceNo: pram.requestHeader.sequenceNo,
            receivedDate,
            processCode: rtnHandleCode,
            apiProcessID: rtnapiHandleId,
        };
        let returnPram: SODetailInfoConsultOutputDto;
        // returnPram.setResponseHeader(responseHeader);
        // 変数「処理コード」=「000000」
        if (ResultCdConstants.CODE_000000 === rtnHandleCode) {
            // 投入日
            let order_date = "";
            if (serviceOrdersEntity.orderDate == null) {
                order_date = "";
            } else {
                order_date = format(serviceOrdersEntity.orderDate, "yyyy/MM/dd HH:mm:ss");
            }
            // 完了日
            let exec_date = "";
            if (serviceOrdersEntity.execDate == null) {
                exec_date = "";
            } else {
                exec_date = format(serviceOrdersEntity.execDate, "yyyy/MM/dd HH:mm:ss");
            }
            /** STEP1.2b版対応　追加　START */
            // 予約日
            let reserve_date = "";
            if (serviceOrdersEntity.reserveDate == null) {
                reserve_date = "";
            } else {
                reserve_date = format(serviceOrdersEntity.reserveDate, "yyyy/MM/dd HH:mm:ss");
            }
            returnPram = {
                jsonBody: {
                    responseHeader,
                    /* #337 対応 変更 START */
                    // SO-ID
                    serviceOrderId: StringUtils.trimToEmpty(serviceOrdersEntity.serviceOrderId),
                    /* #337 対応 変更 END */
                    order_date,
                    exec_date,
                    /** STEP1.2b版対応　追加　START */
                    reserve_date,
                    /** STEP1.2b版対応　追加　END */
                    /* #337 対応 変更 START */
                    // 種別
                    order_type: StringUtils.trimToEmpty(serviceOrdersEntity.orderType),
                    // ステータス
                    status: StringUtils.trimToEmpty(serviceOrdersEntity.orderStatus),
                    // 回線番号
                    lineNo: StringUtils.trimToEmpty(serviceOrdersEntity.lineId),
                    // 内容詳細
                    content: StringUtils.trimToEmpty(serviceOrdersEntity.content),
                    /* #337 対応 変更 END */
                },
            };
        } else {
            returnPram = {
                jsonBody: {
                    responseHeader,
                    // SO-ID
                    serviceOrderId: "",
                    // 投入日
                    order_date: "",
                    // 完了日
                    exec_date: "",
                    /** STEP1.2b版対応　追加　START */
                    reserve_date: "",
                    /** STEP1.2b版対応　追加　END */
                    // 種別
                    order_type: "",
                    // ステータス
                    status: "",
                    // 回線番号
                    lineNo: "",
                    // 内容詳細
                    content: "",
                },
            };
        }
        return returnPram;
    }
}