import RESTCommon from "@/core/common/RESTCommon";
import { IFService } from "@/core/service/IFService";
import LineGroupUseAcquisitionInputDto from "@/core/dto/LineGroupUseAcquisitionInputDto";
import LineGroupUseAcquisitionOutputDto from "@/core/dto/LineGroupUseAcquisitionOutputDto";
import LineGroupUseAcquisitionCouponOffOutputDto from "@/core/dto/LineGroupUseAcquisitionCouponOffOutputDto";
import MsgKeysConstants from "@/core/constant/MsgKeysConstants";
import MvnoUtil from "@/core/common/MvnoUtil";
import ResultCdConstants from "@/core/constant/ResultCdConstants";
import { LinesEntity } from "@/core/entity/LinesEntity";
import Check from "@/core/common/Check";
import SOAPCommon from "@/core/common/SOAPCommon";
import APICommonDAO from "@/core/dao/APICommonDAO";
import APILinesGroupDAO from "@/core/dao/ApiLinesGroupDAO";
import TenantManage from "@/core/common/TenantManage";
import ResponseHeader from "@/core/dto/ResponseHeader";
import "@/types/string.extension";
import CheckUtil from "@/core/common/CheckUtil";
import { TenantsEntity } from "@/core/entity/TenantsEntity";
import { isSQLException } from "@/helpers/queryHelper";
import TenantGroupPlansEntity from "@/core/entity/TenantGroupPlansEntity";
import { GroupPlansEntity } from "@/core/entity/GroupPlansEntity";
import LineGroupsEntity from "@/core/entity/LineGroupsEntity";
import GroupTrafficsEntity from "@/core/entity/GroupTrafficsEntity";
import ParameterName from "@/core/dto/ParameterName";
import SOAPCommonOutputDto from "@/core/dto/SOAPCommonOutputDto";
import SOAPException from "@/types/soapException";
import { getStringParameter } from "@/types/parameter.string";

export default class LineGroupUseAcquisitionService
    extends RESTCommon
    implements IFService<LineGroupUseAcquisitionInputDto, LineGroupUseAcquisitionOutputDto> {
    /** 定数　「回線番号」 */
    private static readonly CONST_LINENO = "回線番号";
    /** 定数　「回線グループID取得」 */
    private static readonly LINE_GROUPID = "回線グループID";

    /** 定数　「卸ポータルグループプランID」 */
    private static readonly POTALGROUP_PLAN = "グループプランID";
    /** STEP4.0版対応　追加　START */
    /** 定数　「クーポンOFF時の通信量要否」 */
    private static readonly CONST_TRAFFICOFCOUPONOFFFLAG = "クーポンOFF時の通信量要否";
    /** STEP4.0版対応　追加　END */
    /** 定数　「1」 */
    private static readonly CONST_SHANAITENANTID_1 = "1";
    /** 定数　「2」 */
    private static readonly CONST_SHANAITENANTID_2 = "2";
    /** 定数　「3」 */
    private static readonly CONST_SHANAITENANTID_3 = "3";
    /** 定数　「4」 */
    private static readonly CONST_SHANAITENANTID_4 = "4";
    /** 総量規制状態_REGULATED */
    private static readonly CONST_DYN_REG_STS_REGULATED = "Regulated";
    /** 総量規制状態_UNREGULATED */
    private static readonly CONST_DYN_REG_STS_UNREGULATED = "Unregulated";
    /** 「Result」結果がNG */
    private static readonly RESULT_NG = "NG";

    /**
     * API共通DAO
     */
    private apiCommonDao: APICommonDAO = new APICommonDAO(this.request, this.context);

    private aPILinesGroupDAO: APILinesGroupDAO = new APILinesGroupDAO(this.request, this.context);

    /** STEP3.1版対応　追加　START */
    private tenantManage: TenantManage = new TenantManage(this.request, this.context);
    /** STEP3.1版対応　追加　END */

    /**
     * SOAP API連携機能初期化
     */
    private sOAPCommon: SOAPCommon = new SOAPCommon(this.request, this.context);

    // @Value("#{mvno[APServerConnectSOAPAPIURL]}")
    // private String soapApiUrl;
    // private soapApiUrl: string;


    /**
     * 回線グループ運用情報参照。<BR>
     *
     * <PRE>
     * REST APIにより、受信したパラメータの回線グループIDをキーにして回線グループ運用情報を取得する。
     * モバイルアクセス回線の回線グループ運用情報を提供する。
     * </PRE>
     *
     * @param param 回線グループ運用情報参照インプット
     * @param isPrivate 内部呼出フラグ
     * @param params 可変長さ引数(内部から呼出す時に使用するパラメータ)
     * @return LineGroupUseAcquisitionOutputDto 回線グループ運用情報参照アウトプット
     */
    public async service(param: LineGroupUseAcquisitionInputDto, ...params: any[]): Promise<LineGroupUseAcquisitionOutputDto> {
        super.debug(param.tenantId, param.requestHeader.sequenceNo,MsgKeysConstants.APGTID0001,
            this.getClassName(), "service", JSON.stringify(param));
        // REST API共通処理が呼び出されたシステム日付
        const receivedDate = this.context.responseHeader?.receivedDate ?? MvnoUtil.getDateTimeNow();
        // 変数「処理コード」初期化
        let cheatCode = ResultCdConstants.CODE_000000;

        const isPrivate = false;
        // 送信番号取得
        const sequenceNo = param.requestHeader.sequenceNo;
        // 送信元システムID取得
        // const senderSystemId = param.requestHeader.senderSystemId;       // only used in CheckResultBean
        // API認証キー取得
        // const apiKey = param.requestHeader.apiKey;                       // only used in CheckResultBean
        // 機能種別取得
        const functionType = param.requestHeader.functionType;
        // テナントID取得
        const tenantId = param.tenantId;
        // 回線グループID取得
        const lineGroupId = getStringParameter(param.lineGroupId);
        // 卸ポータルグループプランID取得
        const potalGroupPlanID = getStringParameter(param.potalGroupPlanID);
        // 回線番号取得
        const lineNo = param.lineNo;
        /** クーポンOFF時の通信量要否 */
        const trafficOfCouponOffFlag = getStringParameter(param.trafficOfCouponOffFlag);

        let serviceModle: string = "";
        // 社内テナント種別
        let tenanType: string = "";
        // 変数回線番号
        let strLineId: string = "";
        // 回線運用データ情報結果初期化
        let document: Document = null;
        const apiProcessID: string = this.context.responseHeader?.apiProcessID ?? "";;
        let linesEntity: LinesEntity = null;
        /** STEP1.2a版対応　追加　START */
        // 変数「エラーメッセージ」を定義する
        const errorMessage: string = null;
        /** STEP1.2a版対応　追加　END */
        /** STEP1.2a版対応　変更　START */
        let output: LineGroupUseAcquisitionOutputDto = null;        // also allows LineGroupUseAcquisitionCouponOffInsideOutputDto
        /** STEP1.2a版対応　変更　END */
        // 前月の通信量
        let trafficPreviousMonth: string = null;
        // 前々月の通信量
        let trafficBeforehandMonth: string = null;
        /** STEP1.2b版対応　追加　END */
        /** STEP5.0対応　追加　START */
        // 前月のクーポンOFF通信量
        let trafficPreviousMonthCouponOff: string = null;
        // 前々月のクーポンOFF通信量
        let trafficBeforehandMonthCouponOff: string = null;
        /** STEP5.0対応　追加　END */

        try {
            // 1 回線グループIDフォーマットチェック
            if (!Check.checkLineGroupId(lineGroupId)) {
                cheatCode = ResultCdConstants.CODE_090101;
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APGTIW0001, LineGroupUseAcquisitionService.LINE_GROUPID, lineGroupId);
                /** STEP1.2a版対応　変更　START */
                // 返却値編集
                /** STEP5.0対応　変更　START */
                output = this.returnEdit(param, cheatCode, apiProcessID, serviceModle,
                    document, errorMessage, isPrivate, receivedDate,
                    trafficPreviousMonth, trafficBeforehandMonth,
                    trafficPreviousMonthCouponOff, trafficBeforehandMonthCouponOff);
                /** STEP5.0対応　変更　END */
                super.debug(param.tenantId, param.requestHeader.sequenceNo,MsgKeysConstants.APGTID0002,
                    this.getClassName(), "service");
                return output;
                /** STEP1.2a版対応　変更　END */
            }
            // ２　卸ポータルグループプランIDフォーマットチェック
            if (!Check.checkPlanIDFmt(potalGroupPlanID)) {
                cheatCode = ResultCdConstants.CODE_090101;
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APGTIW0001, LineGroupUseAcquisitionService.POTALGROUP_PLAN, potalGroupPlanID);
                /** STEP1.2a版対応　変更　START */
                // 返却値編集
                /** STEP5.0対応　変更　START */
                output = this.returnEdit(param, cheatCode, apiProcessID, serviceModle,
                    document, errorMessage, isPrivate, receivedDate,
                    trafficPreviousMonth, trafficBeforehandMonth,
                    trafficPreviousMonthCouponOff, trafficBeforehandMonthCouponOff);
                /** STEP5.0対応　変更　END */
                super.debug(param.tenantId, param.requestHeader.sequenceNo,MsgKeysConstants.APGTID0002,
                    this.getClassName(), "service");
                return output;
                /** STEP1.2a版対応　変更　END */
            }
            /** STEP4.0版対応　追加　START */
            if (!CheckUtil.checkIsNotNull(trafficOfCouponOffFlag)) {
                // クーポンOFF時の通信量要否フォーマットチェック
                if (!Check.checkTrafficOfCouponOffFlag(trafficOfCouponOffFlag)) {
                    cheatCode = ResultCdConstants.CODE_090101;
                    super.warn(tenantId, sequenceNo, MsgKeysConstants.APGTIW0001,
                        LineGroupUseAcquisitionService.CONST_TRAFFICOFCOUPONOFFFLAG, trafficOfCouponOffFlag);
                    // 返却値編集
                    /** STEP5.0対応　変更　START */
                    output = this.returnEdit(param, cheatCode, apiProcessID, serviceModle,
                        document, errorMessage, isPrivate, receivedDate,
                        trafficPreviousMonth, trafficBeforehandMonth,
                        trafficPreviousMonthCouponOff, trafficBeforehandMonthCouponOff);
                    /** STEP5.0対応　変更　END */
                    super.debug(param.tenantId, param.requestHeader.sequenceNo,
                        MsgKeysConstants.APGTID0002, this.getClassName(), "service");
                    return output;
                }
            }
            /** STEP4.0版対応　追加　END */
            // ３　社内テナント種別取得
            // STEP20.0版対応　変更　START
            let tenantsEntity: TenantsEntity;
            try {
                tenantsEntity = await this.apiCommonDao.getTenants(tenantId);
            } catch (e) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    cheatCode = ResultCdConstants.CODE_090102;
                }
                throw e;
            }
            // STEP20.0版対応　変更　END
            if (tenantsEntity != null) {
                if (CheckUtil.checkIsNull(tenantsEntity.tenantType)){
                    tenanType = "";
                } else {
                    tenanType = tenantsEntity.tenantType.toString();
                }
            }

            // ４　回線番号の判断を行う
            if (LineGroupUseAcquisitionService.CONST_SHANAITENANTID_1.equals(tenanType) && CheckUtil.checkIsNotNull(lineNo)) {
                cheatCode = ResultCdConstants.CODE_090102;
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APGTIW0002);
                /** STEP1.2a版対応　変更　START */
                // 返却値編集
                /** STEP5.0対応　変更　START */
                output = this.returnEdit(param, cheatCode, apiProcessID, serviceModle,
                    document, errorMessage, isPrivate, receivedDate,
                    trafficPreviousMonth, trafficBeforehandMonth,
                    trafficPreviousMonthCouponOff, trafficBeforehandMonthCouponOff);
                /** STEP5.0対応　変更　END */
                super.debug(param.tenantId, param.requestHeader.sequenceNo,MsgKeysConstants.APGTID0002,
                    this.getClassName(), "service");
                return output;
                /** STEP1.2a版対応　変更　END */
            }

            // 入力パラメータ．「回線番号」≠空の場合
            if (!CheckUtil.checkIsNotNull(lineNo)) {
                // ４．１　回線番号フォーマットチェック
                if (!Check.checkLineNo(lineNo)) {
                    cheatCode = ResultCdConstants.CODE_090101;
                    super.warn(tenantId, sequenceNo, MsgKeysConstants.APGTIW0001, LineGroupUseAcquisitionService.CONST_LINENO, lineNo);
                    /** STEP1.2a版対応　変更　START */
                    // 返却値編集
                    /** STEP5.0対応　変更　START */
                    output = this.returnEdit(param, cheatCode, apiProcessID, serviceModle,
                        document, errorMessage, isPrivate, receivedDate,
                        trafficPreviousMonth, trafficBeforehandMonth,
                        trafficPreviousMonthCouponOff, trafficBeforehandMonthCouponOff);
                    /** STEP5.0対応　変更　END */
                    super.debug(param.tenantId, param.requestHeader.sequenceNo,MsgKeysConstants.APGTID0002,
                        this.getClassName(), "service");
                    return output;
                    /** STEP1.2a版対応　変更　END */
                }
            }
            // ５　卸ポータルグループプランID所属チェック
            // STEP20.0版対応　変更　START
            let tenatGroupEntity: TenantGroupPlansEntity;
            try {
                tenatGroupEntity = await this.apiCommonDao.getTenantGroupPlans(potalGroupPlanID, tenantId);
            } catch (e) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    cheatCode = ResultCdConstants.CODE_090103;
                }
                throw e;
            }
            // STEP20.0版対応　変更　END
            if (tenatGroupEntity == null) {
                cheatCode = ResultCdConstants.CODE_090103;
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APGTIW0003, potalGroupPlanID, tenantId);
                /** STEP1.2a版対応　変更　START */
                // 返却値編集
                /** STEP5.0対応　変更　START */
                output = this.returnEdit(param, cheatCode, apiProcessID, serviceModle,
                    document, errorMessage, isPrivate, receivedDate,
                    trafficPreviousMonth, trafficBeforehandMonth,
                    trafficPreviousMonthCouponOff, trafficBeforehandMonthCouponOff);
                /** STEP5.0対応　変更　END */
                super.debug(param.tenantId, param.requestHeader.sequenceNo,MsgKeysConstants.APGTID0002,
                    this.getClassName(), "service");
                return output;
                /** STEP1.2a版対応　変更　END */
            }


            /** STEP4.0版対応　追加　START */
            // グループプランIDの利用チェック
            if (!"1".equals(tenanType)) {
                // STEP20.0版対応　変更　START
                let usedPlanInfo: string = null;
                try {
                    usedPlanInfo = await this.aPILinesGroupDAO.getUsingGroupPlans(potalGroupPlanID, lineGroupId);
                } catch (e) {
                    if (isSQLException(e)) {
                        // DBアクセスリトライエラーの場合
                        cheatCode = ResultCdConstants.CODE_090104;
                    }
                    throw e;
                }
                // STEP20.0版対応　変更　END
                if (CheckUtil.checkIsNotNull(usedPlanInfo)) {
                    cheatCode = ResultCdConstants.CODE_090104;
                    super.warn(tenantId, sequenceNo, MsgKeysConstants.APGTIW0009, potalGroupPlanID);
                    // 返却値編集
                    /** STEP5.0対応　変更　START */
                    output = this.returnEdit(param, cheatCode, apiProcessID, serviceModle, document, errorMessage,
                        isPrivate, receivedDate, trafficPreviousMonth, trafficBeforehandMonth
                        , trafficPreviousMonthCouponOff, trafficBeforehandMonthCouponOff);
                    /** STEP5.0対応　変更　END */
                    super.debug(param.tenantId, param.requestHeader.sequenceNo,
                        MsgKeysConstants.APGTID0002, this.getClassName(), "service");
                    return output;
                }
            }
            /** STEP4.0版対応　追加　END */

            // ロジック処理
            // １　回線クループテーブルより該当回線の参照サービスパターンを取得する
            // STEP20.0版対応　変更　START
            let groupPlansEntity: GroupPlansEntity = null;
            try {
                groupPlansEntity = await this.apiCommonDao.getGroupPlans(potalGroupPlanID);
            } catch (e) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    cheatCode = ResultCdConstants.CONST_090201;
                }
                throw e;
            }
            // STEP20.0版対応　変更　END
            if (groupPlansEntity != null) {
                if (CheckUtil.checkIsNull(groupPlansEntity.refServicePattern)) {
                    serviceModle = "";
                } else {
                    serviceModle = groupPlansEntity.refServicePattern.toString();
                }

            }
            if (groupPlansEntity == null || CheckUtil.checkIsNotNull(serviceModle)) {
                cheatCode = ResultCdConstants.CONST_090201;
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APGTIW0004);
                /** STEP1.2a版対応　変更　START */
                // 返却値編集
                /** STEP5.0対応　変更　START */
                output = this.returnEdit(param, cheatCode, apiProcessID, serviceModle,
                    document, errorMessage, isPrivate, receivedDate,
                    trafficPreviousMonth, trafficBeforehandMonth,
                    trafficPreviousMonthCouponOff, trafficBeforehandMonthCouponOff);
                /** STEP5.0対応　変更　END */
                super.debug(param.tenantId, param.requestHeader.sequenceNo,MsgKeysConstants.APGTID0002,
                    this.getClassName(), "service");
                return output;
                /** STEP1.2a版対応　変更　END */
            }
            /** STEP3.1版対応　変更　START */
            // if (!CONST_SHANAITENANTID_1.equals(tenanType)) {
            /** #149対応　変更　START */
            // 回線グループ管理情報取得
            // STEP20.0版対応　変更　START
            let lineGroupsEntity: LineGroupsEntity = null;
            try {
                lineGroupsEntity = await this.aPILinesGroupDAO.getLineGroupsInfo(lineGroupId);
            } catch (e) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    cheatCode = ResultCdConstants.CONST_090203;
                }
                throw e;
            }
            // STEP20.0版対応　変更　END
            // テナント種別により判断
            if (!LineGroupUseAcquisitionService.CONST_SHANAITENANTID_1.equals(tenanType)) {
                // 回線グループ管理情報チェックを行う
                /** STEP3.1版対応　変更　START */
                if (lineGroupsEntity == null
                    || !tenantId.equals(lineGroupsEntity.tenantId)
                    || lineGroupsEntity.status !== 1
                    || lineGroupsEntity.planId == null) {
                    /** STEP3.1版対応　変更　END */
                    cheatCode = ResultCdConstants.CONST_090203;
                    /** STEP3.1版対応　変更　START */
                    super.warn(tenantId, sequenceNo, MsgKeysConstants.APGTIW0007, lineGroupId, tenantId);
                    /** STEP3.1版対応　変更　END */
                    /** STEP1.2a版対応　変更　START */
                    // 返却値編集
                    /** STEP5.0対応　変更　START */
                    output = this.returnEdit(param, cheatCode, apiProcessID, serviceModle,
                        document, errorMessage, isPrivate, receivedDate,
                        trafficPreviousMonth, trafficBeforehandMonth,
                        trafficPreviousMonthCouponOff, trafficBeforehandMonthCouponOff);
                    /** STEP5.0対応　変更　END */
                    super.debug(param.tenantId, param.requestHeader.sequenceNo,MsgKeysConstants.APGTID0002,
                        this.getClassName(), "service");
                    return output;
                    /** STEP1.2a版対応　変更　END */
                }
                /** #149対応　変更　END */
                // １．２．１　回線情報から回線番号を取得
                // STEP20.0版対応　変更　START
                try {
                    linesEntity  = await this.apiCommonDao.getLinesID(lineGroupId);
                } catch (e) {
                    if (isSQLException(e)) {
                        // DBアクセスリトライエラーの場合
                        cheatCode = ResultCdConstants.CONST_090202;
                    }
                    throw e;
                }
                // STEP20.0版対応　変更　END
                // １．２．２　変数「回線番号結果」より判定
                if ( linesEntity != null) {
                    if (CheckUtil.checkIsNull(linesEntity.lineId)) {
                        strLineId = "";
                    } else {
                        strLineId =  linesEntity.lineId;
                    }
                }
                if (CheckUtil.checkIsNotNull(strLineId)) {
                    cheatCode = ResultCdConstants.CONST_090202;
                    super.warn(tenantId, sequenceNo, MsgKeysConstants.APGTIW0006);
                    /** STEP1.2a版対応　変更　START */
                    // 返却値編集
                    /** STEP5.0対応　変更　START */
                    output = this.returnEdit(param, cheatCode, apiProcessID, serviceModle,
                        document, errorMessage, isPrivate, receivedDate,
                        trafficPreviousMonth, trafficBeforehandMonth,
                        trafficPreviousMonthCouponOff, trafficBeforehandMonthCouponOff);
                    /** STEP5.0対応　変更　END */
                    super.debug(param.tenantId, param.requestHeader.sequenceNo,MsgKeysConstants.APGTID0002,
                        this.getClassName(), "service");
                    return output;
                    /** STEP1.2a版対応　変更　END */
                }
            } else {
                // C-OCNの場合、回線グループ情報が存在すればエラー
                if (lineGroupsEntity != null ) {
                    /** STEP3.1版対応　変更　END */
                    cheatCode = ResultCdConstants.CONST_090203;
                    /** STEP3.1版対応　変更　START */
                    super.warn(tenantId, sequenceNo, MsgKeysConstants.APGTIW0007, lineGroupId, tenantId);
                    /** STEP3.1版対応　変更　END */
                    /** STEP1.2a版対応　変更　START */
                    // 返却値編集
                    /** STEP5.0対応　変更　START */
                    output = this.returnEdit(param, cheatCode, apiProcessID, serviceModle,
                        document, errorMessage, isPrivate, receivedDate,
                        trafficPreviousMonth, trafficBeforehandMonth,
                        trafficPreviousMonthCouponOff, trafficBeforehandMonthCouponOff);
                    /** STEP5.0対応　変更　END */
                    super.debug(param.tenantId, param.requestHeader.sequenceNo,MsgKeysConstants.APGTID0002,
                        this.getClassName(), "service");
                    return output;
                    /** STEP1.2a版対応　変更　END */
                }
                // 変数「回線番号」の入力パラメータ．「回線番号」設定
                strLineId = lineNo;
            }
            /** STEP3.1版対応　変更　END */
            /** STEP1.2b版対応　追加　START */
            // 該当回線グループの前月の通信量を取得する
            // STEP20.0版対応　変更　START
            let trafficPreviousMonthEntity: GroupTrafficsEntity = null;
            try {
                trafficPreviousMonthEntity
                    = await this.aPILinesGroupDAO.getTraffic(lineGroupId, MvnoUtil.getTrafficPreviousMonth(-1)[0],
                    MvnoUtil.getTrafficPreviousMonth(-1)[1]);
            } catch (e) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    cheatCode = ResultCdConstants.CONST_090204;
                }
                throw e;
            }
            // STEP20.0版対応　変更　END
            if (trafficPreviousMonthEntity != null && trafficPreviousMonthEntity.traffic != null) {
                trafficPreviousMonth = trafficPreviousMonthEntity.traffic.toString();
            }
            /** STEP5.0対応　追加　START */
            if (trafficPreviousMonthEntity != null && trafficPreviousMonthEntity.trafficCouponOff != null) {
                trafficPreviousMonthCouponOff = trafficPreviousMonthEntity.trafficCouponOff.toString();
            }
            /** STEP5.0対応　追加　END */
            // 該当回線グループの前々月の通信量を取得する
            // STEP20.0版対応　変更　START
            let trafficBeforehandMonthEntity: GroupTrafficsEntity = null;
            try {
                trafficBeforehandMonthEntity
                    = await this.aPILinesGroupDAO.getTraffic(lineGroupId, MvnoUtil.getTrafficPreviousMonth(-2)[0],
                    MvnoUtil.getTrafficPreviousMonth(-2)[1]);
            } catch (e) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    cheatCode = ResultCdConstants.CONST_090205;
                }
                throw e;
            }
            // STEP20.0版対応　変更　END
            if (trafficBeforehandMonthEntity != null && trafficBeforehandMonthEntity.traffic != null) {
                trafficBeforehandMonth = trafficBeforehandMonthEntity.traffic.toString();
            }
            /** STEP5.0対応　追加　START */
            if (trafficBeforehandMonthEntity != null
                && trafficBeforehandMonthEntity.trafficCouponOff != null) {
                trafficBeforehandMonthCouponOff = trafficBeforehandMonthEntity.trafficCouponOff.toString();
            }
            /** STEP5.0対応　追加　END */
            /** STEP1.2b版対応　追加　END */
            // ２．２　SOAP APIにより、回線運用情報を取得する
            try {
                /** STEP3.1版対応　追加　START */
                // STEP20.0版対応　変更　START
                let tpcConnectionResults: [boolean, string, string] = null;
                try {
                    tpcConnectionResults = await this.tenantManage.checkTpcConnection(tenantId);
                } catch (e) {
                    if (isSQLException(e)) {
                        // DBアクセスリトライエラーの場合
                        cheatCode = ResultCdConstants.CODE_090301;
                    }
                    throw e;
                }
                // STEP20.0版対応　変更　END
                // 変数「TPC情報取得結果」より判断する
                if(!tpcConnectionResults[0]){
                    cheatCode = ResultCdConstants.CODE_090301;
                    super.warn(tenantId, sequenceNo, MsgKeysConstants.APGTIW0008,tenantId);
                    // 返却値編集
                    /** STEP5.0対応　変更　START */
                    output = this.returnEdit(param, cheatCode, apiProcessID, serviceModle,
                        document, errorMessage, isPrivate, receivedDate,
                        trafficPreviousMonth, trafficBeforehandMonth,
                        trafficPreviousMonthCouponOff, trafficBeforehandMonthCouponOff);
                    /** STEP5.0対応　変更　END */
                    super.debug(param.tenantId, param.requestHeader.sequenceNo,MsgKeysConstants.APGTID0002,
                        this.getClassName(), "service");
                    return output;
                }
                /** STEP3.1版対応　追加　END */
                const paraList: ParameterName[] = [];
                const parameterName: ParameterName = ParameterName.new(
                    "msisdn",
                    "81" + strLineId.substring(1)
                );
                paraList.push(parameterName);
                /** STEP3.1版対応　変更　START */
                const sOAPCommonOutputDto: SOAPCommonOutputDto = await this.sOAPCommon.callWebSoapApi(
                    "serviceProfileQuery", "AllView", "",
                    paraList, tenantId, sequenceNo, tpcConnectionResults[1]);
                /** STEP3.1版対応　変更　END */

                /** STEP3.1版対応　追加　START */
                if (ResultCdConstants.CODE_000951.equals(sOAPCommonOutputDto.getProcessCode())) {
                    // SG取得がNG
                    cheatCode = ResultCdConstants.CODE_090401;
                    super.warn(tenantId, sequenceNo, MsgKeysConstants.APGTIW0005, "");
                    // 返却値編集
                    /** STEP5.0対応　変更　START */
                    output = this.returnEdit(param, cheatCode, apiProcessID, serviceModle, document,
                        errorMessage, isPrivate, receivedDate,
                        trafficPreviousMonth, trafficBeforehandMonth,
                        trafficPreviousMonthCouponOff, trafficBeforehandMonthCouponOff);
                    /** STEP5.0対応　変更　END */
                    super.debug(param.tenantId, param.requestHeader.sequenceNo,MsgKeysConstants.APGTID0002,
                        this.getClassName(), "service");
                    return output;
                }
                /** STEP3.1版対応　追加　END */

                document = sOAPCommonOutputDto.getDoc();
                const resultStr: string = this.sOAPCommon.getNodeContent(document, "//Result");
                if (sOAPCommonOutputDto.isError() || LineGroupUseAcquisitionService.RESULT_NG.equals(resultStr)) {
                    cheatCode = ResultCdConstants.CODE_090401;
                    super.warn(tenantId, sequenceNo, MsgKeysConstants.APGTIW0005,
                        this.sOAPCommon.getNodeContent(document, "//ErrorInfomation"));
                    /** STEP1.2a版対応　変更　START */
                    // 返却値編集
                    /** STEP5.0対応　変更　START */
                    output = this.returnEdit(param, cheatCode, apiProcessID, serviceModle,
                        document, errorMessage, isPrivate, receivedDate,
                        trafficPreviousMonth, trafficBeforehandMonth,
                        trafficPreviousMonthCouponOff, trafficBeforehandMonthCouponOff);
                    /** STEP5.0対応　変更　END */
                    super.debug(param.tenantId, param.requestHeader.sequenceNo,MsgKeysConstants.APGTID0002,
                        this.getClassName(), "service");
                    return output;
                    /** STEP1.2a版対応　変更　END */
                }
                // STEP20.0版対応　追加　START
            } catch (e) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    // リターンコード値は各エラー時にて設定済み
                    super.error(e, param.tenantId, param.requestHeader.sequenceNo,
                        MsgKeysConstants.APCOME0402, functionType);
                    // 返却値編集
                    output = this.returnEdit(param, cheatCode, apiProcessID, serviceModle, document,
                        errorMessage, isPrivate, receivedDate,
                        trafficPreviousMonth, trafficBeforehandMonth,
                        trafficPreviousMonthCouponOff, trafficBeforehandMonthCouponOff);
                    return output;
                    // STEP20.0版対応　追加　END
                } else if (SOAPException.isSOAPException(e)) {
                    cheatCode = ResultCdConstants.CODE_090401;
                    /** STEP1.2a版対応　変更　START */
                    // 返却値編集
                    /** STEP5.0対応　変更　START */
                    output = this.returnEdit(param, cheatCode, apiProcessID, serviceModle, document,
                        errorMessage, isPrivate, receivedDate,
                        trafficPreviousMonth, trafficBeforehandMonth,
                        trafficPreviousMonthCouponOff, trafficBeforehandMonthCouponOff);
                    /** STEP5.0対応　変更　END */
                    super.debug(param.tenantId, param.requestHeader.sequenceNo,MsgKeysConstants.APGTID0002,
                        this.getClassName(), "service");
                    return output;
                    /** STEP1.2a版対応　変更　END */
                } else {
                    cheatCode = ResultCdConstants.CODE_999999;
                    super.error(e, param.tenantId, param.requestHeader.sequenceNo, MsgKeysConstants.APCOME0401, e.message);
                    /** STEP1.2a版対応　変更　START */
                    // 返却値編集
                    /** STEP5.0対応　変更　START */
                    output = this.returnEdit(param, cheatCode, apiProcessID, serviceModle,
                        document, errorMessage, isPrivate, receivedDate,
                        trafficPreviousMonth, trafficBeforehandMonth,
                        trafficPreviousMonthCouponOff, trafficBeforehandMonthCouponOff);
                    /** STEP5.0対応　変更　END */
                    super.debug(param.tenantId, param.requestHeader.sequenceNo,MsgKeysConstants.APGTID0002,
                        this.getClassName(), "service");
                    return output;
                    /** STEP1.2a版対応　変更　END */
                }
            }
            /** STEP1.2a版対応　変更　START */
            // 返却値編集
            /** STEP5.0対応　変更　START */
            output = this.returnEdit(param, cheatCode, apiProcessID, serviceModle,
                document, errorMessage, isPrivate, receivedDate,
                trafficPreviousMonth, trafficBeforehandMonth,
                trafficPreviousMonthCouponOff, trafficBeforehandMonthCouponOff);
            /** STEP5.0対応　変更　END */
            super.debug(param.tenantId, param.requestHeader.sequenceNo,MsgKeysConstants.APGTID0002,
                this.getClassName(), "service");
            return output;
            /** STEP1.2a版対応　変更　END */
        } catch (e) {
            if (isSQLException(e)) {
                // DBアクセスリトライエラーの場合
                // リターンコード値は各エラー時にて設定済み
                super.error(e, param.tenantId, param.requestHeader.sequenceNo,
                    MsgKeysConstants.APCOME0402, functionType);
                // 返却値編集
                output = this.returnEdit(param, cheatCode, apiProcessID, serviceModle, document,
                    errorMessage, isPrivate, receivedDate,
                    trafficPreviousMonth, trafficBeforehandMonth,
                    trafficPreviousMonthCouponOff, trafficBeforehandMonthCouponOff);
                return output;
                // STEP20.0版対応　追加　END
            } else {
                cheatCode = ResultCdConstants.CODE_999999;
                super.error(tenantId, sequenceNo, MsgKeysConstants.APCOME0401,e);
                /** STEP1.2a版対応　変更　START */
                // 返却値編集
                /** STEP5.0対応　変更　START */
                output = this.returnEdit(param, cheatCode, apiProcessID, serviceModle,
                    document, errorMessage, isPrivate, receivedDate,
                    trafficPreviousMonth, trafficBeforehandMonth,
                    trafficPreviousMonthCouponOff, trafficBeforehandMonthCouponOff);
                /** STEP5.0対応　変更　END */
                super.debug(param.tenantId, param.requestHeader.sequenceNo,MsgKeysConstants.APGTID0002,
                    this.getClassName(), "service");
                return output;
                /** STEP1.2a版対応　変更　END */
            }
        }

    }

    /**
     * 回線グループ運用情報参照。<BR>
     * @param pram 回線グループ運用情報参照Dto
     * @param rtncheatCode 変数 「処理コード」
     * @param rtnapiProcessID 変数 「API処理ID」
     * @param rtnpotalGroupPlanID 変数 「卸ポータルグループプランID」
     * @param document 回線運用データ情報結果
     * @param errorMessage 変数「エラーメッセージ」
     * @param isPrivate 内部呼出フラグ
     * @param receivedDate REST API共通処理が呼び出されたシステム日付
     * @param additionArg 追加引数<br>
     * [0] クーポンOFF時前月通信量
     * [1] クーポンOFF時前々月通信量
     * @return 回線基本情報取得アウトプット
     */
    public returnEdit(pram: LineGroupUseAcquisitionInputDto, rtncheatCode: string,
    rtnapiProcessID: string, rtnpotalGroupPlanID: string, document: Document,
    errorMessage: string, isPrivate: boolean, receivedDate: string,
    trafficPreviousMonth: string, trafficBeforehandMonth: string,
    ...additionArg: any[]): LineGroupUseAcquisitionOutputDto {
        const responseHeader: ResponseHeader = {
            sequenceNo: pram.requestHeader.sequenceNo,
            receivedDate,
            processCode: rtncheatCode,
            apiProcessID: rtnapiProcessID
        };

        let returnPram: LineGroupUseAcquisitionOutputDto = {
            jsonBody: {
                responseHeader,
                trafficOneDay: "",
                traffic1dayAgo: "",
                traffic2daysAgo: "",
                traffic3daysAgo: "",
                traffic4daysAgo: "",
                traffic5daysAgo: "",
                traffic6daysAgo: "",
                traffic7daysAgo: "",
                traffic8daysAgo: "",
                traffic9daysAgo: "",
                traffic10daysAgo: "",
                traffic11daysAgo: "",
                traffic12daysAgo: "",
                traffic13daysAgo: "",
                traffic14daysAgo: "",
                traffic15daysAgo: "",
                traffic16daysAgo: "",
                traffic17daysAgo: "",
                traffic18daysAgo: "",
                traffic19daysAgo: "",
                traffic20daysAgo: "",
                traffic21daysAgo: "",
                traffic22daysAgo: "",
                traffic23daysAgo: "",
                traffic24daysAgo: "",
                traffic25daysAgo: "",
                traffic26daysAgo: "",
                traffic27daysAgo: "",
                traffic28daysAgo: "",
                traffic29daysAgo: "",
                traffic30daysAgo: "",
                trafficThisMonth: "",
                trafficPreviousMonth: "",
                trafficBeforehandMonth: "",
                basicCouponRemains: "",
                basicCouponTermValidity: "",
                couponPieceTime: "",
                carryingOverCoupon1: {
                    carryingOverCoupon1RemainCapacity: "",
                    carryingOverCoupon1TermValidity: ""
                },
                carryingOverCoupon2: {
                    carryingOverCoupon2RemainCapacity: "",
                    carryingOverCoupon2TermValidity: ""
                },
                additionalCoupon1: {
                    additionalCoupon1RemainCapacity: "",
                    additionalCoupon1TermValidity: ""
                },
                additionalCoupon2: {
                    additionalCoupon2RemainCapacity: "",
                    additionalCoupon2TermValidity: ""
                },
                additionalCoupon3: {
                    additionalCoupon3RemainCapacity: "",
                    additionalCoupon3TermValidity: ""
                },
                additionalCoupon4: {
                    additionalCoupon4RemainCapacity: "",
                    additionalCoupon4TermValidity: ""
                },
                additionalCoupon5: {
                    additionalCoupon5RemainCapacity: "",
                    additionalCoupon5TermValidity: ""
                },
                couponOnOff: "",
                heavyUserMonitorStatus: "",
                heavyUserMonitorStartTimeMonth: "",
                heavyUserMonitorStartTimeLatest: "",
                regulationCause: "",
                totalVolumeControlStatus: "",
            }
        }; // set returnPram to else case first, so that can be overwritten one by one later (to make it look like old code)
        try {
            if (!ResultCdConstants.CODE_000000.equals(rtncheatCode)) {
                returnPram = {
                    jsonBody: {
                        responseHeader,
                        trafficOneDay: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic1dayAgo: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic2daysAgo: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic3daysAgo: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic4daysAgo: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic5daysAgo: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic6daysAgo: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic7daysAgo: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic8daysAgo: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic9daysAgo: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic10daysAgo: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic11daysAgo: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic12daysAgo: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic13daysAgo: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic14daysAgo: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic15daysAgo: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic16daysAgo: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic17daysAgo: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic18daysAgo: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic19daysAgo: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic20daysAgo: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic21daysAgo: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic22daysAgo: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic23daysAgo: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic24daysAgo: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic25daysAgo: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic26daysAgo: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic27daysAgo: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic28daysAgo: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic29daysAgo: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic30daysAgo: MvnoUtil.addSpaceAfterStr(null, 0),
                        trafficThisMonth: MvnoUtil.addSpaceAfterStr(null, 0),
                        trafficPreviousMonth: MvnoUtil.addSpaceAfterStr(null, 0),
                        trafficBeforehandMonth: MvnoUtil.addSpaceAfterStr(null, 0),
                        basicCouponRemains: MvnoUtil.addSpaceAfterStr(null, 0),
                        basicCouponTermValidity: MvnoUtil.addSpaceAfterStr(null, 0),
                        couponPieceTime: MvnoUtil.addSpaceAfterStr(null, 0),
                        carryingOverCoupon1: {
                            carryingOverCoupon1RemainCapacity: MvnoUtil.addSpaceAfterStr(null, 0),
                            carryingOverCoupon1TermValidity: MvnoUtil.addSpaceAfterStr(null, 0)
                        },
                        carryingOverCoupon2: {
                            carryingOverCoupon2RemainCapacity: MvnoUtil.addSpaceAfterStr(null, 0),
                            carryingOverCoupon2TermValidity: MvnoUtil.addSpaceAfterStr(null, 0)
                        },
                        additionalCoupon1: {
                            additionalCoupon1RemainCapacity: MvnoUtil.addSpaceAfterStr(null, 0),
                            additionalCoupon1TermValidity: MvnoUtil.addSpaceAfterStr(null, 0)
                        },
                        additionalCoupon2: {
                            additionalCoupon2RemainCapacity: MvnoUtil.addSpaceAfterStr(null, 0),
                            additionalCoupon2TermValidity: MvnoUtil.addSpaceAfterStr(null, 0)
                        },
                        additionalCoupon3: {
                            additionalCoupon3RemainCapacity: MvnoUtil.addSpaceAfterStr(null, 0),
                            additionalCoupon3TermValidity: MvnoUtil.addSpaceAfterStr(null, 0)
                        },
                        additionalCoupon4: {
                            additionalCoupon4RemainCapacity: MvnoUtil.addSpaceAfterStr(null, 0),
                            additionalCoupon4TermValidity: MvnoUtil.addSpaceAfterStr(null, 0)
                        },
                        additionalCoupon5: {
                            additionalCoupon5RemainCapacity: MvnoUtil.addSpaceAfterStr(null, 0),
                            additionalCoupon5TermValidity: MvnoUtil.addSpaceAfterStr(null, 0)
                        },
                        couponOnOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        heavyUserMonitorStatus: MvnoUtil.addSpaceAfterStr(null, 0),
                        heavyUserMonitorStartTimeMonth: MvnoUtil.addSpaceAfterStr(null, 0),
                        heavyUserMonitorStartTimeLatest: MvnoUtil.addSpaceAfterStr(null, 0),
                        regulationCause: MvnoUtil.addSpaceAfterStr(null, 0),
                        totalVolumeControlStatus: MvnoUtil.addSpaceAfterStr(null, 0),
                    }
                };
            } else if (document != null) {
                let trafficdayAgo: string = "";
                let day: string = "";
                const setTrafficdayAgo: string[] = new Array(31);

                day = Check.getPreXDay(0);
                /** STEP2.0a版対応　変更　START */
                const tagFlg = this.sOAPCommon.CheckNodeExist(document,
                    "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_acmlt_lst_info/dyn_" + day);
                /** STEP2.0a版対応　変更　END */

                if (tagFlg) {
                    for (let i = 0; i <= 30; i++) {
                        day = Check.getPreXDay(i);
                        trafficdayAgo = "";
                        trafficdayAgo = this.sOAPCommon.getNodeContent(document,
                            "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_acmlt_lst_info/dyn_" + day);
                        setTrafficdayAgo[i] = trafficdayAgo;
                    }
                } else {
                    for (let i = 1; i <= 31; i++) {
                        day = Check.getPreXDay(i);
                        trafficdayAgo = "";
                        trafficdayAgo = this.sOAPCommon.getNodeContent(document,
                            "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_acmlt_lst_info/dyn_" + day);
                        setTrafficdayAgo[i - 1] = trafficdayAgo;
                    }
                }
                // 当日の通信量
                returnPram.jsonBody.trafficOneDay = MvnoUtil.addSpaceAfterStr(setTrafficdayAgo[0], 0);
                // 1日前の通信量
                returnPram.jsonBody.traffic1dayAgo = MvnoUtil.addSpaceAfterStr(setTrafficdayAgo[1], 0);
                // 2日前の通信量
                returnPram.jsonBody.traffic2daysAgo = MvnoUtil.addSpaceAfterStr(setTrafficdayAgo[2], 0);
                // 3日前の通信量
                returnPram.jsonBody.traffic3daysAgo = MvnoUtil.addSpaceAfterStr(setTrafficdayAgo[3], 0);
                // 4日前の通信量
                returnPram.jsonBody.traffic4daysAgo = MvnoUtil.addSpaceAfterStr(setTrafficdayAgo[4], 0);
                // 5日前の通信量
                returnPram.jsonBody.traffic5daysAgo = MvnoUtil.addSpaceAfterStr(setTrafficdayAgo[5], 0);
                // 6日前の通信量
                returnPram.jsonBody.traffic6daysAgo = MvnoUtil.addSpaceAfterStr(setTrafficdayAgo[6], 0);
                // 7日前の通信量
                returnPram.jsonBody.traffic7daysAgo = MvnoUtil.addSpaceAfterStr(setTrafficdayAgo[7], 0);
                // 8日前の通信量
                returnPram.jsonBody.traffic8daysAgo = MvnoUtil.addSpaceAfterStr(setTrafficdayAgo[8], 0);
                // 9日前の通信量
                returnPram.jsonBody.traffic9daysAgo = MvnoUtil.addSpaceAfterStr(setTrafficdayAgo[9], 0);
                // 10日前の通信量
                returnPram.jsonBody.traffic10daysAgo = MvnoUtil.addSpaceAfterStr(setTrafficdayAgo[10], 0);
                // 11日前の通信量
                returnPram.jsonBody.traffic11daysAgo = MvnoUtil.addSpaceAfterStr(setTrafficdayAgo[11], 0);
                // 12日前の通信量
                returnPram.jsonBody.traffic12daysAgo = MvnoUtil.addSpaceAfterStr(setTrafficdayAgo[12], 0);
                // 13日前の通信量
                returnPram.jsonBody.traffic13daysAgo = MvnoUtil.addSpaceAfterStr(setTrafficdayAgo[13], 0);
                // 14日前の通信量
                returnPram.jsonBody.traffic14daysAgo = MvnoUtil.addSpaceAfterStr(setTrafficdayAgo[14], 0);
                // 15日前の通信量
                returnPram.jsonBody.traffic15daysAgo = MvnoUtil.addSpaceAfterStr(setTrafficdayAgo[15], 0);
                // 16日前の通信量
                returnPram.jsonBody.traffic16daysAgo = MvnoUtil.addSpaceAfterStr(setTrafficdayAgo[16], 0);
                // 17日前の通信量
                returnPram.jsonBody.traffic17daysAgo = MvnoUtil.addSpaceAfterStr(setTrafficdayAgo[17], 0);
                // 18日前の通信量
                returnPram.jsonBody.traffic18daysAgo = MvnoUtil.addSpaceAfterStr(setTrafficdayAgo[18], 0);
                // 19日前の通信量
                returnPram.jsonBody.traffic19daysAgo = MvnoUtil.addSpaceAfterStr(setTrafficdayAgo[19], 0);
                // 20日前の通信量
                returnPram.jsonBody.traffic20daysAgo = MvnoUtil.addSpaceAfterStr(setTrafficdayAgo[20], 0);
                // 21日前の通信量
                returnPram.jsonBody.traffic21daysAgo = MvnoUtil.addSpaceAfterStr(setTrafficdayAgo[21], 0);
                // 22日前の通信量
                returnPram.jsonBody.traffic22daysAgo = MvnoUtil.addSpaceAfterStr(setTrafficdayAgo[22], 0);
                // 23日前の通信量
                returnPram.jsonBody.traffic23daysAgo = MvnoUtil.addSpaceAfterStr(setTrafficdayAgo[23], 0);
                // 24日前の通信量
                returnPram.jsonBody.traffic24daysAgo = MvnoUtil.addSpaceAfterStr(setTrafficdayAgo[24], 0);
                // 25日前の通信量
                returnPram.jsonBody.traffic25daysAgo = MvnoUtil.addSpaceAfterStr(setTrafficdayAgo[25], 0);
                // 26日前の通信量
                returnPram.jsonBody.traffic26daysAgo = MvnoUtil.addSpaceAfterStr(setTrafficdayAgo[26], 0);
                // 27日前の通信量
                returnPram.jsonBody.traffic27daysAgo = MvnoUtil.addSpaceAfterStr(setTrafficdayAgo[27], 0);
                // 28日前の通信量
                returnPram.jsonBody.traffic28daysAgo = MvnoUtil.addSpaceAfterStr(setTrafficdayAgo[28], 0);
                // 29日前の通信量
                returnPram.jsonBody.traffic29daysAgo = MvnoUtil.addSpaceAfterStr(setTrafficdayAgo[29], 0);
                // 30日前の通信量
                returnPram.jsonBody.traffic30daysAgo = MvnoUtil.addSpaceAfterStr(setTrafficdayAgo[30], 0);
                // 当月の通信量
                returnPram.jsonBody.trafficThisMonth = MvnoUtil.addSpaceAfterStr(this.sOAPCommon.getNodeContent(document,
                    "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_acmlt_month_info/dyn_use"), 0);
                // 前月の通信量
                returnPram.jsonBody.trafficPreviousMonth = MvnoUtil.addSpaceAfterStr(trafficPreviousMonth, 0);
                // 前々月の通信量
                returnPram.jsonBody.trafficBeforehandMonth = MvnoUtil.addSpaceAfterStr(trafficBeforehandMonth, 0);
                /** STEP1.2b版対応　修正　END */
                // 基本クーポン残容量
                let basicCouponRemains = MvnoUtil.addSpaceAfterStr(null, 0);
                // 基本クーポン有効期限
                let basicCouponTermValidity = MvnoUtil.addSpaceAfterStr(null, 0);
                if (LineGroupUseAcquisitionService.CONST_SHANAITENANTID_1.equals(rtnpotalGroupPlanID)
                    || LineGroupUseAcquisitionService.CONST_SHANAITENANTID_2.equals(rtnpotalGroupPlanID)
                    || LineGroupUseAcquisitionService.CONST_SHANAITENANTID_3.equals(rtnpotalGroupPlanID)
                    || LineGroupUseAcquisitionService.CONST_SHANAITENANTID_4.equals(rtnpotalGroupPlanID)) {
                    // 基本クーポン残容量
                    const dyn_cap = this.sOAPCommon.getNodeContent(document,
                        "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_month_info/dyn_cap");
                    const dyn_use = this.sOAPCommon.getNodeContent(document,
                        "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_month_info/dyn_use");
                    if (CheckUtil.checkIsNum(dyn_cap) && CheckUtil.checkIsNum(dyn_use)) {
                        const basicCouponRemainsL: BigInt = BigInt(dyn_cap) - BigInt(dyn_use);
                        basicCouponRemains = MvnoUtil.addSpaceAfterStr(basicCouponRemainsL.toString(), 0);
                    }
                    // 基本クーポン有効期限
                    basicCouponTermValidity = MvnoUtil.addSpaceAfterStr(this.sOAPCommon.getNodeContent(document,
                        "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_month_info/dyn_exp_date"), 10);
                }
                // 基本クーポン残容量
                returnPram.jsonBody.basicCouponRemains = basicCouponRemains;
                // 基本クーポン有効期限
                returnPram.jsonBody.basicCouponTermValidity = basicCouponTermValidity;
                // クーポン容量切れ時間
                let couponPieceTime: string = MvnoUtil.addSpaceAfterStr(null, 0);
                const dyn_reg_sta_month: string = this.sOAPCommon.getNodeContent(document,
                    "//dyn_grp_info/dyn_node_info_0/dyn_reg_info/dyn_reg_sta_month");
                const rtndyn_reg_sta_month: string = CheckUtil.forMartdynDate(dyn_reg_sta_month);
                couponPieceTime = MvnoUtil.addSpaceAfterStr(rtndyn_reg_sta_month, 16);
                // クーポン容量切れ時間
                returnPram.jsonBody.couponPieceTime = couponPieceTime;

                // 繰越クーポン1残容量
                let carryingOverCoupon1RemainCapacity1: string = MvnoUtil.addSpaceAfterStr(null, 0);
                // 繰越クーポン1有効期限
                let carryingOverCoupon1TermValidity1: string = MvnoUtil.addSpaceAfterStr(null, 0);
                const tran_dyn_cap_1: string = this.sOAPCommon.getNodeContent(document, "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_tran_info_0/dyn_cap");
                const tran_dyn_use_1: string = this.sOAPCommon.getNodeContent(document, "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_tran_info_0/dyn_use");
                if (CheckUtil.checkIsNum(tran_dyn_cap_1) && CheckUtil.checkIsNum(tran_dyn_use_1)) {
                    const carryingOverCoupon1RemainCapacityL: BigInt = BigInt(tran_dyn_cap_1) - BigInt(tran_dyn_use_1);
                    // 繰越クーポン1残容量
                    carryingOverCoupon1RemainCapacity1 = MvnoUtil.addSpaceAfterStr(carryingOverCoupon1RemainCapacityL.toString(), 0);
                }
                // 繰越クーポン1有効期限
                carryingOverCoupon1TermValidity1 = MvnoUtil.addSpaceAfterStr(this.sOAPCommon.getNodeContent(document,
                    "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_tran_info_0/dyn_exp_date"), 10);
                // 繰越クーポン1
                returnPram.jsonBody.carryingOverCoupon1 = {
                    carryingOverCoupon1RemainCapacity: carryingOverCoupon1RemainCapacity1,
                    carryingOverCoupon1TermValidity: carryingOverCoupon1TermValidity1
                };
                // 繰越クーポン2残容量
                let carryingOverCoupon1RemainCapacity2: string = MvnoUtil.addSpaceAfterStr(null, 0);
                // 繰越クーポン2有効期限
                let carryingOverCoupon1TermValidity2: string = MvnoUtil.addSpaceAfterStr(null, 0);
                const tran_dyn_cap_2: string = this.sOAPCommon.getNodeContent(document, "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_tran_info_1/dyn_cap");
                const tran_dyn_use_2: string = this.sOAPCommon.getNodeContent(document, "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_tran_info_1/dyn_use");
                if (CheckUtil.checkIsNum(tran_dyn_cap_2) && CheckUtil.checkIsNum(tran_dyn_use_2)) {
                    const carryingOverCoupon1RemainCapacityL: BigInt = BigInt(tran_dyn_cap_2) - BigInt(tran_dyn_use_2);
                    // 繰越クーポン2残容量
                    carryingOverCoupon1RemainCapacity2 = MvnoUtil.addSpaceAfterStr(carryingOverCoupon1RemainCapacityL.toString(), 0);
                }
                // 繰越クーポン2有効期限
                carryingOverCoupon1TermValidity2 = MvnoUtil.addSpaceAfterStr(this.sOAPCommon.getNodeContent(document,
                    "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_tran_info_1/dyn_exp_date"), 10);
                // 繰越クーポン2
                returnPram.jsonBody.carryingOverCoupon2 = {
                    carryingOverCoupon2RemainCapacity: carryingOverCoupon1RemainCapacity2,
                    carryingOverCoupon2TermValidity: carryingOverCoupon1TermValidity2
                };
                // 追加クーポン1
                // AdditionalCoupon1 additionalCoupon1 = new AdditionalCoupon1();
                // 追加クーポン1残容量
                let additionalCoupon1RemainCapacity: string = MvnoUtil.addSpaceAfterStr(null, 0);
                // 追加クーポン1有効期限
                let additionalCoupon1TermValidity: string = MvnoUtil.addSpaceAfterStr(null, 0);
                if (LineGroupUseAcquisitionService.CONST_SHANAITENANTID_1.equals(rtnpotalGroupPlanID)
                    || LineGroupUseAcquisitionService.CONST_SHANAITENANTID_2.equals(rtnpotalGroupPlanID)
                    || LineGroupUseAcquisitionService.CONST_SHANAITENANTID_4.equals(rtnpotalGroupPlanID)) {
                    const dyn_cap = this.sOAPCommon.getNodeContent(document,
                        "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_add_info_0/dyn_cap");
                    const dyn_use = this.sOAPCommon.getNodeContent(document,
                        "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_add_info_0/dyn_use");
                    if (CheckUtil.checkIsNum(dyn_cap) && CheckUtil.checkIsNum(dyn_use)) {
                        const additionalCoupon1RemainCapacityL: BigInt = BigInt(dyn_cap) - BigInt(dyn_use);
                        // 追加クーポン1残容量
                        additionalCoupon1RemainCapacity = MvnoUtil.addSpaceAfterStr(additionalCoupon1RemainCapacityL.toString(), 0);
                    }
                    // 追加クーポン1有効期限
                    additionalCoupon1TermValidity = MvnoUtil.addSpaceAfterStr(this.sOAPCommon.getNodeContent(document,
                        "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_add_info_0/dyn_exp_date"), 10);
                } else if (LineGroupUseAcquisitionService.CONST_SHANAITENANTID_3.equals(rtnpotalGroupPlanID)) {
                    const dyn_cap = this.sOAPCommon.getNodeContent(document,
                        "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_addbd_info_0/dyn_cap");
                    const dyn_use = this.sOAPCommon.getNodeContent(document,
                        "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_addbd_info_0/dyn_use");
                    if (CheckUtil.checkIsNum(dyn_cap) && CheckUtil.checkIsNum(dyn_use)) {
                        const additionalCoupon1RemainCapacityL: BigInt = BigInt(dyn_cap) - BigInt(dyn_use);
                        // 追加クーポン1残容量
                        additionalCoupon1RemainCapacity = MvnoUtil.addSpaceAfterStr(additionalCoupon1RemainCapacityL.toString(), 0);
                    }
                    // 追加クーポン1有効期限
                    additionalCoupon1TermValidity = MvnoUtil.addSpaceAfterStr(this.sOAPCommon.getNodeContent(document,
                        "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_addbd_info_0/dyn_exp_date"), 10);
                }
                // 追加クーポン1
                returnPram.jsonBody.additionalCoupon1 = {
                    additionalCoupon1RemainCapacity,
                    additionalCoupon1TermValidity
                };

                // 追加クーポン2
                // AdditionalCoupon2 additionalCoupon2 = new AdditionalCoupon2();
                // 追加クーポン2残容量
                let additionalCoupon2RemainCapacity: string = MvnoUtil.addSpaceAfterStr(null, 0);
                // 追加クーポン2有効期限
                let additionalCoupon2TermValidity: string = MvnoUtil.addSpaceAfterStr(null, 0);
                if (LineGroupUseAcquisitionService.CONST_SHANAITENANTID_1.equals(rtnpotalGroupPlanID)
                    || LineGroupUseAcquisitionService.CONST_SHANAITENANTID_2.equals(rtnpotalGroupPlanID)
                    || LineGroupUseAcquisitionService.CONST_SHANAITENANTID_4.equals(rtnpotalGroupPlanID)) {
                    const dyn_cap = this.sOAPCommon.getNodeContent(document,
                        "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_add_info_1/dyn_cap");
                    const dyn_use = this.sOAPCommon.getNodeContent(document,
                        "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_add_info_1/dyn_use");
                    if (CheckUtil.checkIsNum(dyn_cap) && CheckUtil.checkIsNum(dyn_use)) {
                        const additionalCoupon1RemainCapacityL: BigInt = BigInt(dyn_cap) - BigInt(dyn_use);
                        // 追加クーポン2残容量
                        additionalCoupon2RemainCapacity = MvnoUtil.addSpaceAfterStr(additionalCoupon1RemainCapacityL.toString(), 0);
                    }
                    // 追加クーポン2有効期限
                    additionalCoupon2TermValidity = MvnoUtil.addSpaceAfterStr(this.sOAPCommon.getNodeContent(document,
                        "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_add_info_1/dyn_exp_date"), 10);
                } else if (LineGroupUseAcquisitionService.CONST_SHANAITENANTID_3.equals(rtnpotalGroupPlanID)) {
                    const dyn_cap = this.sOAPCommon.getNodeContent(document,
                        "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_addbd_info_1/dyn_cap");
                    const dyn_use = this.sOAPCommon.getNodeContent(document,
                        "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_addbd_info_1/dyn_use");
                    if (CheckUtil.checkIsNum(dyn_cap) && CheckUtil.checkIsNum(dyn_use)) {
                        const additionalCoupon1RemainCapacityL: bigint = BigInt(dyn_cap) - BigInt(dyn_use);
                        // 追加クーポン2残容量
                        additionalCoupon2RemainCapacity = MvnoUtil.addSpaceAfterStr(additionalCoupon1RemainCapacityL.toString(), 0);
                    }
                    // 追加クーポン2有効期限
                    additionalCoupon2TermValidity = MvnoUtil.addSpaceAfterStr(this.sOAPCommon.getNodeContent(document,
                        "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_addbd_info_1/dyn_exp_date"), 10);
                }
                // 追加クーポン2
                returnPram.jsonBody.additionalCoupon2 = {
                    additionalCoupon2RemainCapacity,
                    additionalCoupon2TermValidity
                };

                // 追加クーポン3
                // AdditionalCoupon3 additionalCoupon3 = new AdditionalCoupon3();
                // 追加クーポン3残容量
                let additionalCoupon3RemainCapacity = MvnoUtil.addSpaceAfterStr(null, 0);
                // 追加クーポン3有効期限
                let additionalCoupon3TermValidity = MvnoUtil.addSpaceAfterStr(null, 0);
                if (LineGroupUseAcquisitionService.CONST_SHANAITENANTID_1.equals(rtnpotalGroupPlanID)
                    || LineGroupUseAcquisitionService.CONST_SHANAITENANTID_2.equals(rtnpotalGroupPlanID)
                    || LineGroupUseAcquisitionService.CONST_SHANAITENANTID_4.equals(rtnpotalGroupPlanID)) {
                    const dyn_cap = this.sOAPCommon.getNodeContent(document,
                        "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_add_info_2/dyn_cap");
                    const dyn_use = this.sOAPCommon.getNodeContent(document,
                        "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_add_info_2/dyn_use");
                    if (CheckUtil.checkIsNum(dyn_cap) && CheckUtil.checkIsNum(dyn_use)) {
                        const additionaCoupon1RemainCapacityL: bigint = BigInt(dyn_cap) - BigInt(dyn_use);
                        // 追加クーポン3残容量
                        additionalCoupon3RemainCapacity = MvnoUtil.addSpaceAfterStr(additionaCoupon1RemainCapacityL.toString(), 0);
                    }
                    // 追加クーポン3有効期限
                    additionalCoupon3TermValidity = MvnoUtil.addSpaceAfterStr(this.sOAPCommon.getNodeContent(document,
                        "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_add_info_2/dyn_exp_date"), 10);
                } else if (LineGroupUseAcquisitionService.CONST_SHANAITENANTID_3.equals(rtnpotalGroupPlanID)) {
                    const dyn_cap = this.sOAPCommon.getNodeContent(document,
                        "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_addbd_info_2/dyn_cap");
                    const dyn_use = this.sOAPCommon.getNodeContent(document,
                        "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_addbd_info_2/dyn_use");
                    if (CheckUtil.checkIsNum(dyn_cap) && CheckUtil.checkIsNum(dyn_use)) {
                        const additionalCoupon1RemainCapacityL: bigint = BigInt(dyn_cap) - BigInt(dyn_use);
                        // 追加クーポン3残容量
                        additionalCoupon3RemainCapacity = MvnoUtil.addSpaceAfterStr(additionalCoupon1RemainCapacityL.toString(), 0);
                    }
                    // 追加クーポン3有効期限
                    additionalCoupon3TermValidity = MvnoUtil.addSpaceAfterStr(this.sOAPCommon.getNodeContent(document,
                        "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_addbd_info_2/dyn_exp_date"), 10);
                }
                // 追加クーポン3
                returnPram.jsonBody.additionalCoupon3 = {
                    additionalCoupon3RemainCapacity,
                    additionalCoupon3TermValidity
                };

                // 追加クーポン4
                // AdditionalCoupon4 additionalCoupon4 = new AdditionalCoupon4();
                // 追加クーポン4残容量
                let additionalCoupon4RemainCapacity = MvnoUtil.addSpaceAfterStr(null, 0);
                // 追加クーポン4有効期限
                let additionalCoupon4TermValidity = MvnoUtil.addSpaceAfterStr(null, 0);
                if (LineGroupUseAcquisitionService.CONST_SHANAITENANTID_1.equals(rtnpotalGroupPlanID)
                    || LineGroupUseAcquisitionService.CONST_SHANAITENANTID_2.equals(rtnpotalGroupPlanID)
                    || LineGroupUseAcquisitionService.CONST_SHANAITENANTID_4.equals(rtnpotalGroupPlanID)) {
                    const dyn_cap = this.sOAPCommon.getNodeContent(document,
                        "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_add_info_3/dyn_cap");
                    const dyn_use = this.sOAPCommon.getNodeContent(document,
                        "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_add_info_3/dyn_use");
                    if (CheckUtil.checkIsNum(dyn_cap) && CheckUtil.checkIsNum(dyn_use)) {
                        const additionalCoupon1RemainCapacityL: bigint = BigInt(dyn_cap) - BigInt(dyn_use);
                        // 追加クーポン4残容量
                        additionalCoupon4RemainCapacity = MvnoUtil.addSpaceAfterStr(additionalCoupon1RemainCapacityL.toString(), 0);
                    }
                    // 追加クーポン4有効期限
                    additionalCoupon4TermValidity = MvnoUtil.addSpaceAfterStr(this.sOAPCommon.getNodeContent(document,
                        "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_add_info_3/dyn_exp_date"), 10);
                } else if (LineGroupUseAcquisitionService.CONST_SHANAITENANTID_3.equals(rtnpotalGroupPlanID)) {
                    const dyn_cap = this.sOAPCommon.getNodeContent(document,
                        "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_addbd_info_3/dyn_cap");
                    const dyn_use = this.sOAPCommon.getNodeContent(document,
                        "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_addbd_info_3/dyn_use");
                    if (CheckUtil.checkIsNum(dyn_cap) && CheckUtil.checkIsNum(dyn_use)) {
                        const additionalCoupon1RemainCapacityL: bigint = BigInt(dyn_cap) - BigInt(dyn_use);
                        // 追加クーポン4残容量
                        additionalCoupon4RemainCapacity = MvnoUtil.addSpaceAfterStr(additionalCoupon1RemainCapacityL.toString(), 0);
                    }
                    // 追加クーポン4有効期限
                    additionalCoupon4TermValidity = MvnoUtil.addSpaceAfterStr(this.sOAPCommon.getNodeContent(document,
                        "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_addbd_info_3/dyn_exp_date"), 10);
                }
                // 追加クーポン4
                returnPram.jsonBody.additionalCoupon4 = {
                    additionalCoupon4RemainCapacity,
                    additionalCoupon4TermValidity
                };

                // 追加クーポン5
                // AdditionalCoupon5 additionalCoupon5 = new AdditionalCoupon5();
                // 追加クーポン5残容量
                let additionalCoupon5RemainCapacity = MvnoUtil.addSpaceAfterStr(null, 0);
                // 追加クーポン5有効期限
                let additionalCoupon5TermValidity = MvnoUtil.addSpaceAfterStr(null, 0);
                if (LineGroupUseAcquisitionService.CONST_SHANAITENANTID_1.equals(rtnpotalGroupPlanID)
                    || LineGroupUseAcquisitionService.CONST_SHANAITENANTID_2.equals(rtnpotalGroupPlanID)
                    || LineGroupUseAcquisitionService.CONST_SHANAITENANTID_4.equals(rtnpotalGroupPlanID)) {
                    const dyn_cap = this.sOAPCommon.getNodeContent(document,
                        "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_add_info_4/dyn_cap");
                    const dyn_use = this.sOAPCommon.getNodeContent(document,
                        "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_add_info_4/dyn_use");
                    if (CheckUtil.checkIsNum(dyn_cap) && CheckUtil.checkIsNum(dyn_use)) {
                        const additionalCoupon1RemainCapacityL: bigint = BigInt(dyn_cap) - BigInt(dyn_use);
                        // 追加クーポン5残容量
                        additionalCoupon5RemainCapacity = MvnoUtil.addSpaceAfterStr(additionalCoupon1RemainCapacityL.toString(), 0);
                    }
                    // 追加クーポン5有効期限
                    additionalCoupon5TermValidity = MvnoUtil.addSpaceAfterStr(this.sOAPCommon.getNodeContent(document,
                        "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_add_info_4/dyn_exp_date"), 10);
                } else if (LineGroupUseAcquisitionService.CONST_SHANAITENANTID_3.equals(rtnpotalGroupPlanID)) {
                    const dyn_cap = this.sOAPCommon.getNodeContent(document,
                        "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_addbd_info_4/dyn_cap");
                    const dyn_use = this.sOAPCommon.getNodeContent(document,
                        "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_addbd_info_4/dyn_use");
                    if (CheckUtil.checkIsNum(dyn_cap) && CheckUtil.checkIsNum(dyn_use)) {
                        const additionalCoupon1RemainCapacityL: bigint = BigInt(dyn_cap) - BigInt(dyn_use);
                        // 追加クーポン5残容量
                        additionalCoupon5RemainCapacity = MvnoUtil.addSpaceAfterStr(additionalCoupon1RemainCapacityL.toString(), 0);
                    }
                    // 追加クーポン5有効期限
                    additionalCoupon5TermValidity = MvnoUtil.addSpaceAfterStr(this.sOAPCommon.getNodeContent(document,
                        "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_addbd_info_4/dyn_exp_date"), 10);
                }
                // 追加クーポン5
                returnPram.jsonBody.additionalCoupon5 = {
                    additionalCoupon5RemainCapacity,
                    additionalCoupon5TermValidity
                };

                // クーポンON/OFF状態
                let ouponOnOff = MvnoUtil.addSpaceAfterStr(null, 0);
                // クーポンON/OFF状態
                if (LineGroupUseAcquisitionService.CONST_SHANAITENANTID_1.equals(rtnpotalGroupPlanID)) {
                    ouponOnOff = MvnoUtil.addSpaceAfterStr(
                        this.sOAPCommon.getNodeContent(document, "//GroupProfileInformation/svpnum0_info/aply_srv"), 1);
                } else if (rtnpotalGroupPlanID.equals(LineGroupUseAcquisitionService.CONST_SHANAITENANTID_2)) {
                    ouponOnOff = MvnoUtil.addSpaceAfterStr(
                        this.sOAPCommon.getNodeContent(document, "//GroupProfileInformation/svpnum0_info/optnum0_info/aply_srv"), 1);
                } else if (rtnpotalGroupPlanID.equals(LineGroupUseAcquisitionService.CONST_SHANAITENANTID_3)) {
                    ouponOnOff = MvnoUtil.addSpaceAfterStr(
                        this.sOAPCommon.getNodeContent(document, "//GroupProfileInformation/svpnum1_info/optnum0_info/aply_srv"), 1);
                }
                returnPram.jsonBody.couponOnOff = ouponOnOff;

                // ヘビーユーザ監視規制状態
                returnPram.jsonBody.heavyUserMonitorStatus = MvnoUtil.addSpaceAfterStr(null, 0);

                // ヘビーユーザ監視規制開始時間（月間）
                returnPram.jsonBody.heavyUserMonitorStartTimeMonth = MvnoUtil.addSpaceAfterStr(null, 0);
                // ヘビーユーザ監視規制開始時間（直近）
                returnPram.jsonBody.heavyUserMonitorStartTimeLatest = MvnoUtil.addSpaceAfterStr(null, 0);

                // 適用サービス状態（規制理由）
                returnPram.jsonBody.regulationCause = this.sOAPCommon.getNodeContent(document,
                    "//dyn_grp_info/dyn_node_info_0/dyn_ses_polinfo/dyn_rule_name");
                // 総量規制状態
                let totalVolumeControlStatus = MvnoUtil.addSpaceAfterStr(null, 0);
                // 総量規制状態
                const dyn_reg_sts = this.sOAPCommon.getNodeContent(document,
                    "//dyn_grp_info/dyn_node_info_0/dyn_reg_info/dyn_reg_sts");
                if (LineGroupUseAcquisitionService.CONST_DYN_REG_STS_REGULATED.equals(dyn_reg_sts)) {
                    totalVolumeControlStatus = "1";
                } else if (LineGroupUseAcquisitionService.CONST_DYN_REG_STS_UNREGULATED.equals(dyn_reg_sts)) {
                    totalVolumeControlStatus = "0";
                }
                // 総量規制状態
                returnPram.jsonBody.totalVolumeControlStatus = totalVolumeControlStatus;
            }
        } catch (e) {
            returnPram.jsonBody.responseHeader.processCode = ResultCdConstants.CODE_999999;
            super.error(e, pram.tenantId, pram.requestHeader.sequenceNo, MsgKeysConstants.APCOME0401, e.message);
        }
        // 内部呼出フラグ=trueの場合
        /*if (isPrivate) {
            // LineGroupUseAcquisitionInsideOutputDto returnPramInside = new LineGroupUseAcquisitionInsideOutputDto();
            let errorMessage = "";
            // 変数「処理コード」<>000000である場合
            if (!rtncheatCode.equals(ResultCdConstants.CODE_000000)) {
                // 変数「エラーメッセージ」=空白の場合
                if (StringUtils.isEmpty(errorMessage)) {
                    // エラーメッセージを設定する
                    errorMessage = (super.getMessage(MsgKeysConstants.APCOMW0001, ["回線グループ運用情報参照"]));
                } else  {
                    // エラーメッセージを設定する
                    errorMessage = (errorMessage);
                }
            } else {
                // エラーメッセージを設定する
                errorMessage = ("");
            }
            const returnPramInside: LineGroupUseAcquisitionInsideOutputDto = {
                jsonBody: {
                    ...returnPram.jsonBody,
                    errorMessage
                }
            };
            return this.getInsideReturnPram(returnPramInside, pram.trafficOfCouponOffFlag, document, pram, ...additionArg);
        } else {*/
            return this.getReturnPram(returnPram, getStringParameter(pram.trafficOfCouponOffFlag), document, pram, ...additionArg);
        // }
    }

    /**
     * クーポンOFF時の通信量要否で出力パターンを変更する
     * @param pram 返却値Dto
     * @param couponOffFlg クーポンOFF時の通信量要否
     * @param document SOAP通信結果
     * @param inParam 入力パラメータ
     * @param additionArg 追加引数<br>
     * [0] クーポンOFF時前月通信量
     * [1] クーポンOFF時前々月通信量
     * @return 返却値Dto
     */
    private getReturnPram(
        pram: LineGroupUseAcquisitionOutputDto, couponOffFlg: string,
        document: Document, inParam: LineGroupUseAcquisitionInputDto, ...additionArg: any[]
    ): LineGroupUseAcquisitionOutputDto {
        if ("1" !== couponOffFlg) {
            return pram;
        }
        const trafficPreviousMonthCouponOff = additionArg[0]?.toString();
        const trafficBeforehandMonthCouponOff = additionArg[1]?.toString();
        let returnPram: LineGroupUseAcquisitionCouponOffOutputDto;

        try {
            if ("000000" !== pram.jsonBody.responseHeader.processCode) {
                returnPram = {
                    jsonBody: {
                        ...pram.jsonBody,               // BeanUtils.copyProperties(pram, returnPram);
                        trafficOneDay_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic1dayAgo_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic2daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic3daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic4daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic5daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic6daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic7daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic8daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic9daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic10daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic11daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic12daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic13daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic14daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic15daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic16daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic17daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic18daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic19daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic20daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic21daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic22daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic23daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic24daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic25daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic26daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic27daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic28daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic29daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic30daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        trafficThisMonth_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        trafficPreviousMonth_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        trafficBeforehandMonth_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0)
                    }
                }
            } else {
                const trafficdayAgoAry: string[] = new Array(31);
                let day = Check.getPreXDay(0);
                const tagFlg = this.sOAPCommon.CheckNodeExist(document,
                    `//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_hvy_lst_info/dyn_${day}`);

                if (tagFlg) {
                    for (let i = 0; i <= 30; i++) {
                        day = Check.getPreXDay(i);
                        trafficdayAgoAry[i] = this.sOAPCommon.getNodeContent(document,
                            `//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_hvy_lst_info/dyn_${day}`);
                    }
                } else {
                    for (let i = 1; i <= 31; i++) {
                        day = Check.getPreXDay(i);
                        trafficdayAgoAry[i - 1] = this.sOAPCommon.getNodeContent(document,
                            `//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_hvy_lst_info/dyn_${day}`);
                    }
                }
                returnPram = {
                    jsonBody: {
                        ...pram.jsonBody,               // BeanUtils.copyProperties(pram, returnPram);
                        trafficOneDay_CouponOff: MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[0], 0),
                        traffic1dayAgo_CouponOff: MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[1], 0),
                        traffic2daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[2], 0),
                        traffic3daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[3], 0),
                        traffic4daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[4], 0),
                        traffic5daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[5], 0),
                        traffic6daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[6], 0),
                        traffic7daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[7], 0),
                        traffic8daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[8], 0),
                        traffic9daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[9], 0),
                        traffic10daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[10], 0),
                        traffic11daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[11], 0),
                        traffic12daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[12], 0),
                        traffic13daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[13], 0),
                        traffic14daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[14], 0),
                        traffic15daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[15], 0),
                        traffic16daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[16], 0),
                        traffic17daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[17], 0),
                        traffic18daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[18], 0),
                        traffic19daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[19], 0),
                        traffic20daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[20], 0),
                        traffic21daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[21], 0),
                        traffic22daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[22], 0),
                        traffic23daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[23], 0),
                        traffic24daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[24], 0),
                        traffic25daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[25], 0),
                        traffic26daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[26], 0),
                        traffic27daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[27], 0),
                        traffic28daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[28], 0),
                        traffic29daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[29], 0),
                        traffic30daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[30], 0),
                        trafficThisMonth_CouponOff: MvnoUtil.addSpaceAfterStr(this.sOAPCommon.getNodeContent(document,
                            "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_hvy_month_info/dyn_use"), 0),
                        trafficPreviousMonth_CouponOff: MvnoUtil.addSpaceAfterStr(trafficPreviousMonthCouponOff, 0),
                        trafficBeforehandMonth_CouponOff: MvnoUtil.addSpaceAfterStr(trafficBeforehandMonthCouponOff, 0)
                    }
                }
            }
        } catch (e) {
            super.error(e, inParam.tenantId, inParam.requestHeader.sequenceNo,
                MsgKeysConstants.APCOME0401, e.message);
            returnPram.jsonBody.responseHeader.processCode = ResultCdConstants.CODE_999999;
            return returnPram;
        }
        return returnPram;
    }

    /**
     * クーポンOFF時の通信量要否で出力パターンを変更する
     * @param pram 返却値Dto
     * @param couponOffFlg クーポンOFF時の通信量要否
     * @param document SOAP通信結果
     * @param inParam 入力パラメータ
     * @param additionArg 追加引数<br>
     * [0] クーポンOFF時前月通信量
     * [1] クーポンOFF時前々月通信量
     * @return 返却値Dto
     */
    /* public getInsideReturnPram(
        pram: LineGroupUseAcquisitionInsideOutputDto, couponOffFlg: string,
        document: Document, inParam: LineGroupUseAcquisitionInputDto, ...additionArg: any[]
    ): LineGroupUseAcquisitionInsideOutputDto {
        if (couponOffFlg !== "1") {
            return pram;
        }
        const trafficPreviousMonthCouponOff = String(additionArg[0]);
        const trafficBeforehandMonthCouponOff = String(additionArg[1]);
        let returnPram: LineGroupUseAcquisitionCouponOffInsideOutputDto;

        try {
            if ("000000" !== pram.jsonBody.responseHeader.processCode) {
                returnPram = {
                    jsonBody: {
                        ...pram.jsonBody,               // BeanUtils.copyProperties(pram, returnPram);
                        trafficOneDayCouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic1dayAgoCouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic2daysAgoCouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic3daysAgoCouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic4daysAgoCouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic5daysAgoCouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic6daysAgoCouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic7daysAgoCouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic8daysAgoCouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic9daysAgoCouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic10daysAgoCouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic11daysAgoCouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic12daysAgoCouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic13daysAgoCouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic14daysAgoCouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic15daysAgoCouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic16daysAgoCouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic17daysAgoCouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic18daysAgoCouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic19daysAgoCouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic20daysAgoCouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic21daysAgoCouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic22daysAgoCouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic23daysAgoCouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic24daysAgoCouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic25daysAgoCouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic26daysAgoCouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic27daysAgoCouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic28daysAgoCouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic29daysAgoCouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        traffic30daysAgoCouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        trafficThisMonthCouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        trafficPreviousMonthCouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                        trafficBeforehandMonthCouponOff: MvnoUtil.addSpaceAfterStr(null, 0)
                    }
                }
            } else {
                const trafficdayAgoAry: string[] = new Array(31);
                let day = Check.getPreXDay(0);
                const tagFlg = this.sOAPCommon.CheckNodeExist(document,
                    "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_hvy_lst_info/dyn_" + day);

                if (tagFlg) {
                    for (let i = 0; i <= 30; i++) {
                        day = Check.getPreXDay(i);
                        trafficdayAgoAry[i] = this.sOAPCommon.getNodeContent(document,
                            "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_hvy_lst_info/dyn_" + day);
                    }
                } else {
                    for (let i = 1; i <= 31; i++) {
                        day = Check.getPreXDay(i);
                        trafficdayAgoAry[i - 1] = this.sOAPCommon.getNodeContent(document,
                            "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_hvy_lst_info/dyn_" + day);
                    }
                }
                returnPram = {
                    jsonBody: {
                        ...pram.jsonBody,               // BeanUtils.copyProperties(pram, returnPram);
                        trafficOneDayCouponOff: MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[0], 0),
                        traffic1dayAgoCouponOff: MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[1], 0),
                        traffic2daysAgoCouponOff: MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[2], 0),
                        traffic3daysAgoCouponOff: MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[3], 0),
                        traffic4daysAgoCouponOff: MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[4], 0),
                        traffic5daysAgoCouponOff: MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[5], 0),
                        traffic6daysAgoCouponOff: MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[6], 0),
                        traffic7daysAgoCouponOff: MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[7], 0),
                        traffic8daysAgoCouponOff: MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[8], 0),
                        traffic9daysAgoCouponOff: MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[9], 0),
                        traffic10daysAgoCouponOff: MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[10], 0),
                        traffic11daysAgoCouponOff: MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[11], 0),
                        traffic12daysAgoCouponOff: MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[12], 0),
                        traffic13daysAgoCouponOff: MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[13], 0),
                        traffic14daysAgoCouponOff: MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[14], 0),
                        traffic15daysAgoCouponOff: MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[15], 0),
                        traffic16daysAgoCouponOff: MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[16], 0),
                        traffic17daysAgoCouponOff: MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[17], 0),
                        traffic18daysAgoCouponOff: MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[18], 0),
                        traffic19daysAgoCouponOff: MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[19], 0),
                        traffic20daysAgoCouponOff: MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[20], 0),
                        traffic21daysAgoCouponOff: MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[21], 0),
                        traffic22daysAgoCouponOff: MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[22], 0),
                        traffic23daysAgoCouponOff: MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[23], 0),
                        traffic24daysAgoCouponOff: MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[24], 0),
                        traffic25daysAgoCouponOff: MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[25], 0),
                        traffic26daysAgoCouponOff: MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[26], 0),
                        traffic27daysAgoCouponOff: MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[27], 0),
                        traffic28daysAgoCouponOff: MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[28], 0),
                        traffic29daysAgoCouponOff: MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[29], 0),
                        traffic30daysAgoCouponOff: MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[30], 0),
                        trafficThisMonthCouponOff: MvnoUtil.addSpaceAfterStr(this.sOAPCommon.getNodeContent(document,
                            "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_hvy_month_info/dyn_use"), 0),
                        trafficPreviousMonthCouponOff: MvnoUtil.addSpaceAfterStr(trafficPreviousMonthCouponOff, 0),
                        trafficBeforehandMonthCouponOff: MvnoUtil.addSpaceAfterStr(trafficBeforehandMonthCouponOff, 0)
                    }
                }
            }
        } catch (e) {
            super.error(e, inParam.tenantId, inParam.requestHeader.sequenceNo,
                MsgKeysConstants.APCOME0401, e.message);
            returnPram.jsonBody.responseHeader.processCode = ResultCdConstants.CODE_999999;
            return returnPram;
        }
        return returnPram;
    }*/

    public generateErrorMessage(tenantId: string,
                                sequenceNo: string,
                                msgKey: string,
                                ...params: any[]): void
    {
        super.error(tenantId, sequenceNo, msgKey, ...params);
        return;
    }
}