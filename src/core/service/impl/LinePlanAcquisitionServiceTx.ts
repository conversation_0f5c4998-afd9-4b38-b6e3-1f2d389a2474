import RESTCommon from "@/core/common/RESTCommon";
import { IFService } from "@/core/service/IFService";
import LinePlanAcquisitionInputDto from "@/core/dto/LinePlanAcquisitionInputDto";
import LinePlanAcquisitionOutputDto from "@/core/dto/LinePlanAcquisitionOutputDto";
import ResultCdConstants from "@/core/constant/ResultCdConstants";
import APILinesDAO from "@/core/dao/APILinesDAO";
import APILinesGroupDAO from "@/core/dao/ApiLinesGroupDAO";
import SOAPCommon from "@/core/common/SOAPCommon";
import APISoDAO from "@/core/dao/APISoDAO";
import SOCommon from "@/core/common/SOCommon";
import TenantManage from "@/core/common/TenantManage";
import config from "config";
import CheckResultBean from "@/core/dto/CheckResultBean";
import SOObject from "@/core/dto/SOObject";
import Constants from "@/core/constant/Constants";
import MvnoUtil from "@/core/common/MvnoUtil";
import ParameterName from "@/core/dto/ParameterName";
import ApiCommon from "@/core/common/ApiCommon";
import APICommonDAO from "@/core/dao/APICommonDAO";
import MsgKeysConstants from "@/core/constant/MsgKeysConstants";
import Check from "@/core/common/Check";
import SOAPCommonOutputDto from "@/core/dto/SOAPCommonOutputDto";
import StringUtils from "@/core/common/StringUtils";
import { isSQLException, retryQuery } from "@/helpers/queryHelper";
import SOAPException from "@/types/soapException";
import { Transaction } from "sequelize";
import { TenantsEntity } from "@/core/entity/TenantsEntity";
import { isLockNotAvailableError, usePsql } from "@/database/psql";
import CheckUtil from "@/core/common/CheckUtil";
import PlansEntity from "@/core/entity/PlansEntity";
import LineGroupsEntity from "@/core/entity/LineGroupsEntity";
import { GroupPlansEntity } from "@/core/entity/GroupPlansEntity";
import { LinesEntity } from "@/core/entity/LinesEntity";
import { format, fromUnixTime, formatDate, parse } from "date-fns";
import TenantPlansEntity from "@/core/entity/TenantPlansEntity";
import { getConfig } from "@/helpers/configHelper";
import { isNone } from "@/utils";
import { getStringParameter } from "@/types/parameter.string";
/**
 * 回線プラン変更機能
 */
export default class LinePlanAcquisitionServiceTx
    extends RESTCommon
    implements
        IFService<LinePlanAcquisitionInputDto, LinePlanAcquisitionOutputDto>
{
    private readonly CONST_RESULT = "NG";

    /**
     * API共通DAO
     */
    private aPICommonDAO = new APICommonDAO(this.request, this.context);

    /**
     * API回線DAO
     */
    private aPILinesDAO = new APILinesDAO(this.request, this.context);

    /**
     * API回線グループDAO
     */
    private aPILinesGroupDAO = new APILinesGroupDAO(this.request, this.context);

    /**
     * SOAP API連携機能初期化
     */
    private sOAPCommon = new SOAPCommon(this.request, this.context);

    /**
     * API SO DAOクラス
     */
    private aPISoDAO = new APISoDAO(this.request, this.context);

    /** API共通処理 */
    private apiCommon = new ApiCommon(this.request, this.context);

    /**
     * SO管理共通DAO
     */
    private soCommon = new SOCommon(this.request, this.context);

    /**
     * テナント管理クラス
     */
    private tenantManage = new TenantManage(this.request, this.context);

    /**
     * プラン変更不可時間帯(予約機能：プラン変のみ)
     */
    private planChangeNgTime = getConfig("PlanChangeNgTime");
    /**
     * 予約実行日時単位(予約機能)
     */
    private reservationDateExecutionUnits: string = getConfig("ReservationDateExecutionUnits");
    private readonly LOCK_WAIT_MILLISEC = 1000;
    /**
     * 予約可能制限日数(予約機能)
     */
    private reservationsLimitDays: string = getConfig("ReservationsLimitDays");

    /**
     * Helper function to safely cleanup transaction and return output
     * CRITICAL: This prevents transaction leaks by ensuring proper cleanup
     */
    private async cleanupTransactionAndReturn(
        tx: Transaction | null,
        output: LinePlanAcquisitionOutputDto,
    ): Promise<LinePlanAcquisitionOutputDto> {
        if (tx) {
            try {
                await tx.rollback();
                this.context.log("LinePlanAcquisitionServiceTx: Transaction rolled back before return");
            } catch (e) {
                this.context.warn("LinePlanAcquisitionServiceTx: Error during transaction rollback", e);
            }
        }
        return output;
    }

    /**
     * 回線プラン変更機能。<BR>
     *
     * <PRE>
     * REST APIにより、受信したパラメータの回線プラン変更機能機能を取得する。
     * 回線プラン変更機能機能を提供する。
     * </PRE>
     *
     * @param param 回線プラン変更機能取得インプット
     * @param isPrivate 内部呼出フラグ
     * @param params 可変長さ引数(内部から呼出す時に使用するパラメータ)
     * @return LinePlanAcquisitionOutputDto 回線プラン変更機能アウトプット
     * @throws Exception
     */
    public async service(
        param: LinePlanAcquisitionInputDto,
        isPrivate: boolean,
        ...params: any[]
    ): Promise<LinePlanAcquisitionOutputDto> {
        super.debug(
            param.tenantId,
            param.requestHeader.sequenceNo,
            MsgKeysConstants.APLPCD0001,
            this.getClassName(),
            "service",
            JSON.stringify(param),
        );
        // REST API共通処理が呼び出されたシステム日付
        const receivedDate = MvnoUtil.getDateTimeNow();

        // Session session = null;
        // Transaction tx = null;

        // 回線プラン変更初期化
        // Object output = null;
        // let output:Object = null;
        // 変数「処理コード」初期化
        let handleCode = ResultCdConstants.CODE_000000;
        // 送信番号取得
        const sequenceNo = param.requestHeader.sequenceNo;
        // 送信元システムID取得
        const senderSystemId = param.requestHeader.senderSystemId;
        // API認証キー取得
        // String apiKey = param.getRequestHeader().getApiKey();
        // 機能種別取得
        const functionType = param.requestHeader.functionType;
        // テナントID取得
        const tenantId = param.tenantId;
        // 回線番号取得
        const lineNo = param.lineNo;
        // サービスプランID
        let servicePlanId: string = null;
        // 再販料金プラン
        let pricePlanId: string = null;
        // 再販料金プラン
        let pricePlanIdAfter: string = null;
        // 再販料金プラン名
        let pricePlanIdAfterName: string = null;
        // 変更前卸ポータルプランID
        const potalPlanIDPre = getStringParameter(param.potalPlanID_pre) ?? "";
        // 変更後卸ポータルプランID
        const potalPlanID = getStringParameter(param.potalPlanID) ?? "";
        // 共通チェック初期化
        // CheckResultBean resultBean = new CheckResultBean();
        // 変数「エラーメッセージ」
        const errorMessage: string = null;
        // 変数「実行ユーザID」を定義する
        const executeUserId: string = null;
        // 変数「実行テナントID」を定義する
        const executeTenantId: string = null;
        // SO電文作成した、SOAPのAPIを接続する
        let soapCommonOutputDto: SOAPCommonOutputDto = null;
        // 回線プラン変更データ情報結果初期化
        let document: Document = null;
        // 予約日
        const reserveDate = param.reserve_date;
        // 予約フラグ
        const reserveFlg = param.reserve_flag;
        // ＳＯ－ＩＤ
        const reserveSoId = param.reserve_soId;
        // 予約実行可否フラグ
        let reserveActFlag = false;
        // 変数「プラン変更タイミング結果」
        let changeTiminglst: string[] = null;
        // 変数「オーダ種別」初期化
        const orderType = Check.checkOrderType(
            reserveDate,
            reserveFlg,
            reserveSoId,
        );

        // 変数「回線グループID」
        let groupIdlst: string[] = null;
        // 変数「グループON/OFFフラグ」
        let groupOnOffFlag: string = null;
        // 変数「MVNO顧客CSV連携不要フラグ」
        const csvUnnecessaryFlag = getStringParameter(param.csvUnnecessaryFlag);
        // 2回目のSO電文作成
        let soapCommonOutputDto2KaiMe: SOAPCommonOutputDto = null;
        // 回線プラン変更データ情報結果初期化(2回目)
        let document2KaiMe: Document = null;
        // 変数「CSV作成要否」
        let csvOutputKbn: string = null;
        // 変数「ファイル名通番」
        const fileNameNo: string = null;
        // 変数．「初期SIMフラグ」
        let shokiSIMFlag: string = null;
        // 変数「料金プランID」取得
        let resalePlanIdPlanList: string[] = null;
        // 変数「変更後のプランT番」取得
        let newPlanIDT: string = null;
        // 変数「テナント情報」.[社内テナント種別]取得
        let tenantTypeCheck: string = null;
        // 変数「プラン変更パターン定型フラグ」
        let planChangeFlag: number[] = null;
        // 変数「変更前プランプラン変更種別」

        let planChangeClassPre: number[] = null;
        // 変数「変更後プランプラン変更種別」
        let planChangeClass: number[] = null;
        // 基本容量
        let basicCapacity: number = null;
        // 変数「REST共通チェック結果」
        // 変数「API処理ID」
        const apiHandleId = this.context.responseHeader.apiProcessID;

        const sequelize = await usePsql();
        let tx: Transaction = null;
        try {
            // 業務チェック
            // オーダ種別チェック
            // 変数「オーダ種別」より判定する
            if (Constants.ORDER_TYPE_9.equals(orderType)) {
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APLPCW0012);
                // 変数「処理コード」設定
                handleCode = ResultCdConstants.CODE_060105;
                // 返却値編集
                const output = this.returnEdit(
                    param,
                    handleCode,
                    apiHandleId,
                    receivedDate,
                );
                super.debug(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APLPCD0002,
                    this.getClassName(),
                    "service",
                );
                return output;
            }

            // 予約実行オーダの場合
            if (Constants.ORDER_TYPE_2.equals(orderType)) {
                // APサーバの複数IP取得
                const localIpList: string[] = MvnoUtil.getLocalIpList();
                // 予約実行可否チェック
                if (!this.context.isInternalRequest) {
                    reserveActFlag = true;
                    // ログ出力
                    super.error(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APCOME0401,
                        "予約実行できませんでした。クライアントIP:" +
                            String(params[2]) +
                            ", APサーバーIP：" +
                            MvnoUtil.getOutputList(localIpList),
                    );
                    // 変数「処理コード」設定
                    handleCode = ResultCdConstants.CODE_999999;
                    // 返却値編集
                    const output = this.returnEdit(
                        param,
                        handleCode,
                        apiHandleId,
                        receivedDate,
                    );
                    super.debug(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APLPCD0002,
                        this.getClassName(),
                        "service",
                    );
                    return output;
                }
            }

            // 回線番号フォーマットチェック
            if (!Check.checkLineNo(lineNo)) {
                // ログ出力
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APLPCW0001,
                    "回線番号",
                    lineNo,
                );
                // 変数「処理コード」設定
                handleCode = ResultCdConstants.CODE_060101;
                // 返却値編集
                const output = this.returnEdit(
                    param,
                    handleCode,
                    apiHandleId,
                    receivedDate,
                );
                super.debug(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APLPCD0002,
                    this.getClassName(),
                    "service",
                );
                return output;
            }

            // 変更前プランIDフォーマットチェック
            if (!Check.checkPlanIDFmt(potalPlanIDPre)) {
                // ログ出力
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APLPCW0001,
                    "変更前卸ポータルプランID",
                    potalPlanIDPre,
                );
                // 変数「処理コード」設定
                handleCode = ResultCdConstants.CODE_060101;
                // 返却値編集
                const output = this.returnEdit(
                    param,
                    handleCode,
                    apiHandleId,
                    receivedDate,
                );
                super.debug(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APLPCD0002,
                    this.getClassName(),
                    "service",
                );
                return output;
            }

            // 変更後プランIDフォーマットチェック
            if (!Check.checkPlanIDFmt(potalPlanID)) {
                // ログ出力
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APLPCW0001,
                    "変更後卸ポータルプランID",
                    potalPlanID,
                );
                // 変数「処理コード」設定
                handleCode = ResultCdConstants.CODE_060101;
                // 返却値編集
                const output = this.returnEdit(
                    param,
                    handleCode,
                    apiHandleId,
                    receivedDate,
                );
                super.debug(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APLPCD0002,
                    this.getClassName(),
                    "service",
                );
                return output;
            }

            // 変更前プランIDと変更後プランID相関チェック
            if (potalPlanID.equals(potalPlanIDPre)) {
                // ログ出力
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APLPCW0001,
                    "変更前卸ポータルプランIDと変更後卸ポータルプランIDが一致している",
                    potalPlanIDPre,
                );
                // 変数「処理コード」設定
                handleCode = ResultCdConstants.CODE_060101;
                // 返却値編集
                const output = this.returnEdit(
                    param,
                    handleCode,
                    apiHandleId,
                    receivedDate,
                );
                super.debug(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APLPCD0002,
                    this.getClassName(),
                    "service",
                );
                return output;
            }

            // 予約前オーダと予約実行オーダの場合
            if (
                Constants.ORDER_TYPE_1.equals(orderType) ||
                Constants.ORDER_TYPE_2.equals(orderType)
            ) {
                // 5 予約日フォーマットチェック
                if (!Check.checkReserveDateFmt(reserveDate)) {
                    // ログ出力
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APLPCW0001,
                        "予約日",
                        reserveDate,
                    );
                    // 変数「処理コード」設定
                    handleCode = ResultCdConstants.CODE_060101;
                    // 返却値編集
                    const output = this.returnEdit(
                        param,
                        handleCode,
                        apiHandleId,
                        receivedDate,
                    );
                    super.debug(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APLPCD0002,
                        this.getClassName(),
                        "service",
                    );
                    return output;
                }
            }

            if (
                Constants.ORDER_TYPE_0.equals(orderType) ||
                Constants.ORDER_TYPE_1.equals(orderType)
            ) {
                // MVNO顧客CSV連携不要フラグフォーマットチェック
                if (
                    StringUtils.isNotEmpty(csvUnnecessaryFlag) &&
                    !"0".equals(csvUnnecessaryFlag) &&
                    !"1".equals(csvUnnecessaryFlag)
                ) {
                    // ログ出力
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APLPCW0001,
                        "MVNO顧客CSV連携不要フラグ",
                        csvUnnecessaryFlag,
                    );
                    // 変数「処理コード」設定
                    handleCode = ResultCdConstants.CODE_060101;
                    // 返却値編集
                    const output = this.returnEdit(
                        param,
                        handleCode,
                        apiHandleId,
                        receivedDate,
                    );
                    super.debug(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APLPCD0002,
                        this.getClassName(),
                        "service",
                    );
                    return output;
                }
            }

            // ５ プラン変更不可時間帯フォーマットチェック
            if (!Check.checkPlanChangeNgTimeFmt(this.planChangeNgTime)) {
                // ログ出力
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APLPCW0013,
                    this.planChangeNgTime,
                );
                // 変数「処理コード」設定
                handleCode = ResultCdConstants.CODE_060106;
                // 返却値編集
                const output = this.returnEdit(
                    param,
                    handleCode,
                    apiHandleId,
                    receivedDate,
                );
                super.debug(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APLPCD0002,
                    this.getClassName(),
                    "service",
                );
                return output;
            }

            // プラン変更不可時間帯範囲チェック
            let time: string = null;
            if (
                Constants.ORDER_TYPE_0.equals(orderType) ||
                Constants.ORDER_TYPE_2.equals(orderType)
            ) {
                time = receivedDate;
            } else {
                time = reserveDate;
            }
            if (!Check.checkPlanChangeNgTime(time, this.planChangeNgTime)) {
                if (
                    Constants.ORDER_TYPE_0.equals(orderType) ||
                    Constants.ORDER_TYPE_2.equals(orderType)
                ) {
                    // ログ出力
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APLPCW0014,
                        this.planChangeNgTime,
                    );
                    // 変数「処理コード」設定
                    handleCode = ResultCdConstants.CODE_060107;
                }
                if (Constants.ORDER_TYPE_1.equals(orderType)) {
                    // ログ出力
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APLPCW0016,
                        this.planChangeNgTime,
                        reserveDate,
                    );
                    // 変数「処理コード」設定
                    handleCode = ResultCdConstants.CODE_060109;
                }
                // 返却値編集
                const output = this.returnEdit(
                    param,
                    handleCode,
                    apiHandleId,
                    receivedDate,
                );
                super.debug(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APLPCD0002,
                    this.getClassName(),
                    "service",
                );
                return output;
            }

            if (Constants.ORDER_TYPE_1.equals(orderType)) {
                // 予約前オーダ:８ 予約日と投入日相関チェック
                if (
                    !Check.checkReserveDateOrderDate(receivedDate, reserveDate)
                ) {
                    // ログ出力
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APLPCW0017,
                        reserveDate,
                        receivedDate,
                    );
                    // 変数「処理コード」設定
                    handleCode = ResultCdConstants.CODE_060110;
                    // 返却値編集
                    const output = this.returnEdit(
                        param,
                        handleCode,
                        apiHandleId,
                        receivedDate,
                    );
                    super.debug(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APLPCD0002,
                        this.getClassName(),
                        "service",
                    );
                    return output;
                }

                // 予約前オーダ:９ 予約実行日時単位フォーマットチェック
                if (
                    !Check.checkReservationDateExecutionUnitsFmt(
                        this.reservationDateExecutionUnits,
                    )
                ) {
                    // ログ出力
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APLPCW0018,
                        this.reservationDateExecutionUnits,
                    );
                    // 変数「処理コード」設定
                    handleCode = ResultCdConstants.CODE_060111;
                    // 返却値編集
                    const output = this.returnEdit(
                        param,
                        handleCode,
                        apiHandleId,
                        receivedDate,
                    );
                    super.debug(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APLPCD0002,
                        this.getClassName(),
                        "service",
                    );
                    return output;
                }

                // 予約前オーダ:１０ 予約実行日時単位と予約日相関チェックチェック
                if (
                    !Check.checkReservationDateExecutionUnits(
                        this.reservationDateExecutionUnits,
                        reserveDate,
                    )
                ) {
                    // ログ出力
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APLPCW0019,
                        this.reservationDateExecutionUnits,
                        reserveDate,
                    );
                    // 変数「処理コード」設定
                    handleCode = ResultCdConstants.CODE_060112;
                    // 返却値編集
                    const output = this.returnEdit(
                        param,
                        handleCode,
                        apiHandleId,
                        receivedDate,
                    );
                    super.debug(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APLPCD0002,
                        this.getClassName(),
                        "service",
                    );
                    return output;
                }

                // 予約前オーダ:１１ 予約可能制限日数フォーマットチェック
                if (
                    !Check.checkReservationsLimitDaysFmt(
                        this.reservationsLimitDays,
                    )
                ) {
                    // ログ出力
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APLPCW0020,
                        this.reservationsLimitDays,
                    );
                    // 変数「処理コード」設定
                    handleCode = ResultCdConstants.CODE_060113;
                    // 返却値編集
                    const output = this.returnEdit(
                        param,
                        handleCode,
                        apiHandleId,
                        receivedDate,
                    );
                    super.debug(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APLPCD0002,
                        this.getClassName(),
                        "service",
                    );
                    return output;
                }

                // 予約前オーダ:１２ 予約可能制限日数範囲チェック
                if (
                    !Check.checkReservationsLimitDays(
                        this.reservationsLimitDays,
                        reserveDate,
                    )
                ) {
                    // ログ出力
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APLPCW0021,
                        this.reservationsLimitDays,
                        reserveDate,
                    );
                    // 変数「処理コード」設定
                    handleCode = ResultCdConstants.CODE_060114;
                    // 返却値編集
                    const output = this.returnEdit(
                        param,
                        handleCode,
                        apiHandleId,
                        receivedDate,
                    );
                    super.debug(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APLPCD0002,
                        this.getClassName(),
                        "service",
                    );
                    return output;
                }
            }

            // 即時オーダ,予約前オーダ
            if (
                Constants.ORDER_TYPE_0.equals(orderType) ||
                Constants.ORDER_TYPE_1.equals(orderType)
            ) {
                // 即時オーダ,予約前オーダ:７ 予約チェック
                let checkReserveResult = false;
                try {
                    checkReserveResult = await Check.checkReserveOrder(
                        lineNo,
                        this.aPISoDAO,
                    );
                } catch (e) {
                    if (isSQLException(e)) {
                        // 変数「処理コード」設定
                        handleCode = ResultCdConstants.CODE_060108;
                    }
                    throw e;
                }
                if (!checkReserveResult) {
                    // ログ出力
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APLPCW0015,
                        lineNo,
                    );
                    // 変数「処理コード」設定
                    handleCode = ResultCdConstants.CODE_060108;
                    // 返却値編集
                    const output = this.returnEdit(
                        param,
                        handleCode,
                        apiHandleId,
                        receivedDate,
                    );
                    super.debug(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APLPCD0002,
                        this.getClassName(),
                        "service",
                    );
                    return output;
                }
            }

            // プラン変更タイミング判定
            const planChangeTimingResult = await retryQuery(
                this.context,
                "LinePlanAcquisition.service get planChange Timing",
                async () => {
                    try {
                        tx = await sequelize.transaction();
                        // トランザクション開始
                        try {
                            // テナントテーブルから「プラン変更パターン定型フラグ」を取得
                            planChangeFlag =
                                await this.aPICommonDAO.getPlanChangeFlag(
                                    tenantId,
                                    tx,
                                );
                            // プランテーブルから変更前プランの「プラン変更種別」を取得
                            planChangeClassPre =
                                await this.aPICommonDAO.getPlanChangeClass(
                                    potalPlanIDPre,
                                    tx,
                                );
                            // プランテーブルから変更後プランの「プラン変更種別」を取得
                            planChangeClass =
                                await this.aPICommonDAO.getPlanChangeClass(
                                    potalPlanID,
                                    tx,
                                );
                        } catch (e) {
                            throw e;
                        }
                        // 取得件数が0件でないことを確認
                        if (
                            CheckUtil.checkIsNull(planChangeFlag) ||
                            planChangeFlag.length === 0 ||
                            CheckUtil.checkIsNull(planChangeClassPre) ||
                            planChangeClassPre.length === 0 ||
                            CheckUtil.checkIsNull(planChangeClass) ||
                            planChangeClass.length === 0
                        ) {
                            let msgKeysConstants: string = null;
                            if (
                                Constants.ORDER_TYPE_0.equals(orderType) ||
                                Constants.ORDER_TYPE_2.equals(orderType)
                            ) {
                                msgKeysConstants = MsgKeysConstants.APLPCW0031;
                                // 変数「処理コード」設定
                                handleCode = ResultCdConstants.CODE_060131;
                            } else if (
                                Constants.ORDER_TYPE_1.equals(orderType)
                            ) {
                                msgKeysConstants = MsgKeysConstants.APLPCW0032;
                                // 変数「処理コード」設定
                                handleCode = ResultCdConstants.CODE_060132;
                            }
                            // ログ出力
                            super.warn(
                                tenantId,
                                sequenceNo,
                                msgKeysConstants,
                                reserveDate,
                            );
                            // 返却値編集
                            const output = this.returnEdit(
                                param,
                                handleCode,
                                apiHandleId,
                                receivedDate,
                            );
                            super.debug(
                                tenantId,
                                sequenceNo,
                                MsgKeysConstants.APLPCD0002,
                                this.getClassName(),
                                "service",
                            );
                            // CRITICAL FIX: Use helper to cleanup transaction before return
                            return await this.cleanupTransactionAndReturn(tx, output);
                        }
                        // プラン変更パターン定型フラグが1（帯域卸タイプ）
                        // もしくは、プラン変更パターン定型フラグが3（社内システムタイプ）の場合
                        if (
                            Number(1) === planChangeFlag[0] ||
                            Number(3) === planChangeFlag[0]
                        ) {
                            // プラン変更タイミング＝null
                            changeTiminglst = [];
                            changeTiminglst.push(null);
                        } else if (Number(2) === planChangeFlag[0]) {
                            // 変更前、変更後プラン共にプラン変更種別が通常プラン(0 or null)の場合
                            if (
                                (Number(0) === planChangeClassPre[0] ||
                                    CheckUtil.checkIsNull(
                                        planChangeClassPre[0],
                                    )) &&
                                (Number(0) === planChangeClass[0] ||
                                    CheckUtil.checkIsNull(planChangeClass[0]))
                            ) {
                                // プラン変更タイミング＝月初
                                changeTiminglst = [];
                                changeTiminglst.push("月初");
                            } else {
                                // プラン変更タイミング＝null
                                changeTiminglst = [];
                                changeTiminglst.push(null);
                            }
                        } else {
                            changeTiminglst =
                                await this.aPILinesDAO.getChangeTiming(
                                    tenantId,
                                    potalPlanIDPre,
                                    potalPlanID,
                                );
                        }

                        let day: string = null;
                        if (
                            Constants.ORDER_TYPE_0.equals(orderType) ||
                            Constants.ORDER_TYPE_2.equals(orderType)
                        ) {
                            day = receivedDate.substring(8, 10);
                        } else {
                            day = reserveDate.substring(8, 10);
                        }
                        // null: いつでも変更可能 、月初: 月初のみ可能
                        if (
                            changeTiminglst.length === 0 ||
                            (StringUtils.isNotEmpty(changeTiminglst[0]) &&
                                !"月初".equals(changeTiminglst[0])) ||
                            ("月初".equals(changeTiminglst[0]) &&
                                Number(day) !== 1)
                        ) {
                            let msgKeysConstants: string = null;

                            if (
                                Constants.ORDER_TYPE_0.equals(orderType) ||
                                Constants.ORDER_TYPE_2.equals(orderType)
                            ) {
                                msgKeysConstants = MsgKeysConstants.APLPCW0031;
                                // 変数「処理コード」設定
                                handleCode = ResultCdConstants.CODE_060131;
                            } else if (
                                Constants.ORDER_TYPE_1.equals(orderType)
                            ) {
                                msgKeysConstants = MsgKeysConstants.APLPCW0032;
                                // 変数「処理コード」設定
                                handleCode = ResultCdConstants.CODE_060132;
                            }
                            // ログ出力
                            super.warn(
                                tenantId,
                                sequenceNo,
                                msgKeysConstants,
                                reserveDate,
                            );
                            // 返却値編集
                            const output = this.returnEdit(
                                param,
                                handleCode,
                                apiHandleId,
                                receivedDate,
                            );
                            super.debug(
                                tenantId,
                                sequenceNo,
                                MsgKeysConstants.APLPCD0002,
                                this.getClassName(),
                                "service",
                            );
                            // CRITICAL FIX: Use helper to cleanup transaction before return
                            return await this.cleanupTransactionAndReturn(tx, output);
                        }
                    } catch (e) {
                        if (isSQLException(e)) {
                            if (
                                Constants.ORDER_TYPE_0.equals(orderType) ||
                                Constants.ORDER_TYPE_2.equals(orderType)
                            ) {
                                // 変数「処理コード」設定
                                handleCode = ResultCdConstants.CODE_060131;
                            } else if (
                                Constants.ORDER_TYPE_1.equals(orderType)
                            ) {
                                // 変数「処理コード」設定
                                handleCode = ResultCdConstants.CODE_060132;
                            }
                        }
                        await tx.rollback();
                        tx = null;
                        throw e;
                    }
                },
                this.apDbRetryMaxCnt,
                this.apDbRetryInterval,
            );
            if (planChangeTimingResult) {
                return planChangeTimingResult;
            }
            handleCode = ResultCdConstants.CODE_000000;

            // 社内テナント種別チェック
            let tenantsEntity: TenantsEntity = null;
            await retryQuery(
                this.context,
                "LinePlanAc.service tenant check",
                async () => {
                    try {
                        if (!tx) {
                            tx = await sequelize.transaction();
                        }
                        tenantsEntity = await this.apiCommonDAO.getTenants(
                            tenantId,
                            tx,
                        );
                    } catch (e) {
                        await tx.rollback();
                        tx = null;
                        if (isSQLException(e)) {
                            handleCode = ResultCdConstants.CODE_060102;
                        }
                        throw e;
                    }
                },
                this.apDbRetryMaxCnt,
                this.apDbRetryInterval,
            );
            handleCode = ResultCdConstants.CODE_000000;
            if (tenantsEntity === null) {
                // ログ出力
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APLPCW0002);
                // 変数「処理コード」設定
                handleCode = ResultCdConstants.CODE_060102;
                // 返却値編集
                const output = this.returnEdit(
                    param,
                    handleCode,
                    apiHandleId,
                    receivedDate,
                );
                super.debug(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APLPCD0002,
                    this.getClassName(),
                    "service",
                );
                return output;
            }

            let checkResult: [boolean, string] = null;
            const result = await retryQuery(
                this.context,
                "LinePlanAcquisitionServiceTx.service update logic",
                async () => {
                    try {
                        if (!tx) {
                            tx = await sequelize.transaction();
                        }
                        if (
                            Constants.ORDER_TYPE_0.equals(orderType) ||
                            Constants.ORDER_TYPE_2.equals(orderType)
                        ) {
                            // 行ロック取得し、回線情報を取得
                            for (
                                let linelockTime = 0;
                                linelockTime < 5;
                                linelockTime++
                            ) {
                                try {
                                    // 回線情報テーブルから情報を取得する
                                    const linesEntity =
                                        await this.aPILinesDAO.getLineInfoforUpdate(
                                            lineNo,
                                            tx,
                                        );
                                    if (
                                        linesEntity === null ||
                                        linesEntity.equals("")
                                    ) {
                                        // ログ出力
                                        super.warn(
                                            tenantId,
                                            sequenceNo,
                                            MsgKeysConstants.APLPCW0003,
                                            lineNo,
                                        );
                                        // 変数「処理コード」設定
                                        handleCode =
                                            ResultCdConstants.CODE_060201;
                                        // 返却値編集
                                        const output = this.returnEdit(
                                            param,
                                            handleCode,
                                            apiHandleId,
                                            receivedDate,
                                        );

                                        super.debug(
                                            tenantId,
                                            sequenceNo,
                                            MsgKeysConstants.APLPCD0002,
                                            this.getClassName(),
                                            "service",
                                        );
                                        // CRITICAL FIX: Use helper to cleanup transaction before return
                                        return await this.cleanupTransactionAndReturn(tx, output);
                                    }
                                    break;
                                } catch (e) {
                                    if (isLockNotAvailableError(e)) {
                                        if (linelockTime === 4) {
                                            await tx.rollback();
                                            tx = null;
                                            // ログ出力
                                            super.warn(
                                                tenantId,
                                                sequenceNo,
                                                MsgKeysConstants.APLPCW0003,
                                                lineNo,
                                            );
                                            // 変数「処理コード」設定
                                            handleCode =
                                                ResultCdConstants.CODE_060201;
                                            // 返却値編集
                                            const output = this.returnEdit(
                                                param,
                                                handleCode,
                                                apiHandleId,
                                                receivedDate,
                                            );
                                            super.debug(
                                                tenantId,
                                                sequenceNo,
                                                MsgKeysConstants.APLPCD0002,
                                                this.getClassName(),
                                                "service",
                                            );
                                            // CRITICAL FIX: Use helper to cleanup transaction before return
                                            return await this.cleanupTransactionAndReturn(tx, output);
                                        }

                                        await tx.rollback();
                                        tx = null;
                                        try {
                                            // 次のロック取得まで待ちミリ秒数分待機
                                            await new Promise((resolve) => {
                                                setTimeout(
                                                    resolve,
                                                    this.LOCK_WAIT_MILLISEC,
                                                );
                                            });
                                        } catch (e) {
                                            this.context.warn(
                                                "LinePlanACquisitionServiceTx.service sleep",
                                                e,
                                            );
                                        }
                                        tx = await sequelize.transaction();
                                        continue;
                                    } else if (isSQLException(e)) {
                                        // DBアクセスリトライエラーの場合
                                        handleCode =
                                            ResultCdConstants.CODE_060201;
                                        throw e;
                                    } else {
                                        // ログ出力
                                        super.warn(
                                            tenantId,
                                            sequenceNo,
                                            MsgKeysConstants.APLPCW0003,
                                            lineNo,
                                        );
                                        // 変数「処理コード」設定
                                        handleCode =
                                            ResultCdConstants.CODE_060201;
                                        // 返却値編集
                                        const output = this.returnEdit(
                                            param,
                                            handleCode,
                                            apiHandleId,
                                            receivedDate,
                                        );
                                        super.debug(
                                            tenantId,
                                            sequenceNo,
                                            MsgKeysConstants.APLPCD0002,
                                            this.getClassName(),
                                            "service",
                                        );
                                        return output;
                                    }
                                }
                            }
                        }

                        // MAWP保守 #604対応 変更 START ※既存処理を行ロック取得後に移動
                        // 回線グループチェック
                        try {
                            groupIdlst = await this.aPILinesDAO.getGroupId(
                                lineNo,
                            );
                        } catch (e) {
                            if (isSQLException(e)) {
                                // DBアクセスリトライエラーの場合
                                handleCode = ResultCdConstants.CODE_061302;
                            }
                            throw e;
                        }
                        if (tenantsEntity.tenantType !== null) {
                            tenantTypeCheck = String(tenantsEntity.tenantType);
                        }
                        if (groupIdlst === null || groupIdlst.length === 0) {
                            // 変数「グループON/OFFフラグ」 = OFF(0)
                            groupOnOffFlag = "0";
                        } else if (groupIdlst.length > 1) {
                            super.warn(
                                tenantId,
                                sequenceNo,
                                MsgKeysConstants.APLPCW1302,
                                lineNo,
                            );
                            // 変数「処理コード」設定
                            handleCode = ResultCdConstants.CODE_061302;
                            // 返却値編集
                            const output = this.returnEdit(
                                param,
                                handleCode,
                                apiHandleId,
                                receivedDate,
                            );
                            super.debug(
                                tenantId,
                                sequenceNo,
                                MsgKeysConstants.APLPCD0002,
                                this.getClassName(),
                                "service",
                            );
                            // CRITICAL FIX: Use helper to cleanup transaction before return
                            return await this.cleanupTransactionAndReturn(tx, output);
                        } else if (
                            groupIdlst.length === 1 &&
                            (tenantTypeCheck === null ||
                                "2".equals(tenantTypeCheck) ||
                                "3".equals(tenantTypeCheck) ||
                                "4".equals(tenantTypeCheck) ||
                                "5".equals(tenantTypeCheck)
                            )
                        ) {
                            // MAWP保守 #833対応 変更 END
                            // groupOnOffFlag = "1";
                            // 更新サービスパターンチェック
                            let check: boolean = null;
                            try {
                                check = await this.checkTpcServicePattern(
                                    tx,
                                    potalPlanID,
                                    groupIdlst,
                                );
                            } catch (e) {
                                if (isSQLException(e)) {
                                    // DBアクセスリトライエラーの場合
                                    handleCode = ResultCdConstants.CODE_061302;
                                }
                                throw e;
                            }
                            if (check) {
                                // 判定OK
                                groupOnOffFlag = "1";
                            } else {
                                // 判定NG
                                if (tenantTypeCheck === null) {
                                    // 社外テナントの場合
                                    // ログ出力
                                    super.warn(
                                        tenantId,
                                        sequenceNo,
                                        MsgKeysConstants.APLPCW1302,
                                        lineNo,
                                    );
                                    // 変数「処理コード」設定
                                    handleCode = ResultCdConstants.CODE_061302;
                                    // 返却値編集
                                    const output = this.returnEdit(
                                        param,
                                        handleCode,
                                        apiHandleId,
                                        receivedDate,
                                    );
                                    super.debug(
                                        tenantId,
                                        sequenceNo,
                                        MsgKeysConstants.APLPCD0002,
                                        this.getClassName(),
                                        "service",
                                    );
                                    // CRITICAL FIX: Use helper to cleanup transaction before return
                                    return await this.cleanupTransactionAndReturn(tx, output);
                                } else {
                                    // 社内テナントの場合
                                    // ログ出力
                                    super.warn(
                                        tenantId,
                                        sequenceNo,
                                        MsgKeysConstants.APLPCW1303,
                                        lineNo,
                                        potalPlanID,
                                    );
                                    // グループON/OFFフラグをOFF
                                    groupOnOffFlag = "0";
                                }
                            }
                            // 上記以外
                        } else {
                            // ログ出力
                            super.warn(
                                tenantId,
                                sequenceNo,
                                MsgKeysConstants.APLPCW1302,
                                lineNo,
                            );
                            // 変数「処理コード」設定
                            handleCode = ResultCdConstants.CODE_061302;
                            // 返却値編集
                            const output = this.returnEdit(
                                param,
                                handleCode,
                                apiHandleId,
                                receivedDate,
                            );
                            super.debug(
                                tenantId,
                                sequenceNo,
                                MsgKeysConstants.APLPCD0002,
                                this.getClassName(),
                                "service",
                            );
                            // CRITICAL FIX: Use helper to cleanup transaction before return
                            return await this.cleanupTransactionAndReturn(tx, output);
                        }
                        // MAWP保守 #604対応 変更 END
                        // テナント管理より、回線IDとテナントID関係チェック
                        try {
                            checkResult = await this.tenantManage.doCheck(
                                lineNo,
                                tenantId,
                            );
                        } catch (e) {
                            if (isSQLException(e)) {
                                // DBアクセスリトライエラーの場合
                                handleCode = ResultCdConstants.CODE_060401;
                            }
                            throw e;
                        }

                        const tenantType = String(tenantsEntity.tenantType);

                        if (checkResult[0] && !"1".equals(tenantType)) {
                            // 変更前プランIDチェック（C2）
                            let linesEntity: LinesEntity = null;
                            try {
                                linesEntity =
                                    await this.aPILinesDAO.getLineInfo(
                                        lineNo,
                                        tx,
                                    );
                            } catch (e) {
                                if (isSQLException(e)) {
                                    // DBアクセスリトライエラーの場合
                                    handleCode = ResultCdConstants.CODE_060501;
                                }
                                throw e;
                            }
                            if (linesEntity === null) {
                                // ログ出力
                                super.warn(
                                    tenantId,
                                    sequenceNo,
                                    MsgKeysConstants.APLPCW0005,
                                    potalPlanIDPre,
                                );
                                // 変数「処理コード」設定
                                handleCode = ResultCdConstants.CODE_060501;
                                // 返却値編集
                                const output = this.returnEdit(
                                    param,
                                    handleCode,
                                    apiHandleId,
                                    receivedDate,
                                );
                                super.debug(
                                    tenantId,
                                    sequenceNo,
                                    MsgKeysConstants.APLPCD0002,
                                    this.getClassName(),
                                    "service",
                                );
                                // CRITICAL FIX: Use helper to cleanup transaction before return
                                return await this.cleanupTransactionAndReturn(tx, output);
                            } else {
                                // 再販料金プラン
                                pricePlanId = linesEntity.pricePlanId;
                                // プランIDを取得する
                                let plansList: PlansEntity[] = null;
                                try {
                                    plansList =
                                        await this.aPILinesDAO.getPlanInfo(
                                            pricePlanId,
                                            tx,
                                        );
                                } catch (e) {
                                    if (isSQLException(e)) {
                                        // DBアクセスリトライエラーの場合
                                        handleCode =
                                            ResultCdConstants.CODE_060501;
                                    }
                                    throw e;
                                }
                                if (
                                    plansList === null ||
                                    plansList.length === 0
                                ) {
                                    // ログ出力
                                    super.warn(
                                        tenantId,
                                        sequenceNo,
                                        MsgKeysConstants.APLPCW0005,
                                        potalPlanIDPre,
                                    );
                                    // 変数「処理コード」設定
                                    handleCode = ResultCdConstants.CODE_060501;
                                    // 返却値編集
                                    const output = this.returnEdit(
                                        param,
                                        handleCode,
                                        apiHandleId,
                                        receivedDate,
                                    );
                                    super.debug(
                                        tenantId,
                                        sequenceNo,
                                        MsgKeysConstants.APLPCD0002,
                                        this.getClassName(),
                                        "service",
                                    );
                                    // CRITICAL FIX: Use helper to cleanup transaction before return
                                    return await this.cleanupTransactionAndReturn(tx, output);
                                } else if (
                                    parseInt(potalPlanIDPre, 0) !==
                                    plansList[0].planId
                                ) {
                                    // ログ出力
                                    super.warn(
                                        tenantId,
                                        sequenceNo,
                                        MsgKeysConstants.APLPCW0005,
                                        potalPlanIDPre,
                                    );
                                    // 変数「処理コード」設定
                                    handleCode = ResultCdConstants.CODE_060501;
                                    // 返却値編集
                                    const output = this.returnEdit(
                                        param,
                                        handleCode,
                                        apiHandleId,
                                        receivedDate,
                                    );
                                    super.debug(
                                        tenantId,
                                        sequenceNo,
                                        MsgKeysConstants.APLPCD0002,
                                        this.getClassName(),
                                        "service",
                                    );
                                    // CRITICAL FIX: Use helper to cleanup transaction before return
                                    return await this.cleanupTransactionAndReturn(tx, output);
                                } else {
                                    // 変更後プランIDチェック（C3）
                                    let plansList1: PlansEntity[] = null;
                                    try {
                                        plansList1 =
                                            await this.aPICommonDAO.getPlans(
                                                potalPlanID,
                                                tx,
                                            );
                                    } catch (e) {
                                        if (isSQLException(e)) {
                                            // DBアクセスリトライエラーの場合
                                            handleCode =
                                                ResultCdConstants.CODE_060601;
                                        }
                                        throw e;
                                    }
                                    if (
                                        plansList1 === null ||
                                        plansList1.length === 0
                                    ) {
                                        // ログ出力
                                        super.warn(
                                            tenantId,
                                            sequenceNo,
                                            MsgKeysConstants.APLPCW0006,
                                            potalPlanID,
                                        );
                                        // 変数「処理コード」設定
                                        handleCode =
                                            ResultCdConstants.CODE_060601;
                                        // 返却値編集
                                        const output = this.returnEdit(
                                            param,
                                            handleCode,
                                            apiHandleId,
                                            receivedDate,
                                        );
                                        super.debug(
                                            tenantId,
                                            sequenceNo,
                                            MsgKeysConstants.APLPCD0002,
                                            this.getClassName(),
                                            "service",
                                        );
                                        // CRITICAL FIX: Use helper to cleanup transaction before return
                                        return await this.cleanupTransactionAndReturn(tx, output);
                                    } else if (
                                        (CheckUtil.checkIsNull(
                                            plansList1[0].pricePlanId,
                                        ) ||
                                            StringUtils.isEmpty(
                                                plansList1[0].pricePlanId.trim(),
                                            )) &&
                                        (CheckUtil.checkIsNull(
                                            plansList1[0].servicePlanId,
                                        ) ||
                                            StringUtils.isEmpty(
                                                plansList1[0].servicePlanId.trim(),
                                            ))
                                    ) {
                                        // ログ出力
                                        super.warn(
                                            tenantId,
                                            sequenceNo,
                                            MsgKeysConstants.APLPCW0006,
                                            potalPlanID,
                                        );
                                        // 変数「処理コード」設定
                                        handleCode =
                                            ResultCdConstants.CODE_060601;
                                        // 返却値編集
                                        const output = this.returnEdit(
                                            param,
                                            handleCode,
                                            apiHandleId,
                                            receivedDate,
                                        );
                                        super.debug(
                                            tenantId,
                                            sequenceNo,
                                            MsgKeysConstants.APLPCD0002,
                                            this.getClassName(),
                                            "service",
                                        );
                                        // CRITICAL FIX: Use helper to cleanup transaction before return
                                        return await this.cleanupTransactionAndReturn(tx, output);
                                    }

                                    // 変数「サービスプランID」を設定する
                                    servicePlanId = plansList1[0].servicePlanId;
                                    pricePlanIdAfter =
                                        plansList1[0].pricePlanId;
                                    pricePlanIdAfterName =
                                        plansList1[0].planName;
                                    basicCapacity = plansList1[0].basicCapacity;
                                    // save T番 for new plan
                                    newPlanIDT = plansList1[0].planIdT;
                                }

                                // プラン変更条件チェック（C4）
                                let plansTypeListPre: string[] = null;
                                let plansTypeList: string[] = null;
                                try {
                                    // プランテーブルから変更前プランの「音声フラグ」「SMS対応」「対応NW」、「フルMVNOフラグ」を取得
                                    plansTypeListPre =
                                        await this.aPICommonDAO.getPlansTypeList(
                                            potalPlanIDPre,
                                            tx,
                                        );
                                    // プランテーブルから変更後プランの「音声フラグ」「SMS対応」「対応NW」、「フルMVNOフラグ」を取得
                                    plansTypeList =
                                        await this.aPICommonDAO.getPlansTypeList(
                                            potalPlanID,
                                            tx,
                                        );
                                } catch (e) {
                                    if (isSQLException(e)) {
                                        // DBアクセスリトライエラーの場合
                                        handleCode =
                                            ResultCdConstants.CODE_060701;
                                    }
                                    throw e;
                                }

                                // プラン変更パターン定型フラグが1（帯域卸タイプ）の場合
                                if (Number(1) === planChangeFlag[0]) {
                                    // 変更前プランと変更後プランの音声フラグ、SMS対応が一致しない場合
                                    if (
                                        !plansTypeList[0].equals(
                                            plansTypeListPre[0],
                                        ) ||
                                        !plansTypeList[1].equals(
                                            plansTypeListPre[1],
                                        )
                                    ) {
                                        // ログ出力
                                        super.warn(
                                            tenantId,
                                            sequenceNo,
                                            MsgKeysConstants.APLPCW0007,
                                        );
                                        // 変数「処理コード」設定
                                        handleCode =
                                            ResultCdConstants.CODE_060701;
                                        // 返却値編集
                                        const output = this.returnEdit(
                                            param,
                                            handleCode,
                                            apiHandleId,
                                            receivedDate,
                                        );
                                        super.debug(
                                            tenantId,
                                            sequenceNo,
                                            MsgKeysConstants.APLPCD0002,
                                            this.getClassName(),
                                            "service",
                                        );
                                        // CRITICAL FIX: Use helper to cleanup transaction before return
                                        return await this.cleanupTransactionAndReturn(tx, output);
                                        // 変更前プランと変更後プランの対応NWが一致しない場合
                                    } else if (
                                        !plansTypeList[2].equals(
                                            plansTypeListPre[2],
                                        ) &&
                                        !(
                                            "LTE".equals(plansTypeListPre[2]) &&
                                            "5G(NSA)".equals(plansTypeList[2])
                                        ) &&
                                        !(
                                            "5G(NSA)".equals(
                                                plansTypeListPre[2],
                                            ) && "LTE".equals(plansTypeList[2])
                                        )
                                    ) {
                                        // ログ出力
                                        super.warn(
                                            tenantId,
                                            sequenceNo,
                                            MsgKeysConstants.APLPCW0007,
                                        );
                                        // 変数「処理コード」設定
                                        handleCode =
                                            ResultCdConstants.CODE_060701;
                                        // 返却値編集
                                        const output = this.returnEdit(
                                            param,
                                            handleCode,
                                            apiHandleId,
                                            receivedDate,
                                        );
                                        super.debug(
                                            tenantId,
                                            sequenceNo,
                                            MsgKeysConstants.APLPCD0002,
                                            this.getClassName(),
                                            "service",
                                        );
                                        // CRITICAL FIX: Use helper to cleanup transaction before return
                                        return await this.cleanupTransactionAndReturn(tx, output);
                                        // 変更前プランと変更後プランのフルMVNOフラグが一致しない場合
                                    } else if (
                                        !plansTypeList[3].equals(
                                            plansTypeListPre[3],
                                        )
                                    ) {
                                        // ログ出力
                                        super.warn(
                                            tenantId,
                                            sequenceNo,
                                            MsgKeysConstants.APLPCW0007,
                                        );
                                        // 変数「処理コード」設定
                                        handleCode =
                                            ResultCdConstants.CODE_060701;
                                        // 返却値編集
                                        const output = this.returnEdit(
                                            param,
                                            handleCode,
                                            apiHandleId,
                                            receivedDate,
                                        );
                                        super.debug(
                                            tenantId,
                                            sequenceNo,
                                            MsgKeysConstants.APLPCD0002,
                                            this.getClassName(),
                                            "service",
                                        );
                                        // CRITICAL FIX: Use helper to cleanup transaction before return
                                        return await this.cleanupTransactionAndReturn(tx, output);
                                        // テナントプランに変更後プランが存在することを確認
                                    } else {
                                        // テナントプランテーブルから変更後プランの「プランID」を取得
                                        let tenantPlansPlanId: number[] = null;
                                        try {
                                            tenantPlansPlanId =
                                                await this.aPICommonDAO.getTenantPlansPlanId(
                                                    tenantId,
                                                    potalPlanID,
                                                    tx,
                                                );
                                        } catch (e) {
                                            if (isSQLException(e)) {
                                                // DBアクセスリトライエラーの場合
                                                handleCode =
                                                    ResultCdConstants.CODE_060701;
                                            }
                                            throw e;
                                        }
                                        if (
                                            CheckUtil.checkIsNull(
                                                tenantPlansPlanId,
                                            ) ||
                                            tenantPlansPlanId.length === 0
                                        ) {
                                            // ログ出力
                                            super.warn(
                                                tenantId,
                                                sequenceNo,
                                                MsgKeysConstants.APLPCW0007,
                                            );
                                            // 変数「処理コード」設定
                                            handleCode =
                                                ResultCdConstants.CODE_060701;
                                            // 返却値編集
                                            const output = this.returnEdit(
                                                param,
                                                handleCode,
                                                apiHandleId,
                                                receivedDate,
                                            );
                                            super.debug(
                                                tenantId,
                                                sequenceNo,
                                                MsgKeysConstants.APLPCD0002,
                                                this.getClassName(),
                                                "service",
                                            );
                                            // CRITICAL FIX: Use helper to cleanup transaction before return
                                            return await this.cleanupTransactionAndReturn(tx, output);
                                        }
                                    }
                                    // プラン変更パターン定型フラグが2（ID卸タイプ）の場合
                                } else if (Number(2) === planChangeFlag[0]) {
                                    // 変更前プランと変更後プランの音声フラグ、SMS対応が一致しない場合
                                    if (
                                        !plansTypeList[0].equals(
                                            plansTypeListPre[0],
                                        ) ||
                                        !plansTypeList[1].equals(
                                            plansTypeListPre[1],
                                        )
                                    ) {
                                        // ログ出力
                                        super.warn(
                                            tenantId,
                                            sequenceNo,
                                            MsgKeysConstants.APLPCW0007,
                                        );
                                        // 変数「処理コード」設定
                                        handleCode =
                                            ResultCdConstants.CODE_060701;
                                        // 返却値編集
                                        const output = this.returnEdit(
                                            param,
                                            handleCode,
                                            apiHandleId,
                                            receivedDate,
                                        );
                                        super.debug(
                                            tenantId,
                                            sequenceNo,
                                            MsgKeysConstants.APLPCD0002,
                                            this.getClassName(),
                                            "service",
                                        );
                                        // CRITICAL FIX: Use helper to cleanup transaction before return
                                        return await this.cleanupTransactionAndReturn(tx, output);
                                        // 変更前プランと変更後プランの対応NWが一致しない場合
                                    } else if (
                                        !plansTypeList[2].equals(
                                            plansTypeListPre[2],
                                        ) &&
                                        !(
                                            "LTE".equals(plansTypeListPre[2]) &&
                                            "5G(NSA)".equals(plansTypeList[2])
                                        ) &&
                                        !(
                                            "5G(NSA)".equals(
                                                plansTypeListPre[2],
                                            ) && "LTE".equals(plansTypeList[2])
                                        )
                                    ) {
                                        // ログ出力
                                        super.warn(
                                            tenantId,
                                            sequenceNo,
                                            MsgKeysConstants.APLPCW0007,
                                        );
                                        // 変数「処理コード」設定
                                        handleCode =
                                            ResultCdConstants.CODE_060701;
                                        // 返却値編集
                                        const output = this.returnEdit(
                                            param,
                                            handleCode,
                                            apiHandleId,
                                            receivedDate,
                                        );
                                        super.debug(
                                            tenantId,
                                            sequenceNo,
                                            MsgKeysConstants.APLPCD0002,
                                            this.getClassName(),
                                            "service",
                                        );
                                        // CRITICAL FIX: Use helper to cleanup transaction before return
                                        return await this.cleanupTransactionAndReturn(tx, output);
                                        // 変更後プランのプラン変更種別が通常プラン(0 or null)ではない場合
                                    } else if (
                                        Number(0) !== planChangeClass[0] &&
                                        !CheckUtil.checkIsNull(
                                            planChangeClass[0],
                                        )
                                    ) {
                                        // ログ出力
                                        super.warn(
                                            tenantId,
                                            sequenceNo,
                                            MsgKeysConstants.APLPCW0007,
                                        );
                                        // 変数「処理コード」設定
                                        handleCode =
                                            ResultCdConstants.CODE_060701;
                                        // 返却値編集
                                        const output = this.returnEdit(
                                            param,
                                            handleCode,
                                            apiHandleId,
                                            receivedDate,
                                        );
                                        super.debug(
                                            tenantId,
                                            sequenceNo,
                                            MsgKeysConstants.APLPCD0002,
                                            this.getClassName(),
                                            "service",
                                        );
                                        return output;
                                        // 変更前プランと変更後プランのフルMVNOフラグが一致しない場合
                                    } else if (
                                        !plansTypeList[3].equals(
                                            plansTypeListPre[3],
                                        )
                                    ) {
                                        // ログ出力
                                        super.warn(
                                            tenantId,
                                            sequenceNo,
                                            MsgKeysConstants.APLPCW0007,
                                        );
                                        // 変数「処理コード」設定
                                        handleCode =
                                            ResultCdConstants.CODE_060701;
                                        // 返却値編集
                                        const output = this.returnEdit(
                                            param,
                                            handleCode,
                                            apiHandleId,
                                            receivedDate,
                                        );
                                        super.debug(
                                            tenantId,
                                            sequenceNo,
                                            MsgKeysConstants.APLPCD0002,
                                            this.getClassName(),
                                            "service",
                                        );
                                        return output;
                                        // テナントプランに変更後プランが存在することを確認
                                    } else {
                                        // テナントプランテーブルから変更後プランの「プランID」を取得
                                        let tenantPlansPlanId: number[] = null;
                                        try {
                                            tenantPlansPlanId =
                                                await this.aPICommonDAO.getTenantPlansPlanId(
                                                    tenantId,
                                                    potalPlanID,
                                                    tx,
                                                );
                                        } catch (e) {
                                            if (isSQLException(e)) {
                                                // DBアクセスリトライエラーの場合
                                                handleCode =
                                                    ResultCdConstants.CODE_060701;
                                            }
                                            throw e;
                                        }
                                        if (
                                            CheckUtil.checkIsNull(
                                                tenantPlansPlanId,
                                            ) ||
                                            tenantPlansPlanId.length === 0
                                        ) {
                                            // ログ出力
                                            super.warn(
                                                tenantId,
                                                sequenceNo,
                                                MsgKeysConstants.APLPCW0007,
                                            );
                                            // 変数「処理コード」設定
                                            handleCode =
                                                ResultCdConstants.CODE_060701;
                                            // 返却値編集
                                            const output = this.returnEdit(
                                                param,
                                                handleCode,
                                                apiHandleId,
                                                receivedDate,
                                            );
                                            super.debug(
                                                tenantId,
                                                sequenceNo,
                                                MsgKeysConstants.APLPCD0002,
                                                this.getClassName(),
                                                "service",
                                            );
                                            return output;
                                        }
                                    }
                                }
                                // プラン変更パターン定型フラグが3（社内システムタイプ）の場合
                                else if (Number(3) === planChangeFlag[0]) {
                                    // 変更前プランと変更後プランのフルMVNOフラグが一致しない場合
                                    if (
                                        !plansTypeList[3].equals(
                                            plansTypeListPre[3],
                                        )
                                    ) {
                                        // ログ出力
                                        super.warn(
                                            tenantId,
                                            sequenceNo,
                                            MsgKeysConstants.APLPCW0007,
                                        );
                                        // 変数「処理コード」設定
                                        handleCode =
                                            ResultCdConstants.CODE_060701;
                                        // 返却値編集
                                        const output = this.returnEdit(
                                            param,
                                            handleCode,
                                            apiHandleId,
                                            receivedDate,
                                        );
                                        super.debug(
                                            tenantId,
                                            sequenceNo,
                                            MsgKeysConstants.APLPCD0002,
                                            this.getClassName(),
                                            "service",
                                        );
                                        return output;
                                        // テナントプランに変更後プランが存在することを確認
                                    } else {
                                        // テナントプランテーブルから変更後プランの「プランID」を取得
                                        let tenantPlansPlanId: number[] = null;
                                        try {
                                            tenantPlansPlanId =
                                                await this.aPICommonDAO.getTenantPlansPlanId(
                                                    tenantId,
                                                    potalPlanID,
                                                    tx,
                                                );
                                        } catch (e) {
                                            if (isSQLException(e)) {
                                                // DBアクセスリトライエラーの場合
                                                handleCode =
                                                    ResultCdConstants.CODE_060701;
                                            }
                                            throw e;
                                        }
                                        if (
                                            CheckUtil.checkIsNull(
                                                tenantPlansPlanId,
                                            ) ||
                                            tenantPlansPlanId.length === 0
                                        ) {
                                            // ログ出力
                                            super.warn(
                                                tenantId,
                                                sequenceNo,
                                                MsgKeysConstants.APLPCW0007,
                                            );
                                            // 変数「処理コード」設定
                                            handleCode =
                                                ResultCdConstants.CODE_060701;
                                            // 返却値編集
                                            const output = this.returnEdit(
                                                param,
                                                handleCode,
                                                apiHandleId,
                                                receivedDate,
                                            );
                                            super.debug(
                                                tenantId,
                                                sequenceNo,
                                                MsgKeysConstants.APLPCD0002,
                                                this.getClassName(),
                                                "service",
                                            );
                                            return output;
                                        }
                                    }
                                } else {
                                    let tenantPlansEntity: TenantPlansEntity =
                                        null;
                                    try {
                                        tenantPlansEntity =
                                            await this.aPICommonDAO.getTenantPlanforPlanChange(
                                                tenantId,
                                                potalPlanIDPre,
                                                potalPlanID,
                                                tx,
                                            );
                                    } catch (e) {
                                        if (isSQLException(e)) {
                                            // DBアクセスリトライエラーの場合
                                            handleCode =
                                                ResultCdConstants.CODE_060701;
                                        }
                                        throw e;
                                    }
                                    if (tenantPlansEntity === null) {
                                        // ログ出力
                                        super.warn(
                                            tenantId,
                                            sequenceNo,
                                            MsgKeysConstants.APLPCW0007,
                                        );
                                        // 変数「処理コード」設定
                                        handleCode =
                                            ResultCdConstants.CODE_060701;
                                        // 返却値編集
                                        const output = this.returnEdit(
                                            param,
                                            handleCode,
                                            apiHandleId,
                                            receivedDate,
                                        );
                                        super.debug(
                                            tenantId,
                                            sequenceNo,
                                            MsgKeysConstants.APLPCD0002,
                                            this.getClassName(),
                                            "service",
                                        );
                                        return output;
                                    }
                                }
                                // プラン変更実施回数チェック（C5）
                                let tenantPlansEntity1: TenantPlansEntity =
                                    new TenantPlansEntity();
                                // プラン変更パターン定型フラグが1（帯域卸タイプ）
                                // もしくはプラン変更パターン定型フラグが3（社内システムタイプ）の場合
                                if (
                                    Number(1) === planChangeFlag[0] ||
                                    Number(3) === planChangeFlag[0]
                                ) {
                                    // 月間変更可能数＝0
                                    tenantPlansEntity1.changeCount = 0;
                                    // プラン変更パターン定型フラグが2（ID卸タイプ）の場合
                                } else if (Number(2) === planChangeFlag[0]) {
                                    // 変更前、変更後プラン共にプラン変更種別が通常プラン(0 or null)の場合
                                    if (
                                        (Number(0) === planChangeClassPre[0] ||
                                            CheckUtil.checkIsNull(
                                                planChangeClassPre[0],
                                            )) &&
                                        (Number(0) === planChangeClass[0] ||
                                            CheckUtil.checkIsNull(
                                                planChangeClass[0],
                                            ))
                                    ) {
                                        // 月間変更可能数＝1
                                        tenantPlansEntity1.changeCount = 1;
                                    } else {
                                        // 月間変更可能数＝0
                                        tenantPlansEntity1.changeCount = 0;
                                    }
                                } else {
                                    try {
                                        tenantPlansEntity1 =
                                            await this.aPICommonDAO.getTenantPlanforPlanChange(
                                                tenantId,
                                                potalPlanIDPre,
                                                potalPlanID,
                                                tx,
                                            );
                                    } catch (e) {
                                        if (isSQLException(e)) {
                                            // DBアクセスリトライエラーの場合
                                            handleCode =
                                                ResultCdConstants.CODE_060801;
                                        }
                                        throw e;
                                    }
                                    if (tenantPlansEntity1 === null) {
                                        // ログ出力
                                        super.warn(
                                            tenantId,
                                            sequenceNo,
                                            MsgKeysConstants.APLPCW0008,
                                        );
                                        // 変数「処理コード」設定
                                        handleCode =
                                            ResultCdConstants.CODE_060801;
                                        // 返却値編集
                                        const output = this.returnEdit(
                                            param,
                                            handleCode,
                                            apiHandleId,
                                            receivedDate,
                                        );
                                        super.debug(
                                            tenantId,
                                            sequenceNo,
                                            MsgKeysConstants.APLPCD0002,
                                            this.getClassName(),
                                            "service",
                                        );
                                        return output;
                                    }
                                }
                                // 月間変更可能数チェックを行う
                                if (tenantPlansEntity1.changeCount !== 0) {
                                    // 当月のプランン変更実施回数を取得する
                                    const str = MvnoUtil.getDateTimeNow();
                                    let date1: Date = null;
                                    let date2: Date = null;
                                    if (
                                        Constants.ORDER_TYPE_0.equals(
                                            orderType,
                                        ) ||
                                        Constants.ORDER_TYPE_2.equals(orderType)
                                    ) {
                                        date1 = this.getDate1(str);
                                        date2 = this.getDate2(str);
                                    } else if (
                                        Constants.ORDER_TYPE_1.equals(orderType)
                                    ) {
                                        date1 = this.getDate1(reserveDate);
                                        date2 = this.getDate2(reserveDate);
                                    }

                                    let planChangeTime: number = 0;
                                    try {
                                        planChangeTime =
                                            await this.aPICommonDAO.getPlanChangeKaisu(
                                                lineNo,
                                                date1,
                                                date2,
                                                tx,
                                            );
                                    } catch (e) {
                                        if (isSQLException(e)) {
                                            // DBアクセスリトライエラーの場合
                                            handleCode =
                                                ResultCdConstants.CODE_060801;
                                        }
                                        throw e;
                                    }
                                    if (
                                        planChangeTime >=
                                        tenantPlansEntity1.changeCount
                                    ) {
                                        // ログ出力
                                        super.warn(
                                            tenantId,
                                            sequenceNo,
                                            MsgKeysConstants.APLPCW0008,
                                        );
                                        // 変数「処理コード」設定
                                        handleCode =
                                            ResultCdConstants.CODE_060801;
                                        // 返却値編集
                                        const output = this.returnEdit(
                                            param,
                                            handleCode,
                                            apiHandleId,
                                            receivedDate,
                                        );
                                        super.debug(
                                            tenantId,
                                            sequenceNo,
                                            MsgKeysConstants.APLPCD0002,
                                            this.getClassName(),
                                            "service",
                                        );
                                        return output;
                                    }
                                }
                            }
                        } else if (!checkResult[0]) {
                            // ログ出力
                            super.warn(
                                tenantId,
                                sequenceNo,
                                MsgKeysConstants.APLPCW0004,
                                lineNo,
                                tenantId,
                            );
                            // 変数「処理コード」設定
                            handleCode = ResultCdConstants.CODE_060401;
                            // 返却値編集
                            const output = this.returnEdit(
                                param,
                                handleCode,
                                apiHandleId,
                                receivedDate,
                            );
                            super.debug(
                                tenantId,
                                sequenceNo,
                                MsgKeysConstants.APLPCD0002,
                                this.getClassName(),
                                "service",
                            );
                            return output;
                        }
                        // 廃止オーダ受付中チェックを実施する
                        let targetDate: string = null;
                        if (Constants.ORDER_TYPE_0.equals(orderType)) {
                            targetDate = format(
                                fromUnixTime(
                                    MvnoUtil.convDateFormat(receivedDate) /
                                        1000,
                                ),
                                "yyyyMMdd",
                            );
                        } else {
                            targetDate = reserveDate.replace(/\//g, "");
                        }
                        let checkAbolishSoResult: boolean = null;
                        try {
                            checkAbolishSoResult =
                                await this.apiCommon.checkAbolishSo(
                                    lineNo,
                                    targetDate,
                                );
                        } catch (e) {
                            if (isSQLException(e)) {
                                // DBアクセスリトライエラーの場合
                                handleCode = ResultCdConstants.CODE_060133;
                            }
                            throw e;
                        }
                        // 変数「廃止オーダ受付中判定結果」より判定する
                        if (!checkAbolishSoResult) {
                            super.warn(
                                tenantId,
                                sequenceNo,
                                MsgKeysConstants.APLPCW0034,
                                lineNo,
                            );
                            handleCode = ResultCdConstants.CODE_060133;
                            const output = this.returnEdit(
                                param,
                                handleCode,
                                apiHandleId,
                                receivedDate,
                            );
                            super.debug(
                                tenantId,
                                sequenceNo,
                                MsgKeysConstants.APLPCD0002,
                                this.getClassName(),
                                "service",
                            );
                            return output;
                        }

                        // 半黒フラグ確認
                        // 回線情報テーブルから情報を取得する
                        let linesEntity: LinesEntity = null;
                        try {
                            linesEntity = await this.aPILinesDAO.getLineInfo(
                                lineNo,
                            );
                        } catch (e) {
                            if (isSQLException(e)) {
                                // DBアクセスリトライエラーの場合
                                handleCode = ResultCdConstants.CODE_060134;
                            }
                            throw e;
                        }
                        // COMMENT LATER
                        // 変数「回線情報（半黒判定）」を判定する
                        if (
                            !(
                                "2".equals(tenantTypeCheck) ||
                                "3".equals(tenantTypeCheck) ||
                                "4".equals(tenantTypeCheck) ||
                                "5".equals(tenantTypeCheck)
                            ) &&
                            linesEntity.simFlag
                        ) {
                            // 社内テナント種別が"2"もしくは"3"もしくは"4"もしくは"5"以外 かつ 半黒フラグが"true"(半黒) の場合
                            // ログ出力
                            super.warn(
                                tenantId,
                                sequenceNo,
                                MsgKeysConstants.APLPCW0035,
                                lineNo,
                            );
                            // 変数「処理コード」設定
                            handleCode = ResultCdConstants.CODE_060134;
                            // 返却値編集
                            const output = this.returnEdit(
                                param,
                                handleCode,
                                apiHandleId,
                                receivedDate,
                            );
                            super.debug(
                                tenantId,
                                sequenceNo,
                                MsgKeysConstants.APLPCD0002,
                                this.getClassName(),
                                "service",
                            );
                            return output;
                        }

                        // // プランテーブルから変更後プランの「対応NW」を取得
                        let plansTypeNwList: string[] = null;
                        try {
                            plansTypeNwList =
                                await this.aPICommonDAO.getPlansTypeList(
                                    potalPlanID,
                                    tx,
                                );
                        } catch (e) {
                            if (isSQLException(e)) {
                                // DBアクセスリトライエラーの場合
                                handleCode = ResultCdConstants.CODE_060135;
                            }
                            throw e;
                        }
                        if ("5G(NSA)".equals(plansTypeNwList[2])) {
                            // 変更後プランの対応NWが「5G(NSA)」の場合
                            if (
                                !(
                                    "5G(NSA)".equals(
                                        linesEntity.contractType,
                                    ) ||
                                    "5G(NSA)(音声)".equals(
                                        linesEntity.contractType,
                                    )
                                )
                            ) {
                                // 回線の契約種別が「5G(NSA)」もしくは「5G(NSA)(音声)」以外の場合
                                // ログ出力
                                super.warn(
                                    tenantId,
                                    sequenceNo,
                                    MsgKeysConstants.APLPCW0037,
                                    lineNo,
                                );
                                // 変数「処理コード」設定
                                handleCode = ResultCdConstants.CODE_060135;
                                // 返却値編集
                                const output = this.returnEdit(
                                    param,
                                    handleCode,
                                    apiHandleId,
                                    receivedDate,
                                );
                                return output;
                            }
                        }

                        // 8社内テナント種別チェックを行う
                        if ("1".equals(tenantType)) {
                            // プラン情報を取得する
                            let plansList2: PlansEntity[] = null;
                            try {
                                plansList2 = await this.aPICommonDAO.getPlans(
                                    potalPlanID,
                                    tx,
                                );
                            } catch (e) {
                                if (isSQLException(e)) {
                                    // DBアクセスリトライエラーの場合
                                    handleCode = ResultCdConstants.CODE_060901;
                                }
                                throw e;
                            }
                            if (
                                plansList2 === null ||
                                plansList2.length === 0
                            ) {
                                // ログ出力
                                super.warn(
                                    tenantId,
                                    sequenceNo,
                                    MsgKeysConstants.APLPCW0009,
                                );
                                // 変数「処理コード」設定
                                handleCode = ResultCdConstants.CODE_060901;
                                // 返却値編集
                                const output = this.returnEdit(
                                    param,
                                    handleCode,
                                    apiHandleId,
                                    receivedDate,
                                );
                                super.debug(
                                    tenantId,
                                    sequenceNo,
                                    MsgKeysConstants.APLPCD0002,
                                    this.getClassName(),
                                    "service",
                                );
                                return output;
                            } else if (
                                CheckUtil.checkIsNull(
                                    plansList2[0].servicePlanId,
                                ) ||
                                StringUtils.isEmpty(plansList2[0].servicePlanId)
                            ) {
                                // ログ出力
                                super.warn(
                                    tenantId,
                                    sequenceNo,
                                    MsgKeysConstants.APLPCW0009,
                                );
                                // 変数「処理コード」設定
                                handleCode = ResultCdConstants.CODE_060901;
                                // 返却値編集
                                const output = this.returnEdit(
                                    param,
                                    handleCode,
                                    apiHandleId,
                                    receivedDate,
                                );
                                super.debug(
                                    tenantId,
                                    sequenceNo,
                                    MsgKeysConstants.APLPCD0002,
                                    this.getClassName(),
                                    "service",
                                );
                                return output;
                            }
                            // 変数「サービスプランID」を設定する
                            servicePlanId = plansList2[0].servicePlanId;
                        }

                        if (
                            Constants.ORDER_TYPE_0.equals(orderType) ||
                            Constants.ORDER_TYPE_2.equals(orderType)
                        ) {
                            // 9SO電文を作成する
                            let tpcConnectionResults: [
                                boolean,
                                string,
                                string,
                            ] = null;
                            try {
                                tpcConnectionResults =
                                    await this.tenantManage.checkTpcConnection(
                                        tenantId,
                                    );
                            } catch (e) {
                                if (isSQLException(e)) {
                                    // DBアクセスリトライエラーの場合
                                    handleCode = ResultCdConstants.CODE_061102;
                                }
                                throw e;
                            }
                            // 変数「TPC情報取得結果」より判断する
                            if (!tpcConnectionResults[0]) {
                                // ログ出力
                                super.warn(
                                    tenantId,
                                    sequenceNo,
                                    MsgKeysConstants.APLPCW0030,
                                    tenantId,
                                );
                                // 変数「処理コード」設定
                                handleCode = ResultCdConstants.CODE_061102;
                                // 返却値編集
                                const output = this.returnEdit(
                                    param,
                                    handleCode,
                                    apiHandleId,
                                    receivedDate,
                                );
                                super.debug(
                                    tenantId,
                                    sequenceNo,
                                    MsgKeysConstants.APLPCD0002,
                                    this.getClassName(),
                                    "service",
                                );
                                return output;
                            }
                            // プライマリTPCへSOAP連携
                            try {
                                soapCommonOutputDto =
                                    await this.sendSoapApiToTpcPlan(
                                        lineNo,
                                        servicePlanId,
                                        tenantId,
                                        sequenceNo,
                                        tpcConnectionResults[1],
                                        true,
                                    );
                            } catch (e) {
                                if (SOAPException.isSOAPException(e)) {
                                    // SOAP送受信でエラーとなった場合
                                    // ログ出力
                                    super.warn(
                                        e,
                                        tenantId,
                                        sequenceNo,
                                        MsgKeysConstants.APLPCW0010,
                                    );
                                    // 変数「処理コード」設定
                                    handleCode = ResultCdConstants.CODE_061101;
                                    // 返却値編集
                                    const output = this.returnEdit(
                                        param,
                                        handleCode,
                                        apiHandleId,
                                        receivedDate,
                                    );
                                    super.debug(
                                        tenantId,
                                        sequenceNo,
                                        MsgKeysConstants.APLPCD0002,
                                        this.getClassName(),
                                        "service",
                                    );
                                    return output;
                                } else {
                                    throw e;
                                }
                            }

                            if (
                                ResultCdConstants.CODE_000951.equals(
                                    soapCommonOutputDto.getProcessCode(),
                                )
                            ) {
                                // SG取得がNG
                                // ログ出力
                                super.warn(
                                    tenantId,
                                    sequenceNo,
                                    MsgKeysConstants.APLPCW0010,
                                );
                                // 変数「処理コード」設定
                                handleCode = ResultCdConstants.CODE_061101;
                                // 返却値編集
                                const output = this.returnEdit(
                                    param,
                                    handleCode,
                                    apiHandleId,
                                    receivedDate,
                                );
                                super.debug(
                                    tenantId,
                                    sequenceNo,
                                    MsgKeysConstants.APLPCD0002,
                                    this.getClassName(),
                                    "service",
                                );
                                return output;
                            }

                            document = soapCommonOutputDto.getDoc();
                            let resultStr = this.sOAPCommon.getNodeContent(
                                document,
                                "//Result",
                            );

                            if (
                                soapCommonOutputDto.isError() ||
                                this.CONST_RESULT.equals(resultStr)
                            ) {
                                // ログ出力
                                super.warn(
                                    tenantId,
                                    sequenceNo,
                                    MsgKeysConstants.APLPCW0010,
                                );
                                // 変数「処理コード」設定
                                handleCode = ResultCdConstants.CODE_061101;
                                // 返却値編集
                                const output = this.returnEdit(
                                    param,
                                    handleCode,
                                    apiHandleId,
                                    receivedDate,
                                );
                                super.debug(
                                    tenantId,
                                    sequenceNo,
                                    MsgKeysConstants.APLPCD0002,
                                    this.getClassName(),
                                    "service",
                                );
                                return output;
                            }

                            // セカンダリTPCへのSOAP連携
                            if (tpcConnectionResults[2] !== null) {
                                // セカンダリTPCが設定されている場合は以下の処理を行う(電文はプライマリと同じものを使用)
                                try {
                                    soapCommonOutputDto =
                                        await this.sendSoapApiToTpcPlan(
                                            lineNo,
                                            servicePlanId,
                                            tenantId,
                                            sequenceNo,
                                            tpcConnectionResults[2],
                                            false,
                                        );

                                    if (
                                        ResultCdConstants.CODE_000951.equals(
                                            soapCommonOutputDto.getProcessCode(),
                                        )
                                    ) {
                                        // SG取得がNG
                                        // ログ出力
                                        super.warn(
                                            tenantId,
                                            sequenceNo,
                                            MsgKeysConstants.APLPCW0022,
                                            "SG取得NG",
                                        );
                                    } else {
                                        // TPCレスポンス判定
                                        document = soapCommonOutputDto.getDoc();
                                        resultStr =
                                            this.sOAPCommon.getNodeContent(
                                                document,
                                                "//Result",
                                            );
                                        if (
                                            soapCommonOutputDto.isError() ||
                                            this.CONST_RESULT.equals(resultStr)
                                        ) {
                                            // ログ出力
                                            const errInfo =
                                                this.sOAPCommon.getNodeContent(
                                                    document,
                                                    "//ErrorInfomation",
                                                );
                                            super.warn(
                                                tenantId,
                                                sequenceNo,
                                                MsgKeysConstants.APLPCW0022,
                                                errInfo,
                                            );
                                        }
                                    }
                                } catch (e) {
                                    if (SOAPException.isSOAPException(e)) {
                                        // SOAP送受信でエラーとなった場合
                                        // ログ出力
                                        super.warn(
                                            e,
                                            tenantId,
                                            sequenceNo,
                                            MsgKeysConstants.APLPCW0022,
                                            e,
                                        );
                                    }
                                }
                            }

                            // 変数.「グループON/OFFフラグ」＝ON（１）の場合
                            if ("1".equals(groupOnOffFlag)) {
                                // 2回目のSO電文作成
                                // プライマリTPCへのSOAP連携
                                try {
                                    soapCommonOutputDto2KaiMe =
                                        await this.sendSoapApiToTpcAplysrv(
                                            lineNo,
                                            tenantId,
                                            sequenceNo,
                                            tpcConnectionResults[1],
                                            true,
                                        );
                                } catch (e) {
                                    if (SOAPException.isSOAPException(e)) {
                                        // SOAP送受信でエラーとなった場合
                                        // ログ出力
                                        super.warn(
                                            e,
                                            tenantId,
                                            sequenceNo,
                                            MsgKeysConstants.APLPCW1401,
                                        );
                                        // 変数「処理コード」設定
                                        handleCode =
                                            ResultCdConstants.CODE_061401;
                                        // 返却値編集
                                        const output = this.returnEdit(
                                            param,
                                            handleCode,
                                            apiHandleId,
                                            receivedDate,
                                        );
                                        super.debug(
                                            tenantId,
                                            sequenceNo,
                                            MsgKeysConstants.APLPCD0002,
                                            this.getClassName(),
                                            "service",
                                        );
                                        return output;
                                    }
                                }

                                if (
                                    ResultCdConstants.CODE_000951.equals(
                                        soapCommonOutputDto2KaiMe.getProcessCode(),
                                    )
                                ) {
                                    // SG取得がNG
                                    // ログ出力
                                    super.warn(
                                        tenantId,
                                        sequenceNo,
                                        MsgKeysConstants.APLPCW1401,
                                    );
                                    // 変数「処理コード」設定
                                    handleCode = ResultCdConstants.CODE_061401;
                                    // 返却値編集
                                    const output = this.returnEdit(
                                        param,
                                        handleCode,
                                        apiHandleId,
                                        receivedDate,
                                    );
                                    super.debug(
                                        tenantId,
                                        sequenceNo,
                                        MsgKeysConstants.APLPCD0002,
                                        this.getClassName(),
                                        "service",
                                    );
                                    return output;
                                }

                                document2KaiMe =
                                    soapCommonOutputDto2KaiMe.getDoc();
                                let resultStr2 = this.sOAPCommon.getNodeContent(
                                    document2KaiMe,
                                    "//Result",
                                );

                                if (
                                    soapCommonOutputDto2KaiMe.isError() ||
                                    this.CONST_RESULT.equals(resultStr2)
                                ) {
                                    // ログ出力
                                    super.warn(
                                        tenantId,
                                        sequenceNo,
                                        MsgKeysConstants.APLPCW1401,
                                    );
                                    // 変数「処理コード」設定
                                    handleCode = ResultCdConstants.CODE_061401;
                                    // 返却値編集
                                    const output = this.returnEdit(
                                        param,
                                        handleCode,
                                        apiHandleId,
                                        receivedDate,
                                    );
                                    super.debug(
                                        tenantId,
                                        sequenceNo,
                                        MsgKeysConstants.APLPCD0002,
                                        this.getClassName(),
                                        "service",
                                    );
                                    return output;
                                }

                                // セカンダリTPCへのSOAP連携
                                if (tpcConnectionResults[2] !== null) {
                                    try {
                                        soapCommonOutputDto2KaiMe =
                                            await this.sendSoapApiToTpcAplysrv(
                                                lineNo,
                                                tenantId,
                                                sequenceNo,
                                                tpcConnectionResults[2],
                                                false,
                                            );

                                        if (
                                            ResultCdConstants.CODE_000951.equals(
                                                soapCommonOutputDto2KaiMe.getProcessCode(),
                                            )
                                        ) {
                                            // SG取得がNG
                                            // ログ出力
                                            super.warn(
                                                tenantId,
                                                sequenceNo,
                                                MsgKeysConstants.APLPCW1402,
                                                "SG取得NG",
                                            );
                                        } else {
                                            // TPCレスポンス判定
                                            document2KaiMe =
                                                soapCommonOutputDto2KaiMe.getDoc();
                                            resultStr2 =
                                                this.sOAPCommon.getNodeContent(
                                                    document2KaiMe,
                                                    "//Result",
                                                );
                                            if (
                                                soapCommonOutputDto2KaiMe.isError() ||
                                                this.CONST_RESULT.equals(
                                                    resultStr2,
                                                )
                                            ) {
                                                // ログ出力
                                                const errInfo =
                                                    this.sOAPCommon.getNodeContent(
                                                        document2KaiMe,
                                                        "//ErrorInfomation",
                                                    );
                                                super.warn(
                                                    tenantId,
                                                    sequenceNo,
                                                    MsgKeysConstants.APLPCW1402,
                                                    errInfo,
                                                );
                                            }
                                        }
                                    } catch (e) {
                                        if (SOAPException.isSOAPException(e)) {
                                            // SOAP送受信でエラーとなった場合
                                            // ログ出力
                                            super.warn(
                                                e,
                                                tenantId,
                                                sequenceNo,
                                                MsgKeysConstants.APLPCW1402,
                                                e.toString(),
                                            );
                                        }
                                    }
                                }
                            }

                            // 10社内テナント種別判定
                            if (!"1".equals(tenantType)) {
                                /** #417対応 変更 START pricePlanIdAfterNameを追加 */
                                if (
                                    !"2".equals(tenantType) &&
                                    !"3".equals(tenantType) &&
                                    !"5".equals(tenantType)
                                ) {
                                    pricePlanIdAfterName = null;
                                }
                                let updateLineInfoPlanChange: number;
                                try {
                                    updateLineInfoPlanChange =
                                        await this.aPILinesDAO.updateLineInfoPlanChange(
                                            lineNo,
                                            pricePlanIdAfter,
                                            pricePlanIdAfterName,
                                            new Date().getTime(),
                                            tx,
                                        );
                                } catch (e) {
                                    if (isSQLException(e)) {
                                        // DBアクセスリトライエラーの場合
                                        handleCode =
                                            ResultCdConstants.CODE_061201;
                                    }
                                    throw e;
                                }
                                if (updateLineInfoPlanChange !== 1) {
                                    // ログ出力
                                    super.warn(
                                        tenantId,
                                        sequenceNo,
                                        MsgKeysConstants.APLPCW0011,
                                    );
                                    // 変数「処理コード」設定
                                    handleCode = ResultCdConstants.CODE_061201;
                                    // 返却値編集
                                    const output = this.returnEdit(
                                        param,
                                        handleCode,
                                        apiHandleId,
                                        receivedDate,
                                    );
                                    super.debug(
                                        tenantId,
                                        sequenceNo,
                                        MsgKeysConstants.APLPCD0002,
                                        this.getClassName(),
                                        "service",
                                    );
                                    return output;
                                }

                                // 回線回線グループのレコード取得
                                try {
                                    groupIdlst =
                                        await this.aPILinesDAO.getGroupId(
                                            lineNo,
                                        );
                                } catch (e) {
                                    if (isSQLException(e)) {
                                        // DBアクセスリトライエラーの場合
                                        handleCode =
                                            ResultCdConstants.CODE_061701;
                                    }

                                    throw e;
                                }
                                if (groupIdlst.length > 0) {
                                    // 回線回線グループのレコードが存在した場合に更新を行う
                                    let updateLineLineGroupsCapacity: number;
                                    try {
                                        updateLineLineGroupsCapacity =
                                            await this.aPILinesGroupDAO.updateLineLineGroupsCapacity(
                                                groupIdlst[0],
                                                lineNo,
                                                basicCapacity,
                                                tx,
                                            );
                                    } catch (e) {
                                        if (isSQLException(e)) {
                                            // DBアクセスリトライエラーの場合
                                            handleCode =
                                                ResultCdConstants.CODE_061701;
                                        }

                                        throw e;
                                    }
                                    if (updateLineLineGroupsCapacity !== 1) {
                                        // 更新に失敗した場合
                                        // ロールバック
                                        await tx.rollback();
                                        tx = null;
                                        // ログ出力
                                        super.warn(
                                            tenantId,
                                            sequenceNo,
                                            MsgKeysConstants.APLPCW0036,
                                        );
                                        // 変数「処理コード」設定
                                        handleCode =
                                            ResultCdConstants.CODE_061701;
                                        // 返却値編集
                                        const output = this.returnEdit(
                                            param,
                                            handleCode,
                                            apiHandleId,
                                            receivedDate,
                                        );
                                        return output;
                                    }
                                }
                            }
                        }

                        // リトライしたタイミングでエラーコードを設定しているので
                        // リトライ後に正常となった場合は正常コードに戻す
                        handleCode = ResultCdConstants.CODE_000000;
                        await tx.commit();
                        tx = null;
                        this.context.log(
                            "LinePlanAcquisitionServiceTx.service transaction committed",
                        );
                        return null;
                    } finally {
                        if (tx !== null) {
                            this.context.warn(
                                "LinePlanAcquisitionServiceTx.service transaction is still open, rolling back",
                            );
                            try {
                                await tx.rollback();
                                tx = null;
                                this.context.warn(
                                    "LinePlanAcquisitionServiceTx.service rollback successful",
                                );
                            } catch (e) {
                                this.context.error(
                                    "LinePlanAcquisitionServiceTx.service error while rolling back transaction",
                                    e,
                                );
                            }
                        }
                    }
                },
                this.apDbRetryMaxCnt,
                this.apDbRetryInterval,
            );

            if (result !== null) {
                return result;
            }
            let nNo: string = null;
            let reqDate: string = null;
            if (
                Constants.ORDER_TYPE_0.equals(orderType) ||
                Constants.ORDER_TYPE_1.equals(orderType)
            ) {
                // CSVファイル用データ収集
                // 変数「社内フラグ」より判定する
                if (
                    !tenantsEntity.office ||
                    (tenantsEntity.office && !"1".equals(csvUnnecessaryFlag))
                ) {
                    // 代表N番の判定する
                    if (StringUtils.isEmpty(checkResult[1])) {
                        // 変数「CSV作成要否」を0で処理する
                        csvOutputKbn = "0";
                        // ログ出力
                        super.error(
                            tenantId,
                            sequenceNo,
                            MsgKeysConstants.APLPCE1603,
                            tenantId,
                        );
                        // 変数「処理コード」設定
                        handleCode = ResultCdConstants.CODE_000000;
                        // 返却値編集
                        const output = this.returnEdit(
                            param,
                            handleCode,
                            apiHandleId,
                            receivedDate,
                        );
                        super.debug(
                            tenantId,
                            sequenceNo,
                            MsgKeysConstants.APLPCD0002,
                            this.getClassName(),
                            "service",
                        );
                        return output;
                    }

                    //  変数「帯域卸フラグ」取得
                    let wholeFlagList: boolean[] = null;
                    try {
                        wholeFlagList = await this.aPILinesDAO.getWholeFlag(
                            tenantId,
                            checkResult[1],
                        );
                    } catch (ex) {
                        if (isSQLException(ex)) {
                            // 変数「CSV作成要否」を0で処理する
                            csvOutputKbn = "0";
                            // ログ出力
                            super.error(
                                ex,
                                tenantId,
                                sequenceNo,
                                MsgKeysConstants.APCOME0402,
                                functionType,
                            );
                            // 変数「処理コード」設定
                            handleCode = ResultCdConstants.CODE_000000;
                            // 返却値編集
                            const output = this.returnEdit(
                                param,
                                handleCode,
                                apiHandleId,
                                receivedDate,
                            );
                            return output;
                        } else {
                            throw ex;
                        }
                    }
                    // 変数「帯域卸フラグ」の件数が0件あるいば変数「帯域卸フラグ」がnullあるいば変数「帯域卸フラグ」がTRUE以外の場合：
                    if (
                        wholeFlagList === null ||
                        wholeFlagList.length === 0 ||
                        wholeFlagList[0] === null ||
                        !wholeFlagList[0]
                    ) {
                        // 送信元システムIDのチェック
                        // 入力パラメータ．「ヘッダー情報」．「送信元システムID」が「9a1z」以外の場合：
                        if (
                            !tenantsEntity.office ||
                            (!"9a1z".equals(senderSystemId) &&
                                tenantsEntity.office)
                        ) {
                            // 変数「料金プランID」取得
                            try {
                                resalePlanIdPlanList =
                                    await this.aPILinesDAO.getResalePlanIdPlan(
                                        potalPlanID,
                                    );
                            } catch (ex) {
                                if (isSQLException(ex)) {
                                    // 変数「CSV作成要否」を0で処理する
                                    csvOutputKbn = "0";
                                    // ログ出力
                                    super.error(
                                        ex,
                                        tenantId,
                                        sequenceNo,
                                        MsgKeysConstants.APCOME0402,
                                        functionType,
                                    );
                                    // 変数「処理コード」設定
                                    handleCode = ResultCdConstants.CODE_000000;
                                    // 返却値編集
                                    const output = this.returnEdit(
                                        param,
                                        handleCode,
                                        apiHandleId,
                                        receivedDate,
                                    );
                                    return output;
                                } else {
                                    throw ex;
                                }
                            }
                            // 変数「料金プランID」の件数が0件の場合
                            if (
                                resalePlanIdPlanList === null ||
                                resalePlanIdPlanList.length === 0 ||
                                !Check.checkCsvResalePlanIdFmt(
                                    StringUtils.trimToEmpty(
                                        resalePlanIdPlanList[0],
                                    ),
                                )
                            ) {
                                // 変数「CSV作成要否」を0で処理する
                                csvOutputKbn = "0";
                                // ログ出力
                                super.error(
                                    tenantId,
                                    sequenceNo,
                                    MsgKeysConstants.APLPCE1605,
                                    potalPlanID,
                                );
                                // 変数「処理コード」設定
                                handleCode = ResultCdConstants.CODE_000000;
                                // 返却値編集
                                const output = this.returnEdit(
                                    param,
                                    handleCode,
                                    apiHandleId,
                                    receivedDate,
                                );
                                super.debug(
                                    tenantId,
                                    sequenceNo,
                                    MsgKeysConstants.APLPCD0002,
                                    this.getClassName(),
                                    "service",
                                );
                                return output;
                                /** #378 対応 変更 END */
                            }

                            // 変更前料金プランのプラン種別を取得する。
                            let planClassList: number[] = null;
                            try {
                                planClassList =
                                    await this.aPILinesDAO.getPlanClass(
                                        potalPlanIDPre,
                                    );
                            } catch (ex) {
                                if (isSQLException(ex)) {
                                    // 変数「CSV作成要否」を0で処理する
                                    csvOutputKbn = "0";
                                    // ログ出力
                                    super.error(
                                        ex,
                                        tenantId,
                                        sequenceNo,
                                        MsgKeysConstants.APCOME0402,
                                        functionType,
                                    );
                                    // 変数「処理コード」設定
                                    handleCode = ResultCdConstants.CODE_000000;
                                    // 返却値編集
                                    const output = this.returnEdit(
                                        param,
                                        handleCode,
                                        apiHandleId,
                                        receivedDate,
                                    );
                                    return output;
                                }
                            }

                            let planClass: string = null;
                            if (
                                planClassList !== null &&
                                planClassList.length !== 0 &&
                                planClassList[0] !== null
                            ) {
                                planClass = planClassList[0].toString();
                            }
                            // 変数「プラン種別」データ取得が０件の場合、あるいば、変数「プラン種別」取得した値がnullの場合
                            if (
                                planClassList === null ||
                                planClassList.length === 0 ||
                                planClass === null
                            ) {
                                // 変数「CSV作成要否」を0で処理する
                                csvOutputKbn = "0";
                                // ログ出力
                                super.error(
                                    tenantId,
                                    sequenceNo,
                                    MsgKeysConstants.APLPCE1606,
                                    potalPlanIDPre,
                                );
                                // 変数「処理コード」設定
                                handleCode = ResultCdConstants.CODE_000000;
                                // 返却値編集
                                const output = this.returnEdit(
                                    param,
                                    handleCode,
                                    apiHandleId,
                                    receivedDate,
                                );
                                super.debug(
                                    tenantId,
                                    sequenceNo,
                                    MsgKeysConstants.APLPCD0002,
                                    this.getClassName(),
                                    "service",
                                );
                                return output;
                                // 変数「プラン種別」取得した値が”2”の場合
                            } else if ("2".equals(planClass)) {
                                // 変数．「初期SIMフラグ」＝ON（1）
                                shokiSIMFlag = "1";
                            } else {
                                // 変数．「初期SIMフラグ」＝OFF（0）
                                shokiSIMFlag = "0";
                            }
                            // 変数「CSV作成要否」を1で処理する
                            csvOutputKbn = "1";
                        } else {
                            // 変数「CSV作成要否」を0で処理する
                            csvOutputKbn = "0";
                        }
                    } else {
                        // 変数「CSV作成要否」を0で処理する
                        csvOutputKbn = "0";
                    }
                } else {
                    // 変数「CSV作成要否」を0で処理する
                    csvOutputKbn = "0";
                }
                // 変数「CSV作成要否」＝“1” の場合
                if ("1".equals(csvOutputKbn)) {
                    nNo = checkResult[1];
                    if ("0".equals(shokiSIMFlag)) {
                        // 即時オーダの場合
                        if (Constants.ORDER_TYPE_0.equals(orderType)) {
                            reqDate = formatDate(
                                parse(
                                    receivedDate,
                                    "yyyy/MM/dd HH:mm:ss",
                                    new Date(),
                                ),
                                "yyyyMMdd",
                            );
                        } else if (Constants.ORDER_TYPE_1.equals(orderType)) {
                            reqDate = formatDate(
                                parse(
                                    reserveDate,
                                    "yyyy/MM/dd HH:mm",
                                    new Date(),
                                ),
                                "yyyyMMdd",
                            );
                        }
                    } else {
                        // 即時オーダの場合
                        if (Constants.ORDER_TYPE_0.equals(orderType)) {
                            reqDate = formatDate(
                                this.getDate2(receivedDate),
                                "yyyyMMdd",
                            );
                        } else if (Constants.ORDER_TYPE_1.equals(orderType)) {
                            reqDate = formatDate(
                                this.getDate2(reserveDate),
                                "yyyyMMdd",
                            );
                        }
                    }
                }
            } else {
                // 変数「CSV作成要否」を0で処理する
                csvOutputKbn = "0";
            }

            // Swimmy連携 need T番 of new plan, so need to check here
            if (csvOutputKbn === "1" && isNone(newPlanIDT)) {
                this.context.error("Missing T番 for plan ", potalPlanID, "(csvOutputKbn=1)");
                // override csvOutputKbn to 0 to skip Swimmy連携
                csvOutputKbn = "0";
            }

            // 返却値編集
            const output = this.returnEdit(
                param,
                handleCode,
                apiHandleId,
                receivedDate,
                nNo,
                csvOutputKbn,
                lineNo,
                newPlanIDT ?? null,
                shokiSIMFlag,
                reqDate,
            );

            if (ResultCdConstants.CODE_000000.equals(handleCode)) {
                // 予約前オーダ以外は自動計算回線グループ情報追加処理を行う。
                if (!Constants.ORDER_TYPE_1.equals(orderType)) {
                    await this.addAutoModbucketLineGroup(
                        apiHandleId,
                        functionType,
                        handleCode,
                        lineNo,
                        null,
                        tenantId,
                        sequenceNo,
                        orderType,
                    );
                }
            }
            super.debug(
                tenantId,
                sequenceNo,
                MsgKeysConstants.APLPCD0002,
                this.getClassName(),
                "service",
            );
            return output;
        } catch (e) {
            if (isSQLException(e)) {
                // ログ出力
                super.warn(
                    e,
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APCOME0402,
                );
                // 返却値編集
                const output = this.returnEdit(
                    param,
                    handleCode,
                    apiHandleId,
                    receivedDate,
                );
                return output;
            } else if (SOAPException.isSOAPException(e)) {
                // ログ出力
                super.warn(
                    e,
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APLPCW0010,
                );
                // 変数「処理コード」設定
                handleCode = ResultCdConstants.CODE_061101;
                // 返却値編集
                const output = this.returnEdit(
                    param,
                    handleCode,
                    apiHandleId,
                    receivedDate,
                );
                super.debug(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APLPCD0002,
                    this.getClassName(),
                    "service",
                );
                return output;
            } else {
                // ログ出力
                super.warn(
                    e,
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APCOME0401,
                    e,
                    receivedDate,
                );
                // 変数「処理コード」設定
                handleCode = ResultCdConstants.CODE_999999;
                // 返却値編集
                const output = this.returnEdit(
                    param,
                    handleCode,
                    apiHandleId,
                    receivedDate,
                );
                super.debug(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APLPCD0002,
                    this.getClassName(),
                    "service",
                );
                return output;
            }
        } finally {
            // if code finishes before a transaction reaches update logic section
            if (tx) {
                await tx.rollback();
                tx = null;
            }
            if (!Constants.ORDER_TYPE_9.equals(orderType) && !reserveActFlag) {
                // SO管理共通パラメータ設定
                await this.soManagementCommon(
                    param,
                    apiHandleId,
                    null,
                    null,
                    handleCode,
                    receivedDate,
                    orderType,
                    reserveDate,
                    isPrivate,
                );
            }
        }
    }

    /**
     * SO管理共通パラメータ設定
     *
     * @param param
     * @param resultBean
     * @param executeUserId
     * @param executeTenantId
     * @param receivedDate REST API共通処理が呼び出されたシステム日付
     * @param orderType
     * @param reserveDate
     * @param isPrivate
     * @throws Exception
     */

    public async soManagementCommon(
        param: LinePlanAcquisitionInputDto,
        apiHandleId: string,
        executeUserId: string,
        executeTenantId: string,
        handleCode: string,
        receivedDate: string,
        orderType: string,
        reserveDate: string,
        isPrivate: boolean,
    ) {
        // SO管理共通を呼び出す
        const soObject = new SOObject();

        // サービスオーダID
        soObject.setServiceOrderId(apiHandleId);
        // 申込日
        soObject.setOrderDate(receivedDate);

        // 予約日
        // 変数「オーダ種別」は「予約前オーダ」,予約実行オーダの場合
        if (Constants.ORDER_TYPE_1.equals(orderType)) {
            soObject.setReserveDate(reserveDate);
        } else {
            soObject.setReserveDate(null);
        }
        // オーダ種別
        soObject.setOrderType(orderType);
        // 完了日
        // 変数「オーダ種別」は「即時オーダ」の場合
        if (
            Constants.ORDER_TYPE_0.equals(orderType) ||
            Constants.ORDER_TYPE_2.equals(orderType)
        ) {
            soObject.setExecDate(MvnoUtil.getDateTimeNow());
        } else {
            soObject.setExecDate(null);
        }
        // オーダステータス
        soObject.setOrderStatus(handleCode);
        // 回線ID
        soObject.setLineId(param.lineNo);
        // 回線グループID
        soObject.setLineGroupId("");
        // 所属テナントID
        soObject.setTenantId(param.tenantId);
        // 実行ユーザID
        soObject.setExecuteUserId(null);
        // 実行テナントID
        soObject.setExecuteTenantId(null);
        // 機能種別
        soObject.setFunctionType(param.requestHeader.functionType);
        // 操作区分
        soObject.setOperationDivision("");
        // 変更前プランID
        soObject.setChangeOldplanId(getStringParameter(param.potalPlanID_pre));
        // 変更後プランID
        soObject.setChangeNewplanId(getStringParameter(param.potalPlanID));
        // 回線所属変更回線ID
        soObject.setChangePlanId("");
        // 基本容量
        soObject.setBasicCapacity("");
        // オプションプランID
        soObject.setOptionPlanId("");
        // 内部呼び出し
        soObject.setInternalFlag(isPrivate);
        // REST電文
        // 変数「オーダ種別」は「予約前オーダ」の場合
        if (Constants.ORDER_TYPE_1.equals(orderType)) {
            soObject.setRestMessage(MvnoUtil.getRestMessage(param));
        } else {
            soObject.setRestMessage(null);
        }
        await this.soCommon.soCommon(soObject);
    }

    /**
     * 返却値編集。<BR>
     *
     * @param param 回線プラン変更機能インプット
     * @param rtnHandleCode 処理コード
     * @param rtnapiHandleId API処理ID
     * @param receivedDate REST API共通処理が呼び出されたシステム日付
     * @return LineAddAcquisitionOutputDto 回線プラン変更機能アウトプット
     */
    private returnEdit(
        param: LinePlanAcquisitionInputDto,
        rtnHandleCode: string,
        rtnapiHandleId: string,
        receivedDate: string,
        nNo: string = null,
        csvOutputKbn: string = "0",
        lineNo: string = null,
        resalePlanIdT: string = null,
        shokiSIMFlag: string = null,
        requestDate: string = null,
    ) {
        const linePlanAcquisitionOutputDto: LinePlanAcquisitionOutputDto = {
            jsonBody: {
                responseHeader: {
                    sequenceNo: param.requestHeader.sequenceNo,
                    receivedDate,
                    processCode: rtnHandleCode,
                    apiProcessID: rtnapiHandleId,
                },
            },
            additionalData: {
                csvOutputKbn,
                nNo,
                csvUnnecessary: csvOutputKbn === "0",
                lineNo,
                resalePlanIdT,
                shokiSIMFlag,
                requestDate,
            },
        };
        return linePlanAcquisitionOutputDto;
    }

    /**
     * システム日付の当月の月初め。<BR>
     *
     * @param str
     * @return システム日付の当月の月初め
     */
    private getDate1(str: string): Date {
        const year = parseInt(str.substring(0, 4), 10);
        const month = parseInt(str.substring(5, 7), 10);
        const cal = new Date();
        cal.setFullYear(year);
        cal.setMonth(month - 1);
        cal.setDate(1);
        return cal;
    }
    /**
     * システム日付の次月の月初め。<BR>
     *
     * @param str
     * @return システム日付の次月の月初め
     */
    private getDate2(str: string): Date {
        const year = parseInt(str.substring(0, 4), 10);
        const month = parseInt(str.substring(5, 7), 10);
        const cal = new Date();
        cal.setFullYear(year);
        cal.setMonth(month);
        cal.setDate(1);
        return cal;
    }

    /**
     * 更新サービスパターンチェック<BR>
     *
     * @param session セッション
     * @param potalPlanID 変更後卸ポータルプランID
     * @param groupIdlst 回線グループID
     * @return 更新サービスパターンチェック true:OK、false:NG
     * @throws Exception 例外
     */
    private async checkTpcServicePattern(
        tx: Transaction,
        potalPlanID: string,
        groupIdlst: string[],
    ) {
        // 回線に対応するプランの更新サービスパターンを取得する
        let portalPlanInfoList: PlansEntity[] = null;
        try {
            portalPlanInfoList = await this.aPICommonDAO.getPlans(potalPlanID);
        } catch (e) {
            if (isSQLException) {
                // DBアクセスリトライエラーの場合
                throw e;
            }
        }
        // プラン情報が一意に取得できない場合エラー
        if (
            portalPlanInfoList === null ||
            portalPlanInfoList.length === 0 ||
            portalPlanInfoList[0].tpcServicePattern === null
        ) {
            return false;
        }
        // 更新サービスパターンが0の場合は、グループ更新サービスパターンを取得せず、判定OK
        if (portalPlanInfoList[0].tpcServicePattern === 0) {
            return true;
        }

        // 回線グループIDが保持する回線グループプランIDを取得
        let lineGroupsInfo: LineGroupsEntity = null;
        try {
            lineGroupsInfo = await this.aPILinesGroupDAO.getLineGroupsInfo(
                groupIdlst[0],
                tx,
            );
        } catch (e) {
            if (isSQLException) {
                // DBアクセスリトライエラーの場合
            }
            throw e;
        }
        // 回線グループ情報が取得できない場合エラー
        if (lineGroupsInfo === null || lineGroupsInfo.planId === null) {
            return false;
        }

        // グループ更新サービスパターンを取得
        let groupEnt: GroupPlansEntity = null;
        try {
            groupEnt = await this.aPILinesGroupDAO.getUpdateServicePatternGroup(
                lineGroupsInfo.planId,
            );
        } catch (e) {
            if (isSQLException) {
                // DBアクセスリトライエラーの場合
            }
            throw e;
        }

        const grpTpcSrvPtn: number =
            groupEnt === null ? null : groupEnt.tpcServicePattern;
        // グループ更新サービスパターンを取得できない場合エラー
        if (grpTpcSrvPtn === null) {
            return false;
        }

        // 更新サービスパターンがグループ更新サービスパターンと一致しない場合
        if (grpTpcSrvPtn !== portalPlanInfoList[0].tpcServicePattern) {
            return false;
        }

        return true;
    }

    /**
     * SOAP API送信(プラン変更)
     *
     * @param lineNo 回線No
     * @param servicePlanId サービスプランID
     * @param tenantId テナントID
     * @param sequenceNo シーケンスNo
     * @param tpcDestInfo TPC情報
     * @param primaryFlg プライマリフラグ
     * @return SOAPCommonOutputDto SOAP応答電文情報
     * @throws Exception
     */
    private async sendSoapApiToTpcPlan(
        lineNo: string,
        servicePlanId: string,
        tenantId: string,
        sequenceNo: string,
        tpcDestInfo: string,
        primaryFlg: boolean,
    ) {
        // TPC電文作成
        const paraList: ParameterName[] = [];
        const phoneNumber = new ParameterName();
        phoneNumber.setName("phone_number");
        phoneNumber.setValue(lineNo);
        const policynumber = new ParameterName();
        policynumber.setName("policy_number");
        policynumber.setValue(servicePlanId);
        paraList.push(phoneNumber);
        paraList.push(policynumber);
        // SOAP-API 電文送受信
        return await this.sOAPCommon.callWebSoapApi(
            "serviceProfileRequestLite",
            "Mod",
            "",
            paraList,
            tenantId,
            sequenceNo,
            tpcDestInfo,
            primaryFlg,
        );
    }

    /**
     * SOAP API送信(総量規制)
     * @param lineNo 回線No
     * @param tenantId テナントID
     * @param sequenceNo シーケンスNo
     * @param tpcDestInfo TPC情報
     * @param primaryFlg プライマリフラグ
     * @return SOAPCommonOutputDto SOAP応答電文情報
     * @throws Exception
     */
    private async sendSoapApiToTpcAplysrv(
        lineNo: string,
        tenantId: string,
        sequenceNo: string,
        tpcDestInfo: string,
        primaryFlg: boolean,
    ) {
        // TPC電文作成
        const paraList2KaiMe: ParameterName[] = [];
        const parameterPronum = new ParameterName();
        parameterPronum.setName("pronum");
        const value1Pronum = "101" + MvnoUtil.addZeroBeforeStr(lineNo, 15);
        parameterPronum.setValue(value1Pronum);
        const parameterAplySrv = new ParameterName();
        parameterAplySrv.setName("aply_srv");
        parameterAplySrv.setValue("0");
        paraList2KaiMe.push(parameterPronum);
        paraList2KaiMe.push(parameterAplySrv);

        // SOAP-API 電文送受信
        return await this.sOAPCommon.callWebSoapApi(
            "serviceProfileRequest",
            "Mod",
            "Service",
            paraList2KaiMe,
            tenantId,
            sequenceNo,
            tpcDestInfo,
            primaryFlg,
        );
    }
}
