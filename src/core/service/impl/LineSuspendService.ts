import { Transaction } from "sequelize";

import Constants from "@/core/constant/Constants";
import MsgKeysConstants from "@/core/constant/MsgKeysConstants";
import ResultCdConstants from "@/core/constant/ResultCdConstants";

import Api<PERSON>ommon from "@/core/common/ApiCommon";
import Check from "@/core/common/Check";
import MvnoUtil from "@/core/common/MvnoUtil";
import RESTCommon from "@/core/common/RESTCommon";
import SOCommon from "@/core/common/SOCommon";
import TenantManage from "@/core/common/TenantManage";

import APICommonDAO from "@/core/dao/APICommonDAO";
import APILinesDAO from "@/core/dao/APILinesDAO";
import APILinesGroupDAO from "@/core/dao/ApiLinesGroupDAO";
import LineSuspendInputDto from "@/core/dto/LineSuspendInputDto";
import LineSuspendOutputDto from "@/core/dto/LineSuspendOutputDto";
import SOObject from "@/core/dto/SOObject";

import { LinesEntity } from "@/core/entity/LinesEntity";
import PlansEntity from "@/core/entity/PlansEntity";

import { usePsql } from "@/database/psql";
import { isSQLException, retryQuery } from "@/helpers/queryHelper";

import { IFService } from "../IFService";

export default class LineSuspendService
    extends RESTCommon
    implements IFService<LineSuspendInputDto, LineSuspendOutputDto>
{
    /**
     * API共通DAO
     */
    // @Autowired
    // private APICommonDAO apiCommonDao;
    private apiCommonDao = new APICommonDAO(this.request, this.context);

    /**
     * API回線DAO
     */
    // @Autowired
    // private APILinesDAO apiLinesDao;
    private apiLinesDao = new APILinesDAO(this.request, this.context);

    /**
     * API回線グループDAO
     */
    // @Autowired
    // private APILinesGroupDAO apiLinesGroupDao;
    private apiLinesGroupDao = new APILinesGroupDAO(this.request, this.context);

    /**
     * SO管理共通DAO
     */
    // @Autowired
    // private SOCommon soCommon;
    private soCommon = new SOCommon(this.request, this.context);

    /**
     * テナント管理
     */
    // @Autowired
    // private TenantManage tenantManage;
    private tenantManage = new TenantManage(this.request, this.context);

    /**
     * API共通処理
     */
    // @Autowired
    // private ApiCommon apiCommon;
    private apiCommon = new ApiCommon(this.request, this.context);

    public async service(
        param: LineSuspendInputDto,
    ): Promise<LineSuspendOutputDto> {
        this.context.log(
            "LineSuspendService.service start",
            JSON.stringify(param),
        );
        // 入力電文の内容を取得
        // 送信番号取得
        const sequenceNo = param.requestHeader.sequenceNo;
        // 送信元システムID取得
        const senderSystemId = param.requestHeader.senderSystemId;
        // API認証キー取得
        const apiKey = param.requestHeader.apiKey;
        // 機能種別取得
        const functionType = param.requestHeader.functionType;

        // テナントID
        const tenantId = param.tenantId;
        // 対象回線の所属テナントID
        const targetTenantId = param.targetTenantId;
        // 回線番号
        const lineNo = param.lineNo;
        // 利用状態変更フラグ
        const suspendFlag = param.suspendFlag;

        // 変数「処理コード」初期化
        let handleCode = ResultCdConstants.CODE_000000;
        // 変数「システム時刻」取得
        const receivedDate = this.context.responseHeader.receivedDate;
        // 変数「API処理ID」
        const apiHandleId = this.context.responseHeader.apiProcessID;

        // トランザクション管理
        let tx: Transaction = null;
        let commitFlag = false;
        const sequelize = await usePsql();

        try {
            // No.1 REST-API電文パラメータ相関チェック
            // ① 各チェック項目のフォーマットが一致しているかを確認
            // ・テナントID
            if (!Constants.FRONT_API_TENANT_ID.equals(tenantId)) {
                this.context.log(
                    "LineSuspendService: tenantId is not front tenant",
                    tenantId,
                );
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APIRSW0001,
                    "テナントID",
                    tenantId,
                );
                handleCode = ResultCdConstants.CODE_580101;
                // SO管理共通
                await this.soManagement(
                    param,
                    apiHandleId,
                    handleCode,
                    receivedDate,
                    functionType,
                );
                // 返却値編集
                const returnParam = this.returnEdit(
                    param,
                    handleCode,
                    apiHandleId,
                    receivedDate,
                );
                // 値の返却
                return returnParam;
            }

            // ・対象回線の所属テナントID
            if (!Check.checkTenatIdFmt(targetTenantId)) {
                this.context.log(
                    "LineSuspendService: targetTenantId is not valid",
                    targetTenantId,
                );
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APIRSW0001,
                    "対象回線の所属テナントID",
                    targetTenantId,
                );
                handleCode = ResultCdConstants.CODE_580101;
                // SO管理共通
                await this.soManagement(
                    param,
                    apiHandleId,
                    handleCode,
                    receivedDate,
                    functionType,
                );
                // 返却値編集
                const returnParam = this.returnEdit(
                    param,
                    handleCode,
                    apiHandleId,
                    receivedDate,
                );
                // 値の返却
                return returnParam;
            }

            // ・回線番号
            if (!Check.checkLineNo(lineNo)) {
                this.context.log(
                    "LineSuspendService: lineNo is not valid",
                    lineNo,
                );
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APIRSW0001,
                    "回線番号",
                    lineNo,
                );
                handleCode = ResultCdConstants.CODE_580101;
                // SO管理共通
                await this.soManagement(
                    param,
                    apiHandleId,
                    handleCode,
                    receivedDate,
                    functionType,
                );
                // 返却値編集
                const returnParam = this.returnEdit(
                    param,
                    handleCode,
                    apiHandleId,
                    receivedDate,
                );
                // 値の返却
                return returnParam;
            }

            // ・利用状態変更フラグ
            if (
                !"0".equals(suspendFlag) &&
                !"1".equals(suspendFlag) &&
                !"2".equals(suspendFlag)
            ) {
                this.context.log(
                    "LineSuspendService: suspendFlag is not valid",
                    suspendFlag,
                );
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APIRSW0001,
                    "利用状態変更フラグ",
                    suspendFlag,
                );
                handleCode = ResultCdConstants.CODE_580101;
                // SO管理共通
                await this.soManagement(
                    param,
                    apiHandleId,
                    handleCode,
                    receivedDate,
                    functionType,
                );
                // 返却値編集
                const returnParam = this.returnEdit(
                    param,
                    handleCode,
                    apiHandleId,
                    receivedDate,
                );
                // 値の返却
                return returnParam;
            }

            // ② 対象回線が廃止済でないか
            let aboneLineCheckResult: LinesEntity = null;
            try {
                aboneLineCheckResult =
                    await this.apiLinesDao.getLineInfoByLines(lineNo);
            } catch (e) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    handleCode = ResultCdConstants.CODE_580102;
                }
                throw e;
            }
            // 変数「対象回線廃止確認結果」より判定する
            if (
                aboneLineCheckResult === null ||
                "03".equals(aboneLineCheckResult.lineStatus)
            ) {
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APIRSW0002,
                    lineNo,
                );
                handleCode = ResultCdConstants.CODE_580102;
                // SO管理共通
                await this.soManagement(
                    param,
                    apiHandleId,
                    handleCode,
                    receivedDate,
                    functionType,
                );
                // 返却値編集
                const returnParam = this.returnEdit(
                    param,
                    handleCode,
                    apiHandleId,
                    receivedDate,
                );
                // 値の返却
                return returnParam;
            }

            // ③ 回線番号がテナントIDに所属しているか
            let checkResult: [boolean, string] = null;
            try {
                checkResult = await this.tenantManage.doCheck(
                    lineNo,
                    targetTenantId,
                );
            } catch (e) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    handleCode = ResultCdConstants.CODE_580103;
                }
                throw e;
            }
            // 変数「関係チェック結果」より判定する
            if (!checkResult[0]) {
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APIRSW0003,
                    lineNo,
                    targetTenantId,
                );
                handleCode = ResultCdConstants.CODE_580103;
                // SO管理共通
                await this.soManagement(
                    param,
                    apiHandleId,
                    handleCode,
                    receivedDate,
                    functionType,
                );
                // 返却値編集
                const returnParam = this.returnEdit(
                    param,
                    handleCode,
                    apiHandleId,
                    receivedDate,
                );
                // 値の返却
                return returnParam;
            }

            // ④ フルMVNOの確認(回線に紐づくプランの確認)
            let planInfo: PlansEntity[] = null;
            try {
                planInfo = await this.apiLinesDao.getPlanInfo(
                    aboneLineCheckResult.pricePlanId,
                );
            } catch (e) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    handleCode = ResultCdConstants.CODE_580105;
                }
                throw e;
            }
            if (
                planInfo.length === 0 ||
                planInfo[0].fullMvnoFlag === null ||
                !planInfo[0].fullMvnoFlag
            ) {
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APIRSW0004,
                    lineNo,
                    aboneLineCheckResult.pricePlanId,
                );
                handleCode = ResultCdConstants.CODE_580105;
                // SO管理共通
                await this.soManagement(
                    param,
                    apiHandleId,
                    handleCode,
                    receivedDate,
                    functionType,
                );
                // 返却値編集
                const returnParam = this.returnEdit(
                    param,
                    handleCode,
                    apiHandleId,
                    receivedDate,
                );
                // 値の返却
                return returnParam;
            }

            /* --------------------------------- リトライ処理開始 --------------------------------- */
            const result = await retryQuery(
                this.context,
                "LineSuspendService.service",
                async () => {
                    if (tx !== null) {
                        this.context.log("LineSuspendService.service rollback");
                        await tx.rollback();
                    }
                    // セッション開始
                    tx = await sequelize.transaction();

                    // No.2 回線情報の更新
                    try {
                        // 利用状態変更フラグの値で更新
                        await this.apiLinesDao.updateLineUsageStatus(
                            lineNo,
                            +suspendFlag,
                            MvnoUtil.convDateFormat(receivedDate),
                            tx,
                        );
                    } catch (ex) {
                        if (isSQLException(ex)) {
                            // DBアクセスリトライエラーの場合
                            handleCode = ResultCdConstants.CODE_580201;
                            throw ex;
                        }
                        // ロールバック
                        await tx.rollback();
                        super.error(
                            ex,
                            tenantId,
                            sequenceNo,
                            MsgKeysConstants.APIRSE0001,
                            lineNo,
                        );
                        handleCode = ResultCdConstants.CODE_580201;
                        // SO管理共通
                        await this.soManagement(
                            param,
                            apiHandleId,
                            handleCode,
                            receivedDate,
                            functionType,
                        );
                        // 返却値編集
                        const returnParam = this.returnEdit(
                            param,
                            handleCode,
                            apiHandleId,
                            receivedDate,
                        );
                        // 値の返却
                        return returnParam;
                    }

                    // No.3 回線回線グループの更新、自動回線グループの追加
                    let groupId = "";
                    try {
                        // (1) 対象回線がグループに所属しているか確認
                        const groupList =
                            await this.apiCommonDao.getGroupIdList(lineNo);
                        if (groupList.length !== 0) {
                            // 回線グループに紐づいている場合

                            groupId = groupList[0];

                            // (2) 回線回線グループの利用状態を更新
                            await this.apiLinesGroupDao.updateLineLineGroupsUsageStatus(
                                groupId,
                                lineNo,
                                +suspendFlag,
                                tx,
                            );
                            // NOTE no need to catch PersistenceException here

                            // (3) 自動計算回線グループにレコードを追加
                            if (
                                ("0".equals(suspendFlag) &&
                                    aboneLineCheckResult.usageStatus === 2) ||
                                ("2".equals(suspendFlag) &&
                                    aboneLineCheckResult.usageStatus === 0)
                            ) {
                                // 電文中の利用状態変更フラグ="0"(利用中) かつ 回線情報の利用状態=2(サスペンド中)
                                // または、
                                // 電文中の利用状態変更フラグ="2"(サスペンド中) かつ 回線情報の利用状態=0(利用中)

                                await this.addAutoModbucketLineGroup(
                                    apiHandleId,
                                    functionType,
                                    handleCode,
                                    lineNo,
                                    groupId,
                                    tenantId,
                                    sequenceNo,
                                    Constants.ORDER_TYPE_0,
                                );
                            }
                        }
                        // 回線グループに紐づいていない場合は、次の処理へ
                    } catch (ex) {
                        if (isSQLException(ex)) {
                            // DBアクセスリトライエラーの場合
                            handleCode = ResultCdConstants.CODE_580301;
                            throw ex;
                        }
                        // ロールバック
                        await tx.rollback();
                        super.error(
                            ex,
                            tenantId,
                            sequenceNo,
                            MsgKeysConstants.APIRSE0002,
                            lineNo,
                            groupId,
                        );
                        handleCode = ResultCdConstants.CODE_580301;
                        // SO管理共通
                        await this.soManagement(
                            param,
                            apiHandleId,
                            handleCode,
                            receivedDate,
                            functionType,
                        );
                        // 返却値編集
                        const returnParam = this.returnEdit(
                            param,
                            handleCode,
                            apiHandleId,
                            receivedDate,
                        );
                        // 値の返却
                        return returnParam;
                    }

                    // No.4 上位処理に処理結果返却
                    // トランザクションコミット
                    await tx.commit();
                    commitFlag = true;

                    // リトライしたタイミングでエラーコードを設定しているので
                    // リトライ後に正常となった場合は正常コードに戻す
                    handleCode = ResultCdConstants.CODE_000000;

                    return null;
                },
                this.apDbRetryMaxCnt,
                this.apDbRetryInterval,
            );
            /* --------------------------------- リトライ処理終了 --------------------------------- */
            if (result !== null) {
                // if not null then it is an early return
                this.context.log("LineSuspendService.service early return");
                return result;
            }

            // SO管理共通
            await this.soManagement(
                param,
                apiHandleId,
                handleCode,
                receivedDate,
                functionType,
            );
            // 返却値編集
            const returnParam = this.returnEdit(
                param,
                handleCode,
                apiHandleId,
                receivedDate,
            );

            return returnParam;
        } catch (e) {
            if (tx !== null && !commitFlag) {
                await tx.rollback();
            }
            if (isSQLException(e)) {
                super.error(
                    e,
                    param.tenantId,
                    param.requestHeader.sequenceNo,
                    MsgKeysConstants.APCOME0402,
                    functionType,
                );
                return this.returnEdit(
                    param,
                    handleCode,
                    apiHandleId,
                    receivedDate,
                );
            }

            super.error(
                e as Error,
                param.tenantId,
                param.requestHeader.sequenceNo,
                MsgKeysConstants.APCOME0401,
                (e as Error).message,
            );
            handleCode = ResultCdConstants.CODE_999999;
            return this.returnEdit(
                param,
                handleCode,
                apiHandleId,
                receivedDate,
            );
        }
    }

    /**
     * SO管理共通パラメータ設定
     *
     * @param param パラメータ
     * @param processId API処理ID
     * @param code オーダステータス
     * @param receivedDate REST API共通処理が呼び出されたシステム日付
     * @param functionType 機能種別
     */
    private async soManagement(
        param: LineSuspendInputDto,
        processId: string,
        code: string,
        receivedDate: string,
        functionType: string,
    ): Promise<void> {
        // SO管理共通を呼び出す
        const soObject = new SOObject();
        // サービスオーダID
        soObject.setServiceOrderId(processId);
        // 投入日
        soObject.setOrderDate(receivedDate);
        // 予約日
        soObject.setReserveDate(null);
        // オーダ種別
        soObject.setOrderType(null);
        // 完了日
        soObject.setExecDate(MvnoUtil.getDateTimeNow());
        // オーダステータス
        soObject.setOrderStatus(code);
        // 回線ID
        soObject.setLineId(param.lineNo);
        // 回線グループID
        soObject.setLineGroupId(null);
        // 所属テナントID
        soObject.setTenantId(param.targetTenantId);
        // 実行ユーザID
        soObject.setExecuteUserId(null);
        // 実行テナントID
        soObject.setExecuteTenantId(null);
        // 機能種別
        soObject.setFunctionType(functionType);
        // 操作区分
        soObject.setOperationDivision(param.suspendFlag);
        // 変更前プランID
        soObject.setChangeOldplanId("");
        // 変更後プランID
        soObject.setChangeNewplanId("");
        // 回線所属変更回線ID
        soObject.setChangePlanId("");
        // 基本容量
        soObject.setBasicCapacity("");
        // オプションプランID
        soObject.setOptionPlanId("");
        // 譲渡先回線番号
        soObject.setDestinationLineNo("");
        // 譲渡データ量
        soObject.setGiftData("");
        // REST電文
        soObject.setRestMessage(null);

        await this.soCommon.soCommon(soObject);
    }

    /**
     * 利用中断／再開／サスペンド機能返却値編集
     *
     * @param param 利用中断／再開／サスペンド機能インプット
     * @param handleCode 処理コード
     * @param apiHandleId API処理ID
     * @param receivedDate REST API共通処理が呼び出されたシステム日付
     * @return LineSuspendOutputDto 利用中断／再開／サスペンド機能アウトプット
     */
    private returnEdit(
        param: LineSuspendInputDto,
        handleCode: string,
        apiHandleId: string,
        receivedDate: string,
    ): LineSuspendOutputDto {
        return {
            jsonBody: {
                responseHeader: {
                    sequenceNo: param.requestHeader.sequenceNo,
                    receivedDate,
                    processCode: handleCode,
                    apiProcessID: apiHandleId,
                },
            },
        };
    }
}
