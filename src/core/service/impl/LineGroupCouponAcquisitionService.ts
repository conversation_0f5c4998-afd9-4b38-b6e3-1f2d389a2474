import RESTCommon from "@/core/common/RESTCommon";
import { IFService } from "../IFService";
import LineGroupCouponAcquisitionInputDto from "@/core/dto/LineGroupCouponAcquisitionInputDto";
import LineGroupCouponAcquisitionOutputDto from "@/core/dto/LineGroupCouponAcquisitionOutputDto";
import MsgKeysConstants from "@/core/constant/MsgKeysConstants";
import MvnoUtil from "@/core/common/MvnoUtil";
import APICommonDAO from "@/core/dao/APICommonDAO";
import APILinesGroupDAO from "@/core/dao/ApiLinesGroupDAO";
import TenantManage from "@/core/common/TenantManage";
import ApiCommon from "@/core/common/ApiCommon";
import SOAPCommonCoupon from "@/core/common/SOAPCommonCoupon";
import SOAPCommon from "@/core/common/SOAPCommon";
import SOCommon from "@/core/common/SOCommon";
import config from "config";
import Check from "@/core/common/Check";
import ResultCdConstants from "@/core/constant/ResultCdConstants";
import ResponseHeader from "@/core/dto/ResponseHeader";
import Constants from "@/core/constant/Constants";
import { TenantsEntity } from "@/core/entity/TenantsEntity";
import { isSQLException } from "@/helpers/queryHelper";
import LineGroupsEntity from "@/core/entity/LineGroupsEntity";
import CheckUtil from "@/core/common/CheckUtil";
import TenantGroupPlansEntity from "@/core/entity/TenantGroupPlansEntity";
import LineLineGroupsEntity from "@/core/entity/LineLineGroupsEntity";
import { GroupPlansEntity } from "@/core/entity/GroupPlansEntity";
import SOObject from "@/core/dto/SOObject";
import SOAPCommonOutputDto from "@/core/dto/SOAPCommonOutputDto";
import SOAPException from "@/types/soapException";
import ParameterName from "@/core/dto/ParameterName";
import { getConfig } from "@/helpers/configHelper";
import { getStringParameter } from "@/types/parameter.string";

export default class LineGroupCouponAcquisitionService
    extends RESTCommon
    implements
        IFService<
            LineGroupCouponAcquisitionInputDto,
            LineGroupCouponAcquisitionOutputDto
        >
{
    /** 定数 「回線グループID」 */
    private static readonly CONST_LINEGROUPID = "回線グループID";

    /** 定数 「卸ポータルグループプランID」 */
    private static readonly CONST_POTALGROUPPLANID =
        "卸ポータルグループプランID";

    /** 定数 「卸ポータルオプショングループプランID」 */
    private static readonly CONST_OPTIONGROUPPLANID =
        "卸ポータルオプショングループプランID";

    /** 定数 「クーポンON/OFF状態」 */
    private static readonly CONST_COUPONONOFF = "クーポンON/OFF状態";

    /** 定数 「更新サービスパターン」 */
    private static readonly CONST_REFSERVICEPATTERN = "更新サービスパターン";

    /** 定数 「ポリシーID」 */
    private static readonly CONST_POLICYID = "ポリシーID";

    /** 定数 「回線番号」 */
    private static readonly CONST_LINENO = "回線番号";

    /** 定数 「予約日」 */
    private static readonly CONST_RESERVEDATE = "予約日";

    /** API共通DAO */

    private apiLinesGroupDao = new APILinesGroupDAO(this.request, this.context);

    private tenantManage = new TenantManage(this.request, this.context);

    private apiCommon = new ApiCommon(this.request, this.context);

    private soapCommonCoupon = new SOAPCommonCoupon(this.request, this.context);

    private soapCommon = new SOAPCommon(this.request, this.context);

    private soCommon = new SOCommon(this.request, this.context);

    /**
     * 予約実行日時単位(予約機能)
     */
    private reservationDateExecutionUnits: string = getConfig(
        "ReservationDateExecutionUnits",
    );
    /**
     * 予約可能制限日数(予約機能)
     */
    private reservationsLimitDays: string = getConfig(
        "ReservationsLimitDays",
    );

    /**
     * 回線グループクーポンオン・オフ機能。<BR>
     *
     * <PRE>
     * SOAP APIにより、受信したパラメータの回線グループIDをキーにして回線グループクーポンON/OFF状態更新処理をを行う
     * </PRE>
     *
     * @param param 回線グループクーポンオン・オフ機能インプット
     * @param isPrivate
     *            内部呼出フラグ
     * @param params
     *            可変長さ引数(内部から呼出す時に使用するパラメータ)
     * @return LineGroupCouponAcquisitionOutputDto 回線グループクーポンオン・オフ機能アウトプット
     */
    public async service(
        param: LineGroupCouponAcquisitionInputDto,
        ...params: any[]
    ): Promise<LineGroupCouponAcquisitionOutputDto> {
        const isPrivate = false;
        // デバッグログ出力
        super.debug(
            param.tenantId,
            param.requestHeader.sequenceNo,
            MsgKeysConstants.APCGPD0001,
            this.getClassName(),
            "service",
            JSON.stringify(param),
        );
        // REST API共通処理が呼び出されたシステム日付
        const receivedDate = MvnoUtil.getDateTimeNow();

        // 出力対象生成
        const responseHeader: ResponseHeader = {
            sequenceNo: param.requestHeader.sequenceNo,
            receivedDate,
            processCode: "",
            apiProcessID: "",
        };
        const outputDto: LineGroupCouponAcquisitionOutputDto = {
            jsonBody: {
                responseHeader,
            },
        };
        // 変数「エラーメッセージ」を定義する
        const errorMessage: string = null;
        // 変数「実行ユーザID」を定義する
        const executeUserId: string = null;
        // 変数「実行テナントID」を定義する
        const executeTenantId: string = null;
        // 予約日
        const reserveDate = param.reserve_date;
        // 予約フラグ
        const reserveFlg = param.reserve_flag;
        // ＳＯ－ＩＤ
        const reserveSoId = param.reserve_soId;
        // 変数「オーダ種別」初期化
        const orderType = Check.checkOrderType(
            reserveDate,
            reserveFlg,
            reserveSoId,
        );
        // 変数「処理コード」初期化
        let cheatCode = ResultCdConstants.CODE_000000;

        try {
            responseHeader.apiProcessID =
                this.context.responseHeader.apiProcessID;
            // 業務チェック
            // オーダ種別チェック
            if (Constants.ORDER_TYPE_9.equals(orderType)) {
                // ログ出力
                super.warn(
                    param.tenantId,
                    param.requestHeader.sequenceNo,
                    MsgKeysConstants.APCGPW0011,
                );
                // 返却値を設定し、返却
                responseHeader.processCode = ResultCdConstants.CODE_120104;
                outputDto.jsonBody.responseHeader = responseHeader;
                return this.returnEdit(outputDto);
            }

            const tenantId = param.tenantId;
            const lineGroupId = getStringParameter(param.lineGroupId);
            let tenantsInfo: TenantsEntity = null;
            try {
                tenantsInfo = await this.apiCommonDAO.getTenants(tenantId);
            } catch (e) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    cheatCode = ResultCdConstants.CODE_100017;
                }
                throw e;
            }
            // STEP20.0版対応 変更 END
            if (tenantsInfo === null) {
                super.warn(
                    param.tenantId,
                    param.requestHeader.sequenceNo,
                    MsgKeysConstants.APCGPW0017,
                    lineGroupId,
                    tenantId,
                );
                // SO管理共通パラメータ設定
                await this.soManagementCommon(
                    param,
                    responseHeader.apiProcessID,
                    executeUserId,
                    executeTenantId,
                    ResultCdConstants.CODE_100017,
                    receivedDate,
                    orderType,
                    reserveDate,
                    isPrivate,
                );
                // 返却値を設定し、返却
                responseHeader.processCode = ResultCdConstants.CODE_100017;
                outputDto.jsonBody.responseHeader = responseHeader;
                return this.returnEdit(outputDto);
            }

            // 回線グループIDが、回線グループ管理に存在するか否か確認する。
            let lineGroupsInfo: LineGroupsEntity = null;
            try {
                lineGroupsInfo = await this.apiLinesGroupDao.getLineGroupsInfo(
                    lineGroupId,
                );
            } catch (e) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    cheatCode = ResultCdConstants.CODE_100018;
                }
                throw e;
            }

            if (
                tenantsInfo.tenantType === null ||
                tenantsInfo.tenantType !== 1
            ) {
                if (lineGroupsInfo === null) {
                    super.warn(
                        param.tenantId,
                        param.requestHeader.sequenceNo,
                        MsgKeysConstants.APCGPW0018,
                        lineGroupId,
                        tenantId,
                    );
                    this.context.log(
                        "LineGroupCouponAcquisitionService: lineGroupsInfo is null",
                        lineGroupId,
                        tenantId,
                    );
                    // // SO管理共通パラメータ設定
                    await this.soManagementCommon(
                        param,
                        responseHeader.apiProcessID,
                        executeUserId,
                        executeTenantId,
                        ResultCdConstants.CODE_100018,
                        receivedDate,
                        orderType,
                        reserveDate,
                        isPrivate,
                    );
                    // 返却値を設定し、返却
                    responseHeader.processCode = ResultCdConstants.CODE_100018;
                    outputDto.jsonBody.responseHeader = responseHeader;
                    return this.returnEdit(outputDto);
                }
                // 回線グループ情報のテナントIDと電文のテナントIDが一致しなければエラー
                if (!tenantId.equals(lineGroupsInfo.tenantId)) {
                    super.warn(
                        param.tenantId,
                        param.requestHeader.sequenceNo,
                        MsgKeysConstants.APCGPW0018,
                        lineGroupId,
                        tenantId,
                    );
                    this.context.log(
                        "LineGroupCouponAcquisitionService: param.tenantId doesnt match with lineGroupsInfo.tenantId",
                        lineGroupsInfo,
                        tenantId,
                    );
                    // // SO管理共通パラメータ設定
                    await this.soManagementCommon(
                        param,
                        responseHeader.apiProcessID,
                        executeUserId,
                        executeTenantId,
                        ResultCdConstants.CODE_100018,
                        receivedDate,
                        orderType,
                        reserveDate,
                        isPrivate,
                    );
                    // 返却値を設定し、返却
                    responseHeader.processCode = ResultCdConstants.CODE_100018;
                    outputDto.jsonBody.responseHeader = responseHeader;
                    return this.returnEdit(outputDto);
                }
                // 回線グループ情報のステータスを判定
                if (lineGroupsInfo.status !== 1) {
                    super.warn(
                        param.tenantId,
                        param.requestHeader.sequenceNo,
                        MsgKeysConstants.APCGPW0018,
                        lineGroupId,
                        tenantId,
                    );
                    this.context.log(
                        "LineGroupCouponAcquisitionService: lineGroupsInfo.status is not equal to 1 ",
                        lineGroupsInfo,
                    );
                    // // SO管理共通パラメータ設定
                    await this.soManagementCommon(
                        param,
                        responseHeader.apiProcessID,
                        executeUserId,
                        executeTenantId,
                        ResultCdConstants.CODE_100018,
                        receivedDate,
                        orderType,
                        reserveDate,
                        isPrivate,
                    );
                    // 返却値を設定し、返却
                    responseHeader.processCode = ResultCdConstants.CODE_100018;
                    outputDto.jsonBody.responseHeader = responseHeader;
                    return this.returnEdit(outputDto);
                }
                // 回線グループ情報にプランIDが存在しない場合エラー
                if (lineGroupsInfo.planId === null) {
                    super.warn(
                        param.tenantId,
                        param.requestHeader.sequenceNo,
                        MsgKeysConstants.APCGPW0018,
                        lineGroupId,
                        tenantId,
                    );
                    this.context.log(
                        "LineGroupCouponAcquisitionService: lineGroupsInfo.planId is null",
                        lineGroupsInfo,
                        tenantId,
                    );
                    // // SO管理共通パラメータ設定
                    await this.soManagementCommon(
                        param,
                        responseHeader.apiProcessID,
                        executeUserId,
                        executeTenantId,
                        ResultCdConstants.CODE_100018,
                        receivedDate,
                        orderType,
                        reserveDate,
                        isPrivate,
                    );
                    // 返却値を設定し、返却
                    responseHeader.processCode = ResultCdConstants.CODE_100018;
                    outputDto.jsonBody.responseHeader = responseHeader;
                    return this.returnEdit(outputDto);
                }
            } else {
                // テナント種別が１(C-OCN)の場合、回線グループ情報が存在すればエラー
                if (lineGroupsInfo !== null) {
                    super.warn(
                        param.tenantId,
                        param.requestHeader.sequenceNo,
                        MsgKeysConstants.APCGPW0018,
                        lineGroupId,
                        tenantId,
                    );
                    this.context.log(
                        "LineGroupCouponAcquisitionService: lineGroupsInfo is not null",
                        lineGroupsInfo,
                        tenantId,
                    );
                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(
                        param,
                        responseHeader.apiProcessID,
                        executeUserId,
                        executeTenantId,
                        ResultCdConstants.CODE_100018,
                        receivedDate,
                        orderType,
                        reserveDate,
                        isPrivate,
                    );
                    // 返却値を設定し、返却
                    responseHeader.processCode = ResultCdConstants.CODE_100018;
                    outputDto.jsonBody.responseHeader = responseHeader;
                    return this.returnEdit(outputDto);
                }
            }

            // １ 回線グループIDフォーマットチェック
            if (!Check.checkLineGroupId(getStringParameter(param.lineGroupId))) {
                // ログ出力
                super.warn(
                    param.tenantId,
                    param.requestHeader.sequenceNo,
                    MsgKeysConstants.APCGPW0001,
                    LineGroupCouponAcquisitionService.CONST_LINEGROUPID,
                    getStringParameter(param.lineGroupId),
                );
                // SO管理共通パラメータ設定
                await this.soManagementCommon(
                    param,
                    responseHeader.apiProcessID,
                    executeUserId,
                    executeTenantId,
                    ResultCdConstants.CODE_120101,
                    receivedDate,
                    orderType,
                    reserveDate,
                    isPrivate,
                );
                // 返却値を設定し、返却
                responseHeader.processCode = ResultCdConstants.CODE_120101;
                // super.debug(param.getTenantId(), param.getRequestHeader().getSequenceNo(),MsgKeysConstants.APCGPD0002,
                //         this.getClass().getName(), "service");
                // super.conCount--;
                outputDto.jsonBody.responseHeader = responseHeader;
                return this.returnEdit(outputDto);
            }

            // ２ 卸ポータルグループプランIDフォーマットチェック
            if (!Check.checkPlanIDFmt(getStringParameter(param.potalGroupPlanID))) {
                // ログ出力
                super.warn(
                    param.tenantId,
                    param.requestHeader.sequenceNo,
                    MsgKeysConstants.APCGPW0001,
                    LineGroupCouponAcquisitionService.CONST_POTALGROUPPLANID,
                    getStringParameter(param.potalGroupPlanID),
                );
                // SO管理共通パラメータ設定
                await this.soManagementCommon(
                    param,
                    responseHeader.apiProcessID,
                    executeUserId,
                    executeTenantId,
                    ResultCdConstants.CODE_120101,
                    receivedDate,
                    orderType,
                    reserveDate,
                    isPrivate,
                );
                // 返却値を設定し、返却
                responseHeader.processCode = ResultCdConstants.CODE_120101;
                // super.debug(param.getTenantId(), param.getRequestHeader().getSequenceNo(),MsgKeysConstants.APCGPD0002,
                //         this.getClass().getName(), "service");
                // super.conCount--;
                outputDto.jsonBody.responseHeader = responseHeader;
                return this.returnEdit(outputDto);
            }

            // ３ 卸ポータルオプショングループプランIDフォーマットチェック
            if (!Check.checkPlanIDFmt(getStringParameter(param.optionGroupPlanId))) {
                // ログ出力
                super.warn(
                    param.tenantId,
                    param.requestHeader.sequenceNo,
                    MsgKeysConstants.APCGPW0001,
                    LineGroupCouponAcquisitionService.CONST_OPTIONGROUPPLANID,
                    getStringParameter(param.optionGroupPlanId),
                );
                // SO管理共通パラメータ設定
                await this.soManagementCommon(
                    param,
                    responseHeader.apiProcessID,
                    executeUserId,
                    executeTenantId,
                    ResultCdConstants.CODE_120101,
                    receivedDate,
                    orderType,
                    reserveDate,
                    isPrivate,
                );
                // 返却値を設定し、返却
                responseHeader.processCode = ResultCdConstants.CODE_120101;
                // super.debug(param.getTenantId(), param.getRequestHeader().getSequenceNo(),MsgKeysConstants.APCGPD0002,
                //         this.getClass().getName(), "service");
                // super.conCount--;
                outputDto.jsonBody.responseHeader = responseHeader;
                return this.returnEdit(outputDto);
            }

            // ４ ON/OFFフォーマットチェック
            if (!Check.checkCouponOnOff(getStringParameter(param.couponOnOff))) {
                // ログ出力
                super.warn(
                    param.tenantId,
                    param.requestHeader.sequenceNo,
                    MsgKeysConstants.APCGPW0001,
                    LineGroupCouponAcquisitionService.CONST_COUPONONOFF,
                    getStringParameter(param.couponOnOff),
                );
                // SO管理共通パラメータ設定
                await this.soManagementCommon(
                    param,
                    responseHeader.apiProcessID,
                    executeUserId,
                    executeTenantId,
                    ResultCdConstants.CODE_120101,
                    receivedDate,
                    orderType,
                    reserveDate,
                    isPrivate,
                );
                // 返却値を設定し、返却
                responseHeader.processCode = ResultCdConstants.CODE_120101;
                // super.debug(param.getTenantId(), param.getRequestHeader().getSequenceNo(),MsgKeysConstants.APCGPD0002,
                //         this.getClass().getName(), "service");
                // super.conCount--;
                outputDto.jsonBody.responseHeader = responseHeader;
                return this.returnEdit(outputDto);
            }

            const lineno = param.lineNo;
            // 回線番号フォーマットチェック
            if (!CheckUtil.checkIsNotNull(lineno)) {
                // 入力パラメータ．「回線番号」の桁数チェック
                if (
                    !(
                        CheckUtil.checkLength(lineno, 11, true) ||
                        CheckUtil.checkLength(lineno, 14, true)
                    )
                ) {
                    // ログ出力
                    super.warn(
                        param.tenantId,
                        param.requestHeader.sequenceNo,
                        MsgKeysConstants.APCGPW0001,
                        LineGroupCouponAcquisitionService.CONST_LINENO,
                        lineno,
                    );

                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(
                        param,
                        responseHeader.apiProcessID,
                        executeUserId,
                        executeTenantId,
                        ResultCdConstants.CODE_120101,
                        receivedDate,
                        orderType,
                        reserveDate,
                        isPrivate,
                    );
                    // 返却値を設定し、返却
                    responseHeader.processCode = ResultCdConstants.CODE_120101;
                    // super.debug(param.getTenantId(), param.getRequestHeader().getSequenceNo(),MsgKeysConstants.APCGPD0002,
                    //         this.getClass().getName(), "service");
                    // super.conCount--;
                    outputDto.jsonBody.responseHeader = responseHeader;
                    return this.returnEdit(outputDto);
                }
                // 入力パラメータ．「回線番号」の数字チェック
                if (!CheckUtil.checkIsNum(lineno)) {
                    // ログ出力
                    super.warn(
                        param.tenantId,
                        param.requestHeader.sequenceNo,
                        MsgKeysConstants.APCGPW0001,
                        LineGroupCouponAcquisitionService.CONST_LINENO,
                        lineno,
                    );
                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(
                        param,
                        responseHeader.apiProcessID,
                        executeUserId,
                        executeTenantId,
                        ResultCdConstants.CODE_120101,
                        receivedDate,
                        orderType,
                        reserveDate,
                        isPrivate,
                    );
                    // 返却値を設定し、返却
                    responseHeader.processCode = ResultCdConstants.CODE_120101;
                    // super.debug(param.getTenantId(), param.getRequestHeader().getSequenceNo(),MsgKeysConstants.APCGPD0002,
                    //         this.getClass().getName(), "service");
                    // super.conCount--;
                    outputDto.jsonBody.responseHeader = responseHeader;
                    return this.returnEdit(outputDto);
                }
            }

            // 予約前オーダ,予約実行オーダ
            if (
                Constants.ORDER_TYPE_1.equals(orderType) ||
                Constants.ORDER_TYPE_2.equals(orderType)
            ) {
                // ６ 予約日フォーマットチェック(予約前オーダ,予約実行オーダ)
                if (!Check.checkReserveDateFmt(reserveDate)) {
                    // ログ出力
                    super.warn(
                        param.tenantId,
                        param.requestHeader.sequenceNo,
                        MsgKeysConstants.APCGPW0001,
                        LineGroupCouponAcquisitionService.CONST_RESERVEDATE,
                        reserveDate,
                    );
                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(
                        param,
                        responseHeader.apiProcessID,
                        executeUserId,
                        executeTenantId,
                        ResultCdConstants.CODE_120101,
                        receivedDate,
                        orderType,
                        reserveDate,
                        isPrivate,
                    );
                    // 返却値を設定し、返却
                    responseHeader.processCode = ResultCdConstants.CODE_120101;
                    outputDto.jsonBody.responseHeader = responseHeader;
                    return this.returnEdit(outputDto);
                }
            }

            // 予約前オーダ
            if (Constants.ORDER_TYPE_1.equals(orderType)) {
                // ７ 予約日と投入日相関チェック(予約前オーダ)
                if (
                    !Check.checkReserveDateOrderDate(receivedDate, reserveDate)
                ) {
                    // ログ出力
                    super.warn(
                        param.tenantId,
                        param.requestHeader.sequenceNo,
                        MsgKeysConstants.APCGPW0012,
                        reserveDate,
                        receivedDate,
                    );
                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(
                        param,
                        responseHeader.apiProcessID,
                        executeUserId,
                        executeTenantId,
                        ResultCdConstants.CODE_120105,
                        receivedDate,
                        orderType,
                        reserveDate,
                        isPrivate,
                    );
                    // 返却値を設定し、返却
                    responseHeader.processCode = ResultCdConstants.CODE_120105;
                    outputDto.jsonBody.responseHeader = responseHeader;
                    return this.returnEdit(outputDto);
                }

                // ８ 予約実行日時単位フォーマットチェック
                if (
                    !Check.checkReservationDateExecutionUnitsFmt(
                        this.reservationDateExecutionUnits,
                    )
                ) {
                    // ログ出力
                    super.warn(
                        param.tenantId,
                        param.requestHeader.sequenceNo,
                        MsgKeysConstants.APCGPW0013,
                        this.reservationDateExecutionUnits,
                    );
                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(
                        param,
                        responseHeader.apiProcessID,
                        executeUserId,
                        executeTenantId,
                        ResultCdConstants.CODE_120106,
                        receivedDate,
                        orderType,
                        reserveDate,
                        isPrivate,
                    );
                    // 返却値を設定し、返却
                    responseHeader.processCode = ResultCdConstants.CODE_120106;
                    outputDto.jsonBody.responseHeader = responseHeader;
                    return this.returnEdit(outputDto);
                }

                // ９ 予約実行日時単位と予約日相関チェックチェック
                if (
                    !Check.checkReservationDateExecutionUnits(
                        this.reservationDateExecutionUnits,
                        reserveDate,
                    )
                ) {
                    // ログ出力
                    super.warn(
                        param.tenantId,
                        param.requestHeader.sequenceNo,
                        MsgKeysConstants.APCGPW0014,
                        this.reservationDateExecutionUnits,
                        reserveDate,
                    );
                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(
                        param,
                        responseHeader.apiProcessID,
                        executeUserId,
                        executeTenantId,
                        ResultCdConstants.CODE_120107,
                        receivedDate,
                        orderType,
                        reserveDate,
                        isPrivate,
                    );
                    // 返却値を設定し、返却
                    responseHeader.processCode = ResultCdConstants.CODE_120107;
                    outputDto.jsonBody.responseHeader = responseHeader;
                    return this.returnEdit(outputDto);
                }

                // １０ 予約可能制限日数フォーマットチェック
                if (
                    !Check.checkReservationsLimitDaysFmt(
                        this.reservationsLimitDays,
                    )
                ) {
                    // ログ出力
                    super.warn(
                        param.tenantId,
                        param.requestHeader.sequenceNo,
                        MsgKeysConstants.APCGPW0015,
                        this.reservationsLimitDays,
                    );
                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(
                        param,
                        responseHeader.apiProcessID,
                        executeUserId,
                        executeTenantId,
                        ResultCdConstants.CODE_120108,
                        receivedDate,
                        orderType,
                        reserveDate,
                        isPrivate,
                    );
                    // 返却値を設定し、返却
                    responseHeader.processCode = ResultCdConstants.CODE_120108;
                    outputDto.jsonBody.responseHeader = responseHeader;
                    return this.returnEdit(outputDto);
                }

                // １１ 予約可能制限日数範囲チェック
                if (
                    !Check.checkReservationsLimitDays(
                        this.reservationsLimitDays,
                        reserveDate,
                    )
                ) {
                    // ログ出力
                    super.warn(
                        param.tenantId,
                        param.requestHeader.sequenceNo,
                        MsgKeysConstants.APCGPW0016,
                        this.reservationsLimitDays,
                        reserveDate,
                    );
                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(
                        param,
                        responseHeader.apiProcessID,
                        executeUserId,
                        executeTenantId,
                        ResultCdConstants.CODE_120109,
                        receivedDate,
                        orderType,
                        reserveDate,
                        isPrivate,
                    );
                    // 返却値を設定し、返却
                    responseHeader.processCode = ResultCdConstants.CODE_120109;
                    outputDto.jsonBody.responseHeader = responseHeader;
                    return this.returnEdit(outputDto);
                }
            }

            // ５ 卸ポータルグループプランID所属チェック
            let tenantGroupPlansList: TenantGroupPlansEntity = null;
            try {
                tenantGroupPlansList =
                    await this.apiCommonDAO.getTenantGroupPlans(
                        getStringParameter(param.potalGroupPlanID),
                        param.tenantId,
                    );
            } catch (e) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    cheatCode = ResultCdConstants.CODE_120102;
                }
                throw e;
            }

            if (tenantGroupPlansList === null) {
                // ログ出力
                super.warn(
                    param.tenantId,
                    param.requestHeader.sequenceNo,
                    MsgKeysConstants.APCGPW0002,
                );
                // SO管理共通パラメータ設定
                await this.soManagementCommon(
                    param,
                    responseHeader.apiProcessID,
                    executeUserId,
                    executeTenantId,
                    ResultCdConstants.CODE_120102,
                    receivedDate,
                    orderType,
                    reserveDate,
                    isPrivate,
                );
                // 返却値を設定し、返却
                responseHeader.processCode = ResultCdConstants.CODE_120102;
                // super.debug(param.getTenantId(), param.getRequestHeader().getSequenceNo(),MsgKeysConstants.APCGPD0002,
                //         this.getClass().getName(), "service");
                // super.conCount--;
                outputDto.jsonBody.responseHeader = responseHeader;
                return this.returnEdit(outputDto);
            }

            // グループプランID利用チェック
            if (
                CheckUtil.checkIsNull(tenantsInfo.tenantType) ||
                tenantsInfo.tenantType !== 1
            ) {
                let groupUsePlan: string = null;
                try {
                    groupUsePlan =
                        await this.apiLinesGroupDao.getUsingGroupPlans(
                            getStringParameter(param.potalGroupPlanID),
                            getStringParameter(param.lineGroupId),
                        );
                } catch (e) {
                    if (isSQLException(e)) {
                        // DBアクセスリトライエラーの場合
                        cheatCode = ResultCdConstants.CODE_120110;
                    }
                    throw e;
                }

                if (CheckUtil.checkIsNotNull(groupUsePlan)) {
                    // ログ出力
                    super.warn(
                        param.tenantId,
                        param.requestHeader.sequenceNo,
                        MsgKeysConstants.APCGPW0020,
                        getStringParameter(param.potalGroupPlanID),
                    );
                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(
                        param,
                        responseHeader.apiProcessID,
                        executeUserId,
                        executeTenantId,
                        ResultCdConstants.CODE_120110,
                        receivedDate,
                        orderType,
                        reserveDate,
                        isPrivate,
                    );
                    // 返却値を設定し、返却
                    responseHeader.processCode = ResultCdConstants.CODE_120110;
                    outputDto.jsonBody.responseHeader = responseHeader;
                    return this.returnEdit(outputDto);
                }
            }

            // 卸ポータルオプショングループプランID利用チェック
            let groupPlanOptionPlan: string = null;
            try {
                groupPlanOptionPlan =
                    await this.apiCommonDAO.getCheckedGroupPlanOptionPlans(
                        getStringParameter(param.optionGroupPlanId),
                        getStringParameter(param.potalGroupPlanID),
                        "12",
                    );
            } catch (e) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    cheatCode = ResultCdConstants.CODE_120103;
                }
                throw e;
            }

            if (CheckUtil.checkIsNotNull(groupPlanOptionPlan)) {
                // ログ出力
                super.warn(
                    param.tenantId,
                    param.requestHeader.sequenceNo,
                    MsgKeysConstants.APCGPW0006,
                    getStringParameter(param.optionGroupPlanId),
                );

                // SO管理共通パラメータ設定
                await this.soManagementCommon(
                    param,
                    responseHeader.apiProcessID,
                    executeUserId,
                    executeTenantId,
                    ResultCdConstants.CODE_120103,
                    receivedDate,
                    orderType,
                    reserveDate,
                    isPrivate,
                );
                // 返却値を設定し、返却
                responseHeader.processCode = ResultCdConstants.CODE_120103;
                // super.debug(param.getTenantId(), param.getRequestHeader().getSequenceNo(),MsgKeysConstants.APCGPD0002,
                //         this.getClass().getName(), "service");
                // super.conCount--;
                outputDto.jsonBody.responseHeader = responseHeader;
                return this.returnEdit(outputDto);
            }

            // 回線の回線グループ所属判定
            if (
                (CheckUtil.checkIsNull(tenantsInfo.tenantType) ||
                    tenantsInfo.tenantType !== 1) &&
                !CheckUtil.checkIsNotNull(lineno)
            ) {
                let lineLineGroup: LineLineGroupsEntity = null;
                try {
                    lineLineGroup = await this.apiLinesGroupDao.getLinesGroupId(
                        lineno,
                    );
                } catch (e) {
                    if (isSQLException(e)) {
                        // DBアクセスリトライエラーの場合
                        cheatCode = ResultCdConstants.CODE_120111;
                    }
                    throw e;
                }

                if (
                    lineLineGroup === null ||
                    !getStringParameter(param.lineGroupId).equals(lineLineGroup.groupId)
                ) {
                    // ログ出力
                    super.warn(
                        param.tenantId,
                        param.requestHeader.sequenceNo,
                        MsgKeysConstants.APCGPW0021,
                        getStringParameter(param.lineGroupId),
                        lineno,
                    );
                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(
                        param,
                        responseHeader.apiProcessID,
                        executeUserId,
                        executeTenantId,
                        ResultCdConstants.CODE_120111,
                        receivedDate,
                        orderType,
                        reserveDate,
                        isPrivate,
                    );
                    // 返却値を設定し、返却
                    responseHeader.processCode = ResultCdConstants.CODE_120111;
                    outputDto.jsonBody.responseHeader = responseHeader;
                    return this.returnEdit(outputDto);
                }
            }

            // 対象回線の廃止オーダが受付中でないか
            if (!CheckUtil.checkIsNotNull(lineno)) {
                let targetDate: string;
                if (Constants.ORDER_TYPE_0.equals(orderType)) {
                    targetDate = receivedDate;
                } else {
                    targetDate = reserveDate;
                }

                let checkAbolishSoResult: boolean;
                try {
                    checkAbolishSoResult = await this.apiCommon.checkAbolishSo(
                        lineno,
                        targetDate,
                    );
                } catch (e) {
                    if (isSQLException(e)) {
                        // DBアクセスリトライエラーの場合
                        cheatCode = ResultCdConstants.CODE_120112;
                    }
                    throw e;
                }
                if (!checkAbolishSoResult) {
                    // ログ出力
                    super.warn(
                        param.tenantId,
                        param.requestHeader.sequenceNo,
                        MsgKeysConstants.APCGPW0019,
                        lineno,
                    );
                    // // SO管理共通パラメータ設定
                    await this.soManagementCommon(
                        param,
                        responseHeader.apiProcessID,
                        executeUserId,
                        executeTenantId,
                        ResultCdConstants.CODE_120112,
                        receivedDate,
                        orderType,
                        reserveDate,
                        isPrivate,
                    );
                    // 返却値を設定し、返却
                    responseHeader.processCode = ResultCdConstants.CODE_120112;
                    outputDto.jsonBody.responseHeader = responseHeader;
                    return this.returnEdit(outputDto);
                }
            }

            // 対象回線の利用状態がサスペンド中でないか
            if (!CheckUtil.checkIsNotNull(lineno)) {
                let checkLineUsageStatusResult: boolean;
                try {
                    checkLineUsageStatusResult =
                        await this.apiCommon.checkLineSuspend(lineno);
                } catch (e) {
                    if (isSQLException(e)) {
                        // DBアクセスリトライエラーの場合
                        cheatCode = ResultCdConstants.CODE_120113;
                    }
                    throw e;
                }

                // 変数「利用状態判定結果」より判定する
                if (!checkLineUsageStatusResult) {
                    // ログ出力
                    super.warn(
                        param.tenantId,
                        param.requestHeader.sequenceNo,
                        MsgKeysConstants.APCGPW0022,
                        lineno,
                    );
                    // // SO管理共通パラメータ設定
                    await this.soManagementCommon(
                        param,
                        responseHeader.sequenceNo,
                        executeUserId,
                        executeTenantId,
                        ResultCdConstants.CODE_120113,
                        receivedDate,
                        orderType,
                        reserveDate,
                        isPrivate,
                    );
                    // 返却値を設定し、返却
                    responseHeader.processCode = ResultCdConstants.CODE_120113;
                    outputDto.jsonBody.responseHeader = responseHeader;
                    return this.returnEdit(outputDto);
                }
            }

            // １ 更新サービスパターンとポリシーIDを取得する
            let groupPlansEntity: GroupPlansEntity = null;
            try {
                groupPlansEntity = await this.apiCommonDAO.getGroupPlans(
                    getStringParameter(param.potalGroupPlanID),
                );
            } catch (e) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    cheatCode = ResultCdConstants.CODE_120201;
                }
                throw e;
            }

            if (groupPlansEntity === null) {
                // ログ出力
                super.warn(
                    param.tenantId,
                    param.requestHeader.sequenceNo,
                    MsgKeysConstants.APCGPW0003,
                );
                // SO管理共通パラメータ設定
                await this.soManagementCommon(
                    param,
                    responseHeader.sequenceNo,
                    executeUserId,
                    executeTenantId,
                    ResultCdConstants.CODE_120101,
                    receivedDate,
                    orderType,
                    reserveDate,
                    isPrivate,
                );
                // 返却値を設定し、返却
                responseHeader.processCode = ResultCdConstants.CODE_120201;
                // super.debug(param.getTenantId(), param.getRequestHeader().getSequenceNo(),MsgKeysConstants.APCGPD0002,
                //         this.getClass().getName(), "service");
                // super.conCount--;
                outputDto.jsonBody.responseHeader = responseHeader;
                return this.returnEdit(outputDto);
            }

            // ２ 更新サービスパターンとポリシーIDのフォーマットチェック
            // ２．１ 更新サービスパターンのフォーマットチェック
            /** １．１版対応 変更 START */

            const tpc = groupPlansEntity.tpcServicePattern;
            if (
                !CheckUtil.checkIsNum(tpc.toString()) ||
                (CheckUtil.checkIsNotNull(lineno) &&
                    tpc !== 1 &&
                    tpc !== 2 &&
                    tpc !== 4) ||
                (!CheckUtil.checkIsNotNull(lineno) &&
                    tpc !== 1 &&
                    tpc !== 2 &&
                    tpc !== 4 &&
                    tpc !== 6)
            ) {
                // ログ出力
                super.warn(
                    param.tenantId,
                    param.requestHeader.sequenceNo,
                    MsgKeysConstants.APCGPW0005,
                    LineGroupCouponAcquisitionService.CONST_REFSERVICEPATTERN,
                    groupPlansEntity.tpcServicePattern,
                );

                // SO管理共通パラメータ設定
                await this.soManagementCommon(
                    param,
                    responseHeader.sequenceNo,
                    executeUserId,
                    executeTenantId,
                    ResultCdConstants.CODE_120202,
                    receivedDate,
                    orderType,
                    reserveDate,
                    isPrivate,
                );
                // 返却値を設定し、返却
                responseHeader.processCode = ResultCdConstants.CODE_120202;
                // super.debug(param.getTenantId(), param.getRequestHeader().getSequenceNo(),MsgKeysConstants.APCGPD0002,
                //         this.getClass().getName(), "service");
                // super.conCount--;
                outputDto.jsonBody.responseHeader = responseHeader;
                return this.returnEdit(outputDto);
            }

            // ２．２ ポリシーIDのフォーマットチェック
            if (!Check.checkPolicyId(groupPlansEntity.policyId.toString())) {
                // ログ出力
                super.warn(
                    param.tenantId,
                    param.requestHeader.sequenceNo,
                    MsgKeysConstants.APCGPW0005,
                    LineGroupCouponAcquisitionService.CONST_POLICYID,
                    groupPlansEntity.policyId,
                );
                // SO管理共通パラメータ設定
                await this.soManagementCommon(
                    param,
                    responseHeader.sequenceNo,
                    executeUserId,
                    executeTenantId,
                    ResultCdConstants.CODE_120202,
                    receivedDate,
                    orderType,
                    reserveDate,
                    isPrivate,
                );
                // 返却値を設定し、返却
                responseHeader.processCode = ResultCdConstants.CODE_120202;
                // super.debug(param.getTenantId(), param.getRequestHeader().getSequenceNo(),MsgKeysConstants.APCGPD0002,
                //         this.getClass().getName(), "service");
                // super.conCount--;
                outputDto.jsonBody.responseHeader = responseHeader;
                return this.returnEdit(outputDto);
            }

            // 入力パラメータ．「回線番号」はnull、""ではない場合 処理３へ
            // 変数「オーダ種別」=予約前オーダ  処理４へ
            if (
                Constants.ORDER_TYPE_0.equals(orderType) ||
                Constants.ORDER_TYPE_2.equals(orderType)
            ) {
                // ３ SO電文作成した、SOAPのAPIを接続する
                let soapCommonOutputDto: SOAPCommonOutputDto = null;
                let tpcConnectionResults: [boolean, string, string] = null;
                try {
                    tpcConnectionResults =
                        await this.tenantManage.checkTpcConnection(tenantId);
                } catch (e) {
                    if (isSQLException(e)) {
                        // DBアクセスリトライエラーの場合
                        cheatCode = ResultCdConstants.CODE_120301;
                    }
                    throw e;
                }

                // 変数「TPC情報取得結果」より判断する
                if (!tpcConnectionResults[0]) {
                    // ログ出力
                    super.warn(
                        param.tenantId,
                        param.requestHeader.sequenceNo,
                        MsgKeysConstants.APCGPW0007,
                        tenantId,
                    );
                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(
                        param,
                        responseHeader.apiProcessID,
                        executeUserId,
                        executeTenantId,
                        ResultCdConstants.CODE_120301,
                        receivedDate,
                        orderType,
                        reserveDate,
                        isPrivate,
                    );
                    // 返却値を設定し、返却
                    responseHeader.processCode = ResultCdConstants.CODE_120301;
                    outputDto.jsonBody.responseHeader = responseHeader;
                    return this.returnEdit(outputDto);
                }

                let couponBefore: string = "";
                let couponAfter: string = "";

                /** １．１版対応 変更 START */
                if (
                    CheckUtil.checkIsNotNull(lineno) &&
                    (groupPlansEntity.tpcServicePattern === 1 ||
                        groupPlansEntity.tpcServicePattern === 2)
                ) {
                    // 回線番が指定されていない場合
                    if (parseInt(getStringParameter(param.couponOnOff), 10) === 0) {
                        // 入力パラメータ．「クーポンON/OFF状態」＝0の場合
                        couponBefore = "0";
                        couponAfter = "1";
                    } else if (parseInt(getStringParameter(param.couponOnOff), 10) === 1) {
                        // 入力パラメータ．「クーポンON/OFF状態」＝1の場合
                        couponBefore = "1";
                        couponAfter = "0";
                    }

                    // ３．１．１．１ SOAP APIにより、サービスプロファイル番号に対するデータを更新する
                    try {
                        soapCommonOutputDto =
                            await this.sendSoapApiToTpcGroupId(
                                getStringParameter(param.lineGroupId),
                                couponBefore,
                                couponAfter,
                                param.tenantId,
                                param.requestHeader.sequenceNo,
                                tpcConnectionResults[1],
                                true,
                            );
                    } catch (e) {
                        if (SOAPException.isSOAPException(e)) {
                            // ログ出力
                            super.warn(
                                param.tenantId,
                                param.requestHeader.sequenceNo,
                                MsgKeysConstants.APCGPW0004,
                            );
                            // SO管理共通パラメータ設定
                            await this.soManagementCommon(
                                param,
                                responseHeader.apiProcessID,
                                executeUserId,
                                executeTenantId,
                                ResultCdConstants.CODE_120401,
                                receivedDate,
                                orderType,
                                reserveDate,
                                isPrivate,
                            );
                            responseHeader.processCode =
                                ResultCdConstants.CODE_120401;
                            super.debug(
                                param.tenantId,
                                param.requestHeader.sequenceNo,
                                MsgKeysConstants.APCGPD0002,
                                this.getClassName(),
                                "service",
                            );
                            outputDto.jsonBody.responseHeader = responseHeader;
                            return this.returnEdit(outputDto);
                        } else {
                            throw e;
                        }
                    }

                    if (
                        !ResultCdConstants.CODE_000000.equals(
                            soapCommonOutputDto.getProcessCode(),
                        )
                    ) {
                        // ログ出力
                        super.warn(
                            param.tenantId,
                            param.requestHeader.sequenceNo,
                            MsgKeysConstants.APCGPW0004,
                        );
                        // SO管理共通パラメータ設定
                        await this.soManagementCommon(
                            param,
                            responseHeader.apiProcessID,
                            executeUserId,
                            executeTenantId,
                            ResultCdConstants.CODE_120401,
                            receivedDate,
                            orderType,
                            reserveDate,
                            isPrivate,
                        );
                        // 返却値を設定し、返却
                        responseHeader.processCode =
                            ResultCdConstants.CODE_120401;
                        outputDto.jsonBody.responseHeader = responseHeader;
                        return this.returnEdit(outputDto);
                    }

                    // セカンダリTPCへSOAP連携を行う
                    if (tpcConnectionResults[2] !== null) {
                        // セカンダリTPCが設定されている場合に連携
                        try {
                            soapCommonOutputDto =
                                await this.sendSoapApiToTpcGroupId(
                                    getStringParameter(param.lineGroupId),
                                    couponBefore,
                                    couponAfter,
                                    param.tenantId,
                                    param.requestHeader.sequenceNo,
                                    tpcConnectionResults[2],
                                    false,
                                );

                            if (
                                !ResultCdConstants.CODE_000000.equals(
                                    soapCommonOutputDto.getProcessCode(),
                                )
                            ) {
                                let errInfo: string = "";
                                if (
                                    ResultCdConstants.CODE_000951.equals(
                                        soapCommonOutputDto.getProcessCode(),
                                    )
                                ) {
                                    // SG取得でNGの場合
                                    errInfo = "SG取得NG";
                                } else {
                                    errInfo = this.soapCommon.getNodeContent(
                                        soapCommonOutputDto.getDoc(),
                                        "//ErrorInfomation",
                                    );
                                }

                                // ログ出力
                                super.warn(
                                    param.tenantId,
                                    param.requestHeader.sequenceNo,
                                    MsgKeysConstants.APCGPW0023,
                                    errInfo,
                                );
                            }
                        } catch (e) {
                            if (SOAPException.isSOAPException(e)) {
                                // ログ出力
                                super.warn(
                                    e,
                                    param.tenantId,
                                    param.requestHeader.sequenceNo,
                                    MsgKeysConstants.APCGPW0023,
                                    e.toString(),
                                );
                            } else {
                                throw e;
                            }
                        }
                    }
                }

                // ３．２ 変数「更新サービスパターン」＝4の場合、下記処理を行う
                /** １．１版対応 変更 END */
                if (
                    CheckUtil.checkIsNotNull(lineno) &&
                    groupPlansEntity.tpcServicePattern === 4
                ) {
                    // 回線番が指定されていない場合
                    if (parseInt(getStringParameter(param.couponOnOff), 0) === 0) {
                        // 入力パラメータ．「クーポンON/OFF状態」＝0の場合
                        couponAfter = "0";
                    } else if (parseInt(getStringParameter(param.couponOnOff), 0) === 1) {
                        // 入力パラメータ．「クーポンON/OFF状態」＝1の場合
                        couponAfter = "1";
                    }

                    const paramArray5: string[][] = [
                        ["group_number", getStringParameter(param.lineGroupId)],
                        ["policy_number", groupPlansEntity.policyId.toString()],
                        ["aply_srv", couponAfter],
                    ];

                    try {
                        // SOAP共通を呼び出す
                        soapCommonOutputDto = await this.getSoapResult(
                            "serviceProfileRequestLite",
                            "Mod",
                            "",
                            paramArray5,
                            param.tenantId,
                            param.requestHeader.sequenceNo,
                            tpcConnectionResults[1],
                            true,
                        );
                    } catch (e) {
                        if (SOAPException.isSOAPException(e)) {
                            // ログ出力
                            super.warn(
                                param.tenantId,
                                param.requestHeader.sequenceNo,
                                MsgKeysConstants.APCGPW0004,
                            );
                            // SO管理共通パラメータ設定
                            await this.soManagementCommon(
                                param,
                                responseHeader.apiProcessID,
                                executeUserId,
                                executeTenantId,
                                ResultCdConstants.CODE_120401,
                                receivedDate,
                                orderType,
                                reserveDate,
                                isPrivate,
                            );
                            responseHeader.processCode =
                                ResultCdConstants.CODE_120401;
                            super.debug(
                                param.tenantId,
                                param.requestHeader.sequenceNo,
                                MsgKeysConstants.APCGPD0002,
                                this.getClassName(),
                                "service",
                            );
                            outputDto.jsonBody.responseHeader = responseHeader;
                            return this.returnEdit(outputDto);
                        } else {
                            throw e;
                        }
                    }

                    if (
                        !ResultCdConstants.CODE_000000.equals(
                            soapCommonOutputDto.getProcessCode(),
                        )
                    ) {
                        // ログ出力
                        super.warn(
                            param.tenantId,
                            param.requestHeader.sequenceNo,
                            MsgKeysConstants.APCGPW0004,
                        );
                        // SO管理共通パラメータ設定
                        await this.soManagementCommon(
                            param,
                            responseHeader.apiProcessID,
                            executeUserId,
                            executeTenantId,
                            ResultCdConstants.CODE_120401,
                            receivedDate,
                            orderType,
                            reserveDate,
                            isPrivate,
                        );
                        // 返却値を設定し、返却
                        responseHeader.processCode =
                            ResultCdConstants.CODE_120401;
                        outputDto.jsonBody.responseHeader = responseHeader;
                        return this.returnEdit(outputDto);
                    }
                    // セカンダリTPCへSOAP連携を行う
                    if (tpcConnectionResults[2] !== null) {
                        // セカンダリTPCが設定されている場合に連携
                        try {
                            soapCommonOutputDto = await this.getSoapResult(
                                "serviceProfileRequestLite",
                                "Mod",
                                "",
                                paramArray5,
                                param.tenantId,
                                param.requestHeader.sequenceNo,
                                tpcConnectionResults[2],
                                false,
                            );
                            if (
                                !ResultCdConstants.CODE_000000.equals(
                                    soapCommonOutputDto.getProcessCode(),
                                )
                            ) {
                                let errInfo: string = "";
                                if (
                                    ResultCdConstants.CODE_000951.equals(
                                        soapCommonOutputDto.getProcessCode(),
                                    )
                                ) {
                                    // SG取得でNGの場合
                                    errInfo = "SG取得NG";
                                } else {
                                    errInfo = this.soapCommon.getNodeContent(
                                        soapCommonOutputDto.getDoc(),
                                        "//ErrorInfomation",
                                    );
                                }
                                // ログ出力
                                super.warn(
                                    param.tenantId,
                                    param.requestHeader.sequenceNo,
                                    MsgKeysConstants.APCGPW0023,
                                    errInfo,
                                );
                            }
                        } catch (e) {
                            if (SOAPException.isSOAPException(e)) {
                                // ログ出力
                                super.warn(
                                    e,
                                    param.tenantId,
                                    param.requestHeader.sequenceNo,
                                    MsgKeysConstants.APCGPW0023,
                                    e.toString(),
                                );
                            } else {
                                throw e;
                            }
                        }
                    }
                }

                /** １．１版対応 追加 START */
                // ３．３ 変数「更新サービスパターン」＝1または2の場合、下記処理を行う
                if (
                    !CheckUtil.checkIsNotNull(lineno) &&
                    (tpc === 1 || tpc === 2)
                ) {
                    // 回線番が指定されている場合
                    if (parseInt(getStringParameter(param.couponOnOff), 0) === 0) {
                        // 入力パラメータ．「クーポンON/OFF状態」＝0の場合
                        couponBefore = "0";
                        couponAfter = "1";
                    } else if (parseInt(getStringParameter(param.couponOnOff), 0) === 1) {
                        // 入力パラメータ．「クーポンON/OFF状態」＝1の場合
                        couponBefore = "1";
                        couponAfter = "0";
                    }

                    try {
                        // SOAP共通を呼び出す
                        soapCommonOutputDto = await this.sendSoapApiToTpcLineNo(
                            lineno,
                            couponBefore,
                            couponAfter,
                            param.tenantId,
                            param.requestHeader.sequenceNo,
                            tpcConnectionResults[1],
                            true,
                        );
                    } catch (e) {
                        if (SOAPException.isSOAPException(e)) {
                            // ログ出力
                            super.warn(
                                param.tenantId,
                                param.requestHeader.sequenceNo,
                                MsgKeysConstants.APCGPW0004,
                            );
                            // SO管理共通パラメータ設定
                            await this.soManagementCommon(
                                param,
                                responseHeader.apiProcessID,
                                executeUserId,
                                executeTenantId,
                                ResultCdConstants.CODE_120401,
                                receivedDate,
                                orderType,
                                reserveDate,
                                isPrivate,
                            );
                            responseHeader.processCode =
                                ResultCdConstants.CODE_120401;
                            super.debug(
                                param.tenantId,
                                param.requestHeader.sequenceNo,
                                MsgKeysConstants.APCGPD0002,
                                this.getClassName(),
                                "service",
                            );
                            outputDto.jsonBody.responseHeader = responseHeader;
                            return this.returnEdit(outputDto);
                        } else {
                            throw e;
                        }
                    }

                    if (
                        !ResultCdConstants.CODE_000000.equals(
                            soapCommonOutputDto.getProcessCode(),
                        )
                    ) {
                        // ログ出力
                        super.warn(
                            param.tenantId,
                            param.requestHeader.sequenceNo,
                            MsgKeysConstants.APCGPW0004,
                        );
                        // SO管理共通パラメータ設定
                        await this.soManagementCommon(
                            param,
                            responseHeader.apiProcessID,
                            executeUserId,
                            executeTenantId,
                            ResultCdConstants.CODE_120401,
                            receivedDate,
                            orderType,
                            reserveDate,
                            isPrivate,
                        );
                        // 返却値を設定し、返却
                        responseHeader.processCode =
                            ResultCdConstants.CODE_120401;
                        outputDto.jsonBody.responseHeader = responseHeader;
                        return this.returnEdit(outputDto);
                    }

                    // セカンダリTPCへSOAP連携を行う
                    if (tpcConnectionResults[2] !== null) {
                        // セカンダリTPCが設定されている場合に連携
                        try {
                            soapCommonOutputDto =
                                await this.sendSoapApiToTpcLineNo(
                                    lineno,
                                    couponBefore,
                                    couponAfter,
                                    param.tenantId,
                                    param.requestHeader.sequenceNo,
                                    tpcConnectionResults[2],
                                    false,
                                );

                            if (
                                !ResultCdConstants.CODE_000000.equals(
                                    soapCommonOutputDto.getProcessCode(),
                                )
                            ) {
                                let errInfo: string = "";

                                if (
                                    ResultCdConstants.CODE_000951.equals(
                                        soapCommonOutputDto.getProcessCode(),
                                    )
                                ) {
                                    // SG取得でNGの場合
                                    errInfo = "SG取得NG";
                                } else {
                                    errInfo = this.soapCommon.getNodeContent(
                                        soapCommonOutputDto.getDoc(),
                                        "//ErrorInfomation",
                                    );
                                }

                                // ログ出力
                                super.warn(
                                    param.tenantId,
                                    param.requestHeader.sequenceNo,
                                    MsgKeysConstants.APCGPW0023,
                                    errInfo,
                                );
                            }
                        } catch (e) {
                            if (SOAPException.isSOAPException(e)) {
                                // ログ出力
                                super.warn(
                                    e,
                                    param.tenantId,
                                    param.requestHeader.sequenceNo,
                                    MsgKeysConstants.APCGPW0023,
                                    e.toString(),
                                );
                            } else {
                                throw e;
                            }
                        }
                    }
                }

                // ３．４ 変数「更新サービスパターン」＝4または6の場合、下記処理を行う
                if (
                    !CheckUtil.checkIsNotNull(lineno) &&
                    (tpc === 4 || tpc === 6)
                ) {
                    // 回線番が指定されている場合
                    if (parseInt(getStringParameter(param.couponOnOff), 0) === 0) {
                        // 入力パラメータ．「クーポンON/OFF状態」＝0の場合
                        couponAfter = "0";
                    } else if (parseInt(getStringParameter(param.couponOnOff), 0) === 1) {
                        // 入力パラメータ．「クーポンON/OFF状態」＝1の場合
                        couponAfter = "1";
                    }

                    // ３．２．１．１ SOAP APIにより、データを更新する
                    const paramArray5: string[][] = [
                        ["phone_number", lineno],
                        ["policy_number", groupPlansEntity.policyId.toString()],
                        ["aply_srv", couponAfter],
                    ];

                    try {
                        // SOAP共通を呼び出す
                        soapCommonOutputDto = await this.getSoapResult(
                            "serviceProfileRequestLite",
                            "Mod",
                            "",
                            paramArray5,
                            param.tenantId,
                            param.requestHeader.sequenceNo,
                            tpcConnectionResults[1],
                            true,
                        );
                    } catch (e) {
                        if (SOAPException.isSOAPException(e)) {
                            // ログ出力
                            super.warn(
                                param.tenantId,
                                param.requestHeader.sequenceNo,
                                MsgKeysConstants.APCGPW0004,
                            );
                            // SO管理共通パラメータ設定
                            await this.soManagementCommon(
                                param,
                                responseHeader.apiProcessID,
                                executeUserId,
                                executeTenantId,
                                ResultCdConstants.CODE_120401,
                                receivedDate,
                                orderType,
                                reserveDate,
                                isPrivate,
                            );
                            responseHeader.processCode =
                                ResultCdConstants.CODE_120401;
                            super.debug(
                                param.tenantId,
                                param.requestHeader.sequenceNo,
                                MsgKeysConstants.APCGPD0002,
                                this.getClassName(),
                                "service",
                            );
                            outputDto.jsonBody.responseHeader = responseHeader;
                            return this.returnEdit(outputDto);
                        } else {
                            throw e;
                        }
                    }

                    if (
                        !ResultCdConstants.CODE_000000.equals(
                            soapCommonOutputDto.getProcessCode(),
                        )
                    ) {
                        // ログ出力
                        super.warn(
                            param.tenantId,
                            param.requestHeader.sequenceNo,
                            MsgKeysConstants.APCGPW0004,
                        );

                        // SO管理共通パラメータ設定
                        await this.soManagementCommon(
                            param,
                            responseHeader.apiProcessID,
                            executeUserId,
                            executeTenantId,
                            ResultCdConstants.CODE_120401,
                            receivedDate,
                            orderType,
                            reserveDate,
                            isPrivate,
                        );
                        // 返却値を設定し、返却
                        responseHeader.processCode =
                            ResultCdConstants.CODE_120401;
                        outputDto.jsonBody.responseHeader = responseHeader;
                        return this.returnEdit(outputDto);
                    }

                    // セカンダリTPCへSOAP連携を行う
                    if (tpcConnectionResults[2] !== null) {
                        // セカンダリTPCが設定されている場合に連携
                        try {
                            soapCommonOutputDto = await this.getSoapResult(
                                "serviceProfileRequestLite",
                                "Mod",
                                "",
                                paramArray5,
                                param.tenantId,
                                param.requestHeader.sequenceNo,
                                tpcConnectionResults[2],
                                false,
                            );

                            if (
                                !ResultCdConstants.CODE_000000.equals(
                                    soapCommonOutputDto.getProcessCode(),
                                )
                            ) {
                                let errInfo: string = "";
                                if (
                                    ResultCdConstants.CODE_000951.equals(
                                        soapCommonOutputDto.getProcessCode(),
                                    )
                                ) {
                                    // SG取得でNGの場合
                                    errInfo = "SG取得NG";
                                } else {
                                    errInfo = this.soapCommon.getNodeContent(
                                        soapCommonOutputDto.getDoc(),
                                        "//ErrorInfomation",
                                    );
                                }

                                // ログ出力
                                super.warn(
                                    param.tenantId,
                                    param.requestHeader.sequenceNo,
                                    MsgKeysConstants.APCGPW0023,
                                    errInfo,
                                );
                            }
                        } catch (e) {
                            if (SOAPException.isSOAPException(e)) {
                                // ログ出力
                                super.warn(
                                    e,
                                    param.tenantId,
                                    param.requestHeader.sequenceNo,
                                    MsgKeysConstants.APCGPW0023,
                                    e.toString(),
                                );
                            } else {
                                throw e;
                            }
                        }
                    }
                }
            }
        } catch (e) {
            if (isSQLException(e)) {
                // DBアクセスリトライエラーの場合
                // リターンコード値は各エラー時にて設定済み
                super.error(
                    e,
                    param.tenantId,
                    param.requestHeader.sequenceNo,
                    MsgKeysConstants.APCOME0402,
                    param.requestHeader.functionType,
                );
                // SO管理共通パラメータ設定
                await this.soManagementCommon(
                    param,
                    responseHeader.sequenceNo,
                    executeUserId,
                    executeTenantId,
                    cheatCode,
                    receivedDate,
                    orderType,
                    reserveDate,
                    isPrivate,
                );
                // responseHeader.setProcessCode(cheatCode);
                responseHeader.processCode = cheatCode;
                outputDto.jsonBody.responseHeader = responseHeader;
                return this.returnEdit(outputDto);
            } else if (SOAPException.isSOAPException(e)) {
                // SO管理共通パラメータ設定
                await this.soManagementCommon(
                    param,
                    responseHeader.apiProcessID,
                    executeUserId,
                    executeTenantId,
                    ResultCdConstants.CODE_120401,
                    receivedDate,
                    orderType,
                    reserveDate,
                    isPrivate,
                );
                responseHeader.processCode = ResultCdConstants.CODE_120401;
                super.debug(
                    param.tenantId,
                    param.requestHeader.sequenceNo,
                    MsgKeysConstants.APCGPD0002,
                    this.getClassName(),
                    "service",
                );
                // super.debug(param.getTenantId(), param.getRequestHeader().getSequenceNo(),MsgKeysConstants.APCGPD0002,
                //         this.getClass().getName(), "service");
                // super.conCount--;
                outputDto.jsonBody.responseHeader = responseHeader;
                return this.returnEdit(outputDto);
            } else {
                // SO管理共通パラメータ設定
                await this.soManagementCommon(
                    param,
                    responseHeader.apiProcessID,
                    executeUserId,
                    executeTenantId,
                    ResultCdConstants.CODE_999999,
                    receivedDate,
                    orderType,
                    reserveDate,
                    isPrivate,
                );
                responseHeader.processCode = ResultCdConstants.CODE_999999;
                super.error(
                    e,
                    param.tenantId,
                    param.requestHeader.sequenceNo,
                    MsgKeysConstants.APCOME0401,
                );
                super.debug(
                    param.tenantId,
                    param.requestHeader.sequenceNo,
                    MsgKeysConstants.APCGPD0002,
                    this.getClassName(),
                    "service",
                );
                // super.debug(param.getTenantId(), param.getRequestHeader().getSequenceNo(),MsgKeysConstants.APCGPD0002,
                //         this.getClass().getName(), "service");
                // super.conCount--;
                outputDto.jsonBody.responseHeader = responseHeader;
                return this.returnEdit(outputDto);
            }
        } finally {
            super.debug(
                param.tenantId,
                param.requestHeader.sequenceNo,
                MsgKeysConstants.APCGPD0002,
                this.getClassName(),
                "service",
            );
        }

        // SO管理共通パラメータ設定
        await this.soManagementCommon(
            param,
            responseHeader.apiProcessID,
            executeUserId,
            executeTenantId,
            ResultCdConstants.CODE_000000,
            receivedDate,
            orderType,
            reserveDate,
            isPrivate,
        );

        // 返却値を設定し、返却
        responseHeader.processCode = ResultCdConstants.CODE_000000;
        outputDto.jsonBody.responseHeader = responseHeader;

        // super.debug(param.getTenantId(), param.getRequestHeader().getSequenceNo(),MsgKeysConstants.APCGPD0002,
        //         this.getClass().getName(), "service");
        // 予約実行の場合は、減算処理は行わない。
        // super.conCount--;
        outputDto.jsonBody.responseHeader = responseHeader;
        return this.returnEdit(outputDto);
    }

    /**
     *  回線グループクーポンオン・オフ機能返却値編集
     *
     * @param outputDto
     *             回線グループクーポンオン・オフ機能OutputDto
     * @param isPrivate
     *            変数 「内部呼出フラグ」
     * @param errorMessage
     *            変数 「エラーメッセージ」
     * @return  回線グループクーポンオン・オフ機能OutputDto
     */
    public returnEdit(outputDto: LineGroupCouponAcquisitionOutputDto) {
        return outputDto;
    }

    /**
     * SO管理共通パラメータ設定
     *
     * @param param
     * @param apiProcessID // NOTE changed from: resultBean チェック結果 (`resultBean.getOthers()`)
     * @param executeUserId
     * @param executeTenantId
     * @param receivedDate REST API共通処理が呼び出されたシステム日付
     * @param orderType
     * @param reserveDate
     * @param isPrivate
     * @throws Exception
     */
    public async soManagementCommon(
        param: LineGroupCouponAcquisitionInputDto,
        apiProcessID: string,
        executeUserId: string,
        executeTenantId: string,
        code: string,
        receivedDate: string,
        orderType: string,
        reserveDate: string,
        isPrivate: boolean,
    ) {
        // SO管理共通を呼び出す
        const soObject = new SOObject();

        // サービスオーダID
        soObject.setServiceOrderId(apiProcessID);
        // 申込日
        soObject.setOrderDate(receivedDate);
        // 予約日
        // 変数「オーダ種別」は「予約前オーダ」の場合
        if (Constants.ORDER_TYPE_1.equals(orderType)) {
            soObject.setReserveDate(reserveDate);
        } else {
            soObject.setReserveDate(null);
        }
        // オーダ種別
        soObject.setOrderType(orderType);
        // 完了日
        // 変数「オーダ種別」は「即時オーダ」の場合
        if (
            Constants.ORDER_TYPE_0.equals(orderType) ||
            Constants.ORDER_TYPE_2.equals(orderType)
        ) {
            soObject.setExecDate(MvnoUtil.getDateTimeNow());
        } else {
            soObject.setExecDate(null);
        }
        // オーダステータス
        soObject.setOrderStatus(code);
        // 回線ID
        soObject.setLineId(param.lineNo);
        // 回線グループID
        soObject.setLineGroupId(getStringParameter(param.lineGroupId));
        // 所属テナントID
        soObject.setTenantId(param.tenantId);
        // 実行ユーザID
        soObject.setExecuteUserId(executeUserId);
        // 実行テナントID
        soObject.setExecuteTenantId(executeTenantId);
        // 機能種別
        soObject.setFunctionType(param.requestHeader.functionType);
        // 操作区分
        soObject.setOperationDivision(getStringParameter(param.couponOnOff));
        // 変更前プランID
        soObject.setChangeOldplanId("");
        // 変更後プランID
        soObject.setChangeNewplanId("");
        // 回線所属変更回線ID
        soObject.setChangePlanId("");
        // 基本容量
        soObject.setBasicCapacity("");
        // オプションプランID
        soObject.setOptionPlanId(getStringParameter(param.optionGroupPlanId));
        // 内部呼び出し
        soObject.setInternalFlag(isPrivate);
        // REST電文
        // 変数「オーダ種別」は「予約前オーダ」の場合
        if (Constants.ORDER_TYPE_1.equals(orderType)) {
            soObject.setRestMessage(MvnoUtil.getRestMessage(param));
        } else {
            soObject.setRestMessage(null);
        }
        // 変数「オーダ種別」が「予約前オーダ」もしくは「予約実行オーダ」の場合
        if (
            Constants.ORDER_TYPE_1.equals(orderType) ||
            Constants.ORDER_TYPE_2.equals(orderType)
        ) {
            await this.soCommon.soCommon(soObject);
        }

        return;
    }

    /**
     * SOAP API送信(回線番号指定あり)
     *
     * @param lineNo 回線ID
     * @param beforeStatus 更新前ステータス
     * @param afterStatus 更新後ステータス
     * @param tenantId テナントID
     * @param sequenceNo シーケンスNo
     * @param tpcDestInfo TPC情報
     * @param primaryFlg プライマリフラグ
     * @return SOAPCommonOutputDto SOAP応答電文情報
     * @throws Exception
     */
    private async sendSoapApiToTpcLineNo(
        lineNo: string,
        beforeStatus: string,
        afterStatus: string,
        tenantId: string,
        sequenceNo: string,
        tpcDestInfo: string,
        primaryFlg: boolean,
    ): Promise<SOAPCommonOutputDto> {
        const paramNameList1: ParameterName[] = [];
        const param1 = new ParameterName();
        param1.setName("pronum");
        param1.setValue("101" + MvnoUtil.addZeroBeforeStr(lineNo, 15));
        const param2 = new ParameterName();
        param2.setName("aply_srv");
        param2.setValue(beforeStatus);
        paramNameList1.push(param1);
        paramNameList1.push(param2);

        const paramNameList2: ParameterName[] = [];
        const param3 = new ParameterName();
        param3.setName("pronum");
        param3.setValue("102" + MvnoUtil.addZeroBeforeStr(lineNo, 15));
        const param4 = new ParameterName();
        param4.setName("aply_srv");
        param4.setValue(afterStatus);
        paramNameList2.push(param3);
        paramNameList2.push(param4);

        // SOAP-API 電文送受信
        return await this.soapCommonCoupon.callWebSoapApi(
            "serviceProfileRequest",
            "Mod",
            "Service",
            paramNameList1,
            "Mod",
            "Service",
            paramNameList2,
            tenantId,
            sequenceNo,
            tpcDestInfo,
            primaryFlg,
        );
    }

    /**
     * SOAP API送信(回線番号指定なし)
     *
     * @param lineGroupId グループID
     * @param beforeStatus 更新前ステータス
     * @param afterStatus 更新後ステータス
     * @param tenantId テナントID
     * @param sequenceNo シーケンスNo
     * @param tpcDestInfo TPC情報
     * @param primaryFlg プライマリフラグ
     * @return SOAPCommonOutputDto SOAP応答電文情報
     * @throws Exception
     */
    private async sendSoapApiToTpcGroupId(
        lineGroupId: string,
        beforeStatus: string,
        afterStatus: string,
        tenantId: string,
        sequenceNo: string,
        tpcDestInfo: string,
        primaryFlg: boolean,
    ): Promise<SOAPCommonOutputDto> {
        const paramNameList1: ParameterName[] = [];
        const param1 = new ParameterName();
        param1.setName("pronum");
        param1.setValue(
            "101000000" + MvnoUtil.addZeroBeforeStr(lineGroupId, 9),
        );
        paramNameList1.push(param1);
        const param2 = new ParameterName();
        param2.setName("aply_srv");
        param2.setValue(beforeStatus);
        paramNameList1.push(param2);

        const paramNameList2: ParameterName[] = [];
        const param3 = new ParameterName();
        param3.setName("pronum");
        param3.setValue(
            "102000000" + MvnoUtil.addZeroBeforeStr(lineGroupId, 9),
        );
        paramNameList2.push(param3);
        const param4 = new ParameterName();
        param4.setName("aply_srv");
        param4.setValue(afterStatus);
        paramNameList2.push(param4);

        // SOAP共通を呼び出す
        return await this.soapCommonCoupon.callWebSoapApi(
            "serviceProfileRequest",
            "Mod",
            "Service",
            paramNameList1,
            "Mod",
            "Service",
            paramNameList2,
            tenantId,
            sequenceNo,
            tpcDestInfo,
            primaryFlg,
        );
    }
    /**
     * SOAP共通実行結果を取得する。
     *
     * @param operationName
     * @param operationTypeName
     * @param profileName
     * @param parameterNameArray
     * @param tenantId
     * @param sequenceNo
     * @param tpcInfo
     * @param primaryFlg
     * @return SOAPCommonOutputDto
     * @throws Exception
     */
    private async getSoapResult(
        operationName: string,
        operationTypeName: string,
        profileName: string,
        parameterNameArray: string[][],
        tenantId: string,
        sequenceNo: string,
        tpcInfo: string,
        primaryFlg: boolean,
    ): Promise<SOAPCommonOutputDto> {
        let soapCommonOutputDto = new SOAPCommonOutputDto();
        const parameterNameList: ParameterName[] = [];
        for (let i = 0; i < parameterNameArray.length; i++) {
            const pramName = new ParameterName();
            pramName.setName(parameterNameArray[i][0]);
            pramName.setValue(parameterNameArray[i][1]);
            parameterNameList.push(pramName);
        }

        soapCommonOutputDto = await this.soapCommon.callWebSoapApi(
            operationName,
            operationTypeName,
            profileName,
            parameterNameList,
            tenantId,
            sequenceNo,
            tpcInfo,
            primaryFlg,
        );

        return soapCommonOutputDto;
    }
}
