import config from "config";
import { compareAsc, format } from "date-fns";

import Constants from "@/core/constant/Constants";
import MsgKeysConstants from "@/core/constant/MsgKeysConstants";
import ResultCdConstants from "@/core/constant/ResultCdConstants";

import Check from "@/core/common/Check";
import CheckUtil from "@/core/common/CheckUtil";
import RESTCommon from "@/core/common/RESTCommon";
import TenantManage from "@/core/common/TenantManage";
import StringUtils from "@/core/common/StringUtils";

import { useMongo } from "@/database/mongo";

import APISoDAO from "@/core/dao/APISoDAO";
import FrontSoCancelInputDto from "@/core/dto/FrontSoCancelInputDto";
import FrontSoCancelOutputDto from "@/core/dto/FronSoCancelOutputDto";
import ResponseHeader from "@/core/dto/ResponseHeader";

import ServiceOrdersEntity from "@/core/entity/ServiceOrdersEntity";
import CoreSwimmyApiLog from "@/models/coreSwimmyApiLog";

import { isSQLException } from "@/helpers/queryHelper";

import { IFService } from "../IFService";
import { getStringParameter } from "@/types/parameter.string";

export default class FrontSoCancelService
    extends RESTCommon
    implements IFService<FrontSoCancelInputDto, FrontSoCancelOutputDto>
{
    /** API_SO_DAO */
    // @Autowired
    // private APISoDAO apiSoDAO;
    private apiSoDAO = new APISoDAO(this.request, this.context);

    /** テナント管理 */
    // @Autowired
    // private TenantManage tenantManage;
    private tenantManage = new TenantManage(this.request, this.context);

    /** 予約キャンセル不可期間(予約キャンセル機能) */
    // @Value("#{mvno[CancelReservationDisableDays]}")
    // private String cancelReservationDisableDays;
    private cancelReservationDisableDays: string = config.get<string>(
        "mvno.CancelReservationDisableDays",
    );

    /** 定数 機能種別：仮登録回線追加 */
    private static FUNCTION_TYPE_APPLA = "51";

    /**
     * SOキャンセル。<BR>
     *
     * <PRE>
     * REST APIにより、受信したパラメータのSOキャンセルを行う。
     * </PRE>
     * @param param SOキャンセルInputDto
     * @param obj 可変長引数
     * @return Object 返却オブジェクト
     */
    public async service(
        param: FrontSoCancelInputDto,
        ...obj: any[]
    ): Promise<FrontSoCancelOutputDto> {
        this.context.log(
            "FrontSoCancelService.service start",
            JSON.stringify(param),
        );
        /** 送信番号取得 */
        // String sequenceNo = param.getRequestHeader().getSequenceNo();
        const sequenceNo = param.requestHeader.sequenceNo;
        /** 送信元システムID取得 */
        // String senderSystemId = param.getRequestHeader().getSenderSystemId();
        // const senderSystemId = param.requestHeader.senderSystemId;
        /** API認証キー取得 */
        // String apiKey = param.getRequestHeader().getApiKey();
        // const apiKey = param.requestHeader.apiKey;
        /** 機能種別取得 */
        // String functionType = param.getRequestHeader().getFunctionType();
        const functionType = param.requestHeader.functionType;

        /** テナントID */
        // String tenantId = param.getTenantId();
        const tenantId = param.tenantId;

        /** 対象SO所属テナントID */
        // String targetTenantId = param.getTargetTenantId();
        const targetTenantId = param.targetTenantId;

        /** SO-ID */
        // String serviceOrderIdKey = param.getServiceOrderIdKey();
        const serviceOrderIdKey = param.serviceOrderIdKey;

        /** MVNO顧客CSV連携不要フラグ */
        // String csvUnnecessaryFlag = param.getCsvUnnecessaryFlag();
        const csvUnnecessaryFlag = getStringParameter(param.csvUnnecessaryFlag);

        /** システム日付 */
        // String systemDateTime = MvnoUtil.getDateTimeNow();
        const systemDateTime = this.context.responseHeader.receivedDate;

        // 変数「キャンセル対象受付番号」
        // String targetSoId = null;

        // deep copy response header from context
        const responseHeader = this.context.responseHeader;
        // 変数「CSV作成要否」
        let csvOutputKbn = "0";

        // 変数「関係チェック結果CSV」
        let checkResultCsv: [boolean, string] = [false, null];

        // STEP20.0版対応　追加　START
        // 変数「処理コード」初期化
        let handleCode = ResultCdConstants.CODE_000000;
        // STEP20.0版対応　追加　END

        try {
            // REST-API電文パラメータ相関チェック
            // テナントIDが"OPF000"であること
            if (!Constants.FRONT_API_TENANT_ID.equals(tenantId)) {
                this.context.log(
                    "FrontSoCancelService: tenantId not OPF000",
                    tenantId,
                );
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APFOCW0001,
                    serviceOrderIdKey,
                    "テナントID",
                    tenantId,
                );
                responseHeader.processCode = ResultCdConstants.CODE_560101;
                return this.resultEdit(responseHeader);
            }

            // SO-IDが英数字15桁以下であること
            if (!Check.checkSoId(serviceOrderIdKey)) {
                this.context.log(
                    "FrontSoCancelService: serviceOrderIdKey invalid",
                    serviceOrderIdKey,
                );
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APFOCW0001,
                    serviceOrderIdKey,
                    "SO-ID",
                    serviceOrderIdKey,
                );
                responseHeader.processCode = ResultCdConstants.CODE_560101;
                return this.resultEdit(responseHeader);
            }

            // テナントIDが英数字6～10桁であること
            if (!Check.checkTargetTenatIdFmt(targetTenantId)) {
                this.context.log(
                    "FrontSoCancelService: targetTenantId invalid",
                    targetTenantId,
                );
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APFOCW0001,
                    serviceOrderIdKey,
                    "対象SO所属テナントID",
                    targetTenantId,
                );
                responseHeader.processCode = ResultCdConstants.CODE_560101;
                return this.resultEdit(responseHeader);
            }

            // MVNO顧客CSV連携不要フラグフォーマットチェック
            if (
                StringUtils.isEmpty(csvUnnecessaryFlag) ||
                (!"0".equals(csvUnnecessaryFlag) &&
                    !"1".equals(csvUnnecessaryFlag))
            ) {
                this.context.log(
                    "FrontSoCancelService: csvUnnecessaryFlag invalid",
                    csvUnnecessaryFlag,
                );
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APFOCW0001,
                    serviceOrderIdKey,
                    "MVNO顧客CSV連携不要フラグ",
                    csvUnnecessaryFlag,
                );
                responseHeader.processCode = ResultCdConstants.CODE_560101;
                return this.resultEdit(responseHeader);
            }

            // SO管理からキャンセル対象の情報を取得する。
            let serviceOrdersEntity: ServiceOrdersEntity = null;
            try {
                serviceOrdersEntity = await this.apiSoDAO.getReserveFrontSo(
                    serviceOrderIdKey,
                );
            } catch (e) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    handleCode = ResultCdConstants.CODE_560201;
                }
                throw e; // rethrow any error
            }
            if (serviceOrdersEntity === null) {
                this.context.log(
                    "FrontSoCancelService: serviceOrdersEntity null",
                    serviceOrderIdKey,
                );
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APFOCW0201,
                    serviceOrderIdKey,
                    "予約情報",
                );
                responseHeader.processCode = ResultCdConstants.CODE_560201;
                return this.resultEdit(responseHeader);
            } else if (serviceOrdersEntity.reserveDate === null) {
                this.context.log(
                    "FrontSoCancelService: service order's reserveDate is null",
                    serviceOrderIdKey,
                );
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APFOCW0201,
                    serviceOrderIdKey,
                    "予約情報．予約日",
                );
                responseHeader.processCode = ResultCdConstants.CODE_560201;
                return this.resultEdit(responseHeader);
            } else if (
                CheckUtil.checkIsNotNull(serviceOrdersEntity.restMessage)
            ) {
                this.context.log(
                    "FrontSoCancelService: service order's restMessage is null",
                    serviceOrderIdKey,
                );
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APFOCW0201,
                    serviceOrderIdKey,
                    "予約情報．REST電文",
                );
                responseHeader.processCode = ResultCdConstants.CODE_560201;
                return this.resultEdit(responseHeader);
            }

            // 取得したREST電文からの情報を取得する。
            const json = serviceOrdersEntity.restMessage;
            const jsonBody = new Map(Object.entries(typeof json === "string" ? JSON.parse(json) : json));
            const jsonHeader = new Map(
                Object.entries(jsonBody.get("requestHeader")),
            );
            // 機能種別
            const jsonFunctionType = jsonHeader.get("functionType");
            if (StringUtils.isEmpty(jsonFunctionType)) {
                this.context.log(
                    "FrontSoCancelService: functionType empty",
                    jsonFunctionType,
                );
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APFOCW0202,
                    serviceOrderIdKey,
                    "機能種別",
                );
                responseHeader.processCode = ResultCdConstants.CODE_560202;
                return this.resultEdit(responseHeader);
            }
            // テナントID
            const jsonTenantId = jsonBody.get("tenantId");
            if (StringUtils.isEmpty(jsonTenantId as string)) {
                this.context.log(
                    "FrontSoCancelService: tenantId empty",
                    jsonTenantId,
                );
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APFOCW0202,
                    serviceOrderIdKey,
                    "テナントID",
                );
                responseHeader.processCode = ResultCdConstants.CODE_560202;
                return this.resultEdit(responseHeader);
            }
            // 対象回線の所属テナントID
            const jsonTargetTenantId = jsonBody.get("targetTenantId");
            if (StringUtils.isEmpty(jsonTargetTenantId as string)) {
                this.context.log(
                    "FrontSoCancelService: targetTenantId empty",
                    jsonTargetTenantId,
                );
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APFOCW0202,
                    serviceOrderIdKey,
                    "対象回線の所属テナントID",
                );
                responseHeader.processCode = ResultCdConstants.CODE_560202;
                return this.resultEdit(responseHeader);
            }
            // 回線番号
            const jsonLineNo = jsonBody.get("lineNo");
            if (StringUtils.isEmpty(jsonLineNo as string)) {
                this.context.log(
                    "FrontSoCancelService: lineNo empty",
                    jsonLineNo,
                );
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APFOCW0202,
                    serviceOrderIdKey,
                    "回線番号",
                );
                responseHeader.processCode = ResultCdConstants.CODE_560202;
                return this.resultEdit(responseHeader);
            }

            /** STEP5.0版対応　追加　START */
            // 機能種別による判定
            if (
                FrontSoCancelService.FUNCTION_TYPE_APPLA.equals(
                    jsonFunctionType,
                )
            ) {
                this.context.log(
                    "FrontSoCancelService: functionType invalid",
                    jsonFunctionType,
                );
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APFOCW0203,
                    serviceOrderIdKey,
                    jsonFunctionType,
                );
                responseHeader.processCode = ResultCdConstants.CODE_560203;
                return this.resultEdit(responseHeader);
            }
            /** STEP5.0版対応　追加　END */

            // SG(システムプロパティ)「キャンセル不可期間」フォーマット判定を行う。
            if (
                !Check.checkCancelReservationDisableDaysFmt(
                    this.cancelReservationDisableDays,
                )
            ) {
                this.context.log(
                    "FrontSoCancelService: cancelReservationDisableDays invalid",
                    this.cancelReservationDisableDays,
                );
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APFOCW0301,
                    this.cancelReservationDisableDays,
                );
                responseHeader.processCode = ResultCdConstants.CODE_560301;
                return this.resultEdit(responseHeader);
            }

            // SG(システムプロパティ)「キャンセル不可期間」判定を行う。
            // SimpleDateFormat df = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
            // 予約日
            // String reserveDate = df.format(serviceOrdersEntity.getReserveDate());
            const reserveDate = format(
                serviceOrdersEntity.reserveDate,
                "yyyy/MM/dd HH:mm:ss",
            );

            if (
                !Check.checkCancelReservationDisableDays(
                    this.cancelReservationDisableDays,
                    reserveDate,
                    systemDateTime,
                )
            ) {
                this.context.log(
                    "FrontSoCancelService: cancelReservationDisableDays invalid",
                    this.cancelReservationDisableDays,
                    reserveDate,
                    systemDateTime,
                );
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APFOCW0302,
                    serviceOrderIdKey,
                    this.cancelReservationDisableDays,
                    systemDateTime,
                );
                responseHeader.processCode = ResultCdConstants.CODE_560302;
                return this.resultEdit(responseHeader);
            }

            // 予約キャンセルの実行日時が、SO管理の予約日を過ぎていないか判定を行う。
            const sysDate = new Date();
            if (compareAsc(sysDate, serviceOrdersEntity.reserveDate) >= 0) {
                this.context.log(
                    "FrontSoCancelService: system date is after reserve date",
                    sysDate,
                    serviceOrdersEntity.reserveDate,
                );
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APFOCW0401,
                    serviceOrderIdKey,
                    systemDateTime,
                    reserveDate,
                );
                responseHeader.processCode = ResultCdConstants.CODE_560401;
                return this.resultEdit(responseHeader);
            }

            // 回線所属先テナントIDが、入力パラメータとREST電文中の値で一致しているか判定する
            if (jsonTargetTenantId !== targetTenantId) {
                this.context.log(
                    "FrontSoCancelService: targetTenantId not match",
                    jsonTargetTenantId,
                    targetTenantId,
                );
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APFOCW0501,
                    serviceOrderIdKey,
                    targetTenantId,
                    jsonTargetTenantId,
                );
                responseHeader.processCode = ResultCdConstants.CODE_560501;
                return this.resultEdit(responseHeader);
            }

            // 予約キャンセルできる対象かどうかを判断する。
            let nNo: string = null;
            if ("52".equals(jsonFunctionType)) {
                csvOutputKbn = "1";

                // STEP20.0版対応　変更　START
                try {
                    checkResultCsv = await this.tenantManage.doCheck(
                        (jsonLineNo as string) || "",
                        jsonTargetTenantId,
                    );
                    if (StringUtils.isEmpty(checkResultCsv[1])) {
                        csvOutputKbn = "0";
                        super.error(
                            tenantId,
                            sequenceNo,
                            MsgKeysConstants.APFOCE0602,
                            serviceOrderIdKey,
                            jsonLineNo,
                            jsonTargetTenantId,
                        );
                    }
                } catch (e) {
                    // DBアクセスリトライエラーの場合
                    if (isSQLException(e)) {
                        csvOutputKbn = "0";
                        super.error(
                            e,
                            tenantId,
                            sequenceNo,
                            MsgKeysConstants.APCOME0402,
                            functionType,
                        );
                    } else {
                        throw e; // rethrow any error
                    }
                }
                // STEP20.0版対応　変更　END

                // 入力パラメータ．「MVNO顧客CSV連携不要フラグ」 から判定
                if ("1".equals(csvUnnecessaryFlag)) {
                    csvOutputKbn = "0";
                }
                nNo = checkResultCsv[1];
            }

            let receiptId = "";
            this.context.log(
                "FrontSoCancelService processCode",
                responseHeader.processCode,
            );
            if ("000000".equals(responseHeader.processCode)) {
                // STEP20.0版対応　変更　START
                try {
                    await this.apiSoDAO.updServiceOrders(
                        null,
                        "キャンセル済み",
                        serviceOrderIdKey,
                        null,
                    );
                } catch (e) {
                    // DBアクセスリトライエラーの場合
                    if (isSQLException(e)) {
                        handleCode = ResultCdConstants.CODE_999999;
                    }
                    throw e;
                }
                // STEP20.0版対応　変更　END

                // get receipt ID from last response of 回線廃止 (if CODE OK and need Swimmy連携)
                if (csvOutputKbn === "1") {
                    await useMongo(this.context);
                    const swimmyApiLog = await CoreSwimmyApiLog.findOne({
                        requestOrderId: serviceOrderIdKey,
                    }).exec();
                    if (swimmyApiLog) {
                        receiptId = this.getReceiptId(
                            swimmyApiLog.responseParam,
                        );
                    } else {
                        this.context.error(
                            "FrontSoCancelService: swimmyApiLog not found for serviceOrderIdKey: " +
                                serviceOrderIdKey,
                        );
                    }
                    if (!receiptId) {
                        this.context.error(
                            "FrontSoCancelService: receiptId not found for serviceOrderIdKey: " +
                                serviceOrderIdKey,
                        );
                    }
                }
            }
            return this.resultEdit(
                responseHeader,
                csvOutputKbn,
                nNo,
                jsonLineNo as string,
                receiptId,
            );
        } catch (e: any) {
            if (isSQLException(e)) {
                // DBアクセスリトライエラーの場合
                // リターンコード値は各エラー時にて設定済み
                super.error(
                    e,
                    param.tenantId,
                    param.requestHeader.sequenceNo,
                    MsgKeysConstants.APCOME0402,
                    functionType,
                );
                responseHeader.processCode = handleCode;
                return this.resultEdit(responseHeader);
            }
            super.error(
                e,
                param.tenantId,
                param.requestHeader.sequenceNo,
                MsgKeysConstants.APCOME0401,
                e,
            );
            responseHeader.processCode = ResultCdConstants.CODE_999999;
            return this.resultEdit(responseHeader);
        }
    }

    /**
     * 返却オブジェクト編集
     *
     * @param responseHeader レスポンスヘッダ
     * @param csvOutputKbn CSV出力フラグ
     * @param nNo N番
     * @param lineNo 予約オーダの回線番号
     * @param receiptId 予約オーダの受付ID
     * @returns 返却オブジェクト
     */
    private resultEdit(
        responseHeader: ResponseHeader,
        csvOutputKbn: string = "0",
        nNo: string = null,
        lineNo: string = null,
        receiptId: string = null,
    ): FrontSoCancelOutputDto {
        return {
            jsonBody: {
                responseHeader,
            },
            additionalData: {
                csvOutputKbn,
                nNo,
                csvUnnecessary: csvOutputKbn === "0",
                lineNo,
                receiptId,
            },
        };
    }

    /**
     * キャンセル対象の回線廃止オーダ時のレスポンスにある「receiptId」を取得する
     * @param responseParam 回線廃止オーダのレスポンスパラメータ
     * @returns receiptId
     */
    private getReceiptId(responseParam: any): string {
        if (
            responseParam === null ||
            responseParam === undefined ||
            typeof responseParam !== "object"
        ) {
            this.context.log(
                "getReceiptId: responseParam empty or not object",
                responseParam,
            );
            return null;
        }
        // response param path should be
        // appResponseInfo.appInfoList[0].appBasic.receiptId
        if (
            responseParam?.appResponseInfo?.appInfoList === undefined ||
            !Array.isArray(responseParam.appResponseInfo.appInfoList)
        ) {
            this.context.log("getReceiptId: appInfoList not array");
            return null;
        }
        if (responseParam.appResponseInfo.appInfoList.length === 0) {
            this.context.log("getReceiptId: appInfoList empty");
            return null;
        }
        return (
            responseParam.appResponseInfo.appInfoList[0]?.appBasic?.receiptId ||
            null
        );
    }
}
