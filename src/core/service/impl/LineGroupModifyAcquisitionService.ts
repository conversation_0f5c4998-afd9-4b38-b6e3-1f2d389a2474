import config from "config";
import RESTCommon from "@/core/common/RESTCommon";
import { IFService } from "../IFService";
import LineGroupModifyAcquisitionInputDto from "@/core/dto/LineGroupModifyAcquisitionInputDto";
import LineGroupModifyAcquisitionOutputDto from "@/core/dto/LineGroupModifyAcquisitionOutputDto";
import APICommonDAO from "@/core/dao/APICommonDAO";
import APILinesGroupDAO from "@/core/dao/ApiLinesGroupDAO";
import SOAPCommonCoupon from "@/core/common/SOAPCommonCoupon";
import SOCommon from "@/core/common/SOCommon";
import TenantManage from "@/core/common/TenantManage";
import APILinesDAO from "@/core/dao/APILinesDAO";
import MvnoUtil from "@/core/common/MvnoUtil";
import ResultCdConstants from "@/core/constant/ResultCdConstants";
import MsgKeysConstants from "@/core/constant/MsgKeysConstants";
import Check from "@/core/common/Check";
import { isSQLException, retryQuery } from "@/helpers/queryHelper";
import { Transaction } from "sequelize";
import SOObject from "@/core/dto/SOObject";
import Constants from "@/core/constant/Constants";
import ParameterName from "@/core/dto/ParameterName";
import SOAPCommonOutputDto from "@/core/dto/SOAPCommonOutputDto";
import SOAPException from "@/types/soapException";
import ResponseHeader from "@/core/dto/ResponseHeader";
import { TenantsEntity } from "@/core/entity/TenantsEntity";
import LineGroupsEntity from "@/core/entity/LineGroupsEntity";
import StringUtils from "@/core/common/StringUtils";
import LineLineGroupsEntity from "@/core/entity/LineLineGroupsEntity";
import ApiCommon from "@/core/common/ApiCommon";
import { isLockNotAvailableError, usePsql } from "@/database/psql";
import { LinesEntity } from "@/core/entity/LinesEntity";
import PlansEntity from "@/core/entity/PlansEntity";
import CheckUtil from "@/core/common/CheckUtil";
import { GroupPlansEntity } from "@/core/entity/GroupPlansEntity";
import { getConfig } from "@/helpers/configHelper";
import { getStringParameter } from "@/types/parameter.string";

export default class LineGroupModifyAcquisitionService
    extends RESTCommon
    implements
        IFService<
            LineGroupModifyAcquisitionInputDto,
            LineGroupModifyAcquisitionOutputDto
        >
{
    private readonly LOCK_WAIT_MILLISEC = 1000;
    /**
     * API共通DAO
     */
    private aPICommonDAO = new APICommonDAO(this.request, this.context);

    /**
     * API回線グループDAO
     */
    private aPILinesGroupDAO = new APILinesGroupDAO(this.request, this.context);

    /**
     * SOAP API連携機能初期化
     */
    private sOAPCommonCoupon = new SOAPCommonCoupon(this.request, this.context);

    /**
     * SO管理共通DAO
     */
    private soCommon = new SOCommon(this.request, this.context);
    /**
     * テナント管理クラス
     */
    private tenantManage = new TenantManage(this.request, this.context);

    /** API共通処理 */
    // private ApiCommon apiCommon;
    private apiCommon = new ApiCommon(this.request, this.context);

    /**
     * API回線DAO
     */
    private aPILinesDAO = new APILinesDAO(this.request, this.context);

    /**
     * 予約実行日時単位(予約機能)
     */
    private reservationDateExecutionUnits: string = getConfig(
        "ReservationDateExecutionUnits",
    );

    /**
     * 予約可能制限日数(予約機能)
     */
    private reservationsLimitDays: string = getConfig("ReservationsLimitDays");
    /**
     * グループペアサービスプランID
     */
    // private groupPairServiceId = config.get<string>(
    //     "mvno.GroupPairServiceId",
    // );
    /**
     * 回線グループ所属回線変更機能。<BR>
     *
     * <PRE>
     * モバイルアクセス卸ポータルシステムは、B-OCN・UNOポータルおよび卸事業者からの回線グループの所属回線変更のリクエストによって、
     * 割当または、割当解除操作と、回線総量制限サービスのONまたはＯＦＦをＴＰＣにリクエストする機能である。
     * </PRE>
     *
     * @param param     回線グループ所属回線変更機能インプット
     * @param isPrivate 内部呼出フラグ
     * @param params    可変長さ引数(内部から呼出す時に使用するパラメータ)
     * @return LineGroupModifyAcquisitionOutputDto 回線グループ所属回線変更機能アウトプット
     */
    public async service(
        param: LineGroupModifyAcquisitionInputDto,
        isPrivate: boolean,
        ...params: any[]
    ): Promise<LineGroupModifyAcquisitionOutputDto> {
        super.debug(
            param.tenantId,
            param.requestHeader.sequenceNo,
            MsgKeysConstants.APGMDD0001,
            this.getClassName(),
            "service",
            JSON.stringify(param),
        );
        // REST API共通処理が呼び出されたシステム日付
        const receivedDate = MvnoUtil.getDateTimeNow();
        /** 変数「処理コード」初期化 */
        let handleCode = ResultCdConstants.CODE_000000;
        /** 送信番号取得 */
        const sequenceNo = param.requestHeader.sequenceNo;
        /** 送信元システムID取得 */
        const senderSystemId = param.requestHeader.senderSystemId;
        /** API認証キー取得 */
        const apiKey = param.requestHeader.apiKey;
        /** 機能種別取得 */
        const functionType = param.requestHeader.functionType;
        /** テナントID取得 */
        const tenantId = param.tenantId;
        /** 回線番号取得 */
        const lineNo = param.lineNo;
        /** 操作取得 */
        const operation = getStringParameter(param.operation);
        /** 回線グループID取得 */
        const lineGroupId = getStringParameter(param.lineGroupId);
        /** 回線総量制限サービス可否取得 */
        const totalVolumeControlFlag = getStringParameter(param.totalVolumeControlFlag);
        // 変数「エラーメッセージ」を定義する
        const errorMessage = null;
        // 変数「実行ユーザID」を定義する
        let executeUserId = null;
        // 変数「実行テナントID」を定義する
        let executeTenantId = null;

        // 回線グループ所属回線変更アウトプット初期化
        let outputDto: LineGroupModifyAcquisitionOutputDto = null;
        // 予約日
        const reserveDate = param.reserve_date;
        // 予約フラグ
        const reserveFlg = param.reserve_flag;
        // ＳＯ－ＩＤ
        const reserveSoId = param.reserve_soId;
        // 変数「オーダ種別」初期化
        const orderType = Check.checkOrderType(
            reserveDate,
            reserveFlg,
            reserveSoId,
        );
        const csvUnnecessaryFlag = getStringParameter(param.csvUnnecessaryFlag);
        // 変数「CSV作成要否」
        let csvOutputKbn = "0";
        let checkResult: [boolean, string] = [false, null];

        // 変数「プラン種別」
        let planClass: number = null;

        // 基本容量
        let basicCapacity: number = null;

        // 回線利用状態 ※割当のみ値が変更される
        let lineUsageStatus: number = null;

        // MAWP保守 #604対応 追加 START
        const sequelize = await usePsql();
        let tx: Transaction = null;

        // 内部から呼び出す時に、可変長さ引数（params）から、実行ユーザIDと実行テナントIDを取得し、変数「実行ユーザID」と変数「実行テナントID」に設定する
        if (isPrivate) {
            // 変数「実行ユーザID」を設定する
            executeUserId = String(params[0]);
            // 変数「実行テナントID」を設定する
            executeTenantId = String(params[1]);
        }

        let commitFlag = false;
        let canRollback = false;
        let nNo: string = null;
        handleCode = this.context.responseHeader.processCode;
        const apiHandleId = this.context.responseHeader.apiProcessID;

        try {
            // 業務チェック
            // オーダ種別チェック
            // 変数「オーダ種別」より判定する
            if (Constants.ORDER_TYPE_9.equals(orderType)) {
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APGMDW0011);
                // 変数「処理コード」設定
                handleCode = ResultCdConstants.CODE_140006;
                // 返却値編集
                outputDto = this.returnEdit(
                    param,
                    handleCode,
                    apiHandleId,
                    errorMessage,
                    isPrivate,
                    receivedDate,
                );
                return outputDto;
            }
            // 予約実行オーダの場合
            if (Constants.ORDER_TYPE_2.equals(orderType)) {
                // APサーバの複数IP取得
                const localIpList = MvnoUtil.getLocalIpList();
                // 予約実行可否チェック
                if (!this.context.isInternalRequest) {
                    this.context.log(
                        "LineGroupModifyAcquisitionService: remoteAddr check failed",
                        String(params[2])
                    )
                    super.error(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APCOME0401,
                        "予約実行できませんでした。クライアントIP:" +
                            String(params[2]) +
                            ", APサーバーIP：" +
                            MvnoUtil.getOutputList(localIpList),
                    );
                    // 変数「処理コード」設定
                    handleCode = ResultCdConstants.CODE_999999;
                    // 返却値編集
                    outputDto = this.returnEdit(
                        param,
                        handleCode,
                        apiHandleId,
                        errorMessage,
                        isPrivate,
                        receivedDate,
                    );
                    return outputDto;
                }
            }

            // 回線番号フォーマットチェックを行う
            if (!Check.checkLineNo(lineNo)) {
                // エラー処理の「■出力ログ内容編集」の項番1より編集し、ログ出力
                this.context.log(
                    "LineGroupModifyAcquisitionService: lineNo doesn't meet format",
                    lineNo
                )
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APGMDW0001,
                    "回線番号",
                    lineNo,
                );
                // 変数「処理コード」に「140001」を設定
                handleCode = ResultCdConstants.CODE_140001;
                // SO管理共通を呼び出す
                await this.soManagementCommon(
                    param,
                    apiHandleId,
                    handleCode,
                    executeUserId,
                    executeTenantId,
                    receivedDate,
                    orderType,
                    reserveDate,
                    isPrivate,
                );

                // 返却値編集
                outputDto = this.returnEdit(
                    param,
                    handleCode,
                    apiHandleId,
                    errorMessage,
                    isPrivate,
                    receivedDate,
                );
                return outputDto;
            }

            // 操作フォーマットチェックを行う
            /** #159横展開対応 追加 START */
            if (!"1".equals(operation) && !"0".equals(operation)) {
                /** #159横展開対応 追加 END */
                // エラー処理の「■出力ログ内容編集」の項番2より編集し、ログ出力
                this.context.log(
                    "LineGroupModifyAcquisitionService: operation doesn't meet format",
                    operation
                )
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APGMDW0001,
                    "操作",
                    operation,
                );
                // 変数「処理コード」に「140001」を設定
                handleCode = ResultCdConstants.CODE_140001;

                // SO管理共通を呼び出す
                await this.soManagementCommon(
                    param,
                    apiHandleId,
                    handleCode,
                    executeUserId,
                    executeTenantId,
                    receivedDate,
                    orderType,
                    reserveDate,
                    isPrivate,
                );

                // 返却値編集
                outputDto = this.returnEdit(
                    param,
                    handleCode,
                    apiHandleId,
                    errorMessage,
                    isPrivate,
                    receivedDate,
                );

                return outputDto;
            }

            // 回線グループIDフォーマットチェックを行う
            if (!Check.checkLineGroupId(lineGroupId)) {
                // エラー処理の「■出力ログ内容編集」の項番3より編集し、ログ出力
                this.context.log(
                    "LineGroupModifyAcquisitionService: lineGroupId doesn't meet format",
                    lineGroupId
                )
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APGMDW0001,
                    "回線グループID",
                    lineGroupId,
                );
                // 変数「処理コード」に「140001」を設定する
                handleCode = ResultCdConstants.CODE_140001;

                // SO管理共通を呼び出す
                await this.soManagementCommon(
                    param,
                    apiHandleId,
                    handleCode,
                    executeUserId,
                    executeTenantId,
                    receivedDate,
                    orderType,
                    reserveDate,
                    isPrivate,
                );

                // 返却値編集
                outputDto = this.returnEdit(
                    param,
                    handleCode,
                    apiHandleId,
                    errorMessage,
                    isPrivate,
                    receivedDate,
                );
                return outputDto;
            }

            // 回線総量制限サービス可否フォーマットチェックを行う
            // 入力パラメータ．「回線総量制限サービス可否」!=1 and !=0の場合
            /** #159横展開対応 追加 START */
            if (
                !"1".equals(totalVolumeControlFlag) &&
                !"0".equals(totalVolumeControlFlag)
            ) {
                /** #159横展開対応 追加 END */
                // エラー処理の「■出力ログ内容編集」の項番4より編集し、ログ出力
                this.context.log(
                    "LineGroupModifyAcquisitionService: totalVolumeControlFlag doesn't meet format",
                    totalVolumeControlFlag
                )
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APGMDW0001,
                    "回線総量制限サービス可否",
                    totalVolumeControlFlag,
                );
                // 変数「処理コード」に「140001」を設定する
                handleCode = ResultCdConstants.CODE_140001;

                // SO管理共通を呼び出す
                await this.soManagementCommon(
                    param,
                    apiHandleId,
                    handleCode,
                    executeUserId,
                    executeTenantId,
                    receivedDate,
                    orderType,
                    reserveDate,
                    isPrivate,
                );
                // 返却値編集
                outputDto = this.returnEdit(
                    param,
                    handleCode,
                    apiHandleId,
                    errorMessage,
                    isPrivate,
                    receivedDate,
                );
                return outputDto;
            }

            if (
                Constants.ORDER_TYPE_0.equals(orderType) ||
                Constants.ORDER_TYPE_1.equals(orderType)
            ) {
                // ５ MVNO顧客CSV連携不要フラグフォーマットチェック
                if (
                    StringUtils.isNotEmpty(csvUnnecessaryFlag) &&
                    !"0".equals(csvUnnecessaryFlag) &&
                    !"1".equals(csvUnnecessaryFlag)
                ) {
                    // エラー処理の「■出力ログ内容編集」の項番33より編集し、ログ出力
                    this.context.log(
                        "LineGroupModifyAcquisitionService: csvUnnecessaryFlag doesn't meet format",
                        csvUnnecessaryFlag
                    )
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APGMDW0001,
                        "MVNO顧客CSV連携不要フラグ",
                        csvUnnecessaryFlag,
                    );
                    // 変数「処理コード」に「140001」を設定する
                    handleCode = ResultCdConstants.CODE_140001;
                    // SO管理共通を呼び出す
                    await this.soManagementCommon(
                        param,
                        apiHandleId,
                        handleCode,
                        executeUserId,
                        executeTenantId,
                        receivedDate,
                        orderType,
                        reserveDate,
                        isPrivate,
                    );
                    // 返却値編集
                    outputDto = this.returnEdit(
                        param,
                        handleCode,
                        apiHandleId,
                        errorMessage,
                        isPrivate,
                        receivedDate,
                    );
                    return outputDto;
                }
            }

            // 社内テナント種別チェックを行う
            let tenantsEntity: TenantsEntity = null;
            try {
                tenantsEntity = await this.aPICommonDAO.getTenants(tenantId);
            } catch (e) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    handleCode = ResultCdConstants.CODE_140002;
                }
                throw e;
            }
            // 社内テナント種別を設定
            // 変数「社内テナント情報」= nullの場合
            if (tenantsEntity === null) {
                // エラー処理の「■出力ログ内容編集」の項番5より編集し、ログ出力
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APGMDW0002);
                // 変数「処理コード」に「140002」を設定する
                handleCode = ResultCdConstants.CODE_140002;
                // SO管理共通を呼び出す
                await this.soManagementCommon(
                    param,
                    apiHandleId,
                    handleCode,
                    executeUserId,
                    executeTenantId,
                    receivedDate,
                    orderType,
                    reserveDate,
                    isPrivate,
                );
                // 返却値編集
                outputDto = this.returnEdit(
                    param,
                    handleCode,
                    apiHandleId,
                    errorMessage,
                    isPrivate,
                    receivedDate,
                );
                return outputDto;
            }

            // 回線グループ管理情報取得を行う
            let lineGroupsEntity: LineGroupsEntity = null;
            try {
                lineGroupsEntity =
                    await this.aPILinesGroupDAO.getLineGroupsInfo(lineGroupId);
            } catch (e) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    handleCode = ResultCdConstants.CODE_140004;
                }
                throw e;
            }
            // 変数「社内テナント情報」．「社内テナント種別」=1（C-OCN）以外の場合
            if (
                tenantsEntity.tenantType === null ||
                tenantsEntity.tenantType !== 1
            ) {
                // 回線グループ管理情報チェックを行う
                // 下記のいずれかに該当すれば、エラーとなる。
                // ・「回線グループ管理情報」=null
                // ・「回線グループ管理情報」．「テナントＩＤ」!=入力パラメータ．「テナントＩＤ」
                // ・「回線グループ管理情報」．「ステータス」!=1
                // ・「回線グループ管理情報」．「プランＩＤ」=null
                if (
                    lineGroupsEntity === null ||
                    !tenantId.equals(lineGroupsEntity.tenantId) ||
                    lineGroupsEntity.status !== 1 ||
                    lineGroupsEntity.planId === null
                ) {
                    // エラー処理の「■出力ログ内容編集」の項番7より編集し、ログ出力
                    this.context.log(
                        "LineGroupModifyAcquisitionService: lineGroupsEntity meets following conditions: [null, status is not 1, planId is null, doens't match tenantId]",
                        lineGroupsEntity,
                        tenantsEntity,
                    )
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APGMDW0004,
                        lineGroupId,
                        tenantId,
                    );
                    // 変数「処理コード」に「140004」を設定する
                    handleCode = ResultCdConstants.CODE_140004;
                    // SO管理共通を呼び出す
                    await this.soManagementCommon(
                        param,
                        apiHandleId,
                        handleCode,
                        executeUserId,
                        executeTenantId,
                        receivedDate,
                        orderType,
                        reserveDate,
                        isPrivate,
                    );
                    // 返却値編集
                    outputDto = this.returnEdit(
                        param,
                        handleCode,
                        apiHandleId,
                        errorMessage,
                        isPrivate,
                        receivedDate,
                    );
                    return outputDto;
                }
                // 変数「社内テナント情報」．「社内テナント種別」=1（C-OCN）の場合
            } else {
                // 回線グループ管理情報が存在すればエラーとなる
                if (lineGroupsEntity !== null) {
                    // エラー処理の「■出力ログ内容編集」の項番7より編集し、ログ出力
                    this.context.log(
                        "LineGroupModifyAcquisitionService: lineGroupsEntity is not null",
                        lineGroupsEntity
                    )
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APGMDW0004,
                        lineGroupId,
                        tenantId,
                    );
                    // 変数「処理コード」に「140004」を設定する
                    handleCode = ResultCdConstants.CODE_140004;
                    // SO管理共通を呼び出す
                    await this.soManagementCommon(
                        param,
                        apiHandleId,
                        handleCode,
                        executeUserId,
                        executeTenantId,
                        receivedDate,
                        orderType,
                        reserveDate,
                        isPrivate,
                    );
                    // 返却値編集
                    outputDto = this.returnEdit(
                        param,
                        handleCode,
                        apiHandleId,
                        errorMessage,
                        isPrivate,
                        receivedDate,
                    );
                    return outputDto;
                }
            }
            // 予約前オーダと予約実行オーダの場合
            if (
                Constants.ORDER_TYPE_1.equals(orderType) ||
                Constants.ORDER_TYPE_2.equals(orderType)
            ) {
                // 予約日フォーマットチェック
                if (!Check.checkReserveDateFmt(reserveDate)) {
                    // エラー処理の「■出力ログ内容編集」の項番4より編集し、ログ出力
                    this.context.log(
                        "LineGroupModifyAcquisitionService: reserveDate doesn't meet format",
                        reserveDate
                    )
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APGMDW0001,
                        "予約日",
                        reserveDate,
                    );
                    // 変数「処理コード」に「140001」を設定する
                    handleCode = ResultCdConstants.CODE_140001;
                    // SO管理共通を呼び出す
                    await this.soManagementCommon(
                        param,
                        apiHandleId,
                        handleCode,
                        executeUserId,
                        executeTenantId,
                        receivedDate,
                        orderType,
                        reserveDate,
                        isPrivate,
                    );
                    // 返却値編集
                    outputDto = this.returnEdit(
                        param,
                        handleCode,
                        apiHandleId,
                        errorMessage,
                        isPrivate,
                        receivedDate,
                    );
                    return outputDto;
                }
            }

            if (Constants.ORDER_TYPE_1.equals(orderType)) {
                // 予約前オーダ: 予約日と投入日相関チェック
                if (
                    !Check.checkReserveDateOrderDate(receivedDate, reserveDate)
                ) {
                    // ログ出力
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APGMDW0012,
                        reserveDate,
                        receivedDate,
                    );
                    // 変数「処理コード」設定
                    handleCode = ResultCdConstants.CODE_140007;
                    // SO管理共通を呼び出す
                    await this.soManagementCommon(
                        param,
                        apiHandleId,
                        handleCode,
                        executeUserId,
                        executeTenantId,
                        receivedDate,
                        orderType,
                        reserveDate,
                        isPrivate,
                    );
                    // 返却値編集
                    outputDto = this.returnEdit(
                        param,
                        handleCode,
                        apiHandleId,
                        errorMessage,
                        isPrivate,
                        receivedDate,
                    );
                    return outputDto;
                }

                // 予約前オーダ:予約実行日時単位フォーマットチェック
                if (
                    !Check.checkReservationDateExecutionUnitsFmt(
                        this.reservationDateExecutionUnits,
                    )
                ) {
                    // ログ出力
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APGMDW0013,
                        this.reservationDateExecutionUnits,
                    );
                    // 変数「処理コード」設定
                    handleCode = ResultCdConstants.CODE_140008;
                    // SO管理共通を呼び出す
                    await this.soManagementCommon(
                        param,
                        apiHandleId,
                        handleCode,
                        executeUserId,
                        executeTenantId,
                        receivedDate,
                        orderType,
                        reserveDate,
                        isPrivate,
                    );
                    // 返却値編集
                    outputDto = this.returnEdit(
                        param,
                        handleCode,
                        apiHandleId,
                        errorMessage,
                        isPrivate,
                        receivedDate,
                    );
                    return outputDto;
                }

                // 予約前オーダ:予約実行日時単位と予約日相関チェックチェック
                if (
                    !Check.checkReservationDateExecutionUnits(
                        this.reservationDateExecutionUnits,
                        reserveDate,
                    )
                ) {
                    // ログ出力
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APGMDW0014,
                        this.reservationDateExecutionUnits,
                        reserveDate,
                    );
                    // 変数「処理コード」設定
                    handleCode = ResultCdConstants.CODE_140009;
                    // SO管理共通を呼び出す
                    await this.soManagementCommon(
                        param,
                        apiHandleId,
                        handleCode,
                        executeUserId,
                        executeTenantId,
                        receivedDate,
                        orderType,
                        reserveDate,
                        isPrivate,
                    );
                    // 返却値編集
                    outputDto = this.returnEdit(
                        param,
                        handleCode,
                        apiHandleId,
                        errorMessage,
                        isPrivate,
                        receivedDate,
                    );
                    return outputDto;
                }

                // 予約前オーダ:予約可能制限日数フォーマットチェック
                if (
                    !Check.checkReservationsLimitDaysFmt(
                        this.reservationsLimitDays,
                    )
                ) {
                    // ログ出力
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APGMDW0015,
                        this.reservationsLimitDays,
                    );
                    // 変数「処理コード」設定
                    handleCode = ResultCdConstants.CODE_140010;
                    // SO管理共通を呼び出す
                    await this.soManagementCommon(
                        param,
                        apiHandleId,
                        handleCode,
                        executeUserId,
                        executeTenantId,
                        receivedDate,
                        orderType,
                        reserveDate,
                        isPrivate,
                    );
                    // 返却値編集
                    outputDto = this.returnEdit(
                        param,
                        handleCode,
                        apiHandleId,
                        errorMessage,
                        isPrivate,
                        receivedDate,
                    );
                    return outputDto;
                }

                // 予約前オーダ:１２ 予約可能制限日数範囲チェック
                if (
                    !Check.checkReservationsLimitDays(
                        this.reservationsLimitDays,
                        reserveDate,
                    )
                ) {
                    // ログ出力
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APGMDW0016,
                        this.reservationsLimitDays,
                        reserveDate,
                    );
                    // 変数「処理コード」設定
                    handleCode = ResultCdConstants.CODE_140011;
                    // SO管理共通を呼び出す
                    await this.soManagementCommon(
                        param,
                        apiHandleId,
                        handleCode,
                        executeUserId,
                        executeTenantId,
                        receivedDate,
                        orderType,
                        reserveDate,
                        isPrivate,
                    );
                    // 返却値編集
                    outputDto = this.returnEdit(
                        param,
                        handleCode,
                        apiHandleId,
                        errorMessage,
                        isPrivate,
                        receivedDate,
                    );
                    return outputDto;
                }
            }
            // 入力パラメータ．「操作」=1（割当）の場合
            if ("1".equals(operation)) {
                // 回線ＩＤ確認を行う
                let lineLineGroupsEntity: LineLineGroupsEntity = null;
                try {
                    lineLineGroupsEntity =
                        await this.aPILinesGroupDAO.getLineId(lineNo);
                } catch (e) {
                    if (isSQLException(e)) {
                        // DBアクセスリトライエラーの場合
                        handleCode = ResultCdConstants.CODE_140100;
                    }
                    throw e;
                }

                // 社内テナント種別チェックを行う
                if (
                    tenantsEntity.tenantType === null ||
                    tenantsEntity.tenantType !== 1
                ) {
                    // 回線ＩＤ情報を取得
                    // LineLineGroupsEntity lineLineGroupsEntity =
                    // aPILinesGroupDAO.getLineId(lineNo);
                    try {
                        lineLineGroupsEntity =
                            await this.aPILinesGroupDAO.getLineId(lineNo);
                    } catch (e) {
                        if (isSQLException(e)) {
                            // DBアクセスリトライエラーの場合
                            handleCode = ResultCdConstants.CODE_140100;
                        }
                        throw e;
                    }

                    // 変数「回線ＩＤ情報」より判定する
                    // 回線ＩＤ情報を取得できた場合
                    if (lineLineGroupsEntity !== null) {
                        // エラー処理の「■出力ログ内容編集」の項番9より編集し、ログ出力
                        this.context.log(
                            "LineGroupModifyAcquisitionService: lineLineGroupsEntity is not null",
                            lineLineGroupsEntity
                        )
                        super.warn(
                            tenantId,
                            sequenceNo,
                            MsgKeysConstants.APGMDW0100,
                            lineNo,
                        );
                        // 変数「処理コード」に「140100」を設定する
                        handleCode = ResultCdConstants.CODE_140100;
                        // SO管理共通を呼び出す
                        await this.soManagementCommon(
                            param,
                            apiHandleId,
                            handleCode,
                            executeUserId,
                            executeTenantId,
                            receivedDate,
                            orderType,
                            reserveDate,
                            isPrivate,
                        );
                        // 返却値編集
                        outputDto = this.returnEdit(
                            param,
                            handleCode,
                            apiHandleId,
                            errorMessage,
                            isPrivate,
                            receivedDate,
                        );
                        return outputDto;
                    }
                }

                // テナント管理を呼び出す
                try {
                    checkResult = await this.tenantManage.doCheck(
                        lineNo,
                        tenantId,
                    );
                } catch (e) {
                    if (isSQLException(e)) {
                        // DBアクセスリトライエラーの場合
                        handleCode = ResultCdConstants.CODE_140100;
                    }
                    throw e;
                }

                if (!checkResult[0]) {
                    // エラー処理の「■出力ログ内容編集」の項番9より編集し、ログ出力
                    this.context.log(
                        "LineGroupModifyAcquisitionService: LineNo doesn't belong to tenants",
                        lineNo,
                        tenantId,
                        checkResult
                    )
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APGMDW0100,
                        lineNo,
                    );
                    // 変数「処理コード」に「140100」を設定する
                    handleCode = ResultCdConstants.CODE_140100;

                    // SO管理共通を呼び出す
                    await this.soManagementCommon(
                        param,
                        apiHandleId,
                        handleCode,
                        executeUserId,
                        executeTenantId,
                        receivedDate,
                        orderType,
                        reserveDate,
                        isPrivate,
                    );

                    // 返却値編集
                    outputDto = this.returnEdit(
                        param,
                        handleCode,
                        apiHandleId,
                        errorMessage,
                        isPrivate,
                        receivedDate,
                    );
                    return outputDto;
                }

                // 廃止オーダ受付中チェックを実施する
                let targetDate: string = null;
                if (Constants.ORDER_TYPE_0.equals(orderType)) {
                    targetDate = receivedDate.substring(0, 10).replace(/\//g, "");
                } else {
                    targetDate = reserveDate.substring(0, 10).replace(/\//g, "");
                }

                let checkAbolishSoResult: boolean = null;
                try {
                    checkAbolishSoResult = await this.apiCommon.checkAbolishSo(
                        lineNo,
                        targetDate,
                    );
                } catch (e) {
                    if (isSQLException(e)) {
                        // DBアクセスリトライエラーの場合
                        handleCode = ResultCdConstants.CODE_140100;
                    }
                    throw e;
                }

                // 変数「廃止オーダ受付中判定結果」より判定する
                if (!checkAbolishSoResult) {
                    // ログ出力
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APGMDW0105,
                        lineNo,
                    );
                    // 変数「処理コード」設定
                    handleCode = ResultCdConstants.CODE_140100;
                    // SO管理共通を呼び出す
                    await this.soManagementCommon(
                        param,
                        apiHandleId,
                        handleCode,
                        executeUserId,
                        executeTenantId,
                        receivedDate,
                        orderType,
                        reserveDate,
                        isPrivate,
                    );
                    // 返却値編集
                    outputDto = this.returnEdit(
                        param,
                        handleCode,
                        apiHandleId,
                        errorMessage,
                        isPrivate,
                        receivedDate,
                    );
                    return outputDto;
                }

                const result = await retryQuery(
                    this.context,
                    "回線回線グループ挿入を行う",
                    async () => {
                        if (tx !== null && canRollback) {
                            try {
                                this.context.log(
                                    "LineGroupModifyAcquisitionService.service rollback",
                                );
                                await tx.rollback();
                            } finally {
                                canRollback = false;
                            }
                        }
                        // トランザクション開始
                        tx = await sequelize.transaction();
                        canRollback = true;

                        // 社内テナント種別チェックを行う
                        if (
                            tenantsEntity.tenantType === null ||
                            tenantsEntity.tenantType !== 1
                        ) {
                            // MAWP保守 #604対応 変更 START
                            let lockFlag: boolean = true;
                            if (Constants.ORDER_TYPE_1.equals(orderType)) {
                                // 予約前オーダの場合は行ロックを取得しない
                                lockFlag = false;
                            }

                            // 行ロック取得し、回線情報を取得
                            let linesEntity: LinesEntity = null;
                            for (
                                let linelockTime = 0;
                                linelockTime < 5;
                                linelockTime++
                            ) {
                                try {
                                    // 回線情報から再販料金プランを取得する
                                    linesEntity =
                                        await this.aPILinesGroupDAO.getResaleChargePlanLock(
                                            lineNo,
                                            lockFlag,
                                            tx,
                                        );

                                    if (linesEntity === null) {
                                        // ロールバック
                                        try {
                                            // rollback before returning
                                            await tx.rollback();
                                        } finally {
                                            canRollback = false;
                                        }
                                        // エラー処理の「■出力ログ内容編集」の項番10より編集し、ログ出力
                                        this.context.log(
                                            "LineGroupModifyAcquisitionService: linesEntity is null",
                                            lineNo
                                        )
                                        super.warn(
                                            tenantId,
                                            sequenceNo,
                                            MsgKeysConstants.APGMDW0101,
                                        );
                                        // 変数「処理コード」に「140101」を設定する
                                        handleCode =
                                            ResultCdConstants.CODE_140101;
                                        // SO管理共通を呼び出す
                                        await this.soManagementCommon(
                                            param,
                                            apiHandleId,
                                            handleCode,
                                            executeUserId,
                                            executeTenantId,
                                            receivedDate,
                                            orderType,
                                            reserveDate,
                                            isPrivate,
                                        );
                                        // 返却値編集
                                        outputDto = this.returnEdit(
                                            param,
                                            handleCode,
                                            apiHandleId,
                                            errorMessage,
                                            isPrivate,
                                            receivedDate,
                                        );
                                        return outputDto;
                                    } else {
                                        break;
                                    }
                                } catch (e) {
                                    if (isLockNotAvailableError(e)) {
                                        if (linelockTime === 4) {
                                            // ロールバック
                                            await tx.rollback();
                                            canRollback = false;
                                            // エラー処理の「■出力ログ内容編集」の項番10より編集し、ログ出力
                                            this.context.log(
                                                "LineGroupModifyAcquisitionService: Failed to get line lock",
                                                lineNo
                                            )
                                            super.warn(
                                                tenantId,
                                                sequenceNo,
                                                MsgKeysConstants.APGMDW0101,
                                            );
                                            // 変数「処理コード」に「140101」を設定する
                                            handleCode =
                                                ResultCdConstants.CODE_140101;
                                            // SO管理共通を呼び出す
                                            await this.soManagementCommon(
                                                param,
                                                apiHandleId,
                                                handleCode,
                                                executeUserId,
                                                executeTenantId,
                                                receivedDate,
                                                orderType,
                                                reserveDate,
                                                isPrivate,
                                            );
                                            // 返却値編集
                                            outputDto = this.returnEdit(
                                                param,
                                                handleCode,
                                                apiHandleId,
                                                errorMessage,
                                                isPrivate,
                                                receivedDate,
                                            );
                                            return outputDto;
                                        }
                                        await tx.rollback();
                                        canRollback = false;

                                        try {
                                            // 次のロック取得まで待ちミリ秒数分待機
                                            await new Promise((resolve) => {
                                                setTimeout(
                                                    resolve,
                                                    this.LOCK_WAIT_MILLISEC,
                                                );
                                            });
                                        } catch (ex) {
                                            this.context.warn(
                                                "LineGroupModifyAcquisitionService.service sleep",
                                                ex,
                                            );
                                        }

                                        tx = await sequelize.transaction();
                                        canRollback = true;
                                        // continue;
                                    } else if (isSQLException(e)) {
                                        // DBアクセスリトライエラーの場合
                                        handleCode =
                                            ResultCdConstants.CODE_140101;
                                        throw e;
                                    } else {
                                        // ロールバック
                                        await tx.rollback();
                                        canRollback = false;
                                        // エラー処理の「■出力ログ内容編集」の項番10より編集し、ログ出力
                                        this.context.log(
                                            "LineGroupModifyAcquisitionService: Failed to get resaleChargePlanLock",
                                            lineNo,
                                            e
                                        )
                                        super.warn(
                                            tenantId,
                                            sequenceNo,
                                            MsgKeysConstants.APGMDW0101,
                                        );
                                        // 変数「処理コード」に「140101」を設定する
                                        handleCode =
                                            ResultCdConstants.CODE_140101;
                                        // SO管理共通を呼び出す
                                        await this.soManagementCommon(
                                            param,
                                            apiHandleId,
                                            handleCode,
                                            executeUserId,
                                            executeTenantId,
                                            receivedDate,
                                            orderType,
                                            reserveDate,
                                            isPrivate,
                                        );
                                        // 返却値編集
                                        outputDto = this.returnEdit(
                                            param,
                                            handleCode,
                                            apiHandleId,
                                            errorMessage,
                                            isPrivate,
                                            receivedDate,
                                        );
                                        return outputDto;
                                    }
                                }
                            }
                            // 回線の利用状態を設定
                            lineUsageStatus = linesEntity.usageStatus;

                            // プランテーブルから「プランID」、「更新サービスパターン」を取得する
                            let plansEntityList: PlansEntity[] = null;
                            if (
                                !CheckUtil.checkIsNotNull(
                                    linesEntity.pricePlanId,
                                )
                            ) {
                                try {
                                    plansEntityList =
                                        await this.aPILinesGroupDAO.getPlanInfo(
                                            linesEntity.pricePlanId,
                                        );
                                } catch (e) {
                                    if (isSQLException(e)) {
                                        // DBアクセスリトライエラーの場合
                                        handleCode =
                                            ResultCdConstants.CODE_140101;
                                    }
                                    throw e;
                                }
                                // 変数「プランIDリスト」より判定する
                                if (
                                    plansEntityList === null ||
                                    plansEntityList.length === 0 ||
                                    plansEntityList.length > 1
                                ) {
                                    // MAWP保守 #604対応 変更 START
                                    // ロールバック
                                    await tx.rollback();
                                    canRollback = false;
                                    // MAWP保守 #604対応 変更 END
                                    // エラー処理の「■出力ログ内容編集」の項番10より編集し、ログ出力
                                    this.context.log(
                                        "LineGroupModifyAcquisitionService: plansEntityList is null",
                                        linesEntity.pricePlanId,
                                        plansEntityList
                                    )
                                    super.warn(
                                        tenantId,
                                        sequenceNo,
                                        MsgKeysConstants.APGMDW0101,
                                    );
                                    // 変数「処理コード」に「140101」を設定する
                                    handleCode = ResultCdConstants.CODE_140101;
                                    // SO管理共通を呼び出す
                                    await this.soManagementCommon(
                                        param,
                                        apiHandleId,
                                        handleCode,
                                        executeUserId,
                                        executeTenantId,
                                        receivedDate,
                                        orderType,
                                        reserveDate,
                                        isPrivate,
                                    );
                                    // 返却値編集
                                    outputDto = this.returnEdit(
                                        param,
                                        handleCode,
                                        apiHandleId,
                                        errorMessage,
                                        isPrivate,
                                        receivedDate,
                                    );
                                    return outputDto;
                                }
                            }

                            // 更新サービスパターンチェックを行う
                            // if (
                            //     plansEntityList[0].tpcServicePattern !=
                            //         null &&
                            //     !"0".equals(
                            //         plansEntityList[0].tpcServicePattern
                            //     )
                            // ) {
                            if (
                                plansEntityList[0].tpcServicePattern !== null &&
                                !"0".equals(
                                    String(
                                        plansEntityList[0].tpcServicePattern,
                                    ),
                                )
                            ) {
                                // グループプランテーブルから「更新サービスパターン」を取得する
                                let groupPlansEntity: GroupPlansEntity = null;
                                try {
                                    groupPlansEntity =
                                        await this.aPILinesGroupDAO.getUpdateServicePatternGroup(
                                            lineGroupsEntity.planId,
                                        );
                                } catch (e) {
                                    if (isSQLException(e)) {
                                        // DBアクセスリトライエラーの場合
                                        handleCode =
                                            ResultCdConstants.CODE_140101;
                                    }
                                    throw e;
                                }
                                // 変数「更新サービスパターン情報」=nullの場合
                                if (groupPlansEntity === null) {
                                    // MAWP保守 #604対応 変更 START
                                    // ロールバック
                                    await tx.rollback();
                                    canRollback = false;
                                    // MAWP保守 #604対応 変更 END
                                    // エラー処理の「■出力ログ内容編集」の項番10より編集し、ログ出力
                                    this.context.log(
                                        "LineGroupModifyAcquisitionService: groupPlansEntity is null",
                                        lineGroupsEntity.planId
                                    )
                                    super.warn(
                                        tenantId,
                                        sequenceNo,
                                        MsgKeysConstants.APGMDW0101,
                                    );
                                    // 変数「処理コード」に「140101」を設定する
                                    handleCode = ResultCdConstants.CODE_140101;
                                    // SO管理共通を呼び出す
                                    await this.soManagementCommon(
                                        param,
                                        apiHandleId,
                                        handleCode,
                                        executeUserId,
                                        executeTenantId,
                                        receivedDate,
                                        orderType,
                                        reserveDate,
                                        isPrivate,
                                    );

                                    // 返却値編集
                                    outputDto = this.returnEdit(
                                        param,
                                        handleCode,
                                        apiHandleId,
                                        errorMessage,
                                        isPrivate,
                                        receivedDate,
                                    );
                                    return outputDto;
                                } else if (
                                    groupPlansEntity.tpcServicePattern !=
                                        null &&
                                    !String(
                                        groupPlansEntity.tpcServicePattern,
                                    ).equals(
                                        String(
                                            plansEntityList[0]
                                                .tpcServicePattern,
                                        ),
                                    )
                                ) {
                                    // 変数「更新サービスパターン情報」．「更新サービスパターン」!=変数「更新サービスパターン（プラン）」の場合
                                    // ※変数「更新サービスパターン情報」．「更新サービスパターン」=nullの場合、
                                    // または、変数「更新サービスパターン（プラン）」 =nullの場合、両方の値は不同と認識です。
                                    // ロールバック
                                    await tx.rollback();
                                    // MAWP保守 #604対応 変更 END
                                    // エラー処理の「■出力ログ内容編集」の項番10より編集し、ログ出力
                                    this.context.log(
                                        "LineGroupModifyAcquisitionService: TpcServicePattern of groupPlansEntity does not match tpcServicePattern of plansEntityList",
                                        groupPlansEntity,
                                        plansEntityList[0]
                                    )
                                    super.warn(
                                        tenantId,
                                        sequenceNo,
                                        MsgKeysConstants.APGMDW0101,
                                    );
                                    // 変数「処理コード」に「140101」を設定する
                                    handleCode = ResultCdConstants.CODE_140101;
                                    // SO管理共通を呼び出す
                                    await this.soManagementCommon(
                                        param,
                                        apiHandleId,
                                        handleCode,
                                        executeUserId,
                                        executeTenantId,
                                        receivedDate,
                                        orderType,
                                        reserveDate,
                                        isPrivate,
                                    );
                                    // 返却値編集
                                    outputDto = this.returnEdit(
                                        param,
                                        handleCode,
                                        apiHandleId,
                                        errorMessage,
                                        isPrivate,
                                        receivedDate,
                                    );
                                    return outputDto;
                                }
                            } else if (
                                plansEntityList[0].tpcServicePattern === null
                            ) {
                                // ※変数「更新サービスパターン情報」．「更新サービスパターン」=nullの場合、
                                // または、変数「更新サービスパターン（プラン）」 =nullの場合、両方の値は不同と認識です。
                                // MAWP保守 #604対応 変更 START
                                // ロールバック
                                await tx.rollback();
                                // MAWP保守 #604対応 変更 END
                                // エラー処理の「■出力ログ内容編集」の項番10より編集し、ログ出力
                                this.context.log(
                                    "LineGroupModifyAcquisitionService: tpcServicePattern is null",
                                    plansEntityList[0]
                                )
                                super.warn(
                                    tenantId,
                                    sequenceNo,
                                    MsgKeysConstants.APGMDW0101,
                                );
                                // 変数「処理コード」に「140101」を設定する
                                handleCode = ResultCdConstants.CODE_140101;

                                // SO管理共通を呼び出す
                                await this.soManagementCommon(
                                    param,
                                    apiHandleId,
                                    handleCode,
                                    executeUserId,
                                    executeTenantId,
                                    receivedDate,
                                    orderType,
                                    reserveDate,
                                    isPrivate,
                                );

                                // 返却値編集
                                outputDto = this.returnEdit(
                                    param,
                                    handleCode,
                                    apiHandleId,
                                    errorMessage,
                                    isPrivate,
                                    receivedDate,
                                );
                                return outputDto;
                            }
                            basicCapacity = plansEntityList[0].basicCapacity;
                        }

                        if (
                            Constants.ORDER_TYPE_0.equals(orderType) ||
                            Constants.ORDER_TYPE_2.equals(orderType)
                        ) {
                            let tpcConnectionResults: [
                                boolean,
                                string,
                                string,
                            ] = null;
                            try {
                                tpcConnectionResults =
                                    await this.tenantManage.checkTpcConnection(
                                        tenantId,
                                    );
                            } catch (e) {
                                if (isSQLException(e)) {
                                    // DBアクセスリトライエラーの場合
                                    handleCode = ResultCdConstants.CODE_141111;
                                }
                                throw e;
                            }
                            // 変数「TPC情報取得結果」より判断する
                            if (!tpcConnectionResults[0]) {
                                // MAWP保守 #604対応 変更 START
                                // ロールバック
                                await tx.rollback();
                                canRollback = false;
                                // MAWP保守 #604対応 変更 END
                                // エラー処理の「■出力ログ内容編集」の項番34より編集し、ログ出力
                                super.warn(
                                    tenantId,
                                    sequenceNo,
                                    MsgKeysConstants.APGMDW0104,
                                    tenantId,
                                );
                                // 変数「処理コード」に「141111」を設定する
                                handleCode = ResultCdConstants.CODE_141111;
                                // SO管理共通を呼び出す
                                await this.soManagementCommon(
                                    param,
                                    apiHandleId,
                                    handleCode,
                                    executeUserId,
                                    executeTenantId,
                                    receivedDate,
                                    orderType,
                                    reserveDate,
                                    isPrivate,
                                );
                                // 返却値編集
                                outputDto = this.returnEdit(
                                    param,
                                    handleCode,
                                    apiHandleId,
                                    errorMessage,
                                    isPrivate,
                                    receivedDate,
                                );
                                return outputDto;
                            }

                            let telegramResult: SOAPCommonOutputDto = null;
                            try {
                                telegramResult = await this.makeSOAPTelegram(
                                    lineGroupId,
                                    tenantId,
                                    sequenceNo,
                                    lineNo,
                                    totalVolumeControlFlag,
                                    tpcConnectionResults[1],
                                    true,
                                );
                            } catch (e) {
                                if (SOAPException.isSOAPException(e)) {
                                    // ロールバック
                                    await tx.rollback();
                                    canRollback = false;
                                    // エラー処理の「■出力ログ内容編集」の項番11より編集し、ログ出力
                                    this.context.log(
                                        "LineGroupModifyAcquisitionService: SOAPException occured",
                                        e
                                    )
                                    super.warn(
                                        tenantId,
                                        sequenceNo,
                                        MsgKeysConstants.APGMDW0103,
                                    );
                                    // 変数「処理コード」に「140103」を設定する
                                    handleCode = ResultCdConstants.CODE_140103;
                                    // SO管理共通を呼び出す
                                    await this.soManagementCommon(
                                        param,
                                        apiHandleId,
                                        handleCode,
                                        executeUserId,
                                        executeTenantId,
                                        receivedDate,
                                        orderType,
                                        reserveDate,
                                        isPrivate,
                                    );
                                    // 返却値編集
                                    outputDto = this.returnEdit(
                                        param,
                                        handleCode,
                                        apiHandleId,
                                        errorMessage,
                                        isPrivate,
                                        receivedDate,
                                    );
                                    return outputDto;
                                } else {
                                    throw e;
                                }
                            }

                            let document: Document = telegramResult.getDoc();
                            let resultStr =
                                this.sOAPCommonCoupon.getNodeContent(
                                    document,
                                    "//Result",
                                );
                            // 変数「回線グループ割当結果」の「Result」結果がNGの場合
                            if ("NG".equals(resultStr)) {
                                // ロールバック
                                await tx.rollback();
                                canRollback = false;
                                // エラー処理の「■出力ログ内容編集」の項番11より編集し、ログ出力
                                this.context.log(
                                    "LineGroupModifyAcquisitionService: SOAP response is NG",
                                    document,
                                    resultStr
                                )
                                super.warn(
                                    tenantId,
                                    sequenceNo,
                                    MsgKeysConstants.APGMDW0103,
                                );
                                // 変数「処理コード」に「140103」を設定する
                                handleCode = ResultCdConstants.CODE_140103;
                                // SO管理共通を呼び出す
                                await this.soManagementCommon(
                                    param,
                                    apiHandleId,
                                    handleCode,
                                    executeUserId,
                                    executeTenantId,
                                    receivedDate,
                                    orderType,
                                    reserveDate,
                                    isPrivate,
                                );
                                // 返却値編集
                                outputDto = this.returnEdit(
                                    param,
                                    handleCode,
                                    apiHandleId,
                                    errorMessage,
                                    isPrivate,
                                    receivedDate,
                                );
                                return outputDto;
                            }

                            // セカンダリTPCへSOAP連携
                            if (tpcConnectionResults[2] !== null) {
                                // セカンダリTPCが設定されている場合は以下の処理を行う(電文はプライマリと同じものを使用)
                                try {
                                    telegramResult =
                                        await this.makeSOAPTelegram(
                                            lineGroupId,
                                            tenantId,
                                            sequenceNo,
                                            lineNo,
                                            totalVolumeControlFlag,
                                            tpcConnectionResults[2],
                                            false,
                                        );

                                    if (
                                        ResultCdConstants.CODE_000951.equals(
                                            telegramResult.getProcessCode(),
                                        )
                                    ) {
                                        // SG取得でNGの場合
                                        this.context.log(
                                            "LineGroupModifyAcquisitionService: secondary TPC SG取得NG",
                                            tenantId,
                                            sequenceNo,
                                            "SG取得NG"
                                        )
                                        super.warn(
                                            tenantId,
                                            sequenceNo,
                                            MsgKeysConstants.APGMDW0106,
                                            "SG取得NG",
                                        );
                                    } else {
                                        document = telegramResult.getDoc();
                                        resultStr =
                                            this.sOAPCommonCoupon.getNodeContent(
                                                document,
                                                "//Result",
                                            );
                                        // 変数「回線グループ割当結果」の「Result」結果がNGの場合
                                        if (
                                            telegramResult.isError() ||
                                            "NG".equals(resultStr)
                                        ) {
                                            // エラー処理の「■出力ログ内容編集」の項番11より編集し、ログ出力
                                            const errInfo =
                                                this.sOAPCommonCoupon.getNodeContent(
                                                    document,
                                                    "//ErrorInfomation",
                                                );
                                            this.context.log(
                                                "LineGroupModifyAcquisitionService: SOAP response is NG",
                                                resultStr,
                                                errInfo
                                            )
                                            super.warn(
                                                tenantId,
                                                sequenceNo,
                                                MsgKeysConstants.APGMDW0106,
                                                errInfo,
                                            );
                                        }
                                    }
                                } catch (e) {
                                    if (SOAPException.isSOAPException(e)) {
                                        this.context.log(
                                            "LineGroupModifyAcquisitionService: SOAPException occured",
                                            e
                                        )
                                        super.warn(
                                            e,
                                            tenantId,
                                            sequenceNo,
                                            MsgKeysConstants.APGMDW0106,
                                            e.toString(),
                                        );
                                    }
                                }
                            }
                            // 社内テナント種別チェックを行う
                            if (
                                tenantsEntity.tenantType === null ||
                                tenantsEntity.tenantType !== 1
                            ) {
                                // 回線回線グループ挿入を行う
                                try {
                                    await this.aPILinesGroupDAO.insertLinesGroup(
                                        lineNo,
                                        lineGroupId,
                                        basicCapacity,
                                        lineUsageStatus,
                                        tx,
                                    );
                                } catch (e) {
                                    if (isSQLException(e)) {
                                        // DBアクセスリトライエラーの場合
                                        handleCode =
                                            ResultCdConstants.CODE_140104;
                                        throw e;
                                    } else {
                                        // MAWP保守 #604対応 変更 START
                                        // ロールバック
                                        await tx.rollback();
                                        canRollback = false;
                                        // MAWP保守 #604対応 変更 END
                                        // エラー処理の「■出力ログ内容編集」の項番12より編集し、ログ出力
                                        super.error(
                                            tenantId,
                                            sequenceNo,
                                            MsgKeysConstants.APGMDE0014,
                                        );
                                        // 変数「処理コード」に「140104」を設定する
                                        handleCode =
                                            ResultCdConstants.CODE_140104;

                                        // SO管理共通を呼び出す
                                        await this.soManagementCommon(
                                            param,
                                            apiHandleId,
                                            handleCode,
                                            executeUserId,
                                            executeTenantId,
                                            receivedDate,
                                            orderType,
                                            reserveDate,
                                            isPrivate,
                                        );
                                        // 返却値編集
                                        outputDto = this.returnEdit(
                                            param,
                                            handleCode,
                                            apiHandleId,
                                            errorMessage,
                                            isPrivate,
                                            receivedDate,
                                        );

                                        return outputDto;
                                    }
                                }
                            }
                        }

                        // トランザクション終了
                        await tx.commit();
                        commitFlag = true;
                        this.context.log(
                            "LineGroupModifyAcquisitionService.service transaction committed",
                        );
                        // リトライしたタイミングでエラーコードを設定しているので
                        // リトライ後に正常となった場合は正常コードに戻す
                        handleCode = ResultCdConstants.CODE_000000;
                        return null;
                    },
                    this.apDbRetryMaxCnt,
                    this.apDbRetryInterval,
                );
                if (result !== null) {
                    return result;
                }
            }
            else if ("0".equals(operation)) {
                // 入力パラメータ．「操作」=0（割当解除）の場合
                /** #159横展開対応 追加 END */

                // 回線ＩＤ確認
                // 社内テナント種別チェックを行う
                if (
                    tenantsEntity.tenantType === null ||
                    tenantsEntity.tenantType !== 1
                ) {
                    // 回線回線グループテーブルから「回線ＩＤ」を取得する
                    let lineLineGroupsEntity: LineLineGroupsEntity = null;
                    try {
                        lineLineGroupsEntity =
                            await this.aPILinesGroupDAO.getLineIdGroup(
                                lineNo,
                                lineGroupId,
                            );
                    } catch (e) {
                        if (isSQLException(e)) {
                            // DBアクセスリトライエラーの場合
                            handleCode = ResultCdConstants.CODE_140201;
                        }
                        throw e;
                    }
                    if (lineLineGroupsEntity === null) {
                        // エラー処理の「■出力ログ内容編集」の項番13より編集し、ログ出力
                        super.warn(
                            tenantId,
                            sequenceNo,
                            MsgKeysConstants.APGMDW0201,
                        );
                        // 変数「処理コード」に「140201」を設定する
                        handleCode = ResultCdConstants.CODE_140201;
                        // SO管理共通を呼び出す
                        await this.soManagementCommon(
                            param,
                            apiHandleId,
                            handleCode,
                            executeUserId,
                            executeTenantId,
                            receivedDate,
                            orderType,
                            reserveDate,
                            isPrivate,
                        );
                        // 返却値編集
                        outputDto = this.returnEdit(
                            param,
                            handleCode,
                            apiHandleId,
                            errorMessage,
                            isPrivate,
                            receivedDate,
                        );
                        return outputDto;
                    }
                }

                if (
                    Constants.ORDER_TYPE_0.equals(orderType) ||
                    Constants.ORDER_TYPE_2.equals(orderType)
                ) {
                    let tpcConnectionResults: [boolean, string, string] = null;
                    try {
                        tpcConnectionResults =
                            await this.tenantManage.checkTpcConnection(
                                tenantId,
                            );
                    } catch (e) {
                        if (isSQLException(e)) {
                            // DBアクセスリトライエラーの場合
                            handleCode = ResultCdConstants.CODE_140204;
                        }
                        throw e;
                    }

                    if (!tpcConnectionResults[0]) {
                        // エラー処理の「■出力ログ内容編集」の項番35より編集し、ログ出力
                        super.warn(
                            tenantId,
                            sequenceNo,
                            MsgKeysConstants.APGMDW0204,
                            tenantId,
                        );
                        // 変数「処理コード」に「140204」を設定する
                        handleCode = ResultCdConstants.CODE_140204;
                        // SO管理共通を呼び出す
                        await this.soManagementCommon(
                            param,
                            apiHandleId,
                            handleCode,
                            executeUserId,
                            executeTenantId,
                            receivedDate,
                            orderType,
                            reserveDate,
                            isPrivate,
                        );
                        // 返却値編集
                        outputDto = this.returnEdit(
                            param,
                            handleCode,
                            apiHandleId,
                            errorMessage,
                            isPrivate,
                            receivedDate,
                        );
                        return outputDto;
                    }

                    let telegramResult: SOAPCommonOutputDto = null;
                    try {
                        telegramResult = await this.makeSOAPTelegram(
                            "0",
                            tenantId,
                            sequenceNo,
                            lineNo,
                            totalVolumeControlFlag,
                            tpcConnectionResults[1],
                            true,
                        );
                    } catch (e) {
                        if (SOAPException.isSOAPException(e)) {
                            // エラー処理の「■出力ログ内容編集」の項番14より編集し、ログ出力
                            this.context.log(
                                "LineGroupModifyAcquisitionService: SOAPException occured",
                                e
                            )
                            super.warn(
                                tenantId,
                                sequenceNo,
                                MsgKeysConstants.APGMDW0202,
                            );
                            // 変数「処理コード」に「140202」を設定する
                            handleCode = ResultCdConstants.CODE_140202;
                            // SO管理共通を呼び出す
                            await this.soManagementCommon(
                                param,
                                apiHandleId,
                                handleCode,
                                executeUserId,
                                executeTenantId,
                                receivedDate,
                                orderType,
                                reserveDate,
                                isPrivate,
                            );
                            // 返却値編集
                            outputDto = this.returnEdit(
                                param,
                                handleCode,
                                apiHandleId,
                                errorMessage,
                                isPrivate,
                                receivedDate,
                            );
                            return outputDto;
                        } else {
                            throw e;
                        }
                    }

                    let document = telegramResult.getDoc();

                    let resultStr: string =
                        this.sOAPCommonCoupon.getNodeContent(
                            document,
                            "//Result",
                        );

                    // 変数「回線グループ割当結果」の「Result」結果がNGの場合
                    if ("NG".equals(resultStr)) {
                        // エラー処理の「■出力ログ内容編集」の項番14より編集し、ログ出力
                        this.context.log(
                            "LineGroupModifyAcquisitionService: SOAP response is NG",
                            document,
                            resultStr
                        )
                        super.warn(
                            tenantId,
                            sequenceNo,
                            MsgKeysConstants.APGMDW0202,
                        );
                        // 変数「処理コード」に「140202」を設定する
                        handleCode = ResultCdConstants.CODE_140202;
                        // SO管理共通を呼び出す
                        await this.soManagementCommon(
                            param,
                            apiHandleId,
                            handleCode,
                            executeUserId,
                            executeTenantId,
                            receivedDate,
                            orderType,
                            reserveDate,
                            isPrivate,
                        );
                        // 返却値編集
                        outputDto = this.returnEdit(
                            param,
                            handleCode,
                            apiHandleId,
                            errorMessage,
                            isPrivate,
                            receivedDate,
                        );
                        return outputDto;
                    }

                    // セカンダリTPCへSOAP連携
                    if (tpcConnectionResults[2] !== null) {
                        // セカンダリTPCが設定されている場合は以下の処理を行う(電文はプライマリと同じものを使用)
                        try {
                            telegramResult = await this.makeSOAPTelegram(
                                "0",
                                tenantId,
                                sequenceNo,
                                lineNo,
                                totalVolumeControlFlag,
                                tpcConnectionResults[2],
                                false,
                            );

                            if (
                                ResultCdConstants.CODE_000951.equals(
                                    telegramResult.getProcessCode(),
                                )
                            ) {
                                // SG取得でNGの場合
                                this.context.log(
                                    "LineGroupModifyAcquisitionService: SOAP SG取得 NGの場合",
                                    tenantId,
                                    sequenceNo,
                                )
                                super.warn(
                                    tenantId,
                                    sequenceNo,
                                    MsgKeysConstants.APGMDW0205,
                                    "SG取得NG",
                                );
                            } else {
                                document = telegramResult.getDoc();
                                resultStr =
                                    this.sOAPCommonCoupon.getNodeContent(
                                        document,
                                        "//Result",
                                    );
                                // 変数「回線グループ割当結果」の「Result」結果がNGの場合
                                if (
                                    telegramResult.isError() ||
                                    "NG".equals(resultStr)
                                ) {
                                    // エラー処理の「■出力ログ内容編集」の項番11より編集し、ログ出力
                                    const errInfo =
                                        this.sOAPCommonCoupon.getNodeContent(
                                            document,
                                            "//ErrorInfomation",
                                        );
                                    this.context.log(
                                        "LineGroupModifyAcquisitionService: SOAP response is NG",
                                        errInfo
                                    )
                                    super.warn(
                                        tenantId,
                                        sequenceNo,
                                        MsgKeysConstants.APGMDW0205,
                                        errInfo,
                                    );
                                }
                            }
                        } catch (e) {
                            if (SOAPException.isSOAPException(e)) {
                                this.context.log(
                                    "LineGroupModifyAcquisitionService: SKOAPException occured",
                                    e
                                )
                                super.warn(
                                    e,
                                    tenantId,
                                    sequenceNo,
                                    MsgKeysConstants.APGMDW0205,
                                    e,
                                );
                            } else {
                                throw e;
                            }
                        }
                    }

                    // 回線回線グループテーブル削除を行う
                    // 社内テナント種別チェックを行う
                    if (
                        tenantsEntity.tenantType === null ||
                        tenantsEntity.tenantType !== 1
                    ) {
                        try {
                            await this.aPILinesGroupDAO.deleteLinesGroup(
                                lineNo,
                                lineGroupId,
                            );
                            // STEP20.0版対応 追加 START
                        } catch (e) {
                            if (isSQLException(e)) {
                                // DBアクセスリトライエラーの場合
                                handleCode = ResultCdConstants.CODE_140203;
                                throw e;
                            } else {
                                // エラー処理の「■出力ログ内容編集」の項番15より編集し、ログ出力
                                super.warn(
                                    tenantId,
                                    sequenceNo,
                                    MsgKeysConstants.APGMDW0203,
                                );
                                // 変数「処理コード」に「140202」を設定する
                                handleCode = ResultCdConstants.CODE_140203;
                                // SO管理共通を呼び出す
                                await this.soManagementCommon(
                                    param,
                                    apiHandleId,
                                    handleCode,
                                    executeUserId,
                                    executeTenantId,
                                    receivedDate,
                                    orderType,
                                    reserveDate,
                                    isPrivate,
                                );
                                // 返却値編集
                                outputDto = this.returnEdit(
                                    param,
                                    handleCode,
                                    apiHandleId,
                                    errorMessage,
                                    isPrivate,
                                    receivedDate,
                                );
                                return outputDto;
                            }
                        }
                    }
                }
            }

            // csv start

            if (
                Constants.ORDER_TYPE_0.equals(orderType) ||
                Constants.ORDER_TYPE_1.equals(orderType)
            ) {
                // 変数「社内テナント情報」.[社内フラグ]より判定する
                if (
                    !tenantsEntity.office ||
                    (tenantsEntity.office && !"1".equals(csvUnnecessaryFlag))
                ) {
                    // 入力パラメータ．「操作」=0（割当解除）の場合
                    if ("0".equals(operation)) {
                        // テナント管理を呼び出す
                        try {
                            checkResult = await this.tenantManage.doCheck(
                                lineNo,
                                tenantId,
                            );
                        } catch (e) {
                            if (isSQLException(e)) {
                                // DBアクセスリトライエラーの場合
                                // 変数「CSV作成要否」を0で処理する
                                csvOutputKbn = "0";
                                // ログ出力
                                this.context.log(
                                    "LineGroupModifyAcquisitionService: tenantManage.doCheck sqlException",
                                    lineNo,
                                    tenantId,
                                    e
                                )
                                super.error(
                                    e,
                                    tenantId,
                                    sequenceNo,
                                    MsgKeysConstants.APCOME0402,
                                    functionType,
                                );
                                // 変数「処理コード」に「000000」を設定
                                handleCode = ResultCdConstants.CODE_000000;
                                // SO管理共通を呼び出す
                                await this.soManagementCommon(
                                    param,
                                    apiHandleId,
                                    handleCode,
                                    executeUserId,
                                    executeTenantId,
                                    receivedDate,
                                    orderType,
                                    reserveDate,
                                    isPrivate,
                                );
                                // 返却値編集
                                outputDto = this.returnEdit(
                                    param,
                                    handleCode,
                                    apiHandleId,
                                    errorMessage,
                                    isPrivate,
                                    receivedDate,
                                );
                                return outputDto;
                            }
                            throw e;
                        }
                    }
                    // 代表N番の判定する
                    if (
                        ("0".equals(operation) && !checkResult[0]) ||
                        StringUtils.isEmpty(checkResult[1])
                    ) {
                        // 変数「CSV作成要否」を0で処理する
                        csvOutputKbn = "0";
                        /** #378対応 変更 START */
                        // // ロールバック
                        // TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                        this.context.log('代表N番の判定する', { operation, checkResult0: checkResult[0], checkResult1: checkResult[1] });
                        if ("1".equals(operation)) {
                            // エラー処理の「■出力ログ内容編集」の項番26より編集し、ログ出力
                            super.error(
                                tenantId,
                                sequenceNo,
                                MsgKeysConstants.APGMDE1603,
                                tenantId,
                            );
                            // 変数「処理コード」に「000000」を設定
                            handleCode = ResultCdConstants.CODE_000000;
                        } else {
                            // エラー処理の「■出力ログ内容編集」の項番30より編集し、ログ出力
                            super.error(
                                tenantId,
                                sequenceNo,
                                MsgKeysConstants.APGMDE5603,
                                tenantId,
                            );
                            // 変数「処理コード」に「000000」を設定
                            handleCode = ResultCdConstants.CODE_000000;
                        }
                        // SO管理共通を呼び出す
                        await this.soManagementCommon(
                            param,
                            apiHandleId,
                            handleCode,
                            executeUserId,
                            executeTenantId,
                            receivedDate,
                            orderType,
                            reserveDate,
                            isPrivate,
                        );
                        // 返却値編集
                        outputDto = this.returnEdit(
                            param,
                            handleCode,
                            apiHandleId,
                            errorMessage,
                            isPrivate,
                            receivedDate,
                        );
                        return outputDto;
                    }

                    /*
                     * // 変数「帯域卸フラグ」取得 List<Boolean> wholeFlagList =
                     * aPILinesDAO.getWholeFlag(tenantId, checkResult[1]); if (wholeFlagList == null
                     * || wholeFlagList.size() == 0 || wholeFlagList.get(0) == null ||
                     * !wholeFlagList.get(0)) {
                     */

                    // 送信元システムIDのチェック
                    // 入力パラメータ．「ヘッダー情報」．「送信元システムID」が「9a1z」以外の場合：
                    if (
                        !tenantsEntity.office ||
                        (tenantsEntity.office && !"9a1z".equals(senderSystemId))
                    ) {
                        // 変数「プランID」取得
                        let planIdLst: number[] = null;
                        try {
                            planIdLst = await this.aPILinesGroupDAO.getPlanId(
                                lineGroupId,
                            );
                        } catch (e) {
                            if (isSQLException(e)) {
                                // DBアクセスリトライエラーの場合
                                // 変数「CSV作成要否」を0で処理する
                                csvOutputKbn = "0";
                                // ログ出力
                                this.context.log(
                                    "LineGroupModifyAcquisitionService: getPlanId sqlException",
                                    lineGroupId,
                                    e
                                )
                                super.error(
                                    e,
                                    tenantId,
                                    sequenceNo,
                                    MsgKeysConstants.APCOME0402,
                                    functionType,
                                );
                                // 変数「処理コード」に「000000」を設定
                                handleCode = ResultCdConstants.CODE_000000;
                                // SO管理共通を呼び出す
                                await this.soManagementCommon(
                                    param,
                                    apiHandleId,
                                    handleCode,
                                    executeUserId,
                                    executeTenantId,
                                    receivedDate,
                                    orderType,
                                    reserveDate,
                                    isPrivate,
                                );
                                // 返却値編集
                                outputDto = this.returnEdit(
                                    param,
                                    handleCode,
                                    apiHandleId,
                                    errorMessage,
                                    isPrivate,
                                    receivedDate,
                                );
                                return outputDto;
                            }
                            throw e;
                        }

                        let planId: string = null;
                        if (
                            planIdLst !== null &&
                            planIdLst.length !== 0 &&
                            planIdLst[0] !== null
                        ) {
                            planId = planIdLst[0].toString();
                        }
                        if (
                            planIdLst === null ||
                            planIdLst.length === 0 ||
                            !Check.checkCsvplanIdFmt(planId)
                        ) {
                            // 変数「CSV作成要否」を0で処理する
                            csvOutputKbn = "0";
                            // // ロールバック
                            // TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                            if ("1".equals(operation)) {
                                // エラー処理の「■出力ログ内容編集」の項番27より編集し、ログ出力
                                super.error(
                                    tenantId,
                                    sequenceNo,
                                    MsgKeysConstants.APGMDE1605,
                                    lineGroupId,
                                );
                                // 変数「処理コード」に「000000」を設定
                                handleCode = ResultCdConstants.CODE_000000;
                            } else {
                                // エラー処理の「■出力ログ内容編集」の項番31より編集し、ログ出力
                                super.error(
                                    tenantId,
                                    sequenceNo,
                                    MsgKeysConstants.APGMDE5605,
                                    lineGroupId,
                                );
                                // 変数「処理コード」に「000000」を設定
                                handleCode = ResultCdConstants.CODE_000000;
                            }
                            // SO管理共通を呼び出す
                            await this.soManagementCommon(
                                param,
                                apiHandleId,
                                handleCode,
                                executeUserId,
                                executeTenantId,
                                receivedDate,
                                orderType,
                                reserveDate,
                                isPrivate,
                            );
                            // 返却値編集
                            outputDto = this.returnEdit(
                                param,
                                handleCode,
                                apiHandleId,
                                errorMessage,
                                isPrivate,
                                receivedDate,
                            );
                            return outputDto;
                        }

                        // 変数「プラン種別」取得
                        let planClasslst: number[] = null;
                        try {
                            planClasslst =
                                await this.aPILinesGroupDAO.getPlanClass(
                                    planIdLst[0],
                                );
                        } catch (e) {
                            if (isSQLException(e)) {
                                // DBアクセスリトライエラーの場合
                                // 変数「CSV作成要否」を0で処理する
                                csvOutputKbn = "0";
                                // ログ出力
                                this.context.log(
                                    "LineGroupModifyAcquisitionService: getPlanClass sqlException",
                                    planIdLst[0],
                                    e
                                )
                                super.error(
                                    e,
                                    tenantId,
                                    sequenceNo,
                                    MsgKeysConstants.APCOME0402,
                                    functionType,
                                );
                                // 変数「処理コード」に「000000」を設定
                                handleCode = ResultCdConstants.CODE_000000;
                                // SO管理共通を呼び出す
                                await this.soManagementCommon(
                                    param,
                                    apiHandleId,
                                    handleCode,
                                    executeUserId,
                                    executeTenantId,
                                    receivedDate,
                                    orderType,
                                    reserveDate,
                                    isPrivate,
                                );
                                // 返却値編集
                                outputDto = this.returnEdit(
                                    param,
                                    handleCode,
                                    apiHandleId,
                                    errorMessage,
                                    isPrivate,
                                    receivedDate,
                                );
                                return outputDto;
                            }
                        }
                        // 変数「プラン種別」の件数０件の場合、あるいば、変数「プラン種別」"1"もしくは"2"でない場合
                        if (
                            planClasslst === null ||
                            planClasslst.length === 0 ||
                            planClasslst[0] === null ||
                            (planClasslst[0] !== 1 && planClasslst[0] !== 2)
                        ) {
                            // 変数「CSV作成要否」を0で処理する
                            csvOutputKbn = "0";
                            // // ロールバック
                            // TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                            if ("1".equals(operation)) {
                                // エラー処理の「■出力ログ内容編集」の項番28より編集し、ログ出力
                                super.error(
                                    tenantId,
                                    sequenceNo,
                                    MsgKeysConstants.APGMDE1606,
                                    String(planIdLst[0]),
                                );
                                // 変数「処理コード」に「000000」を設定
                                handleCode = ResultCdConstants.CODE_000000;
                            } else {
                                // エラー処理の「■出力ログ内容編集」の項番28より編集し、ログ出力
                                super.error(
                                    tenantId,
                                    sequenceNo,
                                    MsgKeysConstants.APGMDE5606,
                                    String(planIdLst[0]),
                                );
                                // 変数「処理コード」に「000000」を設定
                                handleCode = ResultCdConstants.CODE_000000;
                            }
                            // SO管理共通を呼び出す
                            await this.soManagementCommon(
                                param,
                                apiHandleId,
                                handleCode,
                                executeUserId,
                                executeTenantId,
                                receivedDate,
                                orderType,
                                reserveDate,
                                isPrivate,
                            );
                            // 返却値編集
                            outputDto = this.returnEdit(
                                param,
                                handleCode,
                                apiHandleId,
                                errorMessage,
                                isPrivate,
                                receivedDate,
                            );
                            return outputDto;
                        } else {
                            // 変数「プラン種別」
                            planClass = planClasslst[0];
                        }
                        // 変数「CSV作成要否」を1で処理する
                        csvOutputKbn = "1";
                    } else {
                        // 変数「CSV作成要否」を0で処理する
                        csvOutputKbn = "0";
                    }

                    // 【STEP3.1】if (wholeFlagList == null ～ に対応する else の削除
                    /*
                     * } else { // 変数「CSV作成要否」を0で処理する csvOutputKbn = "0"; }
                     */
                } else {
                    // 変数「CSV作成要否」を0で処理する
                    csvOutputKbn = "0";
                }
                nNo = tenantsEntity.tenantType === 5 ? null : checkResult[1];
            } else {
                // 変数「CSV作成要否」を0で処理する
                csvOutputKbn = "0";
            }
            // SO管理共通を呼び出す
            await this.soManagementCommon(
                param,
                apiHandleId,
                handleCode,
                executeUserId,
                executeTenantId,
                receivedDate,
                orderType,
                reserveDate,
                isPrivate,
            );
            // 返却値編集
            outputDto = this.returnEdit(
                param,
                handleCode,
                apiHandleId,
                errorMessage,
                isPrivate,
                receivedDate,
                csvOutputKbn,
                nNo,
                lineNo,
            );

            if (ResultCdConstants.CODE_000000.equals(handleCode)) {
                // 予約前オーダ以外は自動計算回線グループ情報追加処理を行う。
                // 利用状態が2(サスペンド中)以外の場合に自動計算回線グループに追加 ※割当解除時の利用状態は必ず「null」となる
                if (
                    !Constants.ORDER_TYPE_1.equals(orderType) &&
                    (lineUsageStatus == null || lineUsageStatus !== 2)
                ) {
                    await this.addAutoModbucketLineGroup(
                        apiHandleId,
                        functionType,
                        handleCode,
                        lineNo,
                        lineGroupId,
                        tenantId,
                        sequenceNo,
                        orderType,
                    );
                }
            }

            return outputDto;
        } catch (e) {
            if (tx !== null && !commitFlag && canRollback) {
                // トランザクション開始中にエラーの場合、ロールバック
                await tx.rollback();
            }
            if (isSQLException(e)) {
                // DBアクセスリトライエラーの場合
                // リターンコード値は各エラー時にて設定済み
                this.context.log(
                    "LineGroupModifyAcquisitionService: SqlException occured",
                    e
                )
                super.error(
                    e,
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APCOME0402,
                    functionType,
                );
                // SO管理共通を呼び出す
                await this.soManagementCommon(
                    param,
                    apiHandleId,
                    handleCode,
                    executeUserId,
                    executeTenantId,
                    receivedDate,
                    orderType,
                    reserveDate,
                    isPrivate,
                );
                // // 返却値編集
                outputDto = this.returnEdit(
                    param,
                    handleCode,
                    apiHandleId,
                    errorMessage,
                    isPrivate,
                    receivedDate,
                );
                return outputDto;
            } else {
                // MAWP保守 #604対応 追加 END
                handleCode = ResultCdConstants.CODE_999999;
                this.context.log(
                    "LineGroupModifyAcquisitionService: unxepected error occured",
                    e
                )
                super.error(
                    e as Error,
                    param.tenantId,
                    param.requestHeader.sequenceNo,
                    MsgKeysConstants.APCOME0401,
                    e?.message,
                );

                // // SO管理共通を呼び出す
                await this.soManagementCommon(
                    param,
                    apiHandleId,
                    handleCode,
                    executeUserId,
                    executeTenantId,
                    receivedDate,
                    orderType,
                    reserveDate,
                    isPrivate,
                );

                // 返却値編集
                outputDto = this.returnEdit(
                    param,
                    handleCode,
                    apiHandleId,
                    errorMessage,
                    isPrivate,
                    receivedDate,
                );

                return outputDto;
            }
        } finally {
            // MAWP保守 #604対応 追加 END
            super.debug(
                tenantId,
                sequenceNo,
                MsgKeysConstants.APGMDD0002,
                this.getClassName(),
                "service",
            );
        }
    }

    /**
     * SO管理共通を呼び出す
     *
     * @param param           入力オブジェクト
     * @param apiHandleId     API処理ID
     * @param handleCode      処理コード
     * @param executeUserId   実行ユーザID(内部から呼出す時に、nullではない、外部から呼び出す時に、nullである)
     * @param executeTenantId 実行テナントID(内部から呼出す時に、nullではない、外部から呼び出す時に、nullである)
     * @param receivedDate    REST API共通処理が呼び出されたシステム日付
     * @param orderType
     * @param reserveDate
     * @param isPrivate
     * @throws Exception
     */
    private async soManagementCommon(
        param: LineGroupModifyAcquisitionInputDto,
        apiHandleId: string,
        handleCode: string,
        executeUserId: string,
        executeTenantId: string,
        receivedDate: string,
        orderType: string,
        reserveDate: string,
        isPrivate: boolean,
    ) {
        // SO管理オブジェクトを定義し、初期化をする。
        const soObject = new SOObject();

        // サービスオーダID
        soObject.setServiceOrderId(apiHandleId);
        // 申込日
        soObject.setOrderDate(receivedDate);
        // 予約日
        // 変数「オーダ種別」は「予約前オーダ」の場合
        if (Constants.ORDER_TYPE_1.equals(orderType)) {
            soObject.setReserveDate(reserveDate);
        } else {
            soObject.setReserveDate(null);
        }
        // オーダ種別
        soObject.setOrderType(orderType);
        // 完了日
        // 変数「オーダ種別」は「即時オーダ」の場合
        if (
            Constants.ORDER_TYPE_0.equals(orderType) ||
            Constants.ORDER_TYPE_2.equals(orderType)
        ) {
            soObject.setExecDate(MvnoUtil.getDateTimeNow());
        } else {
            soObject.setExecDate(null);
        }
        // オーダステータス
        soObject.setOrderStatus(handleCode);
        // 回線ID
        soObject.setLineId(param.lineNo);
        // 回線グループID
        soObject.setLineGroupId(getStringParameter(param.lineGroupId));
        // 所属テナントID
        soObject.setTenantId(param.tenantId);
        // 実行ユーザID
        soObject.setExecuteUserId(executeUserId);
        // 実行テナントID
        soObject.setExecuteTenantId(executeTenantId);
        // 機能種別
        soObject.setFunctionType(param.requestHeader.functionType);
        // 操作区分
        soObject.setOperationDivision(getStringParameter(param.operation));
        // 変更前プランID
        soObject.setChangeOldplanId("");
        // 変更後プランID
        soObject.setChangeNewplanId("");
        // 回線所属変更回線ID
        soObject.setChangePlanId(param.lineNo);
        // 基本容量
        soObject.setBasicCapacity("");
        // オプションプランID
        soObject.setOptionPlanId("");
        // 内部呼び出し
        soObject.setInternalFlag(isPrivate);
        // REST電文
        // 変数「オーダ種別」は「予約前オーダ」の場合
        if (Constants.ORDER_TYPE_1.equals(orderType)) {
            soObject.setRestMessage(MvnoUtil.getRestMessage(param));
        } else {
            soObject.setRestMessage(null);
        }
        await this.soCommon.soCommon(soObject);
    }

    /**
     * ＴＰＣ電文作成した、SOAPのAPIを接続する
     *
     * @param lineGroupId            回線グループID
     * @param tenantId               テナントID
     * @param sequenceNo             送信番号
     * @param lineNo                 回線番号
     * @param totalVolumeControlFlag 回線総量制限サービス可否
     * @param tpcInfo                TPC情報
     * @param primaryFlg             プライマリフラグ
     * @return String 回線グループ割当結果
     * @throws Exception 送信異常
     */
    private async makeSOAPTelegram(
        lineGroupId: string,
        tenantId: string,
        sequenceNo: string,
        lineNo: string,
        totalVolumeControlFlag: string,
        tpcInfo: string,
        primaryFlg: boolean,
    ): Promise<SOAPCommonOutputDto> {
        // 回線グループ所属回線変更データ情報結果初期化
        const document: Document = null;
        const getCapPolnumParam1List: ParameterName[] = [];
        const getCapPolnumParam2List: ParameterName[] = [];

        // パラメータ名称リスト1を設定
        // msisdn、入力パラメータ．「回線番号」
        // ※回線番号をmsisdnにするには、電話番号の先頭の0を国番号に変換して付与する。 08012345678 → 818012345678
        let parameter1 = new ParameterName();
        parameter1.setName("msisdn");
        const value11 = "81" + lineNo.substring(1);
        parameter1.setValue(value11);
        getCapPolnumParam1List.push(parameter1);
        // grpnum、入力パラメータ．「回線グループＩＤ」
        parameter1 = new ParameterName();
        parameter1.setName("grpnum");
        const value12 = lineGroupId;
        parameter1.setValue(value12);
        getCapPolnumParam1List.push(parameter1);

        // パラメータ名称リスト2を設定
        // "pronum、1010000+入力パラメータ．「回線番号」
        let parameter2 = new ParameterName();
        parameter2.setName("pronum");
        const value21 = "101" + MvnoUtil.addZeroBeforeStr(lineNo, 15);
        parameter2.setValue(value21);
        getCapPolnumParam2List.push(parameter2);
        // aply_srv、入力パラメータ．「回線総量制限サービス可否」"
        parameter2 = new ParameterName();
        parameter2.setName("aply_srv");
        const value22 = totalVolumeControlFlag;
        parameter2.setValue(value22);
        getCapPolnumParam2List.push(parameter2);

        const sOAPCommonOutputDto: SOAPCommonOutputDto =
                await this.sOAPCommonCoupon.callWebSoapApi(
                    "serviceProfileRequest", // パラメータ名称リスト
                    "Mod", // オペレーション種別名称1
                    "Subscriber", // プロファイル名称1
                    getCapPolnumParam1List, // パラメータ名称リスト1
                    "Mod", // オペレーション種別名称2
                    "Service", // プロファイル名称2
                    getCapPolnumParam2List, // パラメータ名称リスト2
                    tenantId, // テナントID
                    sequenceNo, // 送信番号
                    tpcInfo, // TPC情報
                    primaryFlg,
                );
        return sOAPCommonOutputDto;
    }

    /**
     * 返却値編集。<BR>
     *
     * @param pram           回線グループ所属回線変更機能インプット
     * @param rtnHandleCode  処理コード
     * @param rtnapiHandleId API処理ID
     * @param errorMessage   変数「エラーメッセージ」
     * @param isPrivate      内部呼出フラグ
     * @param receivedDate   REST API共通処理が呼び出されたシステム日付
     * @return LineGroupModifyAcquisitionInsideOutputDto/LineGroupModifyAcquisitionOutputDto
     * 回線グループ所属回線変更機能アウトプット(内部/外部)
     */
    private returnEdit(
        pram: LineGroupModifyAcquisitionInputDto,
        rtnHandleCode: string,
        rtnapiHandleId: string,
        errorMessage: string,
        isPrivate: boolean,
        receivedDate: string,
        csvOutputKbn: string = "0",
        nNo: string = null,
        lineNo: string = null,
    ) {
        // ヘッダ情報を定義する
        const responseHeader: ResponseHeader = {
            sequenceNo: pram.requestHeader.sequenceNo, // 送信番号取得
            receivedDate, // 受信日時
            processCode: rtnHandleCode, // 処理コード
            apiProcessID: rtnapiHandleId, // API処理ID
        };
        // 回線グループ所属回線変更機能アウトプット(外部)を定義する
        // ヘッダ情報を設定する
        return {
            jsonBody: {
                responseHeader,
            },
            additionalData: {
                csvOutputKbn,
                nNo,
                csvUnnecessary: csvOutputKbn === "0",
                lineNo: lineNo ?? pram.lineNo,
            },
        };
    }
}
