import "@/types/string.extension";

import config from "config";
import { format, fromUnixTime } from "date-fns";

import Constants from "@/core/constant/Constants";
import MsgKeysConstants from "@/core/constant/MsgKeysConstants";
import ResultCdConstants from "@/core/constant/ResultCdConstants";

import Check from "@/core/common/Check";
import CheckUtil from "@/core/common/CheckUtil";
import MvnoUtil from "@/core/common/MvnoUtil";
import RESTCommon from "@/core/common/RESTCommon";
import SOCommon from "@/core/common/SOCommon";
import TenantManage from "@/core/common/TenantManage";

import APICommonDAO from "@/core/dao/APICommonDAO";
import APILinesDAO from "@/core/dao/APILinesDAO";
import APISoDAO from "@/core/dao/APISoDAO";

import LineDeleteInputDto from "@/core/dto/LineDeleteInputDto";
import LineDeleteOutputDto from "@/core/dto/LineDeleteOutputDto";
import ResponseHeader from "@/core/dto/ResponseHeader";
import SOObject from "@/core/dto/SOObject";

import { LinesEntity } from "@/core/entity/LinesEntity";

import { isSQLException } from "@/helpers/queryHelper";

import { IFService } from "../IFService";
import { getConfig } from "@/helpers/configHelper";
import { getStringParameter } from "@/types/parameter.string";
import { isNone } from "@/utils";

export default class LineDeleteService
    extends RESTCommon
    implements IFService<LineDeleteInputDto, LineDeleteOutputDto>
{
    /**
     * API回線DAO
     */
    private apiLinesDao: APILinesDAO = new APILinesDAO(
        this.request,
        this.context,
    );

    /**
     * API SO DAO
     */
    private aPISoDAO: APISoDAO = new APISoDAO(this.request, this.context);

    /**
     * SO管理共通DAO
     */
    private soCommon: SOCommon = new SOCommon(this.request, this.context);

    /**
     * API共通DAO
     */
    private aPICommonDAO: APICommonDAO = new APICommonDAO(
        this.request,
        this.context,
    );

    /**
     * テナント管理
     */
    private tenantManage: TenantManage = new TenantManage(
        this.request,
        this.context,
    );

    /**
     * 予約実行日時単位(予約機能)
     */
    private reservationDateExecutionUnits: string = getConfig(
        "ReservationDateExecutionUnits",
    );
    /**
     * 予約可能制限日数(予約機能)
     */
    private reservationsLimitDays: string = getConfig("ReservationsLimitDays");
    /**
     * 回線廃止機能。<BR>
     *
     * <PRE>
     * 回線廃止およびMNP転出の申込を卸ポータルフロントにて受付け、
     * 卸ポータルフロントでの処理が完了した後、MVNO顧客管理システムに回線廃止情報を連携する。
     * </PRE>
     *
     * @param param 回線廃止インプット
     * @param clientIPAddress クライアントIP // NOTE added during refactoring
     * @param params 可変長さ引数(内部から呼出す時に使用するパラメータ)
     * @return LineDeleteOutputDto 回線廃止機能アウトプット
     * @throws Exception 処理例外
     */
    async service(
        param: LineDeleteInputDto,
        clientIPAddress: string,
        ...params: any
    ): Promise<LineDeleteOutputDto> {
        const {
            requestHeader: { sequenceNo },
            tenantId,
            targetTenantId,
            lineNo,
            mnpOutFlag,
            /** SO-ID(予約実行呼出し専用) */
            reserve_soId: reserveSoId,
            /** 予約フラグ */
            reserve_flag: reserveFlg,
        } = param;
        const csvUnnecessaryFlag = getStringParameter(param.csvUnnecessaryFlag);
        let { reserve_date: reserveDate } = param;
        // デバッグログ
        super.debug(
            param.tenantId,
            sequenceNo,
            MsgKeysConstants.APLDLD0001,
            this.getClassName(),
            "service",
            JSON.stringify(param),
        );

        if (!CheckUtil.checkIsNotNull(reserveDate)) {
            // STEP8.0版対応　変更　START
            reserveDate += " 02:30";
            // STEP8.0版対応　変更　END
        }

        /** REST API共通処理が呼び出されたシステム日付 */
        const systemDateTime = MvnoUtil.getDateTimeNow();

        /** 返却レスポンスヘッダ生成 */
        const responseHeader = this.context.responseHeader;

        /** 機能種別取得 */
        const functionType = param.requestHeader.functionType;

        // 変数「オーダ種別」
        // String orderType = Check.checkOrderType(reserveDate, reserveFlg, reserveSoId);
        const orderType = Check.checkOrderType(
            reserveDate,
            reserveFlg,
            reserveSoId,
        );

        // STEP20.0版対応　追加　START
        // 変数「処理コード」初期化
        let handleCode = ResultCdConstants.CODE_000000;
        // STEP20.0版対応　追加　END

        // オーダが予約実行かどうかを判定
        if (Constants.ORDER_TYPE_2 === orderType) {
            // APサーバの複数IP取得
            const localIpList = MvnoUtil.getLocalIpList();
            // 予約実行可否チェック
            if (!this.context.isInternalRequest) {
                // ログ出力
                super.error(
                    param.tenantId,
                    sequenceNo,
                    MsgKeysConstants.APCOME0401,
                    `予約実行できませんでした。クライアントIP:${clientIPAddress}, APサーバーIP：${MvnoUtil.getOutputList(
                        localIpList,
                    )}`,
                );
                // 返却値を設定し、返却
                responseHeader.processCode = ResultCdConstants.CODE_999999;
                // 返却値編集
                return this.returnEdit(responseHeader);
            }
        }

        try {
            let nNo: string = null;
            /** STEP5.0対応　変更　START */
            // 共通チェックを行う

            // BaseHandler で共通チェックを行うので、ここは不要になります
            // apiProcessIDは BaseHandler で設定されるので、ここで設定する必要はありません

            // 業務チェック
            // オーダ種別チェック
            // 変数「オーダ種別」より判定する
            if (Constants.ORDER_TYPE_9 === orderType) {
                // ログ出力
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APLDLW0011);
                // 返却値を設定し、返却
                responseHeader.processCode = ResultCdConstants.CODE_520112;
                // 返却値編集
                return this.returnEdit(responseHeader);
            }

            // 業務チェック(予約前オーダ)の場合
            if (Constants.ORDER_TYPE_1 === orderType) {
                // 予約日と投入日相関チェック
                if (
                    !Check.checkReserveDateOrderDate(
                        systemDateTime,
                        reserveDate,
                    )
                ) {
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APLDLW0006,
                        reserveDate,
                        systemDateTime,
                    );
                    responseHeader.processCode = ResultCdConstants.CODE_520107;
                    return this.returnEdit(responseHeader);
                }

                // システムプロパティ．「予約実行日時単位」フォーマットチェックを行う
                if (
                    !Check.checkReservationDateExecutionUnitsFmt(
                        this.reservationDateExecutionUnits,
                    )
                ) {
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APLDLW0007,
                        this.reservationDateExecutionUnits,
                    );
                    responseHeader.processCode = ResultCdConstants.CODE_520108;
                    return this.returnEdit(responseHeader);
                }

                // システムプロパティ．「予約実行日時単位」と予約日相関チェックを行う
                if (
                    !Check.checkReservationDateExecutionUnits(
                        this.reservationDateExecutionUnits,
                        reserveDate,
                    )
                ) {
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APLDLW0008,
                        this.reservationDateExecutionUnits,
                        reserveDate,
                    );
                    responseHeader.processCode = ResultCdConstants.CODE_520109;
                    return this.returnEdit(responseHeader);
                }

                // システムプロパティ．「予約可能制限日数」フォーマットチェックを行う
                if (
                    !Check.checkReservationsLimitDaysFmt(
                        this.reservationsLimitDays,
                    )
                ) {
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APLDLW0009,
                        this.reservationsLimitDays,
                    );
                    responseHeader.processCode = ResultCdConstants.CODE_520110;
                    return this.returnEdit(responseHeader);
                }

                // システムプロパティ．「予約可能制限日数」範囲チェックを行う
                if (
                    !Check.checkReservationsLimitDays(
                        this.reservationsLimitDays,
                        reserveDate,
                    )
                ) {
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APLDLW0010,
                        this.reservationsLimitDays,
                        reserveDate,
                    );
                    responseHeader.processCode = ResultCdConstants.CODE_520111;
                    return this.returnEdit(responseHeader);
                }

                // MNP転出の予約リクエストは受け付けない
                if ("1" === mnpOutFlag) {
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APLDLW0013,
                        lineNo,
                        reserveDate,
                    );
                    responseHeader.processCode = ResultCdConstants.CODE_520114;

                    return this.returnEdit(responseHeader);
                }
            }

            // 業務チェック(即時オーダ)、(予約前オーダ)、(予約実行オーダ)の場合　共通部分
            if (
                Constants.ORDER_TYPE_0.equals(orderType) ||
                Constants.ORDER_TYPE_1.equals(orderType) ||
                Constants.ORDER_TYPE_2.equals(orderType)
            ) {
                // テナントIDの値をチェックし、リクエストが卸ポータルフロントからのものかを判断する
                if (!Constants.FRONT_API_TENANT_ID.equals(tenantId)) {
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APLDLW0012,
                        tenantId,
                    );
                    responseHeader.processCode = ResultCdConstants.CODE_520101;
                    return this.returnEdit(responseHeader);
                }

                // 対象の回線が既に廃止されていないかを確認する
                // STEP20.0版対応　変更　START
                let lineCheckResult: LinesEntity = null;
                try {
                    lineCheckResult = await this.apiLinesDao.getLineInfoByLines(
                        lineNo,
                    );
                } catch (e) {
                    // DBアクセスリトライエラーの場合
                    if (isSQLException(e)) {
                        handleCode = ResultCdConstants.CODE_520102;
                    }
                    throw e;
                }
                // STEP20.0版対応　変更　END

                // 変数「対象回線情報」より判定する
                if (
                    lineCheckResult == null ||
                    "03".equals(lineCheckResult.lineStatus)
                ) {
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APLDLW0001,
                        lineNo,
                    );
                    responseHeader.processCode = ResultCdConstants.CODE_520102;
                    return this.returnEdit(responseHeader);
                }

                // 対象回線の廃止オーダが受付中でないかチェックする
                // STEP20.0版対応　変更　START
                let abolitionOrderList: string[] = null;
                try {
                    abolitionOrderList =
                        await this.apiLinesDao.getAbolitionOrder(
                            lineNo,
                            reserveSoId,
                        );
                } catch (e) {
                    if (isSQLException(e)) {
                        // DBアクセスリトライエラーの場合
                        handleCode = ResultCdConstants.CODE_520103;
                    }
                    throw e;
                }
                // STEP20.0版対応　変更　END

                // 変数「対象回線廃止オーダチェック結果」より判定する
                if (abolitionOrderList.length >= 1) {
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APLDLW0002,
                        lineNo,
                    );
                    responseHeader.processCode = ResultCdConstants.CODE_520103;
                    return this.returnEdit(responseHeader);
                }

                // 回線とテナントIDの所属判定
                // 変数「関係チェック結果」より判定する
                // STEP20.0版対応　変更　START
                let checkResult: [boolean, string] = null;
                try {
                    checkResult = await this.tenantManage.doCheck(
                        lineNo,
                        targetTenantId,
                    );
                } catch (e) {
                    if (isSQLException(e)) {
                        // DBアクセスリトライエラーの場合
                        handleCode = ResultCdConstants.CODE_520104;
                    }
                    throw e;
                }
                if (!checkResult[0]) {
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APLDLW0003,
                        lineNo,
                        targetTenantId,
                    );
                    responseHeader.processCode = ResultCdConstants.CODE_520104;
                    return this.returnEdit(responseHeader);
                }
                nNo = checkResult[1]; // save N番 for success return later

                // 対象回線のSIMが半黒状態でないかチェックする
                // 半黒フラグの値を判定する
                if (lineCheckResult.simFlag) {
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APLDLW0004,
                        lineNo,
                    );
                    responseHeader.processCode = ResultCdConstants.CODE_520105;
                    return this.returnEdit(responseHeader);
                }

                // // 基準日
                let targetDate: string = null;

                if (Constants.ORDER_TYPE_0.equals(orderType)) {
                    // NOTE `fromUnixTime` expects seconds, so we divide by 1000
                    targetDate = format(
                        fromUnixTime(
                            MvnoUtil.convDateFormat(systemDateTime) / 1000,
                        ),
                        "yyyyMMdd",
                    );
                } else {
                    targetDate = reserveDate
                        .substring(0, 10)
                        .replace(/\//g, "");
                }

                // STEP7.0版対応　追加　START
                // MNP転出フラグが”1” (MNP転出である)の場合は、以下のチェックは行わずに⑨へ移動する
                if (mnpOutFlag !== "1") {
                    // STEP20.0版対応　変更　START
                    let enableOrderList: string[] = null;
                    try {
                        enableOrderList =
                            await this.aPICommonDAO.getEnableServiceOrder(
                                lineNo,
                                targetDate,
                                reserveSoId,
                            );
                    } catch (e) {
                        if (isSQLException(e)) {
                            // DBアクセスリトライエラーの場合
                            handleCode = ResultCdConstants.CODE_520106;
                        }
                        throw e;
                    }
                    // 変数「廃止SO存在チェック結果」より判定する
                    if (enableOrderList.length !== 0) {
                        super.warn(
                            tenantId,
                            sequenceNo,
                            MsgKeysConstants.APLDLW0005,
                            lineNo,
                        );
                        responseHeader.processCode =
                            ResultCdConstants.CODE_520106;
                        return this.returnEdit(responseHeader);
                    }
                } else {
                    let reservedOrderList: string[] = null;
                    try {
                        // 回線番号をキーに、SO管理テーブルの情報を取得する
                        reservedOrderList =
                            await this.apiLinesDao.getReservedServiceOrder(
                                lineNo,
                                targetDate,
                            );
                    } catch (e: any) {
                        if (isSQLException(e)) {
                            // DBアクセスリトライエラーの場合
                            handleCode = ResultCdConstants.CODE_520115;
                            throw e;
                        }
                        super.warn(
                            e,
                            tenantId,
                            sequenceNo,
                            MsgKeysConstants.APLDLW0015,
                            lineNo,
                        );
                        responseHeader.processCode =
                            ResultCdConstants.CODE_520115;
                        return this.returnEdit(responseHeader);
                    }

                    // 取得レコードが0件の場合は処理を中断し、No.2「CSVファイル用データ収集」へ移動する
                    if (reservedOrderList.length !== 0) {
                        // SO管理テーブルのオーダステータスを更新する
                        for (const row of reservedOrderList) {
                            const cancelSoId = row;
                            try {
                                await this.aPISoDAO.updServiceOrders(
                                    null,
                                    "キャンセル済み",
                                    cancelSoId,
                                    null,
                                );
                            } catch (e: any) {
                                if (isSQLException(e)) {
                                    // DBアクセスリトライエラーの場合
                                    handleCode = ResultCdConstants.CODE_520116;
                                    throw e;
                                }
                                super.warn(
                                    e,
                                    tenantId,
                                    sequenceNo,
                                    MsgKeysConstants.APLDLW0016,
                                    lineNo,
                                    cancelSoId,
                                );
                                responseHeader.processCode =
                                    ResultCdConstants.CODE_520116;
                                return this.returnEdit(responseHeader);
                            }
                        }
                    }
                }
                // STEP7.0版対応　追加　END
            }

            let isCsvUnnecessary = true; // abolitionCsvOutputKbn default to null in Java code
            // ロジック処理(即時オーダ)、(予約前オーダ)の場合
            if (
                Constants.ORDER_TYPE_0 === orderType ||
                Constants.ORDER_TYPE_1 === orderType
            ) {
                if (csvUnnecessaryFlag === "1") {
                    isCsvUnnecessary = true;
                } else if (
                    csvUnnecessaryFlag === null ||
                    csvUnnecessaryFlag === undefined ||
                    csvUnnecessaryFlag === "" ||
                    csvUnnecessaryFlag === "0"
                ) {
                    isCsvUnnecessary = false;
                    // if N番 is empty, log error and don't do CoreSwimmy連携 (added during refactoring)
                    // https://github.com/playnext-lab/MVNO_OLD_PORTAL_CORE/blob/a67a0cd1839741c93ac806c42f5c9beb55da8756/1.war/src/com/ntt/mawp/service/impl/LineDeleteService.java#L1217-L1223
                    if (isNone(nNo)) {
                        super.error(
                            tenantId,
                            sequenceNo,
                            MsgKeysConstants.APLDLE0002,
                            lineNo,
                            targetTenantId,
                        );
                        isCsvUnnecessary = true;
                    }
                }
            }

            // ディバッグログ出力
            super.debug(
                param.tenantId,
                sequenceNo,
                MsgKeysConstants.APLDLD0002,
                LineDeleteService.name,
                "service",
            );
            responseHeader.processCode = ResultCdConstants.CODE_000000;
            return this.returnEdit(responseHeader, nNo, isCsvUnnecessary);
        } catch (error: any) {
            this.context.error("LineDeleteService error:", error);
            // STEP20.0版対応　追加　START
            if (isSQLException(error)) {
                // DBアクセスリトライエラーの場合
                // リターンコード値は各エラー時にて設定済み
                super.error(
                    error,
                    param.tenantId,
                    param.requestHeader.sequenceNo,
                    MsgKeysConstants.APCOME0402,
                    functionType,
                );
                responseHeader.processCode = handleCode;
                return this.returnEdit(responseHeader);
                // STEP20.0版対応　追加　END
            }
            super.error(
                tenantId,
                sequenceNo,
                MsgKeysConstants.APCOME0401,
                error.message,
            );
            responseHeader.processCode = ResultCdConstants.CODE_999999;
            return this.returnEdit(responseHeader);
        } finally {
            // SO管理共通パラメータ設定
            await this.soManagementCommon(
                param,
                responseHeader.apiProcessID, // resultBean.getOthers(),
                "",
                "",
                responseHeader.processCode,
                systemDateTime,
                orderType,
                reserveDate,
                lineNo,
                mnpOutFlag,
            );
        }
    }

    /**
     * 回線廃止機能返却値編集
     *
     * @param responseHeader
     *            返却レスポンスヘッダ生成
     * @return 回線廃止機能OutputDto
     */
    private returnEdit(
        responseHeader: ResponseHeader,
        nNo: string = null,
        csvUnnecessary: boolean = true,
    ): LineDeleteOutputDto {
        const lineDeleteOutputDto: LineDeleteOutputDto = {
            jsonBody: { responseHeader },
            additionalData: {
                nNo: nNo || "",
                csvUnnecessary,
            },
        };
        return lineDeleteOutputDto;
    }

    /**
     * SO管理共通パラメータ設定
     *
     * @param param パラメータ
     * @param apiProcessID // NOTE changed from: resultBean チェック結果 (`resultBean.getOthers()`)
     * @param executeUserId 実行ユーザID
     * @param executeTenantId 実行テナントID
     * @param code オーダステータス
     * @param receivedDate REST API共通処理が呼び出されたシステム日付
     * @param orderType オーダ種別
     * @param reserveDate 受信日時
     * @param lineNo 回線番号
     * @param mnpOutFlag MNP転出フラグ
     * @throws Exception 例外
     */
    private async soManagementCommon(
        param: LineDeleteInputDto,
        apiProcessID: string,
        executeUserId: string,
        executeTenantId: string,
        code: string,
        receivedDate: string,
        orderType: string,
        reserveDate: string,
        lineNo: string,
        mnpOutFlag: string,
    ) {
        const soObject = new SOObject();
        // サービスオーダID
        // 「予約実行オーダ」の場合
        if (Constants.ORDER_TYPE_2.equals(orderType)) {
            soObject.setServiceOrderId(param.reserve_soId);
        } else {
            soObject.setServiceOrderId(apiProcessID);
        }
        // 申込日
        // 「予約実行オーダ」の場合
        if (Constants.ORDER_TYPE_2.equals(orderType)) {
            soObject.setOrderDate(null);
        } else {
            soObject.setOrderDate(receivedDate);
        }
        // 予約日
        // 「予約前オーダ」の場合
        if (Constants.ORDER_TYPE_1.equals(orderType)) {
            soObject.setReserveDate(reserveDate);
        } else {
            soObject.setReserveDate(null);
        }
        // オーダ種別
        // 「即時オーダ」の場合
        if (Constants.ORDER_TYPE_0.equals(orderType)) {
            soObject.setOrderType(null);
        } else {
            soObject.setOrderType(orderType);
        }
        // 完了日
        // 「即時オーダ」又は「予約実行オーダ」の場合
        if (
            Constants.ORDER_TYPE_0.equals(orderType) ||
            Constants.ORDER_TYPE_2.equals(orderType)
        ) {
            soObject.setExecDate(MvnoUtil.getDateTimeNow());
        } else {
            soObject.setExecDate(null);
        }
        // デーダステータス
        soObject.setOrderStatus(code);
        // 回線ID
        // 「予約実行オーダの場合」
        if (Constants.ORDER_TYPE_2.equals(orderType)) {
            soObject.setLineId(null);
        } else {
            soObject.setLineId(lineNo);
        }
        // 回線グループID
        soObject.setLineGroupId(null);
        // 所属テナントID
        soObject.setTenantId(param.targetTenantId);
        // 実行ユーザID
        soObject.setExecuteUserId(null);
        // 実行テナントID
        soObject.setExecuteTenantId(null);
        // 機能種別
        soObject.setFunctionType(param.requestHeader.functionType);
        // 操作区分
        // 「予約実行オーダ」の場合
        if (Constants.ORDER_TYPE_2.equals(orderType)) {
            soObject.setOperationDivision(null);
        } else {
            soObject.setOperationDivision("");
        }
        // 変更前プランID
        // 「予約実行オーダ」の場合
        if (Constants.ORDER_TYPE_2.equals(orderType)) {
            soObject.setChangeOldplanId(null);
        } else {
            soObject.setChangeOldplanId("");
        }
        // 変更後プランID
        // 「予約実行オーダ」の場合
        if (Constants.ORDER_TYPE_2.equals(orderType)) {
            soObject.setChangeNewplanId(null);
        } else {
            soObject.setChangeNewplanId("");
        }
        // 回線所属変更回線ID
        soObject.setChangePlanId("");
        // 基本容量
        soObject.setBasicCapacity("");
        // オプションプランID
        soObject.setOptionPlanId("");
        // MNP転出フラグ
        // 「予約実行オーダ」の場合
        if (Constants.ORDER_TYPE_2.equals(orderType)) {
            soObject.setMnpOutFlag(null);
        } else {
            soObject.setMnpOutFlag(mnpOutFlag);
        }
        // REST電文
        // 「即時オーダ」又は「予約実行オーダ」の場合
        if (
            Constants.ORDER_TYPE_0.equals(orderType) ||
            Constants.ORDER_TYPE_2.equals(orderType)
        ) {
            soObject.setRestMessage(null);
        } else {
            soObject.setRestMessage(MvnoUtil.getRestMessage(param));
        }
        await this.soCommon.soCommon(soObject);
    }
}
