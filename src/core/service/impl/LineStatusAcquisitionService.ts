import config from "config";

import ApiCommon from "@/core/common/ApiCommon";
import Check from "@/core/common/Check";
import CheckUtil from "@/core/common/CheckUtil";
import MvnoUtil from "@/core/common/MvnoUtil";
import RESTCommon from "@/core/common/RESTCommon";
import SOAPCommon from "@/core/common/SOAPCommon";
import SOCommon from "@/core/common/SOCommon";
import TenantManage from "@/core/common/TenantManage";

import Constants from "@/core/constant/Constants";
import MsgKeysConstants from "@/core/constant/MsgKeysConstants";
import ResultCdConstants from "@/core/constant/ResultCdConstants";

import CheckResultBean from "@/core/dto/CheckResultBean";
import LineStatusAcquisitionInputDto from "@/core/dto/LineStatusAcquisitionInputDto";
import LineStatusAcquisitionOutputDto from "@/core/dto/LineStatusAcquisitionOutputDto";
import ParameterName from "@/core/dto/ParameterName";
import ResponseHeader from "@/core/dto/ResponseHeader";
import SOAPCommonOutputDto from "@/core/dto/SOAPCommonOutputDto";
import SOObject from "@/core/dto/SOObject";

import { isSQLException } from "@/helpers/queryHelper";
import SOAPException from "@/types/soapException";

import { IFService } from "../IFService";
import { getConfig } from "@/helpers/configHelper";
import { getStringParameter } from "@/types/parameter.string";

/**
 * 回線のアクティベート・ディアクティベート機能。
 */
export default class LineStatusAcquisitionService
    extends RESTCommon
    implements
        IFService<
            LineStatusAcquisitionInputDto,
            LineStatusAcquisitionOutputDto
        >
{
    /** 定数　「回線番号」 */
    private readonly LINE_NO = "回線番号";
    /** 定数　「”アクティベート／ディアクティベート”」 */
    private readonly STATUS = "アクティベート／ディアクティベート";
    /** 定数　「1」 */
    private readonly CONST_SHANAITENANTID_1 = "1";
    /** 定数　「0」 */
    private readonly CONST_SHANAITENANTID_0 = "0";
    // /** 定数　「1」 */
    // private static final String CONST_ONE = "1";
    /** 「Result」結果がNG */
    private readonly RESULT_NG = "NG";

    /**
     * SO管理共通DAO
     */
    private soCommon = new SOCommon(this.request, this.context);

    /**
     * SOAP API連携機能初期化
     */
    private sOAPCommon = new SOAPCommon(this.request, this.context);

    /**
     * API共通処理クラス
     */
    private apiCommon = new ApiCommon(this.request, this.context);

    // @Value("#{mvno[APServerConnectSOAPAPIURL]}")
    // private String soapApiUrl;

    /**
     * 予約実行日時単位(予約機能)
     */
    private reservationDateExecutionUnits: string = getConfig(
        "ReservationDateExecutionUnits",
    );
    /**
     * 予約可能制限日数(予約機能)
     */
    private reservationsLimitDays: string = getConfig("ReservationsLimitDays");

    /**
     * テナント管理
     */
    private tenantManage = new TenantManage(this.request, this.context);

    /**
     * 回線のアクティベート・ディアクティベート。<BR>
     *
     * <PRE>
     * REST APIにより、受信したパラメータの回線のアクティベート・ディアクティベートを取得する。
     * </PRE>
     * @param param 回線のアクティベート・ディアクティベートDto
     * @param isPrivate
     *            内部呼出フラグ
     * @param params
     *            可変長さ引数(内部から呼出す時に使用するパラメータ)
     * @return LineStatusAcquisitionOutputDto 回線のアクティベート・ディアクティベートDtoクラス
     * @throws Exception
     */
    async service(
        param: LineStatusAcquisitionInputDto,
        isPrivate: boolean,
        ...params: any[]
    ): Promise<LineStatusAcquisitionOutputDto> {
        // REST API共通処理が呼び出されたシステム日付
        const receivedDate =
            this.context.responseHeader?.receivedDate ??
            MvnoUtil.getDateTimeNow();
        super.debug(
            param.tenantId,
            param.requestHeader.sequenceNo,
            MsgKeysConstants.APLAFD0001,
            this.getClassName(),
            "service",
            JSON.stringify(param),
        );
        // 変数「処理コード」初期化
        let cheatCode = ResultCdConstants.CODE_000000;
        // 送信番号取得
        const sequenceNo = param.requestHeader.sequenceNo;
        // 送信元システムID取得
        // const senderSystemId = param.requestHeader.senderSystemId;
        // API認証キー取得
        // const apiKey = param.requestHeader.apiKey;
        // 機能種別取得
        const functionType = param.requestHeader.functionType;
        // テナントID
        const tenantId = param.tenantId;
        // 回線番号
        const lineNo = param.lineNo;
        // アクティベート/ディアクティベート
        const status = getStringParameter(param.status);
        let apiProcessID = "";
        // 回線運用データ情報結果初期化
        let documents: Document = null;

        let lineStatusAcquisitionOutputDto: LineStatusAcquisitionOutputDto = {
            jsonBody: {
                responseHeader: { ...this.context.responseHeader },
            },
        };
        // we already get result of 共通チェック here
        const resultBean: CheckResultBean = {
            checkResult: true,
            processingCode: this.context.responseHeader.processCode,
            others: this.context.responseHeader.apiProcessID,
            errorMassage: "",
        };

        // NOTE these 3 variables are for isPrivate = true
        const errorMessage: string = null;
        // 変数「実行ユーザID」を定義する
        const executeUserId: string = null;
        // 変数「実行テナントID」を定義する
        const executeTenantId: string = null;

        // 予約日
        const reserveDate = param.reserve_date;
        // 予約フラグ
        const reserveFlag = param.reserve_flag;
        // ＳＯ－ＩＤ
        const reserveSoId = param.reserve_soId;
        // オーダ種別取得
        const orderType = Check.checkOrderType(
            reserveDate,
            reserveFlag,
            reserveSoId,
        );

        try {
            // 変数「処理コード」
            cheatCode = resultBean.processingCode;
            // 変数「API処理ID」
            apiProcessID = resultBean.others;

            // オーダ種別チェック
            if (Constants.ORDER_TYPE_9.equals(orderType)) {
                cheatCode = ResultCdConstants.CODE_050105;
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APLAFW0011);
                lineStatusAcquisitionOutputDto = this.returnEdit(
                    param,
                    cheatCode,
                    apiProcessID,
                    isPrivate,
                    errorMessage,
                    receivedDate,
                );
                return lineStatusAcquisitionOutputDto;
            }

            // 予約実行オーダの場合
            if (Constants.ORDER_TYPE_2.equals(orderType)) {
                // APサーバの複数IP取得
                const localIpList = MvnoUtil.getLocalIpList();
                // 予約実行可否チェック
                if (!this.context.isInternalRequest) {
                    cheatCode = ResultCdConstants.CODE_999999;
                    super.error(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APCOME0401,
                        "予約実行できませんでした。クライアントIP:" +
                            String(params[2]) +
                            ", APサーバーIP：" +
                            MvnoUtil.getOutputList(localIpList),
                    );
                    lineStatusAcquisitionOutputDto = this.returnEdit(
                        param,
                        cheatCode,
                        apiProcessID,
                        isPrivate,
                        errorMessage,
                        receivedDate,
                    );
                    return lineStatusAcquisitionOutputDto;
                }
            }

            // 業務チェック処理
            // １ 回線番号フォーマットチェック
            if (!Check.checkLineNo(lineNo)) {
                cheatCode = ResultCdConstants.CODE_050101;
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APLAFW0001,
                    this.LINE_NO,
                    lineNo,
                );
                // SO管理共通パラメータ設定
                await this.soManagementCommon(
                    param,
                    resultBean,
                    cheatCode,
                    executeUserId,
                    executeTenantId,
                    receivedDate,
                    orderType,
                    reserveDate,
                    isPrivate,
                );
                lineStatusAcquisitionOutputDto = this.returnEdit(
                    param,
                    cheatCode,
                    apiProcessID,
                    isPrivate,
                    errorMessage,
                    receivedDate,
                );
                return lineStatusAcquisitionOutputDto;
            }

            // ２ アクティベート／ディアクティベートフォーマットチェック
            if (
                CheckUtil.checkIsNotNull(status) ||
                !CheckUtil.checkLength(status, 1, true) ||
                !CheckUtil.checkIsNum(status) ||
                (!"1".equals(status) && !"0".equals(status))
            ) {
                cheatCode = ResultCdConstants.CODE_050101;
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APLAFW0001,
                    this.STATUS,
                    status,
                );
                // SO管理共通パラメータ設定
                await this.soManagementCommon(
                    param,
                    resultBean,
                    cheatCode,
                    executeUserId,
                    executeTenantId,
                    receivedDate,
                    orderType,
                    reserveDate,
                    isPrivate,
                );
                lineStatusAcquisitionOutputDto = this.returnEdit(
                    param,
                    cheatCode,
                    apiProcessID,
                    isPrivate,
                    errorMessage,
                    receivedDate,
                );
                return lineStatusAcquisitionOutputDto;
            }

            // 予約前オーダ　或いは　予約実行オーダの場合
            if (
                Constants.ORDER_TYPE_1.equals(orderType) ||
                Constants.ORDER_TYPE_2.equals(orderType)
            ) {
                // 予約日フォーマットチェック
                if (!Check.checkReserveDateFmt(reserveDate)) {
                    cheatCode = ResultCdConstants.CODE_050101;
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APLAFW0001,
                        "予約日",
                        reserveDate,
                    );
                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(
                        param,
                        resultBean,
                        cheatCode,
                        executeUserId,
                        executeTenantId,
                        receivedDate,
                        orderType,
                        reserveDate,
                        isPrivate,
                    );
                    lineStatusAcquisitionOutputDto = this.returnEdit(
                        param,
                        cheatCode,
                        apiProcessID,
                        isPrivate,
                        errorMessage,
                        receivedDate,
                    );
                    return lineStatusAcquisitionOutputDto;
                }
            }

            // 予約前オーダの場合
            if (Constants.ORDER_TYPE_1.equals(orderType)) {
                // 予約日と投入日相関チェック
                if (
                    !Check.checkReserveDateOrderDate(receivedDate, reserveDate)
                ) {
                    cheatCode = ResultCdConstants.CODE_050106;
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APLAFW0012,
                        reserveDate,
                        receivedDate,
                    );
                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(
                        param,
                        resultBean,
                        cheatCode,
                        executeUserId,
                        executeTenantId,
                        receivedDate,
                        orderType,
                        reserveDate,
                        isPrivate,
                    );
                    lineStatusAcquisitionOutputDto = this.returnEdit(
                        param,
                        cheatCode,
                        apiProcessID,
                        isPrivate,
                        errorMessage,
                        receivedDate,
                    );
                    return lineStatusAcquisitionOutputDto;
                }
                // 予約実行日時単位フォーマットチェック
                if (
                    !Check.checkReservationDateExecutionUnitsFmt(
                        this.reservationDateExecutionUnits,
                    )
                ) {
                    cheatCode = ResultCdConstants.CODE_050107;
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APLAFW0013,
                        this.reservationDateExecutionUnits,
                    );
                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(
                        param,
                        resultBean,
                        cheatCode,
                        executeUserId,
                        executeTenantId,
                        receivedDate,
                        orderType,
                        reserveDate,
                        isPrivate,
                    );
                    lineStatusAcquisitionOutputDto = this.returnEdit(
                        param,
                        cheatCode,
                        apiProcessID,
                        isPrivate,
                        errorMessage,
                        receivedDate,
                    );
                    return lineStatusAcquisitionOutputDto;
                }
                // 予約実行日時単位と予約日相関チェック
                if (
                    !Check.checkReservationDateExecutionUnits(
                        this.reservationDateExecutionUnits,
                        reserveDate,
                    )
                ) {
                    cheatCode = ResultCdConstants.CODE_050108;
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APLAFW0014,
                        this.reservationDateExecutionUnits,
                        reserveDate,
                    );
                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(
                        param,
                        resultBean,
                        cheatCode,
                        executeUserId,
                        executeTenantId,
                        receivedDate,
                        orderType,
                        reserveDate,
                        isPrivate,
                    );
                    lineStatusAcquisitionOutputDto = this.returnEdit(
                        param,
                        cheatCode,
                        apiProcessID,
                        isPrivate,
                        errorMessage,
                        receivedDate,
                    );
                    return lineStatusAcquisitionOutputDto;
                }
                // 予約可能制限日数フォーマットチェック
                if (
                    !Check.checkReservationsLimitDaysFmt(
                        this.reservationsLimitDays,
                    )
                ) {
                    cheatCode = ResultCdConstants.CODE_050109;
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APLAFW0015,
                        this.reservationsLimitDays,
                    );
                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(
                        param,
                        resultBean,
                        cheatCode,
                        executeUserId,
                        executeTenantId,
                        receivedDate,
                        orderType,
                        reserveDate,
                        isPrivate,
                    );
                    lineStatusAcquisitionOutputDto = this.returnEdit(
                        param,
                        cheatCode,
                        apiProcessID,
                        isPrivate,
                        errorMessage,
                        receivedDate,
                    );
                    return lineStatusAcquisitionOutputDto;
                }
                // 予約可能制限日数範囲チェック
                if (
                    !Check.checkReservationsLimitDays(
                        this.reservationsLimitDays,
                        reserveDate,
                    )
                ) {
                    cheatCode = ResultCdConstants.CODE_050110;
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APLAFW0016,
                        this.reservationsLimitDays,
                        reserveDate,
                    );
                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(
                        param,
                        resultBean,
                        cheatCode,
                        executeUserId,
                        executeTenantId,
                        receivedDate,
                        orderType,
                        reserveDate,
                        isPrivate,
                    );
                    lineStatusAcquisitionOutputDto = this.returnEdit(
                        param,
                        cheatCode,
                        apiProcessID,
                        isPrivate,
                        errorMessage,
                        receivedDate,
                    );
                    return lineStatusAcquisitionOutputDto;
                }
            }

            let checkResult: [boolean, string] = null;
            try {
                checkResult = await this.tenantManage.doCheck(
                    param.lineNo,
                    param.tenantId,
                );
            } catch (e) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    cheatCode = ResultCdConstants.CODE_050102;
                }
                throw e;
            }
            if (!checkResult[0]) {
                cheatCode = ResultCdConstants.CODE_050102;
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APLAFW0002,
                    param.lineNo,
                    param.tenantId,
                );
                // SO管理共通パラメータ設定
                await this.soManagementCommon(
                    param,
                    resultBean,
                    cheatCode,
                    executeUserId,
                    executeTenantId,
                    receivedDate,
                    orderType,
                    reserveDate,
                    isPrivate,
                );
                lineStatusAcquisitionOutputDto = this.returnEdit(
                    param,
                    cheatCode,
                    apiProcessID,
                    isPrivate,
                    errorMessage,
                    receivedDate,
                );
                return lineStatusAcquisitionOutputDto;
            }

            // 廃止オーダ受付中チェック
            // 廃止オーダ受付中チェックを実施する

            // 基準日
            let targetDate: string = null;

            if (Constants.ORDER_TYPE_0.equals(orderType)) {
                targetDate = receivedDate.substring(0, 10).replace(/\//g, "");
            } else {
                targetDate = reserveDate.substring(0, 10).replace(/\//g, "");
            }

            let checkAbolishSoResult: boolean;
            try {
                checkAbolishSoResult = await this.apiCommon.checkAbolishSo(
                    lineNo,
                    targetDate,
                );
            } catch (e) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    cheatCode = ResultCdConstants.CODE_050103;
                }
                throw e;
            }
            // 変数「廃止オーダ受付中判定結果」より判定する
            if (!checkAbolishSoResult) {
                cheatCode = ResultCdConstants.CODE_050103;
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APLAFW0017,
                    lineNo,
                );
                // SO管理共通パラメータ設定
                await this.soManagementCommon(
                    param,
                    resultBean,
                    cheatCode,
                    executeUserId,
                    executeTenantId,
                    receivedDate,
                    orderType,
                    reserveDate,
                    isPrivate,
                );
                lineStatusAcquisitionOutputDto = this.returnEdit(
                    param,
                    cheatCode,
                    apiProcessID,
                    isPrivate,
                    errorMessage,
                    receivedDate,
                );
                return lineStatusAcquisitionOutputDto;
            }

            // 利用状態(サスペンド中)のチェック
            let checkLineUsageStatusResult: boolean;
            try {
                checkLineUsageStatusResult =
                    await this.apiCommon.checkLineSuspend(lineNo);
            } catch (e) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    cheatCode = ResultCdConstants.CODE_050104;
                }
                throw e;
            }
            // 変数「利用状態判定結果」より判定する
            if (!checkLineUsageStatusResult) {
                cheatCode = ResultCdConstants.CODE_050104;
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APLAFW0018,
                    lineNo,
                );
                // SO管理共通パラメータ設定
                await this.soManagementCommon(
                    param,
                    resultBean,
                    cheatCode,
                    executeUserId,
                    executeTenantId,
                    receivedDate,
                    orderType,
                    reserveDate,
                    isPrivate,
                );
                lineStatusAcquisitionOutputDto = this.returnEdit(
                    param,
                    cheatCode,
                    apiProcessID,
                    isPrivate,
                    errorMessage,
                    receivedDate,
                );
                return lineStatusAcquisitionOutputDto;
            }

            // １ アクティベート状況取得
            let tpcConnectionResults: [boolean, string, string] = null;
            try {
                tpcConnectionResults =
                    await this.tenantManage.checkTpcConnection(param.tenantId);
            } catch (e) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    cheatCode = ResultCdConstants.CODE_050301;
                }
                throw e;
            }
            // 変数「TPC情報取得結果」より判断する
            if (!tpcConnectionResults[0]) {
                cheatCode = ResultCdConstants.CODE_050301;
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APLAFW0010,
                    param.tenantId,
                );
                // SO管理共通パラメータ設定
                await this.soManagementCommon(
                    param,
                    resultBean,
                    cheatCode,
                    executeUserId,
                    executeTenantId,
                    receivedDate,
                    orderType,
                    reserveDate,
                    isPrivate,
                );
                lineStatusAcquisitionOutputDto = this.returnEdit(
                    param,
                    cheatCode,
                    apiProcessID,
                    isPrivate,
                    errorMessage,
                    receivedDate,
                );
                return lineStatusAcquisitionOutputDto;
            }

            // 変数「オーダ種別」=即時オーダ　あるいは　予約実行オーダ
            if (
                Constants.ORDER_TYPE_0.equals(orderType) ||
                Constants.ORDER_TYPE_2.equals(orderType)
            ) {
                // １　アクティベート状況取得
                let sOAPCommonOutput: SOAPCommonOutputDto = null;
                // プライマリTPCへSOAP連携
                try {
                    sOAPCommonOutput = await this.sendSoapApiToTpc(
                        lineNo,
                        status,
                        tenantId,
                        sequenceNo,
                        tpcConnectionResults[1],
                        true,
                    );
                } catch (e) {
                    if (SOAPException.isSOAPException(e)) {
                        // SOAP送受信でエラーとなった場合
                        cheatCode = ResultCdConstants.CODE_050401;
                        super.warn(
                            tenantId,
                            sequenceNo,
                            MsgKeysConstants.APLAFW0007,
                        );
                        // SO管理共通パラメータ設定
                        await this.soManagementCommon(
                            param,
                            resultBean,
                            cheatCode,
                            executeUserId,
                            executeTenantId,
                            receivedDate,
                            orderType,
                            reserveDate,
                            isPrivate,
                        );
                        lineStatusAcquisitionOutputDto = this.returnEdit(
                            param,
                            cheatCode,
                            apiProcessID,
                            isPrivate,
                            errorMessage,
                            receivedDate,
                        );
                        return lineStatusAcquisitionOutputDto;
                    }
                    throw e; // throw other exceptions
                }

                documents = sOAPCommonOutput.getDoc();
                let results = this.sOAPCommon.getNodeContent(
                    documents,
                    "//Result",
                );
                if (this.RESULT_NG.equals(results)) {
                    cheatCode = ResultCdConstants.CODE_050401;
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APLAFW0007,
                    );
                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(
                        param,
                        resultBean,
                        cheatCode,
                        executeUserId,
                        executeTenantId,
                        receivedDate,
                        orderType,
                        reserveDate,
                        isPrivate,
                    );
                    lineStatusAcquisitionOutputDto = this.returnEdit(
                        param,
                        cheatCode,
                        apiProcessID,
                        isPrivate,
                        errorMessage,
                        receivedDate,
                    );
                    return lineStatusAcquisitionOutputDto;
                }

                // セカンダリTPCへSOAP連携
                if (tpcConnectionResults[2] != null) {
                    // セカンダリTPCが設定されている場合は以下の処理を行う(電文はプライマリと同じものを使用)
                    try {
                        sOAPCommonOutput = await this.sendSoapApiToTpc(
                            lineNo,
                            status,
                            tenantId,
                            sequenceNo,
                            tpcConnectionResults[2],
                            false,
                        );

                        if (
                            ResultCdConstants.CODE_000951.equals(
                                sOAPCommonOutput.getProcessCode(),
                            )
                        ) {
                            // SG取得でNGの場合
                            super.warn(
                                tenantId,
                                sequenceNo,
                                MsgKeysConstants.APLAFW0019,
                                "SG取得NG",
                            );
                        } else {
                            // TPCからのResultがNGの場合
                            documents = sOAPCommonOutput.getDoc();
                            results = this.sOAPCommon.getNodeContent(
                                documents,
                                "//Result",
                            );
                            if (this.RESULT_NG.equals(results)) {
                                const errInfo = this.sOAPCommon.getNodeContent(
                                    documents,
                                    "//ErrorInfomation",
                                );
                                super.warn(
                                    tenantId,
                                    sequenceNo,
                                    MsgKeysConstants.APLAFW0019,
                                    errInfo,
                                );
                            }
                        }
                    } catch (e) {
                        if (SOAPException.isSOAPException(e)) {
                            // SOAP送受信でエラーとなった場合
                            super.warn(
                                e,
                                tenantId,
                                sequenceNo,
                                MsgKeysConstants.APLAFW0019,
                                e?.message,
                            );
                        }
                        throw e; // throw other exceptions
                    }
                }
            }

            // 返却値編集
            // SO管理共通パラメータ設定
            await this.soManagementCommon(
                param,
                resultBean,
                cheatCode,
                executeUserId,
                executeTenantId,
                receivedDate,
                orderType,
                reserveDate,
                isPrivate,
            );
            lineStatusAcquisitionOutputDto = this.returnEdit(
                param,
                cheatCode,
                apiProcessID,
                isPrivate,
                errorMessage,
                receivedDate,
            );

            return lineStatusAcquisitionOutputDto;
        } catch (e) {
            this.context.warn(
                "LineStatusAcquisitionService.service error detail:",
                e,
            );
            if (isSQLException(e)) {
                // DBアクセスリトライエラーの場合
                // リターンコード値は各エラー時にて設定済み
                super.error(
                    e,
                    param.tenantId,
                    param.requestHeader.sequenceNo,
                    MsgKeysConstants.APCOME0402,
                    functionType,
                );
                // 返却値編集
                // SO管理共通パラメータ設定
                await this.soManagementCommon(
                    param,
                    resultBean,
                    cheatCode,
                    executeUserId,
                    executeTenantId,
                    receivedDate,
                    orderType,
                    reserveDate,
                    isPrivate,
                );
                lineStatusAcquisitionOutputDto = this.returnEdit(
                    param,
                    cheatCode,
                    apiProcessID,
                    isPrivate,
                    errorMessage,
                    receivedDate,
                );
                return lineStatusAcquisitionOutputDto;
            } else if (SOAPException.isSOAPException(e)) {
                this.context.error(
                    "LineStatusAcquisitionService.service SOAPException:",
                    e,
                );
                cheatCode = ResultCdConstants.CODE_050401;
                // 返却値編集
                // SO管理共通パラメータ設定
                await this.soManagementCommon(
                    param,
                    resultBean,
                    cheatCode,
                    executeUserId,
                    executeTenantId,
                    receivedDate,
                    orderType,
                    reserveDate,
                    isPrivate,
                );
                lineStatusAcquisitionOutputDto = this.returnEdit(
                    param,
                    cheatCode,
                    apiProcessID,
                    isPrivate,
                    errorMessage,
                    receivedDate,
                );
                return lineStatusAcquisitionOutputDto;
            } else {
                cheatCode = ResultCdConstants.CODE_999999;
                super.error(
                    e,
                    param.tenantId,
                    param.requestHeader.sequenceNo,
                    MsgKeysConstants.APCOME0401,
                    e?.messsage,
                );
                // 返却値編集
                // SO管理共通パラメータ設定
                await this.soManagementCommon(
                    param,
                    resultBean,
                    cheatCode,
                    executeUserId,
                    executeTenantId,
                    receivedDate,
                    orderType,
                    reserveDate,
                    isPrivate,
                );
                lineStatusAcquisitionOutputDto = this.returnEdit(
                    param,
                    cheatCode,
                    apiProcessID,
                    isPrivate,
                    errorMessage,
                    receivedDate,
                );
                return lineStatusAcquisitionOutputDto;
            }
        } finally {
            super.debug(
                param.tenantId,
                param.requestHeader.sequenceNo,
                MsgKeysConstants.APLAFD0002,
                this.getClassName(),
                "service",
            );
        }
    }

    /**
     * 回線のアクティベート・ディアクティベート返却値編集
     *
     * @param inputParam 回線のアクティベート・ディアクティベートInputDto
     * @param rtncheatCode 変数 「処理コード」
     * @param rtnapiProcessID 変数 「API処理ID」
     * @param isPrivate 変数 「内部呼出フラグ」
     * @param errorMessage 変数 「エラーメッセージ」
     * @param receivedDate REST API共通処理が呼び出されたシステム日付
     * @return 回線のアクティベート・ディアクティベートOutputDto
     */
    private returnEdit(
        inputParam: LineStatusAcquisitionInputDto,
        rtncheatCode: string,
        rtnapiProcessID: string,
        isPrivate: boolean,
        errorMessage: string,
        receivedDate: string,
    ): LineStatusAcquisitionOutputDto {
        const responseHeader: ResponseHeader = {
            // 送信番号取得
            sequenceNo: inputParam.requestHeader.sequenceNo,
            // 受信日時
            receivedDate,
            // 処理コード
            processCode: rtncheatCode,
            // API処理ID
            apiProcessID: rtnapiProcessID,
        };
        const returnPram: LineStatusAcquisitionOutputDto = {
            // ヘッダ情報を設定する
            jsonBody: { responseHeader },
        };
        return returnPram;
    }

    /**
     * SO管理共通パラメータ設定
     *
     * @param param
     * @param resultBean
     * @param cheatCode
     * @param executeUserId
     * @param executeTenantId
     * @param receivedDate REST API共通処理が呼び出されたシステム日付
     * @param orderType
     * @param reserveDate
     * @param isPrivate
     * @throws Exception
     */
    private async soManagementCommon(
        param: LineStatusAcquisitionInputDto,
        resultBean: CheckResultBean,
        cheatCode: string,
        executeUserId: string,
        executeTenantId: string,
        receivedDate: string,
        orderType: string,
        reserveDate: string,
        isPrivate: boolean,
    ): Promise<void> {
        // SO管理共通を呼び出す
        const soObject = new SOObject();

        // サービスオーダID
        soObject.setServiceOrderId(resultBean.others);
        // 申込日
        soObject.setOrderDate(receivedDate);
        // 完了日
        // 変数「オーダ種別」は「即時オーダ」 或いは「予約実行オーダ」の場合
        if (
            Constants.ORDER_TYPE_0.equals(orderType) ||
            Constants.ORDER_TYPE_2.equals(orderType)
        ) {
            soObject.setExecDate(MvnoUtil.getDateTimeNow());
        } else {
            soObject.setExecDate(null);
        }
        // 予約日
        // 変数「オーダ種別」は「予約前オーダ」の場合
        if (Constants.ORDER_TYPE_1.equals(orderType)) {
            soObject.setReserveDate(reserveDate);
        } else {
            soObject.setReserveDate(null);
        }
        // オーダ種別
        soObject.setOrderType(orderType);
        // オーダステータス
        soObject.setOrderStatus(cheatCode);
        // 回線ID
        soObject.setLineId(param.lineNo);
        // 回線グループID
        soObject.setLineGroupId("");
        // 所属テナントID
        soObject.setTenantId(param.tenantId);
        // 実行ユーザID
        soObject.setExecuteUserId(executeUserId);
        // 実行テナントID
        soObject.setExecuteTenantId(executeTenantId);
        // 機能種別
        soObject.setFunctionType(param.requestHeader.functionType);
        // 操作区分
        soObject.setOperationDivision(getStringParameter(param.status));
        // 変更前プランID
        soObject.setChangeOldplanId("");
        // 変更後プランID
        soObject.setChangeNewplanId("");
        // 回線所属変更回線ID
        soObject.setChangePlanId("");
        // 基本容量
        soObject.setBasicCapacity("");
        // オプションプランID
        soObject.setOptionPlanId("");
        // 内部呼び出し
        soObject.setInternalFlag(isPrivate);
        // REST電文
        // 変数「オーダ種別」は「予約前オーダ」の場合
        if (Constants.ORDER_TYPE_1.equals(orderType)) {
            soObject.setRestMessage(MvnoUtil.getRestMessage(param));
        } else {
            soObject.setRestMessage(null);
        }
        await this.soCommon.soCommon(soObject);
    }

    /**
     * SOAP API送信
     *
     * @param lineNo 回線ID
     * @param status 更新ステータス
     * @param tenantId テナントID
     * @param sequenceNo シーケンスNo
     * @param tpcDestInfo TPC情報
     * @param primaryFlg プライマリフラグ
     * @return SOAPCommonOutputDto SOAP応答電文情報
     * @throws Exception
     */
    private async sendSoapApiToTpc(
        lineNo: string,
        status: string,
        tenantId: string,
        sequenceNo: string,
        tpcDestInfo: string,
        primaryFlg: boolean,
    ): Promise<SOAPCommonOutputDto> {
        // TPC電文作成
        const paramList: ParameterName[] = [];
        const paramName = new ParameterName();
        paramName.setName("phone_number");
        paramName.setValue(lineNo);
        paramList.push(paramName);
        const parmActivate = new ParameterName();
        parmActivate.setName("activate");
        parmActivate.setValue(status);
        paramList.push(parmActivate);

        // SOAP-API 電文送受信
        return await this.sOAPCommon.callWebSoapApi(
            "serviceProfileRequestLite",
            "Mod",
            "",
            paramList,
            tenantId,
            sequenceNo,
            tpcDestInfo,
            primaryFlg,
        );
    }
}
