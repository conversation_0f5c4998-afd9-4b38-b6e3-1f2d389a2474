import { fromUnixTime } from "date-fns";

import Constants from "@/core/constant/Constants";
import MsgKeysConstants from "@/core/constant/MsgKeysConstants";
import ResultCdConstants from "@/core/constant/ResultCdConstants";
import { SimTypes } from "@/constants/simConstants";

import ApiCommon from "@/core/common/ApiCommon";
import MvnoUtil from "@/core/common/MvnoUtil";
import RESTCommon from "@/core/common/RESTCommon";
import SOCommon from "@/core/common/SOCommon";
import TenantManage from "@/core/common/TenantManage";

import APILinesDAO from "@/core/dao/APILinesDAO";
import ReissueSimInputDto from "@/core/dto/ReissueSimInputDto";
import ReissueSimOutputDto from "@/core/dto/ReissueSimOutputDto";
import SOObject from "@/core/dto/SOObject";

import CardEntity from "@/core/entity/CardEntity";
import { LinesEntity } from "@/core/entity/LinesEntity";

import { isSQLException } from "@/helpers/queryHelper";

import { IFService } from "../IFService";
import { getStringParameter } from "@/types/parameter.string";

export default class ReissueSimService
    extends RESTCommon
    implements IFService<ReissueSimInputDto, ReissueSimOutputDto>
{
    /**
     * API回線DAO
     */
    // @Autowired
    // private APILinesDAO apiLinesDao;
    private apiLinesDao = new APILinesDAO(this.request, this.context);

    /**
     * SO管理共通DAO
     */
    // @Autowired
    // private SOCommon soCommon;
    private soCommon = new SOCommon(this.request, this.context);

    /**
     * テナント管理
     */
    // @Autowired
    // private TenantManage tenantManage;
    private tenantManage = new TenantManage(this.request, this.context);

    /**
     * CSV出力共通
     */
    // @Autowired
    // private RestCsvCommon restCsvCommon;

    /**
     * API共通処理
     */
    // @Autowired
    // private ApiCommon apiCommon;
    private apiCommon = new ApiCommon(this.request, this.context);

    /**
     * SIM再発行機能<BR>
     * <PRE>
     * REST APIにより、SIMの再発行を行う。
     * </PRE>
     *
     * @param param SIM再発行InputDto
     * @param ipaddress クライアントIP
     * @param params 可変長引数
     * @return Object 返却値オブジェクト
     */
    public async service(
        param: ReissueSimInputDto,
        ipaddress: string,
    ): Promise<ReissueSimOutputDto> {
        this.debug(
            param.tenantId,
            param.requestHeader.sequenceNo,
            MsgKeysConstants.APRESD0001,
            this.getClassName(),
            "service",
            JSON.stringify(param),
        );
        /** 送信番号取得 */
        const sequenceNo = param.requestHeader.sequenceNo;
        /** 送信元システムID取得 */
        const senderSystemId = param.requestHeader.senderSystemId;
        /** API認証キー取得 */
        const apiKey = param.requestHeader.apiKey;
        /** 機能種別取得 */
        const functionType = param.requestHeader.functionType;

        /** テナントID */
        const tenantId = param.tenantId;
        /** 対象回線の所属テナントID */
        const targetTenantId = param.targetTenantId;
        /** 回線番号 */
        const lineNo = param.lineNo;
        /** SIM番号 */
        const simNo = param.sim_no;
        /** SIM種別 */
        const simType = param.sim_type;
        /** カード種別ID */
        const cardTypeId = param.cardTypeId;
        /** MVNO顧客CSV連携不要フラグ */
        const csvUnnecessaryFlag = getStringParameter(param.csvUnnecessaryFlag);

        /** REST API共通処理が呼び出されたシステム日付 */
        const systemDateTime = this.context.responseHeader.receivedDate;

        /** 基準日(yyyyMMdd) */
        const targetDate = systemDateTime.substring(0, 10).replace(/\//g, "");

        const apiId = this.context.responseHeader.apiProcessID;

        /** 変数 回線情報更新エラー */
        let lineInfoUpdateError = false;

        /** 返却レスポンスヘッダ生成 */
        let processCode = ResultCdConstants.CODE_000000; // this.context.responseHeader.processCode

        let isESIMProfileRedownload = false;

        try {
            // 業務チェック
            // テナントIDの値をチェックし、リクエストが卸ポータルフロントからのものかを判断する
            // (※卸ポータルフロントからの実行であることを示す固定値)
            if (!Constants.FRONT_API_TENANT_ID.equals(tenantId)) {
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APRESW0006,
                    tenantId,
                );
                processCode = ResultCdConstants.CODE_530101;
                await this.soManagementCommon(
                    param,
                    apiId,
                    processCode,
                    systemDateTime,
                );
                return this.resultEdit(
                    param,
                    processCode,
                    apiId,
                    systemDateTime,
                    lineInfoUpdateError,
                );
            }

            // 対象の回線が既に廃止されていないかを確認する
            let aboneLineCheckResult: LinesEntity = null;
            try {
                aboneLineCheckResult =
                    await this.apiLinesDao.getLineInfoByLines(lineNo);
            } catch (e) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    processCode = ResultCdConstants.CODE_530102;
                }
                throw e;
            }
            // 変数「対象回線廃止確認結果」より判定する
            if (
                aboneLineCheckResult === null ||
                "03".equals(aboneLineCheckResult.lineStatus)
            ) {
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APRESW0001,
                    lineNo,
                );
                processCode = ResultCdConstants.CODE_530102;
                await this.soManagementCommon(
                    param,
                    apiId,
                    processCode,
                    systemDateTime,
                );
                return this.resultEdit(
                    param,
                    processCode,
                    apiId,
                    systemDateTime,
                    lineInfoUpdateError,
                );
            }

            // 回線IDと対象回線の所属テナントIDの関係をチェックする
            let tenantCheckResult: [boolean, string] = [false, ""];
            try {
                tenantCheckResult = await this.tenantManage.doCheck(
                    lineNo,
                    targetTenantId,
                );
            } catch (e) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    processCode = ResultCdConstants.CODE_530103;
                }
                throw e;
            }

            if (!tenantCheckResult[0]) {
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APRESW0002,
                    lineNo,
                    targetTenantId,
                );
                processCode = ResultCdConstants.CODE_530103;
                await this.soManagementCommon(
                    param,
                    apiId,
                    processCode,
                    systemDateTime,
                );
                return this.resultEdit(
                    param,
                    processCode,
                    apiId,
                    systemDateTime,
                    lineInfoUpdateError,
                );
            }

            // 対象回線の廃止オーダが受付中でないかチェックする
            let checkAbolishSoResult = false;
            try {
                checkAbolishSoResult = await this.apiCommon.checkAbolishSo(
                    lineNo,
                    targetDate,
                );
            } catch (e) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    processCode = ResultCdConstants.CODE_530104;
                }
                throw e;
            }
            // 変数「対象回線廃止オーダチェック結果」より判定する
            if (!checkAbolishSoResult) {
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APRESW0003,
                    lineNo,
                );
                processCode = ResultCdConstants.CODE_530104;
                await this.soManagementCommon(
                    param,
                    apiId,
                    processCode,
                    systemDateTime,
                );
                return this.resultEdit(
                    param,
                    processCode,
                    apiId,
                    systemDateTime,
                    lineInfoUpdateError,
                );
            }

            // ロジック処理(即時オーダ)
            // カード種別名を取得する
            let cardInfo: CardEntity = null;
            try {
                cardInfo = await this.apiLinesDao.getCard(cardTypeId);
            } catch (e) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    lineInfoUpdateError = true;
                    processCode = ResultCdConstants.CODE_000000;
                }
                throw e;
            }
            // 変数「カード種別ID取得結果」より判定する
            if (cardInfo === null) {
                lineInfoUpdateError = true;
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APRESW0004,
                    cardTypeId,
                );
                // NOTE even though cardInfo is null, the original code sets processCode to CODE_000000
                processCode = ResultCdConstants.CODE_000000;
                await this.soManagementCommon(
                    param,
                    apiId,
                    processCode,
                    systemDateTime,
                );
                return this.resultEdit(
                    param,
                    processCode,
                    apiId,
                    systemDateTime,
                    lineInfoUpdateError,
                );
            }

            // 更新情報を設定する
            const updateData = new LinesEntity();
            updateData.lineId = lineNo;
            updateData.simNumber = simNo;
            updateData.modelType = cardInfo.deviceTypeName;
            updateData.simType = simType;
            updateData.deviceTypeId = cardTypeId;
            updateData.updatedAt = fromUnixTime(
                MvnoUtil.convDateFormat(systemDateTime) / 1000,
            );

            // check for eSIM profile redownload
            // if request sim type is eSIM and the sim number is the same as the one in the database
            if (simType === SimTypes.ESIM) {
                if (aboneLineCheckResult.simNumber === simNo) {
                    isESIMProfileRedownload = true;
                }
                this.context.log(
                    "eSIM profile download:",
                    isESIMProfileRedownload,
                );
            }

            // 回線情報テーブルを更新する
            try {
                await this.apiLinesDao.updateLineSimInfo(updateData);
            } catch (e: any) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    lineInfoUpdateError = true;
                    processCode = ResultCdConstants.CODE_000000;
                    throw e;
                }
                lineInfoUpdateError = true;
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APRESW0005,
                    lineNo,
                );
                processCode = ResultCdConstants.CODE_000000;
                await this.soManagementCommon(
                    param,
                    apiId,
                    processCode,
                    systemDateTime,
                );
                return this.resultEdit(
                    param,
                    processCode,
                    apiId,
                    systemDateTime,
                    lineInfoUpdateError,
                );
            }

            // CSVファイル用データ収集
            let csvUnnecessary: boolean;
            if (csvUnnecessaryFlag === "1") {
                csvUnnecessary = true;
            } else {
                csvUnnecessary = false;
                // N番の有無を判定する
                if (
                    tenantCheckResult[1] === null ||
                    tenantCheckResult[1] === ""
                ) {
                    super.error(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APRESE0001,
                        lineNo,
                        targetTenantId,
                    );
                    csvUnnecessary = true;
                }
            }

            // デバッグログ出力
            super.debug(
                param.tenantId,
                param.requestHeader.sequenceNo,
                MsgKeysConstants.APRESD0002,
                this.getClassName(),
                "service",
            );
            processCode = ResultCdConstants.CODE_000000;
            await this.soManagementCommon(
                param,
                apiId,
                processCode,
                systemDateTime,
            );
            return this.resultEdit(
                param,
                processCode,
                apiId,
                systemDateTime,
                lineInfoUpdateError,
                tenantCheckResult[1],
                csvUnnecessary,
                isESIMProfileRedownload,
            );
        } catch (e: any) {
            if (isSQLException(e)) {
                // DBアクセスリトライエラーの場合
                // リターンコード値は各エラー時にて設定済み
                super.error(
                    e,
                    param.tenantId,
                    param.requestHeader.sequenceNo,
                    MsgKeysConstants.APCOME0402,
                    functionType,
                );
                await this.soManagementCommon(
                    param,
                    apiId,
                    processCode,
                    systemDateTime,
                );
                return this.resultEdit(
                    param,
                    processCode,
                    apiId,
                    systemDateTime,
                    lineInfoUpdateError,
                );
            }
            super.error(
                param.tenantId,
                param.requestHeader.sequenceNo,
                MsgKeysConstants.APCOME0401,
                e,
            );
            processCode = ResultCdConstants.CODE_999999;
            await this.soManagementCommon(
                param,
                apiId,
                processCode,
                systemDateTime,
            );
            return this.resultEdit(
                param,
                processCode,
                apiId,
                systemDateTime,
                lineInfoUpdateError,
            );
        }
    }

    private resultEdit(
        param: ReissueSimInputDto,
        processCode: string,
        apiId: string,
        receivedDate: string,
        lineInfoUpdateError: boolean,
        nNo: string = null,
        csvUnnecessary: boolean = true,
        isESIMProfileRedownload: boolean = false,
    ) {
        const reissueSimOutputDto: ReissueSimOutputDto = {
            jsonBody: {
                responseHeader: {
                    sequenceNo: param.requestHeader.sequenceNo,
                    processCode,
                    receivedDate,
                    apiProcessID: apiId,
                },
            },
            additionalData: {
                nNo: nNo || "",
                lineInfoUpdateError,
                csvUnnecessary,
                isESIMProfileRedownload,
            },
        };
        return reissueSimOutputDto;
    }

    /**
     * SO管理共通パラメータ設定
     *
     * @param param パラメータ
     * @param apiId API処理ID
     * @param code オーダステータス
     * @param receivedDate REST API共通処理が呼び出されたシステム日付
     * @throws Exception 予期せぬエラー
     */
    private async soManagementCommon(
        param: ReissueSimInputDto,
        apiId: string,
        code: string,
        receivedDate: string,
    ) {
        // SO管理共通を呼び出す
        const soObject = new SOObject();

        // サービスオーダID
        soObject.setServiceOrderId(apiId);
        // 投入日
        soObject.setOrderDate(receivedDate);
        // 予約日
        soObject.setReserveDate(null);
        // オーダ種別
        soObject.setOrderType(null);
        // 完了日
        soObject.setExecDate(MvnoUtil.getDateTimeNow());
        // オーダステータス
        soObject.setOrderStatus(code);
        // 回線ID
        soObject.setLineId(param.lineNo);
        // 回線グループID
        soObject.setLineGroupId(null);
        // 所属テナントID
        soObject.setTenantId(param.targetTenantId);
        // 実行ユーザID
        soObject.setExecuteUserId(null);
        // 実行テナントID
        soObject.setExecuteTenantId(null);
        // 機能種別
        soObject.setFunctionType(param.requestHeader.functionType);
        // 操作区分
        soObject.setOperationDivision("");
        // 変更前プランID
        soObject.setChangeOldplanId("");
        // 変更後プランID
        soObject.setChangeNewplanId("");
        // 回線所属変更回線ID
        soObject.setChangePlanId("");
        // 基本容量
        soObject.setBasicCapacity("");
        // オプションプランID
        soObject.setOptionPlanId("");
        // REST電文
        soObject.setRestMessage(null);

        await this.soCommon.soCommon(soObject);
    }
}
