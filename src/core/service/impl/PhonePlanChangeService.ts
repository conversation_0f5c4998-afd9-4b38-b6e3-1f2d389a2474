import RESTCommon from "@/core/common/RESTCommon";
import { IFService } from "@/core/service/IFService";
import PhonePlanChangeInputDto from "@/core/dto/PhonePlanChangeInputDto";
import PhonePlanChangeOutputDto from "@/core/dto/PhonePlanChangeOutputDto";
import APICommonDAO from "@/core/dao/APICommonDAO";
import APILinesDAO from "@/core/dao/APILinesDAO";
import SOCommon from "@/core/common/SOCommon";
import TenantManage from "@/core/common/TenantManage";
import ResultCdConstants from "@/core/constant/ResultCdConstants";
import MvnoUtil from "@/core/common/MvnoUtil";
import ResponseHeader from "@/core/dto/ResponseHeader";
import { isSQLException, retryQuery } from "@/helpers/queryHelper";
import { Transaction } from "sequelize";
import MsgKeysConstants from "@/core/constant/MsgKeysConstants";
import { isLockNotAvailableError, usePsql } from "@/database/psql";
import SOObject from "@/core/dto/SOObject";
import Constants from "@/core/constant/Constants";
import { LinesEntity } from "@/core/entity/LinesEntity";
import { TenantsEntity } from "@/core/entity/TenantsEntity";
import CardEntity from "@/core/entity/CardEntity";

export default class PhonePlanChangeService
    extends RESTCommon
    implements IFService<PhonePlanChangeInputDto, PhonePlanChangeOutputDto>
{
    /** 排他制御の最大リトライ回数を規定 */
    private LOCK_RETRY_COUNT = 5;

    /** 排他制御の待ちミリ秒数を規定 */
    private LOCK_WAIT_MILLISEC = 1000;

    /**
     * API共通DAO
     */
    private aPICommonDAO: APICommonDAO = new APICommonDAO(this.request, this.context);

    /**
     * API回線DAO
     */
    private apiLinesDao: APILinesDAO = new APILinesDAO(this.request, this.context);

    /**
     * SO管理共通DAO
     */
    private soCommon: SOCommon = new SOCommon(this.request, this.context);

    /**
     * テナント管理クラス
     */
    private tenantManager: TenantManage = new TenantManage(this.request, this.context);

    /**
     * 0035でんわプラン変更機能。<BR>
     *
     * <PRE>
     * REST APIにより、受信したパラメータのアクセス方式(契約種別)/カード種別に変更し、
     * Swimmyへ変更情報を連携する。
     * </PRE>
     *
     * @param param 0035でんわプラン変更機能インプット
     * @param isPrivate 内部呼出フラグ
     * @param params 可変長の引数(内部から呼出す時に使用するパラメータ)
     * @return PhonePlanChangeOutputDto 0035でんわプラン変更機能アウトプット
     * @throws Exception 例外
     */
    public async service(
        param: PhonePlanChangeInputDto,
    ): Promise<PhonePlanChangeOutputDto> {
        this.context.log(
            "PhonePlanChangeService.service start",
            JSON.stringify(param),
        );
        // 入力電文の内容を取得
        // 送信番号取得
        const sequenceNo = param.requestHeader.sequenceNo;
        // 送信元システムID取得
        const senderSystemId = param.requestHeader.senderSystemId;
        // API認証キー取得
        const apiKey = param.requestHeader.apiKey;
        // 機能種別取得
        const functionType = param.requestHeader.functionType;
        // テナントID取得
        const tenantId = param.tenantId;
        // 対象回線の所属テナントID
        const targetTenantId = param.targetTenantId;
        // 回線ID
        const lineId = param.lineNo;
        // 0035でんわプランID
        const voicePlanId = param.voicePlanId;

        // 変数「処理コード」初期化
        let handleCode = ResultCdConstants.CODE_000000;
        // 変数「システム時刻」取得
        const receivedDate = MvnoUtil.getDateTimeNow();
        // 変数「API処理ID」
        const apiHandleId = this.context.responseHeader.apiProcessID;       // = resultBean.others
        const sequelize = await usePsql();

        let tx: Transaction = null;
        let canRollback = false;

        try {
            // No.1 REST-API電文パラメータ相関チェック
            // ① 卸ポータルフロントからのリクエストかチェック
            if (Constants.FRONT_API_TENANT_ID !== tenantId) {
                // ログ出力
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APPPCW0001, tenantId);
                handleCode = ResultCdConstants.CODE_590101;
                // SO管理共通
                await this.soManagement(param, apiHandleId, handleCode, receivedDate, functionType);
                // 返却値編集
                const returnParam: PhonePlanChangeOutputDto = this.returnEdit(param, handleCode, apiHandleId, receivedDate);
                // 値の返却
                return returnParam;
            }

            // ② 対象の回線が既に廃止されていないかを確認する
            let aboneLineCheckResult: LinesEntity;
            try {
                aboneLineCheckResult = await this.apiLinesDao.getLineInfoByLines(lineId);
            } catch (e) {
                if (isSQLException(e)) {
                    handleCode = ResultCdConstants.CODE_590102;
                }
                throw e;
            }

            // 変数「対象回線廃止確認結果」より判定する
            if (aboneLineCheckResult == null || "03" === aboneLineCheckResult.lineStatus) {
                // ログ出力
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APPPCW0002, lineId);
                handleCode = ResultCdConstants.CODE_590102;
                // SO管理共通
                await this.soManagement(param, apiHandleId, handleCode, receivedDate, functionType);
                // 返却値編集
                const returnParam: PhonePlanChangeOutputDto = this.returnEdit(param, handleCode, apiHandleId, receivedDate);
                // 値の返却
                return returnParam;
            }

            // ③ 回線IDと対象回線の所属テナントIDの関係をチェックする
            let checkResult: [boolean, string] = null;
            try {
                checkResult = await this.tenantManager.doCheck(lineId, targetTenantId);
            } catch (e) {
                if (isSQLException(e)) {
                    handleCode = ResultCdConstants.CODE_590103;
                }
                throw e;
            }
            // 変数「関係チェック結果」より判定する
            if (!checkResult[0]) {
                // ログ出力
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APPPCW0003, lineId, targetTenantId);
                handleCode = ResultCdConstants.CODE_590103;
                // SO管理共通
                await this.soManagement(param, apiHandleId, handleCode, receivedDate, functionType);
                // 返却値編集
                const returnParam: PhonePlanChangeOutputDto = this.returnEdit(param, handleCode, apiHandleId, receivedDate);
                // 値の返却
                return returnParam;
            }

            // ④ 0035でんわプラン名が存在するかをチェックする
            let voicePlanName: string | null = null;
            try {
                voicePlanName = await this.aPICommonDAO.getVoicePlanName(targetTenantId, voicePlanId);
            } catch (e) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    handleCode = ResultCdConstants.CODE_590104;
                }
                throw e;
            }
            if (voicePlanName === null) {
                // ログ出力
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APPPCW0004, targetTenantId, voicePlanId);
                handleCode = ResultCdConstants.CODE_590104;
                // SO管理共通
                await this.soManagement(param, apiHandleId, handleCode, receivedDate, functionType);
                // 返却値編集
                const returnParam: PhonePlanChangeOutputDto = this.returnEdit(param, handleCode, apiHandleId, receivedDate);
                // 値の返却
                return returnParam;
            }

            const result = await retryQuery(
                this.context,
                "PhonePlanChangeService.service",
                async () => {
                    if (tx !== null && canRollback) {
                        // try to rollback before continuing
                        try {
                            this.context.log(
                                "PhonePlanChangeService.service rollback",
                            );
                            await tx.rollback();
                        } finally {
                            canRollback = false;
                        }
                    }
                    // トランザクション開始
                    tx = await sequelize.transaction();
                    canRollback = true;

                    // No.2 回線行ロック取得
                    for (let linelockTime = 0; linelockTime < this.LOCK_RETRY_COUNT; linelockTime++) {
                        try {
                            // 回線情報テーブルから情報を取得する
                            const lineslockInfo = await this.apiLinesDao.getLineInfoforUpdate(lineId, tx);

                            if (lineslockInfo == null) {
                                try {
                                    // rollback before returning
                                    await tx.rollback();
                                } finally {
                                    canRollback = false;
                                }
                                // レコードが取得できなかった場合、エラー
                                // ログ出力
                                super.warn(tenantId, sequenceNo, MsgKeysConstants.APPPCW0005, lineId);
                                handleCode = ResultCdConstants.CODE_590201;
                                // SO管理共通
                                await this.soManagement(param, apiHandleId, handleCode, receivedDate, functionType);
                                // 返却値編集
                                const returnParam: PhonePlanChangeOutputDto = this.returnEdit(param, handleCode, apiHandleId, receivedDate);
                                // 値の返却
                                return returnParam;

                            } else {
                                // ロックが取得出来たらループから抜ける
                                break;
                            }
                        } catch (error) {
                            if (isLockNotAvailableError(error)) {
                                // ロック取得に失敗した場合 (Pessimistic Lock)
                                if (linelockTime === 4) {
                                    // タイムアウトまで行ロック取得に失敗した場合
                                    // トランザクションロールバック
                                    await tx.rollback();
                                    canRollback = false;
                                    // ログ出力
                                    super.warn(
                                        tenantId,
                                        sequenceNo,
                                        MsgKeysConstants.APNCCW0005,
                                        lineId,
                                    );
                                    handleCode = ResultCdConstants.CODE_590201;
                                    // SO管理共通
                                    await this.soManagement(
                                        param,
                                        apiHandleId,
                                        handleCode,
                                        receivedDate,
                                        functionType,
                                    );
                                    // 返却値編集
                                    const returnParam = this.returnEdit(
                                        param,
                                        handleCode,
                                        apiHandleId,
                                        receivedDate,
                                    );
                                    // 値の返却
                                    return returnParam;
                                }
                                await tx.rollback();
                                canRollback = false;
                                // 次のロック取得まで待ちミリ秒数分待機
                                await new Promise((resolve) =>
                                    setTimeout(
                                        resolve,
                                        this.LOCK_WAIT_MILLISEC,
                                    ),
                                );
                                // ロールバックし、トランザクション再開
                                // moved rollback to before waiting
                                tx = await sequelize.transaction();
                                canRollback = true;
                            } else if (isSQLException(error)) {
                                // DBアクセスリトライエラーの場合 (not related to lock)
                                handleCode = ResultCdConstants.CODE_590201;
                                throw error;
                            } else {
                                // other exception
                                try {
                                    // try to rollback before returning
                                    await tx.rollback();
                                } finally {
                                    canRollback = false;
                                }
                                // 行ロック取得時にエラーが発生した場合
                                // ログ出力
                                super.warn(tenantId, sequenceNo, MsgKeysConstants.APPPCW0005, lineId);
                                handleCode = ResultCdConstants.CODE_590201;
                                // SO管理共通
                                await this.soManagement(param, apiHandleId, handleCode, receivedDate, functionType);
                                // 返却値編集
                                const returnParam: PhonePlanChangeOutputDto = this.returnEdit(param, handleCode, apiHandleId, receivedDate);
                                // 値の返却
                                return returnParam;
                            }
                        }
                    }

                    // No.3 回線情報の更新
                    try {
                        // 0035でんわプランID、0035でんわプラン名を更新
                        await this.apiLinesDao.updateLineVoicePlan(lineId, voicePlanId, voicePlanName, MvnoUtil.convDateFormat(receivedDate), tx);
                    } catch (e) {
                        if (isSQLException(e)) {
                            // DBアクセスリトライエラーの場合
                            handleCode = ResultCdConstants.CODE_590301;
                            throw e;
                        } else {
                            try {
                                await tx.rollback();
                            } finally {
                                canRollback = false;
                            }
                            super.error(e, tenantId, sequenceNo, MsgKeysConstants.APPPCE0001, lineId);
                            handleCode = ResultCdConstants.CODE_590301;
                            // SO管理共通
                            await this.soManagement(param, apiHandleId, handleCode, receivedDate, functionType);
                            // 返却値編集
                            const returnParam: PhonePlanChangeOutputDto = this.returnEdit(param, handleCode, apiHandleId, receivedDate);
                            // 値の返却
                            return returnParam;
                        }
                    }

                    // トランザクションコミット
                    await tx.commit();
                    canRollback = false;

                    // リトライしたタイミングでエラーコードを設定しているので
                    // リトライ後に正常となった場合は正常コードに戻す
                    handleCode = ResultCdConstants.CODE_000000;

                    return null;
                },
                this.apDbRetryMaxCnt,
                this.apDbRetryInterval,
            );
            if (result !== null) {
                // if not null then it is an early return
                return result;
            }

            // SO管理共通
            await this.soManagement(param, apiHandleId, handleCode, receivedDate, functionType);

            // 返却値編集
            return this.returnEdit(param, handleCode, apiHandleId, receivedDate);
        } catch (e) {
            if (tx !== null && canRollback) {
                // トランザクション開始中にエラーの場合、ロールバック
                await tx.rollback();
            }
            if (isSQLException(e)) {
                super.error(e, param.tenantId, param.requestHeader.sequenceNo,
                    MsgKeysConstants.APCOME0402, e.message);
                return this.returnEdit(param, handleCode, apiHandleId, receivedDate);
            } else {
                super.error(e, param.tenantId, param.requestHeader.sequenceNo,
                    MsgKeysConstants.APCOME0401, e.message);
                handleCode = ResultCdConstants.CODE_999999;
                return this.returnEdit(param, handleCode, apiHandleId, receivedDate);
            }
        }
    }

    /**
     * SO管理共通パラメータ設定
     *
     * @param param        0035でんわプラン変更機能インプット
     * @param resultBean   チェック結果
     * @param code         オーダステータス
     * @param receivedDate REST API共通処理が呼び出されたシステム日付
     * @param functionType 機能種別
     * @throws Exception 例外
     */
    private async soManagement(
        param: PhonePlanChangeInputDto,
        apiProcessID: string,
        code: string,
        receivedDate: string,
        functionType: string
    ) {
        // SO管理共通を呼び出す
        const soObject = new SOObject();
        // サービスオーダID
        // soObject.setServiceOrderId(resultBean.getOthers()); - according to BaseHandler.ts line 78, apiProcessID = resultBean.others
        soObject.setServiceOrderId(apiProcessID);
        // 投入日
        soObject.setOrderDate(receivedDate);
        // 予約日
        soObject.setReserveDate(null);
        // オーダ種別
        soObject.setOrderType(null);
        // 完了日
        soObject.setExecDate(MvnoUtil.getDateTimeNow());
        // オーダステータス
        soObject.setOrderStatus(code);
        // 回線ID
        soObject.setLineId(param.lineNo);
        // 回線グループID
        soObject.setLineGroupId(null);
        // 所属テナントID
        soObject.setTenantId(param.targetTenantId);
        // 実行ユーザID
        soObject.setExecuteUserId(null);
        // 実行テナントID
        soObject.setExecuteTenantId(null);
        // 機能種別
        soObject.setFunctionType(functionType);
        // 操作区分
        soObject.setOperationDivision("");
        // 変更前プランID
        soObject.setChangeOldplanId("");
        // 変更後プランID
        soObject.setChangeNewplanId("");
        // 回線所属変更回線ID
        soObject.setChangePlanId("");
        // 基本容量
        soObject.setBasicCapacity("");
        // オプションプランID
        soObject.setOptionPlanId("");
        // 変更後0035でんわプランID
        soObject.setChangeNewVoicePlanId(param.voicePlanId);
        // REST電文
        soObject.setRestMessage(null);

        await this.soCommon.soCommon(soObject);
    }

    /**
     * 0035でんわプラン変更機能返却値編集
     *
     * @param  param                    0035でんわプラン変更機能インプット
     * @param  handleCode               処理コード
     * @param  apiHandleId              API処理ID
     * @param  receivedDate             REST API共通処理が呼び出されたシステム日付
     * @return PhonePlanChangeOutputDto 0035でんわプラン変更機能アウトプット
     * @throws ParseException フォーマットエラー
     */
    private returnEdit(
        param: PhonePlanChangeInputDto,
        handleCode: string,
        apiHandleId: string,
        receivedDate: string
    ): PhonePlanChangeOutputDto {
        const responseHeader = {
            sequenceNo: param.requestHeader.sequenceNo,
            receivedDate,
            processCode: handleCode,
            apiProcessID: apiHandleId
        }

        // 出力情報を作成する
        return {
            jsonBody: {
                responseHeader
            }
        };
    }
}