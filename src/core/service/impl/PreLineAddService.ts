import "@/types/string.extension";

import config from "config";
import { fromUnixTime } from "date-fns";
import { Transaction } from "sequelize";

import SOAPException from "@/types/soapException";

import { IFService } from "../IFService";

import Constants from "@/core/constant/Constants";
import MsgKeysConstants from "@/core/constant/MsgKeysConstants";
import ResultCdConstants from "@/core/constant/ResultCdConstants";

import Check from "@/core/common/Check";
import CheckUtil from "@/core/common/CheckUtil";
import MvnoUtil from "@/core/common/MvnoUtil";
import RESTCommon from "@/core/common/RESTCommon";
import SOAPCommon from "@/core/common/SOAPCommon";
import SOCommon from "@/core/common/SOCommon";
import StringUtils from "@/core/common/StringUtils";
import TenantManage from "@/core/common/TenantManage";

import APICommonDAO from "@/core/dao/APICommonDAO";
import API<PERSON>inesDAO from "@/core/dao/APILinesDAO";
import APISoDAO from "@/core/dao/APISoDAO";

import ParameterName from "@/core/dto/ParameterName";
import PreLineAddInputDto from "@/core/dto/PreLineAddInputDto";
import PreLineAddOutputDto, {
    LineOptionsTBan,
} from "@/core/dto/PreLineAddOutputDto";
import ResponseHeader from "@/core/dto/ResponseHeader";
import SOAPCommonOutputDto from "@/core/dto/SOAPCommonOutputDto";
import SOObject from "@/core/dto/SOObject";

import CardEntity from "@/core/entity/CardEntity";
import { CustomerInfoEntity } from "@/core/entity/CustomerInfoEntity";
import LineOptionsEntity from "@/core/entity/LineOptionsEntity";
import { LinesEntity } from "@/core/entity/LinesEntity";
import PlansEntity from "@/core/entity/PlansEntity";
import { TenantNnumbersEntity } from "@/core/entity/TenantNnumbersEntity";
import TenantPlanLineOptionsEntity from "@/core/entity/TenantPlanLineOptionsEntity";
import { TenantsEntity } from "@/core/entity/TenantsEntity";
import VoiceInfoEntity from "@/core/entity/VoiceInfoEntity";

import { isLockNotAvailableError, usePsql } from "@/database/psql";
import { isSQLException, retryQuery } from "@/helpers/queryHelper";
import { getConfig } from "@/helpers/configHelper";
import { isNone } from "@/utils";

import CoreAPIService from "@/services/coreAPIService";
import FrontAPIService from "@/services/frontAPIService";
import CoreSwimmyApiLog from "@/models/coreSwimmyApiLog";
import { getStringParameter } from "@/types/parameter.string";

export default class PreLineAddService
    extends RESTCommon
    implements IFService<PreLineAddInputDto, PreLineAddOutputDto>
{
    /** XML電文返信結果 */
    private static readonly CONST_RESULT = "NG";

    /**
     * SOAP API連携機能初期化
     */
    private soapCommon = new SOAPCommon(this.request, this.context);

    /**
     * API回線DAO
     */
    private apiLinesDao = new APILinesDAO(this.request, this.context);

    /**
     * ApiCommonDAO
     */
    private apiCommonDao = new APICommonDAO(this.request, this.context);

    /**
     * API SO DAOクラス
     */
    private aPISoDAO = new APISoDAO(this.request, this.context);

    /**
     * SO管理共通DAO
     */
    private soCommon = new SOCommon(this.request, this.context);

    /**
     * テナント管理
     */
    private tenantManage = new TenantManage(this.request, this.context);

    /** 定数 Mapキー CSVファイル出力要否 */
    private static readonly KEY_CSV_OUT_KBN = "csvOutKbn";
    /** 定数 Mapキー お客様基本情報 */
    private static readonly KEY_CUSTOMER_INFO = "cunsomerInfo";

    /** 仮登録回線追加受付不可時間(仮登録回線追加機能) */
    private preLineAddNgTime = getConfig("PreLineAddNgTime");

    /**
     * Service for calling Core (self) API
     *
     * Used for cancellation of reserved orders (same mvne)
     */
    private coreApiService = new CoreAPIService(this.request, this.context);

    /**
     * Service for calling Front API
     *
     * Used for cancellation of haishi orders (same mvne)
     */
    private frontApiService = new FrontAPIService(this.request, this.context);

    /** 定数 社内テナント種別-B-OCN */
    private static readonly TENANT_TYPE_BOCN = 2;

    /** 定数 社内テナント種別-UNOモバイル */
    private static readonly TENANT_TYPE_UNOM = 3;

    /** 定数 判定対象回線オプション種別-国際ローミング利用限度額 */
    private static readonly L_OPTTYPE_ROAMING = "1";

    /** 定数 判定対象回線オプション種別-留守番でんわ */
    private static readonly L_OPTTYPE_VOICEMAIL = "2";

    /** 定数 判定対象回線オプション種別-キャッチホン */
    private static readonly L_OPTTYPE_CALLWAITING = "3";

    /** 定数 判定対象回線オプション種別-国際電話 */
    private static readonly L_OPTTYPE_INTLCALL = "4";

    /** 定数 判定対象回線オプション種別-転送でんわ */
    private static readonly L_OPTTYPE_FORWARDING = "5";

    /** 定数 判定対象回線オプション種別-国際着信転送 */
    private static readonly L_OPTTYPE_INTLFORWARDING = "6";

    /** 予約実行日時単位(予約機能) */
    private reservationDateExecutionUnits: string = getConfig(
        "ReservationDateExecutionUnits",
    );

    /** 予約可能制限日数(予約機能) */
    private reservationsLimitDays: string = getConfig("ReservationsLimitDays");

    /** 排他制御の最大リトライ回数を規定 */
    private static readonly LOCK_RETRY_COUNT = 5;

    /** 排他制御の待ちミリ秒数を規定 */
    private static readonly LOCK_WAIT_MILLISEC = 1000;

    /** MNP転入でない */
    public static readonly MNP_IN_TYPE_NO = 0;
    /** MNP転入 */
    public static readonly MNP_IN_TYPE_YES = 1;
    /** 同一MVNE転入 */
    public static readonly MNP_IN_TYPE_SAME_MVNE_YES = 2;

    /** 定数 Mapキー オプション追加CSVファイル出力要否 */
    private static readonly KEY_CSV_OUT_KBN_OPTION_ADD =
        "csvOutputKbnOptionAdd";
    /** 定数 Mapキー 回線廃止CSVファイル出力要否 */
    private static readonly KEY_CSV_OUT_KBN_ABOLITION = "csvOutputKbnAbolition";

    // 対応NW
    /** 3G */
    public static readonly NETWORK_IN_TYPE_3G = "3G";
    /** LTE */
    public static readonly NETWORK_IN_TYPE_LTE = "LTE";
    /** 5G */
    public static readonly NETWORK_IN_TYPE_5G = "5G(NSA)";

    // /**
    //  * DBアクセスリトライ回数
    //  */
    // private apDbRetryMaxCnt = getConfigAsNumber("ApDbRetryMaxCnt");

    // /**
    //  * DBアクセスリトライ間隔
    //  */
    // private apDbRetryInterval = getConfigAsNumber("ApDbRetryInterval");

    /**
     * 仮登録回線追加。<BR>
     * <PRE>
     * REST APIにより、受信したパラメータのSOキャンセルを行う。
     * </PRE>
     *
     * @param param 仮登録回線追加InputDto
     * @param clientIPAddress クライアントIP // NOTE added during refactoring
     * @// param isPrivate 内部呼出フラグ
     * @param obj 可変長引数
     * @return Object 返却オブジェクト
     */
    async service(
        param: PreLineAddInputDto,
        clientIPAddress: string,
        ...obj: any[]
    ): Promise<PreLineAddOutputDto> {
        // NOTE set isPrivate to false
        /** 内部呼出フラグ */
        const isPrivate = false;

        // デバッグログ
        super.debug(
            param.tenantId,
            param.requestHeader.sequenceNo,
            MsgKeysConstants.APPLAD0001,
            this.getClassName(),
            "service",
            JSON.stringify(param),
        );

        /** 送信番号取得 */
        const sequenceNo = param.requestHeader.sequenceNo;
        /** 送信元システムID取得 */
        const senderSystemId = param.requestHeader.senderSystemId;
        /** API認証キー取得 */
        const apiKey = param.requestHeader.apiKey;
        /** 機能種別取得 */
        const functionType = param.requestHeader.functionType;

        /** テナントID */
        const tenantId = param.tenantId;
        /** 回線追加先テナントID */
        const targetTenantId = param.targetTenantId;
        /** 代表N番 */
        const nBan = param.nBan;
        /** 回線番号 */
        const lineNo = param.lineNo;
        /** 卸ポータルプランID */
        const potalPlanID = getStringParameter(param.potalPlanID);
        /** 国際ローミング利用限度額ID */
        const idIntlRoaming = param.id_intlRoaming;
        /** 留守番でんわID */
        const idVoicemail = param.id_voicemail;
        /** キャッチホンID */
        const idCallWaiting = param.id_callWaiting;
        /** 国際電話ID */
        const idIntlCall = param.id_intlCall;
        /** 転送でんわID */
        const idForwarding = param.id_forwarding;
        /** 国際着信転送ID */
        const idIntlForwarding = param.id_intlForwarding;
        /** SIM番号(DN番号) */
        const simNo = param.sim_no;
        /** SIM種別 */
        const simType = param.sim_type;
        // アクセス方式
        const access = param.access;
        const cardTypeId = param.cardTypeId;
        /** PSID */
        const psid = param.psid;
        /** MNP転入フラグ */
        const mnpInFlag = param.mnpInFlag;
        let mnpInType = PreLineAddService.MNP_IN_TYPE_YES;
        /** MVNO顧客CSV連携不要フラグ */
        const csvUnnecessaryFlag = getStringParameter(param.csvUnnecessaryFlag);
        let csvUnnecessaryFlagInside = true;

        /**
         * Flag to indicate whether reserved orders (プラン変更 and 回線廃止) have been sent to
         * Core's Cancel API and Front Cancel API (haishi only).
         *
         * *Only used for 同一MVNE order*
         */
        let isSameMvneCancelRequestSent = false;

        // IMSI
        const imsi = param.imsi;
        // PUK1
        const puk1 = param.puk1;
        // PUK2
        const puk2 = param.puk2;
        // 利用状態
        const usageStatus = param.lineStatus;

        /** プラン情報 */
        let plansInfo: PlansEntity = null;
        /** カード情報 */
        let cardInfo: CardEntity = null;
        /** CSV作成要否 */
        let csvOutputKbn: string = null;
        // オプション追加CSVの作成要否
        let optionAddCsvOutputKbn: string = null;
        // 回線廃止CSVの作成要否
        let abolitionCsvOutputKbn: string = null;
        /** ファイル名通番 */
        // let fileNameNo: string = null;
        // オプション追加CSVのファイル名通番
        let optionFileNameNo: string = null;
        // 回線廃止CSVのファイル名通番
        let abolitionFileNameNo: string = null;
        /** CSVファイル項目数 */
        // const csvColNum = 154;
        /** CSVファイル出力データ */
        // let csvData: string[] = new String[csvColNum];
        // let csvData: string[] = null;
        /** オプション追加CSVファイル出力データ */
        // String[] optionCsvData = new String[49];
        // let optionCsvData: string[] = null;
        /** 回線廃止CSVファイル出力データ */
        // String[] abolitionCsvData = new String[154];
        // let abolitionCsvData: string[] = null;

        /** お客様基本情報 */
        let customerInfo: CustomerInfoEntity = null;
        /** API処理Id */
        const apiId: string = this.context.responseHeader.apiProcessID;

        /** 予約日 */
        let reserveDate = param.reserve_date;
        if (!CheckUtil.checkIsNotNull(reserveDate)) {
            reserveDate += " 04:00";
        }
        /** 予約フラグ */
        const reserveFlg = param.reserve_flag;
        /** SO-ID(予約実行呼出し専用) */
        const reserveSoId = param.reserve_soId;
        /** オーダ種別 */
        let orderType: string = null;

        /** 回線廃止テナントID */
        const lineDelTenantId = param.lineDelTenantId;

        /** 実行ユーザID (null) */
        const executeUserId: string = null;
        /** 実行テナントID (null) */
        const executeTenantId: string = null;

        /** REST API共通処理が呼び出されたシステム日付 "yyyy/MM/dd HH:mm:ss"形式 */
        const systemDateTime = MvnoUtil.getDateTimeNow();

        /** REST API共通処理が呼び出されたシステム日付 "yyyy/MM/dd"形式 */
        const systemDateTimeYMD = systemDateTime.substring(0, 10);

        const responseHeader = this.context.responseHeader;
        responseHeader.processCode = ResultCdConstants.CODE_000000;

        /** 内部呼び出しエラーメッセージ */
        const errorMessage: string = null;

        let fullMvnoFlag: boolean = null;

        /** 変数「処理コード」初期化 */
        let handleCode = ResultCdConstants.CODE_000000;

        const sequelize = await usePsql();

        const lineOptionsTBan: LineOptionsTBan = {};

        /** 回線廃止用のN番 */
        let nNo_haishi: string = null;

        try {
            // オーダ種別取得
            orderType = Check.checkOrderType(
                reserveDate,
                reserveFlg,
                reserveSoId,
            );

            // オーダが予約実行かどうかを判定
            if (Constants.ORDER_TYPE_2.equals(orderType)) {
                // 送信元IPアドレス取得
                const senderIpAddr = clientIPAddress;
                // APサーバの複数IP取得
                const localIpList = MvnoUtil.getLocalIpList();

                // 予約実行可否チェック
                if (!this.context.isInternalRequest) {
                    super.error(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APCOME0401,
                        "予約実行できませんでした。クライアントIP:" +
                            senderIpAddr +
                            ", APサーバーIP：" +
                            MvnoUtil.getOutputList(localIpList),
                    );
                    responseHeader.processCode = ResultCdConstants.CODE_999999;
                    return this.resultEdit(
                        responseHeader,
                        isPrivate,
                        errorMessage,
                        false,
                    );
                }
            }

            // 業務チェック(共通)
            // オーダ種別チェック
            // 変数「オーダ種別」より判定する
            if (Constants.ORDER_TYPE_9.equals(orderType)) {
                super.warn(
                    param.tenantId,
                    param.requestHeader.sequenceNo,
                    MsgKeysConstants.APPLAW0020,
                );
                responseHeader.processCode = ResultCdConstants.CODE_510112;
                return this.resultEdit(
                    responseHeader,
                    isPrivate,
                    errorMessage,
                    false,
                );
            }

            // REST-API電文パラメータ相関チェック
            // フォーマットチェック
            // テナントIDが“OPF000”であること（※卸ポータルフロントからの実行であることを示す固定値）
            if (!Constants.FRONT_API_TENANT_ID.equals(tenantId)) {
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APPLAW0001,
                    "テナントID",
                    tenantId,
                );
                responseHeader.processCode = ResultCdConstants.CODE_510101;
                return this.resultEdit(
                    responseHeader,
                    isPrivate,
                    errorMessage,
                    false,
                );
            }

            // 回線追加先テナントIDが10桁以内の文字列?
            if (!Check.checkTenatIdFmt(targetTenantId)) {
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APPLAW0001,
                    "回線追加先テナントID",
                    targetTenantId,
                );
                responseHeader.processCode = ResultCdConstants.CODE_510101;
                return this.resultEdit(
                    responseHeader,
                    isPrivate,
                    errorMessage,
                    false,
                );
            }

            // 代表N番が10桁文字列（先頭はN、後続9桁数字）
            if (!Check.checkNNumberFmt(nBan)) {
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APPLAW0001,
                    "代表N番",
                    nBan,
                );
                responseHeader.processCode = ResultCdConstants.CODE_510101;
                return this.resultEdit(
                    responseHeader,
                    isPrivate,
                    errorMessage,
                    false,
                );
            }

            // 回線番号が11桁文字列（数字のみ）であること
            if (!Check.checkLineNo(lineNo)) {
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APPLAW0001,
                    "回線番号",
                    lineNo,
                );
                responseHeader.processCode = ResultCdConstants.CODE_510101;
                return this.resultEdit(
                    responseHeader,
                    isPrivate,
                    errorMessage,
                    false,
                );
            }

            // 卸ポータルプランIDが5桁文字列（数字のみ）であること
            if (!Check.checkPotalPlanID(potalPlanID)) {
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APPLAW0001,
                    "卸ポータルプランID",
                    potalPlanID,
                );
                responseHeader.processCode = ResultCdConstants.CODE_510101;
                return this.resultEdit(
                    responseHeader,
                    isPrivate,
                    errorMessage,
                    false,
                );
            }

            // 国際ローミング利用限度額IDが5桁文字列であること（省略可）
            if (
                StringUtils.isNotEmpty(idIntlRoaming) &&
                !Check.checkStrIdFmt(idIntlRoaming, 5, true)
            ) {
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APPLAW0001,
                    "国際ローミング利用限度額ID",
                    idIntlRoaming,
                );
                responseHeader.processCode = ResultCdConstants.CODE_510101;
                return this.resultEdit(
                    responseHeader,
                    isPrivate,
                    errorMessage,
                    false,
                );
            }

            // 留守番でんわIDが5桁文字列であること（省略可）
            if (
                StringUtils.isNotEmpty(idVoicemail) &&
                !Check.checkStrIdFmt(idVoicemail, 5, true)
            ) {
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APPLAW0001,
                    "留守番でんわID",
                    idVoicemail,
                );
                responseHeader.processCode = ResultCdConstants.CODE_510101;
                return this.resultEdit(
                    responseHeader,
                    isPrivate,
                    errorMessage,
                    false,
                );
            }

            // キャッチホンIDが5桁文字列であること（省略可）
            if (
                StringUtils.isNotEmpty(idCallWaiting) &&
                !Check.checkStrIdFmt(idCallWaiting, 5, true)
            ) {
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APPLAW0001,
                    "キャッチホンID",
                    idCallWaiting,
                );
                responseHeader.processCode = ResultCdConstants.CODE_510101;
                return this.resultEdit(
                    responseHeader,
                    isPrivate,
                    errorMessage,
                    false,
                );
            }

            // 国際電話IDが5桁文字列であること（省略可）
            if (
                StringUtils.isNotEmpty(idIntlCall) &&
                !Check.checkStrIdFmt(idIntlCall, 5, true)
            ) {
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APPLAW0001,
                    "国際電話ID",
                    idIntlCall,
                );
                responseHeader.processCode = ResultCdConstants.CODE_510101;
                return this.resultEdit(
                    responseHeader,
                    isPrivate,
                    errorMessage,
                    false,
                );
            }

            // 転送でんわIDが5桁文字列であること（省略可）
            if (
                StringUtils.isNotEmpty(idForwarding) &&
                !Check.checkStrIdFmt(idForwarding, 5, true)
            ) {
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APPLAW0001,
                    "転送でんわID",
                    idForwarding,
                );
                responseHeader.processCode = ResultCdConstants.CODE_510101;
                return this.resultEdit(
                    responseHeader,
                    isPrivate,
                    errorMessage,
                    false,
                );
            }

            // 国際着信転送IDが5桁文字列であること（省略可）
            if (
                StringUtils.isNotEmpty(idIntlForwarding) &&
                !Check.checkStrIdFmt(idIntlForwarding, 5, true)
            ) {
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APPLAW0001,
                    "国際着信転送ID",
                    idIntlForwarding,
                );
                responseHeader.processCode = ResultCdConstants.CODE_510101;
                return this.resultEdit(
                    responseHeader,
                    isPrivate,
                    errorMessage,
                    false,
                );
            }

            // SIM番号(DN番号)が32桁以内の文字列であること
            if (!Check.checkStrIdFmt(simNo, 32, false)) {
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APPLAW0001,
                    "SIM番号",
                    simNo,
                );
                responseHeader.processCode = ResultCdConstants.CODE_510101;
                return this.resultEdit(
                    responseHeader,
                    isPrivate,
                    errorMessage,
                    false,
                );
            }

            // SIM種別が16桁以内の文字列であること
            if (!Check.checkStrIdFmt(simType, 16, false)) {
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APPLAW0001,
                    "SIM種別",
                    simType,
                );
                responseHeader.processCode = ResultCdConstants.CODE_510101;
                return this.resultEdit(
                    responseHeader,
                    isPrivate,
                    errorMessage,
                    false,
                );
            }

            // アクセス方式
            if (!CheckUtil.checkIsNotNull(access)) {
                if (
                    !(
                        "SA".equals(access) ||
                        "SB".equals(access) ||
                        "SC".equals(access) ||
                        "SD".equals(access) ||
                        "SE".equals(access) ||
                        "SF".equals(access) ||
                        "SG".equals(access) ||
                        "SH".equals(access) ||
                        "SJ".equals(access)
                    )
                ) {
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APPLAW0001,
                        "アクセス方式",
                        access,
                    );
                    responseHeader.processCode = ResultCdConstants.CODE_510101;
                    return this.resultEdit(
                        responseHeader,
                        isPrivate,
                        errorMessage,
                        false,
                    );
                }
            }

            // MVNO顧客CSV連携不要フラグのチェック・変換
            if (CheckUtil.checkIsNotNull(csvUnnecessaryFlag)) {
                csvUnnecessaryFlagInside = true;
            } else if ("1".equals(csvUnnecessaryFlag)) {
                csvUnnecessaryFlagInside = true;
            } else if ("0".equals(csvUnnecessaryFlag)) {
                csvUnnecessaryFlagInside = false;
            } else {
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APPLAW0001,
                    "MVNO顧客CSV連携不要フラグ",
                    csvUnnecessaryFlag,
                );
                responseHeader.processCode = ResultCdConstants.CODE_510101;
                return this.resultEdit(
                    responseHeader,
                    isPrivate,
                    errorMessage,
                    false,
                );
            }

            // カード種別IDのフォーマットチェック
            if (
                !CheckUtil.checkIsNotNull(cardTypeId) ||
                !csvUnnecessaryFlagInside
            ) {
                if (!Check.checkStrIdFmt(cardTypeId, 4, false)) {
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APPLAW0001,
                        "カード種別ID",
                        cardTypeId,
                    );
                    responseHeader.processCode = ResultCdConstants.CODE_510101;
                    return this.resultEdit(
                        responseHeader,
                        isPrivate,
                        errorMessage,
                        false,
                    );
                }
            }

            // PSIDのフォーマットチェック
            if (!CheckUtil.checkIsNotNull(psid)) {
                if (!Check.checkStrIdFmt(psid, 20, false)) {
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APPLAW0001,
                        "PSID",
                        psid,
                    );
                    responseHeader.processCode = ResultCdConstants.CODE_510101;
                    return this.resultEdit(
                        responseHeader,
                        isPrivate,
                        errorMessage,
                        false,
                    );
                }
            }

            // MNP転入フラグのチェック・変換
            if (CheckUtil.checkIsNotNull(mnpInFlag)) {
                mnpInType = PreLineAddService.MNP_IN_TYPE_YES;
            } else if ("1".equals(mnpInFlag)) {
                mnpInType = PreLineAddService.MNP_IN_TYPE_YES;
            } else if ("0".equals(mnpInFlag)) {
                mnpInType = PreLineAddService.MNP_IN_TYPE_NO;
            } else if ("2".equals(mnpInFlag)) {
                mnpInType = PreLineAddService.MNP_IN_TYPE_SAME_MVNE_YES;
            } else {
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APPLAW0001,
                    "MNP転入フラグ",
                    mnpInFlag,
                );
                responseHeader.processCode = ResultCdConstants.CODE_510101;
                return this.resultEdit(
                    responseHeader,
                    isPrivate,
                    errorMessage,
                    false,
                );
            }

            // 予約日フォーマットチェック
            if (!CheckUtil.checkIsNotNull(reserveDate)) {
                if (
                    mnpInType === PreLineAddService.MNP_IN_TYPE_SAME_MVNE_YES ||
                    !Check.checkReserveDateFmt(reserveDate)
                ) {
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APPLAW0001,
                        "予約日",
                        reserveDate,
                    );
                    responseHeader.processCode = ResultCdConstants.CODE_510101;
                    return this.resultEdit(
                        responseHeader,
                        isPrivate,
                        errorMessage,
                        false,
                    );
                }
            }

            // 回線廃止テナントIDフォーマットチェック
            if (!CheckUtil.checkIsNotNull(lineDelTenantId)) {
                // 回線廃止テナントIDが10桁以内の文字列?
                if (!Check.checkTenatIdFmt(lineDelTenantId)) {
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APPLAW0001,
                        "回線廃止テナントID",
                        lineDelTenantId,
                    );
                    responseHeader.processCode = ResultCdConstants.CODE_510101;
                    return this.resultEdit(
                        responseHeader,
                        isPrivate,
                        errorMessage,
                        false,
                    );
                }
            } else {
                // ※MNP転入フラグが“2”の場合必須なのでエラーとする
                if (mnpInType === PreLineAddService.MNP_IN_TYPE_SAME_MVNE_YES) {
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APPLAW0001,
                        "回線廃止テナントID",
                        lineDelTenantId,
                    );
                    responseHeader.processCode = ResultCdConstants.CODE_510101;
                    return this.resultEdit(
                        responseHeader,
                        isPrivate,
                        errorMessage,
                        false,
                    );
                }
            }

            // 業務チェック(予約前オーダ)
            if (Constants.ORDER_TYPE_1.equals(orderType)) {
                // 予約日と投入日相関チェック
                if (
                    !Check.checkReserveDateOrderDate(
                        systemDateTime,
                        reserveDate,
                    )
                ) {
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APPLAW0021,
                        reserveDate,
                        systemDateTime,
                    );
                    responseHeader.processCode = ResultCdConstants.CODE_510501;
                    return this.resultEdit(
                        responseHeader,
                        isPrivate,
                        errorMessage,
                        false,
                    );
                }

                // システムプロパティ．「予約実行日時単位」フォーマットチェックを行う
                if (
                    !Check.checkReservationDateExecutionUnitsFmt(
                        this.reservationDateExecutionUnits,
                    )
                ) {
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APPLAW0022,
                        this.reservationDateExecutionUnits,
                    );
                    responseHeader.processCode = ResultCdConstants.CODE_510502;
                    return this.resultEdit(
                        responseHeader,
                        isPrivate,
                        errorMessage,
                        false,
                    );
                }

                // システムプロパティ．「予約実行日時単位」と予約日相関チェックを行う
                if (
                    !Check.checkReservationDateExecutionUnits(
                        this.reservationDateExecutionUnits,
                        reserveDate,
                    )
                ) {
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APPLAW0023,
                        this.reservationDateExecutionUnits,
                        reserveDate,
                    );
                    responseHeader.processCode = ResultCdConstants.CODE_510503;
                    return this.resultEdit(
                        responseHeader,
                        isPrivate,
                        errorMessage,
                        false,
                    );
                }

                // システムプロパティ．「予約可能制限日数」フォーマットチェックを行う
                if (
                    !Check.checkReservationsLimitDaysFmt(
                        this.reservationsLimitDays,
                    )
                ) {
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APPLAW0024,
                        this.reservationsLimitDays,
                    );
                    responseHeader.processCode = ResultCdConstants.CODE_510504;
                    return this.resultEdit(
                        responseHeader,
                        isPrivate,
                        errorMessage,
                        false,
                    );
                }

                // システムプロパティ．「予約可能制限日数」範囲チェックを行う
                if (
                    !Check.checkReservationsLimitDays(
                        this.reservationsLimitDays,
                        reserveDate,
                    )
                ) {
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APPLAW0025,
                        this.reservationsLimitDays,
                        reserveDate,
                    );
                    responseHeader.processCode = ResultCdConstants.CODE_510505;
                    return this.resultEdit(
                        responseHeader,
                        isPrivate,
                        errorMessage,
                        false,
                    );
                }
            }

            // SG(システムプロパティ)「仮登録回線追加受付不可時間」フォーマット判定
            if (!Check.checkTimeSpanHMFmt(this.preLineAddNgTime)) {
                super.error(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APPLAE0001,
                    this.preLineAddNgTime,
                );
                responseHeader.processCode = ResultCdConstants.CODE_510102;
                return this.resultEdit(
                    responseHeader,
                    isPrivate,
                    errorMessage,
                    false,
                );
            }

            // SG(システムプロパティ)「仮登録回線追加受付不可時間」判定
            const time = systemDateTime.substring(11, 16);
            if (!Check.checkTimeSpanOutHM(time, this.preLineAddNgTime)) {
                super.error(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APPLAE0002,
                    this.preLineAddNgTime,
                );
                responseHeader.processCode = ResultCdConstants.CODE_510103;
                return this.resultEdit(
                    responseHeader,
                    isPrivate,
                    errorMessage,
                    false,
                );
            }

            // MNP転入フラグが“2：同一MVNE転入”の時は以下の処理を行わず、⑥料金プラン使用可能判定へ移動する。
            if (mnpInType !== PreLineAddService.MNP_IN_TYPE_SAME_MVNE_YES) {
                // 既設回線存在判定
                let linesEntity = null;
                try {
                    linesEntity = await this.apiLinesDao.getLineInfo(lineNo);
                } catch (e) {
                    if (isSQLException(e)) {
                        // DBアクセスリトライエラーの場合
                        handleCode = ResultCdConstants.CODE_510105;
                    }
                    throw e;
                }
                if (linesEntity !== null) {
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APPLAW0005,
                        lineNo,
                    );
                    responseHeader.processCode = ResultCdConstants.CODE_510105;
                    return this.resultEdit(
                        responseHeader,
                        isPrivate,
                        errorMessage,
                        false,
                    );
                }
                // 回線新設予約判定
                let lineOpenReserveOrderList: string[] = null;
                try {
                    lineOpenReserveOrderList =
                        await this.apiLinesDao.getLineOpenReserveOrder(lineNo);
                } catch (e) {
                    if (isSQLException(e)) {
                        // DBアクセスリトライエラーの場合
                        handleCode = ResultCdConstants.CODE_510105;
                    }
                    throw e;
                }
                for (const lineOpenReserveOrder of lineOpenReserveOrderList) {
                    if (
                        lineOpenReserveOrder !== null &&
                        !lineOpenReserveOrder.equals(reserveSoId)
                    ) {
                        super.warn(
                            tenantId,
                            sequenceNo,
                            MsgKeysConstants.APPLAW0028,
                            lineNo,
                            lineOpenReserveOrder,
                        );
                        responseHeader.processCode =
                            ResultCdConstants.CODE_510105;
                        return this.resultEdit(
                            responseHeader,
                            isPrivate,
                            errorMessage,
                            false,
                        );
                    }
                }
            }

            // 料金プラン使用可能判定
            let planList: string[] = null;
            try {
                planList = await this.apiLinesDao.getPlanList(
                    targetTenantId,
                    potalPlanID,
                );
            } catch (e) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    handleCode = ResultCdConstants.CODE_510106;
                }
                throw e;
            }
            if (planList === null || planList.length === 0) {
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APPLAW0006,
                    targetTenantId,
                    potalPlanID,
                );
                responseHeader.processCode = ResultCdConstants.CODE_510106;
                return this.resultEdit(
                    responseHeader,
                    isPrivate,
                    errorMessage,
                    false,
                );
            }

            // 音声SIM判定
            let plansEntity: PlansEntity = null;
            try {
                plansEntity = await this.apiLinesDao.getPlansInfoByPlanId(
                    potalPlanID,
                );
            } catch (e) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    handleCode = ResultCdConstants.CODE_510107;
                }
                throw e;
            }

            if (
                mnpInType === PreLineAddService.MNP_IN_TYPE_YES ||
                mnpInType === PreLineAddService.MNP_IN_TYPE_SAME_MVNE_YES
            ) {
                if (
                    plansEntity === null ||
                    plansEntity.voiceFlag === null ||
                    !plansEntity.voiceFlag
                ) {
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APPLAW0008,
                        potalPlanID,
                    );
                    responseHeader.processCode = ResultCdConstants.CODE_510107;
                    return this.resultEdit(
                        responseHeader,
                        isPrivate,
                        errorMessage,
                        false,
                    );
                }
            }

            // 回線オプション使用可能判定
            // テナント情報取得（留守番電話・キャッチホンオプションN番単位使用可能判定用）
            let tenantsInfo: TenantsEntity = null;
            try {
                tenantsInfo = await this.apiCommonDao.getTenantsEntity(
                    targetTenantId,
                );
            } catch (e) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    handleCode = ResultCdConstants.CODE_510108;
                }
                throw e;
            }
            if (tenantsInfo === null) {
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APPLAW0029,
                    targetTenantId,
                );
                responseHeader.processCode = ResultCdConstants.CODE_510108;
                return this.resultEdit(
                    responseHeader,
                    isPrivate,
                    errorMessage,
                    false,
                );
            } else if (
                PreLineAddService.TENANT_TYPE_BOCN !== tenantsInfo.tenantType &&
                PreLineAddService.TENANT_TYPE_UNOM !== tenantsInfo.tenantType
            ) {
                // 社内テナント種別がB-OCN/UNOモバイル以外の場合、テナントプラン回線オプション情報チェック後
                // 留守番電話・キャッチホンオプションがN番単位で使用可能か判定
                if (!CheckUtil.checkIsNotNull(idVoicemail)) {
                    try {
                        // 回線オプション情報取得
                        const lineOptionsEntity: LineOptionsEntity =
                            await this.apiLinesDao.getLineOptionsInfo(
                                idVoicemail,
                            );
                        if (lineOptionsEntity === null) {
                            super.warn(
                                tenantId,
                                sequenceNo,
                                MsgKeysConstants.APPLAW0016,
                                idVoicemail,
                            );
                            responseHeader.processCode =
                                ResultCdConstants.CODE_510108;
                            return this.resultEdit(
                                responseHeader,
                                isPrivate,
                                errorMessage,
                                false,
                            );
                        }

                        // テナントプラン回線オプション情報取得・判定
                        const tenantPlanLineOptionsEntity: TenantPlanLineOptionsEntity =
                            await this.apiLinesDao.getTenantPlanLineOptionsInfo(
                                targetTenantId,
                                potalPlanID,
                                lineOptionsEntity.lineOptionType,
                            );
                        if (tenantPlanLineOptionsEntity === null) {
                            super.warn(
                                tenantId,
                                sequenceNo,
                                MsgKeysConstants.APPLAW0007,
                                targetTenantId,
                                potalPlanID,
                                idVoicemail,
                            );
                            responseHeader.processCode =
                                ResultCdConstants.CODE_510108;
                            return this.resultEdit(
                                responseHeader,
                                isPrivate,
                                errorMessage,
                                false,
                            );
                        }

                        // 留守番電話オプションがN番単位で使用可能か判定
                        if (
                            (await this.apiLinesDao.getVoicemailFromCustInfo(
                                nBan,
                                idVoicemail,
                            )) === null
                        ) {
                            super.warn(
                                tenantId,
                                sequenceNo,
                                MsgKeysConstants.APPLAW0026,
                                idVoicemail,
                                nBan,
                            );
                            responseHeader.processCode =
                                ResultCdConstants.CODE_510108;
                            return this.resultEdit(
                                responseHeader,
                                isPrivate,
                                errorMessage,
                                false,
                            );
                        }
                    } catch (e) {
                        if (isSQLException(e)) {
                            // DBアクセスリトライエラーの場合
                            handleCode = ResultCdConstants.CODE_510108;
                        }
                        throw e;
                    }
                }
                if (!CheckUtil.checkIsNotNull(idCallWaiting)) {
                    try {
                        // 回線オプション情報取得
                        const lineOptionsEntity: LineOptionsEntity =
                            await this.apiLinesDao.getLineOptionsInfo(
                                idCallWaiting,
                            );
                        if (lineOptionsEntity === null) {
                            super.warn(
                                tenantId,
                                sequenceNo,
                                MsgKeysConstants.APPLAW0016,
                                idCallWaiting,
                            );
                            responseHeader.processCode =
                                ResultCdConstants.CODE_510108;
                            return this.resultEdit(
                                responseHeader,
                                isPrivate,
                                errorMessage,
                                false,
                            );
                        }

                        // テナントプラン回線オプション情報取得・判定
                        const tenantPlanLineOptionsEntity: TenantPlanLineOptionsEntity =
                            await this.apiLinesDao.getTenantPlanLineOptionsInfo(
                                targetTenantId,
                                potalPlanID,
                                lineOptionsEntity.lineOptionType,
                            );
                        if (tenantPlanLineOptionsEntity === null) {
                            super.warn(
                                tenantId,
                                sequenceNo,
                                MsgKeysConstants.APPLAW0007,
                                targetTenantId,
                                potalPlanID,
                                idCallWaiting,
                            );
                            responseHeader.processCode =
                                ResultCdConstants.CODE_510108;
                            return this.resultEdit(
                                responseHeader,
                                isPrivate,
                                errorMessage,
                                false,
                            );
                        }

                        // キャッチホンオプションがN番単位で使用可能か判定
                        if (
                            (await this.apiLinesDao.getCallWaitingFromCustInfo(
                                nBan,
                                idCallWaiting,
                            )) === null
                        ) {
                            super.warn(
                                tenantId,
                                sequenceNo,
                                MsgKeysConstants.APPLAW0027,
                                idCallWaiting,
                                nBan,
                            );
                            responseHeader.processCode =
                                ResultCdConstants.CODE_510108;
                            return this.resultEdit(
                                responseHeader,
                                isPrivate,
                                errorMessage,
                                false,
                            );
                        }
                    } catch (e) {
                        if (isSQLException(e)) {
                            // DBアクセスリトライエラーの場合
                            handleCode = ResultCdConstants.CODE_510108;
                        }
                        throw e;
                    }
                }
            }

            // テナント情報取得（回線オプション使用可能判定・テナントN番有効判定）
            let tenantsEntity: TenantsEntity = null;
            try {
                tenantsEntity = await this.apiCommonDao.getTenants(
                    targetTenantId,
                );
            } catch (e) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    handleCode = ResultCdConstants.CODE_510111;
                }
                throw e;
            }
            if (
                !tenantsEntity || // NOTE need to check if tenant is not null
                tenantsEntity.status === null ||
                !tenantsEntity.status
            ) {
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APPLAW0012,
                    targetTenantId,
                );
                responseHeader.processCode = ResultCdConstants.CODE_510111;
                return this.resultEdit(
                    responseHeader,
                    isPrivate,
                    errorMessage,
                    false,
                );
            }

            // テナント情報.社内テナント種別判定
            // 帯域卸フラグ
            let wholeFlagList: boolean[] = null;
            if (tenantsEntity.tenantType === null) {
                // 帯域卸フラグ取得
                try {
                    wholeFlagList = await this.apiLinesDao.getWholeFlag(
                        targetTenantId,
                        nBan,
                    );
                } catch (e) {
                    if (isSQLException(e)) {
                        // DBアクセスリトライエラーの場合
                        handleCode = ResultCdConstants.CODE_510111;
                    }
                    throw e;
                }
                if (wholeFlagList === null || wholeFlagList.length === 0) {
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APPLAW0013,
                        targetTenantId,
                    );
                    responseHeader.processCode = ResultCdConstants.CODE_510111;
                    return this.resultEdit(
                        responseHeader,
                        isPrivate,
                        errorMessage,
                        false,
                    );
                }
            }

            // 国際ローミング
            let resultLineOption: [boolean, string, string, string] = null;
            const checkWholeFlag =
                CheckUtil.checkIsNull(wholeFlagList) ||
                CheckUtil.checkIsNull(wholeFlagList.at(0))
                    ? false
                    : wholeFlagList.at(0);
            resultLineOption = await this.checkLineOptions(
                idIntlRoaming,
                targetTenantId,
                potalPlanID,
                tenantsEntity,
                checkWholeFlag,
                PreLineAddService.L_OPTTYPE_ROAMING,
            );
            if (!resultLineOption[0]) {
                this.warnLineOptions(
                    resultLineOption[2],
                    tenantId,
                    targetTenantId,
                    potalPlanID,
                    idIntlRoaming,
                    lineNo,
                    sequenceNo,
                    functionType,
                );
                responseHeader.processCode = resultLineOption[1];
                return this.resultEdit(
                    responseHeader,
                    isPrivate,
                    errorMessage,
                    false,
                );
            }
            lineOptionsTBan.intlRoamingT = resultLineOption[3];

            // 留守番でんわ
            resultLineOption = await this.checkLineOptions(
                idVoicemail,
                targetTenantId,
                potalPlanID,
                tenantsEntity,
                checkWholeFlag,
                PreLineAddService.L_OPTTYPE_VOICEMAIL,
            );
            if (!resultLineOption[0]) {
                this.warnLineOptions(
                    resultLineOption[2],
                    tenantId,
                    targetTenantId,
                    potalPlanID,
                    idVoicemail,
                    lineNo,
                    sequenceNo,
                    functionType,
                );
                responseHeader.processCode = resultLineOption[1];
                return this.resultEdit(
                    responseHeader,
                    isPrivate,
                    errorMessage,
                    false,
                );
            }
            lineOptionsTBan.voicemailT = await this.getLineOptionsTBanIfNeeded(
                idVoicemail,
                resultLineOption[3],
            );

            // キャッチホン
            resultLineOption = await this.checkLineOptions(
                idCallWaiting,
                targetTenantId,
                potalPlanID,
                tenantsEntity,
                checkWholeFlag,
                PreLineAddService.L_OPTTYPE_CALLWAITING,
            );
            if (!resultLineOption[0]) {
                this.warnLineOptions(
                    resultLineOption[2],
                    tenantId,
                    targetTenantId,
                    potalPlanID,
                    idCallWaiting,
                    lineNo,
                    sequenceNo,
                    functionType,
                );
                responseHeader.processCode = resultLineOption[1];
                return this.resultEdit(
                    responseHeader,
                    isPrivate,
                    errorMessage,
                    false,
                );
            }
            lineOptionsTBan.callWaitingT =
                await this.getLineOptionsTBanIfNeeded(
                    idCallWaiting,
                    resultLineOption[3],
                );

            // 国際電話
            resultLineOption = await this.checkLineOptions(
                idIntlCall,
                targetTenantId,
                potalPlanID,
                tenantsEntity,
                checkWholeFlag,
                PreLineAddService.L_OPTTYPE_INTLCALL,
            );
            if (!resultLineOption[0]) {
                this.warnLineOptions(
                    resultLineOption[2],
                    tenantId,
                    targetTenantId,
                    potalPlanID,
                    idIntlCall,
                    lineNo,
                    sequenceNo,
                    functionType,
                );
                responseHeader.processCode = resultLineOption[1];
                return this.resultEdit(
                    responseHeader,
                    isPrivate,
                    errorMessage,
                    false,
                );
            }
            lineOptionsTBan.intlCallT = resultLineOption[3];

            // 転送でんわ
            resultLineOption = await this.checkLineOptions(
                idForwarding,
                targetTenantId,
                potalPlanID,
                tenantsEntity,
                checkWholeFlag,
                PreLineAddService.L_OPTTYPE_FORWARDING,
            );
            if (!resultLineOption[0]) {
                this.warnLineOptions(
                    resultLineOption[2],
                    tenantId,
                    targetTenantId,
                    potalPlanID,
                    idForwarding,
                    lineNo,
                    sequenceNo,
                    functionType,
                );
                responseHeader.processCode = resultLineOption[1];
                return this.resultEdit(
                    responseHeader,
                    isPrivate,
                    errorMessage,
                    false,
                );
            }
            lineOptionsTBan.forwardingT = resultLineOption[3];

            // 国際着信転送
            resultLineOption = await this.checkLineOptions(
                idIntlForwarding,
                targetTenantId,
                potalPlanID,
                tenantsEntity,
                checkWholeFlag,
                PreLineAddService.L_OPTTYPE_INTLFORWARDING,
            );
            if (!resultLineOption[0]) {
                this.warnLineOptions(
                    resultLineOption[2],
                    tenantId,
                    targetTenantId,
                    potalPlanID,
                    idIntlForwarding,
                    lineNo,
                    sequenceNo,
                    functionType,
                );
                responseHeader.processCode = resultLineOption[1];
                return this.resultEdit(
                    responseHeader,
                    isPrivate,
                    errorMessage,
                    false,
                );
            }
            lineOptionsTBan.intlForwadingT = resultLineOption[3];

            // 社内テナント判定
            let officeFlgList: boolean[] = null;
            try {
                officeFlgList = await this.apiLinesDao.getOffice(
                    targetTenantId,
                );
            } catch (e) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    handleCode = ResultCdConstants.CODE_510109;
                }
                throw e;
            }

            // 社内テナント判定が社外
            if (!officeFlgList.at(0)) {
                let tenantNnumbersEntity: TenantNnumbersEntity = null;
                try {
                    // テナントN番有効判定
                    tenantNnumbersEntity =
                        await this.apiLinesDao.getTenantNnumbers(
                            targetTenantId,
                            nBan,
                        );
                } catch (e) {
                    if (isSQLException(e)) {
                        // DBアクセスリトライエラーの場合
                        handleCode = ResultCdConstants.CODE_510109;
                    }
                    throw e;
                }
                if (tenantNnumbersEntity === null) {
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APPLAW0009,
                        targetTenantId,
                        nBan,
                    );
                    responseHeader.processCode = ResultCdConstants.CODE_510109;
                    return this.resultEdit(
                        responseHeader,
                        isPrivate,
                        errorMessage,
                        false,
                    );
                }
            }

            // フルMVNO時のチェックを行う判定するためプラン情報を取得
            let plansInfoList: PlansEntity = null;
            try {
                plansInfoList = await this.apiLinesDao.getPlansInfoByPlanId(
                    potalPlanID,
                );
            } catch (e) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    handleCode = ResultCdConstants.CODE_510113;
                }
                throw e;
            }
            if (
                plansInfoList.fullMvnoFlag !== null &&
                plansInfoList.fullMvnoFlag
            ) {
                // フルMVNOの場合、入力項目の入力チェックを行う

                // IMSI (15桁以内の文字列であること)
                if (
                    CheckUtil.checkIsNotNull(imsi) ||
                    !CheckUtil.checkLength(imsi, 15, false)
                ) {
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APPLAW0001,
                        "IMSI",
                        imsi,
                    );
                    responseHeader.processCode = ResultCdConstants.CODE_510113;
                    return this.resultEdit(
                        responseHeader,
                        isPrivate,
                        errorMessage,
                        false,
                    );
                }

                // PUK1 (8桁文字列以内(数字のみ)であること)
                if (
                    CheckUtil.checkIsNotNull(puk1) ||
                    !CheckUtil.checkLength(puk1, 8, false) ||
                    !CheckUtil.checkIsNum(puk1)
                ) {
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APPLAW0001,
                        "PUK1",
                        puk1,
                    );
                    responseHeader.processCode = ResultCdConstants.CODE_510113;
                    return this.resultEdit(
                        responseHeader,
                        isPrivate,
                        errorMessage,
                        false,
                    );
                }

                // PUK2 (8桁文字列以内(数字のみ)であること)
                if (
                    CheckUtil.checkIsNotNull(puk2) ||
                    !CheckUtil.checkLength(puk2, 8, false) ||
                    !CheckUtil.checkIsNum(puk2)
                ) {
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APPLAW0001,
                        "PUK2",
                        puk2,
                    );
                    responseHeader.processCode = ResultCdConstants.CODE_510113;
                    return this.resultEdit(
                        responseHeader,
                        isPrivate,
                        errorMessage,
                        false,
                    );
                }

                // 利用状態 (半角数字で0または1または2であること)
                if (
                    !"0".equals(usageStatus) &&
                    !"1".equals(usageStatus) &&
                    !"2".equals(usageStatus)
                ) {
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APPLAW0001,
                        "利用状態",
                        usageStatus,
                    );
                    responseHeader.processCode = ResultCdConstants.CODE_510113;
                    return this.resultEdit(
                        responseHeader,
                        isPrivate,
                        errorMessage,
                        false,
                    );
                }

                // フルMVNOフラグ保存
                fullMvnoFlag = plansInfoList.fullMvnoFlag;
            }

            // 0035でんわ情報取得
            let voiceInfoEntity: VoiceInfoEntity = null;
            if (plansEntity !== null && plansEntity.voiceFlag) {
                // 音声プランの場合に取得
                try {
                    voiceInfoEntity =
                        await this.apiCommonDao.getDefaultVoicePlanInfo(
                            targetTenantId,
                        );
                } catch (e) {
                    if (isSQLException(e)) {
                        // DBアクセスリトライエラーの場合
                        handleCode = ResultCdConstants.CODE_510114;
                    }
                    throw e;
                }
                if (voiceInfoEntity == null) {
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APPLAE0005,
                        targetTenantId,
                    );
                    responseHeader.processCode = ResultCdConstants.CODE_510114;
                    return this.resultEdit(
                        responseHeader,
                        isPrivate,
                        errorMessage,
                        false,
                    );
                }
            }

            // 7-2 お客様基本情報取得時の結果フラグ(成功ならtrue)
            let isSuccessGetCustomerInfo = false;
            // 予約実行オーダ以外はTCP情報を取得
            let soapCommonOutputDto = new SOAPCommonOutputDto();
            let resultStr = "";
            if (!Constants.ORDER_TYPE_2.equals(orderType)) {
                // 接続先TPCの情報(回線追加)
                let tpcConnectionResultsForAdd: [boolean, string, string] =
                    null;

                if (mnpInType === PreLineAddService.MNP_IN_TYPE_SAME_MVNE_YES) {
                    // 3-1 接続先TPCの判定
                    // ・MNP転入フラグが“2：同一MVNE転入”の時は、①、②の処理を行う。

                    // ①接続先TPCの判定(回線廃止)
                    // TPC情報を取得する。
                    let tpcConnectionResultsForDel: [boolean, string, string] =
                        null;
                    try {
                        tpcConnectionResultsForDel =
                            await this.tenantManage.checkTpcConnection(
                                lineDelTenantId,
                            );
                    } catch (e) {
                        if (isSQLException(e)) {
                            // DBアクセスリトライエラーの場合
                            handleCode = ResultCdConstants.CODE_510601;
                        }
                        throw e;
                    }
                    // 変数「TPC情報取得結果」より判断する
                    if (!tpcConnectionResultsForDel[0]) {
                        super.warn(
                            tenantId,
                            sequenceNo,
                            MsgKeysConstants.APPLAW0030,
                            lineDelTenantId,
                        );
                        responseHeader.processCode =
                            ResultCdConstants.CODE_510601;
                        return this.resultEdit(
                            responseHeader,
                            isPrivate,
                            errorMessage,
                            false,
                        );
                    }

                    // ②接続先TPCの判定(回線追加)
                    // TPC情報を取得する。
                    try {
                        tpcConnectionResultsForAdd =
                            await this.tenantManage.checkTpcConnection(
                                targetTenantId,
                            );
                    } catch (e) {
                        if (isSQLException(e)) {
                            // DBアクセスリトライエラーの場合
                            handleCode = ResultCdConstants.CODE_510115;
                        }
                        throw e;
                    }
                    // 変数「TPC情報取得結果」より判断する
                    if (!tpcConnectionResultsForAdd[0]) {
                        super.warn(
                            tenantId,
                            sequenceNo,
                            MsgKeysConstants.APPLAW0015,
                            targetTenantId,
                        );
                        responseHeader.processCode =
                            ResultCdConstants.CODE_510115;
                        return this.resultEdit(
                            responseHeader,
                            isPrivate,
                            errorMessage,
                            false,
                        );
                    }

                    // 4-1 TPC電文作成
                    // 電文作成
                    const paraList: ParameterName[] = [];
                    const phoneNumber = new ParameterName();
                    phoneNumber.setName("phone_number");
                    phoneNumber.setValue(lineNo);
                    const force = new ParameterName();
                    force.setName("force");
                    force.setValue("1");
                    paraList.push(phoneNumber);
                    paraList.push(force);

                    // 5-1 SOAP-API電文送受信
                    try {
                        // SOAP-API電文送受信
                        soapCommonOutputDto =
                            await this.soapCommon.callWebSoapApi(
                                "serviceProfileRequestLite",
                                "Del",
                                "",
                                paraList,
                                tenantId,
                                sequenceNo,
                                tpcConnectionResultsForDel[1],
                            );
                        if (
                            ResultCdConstants.CODE_000951.equals(
                                soapCommonOutputDto.getProcessCode(),
                            )
                        ) {
                            // SG取得がNG
                            // ログに指定するErrorInfomation はdocument がnull なので空文字
                            // ResultがNGの場合はエラー処理後、No.8「上位処理に処理結果返却」へ移動する。
                            super.warn(
                                tenantId,
                                sequenceNo,
                                MsgKeysConstants.APPLAW0031,
                                "",
                            );
                            responseHeader.processCode =
                                ResultCdConstants.CODE_510602;
                            return this.resultEdit(
                                responseHeader,
                                isPrivate,
                                errorMessage,
                                false,
                            );
                        } else {
                            const document = soapCommonOutputDto.getDoc();
                            resultStr = this.soapCommon.getNodeContent(
                                document,
                                "//Result",
                            );

                            // エラー判定
                            if (
                                soapCommonOutputDto.isError() ||
                                PreLineAddService.CONST_RESULT.equals(resultStr)
                            ) {
                                // ResultがNGの場合はエラー処理後、No.8「上位処理に処理結果返却」へ移動する。
                                super.warn(
                                    tenantId,
                                    sequenceNo,
                                    MsgKeysConstants.APPLAW0031,
                                    this.soapCommon.getNodeContent(
                                        document,
                                        "//ErrorInfomation",
                                    ),
                                );
                                responseHeader.processCode =
                                    ResultCdConstants.CODE_510602;
                                return this.resultEdit(
                                    responseHeader,
                                    isPrivate,
                                    errorMessage,
                                    false,
                                );
                            }
                        }
                    } catch (e) {
                        super.warn(
                            e,
                            tenantId,
                            sequenceNo,
                            MsgKeysConstants.APPLAW0031,
                            e,
                        );
                        resultStr = PreLineAddService.CONST_RESULT;
                        responseHeader.processCode =
                            ResultCdConstants.CODE_510602;
                        return this.resultEdit(
                            responseHeader,
                            isPrivate,
                            errorMessage,
                            false,
                        );
                    }

                    // セカンダリTPCへSOAP連携
                    if (tpcConnectionResultsForDel[2] !== null) {
                        // セカンダリTPCが設定されている場合は以下の処理を行う(電文はプライマリと同じものを使用)
                        try {
                            const soapCommonOutputDtoSec =
                                await this.soapCommon.callWebSoapApi(
                                    "serviceProfileRequestLite",
                                    "Del",
                                    "",
                                    paraList,
                                    tenantId,
                                    sequenceNo,
                                    tpcConnectionResultsForDel[2],
                                    false,
                                );

                            if (
                                ResultCdConstants.CODE_000951.equals(
                                    soapCommonOutputDtoSec.getProcessCode(),
                                )
                            ) {
                                // SG取得がNG
                                super.warn(
                                    tenantId,
                                    sequenceNo,
                                    MsgKeysConstants.APPLAW0039,
                                    "SG取得NG",
                                );
                            } else {
                                const document =
                                    soapCommonOutputDtoSec.getDoc();
                                const resultStrSec =
                                    this.soapCommon.getNodeContent(
                                        document,
                                        "//Result",
                                    );

                                // エラー判定
                                if (
                                    soapCommonOutputDtoSec.isError() ||
                                    PreLineAddService.CONST_RESULT.equals(
                                        resultStrSec,
                                    )
                                ) {
                                    // ResultがNGの場合はエラー処理後、処理を続行する
                                    super.warn(
                                        tenantId,
                                        sequenceNo,
                                        MsgKeysConstants.APPLAW0039,
                                        this.soapCommon.getNodeContent(
                                            document,
                                            "//ErrorInfomation",
                                        ),
                                    );
                                }
                            }
                        } catch (e) {
                            // ログ出力
                            if (SOAPException.isSOAPException(e)) {
                                super.warn(
                                    e,
                                    tenantId,
                                    sequenceNo,
                                    MsgKeysConstants.APPLAW0039,
                                    e,
                                );
                            } else {
                                throw e;
                            }
                        }
                    }

                    // NOTE added during refactoring
                    // get N番 for 廃止 tenant (SAME_MVNE)
                    // (need to get here before linesEntity is updated with new tenant)
                    this.context.log(
                        "Get N番 for 廃止 tenant",
                        lineDelTenantId,
                        lineNo,
                    );
                    const haishiTenantCheck = await this.tenantManage.doCheck(
                        lineNo,
                        lineDelTenantId,
                    );
                    this.context.log(
                        "haishiTenantCheck result:",
                        haishiTenantCheck,
                    );
                    // no need to check [0] value since we only need N番?
                    nNo_haishi = haishiTenantCheck[1];
                    if (isNone(nNo_haishi)) {
                        // N番が取得できなかった場合はエラー
                        this.context.error(
                            "Could not get N番 of 廃止 tenant",
                            lineDelTenantId,
                        );
                    }

                    // REFACTORING STEP7: (同一MVNE仮登録回線)
                    // reserved プラン変更 and 回線廃止 will be sent to Core & Front Cancel API
                    // Part of 「6-4 キャンセル対象SOの検索」 but executed here first before
                    // line status and nBan are updated
                    // NOTE continue process even if some of Cancel API calls failed
                    // https://mobilus.backlog.jp/view/MVNO_N_M-3203#comment-279950965
                    try {
                        const reservedOrders =
                            await this.apiLinesDao.getReservedServiceOrderWithOrderType(
                                lineNo,
                                systemDateTime,
                            );

                        if (reservedOrders.length > 0) {
                            const failedCoreCancel: string[] = [];
                            const failedFrontCancel: string[] = [];
                            const cancelOrderList = reservedOrders.filter((o) =>
                                ["プラン変更", "回線廃止"].includes(
                                    o.order_type,
                                ),
                            );

                            for (const cancelOrder of cancelOrderList) {
                                // プラン変更: send to Core's SoCancel API
                                if (cancelOrder.order_type === "プラン変更") {
                                    const cancelResult =
                                        await this.cancelWithCoreAPI(
                                            cancelOrder.service_order_id,
                                            cancelOrder.order_type,
                                            lineDelTenantId,
                                        );
                                    if (!cancelResult) {
                                        failedCoreCancel.push(
                                            cancelOrder.service_order_id,
                                        );
                                    }
                                }

                                // 回線廃止: send to Front cancel API (LineDelCancelExternalAPI)
                                if (cancelOrder.order_type === "回線廃止") {
                                    const frontCancelResult =
                                        await this.cancelHaishiWithFrontAPI(
                                            cancelOrder.service_order_id,
                                            lineDelTenantId,
                                        );
                                    if (!frontCancelResult) {
                                        failedFrontCancel.push(
                                            cancelOrder.service_order_id,
                                        );
                                    }
                                }
                                isSameMvneCancelRequestSent = true;
                            }

                            // log failed cancellations
                            if (failedCoreCancel.length > 0) {
                                this.context.warn(
                                    "PreLineAddService: cancelReservedOrder failed Core's Cancel API:",
                                    failedCoreCancel,
                                );
                            }
                            if (failedFrontCancel.length > 0) {
                                this.context.warn(
                                    "PreLineAddService: cancelReservedOrder failed Front's Cancel API:",
                                    failedFrontCancel,
                                );
                            }
                        }
                    } catch (e) {
                        // warn??
                        this.context.warn(
                            "PreLineAddService external cancel error:",
                            e,
                        );
                    }

                    // 6-1 回線情報更新処理
                    try {
                        const lineInfoUpdateResult = await retryQuery(
                            this.context,
                            "PreLineAddService: 6-1 回線情報更新処理",
                            async () => {
                                try {
                                    await this.apiLinesDao.updateLineInfoLineDeleteStatus(
                                        null, // transaction not started yet
                                        lineNo,
                                        MvnoUtil.convDateFormat(systemDateTime),
                                    );
                                    return null;
                                } catch (e) {
                                    if (isSQLException(e)) {
                                        // DBアクセスリトライエラーの場合
                                        throw e;
                                    } else {
                                        super.warn(
                                            e,
                                            tenantId,
                                            sequenceNo,
                                            MsgKeysConstants.APPLAW0032,
                                            lineNo,
                                        );
                                        responseHeader.processCode =
                                            ResultCdConstants.CODE_510603;
                                        return this.resultEdit(
                                            responseHeader,
                                            isPrivate,
                                            errorMessage,
                                            false,
                                        );
                                    }
                                }
                            },
                            this.apDbRetryMaxCnt,
                            this.apDbRetryInterval,
                        );
                        if (lineInfoUpdateResult !== null) {
                            // if not null then it's an early return
                            return lineInfoUpdateResult;
                        }
                    } catch (e) {
                        if (isSQLException(e)) {
                            // 指定回数すべて失敗した場合はエラーとする
                            handleCode = ResultCdConstants.CODE_510603;
                        }
                        throw e;
                    }

                    // 6-2 廃止回線登録処理
                    try {
                        const insertAboneLinesResult = await retryQuery(
                            this.context,
                            "PreLineAddService: 6-2 廃止回線登録処理",
                            async () => {
                                try {
                                    await this.apiLinesDao.insertAboneLines(
                                        null,
                                        param,
                                        MvnoUtil.convDateFormat(systemDateTime),
                                    );
                                    return null;
                                } catch (e) {
                                    if (isSQLException(e)) {
                                        // DBアクセスリトライエラーの場合
                                        throw e;
                                    } else {
                                        super.warn(
                                            e,
                                            tenantId,
                                            sequenceNo,
                                            MsgKeysConstants.APPLAW0033,
                                            lineNo,
                                        );
                                        responseHeader.processCode =
                                            ResultCdConstants.CODE_510604;
                                        return this.resultEdit(
                                            responseHeader,
                                            isPrivate,
                                            errorMessage,
                                            false,
                                        );
                                    }
                                }
                            },
                            this.apDbRetryMaxCnt,
                            this.apDbRetryInterval,
                        );
                        if (insertAboneLinesResult !== null) {
                            // if not null then it's an early return
                            return insertAboneLinesResult;
                        }
                    } catch (e) {
                        if (isSQLException(e)) {
                            // 指定回数すべて失敗した場合はエラーとする
                            handleCode = ResultCdConstants.CODE_510604;
                        }
                        throw e;
                    }

                    // 6-3 通信量クリア
                    let tx: Transaction = null;
                    let isLocked = false;
                    let isSuccess = false;
                    try {
                        const clearTrafficResult = await retryQuery(
                            this.context,
                            "PreLineAddService: 6-3 通信量クリア",
                            async () => {
                                try {
                                    // トランザクション開始
                                    tx = await sequelize.transaction();

                                    // 行ロック取得
                                    // 排他制御の最大リトライ回数分試行する
                                    for (
                                        let lineLockTime = 0;
                                        lineLockTime <
                                        PreLineAddService.LOCK_RETRY_COUNT;
                                        lineLockTime++
                                    ) {
                                        try {
                                            // 回線通信量テーブルのレコードの行ロックを取得する。
                                            const lineNos =
                                                await this.apiLinesDao.getTrafficforUpdate(
                                                    tx,
                                                    lineNo,
                                                    systemDateTimeYMD,
                                                );
                                            if (lineNos.length > 0) {
                                                // 行ロックが取得できた
                                                isLocked = true;
                                            }
                                            break;
                                        } catch (e) {
                                            if (isLockNotAvailableError(e)) {
                                                // PessimisticLockException
                                                if (lineLockTime === 4) {
                                                    // NOTE original code hard-coded `4`
                                                    // タイムアウトまで取得できない(#{排他制御最大リトライ回数}×#{排他制御待ちミリ秒数}を超えた)場合は、
                                                    // 処理を中断し、エラー処理後、No.8「上位処理に処理結果返却」へ移動する。
                                                    super.warn(
                                                        e,
                                                        tenantId,
                                                        sequenceNo,
                                                        MsgKeysConstants.APPLAW0035,
                                                        lineNo,
                                                        systemDateTimeYMD,
                                                    );
                                                    responseHeader.processCode =
                                                        ResultCdConstants.CODE_510605;
                                                    return this.resultEdit(
                                                        responseHeader,
                                                        isPrivate,
                                                        errorMessage,
                                                        false,
                                                    );
                                                }
                                                await tx.rollback();
                                                try {
                                                    // 排他制御の待ちミリ秒数分待機する
                                                    await new Promise(
                                                        (resolve) =>
                                                            setTimeout(
                                                                resolve,
                                                                PreLineAddService.LOCK_WAIT_MILLISEC,
                                                            ),
                                                    );
                                                } finally {
                                                    // 処理不要
                                                    this.context.log(
                                                        "PreLineAddService: 6-3 通信量クリア: 行ロック取得リトライ",
                                                    );
                                                }
                                                // 回線通信量の行ロックが取得できない場合は、行ロックを再取得する。
                                                tx =
                                                    await sequelize.transaction();
                                                continue;
                                            } else if (isSQLException(e)) {
                                                // DBアクセス時にエラー発生した場合
                                                // tx = null; // commented out so that can be rolled back
                                                throw e;
                                            } else {
                                                // 行ロック取得エラー以外の場合は、エラー処理後、 No.8「上位処理に処理結果返却」へ移動する。
                                                super.warn(
                                                    e,
                                                    tenantId,
                                                    sequenceNo,
                                                    MsgKeysConstants.APPLAW0034,
                                                    lineNo,
                                                    systemDateTimeYMD,
                                                );
                                                responseHeader.processCode =
                                                    ResultCdConstants.CODE_510605;
                                                return this.resultEdit(
                                                    responseHeader,
                                                    isPrivate,
                                                    errorMessage,
                                                    false,
                                                );
                                            }
                                        }
                                    }

                                    // ロックが取得できた場合のルート
                                    // 行ロックが正常で終了した場合は、行ロックした取得条件で下記の内容を更新する。
                                    if (isLocked) {
                                        try {
                                            // 行ロックが正常で終了した場合は、行ロックした取得条件で更新する。
                                            await this.apiLinesDao.updateLineTraffictoNull(
                                                tx,
                                                lineNo,
                                                systemDateTimeYMD,
                                            );
                                            // 例外が発生しなければ、正常に更新できたとみなす
                                            isSuccess = true;
                                        } catch (e) {
                                            if (isSQLException(e)) {
                                                // DBアクセスリトライエラーの場合
                                                // tx = null; // commented out so that can be rolled back
                                                throw e;
                                            } else {
                                                // ※エラーが発生した場合はロールバックして処理を中断し、
                                                // エラー処理後、 No.8「上位処理に処理結果返却」へ移動する。
                                                super.warn(
                                                    e,
                                                    tenantId,
                                                    sequenceNo,
                                                    MsgKeysConstants.APPLAW0036,
                                                    lineNo,
                                                    systemDateTimeYMD,
                                                );
                                                responseHeader.processCode =
                                                    ResultCdConstants.CODE_510605;
                                                return this.resultEdit(
                                                    responseHeader,
                                                    isPrivate,
                                                    errorMessage,
                                                    false,
                                                );
                                            }
                                        }
                                    }
                                    // elseルートは取得レコードが0件の場合は処理を中断し、No.6-4「キャンセル対象SOの検索」へ移動する。
                                    return null;
                                } finally {
                                    if (tx !== null) {
                                        if (isSuccess) {
                                            // 正常に更新できた場合は、コミットする
                                            await tx.commit();
                                        } else {
                                            try {
                                                // エラーが発生した場合はロールバックする
                                                await tx.rollback();
                                            } catch (e) {
                                                this.context.error(
                                                    "PreLineAddService: 6-3 通信量クリア: Rollback failed",
                                                    e,
                                                );
                                            }
                                        }
                                    }
                                }
                            },
                            this.apDbRetryMaxCnt,
                            this.apDbRetryInterval,
                        );
                        if (clearTrafficResult !== null) {
                            // if not null then it's an early return
                            return clearTrafficResult;
                        }
                    } catch (e) {
                        if (isSQLException(e)) {
                            // 指定回数すべて失敗した場合はエラーとする
                            handleCode = ResultCdConstants.CODE_510605;
                        }
                        throw e;
                    }

                    // 6-4 キャンセル対象SOの検索
                    let serviceOrderInfoList: {
                        service_order_id: string;
                        order_type: string;
                    }[] = [];
                    try {
                        serviceOrderInfoList =
                            await this.apiLinesDao.getReservedServiceOrderWithOrderType(
                                lineNo,
                                systemDateTime,
                            );
                    } catch (ex) {
                        if (isSQLException(ex)) {
                            // DBアクセス時にエラー発生した場合
                            handleCode = ResultCdConstants.CODE_510606;
                            throw ex;
                        } else {
                            // エラー処理
                            super.warn(
                                ex,
                                tenantId,
                                sequenceNo,
                                MsgKeysConstants.APPLAW0037,
                                lineNo,
                            );
                            responseHeader.processCode =
                                ResultCdConstants.CODE_510606;
                            return this.resultEdit(
                                responseHeader,
                                isPrivate,
                                errorMessage,
                                false,
                            );
                        }
                    }

                    // 取得レコードが0件の場合は処理を中断し、No.7-1「廃止回線情報CSV作成処理」へ移動する。
                    if (serviceOrderInfoList.length > 0) {
                        // No.6-5 SO管理ステータス変更処理
                        for (const serviceOrderInfo of serviceOrderInfoList) {
                            try {
                                if (
                                    mnpInType ===
                                        PreLineAddService.MNP_IN_TYPE_SAME_MVNE_YES &&
                                    ["プラン変更", "回線廃止"].includes(
                                        serviceOrderInfo.order_type,
                                    )
                                ) {
                                    // NOTE プラン変更 and 回線廃止 should have already been cancelled
                                    this.context.error(
                                        "PreLineAddService.cancelReservedOrder skip:",
                                        {
                                            ...serviceOrderInfo,
                                            isSameMvneCancelRequestSent,
                                        },
                                    );
                                    continue;
                                }

                                await this.aPISoDAO.updServiceOrders(
                                    null,
                                    "キャンセル済み",
                                    serviceOrderInfo.service_order_id,
                                    null,
                                );
                            } catch (ex) {
                                if (isSQLException(ex)) {
                                    // DBアクセス時にエラー発生した場合
                                    handleCode = ResultCdConstants.CODE_510607;
                                    throw ex;
                                } else {
                                    super.warn(
                                        ex,
                                        tenantId,
                                        sequenceNo,
                                        MsgKeysConstants.APPLAW0038,
                                        lineNo,
                                        serviceOrderInfo.service_order_id,
                                    );
                                    responseHeader.processCode =
                                        ResultCdConstants.CODE_510607;
                                    return this.resultEdit(
                                        responseHeader,
                                        isPrivate,
                                        errorMessage,
                                        false,
                                    );
                                }
                            }
                        }
                    }

                    // 7-1 廃止回線情報CSV作成処理
                    if (csvUnnecessaryFlagInside) {
                        // [MVNO顧客CSV連携不要フラグ]が1
                        optionAddCsvOutputKbn = "0";
                        abolitionCsvOutputKbn = "0";
                    } else {
                        // [MVNO顧客CSV連携不要フラグ]が1以外
                        // オプション追加CSVの作成要否と回線廃止CSVの作成要否は
                        // 以下の処理の中で設定する

                        // 7-2 お客様基本情報取得
                        const csvCheckResult = await this.checkDeleteCsvOutput(
                            param,
                        );
                        optionAddCsvOutputKbn = csvCheckResult.get(
                            PreLineAddService.KEY_CSV_OUT_KBN_OPTION_ADD,
                        ) as string;
                        abolitionCsvOutputKbn = csvCheckResult.get(
                            PreLineAddService.KEY_CSV_OUT_KBN_ABOLITION,
                        ) as string;
                        customerInfo = csvCheckResult.get(
                            PreLineAddService.KEY_CUSTOMER_INFO,
                        ) as CustomerInfoEntity;

                        // お客様基本情報取得時の結果フラグ(成功ならtrue)の設定
                        if (
                            customerInfo !== null &&
                            customerInfo !== undefined
                        ) {
                            isSuccessGetCustomerInfo = true;
                        }
                    }

                    // 7-3 オプション追加CSVファイル出力用データ取得
                    if (optionAddCsvOutputKbn === "1") {
                        if (apiId.length > 12) {
                            optionFileNameNo =
                                apiId.substring(apiId.length - 12) + "1";
                        } else {
                            optionFileNameNo = apiId + "1";
                        }
                        // optionCsvData = this.optionCsvDataEdit(
                        //     apiId,
                        //     param,
                        //     customerInfo,
                        //     systemDateTimeYMD,
                        // );
                    }

                    // 7-4 回線廃止CSVファイル出力用データ取得
                    if (abolitionCsvOutputKbn === "1") {
                        if (apiId.length > 12) {
                            abolitionFileNameNo =
                                apiId.substring(apiId.length - 12) + "2";
                        } else {
                            abolitionFileNameNo = apiId + "2";
                        }
                        // abolitionCsvData = this.abolitionCsvDataEdit(
                        //     apiId,
                        //     param,
                        //     customerInfo,
                        //     systemDateTimeYMD,
                        //     mnpInType,
                        // );
                    }
                } else {
                    // 3-1 接続先TPCの判定
                    // ・MNP転入フラグが“2：同一MVNE転入”以外の時は、②の処理を実施後、No.4「TPC電文作成」へ移動する。

                    // ②接続先TPCの判定(回線追加)
                    // TPC情報を取得する。
                    try {
                        tpcConnectionResultsForAdd =
                            await this.tenantManage.checkTpcConnection(
                                targetTenantId,
                            );
                    } catch (ex) {
                        if (isSQLException(ex)) {
                            // DBアクセス時にエラー発生した場合
                            handleCode = ResultCdConstants.CODE_510115;
                        }
                        throw ex;
                    }
                    // 変数「TPC情報取得結果」より判断する
                    if (!tpcConnectionResultsForAdd[0]) {
                        super.warn(
                            tenantId,
                            sequenceNo,
                            MsgKeysConstants.APPLAW0015,
                            targetTenantId,
                        );
                        responseHeader.processCode =
                            ResultCdConstants.CODE_510115;
                        return this.resultEdit(
                            responseHeader,
                            isPrivate,
                            errorMessage,
                            false,
                        );
                    }
                } // NOTE end mnpInType IF/ELSE

                // サービスプランIDチェック
                if (StringUtils.isEmpty(plansEntity.servicePlanId)) {
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APPLAW0003,
                        potalPlanID,
                    );
                    responseHeader.processCode = ResultCdConstants.CODE_510110;
                    return this.resultEdit(
                        responseHeader,
                        isPrivate,
                        errorMessage,
                        false,
                        {
                            param,
                            csvUnnecessaryFlag,
                            csvOutputKbn,
                            optionAddCsvOutputKbn,
                            abolitionCsvOutputKbn,
                            custInfo: customerInfo,
                            planInfo: plansInfo,
                            cardInfo,
                            mnpInType,
                            reserveDate,
                            orderType,
                            receivedDate: responseHeader.receivedDate,
                            isFullMvno: fullMvnoFlag,
                            options: lineOptionsTBan,
                            nNo_haishi,
                            isSameMvneCancelRequestSent,
                        },
                    );
                }

                // サービスプランID
                const servicePlanId = plansEntity.servicePlanId;

                // 電文作成
                const paraList: ParameterName[] = [];
                const phoneNumber = new ParameterName();
                phoneNumber.setName("phone_number");
                phoneNumber.setValue(lineNo);
                const policynumber = new ParameterName();
                policynumber.setName("policy_number");
                policynumber.setValue(servicePlanId);
                paraList.push(phoneNumber);
                paraList.push(policynumber);

                // SOAPCommonOutputDto soapCommonOutputDto = new SOAPCommonOutputDto();
                // String resultStr = "";
                try {
                    // SOAP-API電文送受信
                    soapCommonOutputDto = await this.soapCommon.callWebSoapApi(
                        "serviceProfileRequestLite",
                        "Add",
                        "",
                        paraList,
                        tenantId,
                        sequenceNo,
                        tpcConnectionResultsForAdd[1],
                    );
                    if (
                        ResultCdConstants.CODE_000951.equals(
                            soapCommonOutputDto.getProcessCode(),
                        )
                    ) {
                        // SG取得がNG
                        // ログに指定するErrorInfomation はdocument がnull なので空文字
                        // ResultがNGの場合はエラー処理を行い、処理を続行する。
                        super.warn(
                            tenantId,
                            sequenceNo,
                            MsgKeysConstants.APPLAW0010,
                            "",
                        );
                    } else {
                        const document = soapCommonOutputDto.getDoc();
                        resultStr = this.soapCommon.getNodeContent(
                            document,
                            "//Result",
                        );

                        // エラー判定
                        if (
                            soapCommonOutputDto.isError() ||
                            PreLineAddService.CONST_RESULT.equals(resultStr)
                        ) {
                            // ResultがNGの場合はエラー処理を行い、処理を続行する。
                            super.warn(
                                tenantId,
                                sequenceNo,
                                MsgKeysConstants.APPLAW0010,
                                this.soapCommon.getNodeContent(
                                    document,
                                    "//ErrorInfomation",
                                ),
                            );
                        }
                    }
                } catch (e) {
                    super.warn(
                        e,
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APPLAW0010,
                        e,
                    );
                    resultStr = PreLineAddService.CONST_RESULT;
                }

                // セカンダリTPCへSOAP連携
                if (tpcConnectionResultsForAdd[2] !== null) {
                    // セカンダリTPCが設定されている場合は以下の処理を行う(電文はプライマリと同じものを使用)
                    try {
                        const soapCommonOutputDtoSec =
                            await this.soapCommon.callWebSoapApi(
                                "serviceProfileRequestLite",
                                "Add",
                                "",
                                paraList,
                                tenantId,
                                sequenceNo,
                                tpcConnectionResultsForAdd[2],
                                false,
                            );

                        if (
                            ResultCdConstants.CODE_000951.equals(
                                soapCommonOutputDtoSec.getProcessCode(),
                            )
                        ) {
                            // SG取得がNG
                            super.warn(
                                tenantId,
                                sequenceNo,
                                MsgKeysConstants.APPLAW0040,
                                "SG取得NG",
                            );
                        } else {
                            const document = soapCommonOutputDtoSec.getDoc();
                            const resultStrSec = this.soapCommon.getNodeContent(
                                document,
                                "//Result",
                            );

                            // エラー判定
                            if (
                                soapCommonOutputDtoSec.isError() ||
                                PreLineAddService.CONST_RESULT.equals(
                                    resultStrSec,
                                )
                            ) {
                                // ResultがNGの場合はエラー処理後、処理を続行する
                                super.warn(
                                    tenantId,
                                    sequenceNo,
                                    MsgKeysConstants.APPLAW0040,
                                    this.soapCommon.getNodeContent(
                                        document,
                                        "//ErrorInfomation",
                                    ),
                                );
                            }
                        }
                    } catch (e) {
                        if (SOAPException.isSOAPException(e)) {
                            // ログ出力
                            super.warn(
                                e,
                                tenantId,
                                sequenceNo,
                                MsgKeysConstants.APPLAW0040,
                                e.toString(),
                            );
                        } else {
                            throw e;
                        }
                    }
                }
            } // NOTE end of `!Constants.ORDER_TYPE_2.equals(orderType)`

            // 仮登録回線情報登録処理
            try {
                [plansInfo, cardInfo] = await Promise.all([
                    this.apiLinesDao.getPortalPlanIdInfo(potalPlanID),
                    this.apiLinesDao.getCardTypeInfo(cardTypeId),
                ]);

                // 予約前オーダ以外の場合は仮回線登録処理を実施
                if (!Constants.ORDER_TYPE_1.equals(orderType)) {
                    try {
                        // 回線情報を取得する
                        const tmpLinesEntity =
                            await this.apiLinesDao.getLineInfoByLines(lineNo);
                        let preLinesEntity = null;

                        if (tmpLinesEntity !== null) {
                            // 回線情報格納データ設定
                            preLinesEntity = this.setPreLinesEntity(
                                tmpLinesEntity,
                                param,
                                plansInfo,
                                cardInfo,
                                systemDateTime,
                                reserveDate,
                                orderType,
                                true,
                                fullMvnoFlag,
                                voiceInfoEntity,
                            );

                            this.context.log(
                                "PreLineAddService.service updateLines (回線情報登録)",
                            );
                            // 回線情報登録
                            await this.apiLinesDao.updateLines(preLinesEntity);
                        } else {
                            // 回線情報格納データ設定
                            preLinesEntity = this.setPreLinesEntity(
                                tmpLinesEntity,
                                param,
                                plansInfo,
                                cardInfo,
                                systemDateTime,
                                reserveDate,
                                orderType,
                                false,
                                fullMvnoFlag,
                                voiceInfoEntity,
                            );

                            this.context.log(
                                "PreLineAddService.service insertLines (回線情報登録)",
                            );
                            // 回線情報更新
                            await this.apiLinesDao.insertLines(preLinesEntity);
                        }
                    } catch (ex) {
                        if (isSQLException(ex)) {
                            // DBアクセス時にエラー発生した場合
                            throw ex;
                        } else {
                            super.warn(
                                ex,
                                tenantId,
                                sequenceNo,
                                MsgKeysConstants.APPLAW0011,
                                lineNo,
                            );
                            responseHeader.processCode =
                                ResultCdConstants.CODE_510401;
                            return this.resultEdit(
                                responseHeader,
                                isPrivate,
                                errorMessage,
                                false,
                                {
                                    param,
                                    csvUnnecessaryFlag,
                                    csvOutputKbn,
                                    optionAddCsvOutputKbn,
                                    abolitionCsvOutputKbn,
                                    custInfo: customerInfo,
                                    planInfo: plansInfo,
                                    cardInfo,
                                    mnpInType,
                                    reserveDate,
                                    orderType,
                                    receivedDate: responseHeader.receivedDate,
                                    isFullMvno: fullMvnoFlag,
                                    options: lineOptionsTBan,
                                    nNo_haishi,
                                    isSameMvneCancelRequestSent,
                                },
                            );
                        }
                    }
                }
            } catch (ex) {
                if (isSQLException(ex)) {
                    // DBアクセス時にエラー発生した場合
                    handleCode = ResultCdConstants.CODE_510401;
                }
                throw ex;
            }

            // プラン情報とカード情報の取得に失敗していた場合
            if (plansInfo === null && cardInfo === null) {
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APPLAW0011,
                    lineNo,
                );
                responseHeader.processCode = ResultCdConstants.CODE_510401;
                return this.resultEdit(
                    responseHeader,
                    isPrivate,
                    errorMessage,
                    false,
                    {
                        param,
                        csvUnnecessaryFlag,
                        csvOutputKbn,
                        optionAddCsvOutputKbn,
                        abolitionCsvOutputKbn,
                        custInfo: customerInfo,
                        planInfo: plansInfo,
                        cardInfo,
                        mnpInType,
                        reserveDate,
                        orderType,
                        receivedDate: responseHeader.receivedDate,
                        isFullMvno: fullMvnoFlag,
                        options: lineOptionsTBan,
                        nNo_haishi,
                        isSameMvneCancelRequestSent,
                    },
                );
            }

            // CSVファイル用データ収集
            // 予約実行オーダの場合またはMVNO顧客CSV連携不要の場合、CSV作成要否に"0"を設定
            if (Constants.ORDER_TYPE_2.equals(orderType)) {
                csvOutputKbn = "0";
            } else if (csvUnnecessaryFlagInside) {
                csvOutputKbn = "0";
            } else if (
                mnpInType === PreLineAddService.MNP_IN_TYPE_SAME_MVNE_YES &&
                !isSuccessGetCustomerInfo
            ) {
                // MNP転入フラグが“2：同一MVNE転入”の場合かつNo.7-2「お客様基本情報取得」でエラーが発生した場合は、
                // オプション追加CSV、回線廃止CSV が出力されていないにも関わらず回線追加CSVが出力されることを防ぐため、
                // (8)に移動し、CSVの作成要否を0で処理する。
                csvOutputKbn = "0";
            } else {
                const csvCheckResult = await this.checkCsvOutPut(
                    param,
                    mnpInType,
                );
                csvOutputKbn = csvCheckResult.get(
                    PreLineAddService.KEY_CSV_OUT_KBN,
                ) as string;
                if ("1".equals(csvOutputKbn)) {
                    customerInfo = csvCheckResult.get(
                        PreLineAddService.KEY_CUSTOMER_INFO,
                    ) as CustomerInfoEntity;
                }
            }
            // CSVファイル用データ取得
            // if ("1".equals(csvOutputKbn)) {
            //     if (apiId.length > 12) {
            //         fileNameNo = apiId.substring(apiId.length - 12) + "3";
            //     } else {
            //         fileNameNo = apiId + "3";
            //     }
            //     csvData = this.csvDataEdit(
            //         apiId,
            //         param,
            //         customerInfo,
            //         plansInfo,
            //         cardInfo,
            //         mnpInType,
            //         reserveDate,
            //         orderType,
            //         systemDateTimeYMD,
            //     );
            // }

            // 「SOAP-API電文送受信」でエラーが発生している場合
            if (
                soapCommonOutputDto.isError() ||
                PreLineAddService.CONST_RESULT.equals(resultStr)
            ) {
                responseHeader.processCode = ResultCdConstants.CODE_510301;
                return this.resultEdit(
                    responseHeader,
                    isPrivate,
                    errorMessage,
                    false,
                    {
                        param,
                        csvUnnecessaryFlag,
                        csvOutputKbn,
                        optionAddCsvOutputKbn,
                        abolitionCsvOutputKbn,
                        custInfo: customerInfo,
                        planInfo: plansInfo,
                        cardInfo,
                        mnpInType,
                        reserveDate,
                        orderType,
                        receivedDate: responseHeader.receivedDate,
                        isFullMvno: fullMvnoFlag,
                        options: lineOptionsTBan,
                        nNo_haishi,
                        isSameMvneCancelRequestSent,
                    },
                );
            }

            // ディバッグログ出力
            super.debug(
                param.tenantId,
                param.requestHeader.sequenceNo,
                MsgKeysConstants.APPLAD0002,
                this.getClassName(),
                "service",
            );

            // // tslint:disable-next-line: no-console
            // console.warn(">> before return ok", {
            //     csvOutputKbn,
            //     csvUnnecessaryFlag,
            //     csvUnnecessaryFlagInside,
            //     orderType,
            //     mnpInFlag,
            //     mnpInType,
            //     isSuccessGetCustomerInfo,
            //     cardTypeId,
            // });

            responseHeader.processCode = ResultCdConstants.CODE_000000;
            return this.resultEdit(
                responseHeader,
                isPrivate,
                errorMessage,
                true,
                {
                    param,
                    csvUnnecessaryFlag,
                    csvOutputKbn,
                    optionAddCsvOutputKbn,
                    abolitionCsvOutputKbn,
                    custInfo: customerInfo,
                    planInfo: plansInfo,
                    cardInfo,
                    mnpInType,
                    reserveDate,
                    orderType,
                    receivedDate: responseHeader.receivedDate,
                    isFullMvno: fullMvnoFlag,
                    options: lineOptionsTBan,
                    nNo_haishi,
                    isSameMvneCancelRequestSent,
                },
            );
        } catch (e: any) {
            if (isSQLException(e)) {
                // DBアクセスリトライエラーの場合
                // リターンコード値は各エラー時にて設定済み
                super.error(
                    e,
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APCOME0402,
                    functionType,
                );
                responseHeader.processCode = handleCode;
                // handle code might be CODE_510401 (possible csv output) so include `data` argument
                return this.resultEdit(
                    responseHeader,
                    isPrivate,
                    errorMessage,
                    false,
                    {
                        param,
                        csvUnnecessaryFlag,
                        csvOutputKbn,
                        optionAddCsvOutputKbn,
                        abolitionCsvOutputKbn,
                        custInfo: customerInfo,
                        planInfo: plansInfo,
                        cardInfo,
                        mnpInType,
                        reserveDate,
                        orderType,
                        receivedDate: responseHeader.receivedDate,
                        isFullMvno: fullMvnoFlag,
                        options: lineOptionsTBan,
                        nNo_haishi,
                        isSameMvneCancelRequestSent,
                    },
                );
            } else if (SOAPException.isSOAPException(e)) {
                super.error(
                    e,
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APCOMW0001,
                    "仮登録回線追加",
                );
                responseHeader.processCode = ResultCdConstants.CODE_510301;
                return this.resultEdit(
                    responseHeader,
                    isPrivate,
                    errorMessage,
                    false,
                    {
                        param,
                        csvUnnecessaryFlag,
                        csvOutputKbn,
                        optionAddCsvOutputKbn,
                        abolitionCsvOutputKbn,
                        custInfo: customerInfo,
                        planInfo: plansInfo,
                        cardInfo,
                        mnpInType,
                        reserveDate,
                        orderType,
                        receivedDate: responseHeader.receivedDate,
                        isFullMvno: fullMvnoFlag,
                        options: lineOptionsTBan,
                        nNo_haishi,
                        isSameMvneCancelRequestSent,
                    },
                );
            }
            // other exceptions
            super.error(
                e as Error,
                param.tenantId,
                param.requestHeader.sequenceNo,
                MsgKeysConstants.APCOME0401,
                e?.message,
            );
            responseHeader.processCode = ResultCdConstants.CODE_999999;
            return this.resultEdit(
                responseHeader,
                isPrivate,
                errorMessage,
                false,
            );
        } finally {
            await this.soManagementCommon(
                param,
                responseHeader.apiProcessID,
                executeUserId,
                executeTenantId,
                responseHeader.processCode,
                systemDateTime,
                orderType,
                reserveDate,
                isPrivate,
            );

            const resultCode = responseHeader.processCode;
            // NOTE csv出力 is refactored into CoreSwimmy連携 and will be run in PreLineAddHandler
            // if (
            //     ResultCdConstants.CODE_000000.equals(resultCode) ||
            //     ResultCdConstants.CODE_510110.equals(resultCode) ||
            //     ResultCdConstants.CODE_510301.equals(resultCode) ||
            //     ResultCdConstants.CODE_510401.equals(resultCode)
            // ) {
            // }

            // 自動計算回線グループ情報追加処理
            if (
                await this.addAutoModbucketLineGroupWithReturn(
                    apiId,
                    functionType,
                    resultCode,
                    lineNo,
                    null,
                    tenantId,
                    sequenceNo,
                    orderType,
                    mnpInType,
                )
            ) {
                // 自動計算回線グループ情報追加処理が正常動作した場合
                // 廃止元回線の回線回線グループ削除処理
                await this.deleteAbolitionOrgLineLineGroups(
                    apiId,
                    tenantId,
                    sequenceNo,
                    lineNo,
                    functionType,
                );
            }
        }
    }

    /**
     * 回線オプション使用可能判定<BR>
     *
     * @param lineOptionId 判定対象回線オプションID
     * @param targetTenantId 回線情報有り、無し
     * @param potalPlanID 回線情報エンティティ
     * @param tenantsEntity 回線情報エンティティ
     * @param wholeFlg 帯域卸フラグ
     * @param lineOptionType 判定対象回線オプション種別
     * @return 処理結果 1：true／false 2：処理エラーコード 3：エラーメッセージコード 4：T番コード
     */
    private async checkLineOptions(
        lineOptionId: string,
        targetTenantId: string,
        potalPlanID: string,
        tenantsEntity: TenantsEntity,
        wholeFlg: boolean,
        lineOptionType: string,
    ): Promise<[boolean, string, string, string]> {
        const checkResult: [boolean, string, string, string] = [
            true,
            null,
            null,
            null,
        ];

        // オプションIDの指定がない場合
        if (StringUtils.isEmpty(lineOptionId)) {
            return checkResult;
        }

        // 社内テナント種別がB-OCN/UNOモバイルではなく、回線オプションが留守番電話・キャッチホンの場合はチェックOK
        if (
            PreLineAddService.TENANT_TYPE_BOCN !== tenantsEntity.tenantType &&
            PreLineAddService.TENANT_TYPE_UNOM !== tenantsEntity.tenantType &&
            (PreLineAddService.L_OPTTYPE_VOICEMAIL.equals(lineOptionType) ||
                PreLineAddService.L_OPTTYPE_CALLWAITING.equals(lineOptionType))
        ) {
            return checkResult;
        }

        // 回線オプション情報取得
        let lineOptionsEntity: LineOptionsEntity = null;
        try {
            lineOptionsEntity = await this.apiLinesDao.getLineOptionsInfo(
                lineOptionId,
            );
        } catch (e) {
            if (isSQLException(e)) {
                // DBアクセスリトライエラーの場合
                checkResult[0] = false;
                checkResult[1] = ResultCdConstants.CODE_510111;
                checkResult[2] = MsgKeysConstants.APCOME0402;
                return checkResult;
            }
            throw e;
        }
        if (lineOptionsEntity == null) {
            checkResult[0] = false;
            checkResult[1] = ResultCdConstants.CODE_510111;
            checkResult[2] = MsgKeysConstants.APPLAW0016;
            return checkResult;
        }
        // NOTE added lineOptionId to checkResult
        checkResult[3] = lineOptionsEntity.lineOptionIdT;

        // テナントプラン回線オプション情報取得
        let tenantPlanLineOptionsEntity: TenantPlanLineOptionsEntity = null;
        try {
            tenantPlanLineOptionsEntity =
                await this.apiLinesDao.getTenantPlanLineOptionsInfo(
                    targetTenantId,
                    potalPlanID,
                    lineOptionsEntity.lineOptionType,
                );
        } catch (e) {
            if (isSQLException(e)) {
                // DBアクセスリトライエラーの場合
                checkResult[0] = false;
                checkResult[1] = ResultCdConstants.CODE_510108;
                checkResult[2] = MsgKeysConstants.APCOME0402;
                return checkResult;
            }
            throw e;
        }
        if (tenantPlanLineOptionsEntity === null) {
            checkResult[0] = false;
            checkResult[1] = ResultCdConstants.CODE_510108;
            checkResult[2] = MsgKeysConstants.APPLAW0007;
            return checkResult;
        }

        // テナント種別毎の回線オプション使用可能判定
        let checkFlg = true;
        // テナント種別
        if (tenantsEntity.tenantType === null) {
            // 帯域卸フラグ
            if (wholeFlg) {
                // 【帯域卸】
                checkFlg = lineOptionsEntity.whole;
            } else {
                // 【ID卸】
                checkFlg = lineOptionsEntity.id;
            }
        } else if (tenantsEntity.tenantType === 1) {
            // 【C-OCN】
            checkFlg = lineOptionsEntity.cOcn;
        } else if (tenantsEntity.tenantType === 2) {
            // 【B-OCN】
            checkFlg = lineOptionsEntity.bOcn;
        } else if (tenantsEntity.tenantType === 3) {
            // 【UNOモバイル】
            checkFlg = lineOptionsEntity.uno;
        }

        // 回線オプション使用可能判定NG
        if (!checkFlg) {
            checkResult[0] = false;
            checkResult[1] = ResultCdConstants.CODE_510111;
            checkResult[2] = MsgKeysConstants.APPLAW0014;
        }

        return checkResult;
    }

    /**
     * Get T番 for 留守電話 and キャッチホン if needed.
     * In `checkLineOptions`, T番 is not always returned because of the following condition:
     * > 社内テナント種別がB-OCN/UNOモバイルではなく、回線オプションが留守番電話・キャッチホンの場合はチェックOK
     * @param lineOptionId option id from parameter
     * @param fromCheckLineOptions from `checkLineOptions` return array [3]
     * @returns
     */
    private async getLineOptionsTBanIfNeeded(
        lineOptionId: string,
        fromCheckLineOptions: string | null,
    ): Promise<string> {
        // オプションIDの指定がない場合
        if (StringUtils.isEmpty(lineOptionId)) {
            return null;
        }
        // if tenant is not B-OCN/UNO
        if (isNone(fromCheckLineOptions)) {
            const lineOptionsEntity = await this.apiLinesDao.getLineOptionsInfo(
                lineOptionId,
            );
            return lineOptionsEntity?.lineOptionIdT ?? null;
        }
        return fromCheckLineOptions;
    }

    /**
     * 回線オプション使用可能判定結果ログ出力<BR>
     *
     * @param msgId メッセージID
     * @param tenantId テナントID
     * @param targetTenantId 回線情報エンティティ
     * @param potalPlanID プランID
     * @param lineOptionId 回線オプションID
     * @param lineNo 回線番号
     * @param sequenceNo シーケンス番号
     * @param functionType 機能ID
     */
    private warnLineOptions(
        msgId: string,
        tenantId: string,
        targetTenantId: string,
        potalPlanID: string,
        lineOptionId: string,
        lineNo: string,
        sequenceNo: string,
        functionType: string,
    ): void {
        if (MsgKeysConstants.APPLAW0007.equals(msgId)) {
            super.warn(
                tenantId,
                sequenceNo,
                msgId,
                targetTenantId,
                potalPlanID,
                lineOptionId,
            );
        } else if (MsgKeysConstants.APPLAW0014.equals(msgId)) {
            super.warn(
                tenantId,
                sequenceNo,
                msgId,
                lineOptionId,
                targetTenantId,
            );
        } else if (MsgKeysConstants.APPLAW0016.equals(msgId)) {
            super.warn(tenantId, sequenceNo, msgId, lineOptionId);
        } else if (MsgKeysConstants.APCOME0402.equals(msgId)) {
            super.error(tenantId, sequenceNo, msgId, functionType);
        }
    }

    /**
     * 回線情報Entityの設定処理<BR>
     *
     * @param linesEntity 回線情報エンティティ
     * @param param パラメータ
     * @param plansEntity プランエンティティ
     * @param cardEntity カードエンティティ
     * @param systemDateTime システム日付
     * @param reserveDate 予約日
     * @param orderType オーダ種別
     * @param isExistsLines 回線情報有り、無し
     * @param fullMvnoFlag フルMVNOフラグ
     * @param voiceInfoEntity 0035でんわ情報
     * @return 登録・更新情報を設定したエンティティ
     */
    private setPreLinesEntity(
        linesEntity: LinesEntity,
        param: PreLineAddInputDto,
        plansEntity: PlansEntity,
        cardEntity: CardEntity,
        systemDateTime: string,
        reserveDate: string,
        orderType: string,
        isExistsLines: boolean,
        fullMvnoFlag: boolean,
        voiceInfoEntity: VoiceInfoEntity,
    ): LinesEntity {
        let retLinesEntity: LinesEntity;
        if (isExistsLines) {
            retLinesEntity = linesEntity.toJSON(); // get local copy
            retLinesEntity.updatedAt = fromUnixTime(
                MvnoUtil.convDateFormat(systemDateTime) / 1000,
            );
        } else {
            retLinesEntity = new LinesEntity();
            // 回線ID
            retLinesEntity.lineId = param.lineNo;
            // created_at
            retLinesEntity.createdAt = fromUnixTime(
                MvnoUtil.convDateFormat(systemDateTime) / 1000,
            );
        }

        // 最終オーダ種別
        retLinesEntity.lineStatus = "01";
        // 仮登録フラグ
        retLinesEntity.tempRegist = true;
        // 代表N番
        retLinesEntity.nnumber = param.nBan;
        // 回線グループID
        retLinesEntity.groupId = null;

        // 回線利用開始年月日
        let lineUsageStartDate: string = null;
        if (Constants.ORDER_TYPE_0.equals(orderType)) {
            lineUsageStartDate = systemDateTime;
        } else {
            lineUsageStartDate = reserveDate + ":00";
        }
        retLinesEntity.lineActDate = fromUnixTime(
            MvnoUtil.convDateFormat(lineUsageStartDate) / 1000,
        );

        // サービス開始日
        retLinesEntity.serviceDate = null;

        // 国際ローミング利用限度額
        if (StringUtils.isNotEmpty(param.id_intlRoaming)) {
            retLinesEntity.roamingMaxId = param.id_intlRoaming;
        } else {
            retLinesEntity.roamingMaxId = null;
        }

        // 留守番でんわID
        if (StringUtils.isNotEmpty(param.id_voicemail)) {
            retLinesEntity.voiceMailId = param.id_voicemail;
        } else {
            retLinesEntity.voiceMailId = null;
        }

        // キャッチホンID
        if (StringUtils.isNotEmpty(param.id_callWaiting)) {
            retLinesEntity.callWaitingId = param.id_callWaiting;
        } else {
            retLinesEntity.callWaitingId = null;
        }

        // 国際電話ID
        if (StringUtils.isNotEmpty(param.id_intlCall)) {
            retLinesEntity.intlCallId = param.id_intlCall;
        } else {
            retLinesEntity.intlCallId = null;
        }

        // 転送でんわID
        if (StringUtils.isNotEmpty(param.id_forwarding)) {
            retLinesEntity.forwardingId = param.id_forwarding;
        } else {
            retLinesEntity.forwardingId = null;
        }

        // 国際着信転送ID
        if (StringUtils.isNotEmpty(param.id_intlForwarding)) {
            retLinesEntity.intlForwardingId = param.id_intlForwarding;
        } else {
            retLinesEntity.intlForwardingId = null;
        }

        // SIM番号(DN番号)
        retLinesEntity.simNumber = param.sim_no;
        // 端末種別ID
        retLinesEntity.deviceTypeId = param.cardTypeId;
        // 端末種別名
        if (cardEntity !== null) {
            retLinesEntity.modelType = cardEntity.deviceTypeName;
        } else {
            retLinesEntity.modelType = null;
        }
        // 製造番号(IMEI)
        retLinesEntity.imei = param.psid;
        // 端末利用開始年月日
        retLinesEntity.startDate = null;
        // ドメイン
        retLinesEntity.domain = null;
        // 認証ID
        retLinesEntity.authId = null;
        // 国内用IP ACT東
        retLinesEntity.actEastIp = null;
        // 国内用IP ACT西
        retLinesEntity.actWestIp = null;
        // 国内用IP SBY
        retLinesEntity.sbyIp = null;
        // 国際用固定IPアドレス
        retLinesEntity.fixedIp = null;

        if (plansEntity !== null) {
            // 再販料金プラン
            retLinesEntity.pricePlanId = plansEntity.pricePlanId;
            // 再販料金プラン名
            retLinesEntity.pricePlanName = plansEntity.planName;
        } else {
            // 再販料金プラン
            retLinesEntity.pricePlanId = null;
            // 再販料金プラン名
            retLinesEntity.pricePlanName = null;
        }
        // POI情報
        retLinesEntity.poi = null;
        // 契約種別
        let contractType: string = null;
        if ("SA".equals(param.access)) {
            contractType = "3G";
        } else if ("SB".equals(param.access)) {
            contractType = "3G(SMS)";
        } else if ("SC".equals(param.access)) {
            contractType = "LTE";
        } else if ("SD".equals(param.access)) {
            contractType = "LTE(SMS)";
        } else if ("SE".equals(param.access)) {
            contractType = "LTE(音声)";
        } else if ("SF".equals(param.access)) {
            contractType = "フルLTE";
        } else if ("SG".equals(param.access)) {
            contractType = "フルLTE(SMS)";
        } else if ("SH".equals(param.access)) {
            contractType = "5G(NSA)";
        } else if ("SJ".equals(param.access)) {
            contractType = "5G(NSA)(音声)";
        }
        retLinesEntity.contractType = contractType;
        // 認証パターン
        retLinesEntity.authPattern = null;
        // SIM種別
        retLinesEntity.simType = param.sim_type;
        // 端末機種名
        retLinesEntity.deviceModelName = null;
        // 端末製造番号
        retLinesEntity.imeisv_2 = null;
        // 端末製造番号(予備)
        retLinesEntity.imeisv_3 = null;
        // メモ欄
        retLinesEntity.notes = null;
        // 半黒フラグ
        if (plansEntity !== null) {
            retLinesEntity.simFlag = plansEntity.defaultSimFlag;
        } else {
            retLinesEntity.simFlag = false;
        }
        // 黒化日
        retLinesEntity.activateDate = null;

        // modify_flag
        retLinesEntity.modifyFlag = true;

        // フルMVNO時の値設定
        if (fullMvnoFlag !== null && fullMvnoFlag) {
            retLinesEntity.imsi = param.imsi;
            retLinesEntity.puk1 = param.puk1;
            retLinesEntity.puk2 = param.puk2;
            retLinesEntity.usageStatus = parseInt(param.lineStatus, 10);
        } else {
            retLinesEntity.imsi = null;
            retLinesEntity.puk1 = null;
            retLinesEntity.puk2 = null;
            retLinesEntity.usageStatus = null;
        }

        // nw_modify_flag
        retLinesEntity.nwModifyFlag = false;

        if (voiceInfoEntity !== null) {
            // 0035でんわプランID
            retLinesEntity.voicePlanId = voiceInfoEntity.voicePlanId;
            // 0035でんわプラン名
            retLinesEntity.voicePlanName = voiceInfoEntity.voicePlanName;
        } else {
            // 0035でんわプランID
            retLinesEntity.voicePlanId = null;
            // 0035でんわプラン名
            retLinesEntity.voicePlanName = null;
        }

        this.context.log(
            "PreLineAddService.service setPreLinesEntity end",
            retLinesEntity.lineStatus,
        );
        return retLinesEntity;
    }

    /**
     * 返却オブジェクト編集
     *
     * @param responseHeader レスポンスヘッダ
     * @param isPrivate 呼び出しフラグ
     * @param errorMessage エラーメッセージ
     * @param ok OKフラグ
     * @param data additional data for CoreSwimmy連携
     * @return 返却値オブジェクト
     */
    private resultEdit(
        responseHeader: ResponseHeader,
        isPrivate: boolean,
        errorMessage: string,
        ok: boolean,
        data: {
            param: PreLineAddInputDto;
            csvUnnecessaryFlag: string;
            csvOutputKbn: string;
            optionAddCsvOutputKbn: string;
            abolitionCsvOutputKbn: string;
            custInfo: CustomerInfoEntity;
            planInfo: PlansEntity;
            cardInfo: CardEntity;
            mnpInType: number;
            reserveDate: string;
            orderType: string;
            receivedDate: string;
            isFullMvno: boolean;
            options: LineOptionsTBan;
            nNo_haishi: string;
            isSameMvneCancelRequestSent?: boolean;
        } = null,
    ): PreLineAddOutputDto {
        const result: PreLineAddOutputDto = {
            jsonBody: {
                responseHeader,
            },
        };

        if (!isNone(data)) {
            /**
             * this will be needed for result code
             * - CODE_000000
             * - CODE_510110
             * - CODE_510301
             * - CODE_510401
             */
            result.additionalData = {
                nNo: data.param.nBan,
                nNo_haishi: data.nNo_haishi,
                csvUnnecessary: data.csvUnnecessaryFlag === "1",
                csvOutputKbn: data.csvOutputKbn === "1",
                optionAddCsvOutputKbn: data.optionAddCsvOutputKbn === "1",
                abolitionCsvOutputKbn: data.abolitionCsvOutputKbn === "1",
                custInfo: data.custInfo,
                planInfo: data.planInfo,
                cardInfo: data.cardInfo,
                mnpInType: data.mnpInType,
                reserveDate: data.reserveDate,
                orderType: data.orderType,
                receivedDate: data.receivedDate,
                isFullMvno: isNone(data.isFullMvno) ? false : data.isFullMvno,
                options: data.options,
                isSameMvneCancelRequestSent: data?.isSameMvneCancelRequestSent,
            };
        }

        return result;
    }

    /**
     * SO管理共通パラメータ設定
     *
     * @param param パラメータ
     * @param resultBean チェック結果
     * @param executeUserId 実行ユーザID
     * @param executeTenantId 実行テナントID
     * @param code オーダステータス
     * @param receivedDate REST API共通処理が呼び出されたシステム日付
     * @param orderType オーダ種別
     * @param reserveDate 受信日時
     * @param isPrivate 呼び出しフラグ
     * @throws Exception 例外
     */
    private async soManagementCommon(
        param: PreLineAddInputDto,
        apiProcessID: string,
        executeUserId: string,
        executeTenantId: string,
        code: string,
        receivedDate: string,
        orderType: string,
        reserveDate: string,
        isPrivate: boolean,
    ) {
        // オーダ種別が予想以外オーダの場合処理しない
        if (Constants.ORDER_TYPE_9.equals(orderType)) {
            return;
        }

        // SO管理共通を呼び出す
        const soObject = new SOObject();

        // オーダ種別により、SO管理の設定情報を判断
        if (Constants.ORDER_TYPE_0.equals(orderType)) {
            // サービスオーダID
            soObject.setServiceOrderId(apiProcessID);
            // 申込日
            soObject.setOrderDate(receivedDate);
            // 予約日
            soObject.setReserveDate(null);
            // オーダ種別
            soObject.setOrderType(null);
            // 完了日
            soObject.setExecDate(MvnoUtil.getDateTimeNow());
            // オーダステータス
            soObject.setOrderStatus(code);
            // 回線ID
            soObject.setLineId(param.lineNo);
            // 回線グループID
            soObject.setLineGroupId("");
            // 所属テナントID
            soObject.setTenantId(param.targetTenantId);
            // 実行ユーザID
            soObject.setExecuteUserId(executeUserId);
            // 実行テナントID
            soObject.setExecuteTenantId(executeTenantId);
            // 機能種別
            soObject.setFunctionType(param.requestHeader.functionType);
            // 操作区分
            soObject.setOperationDivision("");
            // 変更前プランID
            soObject.setChangeOldplanId("");
            // 変更後プランID
            soObject.setChangeNewplanId("");
            // 回線所属変更回線ID
            soObject.setChangePlanId("");
            // 基本容量
            soObject.setBasicCapacity("");
            // オプションプランID
            soObject.setOptionPlanId("");
            // 内部呼び出し
            soObject.setInternalFlag(isPrivate);
            // REST電文
            soObject.setRestMessage(null);
            // MNP転入フラグ
            soObject.setMnpInFlag(param.mnpInFlag);
        } else if (Constants.ORDER_TYPE_1.equals(orderType)) {
            // サービスオーダID
            soObject.setServiceOrderId(apiProcessID);
            // 申込日
            soObject.setOrderDate(receivedDate);
            // 予約日
            soObject.setReserveDate(reserveDate);
            // オーダ種別
            soObject.setOrderType(orderType);
            // 完了日
            soObject.setExecDate(null);
            // オーダステータス
            soObject.setOrderStatus(code);
            // 回線ID
            soObject.setLineId(param.lineNo);
            // 回線グループID
            soObject.setLineGroupId(null);
            // 所属テナントID
            soObject.setTenantId(param.targetTenantId);
            // 実行ユーザID
            soObject.setExecuteUserId(null);
            // 実行テナントID
            soObject.setExecuteTenantId(null);
            // 機能種別
            soObject.setFunctionType(param.requestHeader.functionType);
            // 操作区分
            soObject.setOperationDivision("");
            // 変更前プランID
            soObject.setChangeOldplanId("");
            // 変更後プランID
            soObject.setChangeNewplanId("");
            // 回線所属変更回線ID
            soObject.setChangePlanId("");
            // 基本容量
            soObject.setBasicCapacity("");
            // オプションプランID
            soObject.setOptionPlanId("");
            // 内部呼び出し
            soObject.setInternalFlag(isPrivate);
            // REST電文
            soObject.setRestMessage(MvnoUtil.getRestMessage(param));
            // MNP転入フラグ
            soObject.setMnpInFlag(param.mnpInFlag);
        } else if (Constants.ORDER_TYPE_2.equals(orderType)) {
            // サービスオーダID
            soObject.setServiceOrderId(param.reserve_soId);
            // 申込日
            soObject.setOrderDate(null);
            // 予約日
            soObject.setReserveDate(null);
            // オーダ種別
            soObject.setOrderType(orderType);
            // 完了日
            soObject.setExecDate(MvnoUtil.getDateTimeNow());
            // オーダステータス
            soObject.setOrderStatus(code);
            // 回線ID
            soObject.setLineId(param.lineNo);
            // 回線グループID
            soObject.setLineGroupId(null);
            // 所属テナントID
            soObject.setTenantId(param.targetTenantId);
            // 実行ユーザID
            soObject.setExecuteUserId(null);
            // 実行テナントID
            soObject.setExecuteTenantId(null);
            // 機能種別
            soObject.setFunctionType(param.requestHeader.functionType);
            // 操作区分
            soObject.setOperationDivision("");
            // 変更前プランID
            soObject.setChangeOldplanId("");
            // 変更後プランID
            soObject.setChangeNewplanId("");
            // 回線所属変更回線ID
            soObject.setChangePlanId("");
            // 基本容量
            soObject.setBasicCapacity("");
            // オプションプランID
            soObject.setOptionPlanId("");
            // 内部呼び出し
            soObject.setInternalFlag(isPrivate);
            // REST電文
            soObject.setRestMessage(null);
            // MNP転入フラグ
            soObject.setMnpInFlag(param.mnpInFlag);
        }

        await this.soCommon.soCommon(soObject);
    }

    private async checkCsvOutPut(
        param: PreLineAddInputDto,
        mnpInType: number,
    ): Promise<Map<string, string | CustomerInfoEntity>> {
        const result = new Map<string, string | CustomerInfoEntity>();
        /** 代表N番 */
        const nBan = param.nBan;
        /** テナントID */
        const tenantId = param.tenantId;
        /** 送信番号 */
        const sequenceNo = param.requestHeader.sequenceNo;
        /** カード種別ID */
        const cardTypeId = param.cardTypeId;
        /** プランID */
        const potalPlanID = getStringParameter(param.potalPlanID);
        // 機能ID
        const functionType = param.requestHeader.functionType;

        // N番に対応するカード用途を取得
        let cardPurpose: string = null;
        let customerInfo: CustomerInfoEntity = null;
        try {
            this.context.log(
                "PreLineAddService.checkCsvOutPut getCustomerInfo",
                nBan,
            );
            customerInfo = await this.apiLinesDao.getCustomerInfo(nBan);
        } catch (e) {
            if (isSQLException(e)) {
                // DBアクセスリトライエラーの場合
                super.error(
                    e,
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APCOME0402,
                    functionType,
                );
                result.set(PreLineAddService.KEY_CSV_OUT_KBN, "0");
                return result;
            }
            throw e;
        }
        // 取得結果判定
        if (customerInfo === null) {
            super.error(
                tenantId,
                sequenceNo,
                MsgKeysConstants.APPLAE0017,
                nBan,
            );
            result.set(PreLineAddService.KEY_CSV_OUT_KBN, "0");
            return result;
        } else {
            cardPurpose = customerInfo.purpose;
        }
        // カード用途で使用可能なカード種別かをチェック
        let cardCheckCardResult: string = null;
        try {
            cardCheckCardResult = await this.apiLinesDao.getCardCheckCard(
                cardTypeId,
                cardPurpose,
            );
        } catch (e) {
            if (isSQLException(e)) {
                // DBアクセスリトライエラーの場合
                super.error(
                    e,
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APCOME0402,
                    functionType,
                );
                result.set(PreLineAddService.KEY_CSV_OUT_KBN, "0");
                return result;
            }
            throw e;
        }
        // 取得結果判定
        if (cardCheckCardResult === null) {
            super.error(
                tenantId,
                sequenceNo,
                MsgKeysConstants.APPLAE0018,
                cardTypeId,
                cardPurpose,
            );
            result.set(PreLineAddService.KEY_CSV_OUT_KBN, "0");
            return result;
        }
        // カード用途で使用可能なプランかをチェック
        let cardCheckPlanResult: string = null;
        try {
            cardCheckPlanResult = await this.apiLinesDao.getCardCheckPlan(
                potalPlanID,
                cardPurpose,
            );
        } catch (e) {
            if (isSQLException(e)) {
                // DBアクセスリトライエラーの場合
                super.error(
                    e,
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APCOME0402,
                    functionType,
                );
                result.set(PreLineAddService.KEY_CSV_OUT_KBN, "0");
                return result;
            }
            throw e;
        }
        // 取得結果判定
        if (cardCheckPlanResult === null) {
            super.error(
                tenantId,
                sequenceNo,
                MsgKeysConstants.APPLAE0019,
                potalPlanID,
                cardPurpose,
            );
            result.set(PreLineAddService.KEY_CSV_OUT_KBN, "0");
            return result;
        }
        // カード種別で使用可能なプランかをチェック
        let planCardResult: string;
        try {
            planCardResult = await this.apiLinesDao.getPlanCard(
                potalPlanID,
                cardTypeId,
            );
        } catch (e) {
            if (isSQLException(e)) {
                // DBアクセスリトライエラーの場合
                super.error(
                    e,
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APCOME0402,
                    functionType,
                );
                result.set(PreLineAddService.KEY_CSV_OUT_KBN, "0");
                return result;
            }
            throw e;
        }
        // 取得結果判定
        if (planCardResult === null) {
            super.error(
                tenantId,
                sequenceNo,
                MsgKeysConstants.APPLAE0020,
                potalPlanID,
                cardTypeId,
            );
            result.set(PreLineAddService.KEY_CSV_OUT_KBN, "0");
            return result;
        }

        // お客様基本情報の判定
        // ※カード用途取得処理で取得・nullチェック済み
        if (
            (mnpInType === PreLineAddService.MNP_IN_TYPE_YES ||
                mnpInType === PreLineAddService.MNP_IN_TYPE_SAME_MVNE_YES) &&
            (CheckUtil.checkIsNotNull(customerInfo.mnpAddOptionId) ||
                CheckUtil.checkIsNotNull(customerInfo.mnpAddOptionIdT))
        ) {
            // NOTE add T番 check
            super.error(
                tenantId,
                sequenceNo,
                MsgKeysConstants.APPLAE0003,
                nBan,
                param.mnpInFlag,
            );
            result.set(PreLineAddService.KEY_CSV_OUT_KBN, "0");
            return result;
        }

        result.set(PreLineAddService.KEY_CSV_OUT_KBN, "1");
        result.set(PreLineAddService.KEY_CUSTOMER_INFO, customerInfo);
        return result;
    }

    /**
     * お客様基本情報を取得し、オプション追加CSVの作成要否および回線廃止CSVの作成要否を判定する
     *
     * @param param 入力パラメータ
     * @return CSV出力要否、お客様基本情報
     */
    private async checkDeleteCsvOutput(
        param: PreLineAddInputDto,
    ): Promise<Map<string, string | CustomerInfoEntity>> {
        const result = new Map<string, string | CustomerInfoEntity>();
        // テナントID
        const tenantId = param.tenantId;
        // 送信番号
        const sequenceNo = param.requestHeader.sequenceNo;
        // 回線番号
        const lineNo = param.lineNo;
        // MNP転入フラグ
        const mnpInFlag = param.mnpInFlag;

        try {
            // 回線番号をキーに、お客様基本情報を取得する
            this.context.log(
                "PreLineAddService.checkDeleteCsvOutput getLineCustomerInfo",
                param.lineNo,
            );
            const customerInfo = await this.apiLinesDao.getLineCustomerInfo(
                param.lineNo,
            );
            if (customerInfo === null) {
                // レコードの取得に失敗した場合、各CSV出力要否に0をセットしてエラー処理
                super.error(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APPLAE0004,
                    lineNo,
                    mnpInFlag,
                );
                result.set(PreLineAddService.KEY_CSV_OUT_KBN_OPTION_ADD, "0");
                result.set(PreLineAddService.KEY_CSV_OUT_KBN_ABOLITION, "0");
                this.context.log(
                    "PreLineAddService.checkDeleteCsvOutput customerInfo is null",
                );
            } else if (
                customerInfo.mnpDelOptionId === null ||
                customerInfo.mnpDelOptionIdT === null
            ) {
                // NOTE add T番 check
                // MNP転出オプションIDが取得できていない場合、各CSV出力要否に0をセットしてエラー処理
                super.error(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APPLAE0004,
                    lineNo,
                    mnpInFlag,
                );
                result.set(PreLineAddService.KEY_CSV_OUT_KBN_OPTION_ADD, "0");
                result.set(PreLineAddService.KEY_CSV_OUT_KBN_ABOLITION, "0");
                this.context.log(
                    "PreLineAddService.checkDeleteCsvOutput mnpDelOptionId is null",
                );
            } else {
                // お客様基本情報が取得できた場合、各CSV出力要否に1とお客様基本情報をセットする
                result.set(PreLineAddService.KEY_CSV_OUT_KBN_OPTION_ADD, "1");
                result.set(PreLineAddService.KEY_CSV_OUT_KBN_ABOLITION, "1");
                result.set(PreLineAddService.KEY_CUSTOMER_INFO, customerInfo);
            }
        } catch (e) {
            if (isSQLException(e)) {
                // DBアクセスエラーの場合、各CSV出力要否に0をセットしてエラー処理
                super.error(
                    e,
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APCOME0402,
                    param.requestHeader.functionType,
                );
                result.set(PreLineAddService.KEY_CSV_OUT_KBN_OPTION_ADD, "0");
                result.set(PreLineAddService.KEY_CSV_OUT_KBN_ABOLITION, "0");
            } else {
                // レコードの取得に失敗した場合、各CSV出力要否に0をセットしてエラー処理
                super.error(
                    e,
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APPLAE0004,
                    lineNo,
                    mnpInFlag,
                );
                result.set(PreLineAddService.KEY_CSV_OUT_KBN_OPTION_ADD, "0");
                result.set(PreLineAddService.KEY_CSV_OUT_KBN_ABOLITION, "0");
            }
        }

        return result;
    }

    /**
     * Send cancel request to Core's Cancel API
     * @param serviceOrderId Core's Service Order ID (AP0)
     * @param tenantId order tenant ID
     */
    private async cancelWithCoreAPI(
        serviceOrderId: string,
        orderType: string,
        tenantId: string,
    ): Promise<boolean> {
        this.context.log("PreLineAddService.cancelWithCoreAPI start", {
            serviceOrderId,
            orderType,
            tenantId,
        });
        let isOk = false;
        try {
            const cancelResult = await this.coreApiService.sendSoCancelRequest(
                tenantId,
                serviceOrderId,
                "0",
            );
            if (cancelResult?.isOk) {
                this.context.log(
                    "PreLineAddService.cancelWithCoreAPI result OK",
                    serviceOrderId,
                    cancelResult,
                );
            } else {
                // error log
                this.context.error(
                    "PreLineAddService.cancelWithCoreAPI result NG",
                    serviceOrderId,
                    cancelResult,
                );
            }
            isOk = cancelResult.isOk;
        } catch (error) {
            this.context.error(
                "PreLineAddService.cancelWithCoreAPI error",
                serviceOrderId,
                error,
            );
        }
        return isOk;
    }

    /**
     * Send cancel request to Front's Cancel API
     * @param haishiOrderId AP0
     * @param tenantId order tenant ID
     */
    private async cancelHaishiWithFrontAPI(
        haishiOrderId: string,
        tenantId: string,
    ): Promise<boolean> {
        this.context.log("PreLineAddService.cancelHaishiWithFrontAPI start", {
            haishiOrderId,
            tenantId,
        });
        let isOk = false;
        try {
            // find front request order ID from core swimmy
            const apiLog = await CoreSwimmyApiLog.findOne(
                {
                    requestOrderId: haishiOrderId,
                },
                { soId: 1 },
            );
            if (isNone(apiLog) || isNone(apiLog.soId)) {
                this.context.error(
                    "PreLineAddService.cancelHaishiWithFrontAPI CoreSwimmyLog not found or empty soId",
                    haishiOrderId,
                    apiLog?.soId,
                );
                return isOk;
            }

            const result = await this.frontApiService.sendSoCancelRequest(
                tenantId,
                apiLog.soId,
            );
            if (result?.isOk) {
                this.context.log(
                    "PreLineAddService.cancelHaishiWithFrontAPI result OK",
                    haishiOrderId,
                    result,
                );
            } else {
                // error log
                this.context.error(
                    "PreLineAddService.cancelHaishiWithFrontAPI result NG",
                    haishiOrderId,
                    result,
                );
            }
            isOk = result.isOk;
        } catch (error) {
            this.context.error(
                "PreLineAddService.cancelHaishiWithFrontAPI error",
                haishiOrderId,
                error,
            );
        }
        return isOk;
    }
}
