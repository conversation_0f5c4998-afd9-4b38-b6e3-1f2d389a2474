import TenantManage from "@/core/common/TenantManage";
import APICommonDAO from "@/core/dao/APICommonDAO";
import APILinesDAO from "@/core/dao/APILinesDAO";
import RESTCommon from "@/core/common/RESTCommon";
import StringUtils from "@/core/common/StringUtils";
import LinesGroupInfoInputDto from "@/core/dto/LinesGroupInfoInputDto";
import LinesGroupInfoOutputDto from "@/core/dto/LinesGroupInfoOutputDto";
import LinesGroupInfoInsideOutputDto from "@/core/dto/LinesGroupInfoInsideOutputDto";
import LineGroupsEntity from "@/core/entity/LineGroupsEntity";
import { TenantsEntity } from "@/core/entity/TenantsEntity";
import MsgKeysConstants from "@/core/constant/MsgKeysConstants";
import MvnoUtil from "@/core/common/MvnoUtil";
import ResultCdConstants from "@/core/constant/ResultCdConstants";
import { LinesEntity } from "@/core/entity/LinesEntity";
import Check from "@/core/common/Check";

import { IFService } from "../IFService";
import ResponseHeader from "@/core/dto/ResponseHeader";
import LineList from "@/core/dto/LineList";
import CheckUtil from "@/core/common/CheckUtil";
import APILinesGroupDAO from "@/core/dao/ApiLinesGroupDAO";
import { isSQLException } from "@/helpers/queryHelper";
import LineLineGroupsEntity from "@/core/entity/LineLineGroupsEntity";
import { getStringParameter } from "@/types/parameter.string";

export default class LineGroupInfoAcquisitionService
    extends RESTCommon
    implements IFService<LinesGroupInfoInputDto, LinesGroupInfoOutputDto> {

    private apiCommonDao = new APICommonDAO(this.request, this.context);
    private linesGroupDao = new APILinesGroupDAO(this.request, this.context);

    async service(param: LinesGroupInfoInputDto, ...params: any[]): Promise<LinesGroupInfoOutputDto> {
        this.debug(param.tenantId, param.requestHeader.sequenceNo,MsgKeysConstants.APGBID0001,
            this.getClassName(), "service", JSON.stringify(param));
        // REST API共通処理が呼び出されたシステム日付
        const receivedDate = MvnoUtil.getDateTimeNow();
        // 返却値
        let output: LinesGroupInfoOutputDto = null;
        // 　「処理コード」初期化
        let handleCode = ResultCdConstants.CODE_000000;
        // 送信番号
        const sequenceNo = param.requestHeader.sequenceNo;
        // 機能種別
        const functionType = param.requestHeader.functionType;
        // テナントID
        const tenantId = param.tenantId;
        // 回線グループID
        const lineGroupId = getStringParameter(param.lineGroupId);
        // API処理ID
        const apiProcessId = this.context.responseHeader?.apiProcessID ?? "";
        // 社内テナント情報
        let tenantsEntity: TenantsEntity = null;
        // 社内テナント種別
        let tenantType: number = null;
        // 卸ポータルグループプランID
        let potalGroupPlanID: string = null;
        // 卸ポータルグループプラン名
        let potalGroupPlanName: string = null;
        // 回線グループ管理情報
        let lineGroupsEntity: LineGroupsEntity = null;
        // 変数「エラーメッセージ」を定義する
        const errorMessage: string = null;

        const isPrivate = false;        // not exist anymore

        let lineInfos : LinesEntity[] = [];

        try {
            const chkLineGroupID = Check.checkLineGroupId(lineGroupId);

            // 回線グループIDフォーマットチェック結果=trueの場合
            if (chkLineGroupID) {
                // 社内テナント種別チェック
                try {
                    tenantsEntity = await this.apiCommonDao.getTenants(tenantId);
                } catch (error) {
                    // DBアクセスリトライエラーの場合
                    if (isSQLException(error)) {
                        handleCode = ResultCdConstants.CODE_080102;
                    }
                    throw error;
                }
                if (tenantsEntity == null) {
                    // エラー処理の「■出力ログ内容編集」の項番5より編集し、ログ出力
                    this.warn(tenantId, sequenceNo, MsgKeysConstants.APGBIW0005);

                    // 変数「処理コード」に「080102」を設定する
                    handleCode = ResultCdConstants.CODE_080102;

                    // ヘッダ情報を設定する
                    output = this.setHeaderInfo(sequenceNo, handleCode, apiProcessId, isPrivate, receivedDate);
                    // 変数「処理コード」を判定し,ボディ情報（異常系）を設定する
                    this.setBodyInfo(output, lineInfos, handleCode, potalGroupPlanID, potalGroupPlanName, isPrivate, errorMessage, tenantId, sequenceNo);

                    this.debug(param.tenantId, param.requestHeader.sequenceNo,MsgKeysConstants.APGBID0002,
                        this.getClassName(), "service");
                    // 返却値を返却し、処理終了
                    return output;
                } else {
                    // テナントテーブルから「社内テナント種別」を取得する
                    tenantType = tenantsEntity.tenantType;
                    // 変数「社内テナント種別」判定
                    if (!CheckUtil.checkIsNull(tenantType) && tenantType === 1) {
                        // 回線グループ基本情報（C-OCN）を取得する
                        try {
                            lineInfos = await this.linesGroupDao.getLinesGroupInfo(lineGroupId);
                        } catch (error) {
                            if (isSQLException(error)) {
                                // DBアクセスリトライエラーの場合
                                handleCode = ResultCdConstants.CODE_080201;
                            }
                            throw error;
                        }
                        // 変数「回線グループ基本情報リスト」を判定する
                        if (null == lineInfos) {
                            // エラー処理の「■出力ログ内容編集」の項番2より編集し、ログ出力
                            this.warn(tenantId, sequenceNo,MsgKeysConstants.APGBIW0002);

                            // 変数「処理コード」に「080201」を設定し、処理２へ
                            handleCode = ResultCdConstants.CODE_080201;

                            /** STEP1.2a版対応　変更　START */
                            // ヘッダ情報を設定する
                            output = this.setHeaderInfo(sequenceNo, handleCode, apiProcessId, isPrivate, receivedDate);
                            // 変数「処理コード」を判定し,ボディ情報（異常系）を設定する
                            this.setBodyInfo(output, lineInfos, handleCode, potalGroupPlanID, potalGroupPlanName, isPrivate, errorMessage, tenantId, sequenceNo);

                            this.debug(param.tenantId, param.requestHeader.sequenceNo, MsgKeysConstants.APGBID0002,
                                this.getClassName(), "service");
                            // 返却値を返却し、処理終了
                            return output;
                        } else {
                            // 回線グループプラン情報を設定する
                            potalGroupPlanID = "";
                            potalGroupPlanName = "";

                            /** STEP1.2a版対応　変更　START */
                            // ヘッダ情報を設定する
                            output = this.setHeaderInfo(sequenceNo, handleCode, apiProcessId, isPrivate, receivedDate);
                            // 変数「処理コード」を判定し,ボディ情報（異常系）を設定する
                            this.setBodyInfo(output, lineInfos, handleCode, potalGroupPlanID, potalGroupPlanName, isPrivate, errorMessage, tenantId, sequenceNo);

                            this.debug(param.tenantId, param.requestHeader.sequenceNo, MsgKeysConstants.APGBID0002,
                                this.getClassName(), "service");
                            // 返却値を返却し、処理終了
                            return output;
                        }
                    } else {
                        // 回線グループ基本情報取得（C-OCN以外）
                        // 回線グループ管理情報を取得する
                        // STEP20.0版対応　変更　START
                        try {
                            lineGroupsEntity = await this.linesGroupDao.getLineGroupsInfo(lineGroupId);
                        } catch (error) {
                            this.context.error(error, error.message);
                            if (isSQLException(error)) {
                                // DBアクセスリトライエラーの場合
                                handleCode = ResultCdConstants.CODE_080202;
                            }
                            throw error;
                        }
                        // STEP20.0版対応　変更　END
                        // 変数「回線グループ管理情報」=null　または　変数「回線グループ管理情報」．テナントID≠入力パラメータ．「テナントID」
                        // 変数「回線グループ管理情報」．ステータス≠1
                        if (lineGroupsEntity == null || lineGroupsEntity.status !== 1 || tenantId !== lineGroupsEntity.tenantId) {
                            // エラー処理の「■出力ログ内容編集」の項番3より編集し、ログ出力
                            const param0 = lineGroupId;
                            const param1 = tenantId;
                            this.warn(tenantId, sequenceNo, MsgKeysConstants.APGBIW0003, param0, param1);

                            // 変数「処理コード」に「080202」を設定する
                            handleCode = ResultCdConstants.CODE_080202;

                            // ヘッダ情報を設定する
                            output = this.setHeaderInfo(sequenceNo, handleCode, apiProcessId, isPrivate, receivedDate);
                            // 変数「処理コード」を判定し,ボディ情報（異常系）を設定する
                            this.setBodyInfo(output, lineInfos, handleCode, potalGroupPlanID, potalGroupPlanName, isPrivate, errorMessage, tenantId, sequenceNo);
                            this.debug(param.tenantId, param.requestHeader.sequenceNo, MsgKeysConstants.APGBID0002,
                                this.getClassName(), "service");
                            // 返却値を返却し、処理終了
                            return output;
                        } else {
                            // 回線グループ基本情報を取得する
                            // STEP20.0版対応　変更　START
                            let lineGoupsList: LineLineGroupsEntity[] = null;
                            try {
                                lineGoupsList = await this.linesGroupDao.getLineIds(lineGroupId);
                            } catch (error) {
                                if (isSQLException(error)) {
                                    // DBアクセスリトライエラーの場合
                                    handleCode = ResultCdConstants.CODE_080202;
                                }
                                throw error;
                            }
                            // STEP20.0版対応　変更　END
                            if (lineGoupsList == null || lineGoupsList.length === 0) {
                                // エラー処理の「■出力ログ内容編集」の項番4より編集し、ログ出力
                                this.warn(tenantId, sequenceNo, MsgKeysConstants.APGBIW0004);

                                // 変数「処理コード」に「080202」を設定する
                                handleCode = ResultCdConstants.CODE_080202;

                                // ヘッダ情報を設定する
                                output = this.setHeaderInfo(sequenceNo, handleCode, apiProcessId, isPrivate, receivedDate);
                                // 変数「処理コード」を判定し,ボディ情報（異常系）を設定する
                                this.setBodyInfo(output, lineInfos, handleCode, potalGroupPlanID, potalGroupPlanName, isPrivate, errorMessage, tenantId, sequenceNo);

                                this.debug(param.tenantId, param.requestHeader.sequenceNo, MsgKeysConstants.APGBID0002,
                                    this.getClassName(), "service");
                                // 返却値を返却し、処理終了
                                return output;

                            } else {
                                // linesGroupDao.getLineIds(lineGroupId)で取得した回線グループプラン情報を変数.「回線グループプラン情報lineInfos」にコピーする
                                for (const llg of lineGoupsList) {
                                    const line : LinesEntity = LinesEntity.build({
                                        lineId: llg.lineId
                                    });
                                    lineInfos.push(line);
                                }
                                // 卸ポータルグループプランIDと名称を取得する
                                // STEP20.0版対応　変更　START
                                try {
                                    lineGroupsEntity = await this.linesGroupDao.getPlanInfoFromLineLineGroups(lineGroupId);
                                } catch (error) {
                                    if (isSQLException(error)) {
                                        // DBアクセスリトライエラーの場合
                                        handleCode = ResultCdConstants.CODE_080202;
                                    }
                                    throw error;
                                }
                                // STEP20.0版対応　変更　END
                                if (lineGroupsEntity == null) {
                                    // 回線グループプラン情報を設定する（取得できない場合）
                                    potalGroupPlanID = "";
                                    potalGroupPlanName = "";
                                } else {
                                    // 回線グループプラン情報．プランID
                                    potalGroupPlanID = lineGroupsEntity.planId.toString();
                                    // 回線グループプラン情報．プラン名
                                    potalGroupPlanName = lineGroupsEntity.planName;
                                }
                                // ヘッダ情報を設定する
                                output = this.setHeaderInfo(sequenceNo, handleCode, apiProcessId, isPrivate, receivedDate);
                                // 変数「処理コード」を判定し,ボディ情報（異常系）を設定する
                                this.setBodyInfo(output, lineInfos, handleCode, potalGroupPlanID, potalGroupPlanName, isPrivate, errorMessage, tenantId, sequenceNo);

                                this.debug(param.tenantId, param.requestHeader.sequenceNo, MsgKeysConstants.APGBID0002,
                                    this.getClassName(), "service");
                                // 返却値を返却し、処理終了
                                return output;
                            }
                        }
                    }
                }
            } else {
                // エラー処理の「■出力ログ内容編集」の項番1より編集し、ログ出力
                const param0 = "回線グループID";
                const param1 = lineGroupId;
                this.warn(tenantId, sequenceNo, MsgKeysConstants.APGBIW0001, param0, param1);

                // 変数「処理コード」に「080101」を設定し、ロジック処理の処理２へ
                handleCode = ResultCdConstants.CODE_080101;

                /** STEP1.2a版対応　変更　START */
                // ヘッダ情報を設定する
                output = this.setHeaderInfo(sequenceNo, handleCode, apiProcessId, isPrivate, receivedDate);
                // 変数「処理コード」を判定し,ボディ情報（異常系）を設定する
                this.setBodyInfo(output, lineInfos, handleCode, potalGroupPlanID, potalGroupPlanName, isPrivate, errorMessage, tenantId, sequenceNo);
                this.debug(param.tenantId, param.requestHeader.sequenceNo,MsgKeysConstants.APGBID0002,
                    this.getClassName(), "service");
                // 返却値を返却し、処理終了
                return output;
            }
        } catch (error) {
            if (isSQLException(error)) {
                // DBアクセスリトライエラーの場合
                // リターンコード値は各エラー時にて設定済み
                super.error(error, tenantId, sequenceNo, MsgKeysConstants.APCOME0402, functionType);
                // ヘッダ情報を設定する
                output = this.setHeaderInfo(sequenceNo, handleCode, apiProcessId, isPrivate, receivedDate);
                // 変数「処理コード」を判定し,ボディ情報（異常系）を設定する
                this.setBodyInfo(output, lineInfos, handleCode, potalGroupPlanID, potalGroupPlanName, isPrivate, errorMessage,
                    tenantId, sequenceNo);
                return output;
                // STEP20.0版対応　追加　END
            } else {
                handleCode = ResultCdConstants.CODE_999999;
                super.error(error, param.tenantId, param.requestHeader.sequenceNo, MsgKeysConstants.APCOME0401, error.message);
                // ヘッダ情報を設定する
                output = this.setHeaderInfo(sequenceNo, handleCode, apiProcessId, isPrivate, receivedDate);
                // 変数「処理コード」を判定し,ボディ情報（異常系）を設定する
                this.setBodyInfo(output, lineInfos, handleCode, potalGroupPlanID, potalGroupPlanName, isPrivate, errorMessage, tenantId, sequenceNo);
                super.error(error, tenantId, sequenceNo, MsgKeysConstants.APCOME0401, error.message);
                return output;
            }
        }
    }

    /**
     * ヘッダ情報を設定。<BR>
     *
     * @param sequenceNo String
     * @param handleCode String
     * @param apiProcessId String
     * @param isPrivate 内部呼出フラグ
     * @param receivedDate REST API共通処理が呼び出されたシステム日付
     * @return LinesGroupInfoOutputDto
     */
    public setHeaderInfo(sequenceNo: string, handleCode: string, apiProcessId: string,
                          isPrivate: boolean, receivedDate: string): LinesGroupInfoOutputDto {
        const responseHeader: ResponseHeader = {
            sequenceNo,
            receivedDate,
            processCode: handleCode,
            apiProcessID: apiProcessId
        };
        if (isPrivate) {
            const outPut: LinesGroupInfoInsideOutputDto = {
                jsonBody: {
                    responseHeader,
                    potalGroupPlanID: "",
                    potalGroupPlanName: "",
                    lineList: [],
                    errorMessage: ""
                }
            };
            return outPut;
        } else {
            const outPut: LinesGroupInfoOutputDto = {
                jsonBody: {
                    responseHeader,
                    potalGroupPlanID: "",
                    potalGroupPlanName: "",
                    lineList: [],
                }
            };
            return outPut;
        }
    }

    /**
     * ボディ情報（異常系）を設定する。<BR>
     *
     * @param output LinesGroupInfoOutputDto
     * @param lineInfos List<LinesGroupInfoEntity>
     * @param handlingCode String
     * @param potalGroupPlanID 卸ポータルグループプランID
     * @param potalGroupPlanName 卸ポータルグループプラン名
     * @param isPrivate 内部呼出フラグ
     * @param errorMessage エラーメッセージ
     * @param tenantId テナントID
     * @param sequenceNo 送信番号
     */
    public setBodyInfo(output: LinesGroupInfoOutputDto, lineInfos: LinesEntity[], handlingCode: string,
                        potalGroupPlanID: string, potalGroupPlanName: string, isPrivate: boolean, errorMessage: string,
                        tenantId: string, sequenceNo: string) {
        // 内部呼出フラグ=trueの場合
        if (isPrivate) {
            // 回線グループの基本情報参照アウトプットDtoクラス(内部)に変換する
            const result = output as LinesGroupInfoInsideOutputDto;
            // 変数「処理コード」を判定する
            if (!(handlingCode.toLowerCase() === ResultCdConstants.CODE_000000.toLowerCase())) {
                // 卸ポータルグループプランID
                result.jsonBody.potalGroupPlanID = "";
                // 卸ポータルグループプラン名
                result.jsonBody.potalGroupPlanName = "";
                // 回線リスト.回線番号 「固定長:11」
                const list = new Array<LineList>();
                const lineList: LineList = {
                    lineNo: MvnoUtil.addSpaceAfterStr(null, 0)
                }
                list.push(lineList);
                result.jsonBody.lineList = list;
                if (StringUtils.isEmpty(errorMessage)) {
                    // エラーメッセージを設定する
                    result.jsonBody.errorMessage = this.getMessage(MsgKeysConstants.APCOMW0001, ["回線グループ基本情報参照"]);
                } else {
                    // エラーメッセージを設定する
                    result.jsonBody.errorMessage = errorMessage;
                }
            } else {
                // 卸ポータルグループプランID
                result.jsonBody.potalGroupPlanID = potalGroupPlanID;
                // 卸ポータルグループプラン名
                result.jsonBody.potalGroupPlanName = MvnoUtil.addSpaceAfterStr(potalGroupPlanName, 0);
                // 回線リスト.回線番号 「固定長:11」
                const list = new Array<LineList>();
                for (const linesInfo of lineInfos) {
                    const lineList = {
                        lineNo: linesInfo.lineId
                    }
                    list.push(lineList);
                }
                result.jsonBody.lineList = list;
                result.jsonBody.errorMessage = "";
            }
        } else {
            // 回線グループの基本情報参照アウトプットDtoクラス(外部)に変換する
            const result = output as LinesGroupInfoOutputDto;
            // 変数「処理コード」を判定する
            if (!(handlingCode.toLowerCase() === ResultCdConstants.CODE_000000.toLowerCase())) {
                // 卸ポータルグループプランID
                result.jsonBody.potalGroupPlanID = "";
                // 卸ポータルグループプラン名
                result.jsonBody.potalGroupPlanName = "";
                // 回線リスト.回線番号 「固定長:11」
                const list = new Array<LineList>();
                const lineList = {
                    lineNo: MvnoUtil.addSpaceAfterStr(null, 0)
                }
                list.push(lineList);
                result.jsonBody.lineList = list;
            } else {
                // 卸ポータルグループプランID
                result.jsonBody.potalGroupPlanID = potalGroupPlanID;
                // 卸ポータルグループプラン名
                result.jsonBody.potalGroupPlanName = MvnoUtil.addSpaceAfterStr(potalGroupPlanName, 0);
                // 回線リスト.回線番号 「固定長:11」
                const list = new Array<LineList>();
                for (const linesInfo of lineInfos) {
                    const lineList = {
                        lineNo: linesInfo.lineId
                    }
                    list.push(lineList);
                }
                result.jsonBody.lineList = list;
            }
        }
    }

    public generateDebugMessage(tenantId: string,
                                sequenceNo: string,
                                msgKey: string,
                                ...params: any[]): void
    {
        super.debug(tenantId, sequenceNo, msgKey, ...params);
        return;
    }

    public generateErrorMessage(tenantId: string,
                                sequenceNo: string,
                                msgKey: string,
                                ...params: any[]): void
    {
        super.error(tenantId, sequenceNo, msgKey, ...params);
        return;
    }
}