import { formatDate } from "date-fns";

import Constants from "@/core/constant/Constants";
import MsgKeysConstants from "@/core/constant/MsgKeysConstants";
import ResultCdConstants from "@/core/constant/ResultCdConstants";

import ApiCommon from "@/core/common/ApiCommon";
import MvnoUtil from "@/core/common/MvnoUtil";
import SOCommon from "@/core/common/SOCommon";
import TenantManage from "@/core/common/TenantManage";
import RESTCommon from "@/core/common/RESTCommon";

import APILinesDAO from "@/core/dao/APILinesDAO";
import OptionNwpassModifyInputDto from "@/core/dto/OptionNwpassModifyInputDto";
import OptionNwpassModifyOutputDto from "@/core/dto/OptionNwpassModifyOutputDto";
import SOObject from "@/core/dto/SOObject";
import ResponseHeader from "@/core/dto/ResponseHeader";

import { LinesEntity } from "@/core/entity/LinesEntity";

import { isSQLException } from "@/helpers/queryHelper";

import { IFService } from "../IFService";
import { getStringParameter } from "@/types/parameter.string";
import { CustomerInfoEntity } from "@/core/entity/CustomerInfoEntity";
import CheckUtil from "@/core/common/CheckUtil";

export default class OptionNwpassModifyService
    extends RESTCommon
    implements
        IFService<OptionNwpassModifyInputDto, OptionNwpassModifyOutputDto>
{
    /**
     * API回線DAO
     */
    // @Autowired
    // private APILinesDAO apiLinesDao;
    private apiLinesDao = new APILinesDAO(this.request, this.context);

    /**
     * SO管理共通DAO
     */
    // @Autowired
    // private SOCommon soCommon;
    private soCommon = new SOCommon(this.request, this.context);

    /**
     * テナント管理
     */
    // @Autowired
    // private TenantManage tenantManage;
    private tenantManage = new TenantManage(this.request, this.context);

    /**
     * API共通処理
     */
    // @Autowired
    // private ApiCommon apiCommon;
    private apiCommon = new ApiCommon(this.request, this.context);

    /**
     * CSV出力共通処理
     */
    // @Autowired
    // private RestCsvCommon restCsvCommon;

    /** 定数 Mapキー CSV1レコード目作成要否 */
    // private static final String KEY_CSV_OUT_KBN_1 = "csvOutputKbn1";
    /** 定数 Mapキー CSV2レコード目作成要否 */
    // private static final String KEY_CSV_OUT_KBN_2 = "csvOutputKbn2";
    /** 定数 Mapキー お客様基本情報 */
    // private static final String KEY_CUSTOMER_INFO = "customerInfo";

    public async service(
        param: OptionNwpassModifyInputDto,
    ): Promise<OptionNwpassModifyOutputDto> {
        super.debug(
            param.tenantId,
            param.requestHeader.sequenceNo,
            MsgKeysConstants.APONMD0001,
            this.getClassName(),
            "service",
            JSON.stringify(param),
        );

        /** 送信番号取得 */
        const sequenceNo = param.requestHeader.sequenceNo;
        /** 送信元システムID取得 */
        const senderSystemId = param.requestHeader.senderSystemId;
        /** API認証キー取得 */
        const apiKey = param.requestHeader.apiKey;
        /** 機能種別取得 */
        const functionType = param.requestHeader.functionType;

        /** テナントID */
        const tenantId = param.tenantId;
        /** 対象回線の所属テナントID */
        const targetTenantId = param.targetTenantId;
        /** 回線番号 */
        const lineNo = param.lineNo;
        /** NW暗証番号変更 */
        // const pinChange = param.pinChange;
        /** 国際ローミング利用限度額 */
        const intlRoaming = param.intlRoaming;
        /** 留守番でんわ */
        const voicemail = param.voicemail;
        /** キャッチホン */
        const callWaiting = param.callWaiting;
        /** 国際電話 */
        const intlCall = param.intlCall;
        /** 転送でんわ */
        const forwarding = param.forwarding;
        /** 国際着信転送 */
        const intlForwarding = param.intlForwarding;
        /** MVNO顧客CSV連携不要フラグ */
        const csvUnnecessaryFlag = getStringParameter(param.csvUnnecessaryFlag);

        const responseHeader = this.context.responseHeader;

        /**  変数「処理コード」初期化 */
        let handleCode = ResultCdConstants.CODE_000000;

        try {
            // 業務チェック
            // テナントIDの値をチェックし、リクエストが卸ポータルフロントからのものかを判断する（※卸ポータルフロントからの実行であることを示す固定値）
            if (!Constants.FRONT_API_TENANT_ID.equals(tenantId)) {
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APONMW0005,
                    tenantId,
                );
                responseHeader.processCode = ResultCdConstants.CODE_540101;
                return this.returnEdit(responseHeader);
            }

            // 対象の回線が既に廃止されていないかを確認する
            let aboneLineCheckResult: LinesEntity;
            try {
                aboneLineCheckResult =
                    await this.apiLinesDao.getLineInfoByLines(lineNo);
            } catch (e) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    handleCode = ResultCdConstants.CODE_540102;
                }
                throw e;
            }
            // 変数「対象回線廃止確認結果」より判定する
            if (
                aboneLineCheckResult == null ||
                "03".equals(aboneLineCheckResult.lineStatus)
            ) {
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APONMW0001,
                    lineNo,
                );
                responseHeader.processCode = ResultCdConstants.CODE_540102;
                return this.returnEdit(responseHeader);
            }

            // 回線IDと対象回線の所属テナントIDの関係をチェックする
            let checkResult: [boolean, string] = [false, ""];
            try {
                checkResult = await this.tenantManage.doCheck(
                    lineNo,
                    targetTenantId,
                );
            } catch (e) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    handleCode = ResultCdConstants.CODE_540103;
                }
                throw e;
            }

            if (!checkResult[0]) {
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APONMW0002,
                    lineNo,
                    targetTenantId,
                );
                responseHeader.processCode = ResultCdConstants.CODE_540103;
                return this.returnEdit(responseHeader);
            }

            // 対象回線の廃止オーダが受付中でないかチェックする
            const targetDate = formatDate(
                MvnoUtil.convDateFormat(responseHeader.receivedDate),
                "yyyyMMdd",
            );

            let checkAbolishSoResult: boolean = false;
            try {
                checkAbolishSoResult = await this.apiCommon.checkAbolishSo(
                    lineNo,
                    targetDate,
                );
            } catch (e) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    handleCode = ResultCdConstants.CODE_540104;
                }
                throw e;
            }

            // 変数「対象回線廃止オーダチェック結果」より判定する
            if (!checkAbolishSoResult) {
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APONMW0003,
                    lineNo,
                );
                responseHeader.processCode = ResultCdConstants.CODE_540104;
                return this.returnEdit(responseHeader);
            }

            // ロジック処理(即時オーダ)
            // 回線情報の更新

            // 変数「回線オプション追加情報」
            const lineOptionAddInfo = new LinesEntity();
            // 変数「回線オプション更新要否」
            const lineOptionUpdateKubn: Map<string, boolean> = new Map<
                string,
                boolean
            >();

            // 国際ローミング利用限度額
            if (
                "1".equals(intlRoaming.changeCode) ||
                "2".equals(intlRoaming.changeCode)
            ) {
                lineOptionAddInfo.roamingMaxId = intlRoaming.afterOpId;
                lineOptionUpdateKubn.set("intlRoaming", true);
            } else if ("3".equals(intlRoaming.changeCode)) {
                lineOptionAddInfo.roamingMaxId = null;
                lineOptionUpdateKubn.set("intlRoaming", true);
            } else {
                lineOptionAddInfo.roamingMaxId = null;
                lineOptionUpdateKubn.set("intlRoaming", false);
            }

            // 留守番でんわ
            if (
                "1".equals(voicemail.changeCode) ||
                "2".equals(voicemail.changeCode)
            ) {
                lineOptionAddInfo.voiceMailId = voicemail.afterOpId;
                lineOptionUpdateKubn.set("voicemail", true);
            } else if ("3".equals(voicemail.changeCode)) {
                lineOptionAddInfo.voiceMailId = null;
                lineOptionUpdateKubn.set("voicemail", true);
            } else {
                lineOptionAddInfo.voiceMailId = null;
                lineOptionUpdateKubn.set("voicemail", false);
            }

            // キャッチホン
            if (
                "1".equals(callWaiting.changeCode) ||
                "2".equals(callWaiting.changeCode)
            ) {
                lineOptionAddInfo.callWaitingId = callWaiting.afterOpId;
                lineOptionUpdateKubn.set("callWaiting", true);
            } else if ("3".equals(callWaiting.changeCode)) {
                lineOptionAddInfo.callWaitingId = null;
                lineOptionUpdateKubn.set("callWaiting", true);
            } else {
                lineOptionAddInfo.callWaitingId = null;
                lineOptionUpdateKubn.set("callWaiting", false);
            }

            // 国際電話
            if (
                "1".equals(intlCall.changeCode) ||
                "2".equals(intlCall.changeCode)
            ) {
                lineOptionAddInfo.intlCallId = intlCall.afterOpId;
                lineOptionUpdateKubn.set("intlCall", true);
            } else if ("3".equals(intlCall.changeCode)) {
                lineOptionAddInfo.intlCallId = null;
                lineOptionUpdateKubn.set("intlCall", true);
            } else {
                lineOptionAddInfo.intlCallId = null;
                lineOptionUpdateKubn.set("intlCall", false);
            }

            // 転送でんわ
            if (
                "1".equals(forwarding.changeCode) ||
                "2".equals(forwarding.changeCode)
            ) {
                lineOptionAddInfo.forwardingId = forwarding.afterOpId;
                lineOptionUpdateKubn.set("forwarding", true);
            } else if ("3".equals(forwarding.changeCode)) {
                lineOptionAddInfo.forwardingId = null;
                lineOptionUpdateKubn.set("forwarding", true);
            } else {
                lineOptionAddInfo.forwardingId = null;
                lineOptionUpdateKubn.set("forwarding", false);
            }

            // 国際着信転送
            if (
                "1".equals(intlForwarding.changeCode) ||
                "2".equals(intlForwarding.changeCode)
            ) {
                lineOptionAddInfo.intlForwardingId = intlForwarding.afterOpId;
                lineOptionUpdateKubn.set("intlForwarding", true);
            } else if ("3".equals(intlForwarding.changeCode)) {
                lineOptionAddInfo.intlForwardingId = null;
                lineOptionUpdateKubn.set("intlForwarding", true);
            } else {
                lineOptionAddInfo.intlForwardingId = null;
                lineOptionUpdateKubn.set("intlForwarding", false);
            }
            // 更新日時
            lineOptionAddInfo.updatedAt = new Date(
                MvnoUtil.convDateFormat(responseHeader.receivedDate),
            );

            try {
                // 回線情報登録
                await this.apiLinesDao.updateLineOptionInfo(
                    lineNo,
                    lineOptionAddInfo,
                    lineOptionUpdateKubn,
                );
            } catch (e: any) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    handleCode = ResultCdConstants.CODE_540201;
                    throw e;
                }
                super.warn(
                    e,
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APONMW0004,
                    lineNo,
                );
                responseHeader.processCode = ResultCdConstants.CODE_540201;
                return this.returnEdit(responseHeader);
            }

            // CSVファイルデータ収集
            let csvOutputKbn: boolean;
            if (csvUnnecessaryFlag === "1") {
                csvOutputKbn = false;
            } else {
                csvOutputKbn = await this.checkCsvOutput(param, checkResult);
            }

            // ディバッグログ出力
            super.debug(
                param.tenantId,
                param.requestHeader.sequenceNo,
                MsgKeysConstants.APONMD0002,
                this.getClassName(),
                "service",
            );
            responseHeader.processCode = ResultCdConstants.CODE_000000;
            return this.returnEdit(
                responseHeader,
                checkResult[1],
                !csvOutputKbn,
            );
        } catch (e: any) {
            if (isSQLException(e)) {
                // DBアクセスリトライエラーの場合
                // リターンコード値は各エラー時にて設定済み
                super.error(
                    e,
                    param.tenantId,
                    param.requestHeader.sequenceNo,
                    MsgKeysConstants.APCOME0402,
                    functionType,
                );
                responseHeader.processCode = handleCode;
                return this.returnEdit(responseHeader);
            }
            super.error(
                e,
                param.tenantId,
                param.requestHeader.sequenceNo,
                MsgKeysConstants.APCOME0401,
                e,
            );
            responseHeader.processCode = ResultCdConstants.CODE_999999;
            return this.returnEdit(responseHeader);
        } finally {
            await this.soManagementCommon(
                param,
                responseHeader.apiProcessID,
                null,
                null,
                responseHeader.processCode,
                responseHeader.receivedDate,
                null,
                null,
            );
        }
    }

    private returnEdit(
        responseHeader: ResponseHeader,
        nNo: string = null,
        csvUnnecessary: boolean = true,
    ): OptionNwpassModifyOutputDto {
        return {
            jsonBody: {
                responseHeader,
            },
            additionalData: {
                nNo: nNo || "",
                csvUnnecessary,
            },
        };
    }

    /**
     * SO管理共通パラメータ設定
     *
     * @param param パラメータ
     * @param apiId API処理ID (changed during refactoring)
     * @param executeUserId 実行ユーザID
     * @param executeTenantId 実行テナントID
     * @param code オーダステータス
     * @param receivedDate REST API共通処理が呼び出されたシステム日付
     * @param orderType オーダ種別
     * @param reserveDate 受信日時
     * @throws Exception 例外
     */
    private async soManagementCommon(
        param: OptionNwpassModifyInputDto,
        apiIp: string,
        executeUserId: string,
        executeTenantId: string,
        code: string,
        receivedDate: string,
        orderType: string,
        reserveDate: string,
    ) {
        // SO管理共通を呼び出す
        const soObject = new SOObject();
        // サービスオーダID
        soObject.setServiceOrderId(apiIp);
        // 投入日
        soObject.setOrderDate(receivedDate);
        // 予約日
        soObject.setReserveDate(null);
        // オーダ種別
        soObject.setOrderType(null);
        // 完了日
        soObject.setExecDate(MvnoUtil.getDateTimeNow());
        // オーダステータス
        soObject.setOrderStatus(code);
        // 回線ID
        soObject.setLineId(param.lineNo);
        // 回線グループID
        soObject.setLineGroupId("");
        // 所属テナントID
        soObject.setTenantId(param.targetTenantId);
        // 実行ユーザID
        soObject.setExecuteUserId(null);
        // 実行テナントID
        soObject.setExecuteTenantId(null);
        // 機能種別
        soObject.setFunctionType(param.requestHeader.functionType);
        // 操作区分
        soObject.setOperationDivision("");
        // 変更前プランID
        soObject.setChangeOldplanId("");
        // 変更後プランID
        soObject.setChangeNewplanId("");
        // 回線所属変更回線ID
        soObject.setChangePlanId("");
        // 基本容量
        soObject.setBasicCapacity("");
        // オプションプランID
        soObject.setOptionPlanId("");
        // REST電文
        soObject.setRestMessage(null);

        await this.soCommon.soCommon(soObject);
    }

    /**
     *
     * @param param
     * @param checkResult
     * @returns KEY_CSV_OUT_KBN_1 or KEY_CSV_OUT_KBN_2 either is "0" then false otherwise true
     */
    private async checkCsvOutput(
        param: OptionNwpassModifyInputDto,
        checkResult: [boolean, string],
    ): Promise<boolean> {
        const lineNo = param.lineNo;
        const pinChange = param.pinChange;
        const tenantId = param.tenantId;
        const targetTenantId = param.targetTenantId;
        const sequenceNo = param.requestHeader.sequenceNo;
        // const intlRoaming = param.intlRoaming;
        // const voicemail = param.voicemail;
        // const callWaiting = param.callWaiting;
        // const intlCall = param.intlCall;
        // const forwarding = param.forwarding;
        // const intlForwarding = param.intlForwarding;

        let customerInfo: CustomerInfoEntity;
        try {
            customerInfo = await this.apiLinesDao.getLineCustomerInfo(lineNo);
        } catch (e) {
            if (isSQLException(e)) {
                // DBアクセスリトライエラーの場合
                super.error(
                    e,
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APCOME0402,
                    param.requestHeader.functionType,
                );
                // result.put(KEY_CSV_OUT_KBN_1, "0");
                // result.put(KEY_CSV_OUT_KBN_2, "0");
                return false;
            }
            throw e;
        }

        if (customerInfo === null) {
            super.error(
                tenantId,
                sequenceNo,
                MsgKeysConstants.APONME0001,
                lineNo,
                pinChange,
            );
            // result.put(KEY_CSV_OUT_KBN_1, "0");
            // result.put(KEY_CSV_OUT_KBN_2, "0");
            return false;
        } else if (CheckUtil.checkIsNotNull(customerInfo.nwOptionIdT)) {
            if (pinChange === "1") {
                super.error(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APONME0001,
                    lineNo,
                    pinChange,
                );
                // result.put(KEY_CSV_OUT_KBN_1, "0");
                // result.put(KEY_CSV_OUT_KBN_2, "0");
                return false;
            }
        }
        // N番の有無を判定する
        if (checkResult[1] === null || checkResult[1] === "") {
            super.error(
                tenantId,
                sequenceNo,
                MsgKeysConstants.APONME0002,
                lineNo,
                targetTenantId,
            );
            // result.put(KEY_CSV_OUT_KBN_1, "0");
            // result.put(KEY_CSV_OUT_KBN_2, "0");
            return false;
        }
        // NOTE this part seems not needed
        // レコード単位のCSV出力要否を判定する。
        // if ("2".equals(intlRoaming.getChangeCode())
        // 		|| "2".equals(voicemail.getChangeCode())
        // 		|| "2".equals(callWaiting.getChangeCode())
        // 		|| "2".equals(intlCall.getChangeCode())
        // 		|| "2".equals(forwarding.getChangeCode())
        // 		|| "2".equals(intlForwarding.getChangeCode())) {
        // 	result.put(KEY_CSV_OUT_KBN_1, "1");
        // 	result.put(KEY_CSV_OUT_KBN_2, "1");
        // } else {
        // 	result.put(KEY_CSV_OUT_KBN_1, "1");
        // 	result.put(KEY_CSV_OUT_KBN_2, "0");
        // }
        return true;
    }
}
