import "@/types/string.extension";

import { formatDate, fromUnixTime } from "date-fns";

import Constants from "@/core/constant/Constants";
import ResultCdConstants from "@/core/constant/ResultCdConstants";

import ApiCommon from "@/core/common/ApiCommon";
import CheckUtil from "@/core/common/CheckUtil";
import MvnoUtil from "@/core/common/MvnoUtil";
import RESTCommon from "@/core/common/RESTCommon";
import SOCommon from "@/core/common/SOCommon";
import TenantManage from "@/core/common/TenantManage";

import APILinesDAO from "@/core/dao/APILinesDAO";
import LineEnableInputDto from "@/core/dto/LineEnableInputDto";
import LineEnableOutputDto from "@/core/dto/LineEnableOutputDto";
import SOObject from "@/core/dto/SOObject";
import MsgKeysConstants from "@/core/constant/MsgKeysConstants";
import ResponseHeader from "@/core/dto/ResponseHeader";

import { LinesEntity } from "@/core/entity/LinesEntity";

import { isSQLException } from "@/helpers/queryHelper";

import { IFService } from "../IFService";
import { getStringParameter } from "@/types/parameter.string";

export default class LineEnableService
    extends RESTCommon
    implements IFService<LineEnableInputDto, LineEnableOutputDto>
{
    /**
     * API回線DAO
     */
    private apiLinesDao = new APILinesDAO(this.request, this.context);

    /**
     * SO管理共通DAO
     */
    private soCommon = new SOCommon(this.request, this.context);

    /**
     * テナント管理
     */
    private tenantManage = new TenantManage(this.request, this.context);

    /**
     * API共通処理
     */
    private apiCommon = new ApiCommon(this.request, this.context);

    /**
     * 回線黒化機能。<BR>
     *
     * <PRE>
     * SIMのステータス変更を卸ポータルフロントにて受付け、卸ポータルフロントでの処理(ALADIN工事を含む)が完了した後、
     * MVNO顧客にSIMステータス変更情報を連携する
     * </PRE>
     *
     * @param param 回線黒化インプット
     * @param clientIPAddress クライアントIP // NOTE added during refactoring
     * @// param isPrivate 内部呼出フラグ
     * @param params 可変長さ引数(内部から呼出す時に使用するパラメータ)
     * @return LineEnableOutputDto 回線黒化機能アウトプット
     */
    public async service(
        param: LineEnableInputDto,
        clientIPAddress: string,
        ...obj: any[]
    ): Promise<LineEnableOutputDto> {
        super.debug(
            param.tenantId,
            param.requestHeader.sequenceNo,
            MsgKeysConstants.APLEND0001,
            this.getClassName(),
            "service",
            JSON.stringify(param),
        );

        /** 送信番号取得 */
        const sequenceNo = param.requestHeader.sequenceNo;
        /** 送信元システムID取得 */
        const senderSystemId = param.requestHeader.senderSystemId;
        /** API認証キー取得 */
        const apiKey = param.requestHeader.apiKey;
        /** 機能種別取得 */
        const functionType = param.requestHeader.functionType;

        /** テナントID */
        const tenantId = param.tenantId;
        /** 対象回線の所属テナントID */
        const targetTenantId = param.targetTenantId;
        /** 回線番号 */
        const lineNo = param.lineNo;
        /** 課金開始日 */
        const chargeDate = param.chargeDate;
        /** MVNO顧客CSV連携不要フラグ */
        const csvUnnecessaryFlag = getStringParameter(param.csvUnnecessaryFlag);
        /** 同一MVNE転入フラグ */
        let sameMvneInFlag = param.sameMvneInFlag;

        /** CSV作成要否 */
        let csvOutputKbn: string = null;
        /** ファイル名通番 */
        // let fileNameNo: string = null;
        /** CSVファイル項目数 */
        // final int csvColNum = 10;
        /** CSVファイル出力データ */
        // let csvData: string[] = [];

        /** API処理Id */
        const apiId = this.context.responseHeader.apiProcessID;
        /** 実行ユーザID (null) */
        const executeUserId: string = null;
        /** 実行テナントID (null) */
        const executeTenantId: string = null;
        /** REST API共通処理が呼び出されたシステム日付 */
        const systemDateTime = MvnoUtil.getDateTimeNow();

        /** 返却レスポンスヘッダ生成 */
        const responseHeader = this.context.responseHeader;
        // responseHeader.processCode = ResultCdConstants.CODE_000000;

        /** 変数「処理コード」初期化 */
        let handleCode = ResultCdConstants.CODE_000000;

        try {
            // 同一MVNE転入フラグのチェック
            if ("1".equals(sameMvneInFlag)) {
                sameMvneInFlag = "1";
            } else {
                sameMvneInFlag = "0";
            }

            // 業務チェック
            // テナントIDの値をチェックし、リクエストが卸ポータルフロントからのものかを判断する
            if (!Constants.FRONT_API_TENANT_ID.equals(tenantId)) {
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APLENW0005,
                    tenantId,
                );
                responseHeader.processCode = ResultCdConstants.CODE_550101;
                return this.resultEdit(responseHeader);
            }

            // 対象の回線が既に廃止されていないかを確認する
            let aboneLineCheckResult: LinesEntity = null;
            try {
                aboneLineCheckResult =
                    await this.apiLinesDao.getLineInfoByLines(lineNo);
            } catch (e) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    handleCode = ResultCdConstants.CODE_550102;
                }
                throw e;
            }

            // 変数「対象回線廃止確認結果」より判定する
            if (
                aboneLineCheckResult === null ||
                "03".equals(aboneLineCheckResult.lineStatus)
            ) {
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APLENW0001,
                    lineNo,
                );
                responseHeader.processCode = ResultCdConstants.CODE_550102;
                return this.resultEdit(responseHeader);
            }

            // 回線IDと対象回線の所属テナントIDの関係をチェックする
            // STEP20.0版対応　変更　START
            let checkResult: [boolean, string] = null;
            try {
                checkResult = await this.tenantManage.doCheck(
                    lineNo,
                    targetTenantId,
                );
            } catch (e) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    handleCode = ResultCdConstants.CODE_550103;
                }
                throw e;
            }
            // STEP20.0版対応　変更　END
            // 変数「関係チェック結果」より判定する
            if (!checkResult[0]) {
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APLENW0002,
                    lineNo,
                    targetTenantId,
                );
                responseHeader.processCode = ResultCdConstants.CODE_550103;
                return this.resultEdit(responseHeader);
            }

            // 対象回線の廃止オーダが受付中でないかチェックする
            // SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
            // String targetDate = dateFormat.format(MvnoUtil.convDateFormat(systemDateTime));
            const targetDate = formatDate(
                MvnoUtil.convDateFormat(systemDateTime),
                "yyyyMMdd",
            );

            let checkAbolishSoResult: boolean = null;
            try {
                checkAbolishSoResult = await this.apiCommon.checkAbolishSo(
                    lineNo,
                    targetDate,
                );
            } catch (e) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    handleCode = ResultCdConstants.CODE_550104;
                }
                throw e;
            }

            // 変数「対象回線廃止オーダチェック結果」より判定する
            if (!checkAbolishSoResult) {
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APLENW0003,
                    lineNo,
                );
                responseHeader.processCode = ResultCdConstants.CODE_550104;
                return this.resultEdit(responseHeader);
            }

            // ロジック処理(即時オーダ)
            // 黒化日の設定
            let enableDate: string = null;

            if (CheckUtil.checkIsNotNull(chargeDate)) {
                enableDate = systemDateTime.substring(0, 10);
            } else {
                enableDate = chargeDate;
            }

            // 半黒フラグ
            const simFlag = false;
            try {
                await this.apiLinesDao.updateLineKurokaInfo(
                    lineNo,
                    simFlag,
                    fromUnixTime(
                        MvnoUtil.convDateFormat(enableDate + " 00:00:00") /
                            1000,
                    ),
                    fromUnixTime(
                        MvnoUtil.convDateFormat(systemDateTime) / 1000,
                    ),
                );
            } catch (e) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    handleCode = ResultCdConstants.CODE_550201;
                    throw e;
                }
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APLENW0004,
                    lineNo,
                );
                responseHeader.processCode = ResultCdConstants.CODE_550201;
                return this.resultEdit(responseHeader);
            }

            // CSVファイル用データ収集
            if ("1".equals(csvUnnecessaryFlag)) {
                csvOutputKbn = "0";
            } else {
                // N番の有無を判定する
                if (checkResult[1] === null || checkResult[1] === "") {
                    super.error(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APLENE0001,
                        lineNo,
                        targetTenantId,
                    );
                    csvOutputKbn = "0";
                } else {
                    csvOutputKbn = "1";
                }
            }

            // CSVファイル用データ取得
            // if ("1".equals(csvOutputKbn)) {
            //     if (apiId.length > 12) {
            //         fileNameNo = apiId.substring(apiId.length - 12);
            //     } else {
            //         fileNameNo = apiId;
            //     }
            //     csvData = this.csvDataEdit(
            //         this.context.responseHeader.apiProcessID,
            //         param,
            //         checkResult,
            //         enableDate,
            //         sameMvneInFlag,
            //     );
            // }

            // NOTE added during refactoring
            // get current deviceTypeId(T番) for CoreSwimmy連携
            let deviceTypeIdT: string = null;
            if ("1".equals(csvOutputKbn)) {
                try {
                    const cardTypeInfo = await this.apiLinesDao.getCardTypeInfo(
                        aboneLineCheckResult.deviceTypeId,
                    );
                    if (cardTypeInfo !== null) {
                        deviceTypeIdT = cardTypeInfo.deviceTypeIdT;
                    }
                } catch (e) {
                    this.context.error("Failed to get plan info", e);
                    // if couldn't find card info, we can't do CoreSwimmy連携 later
                    handleCode = ResultCdConstants.CODE_999999; // may be a SQL error
                    throw e;
                }
                if (deviceTypeIdT === null) {
                    // NOTE 商用では「カード種別ID（T番）がNULL」のプランでSwimmyに連携しようとすることはない
                    this.context.error(
                        "Card type info not found for:",
                        aboneLineCheckResult.deviceTypeId,
                    );
                    throw new Error("Card type info not found");
                }
            }

            // ディバッグログ出力
            super.debug(
                param.tenantId,
                param.requestHeader.sequenceNo,
                MsgKeysConstants.APLEND0002,
                this.getClassName(),
                "service",
            );
            responseHeader.processCode = ResultCdConstants.CODE_000000;
            // get N番 for additionalData
            const nNo = checkResult[1];
            return this.resultEdit(
                responseHeader,
                nNo,
                csvOutputKbn === "0",
                deviceTypeIdT,
                enableDate.replace(/\//g, ""), // yyyyMMdd
            );
        } catch (e) {
            if (isSQLException(e)) {
                // DBアクセスリトライエラーの場合
                // リターンコード値は各エラー時にて設定済み
                super.error(
                    e as Error,
                    param.tenantId,
                    param.requestHeader.sequenceNo,
                    MsgKeysConstants.APCOME0402,
                    functionType,
                );
                responseHeader.processCode = handleCode;
                return this.resultEdit(responseHeader);
            } else {
                super.error(
                    e as Error,
                    param.tenantId,
                    param.requestHeader.sequenceNo,
                    MsgKeysConstants.APCOME0401,
                    e?.message,
                );
                responseHeader.processCode = ResultCdConstants.CODE_999999;
                return this.resultEdit(responseHeader);
            }
        } finally {
            await this.soManagementCommon(
                param,
                responseHeader.apiProcessID,
                executeUserId,
                executeTenantId,
                responseHeader.processCode,
                systemDateTime,
                null,
                null,
            );
        }
    }

    /**
     * 回線黒化機能返却値編集
     *
     * @param responseHeader レスポンスヘッダ
     * @param nNo N番 (required for OK response)
     * @param csvUnnecessaryFlag CSV不要フラグ (required for OK response)
     * @param deviceTypeIdT カード種別に対するT番 (required for OK response)
     * @param enableDate 電文中の課金開始日 (required for OK response)
     * @return 返却値オブジェクト
     */
    private resultEdit(
        responseHeader: ResponseHeader,
        nNo: string = null,
        csvUnnecessaryFlag: boolean = null,
        deviceTypeIdT: string = null,
        enableDate: string = null,
    ): LineEnableOutputDto {
        // 出力情報を作成する
        return {
            jsonBody: {
                responseHeader,
            },
            additionalData: {
                nNo,
                csvUnnecessary: csvUnnecessaryFlag,
                deviceTypeIdT,
                enableDate,
            },
        };
    }

    /**
     * SO管理共通パラメータ設定
     *
     * @param param パラメータ
     * @param apiProcessID (resultBean) チェック結果
     * @param executeUserId 実行ユーザID
     * @param executeTenantId 実行テナントID
     * @param code オーダステータス
     * @param receivedDate REST API共通処理が呼び出されたシステム日付
     * @param orderType オーダ種別
     * @param reserveDate 受信日時
     * @throws Exception 例外
     */
    private async soManagementCommon(
        param: LineEnableInputDto,
        apiProcessID: string,
        executeUserId: string,
        executeTenantId: string,
        code: string,
        receivedDate: string,
        orderType: string,
        reserveDate: string,
    ): Promise<void> {
        // SO管理共通を呼び出す
        const soObject = new SOObject();

        // サービスオーダID
        soObject.setServiceOrderId(apiProcessID);
        // 投入日
        soObject.setOrderDate(receivedDate);
        // 予約日
        soObject.setReserveDate(null);
        // オーダ種別
        soObject.setOrderType(null);
        // 完了日
        soObject.setExecDate(MvnoUtil.getDateTimeNow());
        // オーダステータス
        soObject.setOrderStatus(code);
        // 回線ID
        soObject.setLineId(param.lineNo);
        // 回線グループID
        soObject.setLineGroupId("");
        // 所属テナントID
        soObject.setTenantId(param.targetTenantId);
        // 実行ユーザID
        soObject.setExecuteUserId(executeUserId);
        // 実行テナントID
        soObject.setExecuteTenantId(executeTenantId);
        // 機能種別
        soObject.setFunctionType(param.requestHeader.functionType);
        // 操作区分
        soObject.setOperationDivision("");
        // 変更前プランID
        soObject.setChangeOldplanId("");
        // 変更後プランID
        soObject.setChangeNewplanId("");
        // 回線所属変更回線ID
        soObject.setChangePlanId("");
        // 基本容量
        soObject.setBasicCapacity("");
        // オプションプランID
        soObject.setOptionPlanId("");
        // REST電文
        soObject.setRestMessage(null);

        await this.soCommon.soCommon(soObject);
    }

    // private csvDataEdit(
    //     apiId: string,
    //     param: LineEnableInputDto,
    //     checkResult: [boolean, string],
    //     enableDate: string,
    //     sameMvneInFlag: string,
    // ): string[] {
    //     return [];
    // }
}
