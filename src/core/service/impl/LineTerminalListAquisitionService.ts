import MsgKeysConstants from "@/core/constant/MsgKeysConstants";
import ResultCdConstants from "@/core/constant/ResultCdConstants";

import CheckUtil from "@/core/common/CheckUtil";
import MvnoUtil from "@/core/common/MvnoUtil";
import RESTCommon from "@/core/common/RESTCommon";
import StringUtils from "@/core/common/StringUtils";

import APICommonDAO from "@/core/dao/APICommonDAO";
import APILinesDAO from "@/core/dao/APILinesDAO";
import LineTerminalListAcquisitionInputDto from "@/core/dto/LineTerminalListAcquisitionInputDto";
import LineTerminalListAcquisitionOutputDto from "@/core/dto/LineTerminalListAcquisitionOutputDto";
import ResponseHeader from "@/core/dto/ResponseHeader";

import { isSQLException } from "@/helpers/queryHelper";
import { IFService } from "../IFService";
import { TenantsEntity } from "@/core/entity/TenantsEntity";
import { TenantNnumbersEntity } from "@/core/entity/TenantNnumbersEntity";

export default class LineTerminalListAquisitionService
    extends RESTCommon
    implements
        IFService<
            LineTerminalListAcquisitionInputDto,
            LineTerminalListAcquisitionOutputDto
        >
{
    /**
     * API共通DAO
     */
    // @Autowired
    // private APICommonDAO apiCommonDao;
    private apiCommonDao = new APICommonDAO(this.request, this.context);

    /**
     * API回線DAO
     */
    // @Autowired
    // private APILinesDAO apiLinesDao;
    private apiLinesDao = new APILinesDAO(this.request, this.context);

    public async service(
        param: LineTerminalListAcquisitionInputDto,
    ): Promise<LineTerminalListAcquisitionOutputDto> {
        // REST API共通処理が呼び出されたシステム日付
        const receivedDate = this.context.responseHeader.receivedDate;
        // 返却値
        /** STEP1.2a版対応　変更　START */
        let output = null as LineTerminalListAcquisitionOutputDto;
        /** STEP1.2a版対応　変更　END */
        // 「処理コード」初期化
        let handleCode = ResultCdConstants.CODE_000000;

        // 送信番号
        const sequenceNo = param.requestHeader.sequenceNo;
        // 送信元システムID
        const sendSystemId = param.requestHeader.senderSystemId;
        // API認証キー
        const apiKey = param.requestHeader.apiKey;
        // 機能種別
        const functionType = param.requestHeader.functionType;
        // テナントID
        const tenantId = param.tenantId;
        // 代表N番
        const nBan = param.nBan;
        // API処理ID
        const apiProcessId = this.context.responseHeader.apiProcessID;
        // 回線番号リストを取得する
        /** STEP3.0版対応　変更　START */
        let lineNoList: string[] = [];
        /** STEP3.0版対応　変更　END */
        /** STEP1.2a版対応　追加　START */
        // 変数「エラーメッセージ」を定義する
        const errorMessage = null;
        /** STEP1.2a版対応　追加　END */

        super.debug(
            tenantId,
            sequenceNo,
            MsgKeysConstants.APLLCD0001,
            this.getClassName(),
            "service",
            "LineTerminalListAcquisitionInputDto [" +
                "sequenceNo=" +
                sequenceNo +
                ", senderSystemId=" +
                sendSystemId +
                ", apiKey=" +
                apiKey +
                ", functionType=" +
                functionType +
                ", tenantId=" +
                tenantId,
            ", nBan=" + nBan + "]",
        );

        try {
            /*------------- 業務チェック処理------------------ */
            // 代表N番相関チェック
            if (
                !CheckUtil.checkIsNotNull(nBan) &&
                !this.checkNBanFormat(nBan)
            ) {
                // ①エラー処理の「■出力ログ内容編集」の項番1より編集し、ログ出力
                const param0 = "代表N番";
                const param1 = nBan;
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APLLCW0001,
                    param0,
                    param1,
                );

                // ②変数「処理コード」に「070101」を設定し、ロジック処理の処理２へ
                const handleCode = ResultCdConstants.CODE_070101;
                /** STEP1.2a版対応　変更　START */
                // ヘッダ情報を設定する
                output = this.setHeaderInfo(
                    sequenceNo,
                    handleCode,
                    apiProcessId,
                    errorMessage,
                    receivedDate,
                );
                // ボディ情報を設定する
                this.setBodyInfo(output, lineNoList, handleCode, errorMessage);
                /** STEP1.2a版対応　変更　END */
                /** STEP5.0対応　削除　START */
                //                        // 同時接続数減算
                //                        /* #493対応 MOD START */
                //                        super.modConCount();
                //                        /* #493対応 MOD END */
                /** STEP5.0対応　削除　END */
                // 返却値を返却し、処理終了
                super.debug(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APLLCD0002,
                    this.getClassName(),
                    "service",
                );
                return output;
            } else {
                /*---------------- ロジック処理-------------------*/
                /** #162対応　追加　START */
                // 代表N番指定判定
                // 入力パラメータ．「代表N番」はNULL以外である場合
                if (StringUtils.isNotEmpty(nBan)) {
                    // STEP20.0版対応　変更　START
                    let tenantNnumbersEntity: TenantNnumbersEntity;
                    try {
                        tenantNnumbersEntity =
                            await this.apiLinesDao.getTenantNnumbers(
                                tenantId,
                                nBan,
                            );
                    } catch (e: any) {
                        // DBアクセスリトライエラーの場合
                        if (isSQLException(e)) {
                            handleCode = ResultCdConstants.CODE_070201;
                        }
                        throw e;
                    }
                    // STEP20.0版対応　変更　END
                    if (tenantNnumbersEntity == null) {
                        // ①エラー処理の「■出力ログ内容編集」の項番2より編集し、ログ出力
                        super.warn(
                            tenantId,
                            sequenceNo,
                            MsgKeysConstants.APLLCW0002,
                        );
                        // ②変数「処理コード」に「070201」を設定し、処理２へ
                        const handleCode = ResultCdConstants.CODE_070201;
                        // ヘッダ情報を設定する
                        output = this.setHeaderInfo(
                            sequenceNo,
                            handleCode,
                            apiProcessId,
                            errorMessage,
                            receivedDate,
                        );
                        // ボディ情報を設定する
                        this.setBodyInfo(
                            output,
                            lineNoList,
                            handleCode,
                            errorMessage,
                        );
                        /** STEP5.0対応　削除　START */
                        //                            // 同時接続数減算
                        //                            /* #493対応 MOD START */
                        //                            super.modConCount();
                        //                            /* #493対応 MOD END */
                        /** STEP5.0対応　削除　END */
                        super.debug(
                            tenantId,
                            sequenceNo,
                            MsgKeysConstants.APLLCD0002,
                            this.getClassName(),
                            "service",
                        );
                        // 返却値を返却し、処理終了
                        return output;
                    } else {
                        // 回線番号リストを取得する
                        // STEP20.0版対応　変更　START
                        try {
                            lineNoList = await this.apiLinesDao.getLineIDList1(
                                nBan,
                            );
                        } catch (e: any) {
                            // DBアクセスリトライエラーの場合
                            if (isSQLException(e)) {
                                handleCode = ResultCdConstants.CODE_070201;
                            }
                            throw e;
                        }

                        // STEP20.0版対応　変更　END
                        // ヘッダ情報を設定する
                        output = this.setHeaderInfo(
                            sequenceNo,
                            handleCode,
                            apiProcessId,
                            errorMessage,
                            receivedDate,
                        );
                        // ボディ情報を設定する
                        this.setBodyInfo(
                            output,
                            lineNoList,
                            handleCode,
                            errorMessage,
                        );
                        /** STEP5.0対応　削除　START */
                        //                            // 同時接続数減算
                        //                            /* #493対応 MOD START */
                        //                            super.modConCount();
                        //                            /* #493対応 MOD END */
                        /** STEP5.0対応　削除　END */
                        // 返却値を返却し、処理終了
                        super.debug(
                            tenantId,
                            sequenceNo,
                            MsgKeysConstants.APLLCD0002,
                            this.getClassName(),
                            "service",
                        );
                        return output;
                    }
                } else {
                    // 自テナントに属する全てのN番の取得
                    // STEP20.0版対応　変更　START
                    let nNumberList: string[] = [];
                    try {
                        nNumberList = await this.apiLinesDao.getNnumbers(
                            tenantId,
                        );
                    } catch (e: any) {
                        // DBアクセスリトライエラーの場合
                        if (isSQLException(e)) {
                            handleCode = ResultCdConstants.CODE_070201;
                        }
                        throw e;
                    }
                    // STEP20.0版対応　変更　END
                    if (nNumberList.length >= 1) {
                        // 回線番号リストを取得する
                        // STEP20.0版対応　変更　START
                        try {
                            lineNoList = await this.apiLinesDao.getLineIDList2(
                                nNumberList,
                                tenantId,
                            );
                        } catch (e: any) {
                            // DBアクセスリトライエラーの場合
                            if (isSQLException(e)) {
                                handleCode = ResultCdConstants.CODE_070201;
                            }
                            throw e;
                        }
                        // STEP20.0版対応　変更　END
                        // ヘッダ情報を設定する
                        output = this.setHeaderInfo(
                            sequenceNo,
                            handleCode,
                            apiProcessId,
                            errorMessage,
                            receivedDate,
                        );
                        // ボディ情報を設定する
                        this.setBodyInfo(
                            output,
                            lineNoList,
                            handleCode,
                            errorMessage,
                        );
                        /** STEP5.0対応　削除　START */
                        //                            // 同時接続数減算
                        //                            /* #493対応 MOD START */
                        //                            super.modConCount();
                        //                            /* #493対応 MOD END */
                        /** STEP5.0対応　削除　END */
                        // 返却値を返却し、処理終了
                        super.debug(
                            tenantId,
                            sequenceNo,
                            MsgKeysConstants.APLLCD0002,
                            this.getClassName(),
                            "service",
                        );
                        return output;
                    } else {
                        /** STEP3.0版対応　変更　START */
                        // テナント社内フラグを取得する
                        // STEP20.0版対応　変更　START
                        let tenantsEntity: TenantsEntity = null;
                        try {
                            tenantsEntity = await this.apiCommonDao.getTenants(
                                tenantId,
                            );
                        } catch (e: any) {
                            // DBアクセスリトライエラーの場合
                            if (isSQLException(e)) {
                                handleCode = ResultCdConstants.CODE_070201;
                            }
                            throw e;
                        }

                        // STEP20.0版対応　変更　END
                        if (tenantsEntity != null && tenantsEntity.office) {
                            // プランIDを取得する
                            // STEP20.0版対応　変更　START
                            let planIdList: string[];
                            try {
                                planIdList = await this.apiLinesDao.getPlanID(
                                    tenantId,
                                );
                            } catch (e: any) {
                                // DBアクセスリトライエラーの場合
                                if (isSQLException(e)) {
                                    handleCode = ResultCdConstants.CODE_070201;
                                }
                                throw e;
                            }
                            // STEP20.0版対応　変更　END
                            if (planIdList.length >= 1) {
                                // 料金プランIDを取得する
                                // STEP20.0版対応　変更　START
                                let resalePlanIdList: string[];
                                try {
                                    resalePlanIdList =
                                        await this.apiLinesDao.getResalePlanID(
                                            planIdList,
                                        );
                                } catch (e: any) {
                                    // DBアクセスリトライエラーの場合
                                    if (isSQLException(e)) {
                                        handleCode =
                                            ResultCdConstants.CODE_070201;
                                    }
                                    throw e;
                                }
                                // STEP20.0版対応　変更　END
                                if (resalePlanIdList.length >= 1) {
                                    // 回線取得対象料金プランIDリスト
                                    const lineGetPlanIdList: string[] = [];
                                    // 料金プランIDリストに対し、各料金プランIDに紐づくプランIDが一意であることを確認
                                    for (const resalePlanId of resalePlanIdList) {
                                        // プランIDを取得する
                                        // STEP20.0版対応　変更　START
                                        let planIdListCheck: string[];
                                        try {
                                            planIdListCheck =
                                                await this.apiCommonDao.getPlanID(
                                                    resalePlanId,
                                                );
                                        } catch (e: any) {
                                            // DBアクセスリトライエラーの場合
                                            if (isSQLException(e)) {
                                                handleCode =
                                                    ResultCdConstants.CODE_070201;
                                            }
                                            throw e;
                                        }
                                        // STEP20.0版対応　変更　END
                                        // プランIDリストにより判定する
                                        if (planIdListCheck.length === 1) {
                                            // 回線取得対象料金プランIDリストに料金プランIDリストをリストに追加
                                            lineGetPlanIdList.push(
                                                resalePlanId,
                                            );
                                        }
                                    }
                                    // 回線取得対象料金プランIDリストの件数判定
                                    if (lineGetPlanIdList.length >= 1) {
                                        // 回線番号リストを取得する
                                        // STEP20.0版対応　変更　START
                                        try {
                                            lineNoList =
                                                await this.apiLinesDao.getLineIDList3(
                                                    tenantId,
                                                    lineGetPlanIdList,
                                                );
                                        } catch (e: any) {
                                            // DBアクセスリトライエラーの場合
                                            if (isSQLException(e)) {
                                                handleCode =
                                                    ResultCdConstants.CODE_070201;
                                            }
                                            throw e;
                                        }
                                        // STEP20.0版対応　変更　END
                                        // ヘッダ情報を設定する
                                        output = this.setHeaderInfo(
                                            sequenceNo,
                                            handleCode,
                                            apiProcessId,
                                            errorMessage,
                                            receivedDate,
                                        );
                                        // ボディ情報を設定する
                                        this.setBodyInfo(
                                            output,
                                            lineNoList,
                                            handleCode,
                                            errorMessage,
                                        );
                                        /** STEP5.0対応　削除　START */
                                        // 同時接続数減算
                                        // /* #493対応 MOD START */
                                        // super.modConCount();
                                        // /* #493対応 MOD END */
                                        /** STEP5.0対応　削除　END */
                                        // 返却値を返却し、処理終了
                                        super.debug(
                                            tenantId,
                                            sequenceNo,
                                            MsgKeysConstants.APLLCD0002,
                                            this.getClassName(),
                                            "service",
                                        );
                                        return output;
                                    }
                                }
                            }

                            // 回線番号リストを取得する
                            // STEP20.0版対応　変更　START
                            try {
                                lineNoList =
                                    await this.apiLinesDao.getLineIDList4(
                                        tenantId,
                                    );
                            } catch (e: any) {
                                // DBアクセスリトライエラーの場合
                                if (isSQLException(e)) {
                                    handleCode = ResultCdConstants.CODE_070201;
                                }
                                throw e;
                            }
                            // STEP20.0版対応　変更　END
                        }
                        /** STEP3.0版対応　変更　END */
                        // ヘッダ情報を設定する
                        output = this.setHeaderInfo(
                            sequenceNo,
                            handleCode,
                            apiProcessId,
                            errorMessage,
                            receivedDate,
                        );
                        // ボディ情報を設定する
                        this.setBodyInfo(
                            output,
                            lineNoList,
                            handleCode,
                            errorMessage,
                        );
                        /** STEP5.0対応　削除　START */
                        // 同時接続数減算
                        // /* #493対応 MOD START */
                        // super.modConCount();
                        // /* #493対応 MOD END */
                        /** STEP5.0対応　削除　END */
                        // 返却値を返却し、処理終了
                        super.debug(
                            tenantId,
                            sequenceNo,
                            MsgKeysConstants.APLLCD0002,
                            this.getClassName(),
                            "service",
                        );
                        return output;
                    }
                }
                /** #162対応　追加　END */
            }
        } catch (e: any) {
            // STEP20.0版対応　追加　START
            if (isSQLException(e)) {
                // DBアクセスリトライエラーの場合
                // リターンコード値は各エラー時にて設定済み
                // 返却値編集
                output = this.setHeaderInfo(
                    sequenceNo,
                    handleCode,
                    apiProcessId,
                    errorMessage,
                    receivedDate,
                );
                // 変数「処理コード」を判定し,ボディ情報（異常系）を設定する
                this.setBodyInfo(output, lineNoList, handleCode, errorMessage);
                super.error(
                    e,
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APCOME0402,
                    e.message,
                );
                return output;
            }

            handleCode = ResultCdConstants.CODE_999999;
            /** STEP1.2a版対応　変更　START */
            // 返却値編集
            output = this.setHeaderInfo(
                sequenceNo,
                handleCode,
                apiProcessId,
                errorMessage,
                receivedDate,
            );
            // 変数「処理コード」を判定し,ボディ情報（異常系）を設定する
            this.setBodyInfo(output, lineNoList, handleCode, errorMessage);
            /** STEP1.2a版対応　変更　END */
            /** STEP5.0対応　削除　START */
            // #493対応 MOD START
            // super.modConCount();
            // #493対応 MOD END
            /** STEP5.0対応　削除　END */
            super.error(
                e,
                tenantId,
                sequenceNo,
                MsgKeysConstants.APCOME0401,
                e.message,
            );
            return output;

            /** STEP5.0対応　変更　START */
        }
        /** STEP5.0対応　変更　END */
    }

    /**
     * 代表N番相関チェック。<BR>
     * @param nBan String
     * @return boolean
     */
    private checkNBanFormat(nBan: string): boolean {
        const pattern: RegExp = /^N\d{9}$/;
        return pattern.test(nBan);
    }

    /** STEP1.2a版対応　変更　START */
    /**
     * ヘッダ情報を設定。<BR>
     *
     * @param sequenceNo String 送信番号
     * @param handleCode String 処理コード
     * @param apiProcessId String API処理ID
     * @param errorMessage 変数「エラーメッセージ」
     * @param receivedDate REST API共通処理が呼び出されたシステム日付
     * @return LineTerminalListAcquisitionOutputDto
     */
    private setHeaderInfo(
        sequenceNo: string,
        handleCode: string,
        apiProcessId: string,
        errorMessage: string,
        receivedDate: string,
    ): LineTerminalListAcquisitionOutputDto {
        const responseHeader = {} as ResponseHeader;

        // 送信番号 「固定長:7」
        if (!CheckUtil.checkLength(sequenceNo, 7, true)) {
            sequenceNo = MvnoUtil.addSpaceAfterStr(sequenceNo, 7);
        }
        responseHeader.sequenceNo = sequenceNo;
        // 受信日時 「固定長:19」
        responseHeader.receivedDate = receivedDate;

        // 処理コード 「固定長:4」
        if (CheckUtil.checkLength(handleCode, 4, true)) {
            handleCode = MvnoUtil.addSpaceAfterStr(handleCode, 4);
        }
        responseHeader.processCode = handleCode;

        // API処理ID 「固定長:10」
        if (CheckUtil.checkLength(apiProcessId, 10, true)) {
            apiProcessId = MvnoUtil.addSpaceAfterStr(apiProcessId, 10);
        }
        responseHeader.apiProcessID = apiProcessId;

        // 内部呼出フラグ = true
        // 回線・端末リストの確認アウトプットDtoクラス(外部)を定義する
        const output = {} as LineTerminalListAcquisitionOutputDto;
        // ヘッダ情報を設定する
        output.responseHeader = responseHeader;
        return output;
    }

    /** STEP1.2a版対応　変更　END */

    /** STEP1.2a版対応　変更　START */
    /**
     * ボディ情報を設定する。<BR>
     *
     * @param out LineTerminalListAcquisitionOutputDto
     * @param errorMessage 変数「エラーメッセージ」
     * @param isPrivate 内部呼出フラグ
     */
    private setBodyInfo(
        out: LineTerminalListAcquisitionOutputDto,
        lineNos: string[],
        handlingCode: string,
        errorMessage: string,
    ) {
        // 内部呼出フラグ = true
        const output = out as LineTerminalListAcquisitionOutputDto;

        // 変数「処理コード」を判定する
        if (
            typeof handlingCode === "string" &&
            handlingCode.toLowerCase() !==
                ResultCdConstants.CODE_000000.toLowerCase()
        ) {
            // 回線リスト.回線番号 「固定長:11」
            const list: { lineNo: string }[] = [
                { lineNo: MvnoUtil.addSpaceAfterStr(null, 0) },
            ];

            // 2.3 ボディ情報（異常系）を設定する
            output.lineList = list;
        } else {
            // 回線リスト.回線番号 「固定長:5」
            const list: { lineNo: string }[] = [];
            // 2.4 ボディ情報（正常系）を設定する
            for (const lineNo of lineNos) {
                const lineList: string = CheckUtil.checkLength(lineNo, 11, true)
                    ? lineNo
                    : MvnoUtil.addSpaceAfterStr(lineNo, 11);
                list.push({ lineNo: lineList });
            }
            output.lineList = list;
        }
    }
    /** STEP1.2a版対応　変更　END */
}
