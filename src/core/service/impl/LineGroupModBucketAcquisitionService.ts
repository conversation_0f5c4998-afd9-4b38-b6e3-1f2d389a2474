import config from "config";

import Constants from "@/core/constant/Constants";
import MsgKeysConstants from "@/core/constant/MsgKeysConstants";
import ResultCdConstants from "@/core/constant/ResultCdConstants";

import Check from "@/core/common/Check";
import CheckUtil from "@/core/common/CheckUtil";
import MvnoUtil from "@/core/common/MvnoUtil";
import RESTCommon from "@/core/common/RESTCommon";
import SOAPCommon from "@/core/common/SOAPCommon";
import SOCommon from "@/core/common/SOCommon";
import TenantManage from "@/core/common/TenantManage";

import APICommonDAO from "@/core/dao/APICommonDAO";
import APILinesGroupDAO from "@/core/dao/ApiLinesGroupDAO";
import LineGroupModBucketAcquisitionInputDto from "@/core/dto/LineGroupModBucketAcquisitionInputDto";
import LineGroupModBucketAcquisitionOutputDto from "@/core/dto/LineGroupModBucketAcquisitionOutputDto";
import ParameterName from "@/core/dto/ParameterName";
import ResponseHeader from "@/core/dto/ResponseHeader";
import SOObject from "@/core/dto/SOObject";

import { TenantsEntity } from "@/core/entity/TenantsEntity";
import LineGroupsEntity from "@/core/entity/LineGroupsEntity";

import { isSQLException } from "@/helpers/queryHelper";
import SOAPException from "@/types/soapException";
import { IFService } from "../IFService";
import { getConfig } from "@/helpers/configHelper";
import { getStringParameter } from "@/types/parameter.string";

/** 定数　「回線グループ容量算出バッファバイト」 */
const CONST_LINEGROUP_BUFFERBYTE = "回線グループ容量算出バッファバイト";
/** 定数　「回線グループID」 */
const CONST_LINEGROUP_ID = "回線グループID";
/** 定数　「容量指定、容量計算」 */
const CONST_CAPACITY_CALCULATION = "容量指定、容量計算";
/** 定数　「容量指定」 */
const CONST_CAPACITY = "容量指定";
/** 定数　「容量計算」 */
const CONST_CALCULATION = "容量計算";
/** 定数　「容量計算」 */
// const CONST_LONG_CALCULATION = BigInt(104857600);
const CONST_LONG_CALCULATION = 104857600;
/** 定数　「社内テナント種別は１（C-OCN）」 */
const CONST_SHANAITENANTID = "1";
/** XML電文返信結果 */
const CONST_RESULT = "NG";

export default class LineGroupModBucketAcquisitionService
    extends RESTCommon
    implements
        IFService<
            LineGroupModBucketAcquisitionInputDto,
            LineGroupModBucketAcquisitionOutputDto
        >
{
    /**
     * API共通DAO
     */
    private aPICommonDAO = new APICommonDAO(this.request, this.context);

    /**
     * API回線グループDAO
     */
    private aPILinesGroupDAO = new APILinesGroupDAO(this.request, this.context);

    /**
     * SOAP API連携機能初期化
     */
    private sOAPCommon = new SOAPCommon(this.request, this.context);

    /**
     * SO管理共通DAO
     */
    private soCommon = new SOCommon(this.request, this.context);

    /**
     * テナント管理クラス
     */
    private tenantManage = new TenantManage(this.request, this.context);

    /**
     * 回線グループ容量算出バッファバイト
     */
    private groupBufferByte = config.get<string>("mvno.GroupBufferByte");

    private reservationDateExecutionUnits: string = getConfig(
        "ReservationDateExecutionUnits",
    );

    private reservationsLimitDays: string = getConfig("ReservationsLimitDays");

    /**
     * 回線グループ基本容量変更機能。<BR>
     *
     * <PRE>
     * モバイルアクセス卸ポータルシステムは、B-OCN・UNOポータルおよび卸事業者からの回線グループ基本容量変更のリクエストによって、
     * 指定された「容量指定」または、「容量計算」操作に応じて、回線グループの容量をＴＰＣにリクエストする機能である。
     * </PRE>
     *
     * @param param 回線グループ基本容量変更機能インプット
     * @param isPrivate 内部呼出フラグ
     * @param params 可変長さ引数(内部から呼出す時に使用するパラメータ)  // NOTE: _always_ `false`
     * @return LineGroupModBucketAcquisitionOutputDto 回線グループ基本容量変更機能アウトプット
     * @throws Exception
     */
    public async service(
        param: LineGroupModBucketAcquisitionInputDto,
        isPrivate: boolean,
        ...params: any[]
    ): Promise<LineGroupModBucketAcquisitionOutputDto> {
        // NOTE params:
        // 0: param.executeUserId
        // 1: param.executeTenantId
        // 2: request.remoteAddr
        super.debug(
            param.tenantId,
            param.requestHeader.sequenceNo,
            MsgKeysConstants.APGMBD0001,
            this.getClassName(),
            "service",
            JSON.stringify(param),
        );

        // REST API共通処理が呼び出されたシステム日付
        // String receivedDate = MvnoUtil.getDateTimeNow();
        const receivedDate = MvnoUtil.getDateTimeNow();
        /** 変数「処理コード」初期化 */
        let handleCode = ResultCdConstants.CODE_000000;
        /** 送信番号取得 */
        const sequenceNo = param.requestHeader.sequenceNo;
        /** 送信元システムID取得 */
        const senderSystemId = param.requestHeader.senderSystemId;
        /** API認証キー取得 */
        const apiKey = param.requestHeader.apiKey;
        /** 機能種別取得 */
        const functionType = param.requestHeader.functionType;
        /** テナントID取得 */
        const tenantId = param.tenantId;
        /** 回線グループID取得 */
        const lineGroupId = getStringParameter(param.lineGroupId) ?? "";
        /** 容量指定取得 */
        const capacity = getStringParameter(param.capacity);
        /** 容量計算取得 */
        const calculation = getStringParameter(param.calculation);
        /** 容量 */
        let strCapacity = "";
        let intCapacity = 0;
        /** 容量変更フラグ */
        let capacityFlag = 0;
        // 変数「エラーメッセージ」を定義する
        const errorMessage: string = null;
        // 変数「実行ユーザID」を定義する
        const executeUserId: string = null;
        // 変数「実行テナントID」を定義する
        const executeTenantId: string = null;

        // 回線グループ基本容量変更アウトプット初期化
        let outputDto: LineGroupModBucketAcquisitionOutputDto = null;

        // 予約日
        const reserveDate = param.reserve_date;
        // 予約フラグ
        const reserveFlag = param.reserve_flag;
        // ＳＯ－ＩＤ
        const reserveSoId = param.reserve_soId;
        // オーダ種別取得
        const orderType = Check.checkOrderType(
            reserveDate,
            reserveFlag,
            reserveSoId,
        );

        let document: Document = null;
        const apiHandleId = this.context.responseHeader.apiProcessID;

        this.context.log("LineGroupModBucketAcquisitionService.service:", {
            orderType,
            apiHandleId,
        });
        try {
            // オーダ種別チェック
            if (Constants.ORDER_TYPE_9.equals(orderType)) {
                handleCode = ResultCdConstants.CODE_150105;
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APGMBW0011);
                outputDto = this.returnEdit(
                    param,
                    handleCode,
                    apiHandleId,
                    "",
                    errorMessage,
                    isPrivate,
                    receivedDate,
                );
                return outputDto;
            }

            // 予約実行オーダの場合
            if (Constants.ORDER_TYPE_2.equals(orderType)) {
                // APサーバの複数IP取得
                const localIpList = MvnoUtil.getLocalIpList();
                // 予約実行可否チェック
                if (!this.context.isInternalRequest) {
                    handleCode = ResultCdConstants.CODE_999999;
                    super.error(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APCOME0401,
                        "予約実行できませんでした。クライアントIP:" +
                            String(params[2]) +
                            ", APサーバーIP：" +
                            MvnoUtil.getOutputList(localIpList),
                    );
                    outputDto = this.returnEdit(
                        param,
                        handleCode,
                        apiHandleId,
                        "",
                        errorMessage,
                        isPrivate,
                        receivedDate,
                    );
                    return outputDto;
                }
            }

            // プロパティフォーマットチェック
            if (CheckUtil.checkIsNotNull(this.groupBufferByte)) {
                handleCode = ResultCdConstants.CODE_150101;
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APGMBW0000,
                    CONST_LINEGROUP_BUFFERBYTE,
                );
                // SO管理共通を呼び出す
                await this.soManagementCommon(
                    param,
                    apiHandleId,
                    handleCode,
                    strCapacity,
                    executeUserId,
                    executeTenantId,
                    receivedDate,
                    orderType,
                    reserveDate,
                    isPrivate,
                );
                outputDto = this.returnEdit(
                    param,
                    handleCode,
                    apiHandleId,
                    "",
                    errorMessage,
                    isPrivate,
                    receivedDate,
                );
                return outputDto;
            }

            // 回線グループIDフォーマットチェック
            if (!Check.checkLineGroupId(lineGroupId)) {
                handleCode = ResultCdConstants.CODE_150102;
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APGMBW0001,
                    CONST_LINEGROUP_ID,
                    lineGroupId,
                );
                // SO管理共通を呼び出す
                await this.soManagementCommon(
                    param,
                    apiHandleId,
                    handleCode,
                    strCapacity,
                    executeUserId,
                    executeTenantId,
                    receivedDate,
                    orderType,
                    reserveDate,
                    isPrivate,
                );
                outputDto = this.returnEdit(
                    param,
                    handleCode,
                    apiHandleId,
                    "",
                    errorMessage,
                    isPrivate,
                    receivedDate,
                );
                return outputDto;
            }

            // 入力パラメータ．「容量指定」、入力パラメータ．「容量計算」相関チェック
            if (
                (CheckUtil.checkIsNotNull(capacity) &&
                    CheckUtil.checkIsNotNull(calculation)) ||
                (!CheckUtil.checkIsNotNull(capacity) &&
                    !CheckUtil.checkIsNotNull(calculation))
            ) {
                handleCode = ResultCdConstants.CODE_150102;
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APGMBW0001,
                    CONST_CAPACITY_CALCULATION,
                    capacity + "、" + calculation,
                );
                // SO管理共通を呼び出す
                await this.soManagementCommon(
                    param,
                    apiHandleId,
                    handleCode,
                    strCapacity,
                    executeUserId,
                    executeTenantId,
                    receivedDate,
                    orderType,
                    reserveDate,
                    isPrivate,
                );
                outputDto = this.returnEdit(
                    param,
                    handleCode,
                    apiHandleId,
                    "",
                    errorMessage,
                    isPrivate,
                    receivedDate,
                );
                return outputDto;
            } else if (!CheckUtil.checkIsNotNull(capacity)) {
                // 入力パラメータ．「容量指定」の数字チェック
                if (!CheckUtil.checkIsNum(capacity)) {
                    handleCode = ResultCdConstants.CODE_150102;
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APGMBW0001,
                        CONST_CAPACITY,
                        capacity,
                    );
                    // SO管理共通を呼び出す
                    await this.soManagementCommon(
                        param,
                        apiHandleId,
                        handleCode,
                        strCapacity,
                        executeUserId,
                        executeTenantId,
                        receivedDate,
                        orderType,
                        reserveDate,
                        isPrivate,
                    );
                    outputDto = this.returnEdit(
                        param,
                        handleCode,
                        apiHandleId,
                        "",
                        errorMessage,
                        isPrivate,
                        receivedDate,
                    );
                    return outputDto;
                }

                // 入力パラメータ．「容量指定」の数字範囲チェック
                const intCapacity = parseInt(capacity, 10);
                if (intCapacity < 0 || intCapacity > CONST_LONG_CALCULATION) {
                    handleCode = ResultCdConstants.CODE_150102;
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APGMBW0001,
                        CONST_CAPACITY,
                        capacity,
                    );
                    // SO管理共通を呼び出す
                    await this.soManagementCommon(
                        param,
                        apiHandleId,
                        handleCode,
                        strCapacity,
                        executeUserId,
                        executeTenantId,
                        receivedDate,
                        orderType,
                        reserveDate,
                        isPrivate,
                    );
                    outputDto = this.returnEdit(
                        param,
                        handleCode,
                        apiHandleId,
                        "",
                        errorMessage,
                        isPrivate,
                        receivedDate,
                    );
                    return outputDto;
                }

                // 容量変更フラグ設定
                capacityFlag = 1;
                // 容量が0MBの場合は、1MBを設定する
                if (intCapacity === 0) {
                    strCapacity = "1";
                } else {
                    strCapacity = capacity;
                }
            } else if (!CheckUtil.checkIsNotNull(calculation)) {
                // 容量計算数字チェック
                if (!CheckUtil.checkIsNum(calculation)) {
                    handleCode = ResultCdConstants.CODE_150102;
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APGMBW0001,
                        CONST_CALCULATION,
                        calculation,
                    );
                    // SO管理共通を呼び出す
                    await this.soManagementCommon(
                        param,
                        apiHandleId,
                        handleCode,
                        strCapacity,
                        executeUserId,
                        executeTenantId,
                        receivedDate,
                        orderType,
                        reserveDate,
                        isPrivate,
                    );
                    outputDto = this.returnEdit(
                        param,
                        handleCode,
                        apiHandleId,
                        "",
                        errorMessage,
                        isPrivate,
                        receivedDate,
                    );
                    return outputDto;
                }

                // 容量計算範囲チェック
                const lCalculation = parseInt(calculation, 10);
                if (lCalculation !== 1) {
                    handleCode = ResultCdConstants.CODE_150102;
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APGMBW0001,
                        CONST_CALCULATION,
                        calculation,
                    );
                    // SO管理共通を呼び出す
                    await this.soManagementCommon(
                        param,
                        apiHandleId,
                        handleCode,
                        strCapacity,
                        executeUserId,
                        executeTenantId,
                        receivedDate,
                        orderType,
                        reserveDate,
                        isPrivate,
                    );
                    outputDto = this.returnEdit(
                        param,
                        handleCode,
                        apiHandleId,
                        "",
                        errorMessage,
                        isPrivate,
                        receivedDate,
                    );
                    return outputDto;
                }

                // 容量変更フラグ設定
                capacityFlag = 0;
            }

            // 予約前オーダ　或いは　予約実行オーダの場合
            if (
                Constants.ORDER_TYPE_1.equals(orderType) ||
                Constants.ORDER_TYPE_2.equals(orderType)
            ) {
                // 予約日フォーマットチェック
                if (!Check.checkReserveDateFmt(reserveDate)) {
                    handleCode = ResultCdConstants.CODE_150102;
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APGMBW0001,
                        "予約日",
                        reserveDate,
                    );
                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(
                        param,
                        apiHandleId,
                        handleCode,
                        strCapacity,
                        executeUserId,
                        executeTenantId,
                        receivedDate,
                        orderType,
                        reserveDate,
                        isPrivate,
                    );
                    outputDto = this.returnEdit(
                        param,
                        handleCode,
                        apiHandleId,
                        "",
                        errorMessage,
                        isPrivate,
                        receivedDate,
                    );
                    return outputDto;
                }
            }

            // 予約前オーダの場合
            if (Constants.ORDER_TYPE_1.equals(orderType)) {
                // 予約日と投入日相関チェック
                if (
                    !Check.checkReserveDateOrderDate(receivedDate, reserveDate)
                ) {
                    handleCode = ResultCdConstants.CODE_150106;
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APGMBW0012,
                        reserveDate,
                        receivedDate,
                    );
                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(
                        param,
                        apiHandleId,
                        handleCode,
                        strCapacity,
                        executeUserId,
                        executeTenantId,
                        receivedDate,
                        orderType,
                        reserveDate,
                        isPrivate,
                    );
                    outputDto = this.returnEdit(
                        param,
                        handleCode,
                        apiHandleId,
                        "",
                        errorMessage,
                        isPrivate,
                        receivedDate,
                    );
                    return outputDto;
                }
                // 予約実行日時単位フォーマットチェック
                if (
                    !Check.checkReservationDateExecutionUnitsFmt(
                        this.reservationDateExecutionUnits,
                    )
                ) {
                    handleCode = ResultCdConstants.CODE_150107;
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APGMBW0013,
                        this.reservationDateExecutionUnits,
                    );
                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(
                        param,
                        apiHandleId,
                        handleCode,
                        strCapacity,
                        executeUserId,
                        executeTenantId,
                        receivedDate,
                        orderType,
                        reserveDate,
                        isPrivate,
                    );
                    outputDto = this.returnEdit(
                        param,
                        handleCode,
                        apiHandleId,
                        "",
                        errorMessage,
                        isPrivate,
                        receivedDate,
                    );
                    return outputDto;
                }
                // 予約実行日時単位と予約日相関チェック
                if (
                    !Check.checkReservationDateExecutionUnits(
                        this.reservationDateExecutionUnits,
                        reserveDate,
                    )
                ) {
                    handleCode = ResultCdConstants.CODE_150108;
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APGMBW0014,
                        this.reservationDateExecutionUnits,
                        reserveDate,
                    );
                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(
                        param,
                        apiHandleId,
                        handleCode,
                        strCapacity,
                        executeUserId,
                        executeTenantId,
                        receivedDate,
                        orderType,
                        reserveDate,
                        isPrivate,
                    );
                    outputDto = this.returnEdit(
                        param,
                        handleCode,
                        apiHandleId,
                        "",
                        errorMessage,
                        isPrivate,
                        receivedDate,
                    );
                    return outputDto;
                }
                // 予約可能制限日数フォーマットチェック
                if (
                    !Check.checkReservationsLimitDaysFmt(
                        this.reservationsLimitDays,
                    )
                ) {
                    handleCode = ResultCdConstants.CODE_150109;
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APGMBW0015,
                        this.reservationsLimitDays,
                    );
                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(
                        param,
                        apiHandleId,
                        handleCode,
                        strCapacity,
                        executeUserId,
                        executeTenantId,
                        receivedDate,
                        orderType,
                        reserveDate,
                        isPrivate,
                    );
                    outputDto = this.returnEdit(
                        param,
                        handleCode,
                        apiHandleId,
                        "",
                        errorMessage,
                        isPrivate,
                        receivedDate,
                    );
                    return outputDto;
                }
                // 予約可能制限日数範囲チェック
                if (
                    !Check.checkReservationsLimitDays(
                        this.reservationsLimitDays,
                        reserveDate,
                    )
                ) {
                    handleCode = ResultCdConstants.CODE_150110;
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APGMBW0016,
                        this.reservationsLimitDays,
                        reserveDate,
                    );
                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(
                        param,
                        apiHandleId,
                        handleCode,
                        strCapacity,
                        executeUserId,
                        executeTenantId,
                        receivedDate,
                        orderType,
                        reserveDate,
                        isPrivate,
                    );
                    outputDto = this.returnEdit(
                        param,
                        handleCode,
                        apiHandleId,
                        "",
                        errorMessage,
                        isPrivate,
                        receivedDate,
                    );
                    return outputDto;
                }
            }

            // 社内テナント種別チェックを行う
            let shaNaiTenantType = "";
            let tenantsEntity: TenantsEntity = null;
            try {
                tenantsEntity = await this.aPICommonDAO.getTenants(tenantId);
            } catch (e) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    handleCode = ResultCdConstants.CODE_150103;
                }
                throw e;
            }
            if (tenantsEntity != null) {
                shaNaiTenantType = String(tenantsEntity.tenantType);
                if (
                    CONST_SHANAITENANTID.equals(shaNaiTenantType) &&
                    !CheckUtil.checkIsNull(tenantsEntity.tenantType)
                ) {
                    handleCode = ResultCdConstants.CODE_150104;
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APGMBW0003,
                    );
                    // SO管理共通を呼び出す
                    await this.soManagementCommon(
                        param,
                        apiHandleId,
                        handleCode,
                        strCapacity,
                        executeUserId,
                        executeTenantId,
                        receivedDate,
                        orderType,
                        reserveDate,
                        isPrivate,
                    );
                    outputDto = this.returnEdit(
                        param,
                        handleCode,
                        apiHandleId,
                        "",
                        errorMessage,
                        isPrivate,
                        receivedDate,
                    );
                    return outputDto;
                }
            } else {
                handleCode = ResultCdConstants.CODE_150103;
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APGMBW0002);
                // SO管理共通を呼び出す
                await this.soManagementCommon(
                    param,
                    apiHandleId,
                    handleCode,
                    strCapacity,
                    executeUserId,
                    executeTenantId,
                    receivedDate,
                    orderType,
                    reserveDate,
                    isPrivate,
                );
                outputDto = this.returnEdit(
                    param,
                    handleCode,
                    apiHandleId,
                    "",
                    errorMessage,
                    isPrivate,
                    receivedDate,
                );
                return outputDto;
            }

            // 回線グループ管理情報取得
            let lineGroupsEntity: LineGroupsEntity = null;
            try {
                lineGroupsEntity =
                    await this.aPILinesGroupDAO.getLineGroupsCapacityShare(
                        lineGroupId,
                    );
            } catch (e) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    handleCode = ResultCdConstants.CODE_150201;
                }
                throw e;
            }

            // 回線グループ管理情報チェック
            if (
                lineGroupsEntity == null ||
                !tenantId.equals(lineGroupsEntity.tenantId) ||
                lineGroupsEntity.status !== 1 ||
                lineGroupsEntity.planId == null
            ) {
                handleCode = ResultCdConstants.CODE_150201;
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APGMBW0005);
                // SO管理共通を呼び出す
                await this.soManagementCommon(
                    param,
                    apiHandleId,
                    handleCode,
                    strCapacity,
                    executeUserId,
                    executeTenantId,
                    receivedDate,
                    orderType,
                    reserveDate,
                    isPrivate,
                );
                outputDto = this.returnEdit(
                    param,
                    handleCode,
                    apiHandleId,
                    "",
                    errorMessage,
                    isPrivate,
                    receivedDate,
                );
                return outputDto;
            }

            // 変数「オーダ種別」=即時オーダ　あるいは　予約実行オーダ
            if (
                Constants.ORDER_TYPE_0.equals(orderType) ||
                Constants.ORDER_TYPE_2.equals(orderType)
            ) {
                // 容量計算である場合
                if (capacityFlag === 0) {
                    intCapacity = 0;
                    intCapacity = parseInt(this.groupBufferByte, 10);

                    // 基本容量の合計を取得する
                    let intBasicCapacity: number = null;
                    try {
                        intBasicCapacity =
                            await this.aPILinesGroupDAO.getLineLineGroupsSumCapacity(
                                lineGroupId,
                            );
                    } catch (e) {
                        if (isSQLException(e)) {
                            // DBアクセスリトライエラーの場合
                            handleCode = ResultCdConstants.CODE_150301;
                        }
                        throw e;
                    }

                    // 基本容量の合計が取得できない場合
                    if (intBasicCapacity == null) {
                        handleCode = ResultCdConstants.CODE_150301;
                        super.warn(
                            tenantId,
                            sequenceNo,
                            MsgKeysConstants.APGMBW0006,
                        );
                        // SO管理共通を呼び出す
                        await this.soManagementCommon(
                            param,
                            apiHandleId,
                            handleCode,
                            strCapacity,
                            executeUserId,
                            executeTenantId,
                            receivedDate,
                            orderType,
                            reserveDate,
                            isPrivate,
                        );
                        outputDto = this.returnEdit(
                            param,
                            handleCode,
                            apiHandleId,
                            "",
                            errorMessage,
                            isPrivate,
                            receivedDate,
                        );
                        return outputDto;
                    }
                    // 容量計算
                    intCapacity = intCapacity + intBasicCapacity;

                    // 容量判定
                    // 容量が100TBを超える場合は100TBを、容量が0MB以下の場合は1MBを設定する
                    if (intCapacity > CONST_LONG_CALCULATION) {
                        intCapacity = CONST_LONG_CALCULATION;
                    } else if (intCapacity <= 0) {
                        intCapacity = 1;
                    }
                    // 容量設定
                    strCapacity = String(intCapacity);
                }

                // TPC情報を取得する。
                let tpcConnectionResults: [boolean, string, string] = null;
                try {
                    tpcConnectionResults =
                        await this.tenantManage.checkTpcConnection(
                            param.tenantId,
                        );
                } catch (e) {
                    if (isSQLException(e)) {
                        // DBアクセスリトライエラーの場合
                        handleCode = ResultCdConstants.CODE_150401;
                    }
                    throw e;
                }
                // 変数「TPC情報取得結果」より判断する
                if (!tpcConnectionResults[0]) {
                    handleCode = ResultCdConstants.CODE_150401;
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APGMBW0010,
                        tenantId,
                    );
                    // SO管理共通を呼び出す
                    await this.soManagementCommon(
                        param,
                        apiHandleId,
                        handleCode,
                        strCapacity,
                        executeUserId,
                        executeTenantId,
                        receivedDate,
                        orderType,
                        reserveDate,
                        isPrivate,
                    );
                    outputDto = this.returnEdit(
                        param,
                        handleCode,
                        apiHandleId,
                        "",
                        errorMessage,
                        isPrivate,
                        receivedDate,
                    );
                    return outputDto;
                }

                // SOAP APIにより、現在容量を取得する
                let capPolnum: string;
                const getCapPolnumParamList: ParameterName[] = [];
                const parameterPronum = new ParameterName();
                parameterPronum.setName("pronum");
                const newLineGroupId =
                    "101000000" + MvnoUtil.addZeroBeforeStr(lineGroupId, 9);
                parameterPronum.setValue(newLineGroupId);
                getCapPolnumParamList.push(parameterPronum);
                const sOAPCommonOutputDto =
                    await this.sOAPCommon.callWebSoapApi(
                        "serviceProfileQuery",
                        "ProfileView",
                        "Service",
                        getCapPolnumParamList,
                        tenantId,
                        sequenceNo,
                        tpcConnectionResults[1],
                    );

                if (
                    ResultCdConstants.CODE_000951.equals(
                        sOAPCommonOutputDto.getProcessCode(),
                    )
                ) {
                    // SG取得がNG
                    handleCode = ResultCdConstants.CODE_150501;
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APGMBW0007,
                    );
                    // SO管理共通を呼び出す
                    await this.soManagementCommon(
                        param,
                        apiHandleId,
                        handleCode,
                        strCapacity,
                        executeUserId,
                        executeTenantId,
                        receivedDate,
                        orderType,
                        reserveDate,
                        isPrivate,
                    );
                    outputDto = this.returnEdit(
                        param,
                        handleCode,
                        apiHandleId,
                        "",
                        errorMessage,
                        isPrivate,
                        receivedDate,
                    );
                    return outputDto;
                }

                document = sOAPCommonOutputDto.getDoc();
                let resultStr = this.sOAPCommon.getNodeContent(
                    document,
                    "//Result",
                );
                if (
                    sOAPCommonOutputDto.isError() ||
                    CONST_RESULT.equals(resultStr)
                ) {
                    handleCode = ResultCdConstants.CODE_150501;
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APGMBW0007,
                    );
                    // SO管理共通を呼び出す
                    await this.soManagementCommon(
                        param,
                        apiHandleId,
                        handleCode,
                        strCapacity,
                        executeUserId,
                        executeTenantId,
                        receivedDate,
                        orderType,
                        reserveDate,
                        isPrivate,
                    );
                    outputDto = this.returnEdit(
                        param,
                        handleCode,
                        apiHandleId,
                        "",
                        errorMessage,
                        isPrivate,
                        receivedDate,
                    );
                    return outputDto;
                    /** STEP1.2a版対応　変更　END */
                } else {
                    capPolnum = this.sOAPCommon.getNodeContent(
                        document,
                        "//ServiceProfileInfomation/cap_polnum",
                    );
                    if (CheckUtil.checkIsNotNull(capPolnum)) {
                        handleCode = ResultCdConstants.CODE_150601;
                        super.warn(
                            tenantId,
                            sequenceNo,
                            MsgKeysConstants.APGMBW0008,
                        );
                        // SO管理共通を呼び出す
                        await this.soManagementCommon(
                            param,
                            apiHandleId,
                            handleCode,
                            strCapacity,
                            executeUserId,
                            executeTenantId,
                            receivedDate,
                            orderType,
                            reserveDate,
                            isPrivate,
                        );
                        outputDto = this.returnEdit(
                            param,
                            handleCode,
                            apiHandleId,
                            "",
                            errorMessage,
                            isPrivate,
                            receivedDate,
                        );
                        return outputDto;
                    }
                }

                // SOAP APIにより、基本容量を設定する
                const setCapacityParamList: ParameterName[] = [];
                // group_number設定
                const paramGroupNumber = new ParameterName();
                paramGroupNumber.setName("group_number");
                paramGroupNumber.setValue(lineGroupId);
                setCapacityParamList.push(paramGroupNumber);
                // policy_number設定
                const paramPolicyNumber = new ParameterName();
                paramPolicyNumber.setName("policy_number");
                paramPolicyNumber.setValue("101");
                setCapacityParamList.push(paramPolicyNumber);
                // cap_month設定
                const paramCapMonth = new ParameterName();
                paramCapMonth.setName("cap_month");
                paramCapMonth.setValue(strCapacity);
                setCapacityParamList.push(paramCapMonth);
                // cap_polum設定
                const paramCapPolum = new ParameterName();
                paramCapPolum.setName("cap_polnum");
                paramCapPolum.setValue(capPolnum);
                setCapacityParamList.push(paramCapPolum);

                let sOAPCommonOutput = await this.sOAPCommon.callWebSoapApi(
                    "serviceProfileRequestLite",
                    "Mod",
                    "",
                    setCapacityParamList,
                    tenantId,
                    sequenceNo,
                    tpcConnectionResults[1],
                );

                if (
                    ResultCdConstants.CODE_000951.equals(
                        sOAPCommonOutput.getProcessCode(),
                    )
                ) {
                    // SG取得がNG
                    handleCode = ResultCdConstants.CODE_150801;
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APGMBW0009,
                    );
                    // SO管理共通を呼び出す
                    await this.soManagementCommon(
                        param,
                        apiHandleId,
                        handleCode,
                        strCapacity,
                        executeUserId,
                        executeTenantId,
                        receivedDate,
                        orderType,
                        reserveDate,
                        isPrivate,
                    );
                    outputDto = this.returnEdit(
                        param,
                        handleCode,
                        apiHandleId,
                        "",
                        errorMessage,
                        isPrivate,
                        receivedDate,
                    );
                    return outputDto;
                }

                document = sOAPCommonOutput.getDoc();
                resultStr = this.sOAPCommon.getNodeContent(
                    document,
                    "//Result",
                );
                if (CONST_RESULT.equals(resultStr)) {
                    handleCode = ResultCdConstants.CODE_150801;
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APGMBW0009,
                    );
                    // SO管理共通を呼び出す
                    await this.soManagementCommon(
                        param,
                        apiHandleId,
                        handleCode,
                        strCapacity,
                        executeUserId,
                        executeTenantId,
                        receivedDate,
                        orderType,
                        reserveDate,
                        isPrivate,
                    );
                    outputDto = this.returnEdit(
                        param,
                        handleCode,
                        apiHandleId,
                        "",
                        errorMessage,
                        isPrivate,
                        receivedDate,
                    );
                    return outputDto;
                }

                // セカンダリTPCへSOAP連携
                if (tpcConnectionResults[2] != null) {
                    // セカンダリTPCが設定されている場合は以下の処理を行う(電文はプライマリと同じものを使用)
                    try {
                        sOAPCommonOutput = await this.sOAPCommon.callWebSoapApi(
                            "serviceProfileRequestLite",
                            "Mod",
                            "",
                            setCapacityParamList,
                            tenantId,
                            sequenceNo,
                            tpcConnectionResults[2],
                            false,
                        );

                        if (
                            ResultCdConstants.CODE_000951.equals(
                                sOAPCommonOutput.getProcessCode(),
                            )
                        ) {
                            // SG取得がNG
                            super.warn(
                                tenantId,
                                sequenceNo,
                                MsgKeysConstants.APGMBW0017,
                                "SG取得NG",
                            );
                        } else {
                            document = sOAPCommonOutput.getDoc();
                            resultStr = this.sOAPCommon.getNodeContent(
                                document,
                                "//Result",
                            );
                            if (
                                sOAPCommonOutput.isError() ||
                                CONST_RESULT.equals(resultStr)
                            ) {
                                // TPCからのResultがNGの場合
                                const errInfo = this.sOAPCommon.getNodeContent(
                                    document,
                                    "//ErrorInfomation",
                                );
                                super.warn(
                                    tenantId,
                                    sequenceNo,
                                    MsgKeysConstants.APGMBW0017,
                                    errInfo,
                                );
                            }
                        }
                    } catch (e) {
                        if (SOAPException.isSOAPException(e)) {
                            super.warn(
                                e,
                                tenantId,
                                sequenceNo,
                                MsgKeysConstants.APGMBW0017,
                                JSON.stringify(e),
                            );
                        }
                        throw e; // not catching other exceptions
                    }
                }
            }

            // SO管理共通を呼び出す
            await this.soManagementCommon(
                param,
                apiHandleId,
                handleCode,
                strCapacity,
                executeUserId,
                executeTenantId,
                receivedDate,
                orderType,
                reserveDate,
                isPrivate,
            );
            outputDto = this.returnEdit(
                param,
                handleCode,
                apiHandleId,
                strCapacity,
                errorMessage,
                isPrivate,
                receivedDate,
            );
            return outputDto;
        } catch (e) {
            if (isSQLException(e)) {
                // DBアクセスリトライエラーの場合
                // リターンコード値は各エラー時にて設定済み
                super.error(
                    e,
                    param.tenantId,
                    param.requestHeader.sequenceNo,
                    MsgKeysConstants.APCOME0402,
                    functionType,
                );
                // SO管理共通を呼び出す
                await this.soManagementCommon(
                    param,
                    apiHandleId,
                    handleCode,
                    strCapacity,
                    executeUserId,
                    executeTenantId,
                    receivedDate,
                    orderType,
                    reserveDate,
                    isPrivate,
                );
                outputDto = this.returnEdit(
                    param,
                    handleCode,
                    apiHandleId,
                    "",
                    errorMessage,
                    isPrivate,
                    receivedDate,
                );
                return outputDto;
            } else if (SOAPException.isSOAPException(e)) {
                handleCode = ResultCdConstants.CODE_150801;
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APGMBW0009);
                // SO管理共通を呼び出す
                await this.soManagementCommon(
                    param,
                    apiHandleId,
                    handleCode,
                    strCapacity,
                    executeUserId,
                    executeTenantId,
                    receivedDate,
                    orderType,
                    reserveDate,
                    isPrivate,
                );
                outputDto = this.returnEdit(
                    param,
                    handleCode,
                    apiHandleId,
                    "",
                    errorMessage,
                    isPrivate,
                    receivedDate,
                );
                return outputDto;
            } else {
                // normal error
                handleCode = ResultCdConstants.CODE_999999;
                super.error(
                    e,
                    param.tenantId,
                    param.requestHeader.sequenceNo,
                    MsgKeysConstants.APCOME0401,
                    e.message,
                );
                // SO管理共通を呼び出す
                await this.soManagementCommon(
                    param,
                    apiHandleId,
                    handleCode,
                    strCapacity,
                    executeUserId,
                    executeTenantId,
                    receivedDate,
                    orderType,
                    reserveDate,
                    isPrivate,
                );
                outputDto = this.returnEdit(
                    param,
                    handleCode,
                    apiHandleId,
                    "",
                    errorMessage,
                    isPrivate,
                    receivedDate,
                );
                return outputDto;
            }
        } finally {
            // 予約前オーダ　或いは　即時オーダの場合
            super.debug(
                tenantId,
                sequenceNo,
                MsgKeysConstants.APGMBD0002,
                this.getClassName(),
                "service",
            );
        }
    }

    /**
     * 返却値編集。<BR>
     *
     * @param pram 回線グループ基本容量変更機能インプット
     * @param rtnHandleCode 処理コード
     * @param rtnapiHandleId API処理ID
     * @param rtnCapacity 容量
     * @param errorMessage 変数「エラーメッセージ」
     * @param isPrivate 内部呼出フラグ
     * @param receivedDate REST API共通処理が呼び出されたシステム日付
     * @return LineGroupModBucketAcquisitionOutputDto 回線グループ基本容量変更機能アウトプット
     */
    private returnEdit(
        pram: LineGroupModBucketAcquisitionInputDto,
        rtnHandleCode: string,
        rtnapiHandleId: string,
        rtnCapacity: string,
        errorMessage: string,
        isPrivate: boolean,
        receivedDate: string,
    ): LineGroupModBucketAcquisitionOutputDto {
        const responseHeader: ResponseHeader = {
            sequenceNo: pram.requestHeader.sequenceNo,
            receivedDate,
            processCode: rtnHandleCode,
            apiProcessID: rtnapiHandleId,
        };
        return {
            jsonBody: {
                responseHeader,
                capacity: rtnCapacity,
            },
        };
    }

    /**
     * SO管理共通を呼び出す
     * @param param 入力オブジェクト
     * @param apiHandleId API処理ID
     * @param handleCode 処理コード
     * @param capacity 容量
     * @param executeUserId 実行ユーザID(内部から呼出す時に、nullではない、外部から呼び出す時に、nullである)
     * @param executeTenantId 実行テナントID(内部から呼出す時に、nullではない、外部から呼び出す時に、nullである)
     * @param receivedDate REST API共通処理が呼び出されたシステム日付
     */
    private async soManagementCommon(
        param: LineGroupModBucketAcquisitionInputDto,
        apiHandleId: string,
        handleCode: string,
        capacity: string,
        executeUserId: string,
        executeTenantId: string,
        receivedDate: string,
        orderType: string,
        reserveDate: string,
        isPrivate: boolean,
    ): Promise<void> {
        // SO管理オブジェクトを定義し、初期化をする。
        const soObject = new SOObject();

        // サービスオーダID
        soObject.setServiceOrderId(apiHandleId);
        // 申込日
        soObject.setOrderDate(receivedDate);
        // 完了日
        // 変数「オーダ種別」は「即時オーダ」 或いは「予約実行オーダ」の場合
        if (
            Constants.ORDER_TYPE_0.equals(orderType) ||
            Constants.ORDER_TYPE_2.equals(orderType)
        ) {
            soObject.setExecDate(MvnoUtil.getDateTimeNow());
        } else {
            soObject.setExecDate(null);
        }
        // 予約日
        // 変数「オーダ種別」は「予約前オーダ」の場合
        if (Constants.ORDER_TYPE_1.equals(orderType)) {
            soObject.setReserveDate(reserveDate);
        } else {
            soObject.setReserveDate(null);
        }
        // オーダ種別
        soObject.setOrderType(orderType);
        // オーダステータス
        soObject.setOrderStatus(handleCode);
        // 回線ID
        soObject.setLineId(null);
        // 回線グループID
        soObject.setLineGroupId(getStringParameter(param.lineGroupId));
        // 所属テナントID
        soObject.setTenantId(param.tenantId);
        // 実行ユーザID
        soObject.setExecuteUserId(executeUserId);
        // 実行テナントID
        soObject.setExecuteTenantId(executeTenantId);
        // 機能種別
        soObject.setFunctionType(param.requestHeader.functionType);
        // 操作区分
        soObject.setOperationDivision("");
        // 変更前プランID
        soObject.setChangeOldplanId("");
        // 変更後プランID
        soObject.setChangeNewplanId("");
        // 回線所属変更回線ID
        soObject.setChangePlanId("");
        // 基本容量
        soObject.setBasicCapacity(capacity);
        // オプションプランID
        soObject.setOptionPlanId("");
        // 内部呼び出し
        soObject.setInternalFlag(isPrivate);
        // REST電文
        // 変数「オーダ種別」は「予約前オーダ」の場合
        if (Constants.ORDER_TYPE_1.equals(orderType)) {
            soObject.setRestMessage(MvnoUtil.getRestMessage(param));
        } else {
            soObject.setRestMessage(null);
        }
        await this.soCommon.soCommon(soObject);
    }
}
