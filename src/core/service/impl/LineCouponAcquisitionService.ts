import config from "config";
import RESTCommon from "@/core/common/RESTCommon";
import { IFService } from "../IFService";
import LineCouponAcquisitionInputDto from "@/core/dto/LineCouponAcquisitionInputDto";
import LineCouponAcquisitionOutputDto from "@/core/dto/LineCouponAcquisitionOutputDto";
import APICommonDAO from "@/core/dao/APICommonDAO";
import SOAPCommon from "@/core/common/SOAPCommon";
import SOAPCommonCoupon from "@/core/common/SOAPCommonCoupon";
import APILinesDAO from "@/core/dao/APILinesDAO";
import SOCommon from "@/core/common/SOCommon";
import TenantManage from "@/core/common/TenantManage";
import ApiCommon from "@/core/common/ApiCommon";
import MvnoUtil from "@/core/common/MvnoUtil";
import Check from "@/core/common/Check";
import ResultCdConstants from "@/core/constant/ResultCdConstants";
import { isSQLException } from "@/helpers/queryHelper";
import SOObject from "@/core/dto/SOObject";
import Constants from "@/core/constant/Constants";
import MsgKeysConstants from "@/core/constant/MsgKeysConstants";
import SOAPCommonOutputDto from "@/core/dto/SOAPCommonOutputDto";
import ParameterName from "@/core/dto/ParameterName";
import TenantPlansEntity from "@/core/entity/TenantPlansEntity";
import { TenantsEntity } from "@/core/entity/TenantsEntity";
import PlansEntity from "@/core/entity/PlansEntity";
import CheckUtil from "@/core/common/CheckUtil";
import SOAPException from "@/types/soapException";
import ResponseHeader from "@/core/dto/ResponseHeader";
import { getConfig } from "@/helpers/configHelper";
import { getStringParameter } from "@/types/parameter.string";

export default class LineCouponAcquisitionService
    extends RESTCommon
    implements
        IFService<
            LineCouponAcquisitionInputDto,
            LineCouponAcquisitionOutputDto
        >
{
    /** 定数 「回線グループID」 */
    private static readonly CONST_LINENO = "回線番号";

    /** 定数 「卸ポータルグループプランID」 */
    private static readonly CONST_POTALPLANID = "卸ポータルプランID";

    /** 定数 「卸ポータルオプショングループプランID」 */
    private static readonly CONST_OPTIONPLANID = "卸ポータルオプションプランID";

    /** 定数 「クーポンON/OFF状態」 */
    private static readonly CONST_COUPONONOFF = "クーポンON/OFF状態";

    /** 定数 「更新サービスパターン」 */
    private static readonly CONST_REFSERVICEPATTERN = "更新サービスパターン";

    /** 定数 「ポリシーID」 */
    private static readonly CONST_POLICYID = "ポリシーID";

    /** 定数 「予約日」 */
    private static readonly CONST_RESERVEDATE = "予約日";

    /** API共通DAO */

    /** SOAP共通  */
    private soapCommon = new SOAPCommon(this.request, this.context);

    private soapCommonCoupon = new SOAPCommonCoupon(this.request, this.context);

    private aPILinesDAO = new APILinesDAO(this.request, this.context);
    /**
     * SO管理共通DAO
     */
    private soCommon = new SOCommon(this.request, this.context);

    private tenantManage = new TenantManage(this.request, this.context);
    /** API共通処理 */
    private apiCommon = new ApiCommon(this.request, this.context);

    /**
     * 予約実行日時単位(予約機能)
     */
    private reservationDateExecutionUnits: string = getConfig(
        "ReservationDateExecutionUnits",
    );

    /**
     * 予約可能制限日数(予約機能)
     */
    private reservationsLimitDays: string = getConfig("ReservationsLimitDays");

    /**
     * 回線クーポンオン・オフ機能。<BR>
     *
     * <PRE>
     * SOAP APIにより、受信したパラメータの回線番号をキーにして回線クーポンON/OFF状態更新処理をを行う
     * </PRE>
     *
     * @param param 回線クーポンオン・オフインプット
     * @param isPrivate 内部呼出フラグ
     * @param params 可変長さ引数(内部から呼出す時に使用するパラメータ)
     * @return LineCouponAcquisitionOutputDto 回線クーポンオン・オフ機能アウトプット
     */

    public async service(
        param: LineCouponAcquisitionInputDto,
        isPrivate: boolean,
        ...params: any[]
    ): Promise<LineCouponAcquisitionOutputDto> {
        // デバッグログ出力
        // super.debug(
        //     param.tenantId,
        //     param.requestHeader.sequenceNo,
        //     MsgKeysConstants.APLCPD0001,
        //     this.getClassName(),
        //     "service",
        //     JSON.stringify(param),
        // );

        // REST API共通処理が呼び出されたシステム日付
        const receivedDate = MvnoUtil.getDateTimeNow();

        // const responseHeader = this.context.responseHeader;
        // responseHeader.sequenceNo = param.requestHeader.sequenceNo;
        // responseHeader.receivedDate = receivedDate

        const responseHeader: ResponseHeader = {
            sequenceNo: param.requestHeader.sequenceNo,
            receivedDate,
            processCode: "",
            apiProcessID: "",
        };
        // 出力対象生成
        const outputDto: LineCouponAcquisitionOutputDto = {
            jsonBody: {
                responseHeader,
            },
        };
        // 変数「エラーメッセージ」
        const errorMessage: string = null;
        // 変数「実行ユーザID」を定義する
        const executeUserId: string = null;
        // 変数「実行テナントID」を定義する
        const executeTenantId: string = null;
        // 予約日
        const reserveDate: string = param.reserve_date;
        // 予約フラグ
        const reserveFlg: boolean = param.reserve_flag;
        // ＳＯ－ＩＤ
        const reserveSoId: string = param.reserve_soId;
        // 変数「オーダ種別」初期化
        const orderType = Check.checkOrderType(
            reserveDate,
            reserveFlg,
            reserveSoId,
        );
        let handleCode: string = ResultCdConstants.CODE_000000;
        try {
            responseHeader.apiProcessID =
                this.context.responseHeader.apiProcessID;
            // 業務チェック
            // オーダ種別チェック
            // 変数「オーダ種別」より判定する
            if (Constants.ORDER_TYPE_9.equals(orderType)) {
                super.warn(
                    param.tenantId,
                    param.requestHeader.sequenceNo,
                    MsgKeysConstants.APLCPW0011,
                );
                // 返却値を設定し、返却
                responseHeader.processCode = ResultCdConstants.CODE_040111;
                return this.returnEdit(outputDto, isPrivate, errorMessage);
            }
            // 予約実行オーダの場合
            if (Constants.ORDER_TYPE_2.equals(orderType)) {
                // APサーバの複数IP取得
                const localIpList = MvnoUtil.getLocalIpList();
                // 予約実行可否チェック
                if (!this.context.isInternalRequest) {
                    // ログ出力
                    super.error(
                        param.tenantId,
                        param.requestHeader.sequenceNo,
                        MsgKeysConstants.APCOME0401,
                        "予約実行できませんでした。クライアントIP:" +
                            String(params[2]) +
                            ", APサーバーIP：" +
                            MvnoUtil.getOutputList(localIpList),
                    );
                    // 返却値を設定し、返却);
                    responseHeader.processCode = ResultCdConstants.CODE_999999;
                    // 返却値編集
                    return this.returnEdit(outputDto, isPrivate, errorMessage);
                }
            }
            // １ 回線番号フォーマットチェック
            if (!Check.checkLineNo(param.lineNo)) {
                // ログ出力
                super.warn(
                    param.tenantId,
                    param.requestHeader.sequenceNo,
                    MsgKeysConstants.APLCPW0001,
                    LineCouponAcquisitionService.CONST_LINENO,
                    param.lineNo,
                );
                // SO管理共通パラメータ設定
                await this.soManagementCommon(
                    param,
                    responseHeader.apiProcessID,
                    executeUserId,
                    executeTenantId,
                    ResultCdConstants.CODE_040101,
                    receivedDate,
                    orderType,
                    reserveDate,
                    isPrivate,
                );
                // 返却値を設定し、返却
                responseHeader.processCode = ResultCdConstants.CODE_040101;
                return this.returnEdit(outputDto, isPrivate, errorMessage);
            }

            // ２ 卸ポータルプランIDフォーマットチェック
            if (!Check.checkPlanIDFmt(getStringParameter(param.potalPlanID))) {
                // ログ出力
                super.warn(
                    param.tenantId,
                    param.requestHeader.sequenceNo,
                    MsgKeysConstants.APLCPW0001,
                    LineCouponAcquisitionService.CONST_POTALPLANID,
                    getStringParameter(param.potalPlanID),
                );
                // SO管理共通パラメータ設定
                await this.soManagementCommon(
                    param,
                    responseHeader.apiProcessID,
                    executeUserId,
                    executeTenantId,
                    ResultCdConstants.CODE_040101,
                    receivedDate,
                    orderType,
                    reserveDate,
                    isPrivate,
                );
                // 返却値を設定し、返却
                responseHeader.processCode = ResultCdConstants.CODE_040101;
                return this.returnEdit(outputDto, isPrivate, errorMessage);
            }

            // ３ 卸ポータルオプションプランIDフォーマットチェック
            if (!Check.checkPlanIDFmt(getStringParameter(param.optionPlanId))) {
                // ログ出力
                super.warn(
                    param.tenantId,
                    param.requestHeader.sequenceNo,
                    MsgKeysConstants.APLCPW0001,
                    LineCouponAcquisitionService.CONST_OPTIONPLANID,
                    getStringParameter(param.optionPlanId),
                );

                // SO管理共通パラメータ設定
                await this.soManagementCommon(
                    param,
                    responseHeader.apiProcessID,
                    executeUserId,
                    executeTenantId,
                    ResultCdConstants.CODE_040101,
                    receivedDate,
                    orderType,
                    reserveDate,
                    isPrivate,
                );
                // 返却値を設定し、返却
                responseHeader.processCode = ResultCdConstants.CODE_040101;
                return this.returnEdit(outputDto, isPrivate, errorMessage);
            }

            if (Constants.ORDER_TYPE_0.equals(orderType)) {
                // ４ クーポンON OFF状態フォーマットチェック
                if (!Check.checkCouponOnOff(getStringParameter(param.couponOnOff))) {
                    // ログ出力
                    super.warn(
                        param.tenantId,
                        param.requestHeader.sequenceNo,
                        MsgKeysConstants.APLCPW0001,
                        LineCouponAcquisitionService.CONST_COUPONONOFF,
                        getStringParameter(param.couponOnOff),
                    );

                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(
                        param,
                        responseHeader.apiProcessID,
                        executeUserId,
                        executeTenantId,
                        ResultCdConstants.CODE_040101,
                        receivedDate,
                        orderType,
                        reserveDate,
                        isPrivate,
                    );
                    // 返却値を設定し、返却
                    responseHeader.processCode = ResultCdConstants.CODE_040101;
                    return this.returnEdit(outputDto, isPrivate, errorMessage);
                }
            }

            // 予約前オーダ,予約実行オーダ
            if (
                Constants.ORDER_TYPE_1.equals(orderType) ||
                Constants.ORDER_TYPE_2.equals(orderType)
            ) {
                // ４ 予約日フォーマットチェック(予約前オーダ,予約実行オーダ)
                if (!Check.checkReserveDateFmt(reserveDate)) {
                    super.warn(
                        param.tenantId,
                        param.requestHeader.sequenceNo,
                        MsgKeysConstants.APLCPW0001,
                        LineCouponAcquisitionService.CONST_RESERVEDATE,
                        reserveDate,
                    );
                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(
                        param,
                        responseHeader.apiProcessID,
                        executeUserId,
                        executeTenantId,
                        ResultCdConstants.CODE_040101,
                        receivedDate,
                        orderType,
                        reserveDate,
                        isPrivate,
                    );
                    // 返却値を設定し、返却
                    responseHeader.processCode = ResultCdConstants.CODE_040101;
                    return this.returnEdit(outputDto, isPrivate, errorMessage);
                }
            }

            // 予約前オーダ
            if (Constants.ORDER_TYPE_1.equals(orderType)) {
                // ５ 予約日と投入日相関チェック
                if (
                    !Check.checkReserveDateOrderDate(receivedDate, reserveDate)
                ) {
                    super.warn(
                        param.tenantId,
                        param.requestHeader.sequenceNo,
                        MsgKeysConstants.APLCPW0012,
                        reserveDate,
                        receivedDate,
                    );
                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(
                        param,
                        responseHeader.apiProcessID,
                        executeUserId,
                        executeTenantId,
                        ResultCdConstants.CODE_040106,
                        receivedDate,
                        orderType,
                        reserveDate,
                        isPrivate,
                    );
                    // 返却値を設定し、返却
                    responseHeader.processCode = ResultCdConstants.CODE_040106;
                    return this.returnEdit(outputDto, isPrivate, errorMessage);
                }

                // ６ 予約実行日時単位フォーマットチェック
                if (
                    !Check.checkReservationDateExecutionUnitsFmt(
                        this.reservationDateExecutionUnits,
                    )
                ) {
                    super.warn(
                        param.tenantId,
                        param.requestHeader.sequenceNo,
                        MsgKeysConstants.APLCPW0013,
                        this.reservationDateExecutionUnits,
                    );
                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(
                        param,
                        responseHeader.apiProcessID,
                        executeUserId,
                        executeTenantId,
                        ResultCdConstants.CODE_040107,
                        receivedDate,
                        orderType,
                        reserveDate,
                        isPrivate,
                    );
                    // 返却値を設定し、返却
                    responseHeader.processCode = ResultCdConstants.CODE_040107;
                    return this.returnEdit(outputDto, isPrivate, errorMessage);
                }

                // ７ 予約実行日時単位と予約日相関チェックチェック
                if (
                    !Check.checkReservationDateExecutionUnits(
                        this.reservationDateExecutionUnits,
                        reserveDate,
                    )
                ) {
                    super.warn(
                        param.tenantId,
                        param.requestHeader.sequenceNo,
                        MsgKeysConstants.APLCPW0014,
                        this.reservationDateExecutionUnits,
                        reserveDate,
                    );
                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(
                        param,
                        responseHeader.apiProcessID,
                        executeUserId,
                        executeTenantId,
                        ResultCdConstants.CODE_040108,
                        receivedDate,
                        orderType,
                        reserveDate,
                        isPrivate,
                    );
                    // 返却値を設定し、返却
                    responseHeader.processCode = ResultCdConstants.CODE_040108;
                    return this.returnEdit(outputDto, isPrivate, errorMessage);
                }

                // ８ 予約可能制限日数フォーマットチェック
                if (
                    !Check.checkReservationsLimitDaysFmt(
                        this.reservationsLimitDays,
                    )
                ) {
                    super.warn(
                        param.tenantId,
                        param.requestHeader.sequenceNo,
                        MsgKeysConstants.APLCPW0015,
                        this.reservationsLimitDays,
                    );
                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(
                        param,
                        responseHeader.apiProcessID,
                        executeUserId,
                        executeTenantId,
                        ResultCdConstants.CODE_040109,
                        receivedDate,
                        orderType,
                        reserveDate,
                        isPrivate,
                    );
                    // 返却値を設定し、返却
                    responseHeader.processCode = ResultCdConstants.CODE_040109;
                    return this.returnEdit(outputDto, isPrivate, errorMessage);
                }

                // ９ 予約可能制限日数範囲チェック
                if (
                    !Check.checkReservationsLimitDays(
                        this.reservationsLimitDays,
                        reserveDate,
                    )
                ) {
                    super.warn(
                        param.tenantId,
                        param.requestHeader.sequenceNo,
                        MsgKeysConstants.APLCPW0016,
                        this.reservationsLimitDays,
                        reserveDate,
                    );
                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(
                        param,
                        responseHeader.apiProcessID,
                        executeUserId,
                        executeTenantId,
                        ResultCdConstants.CODE_040110,
                        receivedDate,
                        orderType,
                        reserveDate,
                        isPrivate,
                    );
                    // 返却値を設定し、返却
                    responseHeader.processCode = ResultCdConstants.CODE_040110;
                    return this.returnEdit(outputDto, isPrivate, errorMessage);
                }
            }

            let checkResult: [boolean, string] = null;
            try {
                checkResult = await this.tenantManage.doCheck(
                    param.lineNo,
                    param.tenantId,
                );
            } catch (e) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    handleCode = ResultCdConstants.CODE_040102;
                }
                throw e;
            }
            if (!checkResult[0]) {
                // ログ出力
                super.warn(
                    param.tenantId,
                    param.requestHeader.sequenceNo,
                    MsgKeysConstants.APLCPW0002,
                    param.lineNo,
                    param.tenantId,
                );

                // SO管理共通パラメータ設定
                await this.soManagementCommon(
                    param,
                    responseHeader.apiProcessID,
                    executeUserId,
                    executeTenantId,
                    ResultCdConstants.CODE_040102,
                    receivedDate,
                    orderType,
                    reserveDate,
                    isPrivate,
                );
                // 返却値を設定し、返却
                responseHeader.processCode = ResultCdConstants.CODE_040102;
                return this.returnEdit(outputDto, isPrivate, errorMessage);
            }

            // ７ プランID所属チェック
            let tenantPlansList: TenantPlansEntity[] = null;
            try {
                tenantPlansList = await this.apiCommonDAO.getTenantPlans(
                    getStringParameter(param.potalPlanID),
                    param.tenantId,
                );
            } catch (e) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    handleCode = ResultCdConstants.CODE_040103;
                }
                throw e;
            }
            if (tenantPlansList === null || tenantPlansList.length === 0) {
                // ログ出力
                super.warn(
                    param.tenantId,
                    param.requestHeader.sequenceNo,
                    MsgKeysConstants.APLCPW0003,
                    getStringParameter(param.potalPlanID),
                    param.tenantId,
                );

                // SO管理共通パラメータ設定
                await this.soManagementCommon(
                    param,
                    responseHeader.apiProcessID,
                    executeUserId,
                    executeTenantId,
                    ResultCdConstants.CODE_040103,
                    receivedDate,
                    orderType,
                    reserveDate,
                    isPrivate,
                );
                // 返却値を設定し、返却
                responseHeader.processCode = ResultCdConstants.CODE_040103;
                return this.returnEdit(outputDto, isPrivate, errorMessage);
            }

            // ８ 回線グループID所属チェック
            let groupId: string = null;
            try {
                groupId = await this.aPILinesDAO.getLinesGroupId(
                    param.lineNo.toString(),
                );
            } catch (e) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    handleCode = ResultCdConstants.CODE_040104;
                }
                throw e;
            }

            if (groupId != null && !groupId.equals("")) {
                // ログ出力
                super.warn(
                    param.tenantId,
                    param.requestHeader.sequenceNo,
                    MsgKeysConstants.APLCPW0004,
                );

                // SO管理共通パラメータ設定
                await this.soManagementCommon(
                    param,
                    responseHeader.apiProcessID,
                    executeUserId,
                    executeTenantId,
                    ResultCdConstants.CODE_040104,
                    receivedDate,
                    orderType,
                    reserveDate,
                    isPrivate,
                );
                // 返却値を設定し、返却
                responseHeader.processCode = ResultCdConstants.CODE_040104;
                return this.returnEdit(outputDto, isPrivate, errorMessage);
            }

            // 社内テナント種別を取得する
            let tenantsEntity: TenantsEntity = null;
            try {
                tenantsEntity = await this.apiCommonDAO.getTenants(
                    param.tenantId,
                );
            } catch (e) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    handleCode = ResultCdConstants.CODE_040112;
                }
                throw e;
            }

            if (tenantsEntity === null) {
                super.warn(
                    param.tenantId,
                    param.requestHeader.sequenceNo,
                    MsgKeysConstants.APLCPW0017,
                );
                // SO管理共通パラメータ設定
                await this.soManagementCommon(
                    param,
                    responseHeader.apiProcessID,
                    executeUserId,
                    executeTenantId,
                    ResultCdConstants.CODE_040112,
                    receivedDate,
                    orderType,
                    reserveDate,
                    isPrivate,
                );
                // 返却値を設定し、返却
                responseHeader.processCode = ResultCdConstants.CODE_040112;
                return this.returnEdit(outputDto, isPrivate, errorMessage);
            } else if (Number(1) !== tenantsEntity.tenantType) {
                // プランIDを取得する
                let lineUsePlanId: string = null;
                try {
                    lineUsePlanId = await this.aPILinesDAO.getLineUsingPlan(
                        param.lineNo,
                    );
                } catch (e) {
                    if (isSQLException(e)) {
                        // DBアクセスリトライエラーの場合
                        handleCode = ResultCdConstants.CODE_040113;
                    }
                    throw e;
                }

                // 変数「回線利用プランID」より判定する
                if (!getStringParameter(param.potalPlanID).equals(lineUsePlanId)) {
                    super.warn(
                        param.tenantId,
                        param.requestHeader.sequenceNo,
                        MsgKeysConstants.APLCPW0018,
                        getStringParameter(param.potalPlanID),
                    );
                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(
                        param,
                        responseHeader.apiProcessID,
                        executeUserId,
                        executeTenantId,
                        ResultCdConstants.CODE_040113,
                        receivedDate,
                        orderType,
                        reserveDate,
                        isPrivate,
                    );
                    // 返却値を設定し、返却
                    responseHeader.processCode = ResultCdConstants.CODE_040113;
                    return this.returnEdit(outputDto, isPrivate, errorMessage);
                }
            }

            // ９ オプションプランID利用チェック
            let planOptPlansList: string = null;
            try {
                planOptPlansList =
                    await this.apiCommonDAO.getCheckedPlanOptionPlans(
                        getStringParameter(param.optionPlanId),
                        getStringParameter(param.potalPlanID),
                        "04",
                    );
            } catch (e) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    handleCode = ResultCdConstants.CODE_040105;
                }
                throw e;
            }
            if (CheckUtil.checkIsNotNull(planOptPlansList)) {
                // ログ出力
                super.warn(
                    param.tenantId,
                    param.requestHeader.sequenceNo,
                    MsgKeysConstants.APLCPW0008,
                    getStringParameter(param.optionPlanId),
                );
                // SO管理共通パラメータ設定
                await this.soManagementCommon(
                    param,
                    responseHeader.apiProcessID,
                    executeUserId,
                    executeTenantId,
                    ResultCdConstants.CODE_040105,
                    receivedDate,
                    orderType,
                    reserveDate,
                    isPrivate,
                );
                // 返却値を設定し、返却
                responseHeader.processCode = ResultCdConstants.CODE_040105;
                return this.returnEdit(outputDto, isPrivate, errorMessage);
            }

            // 廃止オーダ受付中チェックを実施する
            let targetDate: string;

            if (Constants.ORDER_TYPE_0.equals(orderType)) {
                targetDate = receivedDate.substring(0, 10).replace(/\//g, "");
            } else {
                targetDate = reserveDate.substring(0, 10).replace(/\//g, "");
            }

            let checkAbolishSoResult: boolean = null;
            try {
                checkAbolishSoResult = await this.apiCommon.checkAbolishSo(
                    param.lineNo,
                    targetDate,
                );
            } catch (e) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    handleCode = ResultCdConstants.CODE_040114;
                }
                throw e;
            }

            // 変数「廃止オーダ受付中判定結果」より判定する
            if (!checkAbolishSoResult) {
                // ログ出力
                super.warn(
                    param.tenantId,
                    param.requestHeader.sequenceNo,
                    MsgKeysConstants.APLCPW0019,
                    param.lineNo,
                );
                // SO管理共通パラメータ設定
                await this.soManagementCommon(
                    param,
                    responseHeader.apiProcessID,
                    executeUserId,
                    executeTenantId,
                    ResultCdConstants.CODE_040114,
                    receivedDate,
                    orderType,
                    reserveDate,
                    isPrivate,
                );
                // 返却値を設定し、返却
                responseHeader.processCode = ResultCdConstants.CODE_040114;
                return this.returnEdit(outputDto, isPrivate, errorMessage);
            }

            // 利用状態(サスペンド中)のチェック
            let checkLineUsageStatusResult: boolean;
            try {
                checkLineUsageStatusResult =
                    await this.apiCommon.checkLineSuspend(param.lineNo);
            } catch (e) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    handleCode = ResultCdConstants.CODE_040115;
                }
                throw e;
            }
            // 変数「利用状態判定結果」より判定する
            if (!checkLineUsageStatusResult) {
                // ログ出力
                super.warn(
                    param.tenantId,
                    param.requestHeader.sequenceNo,
                    MsgKeysConstants.APLCPW0020,
                    param.lineNo,
                );
                // SO管理共通パラメータ設定
                await this.soManagementCommon(
                    param,
                    responseHeader.apiProcessID,
                    executeUserId,
                    executeTenantId,
                    ResultCdConstants.CODE_040115,
                    receivedDate,
                    orderType,
                    reserveDate,
                    isPrivate,
                );
                // 返却値を設定し、返却
                responseHeader.processCode = ResultCdConstants.CODE_040115;
                return this.returnEdit(outputDto, isPrivate, errorMessage);
            }

            // １ 更新サービスパターンとポリシーIDを取得する
            let plansList: PlansEntity[] = null;
            try {
                plansList = await this.apiCommonDAO.getPlans(getStringParameter(param.potalPlanID));
            } catch (e) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    handleCode = ResultCdConstants.CODE_040201;
                }
                throw e;
            }

            if (plansList === null || plansList.length === 0) {
                // ログ出力
                super.warn(
                    param.tenantId,
                    param.requestHeader.sequenceNo,
                    MsgKeysConstants.APLCPW0005,
                );

                // SO管理共通パラメータ設定
                await this.soManagementCommon(
                    param,
                    responseHeader.apiProcessID,
                    executeUserId,
                    executeTenantId,
                    ResultCdConstants.CODE_040201,
                    receivedDate,
                    orderType,
                    reserveDate,
                    isPrivate,
                );
                // 返却値を設定し、返却
                responseHeader.processCode = ResultCdConstants.CODE_040201;

                return this.returnEdit(outputDto, isPrivate, errorMessage);
            }

            // ２ 更新サービスパターンとポリシーIDのフォーマットチェック
            // ２．１ 更新サービスパターンのフォーマットチェック
            if (
                CheckUtil.checkIsNull(plansList[0].tpcServicePattern) ||
                (plansList[0].tpcServicePattern !== 1 &&
                    plansList[0].tpcServicePattern !== 4 &&
                    plansList[0].tpcServicePattern !== 6 &&
                    plansList[0].tpcServicePattern !== 2)
            ) {
                // ログ出力
                super.warn(
                    param.tenantId,
                    param.requestHeader.sequenceNo,
                    MsgKeysConstants.APLCPW0006,
                    LineCouponAcquisitionService.CONST_REFSERVICEPATTERN,
                    plansList[0].tpcServicePattern,
                );
                // SO管理共通パラメータ設定
                await this.soManagementCommon(
                    param,
                    responseHeader.apiProcessID,
                    executeUserId,
                    executeTenantId,
                    ResultCdConstants.CODE_040202,
                    receivedDate,
                    orderType,
                    reserveDate,
                    isPrivate,
                );
                // 返却値を設定し、返却
                responseHeader.processCode = ResultCdConstants.CODE_040202;

                return this.returnEdit(outputDto, isPrivate, errorMessage);
            }

            // ２．２ ポリシーIDのフォーマットチェック
            if (!Check.checkPolicyId(String(plansList[0].policyId))) {
                // ログ出力
                super.warn(
                    param.tenantId,
                    param.requestHeader.sequenceNo,
                    MsgKeysConstants.APLCPW0006,
                    LineCouponAcquisitionService.CONST_POLICYID,
                    plansList[0].policyId,
                );
                // SO管理共通パラメータ設定
                await this.soManagementCommon(
                    param,
                    responseHeader.apiProcessID,
                    executeUserId,
                    executeTenantId,
                    ResultCdConstants.CODE_040202,
                    receivedDate,
                    orderType,
                    reserveDate,
                    isPrivate,
                );
                // 返却値を設定し、返却
                responseHeader.processCode = ResultCdConstants.CODE_040202;
                return this.returnEdit(outputDto, isPrivate, errorMessage);
            }

            // ２．３ オーダ種別より判断する
            if (
                Constants.ORDER_TYPE_0.equals(orderType) ||
                Constants.ORDER_TYPE_2.equals(orderType)
            ) {
                // ３ SO電文作成した、SOAPのAPIを接続する
                let soapCommonOutputDto: SOAPCommonOutputDto = null;

                // TPC情報を取得する。
                let tpcConnectionResults: [boolean, string, string] = null;
                try {
                    tpcConnectionResults =
                        await this.tenantManage.checkTpcConnection(
                            param.tenantId,
                        );
                } catch (e) {
                    if (isSQLException(e)) {
                        // DBアクセスリトライエラーの場合
                        handleCode = ResultCdConstants.CODE_040310;
                    }
                    throw e;
                }
                // 変数「TPC情報取得結果」より判断する
                if (!tpcConnectionResults[0]) {
                    // ログ出力
                    super.warn(
                        param.tenantId,
                        param.requestHeader.sequenceNo,
                        MsgKeysConstants.APLCPW0010,
                        param.tenantId,
                    );

                    // SO管理共通パラメータ設定
                    await this.soManagementCommon(
                        param,
                        responseHeader.apiProcessID,
                        executeUserId,
                        executeTenantId,
                        ResultCdConstants.CODE_040310,
                        receivedDate,
                        orderType,
                        reserveDate,
                        isPrivate,
                    );
                    // 返却値を設定し、返却
                    responseHeader.processCode = ResultCdConstants.CODE_040310;
                    return this.returnEdit(outputDto, isPrivate, errorMessage);
                }

                let couponBefore: string = "";
                let couponAfter: string = "";

                // 更新サービスパターンにより処理判定
                if (
                    plansList[0].tpcServicePattern === 1 ||
                    plansList[0].tpcServicePattern === 2
                ) {
                    if (parseInt(getStringParameter(param.couponOnOff), 10) === 0) {
                        // ３．１．１ 入力パラメータ．「クーポンON/OFF状態」＝0の場合
                        couponBefore = "0";
                        couponAfter = "1";
                    } else if (parseInt(getStringParameter(param.couponOnOff), 10) === 1) {
                        // ３．１．２ 入力パラメータ．「クーポンON/OFF状態」＝1の場合
                        couponBefore = "1";
                        couponAfter = "0";
                    }

                    // ３．１．１．１ SOAP APIにより、サービスプロファイル番号に対するデータを更新する
                    // SOAP共通を呼び出す
                    try {
                        soapCommonOutputDto = await this.sendSoapApiToTpc(
                            param.lineNo,
                            couponBefore,
                            couponAfter,
                            param.tenantId,
                            param.requestHeader.sequenceNo,
                            tpcConnectionResults[1],
                            true,
                        );
                    } catch (e) {
                        if (SOAPException.isSOAPException(e)) {
                            // ログ出力
                            super.warn(
                                e,
                                param.tenantId,
                                param.requestHeader.sequenceNo,
                                MsgKeysConstants.APLCPW0007,
                            );
                            // SO管理共通パラメータ設定
                            await this.soManagementCommon(
                                param,
                                responseHeader.apiProcessID,
                                executeUserId,
                                executeTenantId,
                                ResultCdConstants.CODE_040401,
                                receivedDate,
                                orderType,
                                reserveDate,
                                isPrivate,
                            );
                            responseHeader.processCode =
                                ResultCdConstants.CODE_040401;

                            return this.returnEdit(
                                outputDto,
                                isPrivate,
                                errorMessage,
                            );
                        } else {
                            throw e;
                        }
                    }

                    if (
                        !ResultCdConstants.CODE_000000.equals(
                            soapCommonOutputDto.getProcessCode(),
                        )
                    ) {
                        // ログ出力
                        super.warn(
                            param.tenantId,
                            param.requestHeader.sequenceNo,
                            MsgKeysConstants.APLCPW0007,
                        );
                        // SO管理共通パラメータ設定
                        await this.soManagementCommon(
                            param,
                            responseHeader.apiProcessID,
                            executeUserId,
                            executeTenantId,
                            ResultCdConstants.CODE_040401,
                            receivedDate,
                            orderType,
                            reserveDate,
                            isPrivate,
                        );
                        // 返却値を設定し、返却
                        responseHeader.processCode =
                            ResultCdConstants.CODE_040401;

                        return this.returnEdit(
                            outputDto,
                            isPrivate,
                            errorMessage,
                        );
                    }

                    // セカンダリTPCへSOAP連携を行う
                    if (tpcConnectionResults[2] !== null) {
                        // セカンダリTPCが設定されている場合に連携
                        try {
                            soapCommonOutputDto = await this.sendSoapApiToTpc(
                                param.lineNo,
                                couponBefore,
                                couponAfter,
                                param.tenantId,
                                param.requestHeader.sequenceNo,
                                tpcConnectionResults[2],
                                false,
                            );

                            if (
                                ResultCdConstants.CODE_000951.equals(
                                    soapCommonOutputDto.getProcessCode(),
                                )
                            ) {
                                // SG取得NGの場合
                                // ログ出力
                                super.warn(
                                    param.tenantId,
                                    param.requestHeader.sequenceNo,
                                    MsgKeysConstants.APLCPW0021,
                                    "SG取得NG",
                                );
                            } else {
                                // TPCからのResultでNGの場合
                                const resultStr =
                                    this.soapCommonCoupon.getNodeContent(
                                        soapCommonOutputDto.getDoc(),
                                        "//Result",
                                    );
                                if (
                                    soapCommonOutputDto.isError() ||
                                    "NG".equals(resultStr)
                                ) {
                                    // ログ出力
                                    const errInfo =
                                        this.soapCommonCoupon.getNodeContent(
                                            soapCommonOutputDto.getDoc(),
                                            "//ErrorInfomation",
                                        );
                                    super.warn(
                                        param.tenantId,
                                        param.requestHeader.sequenceNo,
                                        MsgKeysConstants.APLCPW0021,
                                        errInfo,
                                    );
                                }
                            }
                        } catch (e) {
                            if (SOAPException.isSOAPException(e)) {
                                // ログ出力
                                super.warn(
                                    e,
                                    param.tenantId,
                                    param.requestHeader.sequenceNo,
                                    MsgKeysConstants.APLCPW0021,
                                    e.toString(),
                                );
                            } else {
                                throw e;
                            }
                        }
                    }
                }

                // ３．２ 変数「更新サービスパターン」＝4の場合、下記処理を行う
                if (
                    plansList[0].tpcServicePattern === 4 ||
                    plansList[0].tpcServicePattern === 6
                ) {
                    if (parseInt(getStringParameter(param.couponOnOff), 10) === 0) {
                        // ３．１．１ 入力パラメータ．「クーポンON/OFF状態」＝0の場合
                        couponAfter = "0";
                    } else if (parseInt(getStringParameter(param.couponOnOff), 10) === 1) {
                        // ３．１．２ 入力パラメータ．「クーポンON/OFF状態」＝1の場合
                        couponAfter = "1";
                    }

                    // ３．２．１．１ SOAP APIにより、データを更新する
                    const paramArray5: string[][] = [
                        ["phone_number", param.lineNo],
                        ["policy_number", plansList[0].policyId.toString()],
                        ["aply_srv", couponAfter],
                    ];
                    try {
                        // SOAP共通を呼び出す
                        soapCommonOutputDto = await this.getSoapResult(
                            "serviceProfileRequestLite",
                            "Mod",
                            "",
                            paramArray5,
                            param.tenantId,
                            param.requestHeader.sequenceNo,
                            tpcConnectionResults[1],
                            true,
                        );
                    } catch (e) {
                        if (SOAPException.isSOAPException(e)) {
                            // ログ出力
                            super.warn(
                                e,
                                param.tenantId,
                                param.requestHeader.sequenceNo,
                                MsgKeysConstants.APLCPW0007,
                            );
                            // SO管理共通パラメータ設定
                            await this.soManagementCommon(
                                param,
                                responseHeader.apiProcessID,
                                executeUserId,
                                executeTenantId,
                                ResultCdConstants.CODE_040401,
                                receivedDate,
                                orderType,
                                reserveDate,
                                isPrivate,
                            );
                            responseHeader.processCode =
                                ResultCdConstants.CODE_040401;
                            return this.returnEdit(
                                outputDto,
                                isPrivate,
                                errorMessage,
                            );
                        } else {
                            throw e;
                        }
                    }

                    if (
                        !ResultCdConstants.CODE_000000.equals(
                            soapCommonOutputDto.getProcessCode(),
                        )
                    ) {
                        // ログ出力
                        super.warn(
                            param.tenantId,
                            param.requestHeader.sequenceNo,
                            MsgKeysConstants.APLCPW0007,
                        );
                        // SO管理共通パラメータ設定
                        await this.soManagementCommon(
                            param,
                            responseHeader.apiProcessID,
                            executeUserId,
                            executeTenantId,
                            ResultCdConstants.CODE_040401,
                            receivedDate,
                            orderType,
                            reserveDate,
                            isPrivate,
                        );
                        // 返却値を設定し、返却
                        responseHeader.processCode =
                            ResultCdConstants.CODE_040401;

                        return this.returnEdit(
                            outputDto,
                            isPrivate,
                            errorMessage,
                        );
                    }

                    // セカンダリTPCへSOAP連携を行う
                    if (tpcConnectionResults[2] != null) {
                        // セカンダリTPCが設定されている場合に連携
                        try {
                            soapCommonOutputDto = await this.getSoapResult(
                                "serviceProfileRequestLite",
                                "Mod",
                                "",
                                paramArray5,
                                param.tenantId,
                                param.requestHeader.sequenceNo,
                                tpcConnectionResults[2],
                                false,
                            );

                            if (
                                ResultCdConstants.CODE_000951.equals(
                                    soapCommonOutputDto.getProcessCode(),
                                )
                            ) {
                                // SG取得でNGの場合
                                // ログ出力
                                super.warn(
                                    param.tenantId,
                                    param.requestHeader.sequenceNo,
                                    MsgKeysConstants.APLCPW0021,
                                    "SG取得NG",
                                );
                            } else {
                                const resultStr =
                                    this.soapCommonCoupon.getNodeContent(
                                        soapCommonOutputDto.getDoc(),
                                        "//Result",
                                    );
                                if (
                                    soapCommonOutputDto.isError() ||
                                    "NG".equals(resultStr)
                                ) {
                                    // TPCからのResultでNGの場合
                                    // ログ出力
                                    const errInfo =
                                        this.soapCommonCoupon.getNodeContent(
                                            soapCommonOutputDto.getDoc(),
                                            "//ErrorInfomation",
                                        );
                                    super.warn(
                                        param.tenantId,
                                        param.requestHeader.sequenceNo,
                                        MsgKeysConstants.APLCPW0021,
                                        errInfo,
                                    );
                                }
                            }
                        } catch (e) {
                            if (SOAPException.isSOAPException(e)) {
                                // ログ出力
                                super.warn(
                                    e,
                                    param.tenantId,
                                    param.requestHeader.sequenceNo,
                                    MsgKeysConstants.APLCPW0021,
                                    e.toString(),
                                );
                            } else {
                                throw e;
                            }
                        }
                    }
                }
            }
        } catch (e) {
            if (isSQLException(e)) {
                // DBアクセスリトライエラーの場合
                // リターンコード値は各エラー時にて設定済み
                super.error(
                    e,
                    param.tenantId,
                    param.requestHeader.sequenceNo,
                    MsgKeysConstants.APCOME0402,
                    param.requestHeader.functionType,
                ); // SO管理共通パラメータ設定
                await this.soManagementCommon(
                    param,
                    responseHeader.apiProcessID,
                    executeUserId,
                    executeTenantId,
                    handleCode,
                    receivedDate,
                    orderType,
                    reserveDate,
                    isPrivate,
                );
                responseHeader.processCode = handleCode;
                return this.returnEdit(outputDto, isPrivate, errorMessage);
            }
            // SO管理共通パラメータ設定
            await this.soManagementCommon(
                param,
                responseHeader.apiProcessID,
                executeUserId,
                executeTenantId,
                ResultCdConstants.CODE_999999,
                receivedDate,
                orderType,
                reserveDate,
                isPrivate,
            );
            responseHeader.processCode = ResultCdConstants.CODE_999999;
            super.error(
                e,
                param.tenantId,
                param.requestHeader.sequenceNo,
                MsgKeysConstants.APCOME0401,
            );
            return this.returnEdit(outputDto, isPrivate, errorMessage);
        } finally {
            super.debug(
                param.tenantId,
                param.requestHeader.sequenceNo,
                MsgKeysConstants.APLCFD0002,
                this.getClassName(),
                "service",
            );
        }

        // SO管理共通パラメータ設定
        await this.soManagementCommon(
            param,
            responseHeader.apiProcessID,
            executeUserId,
            executeTenantId,
            ResultCdConstants.CODE_000000,
            receivedDate,
            orderType,
            reserveDate,
            isPrivate,
        );
        // 返却値を設定し、返却
        responseHeader.processCode = ResultCdConstants.CODE_000000;
        return this.returnEdit(outputDto, isPrivate, errorMessage);
    }
    /**
     * 回線クーポンオン・オフ機能返却値編集
     *
     * @param outputDto
     *            回線クーポンオン・オフ機能OutputDto
     * @param isPrivate
     *            変数 「内部呼出フラグ」
     * @param errorMessage
     *            変数 「エラーメッセージ」
     * @return 回線クーポンオン・オフ機能OutputDto
     */
    private returnEdit(
        outputDto: LineCouponAcquisitionOutputDto,
        isPrivate: boolean,
        errorMessage: string,
    ) {
        return outputDto;
    }

    /**
     * SO管理共通パラメータ設定
     *
     * @param param
     * @param resultBean
     * @param executeUserId
     * @param executeTenantId
     * @param receivedDate REST API共通処理が呼び出されたシステム日付
     * @param orderType
     * @param reserveDate
     * @param isPrivate
     * @throws Exception
     */
    private async soManagementCommon(
        param: LineCouponAcquisitionInputDto,
        apiProcessID: string,
        executeUserId: string,
        executeTenantId: string,
        code: string,
        receivedDate: string,
        orderType: string,
        reserveDate: string,
        isPrivate: boolean,
    ) {
        // SO管理共通を呼び出す
        const soObject = new SOObject();

        // サービスオーダID
        soObject.setServiceOrderId(apiProcessID);
        // 申込日
        soObject.setOrderDate(receivedDate);
        // 予約日
        // 変数「オーダ種別」は「予約前オーダ」の場合
        if (Constants.ORDER_TYPE_1.equals(orderType)) {
            soObject.setReserveDate(reserveDate);
        } else {
            soObject.setReserveDate(null);
        }
        // オーダ種別
        soObject.setOrderType(orderType);
        // 完了日
        // 変数「オーダ種別」は「即時オーダ」の場合
        if (
            Constants.ORDER_TYPE_0.equals(orderType) ||
            Constants.ORDER_TYPE_2.equals(orderType)
        ) {
            soObject.setExecDate(MvnoUtil.getDateTimeNow());
        } else {
            soObject.setExecDate(null);
        }
        // オーダステータス
        soObject.setOrderStatus(code);
        // 回線ID
        soObject.setLineId(param.lineNo);
        // 回線グループID
        soObject.setLineGroupId("");
        // 所属テナントID
        soObject.setTenantId(param.tenantId);
        // 実行ユーザID
        soObject.setExecuteUserId(executeUserId);
        // 実行テナントID
        soObject.setExecuteTenantId(executeTenantId);
        // 機能種別
        soObject.setFunctionType(param.requestHeader.functionType);
        // 操作区分
        soObject.setOperationDivision(getStringParameter(param.couponOnOff));
        // 変更前プランID
        soObject.setChangeOldplanId("");
        // 変更後プランID
        soObject.setChangeNewplanId("");
        // 回線所属変更回線ID
        soObject.setChangePlanId("");
        // 基本容量
        soObject.setBasicCapacity("");
        // オプションプランID
        soObject.setOptionPlanId(getStringParameter(param.optionPlanId));
        // 内部呼び出し
        soObject.setInternalFlag(isPrivate);
        // REST電文
        // 変数「オーダ種別」は「予約前オーダ」の場合
        if (Constants.ORDER_TYPE_1.equals(orderType)) {
            soObject.setRestMessage(MvnoUtil.getRestMessage(param));
        } else {
            soObject.setRestMessage(null);
        }

        await this.soCommon.soCommon(soObject);
    }

    /**
     * SOAP共通実行結果を取得する。
     *
     * @param operationName
     * @param operationTypeName
     * @param profileName
     * @param parameterNameArray
     * @param tenantId
     * @param sequenceNo
     * @param tpcGettingResults
     * @param primaryFlg
     * @return
     * @throws Exception
     */
    private async getSoapResult(
        operationName: string,
        operationTypeName: string,
        profileName: string,
        parameterNameArray: string[][],
        tenantId: string,
        sequenceNo: string,
        tpcGettingResults: string,
        primaryFlg: boolean,
    ): Promise<SOAPCommonOutputDto> {
        let soapCommonOutputDto = new SOAPCommonOutputDto();
        const parameterNameList: ParameterName[] = [];

        for (let i = 0; i < parameterNameArray.length; i++) {
            const pramName = new ParameterName();
            pramName.setName(parameterNameArray[i][0].toString());
            pramName.setValue(parameterNameArray[i][1].toString());
            parameterNameList.push(pramName);
        }

        soapCommonOutputDto = await this.soapCommon.callWebSoapApi(
            operationName,
            operationTypeName,
            profileName,
            parameterNameList,
            tenantId,
            sequenceNo,
            tpcGettingResults,
            primaryFlg,
        );

        return soapCommonOutputDto;
    }

    /**
     * SOAP API送信
     *
     * @param lineNo 回線ID
     * @param beforeStatus 更新前ステータス
     * @param afterStatus 更新後ステータス
     * @param tenantId テナントID
     * @param sequenceNo シーケンスNo
     * @param tpcDestInfo TPC情報
     * @param primaryFlg プライマリフラグ
     * @return SOAPCommonOutputDto SOAP応答電文情報
     * @throws Exception
     */
    private async sendSoapApiToTpc(
        lineNo: string,
        beforeStatus: string,
        afterStatus: string,
        tenantId: string,
        sequenceNo: string,
        tpcDestInfo: string,
        primaryFlg: boolean,
    ): Promise<SOAPCommonOutputDto> {
        // TPC電文作成
        const paramNameList1: ParameterName[] = [];
        const param1 = new ParameterName();
        param1.setName("pronum");
        param1.setValue("101" + MvnoUtil.addZeroBeforeStr(lineNo, 15));
        const param2 = new ParameterName();
        param2.setName("aply_srv");
        param2.setValue(beforeStatus);
        paramNameList1.push(param1);
        paramNameList1.push(param2);

        const paramNameList2: ParameterName[] = [];
        const param3 = new ParameterName();
        param3.setName("pronum");
        param3.setValue("102" + MvnoUtil.addZeroBeforeStr(lineNo, 15));
        const param4 = new ParameterName();
        param4.setName("aply_srv");
        param4.setValue(afterStatus);
        paramNameList2.push(param3);
        paramNameList2.push(param4);

        // SOAP-API 電文送受信
        return await this.soapCommonCoupon.callWebSoapApi(
            "serviceProfileRequest",
            "Mod",
            "Service",
            paramNameList1,
            "Mod",
            "Service",
            paramNameList2,
            tenantId,
            sequenceNo,
            tpcDestInfo,
            primaryFlg,
        );
    }
}
