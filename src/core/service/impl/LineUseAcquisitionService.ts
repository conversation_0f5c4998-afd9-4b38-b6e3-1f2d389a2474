import config from "config";

import SOAPException from "@/types/soapException";
import "@/types/string.extension";

import { IFService } from "../IFService";
import MsgKeysConstants from "@/core/constant/MsgKeysConstants";
import ResultCdConstants from "@/core/constant/ResultCdConstants";

import CheckUtil from "@/core/common/CheckUtil";
import MvnoUtil from "@/core/common/MvnoUtil";
import RESTCommon from "@/core/common/RESTCommon";
import TenantManage from "@/core/common/TenantManage";
import SOAPCommon from "@/core/common/SOAPCommon";
import StringUtils from "@/core/common/StringUtils";

import { isSQLException } from "@/helpers/queryHelper";

import APICommonDAO from "@/core/dao/APICommonDAO";
import APILinesDAO from "@/core/dao/APILinesDAO";
import APILinesGroupDAO from "@/core/dao/ApiLinesGroupDAO";

import LineUseInfoCouponOffDto, {
    getEmpty_LineUseInfoCouponOff,
} from "@/core/dto/LineUseInfoCouponOffDto";
import LineUseInfoGiftCouponDto, {
    getEmpty_LineUseInfoGiftCoupon,
} from "@/core/dto/LineUseInfoGiftCouponDto";
import LineUseInfoAppCouponDto, {
    getEmpty_LineUseInfoAppCoupon,
} from "@/core/dto/LineUseInfoAppCouponDto";
import LineUseInfoGroupDto, {
    getEmpty_LineUseInfoGroup,
} from "@/core/dto/LineUseInfoGroupDto";
import Check from "@/core/common/Check";
import { getEmpty_LineUseAcquisitionOutput } from "@/core/dto/LineUseAcquisitionOutputDto";
import { getEmpty_LineUseAcquisitionVersionOutput } from "@/core/dto/LineUseAcquisitionVersionOutputDto";
import { getEmpty_CarryingOverCoupon1 } from "@/core/dto/CarryingOverCoupon1";
import { getEmpty_CarryingOverCoupon2 } from "@/core/dto/CarryingOvercoupon2";
import { getEmpty_AdditionalCoupon1 } from "@/core/dto/AdditionalCoupon1";
import { getEmpty_AdditionalCoupon2 } from "@/core/dto/AdditionalCoupon2";
import { getEmpty_AdditionalCoupon3 } from "@/core/dto/AdditionalCoupon3";
import { getEmpty_AdditionalCoupon4 } from "@/core/dto/AdditionalCoupon4";
import { getEmpty_AdditionalCoupon5 } from "@/core/dto/AdditionalCoupon5";
import { getEmpty_GiftCoupon1 } from "@/core/dto/GiftCoupon1Dto";
import { getEmpty_GiftCoupon2 } from "@/core/dto/GiftCoupon2Dto";
import { getEmpty_AppCoupon1 } from "@/core/dto/AppCoupon1Dto";
import { getEmpty_AppCoupon2 } from "@/core/dto/AppCoupon2Dto";
import { getEmpty_AppCoupon3 } from "@/core/dto/AppCoupon3Dto";
import { getEmpty_AppCoupon4 } from "@/core/dto/AppCoupon4Dto";
import { getEmpty_AppCoupon5 } from "@/core/dto/AppCoupon5Dto";
import { getEmpty_AppCoupon6 } from "@/core/dto/AppCoupon6Dto";
import AppCouponDto, { getEmpty_AppCoupon } from "@/core/dto/AppCouponDto";
import { getEmpty_CarryingOverCoupon1Group } from "@/core/dto/CarryingOverCoupon1Group";
import { getEmpty_CarryingOverCoupon2Group } from "@/core/dto/CarryingOverCoupon2Group";
import BaseOutputDto from "@/core/dto/BaseOutputDto";
import { getEmpty_AdditionalCoupon1Group } from "@/core/dto/AdditionalCoupon1Group";
import { getEmpty_AdditionalCoupon2Group } from "@/core/dto/AdditionalCoupon2Group";
import { getEmpty_AdditionalCoupon3Group } from "@/core/dto/AdditionalCoupon3Group";
import { getEmpty_AdditionalCoupon4Group } from "@/core/dto/AdditionalCoupon4Group";
import { getEmpty_AdditionalCoupon5Group } from "@/core/dto/AdditionalCoupon5Group";
import { getEmpty_AppCoupon1Group } from "@/core/dto/AppCoupon1GroupDto";
import { getEmpty_AppCoupon2Group } from "@/core/dto/AppCoupon2GroupDto";
import { getEmpty_AppCoupon3Group } from "@/core/dto/AppCoupon3GroupDto";
import { getEmpty_AppCoupon4Group } from "@/core/dto/AppCoupon4GroupDto";
import { getEmpty_AppCoupon5Group } from "@/core/dto/AppCoupon5GroupDto";
import { getEmpty_AppCoupon6Group } from "@/core/dto/AppCoupon6GroupDto";
import { getEmpty_LineUseAcquisitionAddOpiton1VersionOutput } from "@/core/dto/LineUseAcquisitionAddOpiton1VersionOutputDto";
import { getEmpty_LineUseAcquisitionAddOpiton2VersionOutput } from "@/core/dto/LineUseAcquisitionAddOpiton2VersionOutputDto";
import { getEmpty_LineUseAcquisitionAddOpiton3VersionOutput } from "@/core/dto/LineUseAcquisitionAddOpiton3VersionOutputDto";
import { getEmpty_LineUseAcquisitionAddOpiton4VersionOutput } from "@/core/dto/LineUseAcquisitionAddOpiton4VersionOutputDto";
import { getEmpty_LineUseAcquisitionAddOpiton1Output } from "@/core/dto/LineUseAcquisitionAddOpiton1OutputDto";
import { getEmpty_LineUseAcquisitionAddOpiton2Output } from "@/core/dto/LineUseAcquisitionAddOpiton2OutputDto";
import { getEmpty_LineUseAcquisitionAddOpiton3Output } from "@/core/dto/LineUseAcquisitionAddOpiton3OutputDto";
import { getEmpty_LineUseAcquisitionAddOpiton4Output } from "@/core/dto/LineUseAcquisitionAddOpiton4Dto";
import { getEmpty_LineUseAcquisitionIpAddressOutput } from "@/core/dto/LineUseAcquisitionIpAddressOutputDto";
import LineUseAcquisitionInputDto from "@/core/dto/LineUseAcquisitionInputDto";
import LineUseAcquisitionBaseOutputDto from "@/core/dto/LineUseAcquisitionBaseOutputDto";
import ParameterName from "@/core/dto/ParameterName";
import ResponseHeader from "@/core/dto/ResponseHeader";

import { GroupPlansEntity } from "@/core/entity/GroupPlansEntity";
import GroupTrafficsEntity from "@/core/entity/GroupTrafficsEntity";
import { LinesEntity } from "@/core/entity/LinesEntity";
import LineTrafficsEntity from "@/core/entity/LineTrafficsEntity";
import PlansEntity from "@/core/entity/PlansEntity";
import TenantGroupPlansEntity from "@/core/entity/TenantGroupPlansEntity";
import TenantPlansEntity from "@/core/entity/TenantPlansEntity";
import { TenantsEntity } from "@/core/entity/TenantsEntity";
import { getStringParameter } from "@/types/parameter.string";

/** 定数　「回線番号」 */
const CONST_LINENO = "回線番号";
/** 定数　「卸ポータルプランID」 */
const CONST_POTALGROUPPLANID = "卸ポータルプランID";
/** 定数　「卸ポータルグループプランID」 */
const POTALGROUP_PLAN = "卸ポータルグループプランID";
// /** 定数　「クーポンOFF時の通信量要否」*/
// const CONST_TRAFFICOFCOUPONOFFFLAG = "クーポンOFF時の通信量要否";
/** 定数　「社内テナント種別は１（C-OCN）」 */
const CONST_SHANAITENANTID = "1";
/** 参照サービスパターン結果1 */
const CONST_SERVICEPATTERN_1 = "1";
/** 参照サービスパターン結果2 */
const CONST_SERVICEPATTERN_2 = "2";
/** 参照サービスパターン結果3 */
const CONST_SERVICEPATTERN_3 = "3";
/** 参照サービスパターン結果4 */
const CONST_SERVICEPATTERN_4 = "4";
/** 参照サービスパターン結果5 */
const CONST_SERVICEPATTERN_5 = "5";
/** 参照サービスパターン結果6 */
const CONST_SERVICEPATTERN_6 = "6";
/** 参照サービスパターン結果8 */
const CONST_SERVICEPATTERN_8 = "8";
/** 参照サービスパターン結果9 */
const CONST_SERVICEPATTERN_9 = "9";
/** 参照サービスパターン結果10 */
const CONST_SERVICEPATTERN_10 = "10";
/** 参照サービスパターン結果11 */
const CONST_SERVICEPATTERN_11 = "11";
/** 参照サービスパターン結果12 */
const CONST_SERVICEPATTERN_12 = "12";
/** XML電文返信結果 */
const CONST_RESULT = "NG";
/** 端末IPアドレス取得要否 */
const CONST_GETIPADDRESS = "端末IPアドレス取得要否";
const CONST_ADDOPTION = "追加オプション";

export default class LineUseAcquisitionService
    extends RESTCommon
    implements
        IFService<LineUseAcquisitionInputDto, LineUseAcquisitionBaseOutputDto>
{
    // /**
    //  * SOAP API送信先アドレス
    //  */
    // @Value("#{mvno[APServerConnectSOAPAPIURL]}")
    // private String soapApiUrl;

    /**
     * 総量規制状態（規制中）判定文字列
     */
    // @Value("#{mvno[TotalVolumeControlInJudgmentString]}")
    // private String totalVolumeControlInJudgment;
    private totalVolumeControlInJudgment = config.get<string>(
        "mvno.TotalVolumeControlInJudgmentString",
    );

    /**
     * API共通DAO
     */
    private aPICommonDAO = new APICommonDAO(this.request, this.context);

    /**
     * API共通DAO
     */
    private aPILinesDAO = new APILinesDAO(this.request, this.context);

    /**
     * API共通DAO
     */
    private aPILinesGroupDAO = new APILinesGroupDAO(this.request, this.context);

    /**
     * SOAP API連携機能初期化
     */
    private sOAPCommon = new SOAPCommon(this.request, this.context);

    /**
     * 総量規制状態（未規制）判定文字列
     */
    // @Value("#{mvno[TotalVolumeControlOutJudgmentString]}")
    // private String totalVolumeControlOutJudgment;
    private totalVolumeControlOutJudgment = config.get<string>(
        "mvno.TotalVolumeControlOutJudgmentString",
    );

    /**
     * ヘビーユーザ監視規制状態判定文字列
     */
    // @Value("#{mvno[HeavyUserMonitorStatusUnregulatedString]}")
    // private String heavyUserMonitorStatusUnregulatedString;
    private heavyUserMonitorStatusUnregulatedString = config.get<string>(
        "mvno.HeavyUserMonitorStatusUnregulatedString",
    );

    /**
     * ヘビーユーザ監視規制状態判定文字列
     */
    // @Value("#{mvno[HeavyUserMonitorStatusRegulatedatLastString]}")
    // private String heavyUserMonitorStatusRegulatedatLastString;
    private heavyUserMonitorStatusRegulatedatLastString = config.get<string>(
        "mvno.HeavyUserMonitorStatusRegulatedatLastString",
    );

    /**
     * ヘビーユーザ監視規制状態判定文字列
     */
    // @Value("#{mvno[HeavyUserMonitorStatusRegulatedatMonthlyString]}")
    // private String heavyUserMonitorStatusRegulatedatMonthlyString;
    private heavyUserMonitorStatusRegulatedatMonthlyString = config.get<string>(
        "mvno.HeavyUserMonitorStatusRegulatedatMonthlyString",
    );

    /**
     * ヘビーユーザ監視規制状態判定文字列
     */
    // @Value("#{mvno[HeavyUserMonitorStatusRegulatedatLastMonthString]}")
    // private String heavyUserMonitorStatusRegulatedatLastMonthString;
    private heavyUserMonitorStatusRegulatedatLastMonthString =
        config.get<string>(
            "mvno.HeavyUserMonitorStatusRegulatedatLastMonthString",
        );

    private tenantManage = new TenantManage(this.request, this.context);

    public async service(
        param: LineUseAcquisitionInputDto,
    ): Promise<LineUseAcquisitionBaseOutputDto> {
        super.debug(
            param.tenantId,
            param.requestHeader.sequenceNo,
            MsgKeysConstants.APLTID0001,
            this.getClassName(),
            "service",
            JSON.stringify(param),
        );
        const isPrivate = false; // NOTE fixed to false during refactoring
        // REST API共通処理が呼び出されたシステム日付
        const receivedDate =
            this.context?.responseHeader?.receivedDate ??
            MvnoUtil.getDateTimeNow();
        /** 変数「処理コード」初期化 */
        let handleCode = ResultCdConstants.CODE_000000;
        /** 送信番号取得 */
        const sequenceNo = param.requestHeader.sequenceNo;
        /** 送信元システムID取得 */
        const senderSystemId = param.requestHeader.senderSystemId;
        /** API認証キー取得 */
        const apiKey = param.requestHeader.apiKey;
        /** 機能種別取得 */
        const functionType = param.requestHeader.functionType;
        /** テナントID取得 */
        const tenantId = param.tenantId;
        /** 卸ポータルプランID取得 */
        const potalPlanID = getStringParameter(param.potalPlanID);
        // 回線グループID取得
        let lineGroupId = null;
        // 卸ポータルグループプランID取得
        const potalGroupPlanID = param.potalGroupPlanID;
        /** 回線番号取得 */
        const lineNo = param.lineNo;

        /** 追加オプション */
        const addOption = getStringParameter(param.addOption);
        // バージョン
        const version = getStringParameter(param.version);
        // 回線運用データ情報結果初期化
        let document: Document = null;
        // サービスパターン初期化
        let servicePattern = "";
        // グループサービスパターン初期化
        let servicePatternGroup = "";
        // API処理ID
        let apiHandleId = "";
        // 回線基本情報取得アウトプット初期化
        let lineUseAcquisitionOutputDto: LineUseAcquisitionBaseOutputDto;
        // CheckResultBean resultBean = null;
        // 変数「エラーメッセージ」を定義する
        const errorMessage: string = null;
        // 前月の通信量
        let trafficPreviousMonth: string = null;
        // 前々月の通信量
        let trafficBeforehandMonth: string = null;
        // 前月のクーポンOFF通信量
        let trafficPreviousMonthCouponOff: string = null;
        // 前々月のクーポンOFF通信量
        let trafficBeforehandMonthCouponOff: string = null;
        // 前月の通信量(グループ)
        let trafficPreviousMonthGroup: string = null;
        // 前々月の通信量(グループ)
        let trafficBeforehandMonthGroup: string = null;
        // 前月のクーポンOFF通信量(グループ)
        let trafficPreviousMonthCouponOffGroup: string = null;
        // 前々月のクーポンOFF通信量(グループ)
        let trafficBeforehandMonthCouponOffGroup: string = null;
        // 端末IPアドレス取得要否
        const getIpAddress = getStringParameter(param.getIpAddress);
        // 利用状態
        let lineUsageStatus: string = null;

        try {
            // 変数「処理コード」
            handleCode = this.context.responseHeader.processCode;
            // 変数「API処理ID」
            apiHandleId = this.context.responseHeader.apiProcessID;
            // NOTE L413~L1376
            // REST共通チェック結果がtrueの場合
            if (Check.checkLineNo(lineNo)) {
                if (!CheckUtil.checkIsNotNull(potalPlanID)) {
                    // 卸ポータルプランIDフォーマットチェック
                    if (!Check.checkPotalPlanID(potalPlanID)) {
                        handleCode = ResultCdConstants.CODE_020101;
                        super.warn(
                            tenantId,
                            sequenceNo,
                            MsgKeysConstants.APLTIW0001,
                            CONST_POTALGROUPPLANID,
                            potalPlanID,
                        );
                        // 返却値編集
                        lineUseAcquisitionOutputDto = this.returnEdit(
                            param,
                            handleCode,
                            apiHandleId,
                            document,
                            servicePattern,
                            isPrivate,
                            errorMessage,
                            receivedDate,
                            trafficPreviousMonth,
                            trafficBeforehandMonth,
                            servicePatternGroup,
                            trafficPreviousMonthGroup,
                            trafficBeforehandMonthGroup,
                            trafficPreviousMonthCouponOffGroup,
                            trafficBeforehandMonthCouponOffGroup,
                            trafficPreviousMonthCouponOff,
                            trafficBeforehandMonthCouponOff,
                            version,
                            lineUsageStatus,
                        );
                        super.debug(
                            param.tenantId,
                            param.requestHeader.sequenceNo,
                            MsgKeysConstants.APLTID0002,
                            this.getClassName(),
                            "service",
                        );
                        return lineUseAcquisitionOutputDto;
                    }
                }

                if (!CheckUtil.checkIsNotNull(getIpAddress)) {
                    if (
                        !"1".equals(getIpAddress) &&
                        !"0".equals(getIpAddress)
                    ) {
                        handleCode = ResultCdConstants.CODE_020101;
                        super.warn(
                            tenantId,
                            sequenceNo,
                            MsgKeysConstants.APLTIW0001,
                            CONST_GETIPADDRESS,
                            getIpAddress,
                        );
                        // 返却値編集
                        lineUseAcquisitionOutputDto = this.returnEdit(
                            param,
                            handleCode,
                            apiHandleId,
                            document,
                            servicePattern,
                            isPrivate,
                            errorMessage,
                            receivedDate,
                            trafficPreviousMonth,
                            trafficBeforehandMonth,
                            servicePatternGroup,
                            trafficPreviousMonthGroup,
                            trafficBeforehandMonthGroup,
                            trafficPreviousMonthCouponOffGroup,
                            trafficBeforehandMonthCouponOffGroup,
                            trafficPreviousMonthCouponOff,
                            trafficBeforehandMonthCouponOff,
                            version,
                            lineUsageStatus,
                        );
                        super.debug(
                            param.tenantId,
                            param.requestHeader.sequenceNo,
                            MsgKeysConstants.APLTID0002,
                            this.getClassName(),
                            "service",
                        );
                        return lineUseAcquisitionOutputDto;
                    }
                }

                if (!CheckUtil.checkIsNotNull(addOption)) {
                    // 追加オプションフォーマットチェック
                    if (
                        !"0".equals(addOption) &&
                        !"1".equals(addOption) &&
                        !"2".equals(addOption) &&
                        !"3".equals(addOption) &&
                        !"4".equals(addOption)
                    ) {
                        handleCode = ResultCdConstants.CODE_020101;
                        super.warn(
                            tenantId,
                            sequenceNo,
                            MsgKeysConstants.APLTIW0001,
                            CONST_ADDOPTION,
                            addOption,
                        );
                        // 返却値編集
                        lineUseAcquisitionOutputDto = this.returnEdit(
                            param,
                            handleCode,
                            apiHandleId,
                            document,
                            servicePattern,
                            isPrivate,
                            errorMessage,
                            receivedDate,
                            trafficPreviousMonth,
                            trafficBeforehandMonth,
                            servicePatternGroup,
                            trafficPreviousMonthGroup,
                            trafficBeforehandMonthGroup,
                            trafficPreviousMonthCouponOffGroup,
                            trafficBeforehandMonthCouponOffGroup,
                            trafficPreviousMonthCouponOff,
                            trafficBeforehandMonthCouponOff,
                            version,
                            lineUsageStatus,
                        );
                        super.debug(
                            param.tenantId,
                            param.requestHeader.sequenceNo,
                            MsgKeysConstants.APLTID0002,
                            this.getClassName(),
                            "service",
                        );
                        return lineUseAcquisitionOutputDto;
                    }
                }

                // 卸ポータルグループプランIDフォーマットチェック
                if (!CheckUtil.checkIsNotNull(potalGroupPlanID)) {
                    if (!Check.checkGroupPlanIDFmt(potalGroupPlanID)) {
                        handleCode = ResultCdConstants.CODE_020101;
                        super.warn(
                            tenantId,
                            sequenceNo,
                            MsgKeysConstants.APLTIW0001,
                            POTALGROUP_PLAN,
                            potalGroupPlanID,
                        );
                        // 返却値編集
                        lineUseAcquisitionOutputDto = this.returnEdit(
                            param,
                            handleCode,
                            apiHandleId,
                            document,
                            servicePattern,
                            isPrivate,
                            errorMessage,
                            receivedDate,
                            trafficPreviousMonth,
                            trafficBeforehandMonth,
                            servicePatternGroup,
                            trafficPreviousMonthGroup,
                            trafficBeforehandMonthGroup,
                            trafficPreviousMonthCouponOffGroup,
                            trafficBeforehandMonthCouponOffGroup,
                            trafficPreviousMonthCouponOff,
                            trafficBeforehandMonthCouponOff,
                            version,
                            lineUsageStatus,
                        );
                        super.debug(
                            param.tenantId,
                            param.requestHeader.sequenceNo,
                            MsgKeysConstants.APLTID0002,
                            this.getClassName(),
                            "service",
                        );
                        return lineUseAcquisitionOutputDto;
                    }
                }

                // バージョンフォーマットチェック
                if (!CheckUtil.checkIsNotNull(version)) {
                    if (!"1".equals(version)) {
                        handleCode = ResultCdConstants.CODE_020101;
                        super.warn(
                            tenantId,
                            sequenceNo,
                            MsgKeysConstants.APLTIW0001,
                            "バージョン",
                            version,
                        );
                        // 返却値編集
                        lineUseAcquisitionOutputDto = this.returnEdit(
                            param,
                            handleCode,
                            apiHandleId,
                            document,
                            servicePattern,
                            isPrivate,
                            errorMessage,
                            receivedDate,
                            trafficPreviousMonth,
                            trafficBeforehandMonth,
                            servicePatternGroup,
                            trafficPreviousMonthGroup,
                            trafficBeforehandMonthGroup,
                            trafficPreviousMonthCouponOffGroup,
                            trafficBeforehandMonthCouponOffGroup,
                            trafficPreviousMonthCouponOff,
                            trafficBeforehandMonthCouponOff,
                            version,
                            lineUsageStatus,
                        );
                        super.debug(
                            param.tenantId,
                            param.requestHeader.sequenceNo,
                            MsgKeysConstants.APLTID0002,
                            this.getClassName(),
                            "service",
                        );
                        return lineUseAcquisitionOutputDto;
                    }
                }

                // テナントテーブルに「社内テナント種別」を取得する
                let shaNaiTenantType = "";
                let tenantsEntity: TenantsEntity = null;
                try {
                    tenantsEntity = await this.aPICommonDAO.getTenants(
                        tenantId,
                    );
                } catch (e) {
                    if (isSQLException(e)) {
                        // DBアクセスリトライエラーの場合
                        handleCode = ResultCdConstants.CODE_020102;
                    }
                    throw e;
                }
                if (tenantsEntity === null) {
                    handleCode = ResultCdConstants.CODE_020102;
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APLTIW0002,
                    );
                    // 返却値編集
                    lineUseAcquisitionOutputDto = this.returnEdit(
                        param,
                        handleCode,
                        apiHandleId,
                        document,
                        servicePattern,
                        isPrivate,
                        errorMessage,
                        receivedDate,
                        trafficPreviousMonth,
                        trafficBeforehandMonth,
                        servicePatternGroup,
                        trafficPreviousMonthGroup,
                        trafficBeforehandMonthGroup,
                        trafficPreviousMonthCouponOffGroup,
                        trafficBeforehandMonthCouponOffGroup,
                        trafficPreviousMonthCouponOff,
                        trafficBeforehandMonthCouponOff,
                        version,
                        lineUsageStatus,
                    );
                    super.debug(
                        param.tenantId,
                        param.requestHeader.sequenceNo,
                        MsgKeysConstants.APLTID0002,
                        this.getClassName(),
                        "service",
                    );
                    return lineUseAcquisitionOutputDto;
                } else {
                    let tenantType = 0;
                    if (CheckUtil.checkIsNull(tenantsEntity.tenantType)) {
                        shaNaiTenantType = "";
                    } else {
                        tenantType = tenantsEntity.tenantType;
                        shaNaiTenantType = String(tenantType);
                    }
                }

                // 卸ポータルプランIDの判断を行う
                if (
                    CONST_SHANAITENANTID.equals(shaNaiTenantType) &&
                    CheckUtil.checkIsNotNull(potalPlanID)
                ) {
                    handleCode = ResultCdConstants.CODE_020103;
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APLTIW0003,
                    );
                    // 返却値編集
                    lineUseAcquisitionOutputDto = this.returnEdit(
                        param,
                        handleCode,
                        apiHandleId,
                        document,
                        servicePattern,
                        isPrivate,
                        errorMessage,
                        receivedDate,
                        trafficPreviousMonth,
                        trafficBeforehandMonth,
                        servicePatternGroup,
                        trafficPreviousMonthGroup,
                        trafficBeforehandMonthGroup,
                        trafficPreviousMonthCouponOffGroup,
                        trafficBeforehandMonthCouponOffGroup,
                        trafficPreviousMonthCouponOff,
                        trafficBeforehandMonthCouponOff,
                        version,
                        lineUsageStatus,
                    );
                    super.debug(
                        param.tenantId,
                        param.requestHeader.sequenceNo,
                        MsgKeysConstants.APLTID0002,
                        this.getClassName(),
                        "service",
                    );
                    return lineUseAcquisitionOutputDto;
                } else if (
                    !CONST_SHANAITENANTID.equals(shaNaiTenantType) &&
                    !CheckUtil.checkIsNotNull(potalPlanID)
                ) {
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APLTIW0009,
                    );
                    handleCode = ResultCdConstants.CODE_020106;
                    // 返却値編集
                    lineUseAcquisitionOutputDto = this.returnEdit(
                        param,
                        handleCode,
                        apiHandleId,
                        document,
                        servicePattern,
                        isPrivate,
                        errorMessage,
                        receivedDate,
                        trafficPreviousMonth,
                        trafficBeforehandMonth,
                        servicePatternGroup,
                        trafficPreviousMonthGroup,
                        trafficBeforehandMonthGroup,
                        trafficPreviousMonthCouponOffGroup,
                        trafficBeforehandMonthCouponOffGroup,
                        trafficPreviousMonthCouponOff,
                        trafficBeforehandMonthCouponOff,
                        version,
                        lineUsageStatus,
                    );
                    super.debug(
                        param.tenantId,
                        param.requestHeader.sequenceNo,
                        MsgKeysConstants.APLTID0002,
                        this.getClassName(),
                        "service",
                    );
                    return lineUseAcquisitionOutputDto;
                }

                let tenantCheck: [boolean, string] = null;
                try {
                    tenantCheck = await this.tenantManage.doCheck(
                        param.lineNo,
                        param.tenantId,
                    );
                } catch (e) {
                    if (isSQLException(e)) {
                        // DBアクセスリトライエラーの場合
                        handleCode = ResultCdConstants.CODE_020104;
                    }
                    throw e;
                }
                if (!tenantCheck[0]) {
                    handleCode = ResultCdConstants.CODE_020104;
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APLTIW0004,
                        param.lineNo,
                        param.tenantId,
                    );
                    // 返却値編集
                    lineUseAcquisitionOutputDto = this.returnEdit(
                        param,
                        handleCode,
                        apiHandleId,
                        document,
                        servicePattern,
                        isPrivate,
                        errorMessage,
                        receivedDate,
                        trafficPreviousMonth,
                        trafficBeforehandMonth,
                        servicePatternGroup,
                        trafficPreviousMonthGroup,
                        trafficBeforehandMonthGroup,
                        trafficPreviousMonthCouponOffGroup,
                        trafficBeforehandMonthCouponOffGroup,
                        trafficPreviousMonthCouponOff,
                        trafficBeforehandMonthCouponOff,
                        version,
                        lineUsageStatus,
                    );
                    super.debug(
                        param.tenantId,
                        param.requestHeader.sequenceNo,
                        MsgKeysConstants.APLTID0002,
                        this.getClassName(),
                        "service",
                    );
                    return lineUseAcquisitionOutputDto;
                }

                // テナントプランテーブルに「テナントID」を取得する
                if (!CheckUtil.checkIsNotNull(potalPlanID)) {
                    let tenantIdIdList: TenantPlansEntity[] = null;
                    try {
                        tenantIdIdList = await this.aPICommonDAO.getTenantPlans(
                            potalPlanID,
                            tenantId,
                        );
                    } catch (e) {
                        if (isSQLException(e)) {
                            // DBアクセスリトライエラーの場合
                            handleCode = ResultCdConstants.CODE_020105;
                        }
                        throw e;
                    }
                    if (
                        tenantIdIdList === null ||
                        tenantIdIdList.length === 0
                    ) {
                        handleCode = ResultCdConstants.CODE_020105;
                        super.warn(
                            tenantId,
                            sequenceNo,
                            MsgKeysConstants.APLTIW0005,
                            tenantId,
                            potalPlanID,
                        );
                        lineUseAcquisitionOutputDto = this.returnEdit(
                            param,
                            handleCode,
                            apiHandleId,
                            document,
                            servicePattern,
                            isPrivate,
                            errorMessage,
                            receivedDate,
                            trafficPreviousMonth,
                            trafficBeforehandMonth,
                            servicePatternGroup,
                            trafficPreviousMonthGroup,
                            trafficBeforehandMonthGroup,
                            trafficPreviousMonthCouponOffGroup,
                            trafficBeforehandMonthCouponOffGroup,
                            trafficPreviousMonthCouponOff,
                            trafficBeforehandMonthCouponOff,
                            version,
                            lineUsageStatus,
                        );
                        super.debug(
                            param.tenantId,
                            param.requestHeader.sequenceNo,
                            MsgKeysConstants.APLTID0002,
                            this.getClassName(),
                            "service",
                        );
                        return lineUseAcquisitionOutputDto;
                    }
                }

                // 追加オプションが"4"の場合、卸ポータルグループプランIDが入力されているか確認する。
                if (StringUtils.equals("4", addOption)) {
                    if (CheckUtil.checkIsNotNull(potalGroupPlanID)) {
                        // TRUEならNULL
                        handleCode = ResultCdConstants.CODE_020107;
                        super.warn(
                            tenantId,
                            sequenceNo,
                            MsgKeysConstants.APLTIW0010,
                            potalGroupPlanID,
                        );
                        // 返却値編集
                        lineUseAcquisitionOutputDto = this.returnEdit(
                            param,
                            handleCode,
                            apiHandleId,
                            document,
                            servicePattern,
                            isPrivate,
                            errorMessage,
                            receivedDate,
                            trafficPreviousMonth,
                            trafficBeforehandMonth,
                            servicePatternGroup,
                            trafficPreviousMonthGroup,
                            trafficBeforehandMonthGroup,
                            trafficPreviousMonthCouponOffGroup,
                            trafficBeforehandMonthCouponOffGroup,
                            trafficPreviousMonthCouponOff,
                            trafficBeforehandMonthCouponOff,
                            version,
                            lineUsageStatus,
                        );
                        super.debug(
                            param.tenantId,
                            param.requestHeader.sequenceNo,
                            MsgKeysConstants.APLTID0002,
                            this.getClassName(),
                            "service",
                        );
                        return lineUseAcquisitionOutputDto;
                    }

                    let tenatGroupEntity: TenantGroupPlansEntity = null;
                    try {
                        // 追加オプションが"4"の場合、卸ポータルグループプランIDがテナントIDに所属かチェック
                        tenatGroupEntity =
                            await this.aPICommonDAO.getTenantGroupPlans(
                                potalGroupPlanID,
                                tenantId,
                            );
                    } catch (e) {
                        if (isSQLException(e)) {
                            // DBアクセスリトライエラーの場合
                            handleCode = ResultCdConstants.CODE_020108;
                        }
                        throw e;
                    }
                    if (tenatGroupEntity == null) {
                        handleCode = ResultCdConstants.CODE_020108;
                        super.warn(
                            tenantId,
                            sequenceNo,
                            MsgKeysConstants.APLTIW0011,
                            potalGroupPlanID,
                            tenantId,
                        );
                        // 返却値編集
                        lineUseAcquisitionOutputDto = this.returnEdit(
                            param,
                            handleCode,
                            apiHandleId,
                            document,
                            servicePattern,
                            isPrivate,
                            errorMessage,
                            receivedDate,
                            trafficPreviousMonth,
                            trafficBeforehandMonth,
                            servicePatternGroup,
                            trafficPreviousMonthGroup,
                            trafficBeforehandMonthGroup,
                            trafficPreviousMonthCouponOffGroup,
                            trafficBeforehandMonthCouponOffGroup,
                            trafficPreviousMonthCouponOff,
                            trafficBeforehandMonthCouponOff,
                            version,
                            lineUsageStatus,
                        );
                        super.debug(
                            param.tenantId,
                            param.requestHeader.sequenceNo,
                            MsgKeysConstants.APLTID0002,
                            this.getClassName(),
                            "service",
                        );
                        return lineUseAcquisitionOutputDto;
                    }
                }

                // ロジック処理
                if (!CheckUtil.checkIsNotNull(potalPlanID)) {
                    let servicePatternList: PlansEntity[] = null;
                    try {
                        servicePatternList = await this.aPICommonDAO.getPlans(
                            potalPlanID,
                        );
                    } catch (e) {
                        if (isSQLException(e)) {
                            // DBアクセスリトライエラーの場合
                            handleCode = ResultCdConstants.CODE_020201;
                        }
                        throw e;
                    }
                    if (servicePatternList !== null) {
                        if (servicePatternList.length === 0) {
                            this.context.log(
                                "ServicePatternList not found",
                                potalPlanID,
                            );
                            handleCode = ResultCdConstants.CODE_020201;
                            super.warn(
                                tenantId,
                                sequenceNo,
                                MsgKeysConstants.APLTIW0006,
                            );
                            // 返却値編集
                            lineUseAcquisitionOutputDto = this.returnEdit(
                                param,
                                handleCode,
                                apiHandleId,
                                document,
                                servicePattern,
                                isPrivate,
                                errorMessage,
                                receivedDate,
                                trafficPreviousMonth,
                                trafficBeforehandMonth,
                                servicePatternGroup,
                                trafficPreviousMonthGroup,
                                trafficBeforehandMonthGroup,
                                trafficPreviousMonthCouponOffGroup,
                                trafficBeforehandMonthCouponOffGroup,
                                trafficPreviousMonthCouponOff,
                                trafficBeforehandMonthCouponOff,
                                version,
                                lineUsageStatus,
                            );
                            super.debug(
                                param.tenantId,
                                param.requestHeader.sequenceNo,
                                MsgKeysConstants.APLTID0002,
                                this.getClassName(),
                                "service",
                            );
                            return lineUseAcquisitionOutputDto;
                        }
                        if (
                            CheckUtil.checkIsNull(
                                servicePatternList.at(0).refServicePattern,
                            )
                        ) {
                            this.context.log(
                                "ServicePatternList refServicePattern is null",
                                potalPlanID,
                                servicePatternList?.at(0)?.refServicePattern,
                            );
                            handleCode = ResultCdConstants.CODE_020201;
                            super.warn(
                                tenantId,
                                sequenceNo,
                                MsgKeysConstants.APLTIW0006,
                            );
                            // 返却値編集
                            lineUseAcquisitionOutputDto = this.returnEdit(
                                param,
                                handleCode,
                                apiHandleId,
                                document,
                                servicePattern,
                                isPrivate,
                                errorMessage,
                                receivedDate,
                                trafficPreviousMonth,
                                trafficBeforehandMonth,
                                servicePatternGroup,
                                trafficPreviousMonthGroup,
                                trafficBeforehandMonthGroup,
                                trafficPreviousMonthCouponOffGroup,
                                trafficBeforehandMonthCouponOffGroup,
                                trafficPreviousMonthCouponOff,
                                trafficBeforehandMonthCouponOff,
                                version,
                                lineUsageStatus,
                            );
                            super.debug(
                                param.tenantId,
                                param.requestHeader.sequenceNo,
                                MsgKeysConstants.APLTID0002,
                                this.getClassName(),
                                "service",
                            );
                            return lineUseAcquisitionOutputDto;
                        } else {
                            servicePattern = String(
                                servicePatternList.at(0).refServicePattern,
                            );
                        }
                    }
                } else {
                    let plansEntityList: PlansEntity[] = null;
                    try {
                        // 電文の卸ポータルプランIDの入力がなし場合、プランテーブルの「参照サービスパターン」取得を行う
                        plansEntityList =
                            await this.aPILinesDAO.getRefServicePattern(lineNo);
                    } catch (e) {
                        if (isSQLException(e)) {
                            // DBアクセスリトライエラーの場合
                            handleCode = ResultCdConstants.CODE_020201;
                        }
                        throw e;
                    }
                    if (
                        plansEntityList === null ||
                        plansEntityList.length === 0 ||
                        plansEntityList.length > 1
                    ) {
                        this.context.log(
                            "PlansEntityList not found or > 1",
                            lineNo,
                            plansEntityList?.length,
                        );
                        handleCode = ResultCdConstants.CODE_020201;
                        super.warn(
                            tenantId,
                            sequenceNo,
                            MsgKeysConstants.APLTIW0006,
                        );
                        // 返却値編集
                        lineUseAcquisitionOutputDto = this.returnEdit(
                            param,
                            handleCode,
                            apiHandleId,
                            document,
                            servicePattern,
                            isPrivate,
                            errorMessage,
                            receivedDate,
                            trafficPreviousMonth,
                            trafficBeforehandMonth,
                            servicePatternGroup,
                            trafficPreviousMonthGroup,
                            trafficBeforehandMonthGroup,
                            trafficPreviousMonthCouponOffGroup,
                            trafficBeforehandMonthCouponOffGroup,
                            trafficPreviousMonthCouponOff,
                            trafficBeforehandMonthCouponOff,
                            version,
                            lineUsageStatus,
                        );
                        super.debug(
                            param.tenantId,
                            param.requestHeader.sequenceNo,
                            MsgKeysConstants.APLTID0002,
                            this.getClassName(),
                            "service",
                        );
                        return lineUseAcquisitionOutputDto;
                    }

                    if (
                        CheckUtil.checkIsNull(
                            plansEntityList.at(0).refServicePattern,
                        )
                    ) {
                        this.context.log(
                            "PlansEntityList refServicePattern is null",
                            lineNo,
                            plansEntityList?.at(0)?.refServicePattern,
                        );
                        handleCode = ResultCdConstants.CODE_020201;
                        super.warn(
                            tenantId,
                            sequenceNo,
                            MsgKeysConstants.APLTIW0006,
                        );
                        // 返却値編集
                        lineUseAcquisitionOutputDto = this.returnEdit(
                            param,
                            handleCode,
                            apiHandleId,
                            document,
                            servicePattern,
                            isPrivate,
                            errorMessage,
                            receivedDate,
                            trafficPreviousMonth,
                            trafficBeforehandMonth,
                            servicePatternGroup,
                            trafficPreviousMonthGroup,
                            trafficBeforehandMonthGroup,
                            trafficPreviousMonthCouponOffGroup,
                            trafficBeforehandMonthCouponOffGroup,
                            trafficPreviousMonthCouponOff,
                            trafficBeforehandMonthCouponOff,
                            version,
                            lineUsageStatus,
                        );
                        super.debug(
                            param.tenantId,
                            param.requestHeader.sequenceNo,
                            MsgKeysConstants.APLTID0002,
                            this.getClassName(),
                            "service",
                        );
                        return lineUseAcquisitionOutputDto;
                    } else {
                        servicePattern = String(
                            plansEntityList.at(0).refServicePattern,
                        );
                    }
                }

                if ("1".equals(version)) {
                    let linesInfo: LinesEntity = null;
                    try {
                        // バージョン指定ありの場合、回線情報から利用状態を取得
                        linesInfo = await this.aPILinesDAO.getLineInfo(lineNo);
                        if (
                            linesInfo != null &&
                            linesInfo.usageStatus !== null
                        ) {
                            lineUsageStatus = linesInfo.usageStatus.toString();
                        }
                    } catch (e) {
                        if (isSQLException(e)) {
                            // DBアクセスリトライエラーの場合
                            handleCode = ResultCdConstants.CODE_020201;
                        }
                        throw e;
                    }
                }

                try {
                    // 該当回線の前月の通信量を取得する
                    const trafficPreviousMonthEntity: LineTrafficsEntity =
                        await this.aPILinesDAO.getTraffic(
                            lineNo,
                            MvnoUtil.getTrafficPreviousMonth(-1)[0],
                            MvnoUtil.getTrafficPreviousMonth(-1)[1],
                        );
                    if (
                        trafficPreviousMonthEntity != null &&
                        trafficPreviousMonthEntity.traffic !== null
                    ) {
                        trafficPreviousMonth =
                            trafficPreviousMonthEntity.traffic.toString();
                    }
                    if (
                        trafficPreviousMonthEntity != null &&
                        trafficPreviousMonthEntity.trafficCouponOff !== null
                    ) {
                        trafficPreviousMonthCouponOff =
                            trafficPreviousMonthEntity.trafficCouponOff;
                    }
                } catch (e) {
                    if (isSQLException(e)) {
                        // DBアクセスリトライエラーの場合
                        handleCode = ResultCdConstants.CODE_020202;
                    }
                    throw e;
                }

                try {
                    // 該当回線の前々月の通信量を取得する
                    const trafficBeforehandMonthEntity: LineTrafficsEntity =
                        await this.aPILinesDAO.getTraffic(
                            lineNo,
                            MvnoUtil.getTrafficPreviousMonth(-2)[0],
                            MvnoUtil.getTrafficPreviousMonth(-2)[1],
                        );
                    if (
                        trafficBeforehandMonthEntity != null &&
                        trafficBeforehandMonthEntity.traffic !== null
                    ) {
                        trafficBeforehandMonth =
                            trafficBeforehandMonthEntity.traffic.toString();
                    }
                    if (
                        trafficBeforehandMonthEntity != null &&
                        trafficBeforehandMonthEntity.trafficCouponOff !== null
                    ) {
                        trafficBeforehandMonthCouponOff =
                            trafficBeforehandMonthEntity.trafficCouponOff;
                    }
                } catch (e) {
                    if (isSQLException(e)) {
                        // DBアクセスリトライエラーの場合
                        handleCode = ResultCdConstants.CODE_020203;
                    }
                    throw e;
                }

                let tpcConnectionResults: [boolean, string, string] = null;
                try {
                    // TPC情報を取得する。
                    tpcConnectionResults =
                        await this.tenantManage.checkTpcConnection(
                            param.tenantId,
                        );
                } catch (e) {
                    if (isSQLException(e)) {
                        // DBアクセスリトライエラーの場合
                        handleCode = ResultCdConstants.CODE_020301;
                    }
                    throw e;
                }
                // 変数「TPC情報取得結果」より判断する
                if (!tpcConnectionResults[0]) {
                    handleCode = ResultCdConstants.CODE_020301;
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APLTIW0008,
                        param.tenantId,
                    );
                    // 返却値編集
                    lineUseAcquisitionOutputDto = this.returnEdit(
                        param,
                        handleCode,
                        apiHandleId,
                        document,
                        servicePattern,
                        isPrivate,
                        errorMessage,
                        receivedDate,
                        trafficPreviousMonth,
                        trafficBeforehandMonth,
                        servicePatternGroup,
                        trafficPreviousMonthGroup,
                        trafficBeforehandMonthGroup,
                        trafficPreviousMonthCouponOffGroup,
                        trafficBeforehandMonthCouponOffGroup,
                        trafficPreviousMonthCouponOff,
                        trafficBeforehandMonthCouponOff,
                        version,
                        lineUsageStatus,
                    );
                    super.debug(
                        param.tenantId,
                        param.requestHeader.sequenceNo,
                        MsgKeysConstants.APLTID0002,
                        this.getClassName(),
                        "service",
                    );
                    return lineUseAcquisitionOutputDto;
                }

                // NOTE SOAP API call is not wrapped in a try-catch block in the original code
                // 変数「回線運用データ情報結果」を判定する
                const paraList: ParameterName[] = [];
                const parameterName = new ParameterName();
                parameterName.setName("msisdn");
                const lineNoNew = "81" + lineNo.substring(1);
                parameterName.setValue(lineNoNew);
                paraList.push(parameterName);
                const sOAPCommonOutputDto =
                    await this.sOAPCommon.callWebSoapApi(
                        "serviceProfileQuery",
                        "AllView",
                        "",
                        paraList,
                        tenantId,
                        sequenceNo,
                        tpcConnectionResults[1],
                    );

                if (
                    ResultCdConstants.CODE_000951.equals(
                        sOAPCommonOutputDto.getProcessCode(),
                    )
                ) {
                    // SG取得がNG
                    handleCode = ResultCdConstants.CODE_020401;
                    // ログに指定するErrorInfomation はdocument がnull なので空文字
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APLTIW0007,
                        "",
                    );
                    // 返却値編集
                    lineUseAcquisitionOutputDto = this.returnEdit(
                        param,
                        handleCode,
                        apiHandleId,
                        document,
                        servicePattern,
                        isPrivate,
                        errorMessage,
                        receivedDate,
                        trafficPreviousMonth,
                        trafficBeforehandMonth,
                        servicePatternGroup,
                        trafficPreviousMonthGroup,
                        trafficBeforehandMonthGroup,
                        trafficPreviousMonthCouponOffGroup,
                        trafficBeforehandMonthCouponOffGroup,
                        trafficPreviousMonthCouponOff,
                        trafficBeforehandMonthCouponOff,
                        version,
                        lineUsageStatus,
                    );
                    super.debug(
                        param.tenantId,
                        param.requestHeader.sequenceNo,
                        MsgKeysConstants.APLTID0002,
                        this.getClassName(),
                        "service",
                    );
                    return lineUseAcquisitionOutputDto;
                }

                document = sOAPCommonOutputDto.getDoc();
                const resultStr = this.sOAPCommon.getNodeContent(
                    document,
                    "//Result",
                );
                if (
                    sOAPCommonOutputDto.isError() ||
                    CONST_RESULT.equals(resultStr)
                ) {
                    handleCode = ResultCdConstants.CODE_020401;
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APLTIW0007,
                        this.sOAPCommon.getNodeContent(
                            document,
                            "//ErrorInfomation",
                        ),
                    );
                    // 返却値編集
                    lineUseAcquisitionOutputDto = this.returnEdit(
                        param,
                        handleCode,
                        apiHandleId,
                        document,
                        servicePattern,
                        isPrivate,
                        errorMessage,
                        receivedDate,
                        trafficPreviousMonth,
                        trafficBeforehandMonth,
                        servicePatternGroup,
                        trafficPreviousMonthGroup,
                        trafficBeforehandMonthGroup,
                        trafficPreviousMonthCouponOffGroup,
                        trafficBeforehandMonthCouponOffGroup,
                        trafficPreviousMonthCouponOff,
                        trafficBeforehandMonthCouponOff,
                        version,
                        lineUsageStatus,
                    );
                    super.debug(
                        param.tenantId,
                        param.requestHeader.sequenceNo,
                        MsgKeysConstants.APLTID0002,
                        this.getClassName(),
                        "service",
                    );
                    return lineUseAcquisitionOutputDto;
                }

                // グループデータ取得の判定
                if (
                    StringUtils.equals("4", addOption) &&
                    !StringUtils.equals("1", getIpAddress)
                ) {
                    // 回線グループID取得
                    lineGroupId = MvnoUtil.addSpaceAfterStr(
                        this.sOAPCommon.getNodeContent(
                            document,
                            "//GroupProfileInformation/grpnum",
                        ),
                        0,
                    );
                    if (StringUtils.equals("0", lineGroupId.trim())) {
                        lineGroupId = "";
                    }
                    // グループプランIDの利用チェック
                    if (!CONST_SHANAITENANTID.equals(shaNaiTenantType)) {
                        let usedPlanInfo: string = null;
                        try {
                            usedPlanInfo =
                                await this.aPILinesGroupDAO.getUsingGroupPlans(
                                    potalGroupPlanID,
                                    lineGroupId,
                                );
                        } catch (e) {
                            if (isSQLException(e)) {
                                // DBアクセスリトライエラーの場合
                                handleCode = ResultCdConstants.CODE_020501;
                            }
                            throw e;
                        }
                        if (CheckUtil.checkIsNotNull(usedPlanInfo)) {
                            handleCode = ResultCdConstants.CODE_020501;
                            super.warn(
                                tenantId,
                                sequenceNo,
                                MsgKeysConstants.APLTIW0012,
                                potalGroupPlanID,
                            );
                            // 返却値編集
                            lineUseAcquisitionOutputDto = this.returnEdit(
                                param,
                                handleCode,
                                apiHandleId,
                                document,
                                servicePattern,
                                isPrivate,
                                errorMessage,
                                receivedDate,
                                trafficPreviousMonth,
                                trafficBeforehandMonth,
                                servicePatternGroup,
                                trafficPreviousMonthGroup,
                                trafficBeforehandMonthGroup,
                                trafficPreviousMonthCouponOffGroup,
                                trafficBeforehandMonthCouponOffGroup,
                                trafficPreviousMonthCouponOff,
                                trafficBeforehandMonthCouponOff,
                                version,
                                lineUsageStatus,
                            );
                            super.debug(
                                param.tenantId,
                                param.requestHeader.sequenceNo,
                                MsgKeysConstants.APLTID0002,
                                this.getClassName(),
                                "service",
                            );
                            return lineUseAcquisitionOutputDto;
                        }
                    }
                    // ロジック処理
                    let groupPlansEntity: GroupPlansEntity = null;
                    try {
                        // 回線グループプランテーブルより該当グループの参照サービスパターンを取得する
                        groupPlansEntity =
                            await this.aPICommonDAO.getGroupPlans(
                                potalGroupPlanID,
                            );
                    } catch (e) {
                        if (isSQLException(e)) {
                            // DBアクセスリトライエラーの場合
                            handleCode = ResultCdConstants.CODE_020502;
                        }
                        throw e;
                    }
                    if (groupPlansEntity !== null) {
                        if (
                            CheckUtil.checkIsNull(
                                groupPlansEntity.refServicePattern,
                            )
                        ) {
                            servicePatternGroup = "";
                        } else {
                            servicePatternGroup = String(
                                groupPlansEntity.refServicePattern,
                            );
                        }
                    }
                    if (
                        groupPlansEntity === null ||
                        CheckUtil.checkIsNotNull(servicePatternGroup)
                    ) {
                        handleCode = ResultCdConstants.CODE_020502;
                        super.warn(
                            tenantId,
                            sequenceNo,
                            MsgKeysConstants.APLTIW0013,
                        );
                        // 返却値編集
                        lineUseAcquisitionOutputDto = this.returnEdit(
                            param,
                            handleCode,
                            apiHandleId,
                            document,
                            servicePattern,
                            isPrivate,
                            errorMessage,
                            receivedDate,
                            trafficPreviousMonth,
                            trafficBeforehandMonth,
                            servicePatternGroup,
                            trafficPreviousMonthGroup,
                            trafficBeforehandMonthGroup,
                            trafficPreviousMonthCouponOffGroup,
                            trafficBeforehandMonthCouponOffGroup,
                            trafficPreviousMonthCouponOff,
                            trafficBeforehandMonthCouponOff,
                            version,
                            lineUsageStatus,
                        );
                        super.debug(
                            param.tenantId,
                            param.requestHeader.sequenceNo,
                            MsgKeysConstants.APLTID0002,
                            this.getClassName(),
                            "service",
                        );
                        return lineUseAcquisitionOutputDto;
                    }
                    let trafficPreviousMonthEntityGroup: GroupTrafficsEntity =
                        null;
                    try {
                        // 該当回線グループの前月の通信量を取得する
                        trafficPreviousMonthEntityGroup =
                            await this.aPILinesGroupDAO.getTraffic(
                                lineGroupId,
                                MvnoUtil.getTrafficPreviousMonth(-1)[0],
                                MvnoUtil.getTrafficPreviousMonth(-1)[1],
                            );
                        if (
                            trafficPreviousMonthEntityGroup != null &&
                            trafficPreviousMonthEntityGroup.traffic !== null
                        ) {
                            trafficPreviousMonthGroup =
                                trafficPreviousMonthEntityGroup.traffic.toString();
                        }
                        // 該当回線グループの前月の通信量（クーポンOFF）を取得する
                        if (
                            trafficPreviousMonthEntityGroup != null &&
                            trafficPreviousMonthEntityGroup.trafficCouponOff !==
                                null
                        ) {
                            trafficPreviousMonthCouponOffGroup =
                                trafficPreviousMonthEntityGroup.trafficCouponOff;
                        }
                    } catch (e) {
                        if (isSQLException(e)) {
                            // DBアクセスリトライエラーの場合
                            handleCode = ResultCdConstants.CODE_020503;
                        }
                        throw e;
                    }
                    let trafficBeforehandMonthEntityGroup: GroupTrafficsEntity =
                        null;
                    try {
                        // 該当回線グループの前々月の通信量を取得する
                        trafficBeforehandMonthEntityGroup =
                            await this.aPILinesGroupDAO.getTraffic(
                                lineGroupId,
                                MvnoUtil.getTrafficPreviousMonth(-2)[0],
                                MvnoUtil.getTrafficPreviousMonth(-2)[1],
                            );
                        if (
                            trafficBeforehandMonthEntityGroup !== null &&
                            trafficBeforehandMonthEntityGroup.traffic !== null
                        ) {
                            trafficBeforehandMonthGroup =
                                trafficBeforehandMonthEntityGroup.traffic.toString();
                        }
                        // 該当回線グループの前々月の通信量（クーポンOFF）を取得する
                        if (
                            trafficBeforehandMonthEntityGroup !== null &&
                            trafficBeforehandMonthEntityGroup.trafficCouponOff !==
                                null
                        ) {
                            trafficBeforehandMonthCouponOffGroup =
                                trafficBeforehandMonthEntityGroup.trafficCouponOff;
                        }
                    } catch (e) {
                        if (isSQLException(e)) {
                            // DBアクセスリトライエラーの場合
                            handleCode = ResultCdConstants.CODE_020504;
                        }
                        throw e;
                    }
                }

                // 返却値編集
                lineUseAcquisitionOutputDto = this.returnEdit(
                    param,
                    handleCode,
                    apiHandleId,
                    document,
                    servicePattern,
                    isPrivate,
                    errorMessage,
                    receivedDate,
                    trafficPreviousMonth,
                    trafficBeforehandMonth,
                    servicePatternGroup,
                    trafficPreviousMonthGroup,
                    trafficBeforehandMonthGroup,
                    trafficPreviousMonthCouponOffGroup,
                    trafficBeforehandMonthCouponOffGroup,
                    trafficPreviousMonthCouponOff,
                    trafficBeforehandMonthCouponOff,
                    version,
                    lineUsageStatus,
                );

                super.debug(
                    param.tenantId,
                    param.requestHeader.sequenceNo,
                    MsgKeysConstants.APLTID0002,
                    this.getClassName(),
                    "service",
                );
                return lineUseAcquisitionOutputDto;
            } else {
                handleCode = ResultCdConstants.CODE_020101;
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APLTIW0001,
                    CONST_LINENO,
                    lineNo,
                );
                // 返却値編集
                lineUseAcquisitionOutputDto = this.returnEdit(
                    param,
                    handleCode,
                    apiHandleId,
                    document,
                    servicePattern,
                    isPrivate,
                    errorMessage,
                    receivedDate,
                    trafficPreviousMonth,
                    trafficBeforehandMonth,
                    servicePatternGroup,
                    trafficPreviousMonthGroup,
                    trafficBeforehandMonthGroup,
                    trafficPreviousMonthCouponOffGroup,
                    trafficBeforehandMonthCouponOffGroup,
                    trafficPreviousMonthCouponOff,
                    trafficBeforehandMonthCouponOff,
                    version,
                    lineUsageStatus,
                );
                super.debug(
                    param.tenantId,
                    param.requestHeader.sequenceNo,
                    MsgKeysConstants.APLTID0002,
                    this.getClassName(),
                    "service",
                );
                return lineUseAcquisitionOutputDto;
            }
        } catch (e) {
            if (isSQLException(e)) {
                // DBアクセスリトライエラーの場合
                // リターンコード値は各エラー時にて設定済み
                super.error(
                    e,
                    param.tenantId,
                    param.requestHeader.sequenceNo,
                    MsgKeysConstants.APCOME0402,
                    functionType,
                );

                lineUseAcquisitionOutputDto = this.returnEdit(
                    param,
                    handleCode,
                    apiHandleId,
                    document,
                    servicePattern,
                    isPrivate,
                    errorMessage,
                    receivedDate,
                    trafficPreviousMonth,
                    trafficBeforehandMonth,
                    servicePatternGroup,
                    trafficPreviousMonthGroup,
                    trafficBeforehandMonthGroup,
                    trafficPreviousMonthCouponOffGroup,
                    trafficBeforehandMonthCouponOffGroup,
                    trafficPreviousMonthCouponOff,
                    trafficBeforehandMonthCouponOff,
                    version,
                    lineUsageStatus,
                );

                return lineUseAcquisitionOutputDto;
            } else if (SOAPException.isSOAPException(e)) {
                handleCode = ResultCdConstants.CODE_020401;
                // 返却値編集
                lineUseAcquisitionOutputDto = this.returnEdit(
                    param,
                    handleCode,
                    apiHandleId,
                    document,
                    servicePattern,
                    isPrivate,
                    errorMessage,
                    receivedDate,
                    trafficPreviousMonth,
                    trafficBeforehandMonth,
                    servicePatternGroup,
                    trafficPreviousMonthGroup,
                    trafficBeforehandMonthGroup,
                    trafficPreviousMonthCouponOffGroup,
                    trafficBeforehandMonthCouponOffGroup,
                    trafficPreviousMonthCouponOff,
                    trafficBeforehandMonthCouponOff,
                    version,
                    lineUsageStatus,
                );
                super.debug(
                    param.tenantId,
                    param.requestHeader.sequenceNo,
                    MsgKeysConstants.APLTID0002,
                    this.getClassName(),
                    "service",
                );
                return lineUseAcquisitionOutputDto;
            } else {
                handleCode = ResultCdConstants.CODE_999999;
                super.error(
                    e as Error,
                    param.tenantId,
                    param.requestHeader.sequenceNo,
                    MsgKeysConstants.APCOME0401,
                    e.message,
                );
                // 返却値編集
                lineUseAcquisitionOutputDto = this.returnEdit(
                    param,
                    handleCode,
                    apiHandleId,
                    document,
                    servicePattern,
                    isPrivate,
                    errorMessage,
                    receivedDate,
                    trafficPreviousMonth,
                    trafficBeforehandMonth,
                    servicePatternGroup,
                    trafficPreviousMonthGroup,
                    trafficBeforehandMonthGroup,
                    trafficPreviousMonthCouponOffGroup,
                    trafficBeforehandMonthCouponOffGroup,
                    trafficPreviousMonthCouponOff,
                    trafficBeforehandMonthCouponOff,
                    version,
                    lineUsageStatus,
                );
                super.debug(
                    param.tenantId,
                    param.requestHeader.sequenceNo,
                    MsgKeysConstants.APLTID0002,
                    this.getClassName(),
                    "service",
                );
                return lineUseAcquisitionOutputDto;
            }
        }
    }

    /**
     * 返信編集。<BR>
     *
     * @param pram 回線基本情報取得インプット
     * @param rtnHandleCode 処理コード
     * @param rtnapiHandleId API処理ID
     * @param document 回線運用データ情報結果
     * @param servicePattern サービスパターン
     * @param receivedDate REST API共通処理が呼び出されたシステム日付
     * @param isPrivate 内部呼出フラグ
     * @param errorMessage 変数「エラーメッセージ」
     * @param trafficPreviousMonth 単回線の前月の通信量
     * @param trafficBeforehandMonth 単回線の前々月の通信量
     * @param servicePatternGroup グループサービスパターン
     * @param trafficPreviousMonthGroup グループ回線の前月の通信量
     * @param trafficBeforehandMonthGroup グループ回線の前々月の通信量
     * @param trafficPreviousMonthCouponOffGroup グループ回線の前月の通信量（クーポンOFF）
     * @param String trafficBeforehandMonthCouponOffGroup グループ回線の前々月の通信量（クーポンOFF）
     * @param additionArg 追加引数
     * [0] 単回線クーポンOFF時前月通信量
     * [1] 単回線クーポンOFF時前々月通信量
     * [2] バージョン
     * [3] 利用状態
     * @return LineUseAcquisitionOutputDto 回線基本情報取得アウトプット
     */
    public returnEdit(
        pram: LineUseAcquisitionInputDto,
        rtnHandleCode: string,
        rtnapiHandleId: string,
        document: Document,
        servicePattern: string,
        isPrivate: boolean,
        errorMessage: string,
        receivedDate: string,
        trafficPreviousMonth: string,
        trafficBeforehandMonth: string,
        servicePatternGroup: string,
        trafficPreviousMonthGroup: string,
        trafficBeforehandMonthGroup: string,
        trafficPreviousMonthCouponOffGroup: string,
        trafficBeforehandMonthCouponOffGroup: string,
        ...additionArg: string[]
    ): LineUseAcquisitionBaseOutputDto {
        const responseHeader: ResponseHeader = {
            sequenceNo: MvnoUtil.addSpaceAfterStr(
                pram.requestHeader.sequenceNo,
                7,
            ),
            receivedDate,
            processCode: MvnoUtil.addSpaceAfterStr(rtnHandleCode, 6),
            apiProcessID: MvnoUtil.addSpaceAfterStr(rtnapiHandleId, 10),
        };

        const version = String(additionArg[2]); // バージョン
        const lineUsageStatus = String(additionArg[3]); // 利用状態

        if ("1".equals(getStringParameter(pram.getIpAddress))) {
            // NOTE removed `isPrivate` block
            const returnPram =
                getEmpty_LineUseAcquisitionIpAddressOutput(responseHeader);
            try {
                if (!ResultCdConstants.CODE_000000.equals(rtnHandleCode)) {
                    returnPram.jsonBody.terminalIpAddress =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                } else if (document !== null) {
                    returnPram.jsonBody.terminalIpAddress =
                        this.sOAPCommon.getNodeContent(
                            document,
                            "//dyn_sub_info/dyn_ue_ipadr",
                        );
                }
            } catch (e) {
                // 処理コード
                super.error(
                    e,
                    pram.tenantId,
                    pram.requestHeader.sequenceNo,
                    MsgKeysConstants.APCOME0401,
                    e.message,
                );
                returnPram.jsonBody.responseHeader.processCode =
                    ResultCdConstants.CODE_999999;
                return returnPram;
            }
            return returnPram;
        }

        // NOTE skipped `isPrivate` block
        const returnPram = getEmpty_LineUseAcquisitionOutput(responseHeader);
        const returnPramVersion =
            getEmpty_LineUseAcquisitionVersionOutput(responseHeader);

        if (!ResultCdConstants.CODE_000000.equals(rtnHandleCode)) {
            // 当日の通信量
            returnPram.jsonBody.trafficOneDay = MvnoUtil.addSpaceAfterStr(
                null,
                0,
            );
            // 1日前の通信量
            returnPram.jsonBody.traffic1dayAgo = MvnoUtil.addSpaceAfterStr(
                null,
                0,
            );
            // 2日前の通信量
            returnPram.jsonBody.traffic2daysAgo = MvnoUtil.addSpaceAfterStr(
                null,
                0,
            );
            // 3日前の通信量
            returnPram.jsonBody.traffic3daysAgo = MvnoUtil.addSpaceAfterStr(
                null,
                0,
            );
            // 4日前の通信量
            returnPram.jsonBody.traffic4daysAgo = MvnoUtil.addSpaceAfterStr(
                null,
                0,
            );
            // 5日前の通信量
            returnPram.jsonBody.traffic5daysAgo = MvnoUtil.addSpaceAfterStr(
                null,
                0,
            );
            // 6日前の通信量
            returnPram.jsonBody.traffic6daysAgo = MvnoUtil.addSpaceAfterStr(
                null,
                0,
            );
            // 7日前の通信量
            returnPram.jsonBody.traffic7daysAgo = MvnoUtil.addSpaceAfterStr(
                null,
                0,
            );
            // 8日前の通信量
            returnPram.jsonBody.traffic8daysAgo = MvnoUtil.addSpaceAfterStr(
                null,
                0,
            );
            // 9日前の通信量
            returnPram.jsonBody.traffic9daysAgo = MvnoUtil.addSpaceAfterStr(
                null,
                0,
            );
            // 10日前の通信量
            returnPram.jsonBody.traffic10daysAgo = MvnoUtil.addSpaceAfterStr(
                null,
                0,
            );
            // 11日前の通信量
            returnPram.jsonBody.traffic11daysAgo = MvnoUtil.addSpaceAfterStr(
                null,
                0,
            );
            // 12日前の通信量
            returnPram.jsonBody.traffic12daysAgo = MvnoUtil.addSpaceAfterStr(
                null,
                0,
            );
            // 13日前の通信量
            returnPram.jsonBody.traffic13daysAgo = MvnoUtil.addSpaceAfterStr(
                null,
                0,
            );
            // 14日前の通信量
            returnPram.jsonBody.traffic14daysAgo = MvnoUtil.addSpaceAfterStr(
                null,
                0,
            );
            // 15日前の通信量
            returnPram.jsonBody.traffic15daysAgo = MvnoUtil.addSpaceAfterStr(
                null,
                0,
            );
            // 16日前の通信量
            returnPram.jsonBody.traffic16daysAgo = MvnoUtil.addSpaceAfterStr(
                null,
                0,
            );
            // 17日前の通信量
            returnPram.jsonBody.traffic17daysAgo = MvnoUtil.addSpaceAfterStr(
                null,
                0,
            );
            // 18日前の通信量
            returnPram.jsonBody.traffic18daysAgo = MvnoUtil.addSpaceAfterStr(
                null,
                0,
            );
            // 19日前の通信量
            returnPram.jsonBody.traffic19daysAgo = MvnoUtil.addSpaceAfterStr(
                null,
                0,
            );
            // 20日前の通信量
            returnPram.jsonBody.traffic20daysAgo = MvnoUtil.addSpaceAfterStr(
                null,
                0,
            );
            // 21日前の通信量
            returnPram.jsonBody.traffic21daysAgo = MvnoUtil.addSpaceAfterStr(
                null,
                0,
            );
            // 22日前の通信量
            returnPram.jsonBody.traffic22daysAgo = MvnoUtil.addSpaceAfterStr(
                null,
                0,
            );
            // 23日前の通信量
            returnPram.jsonBody.traffic23daysAgo = MvnoUtil.addSpaceAfterStr(
                null,
                0,
            );
            // 24日前の通信量
            returnPram.jsonBody.traffic24daysAgo = MvnoUtil.addSpaceAfterStr(
                null,
                0,
            );
            // 25日前の通信量
            returnPram.jsonBody.traffic25daysAgo = MvnoUtil.addSpaceAfterStr(
                null,
                0,
            );
            // 26日前の通信量
            returnPram.jsonBody.traffic26daysAgo = MvnoUtil.addSpaceAfterStr(
                null,
                0,
            );
            // 27日前の通信量
            returnPram.jsonBody.traffic27daysAgo = MvnoUtil.addSpaceAfterStr(
                null,
                0,
            );
            // 28日前の通信量
            returnPram.jsonBody.traffic28daysAgo = MvnoUtil.addSpaceAfterStr(
                null,
                0,
            );
            // 29日前の通信量
            returnPram.jsonBody.traffic29daysAgo = MvnoUtil.addSpaceAfterStr(
                null,
                0,
            );
            // 30日前の通信量
            returnPram.jsonBody.traffic30daysAgo = MvnoUtil.addSpaceAfterStr(
                null,
                0,
            );
            // 当月の通信量
            returnPram.jsonBody.trafficThisMonth = MvnoUtil.addSpaceAfterStr(
                null,
                0,
            );
            // 前月の通信量
            returnPram.jsonBody.trafficPreviousMonth =
                MvnoUtil.addSpaceAfterStr(null, 0);
            // 前々月の通信量
            returnPram.jsonBody.trafficBeforehandMonth =
                MvnoUtil.addSpaceAfterStr(null, 0);
            // 基本クーポン残容量
            returnPram.jsonBody.basicCouponRemains = MvnoUtil.addSpaceAfterStr(
                null,
                0,
            );
            // 基本クーポン有効期限
            returnPram.jsonBody.basicCouponTermValidity =
                MvnoUtil.addSpaceAfterStr(null, 0);
            // クーポン容量切れ時間
            returnPram.jsonBody.couponPieceTime = MvnoUtil.addSpaceAfterStr(
                null,
                0,
            );
            // 繰越クーポン1
            const carryingOverCoupon1 = getEmpty_CarryingOverCoupon1(); // getEmpty_CarryingOverCoupon1Dto();
            // 繰越クーポン1残容量
            carryingOverCoupon1.carryingOverCoupon1RemainCapacity =
                MvnoUtil.addSpaceAfterStr(null, 0);
            // 繰越クーポン1有効期限
            carryingOverCoupon1.carryingOverCoupon1TermValidity =
                MvnoUtil.addSpaceAfterStr(null, 0);
            returnPram.jsonBody.carryingOverCoupon1 = carryingOverCoupon1;
            // 繰越クーポン2
            const carryingOverCoupon2 = getEmpty_CarryingOverCoupon2(); // getEmpty_CarryingOverCoupon2Dto();
            // 繰越クーポン2残容量
            carryingOverCoupon2.carryingOverCoupon2RemainCapacity =
                MvnoUtil.addSpaceAfterStr(null, 0);
            // 繰越クーポン2有効期限
            carryingOverCoupon2.carryingOverCoupon2TermValidity =
                MvnoUtil.addSpaceAfterStr(null, 0);
            returnPram.jsonBody.carryingOverCoupon2 = carryingOverCoupon2;
            // 追加クーポン1
            const additionalCoupon1 = getEmpty_AdditionalCoupon1(); // getEmpty_AdditionalCoupon1Dto();
            // 追加クーポン1残容量
            additionalCoupon1.additionalCoupon1RemainCapacity =
                MvnoUtil.addSpaceAfterStr(null, 0);
            // 追加クーポン1有効期限
            additionalCoupon1.additionalCoupon1TermValidity =
                MvnoUtil.addSpaceAfterStr(null, 0);
            returnPram.jsonBody.additionalCoupon1 = additionalCoupon1;
            // 追加クーポン2
            const additionalCoupon2 = getEmpty_AdditionalCoupon2(); // getEmpty_AdditionalCoupon2Dto();
            // 追加クーポン2残容量
            additionalCoupon2.additionalCoupon2RemainCapacity =
                MvnoUtil.addSpaceAfterStr(null, 0);
            // 追加クーポン2有効期限
            additionalCoupon2.additionalCoupon2TermValidity =
                MvnoUtil.addSpaceAfterStr(null, 0);
            returnPram.jsonBody.additionalCoupon2 = additionalCoupon2;
            // 追加クーポン3
            const additionalCoupon3 = getEmpty_AdditionalCoupon3(); // getEmpty_AdditionalCoupon3Dto();
            // 追加クーポン3残容量
            additionalCoupon3.additionalCoupon3RemainCapacity =
                MvnoUtil.addSpaceAfterStr(null, 0);
            // 追加クーポン3有効期限
            additionalCoupon3.additionalCoupon3TermValidity =
                MvnoUtil.addSpaceAfterStr(null, 0);
            returnPram.jsonBody.additionalCoupon3 = additionalCoupon3;
            // 追加クーポン4
            const additionalCoupon4 = getEmpty_AdditionalCoupon4(); // getEmpty_AdditionalCoupon4Dto();
            // 追加クーポン4残容量
            additionalCoupon4.additionalCoupon4RemainCapacity =
                MvnoUtil.addSpaceAfterStr(null, 0);
            // 追加クーポン4有効期限
            additionalCoupon4.additionalCoupon4TermValidity =
                MvnoUtil.addSpaceAfterStr(null, 0);
            returnPram.jsonBody.additionalCoupon4 = additionalCoupon4;
            // 追加クーポン5
            const additionalCoupon5 = getEmpty_AdditionalCoupon5(); // getEmpty_AdditionalCoupon5Dto();
            // 追加クーポン5残容量
            additionalCoupon5.additionalCoupon5RemainCapacity =
                MvnoUtil.addSpaceAfterStr(null, 0);
            // 追加クーポン5有効期限
            additionalCoupon5.additionalCoupon5TermValidity =
                MvnoUtil.addSpaceAfterStr(null, 0);
            // 追加クーポン5残容量
            returnPram.jsonBody.additionalCoupon5 = additionalCoupon5;
            // クーポンON/OFF状態
            returnPram.jsonBody.couponOnOff = MvnoUtil.addSpaceAfterStr(
                null,
                0,
            );
            // ヘビーユーザ監視規制状態
            returnPram.jsonBody.heavyUserMonitorStatus =
                MvnoUtil.addSpaceAfterStr(null, 0);
            // ヘビーユーザ監視規制開始時間（月間）
            returnPram.jsonBody.heavyUserMonitorStartTimeMonth =
                MvnoUtil.addSpaceAfterStr(null, 0);
            // ヘビーユーザ監視規制開始時間（直近）
            returnPram.jsonBody.heavyUserMonitorStartTimeLatest =
                MvnoUtil.addSpaceAfterStr(null, 0);
            // プリペイド有効期限
            returnPram.jsonBody.prepaidTermValidity = MvnoUtil.addSpaceAfterStr(
                null,
                0,
            );
            // アクティベート状態
            returnPram.jsonBody.activateStatus = MvnoUtil.addSpaceAfterStr(
                null,
                0,
            );
            // 適用サービス状態（規制理由）
            returnPram.jsonBody.regulationCause = MvnoUtil.addSpaceAfterStr(
                null,
                0,
            );
            // 総量規制状態
            returnPram.jsonBody.totalVolumeControlStatus =
                MvnoUtil.addSpaceAfterStr(null, 0);
            // 回線グループID
            returnPram.jsonBody.lineGroupId = MvnoUtil.addSpaceAfterStr(
                null,
                0,
            );

            if ("1".equals(version)) {
                // バージョン指定ありの場合
                returnPramVersion.jsonBody = {
                    ...returnPram.jsonBody,
                    lineStatus: MvnoUtil.addSpaceAfterStr(null, 0),
                };
                return this.getReturnPram(
                    returnPramVersion,
                    getStringParameter(pram.addOption),
                    document,
                    isPrivate,
                    returnPramVersion.jsonBody.responseHeader.processCode,
                    servicePatternGroup,
                    trafficPreviousMonthGroup,
                    trafficBeforehandMonthGroup,
                    trafficPreviousMonthCouponOffGroup,
                    trafficBeforehandMonthCouponOffGroup,
                    ...additionArg,
                );
            } else {
                return this.getReturnPram(
                    returnPram,
                    getStringParameter(pram.addOption),
                    document,
                    isPrivate,
                    returnPram.jsonBody.responseHeader.processCode,
                    servicePatternGroup,
                    trafficPreviousMonthGroup,
                    trafficBeforehandMonthGroup,
                    trafficPreviousMonthCouponOffGroup,
                    trafficBeforehandMonthCouponOffGroup,
                    ...additionArg,
                );
            }
        } else if (document !== null) {
            try {
                // ボディ情報（正常系）を設定する
                let trafficdayAgo = "";
                let day = "";
                const setTrafficdayAgo = new Array(31).fill("");

                day = Check.getPreXDay(0);
                let tagFlg = false;
                tagFlg = this.sOAPCommon.CheckNodeExist(
                    document,
                    "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_acmlt_lst_info/dyn_" +
                        day,
                );
                if (tagFlg) {
                    for (let i = 0; i <= 30; i++) {
                        day = Check.getPreXDay(i);
                        trafficdayAgo = "";
                        trafficdayAgo = this.sOAPCommon.getNodeContent(
                            document,
                            "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_acmlt_lst_info/dyn_" +
                                day,
                        );
                        setTrafficdayAgo[i] = trafficdayAgo;
                    }
                } else {
                    for (let i = 1; i <= 31; i++) {
                        day = Check.getPreXDay(i);
                        trafficdayAgo = "";
                        trafficdayAgo = this.sOAPCommon.getNodeContent(
                            document,
                            "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_acmlt_lst_info/dyn_" +
                                day,
                        );
                        setTrafficdayAgo[i - 1] = trafficdayAgo;
                    }
                }
                // 当日の通信量
                returnPram.jsonBody.trafficOneDay = MvnoUtil.addSpaceAfterStr(
                    setTrafficdayAgo[0],
                    0,
                );
                // 1日前の通信量
                returnPram.jsonBody.traffic1dayAgo = MvnoUtil.addSpaceAfterStr(
                    setTrafficdayAgo[1],
                    0,
                );
                // 2日前の通信量
                returnPram.jsonBody.traffic2daysAgo = MvnoUtil.addSpaceAfterStr(
                    setTrafficdayAgo[2],
                    0,
                );
                // 3日前の通信量
                returnPram.jsonBody.traffic3daysAgo = MvnoUtil.addSpaceAfterStr(
                    setTrafficdayAgo[3],
                    0,
                );
                // 4日前の通信量
                returnPram.jsonBody.traffic4daysAgo = MvnoUtil.addSpaceAfterStr(
                    setTrafficdayAgo[4],
                    0,
                );
                // 5日前の通信量
                returnPram.jsonBody.traffic5daysAgo = MvnoUtil.addSpaceAfterStr(
                    setTrafficdayAgo[5],
                    0,
                );
                // 6日前の通信量
                returnPram.jsonBody.traffic6daysAgo = MvnoUtil.addSpaceAfterStr(
                    setTrafficdayAgo[6],
                    0,
                );
                // 7日前の通信量
                returnPram.jsonBody.traffic7daysAgo = MvnoUtil.addSpaceAfterStr(
                    setTrafficdayAgo[7],
                    0,
                );
                // 8日前の通信量
                returnPram.jsonBody.traffic8daysAgo = MvnoUtil.addSpaceAfterStr(
                    setTrafficdayAgo[8],
                    0,
                );
                // 9日前の通信量
                returnPram.jsonBody.traffic9daysAgo = MvnoUtil.addSpaceAfterStr(
                    setTrafficdayAgo[9],
                    0,
                );
                // 10日前の通信量
                returnPram.jsonBody.traffic10daysAgo =
                    MvnoUtil.addSpaceAfterStr(setTrafficdayAgo[10], 0);
                // 11日前の通信量
                returnPram.jsonBody.traffic11daysAgo =
                    MvnoUtil.addSpaceAfterStr(setTrafficdayAgo[11], 0);
                // 12日前の通信量
                returnPram.jsonBody.traffic12daysAgo =
                    MvnoUtil.addSpaceAfterStr(setTrafficdayAgo[12], 0);
                // 13日前の通信量
                returnPram.jsonBody.traffic13daysAgo =
                    MvnoUtil.addSpaceAfterStr(setTrafficdayAgo[13], 0);
                // 14日前の通信量
                returnPram.jsonBody.traffic14daysAgo =
                    MvnoUtil.addSpaceAfterStr(setTrafficdayAgo[14], 0);
                // 15日前の通信量
                returnPram.jsonBody.traffic15daysAgo =
                    MvnoUtil.addSpaceAfterStr(setTrafficdayAgo[15], 0);
                // 16日前の通信量
                returnPram.jsonBody.traffic16daysAgo =
                    MvnoUtil.addSpaceAfterStr(setTrafficdayAgo[16], 0);
                // 17日前の通信量
                returnPram.jsonBody.traffic17daysAgo =
                    MvnoUtil.addSpaceAfterStr(setTrafficdayAgo[17], 0);
                // 18日前の通信量
                returnPram.jsonBody.traffic18daysAgo =
                    MvnoUtil.addSpaceAfterStr(setTrafficdayAgo[18], 0);
                // 19日前の通信量
                returnPram.jsonBody.traffic19daysAgo =
                    MvnoUtil.addSpaceAfterStr(setTrafficdayAgo[19], 0);
                // 20日前の通信量
                returnPram.jsonBody.traffic20daysAgo =
                    MvnoUtil.addSpaceAfterStr(setTrafficdayAgo[20], 0);
                // 21日前の通信量
                returnPram.jsonBody.traffic21daysAgo =
                    MvnoUtil.addSpaceAfterStr(setTrafficdayAgo[21], 0);
                // 22日前の通信量
                returnPram.jsonBody.traffic22daysAgo =
                    MvnoUtil.addSpaceAfterStr(setTrafficdayAgo[22], 0);
                // 23日前の通信量
                returnPram.jsonBody.traffic23daysAgo =
                    MvnoUtil.addSpaceAfterStr(setTrafficdayAgo[23], 0);
                // 24日前の通信量
                returnPram.jsonBody.traffic24daysAgo =
                    MvnoUtil.addSpaceAfterStr(setTrafficdayAgo[24], 0);
                // 25日前の通信量
                returnPram.jsonBody.traffic25daysAgo =
                    MvnoUtil.addSpaceAfterStr(setTrafficdayAgo[25], 0);
                // 26日前の通信量
                returnPram.jsonBody.traffic26daysAgo =
                    MvnoUtil.addSpaceAfterStr(setTrafficdayAgo[26], 0);
                // 27日前の通信量
                returnPram.jsonBody.traffic27daysAgo =
                    MvnoUtil.addSpaceAfterStr(setTrafficdayAgo[27], 0);
                // 28日前の通信量
                returnPram.jsonBody.traffic28daysAgo =
                    MvnoUtil.addSpaceAfterStr(setTrafficdayAgo[28], 0);
                // 29日前の通信量
                returnPram.jsonBody.traffic29daysAgo =
                    MvnoUtil.addSpaceAfterStr(setTrafficdayAgo[29], 0);
                // 30日前の通信量
                returnPram.jsonBody.traffic30daysAgo =
                    MvnoUtil.addSpaceAfterStr(setTrafficdayAgo[30], 0);
                // 当月の通信量
                returnPram.jsonBody.trafficThisMonth =
                    MvnoUtil.addSpaceAfterStr(
                        this.sOAPCommon.getNodeContent(
                            document,
                            "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_acmlt_month_info/dyn_use",
                        ),
                        0,
                    );
                // 前月の通信量
                returnPram.jsonBody.trafficPreviousMonth =
                    MvnoUtil.addSpaceAfterStr(trafficPreviousMonth, 0);
                // 前々月の通信量
                returnPram.jsonBody.trafficBeforehandMonth =
                    MvnoUtil.addSpaceAfterStr(trafficBeforehandMonth, 0);
                // 基本クーポン残容量
                let basicCouponRemains = MvnoUtil.addSpaceAfterStr(null, 0);
                // 基本クーポン有効期限
                let basicCouponTermValidity = MvnoUtil.addSpaceAfterStr(
                    null,
                    0,
                );
                if (
                    CONST_SERVICEPATTERN_1.equals(servicePattern) ||
                    CONST_SERVICEPATTERN_2.equals(servicePattern) ||
                    CONST_SERVICEPATTERN_3.equals(servicePattern) ||
                    CONST_SERVICEPATTERN_5.equals(servicePattern) ||
                    CONST_SERVICEPATTERN_8.equals(servicePattern) ||
                    CONST_SERVICEPATTERN_10.equals(servicePattern)
                ) {
                    // 基本クーポン残容量
                    const dyn_cap = this.sOAPCommon.getNodeContent(
                        document,
                        "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_month_info/dyn_cap",
                    );
                    const dyn_use = this.sOAPCommon.getNodeContent(
                        document,
                        "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_month_info/dyn_use",
                    );
                    if (
                        CheckUtil.checkIsNum(dyn_cap) &&
                        CheckUtil.checkIsNum(dyn_use)
                    ) {
                        const basicCouponRemainsL =
                            BigInt(dyn_cap) - BigInt(dyn_use);
                        basicCouponRemains = MvnoUtil.addSpaceAfterStr(
                            String(basicCouponRemainsL),
                            0,
                        );
                    }
                    // 基本クーポン有効期限
                    basicCouponTermValidity = MvnoUtil.addSpaceAfterStr(
                        this.sOAPCommon.getNodeContent(
                            document,
                            "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_month_info/dyn_exp_date",
                        ),
                        10,
                    );
                }
                // 基本クーポン残容量
                returnPram.jsonBody.basicCouponRemains = basicCouponRemains;
                // 基本クーポン有効期限
                returnPram.jsonBody.basicCouponTermValidity =
                    basicCouponTermValidity;
                // クーポン容量切れ時間
                let couponPieceTime = MvnoUtil.addSpaceAfterStr(null, 0);
                // クーポン容量切れ時間
                if (
                    CONST_SERVICEPATTERN_1.equals(servicePattern) ||
                    CONST_SERVICEPATTERN_2.equals(servicePattern) ||
                    CONST_SERVICEPATTERN_3.equals(servicePattern) ||
                    CONST_SERVICEPATTERN_4.equals(servicePattern) ||
                    CONST_SERVICEPATTERN_5.equals(servicePattern) ||
                    CONST_SERVICEPATTERN_6.equals(servicePattern) ||
                    CONST_SERVICEPATTERN_8.equals(servicePattern) ||
                    CONST_SERVICEPATTERN_10.equals(servicePattern)
                ) {
                    const dyn_reg_sta_month = this.sOAPCommon.getNodeContent(
                        document,
                        "//dyn_sub_info/dyn_node_info_0/dyn_reg_info/dyn_reg_sta_month",
                    );
                    const rtndyn_reg_sta_month =
                        CheckUtil.forMartdynDate(dyn_reg_sta_month);
                    couponPieceTime = MvnoUtil.addSpaceAfterStr(
                        rtndyn_reg_sta_month,
                        16,
                    );
                }
                // クーポン容量切れ時間
                returnPram.jsonBody.couponPieceTime = couponPieceTime;
                // 繰越クーポン1
                const carryingOverCoupon1 = getEmpty_CarryingOverCoupon1();
                // 繰越クーポン1残容量
                let carryingOverCoupon1RemainCapacity1 =
                    MvnoUtil.addSpaceAfterStr(null, 0);
                // 繰越クーポン1有効期限
                let carryingOverCoupon1TermValidity1 =
                    MvnoUtil.addSpaceAfterStr(null, 0);
                const tran_dyn_cap_1 = this.sOAPCommon.getNodeContent(
                    document,
                    "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_tran_info_0/dyn_cap",
                );
                const tran_dyn_use_1 = this.sOAPCommon.getNodeContent(
                    document,
                    "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_tran_info_0/dyn_use",
                );
                if (
                    CheckUtil.checkIsNum(tran_dyn_cap_1) &&
                    CheckUtil.checkIsNum(tran_dyn_use_1)
                ) {
                    const carryingOverCoupon1RemainCapacityL =
                        BigInt(tran_dyn_cap_1) - BigInt(tran_dyn_use_1);
                    // 繰越クーポン1残容量
                    carryingOverCoupon1RemainCapacity1 =
                        MvnoUtil.addSpaceAfterStr(
                            String(carryingOverCoupon1RemainCapacityL),
                            0,
                        );
                }
                // 繰越クーポン1有効期限
                carryingOverCoupon1TermValidity1 = MvnoUtil.addSpaceAfterStr(
                    this.sOAPCommon.getNodeContent(
                        document,
                        "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_tran_info_0/dyn_exp_date",
                    ),
                    10,
                );
                // 繰越クーポン1残容量
                carryingOverCoupon1.carryingOverCoupon1RemainCapacity =
                    carryingOverCoupon1RemainCapacity1;
                // 追加クーポン1有効期限
                carryingOverCoupon1.carryingOverCoupon1TermValidity =
                    carryingOverCoupon1TermValidity1;
                returnPram.jsonBody.carryingOverCoupon1 = carryingOverCoupon1;
                // 繰越クーポン2
                const carryingOverCoupon2 = getEmpty_CarryingOverCoupon2();
                // 繰越クーポン2残容量
                let carryingOverCoupon1RemainCapacity2 =
                    MvnoUtil.addSpaceAfterStr(null, 0);
                // 繰越クーポン2有効期限
                let carryingOverCoupon1TermValidity2 =
                    MvnoUtil.addSpaceAfterStr(null, 0);
                const tran_dyn_cap_2 = this.sOAPCommon.getNodeContent(
                    document,
                    "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_tran_info_1/dyn_cap",
                );
                const tran_dyn_use_2 = this.sOAPCommon.getNodeContent(
                    document,
                    "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_tran_info_1/dyn_use",
                );
                if (
                    CheckUtil.checkIsNum(tran_dyn_cap_2) &&
                    CheckUtil.checkIsNum(tran_dyn_use_2)
                ) {
                    const carryingOverCoupon1RemainCapacityL =
                        BigInt(tran_dyn_cap_2) - BigInt(tran_dyn_use_2);
                    // 繰越クーポン2残容量
                    carryingOverCoupon1RemainCapacity2 =
                        MvnoUtil.addSpaceAfterStr(
                            String(carryingOverCoupon1RemainCapacityL),
                            0,
                        );
                }
                // 繰越クーポン2有効期限
                carryingOverCoupon1TermValidity2 = MvnoUtil.addSpaceAfterStr(
                    this.sOAPCommon.getNodeContent(
                        document,
                        "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_tran_info_1/dyn_exp_date",
                    ),
                    10,
                );
                // 繰越クーポン2残容量
                carryingOverCoupon2.carryingOverCoupon2RemainCapacity =
                    carryingOverCoupon1RemainCapacity2;
                // 繰越クーポン2有効期限
                carryingOverCoupon2.carryingOverCoupon2TermValidity =
                    carryingOverCoupon1TermValidity2;
                returnPram.jsonBody.carryingOverCoupon2 = carryingOverCoupon2;
                // 追加クーポン1
                const additionalCoupon1 = getEmpty_AdditionalCoupon1();
                // 追加クーポン1残容量
                let additionalCoupon1RemainCapacity = MvnoUtil.addSpaceAfterStr(
                    null,
                    0,
                );
                // 追加クーポン1有効期限
                let additionalCoupon1TermValidity = MvnoUtil.addSpaceAfterStr(
                    null,
                    0,
                );
                if (
                    CONST_SERVICEPATTERN_1.equals(servicePattern) ||
                    CONST_SERVICEPATTERN_2.equals(servicePattern) ||
                    CONST_SERVICEPATTERN_4.equals(servicePattern) ||
                    CONST_SERVICEPATTERN_6.equals(servicePattern)
                ) {
                    const dyn_cap = this.sOAPCommon.getNodeContent(
                        document,
                        "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_add_info_0/dyn_cap",
                    );
                    const dyn_use = this.sOAPCommon.getNodeContent(
                        document,
                        "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_add_info_0/dyn_use",
                    );
                    if (
                        CheckUtil.checkIsNum(dyn_cap) &&
                        CheckUtil.checkIsNum(dyn_use)
                    ) {
                        const additionalCoupon1RemainCapacityL =
                            BigInt(dyn_cap) - BigInt(dyn_use);
                        // 追加クーポン1残容量
                        additionalCoupon1RemainCapacity =
                            MvnoUtil.addSpaceAfterStr(
                                String(additionalCoupon1RemainCapacityL),
                                0,
                            );
                    }
                    // 追加クーポン1有効期限
                    additionalCoupon1TermValidity = MvnoUtil.addSpaceAfterStr(
                        this.sOAPCommon.getNodeContent(
                            document,
                            "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_add_info_0/dyn_exp_date",
                        ),
                        10,
                    );
                } else if (CONST_SERVICEPATTERN_3.equals(servicePattern)) {
                    const dyn_cap = this.sOAPCommon.getNodeContent(
                        document,
                        "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_addbd_info_0/dyn_cap",
                    );
                    const dyn_use = this.sOAPCommon.getNodeContent(
                        document,
                        "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_addbd_info_0/dyn_use",
                    );
                    if (
                        CheckUtil.checkIsNum(dyn_cap) &&
                        CheckUtil.checkIsNum(dyn_use)
                    ) {
                        const additionalCoupon1RemainCapacityL =
                            BigInt(dyn_cap) - BigInt(dyn_use);
                        // 追加クーポン1残容量
                        additionalCoupon1RemainCapacity =
                            MvnoUtil.addSpaceAfterStr(
                                String(additionalCoupon1RemainCapacityL),
                                0,
                            );
                    }
                    // 追加クーポン1有効期限
                    additionalCoupon1TermValidity = MvnoUtil.addSpaceAfterStr(
                        this.sOAPCommon.getNodeContent(
                            document,
                            "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_addbd_info_0/dyn_exp_date",
                        ),
                        10,
                    );
                }
                // 追加クーポン1残容量
                additionalCoupon1.additionalCoupon1RemainCapacity =
                    additionalCoupon1RemainCapacity;
                // 追加クーポン1有効期限
                additionalCoupon1.additionalCoupon1TermValidity =
                    additionalCoupon1TermValidity;
                returnPram.jsonBody.additionalCoupon1 = additionalCoupon1;
                // 追加クーポン2
                const additionalCoupon2 = getEmpty_AdditionalCoupon2();
                // 追加クーポン2残容量
                let additionalCoupon2RemainCapacity = MvnoUtil.addSpaceAfterStr(
                    null,
                    0,
                );
                // 追加クーポン2有効期限
                let additionalCoupon2TermValidity = MvnoUtil.addSpaceAfterStr(
                    null,
                    0,
                );
                if (
                    CONST_SERVICEPATTERN_1.equals(servicePattern) ||
                    CONST_SERVICEPATTERN_2.equals(servicePattern) ||
                    CONST_SERVICEPATTERN_4.equals(servicePattern) ||
                    CONST_SERVICEPATTERN_6.equals(servicePattern)
                ) {
                    const dyn_cap = this.sOAPCommon.getNodeContent(
                        document,
                        "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_add_info_1/dyn_cap",
                    );
                    const dyn_use = this.sOAPCommon.getNodeContent(
                        document,
                        "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_add_info_1/dyn_use",
                    );
                    if (
                        CheckUtil.checkIsNum(dyn_cap) &&
                        CheckUtil.checkIsNum(dyn_use)
                    ) {
                        const additionalCoupon1RemainCapacityL =
                            BigInt(dyn_cap) - BigInt(dyn_use);
                        // 追加クーポン2残容量
                        additionalCoupon2RemainCapacity =
                            MvnoUtil.addSpaceAfterStr(
                                String(additionalCoupon1RemainCapacityL),
                                0,
                            );
                    }
                    // 追加クーポン2有効期限
                    additionalCoupon2TermValidity = MvnoUtil.addSpaceAfterStr(
                        this.sOAPCommon.getNodeContent(
                            document,
                            "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_add_info_1/dyn_exp_date",
                        ),
                        10,
                    );
                } else if (CONST_SERVICEPATTERN_3.equals(servicePattern)) {
                    const dyn_cap = this.sOAPCommon.getNodeContent(
                        document,
                        "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_addbd_info_1/dyn_cap",
                    );
                    const dyn_use = this.sOAPCommon.getNodeContent(
                        document,
                        "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_addbd_info_1/dyn_use",
                    );
                    if (
                        CheckUtil.checkIsNum(dyn_cap) &&
                        CheckUtil.checkIsNum(dyn_use)
                    ) {
                        const additionalCoupon1RemainCapacityL =
                            BigInt(dyn_cap) - BigInt(dyn_use);
                        // 追加クーポン2残容量
                        additionalCoupon2RemainCapacity =
                            MvnoUtil.addSpaceAfterStr(
                                String(additionalCoupon1RemainCapacityL),
                                0,
                            );
                    }
                    // 追加クーポン2有効期限
                    additionalCoupon2TermValidity = MvnoUtil.addSpaceAfterStr(
                        this.sOAPCommon.getNodeContent(
                            document,
                            "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_addbd_info_1/dyn_exp_date",
                        ),
                        10,
                    );
                }
                // 追加クーポン2残容量
                additionalCoupon2.additionalCoupon2RemainCapacity =
                    additionalCoupon2RemainCapacity;
                // 追加クーポン2有効期限
                additionalCoupon2.additionalCoupon2TermValidity =
                    additionalCoupon2TermValidity;
                returnPram.jsonBody.additionalCoupon2 = additionalCoupon2;
                // 追加クーポン3
                const additionalCoupon3 = getEmpty_AdditionalCoupon3();
                // 追加クーポン3残容量
                let additionalCoupon3RemainCapacity = MvnoUtil.addSpaceAfterStr(
                    null,
                    0,
                );
                // 追加クーポン3有効期限
                let additionalCoupon3TermValidity = MvnoUtil.addSpaceAfterStr(
                    null,
                    0,
                );
                if (
                    CONST_SERVICEPATTERN_1.equals(servicePattern) ||
                    CONST_SERVICEPATTERN_2.equals(servicePattern) ||
                    CONST_SERVICEPATTERN_4.equals(servicePattern) ||
                    CONST_SERVICEPATTERN_6.equals(servicePattern)
                ) {
                    const dyn_cap = this.sOAPCommon.getNodeContent(
                        document,
                        "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_add_info_2/dyn_cap",
                    );
                    const dyn_use = this.sOAPCommon.getNodeContent(
                        document,
                        "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_add_info_2/dyn_use",
                    );
                    if (
                        CheckUtil.checkIsNum(dyn_cap) &&
                        CheckUtil.checkIsNum(dyn_use)
                    ) {
                        const additionaCoupon1RemainCapacityL =
                            BigInt(dyn_cap) - BigInt(dyn_use);
                        // 追加クーポン3残容量
                        additionalCoupon3RemainCapacity =
                            MvnoUtil.addSpaceAfterStr(
                                String(additionaCoupon1RemainCapacityL),
                                0,
                            );
                    }
                    // 追加クーポン3有効期限
                    additionalCoupon3TermValidity = MvnoUtil.addSpaceAfterStr(
                        this.sOAPCommon.getNodeContent(
                            document,
                            "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_add_info_2/dyn_exp_date",
                        ),
                        10,
                    );
                } else if (CONST_SERVICEPATTERN_3.equals(servicePattern)) {
                    const dyn_cap = this.sOAPCommon.getNodeContent(
                        document,
                        "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_addbd_info_2/dyn_cap",
                    );
                    const dyn_use = this.sOAPCommon.getNodeContent(
                        document,
                        "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_addbd_info_2/dyn_use",
                    );
                    if (
                        CheckUtil.checkIsNum(dyn_cap) &&
                        CheckUtil.checkIsNum(dyn_use)
                    ) {
                        const additionalCoupon1RemainCapacityL =
                            BigInt(dyn_cap) - BigInt(dyn_use);
                        // 追加クーポン3残容量
                        additionalCoupon3RemainCapacity =
                            MvnoUtil.addSpaceAfterStr(
                                String(additionalCoupon1RemainCapacityL),
                                0,
                            );
                    }
                    // 追加クーポン3有効期限
                    additionalCoupon3TermValidity = MvnoUtil.addSpaceAfterStr(
                        this.sOAPCommon.getNodeContent(
                            document,
                            "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_addbd_info_2/dyn_exp_date",
                        ),
                        10,
                    );
                }
                // 追加クーポン3残容量
                additionalCoupon3.additionalCoupon3RemainCapacity =
                    additionalCoupon3RemainCapacity;
                // 追加クーポン3残容量
                additionalCoupon3.additionalCoupon3TermValidity =
                    additionalCoupon3TermValidity;
                returnPram.jsonBody.additionalCoupon3 = additionalCoupon3;
                // 追加クーポン4
                const additionalCoupon4 = getEmpty_AdditionalCoupon4();
                // 追加クーポン4残容量
                let additionalCoupon4RemainCapacity = MvnoUtil.addSpaceAfterStr(
                    null,
                    0,
                );
                // 追加クーポン4有効期限
                let additionalCoupon4TermValidity = MvnoUtil.addSpaceAfterStr(
                    null,
                    0,
                );
                if (
                    CONST_SERVICEPATTERN_1.equals(servicePattern) ||
                    CONST_SERVICEPATTERN_2.equals(servicePattern) ||
                    CONST_SERVICEPATTERN_4.equals(servicePattern) ||
                    CONST_SERVICEPATTERN_6.equals(servicePattern)
                ) {
                    const dyn_cap = this.sOAPCommon.getNodeContent(
                        document,
                        "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_add_info_3/dyn_cap",
                    );
                    const dyn_use = this.sOAPCommon.getNodeContent(
                        document,
                        "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_add_info_3/dyn_use",
                    );
                    if (
                        CheckUtil.checkIsNum(dyn_cap) &&
                        CheckUtil.checkIsNum(dyn_use)
                    ) {
                        const additionalCoupon1RemainCapacityL =
                            BigInt(dyn_cap) - BigInt(dyn_use);
                        // 追加クーポン4残容量
                        additionalCoupon4RemainCapacity =
                            MvnoUtil.addSpaceAfterStr(
                                String(additionalCoupon1RemainCapacityL),
                                0,
                            );
                    }
                    // 追加クーポン4有効期限
                    additionalCoupon4TermValidity = MvnoUtil.addSpaceAfterStr(
                        this.sOAPCommon.getNodeContent(
                            document,
                            "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_add_info_3/dyn_exp_date",
                        ),
                        10,
                    );
                } else if (CONST_SERVICEPATTERN_3.equals(servicePattern)) {
                    const dyn_cap = this.sOAPCommon.getNodeContent(
                        document,
                        "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_addbd_info_3/dyn_cap",
                    );
                    const dyn_use = this.sOAPCommon.getNodeContent(
                        document,
                        "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_addbd_info_3/dyn_use",
                    );
                    if (
                        CheckUtil.checkIsNum(dyn_cap) &&
                        CheckUtil.checkIsNum(dyn_use)
                    ) {
                        const additionalCoupon1RemainCapacityL =
                            BigInt(dyn_cap) - BigInt(dyn_use);
                        // 追加クーポン4残容量
                        additionalCoupon4RemainCapacity =
                            MvnoUtil.addSpaceAfterStr(
                                String(additionalCoupon1RemainCapacityL),
                                0,
                            );
                    }
                    // 追加クーポン4有効期限
                    additionalCoupon4TermValidity = MvnoUtil.addSpaceAfterStr(
                        this.sOAPCommon.getNodeContent(
                            document,
                            "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_addbd_info_3/dyn_exp_date",
                        ),
                        10,
                    );
                }
                // 追加クーポン4残容量
                additionalCoupon4.additionalCoupon4RemainCapacity =
                    additionalCoupon4RemainCapacity;
                // 追加クーポン4有効期限
                additionalCoupon4.additionalCoupon4TermValidity =
                    additionalCoupon4TermValidity;
                returnPram.jsonBody.additionalCoupon4 = additionalCoupon4;
                // 追加クーポン5
                const additionalCoupon5 = getEmpty_AdditionalCoupon5();
                // 追加クーポン5残容量
                let additionalCoupon5RemainCapacity = MvnoUtil.addSpaceAfterStr(
                    null,
                    0,
                );
                // 追加クーポン5有効期限
                let additionalCoupon5TermValidity = MvnoUtil.addSpaceAfterStr(
                    null,
                    0,
                );
                if (
                    CONST_SERVICEPATTERN_1.equals(servicePattern) ||
                    CONST_SERVICEPATTERN_2.equals(servicePattern) ||
                    CONST_SERVICEPATTERN_4.equals(servicePattern) ||
                    CONST_SERVICEPATTERN_6.equals(servicePattern)
                ) {
                    const dyn_cap = this.sOAPCommon.getNodeContent(
                        document,
                        "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_add_info_4/dyn_cap",
                    );
                    const dyn_use = this.sOAPCommon.getNodeContent(
                        document,
                        "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_add_info_4/dyn_use",
                    );
                    if (
                        CheckUtil.checkIsNum(dyn_cap) &&
                        CheckUtil.checkIsNum(dyn_use)
                    ) {
                        const additionalCoupon1RemainCapacityL =
                            BigInt(dyn_cap) - BigInt(dyn_use);
                        // 追加クーポン5残容量
                        additionalCoupon5RemainCapacity =
                            MvnoUtil.addSpaceAfterStr(
                                String(additionalCoupon1RemainCapacityL),
                                0,
                            );
                    }
                    // 追加クーポン5有効期限
                    additionalCoupon5TermValidity = MvnoUtil.addSpaceAfterStr(
                        this.sOAPCommon.getNodeContent(
                            document,
                            "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_add_info_4/dyn_exp_date",
                        ),
                        10,
                    );
                } else if (CONST_SERVICEPATTERN_3.equals(servicePattern)) {
                    const dyn_cap = this.sOAPCommon.getNodeContent(
                        document,
                        "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_addbd_info_4/dyn_cap",
                    );
                    const dyn_use = this.sOAPCommon.getNodeContent(
                        document,
                        "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_addbd_info_4/dyn_use",
                    );
                    if (
                        CheckUtil.checkIsNum(dyn_cap) &&
                        CheckUtil.checkIsNum(dyn_use)
                    ) {
                        const additionalCoupon1RemainCapacityL =
                            BigInt(dyn_cap) - BigInt(dyn_use);
                        // 追加クーポン5残容量
                        additionalCoupon5RemainCapacity =
                            MvnoUtil.addSpaceAfterStr(
                                String(additionalCoupon1RemainCapacityL),
                                0,
                            );
                    }
                    // 追加クーポン5有効期限
                    additionalCoupon5TermValidity = MvnoUtil.addSpaceAfterStr(
                        this.sOAPCommon.getNodeContent(
                            document,
                            "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_addbd_info_4/dyn_exp_date",
                        ),
                        10,
                    );
                }
                // 追加クーポン5残容量
                additionalCoupon5.additionalCoupon5RemainCapacity =
                    additionalCoupon5RemainCapacity;
                // 追加クーポン5有効期限
                additionalCoupon5.additionalCoupon5TermValidity =
                    additionalCoupon5TermValidity;
                // 追加クーポン5残容量
                returnPram.jsonBody.additionalCoupon5 = additionalCoupon5;
                // クーポンON/OFF状態
                let ouponOnOff = MvnoUtil.addSpaceAfterStr(null, 0);
                // クーポンON/OFF状態
                if (CONST_SERVICEPATTERN_1.equals(servicePattern)) {
                    ouponOnOff = MvnoUtil.addSpaceAfterStr(
                        this.sOAPCommon.getNodeContent(
                            document,
                            "//SubscriberProfileInformation/svpnum0_info/aply_srv",
                        ),
                        1,
                    );
                } else if (
                    CONST_SERVICEPATTERN_4.equals(servicePattern) ||
                    CONST_SERVICEPATTERN_6.equals(servicePattern)
                ) {
                    ouponOnOff = MvnoUtil.addSpaceAfterStr(
                        this.sOAPCommon.getNodeContent(
                            document,
                            "//SubscriberProfileInformation/svpnum0_info/optnum0_info/aply_srv",
                        ),
                        1,
                    );
                } else if (CONST_SERVICEPATTERN_3.equals(servicePattern)) {
                    ouponOnOff = MvnoUtil.addSpaceAfterStr(
                        this.sOAPCommon.getNodeContent(
                            document,
                            "//SubscriberProfileInformation/svpnum1_info/optnum0_info/aply_srv",
                        ),
                        1,
                    );
                }
                returnPram.jsonBody.couponOnOff = ouponOnOff;
                // ヘビーユーザ監視規制状態
                let heavyUserMontorStatus = MvnoUtil.addSpaceAfterStr(null, 0);
                // ヘビーユーザ監視規制状態
                if (
                    CONST_SERVICEPATTERN_2.equals(servicePattern) ||
                    CONST_SERVICEPATTERN_3.equals(servicePattern) ||
                    CONST_SERVICEPATTERN_8.equals(servicePattern) ||
                    CONST_SERVICEPATTERN_9.equals(servicePattern) ||
                    CONST_SERVICEPATTERN_11.equals(servicePattern) ||
                    CONST_SERVICEPATTERN_12.equals(servicePattern)
                ) {
                    heavyUserMontorStatus = MvnoUtil.addSpaceAfterStr(
                        this.sOAPCommon.getNodeContent(
                            document,
                            "//dyn_sub_info/dyn_node_info_0/dyn_reg_info/dyn_hvy_reg_sts",
                        ),
                        1,
                    );
                    if (
                        this.heavyUserMonitorStatusUnregulatedString.equals(
                            heavyUserMontorStatus,
                        )
                    ) {
                        heavyUserMontorStatus = "0";
                    } else if (
                        this.heavyUserMonitorStatusRegulatedatLastString.equals(
                            heavyUserMontorStatus,
                        )
                    ) {
                        heavyUserMontorStatus = "1";
                    } else if (
                        this.heavyUserMonitorStatusRegulatedatMonthlyString.equals(
                            heavyUserMontorStatus,
                        )
                    ) {
                        heavyUserMontorStatus = "2";
                    } else if (
                        this.heavyUserMonitorStatusRegulatedatLastMonthString.equals(
                            heavyUserMontorStatus,
                        )
                    ) {
                        heavyUserMontorStatus = "3";
                    }
                }
                returnPram.jsonBody.heavyUserMonitorStatus =
                    heavyUserMontorStatus;
                // ヘビーユーザ監視規制開始時間（月間）
                let heavyUserMonitorStartTimeMonth = MvnoUtil.addSpaceAfterStr(
                    null,
                    0,
                );
                // ヘビーユーザ監視規制開始時間（月間）
                if (
                    CONST_SERVICEPATTERN_2.equals(servicePattern) ||
                    CONST_SERVICEPATTERN_3.equals(servicePattern) ||
                    CONST_SERVICEPATTERN_9.equals(servicePattern) ||
                    CONST_SERVICEPATTERN_12.equals(servicePattern)
                ) {
                    const dyn_hvy_reg_sta_month =
                        this.sOAPCommon.getNodeContent(
                            document,
                            "//dyn_sub_info/dyn_node_info_0/dyn_reg_info/dyn_hvy_reg_sta_month",
                        );
                    const rtndyn_hvy_reg_sta_month = CheckUtil.forMartdynDate(
                        dyn_hvy_reg_sta_month,
                    );
                    heavyUserMonitorStartTimeMonth = MvnoUtil.addSpaceAfterStr(
                        rtndyn_hvy_reg_sta_month,
                        16,
                    );
                }
                returnPram.jsonBody.heavyUserMonitorStartTimeMonth =
                    heavyUserMonitorStartTimeMonth;
                // ヘビーユーザ監視規制開始時間（直近）
                let heavyUserMonitorStartTimeLatest = MvnoUtil.addSpaceAfterStr(
                    null,
                    0,
                );
                // ヘビーユーザ監視規制開始時間（直近）
                if (
                    CONST_SERVICEPATTERN_2.equals(servicePattern) ||
                    CONST_SERVICEPATTERN_3.equals(servicePattern) ||
                    CONST_SERVICEPATTERN_8.equals(servicePattern) ||
                    CONST_SERVICEPATTERN_9.equals(servicePattern) ||
                    CONST_SERVICEPATTERN_11.equals(servicePattern)
                ) {
                    const dyn_hvy_reg_sta_date = this.sOAPCommon.getNodeContent(
                        document,
                        "//dyn_sub_info/dyn_node_info_0/dyn_reg_info/dyn_hvy_reg_sta_date",
                    );
                    const strdyn_hvy_reg_sta_date =
                        CheckUtil.forMartdynDate(dyn_hvy_reg_sta_date);
                    heavyUserMonitorStartTimeLatest = MvnoUtil.addSpaceAfterStr(
                        strdyn_hvy_reg_sta_date,
                        16,
                    );
                }
                returnPram.jsonBody.heavyUserMonitorStartTimeLatest =
                    heavyUserMonitorStartTimeLatest;
                // プリペイド有効期限
                let prepaidTermValidity = MvnoUtil.addSpaceAfterStr(null, 0);
                // プリペイド有効期限
                if (CONST_SERVICEPATTERN_4.equals(servicePattern)) {
                    let dyn_exp_date = this.sOAPCommon.getNodeContent(
                        document,
                        "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_add_info_0/dyn_exp_date",
                    );
                    if (
                        dyn_exp_date.compareTo(
                            this.sOAPCommon.getNodeContent(
                                document,
                                "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_add_info_1/dyn_exp_date",
                            ),
                        ) < 0
                    ) {
                        dyn_exp_date = this.sOAPCommon.getNodeContent(
                            document,
                            "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_add_info_1/dyn_exp_date",
                        );
                    }
                    if (
                        dyn_exp_date.compareTo(
                            this.sOAPCommon.getNodeContent(
                                document,
                                "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_add_info_2/dyn_exp_date",
                            ),
                        ) < 0
                    ) {
                        dyn_exp_date = this.sOAPCommon.getNodeContent(
                            document,
                            "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_add_info_2/dyn_exp_date",
                        );
                    }
                    if (
                        dyn_exp_date.compareTo(
                            this.sOAPCommon.getNodeContent(
                                document,
                                "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_add_info_3/dyn_exp_date",
                            ),
                        ) < 0
                    ) {
                        dyn_exp_date = this.sOAPCommon.getNodeContent(
                            document,
                            "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_add_info_3/dyn_exp_date",
                        );
                    }
                    if (
                        dyn_exp_date.compareTo(
                            this.sOAPCommon.getNodeContent(
                                document,
                                "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_add_info_4/dyn_exp_date",
                            ),
                        ) < 0
                    ) {
                        dyn_exp_date = this.sOAPCommon.getNodeContent(
                            document,
                            "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_add_info_4/dyn_exp_date",
                        );
                    }
                    prepaidTermValidity = MvnoUtil.addSpaceAfterStr(
                        dyn_exp_date,
                        10,
                    );
                } else if (CONST_SERVICEPATTERN_5.equals(servicePattern)) {
                    const exp_date = this.sOAPCommon.getNodeContent(
                        document,
                        "//SubscriberProfileInformation/svpnum1_info/exp_date",
                    );
                    const exp_dateFmt = CheckUtil.forMartDate(exp_date);
                    prepaidTermValidity = MvnoUtil.addSpaceAfterStr(
                        exp_dateFmt,
                        10,
                    );
                } else if (CONST_SERVICEPATTERN_6.equals(servicePattern)) {
                    const exp_date = this.sOAPCommon.getNodeContent(
                        document,
                        "//SubscriberProfileInformation/svpnum0_info/exp_date",
                    );
                    const exp_dateFmt = CheckUtil.forMartDate(exp_date);
                    prepaidTermValidity = MvnoUtil.addSpaceAfterStr(
                        exp_dateFmt,
                        10,
                    );
                }
                returnPram.jsonBody.prepaidTermValidity = prepaidTermValidity;
                // アクティベート状態
                returnPram.jsonBody.activateStatus = MvnoUtil.addSpaceAfterStr(
                    this.sOAPCommon.getNodeContent(
                        document,
                        "//SubscriberProfileInformation/activate",
                    ),
                    1,
                );
                // 適用サービス状態（規制理由）
                returnPram.jsonBody.regulationCause = MvnoUtil.addSpaceAfterStr(
                    this.sOAPCommon.getNodeContent(
                        document,
                        "//dyn_sub_info/dyn_node_info_0/dyn_ses_polinfo/dyn_rule_name",
                    ),
                    0,
                );
                // 総量規制状態
                let totalVolumeControlStatus = MvnoUtil.addSpaceAfterStr(
                    null,
                    1,
                );
                // 総量規制状態
                const dyn_reg_sts = this.sOAPCommon.getNodeContent(
                    document,
                    "//dyn_sub_info/dyn_node_info_0/dyn_reg_info/dyn_reg_sts",
                );
                if (this.totalVolumeControlInJudgment.equals(dyn_reg_sts)) {
                    totalVolumeControlStatus = "1";
                } else if (
                    this.totalVolumeControlOutJudgment.equals(dyn_reg_sts)
                ) {
                    totalVolumeControlStatus = "0";
                }
                returnPram.jsonBody.totalVolumeControlStatus =
                    totalVolumeControlStatus;
                // 回線グループID
                const lineGroup = MvnoUtil.addSpaceAfterStr(
                    this.sOAPCommon.getNodeContent(
                        document,
                        "//GroupProfileInformation/grpnum",
                    ),
                    0,
                );
                if ("0".equals(lineGroup.trim())) {
                    returnPram.jsonBody.lineGroupId = "";
                } else {
                    returnPram.jsonBody.lineGroupId = lineGroup;
                }
            } catch (e) {
                // 処理コード
                super.error(
                    e as Error,
                    pram.tenantId,
                    pram.requestHeader.sequenceNo,
                    MsgKeysConstants.APCOME0401,
                    e.message,
                );
                returnPram.jsonBody.responseHeader.processCode =
                    ResultCdConstants.CODE_999999;
                return this.getReturnPram(
                    returnPram,
                    getStringParameter(pram.addOption),
                    document,
                    isPrivate,
                    returnPram.jsonBody.responseHeader.processCode,
                    servicePatternGroup,
                    trafficPreviousMonthGroup,
                    trafficBeforehandMonthGroup,
                    trafficPreviousMonthCouponOffGroup,
                    trafficBeforehandMonthCouponOffGroup,
                    ...additionArg,
                );
            }

            if ("1".equals(version)) {
                // バージョン指定ありの場合
                returnPramVersion.jsonBody = {
                    ...returnPram.jsonBody,
                    lineStatus: MvnoUtil.addSpaceAfterStr(lineUsageStatus, 0),
                };
            }
        }

        if ("1".equals(version)) {
            // バージョン指定ありの場合
            return this.getReturnPram(
                returnPramVersion,
                getStringParameter(pram.addOption),
                document,
                isPrivate,
                returnPramVersion.jsonBody.responseHeader.processCode,
                servicePatternGroup,
                trafficPreviousMonthGroup,
                trafficBeforehandMonthGroup,
                trafficPreviousMonthCouponOffGroup,
                trafficBeforehandMonthCouponOffGroup,
                ...additionArg,
            );
        } else {
            // バージョン指定なしの場合
            return this.getReturnPram(
                returnPram,
                getStringParameter(pram.addOption),
                document,
                isPrivate,
                returnPram.jsonBody.responseHeader.processCode,
                servicePatternGroup,
                trafficPreviousMonthGroup,
                trafficBeforehandMonthGroup,
                trafficPreviousMonthCouponOffGroup,
                trafficBeforehandMonthCouponOffGroup,
                ...additionArg,
            );
        }

        // end
    }

    /**
     * クーポンOFF時の通信量要否で出力パターンを変更する
     * @param pram 返却値Dto
     * @param addOption 追加オプション
     * @param document SOAP通信結果
     * @param isPrivate 内部フラグ
     * @param processCode 処理コード
     * @param servicePatternGroup グループサービスパターン
     * @param trafficPreviousMonthGroup グループ回線の前月の通信量
     * @param trafficBeforehandMonthGroup グループ回線の前々月の通信量
     * @param trafficPreviousMonthCouponOffGroup グループ回線の前月の通信量（クーポンOFF）
     * @param trafficBeforehandMonthCouponOffGroup グループ回線の前々月の通信量（クーポンOFF）
     * @param additionArg 追加引数<br>
     * [0] クーポンOFF時前月通信量
     * [1] クーポンOFF時前々月通信量
     * [2] バージョン
     * [3] 利用状態
     * @return 返却値Dto
     */
    private getReturnPram<T extends BaseOutputDto>(
        pram: T,
        addOption: string,
        document: Document,
        isPrivate: boolean,
        processCode: string,
        servicePatternGroup: string,
        trafficPreviousMonthGroup: string,
        trafficBeforehandMonthGroup: string,
        trafficPreviousMonthCouponOffGroup: string,
        trafficBeforehandMonthCouponOffGroup: string,
        ...additionArg: any[]
    ) {
        if (
            CheckUtil.checkIsNotNull(addOption) ||
            "0".equals(addOption) ||
            (!"1".equals(addOption) &&
                !"2".equals(addOption) &&
                !"3".equals(addOption) &&
                !"4".equals(addOption))
        ) {
            return pram;
        }

        const couponOff: LineUseInfoCouponOffDto =
            getEmpty_LineUseInfoCouponOff();
        const giftCoupon: LineUseInfoGiftCouponDto =
            getEmpty_LineUseInfoGiftCoupon();
        const appCoupon: LineUseInfoAppCouponDto =
            getEmpty_LineUseInfoAppCoupon();
        const group: LineUseInfoGroupDto = getEmpty_LineUseInfoGroup();
        const version = String(additionArg[2]);

        if (
            "1".equals(addOption) ||
            "2".equals(addOption) ||
            "3".equals(addOption) ||
            "4".equals(addOption)
        ) {
            try {
                const trafficPreviousMonthCouponOff = String(additionArg[0]);
                const trafficBeforehandMonthCouponOff = String(additionArg[1]);

                if (!"000000".equals(processCode)) {
                    // 当日の通信量
                    couponOff.trafficOneDay_CouponOff =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 1日前の通信量
                    couponOff.traffic1dayAgo_CouponOff =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 2日前の通信量
                    couponOff.traffic2daysAgo_CouponOff =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 3日前の通信量
                    couponOff.traffic3daysAgo_CouponOff =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 4日前の通信量
                    couponOff.traffic4daysAgo_CouponOff =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 5日前の通信量
                    couponOff.traffic5daysAgo_CouponOff =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 6日前の通信量
                    couponOff.traffic6daysAgo_CouponOff =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 7日前の通信量
                    couponOff.traffic7daysAgo_CouponOff =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 8日前の通信量
                    couponOff.traffic8daysAgo_CouponOff =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 9日前の通信量
                    couponOff.traffic9daysAgo_CouponOff =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 10日前の通信量
                    couponOff.traffic10daysAgo_CouponOff =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 11日前の通信量
                    couponOff.traffic11daysAgo_CouponOff =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 12日前の通信量
                    couponOff.traffic12daysAgo_CouponOff =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 13日前の通信量
                    couponOff.traffic13daysAgo_CouponOff =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 14日前の通信量
                    couponOff.traffic14daysAgo_CouponOff =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 15日前の通信量
                    couponOff.traffic15daysAgo_CouponOff =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 16日前の通信量
                    couponOff.traffic16daysAgo_CouponOff =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 17日前の通信量
                    couponOff.traffic17daysAgo_CouponOff =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 18日前の通信量
                    couponOff.traffic18daysAgo_CouponOff =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 19日前の通信量
                    couponOff.traffic19daysAgo_CouponOff =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 20日前の通信量
                    couponOff.traffic20daysAgo_CouponOff =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 21日前の通信量
                    couponOff.traffic21daysAgo_CouponOff =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 22日前の通信量
                    couponOff.traffic22daysAgo_CouponOff =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 23日前の通信量
                    couponOff.traffic23daysAgo_CouponOff =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 24日前の通信量
                    couponOff.traffic24daysAgo_CouponOff =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 25日前の通信量
                    couponOff.traffic25daysAgo_CouponOff =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 26日前の通信量
                    couponOff.traffic26daysAgo_CouponOff =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 27日前の通信量
                    couponOff.traffic27daysAgo_CouponOff =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 28日前の通信量
                    couponOff.traffic28daysAgo_CouponOff =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 29日前の通信量
                    couponOff.traffic29daysAgo_CouponOff =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 30日前の通信量
                    couponOff.traffic30daysAgo_CouponOff =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 当月の通信量
                    couponOff.trafficThisMonth_CouponOff =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 前月の通信量
                    couponOff.trafficPreviousMonth_CouponOff =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 前々月の通信量
                    couponOff.trafficBeforehandMonth_CouponOff =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                } else {
                    let day = "";
                    const trafficdayAgoAry = new Array(31).fill("");

                    day = Check.getPreXDay(0);
                    let tagFlg = false;
                    tagFlg = this.sOAPCommon.CheckNodeExist(
                        document,
                        "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_hvy_lst_info/dyn_" +
                            day,
                    );
                    if (tagFlg) {
                        for (let i = 0; i <= 30; i++) {
                            day = Check.getPreXDay(i);
                            const trafficdayAgo =
                                this.sOAPCommon.getNodeContent(
                                    document,
                                    "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_hvy_lst_info/dyn_" +
                                        day,
                                );
                            trafficdayAgoAry[i] = trafficdayAgo;
                        }
                    } else {
                        for (let i = 1; i <= 31; i++) {
                            day = Check.getPreXDay(i);
                            const trafficdayAgo =
                                this.sOAPCommon.getNodeContent(
                                    document,
                                    "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_hvy_lst_info/dyn_" +
                                        day,
                                );
                            trafficdayAgoAry[i - 1] = trafficdayAgo;
                        }
                    }

                    // 当日の通信量
                    couponOff.trafficOneDay_CouponOff =
                        MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[0], 0);
                    // 1日前の通信量
                    couponOff.traffic1dayAgo_CouponOff =
                        MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[1], 0);
                    // 2日前の通信量
                    couponOff.traffic2daysAgo_CouponOff =
                        MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[2], 0);
                    // 3日前の通信量
                    couponOff.traffic3daysAgo_CouponOff =
                        MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[3], 0);
                    // 4日前の通信量
                    couponOff.traffic4daysAgo_CouponOff =
                        MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[4], 0);
                    // 5日前の通信量
                    couponOff.traffic5daysAgo_CouponOff =
                        MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[5], 0);
                    // 6日前の通信量
                    couponOff.traffic6daysAgo_CouponOff =
                        MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[6], 0);
                    // 7日前の通信量
                    couponOff.traffic7daysAgo_CouponOff =
                        MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[7], 0);
                    // 8日前の通信量
                    couponOff.traffic8daysAgo_CouponOff =
                        MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[8], 0);
                    // 9日前の通信量
                    couponOff.traffic9daysAgo_CouponOff =
                        MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[9], 0);
                    // 10日前の通信量
                    couponOff.traffic10daysAgo_CouponOff =
                        MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[10], 0);
                    // 11日前の通信量
                    couponOff.traffic11daysAgo_CouponOff =
                        MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[11], 0);
                    // 12日前の通信量
                    couponOff.traffic12daysAgo_CouponOff =
                        MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[12], 0);
                    // 13日前の通信量
                    couponOff.traffic13daysAgo_CouponOff =
                        MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[13], 0);
                    // 14日前の通信量
                    couponOff.traffic14daysAgo_CouponOff =
                        MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[14], 0);
                    // 15日前の通信量
                    couponOff.traffic15daysAgo_CouponOff =
                        MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[15], 0);
                    // 16日前の通信量
                    couponOff.traffic16daysAgo_CouponOff =
                        MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[16], 0);
                    // 17日前の通信量
                    couponOff.traffic17daysAgo_CouponOff =
                        MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[17], 0);
                    // 18日前の通信量
                    couponOff.traffic18daysAgo_CouponOff =
                        MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[18], 0);
                    // 19日前の通信量
                    couponOff.traffic19daysAgo_CouponOff =
                        MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[19], 0);
                    // 20日前の通信量
                    couponOff.traffic20daysAgo_CouponOff =
                        MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[20], 0);
                    // 21日前の通信量
                    couponOff.traffic21daysAgo_CouponOff =
                        MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[21], 0);
                    // 22日前の通信量
                    couponOff.traffic22daysAgo_CouponOff =
                        MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[22], 0);
                    // 23日前の通信量
                    couponOff.traffic23daysAgo_CouponOff =
                        MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[23], 0);
                    // 24日前の通信量
                    couponOff.traffic24daysAgo_CouponOff =
                        MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[24], 0);
                    // 25日前の通信量
                    couponOff.traffic25daysAgo_CouponOff =
                        MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[25], 0);
                    // 26日前の通信量
                    couponOff.traffic26daysAgo_CouponOff =
                        MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[26], 0);
                    // 27日前の通信量
                    couponOff.traffic27daysAgo_CouponOff =
                        MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[27], 0);
                    // 28日前の通信量
                    couponOff.traffic28daysAgo_CouponOff =
                        MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[28], 0);
                    // 29日前の通信量
                    couponOff.traffic29daysAgo_CouponOff =
                        MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[29], 0);
                    // 30日前の通信量
                    couponOff.traffic30daysAgo_CouponOff =
                        MvnoUtil.addSpaceAfterStr(trafficdayAgoAry[30], 0);
                    // 当月の通信量
                    couponOff.trafficThisMonth_CouponOff =
                        MvnoUtil.addSpaceAfterStr(
                            this.sOAPCommon.getNodeContent(
                                document,
                                "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_hvy_month_info/dyn_use",
                            ),
                            0,
                        );
                    // 前月の通信量
                    couponOff.trafficPreviousMonth_CouponOff =
                        MvnoUtil.addSpaceAfterStr(
                            trafficPreviousMonthCouponOff,
                            0,
                        );
                    // 前々月の通信量
                    couponOff.trafficBeforehandMonth_CouponOff =
                        MvnoUtil.addSpaceAfterStr(
                            trafficBeforehandMonthCouponOff,
                            0,
                        );
                }
            } catch (e) {
                return this.returnEmptyPram(isPrivate, addOption, version);
            }
        }

        if ("2".equals(addOption) || "3".equals(addOption)) {
            const gft1 = getEmpty_GiftCoupon1();
            const gft2 = getEmpty_GiftCoupon2();
            try {
                if (!"000000".equals(processCode)) {
                    // 譲渡クーポン1を設定
                    gft1.giftCoupon1Capacity = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    gft1.giftCoupon1RemainCapacity = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    gft1.giftCoupon1TermValidity = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    gft1.giftCoupon1Use = MvnoUtil.addSpaceAfterStr(null, 0);
                    giftCoupon.giftCoupon1 = gft1;
                    // 譲渡クーポン2を設定
                    gft2.giftCoupon2Capacity = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    gft2.giftCoupon2TermValidity = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    giftCoupon.giftCoupon2 = gft2;
                } else {
                    // 譲渡クーポン1を設定
                    const gft1cap = this.sOAPCommon.getNodeContent(
                        document,
                        "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_tms_info_0/dyn_cap",
                    );
                    const gft1use = this.sOAPCommon.getNodeContent(
                        document,
                        "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_tms_info_0/dyn_use",
                    );
                    let gft1rem: string = null;
                    if (
                        CheckUtil.checkIsNum(gft1cap) &&
                        CheckUtil.checkIsNum(gft1use)
                    ) {
                        gft1rem = String(BigInt(gft1cap) - BigInt(gft1use));
                    }
                    gft1.giftCoupon1Capacity = MvnoUtil.addSpaceAfterStr(
                        gft1cap,
                        0,
                    );
                    gft1.giftCoupon1Use = MvnoUtil.addSpaceAfterStr(gft1use, 0);
                    gft1.giftCoupon1RemainCapacity = MvnoUtil.addSpaceAfterStr(
                        gft1rem,
                        0,
                    );
                    gft1.giftCoupon1TermValidity = MvnoUtil.addSpaceAfterStr(
                        this.sOAPCommon.getNodeContent(
                            document,
                            "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_tms_info_0/dyn_exp_date",
                        ),
                        0,
                    );
                    giftCoupon.giftCoupon1 = gft1;
                    // 譲渡クーポン2を設定
                    gft2.giftCoupon2Capacity = MvnoUtil.addSpaceAfterStr(
                        this.sOAPCommon.getNodeContent(
                            document,
                            "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_tms_info_1/dyn_cap",
                        ),
                        0,
                    );
                    gft2.giftCoupon2TermValidity = MvnoUtil.addSpaceAfterStr(
                        this.sOAPCommon.getNodeContent(
                            document,
                            "//dyn_sub_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_tms_info_1/dyn_exp_date",
                        ),
                        0,
                    );
                    giftCoupon.giftCoupon2 = gft2;
                }
            } catch (e) {
                // this.context.warn("getReturnPram", e);
                return this.returnEmptyPram(isPrivate, addOption, version);
            }
        }

        if ("3".equals(addOption) || "4".equals(addOption)) {
            try {
                const ap1 = getEmpty_AppCoupon1();
                const ap2 = getEmpty_AppCoupon2();
                const ap3 = getEmpty_AppCoupon3();
                const ap4 = getEmpty_AppCoupon4();
                const ap5 = getEmpty_AppCoupon5();
                const ap6 = getEmpty_AppCoupon6();

                if (!"000000".equals(processCode)) {
                    ap1.aplCoupon1Capacity = MvnoUtil.addSpaceAfterStr(null, 0);
                    ap1.aplCoupon1Use = MvnoUtil.addSpaceAfterStr(null, 0);
                    ap1.aplCoupon1RemainCapacity = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    ap1.aplCoupon1TermValidity = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    appCoupon.aplCoupon1 = ap1;

                    ap2.aplCoupon2Capacity = MvnoUtil.addSpaceAfterStr(null, 0);
                    ap2.aplCoupon2Use = MvnoUtil.addSpaceAfterStr(null, 0);
                    ap2.aplCoupon2RemainCapacity = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    ap2.aplCoupon2TermValidity = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    appCoupon.aplCoupon2 = ap2;

                    ap3.aplCoupon3Capacity = MvnoUtil.addSpaceAfterStr(null, 0);
                    ap3.aplCoupon3Use = MvnoUtil.addSpaceAfterStr(null, 0);
                    ap3.aplCoupon3RemainCapacity = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    ap3.aplCoupon3TermValidity = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    appCoupon.aplCoupon3 = ap3;

                    ap4.aplCoupon4Capacity = MvnoUtil.addSpaceAfterStr(null, 0);
                    ap4.aplCoupon4Use = MvnoUtil.addSpaceAfterStr(null, 0);
                    ap4.aplCoupon4RemainCapacity = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    ap4.aplCoupon4TermValidity = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    appCoupon.aplCoupon4 = ap4;

                    ap5.aplCoupon5Capacity = MvnoUtil.addSpaceAfterStr(null, 0);
                    ap5.aplCoupon5Use = MvnoUtil.addSpaceAfterStr(null, 0);
                    ap5.aplCoupon5RemainCapacity = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    ap5.aplCoupon5TermValidity = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    appCoupon.aplCoupon5 = ap5;

                    ap6.aplCoupon6Capacity = MvnoUtil.addSpaceAfterStr(null, 0);
                    ap6.aplCoupon6Use = MvnoUtil.addSpaceAfterStr(null, 0);
                    ap6.aplCoupon6RemainCapacity = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    ap6.aplCoupon6TermValidity = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    appCoupon.aplCoupon6 = ap6;
                } else {
                    // List<AppCouponDto> apcList = new ArrayList<AppCouponDto>();
                    const apcList: AppCouponDto[] = [];
                    const appcouponNum = 6;
                    for (let i = 0; i < appcouponNum; i++) {
                        const apc = getEmpty_AppCoupon();
                        const parentPath =
                            "//dyn_sub_info/dyn_node_info_0/dyn_sdf_polinfo_" +
                            i +
                            "/dyn_bucket_info";
                        const aplCap = this.sOAPCommon.getNodeContent(
                            document,
                            parentPath + "/dyn_cap_fltr",
                        );
                        const aplUse = this.sOAPCommon.getNodeContent(
                            document,
                            parentPath + "/dyn_use_fltr",
                        );
                        apc.aplCouponCapacity = aplCap;
                        apc.aplCouponUse = aplUse;
                        if (
                            CheckUtil.checkIsNum(aplCap) &&
                            CheckUtil.checkIsNum(aplUse)
                        ) {
                            apc.aplCouponRemainCapacity = String(
                                BigInt(aplCap) - BigInt(aplUse),
                            );
                        }
                        apc.aplCouponTermValidity =
                            this.sOAPCommon.getNodeContent(
                                document,
                                parentPath + "/dyn_exp_date",
                            );
                        apcList.push(apc);
                    }
                    let idx = 0;
                    ap1.aplCoupon1Capacity = apcList.at(idx).aplCouponCapacity;
                    ap1.aplCoupon1Use = apcList.at(idx).aplCouponUse;
                    ap1.aplCoupon1RemainCapacity =
                        apcList.at(idx).aplCouponRemainCapacity;
                    ap1.aplCoupon1TermValidity =
                        apcList.at(idx).aplCouponTermValidity;
                    appCoupon.aplCoupon1 = ap1;

                    idx++;
                    ap2.aplCoupon2Capacity = apcList.at(idx).aplCouponCapacity;
                    ap2.aplCoupon2Use = apcList.at(idx).aplCouponUse;
                    ap2.aplCoupon2RemainCapacity =
                        apcList.at(idx).aplCouponRemainCapacity;
                    ap2.aplCoupon2TermValidity =
                        apcList.at(idx).aplCouponTermValidity;
                    appCoupon.aplCoupon2 = ap2;

                    idx++;
                    ap3.aplCoupon3Capacity = apcList.at(idx).aplCouponCapacity;
                    ap3.aplCoupon3Use = apcList.at(idx).aplCouponUse;
                    ap3.aplCoupon3RemainCapacity =
                        apcList.at(idx).aplCouponRemainCapacity;
                    ap3.aplCoupon3TermValidity =
                        apcList.at(idx).aplCouponTermValidity;
                    appCoupon.aplCoupon3 = ap3;

                    idx++;
                    ap4.aplCoupon4Capacity = apcList.at(idx).aplCouponCapacity;
                    ap4.aplCoupon4Use = apcList.at(idx).aplCouponUse;
                    ap4.aplCoupon4RemainCapacity =
                        apcList.at(idx).aplCouponRemainCapacity;
                    ap4.aplCoupon4TermValidity =
                        apcList.at(idx).aplCouponTermValidity;
                    appCoupon.aplCoupon4 = ap4;

                    idx++;
                    ap5.aplCoupon5Capacity = apcList.at(idx).aplCouponCapacity;
                    ap5.aplCoupon5Use = apcList.at(idx).aplCouponUse;
                    ap5.aplCoupon5RemainCapacity =
                        apcList.at(idx).aplCouponRemainCapacity;
                    ap5.aplCoupon5TermValidity =
                        apcList.at(idx).aplCouponTermValidity;
                    appCoupon.aplCoupon5 = ap5;

                    idx++;
                    ap6.aplCoupon6Capacity = apcList.at(idx).aplCouponCapacity;
                    ap6.aplCoupon6Use = apcList.at(idx).aplCouponUse;
                    ap6.aplCoupon6RemainCapacity =
                        apcList.at(idx).aplCouponRemainCapacity;
                    ap6.aplCoupon6TermValidity =
                        apcList.at(idx).aplCouponTermValidity;
                    appCoupon.aplCoupon6 = ap6;
                }
            } catch (e) {
                // this.context.warn("getReturnPram", e);
                return this.returnEmptyPram(isPrivate, addOption, version);
            }
        }

        if ("4".equals(addOption)) {
            try {
                if (!"000000".equals(processCode)) {
                    // 異常時
                    // 当日の通信量（グループ）
                    group.trafficOneDayGroup = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    // 1日前の通信量（グループ）
                    group.traffic1dayAgoGroup = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    // 2日前の通信量（グループ）
                    group.traffic2daysAgoGroup = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    // 3日前の通信量（グループ）
                    group.traffic3daysAgoGroup = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    // 4日前の通信量（グループ）
                    group.traffic4daysAgoGroup = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    // 5日前の通信量（グループ）
                    group.traffic5daysAgoGroup = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    // 6日前の通信量（グループ）
                    group.traffic6daysAgoGroup = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    // 7日前の通信量（グループ）
                    group.traffic7daysAgoGroup = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    // 8日前の通信量（グループ）
                    group.traffic8daysAgoGroup = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    // 9日前の通信量（グループ）
                    group.traffic9daysAgoGroup = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    // 10日前の通信量（グループ）
                    group.traffic10daysAgoGroup = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    // 11日前の通信量（グループ）
                    group.traffic11daysAgoGroup = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    // 12日前の通信量（グループ）
                    group.traffic12daysAgoGroup = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    // 13日前の通信量（グループ）
                    group.traffic13daysAgoGroup = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    // 14日前の通信量（グループ）
                    group.traffic14daysAgoGroup = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    // 15日前の通信量（グループ）
                    group.traffic15daysAgoGroup = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    // 16日前の通信量（グループ）
                    group.traffic16daysAgoGroup = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    // 17日前の通信量（グループ）
                    group.traffic17daysAgoGroup = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    // 18日前の通信量（グループ）
                    group.traffic18daysAgoGroup = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    // 19日前の通信量（グループ）
                    group.traffic19daysAgoGroup = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    // 20日前の通信量（グループ）
                    group.traffic20daysAgoGroup = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    // 21日前の通信量（グループ）
                    group.traffic21daysAgoGroup = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    // 22日前の通信量（グループ）
                    group.traffic22daysAgoGroup = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    // 23日前の通信量（グループ）
                    group.traffic23daysAgoGroup = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    // 24日前の通信量（グループ）
                    group.traffic24daysAgoGroup = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    // 25日前の通信量（グループ）
                    group.traffic25daysAgoGroup = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    // 26日前の通信量（グループ）
                    group.traffic26daysAgoGroup = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    // 27日前の通信量（グループ）
                    group.traffic27daysAgoGroup = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    // 28日前の通信量（グループ）
                    group.traffic28daysAgoGroup = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    // 29日前の通信量（グループ）
                    group.traffic29daysAgoGroup = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    // 30日前の通信量（グループ）
                    group.traffic30daysAgoGroup = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    // 当月の通信量（グループ）
                    group.trafficThisMonthGroup = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    // 前月の通信量（グループ）
                    group.trafficPreviousMonthGroup = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    // 前々月の通信量（グループ）
                    group.trafficBeforehandMonthGroup =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 基本クーポン残容量（グループ）
                    group.basicCouponRemainsGroup = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    // 基本クーポン容量（グループ）
                    group.basicCouponCapacityGroup = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    // 基本クーポン使用量（グループ）
                    group.basicCouponUseGroup = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    // 基本クーポン有効期限（グループ）
                    group.basicCouponTermValidityGroup =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // クーポン容量切れ時間（グループ）
                    group.couponPieceTimeGroup = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    // 繰越クーポン1（グループ）
                    const carryingOverCoupon1Group =
                        getEmpty_CarryingOverCoupon1Group();
                    // 繰越クーポン1容量
                    carryingOverCoupon1Group.carryingOverCoupon1Capacity =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 繰越クーポン1使用量
                    carryingOverCoupon1Group.carryingOverCoupon1Use =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 繰越クーポン1残容量
                    carryingOverCoupon1Group.carryingOverCoupon1RemainCapacity =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 繰越クーポン1有効期限
                    carryingOverCoupon1Group.carryingOverCoupon1TermValidity =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    group.carryingOverCoupon1Group = carryingOverCoupon1Group;
                    // 繰越クーポン2（グループ）
                    const carryingOverCoupon2Group =
                        getEmpty_CarryingOverCoupon2Group();
                    // 繰越クーポン2容量
                    carryingOverCoupon2Group.carryingOverCoupon2Capacity =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 繰越クーポン2使用量
                    carryingOverCoupon2Group.carryingOverCoupon2Use =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 繰越クーポン2残容量
                    carryingOverCoupon2Group.carryingOverCoupon2RemainCapacity =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 繰越クーポン2有効期限
                    carryingOverCoupon2Group.carryingOverCoupon2TermValidity =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    group.carryingOverCoupon2Group = carryingOverCoupon2Group;
                    // 追加クーポン1（グループ）
                    const additionalCoupon1Group =
                        getEmpty_AdditionalCoupon1Group();
                    // 追加クーポン1容量
                    additionalCoupon1Group.additionalCoupon1Capacity =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 追加クーポン1使用量
                    additionalCoupon1Group.additionalCoupon1Use =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 追加クーポン1残容量
                    additionalCoupon1Group.additionalCoupon1RemainCapacity =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 追加クーポン1有効期限
                    additionalCoupon1Group.additionalCoupon1TermValidity =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    group.additionalCoupon1Group = additionalCoupon1Group;
                    // 追加クーポン2（グループ）
                    const additionalCoupon2Group =
                        getEmpty_AdditionalCoupon2Group();
                    // 追加クーポン2容量
                    additionalCoupon2Group.additionalCoupon2Capacity =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 追加クーポン2使用量
                    additionalCoupon2Group.additionalCoupon2Use =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 追加クーポン2残容量
                    additionalCoupon2Group.additionalCoupon2RemainCapacity =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 追加クーポン2有効期限
                    additionalCoupon2Group.additionalCoupon2TermValidity =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    group.additionalCoupon2Group = additionalCoupon2Group;
                    // 追加クーポン3（グループ）
                    const additionalCoupon3Group =
                        getEmpty_AdditionalCoupon3Group();
                    // 追加クーポン3容量
                    additionalCoupon3Group.additionalCoupon3Capacity =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 追加クーポン3使用量
                    additionalCoupon3Group.additionalCoupon3Use =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 追加クーポン3残容量
                    additionalCoupon3Group.additionalCoupon3RemainCapacity =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 追加クーポン3有効期限
                    additionalCoupon3Group.additionalCoupon3TermValidity =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    group.additionalCoupon3Group = additionalCoupon3Group;
                    // 追加クーポン4（グループ）
                    const additionalCoupon4Group =
                        getEmpty_AdditionalCoupon4Group();
                    // 追加クーポン4容量
                    additionalCoupon4Group.additionalCoupon4Capacity =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 追加クーポン4使用量
                    additionalCoupon4Group.additionalCoupon4Use =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 追加クーポン4残容量
                    additionalCoupon4Group.additionalCoupon4RemainCapacity =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 追加クーポン4有効期限
                    additionalCoupon4Group.additionalCoupon4TermValidity =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    group.additionalCoupon4Group = additionalCoupon4Group;
                    // 追加クーポン5（グループ）
                    const additionalCoupon5Group =
                        getEmpty_AdditionalCoupon5Group();
                    // 追加クーポン5容量
                    additionalCoupon5Group.additionalCoupon5Capacity =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 追加クーポン5使用量
                    additionalCoupon5Group.additionalCoupon5Use =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 追加クーポン5残容量
                    additionalCoupon5Group.additionalCoupon5RemainCapacity =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 追加クーポン5有効期限
                    additionalCoupon5Group.additionalCoupon5TermValidity =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 追加クーポン5残容量
                    group.additionalCoupon5Group = additionalCoupon5Group;
                    // アプリクーポン1（グループ）
                    const aplCoupon1Group = getEmpty_AppCoupon1Group();
                    // アプリクーポン1容量
                    aplCoupon1Group.aplCoupon1Capacity =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // アプリクーポン1使用量
                    aplCoupon1Group.aplCoupon1Use = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    // アプリクーポン1残容量
                    aplCoupon1Group.aplCoupon1RemainCapacity =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // アプリクーポン1有効期限
                    aplCoupon1Group.aplCoupon1TermValidity =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    group.aplCoupon1Group = aplCoupon1Group;
                    // アプリクーポン2（グループ）
                    const aplCoupon2Group = getEmpty_AppCoupon2Group();
                    // アプリクーポン2容量
                    aplCoupon2Group.aplCoupon2Capacity =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // アプリクーポン2使用量
                    aplCoupon2Group.aplCoupon2Use = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    // アプリクーポン2残容量
                    aplCoupon2Group.aplCoupon2RemainCapacity =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // アプリクーポン2有効期限
                    aplCoupon2Group.aplCoupon2TermValidity =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    group.aplCoupon2Group = aplCoupon2Group;
                    // アプリクーポン3（グループ）
                    const aplCoupon3Group = getEmpty_AppCoupon3Group();
                    // アプリクーポン3容量
                    aplCoupon3Group.aplCoupon3Capacity =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // アプリクーポン3使用量
                    aplCoupon3Group.aplCoupon3Use = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    // アプリクーポン3残容量
                    aplCoupon3Group.aplCoupon3RemainCapacity =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // アプリクーポン3有効期限
                    aplCoupon3Group.aplCoupon3TermValidity =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    group.aplCoupon3Group = aplCoupon3Group;
                    // アプリクーポン4（グループ）
                    const aplCoupon4Group = getEmpty_AppCoupon4Group();
                    // アプリクーポン4容量
                    aplCoupon4Group.aplCoupon4Capacity =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // アプリクーポン4使用量
                    aplCoupon4Group.aplCoupon4Use = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    // アプリクーポン4残容量
                    aplCoupon4Group.aplCoupon4RemainCapacity =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // アプリクーポン4有効期限
                    aplCoupon4Group.aplCoupon4TermValidity =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    group.aplCoupon4Group = aplCoupon4Group;
                    // アプリクーポン5（グループ）
                    const aplCoupon5Group = getEmpty_AppCoupon5Group();
                    // アプリクーポン5容量
                    aplCoupon5Group.aplCoupon5Capacity =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // アプリクーポン5使用量
                    aplCoupon5Group.aplCoupon5Use = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    // アプリクーポン5残容量
                    aplCoupon5Group.aplCoupon5RemainCapacity =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // アプリクーポン5有効期限
                    aplCoupon5Group.aplCoupon5TermValidity =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    group.aplCoupon5Group = aplCoupon5Group;
                    // アプリクーポン6（グループ）
                    const aplCoupon6Group = getEmpty_AppCoupon6Group();
                    // アプリクーポン6容量
                    aplCoupon6Group.aplCoupon6Capacity =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // アプリクーポン6使用量
                    aplCoupon6Group.aplCoupon6Use = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    // アプリクーポン6残容量
                    aplCoupon6Group.aplCoupon6RemainCapacity =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // アプリクーポン6有効期限
                    aplCoupon6Group.aplCoupon6TermValidity =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    group.aplCoupon6Group = aplCoupon6Group;
                    // クーポンON/OFF状態（グループ）
                    group.couponOnOffGroup = MvnoUtil.addSpaceAfterStr(null, 0);
                    // ヘビーユーザ監視規制状態（グループ）
                    group.heavyUserMonitorStatusGroup =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // ヘビーユーザ監視規制開始時間（月間）（グループ）
                    group.heavyUserMonitorStartTimeMonthGroup =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // ヘビーユーザ監視規制開始時間（直近）（グループ）
                    group.heavyUserMonitorStartTimeLatestGroup =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 適用サービス状態（規制理由）（グループ）
                    group.regulationCauseGroup = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    // 総量規制状態（グループ）
                    group.totalVolumeControlStatusGroup =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 当日の通信量(クーポンOFF時)（グループ）
                    group.trafficOneDay_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 1日前の通信量(クーポンOFF時)（グループ）
                    group.traffic1dayAgo_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 2日前の通信量(クーポンOFF時)（グループ）
                    group.traffic2daysAgo_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 3日前の通信量(クーポンOFF時)（グループ）
                    group.traffic3daysAgo_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 4日前の通信量(クーポンOFF時)（グループ）
                    group.traffic4daysAgo_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 5日前の通信量(クーポンOFF時)（グループ）
                    group.traffic5daysAgo_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 6日前の通信量(クーポンOFF時)（グループ）
                    group.traffic6daysAgo_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 7日前の通信量(クーポンOFF時)（グループ）
                    group.traffic7daysAgo_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 8日前の通信量(クーポンOFF時)（グループ）
                    group.traffic8daysAgo_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 9日前の通信量(クーポンOFF時)（グループ）
                    group.traffic9daysAgo_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 10日前の通信量(クーポンOFF時)（グループ）
                    group.traffic10daysAgo_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 11日前の通信量(クーポンOFF時)（グループ）
                    group.traffic11daysAgo_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 12日前の通信量(クーポンOFF時)（グループ）
                    group.traffic12daysAgo_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 13日前の通信量(クーポンOFF時)（グループ）
                    group.traffic13daysAgo_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 14日前の通信量(クーポンOFF時)（グループ）
                    group.traffic14daysAgo_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 15日前の通信量(クーポンOFF時)（グループ）
                    group.traffic15daysAgo_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 16日前の通信量(クーポンOFF時)（グループ）
                    group.traffic16daysAgo_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 17日前の通信量(クーポンOFF時)（グループ）
                    group.traffic17daysAgo_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 18日前の通信量(クーポンOFF時)（グループ）
                    group.traffic18daysAgo_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 19日前の通信量(クーポンOFF時)（グループ）
                    group.traffic19daysAgo_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 20日前の通信量(クーポンOFF時)（グループ）
                    group.traffic20daysAgo_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 21日前の通信量(クーポンOFF時)（グループ）
                    group.traffic21daysAgo_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 22日前の通信量(クーポンOFF時)（グループ）
                    group.traffic22daysAgo_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 23日前の通信量(クーポンOFF時)（グループ）
                    group.traffic23daysAgo_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 24日前の通信量(クーポンOFF時)（グループ）
                    group.traffic24daysAgo_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 25日前の通信量(クーポンOFF時)（グループ）
                    group.traffic25daysAgo_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 26日前の通信量(クーポンOFF時)（グループ）
                    group.traffic26daysAgo_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 27日前の通信量(クーポンOFF時)（グループ）
                    group.traffic27daysAgo_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 28日前の通信量(クーポンOFF時)（グループ）
                    group.traffic28daysAgo_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 29日前の通信量(クーポンOFF時)（グループ）
                    group.traffic29daysAgo_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 30日前の通信量(クーポンOFF時)（グループ）
                    group.traffic30daysAgo_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 当月の通信量(クーポンOFF時)（グループ）
                    group.trafficThisMonth_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 前月の通信量(クーポンOFF時)（グループ）
                    group.trafficPreviousMonth_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 前々月の通信量(クーポンOFF時)（グループ）
                    group.trafficBeforehandMonth_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                } else {
                    // 正常時
                    let day = "";
                    const setTrafficdayAgo: string[] = new Array(31).fill("");

                    day = Check.getPreXDay(0);
                    let tagFlg = false;
                    tagFlg = this.sOAPCommon.CheckNodeExist(
                        document,
                        "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_acmlt_lst_info/dyn_" +
                            day,
                    );
                    if (tagFlg) {
                        for (let i = 0; i <= 30; i++) {
                            day = Check.getPreXDay(i);
                            const trafficdayAgo =
                                this.sOAPCommon.getNodeContent(
                                    document,
                                    "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_acmlt_lst_info/dyn_" +
                                        day,
                                );
                            setTrafficdayAgo[i] = trafficdayAgo;
                        }
                    } else {
                        for (let i = 1; i <= 31; i++) {
                            day = Check.getPreXDay(i);
                            const trafficdayAgo =
                                this.sOAPCommon.getNodeContent(
                                    document,
                                    "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_acmlt_lst_info/dyn_" +
                                        day,
                                );
                            setTrafficdayAgo[i - 1] = trafficdayAgo;
                        }
                    }
                    // クーポンOFF
                    let dayCouponOff = "";
                    const trafficdayAgoCouponOff: string[] = new Array(31).fill(
                        "",
                    );

                    dayCouponOff = Check.getPreXDay(0);
                    let tagFlgCouponOff = false;
                    tagFlgCouponOff = this.sOAPCommon.CheckNodeExist(
                        document,
                        "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_hvy_lst_info/dyn_" +
                            dayCouponOff,
                    );
                    if (tagFlgCouponOff) {
                        for (let i = 0; i <= 30; i++) {
                            dayCouponOff = Check.getPreXDay(i);
                            const trafficdayAgo =
                                this.sOAPCommon.getNodeContent(
                                    document,
                                    "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_hvy_lst_info/dyn_" +
                                        dayCouponOff,
                                );
                            trafficdayAgoCouponOff[i] = trafficdayAgo;
                        }
                    } else {
                        for (let i = 1; i <= 31; i++) {
                            dayCouponOff = Check.getPreXDay(i);
                            const trafficdayAgo =
                                this.sOAPCommon.getNodeContent(
                                    document,
                                    "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_hvy_lst_info/dyn_" +
                                        dayCouponOff,
                                );
                            trafficdayAgoCouponOff[i - 1] = trafficdayAgo;
                        }
                    }

                    // 当日の通信量（グループ）
                    group.trafficOneDayGroup = MvnoUtil.addSpaceAfterStr(
                        setTrafficdayAgo[0],
                        0,
                    );
                    // 1日前の通信量（グループ）
                    group.traffic1dayAgoGroup = MvnoUtil.addSpaceAfterStr(
                        setTrafficdayAgo[1],
                        0,
                    );
                    // 2日前の通信量（グループ）
                    group.traffic2daysAgoGroup = MvnoUtil.addSpaceAfterStr(
                        setTrafficdayAgo[2],
                        0,
                    );
                    // 3日前の通信量（グループ）
                    group.traffic3daysAgoGroup = MvnoUtil.addSpaceAfterStr(
                        setTrafficdayAgo[3],
                        0,
                    );
                    // 4日前の通信量（グループ）
                    group.traffic4daysAgoGroup = MvnoUtil.addSpaceAfterStr(
                        setTrafficdayAgo[4],
                        0,
                    );
                    // 5日前の通信量（グループ）
                    group.traffic5daysAgoGroup = MvnoUtil.addSpaceAfterStr(
                        setTrafficdayAgo[5],
                        0,
                    );
                    // 6日前の通信量（グループ）
                    group.traffic6daysAgoGroup = MvnoUtil.addSpaceAfterStr(
                        setTrafficdayAgo[6],
                        0,
                    );
                    // 7日前の通信量（グループ）
                    group.traffic7daysAgoGroup = MvnoUtil.addSpaceAfterStr(
                        setTrafficdayAgo[7],
                        0,
                    );
                    // 8日前の通信量（グループ）
                    group.traffic8daysAgoGroup = MvnoUtil.addSpaceAfterStr(
                        setTrafficdayAgo[8],
                        0,
                    );
                    // 9日前の通信量（グループ）
                    group.traffic9daysAgoGroup = MvnoUtil.addSpaceAfterStr(
                        setTrafficdayAgo[9],
                        0,
                    );
                    // 10日前の通信量（グループ）
                    group.traffic10daysAgoGroup = MvnoUtil.addSpaceAfterStr(
                        setTrafficdayAgo[10],
                        0,
                    );
                    // 11日前の通信量（グループ）
                    group.traffic11daysAgoGroup = MvnoUtil.addSpaceAfterStr(
                        setTrafficdayAgo[11],
                        0,
                    );
                    // 12日前の通信量（グループ）
                    group.traffic12daysAgoGroup = MvnoUtil.addSpaceAfterStr(
                        setTrafficdayAgo[12],
                        0,
                    );
                    // 13日前の通信量（グループ）
                    group.traffic13daysAgoGroup = MvnoUtil.addSpaceAfterStr(
                        setTrafficdayAgo[13],
                        0,
                    );
                    // 14日前の通信量（グループ）
                    group.traffic14daysAgoGroup = MvnoUtil.addSpaceAfterStr(
                        setTrafficdayAgo[14],
                        0,
                    );
                    // 15日前の通信量（グループ）
                    group.traffic15daysAgoGroup = MvnoUtil.addSpaceAfterStr(
                        setTrafficdayAgo[15],
                        0,
                    );
                    // 16日前の通信量（グループ）
                    group.traffic16daysAgoGroup = MvnoUtil.addSpaceAfterStr(
                        setTrafficdayAgo[16],
                        0,
                    );
                    // 17日前の通信量（グループ）
                    group.traffic17daysAgoGroup = MvnoUtil.addSpaceAfterStr(
                        setTrafficdayAgo[17],
                        0,
                    );
                    // 18日前の通信量（グループ）
                    group.traffic18daysAgoGroup = MvnoUtil.addSpaceAfterStr(
                        setTrafficdayAgo[18],
                        0,
                    );
                    // 19日前の通信量（グループ）
                    group.traffic19daysAgoGroup = MvnoUtil.addSpaceAfterStr(
                        setTrafficdayAgo[19],
                        0,
                    );
                    // 20日前の通信量（グループ）
                    group.traffic20daysAgoGroup = MvnoUtil.addSpaceAfterStr(
                        setTrafficdayAgo[20],
                        0,
                    );
                    // 21日前の通信量（グループ）
                    group.traffic21daysAgoGroup = MvnoUtil.addSpaceAfterStr(
                        setTrafficdayAgo[21],
                        0,
                    );
                    // 22日前の通信量（グループ）
                    group.traffic22daysAgoGroup = MvnoUtil.addSpaceAfterStr(
                        setTrafficdayAgo[22],
                        0,
                    );
                    // 23日前の通信量（グループ）
                    group.traffic23daysAgoGroup = MvnoUtil.addSpaceAfterStr(
                        setTrafficdayAgo[23],
                        0,
                    );
                    // 24日前の通信量（グループ）
                    group.traffic24daysAgoGroup = MvnoUtil.addSpaceAfterStr(
                        setTrafficdayAgo[24],
                        0,
                    );
                    // 25日前の通信量（グループ）
                    group.traffic25daysAgoGroup = MvnoUtil.addSpaceAfterStr(
                        setTrafficdayAgo[25],
                        0,
                    );
                    // 26日前の通信量（グループ）
                    group.traffic26daysAgoGroup = MvnoUtil.addSpaceAfterStr(
                        setTrafficdayAgo[26],
                        0,
                    );
                    // 27日前の通信量（グループ）
                    group.traffic27daysAgoGroup = MvnoUtil.addSpaceAfterStr(
                        setTrafficdayAgo[27],
                        0,
                    );
                    // 28日前の通信量（グループ）
                    group.traffic28daysAgoGroup = MvnoUtil.addSpaceAfterStr(
                        setTrafficdayAgo[28],
                        0,
                    );
                    // 29日前の通信量（グループ）
                    group.traffic29daysAgoGroup = MvnoUtil.addSpaceAfterStr(
                        setTrafficdayAgo[29],
                        0,
                    );
                    // 30日前の通信量（グループ）
                    group.traffic30daysAgoGroup = MvnoUtil.addSpaceAfterStr(
                        setTrafficdayAgo[30],
                        0,
                    );
                    // 当月の通信量（グループ）
                    group.trafficThisMonthGroup = MvnoUtil.addSpaceAfterStr(
                        this.sOAPCommon.getNodeContent(
                            document,
                            "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_acmlt_month_info/dyn_use",
                        ),
                        0,
                    );
                    // 前月の通信量（グループ）
                    group.trafficPreviousMonthGroup = MvnoUtil.addSpaceAfterStr(
                        trafficPreviousMonthGroup,
                        0,
                    );
                    // 前々月の通信量（グループ）
                    group.trafficBeforehandMonthGroup =
                        MvnoUtil.addSpaceAfterStr(
                            trafficBeforehandMonthGroup,
                            0,
                        );
                    let dyn_cap = MvnoUtil.addSpaceAfterStr(null, 0);
                    let dyn_use = MvnoUtil.addSpaceAfterStr(null, 0);
                    // 基本クーポン残容量（グループ）
                    let basicCouponRemainsGroup = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    // 基本クーポン有効期限（グループ）
                    let basicCouponTermValidityGroup =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    if (
                        CONST_SERVICEPATTERN_1.equals(servicePatternGroup) ||
                        CONST_SERVICEPATTERN_2.equals(servicePatternGroup) ||
                        CONST_SERVICEPATTERN_3.equals(servicePatternGroup) ||
                        CONST_SERVICEPATTERN_4.equals(servicePatternGroup)
                    ) {
                        // 基本クーポン残容量
                        dyn_cap = this.sOAPCommon.getNodeContent(
                            document,
                            "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_month_info/dyn_cap",
                        );
                        dyn_use = this.sOAPCommon.getNodeContent(
                            document,
                            "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_month_info/dyn_use",
                        );
                        if (
                            CheckUtil.checkIsNum(dyn_cap) &&
                            CheckUtil.checkIsNum(dyn_use)
                        ) {
                            const basicCouponRemainsL =
                                BigInt(dyn_cap) - BigInt(dyn_use);
                            basicCouponRemainsGroup = MvnoUtil.addSpaceAfterStr(
                                String(basicCouponRemainsL),
                                0,
                            );
                        }
                        // 基本クーポン有効期限
                        basicCouponTermValidityGroup =
                            MvnoUtil.addSpaceAfterStr(
                                this.sOAPCommon.getNodeContent(
                                    document,
                                    "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_month_info/dyn_exp_date",
                                ),
                                10,
                            );
                    }
                    // 基本クーポン残容量（グループ）
                    group.basicCouponCapacityGroup = dyn_cap;
                    // 基本クーポン有効期限（グループ）
                    group.basicCouponUseGroup = dyn_use;
                    // 基本クーポン残容量（グループ）
                    group.basicCouponRemainsGroup = basicCouponRemainsGroup;
                    // 基本クーポン有効期限（グループ）
                    group.basicCouponTermValidityGroup =
                        basicCouponTermValidityGroup;
                    // クーポン容量切れ時間（グループ）
                    let couponPieceTimeGroup = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    const dyn_reg_sta_month = this.sOAPCommon.getNodeContent(
                        document,
                        "//dyn_grp_info/dyn_node_info_0/dyn_reg_info/dyn_reg_sta_month",
                    );
                    const rtndyn_reg_sta_month =
                        CheckUtil.forMartdynDate(dyn_reg_sta_month);
                    couponPieceTimeGroup = MvnoUtil.addSpaceAfterStr(
                        rtndyn_reg_sta_month,
                        16,
                    );
                    // クーポン容量切れ時間（グループ）
                    group.couponPieceTimeGroup = couponPieceTimeGroup;
                    // 繰越クーポン1（グループ）
                    const carryingOverCoupon1Group =
                        getEmpty_CarryingOverCoupon1Group();
                    // 繰越クーポン1残容量
                    let carryingOverCoupon1RemainCapacity1 =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 繰越クーポン1有効期限
                    let carryingOverCoupon1TermValidity1 =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    const tran_dyn_cap_1 = this.sOAPCommon.getNodeContent(
                        document,
                        "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_tran_info_0/dyn_cap",
                    );
                    const tran_dyn_use_1 = this.sOAPCommon.getNodeContent(
                        document,
                        "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_tran_info_0/dyn_use",
                    );
                    if (
                        CheckUtil.checkIsNum(tran_dyn_cap_1) &&
                        CheckUtil.checkIsNum(tran_dyn_use_1)
                    ) {
                        const carryingOverCoupon1RemainCapacityL =
                            BigInt(tran_dyn_cap_1) - BigInt(tran_dyn_use_1);
                        // 繰越クーポン1残容量
                        carryingOverCoupon1RemainCapacity1 =
                            MvnoUtil.addSpaceAfterStr(
                                String(carryingOverCoupon1RemainCapacityL),
                                0,
                            );
                    }
                    // 繰越クーポン1有効期限
                    carryingOverCoupon1TermValidity1 =
                        MvnoUtil.addSpaceAfterStr(
                            this.sOAPCommon.getNodeContent(
                                document,
                                "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_tran_info_0/dyn_exp_date",
                            ),
                            10,
                        );
                    // 繰越クーポン1容量
                    carryingOverCoupon1Group.carryingOverCoupon1Capacity =
                        MvnoUtil.addSpaceAfterStr(tran_dyn_cap_1, 0);
                    // 繰越クーポン1使用量
                    carryingOverCoupon1Group.carryingOverCoupon1Use =
                        MvnoUtil.addSpaceAfterStr(tran_dyn_use_1, 0);
                    // 繰越クーポン1残容量
                    carryingOverCoupon1Group.carryingOverCoupon1RemainCapacity =
                        carryingOverCoupon1RemainCapacity1;
                    // 追加クーポン1有効期限
                    carryingOverCoupon1Group.carryingOverCoupon1TermValidity =
                        carryingOverCoupon1TermValidity1;
                    group.carryingOverCoupon1Group = carryingOverCoupon1Group;
                    // 繰越クーポン2（グループ）
                    const carryingOverCoupon2Group =
                        getEmpty_CarryingOverCoupon2Group();
                    // 繰越クーポン2残容量
                    let carryingOverCoupon1RemainCapacity2 =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 繰越クーポン2有効期限
                    let carryingOverCoupon1TermValidity2 =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    const tran_dyn_cap_2 = this.sOAPCommon.getNodeContent(
                        document,
                        "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_tran_info_1/dyn_cap",
                    );
                    const tran_dyn_use_2 = this.sOAPCommon.getNodeContent(
                        document,
                        "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_tran_info_1/dyn_use",
                    );
                    if (
                        CheckUtil.checkIsNum(tran_dyn_cap_2) &&
                        CheckUtil.checkIsNum(tran_dyn_use_2)
                    ) {
                        const carryingOverCoupon1RemainCapacityL =
                            BigInt(tran_dyn_cap_2) - BigInt(tran_dyn_use_2);
                        // 繰越クーポン2残容量
                        carryingOverCoupon1RemainCapacity2 =
                            MvnoUtil.addSpaceAfterStr(
                                String(carryingOverCoupon1RemainCapacityL),
                                0,
                            );
                    }
                    // 繰越クーポン2有効期限
                    carryingOverCoupon1TermValidity2 =
                        MvnoUtil.addSpaceAfterStr(
                            this.sOAPCommon.getNodeContent(
                                document,
                                "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_tran_info_1/dyn_exp_date",
                            ),
                            10,
                        );
                    // 繰越クーポン2容量
                    carryingOverCoupon2Group.carryingOverCoupon2Capacity =
                        MvnoUtil.addSpaceAfterStr(tran_dyn_cap_2, 0);
                    // 繰越クーポン2使用量
                    carryingOverCoupon2Group.carryingOverCoupon2Use =
                        MvnoUtil.addSpaceAfterStr(tran_dyn_use_2, 0);
                    // 繰越クーポン2残容量
                    carryingOverCoupon2Group.carryingOverCoupon2RemainCapacity =
                        carryingOverCoupon1RemainCapacity2;
                    // 繰越クーポン2有効期限
                    carryingOverCoupon2Group.carryingOverCoupon2TermValidity =
                        carryingOverCoupon1TermValidity2;
                    group.carryingOverCoupon2Group = carryingOverCoupon2Group;
                    // 追加クーポン1（グループ）
                    const additionalCoupon1Group =
                        getEmpty_AdditionalCoupon1Group();
                    // 追加クーポン1容量
                    let additionalCoupon1Capacity = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    // 追加クーポン1使用量
                    let additionalCoupon1Use = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    // 追加クーポン1残容量
                    let additionalCoupon1RemainCapacity =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 追加クーポン1有効期限
                    let additionalCoupon1TermValidity =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    if (
                        CONST_SERVICEPATTERN_1.equals(servicePatternGroup) ||
                        CONST_SERVICEPATTERN_2.equals(servicePatternGroup) ||
                        CONST_SERVICEPATTERN_4.equals(servicePatternGroup)
                    ) {
                        additionalCoupon1Capacity =
                            this.sOAPCommon.getNodeContent(
                                document,
                                "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_add_info_0/dyn_cap",
                            );
                        additionalCoupon1Use = this.sOAPCommon.getNodeContent(
                            document,
                            "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_add_info_0/dyn_use",
                        );
                        if (
                            CheckUtil.checkIsNum(additionalCoupon1Capacity) &&
                            CheckUtil.checkIsNum(additionalCoupon1Use)
                        ) {
                            const additionalCoupon1RemainCapacityL =
                                BigInt(additionalCoupon1Capacity) -
                                BigInt(additionalCoupon1Use);
                            // 追加クーポン1残容量
                            additionalCoupon1RemainCapacity =
                                MvnoUtil.addSpaceAfterStr(
                                    String(additionalCoupon1RemainCapacityL),
                                    0,
                                );
                        }
                        // 追加クーポン1有効期限
                        additionalCoupon1TermValidity =
                            MvnoUtil.addSpaceAfterStr(
                                this.sOAPCommon.getNodeContent(
                                    document,
                                    "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_add_info_0/dyn_exp_date",
                                ),
                                10,
                            );
                    } else if (
                        CONST_SERVICEPATTERN_3.equals(servicePatternGroup)
                    ) {
                        additionalCoupon1Capacity =
                            this.sOAPCommon.getNodeContent(
                                document,
                                "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_addbd_info_0/dyn_cap",
                            );
                        additionalCoupon1Use = this.sOAPCommon.getNodeContent(
                            document,
                            "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_addbd_info_0/dyn_use",
                        );
                        if (
                            CheckUtil.checkIsNum(additionalCoupon1Capacity) &&
                            CheckUtil.checkIsNum(additionalCoupon1Use)
                        ) {
                            const additionalCoupon1RemainCapacityL =
                                BigInt(additionalCoupon1Capacity) -
                                BigInt(additionalCoupon1Use);
                            // 追加クーポン1残容量
                            additionalCoupon1RemainCapacity =
                                MvnoUtil.addSpaceAfterStr(
                                    String(additionalCoupon1RemainCapacityL),
                                    0,
                                );
                        }
                        // 追加クーポン1有効期限
                        additionalCoupon1TermValidity =
                            MvnoUtil.addSpaceAfterStr(
                                this.sOAPCommon.getNodeContent(
                                    document,
                                    "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_addbd_info_0/dyn_exp_date",
                                ),
                                10,
                            );
                    }
                    // 追加クーポン1容量
                    additionalCoupon1Group.additionalCoupon1Capacity =
                        MvnoUtil.addSpaceAfterStr(additionalCoupon1Capacity, 0);
                    // 追加クーポン1使用量
                    additionalCoupon1Group.additionalCoupon1Use =
                        MvnoUtil.addSpaceAfterStr(additionalCoupon1Use, 0);
                    // 追加クーポン1残容量
                    additionalCoupon1Group.additionalCoupon1RemainCapacity =
                        additionalCoupon1RemainCapacity;
                    // 追加クーポン1有効期限
                    additionalCoupon1Group.additionalCoupon1TermValidity =
                        additionalCoupon1TermValidity;
                    group.additionalCoupon1Group = additionalCoupon1Group;
                    // 追加クーポン2（グループ）
                    const additionalCoupon2Group =
                        getEmpty_AdditionalCoupon2Group();
                    // 追加クーポン2容量
                    let additionalCoupon2Capacity = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    // 追加クーポン2使用量
                    let additionalCoupon2Use = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    // 追加クーポン2残容量
                    let additionalCoupon2RemainCapacity =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 追加クーポン2有効期限
                    let additionalCoupon2TermValidity =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    if (
                        CONST_SERVICEPATTERN_1.equals(servicePatternGroup) ||
                        CONST_SERVICEPATTERN_2.equals(servicePatternGroup) ||
                        CONST_SERVICEPATTERN_4.equals(servicePatternGroup)
                    ) {
                        additionalCoupon2Capacity =
                            this.sOAPCommon.getNodeContent(
                                document,
                                "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_add_info_1/dyn_cap",
                            );
                        additionalCoupon2Use = this.sOAPCommon.getNodeContent(
                            document,
                            "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_add_info_1/dyn_use",
                        );
                        if (
                            CheckUtil.checkIsNum(additionalCoupon2Capacity) &&
                            CheckUtil.checkIsNum(additionalCoupon2Use)
                        ) {
                            const additionalCoupon1RemainCapacityL =
                                BigInt(additionalCoupon2Capacity) -
                                BigInt(additionalCoupon2Use);
                            // 追加クーポン2残容量
                            additionalCoupon2RemainCapacity =
                                MvnoUtil.addSpaceAfterStr(
                                    String(additionalCoupon1RemainCapacityL),
                                    0,
                                );
                        }
                        // 追加クーポン2有効期限
                        additionalCoupon2TermValidity =
                            MvnoUtil.addSpaceAfterStr(
                                this.sOAPCommon.getNodeContent(
                                    document,
                                    "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_add_info_1/dyn_exp_date",
                                ),
                                10,
                            );
                    } else if (
                        CONST_SERVICEPATTERN_3.equals(servicePatternGroup)
                    ) {
                        additionalCoupon2Capacity =
                            this.sOAPCommon.getNodeContent(
                                document,
                                "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_addbd_info_1/dyn_cap",
                            );
                        additionalCoupon2Use = this.sOAPCommon.getNodeContent(
                            document,
                            "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_addbd_info_1/dyn_use",
                        );
                        if (
                            CheckUtil.checkIsNum(additionalCoupon2Capacity) &&
                            CheckUtil.checkIsNum(additionalCoupon2Use)
                        ) {
                            const additionalCoupon1RemainCapacityL =
                                BigInt(additionalCoupon2Capacity) -
                                BigInt(additionalCoupon2Use);
                            // 追加クーポン2残容量
                            additionalCoupon2RemainCapacity =
                                MvnoUtil.addSpaceAfterStr(
                                    String(additionalCoupon1RemainCapacityL),
                                    0,
                                );
                        }
                        // 追加クーポン2有効期限
                        additionalCoupon2TermValidity =
                            MvnoUtil.addSpaceAfterStr(
                                this.sOAPCommon.getNodeContent(
                                    document,
                                    "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_addbd_info_1/dyn_exp_date",
                                ),
                                10,
                            );
                    }
                    // 追加クーポン2容量
                    additionalCoupon2Group.additionalCoupon2Capacity =
                        MvnoUtil.addSpaceAfterStr(additionalCoupon2Capacity, 0);
                    // 追加クーポン2使用量
                    additionalCoupon2Group.additionalCoupon2Use =
                        MvnoUtil.addSpaceAfterStr(additionalCoupon2Use, 0);
                    // 追加クーポン2残容量
                    additionalCoupon2Group.additionalCoupon2RemainCapacity =
                        additionalCoupon2RemainCapacity;
                    // 追加クーポン2有効期限
                    additionalCoupon2Group.additionalCoupon2TermValidity =
                        additionalCoupon2TermValidity;
                    group.additionalCoupon2Group = additionalCoupon2Group;
                    // 追加クーポン3（グループ）
                    const additionalCoupon3Group =
                        getEmpty_AdditionalCoupon3Group();
                    // 追加クーポン3容量
                    let additionalCoupon3Capacity = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    // 追加クーポン3使用量
                    let additionalCoupon3Use = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    // 追加クーポン3残容量
                    let additionalCoupon3RemainCapacity =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 追加クーポン3有効期限
                    let additionalCoupon3TermValidity =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    if (
                        CONST_SERVICEPATTERN_1.equals(servicePatternGroup) ||
                        CONST_SERVICEPATTERN_2.equals(servicePatternGroup) ||
                        CONST_SERVICEPATTERN_4.equals(servicePatternGroup)
                    ) {
                        additionalCoupon3Capacity =
                            this.sOAPCommon.getNodeContent(
                                document,
                                "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_add_info_2/dyn_cap",
                            );
                        additionalCoupon3Use = this.sOAPCommon.getNodeContent(
                            document,
                            "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_add_info_2/dyn_use",
                        );
                        if (
                            CheckUtil.checkIsNum(additionalCoupon3Capacity) &&
                            CheckUtil.checkIsNum(additionalCoupon3Use)
                        ) {
                            const additionaCoupon1RemainCapacityL =
                                BigInt(additionalCoupon3Capacity) -
                                BigInt(additionalCoupon3Use);
                            // 追加クーポン3残容量
                            additionalCoupon3RemainCapacity =
                                MvnoUtil.addSpaceAfterStr(
                                    String(additionaCoupon1RemainCapacityL),
                                    0,
                                );
                        }
                        // 追加クーポン3有効期限
                        additionalCoupon3TermValidity =
                            MvnoUtil.addSpaceAfterStr(
                                this.sOAPCommon.getNodeContent(
                                    document,
                                    "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_add_info_2/dyn_exp_date",
                                ),
                                10,
                            );
                    } else if (
                        CONST_SERVICEPATTERN_3.equals(servicePatternGroup)
                    ) {
                        additionalCoupon3Capacity =
                            this.sOAPCommon.getNodeContent(
                                document,
                                "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_addbd_info_2/dyn_cap",
                            );
                        additionalCoupon3Use = this.sOAPCommon.getNodeContent(
                            document,
                            "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_addbd_info_2/dyn_use",
                        );
                        if (
                            CheckUtil.checkIsNum(additionalCoupon3Capacity) &&
                            CheckUtil.checkIsNum(additionalCoupon3Use)
                        ) {
                            const additionalCoupon1RemainCapacityL =
                                BigInt(additionalCoupon3Capacity) -
                                BigInt(additionalCoupon3Use);
                            // 追加クーポン3残容量
                            additionalCoupon3RemainCapacity =
                                MvnoUtil.addSpaceAfterStr(
                                    String(additionalCoupon1RemainCapacityL),
                                    0,
                                );
                        }
                        // 追加クーポン3有効期限
                        additionalCoupon3TermValidity =
                            MvnoUtil.addSpaceAfterStr(
                                this.sOAPCommon.getNodeContent(
                                    document,
                                    "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_addbd_info_2/dyn_exp_date",
                                ),
                                10,
                            );
                    }
                    // 追加クーポン3容量
                    additionalCoupon3Group.additionalCoupon3Capacity =
                        MvnoUtil.addSpaceAfterStr(additionalCoupon3Capacity, 0);
                    // 追加クーポン3使用量
                    additionalCoupon3Group.additionalCoupon3Use =
                        MvnoUtil.addSpaceAfterStr(additionalCoupon3Use, 0);
                    // 追加クーポン3残容量
                    additionalCoupon3Group.additionalCoupon3RemainCapacity =
                        additionalCoupon3RemainCapacity;
                    // 追加クーポン3残容量
                    additionalCoupon3Group.additionalCoupon3TermValidity =
                        additionalCoupon3TermValidity;
                    group.additionalCoupon3Group = additionalCoupon3Group;
                    // 追加クーポン4（グループ）
                    const additionalCoupon4Group =
                        getEmpty_AdditionalCoupon4Group();
                    // 追加クーポン4容量
                    let additionalCoupon4Capacity = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    // 追加クーポン4使用量
                    let additionalCoupon4Use = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    // 追加クーポン4残容量
                    let additionalCoupon4RemainCapacity =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 追加クーポン4有効期限
                    let additionalCoupon4TermValidity =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    if (
                        CONST_SERVICEPATTERN_1.equals(servicePatternGroup) ||
                        CONST_SERVICEPATTERN_2.equals(servicePatternGroup) ||
                        CONST_SERVICEPATTERN_4.equals(servicePatternGroup)
                    ) {
                        additionalCoupon4Capacity =
                            this.sOAPCommon.getNodeContent(
                                document,
                                "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_add_info_3/dyn_cap",
                            );
                        additionalCoupon4Use = this.sOAPCommon.getNodeContent(
                            document,
                            "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_add_info_3/dyn_use",
                        );
                        if (
                            CheckUtil.checkIsNum(additionalCoupon4Capacity) &&
                            CheckUtil.checkIsNum(additionalCoupon4Use)
                        ) {
                            const additionalCoupon1RemainCapacityL =
                                BigInt(additionalCoupon4Capacity) -
                                BigInt(additionalCoupon4Use);
                            // 追加クーポン4残容量
                            additionalCoupon4RemainCapacity =
                                MvnoUtil.addSpaceAfterStr(
                                    String(additionalCoupon1RemainCapacityL),
                                    0,
                                );
                        }
                        // 追加クーポン4有効期限
                        additionalCoupon4TermValidity =
                            MvnoUtil.addSpaceAfterStr(
                                this.sOAPCommon.getNodeContent(
                                    document,
                                    "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_add_info_3/dyn_exp_date",
                                ),
                                10,
                            );
                    } else if (
                        CONST_SERVICEPATTERN_3.equals(servicePatternGroup)
                    ) {
                        additionalCoupon4Capacity =
                            this.sOAPCommon.getNodeContent(
                                document,
                                "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_addbd_info_3/dyn_cap",
                            );
                        additionalCoupon4Use = this.sOAPCommon.getNodeContent(
                            document,
                            "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_addbd_info_3/dyn_use",
                        );
                        if (
                            CheckUtil.checkIsNum(additionalCoupon4Capacity) &&
                            CheckUtil.checkIsNum(additionalCoupon4Use)
                        ) {
                            const additionalCoupon1RemainCapacityL =
                                BigInt(additionalCoupon4Capacity) -
                                BigInt(additionalCoupon4Use);
                            // 追加クーポン4残容量
                            additionalCoupon4RemainCapacity =
                                MvnoUtil.addSpaceAfterStr(
                                    String(additionalCoupon1RemainCapacityL),
                                    0,
                                );
                        }
                        // 追加クーポン4有効期限
                        additionalCoupon4TermValidity =
                            MvnoUtil.addSpaceAfterStr(
                                this.sOAPCommon.getNodeContent(
                                    document,
                                    "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_addbd_info_3/dyn_exp_date",
                                ),
                                10,
                            );
                    }
                    // 追加クーポン4容量
                    additionalCoupon4Group.additionalCoupon4Capacity =
                        MvnoUtil.addSpaceAfterStr(additionalCoupon4Capacity, 0);
                    // 追加クーポン4使用量
                    additionalCoupon4Group.additionalCoupon4Use =
                        MvnoUtil.addSpaceAfterStr(additionalCoupon4Use, 0);
                    // 追加クーポン4残容量
                    additionalCoupon4Group.additionalCoupon4RemainCapacity =
                        additionalCoupon4RemainCapacity;
                    // 追加クーポン4有効期限
                    additionalCoupon4Group.additionalCoupon4TermValidity =
                        additionalCoupon4TermValidity;
                    group.additionalCoupon4Group = additionalCoupon4Group;
                    // 追加クーポン5（グループ）
                    const additionalCoupon5Group =
                        getEmpty_AdditionalCoupon5Group();
                    // 追加クーポン5容量
                    let additionalCoupon5Capacity = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    // 追加クーポン5使用量
                    let additionalCoupon5Use = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    // 追加クーポン5残容量
                    let additionalCoupon5RemainCapacity =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 追加クーポン5有効期限
                    let additionalCoupon5TermValidity =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    if (
                        CONST_SERVICEPATTERN_1.equals(servicePatternGroup) ||
                        CONST_SERVICEPATTERN_2.equals(servicePatternGroup) ||
                        CONST_SERVICEPATTERN_4.equals(servicePatternGroup)
                    ) {
                        additionalCoupon5Capacity =
                            this.sOAPCommon.getNodeContent(
                                document,
                                "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_add_info_4/dyn_cap",
                            );
                        additionalCoupon5Use = this.sOAPCommon.getNodeContent(
                            document,
                            "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_add_info_4/dyn_use",
                        );
                        if (
                            CheckUtil.checkIsNum(additionalCoupon5Capacity) &&
                            CheckUtil.checkIsNum(additionalCoupon5Use)
                        ) {
                            const additionalCoupon1RemainCapacityL =
                                BigInt(additionalCoupon5Capacity) -
                                BigInt(additionalCoupon5Use);
                            // 追加クーポン5残容量
                            additionalCoupon5RemainCapacity =
                                MvnoUtil.addSpaceAfterStr(
                                    String(additionalCoupon1RemainCapacityL),
                                    0,
                                );
                        }
                        // 追加クーポン5有効期限
                        additionalCoupon5TermValidity =
                            MvnoUtil.addSpaceAfterStr(
                                this.sOAPCommon.getNodeContent(
                                    document,
                                    "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_add_info_4/dyn_exp_date",
                                ),
                                10,
                            );
                    } else if (
                        CONST_SERVICEPATTERN_3.equals(servicePatternGroup)
                    ) {
                        additionalCoupon5Capacity =
                            this.sOAPCommon.getNodeContent(
                                document,
                                "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_addbd_info_4/dyn_cap",
                            );
                        additionalCoupon5Use = this.sOAPCommon.getNodeContent(
                            document,
                            "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_addbd_info_4/dyn_use",
                        );
                        if (
                            CheckUtil.checkIsNum(additionalCoupon5Capacity) &&
                            CheckUtil.checkIsNum(additionalCoupon5Use)
                        ) {
                            const additionalCoupon1RemainCapacityL =
                                BigInt(additionalCoupon5Capacity) -
                                BigInt(additionalCoupon5Use);
                            // 追加クーポン5残容量
                            additionalCoupon5RemainCapacity =
                                MvnoUtil.addSpaceAfterStr(
                                    String(additionalCoupon1RemainCapacityL),
                                    0,
                                );
                        }
                        // 追加クーポン5有効期限
                        additionalCoupon5TermValidity =
                            MvnoUtil.addSpaceAfterStr(
                                this.sOAPCommon.getNodeContent(
                                    document,
                                    "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_cap_addbd_info_4/dyn_exp_date",
                                ),
                                10,
                            );
                    }
                    // 追加クーポン5容量
                    additionalCoupon5Group.additionalCoupon5Capacity =
                        MvnoUtil.addSpaceAfterStr(additionalCoupon5Capacity, 0);
                    // 追加クーポン5使用量
                    additionalCoupon5Group.additionalCoupon5Use =
                        MvnoUtil.addSpaceAfterStr(additionalCoupon5Use, 0);
                    // 追加クーポン5残容量
                    additionalCoupon5Group.additionalCoupon5RemainCapacity =
                        additionalCoupon5RemainCapacity;
                    // 追加クーポン5有効期限
                    additionalCoupon5Group.additionalCoupon5TermValidity =
                        additionalCoupon5TermValidity;
                    // 追加クーポン5残容量
                    group.additionalCoupon5Group = additionalCoupon5Group;
                    // ＊＊＊　アプリクーポン1（グループ）　＊＊＊
                    const appCoupon1Group = getEmpty_AppCoupon1Group();
                    let parentPath =
                        "//dyn_grp_info/dyn_node_info_0/dyn_sdf_polinfo_0/dyn_bucket_info";
                    // アプリクーポン1容量
                    const aplCoupon1Capacity = this.sOAPCommon.getNodeContent(
                        document,
                        parentPath + "/dyn_cap_fltr",
                    );
                    // アプリクーポン1使用量
                    const aplCoupon1Use = this.sOAPCommon.getNodeContent(
                        document,
                        parentPath + "/dyn_use_fltr",
                    );
                    // アプリクーポン1残容量
                    let aplCoupon1RemainCapacity = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    // アプリクーポン1有効期限
                    const aplCoupon1TermValidity =
                        this.sOAPCommon.getNodeContent(
                            document,
                            parentPath + "/dyn_exp_date",
                        );
                    if (
                        CheckUtil.checkIsNum(aplCoupon1Capacity) &&
                        CheckUtil.checkIsNum(aplCoupon1Use)
                    ) {
                        const aplCoupon1RemainCapacityCalc =
                            BigInt(aplCoupon1Capacity) - BigInt(aplCoupon1Use);
                        // アプリクーポン1残容量
                        aplCoupon1RemainCapacity = MvnoUtil.addSpaceAfterStr(
                            String(aplCoupon1RemainCapacityCalc),
                            0,
                        );
                    }
                    // アプリクーポン1容量
                    appCoupon1Group.aplCoupon1Capacity =
                        MvnoUtil.addSpaceAfterStr(aplCoupon1Capacity, 0);
                    // アプリクーポン1使用量
                    appCoupon1Group.aplCoupon1Use = MvnoUtil.addSpaceAfterStr(
                        aplCoupon1Use,
                        0,
                    );
                    // アプリクーポン1残容量
                    appCoupon1Group.aplCoupon1RemainCapacity =
                        aplCoupon1RemainCapacity;
                    // アプリクーポン1有効期限
                    appCoupon1Group.aplCoupon1TermValidity =
                        MvnoUtil.addSpaceAfterStr(aplCoupon1TermValidity, 10);
                    group.aplCoupon1Group = appCoupon1Group;
                    // ＊＊＊　アプリクーポン2（グループ）　＊＊＊
                    const appCoupon2Group = getEmpty_AppCoupon2Group();
                    parentPath =
                        "//dyn_grp_info/dyn_node_info_0/dyn_sdf_polinfo_1/dyn_bucket_info";
                    // アプリクーポン2容量
                    const aplCoupon2Capacity = this.sOAPCommon.getNodeContent(
                        document,
                        parentPath + "/dyn_cap_fltr",
                    );
                    // アプリクーポン2使用量
                    const aplCoupon2Use = this.sOAPCommon.getNodeContent(
                        document,
                        parentPath + "/dyn_use_fltr",
                    );
                    // アプリクーポン2残容量
                    let aplCoupon2RemainCapacity = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    // アプリクーポン2有効期限
                    const aplCoupon2TermValidity =
                        this.sOAPCommon.getNodeContent(
                            document,
                            parentPath + "/dyn_exp_date",
                        );
                    if (
                        CheckUtil.checkIsNum(aplCoupon2Capacity) &&
                        CheckUtil.checkIsNum(aplCoupon2Use)
                    ) {
                        const aplCoupon2RemainCapacityCalc =
                            BigInt(aplCoupon2Capacity) - BigInt(aplCoupon2Use);
                        // アプリクーポン2残容量
                        aplCoupon2RemainCapacity = MvnoUtil.addSpaceAfterStr(
                            String(aplCoupon2RemainCapacityCalc),
                            0,
                        );
                    }
                    // アプリクーポン2容量
                    appCoupon2Group.aplCoupon2Capacity =
                        MvnoUtil.addSpaceAfterStr(aplCoupon2Capacity, 0);
                    // アプリクーポン2使用量
                    appCoupon2Group.aplCoupon2Use = MvnoUtil.addSpaceAfterStr(
                        aplCoupon2Use,
                        0,
                    );
                    // アプリクーポン2残容量
                    appCoupon2Group.aplCoupon2RemainCapacity =
                        aplCoupon2RemainCapacity;
                    // アプリクーポン2有効期限
                    appCoupon2Group.aplCoupon2TermValidity =
                        MvnoUtil.addSpaceAfterStr(aplCoupon2TermValidity, 10);
                    group.aplCoupon2Group = appCoupon2Group;
                    // ＊＊＊　アプリクーポン3（グループ）　＊＊＊
                    const appCoupon3Group = getEmpty_AppCoupon3Group();
                    parentPath =
                        "//dyn_grp_info/dyn_node_info_0/dyn_sdf_polinfo_2/dyn_bucket_info";
                    // アプリクーポン3容量
                    const aplCoupon3Capacity = this.sOAPCommon.getNodeContent(
                        document,
                        parentPath + "/dyn_cap_fltr",
                    );
                    // アプリクーポン3使用量
                    const aplCoupon3Use = this.sOAPCommon.getNodeContent(
                        document,
                        parentPath + "/dyn_use_fltr",
                    );
                    // アプリクーポン3残容量
                    let aplCoupon3RemainCapacity = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    // アプリクーポン3有効期限
                    const aplCoupon3TermValidity =
                        this.sOAPCommon.getNodeContent(
                            document,
                            parentPath + "/dyn_exp_date",
                        );
                    if (
                        CheckUtil.checkIsNum(aplCoupon3Capacity) &&
                        CheckUtil.checkIsNum(aplCoupon3Use)
                    ) {
                        const aplCoupon3RemainCapacityCalc =
                            BigInt(aplCoupon3Capacity) - BigInt(aplCoupon3Use);
                        // アプリクーポン3残容量
                        aplCoupon3RemainCapacity = MvnoUtil.addSpaceAfterStr(
                            String(aplCoupon3RemainCapacityCalc),
                            0,
                        );
                    }
                    // アプリクーポン3容量
                    appCoupon3Group.aplCoupon3Capacity =
                        MvnoUtil.addSpaceAfterStr(aplCoupon3Capacity, 0);
                    // アプリクーポン3使用量
                    appCoupon3Group.aplCoupon3Use = MvnoUtil.addSpaceAfterStr(
                        aplCoupon3Use,
                        0,
                    );
                    // アプリクーポン3残容量
                    appCoupon3Group.aplCoupon3RemainCapacity =
                        aplCoupon3RemainCapacity;
                    // アプリクーポン3有効期限
                    appCoupon3Group.aplCoupon3TermValidity =
                        MvnoUtil.addSpaceAfterStr(aplCoupon3TermValidity, 10);
                    group.aplCoupon3Group = appCoupon3Group;
                    // ＊＊＊　アプリクーポン4（グループ）　＊＊＊
                    const appCoupon4Group = getEmpty_AppCoupon4Group();
                    parentPath =
                        "//dyn_grp_info/dyn_node_info_0/dyn_sdf_polinfo_3/dyn_bucket_info";
                    // アプリクーポン4容量
                    const aplCoupon4Capacity = this.sOAPCommon.getNodeContent(
                        document,
                        parentPath + "/dyn_cap_fltr",
                    );
                    // アプリクーポン4使用量
                    const aplCoupon4Use = this.sOAPCommon.getNodeContent(
                        document,
                        parentPath + "/dyn_use_fltr",
                    );
                    // アプリクーポン4残容量
                    let aplCoupon4RemainCapacity = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    // アプリクーポン4有効期限
                    const aplCoupon4TermValidity =
                        this.sOAPCommon.getNodeContent(
                            document,
                            parentPath + "/dyn_exp_date",
                        );
                    if (
                        CheckUtil.checkIsNum(aplCoupon4Capacity) &&
                        CheckUtil.checkIsNum(aplCoupon4Use)
                    ) {
                        const aplCoupon4RemainCapacityCalc =
                            BigInt(aplCoupon4Capacity) - BigInt(aplCoupon4Use);
                        // アプリクーポン4残容量
                        aplCoupon4RemainCapacity = MvnoUtil.addSpaceAfterStr(
                            String(aplCoupon4RemainCapacityCalc),
                            0,
                        );
                    }
                    // アプリクーポン4容量
                    appCoupon4Group.aplCoupon4Capacity =
                        MvnoUtil.addSpaceAfterStr(aplCoupon4Capacity, 0);
                    // アプリクーポン4使用量
                    appCoupon4Group.aplCoupon4Use = MvnoUtil.addSpaceAfterStr(
                        aplCoupon4Use,
                        0,
                    );
                    // アプリクーポン4残容量
                    appCoupon4Group.aplCoupon4RemainCapacity =
                        aplCoupon4RemainCapacity;
                    // アプリクーポン4有効期限
                    appCoupon4Group.aplCoupon4TermValidity =
                        MvnoUtil.addSpaceAfterStr(aplCoupon4TermValidity, 10);
                    group.aplCoupon4Group = appCoupon4Group;
                    // ＊＊＊　アプリクーポン5（グループ）　＊＊＊
                    const appCoupon5Group = getEmpty_AppCoupon5Group();
                    parentPath =
                        "//dyn_grp_info/dyn_node_info_0/dyn_sdf_polinfo_4/dyn_bucket_info";
                    // アプリクーポン5容量
                    const aplCoupon5Capacity = this.sOAPCommon.getNodeContent(
                        document,
                        parentPath + "/dyn_cap_fltr",
                    );
                    // アプリクーポン5使用量
                    const aplCoupon5Use = this.sOAPCommon.getNodeContent(
                        document,
                        parentPath + "/dyn_use_fltr",
                    );
                    // アプリクーポン5残容量
                    let aplCoupon5RemainCapacity = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    // アプリクーポン5有効期限
                    const aplCoupon5TermValidity =
                        this.sOAPCommon.getNodeContent(
                            document,
                            parentPath + "/dyn_exp_date",
                        );
                    if (
                        CheckUtil.checkIsNum(aplCoupon5Capacity) &&
                        CheckUtil.checkIsNum(aplCoupon5Use)
                    ) {
                        const aplCoupon5RemainCapacityCalc =
                            BigInt(aplCoupon5Capacity) - BigInt(aplCoupon5Use);
                        // アプリクーポン5残容量
                        aplCoupon5RemainCapacity = MvnoUtil.addSpaceAfterStr(
                            String(aplCoupon5RemainCapacityCalc),
                            0,
                        );
                    }
                    // アプリクーポン5容量
                    appCoupon5Group.aplCoupon5Capacity =
                        MvnoUtil.addSpaceAfterStr(aplCoupon5Capacity, 0);
                    // アプリクーポン5使用量
                    appCoupon5Group.aplCoupon5Use = MvnoUtil.addSpaceAfterStr(
                        aplCoupon5Use,
                        0,
                    );
                    // アプリクーポン5残容量
                    appCoupon5Group.aplCoupon5RemainCapacity =
                        aplCoupon5RemainCapacity;
                    // アプリクーポン5有効期限
                    appCoupon5Group.aplCoupon5TermValidity =
                        MvnoUtil.addSpaceAfterStr(aplCoupon5TermValidity, 10);
                    group.aplCoupon5Group = appCoupon5Group;
                    // ＊＊＊　アプリクーポン6（グループ）　＊＊＊
                    const appCoupon6Group = getEmpty_AppCoupon6Group();
                    parentPath =
                        "//dyn_grp_info/dyn_node_info_0/dyn_sdf_polinfo_5/dyn_bucket_info";
                    // アプリクーポン6容量
                    const aplCoupon6Capacity = this.sOAPCommon.getNodeContent(
                        document,
                        parentPath + "/dyn_cap_fltr",
                    );
                    // アプリクーポン6使用量
                    const aplCoupon6Use = this.sOAPCommon.getNodeContent(
                        document,
                        parentPath + "/dyn_use_fltr",
                    );
                    // アプリクーポン6残容量
                    let aplCoupon6RemainCapacity = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    // アプリクーポン6有効期限
                    const aplCoupon6TermValidity =
                        this.sOAPCommon.getNodeContent(
                            document,
                            parentPath + "/dyn_exp_date",
                        );
                    if (
                        CheckUtil.checkIsNum(aplCoupon6Capacity) &&
                        CheckUtil.checkIsNum(aplCoupon6Use)
                    ) {
                        const aplCoupon6RemainCapacityCalc =
                            BigInt(aplCoupon6Capacity) - BigInt(aplCoupon6Use);
                        // アプリクーポン6残容量
                        aplCoupon6RemainCapacity = MvnoUtil.addSpaceAfterStr(
                            String(aplCoupon6RemainCapacityCalc),
                            0,
                        );
                    }
                    // アプリクーポン6容量
                    appCoupon6Group.aplCoupon6Capacity =
                        MvnoUtil.addSpaceAfterStr(aplCoupon6Capacity, 0);
                    // アプリクーポン6使用量
                    appCoupon6Group.aplCoupon6Use = MvnoUtil.addSpaceAfterStr(
                        aplCoupon6Use,
                        0,
                    );
                    // アプリクーポン6残容量
                    appCoupon6Group.aplCoupon6RemainCapacity =
                        aplCoupon6RemainCapacity;
                    // アプリクーポン6有効期限
                    appCoupon6Group.aplCoupon6TermValidity =
                        MvnoUtil.addSpaceAfterStr(aplCoupon6TermValidity, 10);
                    group.aplCoupon6Group = appCoupon6Group;
                    // クーポンON/OFF状態（グループ）
                    let couponOnOffGroup = MvnoUtil.addSpaceAfterStr(null, 0);
                    // クーポンON/OFF状態（グループ）
                    if (CONST_SERVICEPATTERN_1.equals(servicePatternGroup)) {
                        couponOnOffGroup = MvnoUtil.addSpaceAfterStr(
                            this.sOAPCommon.getNodeContent(
                                document,
                                "//GroupProfileInformation/svpnum0_info/aply_srv",
                            ),
                            1,
                        );
                    } else if (
                        servicePatternGroup.equals(CONST_SERVICEPATTERN_2)
                    ) {
                        couponOnOffGroup = MvnoUtil.addSpaceAfterStr(
                            this.sOAPCommon.getNodeContent(
                                document,
                                "//GroupProfileInformation/svpnum0_info/optnum0_info/aply_srv",
                            ),
                            1,
                        );
                    } else if (
                        servicePatternGroup.equals(CONST_SERVICEPATTERN_3)
                    ) {
                        couponOnOffGroup = MvnoUtil.addSpaceAfterStr(
                            this.sOAPCommon.getNodeContent(
                                document,
                                "//GroupProfileInformation/svpnum1_info/optnum0_info/aply_srv",
                            ),
                            1,
                        );
                    }
                    group.couponOnOffGroup = couponOnOffGroup;

                    // ヘビーユーザ監視規制状態（グループ）
                    let grpHeavyUserMontorStatus = MvnoUtil.addSpaceAfterStr(
                        null,
                        0,
                    );
                    const grpDynHvyRegSts = MvnoUtil.addSpaceAfterStr(
                        this.sOAPCommon.getNodeContent(
                            document,
                            "//dyn_grp_info/dyn_node_info_0/dyn_reg_info/dyn_hvy_reg_sts",
                        ),
                        1,
                    );
                    if (
                        this.heavyUserMonitorStatusUnregulatedString.equals(
                            grpDynHvyRegSts,
                        )
                    ) {
                        grpHeavyUserMontorStatus = "0";
                    } else if (
                        this.heavyUserMonitorStatusRegulatedatLastString.equals(
                            grpDynHvyRegSts,
                        )
                    ) {
                        grpHeavyUserMontorStatus = "1";
                    } else if (
                        this.heavyUserMonitorStatusRegulatedatMonthlyString.equals(
                            grpDynHvyRegSts,
                        )
                    ) {
                        grpHeavyUserMontorStatus = "2";
                    } else if (
                        this.heavyUserMonitorStatusRegulatedatLastMonthString.equals(
                            grpDynHvyRegSts,
                        )
                    ) {
                        grpHeavyUserMontorStatus = "3";
                    }
                    group.heavyUserMonitorStatusGroup =
                        grpHeavyUserMontorStatus;

                    // ヘビーユーザ監視規制開始時間（月間）（グループ）
                    group.heavyUserMonitorStartTimeMonthGroup =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // ヘビーユーザ監視規制開始時間（直近）（グループ）
                    group.heavyUserMonitorStartTimeLatestGroup =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 適用サービス状態（規制理由）（グループ）
                    group.regulationCauseGroup = this.sOAPCommon.getNodeContent(
                        document,
                        "//dyn_grp_info/dyn_node_info_0/dyn_ses_polinfo/dyn_rule_name",
                    );
                    // 総量規制状態（グループ）
                    let totalVolumeControlStatusGroup =
                        MvnoUtil.addSpaceAfterStr(null, 0);
                    // 総量規制状態（グループ）
                    const dyn_reg_sts = this.sOAPCommon.getNodeContent(
                        document,
                        "//dyn_grp_info/dyn_node_info_0/dyn_reg_info/dyn_reg_sts",
                    );
                    if (this.totalVolumeControlInJudgment.equals(dyn_reg_sts)) {
                        totalVolumeControlStatusGroup = "1";
                    } else if (
                        this.totalVolumeControlOutJudgment.equals(dyn_reg_sts)
                    ) {
                        totalVolumeControlStatusGroup = "0";
                    }
                    // 総量規制状態（グループ）
                    group.totalVolumeControlStatusGroup =
                        totalVolumeControlStatusGroup;
                    // 当日の通信量(クーポンOFF時)（グループ）
                    group.trafficOneDay_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(trafficdayAgoCouponOff[0], 0);
                    // 1日前の通信量(クーポンOFF時)（グループ）
                    group.traffic1dayAgo_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(trafficdayAgoCouponOff[1], 0);
                    // 2日前の通信量(クーポンOFF時)（グループ）
                    group.traffic2daysAgo_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(trafficdayAgoCouponOff[2], 0);
                    // 3日前の通信量(クーポンOFF時)（グループ）
                    group.traffic3daysAgo_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(trafficdayAgoCouponOff[3], 0);
                    // 4日前の通信量(クーポンOFF時)（グループ）
                    group.traffic4daysAgo_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(trafficdayAgoCouponOff[4], 0);
                    // 5日前の通信量(クーポンOFF時)（グループ）
                    group.traffic5daysAgo_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(trafficdayAgoCouponOff[5], 0);
                    // 6日前の通信量(クーポンOFF時)（グループ）
                    group.traffic6daysAgo_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(trafficdayAgoCouponOff[6], 0);
                    // 7日前の通信量(クーポンOFF時)（グループ）
                    group.traffic7daysAgo_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(trafficdayAgoCouponOff[7], 0);
                    // 8日前の通信量(クーポンOFF時)（グループ）
                    group.traffic8daysAgo_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(trafficdayAgoCouponOff[8], 0);
                    // 9日前の通信量(クーポンOFF時)（グループ）
                    group.traffic9daysAgo_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(trafficdayAgoCouponOff[9], 0);
                    // 10日前の通信量(クーポンOFF時)（グループ）
                    group.traffic10daysAgo_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(
                            trafficdayAgoCouponOff[10],
                            0,
                        );
                    // 11日前の通信量(クーポンOFF時)（グループ）
                    group.traffic11daysAgo_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(
                            trafficdayAgoCouponOff[11],
                            0,
                        );
                    // 12日前の通信量(クーポンOFF時)（グループ）
                    group.traffic12daysAgo_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(
                            trafficdayAgoCouponOff[12],
                            0,
                        );
                    // 13日前の通信量(クーポンOFF時)（グループ）
                    group.traffic13daysAgo_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(
                            trafficdayAgoCouponOff[13],
                            0,
                        );
                    // 14日前の通信量(クーポンOFF時)（グループ）
                    group.traffic14daysAgo_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(
                            trafficdayAgoCouponOff[14],
                            0,
                        );
                    // 15日前の通信量(クーポンOFF時)（グループ）
                    group.traffic15daysAgo_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(
                            trafficdayAgoCouponOff[15],
                            0,
                        );
                    // 16日前の通信量(クーポンOFF時)（グループ）
                    group.traffic16daysAgo_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(
                            trafficdayAgoCouponOff[16],
                            0,
                        );
                    // 17日前の通信量(クーポンOFF時)（グループ）
                    group.traffic17daysAgo_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(
                            trafficdayAgoCouponOff[17],
                            0,
                        );
                    // 18日前の通信量(クーポンOFF時)（グループ）
                    group.traffic18daysAgo_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(
                            trafficdayAgoCouponOff[18],
                            0,
                        );
                    // 19日前の通信量(クーポンOFF時)（グループ）
                    group.traffic19daysAgo_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(
                            trafficdayAgoCouponOff[19],
                            0,
                        );
                    // 20日前の通信量(クーポンOFF時)（グループ）
                    group.traffic20daysAgo_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(
                            trafficdayAgoCouponOff[20],
                            0,
                        );
                    // 21日前の通信量(クーポンOFF時)（グループ）
                    group.traffic21daysAgo_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(
                            trafficdayAgoCouponOff[21],
                            0,
                        );
                    // 22日前の通信量(クーポンOFF時)（グループ）
                    group.traffic22daysAgo_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(
                            trafficdayAgoCouponOff[22],
                            0,
                        );
                    // 23日前の通信量(クーポンOFF時)（グループ）
                    group.traffic23daysAgo_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(
                            trafficdayAgoCouponOff[23],
                            0,
                        );
                    // 24日前の通信量(クーポンOFF時)（グループ）
                    group.traffic24daysAgo_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(
                            trafficdayAgoCouponOff[24],
                            0,
                        );
                    // 25日前の通信量(クーポンOFF時)（グループ）
                    group.traffic25daysAgo_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(
                            trafficdayAgoCouponOff[25],
                            0,
                        );
                    // 26日前の通信量(クーポンOFF時)（グループ）
                    group.traffic26daysAgo_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(
                            trafficdayAgoCouponOff[26],
                            0,
                        );
                    // 27日前の通信量(クーポンOFF時)（グループ）
                    group.traffic27daysAgo_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(
                            trafficdayAgoCouponOff[27],
                            0,
                        );
                    // 28日前の通信量(クーポンOFF時)（グループ）
                    group.traffic28daysAgo_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(
                            trafficdayAgoCouponOff[28],
                            0,
                        );
                    // 29日前の通信量(クーポンOFF時)（グループ）
                    group.traffic29daysAgo_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(
                            trafficdayAgoCouponOff[29],
                            0,
                        );
                    // 30日前の通信量(クーポンOFF時)（グループ）
                    group.traffic30daysAgo_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(
                            trafficdayAgoCouponOff[30],
                            0,
                        );
                    // 当月の通信量(クーポンOFF時)（グループ）
                    group.trafficThisMonth_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(
                            this.sOAPCommon.getNodeContent(
                                document,
                                "//dyn_grp_info/dyn_node_info_0/dyn_bucket_info/dyn_hvy_month_info/dyn_use",
                            ),
                            0,
                        );
                    // 前月の通信量(クーポンOFF時)（グループ）
                    group.trafficPreviousMonth_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(
                            String(trafficPreviousMonthCouponOffGroup),
                            0,
                        );
                    // 前々月の通信量(クーポンOFF時)（グループ）
                    group.trafficBeforehandMonth_CouponOffGroup =
                        MvnoUtil.addSpaceAfterStr(
                            String(trafficBeforehandMonthCouponOffGroup),
                            0,
                        );
                }
            } catch (e) {
                // this.context.warn("getReturnPram", e);
                return this.returnEmptyPram(isPrivate, addOption, version);
            }
        }

        // NOTE skip `isPrivate` block
        if ("1".equals(version)) {
            if ("1".equals(addOption)) {
                const returnParam =
                    getEmpty_LineUseAcquisitionAddOpiton1VersionOutput(
                        pram.jsonBody.responseHeader,
                    );
                returnParam.jsonBody = {
                    ...returnParam.jsonBody,
                    ...pram.jsonBody,
                    ...couponOff,
                };
                return returnParam;
            }

            if ("2".equals(addOption)) {
                const returnParam =
                    getEmpty_LineUseAcquisitionAddOpiton2VersionOutput(
                        pram.jsonBody.responseHeader,
                    );
                returnParam.jsonBody = {
                    ...returnParam.jsonBody,
                    ...pram.jsonBody,
                    ...couponOff,
                    ...giftCoupon,
                };
                return returnParam;
            }

            if ("3".equals(addOption)) {
                const returnParam =
                    getEmpty_LineUseAcquisitionAddOpiton3VersionOutput(
                        pram.jsonBody.responseHeader,
                    );
                returnParam.jsonBody = {
                    ...returnParam.jsonBody,
                    ...pram.jsonBody,
                    ...couponOff,
                    ...giftCoupon,
                    ...appCoupon,
                };
                return returnParam;
            }
            if ("4".equals(addOption)) {
                const returnParam =
                    getEmpty_LineUseAcquisitionAddOpiton4VersionOutput(
                        pram.jsonBody.responseHeader,
                    );
                returnParam.jsonBody = {
                    ...returnParam.jsonBody,
                    ...pram.jsonBody,
                    ...appCoupon,
                    ...couponOff,
                    ...group,
                };
                return returnParam;
            }
        } else {
            if ("1".equals(addOption)) {
                const returnParam = getEmpty_LineUseAcquisitionAddOpiton1Output(
                    pram.jsonBody.responseHeader,
                );
                returnParam.jsonBody = {
                    ...returnParam.jsonBody,
                    ...pram.jsonBody,
                    ...couponOff,
                };
                return returnParam;
            }

            if ("2".equals(addOption)) {
                const returnParam = getEmpty_LineUseAcquisitionAddOpiton2Output(
                    pram.jsonBody.responseHeader,
                );
                returnParam.jsonBody = {
                    ...returnParam.jsonBody,
                    ...pram.jsonBody,
                    ...couponOff,
                    ...giftCoupon,
                };
                return returnParam;
            }

            if ("3".equals(addOption)) {
                const returnParam = getEmpty_LineUseAcquisitionAddOpiton3Output(
                    pram.jsonBody.responseHeader,
                );
                returnParam.jsonBody = {
                    ...returnParam.jsonBody,
                    ...pram.jsonBody,
                    ...couponOff,
                    ...giftCoupon,
                    ...appCoupon,
                };
                return returnParam;
            }

            if ("4".equals(addOption)) {
                const returnParam = getEmpty_LineUseAcquisitionAddOpiton4Output(
                    pram.jsonBody.responseHeader,
                );
                returnParam.jsonBody = {
                    ...returnParam.jsonBody,
                    ...pram.jsonBody,
                    ...appCoupon,
                    ...couponOff,
                    ...group,
                };
                return returnParam;
            }
        }

        return this.returnEmptyPram(isPrivate, addOption, version);
    }

    /**
     * 空のDtoを返却する
     * @param isPrivate 内部フラグ
     * @param addOption 追加オプション
     * @param version バージョン
     * @return 返却値Dto
     */
    public returnEmptyPram(
        isPrivate: boolean,
        addOption: string,
        version: string,
    ) {
        // NOTE according to Java source, this will causes empty responseHeader to be returned
        // NOTE changed to public during refactoring

        // NOTE skip `isPrivate` block
        const emptyHeader: ResponseHeader = {
            sequenceNo: "",
            receivedDate: "",
            processCode: "",
            apiProcessID: "",
        };
        if ("1".equals(version)) {
            if ("1".equals(addOption)) {
                return getEmpty_LineUseAcquisitionAddOpiton1VersionOutput(
                    emptyHeader,
                );
            } else if ("2".equals(addOption)) {
                return getEmpty_LineUseAcquisitionAddOpiton2VersionOutput(
                    emptyHeader,
                );
            } else if ("3".equals(addOption)) {
                return getEmpty_LineUseAcquisitionAddOpiton3VersionOutput(
                    emptyHeader,
                );
            } else if ("4".equals(addOption)) {
                return getEmpty_LineUseAcquisitionAddOpiton4VersionOutput(
                    emptyHeader,
                );
            } else {
                return getEmpty_LineUseAcquisitionVersionOutput(emptyHeader);
            }
        } else {
            if ("1".equals(addOption)) {
                return getEmpty_LineUseAcquisitionAddOpiton1Output(emptyHeader);
            } else if ("2".equals(addOption)) {
                return getEmpty_LineUseAcquisitionAddOpiton2Output(emptyHeader);
            } else if ("3".equals(addOption)) {
                return getEmpty_LineUseAcquisitionAddOpiton3Output(emptyHeader);
            } else if ("4".equals(addOption)) {
                return getEmpty_LineUseAcquisitionAddOpiton4Output(emptyHeader);
            } else {
                return getEmpty_LineUseAcquisitionOutput(emptyHeader);
            }
        }
    }
}
