import { Transaction } from "sequelize";

import { isLockNotAvailableError, usePsql } from "@/database/psql";

import Constants from "@/core/constant/Constants";
import MsgKeysConstants from "@/core/constant/MsgKeysConstants";
import ResultCdConstants from "@/core/constant/ResultCdConstants";

import Api<PERSON>ommon from "@/core/common/ApiCommon";
import MvnoUtil from "@/core/common/MvnoUtil";
import RESTCommon from "@/core/common/RESTCommon";
import SOCommon from "@/core/common/SOCommon";
import TenantManage from "@/core/common/TenantManage";

import APILinesDAO from "@/core/dao/APILinesDAO";
import NetworkContractChangeInputDto from "@/core/dto/NetworkContractChangeInputDto";
import NetworkContractChangeOutputDto from "@/core/dto/NetworkContractChangeOutputDto";
import SOObject from "@/core/dto/SOObject";

import CardEntity from "@/core/entity/CardEntity";
import { LinesEntity } from "@/core/entity/LinesEntity";

import { isSQLException, retryQuery } from "@/helpers/queryHelper";

import { IFService } from "../IFService";
import { getStringParameter } from "@/types/parameter.string";

export default class NetworkContractChangeService
    extends RESTCommon
    implements
        IFService<
            NetworkContractChangeInputDto,
            NetworkContractChangeOutputDto
        >
{
    /** 排他制御の最大リトライ回数を規定 */
    private static readonly LOCK_RETRY_COUNT = 5;

    /** 排他制御の待ちミリ秒数を規定 */
    private static readonly LOCK_WAIT_MILLISEC = 1000;

    /**
     * API回線DAO
     */
    // @Autowired
    // private APILinesDAO apiLinesDao;
    private apiLinesDao = new APILinesDAO(this.request, this.context);

    /**
     * SO管理共通DAO
     */
    // @Autowired
    // private SOCommon soCommon;
    private soCommon = new SOCommon(this.request, this.context);

    /**
     * API共通処理
     */
    // @Autowired
    // private ApiCommon apiCommon;
    private apiCommon = new ApiCommon(this.request, this.context);

    /**
     * SQLセッション
     */
    // @Autowired
    // private SessionFactory sessionFactory;

    /**
     * テナント管理クラス
     */
    // @Autowired
    // private TenantManage tenantManage;
    private tenantManage = new TenantManage(this.request, this.context);

    /**
     * CSV出力共通処理
     */
    // @Autowired
    // private RestCsvCommon restCsvCommon;

    async service(
        param: NetworkContractChangeInputDto,
        ...params: any[]
    ): Promise<NetworkContractChangeOutputDto> {
        this.context.log(
            "NetworkContractChangeService.service start",
            JSON.stringify(param),
        );
        // 送信番号取得
        const sequenceNo = param.requestHeader.sequenceNo;
        // 機能種別取得
        const functionType = param.requestHeader.functionType;

        // テナントID取得
        const tenantId = param.tenantId;
        // 対象回線の所属テナントID
        const targetTenantId = param.targetTenantId;
        // 回線ID
        const lineId = param.lineNo;
        // 変更前アクセス方式
        const accessPre = param.access_pre;
        // 変更後アクセス方式
        const access = param.access;
        // 変更後カード種別
        const cardTypeId = param.cardTypeId;
        // MVNO顧客CSV連携不要フラグ
        const csvUnnecessaryFlag = getStringParameter(param.csvUnnecessaryFlag);

        // 変数「処理コード」初期化
        let handleCode = this.context.responseHeader.processCode; // CODE_000000
        // 変数「システム時刻」取得
        const receivedDate = this.context.responseHeader.receivedDate;
        // 変数「API処理ID」
        const apiHandleId = this.context.responseHeader.apiProcessID;
        // 返却レスポンスヘッダ生成
        const responseHeader = this.context.responseHeader;

        // トランザクション管理
        let tx: Transaction = null;
        let commitFlag = false;
        let canRollback = false;

        try {
            // No.1 REST-API電文パラメータ相関チェック
            // ① 卸ポータルフロントからのリクエストかチェック
            if (!Constants.FRONT_API_TENANT_ID.equals(tenantId)) {
                // ログ出力
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APNCCW0001,
                    tenantId,
                );
                handleCode = ResultCdConstants.CODE_600101;
                // SO管理共通
                await this.soManagement(
                    param,
                    apiHandleId,
                    handleCode,
                    receivedDate,
                    functionType,
                );
                // 返却値編集
                const returnParam = this.returnEdit(
                    param,
                    handleCode,
                    apiHandleId,
                    receivedDate,
                );
                // 値の返却
                return returnParam;
            }

            // ② 対象の回線が既に廃止されていないかを確認する
            let aboneLineCheckResult: LinesEntity = null;
            try {
                aboneLineCheckResult =
                    await this.apiLinesDao.getLineInfoByLines(lineId);
            } catch (e) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    handleCode = ResultCdConstants.CODE_600102;
                }
                throw e;
            }
            // 変数「対象回線廃止確認結果」より判定する
            if (
                aboneLineCheckResult === null ||
                "03".equals(aboneLineCheckResult.lineStatus)
            ) {
                // ログ出力
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APNCCW0002,
                    lineId,
                );
                handleCode = ResultCdConstants.CODE_600102;
                // SO管理共通
                await this.soManagement(
                    param,
                    apiHandleId,
                    handleCode,
                    receivedDate,
                    functionType,
                );
                // 返却値編集
                const returnParam = this.returnEdit(
                    param,
                    handleCode,
                    apiHandleId,
                    receivedDate,
                );
                // 値の返却
                return returnParam;
            }

            // ③ 回線IDと対象回線の所属テナントIDの関係をチェックする
            let checkResult: [boolean, string] = [false, ""];
            try {
                checkResult = await this.tenantManage.doCheck(
                    lineId,
                    targetTenantId,
                );
            } catch (e) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    handleCode = ResultCdConstants.CODE_600103;
                }
                throw e;
            }
            // 変数「関係チェック結果」より判定する
            if (!checkResult[0]) {
                // ログ出力
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APNCCW0003,
                    lineId,
                    targetTenantId,
                );
                handleCode = ResultCdConstants.CODE_600103;
                // SO管理共通
                await this.soManagement(
                    param,
                    apiHandleId,
                    handleCode,
                    receivedDate,
                    functionType,
                );
                // 返却値編集
                const returnParam = this.returnEdit(
                    param,
                    handleCode,
                    apiHandleId,
                    receivedDate,
                );
                // 値の返却
                return returnParam;
            }

            // ④ 対象回線の廃止オーダが受付中でないかチェックする
            // const dateFormat = new SimpleDateFormat("yyyyMMdd");
            // String targetDate = dateFormat.format(MvnoUtil.convDateFormat(receivedDate));
            const targetDate = responseHeader.receivedDate
                .substring(0, 10)
                .replace(/\//g, "");
            let checkAbolishSoResult = false;
            try {
                checkAbolishSoResult = await this.apiCommon.checkAbolishSo(
                    lineId,
                    targetDate,
                );
            } catch (e) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    handleCode = ResultCdConstants.CODE_600104;
                }
                throw e;
            }
            // 変数「対象回線廃止オーダチェック結果」より判定する
            if (!checkAbolishSoResult) {
                // ログ出力
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APNCCW0004,
                    lineId,
                );
                handleCode = ResultCdConstants.CODE_600104;
                // SO管理共通
                await this.soManagement(
                    param,
                    apiHandleId,
                    handleCode,
                    receivedDate,
                    functionType,
                );
                // 返却値編集
                const returnParam = this.returnEdit(
                    param,
                    handleCode,
                    apiHandleId,
                    receivedDate,
                );
                // 値の返却
                return returnParam;
            }

            const sequelize = await usePsql();
            /* --------------------------------- リトライ処理開始 --------------------------------- */
            const result = await retryQuery(
                this.context,
                "NetworkContractChangeService.service",
                async () => {
                    if (tx !== null && canRollback) {
                        // try to rollback before continuing
                        try {
                            this.context.log(
                                "NetworkContractChangeService.service rollback",
                            );
                            await tx.rollback();
                        } finally {
                            canRollback = false;
                        }
                    }
                    // トランザクション開始
                    tx = await sequelize.transaction();
                    canRollback = true;
                    // No.2 回線行ロック取得
                    for (
                        let lineLocktime = 0;
                        lineLocktime <
                        NetworkContractChangeService.LOCK_RETRY_COUNT;
                        lineLocktime++
                    ) {
                        try {
                            // 回線情報テーブルから情報を取得する
                            const linesLockInfo =
                                await this.apiLinesDao.getLineInfoforUpdate(
                                    lineId,
                                    tx,
                                );
                            if (linesLockInfo === null) {
                                // レコードが取得できなかった場合、エラー
                                // ログ出力
                                try {
                                    // rollback before returning
                                    await tx.rollback();
                                } finally {
                                    canRollback = false;
                                }
                                super.warn(
                                    tenantId,
                                    sequenceNo,
                                    MsgKeysConstants.APNCCW0005,
                                    lineId,
                                );
                                handleCode = ResultCdConstants.CODE_600201;
                                // SO管理共通
                                await this.soManagement(
                                    param,
                                    apiHandleId,
                                    handleCode,
                                    receivedDate,
                                    functionType,
                                );
                                // 返却値編集
                                const returnParam = this.returnEdit(
                                    param,
                                    handleCode,
                                    apiHandleId,
                                    receivedDate,
                                );
                                // 値の返却
                                return returnParam;
                            } else {
                                // ロックが取得出来たらループから抜ける
                                break;
                            }
                        } catch (error) {
                            if (isLockNotAvailableError(error)) {
                                // ロック取得に失敗した場合 (Pessimistic Lock)
                                if (lineLocktime === 4) {
                                    // タイムアウトまで行ロック取得に失敗した場合
                                    // トランザクションロールバック
                                    await tx.rollback();
                                    canRollback = false;
                                    // ログ出力
                                    super.warn(
                                        tenantId,
                                        sequenceNo,
                                        MsgKeysConstants.APNCCW0005,
                                        lineId,
                                    );
                                    handleCode = ResultCdConstants.CODE_600201;
                                    // SO管理共通
                                    await this.soManagement(
                                        param,
                                        apiHandleId,
                                        handleCode,
                                        receivedDate,
                                        functionType,
                                    );
                                    // 返却値編集
                                    const returnParam = this.returnEdit(
                                        param,
                                        handleCode,
                                        apiHandleId,
                                        receivedDate,
                                    );
                                    // 値の返却
                                    return returnParam;
                                }
                                await tx.rollback();
                                canRollback = false;
                                // 次のロック取得まで待ちミリ秒数分待機
                                await new Promise((resolve) =>
                                    setTimeout(
                                        resolve,
                                        NetworkContractChangeService.LOCK_WAIT_MILLISEC,
                                    ),
                                );
                                // ロールバックし、トランザクション再開
                                // moved rollback to before waiting
                                tx = await sequelize.transaction();
                                canRollback = true;
                            } else if (isSQLException(error)) {
                                // DBアクセスリトライエラーの場合 (not related to lock)
                                handleCode = ResultCdConstants.CODE_600201;
                                throw error;
                            } else {
                                // other exception
                                try {
                                    // try to rollback before returning
                                    await tx.rollback();
                                } finally {
                                    canRollback = false;
                                }
                                // 行ロック取得時にエラーが発生した場合
                                // ログ出力
                                super.warn(
                                    tenantId,
                                    sequenceNo,
                                    MsgKeysConstants.APNCCW0005,
                                    lineId,
                                );
                                handleCode = ResultCdConstants.CODE_600201;
                                // SO管理共通
                                await this.soManagement(
                                    param,
                                    apiHandleId,
                                    handleCode,
                                    receivedDate,
                                    functionType,
                                );
                                // 返却値編集
                                const returnParam = this.returnEdit(
                                    param,
                                    handleCode,
                                    apiHandleId,
                                    receivedDate,
                                );
                                // 値の返却
                                return returnParam;
                            }
                        }
                    }

                    // now that we have the lock, we can update the line
                    // No.3 回線情報の更新
                    // (1) カード種別名の取得
                    let cardInfo: CardEntity = null;
                    try {
                        cardInfo = await this.apiLinesDao.getCardTypeInfo(
                            cardTypeId,
                        );
                    } catch (e) {
                        if (isSQLException(e)) {
                            // DBアクセスリトライエラーの場合
                            handleCode = ResultCdConstants.CODE_600301;
                        }
                        throw e;
                    }
                    if (cardInfo === null) {
                        // ロールバック
                        await tx.rollback();
                        canRollback = false;
                        super.warn(
                            tenantId,
                            sequenceNo,
                            MsgKeysConstants.APNCCW0006,
                            cardTypeId,
                        );
                        handleCode = ResultCdConstants.CODE_600301;
                        // SO管理共通
                        await this.soManagement(
                            param,
                            apiHandleId,
                            handleCode,
                            receivedDate,
                            functionType,
                        );
                        // 返却値編集
                        const returnParam = this.returnEdit(
                            param,
                            handleCode,
                            apiHandleId,
                            receivedDate,
                        );
                        // 値の返却
                        return returnParam;
                    }

                    // (2) 更新用の契約種別に変換
                    let strContractType = "";
                    if ("SA".equals(access)) {
                        strContractType = "3G";
                    } else if ("SB".equals(access)) {
                        strContractType = "3G(SMS)";
                    } else if ("SC".equals(access)) {
                        strContractType = "LTE";
                    } else if ("SD".equals(access)) {
                        strContractType = "LTE(SMS)";
                    } else if ("SE".equals(access)) {
                        strContractType = "LTE(音声)";
                    } else if ("SH".equals(access)) {
                        strContractType = "5G(NSA)";
                    } else if ("SJ".equals(access)) {
                        strContractType = "5G(NSA)(音声)";
                    }

                    // (3) 回線情報の更新
                    try {
                        // 契約種別、端末種別ID、端末種別名、nw_modify_flagを更新
                        await this.apiLinesDao.updateLineContractDeviceType(
                            lineId,
                            strContractType,
                            cardTypeId,
                            cardInfo.deviceTypeName,
                            MvnoUtil.convDateFormat(receivedDate),
                            tx,
                        );
                    } catch (ex: any) {
                        if (isSQLException(ex)) {
                            // DBアクセスリトライエラーの場合
                            handleCode = ResultCdConstants.CODE_600301;
                            throw ex;
                        }
                        // ロールバック
                        await tx.rollback();
                        canRollback = false;
                        super.error(
                            ex,
                            tenantId,
                            sequenceNo,
                            MsgKeysConstants.APNCCE0001,
                            lineId,
                        );
                        handleCode = ResultCdConstants.CODE_600301;
                        // SO管理共通
                        await this.soManagement(
                            param,
                            apiHandleId,
                            handleCode,
                            receivedDate,
                            functionType,
                        );
                        // 返却値編集
                        const returnParam = this.returnEdit(
                            param,
                            handleCode,
                            apiHandleId,
                            receivedDate,
                        );
                        // 値の返却
                        return returnParam;
                    }

                    // トランザクションコミット
                    await tx.commit();
                    commitFlag = true;

                    // リトライしたタイミングでエラーコードを設定しているので
                    // リトライ後に正常となった場合は正常コードに戻す
                    handleCode = ResultCdConstants.CODE_000000;

                    return null;
                },
                this.apDbRetryMaxCnt,
                this.apDbRetryInterval,
            );
            /* --------------------------------- リトライ処理終了 --------------------------------- */
            if (result !== null) {
                // if not null then it is an early return
                this.context.log(
                    "NetworkContractChangeService.service early return",
                );
                return result;
            }

            let csvUnnecessary: boolean;
            if ("1" === csvUnnecessaryFlag) {
                csvUnnecessary = true;
            } else {
                // (2) N番の有無を判定する
                if (checkResult[1] == null || checkResult[1] === "") {
                    super.error(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APNCCE0002,
                        lineId,
                        targetTenantId,
                    );
                    csvUnnecessary = true;
                } else {
                    csvUnnecessary = false;
                }
            }

            // SO管理共通
            await this.soManagement(
                param,
                apiHandleId,
                handleCode,
                receivedDate,
                functionType,
            );
            // 返却値編集
            const returnParam = this.returnEdit(
                param,
                handleCode,
                apiHandleId,
                receivedDate,
                checkResult[1],
                csvUnnecessary,
            );

            return returnParam;
        } catch (e: any) {
            if (tx !== null && !commitFlag && canRollback) {
                // トランザクション開始中にエラーの場合、ロールバック
                await tx.rollback();
            }
            if (isSQLException(e)) {
                // DBアクセスリトライエラーの場合
                // リターンコード値は各エラー時にて設定済み
                super.error(
                    e,
                    param.tenantId,
                    param.requestHeader.sequenceNo,
                    MsgKeysConstants.APCOME0402,
                    functionType,
                );
                return this.returnEdit(
                    param,
                    handleCode,
                    apiHandleId,
                    receivedDate,
                );
            }
            super.error(
                e,
                param.tenantId,
                param.requestHeader.sequenceNo,
                MsgKeysConstants.APCOME0401,
                e,
            );
            handleCode = ResultCdConstants.CODE_999999;
            return this.returnEdit(
                param,
                handleCode,
                apiHandleId,
                receivedDate,
            );
        }
    }

    /**
     * SO管理共通パラメータ設定
     *
     * @param param パラメータ
     * @param resultBean チェック結果
     * @param code オーダステータス
     * @param receivedDate REST API共通処理が呼び出されたシステム日付
     * @param functionType 機能種別
     * @throws Exception 例外
     */
    private async soManagement(
        param: NetworkContractChangeInputDto,
        apiId: string,
        code: string,
        receivedDate: string,
        functionType: string,
    ): Promise<void> {
        // SO管理共通を呼び出す
        const soObject = new SOObject();

        // サービスオーダID
        soObject.setServiceOrderId(apiId);
        // 投入日
        soObject.setOrderDate(receivedDate);
        // 予約日
        soObject.setReserveDate(null);
        // オーダ種別
        soObject.setOrderType(null);
        // 完了日
        soObject.setExecDate(MvnoUtil.getDateTimeNow());
        // オーダステータス
        soObject.setOrderStatus(code);
        // 回線ID
        soObject.setLineId(param.lineNo);
        // 回線グループID
        soObject.setLineGroupId(null);
        // 所属テナントID
        soObject.setTenantId(param.targetTenantId);
        // 実行ユーザID
        soObject.setExecuteUserId(null);
        // 実行テナントID
        soObject.setExecuteTenantId(null);
        // 機能種別
        soObject.setFunctionType(functionType);
        // 操作区分
        soObject.setOperationDivision("");
        // 変更前プランID
        soObject.setChangeOldplanId("");
        // 変更後プランID
        soObject.setChangeNewplanId("");
        // 回線所属変更回線ID
        soObject.setChangePlanId("");
        // 基本容量
        soObject.setBasicCapacity("");
        // オプションプランID
        soObject.setOptionPlanId("");
        // 譲渡先回線番号
        soObject.setDestinationLineNo("");
        // 譲渡データ量
        soObject.setGiftData("");
        // 変更前アクセス方式
        soObject.setChangeOldAccess(param.access_pre);
        // 変更後アクセス方式
        soObject.setChangeNewAccess(param.access);
        // REST電文
        soObject.setRestMessage(null);

        await this.soCommon.soCommon(soObject);
    }

    private returnEdit(
        param: NetworkContractChangeInputDto,
        handleCode: string,
        apiHandleId: string,
        receivedDate: string,
        nNo: string = null,
        csvUnnecessary: boolean = true,
    ): NetworkContractChangeOutputDto {
        const responseHeader = this.context.responseHeader;

        // 送信番号
        responseHeader.sequenceNo = param.requestHeader.sequenceNo;
        // 受信日時
        responseHeader.receivedDate = receivedDate;
        // 処理コード
        responseHeader.processCode = handleCode;
        // API処理ID
        responseHeader.apiProcessID = apiHandleId;

        const networkContractChangeOutputDto: NetworkContractChangeOutputDto = {
            jsonBody: { responseHeader },
            additionalData: {
                nNo: nNo || "",
                csvUnnecessary,
            },
        };

        return networkContractChangeOutputDto;
    }
}
