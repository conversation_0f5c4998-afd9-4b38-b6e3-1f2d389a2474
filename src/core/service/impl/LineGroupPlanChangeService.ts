import config from "config";
import { Transaction } from "sequelize";

import Api<PERSON>ommon from "@/core/common/ApiCommon";
import Check from "@/core/common/Check";
import CheckUtil from "@/core/common/CheckUtil";
import MvnoUtil from "@/core/common/MvnoUtil";
import RESTCommon from "@/core/common/RESTCommon";
import SOAPCommon from "@/core/common/SOAPCommon";
import SOCommon from "@/core/common/SOCommon";
import TenantManage from "@/core/common/TenantManage";
import Constants from "@/core/constant/Constants";
import MsgKeysConstants from "@/core/constant/MsgKeysConstants";
import ResultCdConstants from "@/core/constant/ResultCdConstants";
import APILinesGroupDAO from "@/core/dao/ApiLinesGroupDAO";
import APISoDAO from "@/core/dao/APISoDAO";
import CheckResultBean from "@/core/dto/CheckResultBean";
import LineGroupPlanChangeInputDto from "@/core/dto/LineGroupPlanChangeInputDto";
import LineGroupPlanChangeOutputDto from "@/core/dto/LineGroupPlanChangeOutputDto";
import ParameterName from "@/core/dto/ParameterName";
import SOAPCommonOutputDto from "@/core/dto/SOAPCommonOutputDto";
import SOObject from "@/core/dto/SOObject";
import { isLockNotAvailableError, usePsql } from "@/database/psql";
import { GroupPlansEntity } from "@/core/entity/GroupPlansEntity";
import LineGroupsEntity from "@/core/entity/LineGroupsEntity";
import { TenantsEntity } from "@/core/entity/TenantsEntity";
import TenantGroupPlansEntity from "@/core/entity/TenantGroupPlansEntity";
import { getConfig } from "@/helpers/configHelper";
import { isSQLException, retryQuery } from "@/helpers/queryHelper";
import SOAPException from "@/types/soapException";

import { IFService } from "../IFService";
import { getStringParameter } from "@/types/parameter.string";

/**
 * 回線グループプラン変更機能
 */
export default class LineGroupPlanChangeService
    extends RESTCommon
    implements
        IFService<LineGroupPlanChangeInputDto, LineGroupPlanChangeOutputDto>
{
    /** 排他制御の最大リトライ回数を規定 */
    private readonly LOCK_RETRY_COUNT = 5;

    /** 排他制御の待ちミリ秒数を規定 */
    private readonly LOCK_WAIT_MILLISEC = 1000;

    /**
     * テナント管理クラス
     */
    private tenantManage = new TenantManage(this.request, this.context);

    /**
     * API共通処理
     */
    // private apiCommon = new ApiCommon(this.request, this.context);

    /**
     * SOAP共通
     */
    private soapCommon = new SOAPCommon(this.request, this.context);

    /**
     * SO管理共通DAO
     */
    private soCommon = new SOCommon(this.request, this.context);

    /**
     * API共通DAO (reuse from RESTCommon)
     */
    // private apiCommonDAO = new APICommonDAO(this.request, this.context);

    /**
     * API回線グループDAO
     */
    private apiLinesGroupDAO = new APILinesGroupDAO(this.request, this.context);

    /**
     * APISODAO
     */
    private apiSoDAO = new APISoDAO(this.request, this.context);

    /**
     * SG「グループプラン変更不可時間帯」(予約機能：グループプラン変のみ)
     */
    private groupPlanChangeNgTime = getConfig("GroupPlanChangeNgTime");

    /**
     * SG「予約実行日時単位」(予約機能)
     */
    private reservationDateExecutionUnits: string = getConfig(
        "ReservationDateExecutionUnits",
    );

    /**
     * SG「予約可能制限日数」(予約機能)
     */
    private reservationsLimitDays: string = getConfig("ReservationsLimitDays");

    /**
     * 回線グループプラン変更機能。<BR>
     *
     * <PRE>
     * REST APIにより、受信したパラメータの回線グループプランに変更する。
     * 回線グループプラン変更機能を提供する。
     * </PRE>
     *
     * @param param 回線グループプラン変更機能インプット
     * @param isPrivate 内部呼出フラグ
     * @param params 可変長の引数(内部から呼出す時に使用するパラメータ)
     * @return LineGroupChangeOutputDto 回線グループプラン変更機能アウトプット
     * @throws Exception 例外
     */
    public async service(
        param: LineGroupPlanChangeInputDto,
        isPrivate: boolean,
        ...params: any[]
    ): Promise<LineGroupPlanChangeOutputDto> {
        // 入力電文の内容を取得
        // 送信番号取得
        const sequenceNo = param.requestHeader.sequenceNo;
        // 送信元システムID取得
        // const senderSystemId = param.requestHeader.senderSystemId;
        // API認証キー取得
        // const apiKey = param.requestHeader.apiKey;
        // 機能種別取得
        const functionType = param.requestHeader.functionType;
        // テナントID取得
        const tenantId = param.tenantId;
        // 回線グループID
        const lineGroupId = getStringParameter(param.lineGroupId);
        // 変更前卸ポータルグループプランID
        const potalGroupPlanIdPre = getStringParameter(param.potalGroupPlanID_pre);
        // 変更後卸ポータルグループプランID
        const potalGroupPlanId = getStringParameter(param.potalGroupPlanID);
        // 予約日
        const reserveDate = param.reserve_date;
        // 予約フラグ
        const reserveFlg = param.reserve_flag;
        // ＳＯ－ＩＤ
        const reserveSoId = param.reserve_soId;

        // 変数「処理コード」初期化
        let handleCode = ResultCdConstants.CODE_000000;
        // 変数「システム時刻」取得
        const receivedDate = MvnoUtil.getDateTimeNow();
        // 変数「API処理ID」
        const apiHandleId = this.context.responseHeader.apiProcessID;

        // 変数「オーダ種別」初期化
        const orderType = Check.checkOrderType(
            reserveDate,
            reserveFlg,
            reserveSoId,
        );
        this.context.log("LineGroupPlanChangeService.service orderType:", orderType);

        const sequelize = await usePsql();

        try {
            // get resultBean from context
            const resultBean: CheckResultBean = {
                checkResult: true, // already passed 共通チェック in base handler
                processingCode: this.context.responseHeader.processCode,
                others: this.context.responseHeader.apiProcessID,
                errorMassage: "",
            };

            // No1 REST-API電文パラメータ相関チェック
            // (1) オーダ種別の判定
            if (Constants.ORDER_TYPE_9.equals(orderType)) {
                // ログ出力
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APGPCW0001);
                this.context.log("LineGroupPlanChangeService.service orderType NG:", orderType);
                handleCode = ResultCdConstants.CODE_200101;
                // SO管理共通
                await this.soManagement(
                    param,
                    resultBean,
                    handleCode,
                    receivedDate,
                    functionType,
                    orderType,
                    reserveDate,
                );
                // 返却値編集
                const returnParam = this.returnEdit(
                    param,
                    handleCode,
                    apiHandleId,
                    receivedDate,
                );
                // 値の返却
                return returnParam;
            }

            // (2) REST-API電文パラメータ相関チェック
            // 回線グループIDのフォーマットチェック(必須入力、半角数字9桁以下)
            if (!Check.checkLineGroupId(lineGroupId)) {
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APGPCW0002,
                    "回線グループID",
                    lineGroupId,
                );
                handleCode = ResultCdConstants.CODE_200102;
                // SO管理共通
                await this.soManagement(
                    param,
                    resultBean,
                    handleCode,
                    receivedDate,
                    functionType,
                    orderType,
                    reserveDate,
                );
                // 返却値編集
                const returnParam = this.returnEdit(
                    param,
                    handleCode,
                    apiHandleId,
                    receivedDate,
                );
                // 値の返却
                return returnParam;
            }

            // 変更前卸ポータルグループプランIDのフォーマットチェック(必須入力、半角数字5桁)
            if (!Check.checkPlanIDFmt(potalGroupPlanIdPre)) {
                // ログ出力
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APGPCW0002,
                    "変更前卸ポータルグループプランID",
                    potalGroupPlanIdPre,
                );
                handleCode = ResultCdConstants.CODE_200102;
                // SO管理共通
                await this.soManagement(
                    param,
                    resultBean,
                    handleCode,
                    receivedDate,
                    functionType,
                    orderType,
                    reserveDate,
                );
                // 返却値編集
                const returnParam = this.returnEdit(
                    param,
                    handleCode,
                    apiHandleId,
                    receivedDate,
                );
                // 値の返却
                return returnParam;
            }

            // 変更後卸ポータルグループプランIDのフォーマットチェック(必須入力、半角数字5桁)
            if (!Check.checkPlanIDFmt(potalGroupPlanId)) {
                // ログ出力
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APGPCW0002,
                    "変更後卸ポータルグループプランID",
                    potalGroupPlanId,
                );
                handleCode = ResultCdConstants.CODE_200102;
                // SO管理共通
                await this.soManagement(
                    param,
                    resultBean,
                    handleCode,
                    receivedDate,
                    functionType,
                    orderType,
                    reserveDate,
                );
                // 返却値編集
                const returnParam = this.returnEdit(
                    param,
                    handleCode,
                    apiHandleId,
                    receivedDate,
                );
                // 値の返却
                return returnParam;
            }

            // 変更前、変更後卸ポータルグループプランIDの相関チェック
            if (potalGroupPlanId.equals(potalGroupPlanIdPre)) {
                // ログ出力
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APGPCW0002,
                    "変更前卸ポータルグループプランIDと変更後卸ポータルグループプランIDが一致している",
                    potalGroupPlanIdPre,
                );
                handleCode = ResultCdConstants.CODE_200102;
                // SO管理共通
                await this.soManagement(
                    param,
                    resultBean,
                    handleCode,
                    receivedDate,
                    functionType,
                    orderType,
                    reserveDate,
                );
                // 返却値編集
                const returnParam = this.returnEdit(
                    param,
                    handleCode,
                    apiHandleId,
                    receivedDate,
                );
                // 値の返却
                return returnParam;
            }

            // 予約日フォーマットチェック
            if (
                Constants.ORDER_TYPE_1.equals(orderType) ||
                Constants.ORDER_TYPE_2.equals(orderType)
            ) {
                // 予約前オーダと予約実行オーダの場合
                if (!Check.checkReserveDateFmt(reserveDate)) {
                    // ログ出力
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APGPCW0002,
                        "予約日",
                        reserveDate,
                    );
                    handleCode = ResultCdConstants.CODE_200102;
                    // SO管理共通
                    await this.soManagement(
                        param,
                        resultBean,
                        handleCode,
                        receivedDate,
                        functionType,
                        orderType,
                        reserveDate,
                    );
                    // 返却値編集
                    const returnParam = this.returnEdit(
                        param,
                        handleCode,
                        apiHandleId,
                        receivedDate,
                    );
                    // 値の返却
                    return returnParam;
                }
            }

            // グループプラン変更不可時間帯フォーマットチェック
            if (!Check.checkPlanChangeNgTimeFmt(this.groupPlanChangeNgTime)) {
                // ログ出力
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APGPCW0003,
                    this.groupPlanChangeNgTime,
                );

                if (
                    Constants.ORDER_TYPE_0.equals(orderType) ||
                    Constants.ORDER_TYPE_2.equals(orderType)
                ) {
                    // 変数「処理コード」設定
                    handleCode = ResultCdConstants.CODE_200103;
                } else {
                    // 変数「処理コード」設定
                    handleCode = ResultCdConstants.CODE_200106;
                }
                // SO管理共通
                await this.soManagement(
                    param,
                    resultBean,
                    handleCode,
                    receivedDate,
                    functionType,
                    orderType,
                    reserveDate,
                );
                // 返却値編集
                const returnParam = this.returnEdit(
                    param,
                    handleCode,
                    apiHandleId,
                    receivedDate,
                );
                // 値の返却
                return returnParam;
            }

            // グループプラン変更不可時間帯範囲チェック
            let time: string = null;
            if (
                Constants.ORDER_TYPE_0.equals(orderType) ||
                Constants.ORDER_TYPE_2.equals(orderType)
            ) {
                // システム時刻
                time = receivedDate;
            } else {
                // 予約日
                time = reserveDate;
            }
            if (
                !Check.checkPlanChangeNgTime(time, this.groupPlanChangeNgTime)
            ) {
                // ログ出力
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APGPCW0004,
                    this.groupPlanChangeNgTime,
                );

                if (
                    Constants.ORDER_TYPE_0.equals(orderType) ||
                    Constants.ORDER_TYPE_2.equals(orderType)
                ) {
                    // 変数「処理コード」設定
                    handleCode = ResultCdConstants.CODE_200104;
                } else {
                    // 変数「処理コード」設定
                    handleCode = ResultCdConstants.CODE_200107;
                }
                // SO管理共通
                await this.soManagement(
                    param,
                    resultBean,
                    handleCode,
                    receivedDate,
                    functionType,
                    orderType,
                    reserveDate,
                );
                // 返却値編集
                const returnParam = this.returnEdit(
                    param,
                    handleCode,
                    apiHandleId,
                    receivedDate,
                );
                // 値の返却
                return returnParam;
            }

            // 予約時のチェック
            if (Constants.ORDER_TYPE_1.equals(orderType)) {
                // 予約日と投入日相関チェック
                if (
                    !Check.checkReserveDateOrderDate(receivedDate, reserveDate)
                ) {
                    // ログ出力
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APGPCW0006,
                        reserveDate,
                        receivedDate,
                    );
                    // 変数「処理コード」設定
                    handleCode = ResultCdConstants.CODE_200108;
                    // SO管理共通
                    await this.soManagement(
                        param,
                        resultBean,
                        handleCode,
                        receivedDate,
                        functionType,
                        orderType,
                        reserveDate,
                    );
                    // 返却値編集
                    const returnParam = this.returnEdit(
                        param,
                        handleCode,
                        apiHandleId,
                        receivedDate,
                    );
                    // 値の返却
                    return returnParam;
                }

                // 予約実行日時単位フォーマットチェック
                if (
                    !Check.checkReservationDateExecutionUnitsFmt(
                        this.reservationDateExecutionUnits,
                    )
                ) {
                    // ログ出力
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APGPCW0007,
                        this.reservationDateExecutionUnits,
                    );
                    // 変数「処理コード」設定
                    handleCode = ResultCdConstants.CODE_200109;
                    // SO管理共通
                    await this.soManagement(
                        param,
                        resultBean,
                        handleCode,
                        receivedDate,
                        functionType,
                        orderType,
                        reserveDate,
                    );
                    // 返却値編集
                    const returnParam = this.returnEdit(
                        param,
                        handleCode,
                        apiHandleId,
                        receivedDate,
                    );
                    // 値の返却
                    return returnParam;
                }

                // 予約実行日時単位と予約日相関チェックチェック
                if (
                    !Check.checkReservationDateExecutionUnits(
                        this.reservationDateExecutionUnits,
                        reserveDate,
                    )
                ) {
                    // ログ出力
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APGPCW0008,
                        this.reservationDateExecutionUnits,
                        reserveDate,
                    );
                    // 変数「処理コード」設定
                    handleCode = ResultCdConstants.CODE_200110;
                    // SO管理共通
                    await this.soManagement(
                        param,
                        resultBean,
                        handleCode,
                        receivedDate,
                        functionType,
                        orderType,
                        reserveDate,
                    );
                    // 返却値編集
                    const returnParam = this.returnEdit(
                        param,
                        handleCode,
                        apiHandleId,
                        receivedDate,
                    );
                    // 値の返却
                    return returnParam;
                }

                // 予約可能制限日数フォーマットチェック
                if (
                    !Check.checkReservationsLimitDaysFmt(
                        this.reservationsLimitDays,
                    )
                ) {
                    // ログ出力
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APGPCW0009,
                        this.reservationsLimitDays,
                    );
                    // 変数「処理コード」設定
                    handleCode = ResultCdConstants.CODE_200111;
                    // SO管理共通
                    await this.soManagement(
                        param,
                        resultBean,
                        handleCode,
                        receivedDate,
                        functionType,
                        orderType,
                        reserveDate,
                    );
                    // 返却値編集
                    const returnParam = this.returnEdit(
                        param,
                        handleCode,
                        apiHandleId,
                        receivedDate,
                    );
                    // 値の返却
                    return returnParam;
                }

                // 予約可能制限日数範囲チェック
                if (
                    !Check.checkReservationsLimitDays(
                        this.reservationsLimitDays,
                        reserveDate,
                    )
                ) {
                    // ログ出力
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APGPCW0010,
                        this.reservationsLimitDays,
                        reserveDate,
                    );
                    // 変数「処理コード」設定
                    handleCode = ResultCdConstants.CODE_200112;
                    // SO管理共通
                    await this.soManagement(
                        param,
                        resultBean,
                        handleCode,
                        receivedDate,
                        functionType,
                        orderType,
                        reserveDate,
                    );
                    // 返却値編集
                    const returnParam = this.returnEdit(
                        param,
                        handleCode,
                        apiHandleId,
                        receivedDate,
                    );
                    // 値の返却
                    return returnParam;
                }
            }

            // 予約チェック
            if (
                Constants.ORDER_TYPE_0.equals(orderType) ||
                Constants.ORDER_TYPE_1.equals(orderType)
            ) {
                // 即時オーダ,予約前オーダの場合

                // 予約中オーダがあるかチェックを行う
                const reserveOrderList =
                    await this.apiSoDAO.getReserveOrderGroupId(lineGroupId);
                if (reserveOrderList.length > 0) {
                    this.context.log(
                        "LineGroupPlanChangeService.service reserve order list:",
                        reserveOrderList,
                    );
                    // ログ出力
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APGPCW0005,
                        lineGroupId,
                    );
                    // 変数「処理コード」設定
                    handleCode = ResultCdConstants.CODE_200105;
                    // SO管理共通
                    await this.soManagement(
                        param,
                        resultBean,
                        handleCode,
                        receivedDate,
                        functionType,
                        orderType,
                        reserveDate,
                    );
                    // 返却値編集
                    const returnParam = this.returnEdit(
                        param,
                        handleCode,
                        apiHandleId,
                        receivedDate,
                    );
                    // 値の返却
                    return returnParam;
                }
            }

            // No.2 社内テナント種別取得
            let tenantsEntity: TenantsEntity = null;
            try {
                tenantsEntity = await this.apiCommonDAO.getTenantsEntity(
                    tenantId,
                );
            } catch (e) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    handleCode = ResultCdConstants.CODE_200201;
                }
                throw e;
            }
            if (tenantsEntity == null) {
                // ログ出力
                super.warn(tenantId, sequenceNo, MsgKeysConstants.APGPCW0011);
                // 変数「処理コード」設定
                handleCode = ResultCdConstants.CODE_200201;
                // SO管理共通
                await this.soManagement(
                    param,
                    resultBean,
                    handleCode,
                    receivedDate,
                    functionType,
                    orderType,
                    reserveDate,
                );
                // 返却値編集
                const returnParam = this.returnEdit(
                    param,
                    handleCode,
                    apiHandleId,
                    receivedDate,
                );
                // 値の返却
                return returnParam;
            }
            const tenantType = String(tenantsEntity.tenantType);

            let tx: Transaction = null;
            const result = await retryQuery(
                this.context,
                "LineGroupPlanChangeService.service",
                async () => {
                    try {
                        // トランザクション開始
                        tx = await sequelize.transaction();

                        let lineGroupsEntity: LineGroupsEntity = null; // 回線グループ管理情報

                        // No.3 回線グループIDの行ロック取得
                        if (!"1".equals(tenantType)) {
                            // 社内テナント種別が1(C-OCN)以外の場合

                            if (
                                Constants.ORDER_TYPE_0.equals(orderType) ||
                                Constants.ORDER_TYPE_2.equals(orderType)
                            ) {
                                // 即時オーダ、予約実行オーダの場合

                                // 回線グループID行ロック取得
                                for (
                                    let linelockTime = 0;
                                    linelockTime < this.LOCK_RETRY_COUNT;
                                    linelockTime++
                                ) {
                                    try {
                                        // 回線グループ管理テーブルから情報を取得する
                                        lineGroupsEntity =
                                            await this.apiLinesGroupDAO.getLineGroupsLock(
                                                lineGroupId,
                                                tx,
                                                1,
                                                true,
                                            );
                                        if (lineGroupsEntity === null) {
                                            this.context.log(
                                                "LineGroupPlanChangeService.service line group [lock] not found:",
                                                lineGroupId,
                                            );
                                            // レコードが取得できなかった場合、エラー
                                            // ログ出力
                                            super.warn(
                                                tenantId,
                                                sequenceNo,
                                                MsgKeysConstants.APGPCW0012,
                                                lineGroupId,
                                            );
                                            // 変数「処理コード」設定
                                            handleCode =
                                                ResultCdConstants.CODE_200301;
                                            // SO管理共通
                                            await this.soManagement(
                                                param,
                                                resultBean,
                                                handleCode,
                                                receivedDate,
                                                functionType,
                                                orderType,
                                                reserveDate,
                                            );
                                            // 返却値編集
                                            const returnParam = this.returnEdit(
                                                param,
                                                handleCode,
                                                apiHandleId,
                                                receivedDate,
                                            );
                                            // 値の返却
                                            return returnParam;
                                        } else {
                                            // 行ロックが取得できた場合、ループ処理を終了
                                            break;
                                        }
                                    } catch (e) {
                                        this.context.warn(
                                            "LineGroupPlanChangeService.service error while retrieving line groups [lock=true]",
                                            e,
                                        );
                                        // NOTE put PessimisticLockException first before SQLException
                                        if (isLockNotAvailableError(e)) {
                                            // ロック取得に失敗した場合
                                            if (linelockTime === 4) {
                                                // タイムアウトまで行ロック取得に失敗した場合
                                                // トランザクションロールバック
                                                await tx.rollback();
                                                tx = null;
                                                // ログ出力
                                                super.warn(
                                                    tenantId,
                                                    sequenceNo,
                                                    MsgKeysConstants.APGPCW0012,
                                                    lineGroupId,
                                                );
                                                // 変数「処理コード」設定
                                                handleCode =
                                                    ResultCdConstants.CODE_200301;
                                                // SO管理共通
                                                await this.soManagement(
                                                    param,
                                                    resultBean,
                                                    handleCode,
                                                    receivedDate,
                                                    functionType,
                                                    orderType,
                                                    reserveDate,
                                                );
                                                // 返却値編集
                                                const returnParam =
                                                    this.returnEdit(
                                                        param,
                                                        handleCode,
                                                        apiHandleId,
                                                        receivedDate,
                                                    );
                                                // 値の返却
                                                return returnParam;
                                            }
                                            await tx.rollback(); // NOTE rollback first before sleep
                                            tx = null;
                                            try {
                                                // 次のロック取得まで待ちミリ秒数分待機
                                                await new Promise((resolve) => {
                                                    setTimeout(
                                                        resolve,
                                                        this.LOCK_WAIT_MILLISEC,
                                                    );
                                                });
                                            } catch (ex) {
                                                this.context.warn(
                                                    "LineGroupPlanChangeService.service sleep",
                                                    ex,
                                                );
                                            }
                                            // ロールバックし、トランザクション再開
                                            tx = await sequelize.transaction();
                                            continue;
                                        } else if (isSQLException(e)) {
                                            // DBアクセスリトライエラーの場合
                                            handleCode =
                                                ResultCdConstants.CODE_200301;
                                            throw e;
                                        } else {
                                            // 行ロック取得時にエラーが発生した場合
                                            // ログ出力
                                            super.warn(
                                                tenantId,
                                                sequenceNo,
                                                MsgKeysConstants.APGPCW0012,
                                                lineGroupId,
                                            );
                                            // 変数「処理コード」設定
                                            handleCode =
                                                ResultCdConstants.CODE_200301;
                                            // SO管理共通
                                            await this.soManagement(
                                                param,
                                                resultBean,
                                                handleCode,
                                                receivedDate,
                                                functionType,
                                                orderType,
                                                reserveDate,
                                            );
                                            // 返却値編集
                                            const returnParam = this.returnEdit(
                                                param,
                                                handleCode,
                                                apiHandleId,
                                                receivedDate,
                                            );
                                            // 値の返却
                                            return returnParam;
                                        }
                                    }
                                }
                            } else {
                                // 予約前オーダの場合

                                try {
                                    // 回線グループ管理テーブルから情報を取得する
                                    lineGroupsEntity =
                                        await this.apiLinesGroupDAO.getLineGroupsLock(
                                            lineGroupId,
                                            tx,
                                            1,
                                            false,
                                        );
                                    if (lineGroupsEntity == null) {
                                        // レコードが取得できなかった場合、エラー
                                        // ログ出力
                                        super.warn(
                                            tenantId,
                                            sequenceNo,
                                            MsgKeysConstants.APGPCW0012,
                                            lineGroupId,
                                        );
                                        // 変数「処理コード」設定
                                        handleCode =
                                            ResultCdConstants.CODE_200301;
                                        // SO管理共通
                                        await this.soManagement(
                                            param,
                                            resultBean,
                                            handleCode,
                                            receivedDate,
                                            functionType,
                                            orderType,
                                            reserveDate,
                                        );
                                        // 返却値編集
                                        const returnParam = this.returnEdit(
                                            param,
                                            handleCode,
                                            apiHandleId,
                                            receivedDate,
                                        );
                                        // 値の返却
                                        return returnParam;
                                    }
                                } catch (e) {
                                    this.context.warn(
                                        "LineGroupPlanChangeService.service error while retrieving line groups [lock=false]",
                                        e,
                                    );
                                    if (isSQLException(e)) {
                                        // DBアクセスリトライエラーの場合
                                        handleCode =
                                            ResultCdConstants.CODE_200301;
                                        throw e;
                                    }
                                    // レコード取得時にエラーが発生した場合
                                    // ログ出力
                                    super.warn(
                                        tenantId,
                                        sequenceNo,
                                        MsgKeysConstants.APGPCW0012,
                                        lineGroupId,
                                    );
                                    // 変数「処理コード」設定
                                    handleCode = ResultCdConstants.CODE_200301;
                                    // SO管理共通
                                    await this.soManagement(
                                        param,
                                        resultBean,
                                        handleCode,
                                        receivedDate,
                                        functionType,
                                        orderType,
                                        reserveDate,
                                    );
                                    // 返却値編集
                                    const returnParam = this.returnEdit(
                                        param,
                                        handleCode,
                                        apiHandleId,
                                        receivedDate,
                                    );
                                    // 値の返却
                                    return returnParam;
                                }
                            }

                            // 社内テナント種別が1(C-OCN)以外の場合のみチェック
                            // 回線グループテナント所属チェック（C1）
                            if (lineGroupsEntity.tenantId !== tenantId) {
                                // 電文のテナントIDと回線グループ管理テーブルのテナントIDが一致しない場合

                                // ロールバック
                                await tx.rollback();
                                tx = null;
                                // ログ出力
                                super.warn(
                                    tenantId,
                                    sequenceNo,
                                    MsgKeysConstants.APGPCW0013,
                                    lineGroupId,
                                    tenantId,
                                );
                                // 変数「処理コード」設定
                                handleCode = ResultCdConstants.CODE_200401;
                                // SO管理共通
                                await this.soManagement(
                                    param,
                                    resultBean,
                                    handleCode,
                                    receivedDate,
                                    functionType,
                                    orderType,
                                    reserveDate,
                                );
                                // 返却値編集
                                const returnParam = this.returnEdit(
                                    param,
                                    handleCode,
                                    apiHandleId,
                                    receivedDate,
                                );
                                // 値の返却
                                return returnParam;
                            }

                            // 変更前卸ポータルグループプランIDチェック（C2）
                            if (
                                lineGroupsEntity.planId !==
                                parseInt(potalGroupPlanIdPre, 10)
                            ) {
                                // 電文の変更前卸ポータルプランIDと回線グループ管理テーブルのプランIDが一致しない場合

                                // ロールバック
                                await tx.rollback();
                                tx = null;
                                // ログ出力
                                super.warn(
                                    tenantId,
                                    sequenceNo,
                                    MsgKeysConstants.APGPCW0014,
                                    potalGroupPlanIdPre,
                                    String(lineGroupsEntity.planId),
                                );
                                // 変数「処理コード」設定
                                handleCode = ResultCdConstants.CODE_200501;
                                // SO管理共通
                                await this.soManagement(
                                    param,
                                    resultBean,
                                    handleCode,
                                    receivedDate,
                                    functionType,
                                    orderType,
                                    reserveDate,
                                );
                                // 返却値編集
                                const returnParam = this.returnEdit(
                                    param,
                                    handleCode,
                                    apiHandleId,
                                    receivedDate,
                                );
                                // 値の返却
                                return returnParam;
                            }
                        } // endif 社内テナント種別が1(C-OCN)以外の場合

                        // 以下のチェックは社内テナント種別に関わらずチェックを行う
                        // 変更後卸ポータルグループプランIDチェック（C3）
                        let groupPlansEntity: GroupPlansEntity = null;
                        try {
                            groupPlansEntity =
                                await this.apiLinesGroupDAO.getServicePlanId(
                                    parseInt(potalGroupPlanId, 10),
                                    tx,
                                );
                        } catch (e) {
                            if (isSQLException(e)) {
                                // DBアクセスリトライエラーの場合
                                handleCode = ResultCdConstants.CODE_200601;
                            }
                            throw e;
                        }
                        if (
                            groupPlansEntity === null ||
                            CheckUtil.checkIsNotNull(
                                groupPlansEntity.servicePlanId,
                            )
                        ) {
                            // 取得件数が0件 もしくは、サービスプランIDが未設定の場合

                            // ロールバック
                            await tx.rollback();
                            tx = null;
                            // ログ出力
                            super.warn(
                                tenantId,
                                sequenceNo,
                                MsgKeysConstants.APGPCW0015,
                                potalGroupPlanId,
                            );
                            // 変数「処理コード」設定
                            handleCode = ResultCdConstants.CODE_200601;
                            // SO管理共通
                            await this.soManagement(
                                param,
                                resultBean,
                                handleCode,
                                receivedDate,
                                functionType,
                                orderType,
                                reserveDate,
                            );
                            // 返却値編集
                            const returnParam = this.returnEdit(
                                param,
                                handleCode,
                                apiHandleId,
                                receivedDate,
                            );
                            // 値の返却
                            return returnParam;
                        }

                        // 変更後卸ポータルグループプランIDテナント所属チェック（C4）
                        let tenantGroupPlansEntity: TenantGroupPlansEntity =
                            null;
                        try {
                            tenantGroupPlansEntity =
                                await this.apiLinesGroupDAO.getGroupPlanId(
                                    tenantId,
                                    parseInt(potalGroupPlanId, 10),
                                    tx,
                                );
                        } catch (e) {
                            if (isSQLException(e)) {
                                // DBアクセスリトライエラーの場合
                                handleCode = ResultCdConstants.CODE_200701;
                            }
                            throw e;
                        }
                        if (tenantGroupPlansEntity === null) {
                            // 取得件数が0件の場合

                            // ロールバック
                            await tx.rollback();
                            tx = null;
                            // ログ出力
                            super.warn(
                                tenantId,
                                sequenceNo,
                                MsgKeysConstants.APGPCW0016,
                                potalGroupPlanId,
                                tenantId,
                            );
                            // 変数「処理コード」設定
                            handleCode = ResultCdConstants.CODE_200701;
                            // SO管理共通
                            await this.soManagement(
                                param,
                                resultBean,
                                handleCode,
                                receivedDate,
                                functionType,
                                orderType,
                                reserveDate,
                            );
                            // 返却値編集
                            const returnParam = this.returnEdit(
                                param,
                                handleCode,
                                apiHandleId,
                                receivedDate,
                            );
                            // 値の返却
                            return returnParam;
                        }

                        if (
                            Constants.ORDER_TYPE_0.equals(orderType) ||
                            Constants.ORDER_TYPE_2.equals(orderType)
                        ) {
                            // 即時オーダ もしくは、予約実行オーダ の場合
                            // TPCの連携、テーブルの更新を行う

                            // No.7 接続先TPCの判定
                            let tpcConnectionResults: [
                                boolean,
                                string,
                                string,
                            ] = null;
                            try {
                                tpcConnectionResults =
                                    await this.tenantManage.checkTpcConnection(
                                        tenantId,
                                    );
                            } catch (e) {
                                if (isSQLException(e)) {
                                    // DBアクセスリトライエラーの場合
                                    handleCode = ResultCdConstants.CODE_200801;
                                }
                                throw e;
                            }
                            // 変数「TPC情報取得結果」より判断する
                            if (!tpcConnectionResults[0]) {
                                // TPC接続先が取得できなかった場合

                                // ロールバック
                                await tx.rollback();
                                tx = null;
                                // ログ出力
                                super.warn(
                                    tenantId,
                                    sequenceNo,
                                    MsgKeysConstants.APGPCW0017,
                                    tenantId,
                                );
                                // 変数「処理コード」設定
                                handleCode = ResultCdConstants.CODE_200801;
                                // SO管理共通
                                await this.soManagement(
                                    param,
                                    resultBean,
                                    handleCode,
                                    receivedDate,
                                    functionType,
                                    orderType,
                                    reserveDate,
                                );
                                // 返却値編集
                                const returnParam = this.returnEdit(
                                    param,
                                    handleCode,
                                    apiHandleId,
                                    receivedDate,
                                );
                                // 値の返却
                                return returnParam;
                            }

                            let soapCommonOutputDto: SOAPCommonOutputDto = null;
                            try {
                                soapCommonOutputDto =
                                    await this.sendSoapApiToTpc(
                                        lineGroupId,
                                        groupPlansEntity.servicePlanId,
                                        tenantId,
                                        sequenceNo,
                                        tpcConnectionResults[1],
                                        true,
                                    );
                            } catch (e) {
                                this.context.warn(
                                    "LineGroupPlanChangeService.service error while calling SOAP API",
                                    e,
                                );
                                if (SOAPException.isSOAPException(e)) {
                                    // SOAP送受信でエラーとなった場合

                                    // ロールバック
                                    await tx.rollback();
                                    tx = null;
                                    // ログ出力
                                    super.warn(
                                        tenantId,
                                        sequenceNo,
                                        MsgKeysConstants.APGPCW0018,
                                        e?.message,
                                    );
                                    // 変数「処理コード」設定
                                    handleCode = ResultCdConstants.CODE_200901;
                                    // SO管理共通
                                    await this.soManagement(
                                        param,
                                        resultBean,
                                        handleCode,
                                        receivedDate,
                                        functionType,
                                        orderType,
                                        reserveDate,
                                    );
                                    // 返却値編集
                                    const returnParam = this.returnEdit(
                                        param,
                                        handleCode,
                                        apiHandleId,
                                        receivedDate,
                                    );
                                    // 値の返却
                                    return returnParam;
                                }
                                throw e; // don't catch other exceptions
                            }

                            let document = soapCommonOutputDto.getDoc();
                            if (
                                !ResultCdConstants.CODE_000000.equals(
                                    soapCommonOutputDto.getProcessCode(),
                                )
                            ) {
                                // SOAP送受信の結果が正常以外の場合
                                let errInfo = "";
                                if (document !== null) {
                                    errInfo = this.soapCommon.getNodeContent(
                                        document,
                                        "//ErrorInfomation",
                                    );
                                }
                                // ロールバック
                                await tx.rollback();
                                tx = null;
                                // ログ出力
                                super.warn(
                                    tenantId,
                                    sequenceNo,
                                    MsgKeysConstants.APGPCW0018,
                                    errInfo,
                                );
                                // 変数「処理コード」設定
                                handleCode = ResultCdConstants.CODE_200901;
                                // SO管理共通
                                await this.soManagement(
                                    param,
                                    resultBean,
                                    handleCode,
                                    receivedDate,
                                    functionType,
                                    orderType,
                                    reserveDate,
                                );
                                // 返却値編集
                                const returnParam = this.returnEdit(
                                    param,
                                    handleCode,
                                    apiHandleId,
                                    receivedDate,
                                );
                                // 値の返却
                                return returnParam;
                            }

                            // セカンダリTPCへSOAP連携
                            if (tpcConnectionResults[2] !== null) {
                                // セカンダリTPCが設定されている場合は以下の処理を行う(電文はプライマリと同じものを使用)
                                try {
                                    soapCommonOutputDto =
                                        await this.sendSoapApiToTpc(
                                            lineGroupId,
                                            groupPlansEntity.servicePlanId,
                                            tenantId,
                                            sequenceNo,
                                            tpcConnectionResults[2],
                                            false,
                                        );

                                    if (
                                        ResultCdConstants.CODE_000951.equals(
                                            soapCommonOutputDto.getProcessCode(),
                                        )
                                    ) {
                                        // SG取得でNGの場合
                                        super.warn(
                                            tenantId,
                                            sequenceNo,
                                            MsgKeysConstants.APGPCW0019,
                                            "SG取得NG",
                                        );
                                    } else {
                                        document = soapCommonOutputDto.getDoc();
                                        const resultStr =
                                            this.soapCommon.getNodeContent(
                                                document,
                                                "//Result",
                                            );
                                        if (
                                            soapCommonOutputDto.isError() ||
                                            "NG".equals(resultStr)
                                        ) {
                                            // TPCからのResultがNGの場合
                                            const errInfo =
                                                this.soapCommon.getNodeContent(
                                                    document,
                                                    "//ErrorInfomation",
                                                );
                                            super.warn(
                                                tenantId,
                                                sequenceNo,
                                                MsgKeysConstants.APGPCW0019,
                                                errInfo,
                                            );
                                        }
                                    }
                                } catch (e) {
                                    this.context.warn(
                                        "LineGroupPlanChangeService.service error while calling SOAP API (secondary):",
                                        e,
                                    );
                                    if (SOAPException.isSOAPException(e)) {
                                        // ログ出力
                                        super.warn(
                                            e,
                                            tenantId,
                                            sequenceNo,
                                            MsgKeysConstants.APGPCW0019,
                                            e?.message,
                                        );
                                    } else {
                                        // error other than SOAPException is not caught
                                        throw e;
                                    }
                                }
                            }

                            // No.10 回線グループ管理更新
                            if (!"1".equals(tenantType)) {
                                // 社内テナント種別が1(C-OCN)以外の場合
                                try {
                                    await this.apiLinesGroupDAO.updateLineGroupsPlan(
                                        lineGroupId,
                                        parseInt(potalGroupPlanId, 10),
                                        new Date(),
                                        tx,
                                    );
                                } catch (e) {
                                    this.context.warn(
                                        "LineGroupPlanChangeService.service error while updating line groups:",
                                        e,
                                    );
                                    if (isSQLException(e)) {
                                        // DBアクセスリトライエラーの場合
                                        handleCode =
                                            ResultCdConstants.CODE_201001;
                                        throw e;
                                    }
                                    // 更新でエラーとなった場合
                                    // ロールバック
                                    await tx.rollback();
                                    tx = null;
                                    // ログ出力
                                    super.error(
                                        e,
                                        tenantId,
                                        sequenceNo,
                                        MsgKeysConstants.APGPCE0001,
                                    );
                                    // 変数「処理コード」設定
                                    handleCode = ResultCdConstants.CODE_201001;
                                    // SO管理共通
                                    await this.soManagement(
                                        param,
                                        resultBean,
                                        handleCode,
                                        receivedDate,
                                        functionType,
                                        orderType,
                                        reserveDate,
                                    );
                                    // 返却値編集
                                    const returnParam = this.returnEdit(
                                        param,
                                        handleCode,
                                        apiHandleId,
                                        receivedDate,
                                    );
                                    // 値の返却
                                    return returnParam;
                                }
                            }
                        }

                        // トランザクション終了
                        await tx.commit();
                        tx = null;
                        this.context.log(
                            "LineGroupPlanChangeService.service transaction committed",
                        );

                        // リトライしたタイミングでエラーコードを設定しているので
                        // リトライ後に正常となった場合は正常コードに戻す
                        handleCode = ResultCdConstants.CODE_000000;

                        return null; // if no error, return null
                    } finally {
                        // if tx is not null, then somehow the transaction is not committed/rollbacked
                        if (tx !== null) {
                            this.context.warn(
                                "LineGroupPlanChangeService.service transaction is still open, rolling back...",
                            );
                            try {
                                await tx.rollback();
                                this.context.warn(
                                    "LineGroupPlanChangeService.service rollback successful",
                                );
                            } catch (e) {
                                this.context.error(
                                    "LineGroupPlanChangeService.service error while rolling back transaction",
                                    e,
                                );
                            }
                        }
                    }
                },
                this.apDbRetryMaxCnt,
                this.apDbRetryInterval,
            );

            if (result !== null) {
                // if result is not null, then it is an error
                return result;
            }

            // SO管理共通
            await this.soManagement(
                param,
                resultBean,
                handleCode,
                receivedDate,
                functionType,
                orderType,
                reserveDate,
            );
            // 返却値編集
            const returnParam = this.returnEdit(
                param,
                handleCode,
                apiHandleId,
                receivedDate,
            );

            if (ResultCdConstants.CODE_000000.equals(handleCode)) {
                // 予約前オーダ以外は自動計算回線グループ情報追加処理を行う。
                if (!Constants.ORDER_TYPE_1.equals(orderType)) {
                    await this.addAutoModbucketLineGroup(
                        apiHandleId,
                        functionType,
                        handleCode,
                        null,
                        lineGroupId,
                        tenantId,
                        sequenceNo,
                        orderType,
                    );
                }
            }

            return returnParam;
        } catch (e) {
            if (isSQLException(e)) {
                // DBアクセスリトライエラーの場合
                // リターンコード値は各エラー時にて設定済み
                super.error(
                    e,
                    param.tenantId,
                    param.requestHeader.sequenceNo,
                    MsgKeysConstants.APCOME0402,
                    functionType,
                );
                return this.returnEdit(
                    param,
                    handleCode,
                    apiHandleId,
                    receivedDate,
                );
            } else {
                super.error(
                    e as Error,
                    param.tenantId,
                    param.requestHeader.sequenceNo,
                    MsgKeysConstants.APCOME0401,
                    e?.message,
                );
                handleCode = ResultCdConstants.CODE_999999;
                return this.returnEdit(
                    param,
                    handleCode,
                    apiHandleId,
                    receivedDate,
                );
            }
        }
    }

    /**
     * SO管理共通パラメータ設定
     *
     * @param param パラメータ
     * @param resultBean チェック結果
     * @param code オーダステータス
     * @param receivedDate REST API共通処理が呼び出されたシステム日付
     * @param functionType 機能種別
     * @param orderType オーダ種別
     * @param reserveDate 予約日
     */
    public async soManagement(
        param: LineGroupPlanChangeInputDto,
        resultBean: CheckResultBean,
        code: string,
        receivedDate: string,
        functionType: string,
        orderType: string,
        reserveDate: string,
    ) {
        // SO管理共通を呼び出す
        const soObject = new SOObject();

        // サービスオーダID
        soObject.setServiceOrderId(resultBean.others);
        // 投入日
        soObject.setOrderDate(receivedDate);
        // 予約日
        // 変数「オーダ種別」が「予約前オーダ」の場合
        if (Constants.ORDER_TYPE_1.equals(orderType)) {
            soObject.setReserveDate(reserveDate);
        } else {
            soObject.setReserveDate(null);
        }
        // オーダ種別
        soObject.setOrderType(orderType);
        // 完了日
        // 変数「オーダ種別」が「即時オーダ」、「予約実行オーダ」の場合
        if (
            Constants.ORDER_TYPE_0.equals(orderType) ||
            Constants.ORDER_TYPE_2.equals(orderType)
        ) {
            soObject.setExecDate(MvnoUtil.getDateTimeNow());
        } else {
            soObject.setExecDate(null);
        }
        // オーダステータス
        soObject.setOrderStatus(code);
        // 回線ID
        soObject.setLineId(null);
        // 回線グループID
        soObject.setLineGroupId(getStringParameter(param.lineGroupId));
        // 所属テナントID
        soObject.setTenantId(param.tenantId);
        // 実行ユーザID
        soObject.setExecuteUserId(null);
        // 実行テナントID
        soObject.setExecuteTenantId(null);
        // 機能種別
        soObject.setFunctionType(functionType);
        // 操作区分
        soObject.setOperationDivision(null);
        // 変更前プランID
        soObject.setChangeOldplanId("");
        // 変更後プランID
        soObject.setChangeNewplanId("");
        // 回線所属変更回線ID
        soObject.setChangePlanId("");
        // 基本容量
        soObject.setBasicCapacity("");
        // オプションプランID
        soObject.setOptionPlanId("");
        // 譲渡先回線番号
        soObject.setDestinationLineNo("");
        // 譲渡データ量
        soObject.setGiftData("");
        // REST電文
        // 変数「オーダ種別」が「予約前オーダ」の場合
        if (Constants.ORDER_TYPE_1.equals(orderType)) {
            soObject.setRestMessage(MvnoUtil.getRestMessage(param));
        } else {
            soObject.setRestMessage(null);
        }
        // 変更前卸ポータルグループプランID
        soObject.setChangeOldGroupPlanId(getStringParameter(param.potalGroupPlanID_pre));
        // 変更後卸ポータルグループプランID
        soObject.setChangeNewGroupPlanId(getStringParameter(param.potalGroupPlanID));

        await this.soCommon.soCommon(soObject);
    }

    /**
     * 回線グループプラン変更機能返却値編集
     *
     * @param param 回線グループプラン変更機能インプット
     * @param handleCode 処理コード
     * @param apiHandleId API処理ID
     * @param receivedDate REST API共通処理が呼び出されたシステム日付
     * @return LineDataGiftOutputDto 回線グループプラン変更機能アウトプット
     */
    private returnEdit(
        param: LineGroupPlanChangeInputDto,
        handleCode: string,
        apiHandleId: string,
        receivedDate: string,
    ) {
        // 出力情報を作成する
        const lineGroupChangeOutputDto: LineGroupPlanChangeOutputDto = {
            jsonBody: {
                responseHeader: {
                    sequenceNo: param.requestHeader.sequenceNo,
                    receivedDate,
                    processCode: handleCode,
                    apiProcessID: apiHandleId,
                },
            },
        };
        return lineGroupChangeOutputDto;
    }

    /**
     * SOAP API送信
     *
     * @param lineGroupId グループプランID
     * @param servicePlanId サービスプランID
     * @param tenantId テナントID
     * @param sequenceNo シーケンスNo
     * @param tpcDestInfo TPC情報
     * @param primaryFlg プライマリフラグ
     * @return SOAPCommonOutputDto SOAP応答電文情報
     * @throws Exception
     */
    private async sendSoapApiToTpc(
        lineGroupId: string,
        servicePlanId: string,
        tenantId: string,
        sequenceNo: string,
        tpcDestInfo: string,
        primaryFlg: boolean,
    ) {
        // TPC電文作成
        const paraList: ParameterName[] = [];
        const groupNumber = new ParameterName();
        groupNumber.setName("group_number");
        groupNumber.setValue(lineGroupId);
        const policynumber = new ParameterName();
        policynumber.setName("policy_number");
        policynumber.setValue(servicePlanId);
        paraList.push(groupNumber);
        paraList.push(policynumber);

        // SOAP-API 電文送受信
        return await this.soapCommon.callWebSoapApi(
            "serviceProfileRequestLite",
            "Mod",
            "",
            paraList,
            tenantId,
            sequenceNo,
            tpcDestInfo,
            primaryFlg,
        );
    }
}
