import RESTCommon from "@/core/common/RESTCommon";
import { IFService } from "../IFService";
import SoCancelInputDto from "@/core/dto/SoCancelInputDto";
import SoCancelOutputDto from "@/core/dto/SoCancelOutputDto";
import APILinesGroupDAO from "@/core/dao/ApiLinesGroupDAO";
import APICommonDAO from "@/core/dao/APICommonDAO";
import APISoDAO from "@/core/dao/APISoDAO";
import TenantManage from "@/core/common/TenantManage";
import config from "config";
import APILinesDAO from "@/core/dao/APILinesDAO";
import ResponseHeader from "@/core/dto/ResponseHeader";
import ResultCdConstants from "@/core/constant/ResultCdConstants";
import { isSQLException } from "@/helpers/queryHelper";
import MsgKeysConstants from "@/core/constant/MsgKeysConstants";
import MvnoUtil from "@/core/common/MvnoUtil";
import StringUtils from "@/core/common/StringUtils";
import ServiceOrdersEntity from "@/core/entity/ServiceOrdersEntity";
import Check from "@/core/common/Check";
import { compareAsc, format } from "date-fns";
import TenantGroupPlansEntity from "@/core/entity/TenantGroupPlansEntity";
import LineGroupsEntity from "@/core/entity/LineGroupsEntity";
import "@/types/string.extension";
import { useMongo } from "@/database/mongo";
import CoreSwimmyApiLog from "@/models/coreSwimmyApiLog";
import { getStringParameter } from "@/types/parameter.string";
export default class SoCancelService
    extends RESTCommon
    implements IFService<SoCancelInputDto, SoCancelOutputDto>
{
    private apiSoDao = new APISoDAO(this.request, this.context);

    private apiCommonDao = new APICommonDAO(this.request, this.context);

    private apiLinesGroupDAO = new APILinesGroupDAO(this.request, this.context);

    // @Autowired
    // private ContractDAO contractDAO;

    private tenantManage = new TenantManage(this.request, this.context);

    /**
     * API回線DAO
     */
    private apiLinesDao = new APILinesDAO(this.request, this.context);

    // 予約キャンセル不可期間(予約キャンセル機能)
    private cancelReservationDisableDays: string = config.get<string>(
        "mvno.CancelReservationDisableDays",
    );

    /**
     * SOキャンセル。<BR>
     *
     * <PRE>
     * REST APIにより、受信したパラメータのSOキャンセルを行う。
     * </PRE>
     * @param param SOキャンセルInputDto
     * @param isPrivate 内部呼出フラグ
     * @param obj 可変長引数
     * @return Object 返却オブジェクト
     *
     */
    public async service(
        param: SoCancelInputDto,
        ...obj: any[]
    ): Promise<SoCancelOutputDto> {
        super.debug(
            param.tenantId,
            param.requestHeader.sequenceNo,
            MsgKeysConstants.APSOCD0001,
            this.getClassName(),
            "service",
            JSON.stringify(param),
        );

        /** 送信番号取得 */
        const sequenceNo = param.requestHeader.sequenceNo;
        //  /** 送信元システムID取得 */
        const senderSystemId = param.requestHeader.senderSystemId;

        //  /** API認証キー取得 */
        //  String apiKey = param.getRequestHeader().getApiKey();

        //  /** 機能種別取得 */
        const functionType = param.requestHeader.functionType;

        //  /** テナントID */
        const tenantId = param.tenantId;
        //  /** SO-ID */
        const serviceOrderIdKey = param.serviceOrderIdKey;

        //  /** システム日付 */
        const systemDateTime = this.context.responseHeader.receivedDate;

        const responseHeader = this.context.responseHeader;

        // 変数「MVNO顧客CSV連携不要フラグ」
        const csvUnnecessaryFlag = getStringParameter(param.csvUnnecessaryFlag);

        // 変数「CSV作成要否」
        let csvOutputKbn = "0";

        // 変数「関係チェック結果CSV」
        let checkResultCsv: [boolean, string] = [false, null];

        // 変数「処理コード」初期化
        let handleCode = ResultCdConstants.CODE_000000;

        try {
            let nNo: string = null;

            // REST-API電文パラメータ相関チェック
            // テナントIDが英数字6～10桁であること
            if (
                !MvnoUtil.chkLenRange(tenantId, 6, 10) ||
                !MvnoUtil.chkStrType1(tenantId)
            ) {
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APSOCW0001,
                    serviceOrderIdKey,
                    "テナントID",
                    tenantId,
                );
                responseHeader.processCode = ResultCdConstants.CODE_180101;
                return this.resultEdit(responseHeader);
            }

            // SO-IDが英数字15桁以下であること
            if (
                !MvnoUtil.chkLenRange(serviceOrderIdKey, 1, 15) ||
                !MvnoUtil.chkStrType1(serviceOrderIdKey)
            ) {
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APSOCW0001,
                    serviceOrderIdKey,
                    "SO-ID",
                    serviceOrderIdKey,
                );
                responseHeader.processCode = ResultCdConstants.CODE_180101;
                return this.resultEdit(responseHeader);
            }

            // MVNO顧客CSV連携不要フラグフォーマットチェック
            if (
                StringUtils.isNotEmpty(csvUnnecessaryFlag) &&
                csvUnnecessaryFlag !== "0" &&
                csvUnnecessaryFlag !== "1"
            ) {
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APSOCW0001,
                    serviceOrderIdKey,
                    "MVNO顧客CSV連携不要フラグ",
                    csvUnnecessaryFlag,
                );
                responseHeader.processCode = ResultCdConstants.CODE_180101;
                return this.resultEdit(responseHeader);
            }

            let serviceOrdersEntity: ServiceOrdersEntity = null;

            try {
                serviceOrdersEntity = await this.apiSoDao.getCancelObj(
                    serviceOrderIdKey,
                );
            } catch (e: any) {
                if (isSQLException(e)) {
                    handleCode = ResultCdConstants.CODE_180201;
                }
                throw e;
            }
            if (
                serviceOrdersEntity == null ||
                StringUtils.isEmpty(serviceOrdersEntity.restMessage) ||
                serviceOrdersEntity.orderDate == null ||
                serviceOrdersEntity.reserveDate == null
            ) {
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APSOCW0201,
                    serviceOrderIdKey,
                    tenantId,
                );
                responseHeader.processCode = ResultCdConstants.CODE_180201;
                return this.resultEdit(responseHeader);
            }

            // 取得したREST電文からの情報を取得する。
            const json = serviceOrdersEntity.restMessage;
            const jsonBody: Map<string, string> = new Map(
                Object.entries(json),
            );
            const jsonHeader = new Map(
                Object.entries(jsonBody.get("requestHeader")),
            );
            // 機能種別
            const jsonFunctionType = jsonHeader.get("functionType");
            if (StringUtils.isEmpty(jsonFunctionType)) {
                this.context.log(
                    "SoCancelService: functionType empty",
                    jsonFunctionType,
                );
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APSOCW0202,
                    serviceOrderIdKey,
                    "機能種別",
                );
                responseHeader.processCode = ResultCdConstants.CODE_180202;
                return this.resultEdit(responseHeader);
            }

            // テナントID
            const jsonTenantId = jsonBody.get("tenantId");
            if (StringUtils.isEmpty(jsonTenantId as string)) {
                this.context.log(
                    "SoCancelService: tenantId empty",
                    jsonTenantId,
                );
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APSOCW0202,
                    serviceOrderIdKey,
                    "テナントID",
                );
                responseHeader.processCode = ResultCdConstants.CODE_180202;
                return this.resultEdit(responseHeader);
            }

            // 回線番号
            const jsonLineNo = jsonBody.get("lineNo");
            if (
                ("03" === jsonFunctionType ||
                    "04" === jsonFunctionType ||
                    "05" === jsonFunctionType ||
                    "06" === jsonFunctionType) &&
                StringUtils.isEmpty(jsonLineNo)
            ) {
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APSOCW0202,
                    serviceOrderIdKey,
                    "回線番号",
                );
                responseHeader.processCode = ResultCdConstants.CODE_180202;
                return this.resultEdit(responseHeader);
            }

            // 予約日
            const jsonReserve_date = jsonBody.get("reserve_date");
            if (StringUtils.isEmpty(jsonReserve_date as string)) {
                this.context.log(
                    "SoCancelService: reserve_date empty",
                    jsonReserve_date,
                );
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APSOCW0202,
                    serviceOrderIdKey,
                    "予約日",
                );
                responseHeader.processCode = ResultCdConstants.CODE_180202;
                return this.resultEdit(responseHeader);
            }

            // 取得したREST電文の機能種別が、10,12の場合、卸ポータルグループプランIDを取得する。
            let jsonPotalGroupPlanID = "";
            if ("10" === jsonFunctionType || "12" === jsonFunctionType) {
                // 卸ポータルグループプランID
                jsonPotalGroupPlanID = jsonBody.get("potalGroupPlanID");
                if (StringUtils.isEmpty(jsonPotalGroupPlanID)) {
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APSOCW0203,
                        serviceOrderIdKey,
                        "卸ポータルグループプランID",
                    );
                    responseHeader.processCode = ResultCdConstants.CODE_180203;
                    return this.resultEdit(responseHeader);
                }
            }

            // 取得したREST電文の機能種別が、14,15,20の場合、回線グループIDを取得する。
            let jsonLineGroupId = "";
            if (
                "14" === jsonFunctionType ||
                "15" === jsonFunctionType ||
                "20" === jsonFunctionType
            ) {
                // 回線グループID
                jsonLineGroupId = jsonBody.get("lineGroupId");
                if (StringUtils.isEmpty(jsonLineGroupId)) {
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APSOCW0204,
                        serviceOrderIdKey,
                        "回線グループID",
                    );
                    responseHeader.processCode = ResultCdConstants.CODE_180204;
                    return this.resultEdit(responseHeader);
                }
            }

            // SG(システムプロパティ)「キャンセル不可期間」フォーマット判定を行う。
            if (
                !Check.checkCancelReservationDisableDaysFmt(
                    this.cancelReservationDisableDays,
                )
            ) {
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APSOCW0301,
                    this.cancelReservationDisableDays,
                );
                responseHeader.processCode = ResultCdConstants.CODE_180301;
                return this.resultEdit(responseHeader);
            }

            // SG(システムプロパティ)「キャンセル不可期間」判定を行う。
            // 予約日
            const reserveDate = format(
                serviceOrdersEntity.reserveDate,
                "yyyy/MM/dd HH:mm:ss",
            );
            if (
                !Check.checkCancelReservationDisableDays(
                    this.cancelReservationDisableDays,
                    reserveDate,
                    systemDateTime,
                )
            ) {
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APSOCW0302,
                    serviceOrderIdKey,
                    this.cancelReservationDisableDays,
                    systemDateTime,
                );
                responseHeader.processCode = ResultCdConstants.CODE_180302;
                return this.resultEdit(responseHeader);
            }

            // 予約キャンセルの実行日時が、SO管理の予約日を過ぎていないか判定を行う。
            const sysDate = new Date();
            if (compareAsc(sysDate, serviceOrdersEntity.reserveDate) > 0) {
                super.warn(
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APSOCW0401,
                    serviceOrderIdKey,
                    sysDate,
                    reserveDate,
                );
                responseHeader.processCode = ResultCdConstants.CODE_180401;
                return this.resultEdit(responseHeader);
            }

            // 「テナントID」判定
            // 機能種別が3,4,5,6のいずれかでREST APIからのキャンセルの場合
            if (
                "04" === jsonFunctionType ||
                "05" === jsonFunctionType ||
                "06" === jsonFunctionType
            ) {
                let checkResult: [boolean, string] = [false, null];
                try {
                    checkResult = await this.tenantManage.doCheck(
                        jsonLineNo,
                        tenantId,
                    );
                } catch (e: any) {
                    if (isSQLException(e)) {
                        // DBアクセスリトライエラーの場合
                        handleCode = ResultCdConstants.CODE_180501;
                    }
                    throw e;
                }

                if (!checkResult[0]) {
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APSOCW0501,
                        serviceOrderIdKey,
                        jsonFunctionType,
                        jsonLineNo,
                    );
                    responseHeader.processCode = ResultCdConstants.CODE_180501;
                    return this.resultEdit(responseHeader);
                }

                nNo = checkResult[1];
            }

            // 機能種別が10,12のいずれかでREST APIからのキャンセルの場合
            if ("10" === jsonFunctionType || "12" === jsonFunctionType) {
                try {
                    const tenantGroupPlansEntity: TenantGroupPlansEntity =
                        await this.apiCommonDao.getTenantGroupPlans(
                            jsonPotalGroupPlanID,
                            jsonTenantId,
                        );
                    if (
                        tenantGroupPlansEntity == null ||
                        tenantGroupPlansEntity.tenantId !== tenantId
                    ) {
                        super.warn(
                            tenantId,
                            sequenceNo,
                            MsgKeysConstants.APSOCW0503,
                            serviceOrderIdKey,
                            jsonFunctionType,
                            jsonPotalGroupPlanID,
                        );
                        responseHeader.processCode =
                            ResultCdConstants.CODE_180503;
                        return this.resultEdit(responseHeader);
                    }
                } catch (e) {
                    if (isSQLException(e)) {
                        // DBアクセスリトライエラーの場合
                        handleCode = ResultCdConstants.CODE_180503;
                        throw e;
                    }
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APSOCW0503,
                        serviceOrderIdKey,
                        jsonFunctionType,
                        jsonPotalGroupPlanID,
                    );
                    responseHeader.processCode = ResultCdConstants.CODE_180503;
                    return this.resultEdit(responseHeader);
                }
            }

            // 機能種別が14,15,20のいずれかでREST APIからのキャンセルの場合
            if (
                "14" === jsonFunctionType ||
                "15" === jsonFunctionType ||
                "20" === jsonFunctionType
            ) {
                let lineGroupsEntity: LineGroupsEntity = null;
                try {
                    lineGroupsEntity =
                        await this.apiLinesGroupDAO.getLineGroupsInfo(
                            jsonLineGroupId,
                        );
                } catch (e: any) {
                    if (isSQLException(e)) {
                        // DBアクセスリトライエラーの場合
                        handleCode = ResultCdConstants.CODE_180505;
                    }
                    throw e;
                }

                if (
                    lineGroupsEntity === null ||
                    !tenantId.equals(lineGroupsEntity.tenantId) ||
                    lineGroupsEntity.status !== 1 ||
                    lineGroupsEntity.planId === null
                ) {
                    super.warn(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APSOCW0505,
                        serviceOrderIdKey,
                        jsonFunctionType,
                        jsonLineGroupId,
                    );
                    responseHeader.processCode = ResultCdConstants.CODE_180505;
                    return this.resultEdit(responseHeader);
                }
            }

            // 返却先の共通処理では、SO管理に以下の格納を行う。
            try {
                await this.apiSoDao.updServiceOrders(
                    null,
                    "キャンセル済み",
                    serviceOrderIdKey,
                    null,
                );
            } catch (e) {
                if (isSQLException(e)) {
                    // DBアクセスリトライエラーの場合
                    handleCode = ResultCdConstants.CODE_999999;
                }
                throw e;
            }

            // 予約キャンセルできる対象かどうかを判断する。
            if (
                "06" === serviceOrdersEntity.functionType ||
                "14" === serviceOrdersEntity.functionType
            ) {
                // 変数「関係チェック結果CSV」
                try {
                    checkResultCsv = await this.tenantManage.doCheck(
                        jsonLineNo,
                        jsonTenantId,
                    );
                } catch (e) {
                    if (isSQLException(e)) {
                        // DBアクセスリトライエラーの場合
                        super.error(
                            e,
                            tenantId,
                            sequenceNo,
                            MsgKeysConstants.APCOME0402,
                            functionType,
                        );
                        checkResultCsv[0] = false;
                    } else {
                        throw e;
                    }
                }

                if (
                    !checkResultCsv[0] ||
                    StringUtils.isEmpty(checkResultCsv[1])
                ) {
                    // 変数「CSV作成要否」を0で処理する
                    csvOutputKbn = "0";
                    super.error(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APSOCE0602,
                        jsonLineNo,
                        jsonTenantId,
                    );
                    responseHeader.processCode = ResultCdConstants.CODE_000000;
                    return this.resultEdit(responseHeader);
                }

                // 変数「社内フラグ」取得
                let officeLst: boolean[] = null;
                try {
                    officeLst = await this.apiLinesDao.getOffice(jsonTenantId);
                } catch (e: any) {
                    if (isSQLException(e)) {
                        officeLst = null;
                        // DBアクセスリトライエラーの場合
                        super.error(
                            e,
                            tenantId,
                            sequenceNo,
                            MsgKeysConstants.APCOME0402,
                            functionType,
                        );
                    } else {
                        throw e;
                    }
                }

                // 変数「社内フラグ」の件数０件場合
                if (officeLst === null || officeLst.length === 0) {
                    // 変数「CSV作成要否」を0で処理する
                    csvOutputKbn = "0";
                    super.error(
                        tenantId,
                        sequenceNo,
                        MsgKeysConstants.APSOCE0603,
                        jsonTenantId,
                    );
                    responseHeader.processCode = ResultCdConstants.CODE_000000;
                    return this.resultEdit(responseHeader);

                    // 変数「社内フラグ」がtrueの場合,かつ入力パラメータ．「MVNO顧客CSV連携不要フラグ」＝1の場合
                } else if (officeLst[0] && "1" === csvUnnecessaryFlag) {
                    // 変数「CSV作成要否」を0で処理する
                    csvOutputKbn = "0";

                    // 機能種別が14(回線グループ所属回線変更)ならば帯域卸の判定を行わない
                } else if (serviceOrdersEntity.functionType !== "14") {
                    // 変数「帯域卸フラグ」取得
                    let wholeFlagLst: boolean[] = null;
                    try {
                        wholeFlagLst = await this.apiLinesDao.getWholeFlag(
                            jsonTenantId,
                            checkResultCsv[1],
                        );
                    } catch (e: any) {
                        if (isSQLException(e)) {
                            // DBアクセスリトライエラーの場合
                            wholeFlagLst = null;
                            super.error(
                                e,
                                tenantId,
                                sequenceNo,
                                MsgKeysConstants.APCOME0402,
                                functionType,
                            );
                        } else {
                            throw e;
                        }
                    }

                    // 変数「帯域卸フラグ」の件数が0件あるいは変数「帯域卸フラグ」がnullあるいは変数「帯域卸フラグ」がTRUE以外の場合：
                    if (
                        wholeFlagLst === null ||
                        wholeFlagLst.length === 0 ||
                        wholeFlagLst[0] === null ||
                        !wholeFlagLst[0]
                    ) {
                        if ("9a1z" === senderSystemId && officeLst[0]) {
                            // 変数「CSV作成要否」を0で処理する
                            csvOutputKbn = "0";
                        } else {
                            // 変数「CSV作成要否」を1で処理する
                            csvOutputKbn = "1";
                        }
                    } else {
                        // 変数「CSV作成要否」を0で処理する
                        csvOutputKbn = "0";
                    }
                } else {
                    if ("9a1z" === senderSystemId && officeLst[0]) {
                        // 変数「CSV作成要否」を0で処理する
                        csvOutputKbn = "0";
                    } else {
                        // 変数「CSV作成要否」を1で処理する
                        csvOutputKbn = "1";
                    }
                }
            } else {
                // 変数「CSV作成要否」を0で処理する
                csvOutputKbn = "0";
            }
            // 変数「CSV作成要否」＝“1” の場合
            if (!"1".equals(csvOutputKbn)) {
                // 変数「CSV作成要否」を0で処理する
                csvOutputKbn = "0";
            }

            super.debug(
                tenantId,
                sequenceNo,
                MsgKeysConstants.APSOCD0002,
                this.getClassName(),
                "service",
            );
            responseHeader.processCode = ResultCdConstants.CODE_000000;
            let receiptId = "";
            if (csvOutputKbn === "1") {
                await useMongo(this.context);
                const swimmyApiLog = await CoreSwimmyApiLog.findOne({
                    requestOrderId: serviceOrderIdKey,
                }).exec();
                if (swimmyApiLog) {
                    receiptId = this.getReceiptId(
                        swimmyApiLog.responseParam,
                    );
                } else {
                    this.context.error(
                        "SoCancelService: swimmyApiLog not found for serviceOrderIdKey: " +
                            serviceOrderIdKey,
                    );
                }
                if (!receiptId) {
                    this.context.error(
                        "SoCancelService: receiptId not found for serviceOrderIdKey: " +
                            serviceOrderIdKey,
                    );
                }
            }
            return this.resultEdit(
                responseHeader,
                nNo,
                csvOutputKbn,
                jsonLineNo,
                receiptId
            );
        } catch (e) {
            if (isSQLException(e)) {
                super.error(
                    e,
                    tenantId,
                    sequenceNo,
                    MsgKeysConstants.APCOME0402,
                    functionType,
                );
                responseHeader.processCode = handleCode;
                return this.resultEdit(responseHeader);
            }

            super.error(
                e,
                tenantId,
                sequenceNo,
                MsgKeysConstants.APCOME0401,
                e,
            );
            responseHeader.processCode = ResultCdConstants.CODE_999999;
            return this.resultEdit(responseHeader);
        }
    }

    /**
     * 返却オブジェクト編集
     *
     * @param responseHeader
     * @param isPrivate
     * @param errorMessage
     * @param ok
     * @return
     */
    private resultEdit(
        responseHeader: ResponseHeader,
        nNo: string = null,
        csvOutputKbn: string = "0",
        lineNo: string = null,
        receiptId: string = null,
    ): SoCancelOutputDto {
        return {
            jsonBody: {
                responseHeader,
            },
            additionalData: {
                csvOutputKbn,
                nNo,
                csvUnnecessary: csvOutputKbn === "0",
                lineNo,
                receiptId
            },
        };
    }

    /**
     * キャンセル対象の回線廃止オーダ時のレスポンスにある「receiptId」を取得する
     * @param responseParam 回線廃止オーダのレスポンスパラメータ
     * @returns receiptId
     */
    private getReceiptId(responseParam: any): string {
        if (
            responseParam === null ||
            responseParam === undefined ||
            typeof responseParam !== "object"
        ) {
            this.context.log(
                "getReceiptId: responseParam empty or not object",
                responseParam,
            );
            return null;
        }
        if (
            responseParam?.appResponseInfo?.appInfoList === undefined ||
            !Array.isArray(responseParam.appResponseInfo.appInfoList)
        ) {
            this.context.log("getReceiptId: appInfoList not array");
            return null;
        }
        if (responseParam.appResponseInfo.appInfoList.length === 0) {
            this.context.log("getReceiptId: appInfoList empty");
            return null;
        }
        return (
            responseParam.appResponseInfo.appInfoList[0]?.appBasic?.receiptId ||
            null
        );
    }
}