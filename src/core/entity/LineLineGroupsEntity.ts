import { InferAttributes, InferCreationAttributes } from "sequelize";
import { Table, Column, Model, Index, DataType, BelongsTo } from "sequelize-typescript";
import { LinesEntity } from "@/core/entity/LinesEntity";
import LineGroupsEntity from "@/core/entity/LineGroupsEntity";

@Table({
    tableName: "line_line_groups",
})
export default class LineLineGroupsEntity extends Model<
    InferAttributes<LineLineGroupsEntity>,
    InferCreationAttributes<LineLineGroupsEntity>
> {
    /** 回線ID */
    // @Id
    // @Column(name = "line_id")
    // private String lineId = null;
    @Index
    @Column({
        allowNull: false,
        type: DataType.STRING(),
        primaryKey: true,
        field: "line_id",
    })
    lineId: string;

    /** 回線グループID */
    // @Column(name = "group_id")
    // private String groupId = null;
    @Index
    @Column({
        allowNull: false,
        type: DataType.STRING(),
        primaryKey: true,
        field: "group_id",
    })
    groupId: string;

    /** 基本容量 */
    // @Column(name = "basic_capacity")
    // private Integer basicCapacity;
    @Column({
        type: DataType.INTEGER(),
        field: "basic_capacity",
    })
    basicCapacity: number;

    /** 利用状態 */
    // @Column(name = "usage_status")
    // private String usageStatus = null;
    @Column({
        type: DataType.STRING(),
        field: "usage_status",
    })
    usageStatus: string;

    @BelongsTo(() => LinesEntity, {
        foreignKey: "line_id",
        targetKey: "lineId",
        onDelete: "CASCADE",
    })
    linesEntity: LinesEntity;

    @BelongsTo(() => LineGroupsEntity, {
        foreignKey: "group_id",
        targetKey: "groupId",
        onDelete: "CASCADE",
    })
    lineGroupsEntity: LineGroupsEntity;
}
