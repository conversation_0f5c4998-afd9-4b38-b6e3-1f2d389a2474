import { Column, DataType, Model, Table } from "sequelize-typescript";
import { InferAttributes, InferCreationAttributes } from "sequelize";

@Table({
    tableName: "group_option_plan_parameters",
})

/**
 * グループオプションプランパラメータテーブルエンティティ。<BR>
 * <PRE>
 * グループオプションプランパラメータのテーブル対応のエンティティである。
 * </PRE>
 * <AUTHOR> Tany
 * @version 1.0 新規<BR>
 */
export class GroupOptionPlanParametersEntity extends Model<
    InferAttributes<GroupOptionPlanParametersEntity>,
    InferCreationAttributes<GroupOptionPlanParametersEntity>
> {
    /** オプションプランID */
    // @Id
    // @Column(name = "option_plan_id")
    // private int optionPlanId;
    @Column({
        type: DataType.INTEGER(),
        primaryKey: true,
        allowNull: false,
        field: "option_plan_id"
    })
    optionPlanId: number;

    /** パラメータキー */
    // @Id
    // @Column(name = "key")
    // private String key = null;
    @Column({
        type: DataType.STRING(),
        field: "key",
    })
    key: string

    /** パラメータ値 */
    // @Column(name = "value")
    // private String value = null;
    @Column({
        type: DataType.STRING(),
        field: "value",
    })
    value: string
}