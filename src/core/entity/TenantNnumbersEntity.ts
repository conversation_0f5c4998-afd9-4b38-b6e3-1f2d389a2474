import { InferAttributes, InferCreationAttributes } from "sequelize";
import { Table, Column, Model, Index, DataType } from "sequelize-typescript";

@Table({
    tableName: "tenant_nnumbers",
})
export class TenantNnumbersEntity extends Model<
    InferAttributes<TenantNnumbersEntity>,
    InferCreationAttributes<TenantNnumbersEntity>
> {
    /** N番 */
    // @Id
    // @Column(name = "nnumber")
    // private String nnumber = null;
    @Index
    @Column({
        allowNull: false,
        type: DataType.STRING(),
        field: "nnumber",
        primaryKey: true,
    })
    nnumber: string;

    /** テナントID */
    // @Id
    // @Column(name = "tenant_id")
    // private String tenantId = null;
    @Column({
        allowNull: false,
        type: DataType.STRING(),
        field: "tenant_id",
        primaryKey: true,
    })
    tenantId: string;

    /** 帯域卸フラグ */
    // @Id
    // @Column(name = "whole_flag" ,nullable = true)
    // private Boolean wholeFlag = false;
    @Column({
        allowNull: true,
        type: DataType.BOOLEAN(),
        field: "whole_flag",
        primaryKey: true,
    })
    wholeFlag: boolean;

    /** フルMVNOフラグ */
    // @Column(name = "full_mvno_flag")
    // private Boolean fullMvnoFlag;
    @Column({
        type: DataType.BOOLEAN(),
        field: "full_mvno_flag",
    })
    fullMvnoFlag: boolean;
}
