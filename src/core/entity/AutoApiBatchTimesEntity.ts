import { InferAttributes, InferCreationAttributes } from "sequelize";
import { Table, Column, Model, Index, DataType } from "sequelize-typescript";

@Table({
    tableName: "auto_api_batch_times",
})
export default class AutoApiBatchTimesEntity extends Model<
    InferAttributes<AutoApiBatchTimesEntity>,
    InferCreationAttributes<AutoApiBatchTimesEntity>
> {
    @Index
    @Column({
        type: DataType.DATE(),
        field: "pre_exec_time",
        primaryKey: true,
    })
    preExecTime: Date;

    @Index
    @Column({
        type: DataType.DATE(),
        field: "exec_time",
        primaryKey: true,
    })
    execTime: Date;
}