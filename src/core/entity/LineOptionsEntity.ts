import { InferAttributes, InferCreationAttributes } from "sequelize";
import { Table, Column, Model, Index, DataType } from "sequelize-typescript";

@Table({
    tableName: "line_options",
})
export default class LineOptionsEntity extends Model<
    InferAttributes<LineOptionsEntity>,
    InferCreationAttributes<LineOptionsEntity>
> {
    /** 回線オプションID */
    // @Id
    // @Column(name = "line_option_id")
    // private String lineOptionId = null;
    @Index
    @Column({
        type: DataType.STRING(),
        primaryKey: true,
        allowNull: false,
        field: "line_option_id",
    })
    lineOptionId: string;

    /** 回線オプション分類 */
    // @Column(name = "line_option_type")
    // private int lineOptionType;
    @Column({
        type: DataType.INTEGER(),
        field: "line_option_type",
    })
    lineOptionType: number;

    /** 回線オプション名 */
    // @Column(name = "option_plan_name")
    // private String optionPlanName = null;
    @Column({
        type: DataType.STRING(),
        field: "option_plan_name",
    })
    optionPlanName: string;

    /** C-OCN */
    // @Column(name = "c_ocn")
    // private Boolean cOcn = false;
    @Column({
        type: DataType.BOOLEAN(),
        field: "c_ocn",
    })
    cOcn: boolean;

    /** B-OCN */
    // @Column(name = "b_ocn")
    // private Boolean bOcn = false;
    @Column({
        type: DataType.BOOLEAN(),
        field: "b_ocn",
    })
    bOcn: boolean;

    /** UNOモバイル */
    // @Column(name = "uno")
    // private Boolean uno = false;
    @Column({
        type: DataType.BOOLEAN(),
        field: "uno",
    })
    uno: boolean;

    /** 帯域卸 */
    // @Column(name = "whole")
    // private Boolean whole = false;
    @Column({
        type: DataType.BOOLEAN(),
        field: "whole",
    })
    whole: boolean;

    /** ID卸 */
    // @Column(name = "id")
    // private Boolean id = false;
    @Column({
        type: DataType.BOOLEAN(),
        field: "id",
    })
    id: boolean;

    // ----- new fields -----
    /** 回線オプションID（T番） */
    @Column({
        type: DataType.STRING(),
        field: "line_option_id_t",
        // allowNull: false,
    })
    lineOptionIdT: string;
}
