import { InferAttributes, InferCreationAttributes } from "sequelize";
import { Table, Column, Model, Index, DataType } from "sequelize-typescript";

@Table({
    tableName: "line_groups",
})
export default class LineGroupsEntity extends Model<
    InferAttributes<LineGroupsEntity>,
    InferCreationAttributes<LineGroupsEntity>
> {
    /** 回線グループID */
    // @Id
    // @Column(name = "group_id")
    // private String groupId = null;
    @Index
    @Column({
        type: DataType.STRING(),
        primaryKey: true,
        allowNull: false,
        field: "group_id",
    })
    groupId: string;

    /** ステータス */
    // @Column(name = "status")
    // private int status;
    @Column({
        type: DataType.INTEGER(),
        field: "status",
    })
    status: number;

    /** テナントID */
    // @Column(name = "tenant_id")
    // private String tenantId = null;
    @Column({
        type: DataType.STRING(),
        field: "tenant_id",
    })
    tenantId: string;

    /** プランID */
    // @Column(name = "plan_id")
    // private Integer planId;
    @Column({
        type: DataType.INTEGER(),
        field: "plan_id",
    })
    planId: number;

    /** created_at */
    // @Column(name = "created_at")
    // private Date createdAt = null;
    @Column({
        type: DataType.DATE(),
        field: "created_at",
    })
    createdAt: Date;

    /** updated_at */
    // @Column(name = "updated_at")
    // private Date updatedAt = null;
    @Column({
        type: DataType.DATE(),
        field: "updated_at",
    })
    updatedAt: Date;

    /** グループプラン.プラン名 */
    // @Transient
    // private String planName = null;
    // NOTE Non persistent field (not mapped to a database column)
    planName: string;
}
