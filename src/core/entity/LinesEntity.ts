import { InferAttributes, InferCreationAttributes } from "sequelize";
import { Table, Column, Model, Index, DataType } from "sequelize-typescript";

@Table({
    tableName: "lines",
})
export class LinesEntity extends Model<
    InferAttributes<LinesEntity>,
    InferCreationAttributes<LinesEntity>
> {
    /** 回線ID */
    // @Id
    // @Column(name = "line_id")
    // private String lineId = null;
    @Index
    @Column({
        allowNull: false,
        type: DataType.STRING(),
        primaryKey: true,
        field: "line_id",
    })
    lineId: string;

    /** 最終オーダ種別 */
    // @Id
    // @Column(name = "line_status")
    // private String lineStatus = null;
    @Column({
        allowNull: false,
        type: DataType.STRING(),
        field: "line_status",
        primaryKey: true,
    })
    lineStatus: string;

    /** 代表N番 */
    // @Column(name = "nnumber")
    // private String nnumber = null;
    @Column({
        type: DataType.STRING(),
        field: "nnumber",
    })
    nnumber: string;

    /** 回線グループID */
    // @Column(name = "group_id")
    // private String groupId = null;
    @Column({
        type: DataType.STRING(),
        field: "group_id",
    })
    groupId: string;

    /** 回線利用開始年月日 */
    // @Column(name = "open_date")
    // private Date lineActDate = null;
    @Column({
        type: DataType.DATE(),
        field: "open_date",
    })
    lineActDate: Date;

    /** STEP3.0版対応　削除　START */
    /*    */ /** 国際ローミング利用限度額 */ /*
    // NOTE this field is commented out in Java code
    @Column(name = "roaming_max")
    private String roamingMax;*/
    /** STEP3.0版対応　削除　END */

    /** SIM番号(DN番号) */
    // @Column(name = "sim_number")
    // private String simNumber = null;
    @Column({
        type: DataType.STRING(),
        field: "sim_number",
    })
    simNumber: string;

    /** 端末種別名 */
    // @Column(name = "model_type")
    // private String modelType = null;
    @Column({
        type: DataType.STRING(),
        field: "model_type",
    })
    modelType: string;

    /** 製造番号(IMEI) */
    // @Column(name = "imei")
    // private String imei = null;
    @Column({
        type: DataType.STRING(),
        field: "imei",
    })
    imei: string;

    /** 端末利用開始年月日 */
    // @Column(name = "start_date")
    // private Date startDate = null;
    @Column({
        type: DataType.DATE(),
        field: "start_date",
    })
    startDate: Date;

    /** ドメイン */
    // @Column(name = "domain")
    // private String domain = null;
    @Column({
        type: DataType.STRING(),
        field: "domain",
    })
    domain: string;

    /** 認証ID */
    // @Column(name = "auth_id")
    // private String authId = null;
    @Column({
        type: DataType.STRING(),
        field: "auth_id",
    })
    authId: string;

    /** 国内用IP ACT東 */
    // @Column(name = "act_east_ip")
    // private String actEastIp = null;
    @Column({
        type: DataType.STRING(),
        field: "act_east_ip",
    })
    actEastIp: string;

    /** 国内用IP ACT西 */
    // @Column(name = "act_west_ip")
    // private String actWestIp = null;
    @Column({
        type: DataType.STRING(),
        field: "act_west_ip",
    })
    actWestIp: string;

    /** 国内用IP SBY */
    // @Column(name = "sby_ip")
    // private String sbyIp = null;
    @Column({
        type: DataType.STRING(),
        field: "sby_ip",
    })
    sbyIp: string;

    /** 国際用固定IPアドレス */
    // @Column(name = "fixed_ip")
    // private String fixedIp = null;
    @Column({
        type: DataType.STRING(),
        field: "fixed_ip",
    })
    fixedIp: string;

    /** 再販料金プラン */
    // @Column(name = "resale_plan_id")
    // private String pricePlanId = null;
    @Column({
        type: DataType.STRING(),
        field: "resale_plan_id",
    })
    pricePlanId: string;

    /** 再販料金プラン名 */
    // @Column(name = "resale_plan_name")
    // private String pricePlanName = null;
    @Column({
        type: DataType.STRING(),
        field: "resale_plan_name",
    })
    pricePlanName: string;

    /** POI情報 */
    // @Column(name = "poi")
    // private String poi = null;
    @Column({
        type: DataType.STRING(),
        field: "poi",
    })
    poi: string;

    /** created_at */
    // @Column(name = "created_at")
    // private Date createdAt = null;
    @Column({
        type: DataType.DATE(),
        field: "created_at",
    })
    createdAt: Date;

    /** updated_at */
    // @Column(name = "updated_at")
    // private Date updatedAt = null;
    @Column({
        type: DataType.DATE(),
        field: "updated_at",
    })
    updatedAt: Date;

    /** STEP1.2版対応　追加　START */
    /** modify_flag */
    // @Column(name = "modify_flag")
    // private Boolean modifyFlag = false;
    @Column({
        type: DataType.BOOLEAN(),
        field: "modify_flag",
    })
    modifyFlag: boolean;

    /** STEP1.2版対応　追加　END */

    /** STEP2.0a版対応　追加　START */

    /** 契約種別 */
    // @Column(name = "contract_type")
    // private String contractType;
    @Column({
        type: DataType.STRING(),
        field: "contract_type",
    })
    contractType: string;

    /** 認証パターン */
    // @Column(name = "auth_pattern")
    // private String authPattern;
    @Column({
        type: DataType.STRING(),
        field: "auth_pattern",
    })
    authPattern: string;

    /** 端末機種名 */
    // @Column(name = "device_model_name")
    // private String deviceModelName;
    @Column({
        type: DataType.STRING(),
        field: "device_model_name",
    })
    deviceModelName: string;

    /** 端末製造番号 */
    // @Column(name = "imeisv_2")
    // private String imeisv_2;
    @Column({
        type: DataType.STRING(),
        field: "imeisv_2",
    })
    imeisv_2: string;

    /** 端末製造番号(予備) */
    // @Column(name = "imeisv_3")
    // private String imeisv_3;
    @Column({
        type: DataType.STRING(),
        field: "imeisv_3",
    })
    imeisv_3: string;

    /** メモ欄 */
    // @Column(name = "notes")
    // private String notes;
    @Column({
        type: DataType.STRING(),
        field: "notes",
    })
    notes: string;

    /** SIM種別 */
    // @Column(name = "sim_type")
    // private String simType;
    @Column({
        type: DataType.STRING(),
        field: "sim_type",
    })
    simType: string;

    /** STEP2.0a版対応　追加　END */

    /** STEP3.0版対応　追加　START */
    /** 仮登録フラグ */
    // @Column(name = "temp_regist" ,nullable = true)
    // private Boolean tempRegist = false;
    @Column({
        allowNull: true,
        type: DataType.BOOLEAN(),
        field: "temp_regist",
    })
    tempRegist: boolean;

    /** サービス開始日 */
    // @Column(name = "service_date")
    // private Date serviceDate = null;
    @Column({
        type: DataType.DATE(),
        field: "service_date",
    })
    serviceDate: Date;

    /** 国際ローミング利用限度額ID */
    // @Column(name = "roaming_max_id")
    // private String roamingMaxId = null;
    @Column({
        type: DataType.STRING(),
        field: "roaming_max_id",
    })
    roamingMaxId: string;

    /** 留守番でんわID */
    // @Column(name = "voice_mail_id")
    // private String voiceMailId = null;
    @Column({
        type: DataType.STRING(),
        field: "voice_mail_id",
    })
    voiceMailId: string;

    /** キャッチホンID */
    // @Column(name = "call_waiting_id")
    // private String callWaitingId = null;
    @Column({
        type: DataType.STRING(),
        field: "call_waiting_id",
    })
    callWaitingId: string;

    /** 国際電話ID */
    // @Column(name = "intl_call_id")
    // private String intlCallId = null;
    @Column({
        type: DataType.STRING(),
        field: "intl_call_id",
    })
    intlCallId: string;

    /** 転送でんわID */
    // @Column(name = "forwarding_id")
    // private String forwardingId = null;
    @Column({
        type: DataType.STRING(),
        field: "forwarding_id",
    })
    forwardingId: string;

    /** 国際着信転送ID */
    // @Column(name = "intl_forwarding_id")
    // private String intlForwardingId = null;
    @Column({
        type: DataType.STRING(),
        field: "intl_forwarding_id",
    })
    intlForwardingId: string;

    /** STEP3.0版対応　追加　END */

    /** プランID */
    // @Transient
    // private int planId;
    @Column(DataType.VIRTUAL(DataType.INTEGER))
    get planId(): string {
        return this.getDataValue("planId");
    }
    set planId(value: string) {
        this.setDataValue("planId", value);
    }

    /** プラン名 */
    // @Transient
    // private String planName;
    @Column(DataType.VIRTUAL(DataType.STRING))
    get planName(): string {
        return this.getDataValue("planName");
    }
    set planName(value: string) {
        this.setDataValue("planName", value);
    }

    /** 対応NW */
    // @Transient
    // private String network;
    @Column(DataType.VIRTUAL(DataType.STRING))
    get network(): string {
        return this.getDataValue("network");
    }
    set network(value: string) {
        this.setDataValue("network", value);
    }

    /** SMS対応 */
    // @Transient
    // private boolean smsEnable;
    @Column(DataType.VIRTUAL(DataType.BOOLEAN))
    get smsEnable(): boolean {
        return this.getDataValue("smsEnable");
    }
    set smsEnable(value: boolean) {
        this.setDataValue("smsEnable", value);
    }

    /** テナントID */
    // @Transient
    // private String tenantId;
    @Column(DataType.VIRTUAL(DataType.STRING))
    get tenantId(): string {
        return this.getDataValue("tenantId");
    }
    set tenantId(value: string) {
        this.setDataValue("tenantId", value);
    }

    /** STEP4.0版対応　追加　START */
    /** 端末種別ID */
    // @Column(name = "device_type_id")
    // private String deviceTypeId = null;
    @Column({
        type: DataType.STRING(),
        field: "device_type_id",
    })
    deviceTypeId: string;

    /** 半黒フラグ */
    // @Column(name = "sim_flag")
    // private Boolean simFlag = false;
    @Column({
        type: DataType.BOOLEAN(),
        field: "sim_flag",
    })
    simFlag: boolean;

    /** 黒化日 */
    // @Column(name = "activate_date")
    // private Date activateDate = null;
    @Column({
        type: DataType.DATE(),
        field: "activate_date",
    })
    activateDate: Date;

    /** STEP4.0版対応　追加　END */

    // STEP15.0版対応　追加　START
    /** IMSI */
    // @Column(name = "imsi")
    // private String imsi = null;
    @Column({
        type: DataType.STRING(),
        field: "imsi",
    })
    imsi: string;

    /** PUK1 */
    // @Column(name = "puk_1")
    // private String puk1 = null;
    @Column({
        type: DataType.STRING(),
        field: "puk_1",
    })
    puk1: string;

    /** PUK2 */
    // @Column(name = "puk_2")
    // private String puk2 = null;
    @Column({
        type: DataType.STRING(),
        field: "puk_2",
    })
    puk2: string;

    /** 利用状態 */
    // @Column(name = "usage_status")
    // private Integer usageStatus = null;
    @Column({
        type: DataType.INTEGER(),
        field: "usage_status",
    })
    usageStatus: number;

    // STEP15.0版対応　追加　END

    // STEP17.0版対応　追加　START
    /** nw_modify_flag */
    // @Column(name = "nw_modify_flag")
    // private Boolean nwModifyFlag = false;
    @Column({
        type: DataType.BOOLEAN(),
        field: "nw_modify_flag",
    })
    nwModifyFlag: boolean;
    // STEP17.0版対応　追加　END

    // STEP21.0版対応　追加　START
    /** 0035でんわプランID */
    // @Column(name = "voice_plan_id")
    // private String voicePlanId = null;
    @Column({
        type: DataType.STRING(),
        field: "voice_plan_id",
    })
    voicePlanId: string;

    /** 0035でんわプラン名 */
    // @Column(name = "voice_plan_name")
    // private String voicePlanName = null;
    @Column({
        type: DataType.STRING(),
        field: "voice_plan_name",
    })
    voicePlanName: string;
    // STEP21.0版対応　追加　END

    // STEP30.0版対応 追加 START
    /** BBユニバ対象外フラグ */
    @Column({
        type: DataType.BOOLEAN(),
        field: "user_bbuni_ex_flag"
    })
    userBbuniExFlag: boolean;

    /** SwimmyBBユニバ相殺オプション有無フラグ */
    @Column({
        type: DataType.BOOLEAN(),
        field: "swimmy_bbuni_ex_flag"
    })
    swimmyBbuniExFlag: boolean;
}
