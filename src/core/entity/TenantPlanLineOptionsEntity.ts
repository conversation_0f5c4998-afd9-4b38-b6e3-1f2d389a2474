import { InferAttributes, InferCreationAttributes } from "sequelize";
import { Table, Column, Model, Index, DataType } from "sequelize-typescript";

@Table({
    tableName: "tenant_plan_line_options",
})
export default class TenantPlanLineOptionsEntity extends Model<
    InferAttributes<TenantPlanLineOptionsEntity>,
    InferCreationAttributes<TenantPlanLineOptionsEntity>
> {
    /** テナントID */
    // @Id
    // @Column(name = "tenant_id")
    // private String tenantId = null;
    @Index
    @Column({
        type: DataType.STRING(),
        primaryKey: true,
        allowNull: false,
        field: "tenant_id",
    })
    tenantId: string;

    /** プランID */
    // @Id
    // @Column(name = "plan_id")
    // private int planId;
    @Index
    @Column({
        type: DataType.INTEGER(),
        primaryKey: true,
        allowNull: false,
        field: "plan_id",
    })
    planId: number;

    /** 回線オプション分類 */
    // @Id
    // @Column(name = "line_option_type")
    // private int lineOptionType;
    @Index
    @Column({
        type: DataType.INTEGER(),
        primaryKey: true,
        allowNull: false,
        field: "line_option_type",
    })
    lineOptionType: number;
}