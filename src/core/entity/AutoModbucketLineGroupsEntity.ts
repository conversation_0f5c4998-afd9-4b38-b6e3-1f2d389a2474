import { InferAttributes, InferCreationAttributes } from "sequelize";
import { Table, Column, Model, Index, DataType } from "sequelize-typescript";

@Table({
    tableName: "auto_modbucket_line_groups",
})

export default class AutoModbucketLineGroupsEntity extends Model<
    InferAttributes<AutoModbucketLineGroupsEntity>,
    InferCreationAttributes<AutoModbucketLineGroupsEntity>
> {
    @Index
    @Column({
        type: DataType.STRING(),
        primaryKey: true,
        allowNull: false,
        field: "line_id",
    })
    lineId: string;

    @Index
    @Column({
        type: DataType.STRING(),
        primaryKey: true,
        allowNull: false,
        field: "group_id",
    })
    groupId: string;

    @Column({
        type: DataType.STRING(),
        primaryKey: true,
        allowNull: false,
        field: "service_order_id",
    })
    serviceOrderId: string;

    @Column({
        type: DataType.DATE(),
        field: "created_at",
    })
    createdAt: Date;
}