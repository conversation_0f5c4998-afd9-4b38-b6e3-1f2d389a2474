import { InferAttributes, InferCreationAttributes } from "sequelize";
import { Table, Column, Model, Index, DataType } from "sequelize-typescript";

@Table({
    tableName: "ipaddress",
})
export default class IpaddressEntity extends Model<
    InferAttributes<IpaddressEntity>,
    InferCreationAttributes<IpaddressEntity>
> {
    /** テナントID */
    // @Id
    // @Column(name = "tenant_id")
    // private String tenantId = null;
    @Index
    @Column({
        type: DataType.STRING(),
        primaryKey: true,
        allowNull: false,
        field: "tenant_id",
    })
    tenantId: string;

    /** IPアドレス */
    // @Column(name = "ip_address")
    // private String ipAddress = null;
    @Column({
        type: DataType.STRING(),
        allowNull: false,
        field: "ip_address",
    })
    ipAddress: string;

    /** サブネットマスク */
    // @Column(name = "subnet_mask")
    // private Integer subnetMask = null;
    @Column({
        type: DataType.INTEGER(),
        allowNull: false,
        field: "subnet_mask",
    })
    subnetMask: number;
}
