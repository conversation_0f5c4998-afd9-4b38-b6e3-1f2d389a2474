import { InferAttributes, InferCreationAttributes } from "sequelize";
import {
    Table,
    Column,
    Model,
    Index,
    DataType,
    HasMany,
} from "sequelize-typescript";
import TenantPlansEntity from "./TenantPlansEntity";

@Table({
    tableName: "plans",
})
export default class PlansEntity extends Model<
    InferAttributes<PlansEntity>,
    InferCreationAttributes<PlansEntity>
> {
    /** プランID */
    // @Id
    // @Column(name = "plan_id")
    // private int planId;
    @Index
    @Column({
        type: DataType.INTEGER(),
        primaryKey: true,
        allowNull: false,
        field: "plan_id",
    })
    planId: number;

    /** 料金プランID */
    // @Column(name = "resale_plan_id")
    // private String pricePlanId = null;
    @Column({
        type: DataType.STRING(),
        field: "resale_plan_id",
    })
    pricePlanId: string;

    /** プラン種別 */
    // @Column(name = "plan_class")
    // private Integer planClass;
    @Column({
        type: DataType.INTEGER(),
        field: "plan_class",
    })
    planClass: number;

    /** ポリシーID */
    // @Column(name = "policy_id")
    // private Integer policyId;
    @Column({
        type: DataType.INTEGER(),
        field: "policy_id",
    })
    policyId: number;

    /** SIM種別 */
    // @Column(name = "sim_type")
    // private String simType = null;
    @Column({
        type: DataType.STRING(),
        field: "sim_type",
    })
    simType: string;

    /** 対応NW */
    // @Column(name = "network")
    // private String network = null;
    @Column({
        type: DataType.STRING(),
        field: "network",
    })
    network: string;

    /** SMS対応 */
    // @Column(name = "sms_enable" ,nullable = true)
    // private Boolean smsEnable = false;
    @Column({
        type: DataType.BOOLEAN(),
        field: "sms_enable",
        allowNull: true,
    })
    smsEnable: boolean;

    /** プラン名 */
    // @Column(name = "plan_name")
    // private String planName = null;
    @Column({
        type: DataType.STRING(),
        field: "plan_name",
    })
    planName: string;

    /** プラン説明 */
    // @Column(name = "plan_description")
    // private String planDescription = null;
    @Column({
        type: DataType.STRING(),
        field: "plan_description",
    })
    planDescription: string;

    /** 参照サービスパターン */
    // @Column(name = "ref_service_pattern")
    // private Integer refServicePattern;
    @Column({
        type: DataType.INTEGER(),
        field: "ref_service_pattern",
    })
    refServicePattern: number;

    /** 更新サービスパターン */
    // @Column(name = "tpc_service_pattern")
    // private Integer tpcServicePattern;
    @Column({
        type: DataType.INTEGER(),
        field: "tpc_service_pattern",
    })
    tpcServicePattern: number;

    /** STEP1.3版対応　追加　START */
    /** 基本容量 */
    // @Column(name = "basic_capacity")
    // private Integer basicCapacity;
    @Column({
        type: DataType.INTEGER(),
        field: "basic_capacity",
    })
    basicCapacity: number;
    /** STEP1.3版対応　追加　END */

    /** STEP1.2版対応　追加　START */
    /** サービスプランID */
    // @Column(name = "service_plan_id")
    // private String servicePlanId;
    @Column({
        type: DataType.STRING(),
        field: "service_plan_id",
    })
    servicePlanId: string;
    /** STEP1.2版対応　追加　END */

    /** STEP3.0版対応　追加　START */
    /** 音声フラグ */
    // @Column(name = "voice_flag" ,nullable = true)
    // private Boolean voiceFlag = false;
    @Column({
        type: DataType.BOOLEAN(),
        field: "voice_flag",
        allowNull: true,
    })
    voiceFlag: boolean;
    /** STEP3.0版対応　追加　END */

    /** STEP4.0版対応　追加　START */
    /** デフォルト半黒フラグ */
    // @Column(name = "default_sim_flag")
    // private Boolean defaultSimFlag = false;
    @Column({
        type: DataType.BOOLEAN(),
        field: "default_sim_flag",
    })
    defaultSimFlag: boolean;
    /** STEP4.0版対応　追加　END */

    // STEP12.0版対応　追加　START
    /** memo */
    // @Column(name = "memo")
    // private String memo;
    @Column({
        type: DataType.STRING(),
        field: "memo",
    })
    memo: string;

    /** カスタマイズID */
    // @Column(name = "customize_id")
    // private String customizeId;
    @Column({
        type: DataType.STRING(),
        field: "customize_id",
    })
    customizeId: string;

    /** プラン変更種別 */
    // @Column(name = "plan_change_class")
    // private Integer planChangeClass;
    @Column({
        type: DataType.INTEGER(),
        field: "plan_change_class",
    })
    planChangeClass: number;
    // STEP12.0版対応　追加　END

    // STEP15.0版対応　追加　START
    /** フルMVNOフラグ */
    // @Column(name = "full_mvno_flag")
    // private Boolean fullMvnoFlag;
    @Column({
        type: DataType.BOOLEAN(),
        field: "full_mvno_flag",
    })
    fullMvnoFlag: boolean;
    // STEP15.0版対応　追加　END

    /**
     * プランID（T番）
     *
     * プランIDをT番（SwimmyにおけるプランID）で表現したもの
     */
    @Column({
        type: DataType.STRING(),
        field: "plan_id_t",
        allowNull: false,
    })
    planIdT: string;

    // @OneToMany(cascade=CascadeType.ALL, fetch = FetchType.EAGER, mappedBy="plansEntity")
    // private List<TenantPlansEntity> tenantPlansEntityList;
    @HasMany(() => TenantPlansEntity, {
        onDelete: "CASCADE",
        hooks: true,
        foreignKey: "plan_id",
    })
    tenantPlansEntityList: TenantPlansEntity[];

    @Column({
        type: DataType.BOOLEAN(),
        field: "plan_bbuni_ex_flag",
        allowNull: true,
    })
    planBbuniExFlag: boolean;
}
