import { InferAttributes, InferCreationAttributes } from "sequelize";
import { Table, Column, Model, Index, DataType, BelongsTo } from "sequelize-typescript";
import { LinesEntity } from "@/core/entity/LinesEntity";
import { TenantsEntity } from "@/core/entity/TenantsEntity";

/** テナントグループプランテーブルエンティティ。 */
@Table({
    tableName: "tenant_group_plans",
})
export default class TenantGroupPlansEntity extends Model<
    InferAttributes<TenantGroupPlansEntity>,
    InferCreationAttributes<TenantGroupPlansEntity>
> {
    /** テナントID */
    // @Id
    // @Column(name = "tenant_id")
    // private String tenantId = null;
    @Index
    @Column({
        type: DataType.STRING(10),
        allowNull: false,
        primaryKey: true,
        field: "tenant_id",
    })
    tenantId: string;

    /** プランID */
    // @Id
    // @Column(name = "plan_id")
    // private int planId;
    @Index
    @Column({
        type: DataType.INTEGER(),
        allowNull: false,
        primaryKey: true,
        field: "plan_id",
    })
    planId: number;

    @BelongsTo(() => TenantsEntity, {
        foreignKey: "tenant_id",
        targetKey: "tenantId",
        onDelete: "CASCADE",
    })
    linesEntity: TenantsEntity;

    @BelongsTo(() => LinesEntity, {
        foreignKey: "plan_id",
        targetKey: "planId",
        onDelete: "CASCADE",
    })
    tenantsEntity: LinesEntity;
}
