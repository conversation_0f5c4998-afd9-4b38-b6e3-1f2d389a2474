import { InferAttributes, InferCreationAttributes } from "sequelize";
import { Table, Column, Model, Index, DataType, BelongsTo } from "sequelize-typescript";
import { TenantsEntity } from "@/core/entity/TenantsEntity";

@Table({
    tableName: "voice_info",
})


/** 0035でんわ情報テーブルエンティティ。 */
export default class VoiceInfoEntity extends Model<
    InferAttributes<VoiceInfoEntity>,
    InferCreationAttributes<VoiceInfoEntity>
> {
    /**
     * テナントID
     */
    // @Id
    // @Column(name = "tenant_id", nullable = false)
    // private String tenantId;
    @Index
    @Column({
        allowNull: false,
        type: DataType.STRING(10),
        primaryKey: true,
        field: "tenant_id",
    })
    tenantId: string;

    /**
     * 0035でんわプランID
     */
    // @Id
    // @Column(name = "voice_plan_id", nullable = false)
    // private String voicePlanId;
    @Index
    @Column({
        allowNull: false,
        type: DataType.STRING(15),
        primaryKey: true,
        field: "voice_plan_id",
    })
    voicePlanId: string;

    /**
     * 0035でんわプラン名
     */
    // @Column(name = "voice_plan_name", nullable = false)
    // private String voicePlanName;
    @Column({
        allowNull: false,
        type: DataType.STRING(64),
        primaryKey: true,
        field: "voice_plan_name",
    })
    voicePlanName: string;

    /**
     * デフォルトフラグ
     */
    // @Column(name = "default_flag")
    // private boolean defaultFlag;
    @Column({
        type: DataType.BOOLEAN(),
        field: "default_flag",
    })
    defaultFlag: boolean;

    /**
     * 0035でんわプランN番
     */
    // @Column(name = "voice_plan_nnumber")
    // private String voicePlanNnumber;
    @Column({
        type: DataType.STRING(10),
        field: "voice_plan_nnumber",
    })
    voicePlanNnumber: string;

    @BelongsTo(() => TenantsEntity, {
        foreignKey: "tenant_id",
        targetKey: "tenantId",
        onDelete: "CASCADE",
    })
    tenantsEntity: TenantsEntity;
}