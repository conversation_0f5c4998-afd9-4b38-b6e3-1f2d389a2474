import { InferAttributes, InferCreationAttributes } from "sequelize";
import { Table, Column, Model, Index, DataType } from "sequelize-typescript";

@Table({
    tableName: "card",
})
export default class CardEntity extends Model<
    InferAttributes<CardEntity>,
    InferCreationAttributes<CardEntity>
> {
    /** カード種別ID */
    // @Id
    // @Column(name = "device_type_id")
    // private String deviceTypeId = null;
    @Index
    @Column({
        type: DataType.STRING(),
        primaryKey: true,
        allowNull: false,
        field: "device_type_id",
    })
    deviceTypeId: string;

    /** カード種別名 */
    // @Column(name = "device_type_name")
    // private String deviceTypeName = null;
    @Column({
        type: DataType.STRING(),
        field: "device_type_name",
    })
    deviceTypeName: string;

    /** SIMのみフラグ */
    // @Column(name = "sim_flag")
    // private Boolean simFlag = null;
    @Column({
        type: DataType.BOOLEAN(),
        field: "sim_flag",
    })
    simFlag: boolean;

    /** 対応NW */
    // @Column(name = "network")
    // private String network = null;
    @Column({
        type: DataType.STRING(),
        field: "network",
    })
    network: string;

    // ----- new fields -----
    /** カード種別ID（T番） */
    @Column({
        type: DataType.STRING(),
        field: "device_type_id_t",
    })
    deviceTypeIdT: string;
}
