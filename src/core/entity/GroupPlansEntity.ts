import { InferAttributes, InferCreationAttributes } from "sequelize";
import { Table, Column, Model, Index, DataType } from "sequelize-typescript";

/** テナントグループプランテーブルエンティティ。 */
@Table({
    tableName: "group_plans",
})
export class GroupPlansEntity extends Model<
    InferAttributes<GroupPlansEntity>,
    InferCreationAttributes<GroupPlansEntity>
> {
    /** プランID */
    // @Id
    // @Column(name = "plan_id")
    // private int planId;
    @Index
    @Column({
        type: DataType.INTEGER(),
        allowNull: false,
        primaryKey: true,
        field: "plan_id",
    })
    planId: number;

    /** 料金プランID */
    // @Column(name = "resale_plan_id")
    // private String pricePlanId = null;
    @Column({
        type: DataType.CHAR(5),
        field: "resale_plan_id",
    })
    pricePlanId: string;

    /** プラン種別 */
    // @Column(name = "plan_class")
    // private Integer planClass;
    @Column({
        type: DataType.INTEGER(),
        field: "plan_class",
    })
    planClass: number;

    /** ポリシーID */
    // @Column(name = "policy_id")
    // private Integer policyId;
    @Column({
        type: DataType.INTEGER(),
        field: "policy_id",
    })
    policyId: number;

    /** SIM種別 */
    // @Column(name = "sim_type")
    // private String simType = null;
    @Column({
        type: DataType.STRING(16),
        field: "sim_type",
    })
    simType: string;

    /** 対応NW */
    // @Column(name = "network")
    // private String network = null;
    @Column({
        type: DataType.STRING(10),
        field: "network",
    })
    network: string;

    /** SMS対応 */
    // @Column(name = "sms_enable",nullable = true)
    // private Boolean smsEnable = false;
    @Column({
        type: DataType.BOOLEAN(),
        field: "sms_enable",
    })
    smsEnable: boolean;

    /** プラン名 */
    // @Column(name = "plan_name")
    // private String planName = null;
    @Column({
        type: DataType.STRING(64),
        field: "plan_name",
    })
    planName: string;

    /** プラン説明 */
    // @Column(name = "plan_description")
    // private String planDescription = null;
    @Column({
        type: DataType.STRING(100),
        field: "plan_description",
    })
    planDescription: string;

    /** 参照サービスパターン */
    // @Column(name = "ref_service_pattern")
    // private Integer refServicePattern;
    @Column({
        type: DataType.INTEGER(),
        field: "ref_service_pattern",
    })
    refServicePattern: number;

    /** 更新サービスパターン */
    // @Column(name = "tpc_service_pattern")
    // private Integer tpcServicePattern;
    @Column({
        type: DataType.INTEGER(),
        field: "tpc_service_pattern",
    })
    tpcServicePattern: number;

    /** サービスプランID */
    // @Column(name = "service_plan_id")
    // private String servicePlanId;
    @Column({
        type: DataType.CHAR(4),
        field: "service_plan_id",
    })
    servicePlanId: string;
}
