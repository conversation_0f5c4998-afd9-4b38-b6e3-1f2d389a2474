import { InferAttributes, InferCreationAttributes } from "sequelize";
import { Table, Column, Model, Index, DataType } from "sequelize-typescript";

@Table({
    tableName: "tenants",
})
export class TenantsEntity extends Model<
    InferAttributes<TenantsEntity>,
    InferCreationAttributes<TenantsEntity>
> {
    // @Id
    // // テナントID
    // @Column(name="tenant_id",nullable=false,columnDefinition="character(6)")
    // private String tenantId;
    @Index
    @Column({
        allowNull: false,
        type: DataType.CHAR(6),
        primaryKey: true,
        field: "tenant_id",
    })
    tenantId: string;

    // // 社内フラグ
    // @Column(name="office",nullable=false)
    // private Boolean office;
    @Column({
        allowNull: false,
        type: DataType.BOOLEAN,
        field: "office",
    })
    office: boolean;

    // // 社内テナント種別
    // @Column(name="tenant_type")
    // private Integer tenantType;
    @Column({
        type: DataType.INTEGER,
        field: "tenant_type",
    })
    tenantType: number;

    // // ステータス
    // @Column(name="status",nullable=false)
    // private Boolean status;
    @Column({
        allowNull: false,
        type: DataType.BOOLEAN,
        field: "status",
    })
    status: boolean;

    // // お客様名
    // @Column(name="tenant_name",nullable=false,length=20)
    // private String tenantName;
    @Column({
        allowNull: false,
        type: DataType.STRING(20),
        field: "tenant_name",
    })
    tenantName: string;

    // // ハッシュ化パスワード
    // @Column(name="hashed_password",nullable=false,columnDefinition="character(36)")
    // private String hashedPassword;
    @Column({
        allowNull: false,
        type: DataType.CHAR(36),
        field: "hashed_password",
    })
    hashedPassword: string;

    // // テナントレベル
    // @Column(name="tenant_level",nullable=false)
    // private Integer tenantLevel;
    @Column({
        allowNull: false,
        type: DataType.INTEGER,
        field: "tenant_level",
    })
    tenantLevel: number;

    // // 親テナント
    // @Column(name="p_tenant_id",nullable=false,columnDefinition="character(6)")
    // private String pTenantId;
    @Column({
        allowNull: false,
        type: DataType.CHAR(6),
        field: "p_tenant_id",
    })
    pTenantId: string;

    // // メモ
    // @Column(name="memo",length=50)
    // private String memo;
    @Column({
        type: DataType.STRING(50),
        field: "memo",
    })
    memo: string;

    // /** STEP3.1版対応　追加　START */
    // // TPC
    // @Column(name="tpc",length=20)
    // private String tpc;
    @Column({
        type: DataType.STRING(20),
        field: "tpc",
    })
    tpc: string;

    // /** STEP3.1版対応　追加　END */
    // /** STEP5.0対応　追加　START */
    // // テナント接続数上限
    // @Column(name = "tenant_max_connection", nullable = false)
    // private Integer tenantMaxConnection;
    @Column({
        allowNull: false,
        type: DataType.INTEGER,
        field: "tenant_max_connection",
    })
    tenantMaxConnection: number;

    // /** STEP5.0対応　追加　END */

    // // STEP12.0版対応　追加　START
    // /** カスタマイズ数上限 **/
    // @Column(name="customize_max_connection")
    // private Integer customizeMaxConnection;
    @Column({
        type: DataType.INTEGER,
        field: "customize_max_connection",
    })
    customizeMaxConnection: number;

    // /** プラン変更パターン定型フラグ **/
    // @Column(name="plan_change_flag")
    // private Integer planChangeFlag;
    @Column({
        type: DataType.INTEGER,
        field: "plan_change_flag",
    })
    planChangeFlag: number;

    // /** 非音声の国際ローミング付与フラグ **/
    // @Column(name="notvoice_roaming_flag")
    // private Boolean notvoiceRoamingFlag;
    // // STEP12.0版対応　追加　END
    @Column({
        type: DataType.BOOLEAN,
        field: "notvoice_roaming_flag",
    })
    notvoiceRoamingFlag: boolean;

    // STEP22.0版対応　追加　START
    /** TPC（セカンダリ） */
    // @Column(name="tpc2",length=20)
    // private String tpc2;
    @Column({
        type: DataType.STRING(20),
        field: "tpc2",
    })
    tpc2: string;
    // STEP22.0版対応　追加　END

    // added since it exist in DB but not in the entity
    @Column({
        type: DataType.INTEGER,
        field: "csv_output_pattern",
    })
    csvOutputPattern: number;
}
