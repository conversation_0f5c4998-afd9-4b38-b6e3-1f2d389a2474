import { InferAttributes, InferCreationAttributes } from "sequelize";
import { Table, Column, Model, Index, DataType } from "sequelize-typescript";

@Table({
    tableName: "service_orders",
})
export default class ServiceOrdersEntity extends Model<
    InferAttributes<ServiceOrdersEntity>,
    InferCreationAttributes<ServiceOrdersEntity>
> {
    /** サービスオーダID */
    // @Id
    // @Column(name = "service_order_id")
    // private String serviceOrderId;
    @Index
    @Column({
        type: DataType.STRING(),
        primaryKey: true,
        allowNull: false,
        field: "service_order_id",
    })
    serviceOrderId: string;

    /** 投入日 */
    // @Column(name = "order_date")
    // private Date orderDate = null;
    @Column({
        type: DataType.DATE(),
        field: "order_date",
    })
    orderDate: Date;

    /** STEP1.2b版対応　追加　START */
    /** 予約日 */
    // @Column(name = "reserve_date")
    // private Date reserveDate = null;
    @Column({
        type: DataType.DATE(),
        field: "reserve_date",
    })
    reserveDate: Date;

    /** STEP1.2b版対応　追加　END */

    /** 完了日 */
    // @Column(name = "exec_date")
    // private Date execDate = null;
    @Column({
        type: DataType.DATE(),
        field: "exec_date",
    })
    execDate: Date;

    /** STEP2.0a版対応　追加　START */
    /** 機能種別 */
    // @Column(name = "function_type")
    // private String functionType = null;
    @Column({
        type: DataType.STRING(),
        field: "function_type",
    })
    functionType: string;

    /** STEP2.0a版対応　追加　END */

    /** 種別 */
    // @Column(name = "order_type")
    // private String orderType;
    @Column({
        type: DataType.STRING(),
        field: "order_type",
    })
    orderType: string;

    /** オーダステータス */
    // @Column(name = "order_status")
    // private String orderStatus;
    @Column({
        type: DataType.STRING(),
        field: "order_status",
    })
    orderStatus: string;

    /** 回線ID */
    // @Column(name = "line_id")
    // private String lineId;
    @Column({
        type: DataType.STRING(),
        field: "line_id",
    })
    lineId: string;

    /** 所属テナントID */
    // @Column(name = "tenant_id")
    // private String tenantId;
    @Column({
        type: DataType.STRING(),
        field: "tenant_id",
    })
    tenantId: string;

    /** 実行ユーザID */
    // @Column(name = "exec_user_id")
    // private String execUserId;
    @Column({
        type: DataType.STRING(),
        field: "exec_user_id",
    })
    execUserId: string;

    /** 実行テナントID */
    // @Column(name = "exec_tenant_id")
    // private String execTenantId;
    @Column({
        type: DataType.STRING(),
        field: "exec_tenant_id",
    })
    execTenantId: string;

    /** 内容詳細 */
    // @Column(name = "content")
    // private String content;
    @Column({
        type: DataType.STRING(),
        field: "content",
    })
    content: string;

    /** STEP1.2b版対応　追加　START */
    /** 内部呼び出し */
    // @Column(name="internal_flag")
    // private Boolean internalFlag;
    @Column({
        type: DataType.BOOLEAN(),
        field: "internal_flag",
    })
    internalFlag: boolean;

    /** REST電文 */
    // @Column(name = "rest_message")
    // @Type(type = "JsonType")
    // private String restMessage;
    // NOTE in DB, the field type is JSON but in Sequelize we use string
    @Column({
        type: DataType.STRING(),
        field: "rest_message",
    })
    restMessage: string;

    /** STEP1.2b版対応　追加　END */

    /** STEP4.0版対応　追加　START */
    /** 判定種別 */
    // @Column(name = "judge_type")
    // private String decisionType;
    @Column({
        type: DataType.STRING(),
        field: "judge_type",
    })
    decisionType: string;

    /** STEP4.0版対応　追加　END */

    // STEP13.0版対応　追加　START
    /** 回線グループID */
    // @Column(name = "group_id")
    // private String groupId;
    @Column({
        type: DataType.STRING(),
        field: "group_id",
    })
    groupId: string;
}
