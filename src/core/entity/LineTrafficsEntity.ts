import { InferAttributes, InferCreationAttributes } from "sequelize";
import { Table, Column, Model, Index, DataType } from "sequelize-typescript";

@Table({
    tableName: "line_traffics",
})
export default class LineTrafficsEntity extends Model<
    InferAttributes<LineTrafficsEntity>,
    InferCreationAttributes<LineTrafficsEntity>
> {
    /**
     * 回線ID
     */
    // @Id
    // @Column(name="line_id")
    // private String lineId = null;
    @Index
    @Column({
        type: DataType.STRING(),
        primaryKey: true,
        allowNull: false,
        field: "line_id",
    })
    lineId: string;

    /**
     * 年
     */
    // @Id
    // @Column(name="year")
    // private Integer year = null;
    @Index
    @Column({
        type: DataType.INTEGER(),
        primaryKey: true,
        allowNull: false,
        field: "year",
    })
    year: number;

    /**
     * 月
     */
    // @Id
    // @Column(name="month")
    // private Integer month = null;
    @Index
    @Column({
        type: DataType.INTEGER(),
        primaryKey: true,
        allowNull: false,
        field: "month",
    })
    month: number;

    /**
     * 通信量
     */
    // @Column(name="traffic")
    // private BigInteger traffic = null;
    @Column({
        type: DataType.BIGINT(),
        field: "traffic",
    })
    traffic: bigint;

    /** STEP5.0対応　追加　START */
    /**
     * 通信量(クーポンOFF時)
     */
    // @Column(name = "traffic_coupon_off")
    // private String trafficCouponOff = null;
    @Column({
        type: DataType.STRING(),
        field: "traffic_coupon_off",
    })
    trafficCouponOff: string;
}