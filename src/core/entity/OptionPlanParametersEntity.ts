import { Column, DataType, Index, Model, Table } from "sequelize-typescript";
import { InferAttributes, InferCreationAttributes } from "sequelize";


@Table({
    tableName: "option_plan_parameters",
})
export default class OptionPlanParametersEntity extends Model<
    InferAttributes<OptionPlanParametersEntity>,
    InferCreationAttributes<OptionPlanParametersEntity>
> {
    /** オプションプランID */
    // @Id
    // @Column(name = "option_plan_id")
    // private int optionPlanId;
    @Index
    @Column({
        type: DataType.INTEGER(),
        primaryKey: true,
        allowNull: false,
        field: "option_plan_id"
    })
    optionPlanId: number;

    /** パラメータキー */
    // @Id
    // @Column(name = "key")
    // private String key = null;
    @Index
    @Column({
        type: DataType.STRING(),
        primaryKey: true,
        allowNull: false,
        field: "key"
    })
    key: string;

    /** パラメータ値 */
    // @Column(name = "value")
    // private String value = null;
    @Column({
        type: DataType.STRING(),
        field: "value"
    })
    value: string;
}