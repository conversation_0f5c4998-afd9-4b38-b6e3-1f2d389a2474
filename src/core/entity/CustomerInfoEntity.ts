import { InferAttributes, InferCreationAttributes } from "sequelize";
import { Table, Column, Model, Index, DataType } from "sequelize-typescript";

@Table({
    tableName: "customer_info",
})
export class CustomerInfoEntity extends Model<
    InferAttributes<CustomerInfoEntity>,
    InferCreationAttributes<CustomerInfoEntity>
> {
    /**
     * N番
     */
    // @Id
    // @Column(name = "nnumber", nullable=false)
    // String nnumber;
    @Column({
        type: DataType.STRING,
        field: "nnumber",
        primaryKey: true,
        allowNull: false,
    })
    nnumber: string;

    /**
     * レコード種別
     */
    // @Column(name = "record_type", nullable=false)
    // String recordType;
    @Column({
        type: DataType.STRING,
        field: "record_type",
        allowNull: false,
    })
    recordType: string;

    /**
     * 回線種別
     */
    // @Column(name = "line_type", nullable=false)
    // String lineType;
    @Column({
        type: DataType.STRING,
        field: "line_type",
        allowNull: false,
    })
    lineType: string;

    /**
     * カード用途
     */
    // @Column(name = "purpose", nullable=false)
    // String purpose;
    @Column({
        type: DataType.STRING,
        field: "purpose",
        allowNull: false,
    })
    purpose: string;

    /**
     * MNP転入オプションID
     */
    // @Column(name = "mnp_add_option_id")
    // String mnpAddOptionId;
    @Column({
        type: DataType.STRING,
        field: "mnp_add_option_id",
    })
    mnpAddOptionId: string;

    /**
     * MNP転出オプションID
     */
    // @Column(name = "mnp_del_option_id")
    // String mnpDelOptionId;
    @Column({
        type: DataType.STRING,
        field: "mnp_del_option_id",
    })
    mnpDelOptionId: string;

    /**
     * NW暗証番号変更オプションID
     */
    // @Column(name = "nw_option_id")
    // String nwOptionId;
    @Column({
        type: DataType.STRING,
        field: "nw_option_id",
    })
    nwOptionId: string;

    /**
     * 契約者名(ﾌﾘｶﾞﾅ)
     */
    // @Column(name = "contractor_name_kana", nullable=false)
    // String contractorNameKana;
    @Column({
        type: DataType.STRING,
        field: "contractor_name_kana",
        allowNull: false,
    })
    contractorNameKana: string;

    /**
     * 契約者名
     */
    // @Column(name = "contractor_name", nullable=false)
    // String contractorName;
    @Column({
        type: DataType.STRING,
        field: "contractor_name",
        allowNull: false,
    })
    contractorName: string;

    /**
     * 契約者部課名
     */
    // @Column(name = "contractor_section_name")
    // String contractorSectionName;
    @Column({
        type: DataType.STRING,
        field: "contractor_section_name",
    })
    contractorSectionName: string;

    /**
     * 契約者住所郵便番号
     */
    // @Column(name = "contractor_postcode", nullable=false)
    // String contractorPostcode;
    @Column({
        type: DataType.STRING,
        field: "contractor_postcode",
        allowNull: false,
    })
    contractorPostcode: string;

    /**
     * 契約者住所（都道府県）
     */
    // @Column(name = "contractor_address_prefectures", nullable=false)
    // String contractorAddressPrefectures;
    @Column({
        type: DataType.STRING,
        field: "contractor_address_prefectures",
        allowNull: false,
    })
    contractorAddressPrefectures: string;

    /**
     * 契約者住所（市区郡町村）
     */
    // @Column(name = "contractor_address_cities", nullable=false)
    // String contractorAddressCities;
    @Column({
        type: DataType.STRING,
        field: "contractor_address_cities",
        allowNull: false,
    })
    contractorAddressCities: string;

    /**
     * 契約者住所(大字通称)
     */
    // @Column(name = "contractor_address_daiji")
    // String contractorAddressDaiji;
    @Column({
        type: DataType.STRING,
        field: "contractor_address_daiji",
    })
    contractorAddressDaiji: string;

    /**
     * 契約者住所(字)
     */
    // @Column(name = "contractor_address_ji")
    // String contractorAddressJi;
    @Column({
        type: DataType.STRING,
        field: "contractor_address_ji",
    })
    contractorAddressJi: string;

    /**
     * 契約者住所(丁目・番地・号)
     */
    // @Column(name = "contractor_address_block", nullable=false)
    // String contractorAddressBlock;
    @Column({
        type: DataType.STRING,
        field: "contractor_address_block",
        allowNull: false,
    })
    contractorAddressBlock: string;

    /**
     * 契約者住所（建物等）
     */
    // @Column(name = "contractor_address_mansion")
    // String contractorAddressMansion;
    @Column({
        type: DataType.STRING,
        field: "contractor_address_mansion",
    })
    contractorAddressMansion: string;

    /**
     * 事務担当者氏名
     */
    // @Column(name = "representative_name", nullable=false)
    // String representativeName;
    @Column({
        type: DataType.STRING,
        field: "representative_name",
        allowNull: false,
    })
    representativeName: string;

    /**
     * 事務担当者電話番号
     */
    // @Column(name = "representative_phone_number", nullable=false)
    // String representativePhoneNumber;
    @Column({
        type: DataType.STRING,
        field: "representative_phone_number",
        allowNull: false,
    })
    representativePhoneNumber: string;

    /**
     * 事務担当者E-MAIL
     */
    // @Column(name = "\"representative_e-mail\"")
    // String representativeEmail;
    @Column({
        type: DataType.STRING,
        field: "representative_e-mail",
    })
    representativeEmail: string;

    /**
     * カード／回収キット送付先住所（郵便番号）
     */
    // @Column(name = "addressee_postcode", nullable=false)
    // String addresseePostcode;
    @Column({
        type: DataType.STRING,
        field: "addressee_postcode",
        allowNull: false,
    })
    addresseePostcode: string;

    /**
     * カード／回収キット送付先住所（都道府県）
     */
    // @Column(name = "addressee_prefectures", nullable=false)
    // String addresseePrefectures;
    @Column({
        type: DataType.STRING,
        field: "addressee_prefectures",
        allowNull: false,
    })
    addresseePrefectures: string;

    /**
     * カード／回収キット送付先住所（市区郡町村）
     */
    // @Column(name = "addressee_cities", nullable=false)
    // String addresseeCities;
    @Column({
        type: DataType.STRING,
        field: "addressee_cities",
        allowNull: false,
    })
    addresseeCities: string;

    /**
     * カード／回収キット送付先住所（大字通称）
     */
    // @Column(name = "addressee_daiji")
    // String addresseeDaiji;
    @Column({
        type: DataType.STRING,
        field: "addressee_daiji",
    })
    addresseeDaiji: string;

    /**
     * カード／回収キット送付先住所（字）
     */
    // @Column(name = "addressee_ji")
    // String addresseeJi;
    @Column({
        type: DataType.STRING,
        field: "addressee_ji",
    })
    addresseeJi: string;

    /**
     * カード／回収キット送付先住所（丁目番地）
     */
    // @Column(name = "addressee_block")
    // String addresseeBlock;
    @Column({
        type: DataType.STRING,
        field: "addressee_block",
    })
    addresseeBlock: string;

    /**
     * カード／回収キット送付先住所（ビル名等）
     */
    // @Column(name = "addressee_mansion")
    // String addresseeMansion;
    @Column({
        type: DataType.STRING,
        field: "addressee_mansion",
    })
    addresseeMansion: string;

    /**
     * カード／回収キット送付先宛先氏名
     */
    // @Column(name = "addressee_name", nullable=false)
    // String addresseeName;
    @Column({
        type: DataType.STRING,
        field: "addressee_name",
        allowNull: false,
    })
    addresseeName: string;

    /**
     * カード／回収キット送付先宛先部課名
     */
    // @Column(name = "addressee_section_name")
    // String addresseeSectionName;
    @Column({
        type: DataType.STRING,
        field: "addressee_section_name",
    })
    addresseeSectionName: string;

    /**
     * カード／回収キット送付先宛先担当者
     */
    // @Column(name = "addressee_representative_name")
    // String addresseeRepresentativeName;
    @Column({
        type: DataType.STRING,
        field: "addressee_representative_name",
    })
    addresseeRepresentativeName: string;

    /**
     * カード／回収キット送付先電話番号
     */
    // @Column(name = "addressee_phone_number")
    // String addresseePhoneNumber;
    @Column({
        type: DataType.STRING,
        field: "addressee_phone_number",
    })
    addresseePhoneNumber: string;

    /**
     * 代理店コード
     */
    // @Column(name = "agent_code", nullable=false)
    // String agencyCode;
    @Column({
        type: DataType.STRING,
        field: "agent_code",
        allowNull: false,
    })
    agencyCode: string;

    /**
     * お客様対応部門（担当）
     */
    // @Column(name = "customer_service", nullable=false)
    // String customerService ;
    @Column({
        type: DataType.STRING,
        field: "customer_service",
        allowNull: false,
    })
    customerService: string;

    /**
     * お客様対応部門電話番号
     */
    // @Column(name = "customer_service_phone_number", nullable=false)
    // String customerServicePhoneNumber;
    @Column({
        type: DataType.STRING,
        field: "customer_service_phone_number",
        allowNull: false,
    })
    customerServicePhoneNumber: string;

    /**
     * お客様対応部門E-MAIL
     */
    // @Column(name = "\"customer_service_e-mail\"", nullable=false)
    // String customerServiceEmail;
    @Column({
        type: DataType.STRING,
        field: "customer_service_e-mail",
        allowNull: false,
    })
    customerServiceEmail: string;

    // ----- new fields -----
    /**
     * 留守でんわオプションID
     */
    @Column({
        type: DataType.STRING,
        field: "voicemail_option_id",
    })
    voicemailOptionId: string;

    /**
     * キャッチホンオプションID
     */
    @Column({
        type: DataType.STRING,
        field: "callwaiting_option_id",
    })
    callwaitingOptionId: string;

    /**
     * MNP転入オプションID（T番）
     */
    @Column({
        type: DataType.STRING,
        field: "mnp_add_option_id_t",
    })
    mnpAddOptionIdT: string;

    /**
     * MNP転出オプションID（T番）
     */
    @Column({
        type: DataType.STRING,
        field: "mnp_del_option_id_t",
    })
    mnpDelOptionIdT: string;

    /**
     * NW暗証番号変更オプションID（T番）
     */
    @Column({
        type: DataType.STRING,
        field: "nw_option_id_t",
    })
    nwOptionIdT: string;

    /**
     * 留守でんわオプションID（T番）
     */
    @Column({
        type: DataType.STRING,
        field: "voicemail_option_id_t",
    })
    voicemailOptionIdT: string;

    /**
     * キャッチホンオプションID（T番）
     */
    @Column({
        type: DataType.STRING,
        field: "callwaiting_option_id_t",
    })
    callwaitingOptionIdT: string;
}
