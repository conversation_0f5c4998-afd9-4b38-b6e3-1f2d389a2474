import { InferAttributes, InferCreationAttributes } from "sequelize";
import {
    Table,
    Column,
    Model,
    DataType,
    BelongsTo,
    PrimaryKey,
    ForeignKey,
} from "sequelize-typescript";
import PlansEntity from "./PlansEntity";

@Table({
    tableName: "tenant_plans",
})
export default class TenantPlansEntity extends Model<
    InferAttributes<TenantPlansEntity>,
    InferCreationAttributes<TenantPlansEntity>
> {
    /** テナントID */
    // @Id
    // @Column(name = "tenant_id")
    // private String tenantId = null;
    @PrimaryKey
    @Column({
        type: DataType.STRING(),
        allowNull: false,
        field: "tenant_id",
    })
    tenantId: string;

    /** STEP1.2a版対応　追加　START */
    /** 変更可能プランID */
    // @Id
    // @Column(name = "change_plan_id")
    // private int changePlanId;
    @PrimaryKey
    @Column({
        type: DataType.INTEGER(),
        allowNull: false,
        field: "change_plan_id",
    })
    changePlanId: number;

    /** 月間変更可能数 */
    // @Id
    // @Column(name = "change_count")
    // private int changeCount;
    // @PrimaryKey // NOTE removed PK as it doesn't match with DB schema
    @Column({
        type: DataType.INTEGER(),
        allowNull: false,
        field: "change_count",
    })
    changeCount: number;
    /** STEP1.2a版対応　追加　END */

    /** プランID */
    // @Id
    // @Column(name = "plan_id")
    // private int planId;
    @PrimaryKey
    // NOTE: looks like this FK is not being used? (changed TODO to NOTE)
    @ForeignKey(() => PlansEntity)
    @Column({
        type: DataType.INTEGER(),
        allowNull: false,
        field: "plan_id",
    })
    planId: number;

    /** STEP1.2b版対応　追加　START */
    /** プラン変更タイミング */
    // @Id
    // @Column(name = "change_timing")
    // private String changeTiming;
    @Column({
        type: DataType.STRING(),
        primaryKey: true,
        allowNull: false,
        field: "change_timing",
    })
    changeTiming: string;
    /** STEP1.2b版対応　追加　END */
    /** テナントプランエンティティ */
    // @ManyToOne(cascade=CascadeType.ALL)
    // @JoinColumn(name="plan_id")
    // private PlansEntity plansEntity;
    @BelongsTo(() => PlansEntity, {
        foreignKey: "plan_id",
        targetKey: "planId",
        onDelete: "CASCADE",
    })
    plansEntity: PlansEntity;
}
