import { InferAttributes, InferCreationAttributes } from "sequelize";
import { Table, Column, Model, Index, DataType } from "sequelize-typescript";

@Table({
    tableName: "line_tenants",
})
export default class LineTenantsEntity extends Model<
    InferAttributes<LineTenantsEntity>,
    InferCreationAttributes<LineTenantsEntity>
> {
    /**
     * 回線ID
     */
    // @Id
	// @Column(name="line_id")
	// private String lineId = null;
    @Index
    @Column({
        type: DataType.STRING(),
        primaryKey: true,
        allowNull: false,
        field: "line_id",
    })
    lineId: string;

    /**
     * テナントID
     */
    // @Id
	// @Column(name="tenant_id")
	// private String tenantId = null;
    @Index
    @Column({
        type: DataType.STRING(),
        primaryKey: true,
        allowNull: false,
        field: "tenant_id",
    })
    tenantId: string;
}
