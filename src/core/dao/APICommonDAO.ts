import { InvocationContext } from "@azure/functions";
import config from "config";
import { QueryTypes, Op, Transaction } from "sequelize";

import AbstractMvnoBaseCommon from "@/core/common/AbstractMvnoBaseCommon";
import CheckUtil from "@/core/common/CheckUtil";
import MvnoUtil from "@/core/common/MvnoUtil";

import MsgKeysConstants from "@/core/constant/MsgKeysConstants";

import IpaddressEntity from "@/core/entity/IpaddressEntity";
import { LinesEntity } from "@/core/entity/LinesEntity";
import LineGroupsEntity from "@/core/entity/LineGroupsEntity";
import LineTenantsEntity from "@/core/entity/LineTenantsEntity";
import { TenantsEntity } from "@/core/entity/TenantsEntity";
import { TenantNnumbersEntity } from "@/core/entity/TenantNnumbersEntity";
import VoiceInfoEntity from "../entity/VoiceInfoEntity";
import TenantPlansEntity from "../entity/TenantPlansEntity";
import { GroupPlansEntity } from "../entity/GroupPlansEntity";
import TenantGroupPlansEntity from "../entity/TenantGroupPlansEntity";
import PlansEntity from "../entity/PlansEntity";
import LineLineGroupsEntity from "../entity/LineLineGroupsEntity";

import { usePsql } from "@/database/psql";
import { isSQLException, retryQuery } from "@/helpers/queryHelper";

import ExtendedInvocationContext from "@/types/ExtendedInvocationContext";
import { GroupOptionPlanParametersEntity } from "@/core/entity/GroupOptionPlanParametersEntity";
import ServiceOrdersEntity from "@/core/entity/ServiceOrdersEntity";
import OptionPlanParametersEntity from "@/core/entity/OptionPlanParametersEntity";
import OptionPlansEntity from "@/core/entity/OptionPlansEntity";

import StringUtils from "../common/StringUtils";
import { parse} from "date-fns";
export default class APICommonDAO<
    TContext extends InvocationContext = ExtendedInvocationContext,
> extends AbstractMvnoBaseCommon<TContext> {
    // STEP20.0版対応 追加 START
    /**
     * DBアクセスリトライ回数
     */
    private apDbRetryMaxCnt = config.get<number>("mvno.ApDbRetryMaxCnt");

    /**
     * DBアクセスリトライ間隔
     */
    private apDbRetryInterval = config.get<number>("mvno.ApDbRetryInterval");

    // STEP20.0版対応 追加 END

    /**
     * テナントテーブルの情報を取得する 。<BR>
     *
     * @param tenantId
     * @return tenantsEntity
     * @throws Exceptionの場合
     */
    public async getTenants(tenantId: string, transaction: Transaction = null): Promise<TenantsEntity> {
        super.debug(
            tenantId,
            "",
            MsgKeysConstants.APCOMD0001,
            this.getClassName(),
            "getTenants",
            "tenantId=" + tenantId,
        );
        let tenantsEntity: TenantsEntity = null;

        try {
            if (!CheckUtil.checkIsNotNull(tenantId)) {
                if(transaction){
                    const option: any = {
                        where: {
                            tenantId,
                        },
                        transaction,
                    };
                    return await TenantsEntity.findOne(option)
                }else{
                    tenantsEntity = await retryQuery(
                        this.context,
                        "getTenants",
                        async () =>
                            await TenantsEntity.findOne({
                                where: {
                                    tenantId,
                                },
                            }),
                        this.apDbRetryMaxCnt,
                        this.apDbRetryInterval,
                    );
                }
            }
            super.debug(
                tenantId,
                "",
                MsgKeysConstants.APCOMD0002,
                this.getClassName(),
                "getTenants",
            );
        } catch (e) {
            // DBアクセスリトライエラーの場合 → throw e
            if (!isSQLException(e)) {
                super.error(
                    e as Error,
                    "",
                    "",
                    MsgKeysConstants.APCOME0401,
                    (e as Error).message,
                );
            }
            throw e;
        }
        return tenantsEntity;
    }

    /**
     * テナントテーブルの情報を取得する 。<BR>
     *
     * @param tenantId String
     * @return tenantsEntity
     */
    public async getTenantsEntity(
        tenantId: string,
    ): Promise<TenantsEntity | null> {
        return await retryQuery(
            this.context,
            "getTenantsEntity",
            async () =>
                await TenantsEntity.findOne({
                    where: {
                        tenantId,
                        status: true,
                    },
                }),
            this.apDbRetryMaxCnt,
            this.apDbRetryInterval,
        );
    }

    /**
     * テナントIDとステータスを指定で、テナントIDを検索する。
     *
     * @param planId プランID
     * @param tenantId
     *            テナントID
     * @return テナントID
     */
    public async getTenantID2(
        planId: number,
        tenantId: string,
    ): Promise<string[]> {
        const getedTenantIDList: string[] = [];
        const sequelize = await usePsql();
        try {
            const sql = [];
            // SQL文
            sql.push("SELECT distinct tenant_id ");
            sql.push("FROM tenant_plans ");
            sql.push("WHERE plan_id = :planId ");
            sql.push("and tenant_id = :tenantId ");

            const query = sql.join(" ");
            // テナントIDを取得する
            const result = await retryQuery(
                this.context,
                "getTenantID2",
                async () =>
                    (await sequelize.query(query, {
                        replacements: {
                            planId,
                            tenantId: MvnoUtil.addSpaceAfterStr(tenantId, 0),
                        },
                        type: QueryTypes.SELECT,
                    })) as any[],
                this.apDbRetryMaxCnt,
                this.apDbRetryInterval,
            );

            // 値がある場合
            if (result.length > 0) {
                for (const row of result) {
                    getedTenantIDList.push(row.tenant_id);
                }
            }
        } catch (e: any) {
            if (isSQLException(e)) throw e;
            throw new Error(e);
        }
        return getedTenantIDList;
    }

    /**
     * テナントIDと回線IDを指定で、レコード件数を取得する。
     *
     * @param tenantId
     *            テナントID
     * @param lineId
     *            回線ID
     * @return レコード件数
     */
    public async getLineTenantCount(
        tenantId: string,
        lineId: string,
    ): Promise<number> {
        let count: number = 0;
        const sequelize = await usePsql();
        try {
            const sql = [];
            let result: any[] = [];
            // SQL文
            sql.push("SELECT COUNT(*) AS COUNT ");
            sql.push("FROM line_tenants ");
            sql.push("WHERE tenant_id = :tenantId ");
            sql.push("and line_id = :lineId ");

            const query = sql.join(" ");
            result = await retryQuery(
                this.context,
                "getLineTenantCount",
                async () =>
                    (await sequelize.query(query, {
                        replacements: {
                            tenantId: MvnoUtil.addSpaceAfterStr(tenantId, 0),
                            lineId: MvnoUtil.addSpaceAfterStr(lineId, 0),
                        },
                        type: QueryTypes.SELECT,
                    })) as any[],
                this.apDbRetryMaxCnt,
                this.apDbRetryInterval,
            );

            // レコード件数を取得する
            if (result.length > 0) {
                count = +result[0].count;
            }
        } catch (e: any) {
            if (isSQLException(e)) throw e;
            throw new Error(e);
        }
        return count;
    }

    /**
     * 最終オーダ種別と回線IDを指定で、回線情報を検索する。
     *
     * @param lineId 回線ID
     * @return 回線情報（回線ID、代表N番）
     */
    public async getLineID1(lineId: string): Promise<LinesEntity | null> {
        return await retryQuery(
            this.context,
            "getLineID1",
            async () =>
                await LinesEntity.findOne({
                    where: {
                        lineId: MvnoUtil.addSpaceAfterStr(lineId, 0),
                        lineStatus: {
                            [Op.ne]: "03",
                        },
                    },
                }),
            this.apDbRetryMaxCnt,
            this.apDbRetryInterval,
        );
    }

    /**
     * 最終オーダ種別、代表N番情報リストと回線IDを指定で、回線情報を検索する。
     *
     * @param lineId 回線ID
     * @param nNumberList N番リスト
     * @return 回線情報（回線ID、代表N番
     */
    public async getLineID2(
        lineId: string,
        nNumberList: string[],
    ): Promise<LinesEntity | null> {
        return await retryQuery(
            this.context,
            "getLineID2",
            async () =>
                await LinesEntity.findOne({
                    where: {
                        lineId: MvnoUtil.addSpaceAfterStr(lineId, 0),
                        lineStatus: {
                            [Op.ne]: "03",
                        },
                        nnumber: {
                            [Op.in]: nNumberList,
                        },
                    },
                }),
            this.apDbRetryMaxCnt,
            this.apDbRetryInterval,
        );
    }

    /**
     * テナントIDにより、テナントN番情報リストを検索する。
     *
     * @param tenantId テナントID
     * @return テナントN番情報リスト
     */
    public async getNNumber(tenantId: string): Promise<string[]> {
        const result: string[] = [];
        const data = await retryQuery(
            this.context,
            "getNNumber",
            async () =>
                await TenantNnumbersEntity.findAll({
                    where: {
                        tenantId: MvnoUtil.addSpaceAfterStr(tenantId, 0),
                    },
                }),
            this.apDbRetryMaxCnt,
            this.apDbRetryInterval,
        );
        if (data.length > 0) {
            for (const row of data) {
                result.push(row.nnumber);
            }
        }
        return result;
    }

    /**
     * 最終オーダ種別と回線IDを指定で、回線情報を検索する。
     *
     * @param lineId 回線ID
     * @return 回線情報（再販料金プラン、代表N番）
     */
    public async getResalePlanID(lineId: string): Promise<LinesEntity | null> {
        return await retryQuery(
            this.context,
            "getResalePlanID",
            async () =>
                await LinesEntity.findOne({
                    where: {
                        lineId: MvnoUtil.addSpaceAfterStr(lineId, 0),
                        lineStatus: {
                            [Op.ne]: "03",
                        },
                    },
                }),
            this.apDbRetryMaxCnt,
            this.apDbRetryInterval,
        );
    }

    public async getPlanID(resalePlanID: string): Promise<string[]> {
        const planIdList: string[] = [];
        const sequelize = await usePsql();
        try {
            const sql = [];
            // SQL文
            sql.push("SELECT plan_id ");
            sql.push("FROM plans ");
            sql.push("WHERE resale_plan_id = :resalePlanID ");

            const query = sql.join(" ");

            // プランIDを取得する
            const result = await retryQuery(
                this.context,
                "getPlanID",
                async () =>
                    (await sequelize.query(query, {
                        replacements: {
                            resalePlanID: MvnoUtil.addSpaceAfterStr(
                                resalePlanID,
                                0,
                            ),
                        },
                        type: QueryTypes.SELECT,
                    })) as any[],
                this.apDbRetryMaxCnt,
                this.apDbRetryInterval,
            );

            // 値がある場合
            if (result.length > 0) {
                for (const row of result) {
                    planIdList.push(row.plan_id);
                }
            }
        } catch (e: any) {
            if (isSQLException(e)) throw e;
            throw new Error(e);
        }

        return planIdList;
    }

    /**
     * 廃止SOのS日以降のSOが存在しないかチェックする
     *
     * @param lineNo 回線番号
     * @param targetDate 基準日
     * @param reserveSoId 予約時のSO-ID
     * @return 廃止SOリスト
     */
    public async getEnableServiceOrder(
        lineNo: string,
        targetDate: string,
        reserveSoId: string,
    ): Promise<string[]> {
        const getedEnableOrder: string[] = [];
        const sequelize = await usePsql();
        try {
            const sql = [];
            // SQL文
            sql.push("SELECT s.service_order_id ");
            sql.push("FROM lines as l, service_orders as s ");
            sql.push("WHERE s.line_id = :lineNo ");
            sql.push("and s.line_id = l.line_id ");
            sql.push(
                "and s.order_type not in ('回線グループ所属回線変更(割当解除)') ",
            );
            sql.push("and ");
            sql.push("( ");
            sql.push("( ");
            sql.push("s.order_status = '完了' ");
            sql.push("and s.exec_date >= l.open_date ");
            sql.push(
                "and date_trunc('day', s.exec_date) >= date_trunc('day', cast(:targetDate as timestamp)) ",
            );
            sql.push(") ");
            sql.push("or ");
            sql.push("( ");
            sql.push("s.order_status = '予約中' ");
            sql.push("and s.reserve_date >= l.open_date ");
            sql.push(
                "and date_trunc('day', s.reserve_date) >= date_trunc('day', cast(:targetDate as timestamp)) ",
            );
            sql.push(") ");
            sql.push(") ");

            if (!CheckUtil.checkIsNotNull(reserveSoId)) {
                sql.push("and s.service_order_id not in (:reserveSoId) ");
            }

            const query = sql.join(" ");
            const replacements: any = {
                lineNo,
                targetDate: MvnoUtil.addSpaceAfterStr(targetDate, 0),
            };

            if (!CheckUtil.checkIsNotNull(reserveSoId)) {
                replacements.reserveSoId = reserveSoId;
            }

            // SOIDを取得する
            const result = await retryQuery(
                this.context,
                "getEnableServiceOrder",
                async () =>
                    (await sequelize.query(query, {
                        replacements,
                        type: QueryTypes.SELECT,
                    })) as any[],
                this.apDbRetryMaxCnt,
                this.apDbRetryInterval,
            );
            // 値がある場合
            for (const row of result) {
                getedEnableOrder.push(row.service_order_id);
            }
        } catch (e: any) {
            if (isSQLException(e)) throw e;
            throw new Error(e);
        }
        return getedEnableOrder;
    }

    /**
     * SO-IDに紐付くサービスオーダ情報を取得する
     *
     * @param soId サービスオーダID
     * @return SO管理エンティティ
     * @throws SQLException
     */
    public async getServiceOrder(soId: string): Promise<ServiceOrdersEntity> {
        return await retryQuery(
            this.context,
            "getServiceOrder",
            async () =>
                await ServiceOrdersEntity.findOne({
                    where: {
                        serviceOrderId: soId,
                    },
                }),
            this.apDbRetryMaxCnt,
            this.apDbRetryInterval,
        );
    }

    /**
     * 再販料金プランから所属テナントを取得する。
     *
     * @param nNumber N番
     * @return TenantNnumbersEntity
     */
    public async getLineGroupsEntity(
        groupId: string,
    ): Promise<LineGroupsEntity> {
        return await retryQuery(
            this.context,
            "getLineGroupsEntity",
            async () =>
                await LineGroupsEntity.findOne({
                    where: {
                        groupId,
                    },
                }),
            this.apDbRetryMaxCnt,
            this.apDbRetryInterval,
        );
    }

    /**
     * 回線テナントから所属テナントを取得する。
     *
     * @param lineId 回線ID
     * @return LineTenantsEntity
     */
    public async getLineTenantsEntity(
        lineId: string,
    ): Promise<LineTenantsEntity[]> {
        return await retryQuery(
            this.context,
            "getLineTenantsEntity",
            async () =>
                await LineTenantsEntity.findAll({
                    where: {
                        lineId,
                    },
                }),
            this.apDbRetryMaxCnt,
            this.apDbRetryInterval,
        );
    }

    /**
     * テナントN番から所属テナントを取得する。
     *
     * @param nNumber N番
     * @return TenantNnumbersEntity
     */
    public async getTenantNnumbersEntity(
        nnumber: string,
    ): Promise<TenantNnumbersEntity[]> {
        return await retryQuery(
            this.context,
            "getTenantNnumbersEntity",
            async () =>
                await TenantNnumbersEntity.findAll({
                    where: {
                        nnumber,
                    },
                }),
            this.apDbRetryMaxCnt,
            this.apDbRetryInterval,
        );
    }

    /**
     * プランIDを指定して、重複を除いたテナントIDを検索する。
     *
     * @param planId プランID
     * @return テナントID
     */
    public async getTenantIdFromTenantPlans(planId: number): Promise<string[]> {
        // List<String> getedTenantIDList = new ArrayList<String>();
        // Session session = null;
        // Query query = null;
        const getedTenantIDList = [];
        const sequelize = await usePsql();

        try {
            const sql = [];
            // 	// SQL文
            sql.push("SELECT distinct tenant_id ");
            sql.push("FROM tenant_plans ");
            sql.push("WHERE plan_id = :planId ");

            const query = sql.join(" ");

            // テナントIDを取得する
            const result = await retryQuery(
                this.context,
                "getTenantIdFromTenantPlans",
                async () =>
                    (await sequelize.query(query, {
                        replacements: {
                            planId,
                        },
                        type: QueryTypes.SELECT,
                    })) as any[],
                this.apDbRetryMaxCnt,
                this.apDbRetryInterval,
            );
            // 値がある場合
            for (const row of result) {
                getedTenantIDList.push(row.tenant_id);
            }
        } catch (e: any) {
            if (isSQLException(e)) throw e;
            throw new Error(e);
        }
        return getedTenantIDList;
    }

    /**
     * オプションプランパラメータテーブルの情報を取得する。<BR>
     *
     * @param optionPlanId
     * @param paramKey
     * @return optionPlanParametersEntity
     * @throws Exception
     */
    public async getOptionPlanParameters(optionPlanId: string, paramKey: string
    ): Promise<OptionPlanParametersEntity>
    {
        super.debug("","",MsgKeysConstants.APCOMD0001, this.getClassName(), "getOptionPlanParameters", "optionPlanId=" + optionPlanId + ",paramKey=" + paramKey);

        // STEP20.0版対応 変更 START
        return await retryQuery(
            this.context,
            "getSequence",
            async () =>
            {
                super.debug("","",MsgKeysConstants.APCOMD0002, this.getClassName(), "getOptionPlanParameters");
                if (!CheckUtil.checkIsNotNull(optionPlanId)
                    && !CheckUtil.checkIsNotNull(paramKey)
                    && CheckUtil.checkIsNum(optionPlanId)) {
                    return await OptionPlanParametersEntity.findOne({
                        where: {
                            optionPlanId: parseInt(optionPlanId, 10),
                            key: paramKey,
                        },
                    });
                }
                return null;
            },
            this.apDbRetryMaxCnt,
            this.apDbRetryInterval,
        );
        // STEP20.0版対応 変更 END
    }

    /**
     * シーケンスを取得する 。<BR>
     *
     * @return String
     * @throws Exception
     */
    public async getSequence(): Promise<string> {
        super.debug(
            "",
            "",
            MsgKeysConstants.APCOMD0001,
            this.getClassName(),
            "getSequence",
            "",
        );
        const sequelize = await usePsql();

        try {
            const query = "select nextval('api_id_seq')";
            let result: any[] = [];
            result = await retryQuery(
                this.context,
                "getSequence",
                async () =>
                    (await sequelize.query(query, {
                        type: QueryTypes.SELECT,
                    })) as any[],
                this.apDbRetryMaxCnt,
                this.apDbRetryInterval,
            );

            super.debug(
                "",
                "",
                MsgKeysConstants.APCOMD0002,
                this.getClassName(),
                "getSequence",
            );
            if (result.length === 0) {
                throw new Error("Failed to get sequence");
            } else {
                return result[0].nextval;
            }
        } catch (e: any) {
            if (isSQLException(e)) throw e;

            super.error(e, "", "", MsgKeysConstants.APCOME0401, e.message);
            throw new Error(e);
        }
    }

    /**
     * グループプランオプションプラン情報を取得する
     * @param groupOptionPlanId グループオプションプランID
     * @param potalGroupPlanID グループプランID
     * @param optionPlanType オプションプラン種別
     * @return グループプランID
     * @throws SQLException
     */
    public async getCheckedGroupPlanOptionPlans(
        groupOptionPlanId: string,
        potalGroupPlanID: string,
        optionPlanType: string,
    ): Promise<string> {
        const sequelize = await usePsql();
        try {
            const sql = [];
            // SQL文
            sql.push("select gpop.plan_id ");
            sql.push("from group_plan_option_plans gpop, gruop_option_plans gop ");
            sql.push("where gpop.option_plan_id = :option_plan_id ");
            sql.push("and gpop.plan_id = :plan_id ");
            sql.push("and gop.option_plan_type = :option_plan_type ");
            sql.push("and gop.option_plan_id = gpop.option_plan_id ");
            const query = sql.join(" ");

            const list = await retryQuery(
                this.context,
                "getCheckedGroupPlanOptionPlans",
                async () =>
                    (await sequelize.query(query, {
                        replacements: {
                            option_plan_id: parseInt(groupOptionPlanId, 10),
                            plan_id: parseInt(potalGroupPlanID, 10),
                            option_plan_type: optionPlanType,
                        },
                        type: QueryTypes.SELECT,
                    })) as any[],
                this.apDbRetryMaxCnt,
                this.apDbRetryInterval,
            );
            if (list !== null && list.length === 1) {
                return list[0].plan_id.toString();
            }
        } catch (e: any) {
            if (isSQLException(e)) throw e;
            throw new Error(e);
        }
        return null;
    }

    /**
     * IPアドレス取得処理
     * @param tenantId テナントID
     * @return 許可IPアドレスリスト(0件の場合は空のリスト)
     */
    public async getIpAddressList(
        tenantId: string,
    ): Promise<IpaddressEntity[]> {
        return await retryQuery(
            this.context,
            "getIpAddressList",
            async () =>
                await IpaddressEntity.findAll({
                    where: {
                        tenantId,
                    },
                }),
            this.apDbRetryMaxCnt,
            this.apDbRetryInterval,
        );
    }

    /**
     * 回線番号と基準日を指定で、廃止SOを検索する。
     *
     * @param lineNo 回線番号
     * @param targetDate 基準日
     * @return 廃止SOリスト
     */
    public async getCommonAbolitionOrder(lineNo: string, targetDate: string) {
        const getedAbolitionOrder: string[] = [];
        const sequelize = await usePsql();

        try {
            const sql = [];
            // SQL文
            sql.push("SELECT s.service_order_id ");
            sql.push("FROM lines as l, service_orders as s ");
            sql.push("WHERE s.function_type = '52' ");
            sql.push("and s.line_id = :lineNo ");
            sql.push("and s.line_id = l.line_id ");
            sql.push("and ");
            sql.push("( ");
            sql.push("( ");
            sql.push("s.order_status = '完了' ");
            sql.push("and s.exec_date >= l.open_date ");
            sql.push(
                "and date_trunc('day', s.exec_date) <= date_trunc('day', cast(:targetDate as timestamp)) ",
            );
            sql.push(") ");
            sql.push("or ");
            sql.push("( ");
            sql.push("s.order_status = '予約中' ");
            sql.push("and s.reserve_date >= l.open_date ");
            sql.push(
                "and date_trunc('day', s.reserve_date) <= date_trunc('day', cast(:targetDate as timestamp)) ",
            );
            sql.push(") ");
            sql.push(") ");

            // query = session.createSQLQuery(sql.toString());
            // query.setParameter("lineNo", lineNo);
            // query.setParameter("targetDate", targetDate);

            const query = sql.join(" ");
            const result = await retryQuery(
                this.context,
                "getCommonAbolitionOrder",
                async () =>
                    (await sequelize.query(query, {
                        replacements: {
                            lineNo,
                            targetDate,
                        },
                        type: QueryTypes.SELECT,
                    })) as any[],
                this.apDbRetryMaxCnt,
                this.apDbRetryInterval,
            );

            // SOIDを取得する
            // 値がある場合
            for (const obj of result) {
                getedAbolitionOrder.push(obj.service_order_id);
            }
        } catch (ex: any) {
            if (isSQLException(ex)) throw ex;
            throw new Error(ex);
        }
        return getedAbolitionOrder;
    }

    /**
     * 回線グループIDリストを取得する
     * @param lineId String 回線番号
     * @return List&lt;String&gt;回線グループIDリスト
     */
    public async getGroupIdList(lineId: string): Promise<string[]> {
        const sequelize = await usePsql();
        const sql =
            "select group_id from line_line_groups where line_id = :lineId";
        const result = await retryQuery(
            this.context,
            "getGroupIdList",
            async () =>
                (await sequelize.query(sql, {
                    replacements: {
                        lineId,
                    },
                    type: QueryTypes.SELECT,
                })) as any[],
            this.apDbRetryMaxCnt,
            this.apDbRetryInterval,
        );
        if (result.length > 0) {
            return result.map((r) => r.group_id);
        }
        return [];
    }

    /**
     * 回線IDリストを取得する
     * @param groupId String 回線グループID
     * @return List 回線IDリスト
     */
    public async getLineLineGroupToLineId(groupId: string): Promise<string[]> {
        const sequelize = await usePsql();
        const result = [];
        try {
            const sql =
                "select line_id from line_line_groups where group_id = :group_id limit 1";
            const rows = await retryQuery(
                this.context,
                "getLineLineGroupToLineId",
                async () =>
                    (await sequelize.query(sql, {
                        replacements: {
                            group_id: groupId,
                        },
                        type: QueryTypes.SELECT,
                    })) as any[],
                this.apDbRetryMaxCnt,
                this.apDbRetryInterval,
            );
            for (const row of rows) {
                result.push(row.line_id);
            }
        } catch (e) {
            if (isSQLException(e)) throw e;
            throw new Error(e);
        }
        return result;
    }

    /**
     * 自動計算回線グループ情報格納
     * @param lineId 回線ID
     * @param groupId 回線グループID
     * @param serviceOrderId サービスオーダID
     * @param createdAt created_at
     */
    public async insertAutoModBucketLineGroup(
        lineId: string,
        groupId: string,
        serviceOrderId: string,
        createdAt: string,
    ): Promise<void> {
        this.context.log("APICommonDAO.insertAutoModBucketLineGroup", {
            lineId,
            groupId,
            serviceOrderId,
            createdAt,
        });
        const sequelize = await usePsql();
        const strSqlSel = [];
        const strSqlVal = [];
        strSqlSel.push("INSERT INTO ");
        strSqlSel.push("auto_modbucket_line_groups ");

        strSqlSel.push(" (");
        strSqlSel.push("line_id");
        strSqlSel.push(", group_id");
        strSqlSel.push(", service_order_id");
        strSqlSel.push(", created_at");
        strSqlSel.push(" )");

        strSqlVal.push(" VALUES ");
        strSqlVal.push(" (");

        strSqlVal.push(":line_id");
        strSqlVal.push(", :group_id");
        strSqlVal.push(", :service_order_id");
        strSqlVal.push(", to_timestamp( :created_at, 'YYYY/MM/DD HH24:MI:SS')");
        strSqlVal.push(" )");
        const strSql = strSqlSel.join(" ") + strSqlVal.join(" ");
        await retryQuery(
            this.context,
            "insertAutoModBucketLineGroup",
            async () =>
                await sequelize.query(strSql, {
                    replacements: {
                        line_id: lineId,
                        group_id: groupId,
                        service_order_id: serviceOrderId,
                        created_at: createdAt,
                    },
                    type: QueryTypes.INSERT,
                }),
            this.apDbRetryMaxCnt,
            this.apDbRetryInterval,
        );
    }

    /**
     * 0035でんわ情報テーブルからテナントでデフォルトの情報を取得する
     *
     * @param tenantId     テナントID
     * @return 0035でんわプラン情報
     * @throws SQLException
     */
    public async getDefaultVoicePlanInfo(
        tenantId: string,
    ): Promise<VoiceInfoEntity> {
        return await retryQuery(
            this.context,
            "getDefaultVoicePlanInfo",
            async () => {
                const result = await VoiceInfoEntity.findAll({
                    attributes: ["voicePlanId", "voicePlanName"], // select
                    where: {
                        tenantId,
                        defaultFlag: "true",
                    },
                });
                if (result.length > 0) {
                    return result[0];
                }
                return null;
            },
            this.apDbRetryMaxCnt,
            this.apDbRetryInterval,
        );
    }

    // STEP15.0版対応 追加 START
    /**
     * 利用状態がサスペンド中かチェックを行う。
     * @param lineId String 回線ID
     * @return List 利用状態リスト
     * @throws SQLException
     */
    public async getLineUsageStatus(lineId: string): Promise<number[]> {
        const result: number[] = [];
        const sequelize = await usePsql();
        const sql = [];
        sql.push("select l.usage_status ");
        sql.push("from lines l, plans p ");
        sql.push("where l.line_id = :lineId ");
        sql.push("and l.resale_plan_id = p.resale_plan_id ");
        sql.push("and p.full_mvno_flag = 'true' ");
        sql.push("and l.usage_status = 2 ");
        const query = sql.join(" ");
        const rows = await retryQuery(
            this.context,
            "getLineUsageStatus",
            async () =>
                (await sequelize.query(query, {
                    replacements: {
                        lineId,
                    },
                    type: QueryTypes.SELECT,
                })) as any[],
            this.apDbRetryMaxCnt,
            this.apDbRetryInterval,
        );
        for (const row of rows) {
            result.push(row.usage_status);
        }
        return result;
    }

    /**
     * 0035でんわ情報テーブルから指定の0035でんわプラン名を取得する
     *
     * @param tenantId     テナントID
     * @param voicePlanId  0035でんわプランID
     * @return 0035でんわプラン名
     * @throws SQLException
     */
    public async getVoicePlanName(
        tenantId: string,
        voicePlanId: string,
    ): Promise<string> {
        return await retryQuery(
            this.context,
            "getVoicePlanName",
            async () => {
                const voiceInfoEntities = await VoiceInfoEntity.findAll({
                    attributes: ["voicePlanName"], // select
                    where: {
                        tenantId,
                        voicePlanId,
                    },
                });
                if (voiceInfoEntities.length === 1) {
                    return voiceInfoEntities[0].voicePlanName;
                }
                return null;
            },
            this.apDbRetryMaxCnt,
            this.apDbRetryInterval,
        );
    }

    public async getTenantPlans(
        planId: string,
        tenantId: string,
    ): Promise<TenantPlansEntity[]> {
        super.debug(
            tenantId,
            "",
            MsgKeysConstants.APCOMD0001,
            this.getClassName(),
            "getTenantPlans",
            "planId=" + planId + ",tenantId=" + tenantId,
        );
        return await retryQuery(
            this.context,
            "getTenantPlans",
            async () => {
                const whereClause: { tenant_id?: string; plan_id?: string } =
                    {};
                if (
                    CheckUtil.checkIsNotNull(planId) &&
                    CheckUtil.checkIsNotNull(tenantId)
                ) {
                    // nothing to do
                } else if (
                    (planId == null || planId === "") &&
                    !CheckUtil.checkIsNotNull(tenantId)
                ) {
                    whereClause.tenant_id = tenantId;
                } else if (
                    !CheckUtil.checkIsNotNull(planId) &&
                    (tenantId == null || tenantId === "")
                ) {
                    whereClause.plan_id = planId;
                } else {
                    whereClause.plan_id = planId;
                    whereClause.tenant_id = tenantId;
                }
                const results = await TenantPlansEntity.findAll({
                    where: whereClause,
                });
                super.debug(
                    tenantId,
                    "",
                    MsgKeysConstants.APCOMD0002,
                    this.getClassName(),
                    "getTenantPlans",
                );
                return results;
            },
            this.apDbRetryMaxCnt,
            this.apDbRetryInterval,
        );
    }

    public async getTenantGroupPlans(
        planId: string,
        tenantId: string,
    ): Promise<TenantGroupPlansEntity> {
        super.debug(
            tenantId,
            "",
            MsgKeysConstants.APCOMD0001,
            this.getClassName(),
            "getTenantGroupPlans",
            "planId=" + planId + ",tenantId=" + tenantId,
        );

        return await retryQuery(
            this.context,
            "getTenantGroupPlans",
            async () => {
                const tenantPlansEntityList =
                    await TenantGroupPlansEntity.findOne({
                        where: {
                            planId,
                            tenantId,
                        },
                    });
                super.debug(
                    tenantId,
                    "",
                    MsgKeysConstants.APCOMD0002,
                    this.getClassName(),
                    "getTenantGroupPlans",
                );

                return tenantPlansEntityList;
            },
            this.apDbRetryMaxCnt,
            this.apDbRetryInterval,
        );
    }

    public async getPlans(planId: string, transaction: Transaction  = null): Promise<PlansEntity[]> {
        super.debug(
            "",
            "",
            MsgKeysConstants.APCOMD0001,
            this.getClassName(),
            "getPlans",
            "planId=" + planId,
        );
        if(transaction){
            const option: any = {
                where: {
                    planId,
                },
                transaction,
            };

            if (
                !CheckUtil.checkIsNotNull(planId) &&
                CheckUtil.checkIsNum(planId)
            ) {
                return await PlansEntity.findAll({
                    where: {
                        planId,
                    },
                });
            }
            super.debug(
                "",
                "",
                MsgKeysConstants.APCOMD0002,
                this.getClassName(),
                "getPlans",
            );
        }
        return await retryQuery(
            this.context,
            "getPlans",
            async () => {
                let plansList: PlansEntity[] = [];
                if (
                    !CheckUtil.checkIsNotNull(planId) &&
                    CheckUtil.checkIsNum(planId)
                ) {
                    plansList = await PlansEntity.findAll({
                        where: {
                            planId,
                        },
                    });
                }
                super.debug(
                    "",
                    "",
                    MsgKeysConstants.APCOMD0002,
                    this.getClassName(),
                    "getPlans",
                );
                return plansList;
            },
            this.apDbRetryMaxCnt,
            this.apDbRetryInterval,
        );
    }

    /**
     * グループプランテーブルの情報を取得する<BR>
     *
     * @param planId
     * @return groupPlansEntity
     * @throws Exceptionの場合
     */
    public async getGroupPlans(planId: string): Promise<GroupPlansEntity> {
        super.debug(
            "",
            "",
            MsgKeysConstants.APCOMD0001,
            this.getClassName(),
            "getGroupPlans",
            "planId=" + planId,
        );
        return await retryQuery(
            this.context,
            "getGroupPlans",
            async () => {
                let groupPlansEntity: GroupPlansEntity = null;
                if (
                    !CheckUtil.checkIsNotNull(planId) &&
                    CheckUtil.checkIsNum(planId)
                ) {
                    groupPlansEntity = await GroupPlansEntity.findOne({
                        where: {
                            planId,
                        },
                    });
                }
                super.debug(
                    "",
                    "",
                    MsgKeysConstants.APCOMD0002,
                    this.getClassName(),
                    "getGroupPlans",
                );
                return groupPlansEntity;
            },
            this.apDbRetryMaxCnt,
            this.apDbRetryInterval,
        );
    }

    /**
     * グループオプションプランパラメータの情報を取得する。<BR>
     * @param optionPlanId String
     * @param key String
     * @return GroupOptionPlanParametersEntity
     * @exception Exceptionの場合
     */
    public async getGroupOptionPlanParameters(
        optionPlanId: string,
        key: string,
    ): Promise<GroupOptionPlanParametersEntity> {
        super.debug("","",MsgKeysConstants.APCOMD0001, this.getClassName(), "getGroupOptionPlanParameters", "optionPlanId=" + optionPlanId + ",key=" + key)
        let parameters = null;
        try {
            await retryQuery(
                this.context,
                "getGroupOptionPlanParameters",
                async () => {
                    if (!CheckUtil.checkIsNotNull(optionPlanId) && CheckUtil.checkIsNum(optionPlanId)) {
                        parameters = await GroupOptionPlanParametersEntity.findOne({
                            where: {
                                optionPlanId: parseInt(optionPlanId, 10),
                                key,
                            },
                        });
                    }
                    super.debug("", "", MsgKeysConstants.APCOMD0002, this.getClassName(), "getGroupOptionPlanParameters");
                },
                this.apDbRetryMaxCnt,
                this.apDbRetryInterval,
            );
        } catch (e) {
            if (isSQLException(e)) {
                throw e;
            } else {
                super.error(e, "", "", MsgKeysConstants.APCOME0401, e.message);
                throw new Error(e);
            }
        }
        return parameters;
    }

    /**
     * 処理内容に取得したレコード
     * @param lineGroupId 回線グループID
     * @return LinesEntity
     * @throws SQLException
     */
    public async getLinesID(lineGroupId: string): Promise<LinesEntity> {
        return await retryQuery(
            this.context,
            "getLinesID",
            async () => {
                const result = await LineLineGroupsEntity.findAll({
                    where: {
                        groupId: lineGroupId,
                    },
                    attributes: ["lineId"],
                    order: [["lineId", "ASC"]],
                    limit: 1,
                }); // select lineId from line_line_groups, then set lineId to LineEntity

                const lineEntity: LinesEntity = LinesEntity.build({});
                for (const lineId of result) {
                    lineEntity.lineId = lineId.lineId;
                }
                return lineEntity;
            },
            this.apDbRetryMaxCnt,
            this.apDbRetryInterval,
        );
    }

    /**
     * 回線回線グループ情報の件数取得処理
     * @param lineId 回線番号
     * @param session 接続情報
     * @return 件数
     */
    public async getLineLineGroupsCount(
        lineId: string,
        transaction: Transaction,
    ) {
        // SQL文
        // const sql =
        //     "SELECT " +
        //     "  count(*) as count " +
        //     "FROM " +
        //     "  line_line_groups " +
        //     "WHERE " +
        //     "  line_id = :lineId";
        // レコード件数を取得する

        return await LineLineGroupsEntity.count({
            where: {
                lineId,
            },
            transaction,
        });
    }

    /**
     * 回線回線グループ情報の行ロック取得処理
     *
     * @param lineId 回線番号
     * @param session 接続情報
     * @return 回線グループID
     */
    public async getLineLineGroupsLock(
        lineId: string,
        transaction: Transaction,
    ): Promise<string[]> {
        const sequelize = await usePsql();
        const getLineLineGroupList: string[] = [];

        // SQL文
        const sql =
            "SELECT " +
            "  line_id " +
            "FROM " +
            "  line_line_groups " +
            "WHERE " +
            "  line_id = :lineId " +
            "FOR UPDATE NOWAIT";

        const result = (await sequelize.query(sql, {
            replacements: {
                lineId,
            },
            type: QueryTypes.SELECT,
            transaction,
        })) as any[];

        for (const row of result) {
            getLineLineGroupList.push(row.line_id);
        }
        return getLineLineGroupList;
    }

    /**
     * 回線回線グループ情報の削除処理
     *
     * @param lineId 回線番号
     * @param session 接続情報
     */
    public async deleteLineLineGroups(
        lineId: string,
        transaction: Transaction,
    ): Promise<void> {
        this.context.log("APICommonDAO.deleteLineLineGroups", {
            lineId,
        });
        await LineLineGroupsEntity.destroy({
            where: {
                lineId,
            },
            transaction,
        });
    }

    /**
     * プランオプションプランの情報を取得する。<BR>
     *
     * @param optionPlanId オプションプランID
     * @param planId プランID
     * @param optionPlanType オプションプラン種別
     * @return プランID取得結果
     * @throws SQLException
     * @exception Exceptionの場合
     */
    public async getCheckedPlanOptionPlans(
        optionPlanId: string,
        planId: string,
        optionPlanType: string,
    ): Promise<string> {
        const sequelize = await usePsql();
        return await retryQuery(
            this.context,
            "getCheckedPlanOptionPlans",
            async () => {
                const sql: string[] = [];
                sql.push("SELECT p.plan_id ");
                sql.push("FROM plan_option_plans as p, option_plans as o ");
                sql.push("WHERE p.option_plan_id = :option_plan_id ");
                sql.push("and p.plan_id = :plan_id ");
                sql.push("and o.option_plan_type = :option_plan_type ");
                sql.push("and o.option_plan_id = p.option_plan_id");
                const result = (await sequelize.query(sql.join(" "), {
                    replacements: {
                        option_plan_id: parseInt(optionPlanId, 10),
                        plan_id: parseInt(planId, 10),
                        option_plan_type: optionPlanType,
                    },
                    type: QueryTypes.SELECT,
                })) as any[];
                if (result && result.length === 1) {
                    return result[0].plan_id.toString();
                } else {
                    return null;
                }
            },
            this.apDbRetryMaxCnt,
            this.apDbRetryInterval,
        );
    }


    /**
     * テナントのプラン変更パターン定型フラグを取得する
     *
     * @param tenantId テナントID
     * @param session 接続情報
     * @return プラン変更パターン定型フラグ
     */
    public async getPlanChangeFlag(tenantId: string, transaction: Transaction ):Promise<number[]>{
        this.context.log("APICommonDAO.getPlanChangeFlag", {
            tenantId,
        });
        const sequelize = await usePsql();
        const getPlanChangeFlag: number[] = [];
        const sql =  "SELECT "
        + "  plan_change_flag "
        + "FROM "
        + "  tenants "
        + "WHERE "
        + "  tenant_id = :tenantId";
        const result =  (await sequelize.query(sql, {
            replacements: {
                tenantId,
            },
            type: QueryTypes.SELECT,
            transaction,
        })) as any[];
        if(result.length > 0){
            for ( const row of result){
                getPlanChangeFlag.push(row.plan_change_flag)
            }
        }
        return getPlanChangeFlag;
    }

    /**
     * プランのプラン変更種別を取得する
     *
     * @param planId プランID
     * @param session 接続情報
     * @return プラン変更種別
     */
    public async getPlanChangeClass(planId:string,transaction: Transaction  ):Promise<number[]>{
        this.context.log("APICommonDAO.getPlanChangeClass", {
            planId,
        });
        const sequelize = await usePsql();
        const getPlanChangeClass: number[] = [];
        const sql = "SELECT "
        + "  plan_change_class "
        + "FROM "
        + "  plans "
        + "WHERE "
        + "  plan_id = :planId";
        const result = (await sequelize.query(sql, {
            replacements: {
                planId,
            },
            type: QueryTypes.SELECT,
            transaction,
        })) as any[];
        if(result.length > 0){
            for ( const row of result){
                getPlanChangeClass.push(row.plan_change_class)
            }
        }
        return getPlanChangeClass;
    }

    /**
     * プランの音声フラグ、SMS対応、対応NW、フルMVNOフラグを取得する
     *
     * @param planId プランID
     * @param session 接続情報
     * @return 音声フラグ、SMS対応、対応NW、フルMVNOフラグ
     */
    public async getPlansTypeList(
        planId: string,
        transaction: Transaction,
    ):Promise<string[]>{
        const sequelize = await usePsql();
        const getPlansTypeList: string[] = [];

        // SQL文
        const sql
        = "SELECT "
        + "  voice_flag, sms_enable, network, full_mvno_flag "
        + "FROM "
        + "  plans "
        + "WHERE "
        + "  plan_id = :planId";

        const result = (await sequelize.query(sql, {
            replacements: {
                planId: parseInt(planId, 10),
            },
            type: QueryTypes.SELECT,
            transaction,
        })) as any[];

        for (const row of result) {
            getPlansTypeList.push(String(row.voice_flag));
            getPlansTypeList.push(String(row.sms_enable));
            getPlansTypeList.push(String(row.network));
            getPlansTypeList.push(String(row.full_mvno_flag));
        }
        return getPlansTypeList;
    }

    /**
     * テナントプランのプランIDを取得する
     *
     * @param tenantId プランID
     * @param planId テナントID
     * @param session 接続情報
     * @return プランID
     */
    public async getTenantPlansPlanId(
        tenantId: string,
        planId: string,
        transaction:Transaction,
    ):Promise<number[]>{
        const sequelize = await usePsql();
        const getTenantPlansPlanId: number[] = [];
        // SQL文
        const sql
            = "SELECT "
            + "  plan_id "
            + "FROM "
            + "  tenant_plans "
            + "WHERE "
            + "  tenant_id = :tenantId "
            + "AND "
            + "  plan_id = :planId";

        const result = (await sequelize.query(sql, {
            replacements: {
                tenantId,
                planId: parseInt(planId, 10),
            },
            type: QueryTypes.SELECT,
            transaction,
        })) as any[];
        for( const row of result){
            getTenantPlansPlanId.push(row.plan_id)
        }
        return getTenantPlansPlanId;
    };

    /**
     * テナントプラン情報を取得する
     * @param tenantID テナントID
     * @param planID プランID
     * @param changePlanID 変更可能プランID
     * @return テナントプラン情報
     */
    public async getTenantPlanforPlanChange(
        tenantID:string,
        planID: string,
        changePlanID: string,
    ):Promise<TenantPlansEntity>;

    /**
     * テナントプラン情報を取得する
     * @param tenantID テナントID
     * @param planID プランID
     * @param session セッション
     * @param changePlanID 変更可能プランID
     * @return テナントプラン情報
     */
    public async getTenantPlanforPlanChange(
        tenantID:string,
        planID: string,
        changePlanID: string,
        // tslint:disable-next-line:unified-signatures
        transaction: Transaction,
    ):Promise<TenantPlansEntity>;

    public async getTenantPlanforPlanChange(
        tenantID:string,
        planID: string,
        changePlanID: string,
        transaction?: Transaction,
    ):Promise<TenantPlansEntity>{

        const sequelize = await usePsql();
        if(planID != null && changePlanID != null){
            const option : any = {
                where: {
                    tenant_id : MvnoUtil.addSpaceAfterStr(tenantID, 0),
                    plan_id: parseInt(planID, 10),
                    change_plan_id: parseInt(changePlanID, 10),

                },
            };

            if(transaction){
                option.transaction = transaction;
            }
            return await TenantPlansEntity.findOne(option);
        }
        return null;
    }

    /**
     * SO管理テーブルから当月のプラン変更実施回数を取得する
     * @param tenantId テナントID
     * @param lineId 回線ID
     * @return レコード件数
     */
    public async getPlanChangeKaisu(
        lineId: string,
        beginDate: string,
        endDate: string,
    ): Promise<number>;

    /**
     * SO管理テーブルから当月のプラン変更実施回数を取得する
     * @param tenantId テナントID
     * @param lineId 回線ID
     * @param session セッション
     * @return レコード件数
     */
    public async getPlanChangeKaisu(
        lineId: string,
        beginDate: Date,
        endDate: Date,
        transaction: Transaction,
    ): Promise<number>;

    public async getPlanChangeKaisu(
        lineId: string,
        beginDate: Date | string,
        endDate: Date | string,
        transaction: Transaction = null,
    ):Promise<number>{
        let count:number = 0;
        const sequelize = await usePsql();
        const sql :string[] = [];
        // SQL文
        sql.push("SELECT COUNT(service_order_id) AS COUNT ");
        sql.push("FROM service_orders ");
        sql.push("WHERE line_id = :lineId ");
        sql.push("and order_type = 'プラン変更' ");
        sql.push("and order_status = '完了' ");
        sql.push("and exec_date >= :beginDate ");
        sql.push("and exec_date < :endDate ");

        if((typeof beginDate === "string") && (typeof endDate === "string")){
            if(StringUtils.isNotEmpty(beginDate) && StringUtils.isNotEmpty(endDate)){
                const dstart = parse(beginDate, "yyyy-MM-dd HH:mm:ss", new Date());
                const dexit = parse(endDate, "yyyy-MM-dd HH:mm:ss", new Date());
                const result = (await sequelize.query(sql.join(" "), {
                    replacements: {
                        lineId:MvnoUtil.addSpaceAfterStr(lineId, 0),
                        beginDate: dstart,
                        endDate: dexit,
                    },
                    type: QueryTypes.SELECT,
                })) as any[];
                if (result.length > 0) {
                    count = parseInt(result[0].count, 10);
                }
            }
        } else {
            if(transaction){
                const result = (await sequelize.query(sql.join(" "), {
                    replacements: {
                        lineId,
                        beginDate,
                        endDate,
                    },
                    type: QueryTypes.SELECT,
                    transaction,
                })) as any[];
                if (result.length > 0) {
                    count = parseInt(result[0].count, 10);
                }
            }
        }

        return count;
    }

    public async getOptionPlanTBan(
        optionPlanId: string,
    ): Promise<string> {
        const optionPlan = await OptionPlansEntity.findOne({
            attributes: ["optionPlanIdT"],
            where: {
                optionPlanId: parseInt(optionPlanId, 10),
            },
        });
        if (optionPlan) {
            return optionPlan.optionPlanIdT;
        }
        return null;
    }
}
