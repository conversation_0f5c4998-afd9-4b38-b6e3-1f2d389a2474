import { InvocationContext } from "@azure/functions";
import { QueryTypes, Sequelize } from "sequelize";
import CommonUtil from "../common/CommonUtil";
import { retryQuery } from "@/helpers/queryHelper";
import config from "config";
import AutoApiBatchTimesEntity from "@/core/entity/AutoApiBatchTimesEntity";

/**
 * BatchTimesDaoクラス。<BR>
 *
 * @version 1.00 新規<BR>
 */
export default class BatchTimesDao {

    /**
     * バッチ処理日時検索。<BR>
     *
     * @param context InvocationContext
     * @param sequelize Sequelize instance
     * @return バッチ処理日時
     */
    public static async getAutoApiBatchTimes(
        context: InvocationContext,
        sequelize: Sequelize
    ): Promise<AutoApiBatchTimesEntity> {
        const apDbRetryMaxCnt = config.get<number>("mvno.ApDbRetryMaxCnt");
        const apDbRetryInterval = config.get<number>("mvno.ApDbRetryInterval");

        // const sql = "SELECT pre_exec_time, exec_time FROM auto_api_batch_times";

        const result = await AutoApiBatchTimesEntity.findAll();

        if (result.length === 0) {
            return null;
        } else {
            return AutoApiBatchTimesEntity.build({
                preExecTime: new Date(result[0].preExecTime),
                execTime: new Date(result[0].execTime)
            });
        }
    }

    /**
     * API定期実行バッチ処理日時更新
     *
     * @param context InvocationContext
     * @param sequelize Sequelize instance
     * @param preExecTime 前回実施日時
     * @param execTime 今回実施日時
     */
    public static async updAutoApiBatchTimes(
        context: InvocationContext,
        sequelize: Sequelize,
        preExecTime: string | null,
        execTime: string | null
    ): Promise<void> {
        const apDbRetryMaxCnt = config.get<number>("mvno.ApDbRetryMaxCnt");
        const apDbRetryInterval = config.get<number>("mvno.ApDbRetryInterval");

        if (preExecTime === null && execTime === null) {
            return;
        }

        let sql = "UPDATE auto_api_batch_times SET ";
        const replacements: Record<string, any> = {};

        if (preExecTime !== null && execTime !== null) {
            sql += "exec_time = :execTime, pre_exec_time = :preExecTime";
            replacements.execTime = CommonUtil.convDateFormat(execTime);
            replacements.preExecTime = CommonUtil.convDateFormat(preExecTime);
        } else if (preExecTime !== null) {
            sql += "pre_exec_time = :preExecTime";
            replacements.preExecTime = CommonUtil.convDateFormat(preExecTime);
        } else if (execTime !== null) {
            sql += "exec_time = :execTime";
            replacements.execTime = CommonUtil.convDateFormat(execTime);
        }

        await sequelize.query(sql, {
            replacements,
            type: QueryTypes.UPDATE
        });
    }
}
