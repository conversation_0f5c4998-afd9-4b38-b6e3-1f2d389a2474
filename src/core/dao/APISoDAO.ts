import config from "config";
import { BindOrReplacements, QueryTypes, Op } from "sequelize";

import AbstractMvnoBaseCommon from "@/core/common/AbstractMvnoBaseCommon";

import OptionPlansEntity from "@/core/entity/OptionPlansEntity";
import GroupOptionPlansEntity from "@/core/entity/GroupOptionPlansEntity";
import ServiceOrdersEntity from "@/core/entity/ServiceOrdersEntity";
import { TenantsEntity } from "@/core/entity/TenantsEntity";

import { usePsql } from "@/database/psql";
import { isSQLException, retryQuery } from "@/helpers/queryHelper";
import MvnoUtil from "@/core/common/MvnoUtil";
import StringUtils from "@/core/common/StringUtils";

export default class APISoDAO extends AbstractMvnoBaseCommon {
    /**
     * DBアクセスリトライ回数
     */
    private apDbRetryMaxCnt = config.get<number>("mvno.ApDbRetryMaxCnt");

    /**
     * DBアクセスリトライ間隔
     */
    private apDbRetryInterval = config.get<number>("mvno.ApDbRetryInterval");

    /**
     * オブションプランからオブションプラン説明を取得する
     *
     * @param optionPlanId
     *            オプションプランID
     * @return オプションプラン説明
     */
    public async getOptionPlans(optionPlanId: number): Promise<string | null> {
        let optionPlanDescription = null;

        const found = await retryQuery(
            this.context,
            "getOptionPlans",
            async () =>
                OptionPlansEntity.findOne({
                    where: {
                        optionPlanId,
                    },
                }),
            this.apDbRetryMaxCnt,
            this.apDbRetryInterval,
        );
        if (found) {
            optionPlanDescription = found.optionPlanDescription;
        }
        return optionPlanDescription;
    }

    /**
     * グループオブションプランからオブションプラン説明を取得する
     *
     * @param optionPlanId
     *            オプションプランID
     * @return オプションプラン説明
     */
    public async getGruopOptionPlans(
        optionPlanId: number,
    ): Promise<string | null> {
        let optionPlanDescription = null;

        const found = await retryQuery(
            this.context,
            "getGruopOptionPlans",
            async () =>
                await GroupOptionPlansEntity.findOne({
                    where: {
                        optionPlanId,
                    },
                }),
            this.apDbRetryMaxCnt,
            this.apDbRetryInterval,
        );
        if (found) {
            optionPlanDescription = found.optionPlanDescription;
        }
        return optionPlanDescription;
    }

    /**
     * SO管理テーブルにデータを更新する
     *
     * @param execDate 完了日
     * @param orderStatus オーダステータス
     * @param serviceOrderId サービスオーダID
     * @param detail 変数「内容詳細」
     */
    public async updServiceOrders(
        execDate: Date,
        orderStatus: string,
        serviceOrderId: string,
        detail: string,
    ): Promise<number> {
        this.context.log("APISoDAO.updServiceOrders start", {
            execDate,
            orderStatus,
            serviceOrderId,
            detail,
        });
        const sequelize = await usePsql();
        try {
            const sql = [];
            // SQL文
            sql.push("update service_orders set ");
            // 完了日が空の場合、更新しない
            if (execDate) {
                sql.push("exec_date = :execDate, ");
            }
            sql.push("order_status = :orderStatus");
            // 内容詳細が空の場合、更新しない
            if (detail) {
                sql.push(", content = :detail");
            }
            sql.push(" where service_order_id = :serviceOrderId ");

            const query = sql.join(" ");

            const replacements: BindOrReplacements = {
                orderStatus,
                serviceOrderId,
            };
            // 完了日が空の場合、更新しない
            if (execDate) {
                replacements.execDate = execDate;
            }
            // 内容詳細が空の場合、更新しない
            if (detail) {
                replacements.detail = detail;
            }
            const result = await retryQuery(
                this.context,
                "updServiceOrders",
                async () =>
                    await sequelize.query(query, {
                        replacements,
                        type: QueryTypes.UPDATE,
                    }),
                this.apDbRetryMaxCnt,
                this.apDbRetryInterval,
            );
            return result[1];
        } catch (e: any) {
            if (isSQLException(e)) throw e;
            throw new Error(e);
        }
    }

    /**
     * テナント階層を取得する
     * @param tenantID テナントID
     * @return テナント階層情報、自テナント及び配下テナントのテナントIDリスト。
     */
    public async getTenantLevel(tenantID: string): Promise<string[]> {
        const result = [];
        const sequelize = await usePsql();
        try {
            const sql = [];
            sql.push("WITH RECURSIVE TENANTS_INFO AS ( ");
            sql.push("SELECT tenant_id from tenants ");
            sql.push("where tenant_id=:tenantID");
            sql.push("and status = true ");
            sql.push("union ");
            sql.push("SELECT tenants.tenant_id from tenants, TENANTS_INFO ");
            sql.push("where tenants.p_tenant_id = TENANTS_INFO.tenant_id ");
            sql.push("and tenants.status = true ");
            sql.push(") SELECT * FROM TENANTS_INFO AS tenants");

            const query = sql.join(" ");
            const rows = await retryQuery(
                this.context,
                "getTenantLevel",
                async () =>
                    (await sequelize.query(query, {
                        replacements: {
                            tenantID,
                        },
                        type: QueryTypes.SELECT,
                    })) as any[],
                this.apDbRetryMaxCnt,
                this.apDbRetryInterval,
            );

            for (const row of rows) {
                result.push(row.tenant_id);
            }
        } catch (e: any) {
            if (isSQLException(e)) throw e;
            throw new Error(e);
        }
        return result;
    }

    /**
     * SO管理テーブルにデータを挿入する
     */
    public async insertServiceOrders(soEntity: ServiceOrdersEntity) {
        this.context.log("APISoDAO.insertServiceOrders start", soEntity?.serviceOrderId);
        // await ServiceOrdersEntity.upsert(soEntity);
        await retryQuery(
            this.context,
            "insertServiceOrders",
            async () => await soEntity.save(),
            this.apDbRetryMaxCnt,
            this.apDbRetryInterval,
        );
    }

    /**
     * フロント用予約キャンセル用 予約SOの取得。<BR>
     *
     * @param serviceOrderIdKey サービスオーダID
     * @return ServiceOrdersEntity
     */
    public async getReserveFrontSo(serviceOrderIdKey: string) {
        return await retryQuery(
            this.context,
            "getReserveFrontSo",
            async () =>
                await ServiceOrdersEntity.findOne({
                    where: {
                        serviceOrderId: serviceOrderIdKey,
                        orderStatus: "予約中",
                    },
                }),
            this.apDbRetryMaxCnt,
            this.apDbRetryInterval,
        );
    }

    /**
     * テナント階層を取得する（自テナントおよび自テナント配下のステータスが有効な下位テナントのテナントのレコード）
     * @param lineGroupId 回線グループID
     * @return List<TenantsEntity>
     * @throws SQLException
     * @exception HibernateExceptionの場合
     */
    public async getTenantsList(lineGroupId: string): Promise<TenantsEntity[]> {
        const tenantsList: TenantsEntity[] = [];
        const sequelize = await usePsql();
        return retryQuery(
            this.context,
            "getTenantsList",
            async () => {
                const sql = [];
                sql.push("WITH RECURSIVE TENANTS_INFO AS (");
                sql.push(" SELECT");
                sql.push(" T0.tenant_id");
                sql.push(", T0.tenant_name");
                sql.push(" FROM");
                sql.push(" tenants T0");
                sql.push(" WHERE");
                sql.push(" T0.tenant_id = :lineGroupId");
                sql.push(" AND T0.status = TRUE");
                sql.push(" UNION ALL");
                sql.push(" SELECT");
                sql.push(" T1.tenant_id");
                sql.push(", T1.tenant_name");
                sql.push(" FROM");
                sql.push(" tenants T1");
                sql.push(", TENANTS_INFO T2");
                sql.push(" WHERE");
                sql.push(" T1.p_tenant_id = T2.tenant_id");
                sql.push(" AND T1.status = TRUE");
                sql.push(" )SELECT * FROM TENANTS_INFO AS tenants");

                const query = sql.join(" ");
                const list = await sequelize.query(query, {
                            replacements: {
                                lineGroupId: MvnoUtil.addSpaceAfterStr(lineGroupId, 0),
                            },
                            type: QueryTypes.SELECT,
                        }) as any[];

                for (const row of list) {
                    tenantsList.push(
                        TenantsEntity.build({
                            tenantId: row.tenant_id,
                            tenantName: row.tenant_name,
                        })
                    );
                }
                return tenantsList;
            },
            this.apDbRetryMaxCnt,
            this.apDbRetryInterval
        );
    }

    /**
     * SO一覧表示項目の件数を取得する
     * @param tenantIDList テナントIDリスト
     * @param soID SO-ID
     * @param lineID 回線ID
     * @param status ステータス
     * @param orderDayStart 申込日（始期）
     * @param orderDayEnd 申込日（終期）
     * @param execDayStart 完了日（始期）
     * @param execDayEnd 完了日（終期）
     * @param type 種別
     * @return SO一覧情報リスト
     * @throws SQLException
     */
    public async getSoListDisplayDataCount(
        tenantIDList: string[],
        soID: string,
        lineID: string,
        status: string,
        orderDayStart: string,
        orderDayEnd: string,
        execDayStart: string,
        execDayEnd: string,
        type: string
    ): Promise<number> {
        return retryQuery(
            this.context,
            "getSoListDisplayDataCount",
            async () => {
                const sequelize = await usePsql();
                const tenantIDCondition = tenantIDList.map((tenantId) => `'${tenantId}'`).join(", ");
                const sql = [];
                const sqlConditions = [];
                // All the sqlConditions will be joined by "and", so no need to check length and append "and" manually
                // SO-IDがnullではない場合、SO-IDをwhere条件に追加する
                if (StringUtils.isNotEmpty(soID)) {
                    sqlConditions.push(`service_order_id like '%${soID}%'`);
                }
                // 申込日（始期）がnullではない場合、申込日（始期）をwhere条件に追加する
                if (StringUtils.isNotEmpty(orderDayStart)) {
                    sqlConditions.push(`order_date >= to_timestamp('${orderDayStart}', 'YYYY/MM/DDHH24MISSMS')`);
                }
                // 申込日（終期）がnullではない場合、申込日（終期）をwhere条件に追加する
                if (StringUtils.isNotEmpty(orderDayEnd)) {
                    sqlConditions.push(`order_date < to_timestamp('${orderDayEnd}', 'YYYY/MM/DDHH24MISSMS') + interval '1 days'`);
                }
                // 完了日（始期）がnullではない場合、完了日（始期）をwhere条件に追加する
                if (StringUtils.isNotEmpty(execDayStart)) {
                    sqlConditions.push(`exec_date >= to_timestamp('${execDayStart}', 'YYYY/MM/DDHH24MISSMS')`);
                }
                // 完了日（終期）がnullではない場合、完了日（終期）をwhere条件に追加する
                if (StringUtils.isNotEmpty(execDayEnd)) {
                    sqlConditions.push(`exec_date < to_timestamp('${execDayEnd}', 'YYYY/MM/DDHH24MISSMS') + interval '1 days'`);
                }
                // 種別がnullではない場合、種別をwhere条件に追加する
                if (StringUtils.isNotEmpty(type)) {
                    sqlConditions.push(`order_type = '${type}'`);
                }
                // ステータスがnullではない場合、ステータスをwhere条件に追加する
                if (StringUtils.isNotEmpty(status)) {
                    sqlConditions.push(`order_status = '${status}'`);
                }
                // 回線IDがnullではない場合、回線IDをwhere条件に追加する
                if (StringUtils.isNotEmpty(tenantIDCondition)) {
                    sqlConditions.push(`tenant_id in (${tenantIDCondition})`);         // no need to substring 0, -2 as ", " did not append after the last element (join)
                }
                // 回線IDがnullではない場合、回線IDをwhere条件に追加する
                if (StringUtils.isNotEmpty(lineID)) {
                    sqlConditions.push(`line_id like '%${lineID}%'`);
                }
                // 仮登録回線追加機能、回線廃止機能、回線SIM再発行機能、回線オプション_NW暗証番号変更機能、回線黒化機能のSOを検索対象から除外する
                sqlConditions.push("function_Type NOT IN ( '51','52','53','54','55' )");

                sql.push("select count(*) from service_orders");
                if (sqlConditions.length > 0) {
                    sql.push(`where ${sqlConditions.join(" and ")}`);
                }

                const query = sql.join(" ");
                const result: any[] = await sequelize.query(query, {
                    type: QueryTypes.SELECT,
                });
                return parseInt(result[0].count, 10);
            },
            this.apDbRetryMaxCnt,
            this.apDbRetryInterval
        );
    }

    /**
     * SO一覧情報を取得する
     * @param tenantIDList テナントIDリスト
     * @param soID SO-ID
     * @param lineID 回線ID
     * @param status ステータス
     * @param orderDayStart 申込日（始期）
     * @param orderDayEnd 申込日（終期）
     * @param execDayStart 完了日（始期）
     * @param execDayEnd 完了日（終期）
     * @param type 種別
     * @param isPrivate trueの場合、内部呼び出し。falseの場合、外部呼出し
     * @param searchCount 検索件数
     * @return SO一覧情報リスト
     * @throws SQLException
     */
    public async getSoList(
        tenantIDList: string[],
        soID: string,
        lineID: string,
        status: string,
        orderDayStart: string,
        orderDayEnd: string,
        execDayStart: string,
        execDayEnd: string,
        type: string,
        isPrivate: boolean = false,     // probably does not exist anymore
        searchCount: number = 0
    ): Promise<ServiceOrdersEntity[]> {
        return retryQuery(
            this.context,
            "getSoList",
            async () => {
                const sequelize = await usePsql();
                const sql = [];
                const sqlConditions = [];
                // All the sqlConditions will be joined by "and", so no need to check length and append "and" manually
                // No + 24 * 60 * 60 * 1000 as there will have + interval 1 days later
                // SO-IDがnullではない場合、SO-IDをwhere条件に追加する
                if (StringUtils.isNotEmpty(soID)) {
                    sqlConditions.push(`service_order_id like '%${soID}%'`);
                }
                // 申込日（始期）がnullではない場合、申込日（始期）をwhere条件に追加する
                if (StringUtils.isNotEmpty(orderDayStart)) {
                    sqlConditions.push(`order_date >= to_timestamp('${orderDayStart}', 'YYYY/MM/DDHH24MISSMS')`);
                }
                // 申込日（終期）がnullではない場合、申込日（終期）をwhere条件に追加する
                if (StringUtils.isNotEmpty(orderDayEnd)) {
                    sqlConditions.push(`order_date < to_timestamp('${orderDayEnd}', 'YYYY/MM/DDHH24MISSMS') + interval '1 days'`);
                }
                // 完了日（始期）がnullではない場合、完了日（始期）をwhere条件に追加する
                if (StringUtils.isNotEmpty(execDayStart)) {
                    sqlConditions.push(`exec_date >= to_timestamp('${execDayStart}', 'YYYY/MM/DDHH24MISSMS')`);
                }
                // 完了日（終期）がnullではない場合、完了日（終期）をwhere条件に追加する
                if (StringUtils.isNotEmpty(execDayEnd)) {
                    sqlConditions.push(`exec_date < to_timestamp('${execDayEnd}', 'YYYY/MM/DDHH24MISSMS') + interval '1 days'`);
                }
                // 種別がnullではない場合、種別をwhere条件に追加する
                if (StringUtils.isNotEmpty(type)) {
                    sqlConditions.push(`order_type = '${type}'`);
                }
                // ステータスがnullではない場合、ステータスをwhere条件に追加する
                if (StringUtils.isNotEmpty(status)) {
                    sqlConditions.push(`order_status = '${status}'`);
                }
                // テナントIDがnullではない場合、テナントIDをwhere条件に追加する
                if (tenantIDList != null && tenantIDList.length > 0) {
                    sqlConditions.push(`tenant_id in (:tenantIDList)`);
                }
                // 回線IDがnullではない場合、回線IDをwhere条件に追加する
                if (StringUtils.isNotEmpty(lineID)) {
                    sqlConditions.push(`line_id like '%${lineID}%'`);
                }
                // 仮登録回線追加機能、回線廃止機能、SIM再発行機能、回線オプション_NW暗証番号変更機能、回線黒化機能のSOを検索対象から除外する
                sqlConditions.push("function_Type NOT IN ( '51','52','53','54','55' )");

                sql.push("select * from service_orders");

                if (sqlConditions.length > 0) {
                    sql.push(`where ${sqlConditions.join(" and ")}`);
                }
                sql.push("order by order_date desc");

                // 内部が呼び出す場合
                if (isPrivate && searchCount > 0) {
                    sql.push(`limit ${searchCount}`);
                }

                const query = sql.join(" ");
                let list: any[];
                if (tenantIDList != null && tenantIDList.length > 0) {
                    list = await sequelize.query(query, {
                        replacements: {
                            tenantIDList
                        },
                        type: QueryTypes.SELECT,
                    }) as any[];
                } else {
                    list = await sequelize.query(query, {
                        type: QueryTypes.SELECT,
                    }) as any[];
                }

                const resultList: ServiceOrdersEntity[] = [];
                for (const row of list) {
                    const soEntity = ServiceOrdersEntity.build({
                        serviceOrderId: row.service_order_id,
                        orderDate: row.order_date,
                        reserveDate: row.reserve_date,
                        execDate: row.exec_date,
                        functionType: row.function_type,
                        orderType: row.order_type,
                        orderStatus: row.order_status,
                        lineId: row.line_id,
                        tenantId: row.tenant_id,
                        execUserId: row.exec_user_id,
                        execTenantId: row.exec_tenant_id,
                        content: row.content,
                        internalFlag: row.internal_flag,
                        restMessage: row.rest_message,
                        decisionType: row.judge_type,
                        groupId: row.group_id,
                    });
                    resultList.push(soEntity);
                }
                return resultList;
            },
            this.apDbRetryMaxCnt,
            this.apDbRetryInterval
        );
    }

    /**
     * SO詳細情報を取得する
     * @param soID
     * @param tenantIDList
     * @return SO管理テーブルエンティティ
     * @throws SQLException
     */
    public async getSOInfo(soID: string, tenantIDList: string[]): Promise<ServiceOrdersEntity | null> {
        if (tenantIDList != null && tenantIDList.length !== 0 && tenantIDList[0] != null) {
            return retryQuery(
                this.context,
                "getSOInfo",
                async () => {
                    return ServiceOrdersEntity.findOne({
                        where: {
                            serviceOrderId: soID,
                            tenantId: tenantIDList,                             // automatically converted to "in" query
                            functionType: {
                                [Op.notIn]: ["51", "52", "53", "54", "55"],
                            }
                        },
                    });
                },
                this.apDbRetryMaxCnt,
                this.apDbRetryInterval
            );
        } else {
            return null;
        }
    }

    /**
     * 指定テナントIDの子テナントを取得する<BR>
     *
     * @param tenantId
     *            String テナントID
     * @return List&lt;String&gt; 子テナントIDリスト
     * @throws SQLException
     * @exception HibernateExceptionの場合
     */
    public async getSonTenant(tenantId: string): Promise<string[]> {
        return retryQuery(
            this.context,
            "getSonTenant",
            async () => {
                const result = await TenantsEntity.findAll({
                    where: {
                        pTenantId: tenantId,
                        status: true,
                    },
                    attributes: ["tenantId"],                                   // SELECT
                })
                return result.map((tenant) => tenant.tenantId);
            },
            this.apDbRetryMaxCnt,
            this.apDbRetryInterval
        );
    }

    public async getCancelObj(soid:string):Promise<ServiceOrdersEntity | null>{
        return await retryQuery(
            this.context,
            "getCancelObj",
            async () =>
                await ServiceOrdersEntity.findOne({
                    where: {
                        serviceOrderId: soid,
                        orderStatus: '予約中',
                    },
                }),
            this.apDbRetryMaxCnt,
            this.apDbRetryInterval,
        );
    }

    /**
     * 予約中のサービスオーダIDを取得する<BR>
     *
     * @param groupId String 回線グループID
     * @return List<String> サービスオーダIDリスト
     * @throws SQLException
     */
    public async getReserveOrderGroupId(groupId: string) {
        const sequelize = await usePsql();
        return await retryQuery(
            this.context,
            "getReserveOrderGroupId",
            async () => {
                const result: string[] = [];

                const sql: string[] = [];

                sql.push("select service_order_id ");
                sql.push("from service_orders ");
                sql.push("where order_type = '回線グループプラン変更' ");
                sql.push("and order_status = '予約中' ");
                sql.push("and group_id = :group_id ");

                const queryResult = (await sequelize.query(sql.join(" "), {
                    replacements: {
                        group_id: groupId,
                    },
                    type: QueryTypes.SELECT,
                })) as any[];

                for (const row of queryResult) {
                    result.push(row.service_order_id);
                }
                return result;
            },
            this.apDbRetryMaxCnt,
            this.apDbRetryInterval,
        );
    }

    /**
     * サービスオーダIDを取得する<BR>
     * @param lineId String 回線ID
     * @return List&lt;String&gt; サービスオーダIDリスト
     * @throws SQLException
     */
    public async getReserveOrder(lineId: string) {
        const sequelize = await usePsql();
        return await retryQuery(
            this.context,
            "getReserveOrder",
            async () => {
                const result: string[] = [];

                const sql: string[] = [];

                sql.push("select service_order_id ");
                sql.push("from service_orders ");
                sql.push("where order_type = 'プラン変更' ");
                sql.push("and order_status = '予約中' ");
                sql.push("and line_id = :lineID ");

                const queryResult = (await sequelize.query(sql.join(" "), {
                    replacements: {
                        lineID: lineId,
                    },
                    type: QueryTypes.SELECT,
                })) as any[];

                for (const row of queryResult) {
                    result.push(row.service_order_id);
                }
                return result;
            },
            this.apDbRetryMaxCnt,
            this.apDbRetryInterval,
        );
    }
}
