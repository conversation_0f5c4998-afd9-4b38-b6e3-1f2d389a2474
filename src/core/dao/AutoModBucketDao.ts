import { InvocationContext } from "@azure/functions";
import { QueryTypes, Sequelize } from "sequelize";
import CommonUtil from "../common/CommonUtil";

/**
 * 回線グループ基本容量自動計算DAOクラス。<BR>
 *
 * @version 1.00 新規<BR>
 */
export default class AutoModBucketDao {

    /**
     * 基本容量自動計算対象グループID取得
     * @param context InvocationContext
     * @param sequelize Sequelize instance
     * @param preExecTime 前回実施日時
     * @param execTime 今回実施日時
     * @return 取得したレコードList
     */
    public static async getAutoModBucketGroupList(
        context: InvocationContext,
        sequelize: Sequelize,
        preExecTime: string,
        execTime: string
    ): Promise<{group_id: string, tenant_id: string}[]> {
        try {
            const sql = [
                "SELECT DISTINCT ",
                "LG.group_id as group_id",
                ", LG.tenant_id as tenant_id",
                "FROM ",
                "auto_modbucket_line_groups AG ",
                ", line_groups LG ",
                ", group_plans GP ",
                "WHERE AG.created_at >= :preExecTime ",
                "AND AG.created_at < :execTime ",
                "AND LG.group_id = AG.group_id ",
                "AND LG.plan_id = GP.plan_id ",
                "AND LG.status = 1 ",
                "AND GP.plan_class = 1 ",
                "AND EXISTS (SELECT 1 FROM line_line_groups LL WHERE LL.group_id = AG.group_id )",
                "ORDER BY LG.group_id ASC"
            ].join(" ");

            const preExecDate = CommonUtil.convDateFormat(preExecTime);
            const execDate = CommonUtil.convDateFormat(execTime);

            const result = await sequelize.query(sql, {
                replacements: {
                    preExecTime: preExecDate,
                    execTime: execDate
                },
                type: QueryTypes.SELECT
            }) as {group_id: string, tenant_id: string}[];

            return result;
        } catch (e) {
            context.error("AutoModBucketDao.getAutoModBucketGroupList error:", e);
            throw e;
        }
    }

    /**
     * ハッシュ化パスワードの取得
     * @param context InvocationContext
     * @param sequelize Sequelize instance
     * @param tenantId テナントID
     * @return ハッシュ化パスワード
     */
    public static async getTenantHashedPassword(
        context: InvocationContext,
        sequelize: Sequelize,
        tenantId: string
    ): Promise<string | null> {
        try {
            const sql = "SELECT hashed_password FROM tenants WHERE tenant_id = :tenantId";

            const result = await sequelize.query(sql, {
                replacements: { tenantId },
                type: QueryTypes.SELECT
            }) as Record<string, any>[];

            if (result.length === 0 || result[0].hashed_password === null) {
                return null;
            } else {
                return result[0].hashed_password as string;
            }
        } catch (e) {
            context.error("AutoModBucketDao.getTenantHashedPassword error:", e);
            throw e;
        }
    }

    /**
     * 実行ユーザIDの更新
     * @param context InvocationContext
     * @param sequelize Sequelize instance
     * @param soId サービスオーダID
     */
    public static async updServiceOrderExecUserId(
        context: InvocationContext,
        sequelize: Sequelize,
        soId: string
    ): Promise<void> {
        try {
            const sql = "UPDATE service_orders SET exec_user_id = 'システム自動実行' WHERE service_order_id = :soId";

            await sequelize.query(sql, {
                replacements: { soId },
                type: QueryTypes.UPDATE
            });
        } catch (e) {
            context.error("AutoModBucketDao.updServiceOrderExecUserId error:", e);
            throw e;
        }
    }

    /**
     * 自動計算回線グループデータの削除
     * @param context InvocationContext
     * @param sequelize Sequelize instance
     * @param targetDate 基準日
     */
    public static async delAutoModBucketGroupList(
        context: InvocationContext,
        sequelize: Sequelize,
        targetDate: Date
    ): Promise<void> {
        try {
            const sql = "DELETE FROM auto_modbucket_line_groups WHERE created_at < :targetDate";

            await sequelize.query(sql, {
                replacements: { targetDate },
                type: QueryTypes.DELETE
            });
        } catch (e) {
            context.error("AutoModBucketDao.delAutoModBucketGroupList error:", e);
            throw e;
        }
    }
}