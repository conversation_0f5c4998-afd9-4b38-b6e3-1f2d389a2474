import config from "config";
import { fromUnixTime } from "date-fns";
import { InferAttributes, QueryTypes, Transaction, Sequelize } from "sequelize";

import AbstractMvnoBaseCommon from "@/core/common/AbstractMvnoBaseCommon";
import CheckUtil from "@/core/common/CheckUtil";
import CardEntity from "@/core/entity/CardEntity";
import { CustomerInfoEntity } from "@/core/entity/CustomerInfoEntity";
import { LinesEntity } from "@/core/entity/LinesEntity";
import { TenantNnumbersEntity } from "@/core/entity/TenantNnumbersEntity";
import PlansEntity from "@/core/entity/PlansEntity";
import { usePsql } from "@/database/psql";
import { isSQLException, retryQuery } from "@/helpers/queryHelper";
import PreLineAddInputDto from "@/core/dto/PreLineAddInputDto";
import LineOptionsEntity from "@/core/entity/LineOptionsEntity";
import TenantPlanLineOptionsEntity from "@/core/entity/TenantPlanLineOptionsEntity";
import LineTrafficsEntity from "@/core/entity/LineTrafficsEntity";
import MvnoUtil from "@/core/common/MvnoUtil";

export default class APILinesDAO extends AbstractMvnoBaseCommon {
    /**
     * DBアクセスリトライ回数
     */
    private apDbRetryMaxCnt = config.get<number>("mvno.ApDbRetryMaxCnt");

    /**
     * DBアクセスリトライ間隔
     */
    private apDbRetryInterval = config.get<number>("mvno.ApDbRetryInterval");

    /** STEP4.0版対応 変更 START */
    /**
     * 回線番号を元に回線情報を取得する。<BR>
     * @param lineNo 回線番号
     * @return LinesEntity
     * @exception HibernateExceptionの場合
     */
    public async getLineInfoByLines(lineNo: string): Promise<LinesEntity> {
        try {
            return await retryQuery(
                this.context,
                "getLineInfoByLines",
                async () =>
                    await LinesEntity.findOne({
                        where: {
                            lineId: lineNo,
                        },
                    }),
                this.apDbRetryMaxCnt,
                this.apDbRetryInterval,
            );
        } catch (e: any) {
            if (isSQLException(e)) throw e;
            throw new Error(e);
        }
    }

    /**
     * 対象回線の廃止オーダを検索する
     *
     * @param lineNo 回線番号
     * @param reserveSoId 予約時のSO-ID
     * @return 廃止オーダリスト
     */
    public async getAbolitionOrder(
        lineNo: string,
        reserveSoId: string,
    ): Promise<string[]> {
        const abolitionOrder: string[] = [];
        const sequelize = await usePsql();
        try {
            // StringBuilder sql = new StringBuilder();
            const sql = [];
            // SQL文
            sql.push("SELECT s.service_order_id ");
            sql.push("FROM lines as l, service_orders as s ");
            sql.push("WHERE s.function_type = '52' ");
            sql.push("and s.line_id = :lineNo ");
            sql.push("and s.line_id = l.line_id ");
            sql.push("and ");
            sql.push("( ");
            sql.push("( ");
            sql.push("s.order_status = '完了' ");
            sql.push("and s.exec_date >= l.open_date ");
            sql.push(") ");
            sql.push("or ");
            sql.push("( ");
            sql.push("s.order_status = '予約中' ");
            sql.push("and s.reserve_date >= l.open_date ");
            sql.push(") ");
            sql.push(") ");

            if (!CheckUtil.checkIsNotNull(reserveSoId)) {
                sql.push("and s.service_order_id not in (:reserveSoId) ");
            }

            const query = sql.join(" ");

            // 回線番号を取得する
            const result = await retryQuery(
                this.context,
                "getAbolitionOrder",
                async () =>
                    (await sequelize.query(query, {
                        replacements: {
                            lineNo,
                            reserveSoId,
                        },
                        type: QueryTypes.SELECT,
                    })) as any[],
                this.apDbRetryMaxCnt,
                this.apDbRetryInterval,
            );
            // 値がある場合
            if (result.length > 0) {
                for (const row of result) {
                    abolitionOrder.push(row.service_order_id);
                }
            }
        } catch (e: any) {
            if (isSQLException(e)) throw e;
            throw new Error(e);
        }
        return abolitionOrder;
    }

    /**
     * 回線番号をキーに、SO管理テーブルの情報を取得する。<BR>
     *
     * @param lineNo 回線ID
     * @param targetDate 現在システム日付。以下のフォーマットをサポートする。<BR>
     *                   YYYYMMDD<BR>
     *                   YYYY/MM/DD HH24:MI:SS
     * @return サービスオーダIDリスト
     */
    public async getReservedServiceOrder(
        lineNo: string,
        targetDate: string,
    ): Promise<string[]> {
        const getReservedOrderList: string[] = [];
        const sequelize = await usePsql();

        try {
            // SQL文
            const sql =
                "SELECT " +
                "  service_order_id " +
                "FROM " +
                "  service_orders " +
                "WHERE " +
                "  line_id = :lineNo " +
                "AND " +
                "  order_status = '予約中' " +
                "AND " +
                "  date_trunc('day', reserve_date) >= date_trunc('day', cast(:targetDate AS timestamp))";
            // サービスオーダIDを取得する
            const result = await retryQuery(
                this.context,
                "getReservedServiceOrder",
                async () =>
                    (await sequelize.query(sql, {
                        replacements: {
                            lineNo, // 回線ID
                            targetDate, // 現在システム日付
                        },
                        type: QueryTypes.SELECT,
                    })) as any[],
                this.apDbRetryMaxCnt,
                this.apDbRetryInterval,
            );
            // 値がある場合
            for (const row of result) {
                getReservedOrderList.push(row.service_order_id);
            }
        } catch (e: any) {
            if (isSQLException(e)) throw e;
            throw new Error(e);
        }

        return getReservedOrderList;
    }

    /**
     * 回線番号をキーに、SO管理テーブルの情報を取得する。<BR>
     *
     * NOTE: added during refactoring to include order_type in the result
     * @param lineNo 回線ID
     * @param targetDate 現在システム日付。以下のフォーマットをサポートする。<BR>
     *                   YYYYMMDD<BR>
     *                   YYYY/MM/DD HH24:MI:SS
     * @return サービスオーダIDリスト
     */
    public async getReservedServiceOrderWithOrderType(
        lineNo: string,
        targetDate: string,
    ) {
        // const getReservedOrderList: string[] = [];
        const getReservedOrderList: { service_order_id: string; order_type: string }[] = [];
        const sequelize = await usePsql();

        try {
            // SQL文
            const sql =
                "SELECT " +
                "  service_order_id, order_type " +
                "FROM " +
                "  service_orders " +
                "WHERE " +
                "  line_id = :lineNo " +
                "AND " +
                "  order_status = '予約中' " +
                "AND " +
                "  date_trunc('day', reserve_date) >= date_trunc('day', cast(:targetDate AS timestamp))";
            // サービスオーダIDを取得する
            const result = await retryQuery(
                this.context,
                "getReservedServiceOrderWithOrderType",
                async () =>
                    (await sequelize.query(sql, {
                        replacements: {
                            lineNo, // 回線ID
                            targetDate, // 現在システム日付
                        },
                        type: QueryTypes.SELECT,
                    })) as any[],
                this.apDbRetryMaxCnt,
                this.apDbRetryInterval,
            );
            // 値がある場合
            for (const row of result) {
                getReservedOrderList.push({
                    service_order_id: row.service_order_id,
                    order_type: row.order_type,
                });
            }
        } catch (e: any) {
            if (isSQLException(e)) throw e;
            throw new Error(e);
        }

        return getReservedOrderList;
    }

    /**
     * データ譲渡回線テーブルに譲渡元回線番号が存在するか確認する
     *
     * @param lineID 回線ID
     * @param transaction トランザクション
     * @return String
     *
     */
    public async getGiftLine(lineID: string, transaction: Transaction): Promise<string> {
        const sequelize = await usePsql();
        const sql =
            "SELECT line_id FROM gift_line WHERE line_id = :lineID";
        const result = await sequelize.query(sql, {
            replacements: { lineID },
            transaction,
            type: QueryTypes.SELECT,
        });
        if (result.length === 1) {
            return (result[0] as { lineId: string }).lineId;
        }
        return null;
    }

    /**
     * データ譲渡回線テーブルに対して譲渡元回線IDを追加する
     * @param  lineId 回線ID
     * @param  soId SO-ID
     * @param  createdAt                    created_at Note: timestamp in millisecond
     * @param  transaction トランザクション
     * @return 処理結果
     *
     */
    public async insertGiftLineNotDup(
        lineId: string,
        soId: string,
        createdAt: number,
        transaction: Transaction,
    ): Promise<boolean> {
        try {

            const strSqlSel = [];
            const strSqlVal = [];

            strSqlSel.push("INSERT INTO ");
            strSqlSel.push("gift_line ");
            strSqlSel.push(" (");
            strSqlVal.push(" VALUES ");
            strSqlVal.push(" (");

            strSqlSel.push("line_id");
            strSqlVal.push(":line_id");

            strSqlSel.push(", service_order_id");
            strSqlVal.push(", :service_order_id");

            strSqlSel.push(", created_at");
            strSqlVal.push(", :created_at");
            strSqlVal.push(") ");
            strSqlSel.push(") ");

            const strSql = strSqlSel.join(" ") + strSqlVal.join(" ");

            const sequelize = await usePsql();
            await sequelize.query(strSql, {
                replacements: {
                    line_id: lineId,
                    service_order_id: soId,
                    created_at: new Date(createdAt),
                },
                transaction,
                type: QueryTypes.INSERT,
            });

            // STEP20.0版対応 追加 START
        } catch (e: any) {
            if (isSQLException(e) && e.name === 'SequelizeUniqueConstraintError') {
                return false;
            }
            throw new Error(e);
        }
        return true;
    }

    /**
     * データ削除を実施する
     * @param  soId SO-ID
     * @param  transaction トランザクション
     */
    public async deleteGiftLine(soId: string, transaction: Transaction): Promise<void> {
        const sequelize = await usePsql();
        const sql = "DELETE FROM " +
            "gift_line " +
            " WHERE " +
            "service_order_id = :service_order_id";
        await sequelize.query(sql, {
            replacements: { service_order_id: soId },
            transaction,
            type: QueryTypes.DELETE,
        });
    }

    /**
     * カード種別IDを元に、カード種別名を取得する
     * @param deviceTypeId カード種別ID
     * @return カード種別名
     */
    public async getCard(deviceTypeId: string): Promise<CardEntity | null> {
        return await retryQuery(
            this.context,
            "getCard",
            async () =>
                await  CardEntity.findOne({
                    where: {
                        deviceTypeId,
                    },
                }),
            this.apDbRetryMaxCnt,
            this.apDbRetryInterval,
        );
    }

    /**
     * 回線情報の、SIM再発行に関する情報を更新する
     * @param linesEntity 更新用情報
     */
    public async updateLineSimInfo(linesEntity: LinesEntity): Promise<void> {
        // NOTE since we update partially, can't use linesEntity.save()
        this.context.log("updateLineSimInfo start", {
            lineId: linesEntity.lineId,
            simNumber: linesEntity.simNumber,
            modelType: linesEntity.modelType,
            simType: linesEntity.simType,
            deviceTypeId: linesEntity.deviceTypeId,
            updatedAt: linesEntity.updatedAt,
        });
        const res = await LinesEntity.update(
            {
                simNumber: linesEntity.simNumber,
                modelType: linesEntity.modelType,
                simType: linesEntity.simType,
                deviceTypeId: linesEntity.deviceTypeId,
                updatedAt: linesEntity.updatedAt,
            },
            {
                where: {
                    lineId: linesEntity.lineId,
                },
                returning: false,
            },
        );
        this.context.warn("updateLineSimInfo affected:", res[0]);
    }

    /**
     * 回線情報を更新する
     *
     * @param lineNo 回線番号
     * @param optionUpdateInfo 回線オプション更新情報
     * @param optionUpdateKubn 回線オプション更新要否
     */
    public async updateLineOptionInfo(
        lineNo: string,
        optionUpdateInfo: LinesEntity,
        optionUpdateKubn: Map<string, boolean>,
    ): Promise<void> {
        this.context.log("updateLineOptionInfo start", {
            lineNo,
            update: JSON.stringify(optionUpdateKubn),
        });
        // if all values are false, then return
        if (!Array.from(optionUpdateKubn.values()).some((v) => v)) {
            this.context.warn("updateLineOptionInfo: nothing to update");
            return;
        }
        /** for storing updated values */
        const update: Partial<InferAttributes<LinesEntity>> = {};

        const intlRoaming = "intlRoaming"; // 国際ローミング利用限度額ID
        const voicemail = "voicemail"; // 留守番でんわID
        const callWaiting = "callWaiting"; // キャッチホンID
        const intlCall = "intlCall"; // 国際電話ID
        const forwarding = "forwarding"; // 転送でんわID
        const intlForwarding = "intlForwarding"; // 国際着信転送ID

        if (optionUpdateKubn.get(intlRoaming)) {
            update.roamingMaxId = optionUpdateInfo.roamingMaxId;
        }
        if (optionUpdateKubn.get(voicemail)) {
            update.voiceMailId = optionUpdateInfo.voiceMailId;
        }
        if (optionUpdateKubn.get(callWaiting)) {
            update.callWaitingId = optionUpdateInfo.callWaitingId;
        }
        if (optionUpdateKubn.get(intlCall)) {
            update.intlCallId = optionUpdateInfo.intlCallId;
        }
        if (optionUpdateKubn.get(forwarding)) {
            update.forwardingId = optionUpdateInfo.forwardingId;
        }
        if (optionUpdateKubn.get(intlForwarding)) {
            update.intlForwardingId = optionUpdateInfo.intlForwardingId;
        }
        update.updatedAt = optionUpdateInfo.updatedAt;

        const res = await retryQuery(
            this.context,
            "updateLineOptionInfo",
            async () =>
                await LinesEntity.update(update, {
                    where: {
                        lineId: lineNo,
                    },
                    returning: false,
                }),
            this.apDbRetryMaxCnt,
            this.apDbRetryInterval,
        );
        this.context.warn("updateLineOptionInfo affected:", res[0]);
    }

    /**
     * 回線のプラン情報を取得する。<BR>
     * @param planId String プランID
     * @return List
     */
    public async getPlanInfo(planId: string,transaction: Transaction = null ): Promise<PlansEntity[]> {
        if (transaction){
            const option: any = {
                where: {
                    pricePlanId: planId,
                },
                transaction,
            };
            return await PlansEntity.findAll(option);
        }
        return await retryQuery(
            this.context,
            "getPlanInfo",
            async () =>
                await PlansEntity.findAll({
                    where: {
                        pricePlanId: planId,
                    },
                }),
            this.apDbRetryMaxCnt,
            this.apDbRetryInterval,
        );
    }

    /**
     * 指定された回線IDの利用状態を更新する。<BR>
     *
     * @param lineNo 電文中の回線番号
     * @param usageStatus 更新する利用状態
     * @param systemDateTime 更新日時(Timestamp{yyyy/MM/dd HH:mm:ss})
     * @param transaction トランザクション
     * @return 更新件数
     */
    public async updateLineUsageStatus(
        lineNo: string,
        usageStatus: number,
        systemDateTime: number,
        transaction: Transaction,
    ): Promise<number> {
        this.context.log("updateLineUsageStatus start", {
            lineNo,
            usageStatus,
            systemDateTime,
        });
        const sequelize = await usePsql();
        // SQL文
        const sql =
            "UPDATE " +
            "  lines " +
            "SET " +
            "   usage_status = :usage_status " +
            "  ,updated_at = :updated_at " +
            "WHERE " +
            "  line_id = :lineId ";

        const result = await sequelize.query(sql, {
            replacements: {
                usage_status: usageStatus,
                updated_at: fromUnixTime(systemDateTime / 1000), // updated_at
                lineId: lineNo,
            },
            transaction,
            type: QueryTypes.UPDATE,
        });

        return result[1];
    }

    /**
     * カード種別IDを元にカード情報を取得する<BR>
     *
     * @param cardTypeId 回線ID
     * @return CardEntity
     */
    public async getCardTypeInfo(cardTypeId: string) {
        if (cardTypeId === null || cardTypeId === undefined) {
            return null;
        }
        return await retryQuery(
            this.context,
            "getCardTypeInfo",
            async () =>
                await CardEntity.findOne({
                    where: {
                        deviceTypeId: cardTypeId,
                    },
                }),
            this.apDbRetryMaxCnt,
            this.apDbRetryInterval,
        );
    }

    /**
     * 回線情報を行ロックし、取得する<BR>
     * @param {string} lineId 回線番号
     * @param {Transaction} transaction トランザクション
     * @return 回線グループID String (見つからない場合は`null`)
     * @throws SequelizeDatabaseErrorなど
     */
    public async getLineInfoforUpdate(
        lineId: string,
        transaction: Transaction,
    ): Promise<string | null> {
        const sequelize = await usePsql();
        const query =
            "select line_id from lines where line_id = :lineId and line_status not in ('03') for update nowait";
        const result = (await sequelize.query(query, {
            replacements: { lineId },
            type: QueryTypes.SELECT,
            transaction,
        })) as any[];
        if (result.length === 0) {
            return null;
        }
        return result[0].line_id;
    }

    /**
     * 指定された回線IDの契約種別、カード種別を更新する。<BR>
     *
     * @param lineNo 電文中の回線番号
     * @param contractType 更新する契約種別
     * @param deviceTypeId 更新する端末種別ID
     * @param modelType 更新する端末種別名
     * @param systemDateTime 更新日時(Timestamp{yyyy/MM/dd HH:mm:ss}) (millisecond)
     * @param session セッション
     * @return 更新件数
     */
    public async updateLineContractDeviceType(
        lineNo: string,
        contractType: string,
        deviceTypeId: string,
        modelType: string,
        systemDateTime: number,
        transaction: Transaction,
    ): Promise<number> {
        this.context.log("updateLineContractDeviceType start", {
            lineNo,
            contractType,
            deviceTypeId,
            modelType,
            systemDateTime,
        });
        const sequelize = await usePsql();
        // SQL文
        const sql =
            "UPDATE " +
            "  lines " +
            "SET " +
            "   contract_type = :contractType " +
            "  ,device_type_id = :deviceTypeId " +
            "  ,model_type = :modelType " +
            "  ,nw_modify_flag = true " +
            "  ,updated_at = :updated_at " +
            "WHERE " +
            "  line_id = :lineId ";

        const result = await sequelize.query(sql, {
            replacements: {
                contractType, // 契約種別
                deviceTypeId, // 端末種別ID
                modelType, // 端末種別名
                updated_at: fromUnixTime(systemDateTime / 1000), // updated_at
                lineId: lineNo, // 回線ID
            },
            transaction,
            type: QueryTypes.UPDATE,
        });

        return result[1];
    }

    /**
     * 回線種別、MNP転出オプションID、レコード種別を取得する
     *
     * @param lineId 回線ID
     * @return CustomerInfoEntity
     *
     */
    public async getLineCustomerInfo(
        lineId: string,
    ): Promise<CustomerInfoEntity> {
        const sequelize = await usePsql();
        // raw modified query
        const sql =
            "SELECT c.* FROM customer_info c, lines l WHERE c.nnumber = l.nnumber AND l.line_id = :lineId";
        return await retryQuery(
            this.context,
            "getLineCustomerInfo",
            async () =>
                await sequelize.query(sql, {
                    replacements: { lineId },
                    type: QueryTypes.SELECT,
                    mapToModel: true,
                    model: CustomerInfoEntity,
                    plain: true,
                }),
            this.apDbRetryMaxCnt,
            this.apDbRetryInterval,
        );
    }

    // 	#417対応
    /** STEP1.2a版対応 追加 END */

    /** #162対応 追加 START */
    /**
     * テナントID、N番を取得する。<BR>
     * @param tenantId String テナントID
     * @param nNumber String 代表N番
     * @return TenantNnumbersEntity
     * @exception HibernateExceptionの場合
     */
    public async getTenantNnumbers(
        tenantId: string,
        nNumber: string,
    ): Promise<TenantNnumbersEntity> {
        try {
            return await retryQuery(
                this.context,
                "getTenantNnumbers",
                async () =>
                    await TenantNnumbersEntity.findOne({
                        where: {
                            nnumber: nNumber,
                            tenantId,
                        },
                    }),
                this.apDbRetryMaxCnt,
                this.apDbRetryInterval,
            );
        } catch (e: any) {
            if (isSQLException(e)) throw e;
            throw new Error(e);
        }
    }

    /**
     * 指定された回線IDの回線情報を廃止状態(最終オーダ種別を'03')へ更新する。<BR>
     *
     * @param transaction トランザクション
     * @param lineNo 電文中の回線番号(String)
     * @param systemDateTime 個別機能のオーダ受付日時(Timestamp{yyyy/MM/dd HH:mm:ss})
     */
    public async updateLineInfoLineDeleteStatus(
        transaction: Transaction,
        lineNo: string,
        systemDateTime: number,
    ): Promise<void> {
        this.context.log("updateLineInfoLineDeleteStatus start", {
            lineNo,
            systemDateTime,
        });
        const sequelize = await usePsql();

        // SQL文
        const sql =
            "UPDATE " +
            "  lines " +
            "SET " +
            "  line_status = '03', " +
            "  updated_at = :updated_at " +
            "WHERE " +
            "  line_id = :lineId ";

        await sequelize.query(sql, {
            replacements: {
                updated_at: fromUnixTime(systemDateTime / 1000), // updated_at(レコードが変更された日時を格納する。)
                lineId: lineNo, // 回線ID
            },
            transaction,
            type: QueryTypes.UPDATE,
        });
    }

    /**
     * 回線情報、回線回線グループ、プランテーブルからデータを取得し、廃止回線情報テーブルへ挿入する。<BR>
     *
     * @param transaction トランザクション
     * @param param 仮登録回線追加の電文(PreLineAddInputDto)
     * @param systemDateTime 個別機能のオーダ受付日時(Timestamp{yyyy/MM/dd HH:mm:ss})
     */
    public async insertAboneLines(
        transaction: Transaction,
        param: PreLineAddInputDto,
        systemDateTime: number,
    ): Promise<void> {
        this.context.log("insertAboneLines start", {
            lineNo: param.lineNo,
            lineDelTenantId: param.lineDelTenantId,
            systemDateTime,
        });
        const sequelize = await usePsql();

        // SQL文
        let sql: string;

        // insert節
        const insert =
            "INSERT " +
            "INTO " +
            "  abone_lines " +
            "  ( " +
            "    line_id, " +
            "    line_status, " +
            "    temp_regist, " +
            "    nnumber, " +
            "    group_id, " +
            "    open_date, " +
            "    service_date, " +
            "    roaming_max_id, " +
            "    voice_mail_id, " +
            "    call_waiting_id, " +
            "    intl_call_id, " +
            "    forwarding_id, " +
            "    intl_forwarding_id, " +
            "    sim_number, " +
            "    device_type_id, " +
            "    model_type, " +
            "    imei, " +
            "    start_date, " +
            "    domain, " +
            "    auth_id, " +
            "    act_east_ip, " +
            "    act_west_ip, " +
            "    sby_ip, " +
            "    fixed_ip, " +
            "    resale_plan_id, " +
            "    resale_plan_name, " +
            "    poi, " +
            "    contract_type, " +
            "    sim_type, " +
            "    device_model_name, " +
            "    auth_pattern, " +
            "    imeisv_2, " +
            "    imeisv_3, " +
            "    notes, " +
            "    sim_flag, " +
            "    activate_date, " +
            "    modify_flag, " +
            "    old_tenant_id, " +
            "    old_group_id, " +
            "    old_plan_id, " +
            "    old_plan_name, " +
            "    created_at, " +
            "    updated_at " +
            // STEP15.0版対応 追加 START
            "    ,imsi " +
            "    ,puk_1 " +
            "    ,puk_2 " +
            "    ,usage_status " +
            // STEP15.0版対応 追加 END
            // STEP17.0版対応 追加 START
            "    ,nw_modify_flag " +
            // STEP17.0版対応 追加 END
            "  ) ";

        // 回線情報テーブルからレコードを取得し、判定する
        let lineInfo: LinesEntity = null;
        try {
            lineInfo = await this.getLineInfoByLines(param.lineNo);
        } catch (e: any) {
            // DBアクセスリトライエラーの場合
            if (isSQLException(e)) throw e;
        }

        if (lineInfo == null) {
            // STEP20.0版対応 変更 END
            // 回線情報が取得できない(回線番号に一致するレコードがない)場合、DB取得項目にはnullを設定する。ただし、半黒フラグはfalseを設定
            sql =
                insert +
                "SELECT " +
                "  :lineId AS line_id, " +
                "  '03' AS line_status, " +
                "  NULL AS temp_regist, " +
                "  NULL AS nnumber, " +
                "  NULL AS group_id, " +
                "  NULL AS open_date, " +
                "  :serviceDate AS service_date, " +
                "  NULL AS roaming_max_id, " +
                "  NULL AS voice_mail_id, " +
                "  NULL AS call_waiting_id, " +
                "  NULL AS intl_call_id, " +
                "  NULL AS forwarding_id, " +
                "  NULL AS intl_forwarding_id, " +
                "  NULL AS sim_number, " +
                "  NULL AS device_type_id, " +
                "  NULL AS model_type, " +
                "  NULL AS imei, " +
                "  NULL AS start_date, " +
                "  NULL AS domain, " +
                "  NULL AS auth_id, " +
                "  NULL AS act_east_ip, " +
                "  NULL AS act_west_ip, " +
                "  NULL AS sby_ip, " +
                "  NULL AS fixed_ip, " +
                "  NULL AS resale_plan_id, " +
                "  NULL AS resale_plan_name, " +
                "  NULL AS poi, " +
                "  NULL AS contract_type, " +
                "  NULL AS sim_type, " +
                "  NULL AS device_model_name, " +
                "  NULL AS auth_pattern, " +
                "  NULL AS imeisv_2, " +
                "  NULL AS imeisv_3, " +
                "  NULL AS notes, " +
                "  FALSE AS sim_flag, " +
                "  NULL AS activate_date, " +
                "  NULL AS modify_flag, " +
                "  :oldTenantId AS old_tenant_id, " +
                "  NULL AS group_id, " +
                "  NULL AS plan_id, " +
                "  NULL AS plan_name, " +
                "  :createdAt AS created_at, " +
                "  :updatedAt AS updated_at " +
                // STEP15.0版対応 追加 START
                "  , NULL AS imsi " +
                "  , NULL AS puk_1 " +
                "  , NULL AS puk_2 " +
                "  , NULL AS usage_status " +
                // STEP15.0版対応 追加 END
                // STEP17.0版対応 追加 START
                "  , NULL AS nw_modify_flag ";
            // STEP17.0版対応 追加 END
        } else {
            // 回線情報が取得できた場合、回線回線グループ、プランの各テーブルからの取得項目を設定
            sql =
                insert +
                "SELECT " +
                "  :lineId AS line_id, " +
                "  '03' AS line_status, " +
                "  li.temp_regist, " +
                "  li.nnumber, " +
                "  li.group_id, " +
                "  li.open_date, " +
                "  :serviceDate AS service_date, " +
                "  li.roaming_max_id, " +
                "  li.voice_mail_id, " +
                "  li.call_waiting_id, " +
                "  li.intl_call_id, " +
                "  li.forwarding_id, " +
                "  li.intl_forwarding_id, " +
                "  li.sim_number, " +
                "  li.device_type_id, " +
                "  li.model_type, " +
                "  li.imei, " +
                "  li.start_date, " +
                "  li.domain, " +
                "  li.auth_id, " +
                "  li.act_east_ip, " +
                "  li.act_west_ip, " +
                "  li.sby_ip, " +
                "  li.fixed_ip, " +
                "  li.resale_plan_id, " +
                "  li.resale_plan_name, " +
                "  li.poi, " +
                "  li.contract_type, " +
                "  li.sim_type, " +
                "  li.device_model_name, " +
                "  li.auth_pattern, " +
                "  li.imeisv_2, " +
                "  li.imeisv_3, " +
                "  li.notes, " +
                "  li.sim_flag, " +
                "  li.activate_date, " +
                "  li.modify_flag, " +
                "  :oldTenantId AS old_tenant_id, " +
                "  llg.group_id, " +
                "  pl.plan_id, " +
                "  pl.plan_name, " +
                "  :createdAt AS created_at, " +
                "  :updatedAt AS updated_at " +
                // STEP15.0版対応 追加 START
                "  , li.imsi " +
                "  , li.puk_1 " +
                "  , li.puk_2 " +
                "  , li.usage_status " +
                // STEP15.0版対応 追加 END
                // STEP17.0版対応 追加 START
                "  , li.nw_modify_flag " +
                // STEP17.0版対応 追加 END
                "FROM " +
                "  ( " +
                "    lines AS li " +
                "    LEFT OUTER JOIN " +
                "      line_line_groups AS llg " +
                "      ON " +
                "        li.line_id = llg.line_id " +
                "  ) " +
                "  LEFT OUTER JOIN " +
                "  plans AS pl " +
                "    ON " +
                "    li.resale_plan_id = pl.resale_plan_id " +
                "WHERE " +
                "  li.line_id = :lineId";
        }
        const timestamp = fromUnixTime(systemDateTime / 1000);
        await sequelize.query(sql, {
            replacements: {
                lineId: param.lineNo, // 回線ID
                serviceDate: timestamp, // サービス開始日
                oldTenantId: param.lineDelTenantId, // 廃止時テナントID
                createdAt: timestamp, // created_at(レコードが作成された日時を格納する。)
                updatedAt: timestamp, // updated_at(レコードが変更された日時を格納する。)
            },
            transaction,
            type: QueryTypes.INSERT,
        });
    }

    /**
     * 回線通信量テーブルから、システム時刻の前月、前々月の指定された回線レコードを行ロックする。<BR>
     *
     * @param transaction トランザクション
     * @param lineNo 電文中の回線番号(String)
     * @param systemDate 基準となるシステム時刻(String{yyyy/MM/dd})
     * @return 行ロックを行ったレコードのリスト(List<String>)
     */
    public async getTrafficforUpdate(
        transaction: Transaction,
        lineNo: string,
        systemDate: string,
    ): Promise<string[]> {
        const sequelize = await usePsql();
        const getTrafficforUpdateList: string[] = [];

        // SQL文
        const sql =
            "SELECT " +
            "  line_id " +
            "FROM " +
            "  line_traffics " +
            "WHERE " +
            "  line_id = :lineId " +
            "  AND " +
            "  ( " +
            "    ( " +
            "      year = EXTRACT(YEAR FROM to_timestamp(:systemDate, 'YYYY/MM/DD') - interval '1 months') " +
            "      AND month = EXTRACT(MONTH FROM to_timestamp(:systemDate, 'YYYY/MM/DD') - interval '1 months') " +
            "    ) " +
            "    OR " +
            "    ( " +
            "    year = EXTRACT(YEAR FROM to_timestamp(:systemDate, 'YYYY/MM/DD') - interval '2 months') " +
            "    AND month = EXTRACT(MONTH FROM to_timestamp(:systemDate, 'YYYY/MM/DD') - interval '2 months') " +
            "    ) " +
            "  ) " +
            "FOR UPDATE NOWAIT";

        // 実行結果のリストを取得
        const result = (await sequelize.query(sql, {
            replacements: {
                lineId: lineNo, // 回線ID
                systemDate, // 基準となるシステム時刻
            },
            type: QueryTypes.SELECT,
            transaction,
        })) as any[];

        // 取得結果の回数分処理実行
        if (result.length > 0) {
            for (const obj of result) {
                // String型へ変換
                getTrafficforUpdateList.push(obj.line_id.toString());
            }
        }
        return getTrafficforUpdateList;
    }

    /**
     * 回線通信量テーブルの行ロックを取得したレコードの通信量、通信量(クーポンOFF時)をnullでクリアする。<BR>
     *
     * @param transaction トランザクション
     * @param lineNo 電文中の回線番号(String)
     * @param systemDate 基準となるシステム時刻(String)
     */
    public async updateLineTraffictoNull(
        transaction: Transaction,
        lineNo: string,
        systemDate: string,
    ): Promise<void> {
        this.context.log("updateLineTraffictoNull start", {
            lineNo,
            systemDate,
        });
        const sequelize = await usePsql();

        // SQL文
        const sql =
            "UPDATE " +
            "  line_traffics " +
            "SET " +
            "  traffic = NULL, " +
            "  traffic_coupon_off = NULL " +
            "WHERE " +
            "  line_id = :lineId " +
            "  AND " +
            "  ( " +
            "    ( " +
            "      year = EXTRACT(YEAR FROM to_timestamp(:systemDate, 'YYYY/MM/DD') - interval '1 months') " +
            "      AND month = EXTRACT(MONTH FROM to_timestamp(:systemDate, 'YYYY/MM/DD') - interval '1 months') " +
            "    ) " +
            "    OR " +
            "    ( " +
            "    year = EXTRACT(YEAR FROM to_timestamp(:systemDate, 'YYYY/MM/DD') - interval '2 months') " +
            "    AND month = EXTRACT(MONTH FROM to_timestamp(:systemDate, 'YYYY/MM/DD') - interval '2 months') " +
            "    ) " +
            "  ) ";

        await sequelize.query(sql, {
            replacements: {
                lineId: lineNo, // 回線ID
                systemDate, // 基準となるシステム時刻
            },
            transaction,
            type: QueryTypes.UPDATE,
        });
    }

    /**
     * プランIDを元にプラン情報を取得する<BR>
     *
     * @param planId 回線ID
     * @return PlansEntity
     */
    public async getPortalPlanIdInfo(planId: string): Promise<PlansEntity> {
        const sequelize = await usePsql();
        try {
            const sql = "SELECT * FROM plans WHERE plan_id = :planId";
            return await retryQuery(
                this.context,
                "getLineLineGroupToLineId",
                async () =>
                    await sequelize.query(sql, {
                        replacements: {
                            planId: parseInt(planId, 10),
                        },
                        type: QueryTypes.SELECT,
                        mapToModel: true,
                        model: PlansEntity,
                        plain: true,
                    }),
                this.apDbRetryMaxCnt,
                this.apDbRetryInterval,
            );
        } catch (e) {
            if (isSQLException(e)) throw e;
            throw new Error(e);
        }
    }

    /**
     * 回線情報テーブルのデータを更新する
     * @param linesEntity 回線情報
     */
    public async updateLines(linesEntity: LinesEntity): Promise<void> {
        this.context.log(
            "updateLines start",
            linesEntity?.lineId,
            JSON.stringify(linesEntity),
        );
        try {
            const sequelize = await usePsql();
            let strSql: string = "";

            strSql += "UPDATE ";
            strSql += "Lines ";
            strSql += "SET ";

            strSql += "line_status = :line_status ";
            strSql += ", temp_regist = :temp_regist ";
            strSql += ", nnumber = :nnumber ";
            strSql += ", group_id = :group_id ";
            strSql += ", open_date = :open_date ";
            strSql += ", service_date = :service_date ";
            strSql += ", roaming_max_id = :roaming_max_id ";
            strSql += ", voice_mail_id = :voice_mail_id ";
            strSql += ", call_waiting_id = :call_waiting_id ";
            strSql += ", intl_call_id = :intl_call_id ";
            strSql += ", forwarding_id = :forwarding_id ";
            strSql += ", intl_forwarding_id = :intl_forwarding_id ";
            strSql += ", sim_number = :sim_number ";
            /** STEP4.0版対応 追加 START */
            strSql += ", device_type_id = :device_type_id ";
            /** STEP4.0版対応 追加 END */
            strSql += ", model_type = :model_type ";
            strSql += ", imei = :imei ";
            strSql += ", start_date = :start_date ";
            strSql += ", domain = :domain ";
            strSql += ", auth_id = :auth_id ";
            strSql += ", act_east_ip = :act_east_ip ";
            strSql += ", act_west_ip = :act_west_ip ";
            strSql += ", sby_ip = :sby_ip ";
            strSql += ", fixed_ip = :fixed_ip ";
            strSql += ", resale_plan_id = :resale_plan_id ";
            strSql += ", resale_plan_name = :resale_plan_name ";
            strSql += ", poi = :poi ";
            strSql += ", contract_type = :contract_type ";
            strSql += ", sim_type = :sim_type ";
            strSql += ", device_model_name = :device_model_name ";
            strSql += ", auth_pattern = :auth_pattern ";
            strSql += ", imeisv_2 = :imeisv_2 ";
            strSql += ", imeisv_3 = :imeisv_3 ";
            strSql += ", notes = :notes ";
            /** STEP4.0版対応 追加 START */
            strSql += ", sim_flag = :sim_flag ";
            strSql += ", activate_date = :activate_date ";
            /** STEP4.0版対応 追加 END */
            strSql += ", created_at = :created_at ";
            strSql += ", updated_at = :updated_at ";
            strSql += ", modify_flag = :modify_flag ";

            // STEP15.0版対応 追加 START
            strSql += ", imsi = :imsi ";
            strSql += ", puk_1 = :puk_1 ";
            strSql += ", puk_2 = :puk_2 ";
            strSql += ", usage_status = :usage_status ";
            // STEP15.0版対応 追加 END

            // STEP17.0版対応 追加 START
            strSql += ", nw_modify_flag = :nw_modify_flag ";
            // STEP17.0版対応 追加 END

            // STEP21.0版対応 追加 START
            strSql += ", voice_plan_id = :voice_plan_id ";
            strSql += ", voice_plan_name = :voice_plan_name ";
            // STEP21.0版対応 追加 END

            strSql += "WHERE ";
            strSql += "line_id = :line_id";

            await retryQuery(
                this.context,
                "updateLines",
                async () =>
                    await sequelize.query(strSql, {
                        replacements: {
                            line_id: linesEntity.lineId, // 回線ID
                            line_status: linesEntity.lineStatus ?? null, // 最終オーダ種別
                            temp_regist: linesEntity.tempRegist ?? null, // 仮登録フラグ
                            nnumber: linesEntity.nnumber ?? null, // 代表N番
                            group_id: linesEntity.groupId ?? null, // 回線グループID
                            open_date: linesEntity.lineActDate ?? null, // 回線利用開始年月日
                            service_date: linesEntity.serviceDate ?? null, // サービス開始日
                            roaming_max_id: linesEntity.roamingMaxId ?? null, // 国際ローミング利用限度額ID
                            voice_mail_id: linesEntity.voiceMailId ?? null, // 留守番でんわID
                            call_waiting_id: linesEntity.callWaitingId ?? null, // キャッチホンID
                            intl_call_id: linesEntity.intlCallId ?? null, // 国際通話ID
                            forwarding_id: linesEntity.forwardingId ?? null, // 転送でんわID
                            intl_forwarding_id: linesEntity.intlForwardingId ?? null, // SIM番号(DN番号)
                            sim_number: linesEntity.simNumber ?? null, // SIM番号
                            /** STEP4.0版対応 追加 START */
                            device_type_id: linesEntity.deviceTypeId ?? null, // 端末種別ID
                            /** STEP4.0版対応 追加 END */
                            model_type: linesEntity.modelType ?? null, // 端末種別名
                            imei: linesEntity.imei ?? null, // 製造番号(IMEI)
                            start_date: linesEntity.startDate ?? null, // 端末利用開始年月日
                            domain: linesEntity.domain ?? null, // ドメイン
                            auth_id: linesEntity.authId ?? null, // 認証ID
                            act_east_ip: linesEntity.actEastIp ?? null, // 国内用IP ACT東
                            act_west_ip: linesEntity.actWestIp ?? null, // 国内用IP ACT西
                            sby_ip: linesEntity.sbyIp ?? null, // 国内用IP SBY
                            fixed_ip: linesEntity.fixedIp ?? null, // 国際用固定IPアドレス
                            resale_plan_id: linesEntity.pricePlanId ?? null, // 再販料金プラン
                            resale_plan_name: linesEntity.pricePlanName ?? null, // 再販料金プラン名
                            poi: linesEntity.poi ?? null, // POI情報
                            contract_type: linesEntity.contractType ?? null, // 契約種別
                            sim_type: linesEntity.simType ?? null, // SIM種別
                            device_model_name: linesEntity.deviceModelName ?? null, // 端末機種名
                            auth_pattern: linesEntity.authPattern ?? null, // 認証パターン
                            imeisv_2: linesEntity.imeisv_2 ?? null, // 端末製造番号
                            imeisv_3: linesEntity.imeisv_3 ?? null, // 端末製造番号(予備)
                            notes: linesEntity.notes ?? null, // メモ欄
                            /** STEP4.0版対応 追加 STAR */
                            sim_flag: linesEntity.simFlag ?? null, // 半黒フラグ
                            activate_date: linesEntity.activateDate ?? null, // 黒化日
                            /** STEP4.0版対応 追加 END */
                            created_at: linesEntity.createdAt ?? null, // created_at
                            updated_at: linesEntity.updatedAt ?? null, // updated_at
                            modify_flag: linesEntity.modifyFlag ?? null, // modify_flag
                            /** STEP15.0版対応 追加 START */
                            imsi: linesEntity.imsi ?? null, // IMSI
                            puk_1: linesEntity.puk1 ?? null, // PUK1
                            puk_2: linesEntity.puk2 ?? null, // PUK2
                            usage_status: linesEntity.usageStatus ?? null, // 利用状態
                            /** STEP15.0版対応 追加 END */
                            // STEP17.0版対応 追加 START
                            nw_modify_flag: linesEntity.nwModifyFlag ?? null, // nw_modify_flag
                            // STEP17.0版対応 追加 END

                            // STEP21.0版対応 追加 START
                            // 0035でんわプランID
                            voice_plan_id: linesEntity.voicePlanId ?? null,
                            // 0035でんわプラン名
                            voice_plan_name: linesEntity.voicePlanName ?? null,
                            // STEP21.0版対応 追加 END
                        },
                        type: QueryTypes.UPDATE,
                    }),
                this.apDbRetryMaxCnt,
                this.apDbRetryInterval,
            );
        } catch (e: any) {
            if (isSQLException(e)) throw e;
            throw new Error(e);
        }
    }

    /** STEP4.0版対応 変更 END */

    /**
     * 回線情報テーブルにデータを挿入する
     * @param linesEntity 回線情報
     */
    public async insertLines(linesEntity: LinesEntity): Promise<void> {
        this.context.log(
            "insertLines start",
            linesEntity?.lineId,
            JSON.stringify(linesEntity),
        );
        try {
            const sequelize = await usePsql();
            let strSqlSel: string = "";
            let strSqlVal: string = "";

            strSqlSel += "INSERT INTO ";
            strSqlSel += "lines ";
            strSqlSel += " (";

            strSqlVal += " VALUES ";
            strSqlVal += " (";

            strSqlSel += "line_id";
            strSqlVal += ":line_id";

            strSqlSel += ", line_status";
            strSqlVal += ", :line_status";

            strSqlSel += ", temp_regist";
            strSqlVal += ", :temp_regist";

            strSqlSel += ", nnumber";
            strSqlVal += ", :nnumber";

            strSqlSel += ", group_id";
            strSqlVal += ", :group_id";

            strSqlSel += ", open_date";
            strSqlVal += ", :open_date";

            strSqlSel += ", service_date";
            strSqlVal += ", :service_date";

            strSqlSel += ", roaming_max_id";
            strSqlVal += ", :roaming_max_id";

            strSqlSel += ", voice_mail_id";
            strSqlVal += ", :voice_mail_id";

            strSqlSel += ", call_waiting_id";
            strSqlVal += ", :call_waiting_id";

            strSqlSel += ", intl_call_id";
            strSqlVal += ", :intl_call_id";

            strSqlSel += ", forwarding_id";
            strSqlVal += ", :forwarding_id";

            strSqlSel += ", intl_forwarding_id";
            strSqlVal += ", :intl_forwarding_id";

            strSqlSel += ", sim_number";
            strSqlVal += ", :sim_number";

            /** STEP4.0版対応 追加 START */
            strSqlSel += ", device_type_id";
            strSqlVal += ", :device_type_id";
            /** STEP4.0版対応 追加 END */

            strSqlSel += ", model_type";
            strSqlVal += ", :model_type";

            strSqlSel += ", imei";
            strSqlVal += ", :imei";

            strSqlSel += ", start_date";
            strSqlVal += ", :start_date";

            strSqlSel += ", domain";
            strSqlVal += ", :domain";

            strSqlSel += ", auth_id";
            strSqlVal += ", :auth_id";

            strSqlSel += ", act_east_ip";
            strSqlVal += ", :act_east_ip";

            strSqlSel += ", act_west_ip";
            strSqlVal += ", :act_west_ip";

            strSqlSel += ", sby_ip";
            strSqlVal += ", :sby_ip";

            strSqlSel += ", fixed_ip";
            strSqlVal += ", :fixed_ip";

            strSqlSel += ", resale_plan_id";
            strSqlVal += ", :resale_plan_id";

            strSqlSel += ", resale_plan_name";
            strSqlVal += ", :resale_plan_name";

            strSqlSel += ", poi";
            strSqlVal += ", :poi";

            strSqlSel += ", contract_type";
            strSqlVal += ", :contract_type";

            strSqlSel += ", sim_type";
            strSqlVal += ", :sim_type";

            strSqlSel += ", device_model_name";
            strSqlVal += ", :device_model_name";

            strSqlSel += ", auth_pattern";
            strSqlVal += ", :auth_pattern";

            strSqlSel += ", imeisv_2";
            strSqlVal += ", :imeisv_2";

            strSqlSel += ", imeisv_3";
            strSqlVal += ", :imeisv_3";

            strSqlSel += ", notes";
            strSqlVal += ", :notes";

            /** STEP4.0版対応 追加 START */
            strSqlSel += ", sim_flag";
            strSqlVal += ", :sim_flag";

            strSqlSel += ", activate_date";
            strSqlVal += ", :activate_date";
            /** STEP4.0版対応 追加 END */

            strSqlSel += ", created_at";
            strSqlVal += ", :created_at";

            strSqlSel += ", updated_at";
            strSqlVal += ", :updated_at";

            strSqlSel += ", modify_flag";
            strSqlVal += ", :modify_flag";

            // STEP15.0版対応 追加 START
            strSqlSel += ", imsi";
            strSqlVal += ", :imsi";

            strSqlSel += ", puk_1";
            strSqlVal += ", :puk_1";

            strSqlSel += ", puk_2";
            strSqlVal += ", :puk_2";

            strSqlSel += ", usage_status";
            strSqlVal += ", :usage_status";
            // STEP15.0版対応 追加 END

            // STEP17.0版対応 追加 START
            strSqlSel += ", nw_modify_flag";
            strSqlVal += ", :nw_modify_flag";
            // STEP17.0版対応 追加 END

            // STEP21.0版対応 追加 START
            strSqlSel += ", voice_plan_id";
            strSqlVal += ", :voice_plan_id";

            strSqlSel += ", voice_plan_name";
            strSqlVal += ", :voice_plan_name";
            // STEP21.0版対応 追加 END

            strSqlSel += ") ";
            strSqlVal += ") ";

            await retryQuery(
                this.context,
                "insertLines",
                async () =>
                    await sequelize.query(strSqlSel + strSqlVal, {
                        replacements: {
                            line_id: linesEntity.lineId, // 回線ID
                            line_status: linesEntity.lineStatus ?? null, // 最終オーダ種別
                            temp_regist: linesEntity.tempRegist ?? null, // 仮登録フラグ
                            nnumber: linesEntity.nnumber ?? null, // 代表N番
                            group_id: linesEntity.groupId ?? null, // 回線グループID
                            open_date: linesEntity.lineActDate ?? null, // 回線利用開始年月日
                            service_date: linesEntity.serviceDate ?? null, // サービス開始日
                            roaming_max_id: linesEntity.roamingMaxId ?? null, // 国際ローミング利用限度額ID
                            voice_mail_id: linesEntity.voiceMailId ?? null, // 留守番でんわID
                            call_waiting_id: linesEntity.callWaitingId ?? null, // キャッチホンID
                            intl_call_id: linesEntity.intlCallId ?? null, // 国際通話ID
                            forwarding_id: linesEntity.forwardingId ?? null, // 転送でんわID
                            intl_forwarding_id: linesEntity.intlForwardingId ?? null, // SIM番号(DN番号)
                            sim_number: linesEntity.simNumber ?? null, // SIM番号
                            /** STEP4.0版対応 追加 START */
                            device_type_id: linesEntity.deviceTypeId ?? null, // 端末種別ID
                            /** STEP4.0版対応 追加 END */
                            model_type: linesEntity.modelType ?? null, // 端末種別名
                            imei: linesEntity.imei ?? null, // 製造番号(IMEI)
                            start_date: linesEntity.startDate ?? null, // 端末利用開始年月日
                            domain: linesEntity.domain ?? null, // ドメイン
                            auth_id: linesEntity.authId ?? null, // 認証ID
                            act_east_ip: linesEntity.actEastIp ?? null, // 国内用IP ACT東
                            act_west_ip: linesEntity.actWestIp ?? null, // 国内用IP ACT西
                            sby_ip: linesEntity.sbyIp ?? null, // 国内用IP SBY
                            fixed_ip: linesEntity.fixedIp ?? null, // 国際用固定IPアドレス
                            resale_plan_id: linesEntity.pricePlanId ?? null, // 再販料金プラン
                            resale_plan_name: linesEntity.pricePlanName ?? null, // 再販料金プラン名
                            poi: linesEntity.poi ?? null, // POI情報
                            contract_type: linesEntity.contractType ?? null, // 契約種別
                            sim_type: linesEntity.simType ?? null, // SIM種別
                            device_model_name: linesEntity.deviceModelName ?? null, // 端末機種名
                            auth_pattern: linesEntity.authPattern ?? null, // 認証パターン
                            imeisv_2: linesEntity.imeisv_2 ?? null, // 端末製造番号
                            imeisv_3: linesEntity.imeisv_3 ?? null, // 端末製造番号(予備)
                            notes: linesEntity.notes ?? null, // メモ欄
                            /** STEP4.0版対応 追加 START */
                            sim_flag: linesEntity.simFlag ?? null, // 半黒フラグ
                            activate_date: linesEntity.activateDate ?? null, // 黒化日
                            /** STEP4.0版対応 追加 END */
                            created_at: linesEntity.createdAt ?? null, // created_at
                            updated_at: linesEntity.updatedAt ?? null, // updated_at
                            modify_flag: linesEntity.modifyFlag ?? null, // modify_flag

                            /** STEP15.0版対応 追加 START */
                            imsi: linesEntity.imsi ?? null, // IMSI
                            puk_1: linesEntity.puk1 ?? null, // PUK1
                            puk_2: linesEntity.puk2 ?? null, // PUK2
                            usage_status: linesEntity.usageStatus ?? null, // 利用状態
                            /** STEP15.0版対応 追加 END */

                            // STEP17.0版対応 追加 START
                            nw_modify_flag: linesEntity.nwModifyFlag ?? null, // nw_modify_flag
                            // STEP17.0版対応 追加 END

                            // STEP21.0版対応 追加 START
                            // 0035でんわプランID
                            voice_plan_id: linesEntity.voicePlanId ?? null,
                            // 0035でんわプラン名
                            voice_plan_name: linesEntity.voicePlanName ?? null,
                            // STEP21.0版対応 追加 END
                        },
                        type: QueryTypes.INSERT,
                    }),
                this.apDbRetryMaxCnt,
                this.apDbRetryInterval,
            );
        } catch (e: any) {
            if (isSQLException(e)) throw e;
            throw new Error(e);
        }
    }

    /**
     * 指定したN番に紐付くお客様情報を取得する
     *
     * @param nnumber N番
     * @return CustomerInfoEntity
     */
    public async getCustomerInfo(nnumber: string): Promise<CustomerInfoEntity> {
        const sequelize = await usePsql();
        try {
            const sql = "SELECT * FROM customer_info WHERE nnumber = :nnumber";
            return await retryQuery(
                this.context,
                "getCustomerInfo",
                async () =>
                    await sequelize.query(sql, {
                        replacements: { nnumber },
                        type: QueryTypes.SELECT,
                        mapToModel: true,
                        model: CustomerInfoEntity,
                        plain: true,
                    }),
                this.apDbRetryMaxCnt,
                this.apDbRetryInterval,
            );
        } catch (e: any) {
            if (isSQLException(e)) throw e;
            throw new Error(e);
        }
    }

    /**
     * カード用途が利用できるカード種別かどうかを取得する
     *
     * @param deviceTypeId カード種別ID
     * @param purpose カード用途
     * @return String
     *
     */
    public async getCardCheckCard(
        deviceTypeId: string,
        purpose: string,
    ): Promise<string> {
        const sequelize = await usePsql();

        try {
            let sql: string = "";
            sql += "SELECT device_type_id ";
            sql += "FROM card_check_card ";
            sql += "WHERE device_type_id = :deviceTypeId ";
            sql += "AND purpose = :purpose ";

            const result = await retryQuery(
                this.context,
                "getCardCheckCard",
                async () =>
                    (await sequelize.query(sql, {
                        replacements: { deviceTypeId, purpose },
                        type: QueryTypes.SELECT,
                        plain: true,
                    })) as any,
                this.apDbRetryMaxCnt,
                this.apDbRetryInterval,
            );
            return result ? result.device_type_id : null;
        } catch (e: any) {
            if (isSQLException(e)) throw e;
            throw new Error(e);
        }
    }

    /**
     * カード種別が利用できるプランIDかどうかを取得する
     *
     * @param planId プランID
     * @param deviceTypeId カード種別ID
     * @return String
     *
     */
    public async getPlanCard(
        planId: string,
        deviceTypeId: string,
    ): Promise<string> {
        const sequelize = await usePsql();

        try {
            let sql: string = "";
            sql += "SELECT device_type_id ";
            sql += "FROM plan_card pc, plans p ";
            sql += "WHERE p.plan_id = :planId ";
            sql += "AND pc.device_type_id = :deviceTypeId ";
            sql += "AND pc.resale_plan_id = p.resale_plan_id ";

            const result = await retryQuery(
                this.context,
                "getPlanCard",
                async () =>
                    (await sequelize.query(sql, {
                        replacements: { planId: parseInt(planId, 10), deviceTypeId },
                        type: QueryTypes.SELECT,
                        plain: true,
                    })) as any,
                this.apDbRetryMaxCnt,
                this.apDbRetryInterval,
            );

            return result ? result.device_type_id : null;
        } catch (e: any) {
            if (isSQLException(e)) throw e;
            throw new Error(e);
        }
    }

    /**
     * 回線情報更新
     * @param lineNo 回線番号
     * @param simFlag 半黒フラグ
     * @param activateDate 黒化日
     * @param nowTime 現在日付
     */
    public async updateLineKurokaInfo(
        lineNo: string,
        simFlag: boolean,
        activateDate: Date,
        nowTime: Date,
    ): Promise<void> {
        this.context.log("updateLineKurokaInfo start", {
            lineNo,
            simFlag,
            activateDate,
            nowTime,
        });
        const sequelize = await usePsql();
        try {
            const sql = [];
            sql.push("UPDATE ");
            sql.push("Lines ");
            sql.push("SET ");
            sql.push("sim_flag = :simFlag ");
            sql.push(", activate_date = :activateDate ");
            sql.push(", updated_at = :updated_at ");
            sql.push("where ");
            sql.push("line_id = :lineNo");

            await retryQuery(
                this.context,
                "updateLineKurokaInfo",
                async () =>
                    await sequelize.query(sql.join(" "), {
                        replacements: {
                            simFlag,
                            activateDate,
                            lineNo,
                            updated_at: nowTime,
                        },
                        type: QueryTypes.UPDATE,
                    }),
                this.apDbRetryMaxCnt,
                this.apDbRetryInterval,
            );
        } catch (e: any) {
            if (isSQLException(e)) throw e;
            throw new Error(e);
        }
    }

    /**
     * 回線番号リストを取得する
     * @param nNumber String 代表N番
     * @return List&lt;String&gt;回線IDリスト
     */
    public async getLineIDList1(nNumber: string): Promise<string[]> {
        const sequelize = await usePsql();
        try {
            const sql = [];
            sql.push("SELECT distinct line_id ");
            sql.push("FROM lines ");
            sql.push("WHERE nnumber = :nNumber ");
            sql.push("AND line_status <> '03' ");
            sql.push("ORDER BY line_id asc ");

            const result = await retryQuery(
                this.context,
                "getLineIDList1",
                async () =>
                    (await sequelize.query(sql.join(" "), {
                        replacements: { nNumber },
                        type: QueryTypes.SELECT,
                    })) as any[],
                this.apDbRetryMaxCnt,
                this.apDbRetryInterval,
            );
            return result && result.length
                ? result.map((row) => row.line_id.toString())
                : [];
        } catch (e: any) {
            if (isSQLException(e)) throw e;
            throw new Error(e);
        }
    }

    /**
     * 自テナントN番を取得する
     * @param tenantId String テナントID
     * @return List&lt;String&gt;テナントN番リスト
     */
    public async getNnumbers(tenantId: string): Promise<string[]> {
        const sequelize = await usePsql();
        try {
            const sql = [];
            sql.push("SELECT nnumber ");
            sql.push("FROM tenant_nnumbers ");
            sql.push("WHERE tenant_id = :tenantId ");

            const result = await retryQuery(
                this.context,
                "getNnumbers",
                async () =>
                    (await sequelize.query(sql.join(" "), {
                        replacements: { tenantId },
                        type: QueryTypes.SELECT,
                    })) as any[],
                this.apDbRetryMaxCnt,
                this.apDbRetryInterval,
            );
            return result && result.length
                ? result.map((row) => row.nnumber.toString())
                : [];
        } catch (e: any) {
            if (isSQLException(e)) throw e;
            throw new Error(e);
        }
    }

    /**
     * 回線番号リストを取得する
     * @param nNumber String 代表N番
     * @param tenantId String テナントID
     * @return List&lt;String&gt;回線番号リスト
     */
    public async getLineIDList2(
        nNumber: string[],
        tenantId: string,
    ): Promise<string[]> {
        const sequelize = await usePsql();
        try {
            if (!nNumber || nNumber.length === 0) {
                return [];
            }
            const sql = [];
            sql.push("SELECT distinct line_id ");
            sql.push("FROM lines ");
            sql.push(`WHERE nnumber in (:nNumber) `);
            sql.push("AND line_status <> '03' ");
            sql.push("UNION ");
            sql.push("SELECT distinct l.line_id ");
            sql.push("FROM lines AS l, line_tenants AS t ");
            sql.push("WHERE t.tenant_id = :tenantId ");
            sql.push("AND t.line_id = l.line_id ");
            sql.push("AND l.line_status <> '03' ");
            sql.push("ORDER BY line_id ASC ");

            const result = await retryQuery(
                this.context,
                "getLineIDList2",
                async () =>
                    (await sequelize.query(sql.join(" "), {
                        replacements: { tenantId, nNumber },
                        type: QueryTypes.SELECT,
                    })) as any[],
                this.apDbRetryMaxCnt,
                this.apDbRetryInterval,
            );
            return result && result.length
                ? result.map((row) => row.line_id.toString())
                : [];
        } catch (e: any) {
            if (isSQLException(e)) throw e;
            throw new Error(e);
        }
    }

    /**
     * プランIＤを取得する
     * @param tenantId String テナントID
     * @return List&lt;String&gt;プランIＤ番リスト
     */
    public async getPlanID(tenantId: string): Promise<string[]> {
        const sequelize = await usePsql();
        try {
            const sql = [];
            sql.push("SELECT plan_id ");
            sql.push("FROM tenant_plans ");
            sql.push("WHERE tenant_id = :tenantId ");

            const result = await retryQuery(
                this.context,
                "getPlanID",
                async () =>
                    (await sequelize.query(sql.join(" "), {
                        replacements: { tenantId },
                        type: QueryTypes.SELECT,
                    })) as any[],
                this.apDbRetryMaxCnt,
                this.apDbRetryInterval,
            );
            return result && result.length
                ? result.map((row) => row.plan_id.toString())
                : [];
        } catch (e: any) {
            if (isSQLException(e)) throw e;
            throw new Error(e);
        }
    }

    /**
     * 再版料金プラン回線IDを取得する
     * @param planId String プランID
     * @return List&lt;String&gt;再版料金プラン回線IDリスト
     * @throws SQLException
     */
    public async getResalePlanID(planId: string[]): Promise<string[]> {
        const sequelize = await usePsql();
        try {
            return retryQuery(
                this.context,
                "getResalePlanID",
                async () => {
                    if (!planId || planId.length === 0) {
                        return [];
                    }
                    const sql = [];
                    sql.push("select resale_plan_id ");
                    sql.push("from plans ");
                    sql.push(`where plan_id in (:planId) `);
                    const queryResult = (
                        await sequelize.query(sql.join(" "), {
                            replacements: { planId },
                            type: QueryTypes.SELECT,
                        })
                    ) as any[];
                    const result: string[] = [];
                    for (let i = 0; i < queryResult.length; i++) {
                        result.push(queryResult[i].resale_plan_id);
                    }
                    return result;
                },
                this.apDbRetryMaxCnt,
                this.apDbRetryInterval,
            );
        } catch (e: any) {
            if (isSQLException(e)) throw e;
            throw new Error(e);
        }
    }

    /**
     * 回線番号リストを取得する
     * @param tenantId String テナントID
     * @param resalePlanId String 料金プランID文字列
     * @return List&lt;String&gt;回線番号リスト
     */
    public async getLineIDList3(
        tenantId: string,
        resalePlanId: string[],
    ): Promise<string[]> {
        const sequelize = await usePsql();
        try {
            if (!resalePlanId || resalePlanId.length === 0) {
                return [];
            }
            const sql = [];
            sql.push("SELECT distinct t.line_id ");
            sql.push("FROM lines AS l, line_tenants AS t ");
            sql.push("WHERE t.tenant_id = :tenantId ");
            sql.push("AND t.line_id = l.line_id ");
            sql.push("AND l.line_status <> '03' ");
            sql.push("UNION ");
            sql.push("SELECT distinct line_id ");
            sql.push("FROM lines ");
            sql.push(`WHERE resale_plan_id in (:resalePlanId) `);
            sql.push("AND line_status <> '03' ");
            sql.push("ORDER BY line_id ASC ");

            const result = await retryQuery(
                this.context,
                "getLineIDList3",
                async () =>
                    (await sequelize.query(sql.join(" "), {
                        replacements: { tenantId, resalePlanId },
                        type: QueryTypes.SELECT,
                    })) as any[],
                this.apDbRetryMaxCnt,
                this.apDbRetryInterval,
            );
            return result && result.length
                ? result.map((row) => row.line_id.toString())
                : [];
        } catch (e: any) {
            if (isSQLException(e)) throw e;
            throw new Error(e);
        }
    }

    /**
     * 回線番号リストを取得する
     * @param tenantId String テナントID
     * @return List&lt;String&gt;回線番号リスト
     */
    public async getLineIDList4(tenantId: string): Promise<string[]> {
        const sequelize = await usePsql();
        try {
            const sql = [];
            sql.push("SELECT distinct t.line_id ");
            sql.push("FROM lines AS l, line_tenants AS t ");
            sql.push("WHERE t.tenant_id = :tenantId ");
            sql.push("AND t.line_id = l.line_id ");
            sql.push("AND l.line_status <> '03' ");
            sql.push("ORDER BY t.line_id ASC ");

            const result = await retryQuery(
                this.context,
                "getLineIDList4",
                async () =>
                    (await sequelize.query(sql.join(" "), {
                        replacements: { tenantId },
                        type: QueryTypes.SELECT,
                    })) as any[],
                this.apDbRetryMaxCnt,
                this.apDbRetryInterval,
            );
            return result && result.length
                ? result.map((row) => row.line_id.toString())
                : [];
        } catch (e: any) {
            if (isSQLException(e)) throw e;
            throw new Error(e);
        }
    }

    /**
     * プランテーブルの参照サービスパターンを取得する。<BR>
     * @param lineNo String 回線番号
     * @return List<String>
     * @throws HibernateException
     */
    public async getRefServicePattern(lineNo: string): Promise<PlansEntity[]> {
        let plansEntityList: PlansEntity[] = [];
        const sequelize = await usePsql();
        try {
            plansEntityList = await retryQuery(
                this.context,
                "getRefServicePattern",
                async () => {
                    const linesEntity = await sequelize.query(
                        "SELECT resale_plan_id FROM lines WHERE line_id = :lineId AND line_status NOT IN ('03')", {
                            replacements: { lineId: lineNo },
                            type: QueryTypes.SELECT,
                            mapToModel: true,
                            model: LinesEntity,
                            plain: true,
                        });

                    if (linesEntity) {
                        return await sequelize.query("SELECT * FROM plans WHERE resale_plan_id = :pricePlanId", {
                            replacements: {
                                pricePlanId: linesEntity.pricePlanId,
                            },
                            type: QueryTypes.SELECT,
                            mapToModel: true,
                            model: PlansEntity,
                        });
                    }
                    return [];
                },
                this.apDbRetryMaxCnt,
                this.apDbRetryInterval,
            );
        } catch (e: any) {
            if (isSQLException(e)) throw e;
            throw new Error(e);
        }

        return plansEntityList;
    }

    /**
     * 回線の基本情報を取得する。<BR>
     * @param lineNo String 回線番号
     * @return LinesEntity
     * @exception HibernateExceptionの場合
     */
    // original code has two version - with and without session
    public async getLineInfo(lineNo: string, transaction: Transaction = null): Promise<LinesEntity> {
        const sequelize = await usePsql();
        if (transaction) {
            return await sequelize.query("SELECT * FROM lines WHERE line_id = :lineId AND line_status NOT IN ('03')", {
                replacements: { lineId: lineNo },
                type: QueryTypes.SELECT,
                mapToModel: true,
                model: LinesEntity,
                plain: true,
                transaction,
            });
        } else {
            return await retryQuery(
                this.context,
                "getLineInfo",
                async () => {
                    const tx = await sequelize.transaction();
                    const result = await sequelize.query("SELECT * FROM lines WHERE line_id = :lineId AND line_status NOT IN ('03')", {
                        replacements: { lineId: lineNo },
                        type: QueryTypes.SELECT,
                        mapToModel: true,
                        model: LinesEntity,
                        plain: true,
                        transaction: tx,
                    });
                    await tx.commit();
                    return result;
                },
                this.apDbRetryMaxCnt,
                this.apDbRetryInterval,
            );
        }
    }

    /**
     * SO管理テーブルより新設回線予約オーダのサービスオーダIDを取得する。<BR>
     *
     * @param lineNo 回線番号
     * @return サービスオーダIDリスト
     * @throws SQLException
     * @exception HibernateExceptionの場合
     */
    public async getLineOpenReserveOrder(lineNo: string): Promise<string[]> {
        const sequelize = await usePsql();
        return await retryQuery(
            this.context,
            "getLineOpenReserveOrder",
            async () => {
                const sql = [];
                sql.push("SELECT service_order_id ");
                sql.push("FROM service_orders ");
                sql.push("WHERE line_id = :line_id ");
                sql.push("AND function_type = '51' ");
                sql.push("AND order_status = '予約中' ");
                const queryResult = await sequelize.query(sql.join(" "), {
                    replacements: { line_id: lineNo },
                    type: QueryTypes.SELECT,
                }) as any[];
                return queryResult.map((row) => row.service_order_id);
            },
            this.apDbRetryMaxCnt,
            this.apDbRetryInterval,
        );
    }

    /**
     * テナントIDとプランIDで使用可能な料金プランリストを取得する
     * @param tenantId String テナントID
     * @param planId String プランID
     * @return List&lt;String&gt;プランIＤリスト
     * @throws SQLException
     */
    public async getPlanList(tenantId: string, planId: string): Promise<string[]> {
        const sequelize = await usePsql();
        return await retryQuery(
            this.context,
            "getPlanList",
            async () => {
                const sql = [];
                sql.push("SELECT plan_id ");
                sql.push("FROM tenant_plans ");
                sql.push("where tenant_id = :tenantId ");
                sql.push("and plan_id = :planId ");
                const queryResult = await sequelize.query(sql.join(" "), {
                    replacements: { tenantId, planId: parseInt(planId, 10) },
                    type: QueryTypes.SELECT,
                }) as any[];
                return queryResult.map((row) => row.plan_id.toString());
            },
            this.apDbRetryMaxCnt,
            this.apDbRetryInterval,
        );
    }

    /**
     * 回線のプラン情報を取得する。<BR>
     * @param planId String プランID
     * @return List
     * @throws SQLException
     * @exception HibernateExceptionの場合
     */
    public async getPlansInfoByPlanId(planId: string): Promise<PlansEntity> {
        const sequelize = await usePsql();
        return await retryQuery(
            this.context,
            "getPlansInfoByPlanId",
            async () => {
                const sql = [];
                return await PlansEntity.findOne({
                    where: {
                        planId: parseInt(planId, 10),
                    },
                });
            },
            this.apDbRetryMaxCnt,
            this.apDbRetryInterval,
        );
    }

    /**
     * 回線オプション情報を取得する。<BR>
     * @param lineOptionId String 回線オプションID
     * @return LineOptionsEntity
     * @throws SQLException
     * @exception HibernateExceptionの場合
     */
    public async getLineOptionsInfo(lineOptionId: string): Promise<LineOptionsEntity> {
        const sequelize = await usePsql();
        return await retryQuery(
            this.context,
            "getLineOptionsInfo",
            async () => {
                const sql = [];
                return await LineOptionsEntity.findOne({
                    where: {
                        lineOptionId,
                    },
                });
            },
            this.apDbRetryMaxCnt,
            this.apDbRetryInterval,
        );
    }

    /**
     * テナントプラン回線オプション情報を取得する。<BR>
     * @param tenantId String テナントID
     * @param planId String プランID
     * @param lineOptionType String 回線オプション分類
     * @return TenantPlanLineOptionsEntity
     * @throws SQLException
     * @exception HibernateExceptionの場合
     */
    public async getTenantPlanLineOptionsInfo(
        tenantId: string,
        planId: string,
        lineOptionType: number,
    ): Promise<TenantPlanLineOptionsEntity> {
        const sequelize = await usePsql();
        return await retryQuery(
            this.context,
            "getTenantPlanLineOptionsInfo",
            async () => {
                const sql = [];
                return await TenantPlanLineOptionsEntity.findOne({
                    where: {
                        tenantId,
                        planId: parseInt(planId, 10),
                        lineOptionType,
                    },
                });
            },
            this.apDbRetryMaxCnt,
            this.apDbRetryInterval,
        );
    }

    /**
     * お客様基本情報より留守番でんわオプションIDを取得する。<BR>
     *
     * @param nNumber 代表N番
     * @param voicemailOptionId 留守番でんわオプションID
     * @return 留守番でんわオプションID
     * @throws SQLException
     * @exception HibernateExceptionの場合
     */
    public async getVoicemailFromCustInfo(
        nNumber: string,
        voicemailOptionId: string,
    ): Promise<string> {
        const sequelize = await usePsql();
        return await retryQuery(
            this.context,
            "getVoicemailFromCustInfo",
            async () => {
                const sql = [];
                sql.push("SELECT voicemail_option_id ");
                sql.push("FROM customer_info ");
                sql.push("WHERE nnumber = :nnumber ");
                sql.push("AND voicemail_option_id = :voicemail_option_id ");
                const queryResult = await sequelize.query(sql.join(" "), {
                    replacements: { nnumber: nNumber, voicemail_option_id: voicemailOptionId },
                    type: QueryTypes.SELECT,
                }) as any[];
                if (queryResult.length >= 1) {
                    return queryResult[0].voicemail_option_id.toString();
                } else {
                    return null;
                }
            },
            this.apDbRetryMaxCnt,
            this.apDbRetryInterval,
        );
    }

    /**
     * お客様基本情報よりキャッチホンオプションIDを取得する。<BR>
     *
     * @param nNumber 代表N番
     * @param callwaitingId キャッチホンID
     * @return キャッチホンオプションID
     * @throws SQLException
     * @exception HibernateExceptionの場合
     */
    public async getCallWaitingFromCustInfo(
        nNumber: string,
        callwaitingId: string,
    ): Promise<string> {
        const sequelize = await usePsql();
        return await retryQuery(
            this.context,
            "getCallWaitingFromCustInfo",
            async () => {
                const sql = [];
                sql.push("SELECT callwaiting_option_id ");
                sql.push("FROM customer_info ");
                sql.push("WHERE nnumber = :nnumber ");
                sql.push("AND callwaiting_option_id = :callwaiting_option_id ");
                const queryResult = await sequelize.query(sql.join(" "), {
                    replacements: { nnumber: nNumber, callwaiting_option_id: callwaitingId },
                    type: QueryTypes.SELECT,
                }) as any[];
                if (queryResult.length >= 1) {
                    return queryResult[0].callwaiting_option_id.toString();
                } else {
                    return null;
                }
            },
            this.apDbRetryMaxCnt,
            this.apDbRetryInterval,
        );
    }

    /**
     * 帯域卸フラグリストを取得する
     * @param tenantId String テナントID
     * @param nNumber String 代表N番
     * @return List<String>
     * @throws SQLException
     * @exception HibernateExceptionの場合
     */
    public async getWholeFlag(
        tenantId: string,
        nNumber: string,
    ): Promise<boolean[]> {
        const sequelize = await usePsql();
        return await retryQuery(
            this.context,
            "getWholeFlag",
            async () => {
                const sql = [];
                sql.push("SELECT whole_flag ");
                sql.push("FROM tenant_nnumbers ");
                sql.push("WHERE tenant_id = :tenant_id ");
                sql.push("AND nnumber = :nnumber ");
                const queryResult = await sequelize.query(sql.join(" "), {
                    replacements: { tenant_id: tenantId, nnumber: nNumber },
                    type: QueryTypes.SELECT,
                }) as any[];
                return queryResult.map((row) => row.whole_flag);
            },
            this.apDbRetryMaxCnt,
            this.apDbRetryInterval,
        );
    }

    /**
     * 容量追加コードリストを取得する
     * @param potalPlanID String 卸ポータルオプションプランID
     * @return List<String>
     * @throws SQLException
     * @exception HibernateExceptionの場合
     */
    public async getCapacityCode(potalPlanID: string): Promise<string[]> {
        const sequelize = await usePsql();
        return await retryQuery(
            this.context,
            "getCapacityCode",
            async () => {
                const sql = [];
                sql.push("select capacity_code ");
                sql.push("from option_plans ");
                sql.push("where option_plan_id = :option_plan_id ");
                const queryResult = await sequelize.query(sql.join(" "), {
                    replacements: { option_plan_id: parseInt(potalPlanID, 10) },
                    type: QueryTypes.SELECT,
                }) as any[];
                return queryResult.map((row) => row.capacity_code);
            },
            this.apDbRetryMaxCnt,
            this.apDbRetryInterval,
        );
    }

    /**
     * 社内フラグを取得する
     * @param tenantId String テナントID
     *
     * @return 社内フラグリスト
     * @throws SQLException
     */
    public async getOffice(tenantId: string): Promise<boolean[]> {
        const sequelize = await usePsql();
        return await retryQuery(
            this.context,
            "getOffice",
            async () => {
                const sql = [];
                sql.push("SELECT office ");
                sql.push("FROM tenants ");
                sql.push("WHERE tenant_id = :tenantId ");
                const queryResult = await sequelize.query(sql.join(" "), {
                    replacements: { tenantId },
                    type: QueryTypes.SELECT,
                }) as any[];
                return queryResult.map((row) => row.office);
            },
            this.apDbRetryMaxCnt,
            this.apDbRetryInterval,
        );
    }

    /**
     * 指定された回線IDの0035でんわプラン情報を更新する。<BR>
     *
     * @param lineNo         電文中の回線番号
     * @param voicePlanId    更新する0035でんわプランID
     * @param voicePlanName  更新する0035でんわプラン名
     * @param systemDateTime 更新日時(Timestamp{yyyy/MM/dd HH:mm:ss})
     * @param session セッション
     * @return 更新件数
     */
    public async updateLineVoicePlan(
        lineNo: string,
        voicePlanId: string,
        voicePlanName: string,
        systemDateTime: number,
        transaction: Transaction,
    ): Promise<number> {
        this.context.log("updateLineVoicePlan start", {
            lineNo,
            voicePlanId,
            voicePlanName,
            systemDateTime,
        });
        const sequelize = await usePsql();
        const sql = "UPDATE "
            + "  lines "
            + "SET "
            + "   voice_plan_id = :voice_plan_id "
            + "  ,voice_plan_name = :voice_plan_name "
            + "  ,updated_at = :updated_at "
            + "WHERE "
            + "  line_id = :lineId ";
        const timestamp = fromUnixTime(systemDateTime / 1000);
        const [_, updated_count] = await sequelize.query(sql, {
            replacements: {
                voice_plan_id: voicePlanId,
                voice_plan_name: voicePlanName,
                updated_at: timestamp,
                lineId: lineNo,
            },
            type: QueryTypes.UPDATE,
            transaction,
        });
        return updated_count;
    }

    /**
     *  回線グループIDを取得する。<BR>
     *
     * @param lineNo
     *            String 回線番号
     * @return 回線グループID String
     * @throws SQLException
     * @exception HibernateExceptionの場合
     */
    public async getLinesGroupId(lineNo: string): Promise<string> {
        const sequelize = await usePsql();
        return await retryQuery(
            this.context,
            "getLinesGroupId",
            async () => {
                const sql = [];
                sql.push("SELECT G.group_id ");
                sql.push("FROM line_line_groups G ");
                sql.push("INNER JOIN lines L ");
                sql.push("ON G.line_id = L.line_id ");
                sql.push("WHERE L.line_id = :line_id ");
                sql.push("AND L.line_status <> '03' ");
                const queryResult = await sequelize.query(sql.join(" "), {
                    replacements: { line_id: lineNo },
                    type: QueryTypes.SELECT,
                }) as any[];
                if (queryResult.length === 1) {
                    return queryResult[0].group_id.toString();
                }
                return null;
            },
            this.apDbRetryMaxCnt,
            this.apDbRetryInterval,
        );
    }

    /**
     * 回線IDを元に各オプション名を取得する<BR>
     *
     * @param lineID 回線ID
     * @return lineOptionsName
     * @throws SQLException
     */
    public async getLineOptionsName(lineID: string): Promise<string[]> {
        const sequelize = await usePsql();
        return await retryQuery(
            this.context,
            "getLineOptionsName",
            async () => {
                const sql = [];
                sql.push("SELECT ");
                sql.push("(SELECT option_plan_name FROM line_options o ");
                sql.push("WHERE o.line_option_id = l.roaming_max_id) AS RoamingMax ");
                sql.push(",(SELECT option_plan_name FROM line_options o ");
                sql.push("WHERE o.line_option_id = l.voice_mail_id) AS VoiceMail ");
                sql.push(",(SELECT option_plan_name FROM line_options o ");
                sql.push("WHERE o.line_option_id = l.call_waiting_id) AS CallWaiting ");
                sql.push(",(SELECT option_plan_name FROM line_options o ");
                sql.push("WHERE o.line_option_id = l.intl_call_id) AS IntlCall ");
                sql.push(",(SELECT option_plan_name FROM line_options o ");
                sql.push("WHERE o.line_option_id = l.forwarding_id) AS Forwarding ");
                sql.push(",(SELECT option_plan_name FROM line_options o ");
                sql.push("WHERE o.line_option_id = l.intl_forwarding_id) AS IntlForwarding ");
                sql.push("FROM lines l ");
                sql.push("WHERE l.line_id = :lineID ");
                const list = await sequelize.query(sql.join(" "), {
                    replacements: { lineID },
                    type: QueryTypes.SELECT,
                }) as any[];
                if (list == null || list.length === 0) {
                    return null;
                }
                return [
                    list[0].roamingmax,
                    list[0].voicemail,
                    list[0].callwaiting,
                    list[0].intlcall,
                    list[0].forwarding,
                    list[0].intlforwarding,
                ];
            },
            this.apDbRetryMaxCnt,
            this.apDbRetryInterval,
        );
    }

    /**
     * 該当回線の通信量を取得する
     * @param lineId String 回線ID
     * @param year String 年
     * @param month String 月
     *
     * @return 回線通信量
     * @throws SQLException
     */
    public async getTraffic(lineId: string, year: string, month: string): Promise<LineTrafficsEntity> {
        const sequelize = await usePsql();
        return await retryQuery(
            this.context,
            "getTraffic",
            async () => {
                return await LineTrafficsEntity.findOne({
                    where: {
                        lineId: MvnoUtil.addSpaceAfterStr(lineId, 0),
                        year: parseInt(year, 10),
                        month: parseInt(month, 10),
                    },
                });
            },
            this.apDbRetryMaxCnt,
            this.apDbRetryInterval,
        );
    }

    /**
     * カード用途が利用できるプランIDかどうかを取得する
     *
     * @param planId プランID
     * @param purpose カード用途
     * @return String
     * @throws SQLException
     */
    public async getCardCheckPlan(
        planId: string,
        purpose: string,
    ): Promise<string> {
        const sequelize = await usePsql();
        return await retryQuery(
            this.context,
            "getCardCheckPlan",
            async () => {
                const sql: string[] = [];
                sql.push("SELECT cp.resale_plan_id ");
                sql.push("FROM card_check_plan cp, plans p ");
                sql.push("WHERE p.plan_id = :planId ");
                sql.push("AND cp.resale_plan_id = p.resale_plan_id ");
                sql.push("AND cp.purpose = :purpose ");
                const result = (await sequelize.query(sql.join(" "), {
                    replacements: {
                        planId: parseInt(planId, 10),
                        purpose,
                    },
                    type: QueryTypes.SELECT,
                })) as any[];
                if (result && result.length === 1) {
                    return result[0].resale_plan_id;
                } else {
                    return null;
                }
            },
            this.apDbRetryMaxCnt,
            this.apDbRetryInterval,
        );
    }

    /**
     * プランIDを取得する
     * @param lineID 回線ID
     * @return String
     * @throws SQLException
     */
    public async getLineUsingPlan(lineID: string): Promise<string>{
        const sequelize = await usePsql();
        return await retryQuery(
            this.context,
            "getLineUsingPlan",
            async () => {
                const sql: string[] = [];
                sql.push("SELECT p.plan_id ");
                sql.push("FROM plans as p, lines as l ");
                sql.push("WHERE l.line_id = :lineID ");
                sql.push("AND l.line_status <> '03' ");
                sql.push("AND p.resale_plan_id = l.resale_plan_id ");
                const result = (await sequelize.query(sql.join(" "), {
                    replacements: {
                        lineID,
                    },
                    type: QueryTypes.SELECT,
                })) as any[];
                if (result && result.length === 1) {
                    return result[0].plan_id.toString();
                } else {
                    return null;
                }
            },
            this.apDbRetryMaxCnt,
            this.apDbRetryInterval,
        );
    }
    /**
     * プラン変更タイミングリストを取得する
     * @param tenantId String テナントID
     * @param planId String プランID
     * @param changePlanId String 変更可能プランID
     *
     * @return List&lt;String&gt;プラン変更タイミングリスト
     * @throws SQLException
     */
    public async getChangeTiming(tenantId: string, potalPlanIDPre: string, potalPlanID: string):Promise<string[]>{
        this.context.log("getChangeTiming start", {
            tenantId,
            potalPlanIDPre,
            potalPlanID,
        });
        const sequelize = await usePsql();
        return await retryQuery(
            this.context,
            "getChangeTiming",
            async () => {
                const result: string[] = [];
                const sql: string[] = [];
                sql.push("select change_timing ");
                sql.push("from tenant_plans ");
                sql.push("where tenant_id = :tenantId ");
                sql.push("and plan_id = :potalPlanIDPre ");
                sql.push("and change_plan_id = :potalPlanID ");
                const queryResult = await sequelize.query(sql.join(" "), {
                    replacements: {
                        tenantId: MvnoUtil.addSpaceAfterStr(tenantId,0),
                        potalPlanIDPre: Number(potalPlanIDPre),
                        potalPlanID: Number(potalPlanID),
                    },
                    type: QueryTypes.SELECT,
                }) as any[];
                for (const row of queryResult){
                    result.push(row.change_timing)
                }
                return result;
            },
            this.apDbRetryMaxCnt,
            this.apDbRetryInterval,
        )
    }

    /**
     * 回線グループIDリストを取得する
     * @param lineId String 回線番号
     * @return List&lt;String&gt;回線グループIDリスト
     * @throws SQLException
     */
    public async getGroupId(lineId: string):Promise<string[]>{
        this.context.log("getGroupId start", {
            lineId,
        });
        const sequelize = await usePsql();
        return await retryQuery(
            this.context,
            "getGroupId",
            async () => {
                const result: string[] = [];
                const sql: string[] = [];
                sql.push("select group_id ");
                sql.push("from line_line_groups ");
                sql.push("where line_id = :lineId ");
                const queryResult = await sequelize.query(sql.join(" "), {
                    replacements: {
                        lineId,
                    },
                    type: QueryTypes.SELECT,
                }) as any[];
                for (const row of queryResult){
                    result.push(row.group_id)
                }
                return result;
            },
            this.apDbRetryMaxCnt,
            this.apDbRetryInterval,
        )
    }

    /**
     * プラン変更より、回線情報を更新する
     * @param lineNo 回線番号
     * @param plan 再販料金プラン
     * @param nowTime 現在日付
     * @param session セッション
     * @return 更新結果
     */
    public async updateLineInfoPlanChange(
        lineNo: string,
        plan:string,
        planName:string,
        nowTime: number,
        transaction:Transaction = null,
    ): Promise<number>{
        const sequelize = await usePsql();
        const result = await sequelize.query("UPDATE lines SET resale_plan_id = :plan , resale_plan_name = :planName, modify_flag = true, updated_at = :nowTime where line_id = :lineId ", {
            replacements: {
                plan,
                nowTime: fromUnixTime(nowTime / 1000),
                lineId:lineNo,
                planName,
            },
            transaction,
            type: QueryTypes.UPDATE,
        });
        return result[1];
    }

    /**
     * 料金プランIDリストを取得する
     * @param potalPlanID String 変更後卸ポータルプランID
     * @return List&lt;String&gt;料金プランIDリスト
     * @throws SQLException
     */
    public async getResalePlanIdPlan(potalPlanID: string):Promise<string[]>{
        const sequelize = await usePsql();
        return await retryQuery(
            this.context,
            "getResalePlanIdPlan",
            async () => {
                const sql= [];
                sql.push("select resale_plan_id ");
                sql.push("from plans ");
                sql.push("where plan_id = :potalPlanID ");
                const queryResult = await sequelize.query(sql.join(" "), {
                    replacements: { potalPlanID: parseInt(potalPlanID,10) },
                    type: QueryTypes.SELECT,
                }) as any[];
                return queryResult.map((row) => row.resale_plan_id);
            },
            this.apDbRetryMaxCnt,
            this.apDbRetryInterval,
        );
    }

    /**
     * プラン種別リストを取得する
     * @param potalPlanIDPre String 変更前卸ポータルプランID
     * @return List&lt;String&gt;プラン種別リスト
     * @throws SQLException
     */
    public async getPlanClass(potalPlanIDPre: string):Promise<number[]>{
        const sequelize = await usePsql();
        return await retryQuery(
            this.context,
            "getPlanClass",
            async () => {
                const sql= [];
                sql.push("select plan_class ");
                sql.push("from plans ");
                sql.push("where plan_id = :potalPlanIDPre ");
                const queryResult = await sequelize.query(sql.join(" "), {
                    replacements: { potalPlanIDPre: parseInt(potalPlanIDPre,10) },
                    type: QueryTypes.SELECT,
                }) as any[];
                return queryResult.map((row) => row.plan_class);
            },
            this.apDbRetryMaxCnt,
            this.apDbRetryInterval,
        );
    }

    public async getTenantIdByLineIds(lineIds: string[]): Promise<{line_id: string, nnumber: string, tenant_id: string}[]> {
        const sequelize = await usePsql();
        return await retryQuery(
            this.context,
            "getTenantIdByLineIds",
            async () => {
                const sql = [];
                sql.push("SELECT l.line_id, l.nnumber, tn.tenant_id");
                sql.push("FROM lines l")
                sql.push("JOIN tenant_nnumbers tn ON tn.nnumber = l.nnumber");
                sql.push("WHERE l.line_id IN (:lineIds)");
                const queryResult = await sequelize.query(sql.join(" "), {
                    replacements: { lineIds },
                    type: QueryTypes.SELECT,
                }) as any[];
                return queryResult.map((row) => ({
                    line_id: row.line_id,
                    nnumber: row.nnumber,
                    tenant_id: row.tenant_id,
                }));
            },
            this.apDbRetryMaxCnt,
            this.apDbRetryInterval,
        );
    }
}