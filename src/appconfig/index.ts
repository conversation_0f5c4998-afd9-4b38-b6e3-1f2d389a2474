/* tslint:disable:no-string-literal */
import { AppConfigurationClient } from "@azure/app-configuration";
import { InvocationContext } from "@azure/functions";
import { CoreConfig } from "@/types/config";
import CoreMvnoConfigKey, {
    CoreMvnoLocalConfigMap,
} from "@/types/coreMvnoConfig";

export default class AppConfig {
    private static isLocalConfig =
        process.env.LOCAL_SETTINGS_CONFIG || process.env.RUN_IN_DOCKER
            ? true
            : false;
    private static configClient: AppConfigurationClient =
        AppConfig.isLocalConfig
            ? null
            : new AppConfigurationClient(
                  process.env.APP_CONFIGURATION_CONNECTION_STRING,
              );

    private static DB_CONFIG: { [key: string]: string } = {};

    private static STORAGE_CONFIG: { [key: string]: string } = {};

    private static CORE_MVNO_CONFIG: { [key: string]: string } = {};

    /**
     * Get database configuration from Azure App Configuration
     * @param context
     */
    public static async getDBConfig(context: InvocationContext) {
        if (Object.keys(AppConfig.DB_CONFIG).length > 0) {
            return AppConfig.DB_CONFIG;
        }

        if (AppConfig.isLocalConfig) {
            // NOTE postgresql use different key for Core Service !
            AppConfig.DB_CONFIG["DATABASE_CORE_POSTGRESQL_CONNECTION_STRING"] =
                process.env["POSTGRE_CONNECTION_STRING"] ||
                process.env["DATABASE_POSTGRESQL_CONNECTION_STRING"];
            AppConfig.DB_CONFIG["DATABASE_REDIS_HOSTNAME"] =
                process.env["DATABASE_REDIS_HOSTNAME"] || "127.0.0.1";
            AppConfig.DB_CONFIG["DATABASE_REDIS_PORT"] =
                process.env["DATABASE_REDIS_PORT"] || "6379";
            AppConfig.DB_CONFIG["DATABASE_REDIS_ACCESS_KEY"] =
                process.env["DATABASE_REDIS_ACCESS_KEY"] || "";
            AppConfig.DB_CONFIG["DATABASE_MONGODB_CONNECTION_STRING"] =
                process.env["MONGO_CONNECTION_STRING"] ||
                process.env["DATABASE_MONGODB_CONNECTION_STRING"];
            AppConfig.DB_CONFIG["DATABASE_MONGODB_CONNECTION_OPTIONS"] =
                process.env["DATABASE_MONGODB_CONNECTION_OPTIONS"] || "";
        } else {
            const settings = AppConfig.configClient.listConfigurationSettings({
                keyFilter: "DATABASE_*",
            });
            for await (const setting of settings) {
                AppConfig.DB_CONFIG[setting.key] = setting.value;
            }
        }
        return AppConfig.DB_CONFIG;
    }

    public static async getStorageConfig(context: InvocationContext) {
        if (Object.keys(AppConfig.STORAGE_CONFIG).length > 0) {
            return AppConfig.STORAGE_CONFIG;
        }

        if (AppConfig.isLocalConfig) {
            AppConfig.STORAGE_CONFIG["STORAGE_CONNECTION_STRING"] =
                process.env["STORAGE_CONNECTION_STRING"] ||
                process.env["AZURE_ACCOUNT_KEY"];
            AppConfig.STORAGE_CONFIG["STORAGE_CONTAINER_NAME"] =
                process.env["STORAGE_CONTAINER_NAME"] ||
                process.env["AZURE_ACCOUNT_NAME"];
            AppConfig.STORAGE_CONFIG["STORAGE_KAISEN_INFO_CONTAINER_NAME"] =
                process.env["STORAGE_KAISEN_INFO_CONTAINER_NAME"];
            AppConfig.STORAGE_CONFIG["STORAGE_STORAGE_ACCOUNT"] = process.env[
                "STORAGE_STORAGE_ACCOUNT"
                ];
            AppConfig.STORAGE_CONFIG["STORAGE_STORAGE_KEY"] = process.env[
                "STORAGE_STORAGE_KEY"
                ];
        } else {
            const settings = AppConfig.configClient.listConfigurationSettings({
                keyFilter: "STORAGE_*",
            });
            for await (const setting of settings) {
                AppConfig.STORAGE_CONFIG[setting.key] = setting.value;
            }
        }
        return AppConfig.STORAGE_CONFIG;
    }

    public static getCoreConfig(context: InvocationContext): CoreConfig {
        return {
            DB_NAME: {
                CORE_API_SERVICE: "konnect",
            },
            THROTTLING: {
                /**
                 * timeout for the throttle window in seconds
                 *
                 * should be set to maximum timeout of the function
                 */
                WINDOW_SIZE: +process.env.FUNCTION_TIMEOUT_SECONDS || 60,
            },
            CORE_SWIMMY_API: {
                POLLING: {
                    MAX_CONNECTION:
                        +process.env.CORE_SWIMMY_API_POLLING_MAX_CONNECTION ||
                        7,
                    SERVICE_BUS_CONNECTION_STRING:
                        process.env.SERVICEBUS_CONNECTION_STRING,
                    SERVICE_BUS_QUEUE_NAME:
                        process.env.SERVICEBUS_QUEUE_CORE_SWIMMY_API_REQUESTS,
                },
                ENDPOINT_ORIGIN: process.env.CORE_SWIMMY_API_ENDPOINT_ORIGIN,
                ENDPOINT_PATH: process.env.CORE_SWIMMY_API_ENDPOINT_PATH,
                RUN_WITH_MOCK: process.env.CORE_SWIMMY_API_MOCK_MODE === "true",
                MASKING_ONHOLD_MINUTE: process.env.CORE_SWIMMY_API_MASKING_ONHOLD_MINUTE || "15",
                MASKING_ONHOLD_SKIP_HOURS: process.env.CORE_SWIMMY_API_MASKING_ONHOLD_SKIP_HOURS || "[21]",
                MESSAGE_DELAY_SECONDS: process.env.SWIMMY_API_MESSAGE_DELAY_SECONDS || "60",
            },
            CORE_TPC_API: {
                RUN_WITH_MOCK: process.env.CORE_TPC_API_MOCK_MODE === "true",
            },
            NOTIFICATION: {
                SERVICE_BUS_CONNECTION_STRING:
                    process.env.SERVICEBUS_CONNECTION_STRING,
                SERVICE_BUS_QUEUE_NAME:
                    process.env.SERVICEBUS_QUEUE_NOTIFICATION,
            },
            RESERVED_SO: {
                SERVICE_BUS_CONNECTION_STRING:
                    process.env.SERVICEBUS_CONNECTION_STRING,
                SERVICE_BUS_QUEUE_NAME:
                    process.env.SERVICEBUS_QUEUE_RESERVED_SO,
                API_ENDPOINT:
                    process.env.RESERVED_SO_API_ENDPOINT,
                TIMER: {
                    SCHEDULE: process.env.RESERVED_SO_TIMER_SCHEDULE ?? "0 */15 * * * *"
                }
            },
            AUTO_LINE_GROUP_MOD_BUCKET: {
                TIMER: {
                    SCHEDULE: process.env.AUTO_LINE_GROUP_MOD_BUCKET_TIMER_SCHEDULE ?? "0 0 7,8,23 * * *"
                }
            },
            RESERVE_REWRITE: {
                TIMER: {
                    SCHEDULE: process.env.RESERVE_REWRITE_TIMER_SCHEDULE ?? "0 0 0 * * *",
                }
            },
            API_SERVICE: {
                FRONT_EXTERNAL_API: process.env.FRONT_SERVICE_EXTERNAL_API || "",
                // NOTE using same environment variable as `RESERVED_SO.API_ENDPOINT`
                CORE_EXTERNAL_API: process.env.RESERVED_SO_API_ENDPOINT || "",
                RUN_WITH_MOCK: process.env.FRONT_SERVICE_MOCK_MODE === "true",
            },
            /** add filter on cronjob when old instance is still running */
            V1_HAS_STOPPED_RUNNING: process.env.V1_HAS_STOPPED_RUNNING === "true",
        };
    }

    /**
     * Fetch and save Core MVNO configuration from App Configuration
     *
     * - NOTE: This function will be called in base handler so no need to call it in other places
     * - NOTE: for unit test, this function should be called inside `beforeAll`
     * @param context
     */
    public static async loadCoreMvnoConfig(
        context: InvocationContext,
    ): Promise<void> {
        if (Object.keys(AppConfig.CORE_MVNO_CONFIG).length > 0) {
            return;
        }
        if (typeof context?.log === "function") {
            context.log("loadCoreMvnoConfig start", AppConfig.isLocalConfig);
        }
        if (AppConfig.isLocalConfig) {
            for (const [key, value] of Object.entries(CoreMvnoLocalConfigMap)) {
                AppConfig.CORE_MVNO_CONFIG[`CORE_MVNO_${key}`] =
                    process.env[value];
            }
            if (typeof context?.log === "function") {
                context.log(
                    "loadCoreMvnoConfig local",
                    AppConfig.CORE_MVNO_CONFIG,
                );
            }
        } else {
            const settings = AppConfig.configClient.listConfigurationSettings({
                keyFilter: "CORE_MVNO_*",
            });
            for await (const setting of settings) {
                AppConfig.CORE_MVNO_CONFIG[setting.key] = setting.value;
            }
        }
    }

    /**
     * Get Core MVNO configuration value by key
     *
     * NOTE: use `configHelper` if possible
     * @param key key to get from CORE_MVNO_CONFIG
     * @returns
     */
    public static getCoreMvnoConfig(key: CoreMvnoConfigKey): string {
        const value = AppConfig.CORE_MVNO_CONFIG[`CORE_MVNO_${key}`];
        if (value === null || value === undefined) {
            // make it throw error like in `config.get<T>(key)`
            throw new Error(`Key not found: CORE_MVNO_${key}`);
        }
        return value;
    }
}
