import "@/types/string.extension";
import {
    app,
    PostInvocationContext,
    PreInvocationContext,
} from "@azure/functions";
// import handler here
import { BaseHandler } from "@/middleware/BaseHandler";
import { __HealthCheckHandler__ } from "@/functions/__HealthCheckHandler__";
import { LinesDelHandler } from "@/functions/LinesDelHandler";
import { ReissueSimHandler } from "./functions/ReissueSimHandler";
import { OptionNwpassModifyHandler } from "./functions/OptionNwpassModifyHandler";
import { NetworkContractChangeHandler } from "./functions/NetworkContractChangeHandler";
import { LineSuspendHandler } from "./functions/LineSuspendHandler";
import { LineTerminalListAquisitionHandler } from "./functions/LineTerminalListAquisitionHandler";
import { FrontSoCancelHandler } from "./functions/FrontSoCancelHandler";
import { BasePortalHandler } from "./middleware/BasePortalHandler";
import { GetMaintenanceModeStatusHandler } from "./functions/portal/GetMaintenanceModeStatusHandler";
import { ToggleMaintenanceModeStatusHandler } from "./functions/portal/ToggleMaintenanceModeStatusHandler";
import { SearchCoreSwimmyApiLogHandler } from "./functions/portal/SearchCoreSwimmyApiLogHandler";
import { GetCoreSwimmyApiLogDetailHandler } from "./functions/portal/GetCoreSwimmyApiLogDetailHandler";
import { ResendCoreSwimmyTransactionHandler } from "./functions/portal/ResendCoreSwimmyTransactionHandler";
import { UpdateCoreSwimmyApiLogStatusHandler } from "./functions/portal/UpdateCoreSwimmyApiLogStatusHandler";
import { BaseQueueHandler } from "./middleware/BaseQueueHandler";
import { LineInfoAcquisitionHandler } from "./functions/LineInfoAcquisitionHandler";
import { SolistHandler } from "./functions/SolistHandler";
import { PreLineAddHandler } from "./functions/PreLineAddHandler";
import { LineGroupRegistAcquisitionHandler } from "./functions/LineGroupRegistAcquisitionHandler";
import AppConfig from "./appconfig";
import HandleCoreSwimmyApiRequestQueue from "./functions/triggers/HandleCoreSwimmyApiRequestQueue";
import { ExecReservedSOHandler } from "./functions/triggers/ExecReservedSOHandler";
import HandleReservedSOQueue from "./functions/triggers/HandleReservedSOQueue";
import { PhonePlanChangeHandler } from "@/functions/PhonePlanChangeHandler";
import { LineGroupInfoAcquisitionHandler } from "@/functions/LineGroupInfoAcquisitionHandler";
import { BaseExternalApiHandler } from "@/middleware/BaseExternalApiHandler";
import { SODetailInfoConsultHandler } from "@/functions/SODetailInfoConsultHandler";
import { LineGroupUseAcquisitionHandler } from "@/functions/LineGroupUseAcquisitionHandler";
import { LineEnableHandler } from "@/functions/LineEnableHandler";
import { LineUseAcquisitionHandler } from "./functions/LineUseAcquisitionHandler";
import { SoCancelHandler } from "./functions/SoCancelHandler";
import { LineGroupModBucketAcquisitionHandler } from "./functions/LineGroupModBucketAcquisitionHandler";
import LineStatusAcquisitionHandler from "./functions/LineStatusAcquisitionHandler";
import { LineGroupPlanChangeHandler } from "./functions/LineGroupPlanChangeHandler";
import { LinePlanAcquisitionHandler } from "./functions/LinePlanAcquisitionHandler";
import { LineDataGiftHandler } from "@/functions/LineDataGiftHandler";
import { LineCouponAcquisitionHandler } from "./functions/LineCouponAcquisitionHandler";
import { LineGroupCouponAcquisitionHandler } from "./functions/LineGroupCouponAcquisitionHandler";
import { LineAddAcquisitionHandler } from "@/functions/LineAddAcquisitionHandler";
import { LineGroupAddAcquisitionHandler } from "@/functions/LineGroupAddAcquisitionHandler";
import { LineGroupModifyAcquisitionHandler } from "./functions/LineGroupModifyAcquisitionHandler";
import AutoLineGroupModbucketHandler from "@/functions/AutoLineGroupModbucket";
import BaseTimerHandler from "@/middleware/BaseTimerHandler";
import { ResetAutoApiBatchTimeHandler } from "@/functions/ResetAutoApiBatchTimeHandler";
import ReserveRewriteHandler from "@/functions/triggers/ReserveRewriteHandler";
import { GetTenantConnectionsHandler } from "@/functions/portal/GetTenantConnectionsHandler";
import { ResetTenantConnectionsHandler } from "@/functions/portal/ResetTenantConnectionsHandler";
import { GetCronjobListHandler } from "@/functions/portal/GetCronjobListHandler";
import { RestartCronjobHandler } from "@/functions/portal/RestartCronjobHandler";
import { ChangeKaisenBbUnibaHandler } from "@/functions/portal/ChangeKaisenBbUnibaHandler";
import { GetConnectionPoolStatusHandler } from "@/functions/portal/GetConnectionPoolStatusHandler";

// register handler here
app.http("HealthCheckAPI", {
    methods: ["GET", "POST"],
    route: "healthcheck",
    authLevel: "anonymous",
    handler: __HealthCheckHandler__,
});

// 01 回線基本情報参照
app.http("LineInfoAcquisitionAPI", {
    methods: ["POST"],
    route: "Lines/info",
    authLevel: "anonymous",
    handler: BaseExternalApiHandler(LineInfoAcquisitionHandler),
});

// 02 回線運用情報参照
app.http("LineUseAcquisitionAPI", {
    methods: ["POST"],
    route: "Lines/traffic",
    authLevel: "anonymous",
    handler: BaseExternalApiHandler(LineUseAcquisitionHandler),
});

// 03 回線追加チャージ(クーポン)容量・期間追加
app.http("LineAddAcquisitionAPI", {
    methods: ["POST"],
    route: "Lines/charge",
    authLevel: "anonymous",
    handler: BaseExternalApiHandler(LineAddAcquisitionHandler),
});

// 04 回線クーポンオン・オフ
app.http("LinesCouponAcquisitionAPI", {
    methods: ["POST"],
    route: "Lines/coupon",
    authLevel: "anonymous",
    handler: BaseExternalApiHandler(LineCouponAcquisitionHandler),
})

// 05 回線アクティベート/ディアクティベート
app.http("LineStatusAcquisitionAPI", {
    methods: ["POST"],
    route: "Lines/activation",
    authLevel: "anonymous",
    handler: BaseExternalApiHandler(LineStatusAcquisitionHandler),
});

// 06 回線プラン変更機能
app.http("LinePlanAcquisitionAPI", {
    methods: ["POST"],
    route: "Lines/plan",
    authLevel: "anonymous",
    handler: BaseExternalApiHandler(LinePlanAcquisitionHandler),
});

// 07 回線リスト確認
app.http("LineTerminalListAquisitionAPI", {
    methods: ["POST"],
    route: "Lines/list",
    authLevel: "anonymous",
    handler: BaseExternalApiHandler(LineTerminalListAquisitionHandler),
});

// 08 回線グループ基本情報参照
app.http("LineGroupInfoAcquisitionAPI", {
    methods: ["POST"],
    route: "LinesGroup/info",
    authLevel: "anonymous",
    handler: BaseExternalApiHandler(LineGroupInfoAcquisitionHandler),
});

// 09 回線グループ運用情報参照
app.http("LineGroupUseAcquisitionAPI", {
    methods: ["POST"],
    route: "LinesGroup/traffic",
    authLevel: "anonymous",
    handler: BaseExternalApiHandler(LineGroupUseAcquisitionHandler),
});

// 10 回線グループ追加チャージ(クーポン)容量・期間追加
app.http("LineGroupAddAcquisitionAPI", {
    methods: ["POST"],
    route: "LinesGroup/charge",
    authLevel: "anonymous",
    handler: BaseExternalApiHandler(LineGroupAddAcquisitionHandler),
})

// 12 回線グループクーポンオン・オフ
app.http("LineGroupCouponAcquisitionAPI", {
    methods: ["POST"],
    route: "LinesGroup/coupon",
    authLevel: "anonymous",
    handler: BaseExternalApiHandler(LineGroupCouponAcquisitionHandler),
});

// 13 回線グループ新規作成/廃止
app.http("LineGroupRegistAcquisition", {
    methods: ["POST"],
    route: "LinesGroup/LineGroup_regist",
    authLevel: "function",
    handler: BaseExternalApiHandler(LineGroupRegistAcquisitionHandler),
});

// 14 回線グループ所属回線変更機能
app.http("LineGroupModifyAcquisitionAPI", {
    methods: ["POST"],
    route: "LinesGroup/LineGroup_modify",
    authLevel: "anonymous",
    handler: BaseExternalApiHandler(LineGroupModifyAcquisitionHandler),
});

// 15 回線グループ基本容量変更
app.http("LineGroupModBucketAcquisitionAPI", {
    methods: ["POST"],
    route: "LinesGroup/LineGroup_mod_bucket",
    authLevel: "anonymous",
    handler: BaseExternalApiHandler(LineGroupModBucketAcquisitionHandler),
});

// 16 SO一覧
app.http("SoListAPI", {
    methods: ["POST"],
    route: "solist",
    authLevel: "anonymous",
    handler: BaseExternalApiHandler(SolistHandler),
});

// 17 SO詳細
app.http("SODetailInfoConsultAPI", {
    methods: ["POST"],
    route: "sodesc",
    authLevel: "anonymous",
    handler: BaseExternalApiHandler(SODetailInfoConsultHandler),
});

// 18 予約キャンセル
app.http("SoCancelAPI", {
    methods: ["POST"],
    route: "so_cancel",
    authLevel: "anonymous",
    handler: BaseExternalApiHandler(SoCancelHandler),
});

// 19 回線データ譲渡
app.http("LineDataGiftAPI", {
    methods: ["POST"],
    route: "Lines/gift",
    authLevel: "anonymous",
    handler: BaseExternalApiHandler(LineDataGiftHandler),
})

// 20 回線グループプラン変更
app.http("LineGroupPlanChangeAPI", {
    methods: ["POST"],
    route: "LinesGroup/plan",
    authLevel: "anonymous",
    handler: BaseExternalApiHandler(LineGroupPlanChangeHandler)
})

// 51 仮登録回線追加
app.http("PreLineAddAPI", {
    methods: ["POST"],
    route: "Lines/preadd",
    authLevel: "anonymous",
    handler: BaseHandler(PreLineAddHandler),
});

// 52 回線を廃止するためのIF
app.http("LinesDelAPI", {
    methods: ["GET", "POST"],
    route: "Lines/del",
    authLevel: "anonymous",
    handler: BaseHandler(LinesDelHandler),
});

// 53 回線SIM再発行
app.http("ReissueSimAPI", {
    methods: ["POST"],
    route: "Lines/sim",
    authLevel: "anonymous",
    handler: BaseHandler(ReissueSimHandler),
});

// 54 回線オプション_NW暗証番号変更
app.http("OptionNwpassModifyAPI", {
    methods: ["POST"],
    route: "Lines/lineop",
    authLevel: "anonymous",
    handler: BaseHandler(OptionNwpassModifyHandler),
});

// 55 回線黒化
app.http("LineEnableAPI", {
    methods: ["POST"],
    route: "Lines/enable",
    authLevel: "anonymous",
    handler: BaseHandler(LineEnableHandler),
});

// 56 フロント用予約キャンセル
app.http("FrontSoCancelAPI", {
    methods: ["POST"],
    route: "so_frontcancel",
    authLevel: "anonymous",
    handler: BaseHandler(FrontSoCancelHandler),
});

// 58 利用中断/再開/サスペンド
app.http("LineSuspendAPI", {
    methods: ["POST"],
    route: "Lines/suspend",
    authLevel: "anonymous",
    handler: BaseHandler(LineSuspendHandler),
});

// 59 0035でんわプラン変更
app.http("PhonePlanChangeAPI", {
    methods: ["POST"],
    route: "Lines/voice",
    authLevel: "anonymous",
    handler: BaseExternalApiHandler(PhonePlanChangeHandler),
});

// 60 NW契約変更
app.http("NetworkContractChangeAPI", {
    methods: ["POST"],
    route: "Lines/access",
    authLevel: "anonymous",
    handler: BaseHandler(NetworkContractChangeHandler),
});

// Portal Front API:
app.http("GetMaintenanceModeStatusAPI", {
    methods: ["POST"],
    route: "portal/coreswimmylog/get_maintenance_mode",
    authLevel: "function",
    handler: BasePortalHandler(GetMaintenanceModeStatusHandler),
});

app.http("ToggleMaintenanceModeStatusAPI", {
    methods: ["POST"],
    route: "portal/coreswimmylog/toggle_maintenance_mode",
    authLevel: "function",
    handler: BasePortalHandler(ToggleMaintenanceModeStatusHandler),
});

app.http("SearchCoreSwimmyApiLogAPI", {
    methods: ["POST"],
    route: "portal/coreswimmylog/search",
    authLevel: "function",
    handler: BasePortalHandler(SearchCoreSwimmyApiLogHandler),
});

app.http("GetCoreSwimmyApiLogDetailAPI", {
    methods: ["GET"],
    route: "portal/coreswimmylog/log_by_so_id/{requestOrderId}", // NOTE requestOrderId changed to id (_id)
    authLevel: "function",
    handler: BasePortalHandler(GetCoreSwimmyApiLogDetailHandler),
});

app.http("ResendCoreSwimmyTransactionAPI", {
    methods: ["POST"],
    route: "portal/coreswimmylog/re_send",
    authLevel: "function",
    handler: BasePortalHandler(ResendCoreSwimmyTransactionHandler),
});

app.http("UpdateCoreSwimmyApiLogStatusAPI", {
    methods: ["POST"],
    route: "portal/coreswimmylog/status_update",
    authLevel: "function",
    handler: BasePortalHandler(UpdateCoreSwimmyApiLogStatusHandler),
});

app.http("ResetAutoApiBatchTimeAPI", {
    methods: ["POST"],
    route: "portal/autobatchtime/reset",
    authLevel: "anonymous",
    handler: BasePortalHandler(ResetAutoApiBatchTimeHandler)
})

app.http("GetTenantConnectionsAPI", {
    methods: ["GET"],
    route: "portal/tenants/connections",
    authLevel: "function", // 認可が必要なら変更
    handler: BasePortalHandler(GetTenantConnectionsHandler),
});

app.http("ResetTenantConnectionsAPI", {
    methods: ["POST"],
    route: "portal/tenants/reset_connections",
    authLevel: "function", // 認可が必要なら変更
    handler: BasePortalHandler(ResetTenantConnectionsHandler),
});

app.http("GetCronjobListAPI", {
    methods: ["GET"],
    route: "portal/cronjob/list",
    authLevel: "function", // 認可が必要なら変更
    handler: BasePortalHandler(GetCronjobListHandler),
});

app.http("RestartCronjobAPI", {
    methods: ["POST"],
    route: "portal/cronjob/restart",
    authLevel: "function", // 認可が必要なら変更
    handler: BasePortalHandler(RestartCronjobHandler),
})

app.http("ChangeKaisenBbUnibaAPI", {
    methods: ["POST"],
    route: "portal/kaisen/bb_uniba",
    authLevel: "function",
    handler: BasePortalHandler(ChangeKaisenBbUnibaHandler)
})

app.http("GetConnectionPoolStatusAPI", {
    methods: ["GET"],
    route: "portal/db/connection-pool-status",
    authLevel: "function",
    handler: BasePortalHandler(GetConnectionPoolStatusHandler),
})

app.serviceBusQueue("HandleCoreSwimmyApiRequestQueue", {
    connection: "SERVICEBUS_CONNECTION_STRING",
    queueName:
        AppConfig.getCoreConfig(null).CORE_SWIMMY_API.POLLING
            .SERVICE_BUS_QUEUE_NAME,
    handler: BaseQueueHandler(HandleCoreSwimmyApiRequestQueue),
});

app.timer("AutoLineGroupModbucketTimer", {
    schedule: AppConfig.getCoreConfig(null).AUTO_LINE_GROUP_MOD_BUCKET.TIMER.SCHEDULE,
    handler: BaseTimerHandler(AutoLineGroupModbucketHandler),
})

app.timer("ExecReservedSOTimer", {
    schedule: AppConfig.getCoreConfig(null).RESERVED_SO.TIMER.SCHEDULE,
    handler: ExecReservedSOHandler.Handler,
});

app.timer("ReserveRewriteTimer", {
    schedule: AppConfig.getCoreConfig(null).RESERVE_REWRITE.TIMER.SCHEDULE,
    handler: BaseTimerHandler(ReserveRewriteHandler.Handler),
})

app.serviceBusQueue("HandleReservedSOQueue", {
    connection: "SERVICEBUS_CONNECTION_STRING",
    queueName: AppConfig.getCoreConfig(null).RESERVED_SO.SERVICE_BUS_QUEUE_NAME,
    handler: BaseQueueHandler(HandleReservedSOQueue),
});