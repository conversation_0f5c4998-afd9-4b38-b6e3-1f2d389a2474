import config from "config";
import AbstractMvnoBaseCommon from "@/core/common/AbstractMvnoBaseCommon";
import ExtendedInvocationContext from "@/types/ExtendedInvocationContext";
import MsgKeysConstants from "@/core/constant/MsgKeysConstants";

export class ExecReservedSOProperties extends AbstractMvnoBaseCommon {
    /** プロパティ項目名(API処理IDサーバ識別) */
    public static readonly APIPROCESSIDSERVER = "mvno.ApiProcessIDServer";
    /** プロパティ項目名(TPC接続不可時間) */
    public static readonly TPCREGULATIONTIME = "mvno.TpcRegulationTime";
    /** プロパティ項目名(TPC最大同時接続数) */
    public static readonly TPCMAXCONNECTIONS = "mvno.TpcMaxConnections";
    /** プロパティ項目名(実行キュー積み込み処理間隔(ミリ秒)) */
    public static readonly PRODUCERPERIOD = "mvno.ProducerPeriod";
    /** プロパティ項目名(実行キュー送信処理間隔(ミリ秒)) */
    public static readonly WORKERPERIOD = "mvno.WorkerPeriod";
    /** プロパティ項目名(アプリケーション用DBユーザID) */
    public static readonly APDBUSERID = "mvno.ApDbUserId";
    /** プロパティ項目名(アプリケーション用DBユーザPW) */
    public static readonly APDBUSERPW = "mvno.ApDbUserPw";
    /** プロパティ項目名(アプリケーション用DB接続ポート) */
    public static readonly APDBPORT = "mvno.ApDbPort";

    /** API処理IDサーバ識別 */
    private apiProcessIDServer: string = config.get<string>(ExecReservedSOProperties.APIPROCESSIDSERVER);
    /** TPC接続不可時間 */
    private tpcRegulationTime: string = config.get<string>(ExecReservedSOProperties.TPCREGULATIONTIME);
    /** TPC最大同時接続数 */
    private tpcMaxConnections: number = config.get<number>(ExecReservedSOProperties.TPCMAXCONNECTIONS);
    /** 実行キュー積み込み処理間隔ミリ秒)) */
    private producerPeriod: number = config.get<number>(ExecReservedSOProperties.PRODUCERPERIOD);
    /** 実行キュー送信処理間隔ミリ秒)) */
    private workerPeriod: number = config.get<number>(ExecReservedSOProperties.WORKERPERIOD);
    /** アプリケーション用DBユーザID */
    private apDbUserId: string = config.get<string>(ExecReservedSOProperties.APDBUSERID);
    /** アプリケーション用DBユーザPW */
    private apDbUserPw: string = config.get<string>(ExecReservedSOProperties.APDBUSERPW);
    /** アプリケーション用DB接続ポート */
    private apDbPort: string = config.get<string>(ExecReservedSOProperties.APDBPORT);

    private tpcRegulationStart: string = null;
    private tpcRegulationEnd: string = null;

    private static instance: ExecReservedSOProperties = null;

    /**
     * シングルトンインスタンスを取得する
     * @param context コンテキスト
     * @returns ExecReservedSOPropertiesのインスタンス
     */
    public static getInstance(context: ExtendedInvocationContext): ExecReservedSOProperties {
        if (!this.instance) {
            this.instance = new ExecReservedSOProperties(context);
        }
        return this.instance;
    }

    /**
     * コンストラクタ
     * @param context コンテキスト
     */
    constructor(context: ExtendedInvocationContext) {
        super(null, context);
        this.loadTpcRegulationTime();
    }

    /**
     * TPC接続不可開始時間、終了時間設定
     * @returns TPC規制開始時間、終了時間を設定できた場合はtrue、設定に失敗した場合はfalseを返却
     */
    private loadTpcRegulationTime(): boolean {
        this.tpcRegulationStart = null;
        this.tpcRegulationEnd = null;

        if (!this.tpcRegulationTime) {
            this.context.debug("TPC接続不可開始時間、終了時間設定に失敗しました。");
            return false;
        }

        const regex1 = /^\s*\d{1,2}:\d{2}\s*-\s*\d{1,2}:\d{2}\s*$/;
        const regex2 = /\d{1,2}:\d{2}/g;

        if (!regex1.test(this.tpcRegulationTime)) {
            this.context.debug("TPC接続不可時間のフォーマットが不正です。");
            return false;
        }

        const matches = this.tpcRegulationTime.match(regex2);
        if (matches && matches.length >= 2) {
            this.tpcRegulationStart = `${matches[0]}:00`;
            this.tpcRegulationEnd = `${matches[1]}:00`;
            return true;
        }

        this.context.debug("TPC接続不可時間のフォーマットが不正です。");
        return false;
    }

    /**
     * 必須項目チェック
     * @returns 必須項目が全て指定されていた場合は空文字、指定されていない必須項目があった場合はその項目名
     */
    public requireCheck(): string {
        // チェック対象項目リストを取得する。
        const list = this.getCheckList();
        for (const item of list) {
            if (!config.has(item) || config.get(item) === "") {
                return item;
            }
        }
        return "";
    }

    /**
     * 必須項目チェック（静的メソッド）
     * @returns 必須項目が全て指定されていた場合は空文字、指定されていない必須項目があった場合はその項目名
     */
    public static requireCheck(): string {
        const requiredFields = [
            ExecReservedSOProperties.APIPROCESSIDSERVER,
            ExecReservedSOProperties.TPCREGULATIONTIME,
            ExecReservedSOProperties.TPCMAXCONNECTIONS,
            ExecReservedSOProperties.PRODUCERPERIOD,
            ExecReservedSOProperties.WORKERPERIOD,
            ExecReservedSOProperties.APDBUSERID,
            ExecReservedSOProperties.APDBUSERPW,
            ExecReservedSOProperties.APDBPORT,
        ];

        for (const field of requiredFields) {
            if (!config.has(field) || config.get(field) === "") {
                return field;
            }
        }

        return "";
    }

    /**
     * 必須チェック対象項目設定
     * @returns チェック対象項目リスト
     */
    private getCheckList(): string[] {
        const list: string[] = [];
        list.push(ExecReservedSOProperties.APIPROCESSIDSERVER);
        list.push(ExecReservedSOProperties.TPCREGULATIONTIME);
        list.push(ExecReservedSOProperties.TPCMAXCONNECTIONS);
        list.push(ExecReservedSOProperties.PRODUCERPERIOD);
        list.push(ExecReservedSOProperties.WORKERPERIOD);
        list.push(ExecReservedSOProperties.APDBUSERID);
        list.push(ExecReservedSOProperties.APDBUSERPW);
        list.push(ExecReservedSOProperties.APDBPORT);
        return list;
    }

    /**
     * TPC接続不可時間フォーマットチェック
     * @returns 開始時間が終了時間より前の場合はtrue、それ以外はfalse
     */
    public chkTpcRegulationTime(): boolean {
        const startTime = this.getTimeFromStr(this.getTpcRegulationStart());
        if (startTime === null) {
            return false;
        }

        const endTime = this.getTimeFromStr(this.getTpcRegulationEnd());
        if (endTime === null) {
            return false;
        }

        return startTime.getTime() < endTime.getTime();
    }

    /**
     * API処理IDサーバ識別チェック
     * @returns 3文字の場合はtrue、それ以外はfalse
     */
    public chkApiProcessIDServer(): boolean {
        if (this.getApiProcessIDServer() === null) {
            return false;
        }
        return this.getApiProcessIDServer().length === 3;
    }

    /**
     * TPC最大同時接続数チェック
     * @returns 1以上60以下の場合はtrue、それ以外はfalse
     */
    public chkTpcMaxConnections(): boolean {
        const val = this.getTpcMaxConnections();
        if (val === null) {
            return false;
        }
        return val >= 1 && val <= 60;
    }

    /**
     * 実行キュー積み込み処理間隔(ミリ秒)チェック
     * @returns 値が設定されている場合はtrue、それ以外はfalse
     */
    public chkProducerPeriod(): boolean {
        return this.getProducerPeriod() !== null;
    }

    /**
     * 実行キュー送信処理間隔(ミリ秒)チェック
     * @returns 値が設定されている場合はtrue、それ以外はfalse
     */
    public chkWorkerPeriod(): boolean {
        return this.getWorkerPeriod() !== null;
    }

    /**
     * 時刻文字列をDate型に変換する
     * @param timeStr 時刻文字列 (HH:mm:ss形式)
     * @returns Date型の時刻、変換できない場合はnull
     */
    private getTimeFromStr(timeStr: string): Date | null {
        if (timeStr === null) {
            return null;
        }

        // 24:00:00の特殊ケース処理
        if (timeStr === "24:00:00") {
            const date = new Date();
            date.setHours(23, 59, 59, 999);
            return date;
        }

        try {
            // 今日の日付に時刻を設定
            const [hours, minutes, seconds] = timeStr.split(':').map(Number);
            const date = new Date();
            date.setHours(hours, minutes, seconds, 0);
            return date;
        } catch (e) {
            this.context.error(e.message);
            return null;
        }
    }

    /**
     * プロパティ値取得(API処理IDサーバ識別)
     * @returns ApiProcessIDServer設定値
     */
    public getApiProcessIDServer(): string {
        return this.apiProcessIDServer;
    }

    /**
     * プロパティ値取得(TPC接続不可時間)
     * @returns TpcRegulationTime設定値
     */
    public getTpcRegulationTime(): string {
        return this.tpcRegulationTime;
    }

    /**
     * プロパティ値取得(TPC接続不可開始時間)
     * @returns TpcRegulationStart設定値+":00"
     */
    public getTpcRegulationStart(): string | null {
        return this.tpcRegulationStart;
    }

    /**
     * プロパティ値取得(TPC接続不可終了時間)
     * @returns TpcRegulationEnd設定値+":00"
     */
    public getTpcRegulationEnd(): string | null {
        return this.tpcRegulationEnd;
    }

    /**
     * プロパティ値取得(TPC最大同時接続数)
     * @returns TpcMaxConnections設定値
     */
    public getTpcMaxConnections(): number {
        return this.tpcMaxConnections;
    }

    /**
     * プロパティ値取得(実行キュー積み込み処理間隔(ミリ秒))
     * @returns ProducerPeriod設定値
     */
    public getProducerPeriod(): number {
        return this.producerPeriod;
    }

    /**
     * プロパティ値取得(実行キュー送信処理間隔(ミリ秒))
     * @returns WorkerPeriod設定値
     */
    public getWorkerPeriod(): number {
        return this.workerPeriod;
    }

    /**
     * プロパティ値取得(アプリケーション用DBユーザID)
     * @returns ApDbUserId設定値
     */
    public getApDbUserId(): string {
        return this.apDbUserId;
    }

    /**
     * プロパティ値取得(アプリケーション用DBユーザPW)
     * @returns ApDbUserPw設定値
     */
    public getApDbUserPw(): string {
        return this.apDbUserPw;
    }

    /**
     * プロパティ値取得(アプリケーション用DB接続ポート)
     * @returns ApDbPort設定値
     */
    public getApDbPort(): string {
        return this.apDbPort;
    }
}