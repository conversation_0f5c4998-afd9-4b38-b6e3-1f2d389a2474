import { addHours, format, startOfHour } from "date-fns";

/**
 * @param formatStr
 * @returns formatted date string
 */
export const getSystemDatetime = (
    formatStr: string = "yyyyMMddHHmmss",
): string => {
    return format(new Date(), formatStr);
};

/**
 * Check whether a value is null, undefined, NaN, empty string, empty array or empty object
 * @param {any} value value to test
 */
export const isNone = (value: any): boolean => {
    return (
        value === null ||
        value === undefined ||
        value === "" ||
        (typeof value === "number" && isNaN(value)) ||
        (Array.isArray(value) && value.length === 0) ||
        (typeof value === "object" &&
            value !== null &&
            Object.keys(value).length === 0)
    );
};

/**
 * Remove undefined (and null) properties (recursively) from an object.
 * If the property is an array of objects, also check for its elements.
 * See test cases for examples.
 *
 * **Note:**
 * * does not remove undefined or null item from array
 * * if `null` or `undefined` is passed, it will return an empty object `{}`
 *
 * @param obj object to remove empty properties from
 * @param removeNull also remove null properties
 * @param removeEmptyObjItem also remove empty object items from arrays or properties
 * @returns new object with empty properties removed
 */
export const removeEmptyProperties = (
    obj: any,
    removeNull = true,
    removeEmptyObjItem = false,
) => {
    if (typeof obj !== "object" && obj !== undefined) return obj; // return if not an object

    const newObj: any = {};
    for (const key in obj) {
        if (obj[key] === undefined || (removeNull && obj[key] === null)) {
            continue;
        }
        if (Array.isArray(obj[key])) {
            const arr = obj[key].map((item: any) => {
                if (typeof item === "object" && item !== null) {
                    return removeEmptyProperties(
                        item,
                        removeNull,
                        removeEmptyObjItem,
                    );
                }
                return item;
            });
            if (removeEmptyObjItem) {
                newObj[key] = arr.filter(
                    (item: any) =>
                        typeof item !== "object" ||
                        item === null ||
                        item === undefined ||
                        Object.keys(item).length > 0,
                );
            } else {
                newObj[key] = arr;
            }
        } else if (typeof obj[key] === "object" && obj[key] !== null) {
            newObj[key] = removeEmptyProperties(
                obj[key],
                removeNull,
                removeEmptyObjItem,
            );
            if (removeEmptyObjItem && Object.keys(newObj[key]).length === 0) {
                delete newObj[key];
            }
        } else {
            newObj[key] = obj[key];
        }
    }
    return newObj;
};

/**
 * Get boolean value from string or boolean
 * @returns `true` if `val` is either boolean `true` or string `'true'`
 */
export const getBooleanValue = (val: unknown) => {
    return val === "true" || val === true;
};

/**
 * Parse a value to a number, return fallback if it is not a number
 * @param val
 * @param fallback
 */
export const getNumberValue = (val: unknown, fallback = NaN): number => {
    if (typeof val === "number") {
        return isNaN(val) ? fallback : val;
    }
    if (typeof val === "string") {
        const parsed = parseFloat(val);
        return isNaN(parsed) ? fallback : parsed;
    }
    return fallback;
};

/**
 * Returns an array value from a string or array, or a fallback array if the value is not valid
 *
 * Does not check type of array items, so it can return an array of any type.
 * @param val
 * @param fallback
 * @returns
 */
export const getArrayValue = <T>(val: unknown, fallback: T[] = []): T[] => {
    if (Array.isArray(val)) {
        return val as T[];
    }
    if (typeof val === "string") {
        try {
            const parsed = JSON.parse(val);
            if (Array.isArray(parsed)) {
                return parsed as T[];
            }
        } catch (e) {
            // If JSON parsing fails, return fallback
            return fallback;
        }
    }
    return fallback;
};

/**
 * Format date string to YYYYMMDD if it is not already in that format
 */
export const fmtDateForHaishi = (dt: string | undefined | null) => {
    if (!dt || typeof dt !== "string") return null;
    if (dt.match(/^\d{8}$/)) return dt;
    return dt.substring(0, 10).replace(/\//g, "");
};

/**
 * Zip two arrays into an array of tuples (result's length = first array's length)
 * @param arr1 First array
 * @param arr2 Second array
 */
export const zipArrays = <T, U>(arr1: T[], arr2: U[]): [T, U][] => {
    return arr1.map((e, i) => [e, arr2[i]]);
};

/**
 * Get schedule time for CoreSwimmy to send to queue.
 * @param current current time in unix seconds
 * @param scheduleMinute next hour minute value
 * @param skipHours hours to skip when scheduling
 * @returns scheduled time in unix seconds
 */
export const getScheduleTimeForSwimmyOrder = (
    current: number,
    scheduledMinute: number,
    skipHours: number[],
): number => {
    const baseDate = startOfHour(new Date(current * 1000));
    // assuming local time is set to JST
    baseDate.setMinutes(scheduledMinute);
    let result = addHours(baseDate, 1);
    while (skipHours.includes(result.getHours())) {
        result = addHours(result, 1);
    }
    return Math.floor(result.getTime() / 1000);
};
