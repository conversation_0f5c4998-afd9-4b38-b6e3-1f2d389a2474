import AbstractMvnoBaseCommon from "@/core/common/AbstractMvnoBaseCommon";
import ExtendedInvocationContext from "@/types/ExtendedInvocationContext";
import { HttpRequest } from "@azure/functions";

/**
 * expose logging functions to be used in classes that do not inherit from AbstractMvnoBaseCommon
 */
export default class CustomLogger extends AbstractMvnoBaseCommon {
    public debug(
        tenantId: string,
        sequenceNo: string,
        msgKey: string,
        ...params: any[]
    ) {
        super.debug(tenantId, sequenceNo, msgKey, ...params);
    }

    public error(
        arg1: string | Error,
        arg2: string,
        arg3: string,
        arg4: string,
        ...params: any[]
    ) {
        if (arg1 instanceof Error) {
            super.error(arg1, arg2, arg3, arg4, ...params);
        } else {
            // combine arg4 and params if any
            const p = [];
            if (arg4) {
                p.push(arg4);
            }
            if (params) {
                p.push(...params);
            }
            super.error(arg1, arg2, arg3, ...p);
        }
    }

    public warn(
        arg1: string | Error,
        arg2: string,
        arg3: string,
        arg4: string,
        ...params: any[]
    ) {
        if (arg1 instanceof Error) {
            super.warn(arg1, arg2, arg3, arg4, ...params);
        } else {
            // combine arg4 and params if any
            const p = [];
            if (arg4) {
                p.push(arg4);
            }
            if (params) {
                p.push(...params);
            }
            super.warn(arg1, arg2, arg3, ...p);
        }
    }
}
