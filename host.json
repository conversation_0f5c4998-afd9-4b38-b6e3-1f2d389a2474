{"version": "2.0", "logging": {"applicationInsights": {"samplingSettings": {"isEnabled": false, "excludedTypes": "Request"}}, "logLevel": {"default": "Trace", "applicationInsights": "Trace"}}, "extensionBundle": {"id": "Microsoft.Azure.Functions.ExtensionBundle", "version": "[4.*, 5.0.0)"}, "extensions": {"http": {"routePrefix": "MVNO/api/V100", "maxConcurrentRequests": 100}, "serviceBus": {"autoCompleteMessages": true, "maxConcurrentCalls": 10, "maxAutoLockRenewalDuration": "00:05:00"}}}