const config = {
    // NOTE commented out config keys are defined in App Configuration
    mvno: {
        /** 予約実行日時単位(予約機能) */
        // ReservationDateExecutionUnits: 15,
        /** 予約可能制限日数(予約機能) */
        // ReservationsLimitDays: 60,
        /** API処理IDサーバ識別 */
        ApiProcessIDServer: "AP0",
        /** 接続許可IPアドレス */
        // AllowedSourceIpAddress: "*************,*************", // NOTE: the 1 at the end of the second IP address is a full-width character in original file
        /** サーバ同時接続上限数 */
        // ServerConnectionLimit: 60, // moved to App Configuration
        /** 予約キャンセル不可期間(予約キャンセル機能) */
        CancelReservationDisableDays: 1,

        /** TPC通信用送信元APサーバIPアドレス */
        // TpcSourceIpAddress: "*************",
        /** TPC通信用送信先TPCサーバIPアドレス */
        // TpcDestIpAddress: "**************",
        // TpcDestIpAddress2: "**************",
        // TpcDestIpAddress3: "**************",
        /** TPC通信用送信先TPCサーバサービスネーム（標準SOAP用） */
        // TpcDestServiceName: "http://${IpAddr}/services/serviceProfileOperation",
        /** TPC通信用送信先TPCサーバサービスネーム（簡易SOAP用） */
        // TpcLiteRequestURI: "http://${IpAddr}/services/serviceProfileOperation",
        /** TPCとのタイムアウト時間 */
        TpcTimeout: 60000,
        /** SOAP標準 */
        SOAP: "SOAP1.2",

        /** 総量規制状態（規制中）判定文字列 */
        TotalVolumeControlInJudgmentString: "Regulated",
        /** 総量規制状態（未規制）判定文字列 */
        TotalVolumeControlOutJudgmentString: "Unregulated",
        /** ヘビーユーザ監視規制状態判定文字列（Unregulated） */
        HeavyUserMonitorStatusUnregulatedString: "Unregulated",
        /** ヘビーユーザ監視規制状態判定文字列（Regulated at Last） */
        HeavyUserMonitorStatusRegulatedatLastString: "Regulated at Last",
        /** ヘビーユーザ監視規制状態判定文字列（Regulated at Monthly） */
        HeavyUserMonitorStatusRegulatedatMonthlyString: "Regulated at Monthly",
        /** ヘビーユーザ監視規制状態判定文字列（Regulated at Last/Monthly） */
        HeavyUserMonitorStatusRegulatedatLastMonthString:
            "Regulated at Last/Monthly",

        /** アプリケーション用DBリトライ最大回数 */
        ApDbRetryMaxCnt: 3,
        /** アプリケーション用DBリトライ間隔 */
        ApDbRetryInterval: 5,

        /** 仮登録回線追加受付不可時間(仮登録回線追加機能) */
        // PreLineAddNgTime: "0:00-4:00",

        /** 回線グループ再利用不可期間 */
        GroupIDReuseTerm: 100,

        /** 回線グループ容量算出バッファバイト */
        GroupBufferByte: 0,

        /** データ譲渡不可フラグ */
        GiftNgFlag: "0",
        /** データ譲渡不可時間帯 */
        GiftNgTime: "ALL",

        /** 追加容量上限値 */
        AddBucketUpperLimit: "104857600",
        /** 追加容量下限値 */
        AddBucketLowerLimit: "0",

        /** DBサーバ仮想IPアドレス */
        DbServerVirtaulIpAddress: "127.0.0.1",
        /** アプリケーション用DBユーザID */
        ApDbUserId: "mawp",
        /** アプリケーション用DBユーザPW */
        ApDbUserPw: "mawp",
        /** アプリケーション用DB接続ポート */
        ApDbPort: "******************************************",
        /** アプリケーション用DB接続ドライバ */
        ApDbDriver: "org.postgresql.Driver",
        /** バッチログ名 */
        AutoModbucketApiLogFile: "AutoModbucketApi.log",
        /** バッチログファイルパス */
        AutoModbucketApiLogFilePath: "/var/log/mawp",
        /** 回線グループ基本容量変更APIベースURI */
        AutoModbucketApiBaseUri: "https://*************/MVNO/api/V100/LinesGroup/LineGroup_mod_bucket",
        /** 自動計算回線グループ保持期間 */
        AutoModbucketApiDataLimit: "31",

        /** REST API接続時のコネクションタイムアウト値(秒) */
        httpClientConnectTimeout: 20,
        /** REST API接続時の読み取りタイムアウト値(秒) */
        httpClientReadTimeout: 3600,
        /** TPC接続不可時間 */
        TpcRegulationTime: "0:00-0:45",
        /** TPC最大同時接続数 */
        TpcMaxConnections: 10,

        /** 実行キュー積み込み処理間隔(ミリ秒) */
        ProducerPeriod: 60000,
        /** 実行キュー送信処理間隔(ミリ秒) */
        WorkerPeriod: 1000,

        /** 実行キュー積み込み機能が実行キューへ積み込み可能な最大オーダ数 */
        maxRequestQueueSize: 1000
    },
};

export default config;
