
> mvno_core_api@1.0.0 test:src /Users/<USER>/dev/mvno_azure/MVNO_CORE_API
> env-cmd --file .env.json jest -i --testPathIgnorePatterns=dist/ "--" "test/core/service/impl/LinePlanAcquisitionServiceTx.test.ts"

  console.error
    WARNING: NODE_ENV value of 'test' did not match any deployment config file names.

      1 | import axios, { AxiosError, AxiosInstance } from "axios";
    > 2 | import config from "config";
        | ^
      3 | import xpath from "xpath";
      4 |
      5 | import SOAPException from "@/types/soapException";

      at _warnOrThrow (node_modules/.pnpm/config@3.3.12/node_modules/config/lib/config.js:1494:13)
      at node_modules/.pnpm/config@3.3.12/node_modules/config/lib/config.js:1473:7
          at Array.forEach (<anonymous>)
      at Config.Object.<anonymous>.util.runStrictnessChecks (node_modules/.pnpm/config@3.3.12/node_modules/config/lib/config.js:1466:12)
      at new Config (node_modules/.pnpm/config@3.3.12/node_modules/config/lib/config.js:119:8)
      at Object.<anonymous> (node_modules/.pnpm/config@3.3.12/node_modules/config/lib/config.js:1514:33)
      at Object.<anonymous> (src/core/common/SOAPCommon.ts:2:1)
      at Object.<anonymous> (test/core/service/impl/LinePlanAcquisitionServiceTx.test.ts:7:1)

  console.error
    WARNING: See https://github.com/node-config/node-config/wiki/Strict-Mode

      1 | import axios, { AxiosError, AxiosInstance } from "axios";
    > 2 | import config from "config";
        | ^
      3 | import xpath from "xpath";
      4 |
      5 | import SOAPException from "@/types/soapException";

      at _warnOrThrow (node_modules/.pnpm/config@3.3.12/node_modules/config/lib/config.js:1495:13)
      at node_modules/.pnpm/config@3.3.12/node_modules/config/lib/config.js:1473:7
          at Array.forEach (<anonymous>)
      at Config.Object.<anonymous>.util.runStrictnessChecks (node_modules/.pnpm/config@3.3.12/node_modules/config/lib/config.js:1466:12)
      at new Config (node_modules/.pnpm/config@3.3.12/node_modules/config/lib/config.js:119:8)
      at Object.<anonymous> (node_modules/.pnpm/config@3.3.12/node_modules/config/lib/config.js:1514:33)
      at Object.<anonymous> (src/core/common/SOAPCommon.ts:2:1)
      at Object.<anonymous> (test/core/service/impl/LinePlanAcquisitionServiceTx.test.ts:7:1)

FAIL test/core/service/impl/LinePlanAcquisitionServiceTx.test.ts (8.466 s)
  @/core/service/impl/LinePlanAcquisitionServiceTx
    tenantType RINKモバイル
      ✕ should return CODE_000000 if ORDER_TYPE = 0 (85 ms)
      ✕ should return CODE_000000 if ORDER_TYPE = 1 (42 ms)
      ✓ should return CODE_000000 if ORDER_TYPE = 2 (39 ms)
      ✓ should return CODE_000000 , Update 回線Group record if found (58 ms)
    OK CASES (CODE_000000) 回線グループIDが存在しない場合 & テナントがC-OCNの場合 > 回線情報を更新しない 
      ✕ should return CODE_000000 if [予約前オーダ] (21 ms)
      ✕ should return CODE_000000 if [即時オーダ] (28 ms)
      ✓ should return CODE_000000 if [予約実行オーダ] (26 ms)
      ✕ should return CODE_000000 even if second TPC cannot be reached (SOAPException)  (31 ms)
      ✕ should return CODE_000000 even if second TPC SOAP call fails (29 ms)
    OK CASES (CODE_000000) 回線グループIDが存在しない場合 & C-OCN以外の場合 > 回線情報を更新 
      ✓ should return CODE_000000 if [予約前オーダ] (32 ms)
      ✓ should return CODE_000000 if [即時オーダ] (31 ms)
      ✓ should return CODE_000000 if [予約実行オーダ] (24 ms)
      ✓ should return CODE_000000 even if second TPC cannot be reached (SOAPException)  (42 ms)
      ✓ should return CODE_000000 even if second TPC SOAP call fails (30 ms)
    OK CASES 回線グループIDが存在しない場合 & C-OCN以外の場合 SOAP EXCEPTION
      ✓ should return CODE_061101 if SOAP Exception occurs (41 ms)
      ✓ should return CODE_000000 even if second TPC cannot be reached (SOAPException)  (25 ms)
      ✓ should return CODE_000000 even if second TPC SOAP call fails (31 ms)
    OK CASES (CODE_000000) 回線グループIDが存在する場合 & テナントがC-OCN以外の場合 > 回線情報を更新 & 回線グループIDを更新
      ✓ should return CODE_000000 if [予約前オーダ] (38 ms)
      ✓ should return CODE_000000 if [即時オーダ] (37 ms)
      ✓ should return CODE_000000 if [予約実行オーダ] (46 ms)
      ✓ should return CODE_000000 even if second TPC cannot be reached (SOAPException)  (31 ms)
      ✓ should return CODE_000000 even if second TPC SOAP call fails (27 ms)
    OK CASES CODE_000000 回線グループIDが存在する場合 SAOP TEST
      ✓ should return CODE_000000  (39 ms)
      ✓ should return CODE_000000 even if second TPC fails SOAPException sendSoapApiToTpcAplysrv (38 ms)
      ✓ should return CODE_000000 even if second TPC SOAP response is NG (42 ms)
      ✓ should return CODE_000000  even if  TPC SOAP call returns CODE_000951 (30 ms)
    NG CASES 回線回線グループのレコードを更新
      ✓ should return CODE_061201 if updateLineInfoPlanChange fails to query (245 ms)
      ✓ should return CODE_061201 if updateLineInfoPlanChange couldnt modify data (54 ms)
      ✓ should return CODE_061701 if updateLineLineGroupsCapacity couldnt modify data (34 ms)
      ✓ should return CODE_061701 if updateLineLineGroupsCapacity fails to query (252 ms)
    NG CASE for 999999
      ✓ should return CODE_999999 if getGroupId throws error other than db (22 ms)
      ✓ should return CODE_999999 if when [予約実行オーダ] (予約実行オーダ) and client address invalid (9 ms)
      ✓ should return CODE_999999 if getTenants throws error other than db (20 ms)
    NG CASES C-OCNの場合
      ✓ should return CODE_060133 if checkAbolishSo returns false (22 ms)
      ✓ should return CODE_060133 if checkAbolishSo fails to query (185 ms)
      ✓ should return CODE_060134 if line simflag is true and tenatType is not 2,3,4  (47 ms)
      ✓ should return CODE_060134 if line simflag is true and tenatType is not 2,3,4  & getLineInfo fails to query (190 ms)
      ✓ should return CODE_060135 if 変更後プランの対応NWが「5G(NSA)」&& 回線の契約種別が「5G(NSA)」もしくは「5G(NSA)(音声)」以外の場合 (31 ms)
    NG CASES C-OCNの以外の場合
      ✓ should return CODE_060701 if 変更前プランと変更後プランのフルMVNOフラグが一致しない場合, プラン変更パターン定型フラグが3（社内システムタイプ） (23 ms)
      ✓ should return CODE_060701 if 変更前プランと変更後プランの音声フラグ、SMS対応が一致しない場合, プラン変更パターン定型フラグが2（ID卸タイプ）） (26 ms)
      ✓ should return CODE_060701 if 変更後プランのプラン変更種別が通常プラン(0 or null)ではない場合, プラン変更パターン定型フラグが2（ID卸タイプ）） (22 ms)
      ✓ should return CODE_060701 if getTenantPlansPlanId fails to query, i gues number3 (188 ms)
      ✓ should return CODE_060701 if 変更前プランと変更後プランの音声フラグ、SMS対応が一致しない場合, プラン変更パターン定型フラグが2（ID卸タイプ）の場合 (26 ms)
      ✓ should return CODE_060701 if 変更前プランと変更後プランの対応NWが一致しない場合, プラン変更パターン定型フラグが2（ID卸タイプ）の場合 (20 ms)
      ✓ should return CODE_060701 if テナントプランテーブルから変更後プランの「プランID」を取得 returns null, プラン変更パターン定型フラグが2（ID卸タイプ）の場合 (14 ms)
      ✓ should return CODE_060701 if getTenantPlansPlanId fails to query, プラン変更パターン定型フラグが2（ID卸タイプ）の場合 (185 ms)
      ✓ should return CODE_060501 if planList.planId doesnt match to potalPlanId_pre (25 ms)
      ✓ should return CODE_060601 if aPICommonDAO.getPlans fails to query (200 ms)
      ✓ should return CODE_060701 if  変更前プランと変更後プランのフルMVNOフラグが一致しない場合, プラン変更パターン定型フラグが3（社内システムタイプ） (22 ms)
      ✓ should return CODE_060701 if 変更前プランと変更後プランの対応NWが一致しない場合, プラン変更パターン定型フラグが1（帯域卸タイプ）の場合 (15 ms)
      ✓ should return CODE_060701 if 変更前プランと変更後プランの音声フラグ、SMS対応が一致しない場合, プラン変更パターン定型フラグが1（帯域卸タイプ）の場合 (14 ms)
      ✓ should return CODE_060701 if 変更前プランと変更後プランのフルMVNOフラグが一致しない場合, プラン変更パターン定型フラグが1（帯域卸タイプ）の場合 (13 ms)
      ✓ should return CODE_060701 if getTenantPlansPlanId returns null, プラン変更パターン定型フラグが1（帯域卸タイプ）の場合 (14 ms)
      ✓ should return CODE_060701 if getTenantPlansPlanId , プラン変更パターン定型フラグが1（帯域卸タイプ）の場合 (175 ms)
      ✓ should return CODE_060701 if getTenantPlanforPlanChange fails to query, プラン変更パターン定型フラグ is not 1, 2 or 3 (189 ms)
      ✕ should return CODE_060801 if planChangeTime from getPlanChangeKaisu is less than tenantPlans.changeCount (28 ms)
      ✓ should return CODE_060801 if getPlanChangeKaisu fails to query (181 ms)
      ✓ should return CODE_060601 if aPICommonDAO.getPlans returns null (31 ms)
      ✓ should return CODE_060601 if pricePLanId returned by aPICommonDAO.getPlans is null (23 ms)
      ✓ should return CODE_060701 if getPlansTypeList fails to query (192 ms)
    NG CASES
      ✓ should return CODE_060101 if lineNo is invalid (20 ms)
      ✓ should return CODE_060101 if potalPlanID_pre is invalid (15 ms)
      ✓ should return CODE_060101 if potalPlanID is invalid  (14 ms)
      ✓ should return CODE_060101 if potalPlanID amd potalPlanID_pre are the same (12 ms)
      ✓ should return CODE_060101 if [予約実行オーダ] or [予約前オーダ]  and reserveDate is invalid (7 ms)
      ✓ should return CODE_060101 if [予約前オーダ] and csvUnnecessaryFlag is invalid (7 ms)
      ✓ should return CODE_060102 if getTenants throws db error (169 ms)
      ✓ should return CODE_060102 if getTenants returns null (23 ms)
      ✓ should return CODE_060105 if [予想以外オーダ] (10 ms)
      ✓ should return CODE_060105 if order type cannot be detected (8 ms)
      ✓ should return CODE_060106 if planChangeNgTime config is invalid (15 ms)
      ✓ should return CODE_060107 if [即時オーダ] and checkPlanChangeNgTime is invalid (14 ms)
      ✓ should return CODE_060108 if theres existing reserved orders, [即時オーダ] (15 ms)
      ✓ should return CODE_060108 if [予約前オーダ] and  checkReserveOrder returns false (13 ms)
      ✓ should return CODE_060108 if [予約前オーダ] and  checkReserveOrder throws db error (11 ms)
      ✓ should return CODE_060109 if [予約前オーダ] and checkPlanChangeNgTime is invalid (13 ms)
      ✓ should return CODE_060110 if [予約前オーダ] and checkReserveDateOrderDate is invalid (11 ms)
      ✓ should return CODE_060111 if [予約前オーダ] and checkReservationDateExecutionUnitsFmt is invalid (17 ms)
      ✓ should return CODE_060112 if [予約前オーダ] and checkReservationDateExecutionUnits is invalid (13 ms)
      ✓ should return CODE_060113 if [予約前オーダ] and checkReservationsLimitDays is invalid (11 ms)
      ✓ should return CODE_060114 if [予約前オーダ] and checkReservationsLimitDays is invalid (11 ms)
      ✓ should return CODE_060131 if getPlanChangeFlag fails, [即時オーダ] (166 ms)
      ✓ should return CODE_060132 if getPlanChangeFlag fails, [予約前オーダ] (175 ms)
      ✓ should return CODE_060201 if line info not found in db (22 ms)
      ✓ should return CODE_060201 if getLineInfoforUpdate fails to query (169 ms)
      ✓ should return CODE_060201 if failed to get line info lock (32 ms)
      ✓ should return CODE_060201 if getLineInfoforUpdate occurs error (14 ms)
      ✓ should return CODE_060401 if doCheck fails to query (173 ms)
      ✓ should return CODE_060401 if doCheck returns false (22 ms)
      ✓ should return CODE_061302 if getGroupId fails to query (176 ms)
      ✓ should return CODE_061302 if multiple groupId found in line_line_group (26 ms)
      ✓ should return CODE_061302 if groupId found in line_line_group and tenantType is not null, 2, 3 or 4 (21 ms)
      ✓ should return CODE_060901 if getPlans fails to query (191 ms)
      ✓ should return CODE_060901 if getPlans returns null (23 ms)
      ✓ should return CODE_060901 if servicePlanId from PlansEntity is empty (23 ms)
      ✓ should return CODE_060501 if aPILinesDAO.getLineInfo fails to query (181 ms)
      ✓ should return CODE_060501 if aPILinesDAO.getLineInfo return null (21 ms)
      ✓ should return CODE_060108 if theres existing reserved orders, [予約前オーダ] (20 ms)
      ✓ should return CODE_060131 if プラン変更種別 is null (19 ms)
      ✓ should return CODE_060131 if changeTiming is 月発 but orderdate is not first day of the month (15 ms)

  ● @/core/service/impl/LinePlanAcquisitionServiceTx › tenantType RINKモバイル › should return CODE_000000 if ORDER_TYPE = 0

    expect(received).not.toBeNull()

    Received: null

      182 |         });
      183 |         if (exists) {
    > 184 |             expect(apiLog).not.toBeNull();
          |                                ^
      185 |             expect(enqueueMessage).toBeCalledTimes(1);
      186 |         } else {
      187 |             expect(apiLog).toBeNull();

      at test/core/service/impl/LinePlanAcquisitionServiceTx.test.ts:184:32
      at fulfilled (test/core/service/impl/LinePlanAcquisitionServiceTx.test.ts:5:58)

  ● @/core/service/impl/LinePlanAcquisitionServiceTx › tenantType RINKモバイル › should return CODE_000000 if ORDER_TYPE = 1

    expect(received).not.toBeNull()

    Received: null

      182 |         });
      183 |         if (exists) {
    > 184 |             expect(apiLog).not.toBeNull();
          |                                ^
      185 |             expect(enqueueMessage).toBeCalledTimes(1);
      186 |         } else {
      187 |             expect(apiLog).toBeNull();

      at test/core/service/impl/LinePlanAcquisitionServiceTx.test.ts:184:32
      at fulfilled (test/core/service/impl/LinePlanAcquisitionServiceTx.test.ts:5:58)

  ● @/core/service/impl/LinePlanAcquisitionServiceTx › OK CASES (CODE_000000) 回線グループIDが存在しない場合 & テナントがC-OCNの場合 > 回線情報を更新しない  › should return CODE_000000 if [予約前オーダ]

    expect(received).not.toBeNull()

    Received: null

      182 |         });
      183 |         if (exists) {
    > 184 |             expect(apiLog).not.toBeNull();
          |                                ^
      185 |             expect(enqueueMessage).toBeCalledTimes(1);
      186 |         } else {
      187 |             expect(apiLog).toBeNull();

      at test/core/service/impl/LinePlanAcquisitionServiceTx.test.ts:184:32
      at fulfilled (test/core/service/impl/LinePlanAcquisitionServiceTx.test.ts:5:58)

  ● @/core/service/impl/LinePlanAcquisitionServiceTx › OK CASES (CODE_000000) 回線グループIDが存在しない場合 & テナントがC-OCNの場合 > 回線情報を更新しない  › should return CODE_000000 if [即時オーダ]

    expect(received).not.toBeNull()

    Received: null

      182 |         });
      183 |         if (exists) {
    > 184 |             expect(apiLog).not.toBeNull();
          |                                ^
      185 |             expect(enqueueMessage).toBeCalledTimes(1);
      186 |         } else {
      187 |             expect(apiLog).toBeNull();

      at test/core/service/impl/LinePlanAcquisitionServiceTx.test.ts:184:32
      at fulfilled (test/core/service/impl/LinePlanAcquisitionServiceTx.test.ts:5:58)

  ● @/core/service/impl/LinePlanAcquisitionServiceTx › OK CASES (CODE_000000) 回線グループIDが存在しない場合 & テナントがC-OCNの場合 > 回線情報を更新しない  › should return CODE_000000 even if second TPC cannot be reached (SOAPException) 

    expect(received).not.toBeNull()

    Received: null

      182 |         });
      183 |         if (exists) {
    > 184 |             expect(apiLog).not.toBeNull();
          |                                ^
      185 |             expect(enqueueMessage).toBeCalledTimes(1);
      186 |         } else {
      187 |             expect(apiLog).toBeNull();

      at test/core/service/impl/LinePlanAcquisitionServiceTx.test.ts:184:32
      at fulfilled (test/core/service/impl/LinePlanAcquisitionServiceTx.test.ts:5:58)

  ● @/core/service/impl/LinePlanAcquisitionServiceTx › OK CASES (CODE_000000) 回線グループIDが存在しない場合 & テナントがC-OCNの場合 > 回線情報を更新しない  › should return CODE_000000 even if second TPC SOAP call fails

    expect(received).not.toBeNull()

    Received: null

      182 |         });
      183 |         if (exists) {
    > 184 |             expect(apiLog).not.toBeNull();
          |                                ^
      185 |             expect(enqueueMessage).toBeCalledTimes(1);
      186 |         } else {
      187 |             expect(apiLog).toBeNull();

      at test/core/service/impl/LinePlanAcquisitionServiceTx.test.ts:184:32
      at fulfilled (test/core/service/impl/LinePlanAcquisitionServiceTx.test.ts:5:58)

  ● @/core/service/impl/LinePlanAcquisitionServiceTx › NG CASES C-OCNの以外の場合 › should return CODE_060801 if planChangeTime from getPlanChangeKaisu is less than tenantPlans.changeCount

    expect(received).toEqual(expected) // deep equality

    Expected: "060801"
    Received: "999999"

      171 |         expect(result?.jsonBody).toBeDefined();
      172 |         expect(result.jsonBody.responseHeader).toBeDefined();
    > 173 |         expect(result.jsonBody.responseHeader.processCode).toEqual(processCode);
          |                                                            ^
      174 |     };
      175 |
      176 |     const checkCoreSwimmyApiLog = async (

      at checkResponse (test/core/service/impl/LinePlanAcquisitionServiceTx.test.ts:173:60)
      at test/core/service/impl/LinePlanAcquisitionServiceTx.test.ts:2105:13
      at fulfilled (test/core/service/impl/LinePlanAcquisitionServiceTx.test.ts:5:58)

-------------------------------------|---------|----------|---------|---------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
File                                 | % Stmts | % Branch | % Funcs | % Lines | Uncovered Line #s                                                                                                                                                                                                                                                                                                                                                                                                                  
-------------------------------------|---------|----------|---------|---------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
All files                            |   44.94 |    23.49 |    30.7 |   44.75 |                                                                                                                                                                                                                                                                                                                                                                                                                                    
 config                              |     100 |      100 |     100 |     100 |                                                                                                                                                                                                                                                                                                                                                                                                                                    
  default.ts                         |     100 |      100 |     100 |     100 |                                                                                                                                                                                                                                                                                                                                                                                                                                    
 src/appconfig                       |   50.84 |    68.57 |      80 |      60 |                                                                                                                                                                                                                                                                                                                                                                                                                                    
  index.ts                           |   50.84 |    68.57 |      80 |      60 | 53-57,64-91,176,193-197,213                                                                                                                                                                                                                                                                                                                                                                                                        
 src/constants                       |     100 |      100 |     100 |     100 |                                                                                                                                                                                                                                                                                                                                                                                                                                    
  coreSwimmy.ts                      |     100 |      100 |     100 |     100 |                                                                                                                                                                                                                                                                                                                                                                                                                                    
  frontApi.ts                        |     100 |      100 |     100 |     100 |                                                                                                                                                                                                                                                                                                                                                                                                                                    
  notificationType.ts                |     100 |      100 |     100 |     100 |                                                                                                                                                                                                                                                                                                                                                                                                                                    
  simConstants.ts                    |     100 |      100 |     100 |     100 |                                                                                                                                                                                                                                                                                                                                                                                                                                    
 src/core/common                     |   33.88 |    26.82 |   44.17 |   33.74 |                                                                                                                                                                                                                                                                                                                                                                                                                                    
  AbstractMvnoBaseCommon.ts          |   83.33 |       75 |     100 |   83.33 | 68,138-149,261,288                                                                                                                                                                                                                                                                                                                                                                                                                 
  ApiCommon.ts                       |   66.66 |      100 |   66.66 |   66.66 | 40-45                                                                                                                                                                                                                                                                                                                                                                                                                              
  Check.ts                           |   23.37 |    23.82 |   28.26 |   23.37 | 84-90,107-110,124,129,132,141,163-166,178,191,197,226-286,297,310,322-911,922,928,940-1121,1132,1138,1151,1167,1182,1187,1197,1223,1231-1273,1288,1307,1310,1313                                                                                                                                                                                                                                                                   
  CheckUtil.ts                       |   48.61 |    57.44 |   52.94 |   48.61 | 49,140,148,185-334                                                                                                                                                                                                                                                                                                                                                                                                                 
  FunctionExternalApiHandler.ts      |      50 |        0 |       0 |      50 | 28-33,40-48                                                                                                                                                                                                                                                                                                                                                                                                                        
  FunctionHandler.ts                 |      50 |      100 |       0 |      50 | 17                                                                                                                                                                                                                                                                                                                                                                                                                                 
  MvnoUtil.ts                        |      44 |    28.57 |   57.89 |   43.05 | 28,59,62,94,138,172-298                                                                                                                                                                                                                                                                                                                                                                                                            
  RESTCommon.ts                      |    9.17 |     4.42 |      10 |    9.17 | 104-1161,1335,1343,1350-1366,1380-1391,1400-1503,1518-1536,1567-1922                                                                                                                                                                                                                                                                                                                                                               
  SOAPCommon.ts                      |   67.08 |       50 |   66.66 |   66.87 | 121-144,155-183,237,251,283-311,336-364,396,426,438-462,474-490                                                                                                                                                                                                                                                                                                                                                                    
  SOAPCommonUtils.ts                 |   52.08 |    28.57 |      60 |   52.08 | 65,82,123-203,214,233                                                                                                                                                                                                                                                                                                                                                                                                              
  SOCommon.ts                        |   48.56 |    31.81 |     100 |   48.37 | 52-53,64-65,74-78,101-105,137,146,160-173,179,184,239,241-244,247-250,254-324,410-421,432,444,452-457,463,466,471-537,594-595,602,678,687,718-719,760,770,775,786,804-822,851                                                                                                                                                                                                                                                      
  StringUtils.ts                     |   57.14 |    27.27 |      50 |   57.14 | 21-25,50                                                                                                                                                                                                                                                                                                                                                                                                                           
  TenantManage.ts                    |   25.71 |    13.95 |      50 |   25.71 | 44-163,214-220,254-285,304-328                                                                                                                                                                                                                                                                                                                                                                                                     
 src/core/constant                   |     100 |      100 |     100 |     100 |                                                                                                                                                                                                                                                                                                                                                                                                                                    
  Constants.ts                       |     100 |      100 |     100 |     100 |                                                                                                                                                                                                                                                                                                                                                                                                                                    
  MessageProperties.ts               |     100 |      100 |     100 |     100 |                                                                                                                                                                                                                                                                                                                                                                                                                                    
  MsgKeysConstants.ts                |     100 |      100 |     100 |     100 |                                                                                                                                                                                                                                                                                                                                                                                                                                    
  MvnoConstants.ts                   |     100 |      100 |     100 |     100 |                                                                                                                                                                                                                                                                                                                                                                                                                                    
  ResultCdConstants.ts               |     100 |      100 |     100 |     100 |                                                                                                                                                                                                                                                                                                                                                                                                                                    
 src/core/dao                        |   20.84 |     5.42 |   18.67 |   21.19 |                                                                                                                                                                                                                                                                                                                                                                                                                                    
  APICommonDAO.ts                    |   36.25 |       25 |   29.72 |   36.79 | 80-241,279-324,352-511,577-765,836-840,879-904,976-1153,1187,1232-1467,1676,1724-1736,1762-1771                                                                                                                                                                                                                                                                                                                                    
  APILinesDAO.ts                     |   10.55 |     0.85 |   12.57 |    10.7 | 39-461,479-551,581,605-2012,2065-2267,2316-2592,2716-2729,2764-2778                                                                                                                                                                                                                                                                                                                                                                
  APISoDAO.ts                        |   32.58 |    14.58 |   28.88 |   32.87 | 35-82,148-149,192-193,220-624                                                                                                                                                                                                                                                                                                                                                                                                      
  ApiLinesGroupDAO.ts                |   13.71 |     7.69 |    8.82 |   13.83 | 42-146,182-851,900-1016                                                                                                                                                                                                                                                                                                                                                                                                            
 src/core/dto                        |   63.33 |      100 |   62.02 |   63.33 |                                                                                                                                                                                                                                                                                                                                                                                                                                    
  ParameterName.ts                   |   55.55 |      100 |      80 |   55.55 | 10-13                                                                                                                                                                                                                                                                                                                                                                                                                              
  SOAPCommonOutputDto.ts             |    87.5 |      100 |   85.71 |    87.5 | 32                                                                                                                                                                                                                                                                                                                                                                                                                                 
  SOObject.ts                        |   66.66 |      100 |   63.93 |   66.66 | 119,295,337,351,380-417,456-538                                                                                                                                                                                                                                                                                                                                                                                                    
  TpcDataCheckResultDto.ts           |   14.28 |      100 |       0 |   14.28 | 16-51                                                                                                                                                                                                                                                                                                                                                                                                                              
 src/core/entity                     |   98.66 |      100 |   72.22 |   98.46 |                                                                                                                                                                                                                                                                                                                                                                                                                                    
  AutoApiBatchTimesEntity.ts         |     100 |      100 |     100 |     100 |                                                                                                                                                                                                                                                                                                                                                                                                                                    
  AutoModbucketLineGroupsEntity.ts   |     100 |      100 |     100 |     100 |                                                                                                                                                                                                                                                                                                                                                                                                                                    
  CardEntity.ts                      |     100 |      100 |     100 |     100 |                                                                                                                                                                                                                                                                                                                                                                                                                                    
  CustomerInfoEntity.ts              |     100 |      100 |     100 |     100 |                                                                                                                                                                                                                                                                                                                                                                                                                                    
  GroupOptionPlanParametersEntity.ts |     100 |      100 |     100 |     100 |                                                                                                                                                                                                                                                                                                                                                                                                                                    
  GroupOptionPlansEntity.ts          |     100 |      100 |     100 |     100 |                                                                                                                                                                                                                                                                                                                                                                                                                                    
  GroupPlansEntity.ts                |     100 |      100 |     100 |     100 |                                                                                                                                                                                                                                                                                                                                                                                                                                    
  GroupTrafficsEntity.ts             |     100 |      100 |     100 |     100 |                                                                                                                                                                                                                                                                                                                                                                                                                                    
  IpaddressEntity.ts                 |     100 |      100 |     100 |     100 |                                                                                                                                                                                                                                                                                                                                                                                                                                    
  LineGroupsEntity.ts                |     100 |      100 |     100 |     100 |                                                                                                                                                                                                                                                                                                                                                                                                                                    
  LineLineGroupsEntity.ts            |     100 |      100 |     100 |     100 |                                                                                                                                                                                                                                                                                                                                                                                                                                    
  LineOptionsEntity.ts               |     100 |      100 |     100 |     100 |                                                                                                                                                                                                                                                                                                                                                                                                                                    
  LineTenantsEntity.ts               |     100 |      100 |     100 |     100 |                                                                                                                                                                                                                                                                                                                                                                                                                                    
  LineTrafficsEntity.ts              |     100 |      100 |     100 |     100 |                                                                                                                                                                                                                                                                                                                                                                                                                                    
  LinesEntity.ts                     |   94.02 |      100 |      60 |   93.84 | 368,390,401,412                                                                                                                                                                                                                                                                                                                                                                                                                    
  OptionPlanParametersEntity.ts      |     100 |      100 |     100 |     100 |                                                                                                                                                                                                                                                                                                                                                                                                                                    
  OptionPlansEntity.ts               |     100 |      100 |     100 |     100 |                                                                                                                                                                                                                                                                                                                                                                                                                                    
  PlansEntity.ts                     |     100 |      100 |     100 |     100 |                                                                                                                                                                                                                                                                                                                                                                                                                                    
  ServiceOrdersEntity.ts             |     100 |      100 |     100 |     100 |                                                                                                                                                                                                                                                                                                                                                                                                                                    
  TenantGroupPlansEntity.ts          |     100 |      100 |     100 |     100 |                                                                                                                                                                                                                                                                                                                                                                                                                                    
  TenantNnumbersEntity.ts            |     100 |      100 |     100 |     100 |                                                                                                                                                                                                                                                                                                                                                                                                                                    
  TenantPlanLineOptionsEntity.ts     |     100 |      100 |     100 |     100 |                                                                                                                                                                                                                                                                                                                                                                                                                                    
  TenantPlansEntity.ts               |    92.3 |      100 |      50 |    90.9 | 64                                                                                                                                                                                                                                                                                                                                                                                                                                 
  TenantsEntity.ts                   |     100 |      100 |     100 |     100 |                                                                                                                                                                                                                                                                                                                                                                                                                                    
  VoiceInfoEntity.ts                 |     100 |      100 |     100 |     100 |                                                                                                                                                                                                                                                                                                                                                                                                                                    
 src/core/service/impl               |   40.08 |    40.61 |   44.44 |   40.08 |                                                                                                                                                                                                                                                                                                                                                                                                                                    
  LinePlanAcquisitionServiceTx.ts    |   82.37 |    82.25 |     100 |   82.37 | 821-826,875-876,913-918,1121,1227-1231,1241-1263,1375-1380,1387-1409,1798-1820,1828-1850,1912-1934,1947-1952,1961-1983,2007-2029,2046-2060,2072-2077,2081-2103,2120-2124,2149-2171,2315-2319,2447-2451,2456-2478,2519,2530-2551,2565-2586,2610,2671-2697,2708-2729,2744-2765,2846,2899-2905,2975,3040-3062,3086-3108,3122-3146,3158-3178,3197-3221,3225,3234,3242,3270-3276,3351-3373,3608-3610,3619,3634-3637,3651-3654,3661,3669 
  PreLineAddService.ts               |    4.47 |        0 |       0 |    4.47 | 67-92,100-114,141-146,204-4244                                                                                                                                                                                                                                                                                                                                                                                                     
 src/database                        |   66.15 |    62.06 |      60 |   63.63 |                                                                                                                                                                                                                                                                                                                                                                                                                                    
  mongo.ts                           |   95.23 |      100 |     100 |   94.11 | 24                                                                                                                                                                                                                                                                                                                                                                                                                                 
  psql.ts                            |     100 |    82.35 |     100 |     100 | 71-72                                                                                                                                                                                                                                                                                                                                                                                                                              
  redis.ts                           |   19.23 |        0 |       0 |   17.39 | 8-40,44                                                                                                                                                                                                                                                                                                                                                                                                                            
 src/functions                       |   82.14 |    78.57 |     100 |   82.14 |                                                                                                                                                                                                                                                                                                                                                                                                                                    
  LinePlanAcquisitionHandler.ts      |   82.14 |    78.57 |     100 |   82.14 | 59-60,79-95                                                                                                                                                                                                                                                                                                                                                                                                                        
 src/helpers                         |   65.45 |    55.55 |   63.63 |   66.03 |                                                                                                                                                                                                                                                                                                                                                                                                                                    
  configHelper.ts                    |   88.88 |        0 |     100 |   88.88 | 17                                                                                                                                                                                                                                                                                                                                                                                                                                 
  queryHelper.ts                     |   95.83 |    83.33 |     100 |   95.65 | 63                                                                                                                                                                                                                                                                                                                                                                                                                                 
  stringHelper.ts                    |      25 |        0 |       0 |      25 | 7-11                                                                                                                                                                                                                                                                                                                                                                                                                               
  tBanHelper.ts                      |   22.22 |        0 |       0 |   23.52 | 14-59                                                                                                                                                                                                                                                                                                                                                                                                                              
 src/models                          |   21.37 |        0 |       0 |   21.37 |                                                                                                                                                                                                                                                                                                                                                                                                                                    
  coreSwimmyApiLog.ts                |   21.37 |        0 |       0 |   21.37 | 258-310,338,344,351,359-412,422-435,447-475,483,505-530,537,546,574-686                                                                                                                                                                                                                                                                                                                                                            
 src/services                        |   11.71 |        0 |       0 |   11.48 |                                                                                                                                                                                                                                                                                                                                                                                                                                    
  baseService.ts                     |      40 |      100 |       0 |      40 | 15-17                                                                                                                                                                                                                                                                                                                                                                                                                              
  coreAPIService.ts                  |   20.83 |        0 |       0 |   20.83 | 34-162                                                                                                                                                                                                                                                                                                                                                                                                                             
  coreSwimmyService.ts               |    4.95 |        0 |       0 |    5.01 | 73-2501                                                                                                                                                                                                                                                                                                                                                                                                                            
  frontAPIService.ts                 |   21.56 |        0 |       0 |   21.56 | 35-167                                                                                                                                                                                                                                                                                                                                                                                                                             
  mockdata.ts                        |   25.71 |        0 |       0 |   26.47 | 17-25,37-63,81,91-97,1022,1033                                                                                                                                                                                                                                                                                                                                                                                                     
  notificationService.ts             |   27.27 |      100 |       0 |   27.27 | 22-34                                                                                                                                                                                                                                                                                                                                                                                                                              
  redisCacheService.ts               |    42.1 |        0 |       0 |   35.29 | 16-25,37-43                                                                                                                                                                                                                                                                                                                                                                                                                        
 src/types                           |   53.57 |    45.45 |      75 |   53.57 |                                                                                                                                                                                                                                                                                                                                                                                                                                    
  coreMvnoConfig.ts                  |     100 |      100 |     100 |     100 |                                                                                                                                                                                                                                                                                                                                                                                                                                    
  parameter.string.ts                |    62.5 |        0 |     100 |    62.5 | 23,26,29                                                                                                                                                                                                                                                                                                                                                                                                                           
  soapException.ts                   |   28.57 |       40 |      50 |   28.57 | 18-32                                                                                                                                                                                                                                                                                                                                                                                                                              
  string.extension.ts                |     100 |      100 |     100 |     100 |                                                                                                                                                                                                                                                                                                                                                                                                                                    
 src/utils                           |   24.44 |     1.58 |    6.66 |   16.88 |                                                                                                                                                                                                                                                                                                                                                                                                                                    
  index.ts                           |   28.16 |     1.81 |    8.33 |   18.96 | 10,49-91,99,108-115,127-141,148-150,159,174-181                                                                                                                                                                                                                                                                                                                                                                                    
  logger.ts                          |   10.52 |        0 |       0 |   10.52 | 15-58                                                                                                                                                                                                                                                                                                                                                                                                                              
 test/testing                        |   42.85 |       20 |      40 |    40.9 |                                                                                                                                                                                                                                                                                                                                                                                                                                    
  DefaultContext.ts                  |      90 |      100 |      50 |    87.5 | 57                                                                                                                                                                                                                                                                                                                                                                                                                                 
  DefaultRequest.ts                  |      75 |      100 |       0 |   66.66 | 28                                                                                                                                                                                                                                                                                                                                                                                                                                 
  TestHelper.ts                      |     100 |    77.77 |     100 |     100 | 24-30                                                                                                                                                                                                                                                                                                                                                                                                                              
  soapTestHelper.ts                  |   11.47 |     2.77 |    7.14 |   12.06 | 25-62,75,100-221                                                                                                                                                                                                                                                                                                                                                                                                                   
  testdataHelper.ts                  |   47.82 |       30 |      50 |   47.82 | 21-41,54                                                                                                                                                                                                                                                                                                                                                                                                                           
-------------------------------------|---------|----------|---------|---------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
Test Suites: 1 failed, 1 total
Tests:       7 failed, 93 passed, 100 total
Snapshots:   0 total
Time:        9.168 s
Ran all test suites matching /test\/core\/service\/impl\/LinePlanAcquisitionServiceTx.test.ts/i.
 ELIFECYCLE  Command failed with exit code 1.
