import { describe, expect, test } from "@jest/globals";
import { Sequelize } from "sequelize";

import { LinesEntity } from "@/core/entity/LinesEntity";
import { isLockNotAvailableError, usePsql } from "@/database/psql";
import { describeWithDB } from "../testing/TestHelper";

describeWithDB("DB locking test", () => {
    let sequelize: Sequelize = null;

    const lines = [
        {
            lineId: "03024042301",
            lineStatus: "01",
            nnumber: "N121152174",
            simFlag: false,
        },
        {
            lineId: "03024042302",
            lineStatus: "03",
            nnumber: "N121152174",
            simFlag: false,
        },
    ];

    beforeAll(async () => {
        sequelize = await usePsql();
        // create testdata
        await Promise.all(
            lines.map(async (line) => {
                await LinesEntity.create(line);
            }),
        );
    });

    afterAll(async () => {
        await LinesEntity.destroy({
            where: {
                lineId: lines.map((line) => line.lineId),
            },
        });
        await sequelize.close();
    });

    // while acquiring a lock on a row, should throw error immediately if the row is already locked by another transaction
    describe("isLockNotAvailableError (select ... for update nowait)", () => {
        test("should throw no error if requested rows are different", async () => {
            const [tx1, tx2] = await Promise.all([
                sequelize.transaction(),
                sequelize.transaction(),
            ]);
            const query =
                "select line_id from lines where line_id = :line_id for update nowait";
            try {
                const line1Promise = sequelize.query(query, {
                    replacements: { line_id: lines[0].lineId },
                    transaction: tx1,
                });
                const line2Promise = sequelize.query(query, {
                    replacements: { line_id: lines[1].lineId },
                    transaction: tx2,
                });

                let errorThrown = false;
                try {
                    await line1Promise;
                    await line2Promise;
                } catch (error) {
                    errorThrown = true;
                    expect(isLockNotAvailableError(error)).toBe(false);
                }
                expect(errorThrown).toBe(false);
            } finally {
                await Promise.all([tx1.rollback(), tx2.rollback()]);
            }
        });

        test("should throw error if requested rows are same", async () => {
            const [tx1, tx2] = await Promise.all([
                sequelize.transaction(),
                sequelize.transaction(),
            ]);
            const query =
                "select line_id from lines where line_id = :line_id for update nowait";
            try {
                const line1Promise = sequelize.query(query, {
                    replacements: { line_id: lines[0].lineId },
                    transaction: tx1,
                });
                const line2Promise = sequelize.query(query, {
                    replacements: { line_id: lines[0].lineId },
                    transaction: tx2,
                });

                let errorThrown = false;
                try {
                    await line1Promise;
                    await line2Promise;
                } catch (error) {
                    errorThrown = true;
                    // console.error(error);
                    expect(isLockNotAvailableError(error)).toBe(true);
                }
                expect(errorThrown).toBe(true);
            } finally {
                await Promise.all([tx1.rollback(), tx2.rollback()]);
            }
        });
    });
});
