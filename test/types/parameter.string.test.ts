import { getStringParameter } from "@/types/parameter.string";

describe("Parameter String", () => {
    describe("Normal Cases", () => {
        const cases = {
            param1: "asdf",
            param2: "123",
            param3: 456,
            param4: "",
            param5: " ",
            param6: undefined,
            param7: null,
            param8: NaN, // this will be converted to null
            param9: "-99999999999",
            param10: "99999999999",
            param11: -99999999999,
            param12: 99999999999,
            param13: "000",
            param14: "deleted",
        };
        const request = JSON.parse(JSON.stringify(cases));
        delete request.param14;

        test("should return string value for string input", () => {
            expect(getStringParameter(request.param1)).toEqual("asdf");
        });

        test("should return string value for numeric input", () => {
            expect(getStringParameter(request.param2)).toEqual("123");
        });

        test("should convert number to string", () => {
            expect(getStringParameter(request.param3)).toEqual("456");
        });

        test("should return empty string for empty input", () => {
            expect(getStringParameter(request.param4)).toEqual("");
        });

        test("should return string with space for space input", () => {
            expect(getStringParameter(request.param5)).toEqual(" ");
        });

        test("should return undefined for undefined input", () => {
            expect(getStringParameter(request.param6)).toBeUndefined();
        });

        test("should return null for null input", () => {
            expect(getStringParameter(request.param7)).toBeNull();
        });

        test("should return null for NaN input", () => {
            expect(getStringParameter(request.param8)).toBeNull();
        });

        test("should return string for negative large number", () => {
            expect(getStringParameter(request.param9)).toEqual("-99999999999");
        });

        test("should return string for positive large number", () => {
            expect(getStringParameter(request.param10)).toEqual("99999999999");
        });

        test("should convert negative large number to string", () => {
            expect(getStringParameter(request.param11)).toEqual("-99999999999");
        });

        test("should convert positive large number to string", () => {
            expect(getStringParameter(request.param12)).toEqual("99999999999");
        });

        test("should return string for zero input", () => {
            expect(getStringParameter(request.param13)).toEqual("000");
        });

        test("should return string for deleted input", () => {
            expect(getStringParameter(request.param14)).toBeUndefined();
        });
    });

    describe("Runtime Type-Mismatch Cases", () => {
        // the function only accepts `string | number`, but need to check other types which can
        // be passed at runtime.
        // since there is no field with allowed value `true` or `false` in documentation,
        // perhaps these cases should not happen in reality.
        const cases = [
            ["boolean true", true],
            ["boolean false", false],
            ["object", { key: "value" }],
            ["array", ["value1", "value2"]],
            ["function", () => "value"],
            ["bigint", BigInt(123)],
            ["symbol", Symbol("sym")],
        ];

        test.each(cases)(
            "should return the same value for %s input",
            (_name, input: any) => {
                expect(getStringParameter(input as unknown as any)).toEqual(
                    input,
                );
            },
        );
    });
});
