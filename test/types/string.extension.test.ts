import { describe, expect, test, beforeEach } from "@jest/globals";
import "@/types/string.extension";

describe("[types] string.extension", () => {
    test("Should return true when comparing two strings", () => {
        expect("test".equals("test")).toBe(true);
    });

    test("Should return false when comparing null with a string", () => {
        expect("test".equals(null)).toBe(false);
    });

    test("Should return false when comparing a string with different string", () => {
        expect("test".equals("test2")).toBe(false);
    });
});
