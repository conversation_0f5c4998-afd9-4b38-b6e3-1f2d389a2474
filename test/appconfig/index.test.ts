import { describe, expect, test } from "@jest/globals";
import { InvocationContext } from '@azure/functions';
import AppConfig from "@/appconfig/index";

const mockContext: InvocationContext = jest.fn() as any;

describe("appconfig/index", () => {
    describe("getDBConfig", () => {
        test("should return database configuration", async () => {
            let dbConfig = await AppConfig.getDBConfig(mockContext);
            // This will use local settings after first call
            dbConfig = await AppConfig.getDBConfig(mockContext);
            expect(dbConfig).toBeDefined();
        });

        // TODO try set env vars process.env.LOCAL_SETTINGS_CONFIG || process.env.RUN_IN_DOCKER for AppConfig
        // that's for making isLocalConfig = false and cover 44-58
    });

    describe("getCoreConfig", () => {
        test("should return core configuration", async () => {
            const coreConfig = await AppConfig.getCoreConfig(mockContext);
            expect(coreConfig).toBeDefined();
        });
    });
});