import {
    describeWithDB,
    generateProcessID,
    generateSOID,
} from "../testing/TestHelper";
import DefaultContext from "../testing/DefaultContext";

import { CoreSwimmyStatus } from "@/constants/coreSwimmy";
import { disconnect<PERSON>ongo, useMongo } from "@/database/mongo";
import CoreSwimmyApiLog from "@/models/coreSwimmyApiLog";
import { SwimmyType } from "@/types/coreSwimmyApiLog";
import { getNumberValue } from "@/utils";
import AppConfig from "@/appconfig";

describeWithDB("@/models/coreSwimmyApiLog", () => {
    const testdata = {
        /** PF0xx */
        soid: {
            haishi_shinki: generateSOID(), // 廃止 + 新規
            reissue: generateSOID(), // SIM再発行
            option_change: generateSOID(), // オプション変更
            access: generateSOID(), // アクセス方式変更
            unrelated: generateSOID(), // not related to the chain
        },
        requestOrderId: {
            haishi_shinki: generateProcessID(), // 廃止 + 新規
            reissue: generateProcessID(), // SIM再発行
            option_change: generateProcessID(), // オプション変更
            access: generateProcessID(), // アクセス方式変更
            unrelated: generateProcessID(), // not related to the chain
        },
        lineNo: "08024100701",
        otherLineNo: "08024100702",
        tenantId: "TST000",
        delay: getNumberValue(
            AppConfig.getCoreConfig(null).CORE_SWIMMY_API.MESSAGE_DELAY_SECONDS,
            60,
        ),
    };

    beforeAll(async () => {
        await useMongo(DefaultContext);
    });

    afterAll(async () => {
        await disconnectMongo(DefaultContext);
    });

    afterEach(async () => {
        // clear testdata
        await CoreSwimmyApiLog.deleteMany({
            soId: { $in: Object.values(testdata.soid) },
        });
    });

    describe("createSwimmyApiLog", () => {
        // example input param (廃止)
        const __createTemplate = {
            tenantId: testdata.tenantId,
            tempoId: "",
            kaisenNo: testdata.lineNo,
            swimmyType: "008" as SwimmyType,
            soId: testdata.soid.haishi_shinki,
            requestOrderId: testdata.requestOrderId.haishi_shinki,
            requestParam: {},
            status: CoreSwimmyStatus.TOUROKU_IRAI_NOT_YET_SENT,
            jikkouchuFlg: true, // set true because we're sending to SB
            isChainedTransaction: undefined as boolean,
        };

        async function expectFromDB(
            id: string,
            status: CoreSwimmyStatus,
            isChainedTransaction: boolean,
            waitingIds: string[],
            scheduledAtIsSet?: boolean,
        ) {
            const doc = await CoreSwimmyApiLog.findById(id);
            expect(doc).not.toBeNull();
            expect(doc.status).toEqual(status);
            // jikkouchuFlg should only be `true` when status is TOUROKU_IRAI_NOT_YET_SENT
            expect(doc.jikkouchuFlg).toEqual(
                status === CoreSwimmyStatus.TOUROKU_IRAI_NOT_YET_SENT,
            );
            expect(doc.isChainedTransaction).toEqual(isChainedTransaction);
            expect(doc.needToActiveTheseIDs).toEqual(waitingIds);

            if (scheduledAtIsSet === true) {
                expect(doc.scheduledAt).not.toBeNull();
                expect(doc.scheduledAt).toBeGreaterThan(
                    Math.floor(Date.now() / 1000),
                );
            } else if (scheduledAtIsSet === false) {
                expect(doc.scheduledAt).toBeNull();
            }
        }

        describe("Normal case", () => {
            test("should create a new CoreSwimmyApiLog", async () => {
                const log = await CoreSwimmyApiLog.createSwimmyApiLog(
                    __createTemplate,
                );
                expect(log).not.toBeNull();
                expect(log.status).toEqual(
                    CoreSwimmyStatus.TOUROKU_IRAI_NOT_YET_SENT,
                );
                expect(log.jikkouchuFlg).toEqual(true);
                expectFromDB(
                    log.id,
                    CoreSwimmyStatus.TOUROKU_IRAI_NOT_YET_SENT,
                    false,
                    [],
                );
            });

            test("should not link transaction if no previous transaction created within 1 minute", async () => {
                const optionChangeInput = structuredClone(__createTemplate);
                optionChangeInput.swimmyType = "006"; // 回線オプション変更
                optionChangeInput.soId = testdata.soid.option_change;
                optionChangeInput.requestOrderId =
                    testdata.requestOrderId.option_change;
                const firstLog = await CoreSwimmyApiLog.createSwimmyApiLog(
                    optionChangeInput,
                );
                await CoreSwimmyApiLog.updateOne(
                    {
                        _id: firstLog.id,
                    },
                    {
                        $set: {
                            createdAt:
                                firstLog.createdAt - (testdata.delay + 10), // make it older than delay
                        },
                    },
                );

                const reissueInput = structuredClone(__createTemplate);
                reissueInput.swimmyType = "004"; // SIM再発行
                reissueInput.soId = testdata.soid.reissue;
                reissueInput.requestOrderId = testdata.requestOrderId.reissue;
                const secondLog = await CoreSwimmyApiLog.createSwimmyApiLog(
                    reissueInput,
                );

                // TODO confirm if previous transaction has not linked yet but created more than 1 minute ago
                // for example during maintenance mode -> subsequent transaction will be enqueued directly
                await expectFromDB(
                    firstLog.id,
                    CoreSwimmyStatus.TOUROKU_IRAI_NOT_YET_SENT,
                    false,
                    [],
                    false,
                );
                await expectFromDB(
                    secondLog.id,
                    CoreSwimmyStatus.TOUROKU_IRAI_NOT_YET_SENT,
                    false,
                    [],
                    false,
                );
            });

            test("should link transaction if previous transaction created within 1 minute and not finished yet", async () => {
                const optionChangeInput = structuredClone(__createTemplate);
                optionChangeInput.swimmyType = "006"; // 回線オプション変更
                optionChangeInput.soId = testdata.soid.option_change;
                optionChangeInput.requestOrderId =
                    testdata.requestOrderId.option_change;
                const firstLog = await CoreSwimmyApiLog.createSwimmyApiLog(
                    optionChangeInput,
                );

                const reissueInput = structuredClone(__createTemplate);
                reissueInput.swimmyType = "004"; // SIM再発行
                reissueInput.soId = testdata.soid.reissue;
                reissueInput.requestOrderId = testdata.requestOrderId.reissue;
                const secondLog = await CoreSwimmyApiLog.createSwimmyApiLog(
                    reissueInput,
                );

                await expectFromDB(
                    firstLog.id,
                    CoreSwimmyStatus.TOUROKU_IRAI_NOT_YET_SENT,
                    false,
                    [secondLog.id],
                    false,
                );
                await expectFromDB(
                    secondLog.id,
                    CoreSwimmyStatus.ON_HOLD,
                    false,
                    [],
                    false,
                );
            });

            test("should enqueue transaction if previous transaction created within 1 minute but already finished", async () => {
                const optionChangeInput = structuredClone(__createTemplate);
                optionChangeInput.swimmyType = "006"; // 回線オプション変更
                optionChangeInput.soId = testdata.soid.option_change;
                optionChangeInput.requestOrderId =
                    testdata.requestOrderId.option_change;
                const firstLog = await CoreSwimmyApiLog.createSwimmyApiLog(
                    optionChangeInput,
                );
                await CoreSwimmyApiLog.updateOne(
                    {
                        _id: firstLog.id,
                    },
                    {
                        $set: {
                            status: CoreSwimmyStatus.TOUROKU_IRAI_RESULT_OK,
                            jikkouchuFlg: false,
                        },
                    },
                );

                const reissueInput = structuredClone(__createTemplate);
                reissueInput.swimmyType = "004"; // SIM再発行
                reissueInput.soId = testdata.soid.reissue;
                reissueInput.requestOrderId = testdata.requestOrderId.reissue;
                const secondLog = await CoreSwimmyApiLog.createSwimmyApiLog(
                    reissueInput,
                );

                await expectFromDB(
                    firstLog.id,
                    CoreSwimmyStatus.TOUROKU_IRAI_RESULT_OK,
                    false,
                    [],
                    false,
                );
                await expectFromDB(
                    secondLog.id,
                    CoreSwimmyStatus.TOUROKU_IRAI_NOT_YET_SENT,
                    false,
                    [],
                    true, // scheduledAt is set
                );
            });

            test("should create linked transaction: オプション変更(未登録依頼) -> アクセス方式変更(待機中) + SIM再発行(待機中) ", async () => {
                const optionChangeInput = structuredClone(__createTemplate);
                optionChangeInput.swimmyType = "006"; // 回線オプション変更
                optionChangeInput.soId = testdata.soid.option_change;
                optionChangeInput.requestOrderId =
                    testdata.requestOrderId.option_change;
                const optionChange = await CoreSwimmyApiLog.createSwimmyApiLog(
                    optionChangeInput,
                );

                const accessChangeInput = structuredClone(__createTemplate);
                accessChangeInput.swimmyType = "009"; // アクセス方式変更
                accessChangeInput.soId = testdata.soid.access;
                accessChangeInput.requestOrderId =
                    testdata.requestOrderId.access;
                const accessChange = await CoreSwimmyApiLog.createSwimmyApiLog(
                    accessChangeInput,
                );

                const reissueInput = structuredClone(__createTemplate);
                reissueInput.swimmyType = "004"; // SIM再発行
                reissueInput.soId = testdata.soid.reissue;
                reissueInput.requestOrderId = testdata.requestOrderId.reissue;
                const reissue = await CoreSwimmyApiLog.createSwimmyApiLog(
                    reissueInput,
                );

                await expectFromDB(
                    optionChange.id,
                    CoreSwimmyStatus.TOUROKU_IRAI_NOT_YET_SENT,
                    false,
                    [accessChange.id],
                    false,
                );
                await expectFromDB(
                    accessChange.id,
                    CoreSwimmyStatus.ON_HOLD,
                    false,
                    [reissue.id],
                    false,
                );
                await expectFromDB(
                    reissue.id,
                    CoreSwimmyStatus.ON_HOLD,
                    false,
                    [],
                    false,
                );
            });

            test("should create linked transaction: オプション変更(NG) -> アクセス方式変更(未登録依頼) + SIM再発行(待機中) ", async () => {
                const optionChangeInput = structuredClone(__createTemplate);
                optionChangeInput.swimmyType = "006"; // 回線オプション変更
                optionChangeInput.soId = testdata.soid.option_change;
                optionChangeInput.requestOrderId =
                    testdata.requestOrderId.option_change;
                const optionChange = await CoreSwimmyApiLog.createSwimmyApiLog(
                    optionChangeInput,
                );

                const accessChangeInput = structuredClone(__createTemplate);
                accessChangeInput.swimmyType = "009"; // アクセス方式変更
                accessChangeInput.soId = testdata.soid.access;
                accessChangeInput.requestOrderId =
                    testdata.requestOrderId.access;
                const accessChange = await CoreSwimmyApiLog.createSwimmyApiLog(
                    accessChangeInput,
                );

                // update オプション変更 to NG after creating アクセス方式変更
                await CoreSwimmyApiLog.updateOne(
                    {
                        _id: optionChange.id,
                    },
                    {
                        $set: {
                            status: CoreSwimmyStatus.TOUROKU_IRAI_RESULT_NG,
                            jikkouchuFlg: false,
                        },
                    },
                );
                // set アクセス方式変更 to processing (simulate being activated but still not finished)
                await CoreSwimmyApiLog.updateOne(
                    {
                        _id: accessChange.id,
                    },
                    {
                        $set: {
                            status: CoreSwimmyStatus.TOUROKU_IRAI_NOT_YET_SENT,
                            jikkouchuFlg: true,
                        },
                    },
                );

                const reissueInput = structuredClone(__createTemplate);
                reissueInput.swimmyType = "004"; // SIM再発行
                reissueInput.soId = testdata.soid.reissue;
                reissueInput.requestOrderId = testdata.requestOrderId.reissue;
                const reissue = await CoreSwimmyApiLog.createSwimmyApiLog(
                    reissueInput,
                );

                await expectFromDB(
                    optionChange.id,
                    CoreSwimmyStatus.TOUROKU_IRAI_RESULT_NG,
                    false,
                    [accessChange.id],
                    false,
                );
                await expectFromDB(
                    accessChange.id,
                    CoreSwimmyStatus.TOUROKU_IRAI_NOT_YET_SENT,
                    false,
                    [reissue.id],
                    false,
                );
                await expectFromDB(
                    reissue.id,
                    CoreSwimmyStatus.ON_HOLD,
                    false,
                    [],
                    false,
                );
            });

            test("should create scheduled transaction: オプション変更(NG) -> アクセス方式変更(NG) + SIM再発行(未登録依頼)", async () => {
                const optionChangeInput = structuredClone(__createTemplate);
                optionChangeInput.swimmyType = "006"; // 回線オプション変更
                optionChangeInput.soId = testdata.soid.option_change;
                optionChangeInput.requestOrderId =
                    testdata.requestOrderId.option_change;
                const optionChange = await CoreSwimmyApiLog.createSwimmyApiLog(
                    optionChangeInput,
                );

                const accessChangeInput = structuredClone(__createTemplate);
                accessChangeInput.swimmyType = "009"; // アクセス方式変更
                accessChangeInput.soId = testdata.soid.access;
                accessChangeInput.requestOrderId =
                    testdata.requestOrderId.access;
                const accessChange = await CoreSwimmyApiLog.createSwimmyApiLog(
                    accessChangeInput,
                );

                // update both オプション変更 and アクセス方式変更 to NG (both are completed)
                await CoreSwimmyApiLog.updateMany(
                    {
                        _id: { $in: [optionChange.id, accessChange.id] },
                    },
                    {
                        $set: {
                            status: CoreSwimmyStatus.TOUROKU_IRAI_RESULT_NG,
                            jikkouchuFlg: false,
                        },
                    },
                );

                const reissueInput = structuredClone(__createTemplate);
                reissueInput.swimmyType = "004"; // SIM再発行
                reissueInput.soId = testdata.soid.reissue;
                reissueInput.requestOrderId = testdata.requestOrderId.reissue;
                const reissue = await CoreSwimmyApiLog.createSwimmyApiLog(
                    reissueInput,
                );

                await expectFromDB(
                    optionChange.id,
                    CoreSwimmyStatus.TOUROKU_IRAI_RESULT_NG,
                    false,
                    [accessChange.id],
                    false,
                );
                await expectFromDB(
                    accessChange.id,
                    CoreSwimmyStatus.TOUROKU_IRAI_RESULT_NG,
                    false,
                    [], // new transaction is not linked
                    false,
                );
                await expectFromDB(
                    reissue.id,
                    CoreSwimmyStatus.TOUROKU_IRAI_NOT_YET_SENT,
                    false,
                    [],
                    true, // since previous transactions already finished
                );
            });
        });

        describe("同一MVNE chain", () => {
            async function createHaishiShinkiChain(): Promise<{
                haishiId: string;
                shinkiId: string;
            }> {
                const haishiInput = structuredClone(__createTemplate);
                haishiInput.isChainedTransaction = true;
                const haishi = await CoreSwimmyApiLog.createSwimmyApiLog(
                    haishiInput,
                    { skipWaitingCheck: true },
                );
                const shinkiInput = structuredClone(__createTemplate);
                shinkiInput.swimmyType = "007"; // 回線追加
                const shinki = await CoreSwimmyApiLog.createSwimmyApiLog(
                    shinkiInput,
                    { setWaitingForId: haishi.id },
                );

                expect(haishi).not.toBeNull();
                expect(shinki).not.toBeNull();

                return { haishiId: haishi.id, shinkiId: shinki.id };
            }

            test("should create 新規(待機中) order after 廃止", async () => {
                const haishiInput = structuredClone(__createTemplate);
                haishiInput.isChainedTransaction = true;
                const haishi = await CoreSwimmyApiLog.createSwimmyApiLog(
                    haishiInput,
                    { skipWaitingCheck: true },
                );

                expect(haishi).not.toBeNull();
                expect(haishi.status).toEqual(
                    CoreSwimmyStatus.TOUROKU_IRAI_NOT_YET_SENT,
                );
                expect(haishi.jikkouchuFlg).toEqual(true);

                const shinkiInput = structuredClone(__createTemplate);
                shinkiInput.swimmyType = "007"; // 回線追加
                const shinki = await CoreSwimmyApiLog.createSwimmyApiLog(
                    shinkiInput,
                    { setWaitingForId: haishi.id },
                );
                expect(shinki).not.toBeNull();
                expect(shinki.status).toEqual(CoreSwimmyStatus.ON_HOLD);
                expect(shinki.jikkouchuFlg).toEqual(false);

                await expectFromDB(
                    haishi.id,
                    CoreSwimmyStatus.TOUROKU_IRAI_NOT_YET_SENT,
                    true,
                    [shinki.id],
                );
                await expectFromDB(
                    shinki.id,
                    CoreSwimmyStatus.ON_HOLD,
                    true,
                    [],
                );
            });

            test("should create オプション変更(待機中) after 廃止(未)→新規(待機中)", async () => {
                const { haishiId, shinkiId } = await createHaishiShinkiChain();
                const optionChangeInput = structuredClone(__createTemplate);
                optionChangeInput.swimmyType = "006"; // 回線オプション変更
                optionChangeInput.soId = testdata.soid.option_change;
                optionChangeInput.requestOrderId =
                    testdata.requestOrderId.option_change;
                const optionChange = await CoreSwimmyApiLog.createSwimmyApiLog(
                    optionChangeInput,
                );
                expect(optionChange).not.toBeNull();
                expect(optionChange.status).toEqual(CoreSwimmyStatus.ON_HOLD);
                expect(optionChange.jikkouchuFlg).toEqual(false);

                await expectFromDB(
                    haishiId,
                    CoreSwimmyStatus.TOUROKU_IRAI_NOT_YET_SENT,
                    true,
                    [shinkiId],
                );
                await expectFromDB(shinkiId, CoreSwimmyStatus.ON_HOLD, true, [
                    optionChange.id,
                ]);
                await expectFromDB(
                    optionChange.id,
                    CoreSwimmyStatus.ON_HOLD,
                    true,
                    [],
                );
            });

            test("should create オプション変更(待機中) after 廃止(OK)→新規(未) where 新規 is scheduled > wait time", async () => {
                const { haishiId, shinkiId } = await createHaishiShinkiChain();
                const tenMinutesBefore = Math.floor(Date.now() / 1000) - 600;
                // update createdAt and status
                await CoreSwimmyApiLog.updateOne(
                    {
                        _id: haishiId,
                    },
                    {
                        $set: {
                            status: CoreSwimmyStatus.TOUROKU_IRAI_RESULT_OK,
                            jikkouchuFlg: false,
                            createdAt: tenMinutesBefore,
                        },
                    },
                );
                await CoreSwimmyApiLog.updateOne(
                    {
                        _id: shinkiId,
                    },
                    {
                        $set: {
                            status: CoreSwimmyStatus.TOUROKU_IRAI_NOT_YET_SENT,
                            jikkouchuFlg: true,
                            createdAt: tenMinutesBefore,
                        },
                    },
                );

                const optionChangeInput = structuredClone(__createTemplate);
                optionChangeInput.swimmyType = "006"; // 回線オプション変更
                optionChangeInput.soId = testdata.soid.option_change;
                optionChangeInput.requestOrderId =
                    testdata.requestOrderId.option_change;
                const optionChange = await CoreSwimmyApiLog.createSwimmyApiLog(
                    optionChangeInput,
                );
                expect(optionChange).not.toBeNull();
                expect(optionChange.status).toEqual(CoreSwimmyStatus.ON_HOLD);
                expect(optionChange.jikkouchuFlg).toEqual(false);

                await expectFromDB(
                    haishiId,
                    CoreSwimmyStatus.TOUROKU_IRAI_RESULT_OK,
                    true,
                    [shinkiId],
                );
                await expectFromDB(
                    shinkiId,
                    CoreSwimmyStatus.TOUROKU_IRAI_NOT_YET_SENT,
                    true,
                    [optionChange.id],
                );
                await expectFromDB(
                    optionChange.id,
                    CoreSwimmyStatus.ON_HOLD,
                    true,
                    [],
                );
            });

            test("should create linked アクセス方式変更(待機中) after 廃止(未)→新規(待機中)→オプション変更(待機中)", async () => {
                const { haishiId, shinkiId } = await createHaishiShinkiChain();
                const optionChangeInput = structuredClone(__createTemplate);
                optionChangeInput.swimmyType = "006"; // 回線オプション変更
                optionChangeInput.soId = testdata.soid.option_change;
                optionChangeInput.requestOrderId =
                    testdata.requestOrderId.option_change;
                const optionChange = await CoreSwimmyApiLog.createSwimmyApiLog(
                    optionChangeInput,
                );

                const accessChangeInput = structuredClone(__createTemplate);
                accessChangeInput.swimmyType = "009"; // アクセス方式変更
                accessChangeInput.soId = testdata.soid.access;
                accessChangeInput.requestOrderId =
                    testdata.requestOrderId.access;
                const accessChange = await CoreSwimmyApiLog.createSwimmyApiLog(
                    accessChangeInput,
                );

                expect(accessChange).not.toBeNull();
                expect(accessChange.status).toEqual(CoreSwimmyStatus.ON_HOLD);
                expect(accessChange.jikkouchuFlg).toEqual(false);

                await expectFromDB(
                    haishiId,
                    CoreSwimmyStatus.TOUROKU_IRAI_NOT_YET_SENT,
                    true,
                    [shinkiId],
                );
                await expectFromDB(shinkiId, CoreSwimmyStatus.ON_HOLD, true, [
                    optionChange.id,
                ]);
                await expectFromDB(
                    optionChange.id,
                    CoreSwimmyStatus.ON_HOLD,
                    true,
                    [accessChange.id],
                );
                await expectFromDB(
                    accessChange.id,
                    CoreSwimmyStatus.ON_HOLD,
                    true,
                    [],
                );
            });

            test("should create linked アクセス方式変更(待機中) during partial execution: 廃止(OK)→新規(OK)→オプション変更(未登録依頼)", async () => {
                const { haishiId, shinkiId } = await createHaishiShinkiChain();
                const optionChangeInput = structuredClone(__createTemplate);
                optionChangeInput.swimmyType = "006"; // 回線オプション変更
                optionChangeInput.soId = testdata.soid.option_change;
                optionChangeInput.requestOrderId =
                    testdata.requestOrderId.option_change;
                const optionChange = await CoreSwimmyApiLog.createSwimmyApiLog(
                    optionChangeInput,
                );

                await Promise.all([
                    // 廃止(OK) & 新規(OK)
                    CoreSwimmyApiLog.updateMany(
                        {
                            _id: { $in: [haishiId, shinkiId] },
                        },
                        {
                            status: CoreSwimmyStatus.TOUROKU_IRAI_RESULT_OK,
                            jikkouchuFlg: false,
                        },
                    ),
                    // オプション変更(未登録依頼)
                    CoreSwimmyApiLog.updateOne(
                        {
                            _id: optionChange.id,
                        },
                        {
                            status: CoreSwimmyStatus.TOUROKU_IRAI_NOT_YET_SENT,
                            jikkouchuFlg: true,
                        },
                    ),
                ]);

                const accessChangeInput = structuredClone(__createTemplate);
                accessChangeInput.swimmyType = "009"; // アクセス方式変更
                accessChangeInput.soId = testdata.soid.access;
                accessChangeInput.requestOrderId =
                    testdata.requestOrderId.access;
                const accessChange = await CoreSwimmyApiLog.createSwimmyApiLog(
                    accessChangeInput,
                );

                await expectFromDB(
                    haishiId,
                    CoreSwimmyStatus.TOUROKU_IRAI_RESULT_OK,
                    true,
                    [shinkiId],
                );
                await expectFromDB(
                    shinkiId,
                    CoreSwimmyStatus.TOUROKU_IRAI_RESULT_OK,
                    true,
                    [optionChange.id],
                );
                await expectFromDB(
                    optionChange.id,
                    CoreSwimmyStatus.TOUROKU_IRAI_NOT_YET_SENT,
                    true,
                    [accessChange.id],
                );
                await expectFromDB(
                    accessChange.id,
                    CoreSwimmyStatus.ON_HOLD,
                    true,
                    [],
                );
            });

            test("should create linked アクセス方式変更(未登録依頼) when there is NG in last part of chain: 廃止(OK)→新規(OK)→オプション変更(NG)", async () => {
                // NOTE because last transaction already finished, new transaction will be created as scheduled message but not chained part of 同一MVNE (+ isChainedTransaction = false)
                const { haishiId, shinkiId } = await createHaishiShinkiChain();
                const optionChangeInput = structuredClone(__createTemplate);
                optionChangeInput.swimmyType = "006"; // 回線オプション変更
                optionChangeInput.soId = testdata.soid.option_change;
                optionChangeInput.requestOrderId =
                    testdata.requestOrderId.option_change;
                const optionChange = await CoreSwimmyApiLog.createSwimmyApiLog(
                    optionChangeInput,
                );

                await Promise.all([
                    // 廃止(OK) & 新規(OK)
                    CoreSwimmyApiLog.updateMany(
                        {
                            _id: { $in: [haishiId, shinkiId] },
                        },
                        {
                            status: CoreSwimmyStatus.TOUROKU_IRAI_RESULT_OK,
                            jikkouchuFlg: false,
                        },
                    ),
                    // オプション変更(NG)
                    CoreSwimmyApiLog.updateOne(
                        {
                            _id: optionChange.id,
                        },
                        {
                            status: CoreSwimmyStatus.TOUROKU_IRAI_RESULT_NG,
                            jikkouchuFlg: false,
                        },
                    ),
                ]);

                const accessChangeInput = structuredClone(__createTemplate);
                accessChangeInput.swimmyType = "009"; // アクセス方式変更
                accessChangeInput.soId = testdata.soid.access;
                accessChangeInput.requestOrderId =
                    testdata.requestOrderId.access;
                const accessChange = await CoreSwimmyApiLog.createSwimmyApiLog(
                    accessChangeInput,
                );

                await expectFromDB(
                    haishiId,
                    CoreSwimmyStatus.TOUROKU_IRAI_RESULT_OK,
                    true,
                    [shinkiId],
                );
                await expectFromDB(
                    shinkiId,
                    CoreSwimmyStatus.TOUROKU_IRAI_RESULT_OK,
                    true,
                    [optionChange.id],
                );
                await expectFromDB(
                    optionChange.id,
                    CoreSwimmyStatus.TOUROKU_IRAI_RESULT_NG,
                    true,
                    [], // new transaction is not linked
                );
                await expectFromDB(
                    accessChange.id,
                    CoreSwimmyStatus.TOUROKU_IRAI_NOT_YET_SENT, // sent as scheduled message
                    false, // not part of 同一MVNE chain
                    [],
                    true, // scheduledAt is set
                );
            });

            test("should create linked アクセス方式変更(待機中) when there is NG in chain: 廃止(OK)→新規(NG)→オプション変更(待機中)", async () => {
                const { haishiId, shinkiId } = await createHaishiShinkiChain();
                const optionChangeInput = structuredClone(__createTemplate);
                optionChangeInput.swimmyType = "006"; // 回線オプション変更
                optionChangeInput.soId = testdata.soid.option_change;
                optionChangeInput.requestOrderId =
                    testdata.requestOrderId.option_change;
                const optionChange = await CoreSwimmyApiLog.createSwimmyApiLog(
                    optionChangeInput,
                );

                await Promise.all([
                    // 廃止(OK)
                    CoreSwimmyApiLog.updateOne(
                        {
                            _id: haishiId,
                        },
                        {
                            status: CoreSwimmyStatus.TOUROKU_IRAI_RESULT_OK,
                            jikkouchuFlg: false,
                        },
                    ),
                    // 新規(NG)
                    CoreSwimmyApiLog.updateOne(
                        {
                            _id: shinkiId,
                        },
                        {
                            status: CoreSwimmyStatus.TOUROKU_IRAI_RESULT_NG,
                            jikkouchuFlg: false,
                        },
                    ),
                ]);

                const accessChangeInput = structuredClone(__createTemplate);
                accessChangeInput.swimmyType = "009"; // アクセス方式変更
                accessChangeInput.soId = testdata.soid.access;
                accessChangeInput.requestOrderId =
                    testdata.requestOrderId.access;
                const accessChange = await CoreSwimmyApiLog.createSwimmyApiLog(
                    accessChangeInput,
                );

                await expectFromDB(
                    haishiId,
                    CoreSwimmyStatus.TOUROKU_IRAI_RESULT_OK,
                    true,
                    [shinkiId],
                );
                await expectFromDB(
                    shinkiId,
                    CoreSwimmyStatus.TOUROKU_IRAI_RESULT_NG,
                    true,
                    [optionChange.id],
                );
                await expectFromDB(
                    optionChange.id,
                    CoreSwimmyStatus.ON_HOLD,
                    true,
                    [accessChange.id],
                );
                await expectFromDB(
                    accessChange.id,
                    CoreSwimmyStatus.ON_HOLD,
                    true,
                    [],
                );
            });

            test("should create linked アクセス方式変更(未登録依頼) if last part is error: 廃止(OK)→新規(OK)→オプション変更(NG)", async () => {
                // NOTE because last transaction already finished, new transaction will be created as scheduled message but not chained part of 同一MVNE (+ isChainedTransaction = false)
                const { haishiId, shinkiId } = await createHaishiShinkiChain();
                const optionChangeInput = structuredClone(__createTemplate);
                optionChangeInput.swimmyType = "006"; // 回線オプション変更
                optionChangeInput.soId = testdata.soid.option_change;
                optionChangeInput.requestOrderId =
                    testdata.requestOrderId.option_change;
                const optionChange = await CoreSwimmyApiLog.createSwimmyApiLog(
                    optionChangeInput,
                );

                await Promise.all([
                    // 廃止(OK) & 新規(OK)
                    CoreSwimmyApiLog.updateMany(
                        {
                            _id: { $in: [haishiId, shinkiId] },
                        },
                        {
                            status: CoreSwimmyStatus.TOUROKU_IRAI_RESULT_OK,
                            jikkouchuFlg: false,
                        },
                    ),
                    // オプション変更(NG)
                    CoreSwimmyApiLog.updateOne(
                        {
                            _id: optionChange.id,
                        },
                        {
                            status: CoreSwimmyStatus.STOPPING_FOR_ERROR,
                            jikkouchuFlg: false,
                        },
                    ),
                ]);

                const accessChangeInput = structuredClone(__createTemplate);
                accessChangeInput.swimmyType = "009"; // アクセス方式変更
                accessChangeInput.soId = testdata.soid.access;
                accessChangeInput.requestOrderId =
                    testdata.requestOrderId.access;
                const accessChange = await CoreSwimmyApiLog.createSwimmyApiLog(
                    accessChangeInput,
                );

                await expectFromDB(
                    haishiId,
                    CoreSwimmyStatus.TOUROKU_IRAI_RESULT_OK,
                    true,
                    [shinkiId],
                );
                await expectFromDB(
                    shinkiId,
                    CoreSwimmyStatus.TOUROKU_IRAI_RESULT_OK,
                    true,
                    [optionChange.id],
                );
                await expectFromDB(
                    optionChange.id,
                    CoreSwimmyStatus.STOPPING_FOR_ERROR,
                    true,
                    [], // new transaction is not linked
                );
                await expectFromDB(
                    accessChange.id,
                    CoreSwimmyStatus.TOUROKU_IRAI_NOT_YET_SENT,
                    false,
                    [],
                    true,
                );
            });

            test("should create not-linked アクセス方式変更(未登録依頼) if the chain has finished 廃止(OK)→新規(OK)→オプション変更(OK)", async () => {
                const { haishiId, shinkiId } = await createHaishiShinkiChain();
                const optionChangeInput = structuredClone(__createTemplate);
                optionChangeInput.swimmyType = "006"; // 回線オプション変更
                optionChangeInput.soId = testdata.soid.option_change;
                optionChangeInput.requestOrderId =
                    testdata.requestOrderId.option_change;
                const optionChange = await CoreSwimmyApiLog.createSwimmyApiLog(
                    optionChangeInput,
                );

                // 廃止(OK) & 新規(OK) & オプション変更(OK)
                const res = await CoreSwimmyApiLog.updateMany(
                    {
                        _id: { $in: [haishiId, shinkiId, optionChange.id] },
                    },
                    {
                        status: CoreSwimmyStatus.TOUROKU_IRAI_RESULT_OK,
                        jikkouchuFlg: false,
                    },
                );

                const accessChangeInput = structuredClone(__createTemplate);
                accessChangeInput.swimmyType = "009"; // アクセス方式変更
                accessChangeInput.soId = testdata.soid.access;
                accessChangeInput.requestOrderId =
                    testdata.requestOrderId.access;
                const accessChange = await CoreSwimmyApiLog.createSwimmyApiLog(
                    accessChangeInput,
                );

                await expectFromDB(
                    haishiId,
                    CoreSwimmyStatus.TOUROKU_IRAI_RESULT_OK,
                    true,
                    [shinkiId],
                );
                await expectFromDB(
                    shinkiId,
                    CoreSwimmyStatus.TOUROKU_IRAI_RESULT_OK,
                    true,
                    [optionChange.id],
                );
                await expectFromDB(
                    optionChange.id,
                    CoreSwimmyStatus.TOUROKU_IRAI_RESULT_OK,
                    true,
                    [],
                );
                await expectFromDB(
                    accessChange.id,
                    CoreSwimmyStatus.TOUROKU_IRAI_NOT_YET_SENT,
                    false,
                    [],
                );
            });

            test("should create not-linked アクセス方式変更(未登録依頼) if an active 廃止→新規 chain is from different lineNo", async () => {
                // link 廃止(未)→新規(待機中)→オプション変更(待機中)
                const { haishiId, shinkiId } = await createHaishiShinkiChain();
                const optionChangeInput = structuredClone(__createTemplate);
                optionChangeInput.swimmyType = "006"; // 回線オプション変更
                optionChangeInput.soId = testdata.soid.option_change;
                optionChangeInput.requestOrderId =
                    testdata.requestOrderId.option_change;
                const optionChange = await CoreSwimmyApiLog.createSwimmyApiLog(
                    optionChangeInput,
                );

                // アクセス方式変更(未登録依頼) from different lineNo
                const accessChangeInput = structuredClone(__createTemplate);
                accessChangeInput.kaisenNo = testdata.otherLineNo;
                accessChangeInput.swimmyType = "009"; // アクセス方式変更
                accessChangeInput.soId = testdata.soid.unrelated;
                accessChangeInput.requestOrderId =
                    testdata.requestOrderId.unrelated;
                const accessChange = await CoreSwimmyApiLog.createSwimmyApiLog(
                    accessChangeInput,
                );

                await expectFromDB(
                    haishiId,
                    CoreSwimmyStatus.TOUROKU_IRAI_NOT_YET_SENT,
                    true,
                    [shinkiId],
                );
                await expectFromDB(shinkiId, CoreSwimmyStatus.ON_HOLD, true, [
                    optionChange.id,
                ]);
                await expectFromDB(
                    optionChange.id,
                    CoreSwimmyStatus.ON_HOLD,
                    true,
                    [],
                );
                await expectFromDB(
                    accessChange.id,
                    CoreSwimmyStatus.TOUROKU_IRAI_NOT_YET_SENT,
                    false,
                    [],
                );
            });

            test("should create not-linked アクセス方式変更(未登録依頼) if existing transactions is not a chain", async () => {
                // transactions: 新規(未) and オプション変更(NG)
                const shinkiInput = structuredClone(__createTemplate);
                shinkiInput.swimmyType = "007"; // 回線追加
                const shinki = await CoreSwimmyApiLog.createSwimmyApiLog(
                    shinkiInput,
                    { skipWaitingCheck: true },
                );
                const optionChangeInput = structuredClone(__createTemplate);
                optionChangeInput.swimmyType = "006"; // 回線オプション変更
                optionChangeInput.soId = testdata.soid.option_change;
                optionChangeInput.requestOrderId =
                    testdata.requestOrderId.option_change;
                optionChangeInput.status =
                    CoreSwimmyStatus.TOUROKU_IRAI_RESULT_NG;
                optionChangeInput.jikkouchuFlg = false;

                const optionChange = await CoreSwimmyApiLog.createSwimmyApiLog(
                    optionChangeInput,
                    { skipWaitingCheck: true },
                );

                // アクセス方式変更(未登録依頼)
                const accessChangeInput = structuredClone(__createTemplate);
                accessChangeInput.swimmyType = "009"; // アクセス方式変更
                accessChangeInput.soId = testdata.soid.access;
                accessChangeInput.requestOrderId =
                    testdata.requestOrderId.access;
                const accessChange = await CoreSwimmyApiLog.createSwimmyApiLog(
                    accessChangeInput,
                );

                await expectFromDB(
                    shinki.id,
                    CoreSwimmyStatus.TOUROKU_IRAI_NOT_YET_SENT,
                    false,
                    [],
                );
                await expectFromDB(
                    optionChange.id,
                    CoreSwimmyStatus.TOUROKU_IRAI_RESULT_NG,
                    false,
                    [],
                );
                await expectFromDB(
                    accessChange.id,
                    CoreSwimmyStatus.TOUROKU_IRAI_NOT_YET_SENT,
                    false,
                    [],
                );
            });

            describe("need confirmation", () => {
                // TODO confirm these cases
                test("should create linked アクセス方式変更(待機中) even if there is 取り下げ in middle of chain: 廃止(OK)→新規(取り下げ)→オプション変更(待機中)", async () => {
                    // NOTE example: 新規 was 登録依頼済NG and status was changed manually to 取り下げ even though there is 待機中 order linked to it
                    const { haishiId, shinkiId } =
                        await createHaishiShinkiChain();
                    const optionChangeInput = structuredClone(__createTemplate);
                    optionChangeInput.swimmyType = "006"; // 回線オプション変更
                    optionChangeInput.soId = testdata.soid.option_change;
                    optionChangeInput.requestOrderId =
                        testdata.requestOrderId.option_change;
                    const optionChange =
                        await CoreSwimmyApiLog.createSwimmyApiLog(
                            optionChangeInput,
                        );

                    // 廃止(OK) & 新規(取り下げ)
                    const res = await Promise.all([
                        CoreSwimmyApiLog.updateOne(
                            {
                                _id: haishiId,
                            },
                            {
                                status: CoreSwimmyStatus.TOUROKU_IRAI_RESULT_OK,
                                jikkouchuFlg: false,
                            },
                        ),
                        CoreSwimmyApiLog.updateOne(
                            {
                                _id: shinkiId,
                            },
                            {
                                status: CoreSwimmyStatus.TORISAGE_RESULT_NG,
                                jikkouchuFlg: false,
                            },
                        ),
                    ]);

                    const accessChangeInput = structuredClone(__createTemplate);
                    accessChangeInput.swimmyType = "009"; // アクセス方式変更
                    accessChangeInput.soId = testdata.soid.access;
                    accessChangeInput.requestOrderId =
                        testdata.requestOrderId.access;
                    const accessChange =
                        await CoreSwimmyApiLog.createSwimmyApiLog(
                            accessChangeInput,
                        );

                    await expectFromDB(
                        haishiId,
                        CoreSwimmyStatus.TOUROKU_IRAI_RESULT_OK,
                        true,
                        [shinkiId],
                    );
                    await expectFromDB(
                        shinkiId,
                        CoreSwimmyStatus.TORISAGE_RESULT_NG,
                        true,
                        [optionChange.id],
                    );
                    await expectFromDB(
                        optionChange.id,
                        CoreSwimmyStatus.ON_HOLD,
                        true,
                        [accessChange.id],
                    );
                    await expectFromDB(
                        accessChange.id,
                        CoreSwimmyStatus.ON_HOLD,
                        true,
                        [],
                    );
                });

                test("should create not-linked アクセス方式変更(未登録依頼) if the last part of chain is 取り下げ: 廃止(OK)→新規(OK)→オプション変更(取り下げ)", async () => {
                    // NOTE same as the previous test but the last part is 取り下げ
                    const { haishiId, shinkiId } =
                        await createHaishiShinkiChain();
                    const optionChangeInput = structuredClone(__createTemplate);
                    optionChangeInput.swimmyType = "006"; // 回線オプション変更
                    optionChangeInput.soId = testdata.soid.option_change;
                    optionChangeInput.requestOrderId =
                        testdata.requestOrderId.option_change;
                    const optionChange =
                        await CoreSwimmyApiLog.createSwimmyApiLog(
                            optionChangeInput,
                        );

                    // 廃止(OK) & 新規(OK) & オプション変更(取り下げ)
                    await Promise.all([
                        CoreSwimmyApiLog.updateMany(
                            {
                                _id: { $in: [haishiId, shinkiId] },
                            },
                            {
                                status: CoreSwimmyStatus.TOUROKU_IRAI_RESULT_OK,
                                jikkouchuFlg: false,
                            },
                        ),
                        CoreSwimmyApiLog.updateOne(
                            {
                                _id: optionChange.id,
                            },
                            {
                                status: CoreSwimmyStatus.TORISAGE_RESULT_NG,
                                jikkouchuFlg: false,
                            },
                        ),
                    ]);

                    const accessChangeInput = structuredClone(__createTemplate);
                    accessChangeInput.swimmyType = "009"; // アクセス方式変更
                    accessChangeInput.soId = testdata.soid.access;
                    accessChangeInput.requestOrderId =
                        testdata.requestOrderId.access;
                    const accessChange =
                        await CoreSwimmyApiLog.createSwimmyApiLog(
                            accessChangeInput,
                        );

                    await expectFromDB(
                        haishiId,
                        CoreSwimmyStatus.TOUROKU_IRAI_RESULT_OK,
                        true,
                        [shinkiId],
                    );
                    await expectFromDB(
                        shinkiId,
                        CoreSwimmyStatus.TOUROKU_IRAI_RESULT_OK,
                        true,
                        [optionChange.id],
                    );
                    await expectFromDB(
                        optionChange.id,
                        CoreSwimmyStatus.TORISAGE_RESULT_NG,
                        true,
                        [],
                    );
                    await expectFromDB(
                        accessChange.id,
                        CoreSwimmyStatus.TOUROKU_IRAI_NOT_YET_SENT,
                        false,
                        [],
                    );
                });
            });
        });
    });
});
