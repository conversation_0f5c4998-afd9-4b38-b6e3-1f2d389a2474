import { TableClient } from "@azure/data-tables";
import { InvocationContext } from "@azure/functions";
import StorageTableService from "../../src/services/storageTableService";
import DefaultContext from "../testing/DefaultContext";
import { describe, expect } from "@jest/globals";

describe("StorageTableService with Azurite", () => {
    let service: StorageTableService;
    const tableName = "MntScriptPid";
    let verificationClient: TableClient;

    beforeAll(async () => {
        verificationClient = TableClient.fromConnectionString(
            "UseDevelopmentStorage=true",
            tableName
        );

        try {
            await verificationClient.deleteTable();
        } catch (e) {
            // Table may not exist, that's fine
        }
    });

    beforeEach(() => {
        service = new StorageTableService(DefaultContext, tableName);
    });

    afterEach(async () => {
        try {
            await service.clear();
        } catch (e) {
            console.error("Error during cleanup:", e);
            throw e;
        }
    });

    describe("init", () => {
        it("initializes with development storage", async () => {
            const client = await service.init();
            expect(client).not.toBe(null);
        });

        it("returns existing tableClient when already initialized", async () => {
            const client1 = await service.init();
            const client2 = await service.init();
            expect(client1).toBe(client2);
        });
    });

    describe("getUniqueRowKeys", () => {
        it("returns empty array when no entities found", async () => {
            await service.init();
            const result = await service.getUniqueRowKeys();
            expect(result).toBeInstanceOf(Array);
            expect(result).toHaveLength(0);
        });

        it("returns unique row keys from entities", async () => {
            await service.init();
            const client = await service.init();

            // Add test entities
            await client.createEntity({
                partitionKey: "partition1",
                rowKey: "key1",
                data: "value1"
            });

            await client.createEntity({
                partitionKey: "partition1",
                rowKey: "key2",
                data: "value2"
            });

            await client.createEntity({
                partitionKey: "partition2",
                rowKey: "key1",
                data: "value3"
            });

            const result = await service.getUniqueRowKeys();
            expect(result.sort()).toEqual(["key1", "key2"]);
        });
    });

    describe("listEntitiesByPartitionKey", () => {
        it("returns entities with matching partition key", async () => {
            await service.init();
            const client = await service.init();

            await client.createEntity({
                partitionKey: "partition1",
                rowKey: "key1",
                data: "value1"
            });

            await client.createEntity({
                partitionKey: "partition2",
                rowKey: "key2",
                data: "value2"
            });

            const entitiesIterator = await service.listEntitiesByPartitionKey("partition1");

            const entities = [];
            for await (const entity of entitiesIterator) {
                entities.push(entity);
            }

            expect(entities.length).toEqual(1);
            expect(entities[0].rowKey).toEqual("key1");
            expect(entities[0].partitionKey).toEqual("partition1");
        });
    });

    describe("submitTransaction", () => {
        it("executes batch operations", async () => {
            await service.init();

            const batch = [[
                "upsert",
                {
                    partitionKey: "batch1",
                    rowKey: "row1",
                    data: "value1"
                }
            ], [
                "upsert",
                {
                    partitionKey: "batch1",
                    rowKey: "row2",
                    data: "value2"
                }
            ]];

            await service.submitTransaction(batch);

            const entitiesIterator = await service.listEntitiesByPartitionKey("batch1");
            const entities = [];
            for await (const entity of entitiesIterator) {
                entities.push(entity);
            }

            expect(entities.length).toEqual(2);
        });
    });

    describe("listEntitiesByPartitionKeyPaged", () => {
        it("returns paged results with correct parameters", async () => {
            await service.init();
            const client = await service.init();

            for (let i = 0; i < 12; i++) {
                await client.createEntity({
                    partitionKey: "paged",
                    rowKey: `key${i}`,
                    data: `value${i}`
                });
            }

            const pagedResults = service.listEntitiesByPartitionKeyPaged("paged", 10);

            const firstPageIterator = await pagedResults.next();
            const firstPage = firstPageIterator.value;

            expect(firstPage).toHaveLength(10);
            expect(firstPage[0].partitionKey).toEqual("paged");
        });
    });

    describe("deleteEntity", () => {
        it("removes entity with specified keys", async () => {
            const client = await service.init();

            await client.createEntity({
                partitionKey: "delete",
                rowKey: "row1",
                data: "value"
            });

            await client.createEntity({
                partitionKey: "delete",
                rowKey: "row2",
                data: "value"
            });

            const entitiesBefore = await service.getUniqueRowKeys();
            expect(entitiesBefore).toHaveLength(2);

            await service.deleteEntity("delete", "row1");

            const entities = await service.getUniqueRowKeys();
            expect(entities).toHaveLength(1);
            expect(entities).toContain("row2");
        });
    });

    describe('createEntity', () => {
        it('creates an entity with the specified data', async () => {
            const client = await service.init();
            await service.createEntity({
                partitionKey: "create",
                rowKey: "row1",
                data: "value"
            });
            await service.createEntity({
                partitionKey: "create",
                rowKey: "row1",
                data: "value2"
            });
            const entities = await service.getUniqueRowKeys();
            expect(entities).toContain("row1");
            const entity = await client.getEntity("create", "row1") as any;
            expect(entity.data).toEqual("value2");
        });
    });

    describe("getEntity", () => {
        it("returns entity with specified keys", async () => {
            await service.init();
            await service.createEntity({
                partitionKey: "get",
                rowKey: "row1",
                data: "value"
            });
            const entity = await service.getEntity("get", "row1") as any;
            expect(entity).not.toBeNull();
            expect(entity.data).toEqual("value");
        });
    })
});