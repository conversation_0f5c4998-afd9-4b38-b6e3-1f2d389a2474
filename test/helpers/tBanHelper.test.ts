import TBanHelper from "@/helpers/tBanHelper";
import { describeWithDB } from "../testing/TestHelper";
import { Sequelize } from "sequelize";
import { usePsql } from "@/database/psql";

describeWithDB("@/helpers/tBanHelper", () => {
    let sequelize: Sequelize = null;
    const testData = {
        customerInfo: {
            nnumber: "N141096449",
            nwOptionIdT: "T6000155",
            not_exist_nnumber: "N99999999",
        },
        lineOption: {
            lineOptionId: "AO122",
            lineOptionIdT: "T6000122",
            not_exist_lineOptionId: "A99999",
        },
        deviceType: {
            deviceTypeId: "A99",
            deviceTypeIdT: "T2000A99",
            not_exist_deviceTypeId: "A999999",
        },
    };

    beforeAll(async () => {
        sequelize = await usePsql();
    });

    afterAll(async () => {
        await sequelize.close();
    });

    describe("getNWOptionTBan", () => {
        test("should return nwOptionIdT if found", async () => {
            const result = await TBanHelper.getNWOptionTBan(
                testData.customerInfo.nnumber,
            );
            expect(result).toBe(testData.customerInfo.nwOptionIdT);
        });

        test("should return null if not found", async () => {
            const result = await TBanHelper.getNWOptionTBan(
                testData.customerInfo.not_exist_nnumber,
            );
            expect(result).toBeNull();
        });
    });

    describe("getLineOptionTBan", () => {
        test("should return lineOptionIdT if found", async () => {
            const result = await TBanHelper.getLineOptionTBan(
                testData.lineOption.lineOptionId,
            );
            expect(result).toBe(testData.lineOption.lineOptionIdT);
        });

        test("should return null if not found", async () => {
            const result = await TBanHelper.getLineOptionTBan(
                testData.lineOption.not_exist_lineOptionId,
            );
            expect(result).toBeNull();
        });
    });
    describe("getDeviceTypeTBan", () => {
        test("should return deviceTypeIdT if found", async () => {
            const result = await TBanHelper.getDeviceTypeTBan(
                testData.deviceType.deviceTypeId,
            );
            expect(result).toBe(testData.deviceType.deviceTypeIdT);
        });

        test("should return null if not found", async () => {
            const result = await TBanHelper.getDeviceTypeTBan(
                testData.deviceType.not_exist_deviceTypeId,
            );
            expect(result).toBeNull();
        });
    });
});
