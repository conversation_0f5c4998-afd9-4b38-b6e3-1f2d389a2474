import AppConfig from "@/appconfig";
import {
    getConfig,
    getConfigAsNumber,
    tryGetConfig,
} from "@/helpers/configHelper";
import CoreMvnoConfigKey from "@/types/coreMvnoConfig";
import DefaultContext from "../testing/DefaultContext";

describe("helpers/configHelper", () => {
    const validConfigKey: { [key: string]: CoreMvnoConfigKey } = {
        number: "ServerConnectionLimit",
        string: "TpcDestIpAddress",
    };
    const invalidKey = "invalidKey" as CoreMvnoConfigKey;

    beforeAll(async () => {
        await AppConfig.loadCoreMvnoConfig(DefaultContext);
    });

    describe("tryGetConfig", () => {
        test("should return value from config", () => {
            const numberValue = parseInt(
                tryGetConfig(validConfigKey.number),
                10,
            );
            const stringValue = tryGetConfig(validConfigKey.string);
            expect(numberValue).not.toBeNull();
            expect(typeof numberValue).toBe("number");
            expect(stringValue).not.toBeNull();
            expect(typeof stringValue).toBe("string");
            expect(stringValue.length).toBeGreaterThan(0);
        });

        test("should return null if key is not found", () => {
            const value = tryGetConfig(invalidKey);
            expect(value).toBeNull();
        });

        test("should return custom default value if key is not found (%s)", () => {
            let value: any;
            let defaultValue = "somevalue";

            value = tryGetConfig(invalidKey, defaultValue as string);

            expect(value).toBe(defaultValue);
        });
    });

    describe("getConfig", () => {
        test("should return value from config", () => {
            const value = getConfig(validConfigKey.string);
            expect(typeof value).toBe("string");
            expect(value.length).toBeGreaterThan(0);
        });

        test("should throw error if key is not found", () => {
            expect(() => getConfig(invalidKey)).toThrow();
        });
    });

    describe("getConfigAsNumber", () => {
        test("should return value from config as number", () => {
            const value = getConfig(validConfigKey.number);
            const numberValue = getConfigAsNumber(validConfigKey.number);
            expect(typeof numberValue).toBe("number");
            expect(numberValue).toBe(parseInt(value, 10));
        });

        test("should throw error if key is not found", () => {
            expect(() => getConfigAsNumber(invalidKey)).toThrow();
        });
    });
});
