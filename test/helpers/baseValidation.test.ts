import { CORE_ERROR_MESSAGES } from "@/constants/coreErrorMessages";
import SOManagerInputDto from "@/core/dto/SOManagerInputDto";
import validateBaseRequest from "@/helpers/baseValidation";
import { describe, expect, test } from "@jest/globals";

describe("helpers/baseValidation", () => {
    describe("validateBaseRequest OK cases", () => {
        test("should return OK if jsonBody is valid", () => {
            const jsonBody = {
                targetSoId: "PF0123456789abcdef123456789"
            }
            const result = validateBaseRequest(
                jsonBody as unknown as SOManagerInputDto,
            );
            expect(result.checkResult).toBe(true);
            expect(result.ngReason).toBe("");
        })
    })
    describe("validateBaseRequest NG cases", () => {
        test("should return error if jsonB<PERSON> is not an object", () => {
            const jsonBody = "not an object";
            const result = validateBaseRequest(
                jsonBody as unknown as SOManagerInputDto,
            );
            expect(result.checkResult).toBe(false);
            expect(result.ngReason).toBe(
                CORE_ERROR_MESSAGES.EMPTY_REQUEST_BODY,
            );
        });

        test("should return error if jsonBody is empty", () => {
            const jsonBody = undefined;
            const result = validateBaseRequest(
                jsonBody as unknown as SOManagerInputDto,
            );
            expect(result.checkResult).toBe(false);
            expect(result.ngReason).toBe(CORE_ERROR_MESSAGES.EMPTY_REQUEST_BODY);
        });

        test.skip("should return error if targetSoId is not defined", () => {
            const jsonBody = {
                targetSoId: undefined,
            };
            const result = validateBaseRequest(
                jsonBody as unknown as SOManagerInputDto,
            );
            expect(result.checkResult).toBe(false);
            expect(result.ngReason).toBe(CORE_ERROR_MESSAGES.TARGET_SOID_EMPTY);
        });

        test("should return error if targetSoId is not a string", () => {
            const jsonBody = {
                targetSoId: 123,
            };
            const result = validateBaseRequest(
                jsonBody as unknown as SOManagerInputDto,
            );
            expect(result.checkResult).toBe(false);
            expect(result.ngReason).toBe(CORE_ERROR_MESSAGES.TARGET_SOID_INVALID);
        });
    });
});
