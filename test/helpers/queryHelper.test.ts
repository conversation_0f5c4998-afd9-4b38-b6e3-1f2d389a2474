jest.setTimeout(30000);
import { describe, expect, test } from "@jest/globals";
import {
    Sequelize,
    ConnectionTimedOutError,
    ConnectionError,
    UniqueConstraintError,
} from "sequelize";

import { describeWithDB } from "../testing/TestHelper";
import { usePsql } from "@/database/psql";

import { isSQLException, retryQuery } from "@/helpers/queryHelper";
import DefaultContext from "../testing/DefaultContext";
import { TenantsEntity } from "@/core/entity/TenantsEntity";
import { LinesEntity } from "@/core/entity/LinesEntity";

interface Spies {
    warn: jest.SpyInstance;
    error: jest.SpyInstance;
    info: jest.SpyInstance;
}

describeWithDB("@/helpers/queryHelper", () => {
    describe("retryQuery", () => {
        let sequelize: Sequelize = null;

        const testdata = {
            tenantId: "TSA000",
            lineId: "08024043101",
            nNumber: "N141003811",
            planId: "40001",
            constants: {
                lineStatusOK: "01",
                lineStatusNG: "03",
            },
        };
        const config = {
            ApDbRetryMaxCnt: 3,
            ApDbRetryInterval: 0.01, // 10ms for testing
        };

        // type of Jest spies
        const spies: Spies = {
            warn: null,
            error: null,
            info: null,
        };

        async function clearTestData() {
            await Promise.allSettled([
                LinesEntity.destroy({
                    where: { lineId: testdata.lineId },
                }),
            ]);
        }

        beforeAll(async () => {
            sequelize = await usePsql();
            // delete test data
            await clearTestData();
        });

        afterAll(async () => {
            await sequelize.close();
        });

        beforeEach(() => {
            spies.warn = jest
                .spyOn(DefaultContext, "warn")
                // .mockImplementation(console.warn);
                .mockImplementation(() => {});
            spies.error = jest
                .spyOn(DefaultContext, "error")
                // .mockImplementation(console.error);
                .mockImplementation(() => {});
            spies.info = jest
                .spyOn(DefaultContext, "info")
                // .mockImplementation(console.info);
                .mockImplementation(() => {});
        });

        afterEach(() => {
            jest.restoreAllMocks();
            jest.clearAllMocks();
        });

        describe("resolved cases", () => {
            it("should return query result correctly", async () => {
                const tenant = await retryQuery(
                    DefaultContext,
                    "shouldReturnQueryResultCorrectly",
                    async () => {
                        return await TenantsEntity.findOne({
                            where: { tenantId: "TSA000" },
                        });
                    },
                    config.ApDbRetryMaxCnt,
                    config.ApDbRetryInterval,
                );
                expect(tenant).not.toBeNull();
                expect(tenant.tenantId).toBe("TSA000");
                expect(spies.warn).not.toHaveBeenCalled();
                expect(spies.error).not.toHaveBeenCalled();
            });

            it("should return empty query result correctly", async () => {
                const tenant = await retryQuery(
                    DefaultContext,
                    "shouldReturnEmptyQueryResultCorrectly",
                    async () => {
                        return await TenantsEntity.findOne({
                            where: { tenantId: "TSA001" },
                        });
                    },

                    config.ApDbRetryMaxCnt,
                    config.ApDbRetryInterval,
                );
                expect(tenant).toBeNull();
                expect(spies.warn).not.toHaveBeenCalled();
                expect(spies.error).not.toHaveBeenCalled();
            });

            it("should insert data correctly", async () => {
                try {
                    const result = await retryQuery(
                        DefaultContext,
                        "shouldInsertDataCorrectly",
                        async () => {
                            return await LinesEntity.create({
                                lineId: testdata.lineId,
                                lineStatus: testdata.constants.lineStatusOK,
                                nnumber: testdata.nNumber,
                                simFlag: false,
                                planId: testdata.planId,
                            });
                        },
                        config.ApDbRetryMaxCnt,
                        config.ApDbRetryInterval,
                    );
                    expect(result).not.toBeNull();
                    expect(result.lineId).toBe(testdata.lineId);
                    expect(spies.warn).not.toHaveBeenCalled();
                    expect(spies.error).not.toHaveBeenCalled();
                } finally {
                    await LinesEntity.destroy({
                        where: { lineId: testdata.lineId },
                    });
                }
            });

            it("should return result after 1st call resolved (no retry)", async () => {
                jest.spyOn(TenantsEntity, "findOne");

                const result = await retryQuery(
                    DefaultContext,
                    "shouldWaitForRetryInterval-1",
                    async () => {
                        return await TenantsEntity.findOne({
                            where: { tenantId: "TSA000" },
                        });
                    },
                    config.ApDbRetryMaxCnt,
                    config.ApDbRetryInterval,
                );
                expect(result).not.toBeNull();
                expect(result.tenantId).toBe("TSA000");
                expect(spies.warn).not.toBeCalled();
                expect(spies.error).not.toBeCalled();
            });

            it("should return result after 2nd call resolved (retry once)", async () => {
                jest.spyOn(TenantsEntity, "findOne").mockImplementationOnce(
                    () => {
                        throw new ConnectionTimedOutError(
                            new Error("Connection timed out"),
                        );
                    },
                );

                const result = await retryQuery(
                    DefaultContext,
                    "shouldWaitForRetryInterval-2",
                    async () => {
                        return await TenantsEntity.findOne({
                            where: { tenantId: "TSA000" },
                        });
                    },
                    config.ApDbRetryMaxCnt,
                    config.ApDbRetryInterval,
                );
                expect(result).not.toBeNull();
                expect(result.tenantId).toBe("TSA000");
                expect(spies.warn).toHaveBeenCalledTimes(1);
                expect(spies.error).not.toBeCalled();
            });

            it("should return result after 3rd call resolved (retry twice)", async () => {
                jest.spyOn(TenantsEntity, "findOne")
                    .mockImplementationOnce(() => {
                        // 1st call failed
                        throw new ConnectionTimedOutError(
                            new Error("Connection timed out"),
                        );
                    })
                    .mockImplementationOnce(() => {
                        // 2nd call failed
                        throw new ConnectionTimedOutError(
                            new Error("Connection timed out"),
                        );
                    });

                const result = await retryQuery(
                    DefaultContext,
                    "shouldWaitForRetryInterval-3",
                    async () => {
                        return await TenantsEntity.findOne({
                            where: { tenantId: "TSA000" },
                        });
                    },
                    config.ApDbRetryMaxCnt,
                    config.ApDbRetryInterval,
                );
                expect(result).not.toBeNull();
                expect(result.tenantId).toBe("TSA000");
                expect(spies.warn).toHaveBeenCalledTimes(2);
                expect(spies.error).not.toBeCalled();
            });

            it("should return result after 4th call resolved (retry 3 times)", async () => {
                jest.spyOn(TenantsEntity, "findOne")
                    .mockImplementationOnce(() => {
                        // 1st call failed
                        throw new ConnectionTimedOutError(
                            new Error("Connection timed out"),
                        );
                    })
                    .mockImplementationOnce(() => {
                        // 2nd call failed
                        throw new ConnectionTimedOutError(
                            new Error("Connection timed out"),
                        );
                    })
                    .mockImplementationOnce(() => {
                        // 3rd call failed
                        throw new ConnectionTimedOutError(
                            new Error("Connection timed out"),
                        );
                    });

                const result = await retryQuery(
                    DefaultContext,
                    "shouldWaitForRetryInterval-4",
                    async () => {
                        return await TenantsEntity.findOne({
                            where: { tenantId: "TSA000" },
                        });
                    },
                    config.ApDbRetryMaxCnt,
                    config.ApDbRetryInterval,
                );
                expect(result).not.toBeNull();
                expect(result.tenantId).toBe("TSA000");
                expect(spies.warn).toHaveBeenCalledTimes(3);
                expect(spies.error).not.toBeCalled();
            });

            it("should continue retry if sleep failed", async () => {
                jest.spyOn(TenantsEntity, "findOne")
                    .mockImplementationOnce(() => {
                        // 1st call failed
                        throw new ConnectionTimedOutError(
                            new Error("Connection timed out"),
                        );
                    })
                    .mockImplementationOnce(() => {
                        // 2nd call failed
                        throw new ConnectionTimedOutError(
                            new Error("Connection timed out"),
                        );
                    });

                jest.spyOn(global, "setTimeout").mockImplementationOnce(() => {
                    throw new Error("setTimeout failed");
                });

                const result = await retryQuery(
                    DefaultContext,
                    "shouldContinueRetryIfSleepFailed",
                    async () => {
                        return await TenantsEntity.findOne({
                            where: { tenantId: "TSA000" },
                        });
                    },
                    config.ApDbRetryMaxCnt,
                    config.ApDbRetryInterval,
                );
                expect(result).not.toBeNull();
                expect(result.tenantId).toBe("TSA000");

                expect(spies.warn).toHaveBeenCalledTimes(2);
                expect(spies.error).toHaveBeenCalledTimes(1);
            });
        });

        describe("rejected cases", () => {
            it("should reject if query failed", async () => {
                try {
                    // insert line first
                    await LinesEntity.create({
                        lineId: testdata.lineId,
                        lineStatus: testdata.constants.lineStatusOK,
                        nnumber: testdata.nNumber,
                        simFlag: false,
                        planId: testdata.planId,
                    });

                    // unique constraint violation
                    await expect(async () => {
                        await retryQuery(
                            DefaultContext,
                            "shouldRejectIfQueryFailed",
                            async () => {
                                return await LinesEntity.create({
                                    lineId: testdata.lineId,
                                    lineStatus: testdata.constants.lineStatusOK,
                                    nnumber: testdata.nNumber,
                                    simFlag: false,
                                    planId: testdata.planId,
                                });
                            },
                            config.ApDbRetryMaxCnt,
                            config.ApDbRetryInterval,
                        );
                    }).rejects.toThrow();
                    expect(spies.warn).toHaveBeenCalledTimes(3);
                    expect(spies.error).toHaveBeenCalledTimes(1);
                } finally {
                    await LinesEntity.destroy({
                        where: { lineId: testdata.lineId },
                    });
                }
            });

            it("should reject after 3 retries (4 calls) failed", async () => {
                jest.spyOn(TenantsEntity, "findOne")
                    .mockImplementationOnce(() => {
                        // 1st call failed
                        throw new ConnectionTimedOutError(
                            new Error("Connection timed out"),
                        );
                    })
                    .mockImplementationOnce(() => {
                        // 2nd call failed
                        throw new ConnectionTimedOutError(
                            new Error("Connection timed out"),
                        );
                    })
                    .mockImplementationOnce(() => {
                        // 3rd call failed
                        throw new ConnectionTimedOutError(
                            new Error("Connection timed out"),
                        );
                    })
                    .mockImplementationOnce(() => {
                        // 4th call failed
                        throw new ConnectionTimedOutError(
                            new Error("Connection timed out"),
                        );
                    });

                await expect(async () => {
                    await retryQuery(
                        DefaultContext,
                        "shouldWaitForRetryInterval-4-failed",
                        async () => {
                            return await TenantsEntity.findOne({
                                where: { tenantId: "TSA000" },
                            });
                        },
                        config.ApDbRetryMaxCnt,
                        config.ApDbRetryInterval,
                    );
                }).rejects.toThrow();

                expect(spies.warn).toHaveBeenCalledTimes(3);
                expect(spies.error).toHaveBeenCalledTimes(1);
            });

            it("should reject immediately if the error is not DB-related", async () => {
                jest.spyOn(TenantsEntity, "findOne").mockImplementation(() => {
                    throw new Error("Unknown error");
                });

                await expect(async () => {
                    await retryQuery(
                        DefaultContext,
                        "shouldRejectImmediately",
                        async () => {
                            return await TenantsEntity.findOne({
                                where: { tenantId: "TSA000" },
                            });
                        },
                        config.ApDbRetryMaxCnt,
                        config.ApDbRetryInterval,
                    );
                }).rejects.toThrow();

                expect(spies.warn).not.toHaveBeenCalled();
                expect(spies.error).toHaveBeenCalledTimes(1);
            });
        });
    });

    describe("isSQLException", () => {
        const trueCases = [
            new ConnectionTimedOutError(new Error("Connection timed out")),
            new ConnectionError(new Error("Connection error")),
            new UniqueConstraintError(new Error("Unique constraint violation")),
        ].map((err) => [err.name, err]);
        const falseCases = [
            new Error("Unknown error"),
            new TypeError("Type error"),
            new RangeError("Range error"),
        ].map((err) => [err.name, err]);

        it.each(trueCases)(
            "should return true if error is Sequelize error: %s",
            (type, err) => {
                const result = isSQLException(err);
                expect(result).toBe(true);
            },
        );

        it.each(falseCases)(
            "should return false if error is not Sequelize error: %s",
            (type, err) => {
                const result = isSQLException(err);
                expect(result).toBe(false);
            },
        );
    });
});
