import StringHelper from "@/helpers/stringHelper";

describe("@/helpers/stringHelper", () => {
    describe("normalizePhoneNum", () => {
        test("should remove hyphens and spaces", async () => {
            const phoneNum = " ************  ";
            expect(StringHelper.normalizePhoneNum(phoneNum)).toBe("1234567890");
        });

        test("should return input as is if not string", async () => {
            const phoneNum = 1234567890;
            expect(StringHelper.normalizePhoneNum(phoneNum as any)).toBe(
                1234567890,
            );
        });
    });
});
