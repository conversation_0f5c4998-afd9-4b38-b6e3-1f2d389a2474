import { describe, expect, test } from "@jest/globals";
import {
    fmtDateFor<PERSON>aishi,
    getArrayValue,
    getBooleanValue,
    getNumberValue,
    getScheduleTimeForSwimmyOrder,
    getSystemDatetime,
    isNone,
    removeEmptyProperties,
    zipArrays,
} from "@/utils";

describe("utils", () => {
    describe("getSystemDatetime", () => {
        test("should return default formatted date string", () => {
            const result = getSystemDatetime();
            expect(result).toMatch(/^\d{14}$/);
        });

        test("should return custom formatted date string", () => {
            const result = getSystemDatetime("yyyy/MM/dd HH:mm:ss");
            expect(result).toMatch(/^\d{4}\/\d{2}\/\d{2} \d{2}:\d{2}:\d{2}$/);
        });
    });

    describe("isNone", () => {
        const emptyCases = [null, undefined, "", NaN, [], {}];

        emptyCases.forEach((val) => {
            test(`should return true for ${JSON.stringify(val)}`, () => {
                expect(isNone(val)).toBe(true);
            });
        });

        const nonEmptyCases = ["asdf", 0, 1, true, false, [1, 2], { a: 1 }];
        nonEmptyCases.forEach((val) => {
            test(`should return false for ${JSON.stringify(val)}`, () => {
                expect(isNone(val)).toBe(false);
            });
        });
    });

    describe("removeEmptyProperties", () => {
        const obj = {
            a: 1,
            b: undefined,
            c: {
                d: 2,
                e: undefined,
                f: null,
                g: {
                    arr: [1, undefined, 3, null, "asdf"],
                },
                z: {}, // empty object
                arrObj: [
                    {
                        one: 1,
                        two: undefined,
                        three: null,
                        four: "4",
                    },
                    {
                        five: undefined,
                    },
                    {
                        emptyArr: [],
                        notEmptyArr: [1, 2, 3],
                    },
                ],
            },
            h: null,
        };

        test("should remove undefined properties", () => {
            const result = removeEmptyProperties(obj, false, false);
            expect(result).toStrictEqual({
                a: 1,
                c: {
                    d: 2,
                    f: null,
                    g: {
                        arr: [1, undefined, 3, null, "asdf"],
                    },
                    z: {}, // kept empty object
                    arrObj: [
                        {
                            one: 1,
                            three: null,
                            four: "4",
                        },
                        {}, // kept empty object
                        {
                            emptyArr: [],
                            notEmptyArr: [1, 2, 3],
                        },
                    ],
                },
                h: null,
            });
        });

        test("should remove undefined and null properties", () => {
            const result = removeEmptyProperties(obj, true, false);
            expect(result).toStrictEqual({
                a: 1,
                c: {
                    d: 2,
                    g: {
                        arr: [1, undefined, 3, null, "asdf"],
                    },
                    z: {}, // kept empty object
                    arrObj: [
                        {
                            one: 1,
                            four: "4",
                        },
                        {}, // kept empty object
                        {
                            emptyArr: [],
                            notEmptyArr: [1, 2, 3],
                        },
                    ],
                },
            });
        });

        test("should remove undefined and empty objects (array item)", () => {
            const result = removeEmptyProperties(obj, false, true);
            expect(result).toStrictEqual({
                a: 1,
                c: {
                    d: 2,
                    f: null,
                    g: {
                        arr: [1, undefined, 3, null, "asdf"],
                    },
                    arrObj: [
                        {
                            one: 1,
                            three: null,
                            four: "4",
                        },
                        {
                            emptyArr: [],
                            notEmptyArr: [1, 2, 3],
                        },
                    ],
                },
                h: null,
            });
        });

        test("should remove undefined, null and empty objects (object item)", () => {
            const result = removeEmptyProperties(obj, true, true);
            expect(result).toStrictEqual({
                a: 1,
                c: {
                    d: 2,
                    g: {
                        arr: [1, undefined, 3, null, "asdf"],
                    },
                    arrObj: [
                        {
                            one: 1,
                            four: "4",
                        },
                        {
                            emptyArr: [],
                            notEmptyArr: [1, 2, 3],
                        },
                    ],
                },
            });
        });

        test("should return as it is if non-object is passed", () => {
            expect(removeEmptyProperties(1)).toStrictEqual(1);
            expect(removeEmptyProperties(false)).toStrictEqual(false);
            expect(removeEmptyProperties("string")).toStrictEqual("string");
        });

        test("should return empty object if undefined or null is passed", () => {
            expect(removeEmptyProperties(undefined)).toStrictEqual({});
            expect(removeEmptyProperties(null)).toStrictEqual({});
        });

        test.skip("time complexity test", () => {
            const deepcopy = (o: any) => JSON.parse(JSON.stringify(o));
            // actual object should be smaller than this
            const nestedObj = {
                o1: deepcopy(obj),
                o2: deepcopy(obj),
                o3: {
                    o4: deepcopy(obj),
                    o5: {
                        o6: deepcopy(obj),
                    },
                },
            };
            const start = new Date().getTime();
            for (let i = 0; i < 100000; i++) {
                removeEmptyProperties(nestedObj, true, true);
            }
            const end = new Date().getTime();
            console.log("Time taken:", end - start, "ms");
            console.log("Average time taken:", (end - start) / 100000, "ms");
            // should be less than 0.1ms average of each iteration
            expect((end - start) / 100000).toBeLessThan(0.1);
        });
    });

    describe("getBooleanValue", () => {
        test("should return true for 'true' string", () => {
            expect(getBooleanValue("true")).toBe(true);
        });

        test("should return true for boolean true", () => {
            expect(getBooleanValue(true)).toBe(true);
        });

        test("should return false for 'false' string", () => {
            expect(getBooleanValue("false")).toBe(false);
        });

        test("should return false for boolean false", () => {
            expect(getBooleanValue(false)).toBe(false);
        });

        test("should return false for other strings", () => {
            expect(getBooleanValue("anything else")).toBe(false);
        });

        test("should return false for null", () => {
            expect(getBooleanValue(null)).toBe(false);
        });
    });

    describe("getNumberValue", () => {
        test("should return number for numeric string", () => {
            expect(getNumberValue("123")).toBe(123);
        });

        test("should return number for numeric value", () => {
            expect(getNumberValue(456)).toBe(456);
        });

        test("should return NaN for non-numeric string", () => {
            expect(getNumberValue("not a number")).toBeNaN();
        });

        test("should return NaN for null", () => {
            expect(getNumberValue(null)).toBeNaN();
        });

        test("should return NaN for undefined", () => {
            expect(getNumberValue(undefined)).toBeNaN();
        });

        test("should return fallback value for non-numeric string", () => {
            expect(getNumberValue("not a number", 42)).toBe(42);
        });

        test("should return fallback value for null", () => {
            expect(getNumberValue(null, 42)).toBe(42);
        });

        test("should return fallback value for undefined", () => {
            expect(getNumberValue(undefined, 42)).toBe(42);
        });

        test("should return fallback value for empty string", () => {
            expect(getNumberValue("", 42)).toBe(42);
        });

        test("should return fallback value for NaN", () => {
            expect(getNumberValue(NaN, 42)).toBe(42);
        });
    });

    describe("getArrayValue", () => {
        test("should return array for array input", () => {
            expect(getArrayValue<number>([1, 2, 3])).toEqual([1, 2, 3]);
        });

        test("should return array of valid input [numbers]", () => {
            expect(getArrayValue<number>("[23,3,4]")).toEqual([23, 3, 4]);
        });

        test("should return array of valid input [strings]", () => {
            expect(getArrayValue<string>('["a", "b", "cde"]')).toEqual([
                "a",
                "b",
                "cde",
            ]);
        });

        test("should return empty array for null input", () => {
            expect(getArrayValue<number>(null)).toEqual([]);
        });

        test("should return empty array for undefined input", () => {
            expect(getArrayValue<number>(undefined)).toEqual([]);
        });

        test("should return empty array for non-array input", () => {
            expect(getArrayValue<number>("not an array")).toEqual([]);
        });

        test("should return empty array if string parse fails", () => {
            expect(getArrayValue<number>("[23,3,,]")).toEqual([]);
        });

        test("should return fallback array for non-array input", () => {
            expect(getArrayValue<number>("not an array", [42, 43])).toEqual([
                42, 43,
            ]);
        });
    });

    describe("fmtDateForHaishi", () => {
        test("should return null if date is null", () => {
            expect(fmtDateForHaishi(null)).toBeNull();
        });

        test("should return null if date is undefined", () => {
            expect(fmtDateForHaishi(undefined)).toBeNull();
        });

        test("should return in YYYYMMDD format", () => {
            expect(fmtDateForHaishi("2024/07/11")).toBe("20240711");
            expect(fmtDateForHaishi("2024/07/11 02:30")).toBe("20240711");
        });

        test("should return as it is if already in YYYYMMDD format", () => {
            expect(fmtDateForHaishi("20240711")).toBe("20240711");
        });
    });

    describe("zipArrays", () => {
        test("should zip two arrays of same length", () => {
            const arr1 = [1, 2, 3];
            const arr2 = ["a", "b", "c"];
            const result = zipArrays(arr1, arr2);
            expect(result).toEqual([
                [1, "a"],
                [2, "b"],
                [3, "c"],
            ]);
        });

        test("should zip two arrays of different lengths", () => {
            const arr1 = [1, 2];
            const arr2 = ["a", "b", "c"];
            const result = zipArrays(arr1, arr2);
            expect(result).toEqual([
                [1, "a"],
                [2, "b"],
            ]);
        });

        test("should return array with length of 1st array", () => {
            const arr1: number[] = [];
            const arr2 = ["a", "b", "c"];
            const result1 = zipArrays(arr1, arr2);
            expect(result1).toEqual([]);

            const arr3 = [1, 2, 3];
            const arr4: string[] = [];
            const result2 = zipArrays(arr3, arr4);
            expect(result2).toEqual([
                [1, undefined],
                [2, undefined],
                [3, undefined],
            ]);

            const result3 = zipArrays([], []);
            expect(result3).toEqual([]);
        });
    });

    describe("getScheduleTimeForSwimmyOrder", () => {
        test("should return next schedule time [hour+1]", () => {
            const now = new Date();
            const current = Math.floor(now.getTime() / 1000);
            const scheduledMinute = 15;
            const skipHours: number[] = [
                (now.getHours() + 2) % 24,
                (now.getHours() + 3) % 24,
            ];
            const result = getScheduleTimeForSwimmyOrder(
                current,
                scheduledMinute,
                skipHours,
            );
            const resultDate = new Date(result * 1000);

            expect(resultDate.getMinutes()).toBe(scheduledMinute);
            expect(resultDate.getSeconds()).toBe(0);
            expect(resultDate.getMilliseconds()).toBe(0);
            const newHour = (now.getHours() + 1) % 24;
            expect(resultDate.getHours()).toBe(newHour);
        });

        test("should return next schedule time [day changed]", () => {
            const now = new Date("2024-07-11T23:45:00+09:00");
            const current = Math.floor(now.getTime() / 1000);
            const scheduledMinute = 15;
            const skipHours: number[] = [21, 22];
            const result = getScheduleTimeForSwimmyOrder(
                current,
                scheduledMinute,
                skipHours,
            );
            const resultDate = new Date(result * 1000);

            expect(resultDate.getMinutes()).toBe(scheduledMinute);
            expect(resultDate.getSeconds()).toBe(0);
            expect(resultDate.getMilliseconds()).toBe(0);
            expect(resultDate.getHours()).toBe(0);
            expect(resultDate.getDate()).toBe(12); // Should be next day
        });

        test("should skip hours correctly [+2]", () => {
            const now = new Date("2024-07-11T20:45:00+09:00");
            const current = Math.floor(now.getTime() / 1000);
            const scheduledMinute = 15;
            const skipHours: number[] = [21, 23];
            const result = getScheduleTimeForSwimmyOrder(
                current,
                scheduledMinute,
                skipHours,
            );
            const resultDate = new Date(result * 1000);

            expect(resultDate.getMinutes()).toBe(scheduledMinute);
            expect(resultDate.getSeconds()).toBe(0);
            expect(resultDate.getMilliseconds()).toBe(0);
            expect(resultDate.getHours()).toBe(22); // Should be in 22:xx
        });

        test("should skip hours correctly [+2]", () => {
            const now = new Date("2024-07-11T20:45:00+09:00");
            const current = Math.floor(now.getTime() / 1000);
            const scheduledMinute = 30;
            const skipHours: number[] = [21, 22];
            const result = getScheduleTimeForSwimmyOrder(
                current,
                scheduledMinute,
                skipHours,
            );
            const resultDate = new Date(result * 1000);

            expect(resultDate.getMinutes()).toBe(scheduledMinute);
            expect(resultDate.getSeconds()).toBe(0);
            expect(resultDate.getMilliseconds()).toBe(0);
            expect(resultDate.getHours()).toBe(23); // Should be in 23:xx
        });

        test("should skip hours correctly [next day/month]", () => {
            const now = new Date("2024-07-31T20:45:00+09:00");
            // check month
            expect(now.getMonth()).toBe(6); // July (0-indexed)
            const current = Math.floor(now.getTime() / 1000);
            const scheduledMinute = 15;
            const skipHours: number[] = [22, 16, 18, 21, 23, 0]; // unordered case
            const result = getScheduleTimeForSwimmyOrder(
                current,
                scheduledMinute,
                skipHours,
            );
            const resultDate = new Date(result * 1000);

            expect(resultDate.getMinutes()).toBe(scheduledMinute);
            expect(resultDate.getSeconds()).toBe(0);
            expect(resultDate.getMilliseconds()).toBe(0);
            expect(resultDate.getHours()).toBe(1); // Should be in 01:xx next day
            // date changed
            expect(resultDate.getDate()).toBe(1); // Should be next month
            expect(resultDate.getMonth()).toBe(7); // Should be August (0-indexed)
        });
    });
});
