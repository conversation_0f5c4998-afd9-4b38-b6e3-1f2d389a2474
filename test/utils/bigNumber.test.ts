import BigNumber from "bignumber.js";

describe("BigNumber.js", () => {
    describe("comparison", () => {
        const value = new BigNumber(512);
        const lowerLimit = new BigNumber(0);
        const upperLimit = new BigNumber(104857600);

        test("should handle comparison correctly with BigNumber methods", () => {
            expect(value.isGreaterThanOrEqualTo(lowerLimit)).toEqual(true);
            expect(value.isLessThanOrEqualTo(upperLimit)).toEqual(true);
            expect(value.isGreaterThan(lowerLimit)).toEqual(true);
            expect(value.isLessThan(upperLimit)).toEqual(true);
            expect(value.isEqualTo(lowerLimit)).toEqual(false);
            expect(value.isEqualTo(upperLimit)).toEqual(false);
        });

        test.failing("should *failed* handle comparison correctly with js operators", () => {
            expect(value >= lowerLimit).toEqual(true);
            expect(value <= upperLimit).toEqual(true);
            expect(value > lowerLimit).toEqual(true);
            expect(value < upperLimit).toEqual(true);
            expect(value === lowerLimit).toEqual(false);
            expect(value === upperLimit).toEqual(false);
        });
    });
});
