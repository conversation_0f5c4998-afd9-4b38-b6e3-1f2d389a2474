import { HttpRequest } from "@azure/functions";

import DefaultRequest from "../testing/DefaultRequest";
import DefaultContext from "../testing/DefaultContext";

import MsgKeysConstants from "@/core/constant/MsgKeysConstants";
import CustomLogger from "@/utils/logger";

describe("src/utils/logger", () => {
    let request: HttpRequest;

    const spies = {
        debug: jest.spyOn(DefaultContext, "debug"),
        warn: jest.spyOn(DefaultContext, "warn"),
        error: jest.spyOn(DefaultContext, "error"),
    };

    beforeEach(() => {
        spies.debug = jest
            .spyOn(DefaultContext, "debug")
            .mockImplementation(() => {});
        spies.warn = jest
            .spyOn(DefaultContext, "warn")
            .mockImplementation(() => {});
        spies.error = jest
            .spyOn(DefaultContext, "error")
            .mockImplementation(() => {});
        request = {
            ...DefaultRequest,
            headers: {
                get: jest.fn().mockReturnValue("localhost"),
            },
        } as unknown as HttpRequest;
    });

    afterEach(() => {
        jest.restoreAllMocks();
        jest.clearAllMocks();
    });

    describe("debug log", () => {
        test("should log debug message", () => {
            const logger = new CustomLogger(request, DefaultContext);
            const message =
                "localhost DEBUG Tenant123 123456 APCGPD0001 処理開始 [t1:t2] hello";
            logger.debug(
                "Tenant123",
                "123456",
                MsgKeysConstants.APCGPD0001,
                "t1",
                "t2",
                "hello",
            );
            expect(spies.debug).toHaveBeenCalled();
            expect(spies.debug).toHaveBeenCalledWith(message);
        });
    });

    describe("error log", () => {
        test("should log error message", () => {
            const logger = new CustomLogger(request, DefaultContext);
            const message =
                "localhost ERROR Tenant123 123456 MPSCOD0002 処理終了 [t1:t2]";
            logger.error(
                "Tenant123",
                "123456",
                MsgKeysConstants.MPSCOD0002,
                "t1",
                "t2",
            );
            expect(spies.error).toHaveBeenCalled();
            expect(spies.error).toHaveBeenCalledWith(message);
        });

        test("should log error object", () => {
            const logger = new CustomLogger(request, DefaultContext);
            const error = new Error("test error");
            const message =
                "localhost ERROR Tenant123 123456 MPSCOD0002 処理終了 [t1:t2]";
            logger.error(
                error,
                "Tenant123",
                "123456",
                MsgKeysConstants.MPSCOD0002,
                "t1",
                "t2",
            );
            expect(spies.error).toHaveBeenCalled();
            expect(spies.error).toHaveBeenCalledWith(message, error);
        });
    });

    describe("warn log", () => {
        test("should log warn message", () => {
            const logger = new CustomLogger(request, DefaultContext);
            const message =
                "localhost WARN Tenant123 123456 APCGPD0002 処理終了 [t1:t2]";
            logger.warn(
                "Tenant123",
                "123456",
                MsgKeysConstants.APCGPD0002,
                "t1",
                "t2",
            );
            expect(spies.warn).toHaveBeenCalled();
            expect(spies.warn).toHaveBeenCalledWith(message);
        });

        test("should log warn message with error object", () => {
            const logger = new CustomLogger(request, DefaultContext);
            const error = new Error("test error");
            const message =
                "localhost WARN Tenant123 123456 APCGPD0002 処理終了 [t1:t2]";
            logger.warn(
                error,
                "Tenant123",
                "123456",
                MsgKeysConstants.APCGPD0002,
                "t1",
                "t2",
            );
            expect(spies.warn).toHaveBeenCalled();
            expect(spies.warn).toHaveBeenCalledWith(message, error);
        });
    });
});
