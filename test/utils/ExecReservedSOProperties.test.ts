import { expect, jest, describe, test, beforeEach, afterEach } from "@jest/globals";
import config from "config";
import { ExecReservedSOProperties } from "@/utils/ExecReservedSOProperties";
import DefaultContext from "../testing/DefaultContext";

describe("ExecReservedSOProperties", () => {
    const context = DefaultContext;
    let instance: ExecReservedSOProperties;

    beforeEach(() => {
        // Mock config values
        jest.spyOn(config, 'has').mockImplementation(() => true);
        jest.spyOn(config, 'get').mockImplementation((key: string) => {
            const configValues = {
                "mvno.ApiProcessIDServer": "ABC",
                "mvno.TpcRegulationTime": "9:00-17:00",
                "mvno.TpcMaxConnections": 30,
                "mvno.ProducerPeriod": 1000,
                "mvno.WorkerPeriod": 2000,
                "mvno.ApDbUserId": "testuser",
                "mvno.ApDbUserPw": "testpass",
                "mvno.ApDbPort": "5432"
            };
            return configValues[key];
        });

        instance = ExecReservedSOProperties.getInstance(context);
    });

    afterEach(() => {
        jest.clearAllMocks();
        (ExecReservedSOProperties as any).instance = null;
    });

    describe("getInstance", () => {
        test("should return the same instance when called multiple times", () => {
            const instance1 = ExecReservedSOProperties.getInstance(context);
            const instance2 = ExecReservedSOProperties.getInstance(context);
            expect(instance1).toBe(instance2);
        });
    });

    describe("requireCheck", () => {
        test("should return empty string when all required fields are present", () => {
            const result = instance.requireCheck();
            expect(result).toBe("");
        });

        test("should return field name when a required field is missing", () => {
            jest.spyOn(config, 'has').mockImplementation((key: string) =>
                key !== "mvno.ApiProcessIDServer"
            );
            const result = instance.requireCheck();
            expect(result).toBe("mvno.ApiProcessIDServer");
        });

        test("should return field name when a required field is empty", () => {
            jest.spyOn(config, 'get').mockImplementation((key: string) =>
                key === "mvno.ApiProcessIDServer" ? "" : "value"
            );
            const result = instance.requireCheck();
            expect(result).toBe("mvno.ApiProcessIDServer");
        });
    });

    describe("TPC Regulation Time", () => {
        test("should correctly parse valid regulation time", () => {
            expect(instance.getTpcRegulationStart()).toBe("9:00:00");
            expect(instance.getTpcRegulationEnd()).toBe("17:00:00");
            expect(instance.chkTpcRegulationTime()).toBe(true);
        });

        test("should handle invalid regulation time format", () => {
            jest.spyOn(config, 'get').mockImplementation((key: string) =>
                key === "mvno.TpcRegulationTime" ? "invalid-time" : "value"
            );
            const newInstance = new ExecReservedSOProperties(context);
            expect(newInstance.getTpcRegulationStart()).toBeNull();
            expect(newInstance.getTpcRegulationEnd()).toBeNull();
            expect(newInstance.chkTpcRegulationTime()).toBe(false);
        });

        test("should handle 24:00 as special case", () => {
            jest.spyOn(config, 'get').mockImplementation((key: string) =>
                key === "mvno.TpcRegulationTime" ? "9:00-24:00" : "value"
            );
            const newInstance = new ExecReservedSOProperties(context);
            expect(newInstance.getTpcRegulationEnd()).toBe("24:00:00");
            expect(newInstance.chkTpcRegulationTime()).toBe(true);
        });

        test("should return false when end time is before start time", () => {
            jest.spyOn(config, 'get').mockImplementation((key: string) =>
                key === "mvno.TpcRegulationTime" ? "17:00-9:00" : "value"
            );
            const newInstance = new ExecReservedSOProperties(context);
            expect(newInstance.chkTpcRegulationTime()).toBe(false);
        });
    });

    describe("API Process ID Server", () => {
        test("should validate correct server ID length", () => {
            expect(instance.chkApiProcessIDServer()).toBe(true);
        });

        test("should reject invalid server ID length", () => {
            jest.spyOn(config, 'get').mockImplementation((key: string) =>
                key === "mvno.ApiProcessIDServer" ? "ABCD" : "value"
            );
            const newInstance = new ExecReservedSOProperties(context);
            expect(newInstance.chkApiProcessIDServer()).toBe(false);
        });
    });

    describe("TPC Max Connections", () => {
        test("should validate connections within range", () => {
            expect(instance.chkTpcMaxConnections()).toBe(true);
        });

        test("should reject connections outside range", () => {
            jest.spyOn(config, 'get').mockImplementation((key: string) =>
                key === "mvno.TpcMaxConnections" ? 61 : "value"
            );
            const newInstance = new ExecReservedSOProperties(context);
            expect(newInstance.chkTpcMaxConnections()).toBe(false);
        });
    });

    describe("Period Checks", () => {
        test("should validate producer period", () => {
            expect(instance.chkProducerPeriod()).toBe(true);
            expect(instance.getProducerPeriod()).toBe(1000);
        });

        test("should validate worker period", () => {
            expect(instance.chkWorkerPeriod()).toBe(true);
            expect(instance.getWorkerPeriod()).toBe(2000);
        });
    });

    describe("Database Configuration", () => {
        test("should return correct database configuration", () => {
            expect(instance.getApDbUserId()).toBe("testuser");
            expect(instance.getApDbUserPw()).toBe("testpass");
            expect(instance.getApDbPort()).toBe("5432");
        });
    });
});