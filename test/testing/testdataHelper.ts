import config from "config";
import { LinesEntity } from "@/core/entity/LinesEntity";
import { TenantNnumbersEntity } from "@/core/entity/TenantNnumbersEntity";
import { TenantsEntity } from "@/core/entity/TenantsEntity";
import ExtendedInvocationContext from "@/types/ExtendedInvocationContext";
import { HttpRequest } from "@azure/functions";
import { getConfig } from "@/helpers/configHelper";
import { setMinutes } from "date-fns";

/**
 * テナントのN番を紐付け、回線番号を登録する。
 * (`TenantManage .doCheck`のテストを通すため)
 * @param tenantId テナントID (DBに登録されているもの)
 * @param lineNo 回線番号
 */
export async function insertLineNumber(
    tenantId: string,
    lineNo: string,
    nNo: string = null,
) {
    await deleteLineNumber(lineNo);
    let nnumber = nNo;
    if (!nNo) {
        const nno = await TenantNnumbersEntity.findOne({
            where: {
                tenantId,
            },
        });
        if (nno === null) {
            if (tenantId === "CON000") {
                // NOTE hard-coded N番 for CON000
                nnumber = "N141096449";
            }
        } else {
            nnumber = nno.nnumber;
        }
    }
    if (!nnumber) {
        throw new Error(`N番が取得できませんでした。 ${tenantId}`);
    }
    return await LinesEntity.create({
        lineId: lineNo,
        lineStatus: "01",
        nnumber,
        simFlag: false,
    });
}

/**
 * 回線番号を削除する。
 * @param lineNo 回線番号
 */
export async function deleteLineNumber(lineNo: string) {
    return await LinesEntity.destroy({
        where: { lineId: lineNo },
    });
}

/**
 * Get config data for test
 *
 * * typing:
 *   ```ts
 *   ReturnType<typeof getConfigDataForTest>
 *   ```
 */
export function getConfigDataForTest() {
    return {
        // service configs:
        groupBufferByte: config.get<string>("mvno.GroupBufferByte"),
        reservationDateExecutionUnits: "15",
        reservationsLimitDays: "60",
        // SOAP related configs:
        timeout: config.get<number>("mvno.TpcTimeout"),
        soap: config.get<string>("mvno.SOAP"),
        soapApiUrl: getConfig("TpcDestServiceName").replace(
            "${IpAddr}",
            getConfig("TpcDestIpAddress"),
        ),
        timeoutOffset: 200,
    };
}

/**
 * get date for reserve_date with correct minutes
 * @param reservationDateExecutionUnits
 * @returns
 */
export function getDateNowForReservationTest(
    configData: ReturnType<typeof getConfigDataForTest>,
) {
    return setMinutes(new Date(), +configData?.reservationDateExecutionUnits);
}