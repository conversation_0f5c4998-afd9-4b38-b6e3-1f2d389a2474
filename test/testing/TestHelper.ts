import { describe, test } from "@jest/globals";
import * as queryHelper from "@/helpers/queryHelper";

// we need to check PostgreSQL availability once, so use a flag to store the result
let canUsePSQLFlag: boolean = null;

/**
 * check whether the test environment can use PostgreSQL
 */
export function canUsePSQL(): boolean {
    if (canUsePSQLFlag === null) {
        canUsePSQLFlag =
            process.env.SKIP_DB_TEST !== "true" &&
            (process.env.DATABASE_POSTGRESQL_CONNECTION_STRING !== undefined ||
                process.env.POSTGRE_CONNECTION_STRING !== undefined);
    }
    return canUsePSQLFlag;
}

/**
 * run test only if PostgreSQL is available
 * use as `testWithDB` instead of `test` for tests that require PostgreSQL
 */
export const testWithDB = canUsePSQL() ? test : test.skip;

/**
 * run test block only if PostgreSQL is available
 * use as `describeWithDB` instead of `describe` for test blocks that require PostgreSQL
 */
export const describeWithDB = canUsePSQL() ? describe : describe.skip;

/**
 * Generate a random process ID in the format of AP0900xxxxxxxxx
 */
export const generateProcessID = () => {
    return `AP0900${Math.floor(Math.random() * 1000000000)
        .toString()
        .padStart(9, "0")}`;
};

/**
 * Generate random SOID for test
 */
export const generateSOID = () => {
    return `PF0${Math.random().toString(16).substring(2).padStart(24, "0")}`;
};

/**
 * Make sure retryQuery won't take too long to wait
 *
 * If you have access to the instance which calls `retryQuery` directly within its methods,
 * you can instead use this way to inject custom interval.
 *
 * ```typescript
 * Object.defineProperty(
 *     instance,
 *     "apDbRetryInterval",
 *     { value: 0.01 }  // 10ms
 * );
 * ```
 *
 * @param interval interval in seconds (default 0.01)
 */
export const useFasterRetries = (interval = 0.01) => {
    const original = queryHelper.retryQuery;
    return jest
        .spyOn(queryHelper, "retryQuery")
        .mockImplementation(
            async (context, caller, fn, apDbRetryMaxCnt, apDbRetryInterval) => {
                return await original(
                    context,
                    caller,
                    fn,
                    apDbRetryMaxCnt,
                    interval,
                );
            },
        );
};
