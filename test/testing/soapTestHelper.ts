import { getConfig as getMvnoConfig } from "@/helpers/configHelper";
import nock from "nock";

type SOAPMockResponse = {
    /** result code (e.g. `OK`, `NG`) */
    result: string;
    /** content of `<ErrorInfomation>` tag */
    errorMessage?: string;
    /** List of [XPath, value] pairs, e.g. [["//a", "1"], ["//b", "2"]] */
    data?: [string, string][];
    /** return with server error */
    throwError?: boolean;
    /** HTTP status code (default = 200) */
    responseCode?: number;
};

export default class SOAPTestHelper {
    /** SOAPオペレーション名称 */
    private static readonly CONST_OPERATIONNAME = "serviceProfileRequestLite";

    /**
     * Disable all network connections (call it in `beforeEach`)
     */
    public static setup() {
        nock.disableNetConnect();
    }

    /**
     * Clean all nock mocks (call it in `afterEach`)
     */
    public static tearDown() {
        nock.abortPendingRequests();
        nock.cleanAll();
        nock.enableNetConnect();
    }

    public static getConfig() {
        const soapConfig = {
            TpcDestIpAddress: getMvnoConfig("TpcDestIpAddress"),
            TpcDestIpAddress2: getMvnoConfig("TpcDestIpAddress2"),
            TpcDestIpAddress3: getMvnoConfig("TpcDestIpAddress3"),
            /** normal endpoint */
            tpcDestServiceName: getMvnoConfig("TpcDestServiceName"),
            /** lite endpoint */
            tpcLiteRequestURI: getMvnoConfig("TpcLiteRequestURI"),
        };
        return soapConfig;
    }

    /**
     * Create endpoint URL for SOAP service
     * @param operationName
     * @param dest configuration key for destination IP address or actual IP address
     * @returns
     */
    public static getURI(operationName: string, dest: string) {
        const config = SOAPTestHelper.getConfig();
        const destination = config[dest] ?? dest;
        if (operationName === SOAPTestHelper.CONST_OPERATIONNAME) {
            return config.tpcLiteRequestURI.replace("${IpAddr}", destination);
        } else {
            return config.tpcDestServiceName.replace("${IpAddr}", destination);
        }
    }

    /**
     * Create a simple SOAP response
     * @param result result code (e.g. `OK`, `NG`)
     * @param errorMessage content of `<ErrorInfomation>` tag
     * @returns
     */
    public static simpleResponse(result: string, errorMessage: string = null) {
        let errorXML = "";
        if (errorMessage) {
            errorXML = `<ErrorInfomation>${errorMessage}</ErrorInfomation>`;
        }
        return `
            <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
                xmlns:ns1="http://ws.apache.org/axis2">
                <soapenv:Body>
                    <Result>${result}</Result>
                    ${errorXML}
                </soapenv:Body>
            </soapenv:Envelope>
            `.trim();
    }

    /**
     * Create a custom SOAP response
     * @param result `//Result`
     * @param errorInformation `//ErrorInfomation`
     * @param data List of [XPath, value] pairs, e.g. [["//a", "1"], ["//b", "2"]] (XPath must start with `//` and unique)
     */
    public static dataResponse(
        result: string,
        errorInformation: string,
        data: [string, string][],
    ) {
        // check if XPath is unique
        if (
            Array.isArray(data) &&
            data.length > 0 &&
            new Set(data.map((d) => d[0])).size !== data.length
        ) {
            throw new Error("XPath must be unique");
        }
        let errorXML = "";
        if (errorInformation) {
            errorXML = `<ErrorInfomation>${errorInformation}</ErrorInfomation>`;
        }
        const dataXML =
            Array.isArray(data) && data.length > 0
                ? createXMLFromXPaths("Data", data)
                : "";
        return `
            <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
                xmlns:ns1="http://ws.apache.org/axis2">
                <soapenv:Body>
                    <Result>${result}</Result>
                    ${errorXML}
                    ${dataXML}
                </soapenv:Body>
            </soapenv:Envelope>
            `.trim();
    }

    public static mockResponse(
        operationName: string,
        dest: string,
        response: SOAPMockResponse,
    ) {
        const uri = SOAPTestHelper.getURI(operationName, dest as any);
        const responseXML = SOAPTestHelper.dataResponse(
            response.result,
            response.errorMessage,
            response.data,
        );
        if (response.throwError) {
            // console.warn(">> set error response for", uri);
            nock(uri).persist().post("").replyWithError("Network error");
        } else {
            // console.warn(">> set mock response for", uri);
            const headers: nock.ReplyHeaders = {
                "Content-Type": "text/xml",
            };
            nock(uri)
                .persist()
                .post("")
                .reply(response.responseCode ?? 200, responseXML, headers);
        }
    }

    public static mockResponseOnce(
        operationName: string,
        dest: string,
        response: SOAPMockResponse,
    ) {
        const uri = SOAPTestHelper.getURI(operationName, dest);
        const responseXML = SOAPTestHelper.simpleResponse(
            response.result,
            response.errorMessage,
        );
        if (response.throwError) {
            // console.warn(">> set error response for", uri);
            nock(uri).persist().post("").once().replyWithError("Network error");
        } else {
            // console.warn(">> set mock response for", uri);
            const headers: nock.ReplyHeaders = {
                "Content-Type": "text/xml",
            };
            nock(uri)
                .persist()
                .post("")
                .once()
                .reply(response.responseCode ?? 200, responseXML, headers);
        }
    }
}

/**
 * Creates an XML string from a list of XPath and value pairs.
 * @param data - List of [XPath, value] pairs.
 * @returns The constructed XML string.
 */
function createXMLFromXPaths(root: string, data: [string, string][]): string {
    const xmlObject: any = {};

    // Helper function to set value in the nested object
    const setValue = (obj: any, path: string[], value: string) => {
        const key = path.shift();
        if (!key) return;
        if (path.length === 0) {
            obj[key] = value;
        } else {
            if (!obj[key]) obj[key] = {};
            setValue(obj[key], path, value);
        }
    };

    // Parse each XPath and set the value in the nested object
    data.forEach(([xpath, value]) => {
        const parts = xpath.replace(/^\/\//, "").split("/");
        setValue(xmlObject, parts, value);
    });

    // Helper function to convert the nested object to an XML string
    const buildXML = (obj: any): string => {
        return Object.entries(obj)
            .map(([key, val]) => {
                if (typeof val === "object") {
                    return `<${key}>${buildXML(val)}</${key}>`;
                } else {
                    return `<${key}>${val}</${key}>`;
                }
            })
            .join("");
    };

    const result = buildXML(xmlObject);
    if (result === "") return "";
    return `<${root}>${result}</${root}>`;
}
