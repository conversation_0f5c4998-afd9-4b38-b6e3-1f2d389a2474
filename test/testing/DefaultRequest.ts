import { HttpRequest } from "@azure/functions";

export default {
    method: "GET",
    url: "https://example.com",
    headers: {
        get: jest.fn(),
    },
    query: {},
    params: {},
    body: {},
    rawBody: "",
    cookies: {},
    ip: "",
    user: null,
    bodyUsed: false,
    arrayBuffer: jest.fn(),
    blob: jest.fn(),
    buffer: jest.fn(),
    json: jest.fn(),
    text: jest.fn(),
} as unknown as HttpRequest;

/**
 * On-the-fly version of DefaultRequest
 */
export const getDefaultRequest = (): HttpRequest => {
    return {
        method: "GET",
        url: "https://example.com",
        headers: {
            get: jest.fn(),
        },
        query: {},
        params: {},
        body: {},
        rawBody: "",
        cookies: {},
        ip: "",
        user: null,
        bodyUsed: false,
        arrayBuffer: jest.fn(),
        blob: jest.fn(),
        buffer: jest.fn(),
        json: jest.fn(),
        text: jest.fn(),
    } as unknown as HttpRequest;
}