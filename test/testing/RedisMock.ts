// mock redis client from @/src/core/database/redis.ts
import { Redis } from "ioredis"; // real one
import RedisMock from "ioredis-mock";

let redisClient: Redis = null;

async function loadRedisMock(context: any) {
    if (redisClient && redisClient.status === "ready") {
        return redisClient;
    }
    redisClient = new RedisMock();
    return redisClient;
}

/**
 * usage: put mock in top level of a test file, then restore it in afterAll block  
 * note: don't use `@` alias in the path, because it will not be resolved in compiled js
 *
 * example:
 *
 * ```
 * import { useRedisMock } from "@/test/testing/RedisMock";
 * jest.mock('../../../src/database/redis', () => ({
 *   ...jest.requireActual("../../../src/database/redis"),
 *   useRedis: jest.fn().mockImplementation(useRedisMock),
 * }));
 * 
 * // other imports here ...
 *
 * describe('...', () => {
 *   afterAll(() => {
 *    jest.restoreAllMocks();
 *   });
 * });
 * ```
 * @param context
 * @returns
 */
export const useRedisMock = async (context: any) => {
    return await loadRedisMock(context);
};
