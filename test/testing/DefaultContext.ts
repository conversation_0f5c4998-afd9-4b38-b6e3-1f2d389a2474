import ExtendedInvocationContext from "@/types/ExtendedInvocationContext";
import ExtendedPortalInvocationContext from "@/types/ExtendedPortalInvocationContext";

/**
 * Generate random invocationId.
 * Real one will be in uuidv4 format,
 * but for testing purpose, we just need a random string.
 *
 * Put this in beforeEach block of test file to reset invocationId for each test
 * ```
 * beforeEach(() => {
 *    context.invocationId = getNextInvocationId();
 * });
 * ```
 */
export const getNextInvocationId = () => {
    const characters = "0123456789abcdef";
    return Array.from(
        { length: 32 },
        () => characters[Math.floor(Math.random() * characters.length)],
    ).join("");
};

export default {
    log: jest.fn().mockImplementation((...messages) => {
        // console.log(...messages);
    }),
    error: jest.fn().mockImplementation((...messages) => {
        // console.error(...messages);
    }),
    warn: jest.fn().mockImplementation((...messages) => {
        // console.warn(...messages);
    }),
    info: jest.fn(),
    trace: jest.fn(),
    debug: jest.fn(),
    jsonBody: null,
    responseHeader: null,
    invocationId: getNextInvocationId(),
    isInternalRequest: true, // default to true to pass checkReserve validation
} as unknown as ExtendedInvocationContext;

export const extendedPortalInvocationContext = {
    sessionData: jest.fn(),
    log: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    info: jest.fn(),
    trace: jest.fn(),
    debug: jest.fn(),
} as unknown as ExtendedPortalInvocationContext;

/**
 * On-the-fly version of DefaultContext
 */
export const getDefaultContext = (): ExtendedInvocationContext => {
    return {
        log: jest.fn().mockImplementation((...messages) => {
            // console.log(...messages);
        }),
        error: jest.fn().mockImplementation((...messages) => {
            // console.error(...messages);
        }),
        warn: jest.fn().mockImplementation((...messages) => {
            // console.warn(...messages);
        }),
        info: jest.fn(),
        trace: jest.fn(),
        debug: jest.fn().mockImplementation((...messages) => {
            // console.debug(...messages);
        }),
        jsonBody: null,
        responseHeader: null,
        invocationId: getNextInvocationId(),
        isInternalRequest: true, // default to true to pass checkReserve validation
    } as unknown as ExtendedInvocationContext;
};
