import { ConnectionTimedOutError, Sequelize } from "sequelize";
import {
    describeWithDB,
    generateProcessID,
    generateSOID,
} from "../testing/TestHelper";
import { addDays, addMinutes, formatDate } from "date-fns";
import ResultCdConstants from "@/core/constant/ResultCdConstants";
import AppConfig from "@/appconfig";
import DefaultContext from "../testing/DefaultContext";
import { usePsql } from "@/database/psql";
import { disconnectMongo, useMongo } from "@/database/mongo";
import LineCouponAcquisitionInputDto from "@/core/dto/LineCouponAcquisitionInputDto";
import { LineCouponAcquisitionHandler } from "@/functions/LineCouponAcquisitionHandler";
import DefaultRequest from "../testing/DefaultRequest";
import { format, parse } from "path";
import Check from "@/core/common/Check";
import LineCouponAcquisitionService from "@/core/service/impl/LineCouponAcquisitionService";
import LineTenantsEntity from "@/core/entity/LineTenantsEntity";
import TenantManage from "@/core/common/TenantManage";
import TenantPlansEntity from "@/core/entity/TenantPlansEntity";
import APICommonDAO from "@/core/dao/APICommonDAO";
import LineLineGroupsEntity from "@/core/entity/LineLineGroupsEntity";
import LineGroupsEntity from "@/core/entity/LineGroupsEntity";
import { LinesEntity } from "@/core/entity/LinesEntity";
import APILinesDAO from "@/core/dao/APILinesDAO";
import { TenantsEntity } from "@/core/entity/TenantsEntity";
import ApiCommon from "@/core/common/ApiCommon";
import { th } from "date-fns/locale";
import PlansEntity from "@/core/entity/PlansEntity";
import nock from "nock";
import { getConfigDataForTest } from "../testing/testdataHelper";
import SOAPCommonOutputDto from "@/core/dto/SOAPCommonOutputDto";
import { readXML } from "@/core/common/SOAPCommonUtils";
import SOAPCommon from "@/core/common/SOAPCommon";
import ServiceOrdersEntity from "@/core/entity/ServiceOrdersEntity";

const createResponseXML = (
    result: "OK" | "NG",
    test: string,
    errorInfomation: string,
) => {
    return `
    <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
        xmlns:ns1="http://ws.apache.org/axis2">
        <soapenv:Body>
            <Result>${result}</Result>
            <ServiceProfileInfomation>
                <test>${test}</test>
            </ServiceProfileInfomation>
            <ErrorInfomation>${errorInfomation}</ErrorInfomation>
        </soapenv:Body>
    </soapenv:Envelope>
    `.trim();
};

describeWithDB("04 回線クーポンON/OFF /MVNO/api/V100/Lines/coupon", () => {
    let sequelize: Sequelize;
    const testdata = {
        targetSoId: generateSOID(),
        tenantId: "OPF000",
        lineNo: "00001963316",
        potalPlanID: "10001",
        reserve_soId: generateSOID(),
        reserve_date: formatDate(
            addMinutes(new Date(), (15 - (new Date().getMinutes() % 15)) % 15),
            "yyyy/MM/dd HH:mm",
        ),
    };

    const request = {
        requestHeader: {
            sequenceNo: "1234567",
            senderSystemId: "0001",
            apiKey: "$$$",
            functionType: "04",
            receivedDate: formatDate(new Date(), "yyyy/MM/dd HH:mm:ss"),
        },
        potalPlanID: testdata.potalPlanID,
        optionPlanId: "10401",
        couponOnOff: "1",
        lineNo: testdata.lineNo,
        tenantId: testdata.tenantId,
        targetSoId: testdata.targetSoId,
        reserve_flag: false,
        reserve_soId: undefined,
        reserve_date: "",
    } as LineCouponAcquisitionInputDto;
    const responseHeader = {
        sequenceNo: request.requestHeader.sequenceNo,
        receivedDate: formatDate(new Date(), "yyyy/MM/dd HH:mm:ss"),
        processCode: ResultCdConstants.CODE_000000,
        apiProcessID: generateProcessID(),
    };

    const nockHeaders: nock.ReplyHeaders = {
        "Content-Type": "text/xml",
    };

    let configData: ReturnType<typeof getConfigDataForTest>;

    beforeEach(async () => {
        DefaultContext.jsonBody = Object.assign({}, request);
        DefaultContext.responseHeader = Object.assign({}, responseHeader);
        jest.spyOn(Check, "checkReserve").mockReturnValue(true);
        jest.spyOn(Check, "checkReserveDateOrderDate").mockReturnValue(true);

        testdata.reserve_date = formatDate(
            addMinutes(new Date(), (15 - (new Date().getMinutes() % 15)) % 15),
            "yyyy/MM/dd HH:mm",
        );
    });

    afterEach(async () => {
        await ServiceOrdersEntity.destroy({
            where: {
                serviceOrderId: responseHeader.apiProcessID,
            },
        });
        jest.restoreAllMocks();
        jest.clearAllMocks();
        nock.abortPendingRequests();
        nock.cleanAll();
    });

    afterAll(async () => {
        nock.enableNetConnect();
        await Promise.all([
            LineTenantsEntity.destroy({
                where: { lineId: testdata.lineNo },
            }),
            TenantPlansEntity.destroy({
                where: { tenantId: testdata.tenantId },
            }),
            TenantsEntity.update(
                {
                    tenantType: null,
                },
                {
                    where: { tenantId: testdata.tenantId },
                },
            ),
        ]);
        await sequelize.close();
        await disconnectMongo(DefaultContext);
    });

    beforeAll(async () => {
        await AppConfig.loadCoreMvnoConfig(DefaultContext);
        sequelize = await usePsql();
        configData = getConfigDataForTest();
        await useMongo(DefaultContext);
        await AppConfig.loadCoreMvnoConfig(DefaultContext);
        nock.disableNetConnect();
        await Promise.all([
            LineTenantsEntity.create({
                lineId: testdata.lineNo,
                tenantId: testdata.tenantId,
            }),
            TenantPlansEntity.create({
                tenantId: testdata.tenantId,
                planId: parseInt(testdata.potalPlanID, 0),
                changeCount: 0,
                changeTiming: "1",
                changePlanId: parseInt(testdata.potalPlanID, 0),
            }),
            TenantsEntity.update(
                {
                    tenantType: 1,
                },
                {
                    where: { tenantId: testdata.tenantId },
                },
            ),
        ]);
    });

    describe("OK (CODE_000000)", () => {
        beforeEach(async () => {
            jest.spyOn(
                TenantManage.prototype,
                "checkTpcConnection",
            ).mockReturnValue(
                Promise.resolve([
                    true,
                    "TpcDestIpAddress",
                    "TpcDestIpAddress2",
                ]),
            );
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, createResponseXML("OK", "test".toString(), ""))
                .post("")
                .reply(200, createResponseXML("OK", "test".toString(), ""));
        });
        test("should return CODE_000000 if there order type is 0 ()", async () => {
            DefaultContext.jsonBody.reserve_flag = false;
            const result = await LineCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );
        });

        test("should return CODE_000000 even if couponOnOff is a number (1)", async () => {
            DefaultContext.jsonBody.reserve_flag = false;
            DefaultContext.jsonBody.couponOnOff = 1 as any;
            const result = await LineCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );
        });

        test("should return CODE_000000 even if couponOnOff is a number (0)", async () => {
            DefaultContext.jsonBody.reserve_flag = false;
            DefaultContext.jsonBody.couponOnOff = 0 as any;
            const result = await LineCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );
        });

        test("should return CODE_000000 if there order type is 1 (予約実行オーダ)", async () => {
            DefaultContext.jsonBody.reserve_flag = false;
            DefaultContext.jsonBody.reserve_date = testdata.reserve_date;
            DefaultContext.jsonBody.reserve_soId = null;

            const result = await LineCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );
        });

        test("should return CODE_000000 if there order type is 2 (予約実行オーダ)", async () => {
            DefaultContext.jsonBody.reserve_flag = true;
            DefaultContext.jsonBody.reserve_date = testdata.reserve_date;
            DefaultContext.jsonBody.reserve_soId = testdata.reserve_soId;

            const result = await LineCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );
        });

        test("should return CODE_000000 if there order type is 0 , pattern 4", async () => {
            DefaultContext.jsonBody.reserve_flag = false;
            jest.spyOn(APICommonDAO.prototype, "getPlans").mockResolvedValue(
                Promise.resolve([
                    PlansEntity.build({
                        planId: 10401,
                        pricePlanId: null,
                        planClass: 2,
                        policyId: 201,
                        simType: null,
                        network: "LTE",
                        smsEnable: null,
                        planDescription: "test",
                        planName: "test",
                        refServicePattern: 0,
                        tpcServicePattern: 4,
                        basicCapacity: null,
                        servicePlanId: null,
                        voiceFlag: null,
                        defaultSimFlag: false,
                        memo: null,
                        customizeId: null,
                        planChangeClass: null,
                        fullMvnoFlag: null,
                        planIdT: "********",
                    }),
                ]),
            );
            const result = await LineCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );
        });
    });

    describe("OK (CODE_0000) secondary SOAP", () => {
        test("should return CODE_000000 if even if secondary TPC SOAP call fails", async () => {
            jest.spyOn(
                TenantManage.prototype,
                "checkTpcConnection",
            ).mockReturnValue(
                Promise.resolve([
                    true,
                    "TpcDestIpAddress",
                    "TpcDestIpAddress2",
                ]),
            );
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, createResponseXML("OK", "test", ""))
                .post("")
                .reply(200, createResponseXML("NG", "", "error"));
            DefaultContext.jsonBody.reserve_flag = false;
            const result = await LineCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );
        });

        test("should return CODE_000000 if even if secondary TPC cannot be reached (SOAPException)", async () => {
            jest.spyOn(
                TenantManage.prototype,
                "checkTpcConnection",
            ).mockReturnValue(
                Promise.resolve([
                    true,
                    "TpcDestIpAddress",
                    "TpcDestIpAddress2",
                ]),
            );
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, createResponseXML("OK", "test", ""))
                .post("");
            DefaultContext.jsonBody.reserve_flag = false;
            const result = await LineCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );
        });

        // TODO : fix later
        test.skip("should return CODE_000000 if even if secondary TPC SOAP call returns CODE_000951)", async () => {
            jest.spyOn(
                TenantManage.prototype,
                "checkTpcConnection",
            ).mockReturnValue(
                Promise.resolve([
                    true,
                    "TpcDestIpAddress",
                    "TpcDestIpAddress2",
                ]),
            );
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, createResponseXML("OK", "test", ""))
                .persist();
            const output = new SOAPCommonOutputDto();
            output.setProcessCode(ResultCdConstants.CODE_000000);
            output.setDoc(readXML(createResponseXML("OK", "test", "")));
            const outputNG = new SOAPCommonOutputDto();
            outputNG.setProcessCode(ResultCdConstants.CODE_000951);
            jest.spyOn(SOAPCommon.prototype, "callWebSoapApi")
                .mockResolvedValueOnce(output)
                .mockResolvedValueOnce(outputNG);
            DefaultContext.jsonBody.reserve_flag = false;
            request.reserve_flag = false;
            const instance = new LineCouponAcquisitionService(
                DefaultRequest,
                DefaultContext,
            );
            const result = await instance.service(request, false);
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );
            expect(DefaultContext.warn).toHaveBeenCalledTimes(1);
        });

        test("should return CODE_040401 if TPC cannot reached (SOAPException) , pattern 4", async () => {
            jest.spyOn(
                TenantManage.prototype,
                "checkTpcConnection",
            ).mockReturnValue(
                Promise.resolve([
                    true,
                    "TpcDestIpAddress",
                    "TpcDestIpAddress2",
                ]),
            );
            DefaultContext.jsonBody.reserve_flag = false;
            jest.spyOn(APICommonDAO.prototype, "getPlans").mockResolvedValue(
                Promise.resolve([
                    PlansEntity.build({
                        planId: 10401,
                        pricePlanId: null,
                        planClass: 2,
                        policyId: 201,
                        simType: null,
                        network: "LTE",
                        smsEnable: null,
                        planDescription: "test",
                        planName: "test",
                        refServicePattern: 0,
                        tpcServicePattern: 4,
                        basicCapacity: null,
                        servicePlanId: null,
                        voiceFlag: null,
                        defaultSimFlag: false,
                        memo: null,
                        customizeId: null,
                        planChangeClass: null,
                        fullMvnoFlag: null,
                        planIdT: "********",
                    }),
                ]),
            );
            const result = await LineCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_040401,
            );
        });
        test("should return CODE_040401 if SOAP response is NG , pattern 4", async () => {
            jest.spyOn(
                TenantManage.prototype,
                "checkTpcConnection",
            ).mockReturnValue(
                Promise.resolve([
                    true,
                    "TpcDestIpAddress",
                    "TpcDestIpAddress2",
                ]),
            );
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, createResponseXML("NG", "", "test error"));
            DefaultContext.jsonBody.reserve_flag = false;
            jest.spyOn(APICommonDAO.prototype, "getPlans").mockResolvedValue(
                Promise.resolve([
                    PlansEntity.build({
                        planId: 10401,
                        pricePlanId: null,
                        planClass: 2,
                        policyId: 201,
                        simType: null,
                        network: "LTE",
                        smsEnable: null,
                        planDescription: "test",
                        planName: "test",
                        refServicePattern: 0,
                        tpcServicePattern: 4,
                        basicCapacity: null,
                        servicePlanId: null,
                        voiceFlag: null,
                        defaultSimFlag: false,
                        memo: null,
                        customizeId: null,
                        planChangeClass: null,
                        fullMvnoFlag: null,
                        planIdT: "********",
                    }),
                ]),
            );
            const result = await LineCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_040401,
            );
        });

        test("should return CODE_000000 if even if secondary TPC cannot be reached (SOAPException), pattern 4", async () => {
            jest.spyOn(
                TenantManage.prototype,
                "checkTpcConnection",
            ).mockReturnValue(
                Promise.resolve([
                    true,
                    "TpcDestIpAddress",
                    "TpcDestIpAddress2",
                ]),
            );
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, createResponseXML("OK", "test", ""));
            DefaultContext.jsonBody.reserve_flag = false;
            jest.spyOn(APICommonDAO.prototype, "getPlans").mockResolvedValue(
                Promise.resolve([
                    PlansEntity.build({
                        planId: 10401,
                        pricePlanId: null,
                        planClass: 2,
                        policyId: 201,
                        simType: null,
                        network: "LTE",
                        smsEnable: null,
                        planDescription: "test",
                        planName: "test",
                        refServicePattern: 0,
                        tpcServicePattern: 4,
                        basicCapacity: null,
                        servicePlanId: null,
                        voiceFlag: null,
                        defaultSimFlag: false,
                        memo: null,
                        customizeId: null,
                        planChangeClass: null,
                        fullMvnoFlag: null,
                        planIdT: "********",
                    }),
                ]),
            );
            const result = await LineCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );
        });

        test("should return CODE_000000 if even if secondary TPC SOAP call fails, pattern 4", async () => {
            jest.spyOn(
                TenantManage.prototype,
                "checkTpcConnection",
            ).mockReturnValue(
                Promise.resolve([
                    true,
                    "TpcDestIpAddress",
                    "TpcDestIpAddress2",
                ]),
            );
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, createResponseXML("OK", "test", ""))
                .post("")
                .reply(200, createResponseXML("NG", "", "error"));
            DefaultContext.jsonBody.reserve_flag = false;
            jest.spyOn(APICommonDAO.prototype, "getPlans").mockResolvedValue(
                Promise.resolve([
                    PlansEntity.build({
                        planId: 10401,
                        pricePlanId: null,
                        planClass: 2,
                        policyId: 201,
                        simType: null,
                        network: "LTE",
                        smsEnable: null,
                        planDescription: "test",
                        planName: "test",
                        refServicePattern: 0,
                        tpcServicePattern: 4,
                        basicCapacity: null,
                        servicePlanId: null,
                        voiceFlag: null,
                        defaultSimFlag: false,
                        memo: null,
                        customizeId: null,
                        planChangeClass: null,
                        fullMvnoFlag: null,
                        planIdT: "********",
                    }),
                ]),
            );
            const result = await LineCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );
        });
    });

    describe("OK (CODE_04XXXX)", () => {
        test("should return CODE_040111 if there order type is 9 (予想以外オーダ)", async () => {
            DefaultContext.jsonBody.reserve_flag = true;
            const result = await LineCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_040111,
            );
        });

        test("should return CODE_040101 if lineId is not valid", async () => {
            DefaultContext.jsonBody.reserve_flag = false;
            DefaultContext.jsonBody.lineNo = null;
            const result = await LineCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_040101,
            );
        });

        test("should return CODE_040101 if potalPlanID is not valid", async () => {
            DefaultContext.jsonBody.reserve_flag = false;
            DefaultContext.jsonBody.potalPlanID = null;
            const result = await LineCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_040101,
            );
        });

        test("should return CODE_040101 if couponOnOff is not valid && order type = 0", async () => {
            DefaultContext.jsonBody.reserve_flag = false;
            DefaultContext.jsonBody.couponOnOff = null;
            const result = await LineCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_040101,
            );
        });

        test("should return CODE_040101 if couponOnOff is not valid (3: number) && order type = 0", async () => {
            DefaultContext.jsonBody.reserve_flag = false;
            DefaultContext.jsonBody.couponOnOff = 3;
            const result = await LineCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_040101,
            );
        });

        test("should return CODE_040101 if optionPlanId is not valid", async () => {
            DefaultContext.jsonBody.reserve_flag = false;
            DefaultContext.jsonBody.optionPlanId = null;
            const result = await LineCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_040101,
            );
        });

        test("should return CODE_040101 if reserveDate is not valid && order type is 2 (予約実行オーダ)", async () => {
            DefaultContext.jsonBody.reserve_flag = true;
            DefaultContext.jsonBody.reserve_date = "2024:12:12 12:12";
            DefaultContext.jsonBody.reserve_soId = testdata.reserve_soId;

            const result = await LineCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_040101,
            );
        });

        test("should return CODE_040101 if reserveDate is not valid && order type is 1 (予約実行オーダ)", async () => {
            DefaultContext.jsonBody.reserve_flag = false;
            DefaultContext.jsonBody.reserve_date = "2024:12:12 12:12";
            DefaultContext.jsonBody.reserve_soId = null;

            const result = await LineCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_040101,
            );
        });

        test("should return CODE_040106 if checkReserveDateOrderDate fails while order type is 1 (予約実行オーダ)", async () => {
            DefaultContext.jsonBody.reserve_flag = false;
            DefaultContext.jsonBody.reserve_date = testdata.reserve_date;
            DefaultContext.jsonBody.reserve_soId = null;
            jest.spyOn(Check, "checkReserveDateOrderDate").mockReturnValue(
                false,
            );
            const result = await LineCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_040106,
            );
        });

        test("should return CODE_040107 if reservationDateExecutionUnits fails while order type is 1 (予約実行オーダ)", async () => {
            DefaultContext.jsonBody.reserve_flag = false;
            DefaultContext.jsonBody.reserve_date = testdata.reserve_date;
            DefaultContext.jsonBody.reserve_soId = null;
            jest.spyOn(
                Check,
                "checkReservationDateExecutionUnitsFmt",
            ).mockReturnValue(false);
            const result = await LineCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_040107,
            );
        });

        test("should return CODE_040108 if checkReservationDateExecutionUnits fails while order type is 1 (予約実行オーダ)", async () => {
            DefaultContext.jsonBody.reserve_flag = false;
            DefaultContext.jsonBody.reserve_date = testdata.reserve_date;
            DefaultContext.jsonBody.reserve_soId = null;
            jest.spyOn(
                Check,
                "checkReservationDateExecutionUnits",
            ).mockReturnValue(false);
            const result = await LineCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_040108,
            );
        });

        test("should return CODE_040109 if checkReservationsLimitDaysFmt fails while order type is 1 (予約実行オーダ)", async () => {
            DefaultContext.jsonBody.reserve_flag = false;
            DefaultContext.jsonBody.reserve_date = testdata.reserve_date;
            DefaultContext.jsonBody.reserve_soId = null;
            jest.spyOn(Check, "checkReservationsLimitDaysFmt").mockReturnValue(
                false,
            );
            const result = await LineCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_040109,
            );
        });

        test("should return CODE_040110 if checkReservationsLimitDays fails while order type is 1 (予約実行オーダ)", async () => {
            DefaultContext.jsonBody.reserve_flag = false;
            DefaultContext.jsonBody.reserve_date = testdata.reserve_date;
            DefaultContext.jsonBody.reserve_soId = null;
            jest.spyOn(Check, "checkReservationsLimitDays").mockReturnValue(
                false,
            );
            const result = await LineCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_040110,
            );
        });

        test("should return CODE_040102 if lineNo doesnt exist in lineTenants", async () => {
            DefaultContext.jsonBody.reserve_flag = false;
            DefaultContext.jsonBody.reserve_date = testdata.reserve_date;
            DefaultContext.jsonBody.reserve_soId = null;
            jest.spyOn(TenantManage.prototype, "doCheck").mockReturnValue(
                Promise.resolve([false, null]),
            );
            const result = await LineCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_040102,
            );
        });

        test("should return CODE_040102 if tenantManage.doCheck fails to query", async () => {
            DefaultContext.jsonBody.reserve_flag = false;
            DefaultContext.jsonBody.reserve_date = testdata.reserve_date;
            DefaultContext.jsonBody.reserve_soId = null;
            jest.spyOn(TenantManage.prototype, "doCheck").mockImplementation(
                async () => {
                    throw new ConnectionTimedOutError(
                        new Error("Connection Timed Out"),
                    );
                },
            );
            const result = await LineCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_040102,
            );
        });

        test("should return CODE_040103 if apiCommonDAO.getTenantPlans fails to query", async () => {
            DefaultContext.jsonBody.reserve_flag = false;
            DefaultContext.jsonBody.reserve_date = testdata.reserve_date;
            DefaultContext.jsonBody.reserve_soId = null;
            jest.spyOn(
                APICommonDAO.prototype,
                "getTenantPlans",
            ).mockImplementation(async () => {
                throw new ConnectionTimedOutError(
                    new Error("Connection Timed Out"),
                );
            });
            const result = await LineCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_040103,
            );
        });

        test("should return CODE_040103 if apiCommonDAO.getTenantPlans returns null", async () => {
            DefaultContext.jsonBody.reserve_flag = false;
            DefaultContext.jsonBody.reserve_date = testdata.reserve_date;
            DefaultContext.jsonBody.reserve_soId = null;
            jest.spyOn(
                APICommonDAO.prototype,
                "getTenantPlans",
            ).mockReturnValue(Promise.resolve(null));
            const result = await LineCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_040103,
            );
        });

        test("should return CODE_040104 if aPILinesDAO.getLinesGroupId doesnt return null", async () => {
            DefaultContext.jsonBody.reserve_flag = false;
            DefaultContext.jsonBody.reserve_date = testdata.reserve_date;
            DefaultContext.jsonBody.reserve_soId = null;
            jest.spyOn(
                APILinesDAO.prototype,
                "getLinesGroupId",
            ).mockReturnValue(Promise.resolve("123456"));
            const result = await LineCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_040104,
            );
        });

        test("should return CODE_040104 if aPILinesDAO.getLinesGroupId fails to query", async () => {
            DefaultContext.jsonBody.reserve_flag = false;
            DefaultContext.jsonBody.reserve_date = testdata.reserve_date;
            DefaultContext.jsonBody.reserve_soId = null;
            jest.spyOn(
                APILinesDAO.prototype,
                "getLinesGroupId",
            ).mockImplementation(async () => {
                throw new ConnectionTimedOutError(new Error("time out error"));
            });
            const result = await LineCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_040104,
            );
        });

        test("should return CODE_040112 if apiCommonDAO.getTenants fails to query", async () => {
            DefaultContext.jsonBody.reserve_flag = false;
            DefaultContext.jsonBody.reserve_date = testdata.reserve_date;
            DefaultContext.jsonBody.reserve_soId = null;
            jest.spyOn(APICommonDAO.prototype, "getTenants").mockImplementation(
                async () => {
                    throw new ConnectionTimedOutError(
                        new Error("time out error"),
                    );
                },
            );
            const result = await LineCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_040112,
            );
        });

        test("should return CODE_040112 if no tenant found", async () => {
            DefaultContext.jsonBody.reserve_flag = false;
            DefaultContext.jsonBody.reserve_date = testdata.reserve_date;
            DefaultContext.jsonBody.reserve_soId = null;
            jest.spyOn(APICommonDAO.prototype, "getTenants").mockReturnValue(
                Promise.resolve(null),
            );
            const result = await LineCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_040112,
            );
        });

        test("should return CODE_040113 if aPILinesDAO.getLineUsingPlan fails to query", async () => {
            DefaultContext.jsonBody.reserve_flag = false;
            DefaultContext.jsonBody.reserve_date = testdata.reserve_date;
            DefaultContext.jsonBody.reserve_soId = null;
            jest.spyOn(APICommonDAO.prototype, "getTenants").mockReturnValue(
                Promise.resolve(
                    TenantsEntity.build({
                        tenantId: testdata.tenantId,
                        tenantType: 4,
                    }),
                ),
            );
            jest.spyOn(
                APILinesDAO.prototype,
                "getLineUsingPlan",
            ).mockImplementation(async () => {
                throw new ConnectionTimedOutError(new Error("time out error"));
            });
            const result = await LineCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_040113,
            );
        });

        test("should return CODE_040113 if line planID doesnt match with param planID", async () => {
            DefaultContext.jsonBody.reserve_flag = false;
            DefaultContext.jsonBody.reserve_date = testdata.reserve_date;
            DefaultContext.jsonBody.reserve_soId = null;
            jest.spyOn(APICommonDAO.prototype, "getTenants").mockReturnValue(
                Promise.resolve(
                    TenantsEntity.build({
                        tenantId: testdata.tenantId,
                        tenantType: 4,
                    }),
                ),
            );
            const result = await LineCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_040113,
            );
        });

        test("should return CODE_040105 if apiCommonDAO.getCheckedPlanOptionPlans fails to query", async () => {
            DefaultContext.jsonBody.reserve_flag = false;
            DefaultContext.jsonBody.reserve_date = testdata.reserve_date;
            DefaultContext.jsonBody.reserve_soId = null;
            jest.spyOn(
                APICommonDAO.prototype,
                "getCheckedPlanOptionPlans",
            ).mockImplementation(async () => {
                throw new ConnectionTimedOutError(new Error("time out error"));
            });
            const result = await LineCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_040105,
            );
        });

        test("should return CODE_040105 if getCheckedPlanOptionPlans returns null", async () => {
            DefaultContext.jsonBody.reserve_flag = false;
            DefaultContext.jsonBody.reserve_date = testdata.reserve_date;
            DefaultContext.jsonBody.reserve_soId = null;
            jest.spyOn(
                APICommonDAO.prototype,
                "getCheckedPlanOptionPlans",
            ).mockReturnValue(Promise.resolve(null));
            const result = await LineCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_040105,
            );
        });

        test("should return CODE_040114 if apiCommon.checkAbolishSo returns false", async () => {
            DefaultContext.jsonBody.reserve_flag = false;
            DefaultContext.jsonBody.reserve_date = testdata.reserve_date;
            DefaultContext.jsonBody.reserve_soId = null;
            jest.spyOn(ApiCommon.prototype, "checkAbolishSo").mockReturnValue(
                Promise.resolve(false),
            );
            const result = await LineCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_040114,
            );
        });

        test("should return CODE_040114 if apiCommon.checkAbolishSo fails to query", async () => {
            DefaultContext.jsonBody.reserve_flag = false;
            DefaultContext.jsonBody.reserve_date = testdata.reserve_date;
            DefaultContext.jsonBody.reserve_soId = null;
            jest.spyOn(
                ApiCommon.prototype,
                "checkAbolishSo",
            ).mockImplementation(async () => {
                throw new ConnectionTimedOutError(new Error("time out error"));
            });
            const result = await LineCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_040114,
            );
        });

        test("should return CODE_040115 if apiCommon.checkLineSuspend fails to query", async () => {
            DefaultContext.jsonBody.reserve_flag = false;
            DefaultContext.jsonBody.reserve_date = testdata.reserve_date;
            DefaultContext.jsonBody.reserve_soId = null;
            jest.spyOn(
                ApiCommon.prototype,
                "checkLineSuspend",
            ).mockImplementation(async () => {
                throw new ConnectionTimedOutError(new Error("time out error"));
            });
            const result = await LineCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_040115,
            );
        });

        test("should return CODE_040115 if apiCommon.checkLineSuspend fails to query", async () => {
            DefaultContext.jsonBody.reserve_flag = false;
            DefaultContext.jsonBody.reserve_date = testdata.reserve_date;
            DefaultContext.jsonBody.reserve_soId = null;
            jest.spyOn(ApiCommon.prototype, "checkLineSuspend").mockReturnValue(
                Promise.resolve(false),
            );
            const result = await LineCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_040115,
            );
        });

        test("should return CODE_040201 if apiCommonDAO.getPlans fails to query", async () => {
            DefaultContext.jsonBody.reserve_flag = false;
            DefaultContext.jsonBody.reserve_date = testdata.reserve_date;
            DefaultContext.jsonBody.reserve_soId = null;
            jest.spyOn(APICommonDAO.prototype, "getPlans").mockImplementation(
                async () => {
                    throw new ConnectionTimedOutError(
                        new Error("time out error"),
                    );
                },
            );
            const result = await LineCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_040201,
            );
        });

        test("should return CODE_040201 if apiCommonDAO.getPlans fails to query", async () => {
            DefaultContext.jsonBody.reserve_flag = false;
            DefaultContext.jsonBody.reserve_date = testdata.reserve_date;
            DefaultContext.jsonBody.reserve_soId = null;
            jest.spyOn(APICommonDAO.prototype, "getPlans").mockReturnValue(
                Promise.resolve(null),
            );
            const result = await LineCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_040201,
            );
        });

        test("should return CODE_040202 if service Pattern doesnt match", async () => {
            DefaultContext.jsonBody.reserve_flag = false;
            DefaultContext.jsonBody.reserve_date = testdata.reserve_date;
            DefaultContext.jsonBody.reserve_soId = null;
            jest.spyOn(APICommonDAO.prototype, "getPlans").mockResolvedValue(
                Promise.resolve([
                    PlansEntity.build({
                        planId: 10401,
                        pricePlanId: null,
                        planClass: 2,
                        policyId: 201,
                        simType: null,
                        network: "LTE",
                        smsEnable: null,
                        planDescription: "test",
                        planName: "test",
                        refServicePattern: 0,
                        tpcServicePattern: 3,
                        basicCapacity: null,
                        servicePlanId: null,
                        voiceFlag: null,
                        defaultSimFlag: false,
                        memo: null,
                        customizeId: null,
                        planChangeClass: null,
                        fullMvnoFlag: null,
                        planIdT: "********",
                    }),
                ]),
            );
            const result = await LineCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_040202,
            );
        });

        test("should return CODE_040202 if policyId doesnt match", async () => {
            DefaultContext.jsonBody.reserve_flag = false;
            DefaultContext.jsonBody.reserve_date = testdata.reserve_date;
            DefaultContext.jsonBody.reserve_soId = null;
            jest.spyOn(APICommonDAO.prototype, "getPlans").mockResolvedValue(
                Promise.resolve([
                    PlansEntity.build({
                        planId: 10401,
                        pricePlanId: null,
                        planClass: 2,
                        policyId: null,
                        simType: null,
                        network: "LTE",
                        smsEnable: null,
                        planDescription: "test",
                        planName: "test",
                        refServicePattern: 0,
                        tpcServicePattern: 4,
                        basicCapacity: null,
                        servicePlanId: null,
                        voiceFlag: null,
                        defaultSimFlag: false,
                        memo: null,
                        customizeId: null,
                        planChangeClass: null,
                        fullMvnoFlag: null,
                        planIdT: "********",
                    }),
                ]),
            );
            const result = await LineCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_040202,
            );
        });

        test("should return CODE_040310 if checkTpcConnection fails to query", async () => {
            DefaultContext.jsonBody.reserve_flag = true;
            DefaultContext.jsonBody.reserve_date = testdata.reserve_date;
            DefaultContext.jsonBody.reserve_soId = testdata.reserve_soId;

            jest.spyOn(
                TenantManage.prototype,
                "checkTpcConnection",
            ).mockImplementation(async () => {
                throw new ConnectionTimedOutError(new Error("time out error"));
            });
            const result = await LineCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_040310,
            );
        });

        test("should return CODE_040310 if checkTpcConnection returns null", async () => {
            DefaultContext.jsonBody.reserve_flag = true;
            DefaultContext.jsonBody.reserve_date = testdata.reserve_date;
            DefaultContext.jsonBody.reserve_soId = testdata.reserve_soId;
            jest.spyOn(
                TenantManage.prototype,
                "checkTpcConnection",
            ).mockReturnValue(Promise.resolve([false, null, null]));
            const result = await LineCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_040310,
            );
        });

        test("should return CODE_040401 if TPC SOAP call fails", async () => {
            jest.spyOn(
                TenantManage.prototype,
                "checkTpcConnection",
            ).mockReturnValue(
                Promise.resolve([
                    true,
                    "TpcDestIpAddress",
                    "TpcDestIpAddress2",
                ]),
            );
            DefaultContext.jsonBody.reserve_flag = false;
            const result = await LineCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_040401,
            );
        });
        test("should return CODE_040401 if SOAP response is NG", async () => {
            jest.spyOn(
                TenantManage.prototype,
                "checkTpcConnection",
            ).mockReturnValue(
                Promise.resolve([
                    true,
                    "TpcDestIpAddress",
                    "TpcDestIpAddress2",
                ]),
            );
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, createResponseXML("NG", "", "test error"));
            DefaultContext.jsonBody.reserve_flag = false;
            const result = await LineCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_040401,
            );
        });
    });

    describe("NG (CODE_999999)", () => {
        test("should return CODE_999999 if there order type is 2 (予約実行オーダ) and client ip address is invalid", async () => {
            try {
                DefaultContext.isInternalRequest = false;
                jest.spyOn(Check, "checkReserve").mockReturnValue(false);
                DefaultContext.jsonBody.reserve_flag = true;
                DefaultContext.jsonBody.reserve_date = testdata.reserve_date;
                DefaultContext.jsonBody.reserve_soId = testdata.reserve_soId;
                const result = await LineCouponAcquisitionHandler.Handler(
                    DefaultRequest,
                    DefaultContext,
                );
                expect(typeof result).toBe("object");
                expect(result).toHaveProperty("jsonBody");
                expect(result.jsonBody).toHaveProperty("responseHeader");
                expect(result.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_999999,
                );
            } finally {
                DefaultContext.isInternalRequest = true;
            }
        });

        test("should return CODE_999999 if tenantManage.doCheck occurs error", async () => {
            DefaultContext.jsonBody.reserve_flag = false;
            DefaultContext.jsonBody.reserve_date = testdata.reserve_date;
            DefaultContext.jsonBody.reserve_soId = null;
            jest.spyOn(TenantManage.prototype, "doCheck").mockRejectedValue(
                new Error("Error"),
            );
            const result = await LineCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_999999,
            );
        });

        test("should return CODE_999999 if apiCommonDAO.getTenantPlans occurs error", async () => {
            DefaultContext.jsonBody.reserve_flag = false;
            DefaultContext.jsonBody.reserve_date = testdata.reserve_date;
            DefaultContext.jsonBody.reserve_soId = null;
            jest.spyOn(
                APICommonDAO.prototype,
                "getTenantPlans",
            ).mockRejectedValue(new Error("Error"));
            const result = await LineCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_999999,
            );
        });

        test("should return CODE_999999 if aPILinesDAO.getLinesGroupId occurs error", async () => {
            DefaultContext.jsonBody.reserve_flag = false;
            DefaultContext.jsonBody.reserve_date = testdata.reserve_date;
            DefaultContext.jsonBody.reserve_soId = null;
            jest.spyOn(
                APILinesDAO.prototype,
                "getLinesGroupId",
            ).mockRejectedValue(new Error("Error"));
            const result = await LineCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_999999,
            );
        });

        test("should return CODE_999999 if apiCommonDAO.getTenants occurs error", async () => {
            DefaultContext.jsonBody.reserve_flag = false;
            DefaultContext.jsonBody.reserve_date = testdata.reserve_date;
            DefaultContext.jsonBody.reserve_soId = null;
            jest.spyOn(APICommonDAO.prototype, "getTenants").mockRejectedValue(
                new Error("Error"),
            );
            const result = await LineCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_999999,
            );
        });

        test("should return CODE_999999 if aPILinesDAO.getLineUsingPlan occurs error", async () => {
            DefaultContext.jsonBody.reserve_flag = false;
            DefaultContext.jsonBody.reserve_date = testdata.reserve_date;
            DefaultContext.jsonBody.reserve_soId = null;
            jest.spyOn(APICommonDAO.prototype, "getTenants").mockReturnValue(
                Promise.resolve(
                    TenantsEntity.build({
                        tenantId: testdata.tenantId,
                        tenantType: 4,
                    }),
                ),
            );
            jest.spyOn(
                APILinesDAO.prototype,
                "getLineUsingPlan",
            ).mockRejectedValue(new Error("Error"));
            const result = await LineCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_999999,
            );
        });

        test("should return CODE_999999 if apiCommonDAO.getCheckedPlanOptionPlans occurs error", async () => {
            DefaultContext.jsonBody.reserve_flag = false;
            DefaultContext.jsonBody.reserve_date = testdata.reserve_date;
            DefaultContext.jsonBody.reserve_soId = null;
            jest.spyOn(
                APICommonDAO.prototype,
                "getCheckedPlanOptionPlans",
            ).mockRejectedValue(new Error("Error"));
            const result = await LineCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_999999,
            );
        });

        test("should return CODE_999999 if apiCommon.checkAbolishSo occurs error", async () => {
            DefaultContext.jsonBody.reserve_flag = false;
            DefaultContext.jsonBody.reserve_date = testdata.reserve_date;
            DefaultContext.jsonBody.reserve_soId = null;
            jest.spyOn(ApiCommon.prototype, "checkAbolishSo").mockRejectedValue(
                new Error("Error"),
            );
            const result = await LineCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_999999,
            );
        });
        test("should return CODE_999999 if apiCommon.checkLineSuspend occurs error", async () => {
            DefaultContext.jsonBody.reserve_flag = false;
            DefaultContext.jsonBody.reserve_date = testdata.reserve_date;
            DefaultContext.jsonBody.reserve_soId = null;
            jest.spyOn(
                ApiCommon.prototype,
                "checkLineSuspend",
            ).mockRejectedValue(new Error("Error"));
            const result = await LineCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_999999,
            );
        });

        test("should return CODE_999999 if apiCommonDAO.getPlans occurs error", async () => {
            DefaultContext.jsonBody.reserve_flag = false;
            DefaultContext.jsonBody.reserve_date = testdata.reserve_date;
            DefaultContext.jsonBody.reserve_soId = null;
            jest.spyOn(APICommonDAO.prototype, "getPlans").mockRejectedValue(
                new Error("Error"),
            );
            const result = await LineCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_999999,
            );
        });

        test("should return CODE_040310 if checkTpcConnection occurs error", async () => {
            DefaultContext.jsonBody.reserve_flag = true;
            DefaultContext.jsonBody.reserve_date = testdata.reserve_date;
            DefaultContext.jsonBody.reserve_soId = testdata.reserve_soId;
            jest.spyOn(
                TenantManage.prototype,
                "checkTpcConnection",
            ).mockImplementation(async () => {
                throw new ConnectionTimedOutError(new Error("time out error"));
            });
            const result = await LineCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_040310,
            );
        });
    });
});

// TODO: test CODE_000951 and check warn message called
// TODO: check SOAP response value
// organize test data
