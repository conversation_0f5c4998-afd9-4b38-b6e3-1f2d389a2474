import { Sequelize } from "sequelize-typescript";
import { QueryTypes } from "sequelize";
import ReserveRewriteHandler from "../../../src/functions/triggers/ReserveRewriteHandler";
import DefaultContext from "../../testing/DefaultContext";
import AppConfig from "@/appconfig";
import StorageTableService from "@/services/storageTableService";
import { InvocationContext } from "@azure/functions";
import { expect, test } from "@jest/globals";
import { TableClient } from "@azure/data-tables";

describe("ReserveRewriteHandler", () => {
    let sequelize: Sequelize;
    let context: InvocationContext;
    let storageTable: StorageTableService;

    beforeAll(async () => {
        const dbConfig = await AppConfig.getDBConfig(null);
        const psqlDBURI = dbConfig.DATABASE_CORE_POSTGRESQL_CONNECTION_STRING;
        sequelize = new Sequelize(psqlDBURI, {
            pool: { max: 5, min: 0, idle: 0, acquire: 3000 },
            define: { timestamps: false },
            dialectOptions: { useUTC: false },
            timezone: "+09:00"
        });
        await sequelize.authenticate();
    });

    beforeEach(async () => {
        context = DefaultContext;
        storageTable = new StorageTableService(context, "MntScriptPid");
        await storageTable.init();
    });

    afterEach(async () => {
        jest.clearAllMocks();
        jest.restoreAllMocks();
        await sequelize.query(
            `DELETE FROM service_orders WHERE service_order_id LIKE 'TEST%'`,
            { type: QueryTypes.DELETE }
        );

        await storageTable.clear();
    });

    afterAll(async () => {
        await sequelize.close();
    });

    test("should update orders matching criteria", async () => {
        await sequelize.query(`
          INSERT INTO service_orders
          (service_order_id, order_status, function_type, tenant_id, reserve_date)
          VALUES
          ('TEST001', '予約中', '03', 'KIC000', CURRENT_DATE + interval '4 hours')
        `, {
            type: QueryTypes.INSERT
        });

        const htmlResponse = await ReserveRewriteHandler.Handler(null, context);

        expect(htmlResponse.status).toBe(200);

        const result = await sequelize.query(
            `SELECT reserve_date FROM service_orders WHERE service_order_id = 'TEST001'`,
            { type: QueryTypes.SELECT }
        ) as { reserve_date: string }[];

        const fourtyFiveMinuteUpdate = await sequelize.query(
            `SELECT CURRENT_DATE + interval '45 minutes' AS current_date`,
            { type: QueryTypes.SELECT }
        ) as { current_date: string }[];
        expect(new Date(result[0].reserve_date).toISOString()).toBe(new Date(fourtyFiveMinuteUpdate[0].current_date).toISOString());
        expect(context.log).toHaveBeenCalledWith("書き換え対象のオーダIDは次の通りです。");
        expect(context.error).toHaveBeenCalledTimes(0);

        const pid = await storageTable.getUniqueRowKeys();
        expect(pid.length).toBe(0);
    });

    test("should handle no matching orders", async () => {
        await sequelize.query(`
            INSERT INTO service_orders
            (service_order_id, order_status, function_type, tenant_id, reserve_date)
            VALUES
            ('TEST002', '処理中', '03', 'KIC000', NOW())
        `, {
            type: QueryTypes.INSERT
        });

        const htmlResponse = await ReserveRewriteHandler.Handler(null, context);

        expect(htmlResponse.status).toBe(200);

        expect(context.log).toHaveBeenCalledWith("書き換え対象のオーダがありません。");
        expect(context.error).toHaveBeenCalledTimes(0);

        const pid = await storageTable.getUniqueRowKeys();
        expect(pid.length).toBe(0);
    });

    test("should prevent double execution", async () => {
        const storageTable = new StorageTableService(context, "MntScriptPid");
        const tableClient = await storageTable.init();

        await tableClient.createEntity({
            partitionKey: "ReserveRewrite",
            rowKey: `${new Date().toISOString()}`,
            data: JSON.stringify({ pid: 12345, now: new Date().toISOString() }),
        });

        const htmlResponse = await ReserveRewriteHandler.Handler(null, context);

        expect(htmlResponse.status).toBe(200);

        expect(context.error).toHaveBeenCalledWith("二重起動を検知したため起動を抑止します。");

        const pid = await storageTable.getUniqueRowKeys();
        console.warn(pid);
        expect(pid.length).toBe(1);
    });

    test("should allow execution if last run was more than 30 minutes ago", async () => {
        const storageTable = new StorageTableService(context, "MntScriptPid");
        const tableClient = await storageTable.init();

        await tableClient.createEntity({
            partitionKey: "ReserveRewrite",
            rowKey: `${new Date(Date.now() - 31 * 60 * 1000).toISOString()}`,
            data: JSON.stringify({ pid: 12345, now: new Date().toISOString() }),
        });

        const htmlResponse = await ReserveRewriteHandler.Handler(null, context);

        expect(htmlResponse.status).toBe(200);

        expect(context.error).toHaveBeenCalledTimes(0);

        const pid = await storageTable.getUniqueRowKeys();
        expect(pid.length).toBe(0);
    });

    test("should handle database query errors", async () => {
        jest.spyOn(ReserveRewriteHandler.prototype as any, "useCorePsql")
            .mockImplementation(async () => {
                const dbConfig = await AppConfig.getDBConfig(null);
                const psqlDBURI = dbConfig.DATABASE_CORE_POSTGRESQL_CONNECTION_STRING;
                const sequelize = new Sequelize(psqlDBURI, {
                    pool: { max: 5, min: 0, idle: 0, acquire: 3000 },
                    define: { timestamps: false },
                    dialectOptions: { useUTC: false },
                    timezone: "+09:00"
                });
                await sequelize.authenticate();

                const mockedSequelize = sequelize;
                jest.spyOn(mockedSequelize, "query").mockImplementation(() => {
                    throw new Error("Database connection failed");
                });
                return mockedSequelize;
            });

        try {
            const htmlResponse = await ReserveRewriteHandler.Handler(null, context);

            expect(htmlResponse.status).toBe(500);
        } catch (e) {
            expect(e.message).toBe("Database connection failed");
            expect(context.error).toHaveBeenNthCalledWith(2, "予約日時書き換え対象オーダの検索に失敗しました。", expect.any(Error));
        }

        const pid = await storageTable.getUniqueRowKeys();
        expect(pid.length).toBe(0);
    });
});