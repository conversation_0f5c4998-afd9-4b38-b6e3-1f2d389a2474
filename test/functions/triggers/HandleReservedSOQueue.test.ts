import axios from "axios";
import HandleReservedSOQueue from "@/functions/triggers/HandleReservedSOQueue";
import RestAPIClient from "@/core/restclient/RestAPIClient";
import StorageTableService from "@/services/storageTableService";
import ExtendedInvocationContext from "@/types/ExtendedInvocationContext";
import DefaultContext from "../../testing/DefaultContext";
import { ExecReservedSOHandler } from "@/functions/triggers/ExecReservedSOHandler";
import { generateProcessID } from "../../testing/TestHelper";

jest.mock("axios");

describe("HandleReservedSOQueue", () => {
    const context: ExtendedInvocationContext = DefaultContext;
    let storageService: StorageTableService;
    let mockDoRestAPIRequest: jest.SpyInstance;
    const serviceOrderId = generateProcessID();

    beforeEach(async () => {
        storageService = new StorageTableService(context, "ReservedSO");
        await storageService.init();

        mockDoRestAPIRequest = jest.spyOn(RestAPIClient.prototype, 'doRestAPIRequest');

        jest.clearAllMocks();
    });

    afterEach(async () => {
        mockDoRestAPIRequest.mockRestore();
        storageService = new StorageTableService(context, "ReservedSO");
        await storageService.init();
        await storageService.clear();
    });

    it("Can call API normally", async () => {
        const mockProcessCode = "000000";
        mockDoRestAPIRequest.mockResolvedValue(mockProcessCode);

        const testMessage = {
            serviceOrderId,
            restMessage: {
                requestHeader: {
                    functionType: "TEST_FUNCTION",
                },
                requestBody: {
                    testData: "test",
                },
            },
        };

        await HandleReservedSOQueue(testMessage, context);

        expect(mockDoRestAPIRequest).toHaveBeenCalledWith(
            testMessage.restMessage,
            serviceOrderId,
        );

        const entity = await storageService.getEntity("orders", serviceOrderId) as any;
        expect(entity).toBeDefined();
        expect(entity.status).toBe(ExecReservedSOHandler.STATUS_COMPLETED);
        expect(context.info).toHaveBeenCalled();
    });

    it("Failed calling API still set status to completed", async () => {
        mockDoRestAPIRequest.mockRejectedValue(new Error("API error"));
        const testMessage = {
            serviceOrderId,
            restMessage: {
                requestHeader: {
                    functionType: "TEST_FUNCTION",
                },
            },
        };
        await HandleReservedSOQueue(testMessage, context);
        expect(context.error).toHaveBeenCalled();
        const entity = await storageService.getEntity("orders", serviceOrderId) as any;
        expect(entity).toBeDefined();
        expect(entity.status).toBe(ExecReservedSOHandler.STATUS_COMPLETED);
    });

    it("not call API if already processing or completed", async () => {
        await storageService.createEntity({
            partitionKey: "orders",
            rowKey: serviceOrderId,
            status: ExecReservedSOHandler.STATUS_PROCESSING,
            timestamp: new Date().toISOString(),
        });
        mockDoRestAPIRequest.mockResolvedValue("000000");
        const testMessage = {
            serviceOrderId,
            restMessage: {
                requestHeader: {
                    functionType: "TEST_FUNCTION",
                },
            },
        };
        await HandleReservedSOQueue(testMessage, context);
        expect(mockDoRestAPIRequest).not.toHaveBeenCalled();
        const entity = await storageService.getEntity("orders", serviceOrderId) as any;
        expect(entity).toBeDefined();
        expect(entity.status).toBe(ExecReservedSOHandler.STATUS_PROCESSING);
    });

    it("should throw exception if fails parsing message", async () => {
        const invalidMessage = undefined;
        await expect(HandleReservedSOQueue(invalidMessage, context)).rejects.toThrow();
        expect(context.error).toHaveBeenCalled();
    });

    it("error if timeout", async () => {
        const timeoutError = new Error("timeout of 10000ms exceeded");
        // tslint:disable-next-line:no-string-literal
        timeoutError["code"] = "ECONNABORTED";
        (axios as any).isAxiosError = jest.fn().mockReturnValue(true);
        mockDoRestAPIRequest.mockRejectedValue(timeoutError);
        const testMessage = {
            serviceOrderId,
            restMessage: {
                requestHeader: {
                    functionType: "TEST_FUNCTION",
                },
            },
        };
        await HandleReservedSOQueue(testMessage, context);
        expect(context.error).toHaveBeenCalled();
        const entity = await storageService.getEntity("orders", serviceOrderId) as any;
        expect(entity).toBeDefined();
        expect(entity.status).toBe(ExecReservedSOHandler.STATUS_COMPLETED);
    });
});