import { useRedisMock } from "../../testing/RedisMock";
jest.mock("../../../src/database/redis", () => ({
    ...jest.requireActual("../../../src/database/redis"),
    useRedis: jest.fn().mockImplementation(useRedisMock),
}));
jest.mock("../../../src/services/notificationService", () => ({
    ...jest.requireActual("../../../src/services/notificationService"),
    NotificationService: {
        sendMessage: jest.fn(),
    },
}));
import { ServiceBusClient } from "@azure/service-bus";

import { expect, test, beforeEach } from "@jest/globals";

import "@/types/string.extension";

import DefaultContext from "../../testing/DefaultContext";
import { generateProcessID, generateSOID } from "../../testing/TestHelper";

import { CoreSwimmyStatus } from "@/constants/coreSwimmy";
import { disconnectMongo, useMongo } from "@/database/mongo";
import CoreSwimmyApiLog from "@/models/coreSwimmyApiLog";
import { SwimmyType } from "@/types/coreSwimmyApiLog";

import HandleCoreSwimmyApiRequestQueue from "@/functions/triggers/HandleCoreSwimmyApiRequestQueue";

// assume that env variable CORE_SWIMMY_API_MOCK_MODE is set to true

describe("HandleCoreSwimmyApiRequestQueue", () => {
    const testdata = {
        soId: generateSOID(),
        requestOrderId: generateProcessID(),
        tenantId: "TST000",
        tempoId: "SxxTST0001",
        kaisenNo: "08012312345",
        swimmyType: "004" as SwimmyType,
        requestParam: {
            somedata: 123,
            test: 456,
        },
        status: CoreSwimmyStatus.TOUROKU_IRAI_NOT_YET_SENT,
        jikkouchuFlg: true,
    };
    const chainedData = {
        soId: generateSOID(),
        requestOrderId: generateProcessID(),
    };
    const chainedSO = {
        haishi: {
            soId: chainedData.soId,
            requestOrderId: chainedData.requestOrderId,
            tenantId: "TST000",
            kaisenNo: "08012312399",
            swimmyType: "008" as SwimmyType,
            requestParam: {
                somedata: 123,
                test: 456,
            },
            status: CoreSwimmyStatus.TOUROKU_IRAI_NOT_YET_SENT,
            jikkouchuFlg: true,
        },
        add: {
            soId: chainedData.soId,
            requestOrderId: chainedData.requestOrderId,
            tenantId: "TSA000",
            kaisenNo: "08012312399",
            swimmyType: "007" as SwimmyType,
            requestParam: {
                somedata: 123,
                test: 456,
            },
            status: CoreSwimmyStatus.ON_HOLD,
            jikkouchuFlg: false,
        },
    };

    let sendMessagesSpy: jest.SpyInstance;

    beforeAll(async () => {
        await useMongo(DefaultContext);
    });

    afterAll(async () => {
        disconnectMongo(DefaultContext);
        jest.restoreAllMocks();
    });

    beforeEach(async () => {
        sendMessagesSpy = jest.fn();
        // mock service bus
        jest.spyOn(ServiceBusClient.prototype, "createSender").mockReturnValue({
            sendMessages: sendMessagesSpy,
        } as any);

        await CoreSwimmyApiLog.createSwimmyApiLog(testdata);
        const haishiSO = await CoreSwimmyApiLog.createSwimmyApiLog(
            chainedSO.haishi,
            { skipWaitingCheck: true },
        );
        await CoreSwimmyApiLog.createSwimmyApiLog(chainedSO.add, {
            setWaitingForId: haishiSO.id,
        });
    });

    afterEach(async () => {
        await CoreSwimmyApiLog.deleteMany({
            requestOrderId: {
                $in: [testdata.requestOrderId, chainedData.requestOrderId],
            },
        });
        // reset jest.fn counters
        jest.clearAllMocks();
        jest.resetAllMocks();
    });

    describe("OK cases", () => {
        test("should update status to TOUROKU_IRAI_RESULT_OK", async () => {
            const message = (
                await CoreSwimmyApiLog.findOne({
                    requestOrderId: testdata.requestOrderId,
                })
            ).toObject();

            expect(message.jikkouchuFlg).toEqual(true);
            expect(message.status).toEqual(
                CoreSwimmyStatus.TOUROKU_IRAI_NOT_YET_SENT,
            );

            await HandleCoreSwimmyApiRequestQueue(message, DefaultContext);

            const updated = await CoreSwimmyApiLog.findOne({
                requestOrderId: testdata.requestOrderId,
            });

            expect(updated).not.toBeNull();
            expect(updated.jikkouchuFlg).toEqual(false);
            expect(updated.status).toEqual(
                CoreSwimmyStatus.TOUROKU_IRAI_RESULT_OK,
            );
            expect(updated.responseParam).not.toBeUndefined();
            expect(updated.getResponseAt).not.toBeUndefined();
            expect(updated.getResponseAt).toBeGreaterThan(0);

            expect(updated.statusTime).toBeGreaterThan(0);
            expect(updated.statusLog).not.toBeUndefined();
            expect(Object.values(updated.statusLog)).toHaveLength(2);
            expect(Object.values(updated.statusLog)).toContain(
                CoreSwimmyStatus.TOUROKU_IRAI_RESULT_OK,
            );
            expect(updated.responseLog).not.toBeUndefined();
            expect(Object.values(updated.responseLog)).toHaveLength(1);

            expect(updated.ngReason).toBeUndefined();
            expect(updated.ngReasonLog).toBeUndefined();
        });

        test("should update status to TOUROKU_IRAI_RESULT_OK and activateTransaction", async () => {
            const message = (
                await CoreSwimmyApiLog.findOne({
                    requestOrderId: chainedData.requestOrderId,
                })
            ).toObject();

            expect(message.jikkouchuFlg).toEqual(true);
            expect(message.status).toEqual(
                CoreSwimmyStatus.TOUROKU_IRAI_NOT_YET_SENT,
            );

            // check next chained transaction status before processing
            const nextTransactionPre = await CoreSwimmyApiLog.findOne({
                requestOrderId: chainedSO.add.requestOrderId,
                swimmyType: chainedSO.add.swimmyType,
            });
            expect(nextTransactionPre).not.toBeNull();
            expect(nextTransactionPre.jikkouchuFlg).toEqual(false);
            expect(nextTransactionPre.status).toEqual(CoreSwimmyStatus.ON_HOLD);

            await HandleCoreSwimmyApiRequestQueue(message, DefaultContext);

            // 廃止 transaction
            const updated = await CoreSwimmyApiLog.findOne({
                requestOrderId: chainedSO.haishi.requestOrderId,
                swimmyType: chainedSO.haishi.swimmyType,
            });

            expect(updated).not.toBeNull();
            expect(updated.jikkouchuFlg).toEqual(false);
            expect(updated.status).toEqual(
                CoreSwimmyStatus.TOUROKU_IRAI_RESULT_OK,
            );
            expect(updated.responseParam).not.toBeUndefined();
            expect(updated.getResponseAt).not.toBeUndefined();
            expect(updated.getResponseAt).toBeGreaterThan(0);

            expect(updated.statusTime).toBeGreaterThan(0);
            expect(updated.statusLog).not.toBeUndefined();
            expect(Object.values(updated.statusLog)).toHaveLength(2);
            expect(Object.values(updated.statusLog)).toContain(
                CoreSwimmyStatus.TOUROKU_IRAI_RESULT_OK,
            );
            expect(updated.responseLog).not.toBeUndefined();
            expect(Object.values(updated.responseLog)).toHaveLength(1);

            expect(updated.ngReason).toBeUndefined();
            expect(updated.ngReasonLog).toBeUndefined();

            // 追加 transaction
            const nextTransaction = await CoreSwimmyApiLog.findOne({
                requestOrderId: chainedSO.add.requestOrderId,
                swimmyType: chainedSO.add.swimmyType,
            });

            // activation: change status to TOUROKU_IRAI_NOT_YET_SENT and send to queue (jikkouchuFlg = true)
            expect(nextTransaction).not.toBeNull();
            expect(nextTransaction.jikkouchuFlg).toEqual(true);
            expect(nextTransaction.status).toEqual(
                CoreSwimmyStatus.TOUROKU_IRAI_NOT_YET_SENT,
            );

            // should send scheduled message
            expect(sendMessagesSpy).toBeCalledTimes(1);
            expect(sendMessagesSpy).toBeCalledWith(
                expect.arrayContaining([
                    expect.objectContaining({
                        scheduledEnqueueTimeUtc: expect.any(Date),
                    }),
                ]),
            );
        });

        test("should log error if activateTransaction fails", async () => {
            jest.spyOn(
                CoreSwimmyApiLog,
                "activateWaitingTransaction",
            ).mockRejectedValue(new Error("activateTransaction failed"));

            const message = (
                await CoreSwimmyApiLog.findOne({
                    requestOrderId: chainedData.requestOrderId,
                })
            ).toObject();

            expect(message.jikkouchuFlg).toEqual(true);
            expect(message.status).toEqual(
                CoreSwimmyStatus.TOUROKU_IRAI_NOT_YET_SENT,
            );

            await HandleCoreSwimmyApiRequestQueue(message, DefaultContext);

            // 廃止 transaction
            // 廃止 transaction
            const updated = await CoreSwimmyApiLog.findOne({
                requestOrderId: chainedSO.haishi.requestOrderId,
                swimmyType: chainedSO.haishi.swimmyType,
            });

            expect(updated).not.toBeNull();
            expect(updated.jikkouchuFlg).toEqual(false);
            expect(updated.status).toEqual(
                CoreSwimmyStatus.TOUROKU_IRAI_RESULT_OK,
            );
            expect(updated.responseParam).not.toBeUndefined();
            expect(updated.getResponseAt).not.toBeUndefined();
            expect(updated.getResponseAt).toBeGreaterThan(0);

            expect(updated.statusTime).toBeGreaterThan(0);
            expect(updated.statusLog).not.toBeUndefined();
            expect(Object.values(updated.statusLog)).toHaveLength(2);
            expect(Object.values(updated.statusLog)).toContain(
                CoreSwimmyStatus.TOUROKU_IRAI_RESULT_OK,
            );
            expect(updated.responseLog).not.toBeUndefined();
            expect(Object.values(updated.responseLog)).toHaveLength(1);

            expect(updated.ngReason).toBeUndefined();
            expect(updated.ngReasonLog).toBeUndefined();

            // 追加 transaction
            const nextTransaction = await CoreSwimmyApiLog.findOne({
                requestOrderId: chainedSO.add.requestOrderId,
                swimmyType: chainedSO.add.swimmyType,
            });

            // activation not processed
            expect(nextTransaction).not.toBeNull();
            expect(nextTransaction.jikkouchuFlg).toEqual(false);
            expect(nextTransaction.status).toEqual(CoreSwimmyStatus.ON_HOLD);
            // error is logged: HandleCoreSwimmyApiRequestQueue 1x, activateTransaction 1x
            expect(DefaultContext.error).toBeCalledTimes(2);
        });
    });

    describe("NG cases", () => {
        test("should update status to STOPPING_FOR_ERROR if requestParam is empty", async () => {
            const message = (
                await CoreSwimmyApiLog.findOne({
                    requestOrderId: testdata.requestOrderId,
                })
            ).toObject();
            message.requestParam = null;

            expect(message.jikkouchuFlg).toEqual(true);
            expect(message.status).toEqual(
                CoreSwimmyStatus.TOUROKU_IRAI_NOT_YET_SENT,
            );

            await HandleCoreSwimmyApiRequestQueue(message, DefaultContext);

            const updated = await CoreSwimmyApiLog.findOne({
                requestOrderId: testdata.requestOrderId,
            });

            expect(updated).not.toBeNull();
            expect(updated.jikkouchuFlg).toEqual(false);
            expect(updated.status).toEqual(CoreSwimmyStatus.STOPPING_FOR_ERROR);
            expect(updated.responseParam).toBeUndefined();
            expect(updated.getResponseAt).toBeUndefined();

            expect(updated.statusTime).toBeGreaterThan(0);
            expect(updated.statusLog).not.toBeUndefined();
            expect(Object.values(updated.statusLog)).toHaveLength(2);
            expect(Object.values(updated.statusLog)).toContain(
                CoreSwimmyStatus.STOPPING_FOR_ERROR,
            );
            expect(updated.responseLog).not.toBeUndefined();
            expect(Object.values(updated.responseLog)).toHaveLength(0);

            expect(updated.ngReason).not.toBeUndefined();
            expect(typeof updated.ngReason).toBe("string");
            expect(updated.ngReason).not.toEqual("");
            expect(updated.ngReasonLog).not.toBeUndefined();
            expect(Object.values(updated.ngReasonLog)).toHaveLength(1);
        });

        const RequestFailureCases = [
            ["API server returns 503 Service Unavailable", "/test/http-503"],
            ["API server returns 500 Internal Service Error", "/test/http-500"],
            ["API server returns 409 Conflict", "/test/http-409"],
            ["API server response is empty", "/test/resp-empty"],
            [
                "API server returns NG HTTP status code (Axios error)",
                "/test/http-error",
            ],
            ["error thrown during API call", "/test/throw-error"],
        ];

        test.each(RequestFailureCases)(
            "should update status to STOPPING_FOR_ERROR if %s",
            async (message, path) => {
                const endpoint_path = process.env.CORE_SWIMMY_API_ENDPOINT_PATH;
                try {
                    process.env.CORE_SWIMMY_API_ENDPOINT_PATH = path;
                    const message = (
                        await CoreSwimmyApiLog.findOne({
                            requestOrderId: testdata.requestOrderId,
                        })
                    ).toObject();

                    expect(message.jikkouchuFlg).toEqual(true);
                    expect(message.status).toEqual(
                        CoreSwimmyStatus.TOUROKU_IRAI_NOT_YET_SENT,
                    );

                    await HandleCoreSwimmyApiRequestQueue(
                        message,
                        DefaultContext,
                    );

                    const updated = await CoreSwimmyApiLog.findOne({
                        requestOrderId: testdata.requestOrderId,
                    });

                    expect(updated).not.toBeNull();
                    expect(updated.jikkouchuFlg).toEqual(false);
                    expect(updated.status).toEqual(
                        CoreSwimmyStatus.STOPPING_FOR_ERROR,
                    );
                    expect(updated.responseParam).toBeUndefined();
                    expect(updated.getResponseAt).toBeUndefined();

                    expect(updated.statusTime).toBeGreaterThan(0);
                    expect(updated.statusLog).not.toBeUndefined();
                    expect(Object.values(updated.statusLog)).toHaveLength(2);
                    expect(Object.values(updated.statusLog)).toContain(
                        CoreSwimmyStatus.STOPPING_FOR_ERROR,
                    );
                    expect(updated.responseLog).not.toBeUndefined();
                    expect(Object.values(updated.responseLog)).toHaveLength(0);

                    expect(updated.ngReason).not.toBeUndefined();
                    expect(typeof updated.ngReason).toBe("string");
                    expect(updated.ngReason).not.toEqual("");
                    expect(updated.ngReasonLog).not.toBeUndefined();
                    expect(Object.values(updated.ngReasonLog)).toHaveLength(1);
                } finally {
                    process.env.CORE_SWIMMY_API_ENDPOINT_PATH = endpoint_path;
                }
            },
        );

        // server should return response object
        const ResultNGCases = [
            ["API server returns 400 Bad Request", "/test/http-400"],
            ["API server returns 200 but with NG response", "/test/resp-error"],
        ];

        test.each(ResultNGCases)(
            "should update status to TOUROKU_IRAI_RESULT_NG if %s",
            async (message, path) => {
                const endpoint_path = process.env.CORE_SWIMMY_API_ENDPOINT_PATH;
                try {
                    // set endpoint to error mock endpoint
                    process.env.CORE_SWIMMY_API_ENDPOINT_PATH = path;
                    const message = (
                        await CoreSwimmyApiLog.findOne({
                            requestOrderId: testdata.requestOrderId,
                        })
                    ).toObject();

                    expect(message.jikkouchuFlg).toEqual(true);
                    expect(message.status).toEqual(
                        CoreSwimmyStatus.TOUROKU_IRAI_NOT_YET_SENT,
                    );

                    await HandleCoreSwimmyApiRequestQueue(
                        message,
                        DefaultContext,
                    );

                    const updated = await CoreSwimmyApiLog.findOne({
                        requestOrderId: testdata.requestOrderId,
                    });

                    expect(updated).not.toBeNull();
                    expect(updated.jikkouchuFlg).toEqual(false);
                    expect(updated.status).toEqual(
                        CoreSwimmyStatus.TOUROKU_IRAI_RESULT_NG,
                    );
                    expect(updated.responseParam).not.toBeUndefined();
                    expect(updated.getResponseAt).not.toBeUndefined();
                    expect(updated.getResponseAt).toBeGreaterThan(0);

                    expect(updated.statusTime).toBeGreaterThan(0);
                    expect(updated.statusLog).not.toBeUndefined();
                    expect(Object.values(updated.statusLog)).toHaveLength(2);
                    expect(Object.values(updated.statusLog)).toContain(
                        CoreSwimmyStatus.TOUROKU_IRAI_RESULT_NG,
                    );
                    expect(updated.responseLog).not.toBeUndefined();
                    expect(Object.values(updated.responseLog)).toHaveLength(1);

                    expect(updated.ngReason).not.toBeUndefined();
                    expect(typeof updated.ngReason).toBe("string");
                    expect(updated.ngReason).not.toEqual("");
                    expect(updated.ngReasonLog).not.toBeUndefined();
                    expect(Object.values(updated.ngReasonLog)).toHaveLength(1);
                } finally {
                    process.env.CORE_SWIMMY_API_ENDPOINT_PATH = endpoint_path;
                }
            },
        );

        test("should throw error if message is empty", async () => {
            await HandleCoreSwimmyApiRequestQueue(null, DefaultContext);
            // only if DefaultContext is jest.Mock type
            if (jest.isMockFunction(DefaultContext.error)) {
                expect(DefaultContext.error).toBeCalledTimes(1);
                // console.log("mock ok");
            } else {
                // since we can't check if DefaultContext.error is called always mark this as true
                expect(true).toBeTruthy();
                // console.log("fallback ok");
            }
        });
    });
});
