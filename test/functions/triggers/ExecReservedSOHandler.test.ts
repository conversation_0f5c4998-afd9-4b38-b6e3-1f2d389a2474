import { expect, jest, test, describe, beforeEach, afterEach } from "@jest/globals";
import { ExecReservedSOHandler } from "@/functions/triggers/ExecReservedSOHandler";
import StorageTableService from "@/services/storageTableService";
import ServiceOrdersEntity from "@/core/entity/ServiceOrdersEntity";
import * as serviceBusModule from "@/services/servicebus";  // Change import to import entire module
import { addMinutes } from "date-fns";
import AppConfig from "@/appconfig";
import { usePsql } from "@/database/psql";
import { Sequelize } from "sequelize";
import { generateProcessID } from "../../testing/TestHelper";
import { disconnectMongo } from "@/database/mongo";
import DefaultContext from "../../testing/DefaultContext";
import config from "config";

describe("ExecReservedSOHandler", () => {
    const context = DefaultContext;
    let handler: ExecReservedSOHandler;
    let sequelize: Sequelize;
    const serviceOrderId = generateProcessID();
    let mockEnqueueMessage: jest.SpiedFunction<any>;

    beforeEach(async () => {
        handler = new ExecReservedSOHandler(context);
        await AppConfig.loadCoreMvnoConfig(context);
        sequelize = await usePsql();

        // Setup spy before each test
        mockEnqueueMessage = jest.spyOn(serviceBusModule, 'default');
        mockEnqueueMessage.mockImplementation(
            async (context, connectionString, queueName, id, payload) => {
                return Promise.resolve();
            }
        )
    });

    afterEach(async () => {
        mockEnqueueMessage.mockRestore();  // Restore the original implementation
        await ServiceOrdersEntity.destroy({ where: {
                serviceOrderId
        } });

        const storageTable = new StorageTableService(context, "ReservedSO");
        await storageTable.init();
        await storageTable.clear();
    });

    afterAll(async () => {
        await sequelize.close();
        await disconnectMongo(context);
    })

    describe("checkDoubleExec", () => {
        afterEach(async () => {
            const storageTable = new StorageTableService(context, "ExecReservedSOPid");
            const tableClient = await storageTable.init();
            await storageTable.clear();
        })
        test("should allow execution if last run was more than 5 minutes ago", async () => {
            const storageTable = new StorageTableService(context, "ExecReservedSOPid");
            const tableClient = await storageTable.init();

            await tableClient.createEntity({
                partitionKey: "ExecReservedSO",
                rowKey: `${new Date(Date.now() - 6 * 60 * 1000).toISOString()}`,
                data: JSON.stringify({ pid: 12345, now: new Date().toISOString() }),
            });

            await handler.execReservedSO();

            expect(context.warn).not.toHaveBeenCalled();
            expect(context.error).not.toHaveBeenCalled();
        });
        test("should prevent double execution", async () => {
            const storageTable = new StorageTableService(context, "ExecReservedSOPid");
            const tableClient = await storageTable.init();

            await tableClient.createEntity({
                partitionKey: "ExecReservedSO",
                rowKey: `${new Date().toISOString()}`,
                data: JSON.stringify({ pid: 12345, now: new Date().toISOString() }),
            });

            const result = await handler.execReservedSO();

            expect(context.warn).toHaveBeenCalled();
            expect(context.error).not.toHaveBeenCalled();
        });
    });

    describe("getReservedServiceOrders", () => {
        beforeEach(async () => {
            await ServiceOrdersEntity.create({
                serviceOrderId,
                orderStatus: "予約中",
                reserveDate: addMinutes(new Date(), -30),
                tenantId: "TST000",
                lineId: "08010140000",
                orderType: "プラン変更",
            });
        });

        test("should fetch ready service orders", async () => {
            await handler.execReservedSO();

            expect(context.error).not.toHaveBeenCalled();
            expect(context.info).toHaveBeenCalled();
            expect(mockEnqueueMessage).toHaveBeenCalled();
        });

        test("should not fetch future service orders", async () => {
            await ServiceOrdersEntity.update(
                { reserveDate: addMinutes(new Date(), 30) },
                { where: { orderStatus: "予約中" } }
            );

            await handler.execReservedSO();

            expect(mockEnqueueMessage).not.toHaveBeenCalled();
        });

        test("should not fetch non-pending service orders", async () => {
            await ServiceOrdersEntity.update(
                { orderStatus: "完了" },
                { where: { orderStatus: "予約中" } }
            );

            await handler.execReservedSO();

            expect(mockEnqueueMessage).not.toHaveBeenCalled();
        });
    });

    describe("sendToServiceBus", () => {
        let reservedSOStorageTable;

        beforeEach(async () => {
            reservedSOStorageTable = new StorageTableService(context, "ReservedSO");
            await reservedSOStorageTable.init();
        });

        test("should not send already processing orders", async () => {
            await ServiceOrdersEntity.create({
                serviceOrderId,
                orderStatus: "予約中",
                reserveDate: addMinutes(new Date(), -30),
                tenantId: "TST000",
                lineId: "08010140000",
                orderType: "プラン変更",
            });

            await reservedSOStorageTable.createEntity({
                partitionKey: "orders",
                rowKey: serviceOrderId,
                status: ExecReservedSOHandler.STATUS_PROCESSING,
            });

            await handler.execReservedSO();

            expect(mockEnqueueMessage).not.toHaveBeenCalled();
        });

        test("should send new orders to queue", async () => {
            await ServiceOrdersEntity.create({
                serviceOrderId,
                orderStatus: "予約中",
                reserveDate: addMinutes(new Date(), -30),
                tenantId: "TST000",
                lineId: "08010140000",
                orderType: "プラン変更",
            });

            await handler.execReservedSO();

            const coreConfig = AppConfig.getCoreConfig(context);
            const queueName = coreConfig.RESERVED_SO.SERVICE_BUS_QUEUE_NAME

            expect(mockEnqueueMessage).toHaveBeenCalledWith(
                context,
                expect.anything(),
                queueName,
                serviceOrderId,
                expect.anything()
            );
        });
    });

    describe("TCP regulation", () => {
        test("should not process during TCP regulation time", async () => {
            // Mock TCP regulation check to return true
            jest.spyOn(handler as any, 'isTcpRegulation').mockImplementation(() => true);

            await handler.execReservedSO();

            expect(context.debug).toHaveBeenCalledWith("TCP out of service.");
            expect(mockEnqueueMessage).not.toHaveBeenCalled();
        });
    });
});