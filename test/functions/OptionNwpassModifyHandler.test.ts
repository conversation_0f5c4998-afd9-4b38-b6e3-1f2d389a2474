import { expect, test, beforeEach } from "@jest/globals";

const enqueueMessage = jest.fn();
jest.mock("../../src/services/servicebus", () => ({
    __esModule: true,
    default: enqueueMessage,
}));

import { addDays, addMinutes, formatDate, parse } from "date-fns";
import { Sequelize } from "sequelize";

import "@/types/string.extension";

import {
    describeWithDB,
    generateProcessID,
    generateSOID,
} from "../testing/TestHelper";
import DefaultContext from "../testing/DefaultContext";
import DefaultRequest from "../testing/DefaultRequest";

import { OptionNwpassModifyHandler } from "@/functions/OptionNwpassModifyHandler";

import AppConfig from "@/appconfig";
import ResultCdConstants from "@/core/constant/ResultCdConstants";
import ResponseHeader from "@/core/dto/ResponseHeader";
import { LinesEntity } from "@/core/entity/LinesEntity";
import LineTenantsEntity from "@/core/entity/LineTenantsEntity";
import ServiceOrdersEntity from "@/core/entity/ServiceOrdersEntity";
import { TenantNnumbersEntity } from "@/core/entity/TenantNnumbersEntity";

import CoreSwimmyApiLog from "@/models/coreSwimmyApiLog";

import { usePsql } from "@/database/psql";
import { disconnectMongo, useMongo } from "@/database/mongo";

import TBanHelper from "@/helpers/tBanHelper";

describeWithDB(
    "54 回線オプション_NW暗証番号変更 /MVNO/api/V100/Lines/lineop",
    () => {
        let sequelize: Sequelize;
        const testdata = {
            lineOK: "08024043011",
            lineNotExists: "08024043012",
            lineStatusNG: "08024043013",
            lineNotLinked: "08024043014",
            lineWithAbolish: "08024043015",
        };
        const abolishSo = generateProcessID();
        const lineUpdatedAt = addDays(new Date(), -1);

        const request = {
            requestHeader: {
                sequenceNo: "1234567",
                senderSystemId: "0001",
                apiKey: "",
                functionType: "54",
            },
            targetSoId: generateSOID(),
            tenantId: "OPF000",
            targetTenantId: "TST000",
            lineNo: testdata.lineOK,
            pinChange: "1",
            intlRoaming: {
                changeCode: "1", // 追加
                beforeOpId: "",
                afterOpId: "AO147",
            },
            voicemail: {
                changeCode: "0",
            },
            callWaiting: {
                changeCode: "0",
            },
            intlCall: {
                changeCode: "2", // 変更
                beforeOpId: "AO122",
                afterOpId: "AO123",
            },
            forwarding: {
                changeCode: "3", // 削除
                beforeOpId: "AO144",
                afterOpId: "",
            },
            intlForwarding: {
                changeCode: "0",
            },
            csvUnnecessaryFlag: "0",
        };

        const responseHeader = {
            sequenceNo: request.requestHeader.sequenceNo,
            receivedDate: formatDate(new Date(), "yyyy/MM/dd HH:mm:ss"),
            processCode: ResultCdConstants.CODE_000000,
            apiProcessID: generateProcessID(),
        } as ResponseHeader;

        let nnumber = "";

        beforeAll(async () => {
            await AppConfig.loadCoreMvnoConfig(DefaultContext);
            sequelize = await usePsql();
            await useMongo(DefaultContext);

            await ServiceOrdersEntity.create({
                serviceOrderId: abolishSo,
                tenantId: request.targetTenantId,
                lineId: testdata.lineWithAbolish,
                orderStatus: "完了",
                orderType: "回線廃止",
                reserveDate: null,
                execDate: addMinutes(new Date(), -5),
                functionType: "52",
            });

            const tenantNNo = await TenantNnumbersEntity.findOne({
                where: {
                    tenantId: request.targetTenantId,
                },
            });
            if (!tenantNNo) {
                throw new Error("Tenant Nnumber not found");
            }
            nnumber = tenantNNo.nnumber;
        });

        afterAll(async () => {
            await ServiceOrdersEntity.destroy({
                where: {
                    serviceOrderId: abolishSo,
                },
            });
            await sequelize.close();
            await disconnectMongo(DefaultContext);
        });

        beforeEach(async () => {
            // reset mock
            (enqueueMessage as jest.Mock).mockReset();

            DefaultContext.jsonBody = Object.assign({}, request);
            DefaultContext.responseHeader = Object.assign({}, responseHeader);

            // insert line numbers
            await Promise.all([
                LinesEntity.create({
                    lineId: testdata.lineOK,
                    tenantId: request.targetTenantId,
                    lineStatus: "01",
                    simFlag: false,
                    updatedAt: lineUpdatedAt,
                    nnumber,
                }),
                LinesEntity.create({
                    lineId: testdata.lineStatusNG,
                    tenantId: request.targetTenantId,
                    lineStatus: "03",
                    simFlag: false,
                    updatedAt: lineUpdatedAt,
                    nnumber,
                }),
                LinesEntity.create({
                    lineId: testdata.lineNotLinked,
                    tenantId: request.targetTenantId,
                    lineStatus: "01",
                    simFlag: false,
                    updatedAt: lineUpdatedAt,
                    // nnumber, // no link
                }),
                LinesEntity.create({
                    lineId: testdata.lineWithAbolish,
                    tenantId: request.targetTenantId,
                    lineStatus: "01",
                    simFlag: false,
                    updatedAt: lineUpdatedAt,
                    lineActDate: lineUpdatedAt,
                    nnumber,
                }),
            ]);
            // register to line tenant
            await Promise.all(
                [
                    testdata.lineOK,
                    testdata.lineStatusNG,
                    testdata.lineWithAbolish,
                ].map(async (lineId) => {
                    await LineTenantsEntity.create({
                        lineId,
                        tenantId: request.targetTenantId,
                    });
                }),
            );
        });

        afterEach(async () => {
            await LineTenantsEntity.destroy({
                where: {
                    lineId: [
                        testdata.lineOK,
                        testdata.lineStatusNG,
                        testdata.lineNotLinked,
                        testdata.lineWithAbolish,
                    ],
                },
            });
            await Promise.all([
                LinesEntity.destroy({
                    where: {
                        lineId: Object.values(testdata),
                    },
                }),
                ServiceOrdersEntity.destroy({
                    where: {
                        serviceOrderId: responseHeader.apiProcessID,
                    },
                }),
                CoreSwimmyApiLog.deleteMany({
                    requestOrderId: responseHeader.apiProcessID,
                }),
            ]);
            jest.restoreAllMocks();
            jest.clearAllMocks();
        });

        // helpers
        const checkServiceOrder = async (soStatus: string) => {
            const serviceOrder = await ServiceOrdersEntity.findOne({
                where: {
                    serviceOrderId: responseHeader.apiProcessID,
                },
            });

            // new service order is created
            expect(serviceOrder).not.toBeNull();
            expect(serviceOrder.tenantId).toEqual(
                DefaultContext.jsonBody.targetTenantId,
            );
            expect(serviceOrder.lineId).toEqual(DefaultContext.jsonBody.lineNo);
            expect(serviceOrder.orderStatus).toEqual(soStatus);
            expect(serviceOrder.orderType).toEqual(
                "回線オプション_NW暗証番号変更",
            );
            expect(serviceOrder.reserveDate).toBeNull();
        };

        /**
         * check whether SwimmyApiLog exists and Service Bus is called
         */
        const checkCoreSwimmyApiLog = async (
            apiProcessID: string,
            exists: boolean,
        ) => {
            const apiLog = await CoreSwimmyApiLog.findOne({
                requestOrderId: apiProcessID,
            });
            if (exists) {
                expect(apiLog).not.toBeNull();
                expect(enqueueMessage).toBeCalledTimes(1);
            } else {
                expect(apiLog).toBeNull();
                expect(enqueueMessage).not.toBeCalled();
            }
        };

        const createOption = (
            changeCode: "0" | "1" | "2" | "3",
            beforeOpId: string = null,
            afterOpId: string = null,
        ) => ({
            changeCode,
            beforeOpId,
            afterOpId,
        });

        describe("OK (CODE_000000)", () => {
            const modifyCases = [
                [
                    "国際ローミング利用限度額 2:変更",
                    { intlRoaming: createOption("2", "AO100", "AO147") },
                ],
                [
                    "国際ローミング利用限度額 3:削除",
                    { intlRoaming: createOption("3", "AO100", "") },
                ],
                [
                    "国際ローミング利用限度額 0:削除",
                    { intlRoaming: createOption("0") },
                ],
                [
                    "留守電話 1:追加",
                    { voicemail: createOption("1", "", "AO147") },
                ],
                [
                    "留守電話 2:変更",
                    { voicemail: createOption("2", "AO100", "AO147") },
                ],
                [
                    "留守電話 3:削除",
                    { voicemail: createOption("3", "AO100", "") },
                ],
                ["留守電話 0:削除", { voicemail: createOption("0") }],
                [
                    "キャッチホン 1:追加",
                    { callWaiting: createOption("1", "", "AO147") },
                ],
                [
                    "キャッチホン 2:変更",
                    { callWaiting: createOption("2", "AO100", "AO147") },
                ],
                [
                    "キャッチホン 3:削除",
                    { callWaiting: createOption("3", "AO100", "") },
                ],
                ["キャッチホン 0:削除", { callWaiting: createOption("0") }],
                [
                    "国際電話 3:削除",
                    { intlCall: createOption("3", "AO100", "") },
                ],
                ["国際電話 0:削除", { intlCall: createOption("0") }],
                [
                    "転送でんわ 1:追加",
                    { forwarding: createOption("1", "", "AO147") },
                ],
                [
                    "転送でんわ 2:変更",
                    { forwarding: createOption("2", "AO100", "AO147") },
                ],
                [
                    "転送でんわ 3:削除",
                    { forwarding: createOption("3", "AO100", "") },
                ],
                ["転送でんわ 0:削除", { forwarding: createOption("0") }],
                [
                    "国際着信転送 1:追加",
                    { intlForwarding: createOption("1", "", "AO147") },
                ],
                [
                    "国際着信転送 2:変更",
                    { intlForwarding: createOption("2", "AO100", "AO147") },
                ],
                [
                    "国際着信転送 3:削除",
                    { intlForwarding: createOption("3", "AO100", "") },
                ],
                ["国際着信転送 0:削除", { intlForwarding: createOption("0") }],
            ];

            test("should return CODE_000000 when there is no error", async () => {
                const result = await OptionNwpassModifyHandler.Handler(
                    DefaultRequest,
                    DefaultContext,
                );
                expect(typeof result).toBe("object");
                expect(result).toHaveProperty("jsonBody");
                expect(result.jsonBody).toHaveProperty("responseHeader");
                expect(result.jsonBody.responseHeader.apiProcessID).toEqual(
                    DefaultContext.responseHeader.apiProcessID,
                );
                expect(result.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_000000,
                );
                expect(result.jsonBody.responseHeader.sequenceNo).toEqual(
                    DefaultContext.responseHeader.sequenceNo,
                );
                expect(result.jsonBody.responseHeader.receivedDate).toEqual(
                    DefaultContext.responseHeader.receivedDate,
                );

                await checkServiceOrder("完了");
                const line = await LinesEntity.findOne({
                    where: {
                        lineId: DefaultContext.jsonBody.lineNo,
                    },
                });
                expect(line).not.toBeNull();
                // should be equal to response received date (updated)
                expect(line.updatedAt.getTime()).toEqual(
                    parse(
                        result.jsonBody.responseHeader.receivedDate,
                        "yyyy/MM/dd HH:mm:ss",
                        new Date(),
                    ).getTime(),
                );

                await checkCoreSwimmyApiLog(responseHeader.apiProcessID, true);
            });

            test("should return CODE_000000 when there is no error (csvUnneccessaryFlag = 0 number)", async () => {
                DefaultContext.jsonBody.csvUnnecessaryFlag = 0;
                const result = await OptionNwpassModifyHandler.Handler(
                    DefaultRequest,
                    DefaultContext,
                );
                expect(typeof result).toBe("object");
                expect(result).toHaveProperty("jsonBody");
                expect(result.jsonBody).toHaveProperty("responseHeader");
                expect(result.jsonBody.responseHeader.apiProcessID).toEqual(
                    DefaultContext.responseHeader.apiProcessID,
                );
                expect(result.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_000000,
                );
                expect(result.jsonBody.responseHeader.sequenceNo).toEqual(
                    DefaultContext.responseHeader.sequenceNo,
                );
                expect(result.jsonBody.responseHeader.receivedDate).toEqual(
                    DefaultContext.responseHeader.receivedDate,
                );

                await checkServiceOrder("完了");
                const line = await LinesEntity.findOne({
                    where: {
                        lineId: DefaultContext.jsonBody.lineNo,
                    },
                });
                expect(line).not.toBeNull();
                // should be equal to response received date (updated)
                expect(line.updatedAt.getTime()).toEqual(
                    parse(
                        result.jsonBody.responseHeader.receivedDate,
                        "yyyy/MM/dd HH:mm:ss",
                        new Date(),
                    ).getTime(),
                );

                await checkCoreSwimmyApiLog(responseHeader.apiProcessID, true);
            });

            test.each(modifyCases)(
                "should return CODE_000000 for %s",
                async (title, option: any) => {
                    DefaultContext.jsonBody = Object.assign(
                        {},
                        request,
                        option,
                    );
                    jest.spyOn(
                        TBanHelper,
                        "getLineOptionTBan",
                    ).mockImplementation((lineOption: string) => {
                        return Promise.resolve(`T00${lineOption}`);
                    });

                    const result = await OptionNwpassModifyHandler.Handler(
                        DefaultRequest,
                        DefaultContext,
                    );

                    expect(typeof result).toBe("object");
                    expect(result).toHaveProperty("jsonBody");
                    expect(result.jsonBody).toHaveProperty("responseHeader");
                    expect(result.jsonBody.responseHeader.apiProcessID).toEqual(
                        DefaultContext.responseHeader.apiProcessID,
                    );
                    expect(result.jsonBody.responseHeader.processCode).toEqual(
                        ResultCdConstants.CODE_000000,
                    );
                    expect(result.jsonBody.responseHeader.sequenceNo).toEqual(
                        DefaultContext.responseHeader.sequenceNo,
                    );
                    expect(result.jsonBody.responseHeader.receivedDate).toEqual(
                        DefaultContext.responseHeader.receivedDate,
                    );

                    await checkServiceOrder("完了");
                    const line = await LinesEntity.findOne({
                        where: {
                            lineId: DefaultContext.jsonBody.lineNo,
                        },
                    });
                    expect(line).not.toBeNull();
                    // should be equal to response received date (updated)
                    expect(line.updatedAt.getTime()).toEqual(
                        parse(
                            result.jsonBody.responseHeader.receivedDate,
                            "yyyy/MM/dd HH:mm:ss",
                            new Date(),
                        ).getTime(),
                    );

                    await checkCoreSwimmyApiLog(
                        responseHeader.apiProcessID,
                        true,
                    );
                },
            );

            test("should not create CoreSwimmyApiLog if csvUnneccessaryFlag is 1", async () => {
                DefaultContext.jsonBody.csvUnnecessaryFlag = "1";
                const result = await OptionNwpassModifyHandler.Handler(
                    DefaultRequest,
                    DefaultContext,
                );
                expect(typeof result).toBe("object");
                expect(result).toHaveProperty("jsonBody");
                expect(result.jsonBody).toHaveProperty("responseHeader");
                expect(result.jsonBody.responseHeader.apiProcessID).toEqual(
                    DefaultContext.responseHeader.apiProcessID,
                );
                expect(result.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_000000,
                );
                expect(result.jsonBody.responseHeader.sequenceNo).toEqual(
                    DefaultContext.responseHeader.sequenceNo,
                );
                expect(result.jsonBody.responseHeader.receivedDate).toEqual(
                    DefaultContext.responseHeader.receivedDate,
                );

                await checkServiceOrder("完了");
                const line = await LinesEntity.findOne({
                    where: {
                        lineId: DefaultContext.jsonBody.lineNo,
                    },
                });
                expect(line).not.toBeNull();
                // should be equal to response received date (updated)
                expect(line.updatedAt.getTime()).toEqual(
                    parse(
                        result.jsonBody.responseHeader.receivedDate,
                        "yyyy/MM/dd HH:mm:ss",
                        new Date(),
                    ).getTime(),
                );

                await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
            });

            test("should not create CoreSwimmyApiLog if csvUnneccessaryFlag is 1 (number)", async () => {
                DefaultContext.jsonBody.csvUnnecessaryFlag = 1;
                const result = await OptionNwpassModifyHandler.Handler(
                    DefaultRequest,
                    DefaultContext,
                );
                expect(typeof result).toBe("object");
                expect(result).toHaveProperty("jsonBody");
                expect(result.jsonBody).toHaveProperty("responseHeader");
                expect(result.jsonBody.responseHeader.apiProcessID).toEqual(
                    DefaultContext.responseHeader.apiProcessID,
                );
                expect(result.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_000000,
                );
                expect(result.jsonBody.responseHeader.sequenceNo).toEqual(
                    DefaultContext.responseHeader.sequenceNo,
                );
                expect(result.jsonBody.responseHeader.receivedDate).toEqual(
                    DefaultContext.responseHeader.receivedDate,
                );

                await checkServiceOrder("完了");
                const line = await LinesEntity.findOne({
                    where: {
                        lineId: DefaultContext.jsonBody.lineNo,
                    },
                });
                expect(line).not.toBeNull();
                // should be equal to response received date (updated)
                expect(line.updatedAt.getTime()).toEqual(
                    parse(
                        result.jsonBody.responseHeader.receivedDate,
                        "yyyy/MM/dd HH:mm:ss",
                        new Date(),
                    ).getTime(),
                );

                await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
            });
        });

        describe("NG (CODE_54xxxx)", () => {
            test("should return CODE_540101 if tenant ID is not Front Tenant", async () => {
                DefaultContext.jsonBody.tenantId = "TST001";
                const result = await OptionNwpassModifyHandler.Handler(
                    DefaultRequest,
                    DefaultContext,
                );
                expect(typeof result).toBe("object");
                expect(result).toHaveProperty("jsonBody");
                expect(result.jsonBody).toHaveProperty("responseHeader");
                expect(result.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_540101,
                );

                await checkServiceOrder("失敗");
                const line = await LinesEntity.findOne({
                    where: {
                        lineId: DefaultContext.jsonBody.lineNo,
                    },
                });
                expect(line).not.toBeNull();
                expect(line.updatedAt.getTime()).toEqual(
                    lineUpdatedAt.getTime(),
                );

                await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
            });

            test("should return CODE_540102 if line does not exist", async () => {
                DefaultContext.jsonBody.lineNo = testdata.lineNotExists;
                const result = await OptionNwpassModifyHandler.Handler(
                    DefaultRequest,
                    DefaultContext,
                );
                expect(typeof result).toBe("object");
                expect(result).toHaveProperty("jsonBody");
                expect(result.jsonBody).toHaveProperty("responseHeader");
                expect(result.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_540102,
                );

                await checkServiceOrder("失敗");
                const line = await LinesEntity.findOne({
                    where: {
                        lineId: DefaultContext.jsonBody.lineNo,
                    },
                });
                expect(line).toBeNull();

                await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
            });

            test("should return CODE_540102 if line status is not 01", async () => {
                DefaultContext.jsonBody.lineNo = testdata.lineStatusNG;
                const result = await OptionNwpassModifyHandler.Handler(
                    DefaultRequest,
                    DefaultContext,
                );
                expect(typeof result).toBe("object");
                expect(result).toHaveProperty("jsonBody");
                expect(result.jsonBody).toHaveProperty("responseHeader");
                expect(result.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_540102,
                );

                await checkServiceOrder("失敗");
                const line = await LinesEntity.findOne({
                    where: {
                        lineId: DefaultContext.jsonBody.lineNo,
                    },
                });
                expect(line).not.toBeNull();
                expect(line.updatedAt.getTime()).toEqual(
                    lineUpdatedAt.getTime(),
                );

                await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
            });

            test("should return CODE_540103 if line is not linked to tenant", async () => {
                DefaultContext.jsonBody.lineNo = testdata.lineNotLinked;
                const result = await OptionNwpassModifyHandler.Handler(
                    DefaultRequest,
                    DefaultContext,
                );
                expect(typeof result).toBe("object");
                expect(result).toHaveProperty("jsonBody");
                expect(result.jsonBody).toHaveProperty("responseHeader");
                expect(result.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_540103,
                );

                await checkServiceOrder("失敗");
                const line = await LinesEntity.findOne({
                    where: {
                        lineId: DefaultContext.jsonBody.lineNo,
                    },
                });
                expect(line).not.toBeNull();
                expect(line.updatedAt.getTime()).toEqual(
                    lineUpdatedAt.getTime(),
                );

                await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
            });

            test("should return CODE_540104 if line has an ongoing abolish order", async () => {
                DefaultContext.jsonBody.lineNo = testdata.lineWithAbolish;
                const result = await OptionNwpassModifyHandler.Handler(
                    DefaultRequest,
                    DefaultContext,
                );
                expect(typeof result).toBe("object");
                expect(result).toHaveProperty("jsonBody");
                expect(result.jsonBody).toHaveProperty("responseHeader");
                expect(result.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_540104,
                );

                await checkServiceOrder("失敗");
                const line = await LinesEntity.findOne({
                    where: {
                        lineId: DefaultContext.jsonBody.lineNo,
                    },
                });
                expect(line).not.toBeNull();
                expect(line.updatedAt.getTime()).toEqual(
                    lineUpdatedAt.getTime(),
                );

                await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
            });
        });

        test("should return null", async () => {
            const result = OptionNwpassModifyHandler.Meta.getOrderType(request);
            expect(result).toBeNull();
        });
        // TODO add more test cases
    },
);
