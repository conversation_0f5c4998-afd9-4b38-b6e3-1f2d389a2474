
import { expect, test, beforeEach, describe, jest } from "@jest/globals";
import { addMinutes, formatDate } from "date-fns";
import { ConnectionTimedOutError, Sequelize } from "sequelize";
import { describeWithDB, generateProcessID, generateSOID } from "../testing/TestHelper";
import { usePsql } from "@/database/psql";
import { disconnectMongo, useMongo } from "@/database/mongo";
import ResultCdConstants from "@/core/constant/ResultCdConstants";
import DefaultContext from "../testing/DefaultContext";
import DefaultRequest from "../testing/DefaultRequest";
import defaultContext from "../testing/DefaultContext";
import { LinesEntity } from "@/core/entity/LinesEntity";
import LineTenantsEntity from "@/core/entity/LineTenantsEntity";
import defaultRequest from "../testing/DefaultRequest";
import { LineGroupInfoAcquisitionHandler } from "@/functions/LineGroupInfoAcquisitionHandler";
import { HttpResponseInit } from "@azure/functions";
import MvnoUtil from "@/core/common/MvnoUtil";
import { TenantsEntity } from "@/core/entity/TenantsEntity";
import APICommonDAO from "@/core/dao/APICommonDAO";
import APILinesDAO from "@/core/dao/APILinesDAO";
import PlansEntity from "@/core/entity/PlansEntity";
import CheckUtil from "@/core/common/CheckUtil";
import LineGroupInfoAcquisitionService from "@/core/service/impl/LineGroupInfoAcquisitionService";
import LineGroupsEntity from "@/core/entity/LineGroupsEntity";
import Check from "@/core/common/Check";
import APILinesGroupDAO from "@/core/dao/ApiLinesGroupDAO";
import LineLineGroupsEntity from "@/core/entity/LineLineGroupsEntity";
import AppConfig from "@/appconfig";

describeWithDB("08 回線グループ基本情報参照 /MVNO/api/v100/LinesGroup/info", () => {
    Object.defineProperty(LineGroupInfoAcquisitionHandler, "apDbRetryInterval", { value: 0.01 }); // 10ms
    let sequelize: Sequelize;

    const testdata = {
        tenantId: "TST000",
        tenantCocnId: "CON000",
    };

    const request = {
        requestHeader: {
            sequenceNo: "1234567",
            senderSystemId: "0001",
            apiKey: "",
            functionType: "08",
            receivedDate: formatDate(new Date(), "yyyy/MM/dd HH:mm:ss"),
        },
    };

    const COCNLineGroup = {
        groupId: "000000001",
        status: 1,
        tenantId: testdata.tenantCocnId,
        planId: 50001
    };

    const TSTLineGroup = {
        groupId: "000000002",
        status: 1,
        tenantId: testdata.tenantId,
        planId: 50001
    };

    const NonAvailableLineGroup = {
        groupId: "000000003",
        status: 2,
        tenantId: testdata.tenantId,
        planId: 50001
    }

    const LineGroupWithoutLineLineGroup = {
        groupId: "000000004",
        status: 1,
        tenantId: testdata.tenantId,
        planId: 50001
    }

    const existLineGroupId = "7001025";

    const nonExistLineGroupId = "000000000";

    const responseHeader = {
        sequenceNo: request.requestHeader.sequenceNo,
        receivedDate: request.requestHeader.receivedDate,
        processCode: ResultCdConstants.CODE_000000,
        apiProcessID: generateProcessID(),
    };

    const checkResponseHeader = (result: HttpResponseInit, expectedProcessCode: string = ResultCdConstants.CODE_000000) => {
        expect(result.jsonBody.responseHeader).toEqual({
            processCode: expectedProcessCode,
            apiProcessID: responseHeader.apiProcessID,
            sequenceNo: request.requestHeader.sequenceNo,
            receivedDate: responseHeader.receivedDate,
        });
    };

    const checkEmptyResponseContent = (result: HttpResponseInit) => {
        expect(result.jsonBody.potalGroupPlanID).toEqual("");
        expect(result.jsonBody.potalGroupPlanName).toEqual("");
        expect(result.jsonBody.lineList).toEqual([{ lineNo: "" }]);
    }

    beforeAll(async () => {
        await AppConfig.loadCoreMvnoConfig(DefaultContext);
        sequelize = await usePsql();
        await useMongo(DefaultContext);
        await LineGroupsEntity.create(COCNLineGroup);
        await LineGroupsEntity.create(TSTLineGroup);
        await LineGroupsEntity.create(NonAvailableLineGroup);
        await LineGroupsEntity.create(LineGroupWithoutLineLineGroup);
        await LineLineGroupsEntity.create({
            groupId: TSTLineGroup.groupId,
            lineId: "08010123280001",
            basicCapacity: 1024,
        });
    });

    afterAll(async () => {
        await LineLineGroupsEntity.destroy({ where: { groupId: TSTLineGroup.groupId } });
        await LineGroupsEntity.destroy({ where: { groupId: COCNLineGroup.groupId } });
        await LineGroupsEntity.destroy({ where: { groupId: TSTLineGroup.groupId } });
        await LineGroupsEntity.destroy({ where: { groupId: NonAvailableLineGroup.groupId } });
        await LineGroupsEntity.destroy({ where: { groupId: LineGroupWithoutLineLineGroup.groupId } });
        await sequelize.close();
        await disconnectMongo(DefaultContext);
    });

    beforeEach(async () => {
        defaultContext.jsonBody = Object.assign({}, request);
        defaultContext.responseHeader = Object.assign({}, responseHeader);
        jest.spyOn(MvnoUtil, "getDateTimeNow").mockReturnValue(responseHeader.receivedDate);
    });

    afterEach(async () => {
        jest.restoreAllMocks();
        jest.clearAllMocks();
    });

    describe("CommonCheckFailedHandler", () => {
        test('CommonCheckFailedHandler with %s',async () => {
            defaultContext.jsonBody = Object.assign({}, {
                ...request,
            });
            defaultContext.responseHeader = Object.assign({}, { ...responseHeader, processCode: ResultCdConstants.CODE_999999 });
            const response = await LineGroupInfoAcquisitionHandler.CommonCheckFailedHandler(defaultRequest, defaultContext, "フォーマットチェックエラーが発生しました。");
            checkResponseHeader(response, ResultCdConstants.CODE_999999);
            checkEmptyResponseContent(response);
        });
    });

    describe("format check fail", () => {
        test("Should return 080101 for failing line group id format check", async () => {
            const result = await LineGroupInfoAcquisitionHandler.Handler(defaultRequest, defaultContext);
            checkResponseHeader(result, ResultCdConstants.CODE_080101);
            checkEmptyResponseContent(result);
        });

        test("Should return 080102 for null tenant", async () => {
            defaultContext.jsonBody = Object.assign({}, {
                ...request,
                lineGroupId: TSTLineGroup.groupId,
            });
            jest.spyOn(APICommonDAO.prototype, "getTenants").mockReturnValue(null);
            const result = await LineGroupInfoAcquisitionHandler.Handler(defaultRequest, defaultContext);
            checkResponseHeader(result, ResultCdConstants.CODE_080102);
            checkEmptyResponseContent(result);
        });

        test("Should return 080102 if getTenants fails to query", async () => {
            defaultContext.jsonBody = Object.assign({}, {
                ...request,
                lineGroupId: TSTLineGroup.groupId,
            });
            jest.spyOn(APICommonDAO.prototype, "getTenants").mockImplementation(async () =>{
                throw new ConnectionTimedOutError(new Error("timeout error"));
            });
            const result = await LineGroupInfoAcquisitionHandler.Handler(defaultRequest, defaultContext);
            checkResponseHeader(result, ResultCdConstants.CODE_080102);
            checkEmptyResponseContent(result);
        });

        const lineGroupIdCases = [undefined, null, ""];
        test.each(lineGroupIdCases)("Should return CODE_080101 if lineGroupId is `%s`", async (target) => {
            defaultContext.jsonBody = Object.assign({}, {
                ...request,
                lineGroupId: target,
                tenantId: testdata.tenantId,
            });
            const result = await LineGroupInfoAcquisitionHandler.Handler(defaultRequest, defaultContext);
            checkResponseHeader(result, ResultCdConstants.CODE_080101);
            checkEmptyResponseContent(result);
        });
    });

    describe("C-OCN flow", () => {
        test("Should return 080201 for failing getting lines group info", async () => {
            defaultContext.jsonBody = Object.assign({}, {
                ...request,
                lineGroupId: nonExistLineGroupId,
                tenantId: testdata.tenantCocnId,
            });
            jest.spyOn(APILinesGroupDAO.prototype, "getLinesGroupInfo").mockReturnValue(null);
            const result = await LineGroupInfoAcquisitionHandler.Handler(defaultRequest, defaultContext);
            checkResponseHeader(result, ResultCdConstants.CODE_080201);
            checkEmptyResponseContent(result);
        });
        
        test("Should return 080201 if getLinesGroupInfo fails to query", async () => {
            defaultContext.jsonBody = Object.assign({}, {
                ...request,
                lineGroupId: nonExistLineGroupId,
                tenantId: testdata.tenantCocnId,
            });
            jest.spyOn(APILinesGroupDAO.prototype, "getLinesGroupInfo").mockImplementation(async () =>{
                throw new ConnectionTimedOutError(new Error("timeout error"));
            });
            const result = await LineGroupInfoAcquisitionHandler.Handler(defaultRequest, defaultContext);
            checkResponseHeader(result, ResultCdConstants.CODE_080201);
            checkEmptyResponseContent(result);
        });

        test("Should return empty result for getting lines group info", async () => {
            defaultContext.jsonBody = Object.assign({}, {
                ...request,
                lineGroupId: COCNLineGroup.groupId,
                tenantId: testdata.tenantCocnId,
            });
            const result = await LineGroupInfoAcquisitionHandler.Handler(defaultRequest, defaultContext);
            checkResponseHeader(result);
            expect(result.jsonBody.potalGroupPlanID).toEqual("");
            expect(result.jsonBody.potalGroupPlanName).toEqual("");
            expect(result.jsonBody.lineList).toEqual([]);
        });
    });

    describe("Non C-OCN flow", () => {
        test("Should return 080202 for failing getting lines group info", async () => {
            defaultContext.jsonBody = Object.assign({}, {
                ...request,
                lineGroupId: nonExistLineGroupId,
                tenantId: testdata.tenantId,
            });
            jest.spyOn(APILinesGroupDAO.prototype, "getLinesGroupInfo").mockReturnValue(null);
            const result = await LineGroupInfoAcquisitionHandler.Handler(defaultRequest, defaultContext);
            checkResponseHeader(result, ResultCdConstants.CODE_080202);
            checkEmptyResponseContent(result);
        });

        test("Should return 080202 if getLineGroupsInfo fails to query", async () => {
            defaultContext.jsonBody = Object.assign({}, {
                ...request,
                lineGroupId: nonExistLineGroupId,
                tenantId: testdata.tenantId,
            });
            jest.spyOn(APILinesGroupDAO.prototype, "getLineGroupsInfo").mockImplementation(async ()=> {
                throw new ConnectionTimedOutError(new Error("timeout error"));
            })
            const result = await LineGroupInfoAcquisitionHandler.Handler(defaultRequest, defaultContext);
            checkResponseHeader(result, ResultCdConstants.CODE_080202);
            checkEmptyResponseContent(result);
        });

        test("Should return 080202 for line group info tenant id mismatch", async () => {
            defaultContext.jsonBody = Object.assign({}, {
                ...request,
                lineGroupId: COCNLineGroup.groupId,
                tenantId: testdata.tenantId,
            });
            const result = await LineGroupInfoAcquisitionHandler.Handler(defaultRequest, defaultContext);
            checkResponseHeader(result, ResultCdConstants.CODE_080202);
            checkEmptyResponseContent(result);
        });

        test("Should return 080202 for line group info status != 1", async () => {
            defaultContext.jsonBody = Object.assign({}, {
                ...request,
                lineGroupId: NonAvailableLineGroup.groupId,
                tenantId: testdata.tenantId,
            });
            const result = await LineGroupInfoAcquisitionHandler.Handler(defaultRequest, defaultContext);
            checkResponseHeader(result, ResultCdConstants.CODE_080202);
            checkEmptyResponseContent(result);
        });

        test("Should return 080202 for failing getting line line group", async () => {
            defaultContext.jsonBody = Object.assign({}, {
                ...request,
                lineGroupId: LineGroupWithoutLineLineGroup.groupId,
                tenantId: testdata.tenantId,
            });
            jest.spyOn(APILinesGroupDAO.prototype, "getUsingGroupPlans").mockReturnValue(null);
            const result = await LineGroupInfoAcquisitionHandler.Handler(defaultRequest, defaultContext);
            checkResponseHeader(result, ResultCdConstants.CODE_080202);
            checkEmptyResponseContent(result);
        });

        test("Should return response with line group info", async () => {
            defaultContext.jsonBody = Object.assign({}, {
                ...request,
                lineGroupId: TSTLineGroup.groupId,
                tenantId: testdata.tenantId,
            });
            const result = await LineGroupInfoAcquisitionHandler.Handler(defaultRequest, defaultContext);
            checkResponseHeader(result);
            expect(result.jsonBody.potalGroupPlanID).toEqual("50001");
            expect(result.jsonBody.potalGroupPlanName).toEqual("【容量シェア 】事前検証用グループプラン");
            expect(result.jsonBody.lineList).toEqual([{lineNo: '08010123280001'}]);
        });

        test("Should return result with empty potalGroupPlanID,potalGroupPlanName if getPlanInfoFromLineLineGroups returns null", async () => {
            defaultContext.jsonBody = Object.assign({}, {
                ...request,
                lineGroupId: TSTLineGroup.groupId,
                tenantId: testdata.tenantId,
            });
            jest.spyOn(APILinesGroupDAO.prototype,"getPlanInfoFromLineLineGroups").mockResolvedValue(null);
            const result = await LineGroupInfoAcquisitionHandler.Handler(defaultRequest, defaultContext);
            checkResponseHeader(result);
            expect(result.jsonBody.potalGroupPlanID).toEqual("");
            expect(result.jsonBody.potalGroupPlanName).toEqual("");
            expect(result.jsonBody.lineList).toEqual([{lineNo: '08010123280001'}]);
        });

        test("Should return response with line group info", async () => {
            defaultContext.jsonBody = Object.assign({}, {
                ...request,
                lineGroupId: existLineGroupId,
                tenantId: testdata.tenantId,
            });
            const result = await LineGroupInfoAcquisitionHandler.Handler(defaultRequest, defaultContext);
            checkResponseHeader(result);
            expect(result.jsonBody.potalGroupPlanID).toEqual("50001");
            expect(result.jsonBody.potalGroupPlanName).toEqual("【容量シェア 】事前検証用グループプラン");
            expect(result.jsonBody.lineList).toEqual([{lineNo: '08010123280001'}]);
        });

        test("Should return response with line group info (numeric lineGroupId)", async () => {
            defaultContext.jsonBody = Object.assign({}, {
                ...request,
                lineGroupId: parseInt(existLineGroupId, 10),
                tenantId: testdata.tenantId,
            });
            const result = await LineGroupInfoAcquisitionHandler.Handler(defaultRequest, defaultContext);
            checkResponseHeader(result);
            expect(result.jsonBody.potalGroupPlanID).toEqual("50001");
            expect(result.jsonBody.potalGroupPlanName).toEqual("【容量シェア 】事前検証用グループプラン");
            expect(result.jsonBody.lineList).toEqual([{lineNo: '08010123280001'}]);
        });

        test("Should return 080202 if getLineIds fails to query", async () => {
            defaultContext.jsonBody = Object.assign({}, {
                ...request,
                lineGroupId: existLineGroupId,
                tenantId: testdata.tenantId,
            });
            jest.spyOn(APILinesGroupDAO.prototype, "getLineIds").mockImplementation(async () =>{
                throw new ConnectionTimedOutError(new Error("timeout error"));
            })
            const result = await LineGroupInfoAcquisitionHandler.Handler(defaultRequest, defaultContext);
            checkResponseHeader(result, ResultCdConstants.CODE_080202);
        });

        test("Should return 080202 if getLineIds fails to query", async () => {
            defaultContext.jsonBody = Object.assign({}, {
                ...request,
                lineGroupId: existLineGroupId,
                tenantId: testdata.tenantId,
            });
            jest.spyOn(APILinesGroupDAO.prototype, "getLineIds").mockImplementation(async () =>{
                throw new ConnectionTimedOutError(new Error("timeout error"));
            })
            const result = await LineGroupInfoAcquisitionHandler.Handler(defaultRequest, defaultContext);
            checkResponseHeader(result, ResultCdConstants.CODE_080202);
        });

        test("Should return 080202 if getPlanInfoFromLineLineGroups fails to query", async () => {
            defaultContext.jsonBody = Object.assign({}, {
                ...request,
                lineGroupId: existLineGroupId,
                tenantId: testdata.tenantId,
            });
            jest.spyOn(APILinesGroupDAO.prototype, "getPlanInfoFromLineLineGroups").mockImplementation(async () =>{
                throw new ConnectionTimedOutError(new Error("timeout error"));
            })
            const result = await LineGroupInfoAcquisitionHandler.Handler(defaultRequest, defaultContext);
            checkResponseHeader(result, ResultCdConstants.CODE_080202);
        });

        test("Should return 999999 if an unexpected error occurs", async () => {
            defaultContext.jsonBody = Object.assign({}, {
                ...request,
                lineGroupId: existLineGroupId,
                tenantId: testdata.tenantId,
            });
            jest.spyOn(APILinesGroupDAO.prototype, "getPlanInfoFromLineLineGroups").mockRejectedValue(new TypeError("some error"));
            const result = await LineGroupInfoAcquisitionHandler.Handler(defaultRequest, defaultContext);
            checkResponseHeader(result, ResultCdConstants.CODE_999999);
        });

        test("Should return 999999 if an unexpected error occurs calling service", async () => {
            defaultContext.jsonBody = Object.assign({}, {
                ...request,
                lineGroupId: existLineGroupId,
                tenantId: testdata.tenantId,
            });

            jest.spyOn(LineGroupInfoAcquisitionService.prototype, 'service')
            .mockImplementationOnce(async () => {
                throw new Error("Unexpected error");
            });
            jest.spyOn(APILinesGroupDAO.prototype, "getPlanInfoFromLineLineGroups").mockRejectedValue(new TypeError("some error"));
            const result = await LineGroupInfoAcquisitionHandler.Handler(defaultRequest, defaultContext);
            checkResponseHeader(result, ResultCdConstants.CODE_999999);
            expect(defaultContext.error).toHaveBeenCalled();
            checkEmptyResponseContent(result);
        });
    });
});