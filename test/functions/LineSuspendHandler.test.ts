import { expect, test, beforeEach } from "@jest/globals";

import { addDays, addMinutes, formatDate } from "date-fns";
import { ConnectionTimedOutError, Op, QueryTypes, Sequelize } from "sequelize";

import "@/types/string.extension";

import DefaultContext from "../testing/DefaultContext";
import DefaultRequest from "../testing/DefaultRequest";
import {
    describeWithDB,
    generateProcessID,
    generateSOID,
    useFasterRetries,
} from "../testing/TestHelper";

import { LineSuspendHandler } from "@/functions/LineSuspendHandler";

import AppConfig from "@/appconfig";
import ResultCdConstants from "@/core/constant/ResultCdConstants";
import ResponseHeader from "@/core/dto/ResponseHeader";

import { LinesEntity } from "@/core/entity/LinesEntity";
import LineTenantsEntity from "@/core/entity/LineTenantsEntity";
import LineGroupsEntity from "@/core/entity/LineGroupsEntity";
import PlansEntity from "@/core/entity/PlansEntity";
import ServiceOrdersEntity from "@/core/entity/ServiceOrdersEntity";
import TenantPlansEntity from "@/core/entity/TenantPlansEntity";

import ExtendedInvocationContext from "@/types/ExtendedInvocationContext";
import { usePsql } from "@/database/psql";

describeWithDB(
    "58 利用中断/再開/サスペンド /MVNO/api/V100/Lines/suspend",
    () => {
        let sequelize: Sequelize;

        const UsageStatusType = {
            ACTIVE: 0,
            SUSPENDING: 1,
            SUSPENDED: 2,
        };
        const OrderTypes = {
            [UsageStatusType.ACTIVE]: "利用状態変更（利用中）",
            [UsageStatusType.SUSPENDING]: "利用状態変更（利用中断中）",
            [UsageStatusType.SUSPENDED]: "利用状態変更（サスペンド中）",
        };

        const testdata = {
            lines: {
                ok: "08024051001",
                notExists: "08024051002",
                statusNG: "08024051003",
                notLinked: "08024051004",
                abolish: "08024051005",
                lite: "08024051006",
                pricePlanNotExist: "08024051007",
                suspended: "08024051008",
            },
            abolishSo: generateProcessID(),
            lineGroupId: "",
            fullMvnoPlan: "",
            litePlan: "",
            lineUpdateAt: addDays(new Date(), -1),
        };

        const request = {
            requestHeader: {
                sequenceNo: "1234567",
                senderSystemId: "0001",
                apiKey: "",
                functionType: "58",
            },
            targetSoId: generateSOID(),
            tenantId: "OPF000",
            targetTenantId: "TST000",
            lineNo: testdata.lines.ok,
            suspendFlag: `${UsageStatusType.SUSPENDED}`,
        };

        const responseHeader = {
            sequenceNo: request.requestHeader.sequenceNo,
            receivedDate: formatDate(new Date(), "yyyy/MM/dd HH:mm:ss"),
            processCode: ResultCdConstants.CODE_000000,
            apiProcessID: generateProcessID(),
        } as ResponseHeader;

        beforeAll(async () => {
            await AppConfig.loadCoreMvnoConfig(DefaultContext);
            sequelize = await usePsql();

            // clear test data in case previous run failed
            await clearTestData();

            // get group ID (must be exist in line_groups table)
            const groupId = await LineGroupsEntity.findOne({
                where: {
                    tenantId: request.targetTenantId,
                },
            });
            if (groupId) {
                testdata.lineGroupId = groupId.groupId;
            } else {
                throw new Error(
                    `could not find line group for tenant ${request.targetTenantId}`,
                );
            }

            // get Full MVNO Plan and lite plan
            const [full, lite] = await Promise.all([
                PlansEntity.findOne({
                    include: [
                        {
                            model: TenantPlansEntity,
                            as: "tenantPlansEntityList",
                            where: {
                                tenantId: request.targetTenantId,
                            },
                        },
                    ],
                    where: {
                        fullMvnoFlag: true,
                    },
                }),
                PlansEntity.findOne({
                    include: [
                        {
                            model: TenantPlansEntity,
                            as: "tenantPlansEntityList",
                            where: {
                                tenantId: request.targetTenantId,
                            },
                        },
                    ],
                    where: {
                        fullMvnoFlag: {
                            [Op.not]: true,
                        },
                    },
                }),
            ]);
            if (!full || !lite) {
                throw new Error("could not find full MVNO or lite plan");
            }
            testdata.fullMvnoPlan = full.pricePlanId;
            testdata.litePlan = lite.pricePlanId;

            await ServiceOrdersEntity.create({
                serviceOrderId: testdata.abolishSo,
                tenantId: request.targetTenantId,
                lineId: testdata.lines.abolish,
                orderStatus: "完了",
                orderType: "回線廃止",
                reserveDate: null,
                execDate: addMinutes(new Date(), -1),
                functionType: "52",
            });
        });

        afterAll(async () => {
            await ServiceOrdersEntity.destroy({
                where: {
                    serviceOrderId: testdata.abolishSo,
                },
            });
            await sequelize.close();
        });

        const clearTestData = async () => {
            await LineTenantsEntity.destroy({
                where: {
                    lineId: Object.values(testdata.lines),
                },
            });
            await Promise.all([
                LinesEntity.destroy({
                    where: {
                        lineId: Object.values(testdata.lines),
                    },
                }),
                ServiceOrdersEntity.destroy({
                    where: {
                        serviceOrderId: responseHeader.apiProcessID,
                    },
                }),
            ]);
        };

        beforeEach(async () => {
            DefaultContext.jsonBody = Object.assign({}, request);
            DefaultContext.responseHeader = Object.assign({}, responseHeader);

            useFasterRetries();

            // to create: line, line_line_group, line_tenant

            // insert line numbers
            await Promise.all([
                LinesEntity.create({
                    lineId: testdata.lines.ok,
                    tenantId: request.targetTenantId,
                    lineStatus: "01",
                    simFlag: false,
                    pricePlanId: testdata.fullMvnoPlan,
                    usageStatus: UsageStatusType.ACTIVE,
                    updatedAt: testdata.lineUpdateAt,
                }),
                LinesEntity.create({
                    lineId: testdata.lines.statusNG,
                    tenantId: request.targetTenantId,
                    lineStatus: "03",
                    simFlag: false,
                    pricePlanId: testdata.fullMvnoPlan,
                    usageStatus: UsageStatusType.ACTIVE,
                    updatedAt: testdata.lineUpdateAt,
                }),
                LinesEntity.create({
                    lineId: testdata.lines.notLinked,
                    tenantId: request.targetTenantId,
                    lineStatus: "01",
                    simFlag: false,
                    pricePlanId: testdata.fullMvnoPlan,
                    usageStatus: UsageStatusType.ACTIVE,
                    updatedAt: testdata.lineUpdateAt,
                }),
                LinesEntity.create({
                    lineId: testdata.lines.abolish,
                    tenantId: request.targetTenantId,
                    lineStatus: "01",
                    simFlag: false,
                    pricePlanId: testdata.fullMvnoPlan,
                    usageStatus: UsageStatusType.ACTIVE,
                    updatedAt: testdata.lineUpdateAt,
                }),
                LinesEntity.create({
                    lineId: testdata.lines.lite,
                    tenantId: request.targetTenantId,
                    lineStatus: "01",
                    simFlag: false,
                    pricePlanId: testdata.litePlan,
                    usageStatus: UsageStatusType.ACTIVE,
                    updatedAt: testdata.lineUpdateAt,
                }),
                LinesEntity.create({
                    lineId: testdata.lines.pricePlanNotExist,
                    tenantId: request.targetTenantId,
                    lineStatus: "01",
                    simFlag: false,
                    // pricePlanId: "",
                    usageStatus: UsageStatusType.ACTIVE,
                    updatedAt: testdata.lineUpdateAt,
                }),
                LinesEntity.create({
                    lineId: testdata.lines.suspended,
                    tenantId: request.targetTenantId,
                    lineStatus: "01",
                    simFlag: false,
                    pricePlanId: testdata.fullMvnoPlan,
                    usageStatus: UsageStatusType.SUSPENDED,
                    updatedAt: testdata.lineUpdateAt,
                }),
            ]);
            // register to line tenant
            await Promise.all(
                [
                    testdata.lines.ok,
                    testdata.lines.statusNG,
                    testdata.lines.abolish,
                    testdata.lines.lite,
                    testdata.lines.pricePlanNotExist,
                    testdata.lines.suspended,
                ].map(async (lineId) => {
                    await LineTenantsEntity.create({
                        lineId,
                        tenantId: request.targetTenantId,
                    });
                }),
            );
        });

        afterEach(async () => {
            await clearTestData();
            jest.restoreAllMocks();
            jest.clearAllMocks();
        });

        /**
         * validate HttpResponse object and validate process code
         * // TODO can be moved to common test helper later
         */
        const checkResponseObject = (
            result: any,
            context: ExtendedInvocationContext,
            code: string,
        ) => {
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.apiProcessID).toEqual(
                context.responseHeader.apiProcessID,
            );
            expect(result.jsonBody.responseHeader.processCode).toEqual(code);
            expect(result.jsonBody.responseHeader.sequenceNo).toEqual(
                context.responseHeader.sequenceNo,
            );
            expect(result.jsonBody.responseHeader.receivedDate).toEqual(
                context.responseHeader.receivedDate,
            );
        };

        const checkServiceOrder = async (
            soStatus: string,
            suspendFlag: string,
            created = true,
        ) => {
            const serviceOrder = await ServiceOrdersEntity.findOne({
                where: {
                    serviceOrderId: responseHeader.apiProcessID,
                },
            });

            if (!created) {
                // new service order is not created, skip remaining validation
                expect(serviceOrder).toBeNull();
                return;
            }
            // new service order is created
            expect(serviceOrder).not.toBeNull();
            expect(serviceOrder.tenantId).toEqual(
                DefaultContext.jsonBody.targetTenantId,
            );
            expect(serviceOrder.lineId).toEqual(DefaultContext.jsonBody.lineNo);
            expect(serviceOrder.orderStatus).toEqual(soStatus);
            expect(OrderTypes[suspendFlag]).not.toBeUndefined();
            expect(serviceOrder.orderType).toEqual(OrderTypes[suspendFlag]);
            expect(serviceOrder.reserveDate).toBeNull();
        };

        const checkLineResult = async (
            lineNo: string,
            updated: boolean,
            suspendFlag: number | string,
        ) => {
            const line = await LinesEntity.findOne({
                where: {
                    lineId: lineNo,
                },
            });
            expect(line).not.toBeNull();
            if (updated) {
                expect(line.updatedAt.getTime()).toBeGreaterThan(
                    testdata.lineUpdateAt.getTime(),
                );
                expect(line.usageStatus).toEqual(+suspendFlag);
            } else {
                expect(line.updatedAt.getTime()).toEqual(
                    testdata.lineUpdateAt.getTime(),
                );
                const oldStatus =
                    lineNo === testdata.lines.suspended
                        ? UsageStatusType.SUSPENDED
                        : UsageStatusType.ACTIVE;
                expect(line.usageStatus).toEqual(oldStatus);
            }
        };

        const checkLineGroupAndModbucket = async (
            exists: boolean,
            lineId: string,
            groupId: string,
            suspendFlag: string | number,
            processID: string,
        ) => {
            const [row, bucket] = await Promise.all([
                sequelize.query(
                    "select line_id, usage_status from line_line_groups where group_id = :group_id and line_id = :line_id",
                    {
                        replacements: {
                            group_id: groupId,
                            line_id: lineId,
                        },
                        type: QueryTypes.SELECT,
                    },
                ),
                sequelize.query(
                    "select * from auto_modbucket_line_groups where group_id = :group_id and line_id = :line_id and service_order_id = :so_id",
                    {
                        replacements: {
                            group_id: groupId,
                            line_id: lineId,
                            so_id: processID,
                        },
                        type: QueryTypes.SELECT,
                    },
                ),
            ]);

            if (!exists) {
                expect(row.length).toEqual(0);
                expect(bucket.length).toEqual(0);
            } else {
                expect(row.length).not.toEqual(0);
                expect((row as any[])[0].usage_status).toEqual(+suspendFlag);
                expect(bucket.length).toEqual(1);
            }
        };

        describe("OK (CODE_000000)", () => {
            const OK_CASE = [
                [
                    "利用中 → サスペンド中",
                    testdata.lines.ok,
                    UsageStatusType.SUSPENDED,
                ],
                [
                    "利用中 → 利用中断中",
                    testdata.lines.ok,
                    UsageStatusType.SUSPENDING,
                ],
                [
                    "サスペンド中 → 利用中",
                    testdata.lines.suspended,
                    UsageStatusType.ACTIVE,
                ],
                [
                    "サスペンド中 → 利用中断中",
                    testdata.lines.suspended,
                    UsageStatusType.SUSPENDING,
                ],
            ];

            test.each(OK_CASE)(
                "should return CODE_000000 when when there is no error (%s)",
                async (description, lineNo: string, suspendFlag) => {
                    DefaultContext.jsonBody.lineNo = lineNo;
                    DefaultContext.jsonBody.suspendFlag = `${suspendFlag}`;
                    const result = await LineSuspendHandler.Handler(
                        DefaultRequest,
                        DefaultContext,
                    );

                    checkResponseObject(
                        result,
                        DefaultContext,
                        ResultCdConstants.CODE_000000,
                    );
                    await checkServiceOrder(
                        "完了",
                        DefaultContext.jsonBody.suspendFlag,
                    );
                    await checkLineResult(
                        DefaultContext.jsonBody.lineNo,
                        true,
                        suspendFlag,
                    );
                    await checkLineGroupAndModbucket(
                        false,
                        lineNo,
                        testdata.lineGroupId,
                        suspendFlag,
                        responseHeader.apiProcessID,
                    );
                },
            );

            test("should return CODE_000000 and insert to modbucket when there is no error", async () => {
                try {
                    // insert to line_line_group first
                    await sequelize.query(
                        "insert into line_line_groups(line_id, group_id, basic_capacity, usage_status) values (:line_id, :group_id, :cap, :status)",
                        {
                            replacements: {
                                line_id: testdata.lines.ok,
                                group_id: testdata.lineGroupId,
                                cap: 10240,
                                status: UsageStatusType.ACTIVE,
                            },
                        },
                    );

                    const result = await LineSuspendHandler.Handler(
                        DefaultRequest,
                        DefaultContext,
                    );
                    checkResponseObject(
                        result,
                        DefaultContext,
                        ResultCdConstants.CODE_000000,
                    );
                    await checkServiceOrder(
                        "完了",
                        DefaultContext.jsonBody.suspendFlag,
                    );
                    await checkLineResult(
                        DefaultContext.jsonBody.lineNo,
                        true,
                        DefaultContext.jsonBody.suspendFlag,
                    );
                    await checkLineGroupAndModbucket(
                        true,
                        testdata.lines.ok,
                        testdata.lineGroupId,
                        DefaultContext.jsonBody.suspendFlag,
                        responseHeader.apiProcessID,
                    );
                } finally {
                    // remove from line_line_group and modbucket
                    await Promise.allSettled([
                        sequelize.query(
                            "delete from line_line_groups where line_id = :line_id",
                            {
                                replacements: {
                                    line_id: testdata.lines.ok,
                                },
                            },
                        ),
                        sequelize.query(
                            "delete from auto_modbucket_line_groups where line_id = :line_id",
                            {
                                replacements: {
                                    line_id: testdata.lines.ok,
                                },
                            },
                        ),
                    ]);
                }
            });
        });

        describe("NG (CODE_58xxxx)", () => {
            test("should return CODE_580101 when request tenant is not Front API tenant", async () => {
                DefaultContext.jsonBody.tenantId = "TST999";
                const result = await LineSuspendHandler.Handler(
                    DefaultRequest,
                    DefaultContext,
                );

                checkResponseObject(
                    result,
                    DefaultContext,
                    ResultCdConstants.CODE_580101,
                );
                await checkServiceOrder(
                    "失敗",
                    DefaultContext.jsonBody.suspendFlag,
                );
                // line not updated
                await checkLineResult(
                    DefaultContext.jsonBody.lineNo,
                    false,
                    DefaultContext.jsonBody.suspendFlag,
                );
            });

            test("should return CODE_580101 when target tenant ID format is invalid", async () => {
                DefaultContext.jsonBody.targetTenantId = "TST12345678";
                const result = await LineSuspendHandler.Handler(
                    DefaultRequest,
                    DefaultContext,
                );

                checkResponseObject(
                    result,
                    DefaultContext,
                    ResultCdConstants.CODE_580101,
                );
                await checkServiceOrder(
                    "失敗",
                    DefaultContext.jsonBody.suspendFlag,
                    false,
                );
                // line not updated
                await checkLineResult(
                    DefaultContext.jsonBody.lineNo,
                    false,
                    DefaultContext.jsonBody.suspendFlag,
                );
            });

            const LINE_FORMAT_NG = [
                ["length = 10", "0802405100"],
                ["length = 12", "080240510012"],
                ["null", null],
                ["not numeric", "080240510AB"],
            ];

            test.each(LINE_FORMAT_NG)(
                "should return CODE_580101 if lineNo format is invalid (%s)",
                async (description, lineNo) => {
                    DefaultContext.jsonBody.lineNo = lineNo;
                    const result = await LineSuspendHandler.Handler(
                        DefaultRequest,
                        DefaultContext,
                    );

                    checkResponseObject(
                        result,
                        DefaultContext,
                        ResultCdConstants.CODE_580101,
                    );
                    await checkServiceOrder(
                        "失敗",
                        DefaultContext.jsonBody.suspendFlag,
                    );
                },
            );

            test("should return CODE_580101 if suspendFlag value is not valid", async () => {
                DefaultContext.jsonBody.suspendFlag = "3";
                const result = await LineSuspendHandler.Handler(
                    DefaultRequest,
                    DefaultContext,
                );

                checkResponseObject(
                    result,
                    DefaultContext,
                    ResultCdConstants.CODE_580101,
                );
                await checkServiceOrder(
                    "失敗",
                    DefaultContext.jsonBody.suspendFlag,
                    false,
                );
                // line not updated
                await checkLineResult(
                    DefaultContext.jsonBody.lineNo,
                    false,
                    DefaultContext.jsonBody.suspendFlag,
                );
            });

            test("should return CODE_580102 if line does not exist", async () => {
                DefaultContext.jsonBody.lineNo = testdata.lines.notExists;
                const result = await LineSuspendHandler.Handler(
                    DefaultRequest,
                    DefaultContext,
                );

                checkResponseObject(
                    result,
                    DefaultContext,
                    ResultCdConstants.CODE_580102,
                );
                await checkServiceOrder(
                    "失敗",
                    DefaultContext.jsonBody.suspendFlag,
                );
            });

            test("should return CODE_580102 if line status is NG", async () => {
                DefaultContext.jsonBody.lineNo = testdata.lines.statusNG;
                const result = await LineSuspendHandler.Handler(
                    DefaultRequest,
                    DefaultContext,
                );

                checkResponseObject(
                    result,
                    DefaultContext,
                    ResultCdConstants.CODE_580102,
                );
                await checkServiceOrder(
                    "失敗",
                    DefaultContext.jsonBody.suspendFlag,
                );
                // line not updated
                await checkLineResult(
                    DefaultContext.jsonBody.lineNo,
                    false,
                    DefaultContext.jsonBody.suspendFlag,
                );
            });

            test("should return CODE_580102 if DB error when reading line info", async () => {
                jest.spyOn(LinesEntity, "findOne").mockRejectedValue(
                    new ConnectionTimedOutError(new Error("timeout")),
                );

                const result = await LineSuspendHandler.Handler(
                    DefaultRequest,
                    DefaultContext,
                );

                checkResponseObject(
                    result,
                    DefaultContext,
                    ResultCdConstants.CODE_580102,
                );
                await checkServiceOrder(
                    "失敗",
                    DefaultContext.jsonBody.suspendFlag,
                    false,
                );
            });

            test("should return CODE_580103 if line is not linked to tenant", async () => {
                DefaultContext.jsonBody.lineNo = testdata.lines.notLinked;
                const result = await LineSuspendHandler.Handler(
                    DefaultRequest,
                    DefaultContext,
                );

                checkResponseObject(
                    result,
                    DefaultContext,
                    ResultCdConstants.CODE_580103,
                );
                await checkServiceOrder(
                    "失敗",
                    DefaultContext.jsonBody.suspendFlag,
                );
                // line not updated
                await checkLineResult(
                    DefaultContext.jsonBody.lineNo,
                    false,
                    DefaultContext.jsonBody.suspendFlag,
                );
            });

            test("should return CODE_580105 if line's price plan could not be found", async () => {
                DefaultContext.jsonBody.lineNo =
                    testdata.lines.pricePlanNotExist;
                const result = await LineSuspendHandler.Handler(
                    DefaultRequest,
                    DefaultContext,
                );

                checkResponseObject(
                    result,
                    DefaultContext,
                    ResultCdConstants.CODE_580105,
                );
                await checkServiceOrder(
                    "失敗",
                    DefaultContext.jsonBody.suspendFlag,
                );
                // line not updated
                await checkLineResult(
                    DefaultContext.jsonBody.lineNo,
                    false,
                    DefaultContext.jsonBody.suspendFlag,
                );
            });

            test("should return CODE_580105 if line's plan is not full MVNO plan", async () => {
                DefaultContext.jsonBody.lineNo = testdata.lines.lite;
                const result = await LineSuspendHandler.Handler(
                    DefaultRequest,
                    DefaultContext,
                );

                checkResponseObject(
                    result,
                    DefaultContext,
                    ResultCdConstants.CODE_580105,
                );
                await checkServiceOrder(
                    "失敗",
                    DefaultContext.jsonBody.suspendFlag,
                );
                // line not updated
                await checkLineResult(
                    DefaultContext.jsonBody.lineNo,
                    false,
                    DefaultContext.jsonBody.suspendFlag,
                );
            });
        });

        test("should return null", async () => {
            // request.reserve_date = null
            const result = LineSuspendHandler.Meta.getOrderType(request);
            expect(result).toBeNull();
        });
    },
);
