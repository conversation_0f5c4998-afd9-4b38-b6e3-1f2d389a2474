import { expect, test, beforeEach, jest } from "@jest/globals";
jest.setTimeout(10000); // 2 × LOCK_RETRY_COUNT × LOCK_WAIT_MILLISEC

const enqueueMessage = jest.fn();
jest.mock("../../src/services/servicebus", () => ({
    __esModule: true,
    default: enqueueMessage,
}));

import { addDays, addMinutes, formatDate } from "date-fns";
import { Sequelize, Transaction } from "sequelize";

import "@/types/string.extension";

import {
    describeWithDB,
    generateProcessID,
    generateSOID,
} from "../testing/TestHelper";
import DefaultContext from "../testing/DefaultContext";
import DefaultRequest from "../testing/DefaultRequest";

import { NetworkContractChangeHandler } from "@/functions/NetworkContractChangeHandler";

import AppConfig from "@/appconfig";
import ResultCdConstants from "@/core/constant/ResultCdConstants";
import ResponseHeader from "@/core/dto/ResponseHeader";
import { LinesEntity } from "@/core/entity/LinesEntity";
import LineTenantsEntity from "@/core/entity/LineTenantsEntity";
import ServiceOrdersEntity from "@/core/entity/ServiceOrdersEntity";

import CoreSwimmyApiLog from "@/models/coreSwimmyApiLog";

import { usePsql } from "@/database/psql";
import { disconnectMongo, useMongo } from "@/database/mongo";

describeWithDB("60 NW契約変更 /MVNO/api/V100/Lines/access", () => {
    let sequelize: Sequelize;
    const testdata = {
        lineOK: "08024050701",
        lineNotExists: "08024050702",
        lineStatusNG: "08024050703",
        lineNotLinked: "08024050704",
        lineWithAbolish: "08024050705",
    };
    const abolishSo = generateProcessID();
    const lineUpdatedAt = addDays(new Date(), -1);

    const deviceId = {
        lte: "AD7",
        sms: "AE0",
        voice: "AW9",
    };

    const request = {
        requestHeader: {
            sequenceNo: "1234567",
            senderSystemId: "0001",
            apiKey: "",
            functionType: "60",
        },
        targetSoId: generateSOID(),
        tenantId: "OPF000",
        targetTenantId: "TST000",
        lineNo: testdata.lineOK,
        access_pre: "SE", // not used in implementation
        access: "SJ",
        cardTypeId: deviceId.voice,
        csvUnnecessaryFlag: "0",
    };

    const contractMap = {
        SA: "3G",
        SB: "3G(SMS)",
        SC: "LTE",
        SD: "LTE(SMS)",
        SE: "LTE(音声)",
        SH: "5G(NSA)",
        SJ: "5G(NSA)(音声)",
    };

    const responseHeader = {
        sequenceNo: request.requestHeader.sequenceNo,
        receivedDate: formatDate(new Date(), "yyyy/MM/dd HH:mm:ss"),
        processCode: ResultCdConstants.CODE_000000,
        apiProcessID: generateProcessID(),
    } as ResponseHeader;

    beforeAll(async () => {
        await AppConfig.loadCoreMvnoConfig(DefaultContext);
        sequelize = await usePsql();
        await useMongo(DefaultContext);
        await ServiceOrdersEntity.create({
            serviceOrderId: abolishSo,
            tenantId: request.targetTenantId,
            lineId: testdata.lineWithAbolish,
            orderStatus: "完了",
            orderType: "回線廃止",
            reserveDate: null,
            execDate: addMinutes(new Date(), -5),
            functionType: "52",
        });
    });

    afterAll(async () => {
        await ServiceOrdersEntity.destroy({
            where: {
                serviceOrderId: abolishSo,
            },
        });
        await sequelize.close();
        await disconnectMongo(DefaultContext);
    });

    beforeEach(async () => {
        // reset mock
        (enqueueMessage as jest.Mock).mockClear();

        DefaultContext.jsonBody = Object.assign({}, request);
        DefaultContext.responseHeader = Object.assign({}, responseHeader);

        // insert line numbers
        await Promise.all([
            LinesEntity.create({
                lineId: testdata.lineOK,
                tenantId: request.targetTenantId,
                lineStatus: "01",
                simFlag: false,
                contractType: "3G",
                nwModifyFlag: false,
                updatedAt: lineUpdatedAt,
                nnumber: "N121152174",
            }),
            LinesEntity.create({
                lineId: testdata.lineStatusNG,
                tenantId: request.targetTenantId,
                lineStatus: "03",
                simFlag: false,
                contractType: "3G",
                nwModifyFlag: false,
                updatedAt: lineUpdatedAt,
                nnumber: "N121152174",
            }),
            LinesEntity.create({
                lineId: testdata.lineNotLinked,
                tenantId: request.targetTenantId,
                lineStatus: "01",
                simFlag: false,
                contractType: "3G",
                nwModifyFlag: false,
                updatedAt: lineUpdatedAt,
            }),
            LinesEntity.create({
                lineId: testdata.lineWithAbolish,
                tenantId: request.targetTenantId,
                lineStatus: "01",
                simFlag: false,
                updatedAt: lineUpdatedAt,
                contractType: "3G",
                nwModifyFlag: false,
                lineActDate: lineUpdatedAt,
                nnumber: "N121152174",
            }),
        ]);
        // register to line tenant
        await Promise.all(
            [
                testdata.lineOK,
                testdata.lineStatusNG,
                testdata.lineWithAbolish,
            ].map(async (lineId) => {
                await LineTenantsEntity.create({
                    lineId,
                    tenantId: request.targetTenantId,
                });
            }),
        );
    });

    afterEach(async () => {
        await LineTenantsEntity.destroy({
            where: {
                lineId: [
                    testdata.lineOK,
                    testdata.lineStatusNG,
                    testdata.lineNotLinked,
                    testdata.lineWithAbolish,
                ],
            },
        });
        await Promise.all([
            LinesEntity.destroy({
                where: {
                    lineId: Object.values(testdata),
                },
            }),
            ServiceOrdersEntity.destroy({
                where: {
                    serviceOrderId: responseHeader.apiProcessID,
                },
            }),
            CoreSwimmyApiLog.deleteMany({
                requestOrderId: responseHeader.apiProcessID,
            }),
        ]);
    });

    // helpers
    const checkServiceOrder = async (soStatus: string) => {
        const serviceOrder = await ServiceOrdersEntity.findOne({
            where: {
                serviceOrderId: responseHeader.apiProcessID,
            },
        });

        // new service order is created
        expect(serviceOrder).not.toBeNull();
        expect(serviceOrder.tenantId).toEqual(
            DefaultContext.jsonBody.targetTenantId,
        );
        expect(serviceOrder.lineId).toEqual(DefaultContext.jsonBody.lineNo);
        expect(serviceOrder.orderStatus).toEqual(soStatus);
        expect(serviceOrder.orderType).toEqual("NW契約変更");
        expect(serviceOrder.reserveDate).toBeNull();
    };

    /**
     * check whether SwimmyApiLog exists and Service Bus is called
     */
    const checkCoreSwimmyApiLog = async (
        apiProcessID: string,
        exists: boolean,
    ) => {
        const apiLog = await CoreSwimmyApiLog.findOne({
            requestOrderId: apiProcessID,
        });
        if (exists) {
            expect(apiLog).not.toBeNull();
            expect(enqueueMessage).toBeCalledTimes(1);
        } else {
            expect(apiLog).toBeNull();
            expect(enqueueMessage).not.toBeCalled();
        }
    };

    const csvUnneccessaryFlagType = ["string", "number"];
    describe.each(csvUnneccessaryFlagType)(
        "OK (CODE_000000) [csvUnneccessaryFlag is %s]",
        (csvUnneccessaryFlagType) => {
            const contractTypePair = Object.entries(contractMap);
            test.each(contractTypePair)(
                "should return CODE_000000 if no error 【%s: %s】",
                async (access, contractTypeName) => {
                    DefaultContext.jsonBody.access = access;
                    if (csvUnneccessaryFlagType === "number") {
                        DefaultContext.jsonBody.csvUnnecessaryFlag = 0;
                    }
                    const result = await NetworkContractChangeHandler.Handler(
                        DefaultRequest,
                        DefaultContext,
                    );
                    expect(typeof result).toBe("object");
                    expect(result).toHaveProperty("jsonBody");
                    expect(result.jsonBody).toHaveProperty("responseHeader");
                    expect(result.jsonBody.responseHeader.apiProcessID).toEqual(
                        DefaultContext.responseHeader.apiProcessID,
                    );
                    expect(result.jsonBody.responseHeader.processCode).toEqual(
                        ResultCdConstants.CODE_000000,
                    );
                    expect(result.jsonBody.responseHeader.sequenceNo).toEqual(
                        DefaultContext.responseHeader.sequenceNo,
                    );
                    expect(result.jsonBody.responseHeader.receivedDate).toEqual(
                        DefaultContext.responseHeader.receivedDate,
                    );

                    await checkServiceOrder("完了");
                    const line = await LinesEntity.findOne({
                        where: {
                            lineId: DefaultContext.jsonBody.lineNo,
                        },
                    });
                    expect(line).not.toBeNull();
                    expect(line.updatedAt.getTime()).toBeGreaterThan(
                        lineUpdatedAt.getTime(),
                    );

                    // check updates
                    expect(line.contractType).toEqual(contractTypeName);
                    expect(line.nwModifyFlag).toEqual(true);
                    expect(line.deviceTypeId).toEqual(
                        DefaultContext.jsonBody.cardTypeId,
                    );

                    await checkCoreSwimmyApiLog(
                        responseHeader.apiProcessID,
                        true,
                    );
                },
            );

            test.each(contractTypePair)(
                "should not create CoreSwimmyApiLog if csvUnneccessaryFlag is 1 【%s: %s】",
                async (access, contractTypeName) => {
                    DefaultContext.jsonBody.csvUnnecessaryFlag =
                        csvUnneccessaryFlagType === "number" ? 1 : "1";
                    DefaultContext.jsonBody.access = access;
                    const result = await NetworkContractChangeHandler.Handler(
                        DefaultRequest,
                        DefaultContext,
                    );
                    expect(typeof result).toBe("object");
                    expect(result).toHaveProperty("jsonBody");
                    expect(result.jsonBody).toHaveProperty("responseHeader");
                    expect(result.jsonBody.responseHeader.apiProcessID).toEqual(
                        DefaultContext.responseHeader.apiProcessID,
                    );
                    expect(result.jsonBody.responseHeader.processCode).toEqual(
                        ResultCdConstants.CODE_000000,
                    );
                    expect(result.jsonBody.responseHeader.sequenceNo).toEqual(
                        DefaultContext.responseHeader.sequenceNo,
                    );
                    expect(result.jsonBody.responseHeader.receivedDate).toEqual(
                        DefaultContext.responseHeader.receivedDate,
                    );

                    await checkServiceOrder("完了");
                    const line = await LinesEntity.findOne({
                        where: {
                            lineId: DefaultContext.jsonBody.lineNo,
                        },
                    });
                    expect(line).not.toBeNull();
                    expect(line.updatedAt.getTime()).toBeGreaterThan(
                        lineUpdatedAt.getTime(),
                    );

                    // check updates
                    expect(line.contractType).toEqual(contractTypeName);
                    expect(line.nwModifyFlag).toEqual(true);
                    expect(line.deviceTypeId).toEqual(
                        DefaultContext.jsonBody.cardTypeId,
                    );

                    await checkCoreSwimmyApiLog(
                        responseHeader.apiProcessID,
                        false,
                    );
                },
            );
        },
    );

    describe("OK (CODE_000000) [csvUnnecessaryFlag is undefined]", () => {
        const contractTypePair = Object.entries(contractMap);
        test.each(contractTypePair)(
            "should return CODE_000000 if no error 【%s: %s】",
            async (access, contractTypeName) => {
                DefaultContext.jsonBody.access = access;

                delete DefaultContext.jsonBody.csvUnnecessaryFlag;
                const result = await NetworkContractChangeHandler.Handler(
                    DefaultRequest,
                    DefaultContext,
                );
                expect(typeof result).toBe("object");
                expect(result).toHaveProperty("jsonBody");
                expect(result.jsonBody).toHaveProperty("responseHeader");
                expect(result.jsonBody.responseHeader.apiProcessID).toEqual(
                    DefaultContext.responseHeader.apiProcessID,
                );
                expect(result.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_000000,
                );
                expect(result.jsonBody.responseHeader.sequenceNo).toEqual(
                    DefaultContext.responseHeader.sequenceNo,
                );
                expect(result.jsonBody.responseHeader.receivedDate).toEqual(
                    DefaultContext.responseHeader.receivedDate,
                );

                await checkServiceOrder("完了");
                const line = await LinesEntity.findOne({
                    where: {
                        lineId: DefaultContext.jsonBody.lineNo,
                    },
                });
                expect(line).not.toBeNull();
                expect(line.updatedAt.getTime()).toBeGreaterThan(
                    lineUpdatedAt.getTime(),
                );

                // check updates
                expect(line.contractType).toEqual(contractTypeName);
                expect(line.nwModifyFlag).toEqual(true);
                expect(line.deviceTypeId).toEqual(
                    DefaultContext.jsonBody.cardTypeId,
                );

                await checkCoreSwimmyApiLog(responseHeader.apiProcessID, true);
            },
        );
    });

    describe("NG (CODE_60xxxx)", () => {
        test("should return CODE_600101 if tenantId is not Front API tenant", async () => {
            DefaultContext.jsonBody.tenantId = "TSA000";
            const result = await NetworkContractChangeHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_600101,
            );

            await checkServiceOrder("失敗");
            const line = await LinesEntity.findOne({
                where: {
                    lineId: DefaultContext.jsonBody.lineNo,
                },
            });
            expect(line).not.toBeNull();
            expect(line.updatedAt.getTime()).toEqual(lineUpdatedAt.getTime()); // no update

            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
        });

        test("should return CODE_600102 if line not found", async () => {
            DefaultContext.jsonBody.lineNo = testdata.lineNotExists;
            const result = await NetworkContractChangeHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_600102,
            );

            await checkServiceOrder("失敗");
            const line = await LinesEntity.findOne({
                where: {
                    lineId: DefaultContext.jsonBody.lineNo,
                },
            });
            expect(line).toBeNull();

            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
        });

        test("should return CODE_600102 if line status is not 01", async () => {
            DefaultContext.jsonBody.lineNo = testdata.lineStatusNG;
            const result = await NetworkContractChangeHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_600102,
            );

            await checkServiceOrder("失敗");
            const line = await LinesEntity.findOne({
                where: {
                    lineId: DefaultContext.jsonBody.lineNo,
                },
            });
            expect(line).not.toBeNull();
            expect(line.updatedAt.getTime()).toEqual(lineUpdatedAt.getTime()); // no update

            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
        });

        test("should return CODE_600103 if line not linked to tenant", async () => {
            DefaultContext.jsonBody.lineNo = testdata.lineNotLinked;
            const result = await NetworkContractChangeHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_600103,
            );

            await checkServiceOrder("失敗");
            const line = await LinesEntity.findOne({
                where: {
                    lineId: DefaultContext.jsonBody.lineNo,
                },
            });
            expect(line).not.toBeNull();
            expect(line.updatedAt.getTime()).toEqual(lineUpdatedAt.getTime()); // no update

            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
        });

        test("should return CODE_600104 if line has abolition order", async () => {
            DefaultContext.jsonBody.lineNo = testdata.lineWithAbolish;
            const result = await NetworkContractChangeHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_600104,
            );

            await checkServiceOrder("失敗");
            const line = await LinesEntity.findOne({
                where: {
                    lineId: DefaultContext.jsonBody.lineNo,
                },
            });
            expect(line).not.toBeNull();
            expect(line.updatedAt.getTime()).toEqual(lineUpdatedAt.getTime()); // no update

            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
        });

        // NOTE unskip if need to test lock (took about 8~9 seconds to complete)
        describe.skip("lock test", () => {
            test("should return CODE_600201 if failed to acquire lock (after 5 retries)", async () => {
                let tx: Transaction;
                try {
                    tx = await sequelize.transaction();
                    await sequelize.query(
                        "select line_id from lines where line_id = :line_id for update nowait",
                        {
                            replacements: { line_id: testdata.lineOK },
                            transaction: tx,
                        },
                    );

                    const result = await NetworkContractChangeHandler.Handler(
                        DefaultRequest,
                        DefaultContext,
                    );
                    await tx.rollback(); // release lock before checking result
                    tx = null;
                    expect(typeof result).toBe("object");
                    expect(result).toHaveProperty("jsonBody");
                    expect(result.jsonBody).toHaveProperty("responseHeader");
                    expect(result.jsonBody.responseHeader.processCode).toEqual(
                        ResultCdConstants.CODE_600201,
                    );

                    await checkServiceOrder("失敗");
                    const line = await LinesEntity.findOne({
                        where: {
                            lineId: DefaultContext.jsonBody.lineNo,
                        },
                    });
                    expect(line).not.toBeNull();
                    expect(line.updatedAt.getTime()).toEqual(
                        lineUpdatedAt.getTime(),
                    ); // no update

                    await checkCoreSwimmyApiLog(
                        responseHeader.apiProcessID,
                        false,
                    );
                } catch (e) {
                    // if error, mark as failed
                    expect(e).toBeNull();
                } finally {
                    if (tx) await tx.rollback();
                }
            });

            test("should return CODE_000000 if lock is acquired (after some retries)", async () => {
                let tx: Transaction;
                try {
                    tx = await sequelize.transaction();
                    await sequelize.query(
                        "select line_id from lines where line_id = :line_id for update nowait",
                        {
                            replacements: { line_id: testdata.lineOK },
                            transaction: tx,
                        },
                    );

                    // keep trying to acquire lock
                    const resultPromise = NetworkContractChangeHandler.Handler(
                        DefaultRequest,
                        DefaultContext,
                    );

                    // rollback after ~3 seconds
                    await new Promise((resolve) => setTimeout(resolve, 3000));
                    const [result] = await Promise.all([
                        resultPromise,
                        tx.rollback(),
                    ]);

                    tx = null;

                    expect(typeof result).toBe("object");
                    expect(result).toHaveProperty("jsonBody");
                    expect(result.jsonBody).toHaveProperty("responseHeader");
                    expect(result.jsonBody.responseHeader.apiProcessID).toEqual(
                        DefaultContext.responseHeader.apiProcessID,
                    );
                    expect(result.jsonBody.responseHeader.processCode).toEqual(
                        ResultCdConstants.CODE_000000,
                    );
                    expect(result.jsonBody.responseHeader.sequenceNo).toEqual(
                        DefaultContext.responseHeader.sequenceNo,
                    );
                    expect(result.jsonBody.responseHeader.receivedDate).toEqual(
                        DefaultContext.responseHeader.receivedDate,
                    );

                    await checkServiceOrder("完了");
                    const line = await LinesEntity.findOne({
                        where: {
                            lineId: DefaultContext.jsonBody.lineNo,
                        },
                    });
                    expect(line).not.toBeNull();
                    expect(line.updatedAt.getTime()).toBeGreaterThan(
                        lineUpdatedAt.getTime(),
                    );

                    // check updates
                    expect(line.contractType).toEqual(
                        contractMap[DefaultContext.jsonBody.access],
                    );
                    expect(line.nwModifyFlag).toEqual(true);
                    expect(line.deviceTypeId).toEqual(
                        DefaultContext.jsonBody.cardTypeId,
                    );

                    // sent to service bus
                    expect(enqueueMessage).toHaveBeenCalledTimes(1);
                } catch (e) {
                    // if error, mark as failed
                    expect(e).toBeNull();
                } finally {
                    if (tx) await tx.rollback();
                }
            });
        });

        test("should return CODE_600301 if card type is not found", async () => {
            DefaultContext.jsonBody.cardTypeId = "ZZZ";
            const result = await NetworkContractChangeHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_600301,
            );

            await checkServiceOrder("失敗");
            const line = await LinesEntity.findOne({
                where: {
                    lineId: DefaultContext.jsonBody.lineNo,
                },
            });
            expect(line).not.toBeNull();
            expect(line.updatedAt.getTime()).toEqual(lineUpdatedAt.getTime()); // no update

            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
        });
    });

    test("should return null", async () => {
        const result = NetworkContractChangeHandler.Meta.getOrderType(request);
        expect(result).toBeNull();
    });
});
