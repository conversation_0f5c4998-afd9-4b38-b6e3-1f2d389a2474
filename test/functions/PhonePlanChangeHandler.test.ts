import { describeWithDB, generateProcessID } from "../testing/TestHelper";
import { PhonePlanChangeHandler } from "@/functions/PhonePlanChangeHandler";
import PhonePlanChangeService from "@/core/service/impl/PhonePlanChangeService";
import { ConnectionTimedOutError, Sequelize, Transaction } from "sequelize";
import { formatDate } from "date-fns";
import ResultCdConstants from "@/core/constant/ResultCdConstants";
import defaultRequest from "../testing/DefaultRequest";
import defaultContext from "../testing/DefaultContext";
import { disconnectMongo, useMongo } from "@/database/mongo";
import { usePsql } from "@/database/psql";
import { LinesEntity } from "@/core/entity/LinesEntity";
import { describe, expect, jest, test } from "@jest/globals";
import DefaultContext from "../testing/DefaultContext";
import MvnoUtil from "@/core/common/MvnoUtil";
import TenantManage from "@/core/common/TenantManage";
import APICommonDAO from "@/core/dao/APICommonDAO";
import APILinesDAO from "@/core/dao/APILinesDAO";
import ServiceOrdersEntity from "@/core/entity/ServiceOrdersEntity";
import DefaultRequest from "../testing/DefaultRequest";
import AppConfig from "@/appconfig";
import PhonePlanChangeInputDto from "@/core/dto/PhonePlanChangeInputDto";


describeWithDB("59 0035でんわプラン変更 /MVNO/api/V100/Lines/voice", () => {
    Object.defineProperty(PhonePlanChangeService, "apDbRetryInterval", { value: 0.01 });
    let sequelize: Sequelize;

    const testdata = {
        lineOK: "08024043001",
        lineNotExists: "08024043002",
        abolishSo: generateProcessID(),
    };

    const request = {
        requestHeader: {
            sequenceNo: "1234567",
            senderSystemId: "0001",
            apiKey: "",
            functionType: "59",
            receivedDate: formatDate(new Date(), "yyyy/MM/dd HH:mm:ss"),
        },
        lineNo: testdata.lineOK,
        tenantId: "OPF000",
        targetTenantId: "TST000",
        voicePlanId: "BtiredPlan",
    };

    const responseHeader = {
        sequenceNo: request.requestHeader.sequenceNo,
        receivedDate: request.requestHeader.receivedDate,
        processCode: ResultCdConstants.CODE_000000,
        apiProcessID: generateProcessID(),
    };

    const lineData = {
        lineId: testdata.lineOK,
        lineStatus: "01",
        nnumber: "N121152174", // NOTE TST000's nnumber
        groupId: "800001",
        lineActDate: new Date(),
        simNumber: "DN999999999999",
        modelType: "SIMのみ LTE SIM",
        imei: "PSIDimei",
        startDate: new Date(),
        domain: "domain",
        authId: "authId",
        actEastIp: "***********",
        actWestIp: "***********",
        sbyIp: "***********",
        fixedIp: "***********",
        pricePlanId: "AR550",
        pricePlanName: "[docomo] C_OCN卸プラン 先行/後発_日次 LTE(音声)",
        poi: "東POI接続（222）",
        createdAt: new Date(),
        updatedAt: new Date(),
        modifyFlag: false,
        contractType: "LTE(音声)",
        simType: "nanoSIM",
        deviceModelName: "UX302NC",
        authPattern: "1",
        imeisv_2: "S2001114",
        imeisv_3: "S2003011",
        notes: "notes",
        tempRegist: false,
        serviceDate: new Date(),
        roamingMaxId: "AO105",
        voiceMailId: null,
        callWaitingId: "AO150",
        intlCallId: "AO122",
        forwardingId: "AO144",
        intlForwardingId: "AO149",
        deviceTypeId: "A11",
        simFlag: false,
        activateDate: new Date(),
        imsi: "123456789012345",
        puk1: "87654321",
        puk2: "98765432",
        usageStatus: 0,
        nwModifyFlag: false,
        voicePlanId: "BtiredPlan",
        voicePlanName: "",
        tenantId: request.tenantId,
    } as LinesEntity;

    beforeAll(async () => {
        await AppConfig.loadCoreMvnoConfig(DefaultContext);
        sequelize = await usePsql();
        await useMongo(DefaultContext);
        await LinesEntity.destroy({ where: { lineId: testdata.lineOK } });
        await LinesEntity.create(lineData);
    });

    afterEach(async () => {
        jest.restoreAllMocks();
        jest.clearAllMocks();
        await ServiceOrdersEntity.destroy({
            where: {
                serviceOrderId: responseHeader.apiProcessID,
            },
        });
    });

    afterAll(async () => {
        await LinesEntity.destroy({ where: { lineId: testdata.lineOK } });
        console.log("sequelize close");
        await sequelize.close();
        await disconnectMongo(DefaultContext);
    });

    beforeEach(async () => {
        defaultContext.jsonBody = Object.assign({}, request);
        defaultContext.responseHeader = Object.assign({}, responseHeader);
        jest.spyOn(MvnoUtil, "getDateTimeNow").mockReturnValue(responseHeader.receivedDate);
    });

    it("Should return 590101 for failing OPF tenant ID check", async () => {
        defaultContext.jsonBody.tenantId = "TST000";
        const result = await PhonePlanChangeHandler.Handler(defaultRequest, defaultContext);
        expect(result.jsonBody).toMatchObject({
            responseHeader: {
                ...responseHeader,
                processCode: ResultCdConstants.CODE_590101,
            },
        });
    });

    it("Should return 590102 for failing abone line check", async () => {
        jest.spyOn(APILinesDAO.prototype, "getLineInfoByLines").mockReturnValue(null);
        const result = await PhonePlanChangeHandler.Handler(defaultRequest, defaultContext);
        expect(result.jsonBody).toMatchObject({
            responseHeader: {
                ...responseHeader,
                processCode: ResultCdConstants.CODE_590102,
            },
        });
    });

    it("Should return 590102 if getLineInfoByLines fails to query", async () => {
        jest.spyOn(APILinesDAO.prototype, "getLineInfoByLines").mockImplementation(async ()=>{
            throw new ConnectionTimedOutError(new Error("timeout error"));
        })
        const result = await PhonePlanChangeHandler.Handler(defaultRequest, defaultContext);
        expect(result.jsonBody).toMatchObject({
            responseHeader: {
                ...responseHeader,
                processCode: ResultCdConstants.CODE_590102,
            },
        });
    });

    it("should return 590103 for failing line tenant relation check", async () => {
        jest.spyOn(TenantManage.prototype, "doCheck").mockReturnValue(Promise.resolve([false, ""]));
        const result = await PhonePlanChangeHandler.Handler(defaultRequest, defaultContext);
        expect(result.jsonBody).toMatchObject({
            responseHeader: {
                ...responseHeader,
                processCode: ResultCdConstants.CODE_590103,
            },
        });
    });

    it("should return 590103 if doCheck fails to query", async () => {
        jest.spyOn(TenantManage.prototype, "doCheck").mockImplementation(async ()=>{
            throw new ConnectionTimedOutError(new Error("timeout error"));
        })
        const result = await PhonePlanChangeHandler.Handler(defaultRequest, defaultContext);
        expect(result.jsonBody).toMatchObject({
            responseHeader: {
                ...responseHeader,
                processCode: ResultCdConstants.CODE_590103,
            },
        });
    });

    it("should return 590104 for failing getting the voice plan name", async () => {
        jest.spyOn(APICommonDAO.prototype, "getVoicePlanName").mockReturnValue(Promise.resolve(null));
        const result = await PhonePlanChangeHandler.Handler(defaultRequest, defaultContext);
        expect(result).toMatchObject({
            jsonBody: {
                responseHeader: {
                    ...responseHeader,
                    processCode: ResultCdConstants.CODE_590104,
                },
            },
        });
    });

    it("should return 590104 if getVoicePlanName fails to query", async () => {
        jest.spyOn(APICommonDAO.prototype, "getVoicePlanName").mockImplementation(async ()=>{
            throw new ConnectionTimedOutError(new Error("timeout error"));
        })
        const result = await PhonePlanChangeHandler.Handler(defaultRequest, defaultContext);
        expect(result).toMatchObject({
            jsonBody: {
                responseHeader: {
                    ...responseHeader,
                    processCode: ResultCdConstants.CODE_590104,
                },
            },
        });
    });

    // NOTE remove .skip if you need to test lock (took about 8~9 seconds to complete)
    describe.skip("lock test", () => {
        it.skip("should return 590201 if failed to acquire lock (after 5 retries)", async () => {
            let tx: Transaction;
            try {
                tx = await sequelize.transaction();
                await sequelize.query(
                    "select line_id from lines where line_id = :line_id for update nowait",
                    {
                        replacements: { line_id: testdata.lineOK },
                        transaction: tx,
                    },
                );
                const result = await PhonePlanChangeHandler.Handler(defaultRequest, defaultContext);
                await tx.rollback(); // release lock before checking result
                tx = null;
                expect(result).toMatchObject({
                    jsonBody: {
                        responseHeader: {
                            ...responseHeader,
                            processCode: ResultCdConstants.CODE_590201,
                        },
                    },
                });

                const line = await LinesEntity.findOne({
                    where: {
                        lineId: DefaultContext.jsonBody.lineNo,
                    },
                });
                expect(line).not.toBeNull();
            } catch (error) {
                expect(error).toBeNull();
            } finally {
                if (tx) {
                    await tx.rollback();
                    tx = null;
                }
            }
        });

        test("should return CODE_000000 if lock is acquired (after some retries)", async () => {
            let tx: Transaction;
            try {
                tx = await sequelize.transaction();
                await sequelize.query(
                    "select line_id from lines where line_id = :line_id for update nowait",
                    {
                        replacements: { line_id: testdata.lineOK },
                        transaction: tx,
                    },
                );

                // keep trying to acquire lock
                const resultPromise = PhonePlanChangeHandler.Handler(
                    DefaultRequest,
                    DefaultContext,
                );

                // rollback after ~3 seconds
                const [result] = await Promise.all([
                    resultPromise,
                    setTimeout(() => tx.rollback(), 3000),
                ]);

                tx = null;
                expect(result).toMatchObject({
                    jsonBody: {
                        responseHeader: {
                            ...responseHeader,
                            processCode: ResultCdConstants.CODE_000000,
                        },
                    },
                });
                const line = await LinesEntity.findOne({
                    where: {
                        lineId: DefaultContext.jsonBody.lineNo,
                    },
                });
                expect(line).not.toBeNull();
                expect(line.voicePlanId).toBe(DefaultContext.jsonBody.voicePlanId);
                expect(line.voicePlanName).toBe("従量プラン");
            } catch (e) {
                // if error, mark as failed
                expect(e).toBeNull();
            } finally {
                if (tx) await tx.rollback();
            }
        });
    });
    
    it("should return CODE_590201 if getLineInfoforUpdate returns null", async () => {
        jest.spyOn(APILinesDAO.prototype, "getLineInfoforUpdate").mockResolvedValue(null)
        const result = await PhonePlanChangeHandler.Handler(defaultRequest, defaultContext);
        expect(result).toMatchObject({
            jsonBody: {
                responseHeader: {
                    ...responseHeader,
                    processCode: ResultCdConstants.CODE_590201,
                },
            },
        });
    });

    test("should return 590201 if failed to acquire lock (after 5 retries)", async () => {
        let tx: Transaction;
        const aPILinesDAO = new APILinesDAO(
            DefaultRequest,
            DefaultContext,
        );
        
        const instance = new PhonePlanChangeService(defaultRequest,defaultContext)
        Object.defineProperty(instance, "LOCK_WAIT_MILLISEC", {
            value: 1,
        });

        try {
            tx = await sequelize.transaction();
            await aPILinesDAO.getLineInfoforUpdate(
                request.lineNo,
                tx,
            );
            const __request = {
                requestHeader: request.requestHeader,
                lineNo: request.lineNo,
                tenantId: request.tenantId,
                targetTenantId: request.targetTenantId,
                voicePlanId: request.voicePlanId,
                targetSoId: undefined,
                reserve_flag: undefined,
                reserve_soId: undefined,
            } as PhonePlanChangeInputDto;

            const result = await instance.service(__request)
            expect(result).toMatchObject({
                jsonBody: {
                    responseHeader: {
                        ...responseHeader,
                        processCode: ResultCdConstants.CODE_590201,
                    },
                },
            });
        } finally {
            await tx.rollback();
        }
    });

    it("should return 590301 if failed to update line voice plan", async () => {
        jest.spyOn(APILinesDAO.prototype, "updateLineVoicePlan").mockImplementation(() => {
            throw new Error("Test Error");
        });
        const result = await PhonePlanChangeHandler.Handler(defaultRequest, defaultContext);
        expect(result).toMatchObject({
            jsonBody: {
                responseHeader: {
                    ...responseHeader,
                    processCode: ResultCdConstants.CODE_590301,
                },
            },
        });
    });

    it("should return 000000 if successfully update line voice plan", async () => {
        const result = await PhonePlanChangeHandler.Handler(defaultRequest, defaultContext);
        expect(result).toMatchObject({
            jsonBody: {
                responseHeader: {
                    ...responseHeader,
                    processCode: ResultCdConstants.CODE_000000,
                },
            },
        });
        const line = await LinesEntity.findOne({
            where: {
                lineId: DefaultContext.jsonBody.lineNo,
            },
        });
        expect(line).not.toBeNull();
        expect(line.voicePlanId).toBe(DefaultContext.jsonBody.voicePlanId);
        expect(line.voicePlanName).toBe("従量プラン");
    });

});