import { afterAll, beforeEach, describe, expect, jest } from "@jest/globals";
const enqueueMessage = jest.fn();
jest.mock("../../src/services/servicebus", () => ({
    __esModule: true,
    default: enqueueMessage,
}));

import LineAddAcquisitionInputDto from '@/core/dto/LineAddAcquisitionInputDto';
import ResultCdConstants from '@/core/constant/ResultCdConstants';
import DefaultContext from "../testing/DefaultContext";
import DefaultRequest from "../testing/DefaultRequest";
import { LinesEntity } from "@/core/entity/LinesEntity";
import { Sequelize } from "sequelize";
import AppConfig from "@/appconfig";
import { usePsql } from "@/database/psql";
import { useMongo, disconnectMongo } from "@/database/mongo";
import { LineAddAcquisitionHandler } from "@/functions/LineAddAcquisitionHandler";
import { addDays, format, formatDate } from "date-fns";
import { generateProcessID, generateSOID, useFasterRetries } from "../testing/TestHelper";
import ResponseHeader from "@/core/dto/ResponseHeader";
import PlansEntity from "@/core/entity/PlansEntity";
import SOAPTestHelper from "../testing/soapTestHelper";
import nock from "nock";
import { getConfigDataForTest } from "../testing/testdataHelper";
import ServiceOrdersEntity from "@/core/entity/ServiceOrdersEntity";
import Check from "@/core/common/Check";
import TenantManage from "@/core/common/TenantManage";
import LineTenantsEntity from "@/core/entity/LineTenantsEntity";
import APICommonDAO from "@/core/dao/APICommonDAO";
import ApiCommon from "@/core/common/ApiCommon";
import SOAPCommon from "@/core/common/SOAPCommon";
import SOAPCommonOutputDto from "@/core/dto/SOAPCommonOutputDto";
import TenantPlansEntity from "@/core/entity/TenantPlansEntity";
import OptionPlansEntity from "@/core/entity/OptionPlansEntity";
import OptionPlanParametersEntity from "@/core/entity/OptionPlanParametersEntity";
import APILinesDAO from "@/core/dao/APILinesDAO";
import CheckUtil from "@/core/common/CheckUtil";
import { readXML } from "@/core/common/SOAPCommonUtils";
import CoreSwimmyApiLog from "@/models/coreSwimmyApiLog";
import ParameterName from "@/core/dto/ParameterName";
import { TenantsEntity } from "@/core/entity/TenantsEntity";


describe("LineAddAcquisitionHandler", () => {
    let sequelize: Sequelize;

    const responseHeader = {
        sequenceNo: "1234567",
        receivedDate: formatDate(new Date(), "yyyy/MM/dd HH:mm:ss"),
        processCode: ResultCdConstants.CODE_000000,
        apiProcessID: generateProcessID(),
    } as ResponseHeader;

    const testData = {
        requestHeader: {
            sequenceNo: "1234567",
            senderSystemId: "0001",
            apiKey: "",
            functionType: "03",
        },
        tenantId: "TST000",
        potalPlanID: "10000",
        optionPlanId: "99999",
        lineNo: "99999999999",
        targetSoId: generateSOID(),
    } as LineAddAcquisitionInputDto;
    const testDataRKM = {
        requestHeader: {
            sequenceNo: "1234567",
            senderSystemId: "0001",
            apiKey: "",
            functionType: "03",
        },
        tenantId: "RKM000",
        potalPlanID: "10000",
        optionPlanId: "99999",
        lineNo: "99999999999",
        targetSoId: generateSOID(),
    } as LineAddAcquisitionInputDto;
    
    function setOrderType(orderType: "0" | "1" | "2" | "9") {
        const nextMonthDate = new Date();
        nextMonthDate.setMonth(nextMonthDate.getMonth() + 1);
        nextMonthDate.setHours(0, 0, 0, 0);
        switch (orderType) {
            case "1":
                DefaultContext.jsonBody.reserve_date = formatDate(nextMonthDate, "yyyy/MM/dd HH:mm");
                DefaultContext.jsonBody.reserve_flag = false;
                DefaultContext.jsonBody.reserve_soId = null;
                break;
            case "2":
                DefaultContext.jsonBody.reserve_date = formatDate(nextMonthDate, "yyyy/MM/dd HH:mm");
                DefaultContext.jsonBody.reserve_flag = true;
                DefaultContext.jsonBody.reserve_soId = responseHeader.apiProcessID;
                break;
            case "9":
                DefaultContext.jsonBody.reserve_date = null;
                DefaultContext.jsonBody.reserve_flag = false;
                DefaultContext.jsonBody.reserve_soId = responseHeader.apiProcessID;
                break;
            case "0":
            default:
                DefaultContext.jsonBody.reserve_date = null;
                DefaultContext.jsonBody.reserve_flag = null;
                DefaultContext.jsonBody.reserve_soId = null;
                break;
        }
    }

    let configData: any;

    beforeAll(async () => {
        sequelize = await usePsql();
        await useMongo(DefaultContext);
        await AppConfig.loadCoreMvnoConfig(DefaultContext);
        configData = getConfigDataForTest();
        try {
            await Promise.all([
                LinesEntity.create({
                    lineId: testData.lineNo,
                    lineStatus: "01",
                    nnumber: "N100000100",
                    simFlag: false,
                    pricePlanId: "AB123",
                    voiceMailId: "AO143",
                }),
                PlansEntity.create({
                    planId: parseInt(testData.potalPlanID, 10),
                    planClass: 1,
                    policyId: 201,
                    network: "LTE",
                    planName: "test",
                    planDescription: "test",
                    refServicePattern: 1,
                    tpcServicePattern: 1,
                    defaultSimFlag: false,
                    planIdT: "T2010001",
                    pricePlanId: "AB123"
                }),
                OptionPlansEntity.create({
                    optionPlanId: parseInt(testData.optionPlanId, 10),
                    optionPlanName: "test",
                    optionPlanDescription: "test",
                    optionPlanType: "03",
                    capacityCode: "03001",
                    optionPlanIdT: "T30" + testData.optionPlanId
                }),
            ]);

            await Promise.all([
                LineTenantsEntity.create({
                    lineId: testData.lineNo,
                    tenantId: testData.tenantId
                }),
                TenantPlansEntity.create({
                    tenantId: testData.tenantId,
                    planId: parseInt(testData.potalPlanID, 10),
                    changePlanId: parseInt(testData.potalPlanID, 10),
                    changeCount: 1,
                    changeTiming: "01",
                }),
                OptionPlanParametersEntity.create({
                    optionPlanId: parseInt(testData.optionPlanId, 10),
                    key: "add_bucket",
                    value: "512"
                }),
                OptionPlanParametersEntity.create({
                    optionPlanId: parseInt(testData.optionPlanId, 10),
                    key: "addbd_bucket",
                    value: "512"
                }),
                OptionPlanParametersEntity.create({
                    optionPlanId: parseInt(testData.optionPlanId, 10),
                    key: "exp_date",
                    value: "0"
                })
            ]);
            await sequelize.query(
                "insert into plan_option_plans(plan_id, option_plan_id) values (:planId, :optionPlanId)",
                {
                    replacements: {
                        planId: parseInt(testData.potalPlanID, 10),
                        optionPlanId: parseInt(testData.optionPlanId, 10),
                    },
                },
            );
            // create test data for RINKモバイル
            await Promise.all([
                LineTenantsEntity.create({
                    lineId: testData.lineNo,
                    tenantId: testDataRKM.tenantId
                }),
                TenantPlansEntity.create({
                    tenantId: testDataRKM.tenantId,
                    planId: parseInt(testData.potalPlanID, 10),
                    changePlanId: parseInt(testData.potalPlanID, 10),
                    changeCount: 1,
                    changeTiming: "01",
                }),
            ]);
            SOAPTestHelper.setup();
            useFasterRetries();
        } catch (e) {
            console.error(e);
            throw e;
        }
    });

    afterAll(async () => {
        try {
            await sequelize.query("delete from plan_option_plans where plan_id = :planId", {
                replacements: { planId: testData.potalPlanID },
            });
            await OptionPlanParametersEntity.destroy({ where: { optionPlanId: parseInt(testData.optionPlanId, 10) } });
            await TenantPlansEntity.destroy({ where: { planId: testData.potalPlanID } });
            await LineTenantsEntity.destroy({ where: { tenantId: testData.tenantId } });
            await LineTenantsEntity.destroy({ where: { tenantId: testDataRKM.tenantId } });
            await OptionPlansEntity.destroy({ where: { optionPlanId: testData.optionPlanId } });
            await PlansEntity.destroy({ where: { planId: testData.potalPlanID } });
            await LinesEntity.destroy({ where: { lineId: testData.lineNo } });
        } catch (e) {
            console.error(e);
            throw e;
        }
        await disconnectMongo(DefaultContext);
        await sequelize.close();
    });

    beforeEach(async () => {
        (enqueueMessage as jest.Mock).mockClear();
        DefaultContext.jsonBody = Object.assign({}, testData);
        DefaultContext.responseHeader = Object.assign({}, responseHeader);
    });

    afterEach(async () => {
        SOAPTestHelper.tearDown();
        jest.restoreAllMocks();
        jest.clearAllMocks();
        jest.resetModules();
        setOrderType("0");
        await ServiceOrdersEntity.destroy({ where: { serviceOrderId: responseHeader.apiProcessID } });
        await CoreSwimmyApiLog.deleteMany({ requestOrderId: responseHeader.apiProcessID });
    });

    /**
     * check whether SwimmyApiLog exists and Service Bus is called
     */
    const checkCoreSwimmyApiLog = async (
        exists: boolean,
    ) => {
        const apiLog = await CoreSwimmyApiLog.findOne({
            requestOrderId: responseHeader.apiProcessID,
        });
        if (exists) {
            expect(apiLog).not.toBeNull();
            expect(enqueueMessage).toBeCalledTimes(1);
        } else {
            expect(apiLog).toBeNull();
            expect(enqueueMessage).not.toBeCalled();
        }
    };

    async function throwSqlException(...args: any[]): Promise<any> {
        await LinesEntity.create({
            lineId: testData.lineNo,
            lineStatus: "01",
            nnumber: "N100000100",
            simFlag: false,
            pricePlanId: "AB123",
            voiceMailId: "AO143",
        });
        throw new Error("Error");
    }

    async function mockPlansEntity(pattern: number, policyId: number) {
        const plan = await PlansEntity.findOne({ where: { planId: parseInt(testData.potalPlanID, 10) } });
        plan.tpcServicePattern = pattern;
        plan.policyId = policyId;
        jest.spyOn(APICommonDAO.prototype, "getPlans").mockImplementation(async () => {
            return [plan];
        });
    }

    describe('OK Case', () => {
        beforeEach(() => {
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, SOAPTestHelper.simpleResponse("OK"))
                .persist();
        });

        test.each([
            { orderType: "0", pattern: 1 },
            { orderType: "0", pattern: 2 },
            { orderType: "0", pattern: 3 },
            { orderType: "0", pattern: 4 },
            { orderType: "0", pattern: 5 },
            { orderType: "1", pattern: 1 },
            { orderType: "1", pattern: 2 },
            { orderType: "1", pattern: 3 },
            { orderType: "1", pattern: 4 },
            { orderType: "1", pattern: 5 },
            { orderType: "2", pattern: 1 },
            { orderType: "2", pattern: 2 },
            { orderType: "2", pattern: 3 },
            { orderType: "2", pattern: 4 },
            { orderType: "2", pattern: 5 },
        ])('ordertype = $orderType, pattern = $pattern', async ({ orderType, pattern }) => {
            await mockPlansEntity(pattern, 201);
            DefaultContext.jsonBody = testData;
            setOrderType(orderType as "0" | "1" | "2" | "9");

            if (orderType === "2") {
                await ServiceOrdersEntity.create({
                    serviceOrderId: responseHeader.apiProcessID,
                    tenantId: testData.tenantId,
                    lineId: testData.lineNo,
                    orderStatus: "予約中",
                    orderType: "クーポン追加",
                    reserveDate: new Date(),
                    functionType: "03",
                    orderDate: new Date(),
                })
            }

            const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);

            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );

            // check should be inserted to DB
            const serviceOrder = await ServiceOrdersEntity.findOne({
                where: {
                    serviceOrderId: responseHeader.apiProcessID,
                },
            });
            expect(serviceOrder).not.toBeNull();
            expect(serviceOrder.tenantId).toEqual(testData.tenantId);
            expect(serviceOrder.lineId).toEqual(testData.lineNo);
            expect(serviceOrder.orderType).toEqual("クーポン追加");
            if (orderType === "0") {
                expect(serviceOrder.orderStatus).toEqual("完了");
                expect(serviceOrder.reserveDate).toBeNull();
                await checkCoreSwimmyApiLog(true);
            } else if (orderType === "1") {
                expect(serviceOrder.orderStatus).toEqual("予約中");
                expect(serviceOrder.reserveDate).not.toBeNull();
                await checkCoreSwimmyApiLog(false);
            } else if (orderType === "2") {
                expect(serviceOrder.orderStatus).toEqual("完了");
                await checkCoreSwimmyApiLog(true);
            }
        });

        it("should not return error if potalPlanID is given as number", async () => {
            DefaultContext.jsonBody = structuredClone(testData);
            DefaultContext.jsonBody.potalPlanID = 10000 as any; // simulate number input
            setOrderType("0");
            const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_000000);
            await checkCoreSwimmyApiLog(true);
        });

        it("should not return error if optionPlanId is given as number", async () => {
            DefaultContext.jsonBody = structuredClone(testData);
            DefaultContext.jsonBody.optionPlanId = 99999 as any; // simulate number input
            setOrderType("0");
            const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_000000);
            await checkCoreSwimmyApiLog(true);
        });

        it("should not call swimmy if wholeFlag exists", async () => {
            jest.spyOn(APILinesDAO.prototype, "getWholeFlag").mockResolvedValueOnce([true]);

            const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_000000);
            await checkCoreSwimmyApiLog(false);
        });

        it("should call swimmy if SQL error during getting wholeFlag", async () => {
            jest.spyOn(APILinesDAO.prototype, "getWholeFlag").mockImplementation(throwSqlException);
            const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_000000);
            await checkCoreSwimmyApiLog(true);
        });

        it("should not call swimmy if checkResult[1] is null", async () => {
            jest.spyOn(TenantManage.prototype, "doCheck").mockResolvedValue([true, null]);

            const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_000000);
            await checkCoreSwimmyApiLog(false);
        });

        it("should not call swimmy if tenantTypeCheck = 0", async () => {
            // Mock tenant type to be 1 (C-OCN)
            jest.spyOn(APICommonDAO.prototype, "getTenants").mockResolvedValue({
                tenantName: "test",
                tenantType: 1,
                tenantId: testData.tenantId,
                office: true,
                status: true,
                tenantLevel: 1
            } as TenantsEntity);

            const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);

            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_000000);
            await checkCoreSwimmyApiLog(false);
        });

        it("should not call swimmy if SQL error during tenantTypeCheck", async () => {
            jest.spyOn(APICommonDAO.prototype, "getTenants")
                .mockResolvedValueOnce({
                    tenantName: "test",
                    tenantType: 1,
                    tenantId: testData.tenantId,
                    office: true,
                    status: true,
                    tenantLevel: 1
                } as TenantsEntity)
                .mockImplementation(throwSqlException);

            const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);

            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_000000);
            await checkCoreSwimmyApiLog(false);
        })

        it("should not call swimmy if SQL Error when getting TBan", async () => {
            jest.spyOn(APICommonDAO.prototype, "getOptionPlanTBan").mockImplementation(throwSqlException);
            const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_000000);
            await checkCoreSwimmyApiLog(false);
        });

        it("should not call swimmy if TBan is null", async () => {
            jest.spyOn(APICommonDAO.prototype, "getOptionPlanTBan").mockResolvedValue(null);
            const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_000000);
            await checkCoreSwimmyApiLog(false);
        });

        it("should not call swimmy if tBan format is invalid", async () => {
            jest.spyOn(APICommonDAO.prototype, "getOptionPlanTBan").mockResolvedValueOnce("!@#$%^&");
            const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_000000);
            await checkCoreSwimmyApiLog(false);
        });
    });
    describe('OK Case RINKモバイル', () => {
        beforeEach(() => {
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, SOAPTestHelper.simpleResponse("OK"))
                .persist();
        });

        test.each([
            { orderType: "0", pattern: 1 },
            { orderType: "0", pattern: 2 },
            { orderType: "0", pattern: 3 },
            { orderType: "0", pattern: 4 },
            { orderType: "0", pattern: 5 },
            { orderType: "1", pattern: 1 },
            { orderType: "1", pattern: 2 },
            { orderType: "1", pattern: 3 },
            { orderType: "1", pattern: 4 },
            { orderType: "1", pattern: 5 },
            { orderType: "2", pattern: 1 },
            { orderType: "2", pattern: 2 },
            { orderType: "2", pattern: 3 },
            { orderType: "2", pattern: 4 },
            { orderType: "2", pattern: 5 },
        ])('ordertype = $orderType, pattern = $pattern', async ({ orderType, pattern }) => {
            await mockPlansEntity(pattern, 201);
            DefaultContext.jsonBody = testDataRKM;
            setOrderType(orderType as "0" | "1" | "2" | "9");

            if (orderType === "2") {
                await ServiceOrdersEntity.create({
                    serviceOrderId: responseHeader.apiProcessID,
                    tenantId: testDataRKM.tenantId,
                    lineId: testDataRKM.lineNo,
                    orderStatus: "予約中",
                    orderType: "クーポン追加",
                    reserveDate: new Date(),
                    functionType: "03",
                    orderDate: new Date(),
                })
            }

            const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);

            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );

            // check should be inserted to DB
            const serviceOrder = await ServiceOrdersEntity.findOne({
                where: {
                    serviceOrderId: responseHeader.apiProcessID,
                },
            });
            expect(serviceOrder).not.toBeNull();
            expect(serviceOrder.tenantId).toEqual(testDataRKM.tenantId);
            expect(serviceOrder.lineId).toEqual(testDataRKM.lineNo);
            expect(serviceOrder.orderType).toEqual("クーポン追加");
            if (orderType === "0") {
                expect(serviceOrder.orderStatus).toEqual("完了");
                expect(serviceOrder.reserveDate).toBeNull();
            } else if (orderType === "1") {
                expect(serviceOrder.orderStatus).toEqual("予約中");
                expect(serviceOrder.reserveDate).not.toBeNull();
            } else if (orderType === "2") {
                expect(serviceOrder.orderStatus).toEqual("完了");
            }
            await checkCoreSwimmyApiLog(false);
        });
    });

    describe('NG Cases', () => {
        it("should return CODE_030105 if orderType = 9", async () => {
            setOrderType("9");
            const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_030105);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_999999 if orderType = 2, Check.checkReserve returns false", async () => {
            try {
                DefaultContext.isInternalRequest = false;
                setOrderType("2");
                jest.spyOn(Check, "checkReserve").mockReturnValue(false);
                const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
                expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_999999);
                await checkCoreSwimmyApiLog(false);
            } finally {
                DefaultContext.isInternalRequest = true;
            }
        });

        it("should return CODE_030101 if lineNo failed format check", async () => {
            DefaultContext.jsonBody.lineNo = "ABC";
            const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_030101);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_030101 if potalPlanID failed format check", async () => {
            DefaultContext.jsonBody.potalPlanID = "ABC";
            const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_030101);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_030101 if optionPlanId failed format check", async () => {
            DefaultContext.jsonBody.optionPlanId = "ABC";
            const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_030101);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_030101 if reserveDate failed format check, if orderType = 1/2", async () => {
            setOrderType("1");
            DefaultContext.jsonBody.reserve_date = "ABC";
            const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_030101);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_030106 if failed reservedDate < now, for orderType = 1", async () => {
            setOrderType("1");
            const currentDate = new Date();
            currentDate.setHours(currentDate.getHours() - 1);
            DefaultContext.jsonBody.reserve_date = formatDate(currentDate, "yyyy/MM/dd HH:mm");
            const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_030106);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_030107 if reservationDateExecutionUnits fails format check, for orderType = 1", async () => {
            setOrderType("1");
            jest.spyOn(Check, "checkReservationDateExecutionUnitsFmt").mockReturnValue(false);
            const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_030107);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_030108 if reserveDate fails relation check with reservationDateExecutionUnits, for orderType = 1", async () => {
            setOrderType("1");
            jest.spyOn(Check, "checkReservationDateExecutionUnits").mockReturnValue(false);
            const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_030108);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_030109 if reservationsLimitDays fails format check, for orderType = 1", async () => {
            setOrderType("1");
            jest.spyOn(Check, "checkReservationsLimitDaysFmt").mockReturnValue(false);
            const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_030109);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_030110 if reserveDate fails relation check with reservationsLimitDays, for orderType = 1", async () => {
            setOrderType("1");
            jest.spyOn(Check, "checkReservationsLimitDays").mockReturnValue(false);
            const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_030110);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_030102 if tenantManage.doCheck throws SQL Error", async () => {
            jest.spyOn(TenantManage.prototype, "doCheck").mockImplementation(throwSqlException);
            const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_030102);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_030102 if tenantManage.doCheck failed", async () => {
            jest.spyOn(TenantManage.prototype, "doCheck").mockResolvedValue([false, ""]);
            const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_030102);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_030103 if SQL Error thrown while getting tenant plans", async () => {
            jest.spyOn(APICommonDAO.prototype, "getTenantPlans").mockImplementation(throwSqlException);
            const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_030103);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_030103 if no tenant plans found", async () => {
            jest.spyOn(APICommonDAO.prototype, "getTenantPlans").mockResolvedValue([]);
            const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_030103);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_030111 if SQL Error while finding tenant", async () => {
            jest.spyOn(APICommonDAO.prototype, "getTenants").mockImplementation(throwSqlException);
            const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_030111);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_030111 if failed to find tenant for some reason", async () => {
            jest.spyOn(APICommonDAO.prototype, "getTenants").mockResolvedValue(null);
            const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_030111);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_030112 if tenant type != 1 and failed SQL Error at finding plan id", async () => {
            jest.spyOn(APILinesDAO.prototype, "getLineUsingPlan").mockImplementation(throwSqlException);
            const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_030112);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_030112 if tenant type != 1 and plan id not match", async () => {
            jest.spyOn(APILinesDAO.prototype, "getLineUsingPlan").mockResolvedValue(null);
            const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_030112);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_030104 if SQL Error when checking plan option plan", async () => {
            jest.spyOn(APICommonDAO.prototype, "getCheckedPlanOptionPlans").mockImplementation(throwSqlException);
            const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_030104);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_030104 if failed checking plan option plan", async () => {
            jest.spyOn(APICommonDAO.prototype, "getCheckedPlanOptionPlans").mockResolvedValue(null);
            const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_030104);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_030113 if SQL Error when checking abolish so", async () => {
            jest.spyOn(ApiCommon.prototype, "checkAbolishSo").mockImplementation(throwSqlException);
            const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_030113);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_030113 if failed checking abolish so", async () => {
            jest.spyOn(ApiCommon.prototype, "checkAbolishSo").mockResolvedValue(null);
            const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_030113);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_030114 if tenant type != 2/3/4 and SQL Error when checking line suspend", async () => {
            jest.spyOn(ApiCommon.prototype, "checkLineSuspend").mockImplementation(throwSqlException);
            const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_030114);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_030114 if tenant type != 2/3/4 and failed checking line suspend", async () => {
            jest.spyOn(ApiCommon.prototype, "checkLineSuspend").mockResolvedValue(null);
            const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_030114);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_030201 if SQL Error while getting plan list", async () => {
            jest.spyOn(APICommonDAO.prototype, "getPlans").mockImplementation(throwSqlException);
            const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_030201);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_030201 if no plans found", async () => {
            jest.spyOn(APICommonDAO.prototype, "getPlans").mockResolvedValue([]);
            const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_030201);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_030202 if plan's tpcServicePattern is null", async () => {
            await mockPlansEntity(null, 201);
            const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_030202);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_030202 if plan's policy id is null", async () => {
            await mockPlansEntity(1, null);
            const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_030202);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_030202 if plan's tpcServicePattern is not between 1-5", async () => {
            await mockPlansEntity(0, 201);
            const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_030202);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_030202 if fails format check for plan's policy id", async () => {
            await mockPlansEntity(1, 30);
            const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_030202);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_030310 if SQL Error during checking TPC Connection, if orderType = 0/2", async () => {
            setOrderType("0");
            jest.spyOn(TenantManage.prototype, "checkTpcConnection").mockImplementation(throwSqlException);
            const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_030310);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_030310 if failed checking TPC Connection, if orderType = 0/2", async () => {
            setOrderType("0");
            jest.spyOn(TenantManage.prototype, "checkTpcConnection").mockResolvedValue([false, "", ""]);
            const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_030310);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_030301 if SQL Error during getting option plan parameter, if tpcServicePattern is 1/3/4", async () => {
            await mockPlansEntity(1, 201);
            jest.spyOn(APICommonDAO.prototype, "getOptionPlanParameters").mockImplementation(throwSqlException);
            const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_030301);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_030301 if no option plan parameter found, if tpcServicePattern is 1/3/4", async () => {
            await mockPlansEntity(1, 201);
            jest.spyOn(APICommonDAO.prototype, "getOptionPlanParameters").mockResolvedValue(null);
            const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_030301);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_030303 if SQL Error during getting option plan parameter, if tpcServicePattern is 2", async () => {
            await mockPlansEntity(2, 201);
            jest.spyOn(APICommonDAO.prototype, "getOptionPlanParameters").mockImplementation(throwSqlException);
            const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_030303);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_030303 if no option plan parameter found, if tpcServicePattern is 2", async () => {
            await mockPlansEntity(2, 201);
            jest.spyOn(APICommonDAO.prototype, "getOptionPlanParameters").mockResolvedValue(null);
            const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_030303);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_999999 if non SQL/SOAP error", async () => {
            jest.spyOn(APICommonDAO.prototype, "getPlans").mockImplementation(async () => {
                throw new Error("some error");
            });
            const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_999999);
            await checkCoreSwimmyApiLog(false);
        });

        describe('tpcServicePattern = 1/3/4', () => {
            beforeEach(async () => {
                await mockPlansEntity(1, 201);
            });
            it("should return CODE_030301 if SQL Error when getting option plan parameter for expiration date", async () => {
                jest.spyOn(APICommonDAO.prototype, "getOptionPlanParameters").mockImplementation(async (optionPlanId: string, paramKey: string) => {
                    if (paramKey === "expiration_date") {
                        await throwSqlException();
                    } else {
                        if (!CheckUtil.checkIsNotNull(optionPlanId)
                            && !CheckUtil.checkIsNotNull(paramKey)
                            && CheckUtil.checkIsNum(optionPlanId)) {
                            return await OptionPlanParametersEntity.findOne({
                                where: {
                                    optionPlanId: parseInt(optionPlanId, 10),
                                    key: paramKey,
                                },
                            });
                        }
                    }
                });
                const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
                expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_030301);
                await checkCoreSwimmyApiLog(false);
            });

            it("should return CODE_030301 if SQL Error when getting option plan parameter for exp_date", async () => {
                jest.spyOn(APICommonDAO.prototype, "getOptionPlanParameters").mockImplementation(async (optionPlanId: string, paramKey: string) => {
                    if (paramKey === "exp_date") {
                        await throwSqlException();
                    } else {
                        if (!CheckUtil.checkIsNotNull(optionPlanId)
                            && !CheckUtil.checkIsNotNull(paramKey)
                            && CheckUtil.checkIsNum(optionPlanId)) {
                            return await OptionPlanParametersEntity.findOne({
                                where: {
                                    optionPlanId: parseInt(optionPlanId, 10),
                                    key: paramKey,
                                },
                            });
                        }
                    }
                });
                const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
                expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_030301);
                await checkCoreSwimmyApiLog(false);
            });

            it("should return CODE_030301 if failed to get option plan parameter for expiration date", async () => {
                jest.spyOn(APICommonDAO.prototype, "getOptionPlanParameters").mockImplementation(async (optionPlanId: string, paramKey: string) => {
                    if (paramKey === "exp_date" || paramKey === "expiration_date") {
                        return null;
                    } else {
                        if (!CheckUtil.checkIsNotNull(optionPlanId)
                            && !CheckUtil.checkIsNotNull(paramKey)
                            && CheckUtil.checkIsNum(optionPlanId)) {
                            return await OptionPlanParametersEntity.findOne({
                                where: {
                                    optionPlanId: parseInt(optionPlanId, 10),
                                    key: paramKey,
                                },
                            });
                        }
                    }
                });
                const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
                expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_030301);
                await checkCoreSwimmyApiLog(false);
            });

            it("should return CODE_030302 if additionalCouponRemainCapacity out of limit", async () => {
                jest.spyOn(APICommonDAO.prototype, "getOptionPlanParameters").mockResolvedValueOnce({
                    value: "-1"
                } as OptionPlanParametersEntity);
                const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
                expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_030302);
                await checkCoreSwimmyApiLog(false);
            });

            it("should return CODE_030302 if additionalCouponTermValidity is out of range", async () => {
                jest.spyOn(APICommonDAO.prototype, "getOptionPlanParameters").mockResolvedValueOnce({
                    value: "1"
                } as OptionPlanParametersEntity).mockResolvedValueOnce({
                    value: "-1"
                } as OptionPlanParametersEntity);
                const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
                expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_030302);
                await checkCoreSwimmyApiLog(false);
            });

            it("should return CODE_030302 if additionalCouponTermValidityNichisu is out of range", async () => {
                jest.spyOn(APICommonDAO.prototype, "getOptionPlanParameters").mockResolvedValueOnce({
                    value: "1"
                } as OptionPlanParametersEntity).mockResolvedValueOnce(
                    null
                ).mockResolvedValueOnce({
                    value: "-1"
                } as OptionPlanParametersEntity);
                const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
                expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_030302);
                await checkCoreSwimmyApiLog(false);
            });

            it("should return CODE_100401 if soap handle code = 000951", async () => {
                const soapOutput = new SOAPCommonOutputDto();
                soapOutput.setProcessCode("000951");
                jest.spyOn(SOAPCommon.prototype, "callWebSoapApi").mockResolvedValue(soapOutput);
                const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
                expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_030401);
                await checkCoreSwimmyApiLog(false);
            });

            it("should return CODE_030401 if soap returns error", async () => {
                nock(configData.soapApiUrl)
                    .post("")
                    .reply(200, SOAPTestHelper.simpleResponse("NG", "Error"))
                    .persist();
                const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
                expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_030401);
                await checkCoreSwimmyApiLog(false);
            });
        });

        describe('tpcServicePattern = 2', () => {
            beforeEach(async () => {
                await mockPlansEntity(2, 201)
            });

            it("should return CODE_030303 if additionalCouponRemainCapacity is null", async () => {
                jest.spyOn(APICommonDAO.prototype, "getOptionPlanParameters").mockResolvedValueOnce(null);
                const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
                expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_030303);
                await checkCoreSwimmyApiLog(false);
            });

            it("should return CODE_030304 if additionalCouponRemainCapacity is out of range", async () => {
                jest.spyOn(APICommonDAO.prototype, "getOptionPlanParameters").mockResolvedValueOnce({
                    value: "-1"
                } as OptionPlanParametersEntity);
                const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
                expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_030304);
                await checkCoreSwimmyApiLog(false);
            });

            it("should return CODE_030401 if SOAP response code = 000951", async () => {
                const soapOutput = new SOAPCommonOutputDto();
                soapOutput.setProcessCode("000951");
                jest.spyOn(SOAPCommon.prototype, "callWebSoapApi").mockResolvedValue(soapOutput);
                const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
                expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_030401);
                await checkCoreSwimmyApiLog(false);
            });

            it("should return CODE_030401 if SOAP response is error", async () => {
                nock(configData.soapApiUrl)
                    .post("")
                    .reply(200, SOAPTestHelper.simpleResponse("NG", "Error"))
                    .persist();
                const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
                expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_030401);
                await checkCoreSwimmyApiLog(false);
            });
        });

        describe('tpcServicePattern = 5', () => {
            beforeEach(async () => {
                await mockPlansEntity(5, 201);
            });

            it("should return CODE_030305 if SQL Error during getting additionalCouponTermValidityNichisu", async () => {
                jest.spyOn(APICommonDAO.prototype, "getOptionPlanParameters").mockImplementation(throwSqlException);
                const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
                expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_030305);
                await checkCoreSwimmyApiLog(false);
            });

            it("should return CODE_030305 if additionalCouponTermValidityNichisu is null", async () => {
                jest.spyOn(APICommonDAO.prototype, "getOptionPlanParameters").mockResolvedValueOnce(null);
                const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
                expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_030305);
                await checkCoreSwimmyApiLog(false);
            });

            it("should return CODE_030306 if additionalCouponTermValidityNichiSu is out of range", async () => {
                jest.spyOn(APICommonDAO.prototype, "getOptionPlanParameters").mockResolvedValueOnce({
                    value: "-1"
                } as OptionPlanParametersEntity);
                const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
                expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_030306);
                await checkCoreSwimmyApiLog(false);
            });

            it("should return CODE_030307 if first SOAP process code = CODE_000951", async () => {
                const soapOutput = new SOAPCommonOutputDto();
                soapOutput.setProcessCode("000951");
                jest.spyOn(SOAPCommon.prototype, "callWebSoapApi").mockResolvedValue(soapOutput);
                const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
                expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_030307);
                await checkCoreSwimmyApiLog(false);
            });

            it("should return CODE_030307 if first SOAP response is error", async () => {
                nock(configData.soapApiUrl)
                    .post("")
                    .reply(200, SOAPTestHelper.simpleResponse("NG", "Error"))
                    .persist();
                const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
                expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_030307);
                await checkCoreSwimmyApiLog(false);
            });

            it("should return CODE_030401 if second SOAP process code = CODE_000951", async () => {
                const normalSoapOutput = new SOAPCommonOutputDto();
                normalSoapOutput.setProcessCode("000000");
                normalSoapOutput.setDoc(readXML(
                    SOAPTestHelper.simpleResponse("OK")
                ));
                const errorSoapOutput = new SOAPCommonOutputDto();
                errorSoapOutput.setProcessCode("000951");
                jest.spyOn(SOAPCommon.prototype, "callWebSoapApi").mockResolvedValueOnce(normalSoapOutput).mockResolvedValueOnce(errorSoapOutput);
                const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
                expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_030401);
                await checkCoreSwimmyApiLog(false);
            });

            it("should return CODE_030401 if second SOAP failed", async () => {
                nock(configData.soapApiUrl)
                    .post("")
                    .reply(200, SOAPTestHelper.simpleResponse("OK"))
                    .post("")
                    .reply(200, SOAPTestHelper.simpleResponse("NG", "Error"));
                const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
                expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_030401);
                await checkCoreSwimmyApiLog(false);
            });

            it("should return CODE_030401 if SOAPException", async () => {
                nock(configData.soapApiUrl).post("")
                    .reply(200, SOAPTestHelper.simpleResponse("OK"));   // fails at second call
                const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
                expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_030401);
                await checkCoreSwimmyApiLog(false);
            });

            it("should set correct exp_date parameter for SOAP call", async () => {
                jest.spyOn(APICommonDAO.prototype, "getOptionPlanParameters").mockResolvedValueOnce({
                    value: "30"
                } as OptionPlanParametersEntity);

                let secondCallBody: string;
                nock(configData.soapApiUrl)
                    .post("", (body) => {
                        return true;
                    })
                    .reply(200, `
                        <?xml version="1.0" encoding="UTF-8"?>
                        <soap:Envelope>
                            <soap:Body>
                                <ServiceProfileInfomation>
                                    <Result>OK</Result>
                                    <exp_date>2024/01/01</exp_date>
                                    <ErrorInfomation></ErrorInfomation>
                                </ServiceProfileInfomation>
                            </soap:Body>
                        </soap:Envelope>
                    `)
                    .post("", (body) => {
                        secondCallBody = body;
                        return true;
                    })
                    .reply(200, SOAPTestHelper.simpleResponse("OK"));

                const result = await LineAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
                expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_000000);

                const yymmdd = format(addDays(new Date(), 30), "yyMMdd");
                expect(secondCallBody).toContain(
                    `<Parameter name="exp_date">407${yymmdd}</Parameter>`,
                );

                await checkCoreSwimmyApiLog(true);
            });
        });
    });
});