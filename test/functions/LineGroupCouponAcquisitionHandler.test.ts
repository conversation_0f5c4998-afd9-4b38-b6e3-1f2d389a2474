import ResultCdConstants from "@/core/constant/ResultCdConstants";
import { describeWithDB, generateProcessID } from "../testing/TestHelper";
import { addMinutes, formatDate } from "date-fns";
import AppConfig from "@/appconfig";
import { ConnectionTimedOutError, Sequelize } from "sequelize";
import DefaultContext from "../testing/DefaultContext";
import DefaultRequest from "../testing/DefaultRequest";
import MvnoUtil from "@/core/common/MvnoUtil";
import { useMongo } from "@/database/mongo";
import { usePsql } from "@/database/psql";
import { disconnectMongo } from "@/database/mongo";
import { LineGroupUseAcquisitionHandler } from "@/functions/LineGroupUseAcquisitionHandler";
import { LineGroupCouponAcquisitionHandler } from "@/functions/LineGroupCouponAcquisitionHandler";
import APICommonDAO from "@/core/dao/APICommonDAO";
import APILinesGroupDAO from "@/core/dao/ApiLinesGroupDAO";
import LineGroupsEntity from "@/core/entity/LineGroupsEntity";
import Check from "@/core/common/Check";
import { TenantsEntity } from "@/core/entity/TenantsEntity";
import CheckUtil from "@/core/common/CheckUtil";
import TenantGroupPlansEntity from "@/core/entity/TenantGroupPlansEntity";
import { GroupPlansEntity } from "@/core/entity/GroupPlansEntity";
import GroupOptionPlansEntity from "@/core/entity/GroupOptionPlansEntity";
import LineLineGroupsEntity from "@/core/entity/LineLineGroupsEntity";
import ApiCommon from "@/core/common/ApiCommon";
import TenantManage from "@/core/common/TenantManage";
import nock from "nock";
import { getConfigDataForTest } from "../testing/testdataHelper";
import SOAPCommon from "@/core/common/SOAPCommon";
import LineGroupCouponAcquisitionService from "@/core/service/impl/LineGroupCouponAcquisitionService";
import ServiceOrdersEntity from "@/core/entity/ServiceOrdersEntity";

const createResponseXML = (
    result: "OK" | "NG",
    aply_srv: string,
    errorInfomation: string,
) => {
    return `
    <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
        xmlns:ns1="http://ws.apache.org/axis2">
        <soapenv:Body>
            <Result>${result}</Result>
            <ServiceProfileInfomation>
                <aply_srv>${aply_srv}</aply_srv>
            </ServiceProfileInfomation>
            <ErrorInfomation>${errorInfomation}</ErrorInfomation>
        </soapenv:Body>
    </soapenv:Envelope>
    `.trim();
};

describeWithDB("12 回線グループクーポンON/OFF /MVNO/api/V100/LinesGroup/coupon", () => {
    let sequelize: Sequelize;

    const testdata = {
        reserveDate: formatDate(
            addMinutes(new Date(), (15 - (new Date().getMinutes() % 15)) % 15),
            "yyyy/MM/dd HH:mm",
        ),
        tenantId: "BON000",
        lineGroupId: "600009",
    };
    const request = {
        requestHeader: {
            sequenceNo: "1234567",
            senderSystemId: "0001",
            apiKey: "$$$",
            functionType: "12",
            receivedDate: formatDate(new Date(), "yyyy/MM/dd HH:mm:ss"),
        },
        lineGroupId: testdata.lineGroupId,
        potalGroupPlanID: "10001",
        optionGroupPlanId: "10401",
        couponOnOff: "1",
        lineNo: "00001963316",
        tenantId: testdata.tenantId,
        targetSoId: testdata.tenantId,
        reserve_flag: false,
        reserve_soId: undefined,
        reserve_date: undefined,
    };

    const responseHeader = {
        sequenceNo: request.requestHeader.sequenceNo,
        receivedDate: request.requestHeader.receivedDate,
        processCode: ResultCdConstants.CODE_000000,
        apiProcessID: generateProcessID(),
    };

    let configData: ReturnType<typeof getConfigDataForTest>;

    afterEach(async () => {
        nock.abortPendingRequests();
        nock.cleanAll();
        jest.restoreAllMocks();
        jest.clearAllMocks();

        await ServiceOrdersEntity.destroy({
            where: {
                serviceOrderId: responseHeader.apiProcessID,
            }
        });
    });
    beforeEach(() => {
        DefaultContext.jsonBody = Object.assign({}, request);
        DefaultContext.responseHeader = Object.assign({}, responseHeader);
        jest.spyOn(MvnoUtil, "getDateTimeNow").mockReturnValue(
            responseHeader.receivedDate,
        );
    });
    afterAll(async () => {
        nock.enableNetConnect();
        await Promise.all([
            LineGroupsEntity.destroy({
                where: {
                    groupId: testdata.lineGroupId,
                },
            }),
            TenantGroupPlansEntity.destroy({
                where: {
                    tenantId: testdata.tenantId,
                    planId: 10001,
                },
            }),
        ]);
        await sequelize.close();
        await disconnectMongo(DefaultContext);
    });
    beforeAll(async () => {
        await AppConfig.loadCoreMvnoConfig(DefaultContext);
        sequelize = await usePsql();
        await useMongo(DefaultContext);
        configData = getConfigDataForTest();
        nock.disableNetConnect();
        await Promise.all([
            LineGroupsEntity.create({
                groupId: testdata.lineGroupId,
                status: 1,
                tenantId: testdata.tenantId,
                planId: 10001,
            }),
            TenantGroupPlansEntity.create({
                tenantId: testdata.tenantId,
                planId: 10001,
            }),
        ]);
    });
    describe("OK (CODE_000000)", () => {
        test("should return CODE_000000 if there is no error and order type 0", async () => {
            // order type 0
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, createResponseXML("OK", "1", ""))
                .persist();

            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getLinesGroupId",
            ).mockImplementation(async () => {
                return LineLineGroupsEntity.build({
                    lineId: testdata.lineGroupId,
                    groupId: testdata.lineGroupId,
                    basicCapacity: 10240,
                    usageStatus: "2",
                });
            });

            const result = await LineGroupCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );
        });

        test("should return CODE_000000 if there is no error and order type is 1 (予約前オーダ)", async () => {
            try {
                DefaultContext.isInternalRequest = false;
                jest.spyOn(
                    APILinesGroupDAO.prototype,
                    "getLinesGroupId",
                ).mockImplementation(async () => {
                    return LineLineGroupsEntity.build({
                        lineId: testdata.lineGroupId,
                        groupId: testdata.lineGroupId,
                        basicCapacity: 10240,
                        usageStatus: "2",
                    });
                });
                DefaultContext.jsonBody.reserve_date = testdata.reserveDate;
                const result = await LineGroupCouponAcquisitionHandler.Handler(
                    DefaultRequest,
                    DefaultContext,
                );
                expect(typeof result).toBe("object");
                expect(result).toHaveProperty("jsonBody");
                expect(result.jsonBody).toHaveProperty("responseHeader");
                expect(result.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_000000,
                );
            } finally {
                DefaultContext.isInternalRequest = true;
            }
        });

        test("should return CODE_000000 if there is no error and and order type is 2 (予約実行オーダ)", async () => {
            // order type 2
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, createResponseXML("OK", "1", ""))
                .persist();
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getLinesGroupId",
            ).mockImplementation(async () => {
                return LineLineGroupsEntity.build({
                    lineId: testdata.lineGroupId,
                    groupId: testdata.lineGroupId,
                    basicCapacity: 10240,
                    usageStatus: "2",
                });
            });
            DefaultContext.jsonBody.reserve_soId = generateProcessID();
            DefaultContext.jsonBody.reserve_flag = true;
            DefaultContext.jsonBody.reserve_date = testdata.reserveDate;
            const result = await LineGroupCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );
        });

        test("should return CODE_000000 even if second TPC cannot be reached (SOAPException)", async () => {
            // order type 0
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, createResponseXML("OK", "1", ""));
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getLinesGroupId",
            ).mockImplementation(async () => {
                return LineLineGroupsEntity.build({
                    lineId: testdata.lineGroupId,
                    groupId: testdata.lineGroupId,
                    basicCapacity: 10240,
                    usageStatus: "2",
                });
            });
            const result = await LineGroupCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );
        });

        test("should return CODE_000000 even if second TPC SOAP doesnt return CODE_000000", async () => {
            // order type 0
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, createResponseXML("OK", "1", ""))
                .post("")
                .reply(200, createResponseXML("NG", "", "some error"));
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getLinesGroupId",
            ).mockImplementation(async () => {
                return LineLineGroupsEntity.build({
                    lineId: testdata.lineGroupId,
                    groupId: testdata.lineGroupId,
                    basicCapacity: 10240,
                    usageStatus: "2",
                });
            });
            const result = await LineGroupCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );
        });

        test("should return CODE_000000 : couponOnOff is number (1)", async () => {
            // order type 0
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, createResponseXML("OK", "1", ""))
                .persist();

            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getLinesGroupId",
            ).mockImplementation(async () => {
                return LineLineGroupsEntity.build({
                    lineId: testdata.lineGroupId,
                    groupId: testdata.lineGroupId,
                    basicCapacity: 10240,
                    usageStatus: "2",
                });
            });

            DefaultContext.jsonBody.couponOnOff = 1;
            const result = await LineGroupCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );
        });

        test("should return CODE_000000 : couponOnOff is number (0)", async () => {
            // order type 0
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, createResponseXML("OK", "1", ""))
                .persist();

            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getLinesGroupId",
            ).mockImplementation(async () => {
                return LineLineGroupsEntity.build({
                    lineId: testdata.lineGroupId,
                    groupId: testdata.lineGroupId,
                    basicCapacity: 10240,
                    usageStatus: "2",
                });
            });

            DefaultContext.jsonBody.couponOnOff = 0;
            const result = await LineGroupCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );
        });

        test("should return CODE_000000 : lineGroupId is number", async () => {
            // order type 0
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, createResponseXML("OK", "1", ""))
                .persist();

            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getLinesGroupId",
            ).mockImplementation(async () => {
                return LineLineGroupsEntity.build({
                    lineId: testdata.lineGroupId,
                    groupId: testdata.lineGroupId,
                    basicCapacity: 10240,
                    usageStatus: "2",
                });
            });

            DefaultContext.jsonBody.lineGroupId = parseInt(request.lineGroupId, 10);
            const result = await LineGroupCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );
        });

        test("should return CODE_000000 : potalGroupPlanID is number", async () => {
            // order type 0
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, createResponseXML("OK", "1", ""))
                .persist();

            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getLinesGroupId",
            ).mockImplementation(async () => {
                return LineLineGroupsEntity.build({
                    lineId: testdata.lineGroupId,
                    groupId: testdata.lineGroupId,
                    basicCapacity: 10240,
                    usageStatus: "2",
                });
            });

            DefaultContext.jsonBody.potalGroupPlanID = parseInt(request.potalGroupPlanID, 10);
            const result = await LineGroupCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );
        });

        test("should return CODE_000000 : optionGroupPlanId is number", async () => {
            // order type 0
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, createResponseXML("OK", "1", ""))
                .persist();

            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getLinesGroupId",
            ).mockImplementation(async () => {
                return LineLineGroupsEntity.build({
                    lineId: testdata.lineGroupId,
                    groupId: testdata.lineGroupId,
                    basicCapacity: 10240,
                    usageStatus: "2",
                });
            });

            DefaultContext.jsonBody.optionGroupPlanId = parseInt(request.optionGroupPlanId, 10);
            const result = await LineGroupCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );
        });
    });
    describe("lineNO is null and tpcServicePattern is 1 or 2", () => {
        test("should return CODE_000000 if there is no error", async () => {
            // order type 0
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, createResponseXML("OK", "1", ""))
                .persist();
            jest.spyOn(
                APICommonDAO.prototype,
                "getGroupPlans",
            ).mockImplementation(async () => {
                return GroupPlansEntity.build({
                    planId: 10401,
                    planClass: 2,
                    policyId: 201,
                    simType: null,
                    network: null,
                    planDescription: "test",
                    planName: "test",
                    refServicePattern: 0,
                    tpcServicePattern: 2,
                    servicePlanId: null,
                });
            });
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getLinesGroupId",
            ).mockImplementation(async () => {
                return LineLineGroupsEntity.build({
                    lineId: testdata.lineGroupId,
                    groupId: testdata.lineGroupId,
                    basicCapacity: 10240,
                    usageStatus: "2",
                });
            });
            DefaultContext.jsonBody.lineNo = "";
            const result = await LineGroupCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );
        });

        test("should return CODE_120401 if TPC cannot be reached (SOAPException)", async () => {
            // order type 0
            jest.spyOn(
                APICommonDAO.prototype,
                "getGroupPlans",
            ).mockImplementation(async () => {
                return GroupPlansEntity.build({
                    planId: 10401,
                    planClass: 2,
                    policyId: 201,
                    simType: null,
                    network: null,
                    planDescription: "test",
                    planName: "test",
                    refServicePattern: 0,
                    tpcServicePattern: 2,
                    servicePlanId: null,
                });
            });
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getLinesGroupId",
            ).mockImplementation(async () => {
                return LineLineGroupsEntity.build({
                    lineId: testdata.lineGroupId,
                    groupId: testdata.lineGroupId,
                    basicCapacity: 10240,
                    usageStatus: "2",
                });
            });
            DefaultContext.jsonBody.lineNo = "";
            const result = await LineGroupCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_120401,
            );
        });

        test("should return CODE_120401 if serviceProfileRequest SOAP response is NG", async () => {
            // order type 0
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, createResponseXML("NG", "", "error"));
            jest.spyOn(
                APICommonDAO.prototype,
                "getGroupPlans",
            ).mockImplementation(async () => {
                return GroupPlansEntity.build({
                    planId: 10401,
                    planClass: 2,
                    policyId: 201,
                    simType: null,
                    network: null,
                    planDescription: "test",
                    planName: "test",
                    refServicePattern: 0,
                    tpcServicePattern: 2,
                    servicePlanId: null,
                });
            });
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getLinesGroupId",
            ).mockImplementation(async () => {
                return LineLineGroupsEntity.build({
                    lineId: testdata.lineGroupId,
                    groupId: testdata.lineGroupId,
                    basicCapacity: 10240,
                    usageStatus: "2",
                });
            });
            DefaultContext.jsonBody.lineNo = "";
            const result = await LineGroupCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_120401,
            );
        });

        test("should return CODE_000000 even if second TCP SOAP fails (SOAPException)", async () => {
            // order type 0
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, createResponseXML("OK", "1", ""));
            jest.spyOn(
                APICommonDAO.prototype,
                "getGroupPlans",
            ).mockImplementation(async () => {
                return GroupPlansEntity.build({
                    planId: 10401,
                    planClass: 2,
                    policyId: 201,
                    simType: null,
                    network: null,
                    planDescription: "test",
                    planName: "test",
                    refServicePattern: 0,
                    tpcServicePattern: 2,
                    servicePlanId: null,
                });
            });
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getLinesGroupId",
            ).mockImplementation(async () => {
                return LineLineGroupsEntity.build({
                    lineId: testdata.lineGroupId,
                    groupId: testdata.lineGroupId,
                    basicCapacity: 10240,
                    usageStatus: "2",
                });
            });
            DefaultContext.jsonBody.lineNo = "";
            const result = await LineGroupCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );
        });

        test("should return CODE_000000 even if second TCP SOAP returns doesnt return CODE_000000", async () => {
            // order type 0
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, createResponseXML("OK", "1", ""))
                .post("")
                .reply(200, createResponseXML("NG", "", "error"));
            jest.spyOn(
                APICommonDAO.prototype,
                "getGroupPlans",
            ).mockImplementation(async () => {
                return GroupPlansEntity.build({
                    planId: 10401,
                    planClass: 2,
                    policyId: 201,
                    simType: null,
                    network: null,
                    planDescription: "test",
                    planName: "test",
                    refServicePattern: 0,
                    tpcServicePattern: 2,
                    servicePlanId: null,
                });
            });
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getLinesGroupId",
            ).mockImplementation(async () => {
                return LineLineGroupsEntity.build({
                    lineId: testdata.lineGroupId,
                    groupId: testdata.lineGroupId,
                    basicCapacity: 10240,
                    usageStatus: "2",
                });
            });
            DefaultContext.jsonBody.lineNo = "";
            const result = await LineGroupCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );
        });
    });
    describe("lineNo is null and tpcServicePatter is 4", () => {
        test("should return CODE_000000 if there is no error", async () => {
            // order type 0
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, createResponseXML("OK", "1", ""))
                .persist();
            jest.spyOn(
                APICommonDAO.prototype,
                "getGroupPlans",
            ).mockImplementation(async () => {
                return GroupPlansEntity.build({
                    planId: 10401,
                    planClass: 2,
                    policyId: 201,
                    simType: null,
                    network: null,
                    planDescription: "test",
                    planName: "test",
                    refServicePattern: 0,
                    tpcServicePattern: 4,
                    servicePlanId: null,
                });
            });
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getLinesGroupId",
            ).mockImplementation(async () => {
                return LineLineGroupsEntity.build({
                    lineId: testdata.lineGroupId,
                    groupId: testdata.lineGroupId,
                    basicCapacity: 10240,
                    usageStatus: "2",
                });
            });
            DefaultContext.jsonBody.lineNo = "";
            const result = await LineGroupCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );
        });

        test("should return CODE_120401 if TPC cannot be reached (SOAPException)", async () => {
            // order type 0
            jest.spyOn(
                APICommonDAO.prototype,
                "getGroupPlans",
            ).mockImplementation(async () => {
                return GroupPlansEntity.build({
                    planId: 10401,
                    planClass: 2,
                    policyId: 201,
                    simType: null,
                    network: null,
                    planDescription: "test",
                    planName: "test",
                    refServicePattern: 0,
                    tpcServicePattern: 4,
                    servicePlanId: null,
                });
            });
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getLinesGroupId",
            ).mockImplementation(async () => {
                return LineLineGroupsEntity.build({
                    lineId: testdata.lineGroupId,
                    groupId: testdata.lineGroupId,
                    basicCapacity: 10240,
                    usageStatus: "2",
                });
            });
            DefaultContext.jsonBody.lineNo = "";
            const result = await LineGroupCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_120401,
            );
        });

        test("should return CODE_120401 if serviceProfileRequestLite SOAP response is NG", async () => {
            // order type 0
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, createResponseXML("NG", "1", "error"));
            jest.spyOn(
                APICommonDAO.prototype,
                "getGroupPlans",
            ).mockImplementation(async () => {
                return GroupPlansEntity.build({
                    planId: 10401,
                    planClass: 2,
                    policyId: 201,
                    simType: null,
                    network: null,
                    planDescription: "test",
                    planName: "test",
                    refServicePattern: 0,
                    tpcServicePattern: 4,
                    servicePlanId: null,
                });
            });
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getLinesGroupId",
            ).mockImplementation(async () => {
                return LineLineGroupsEntity.build({
                    lineId: testdata.lineGroupId,
                    groupId: testdata.lineGroupId,
                    basicCapacity: 10240,
                    usageStatus: "2",
                });
            });
            DefaultContext.jsonBody.lineNo = "";
            const result = await LineGroupCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_120401,
            );
        });

        test("should return CODE_000000 even if second TCP SOAP fails (SOAPException)", async () => {
            // order type 0
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, createResponseXML("OK", "1", ""));
            jest.spyOn(
                APICommonDAO.prototype,
                "getGroupPlans",
            ).mockImplementation(async () => {
                return GroupPlansEntity.build({
                    planId: 10401,
                    planClass: 2,
                    policyId: 201,
                    simType: null,
                    network: null,
                    planDescription: "test",
                    planName: "test",
                    refServicePattern: 0,
                    tpcServicePattern: 4,
                    servicePlanId: null,
                });
            });
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getLinesGroupId",
            ).mockImplementation(async () => {
                return LineLineGroupsEntity.build({
                    lineId: testdata.lineGroupId,
                    groupId: testdata.lineGroupId,
                    basicCapacity: 10240,
                    usageStatus: "2",
                });
            });
            DefaultContext.jsonBody.lineNo = "";
            const result = await LineGroupCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );
        });

        test("should return CODE_000000 even if second TCP SOAP response doesnt return CODE_000000", async () => {
            // order type 0
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, createResponseXML("OK", "1", ""))
                .post("")
                .reply(200, createResponseXML("NG", "1", "error"));
            jest.spyOn(
                APICommonDAO.prototype,
                "getGroupPlans",
            ).mockImplementation(async () => {
                return GroupPlansEntity.build({
                    planId: 10401,
                    planClass: 2,
                    policyId: 201,
                    simType: null,
                    network: null,
                    planDescription: "test",
                    planName: "test",
                    refServicePattern: 0,
                    tpcServicePattern: 4,
                    servicePlanId: null,
                });
            });
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getLinesGroupId",
            ).mockImplementation(async () => {
                return LineLineGroupsEntity.build({
                    lineId: testdata.lineGroupId,
                    groupId: testdata.lineGroupId,
                    basicCapacity: 10240,
                    usageStatus: "2",
                });
            });
            DefaultContext.jsonBody.lineNo = "";
            const result = await LineGroupCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );
        });
    });
    describe("lineNo is not null and tpcServicePattern is 4 or 6", () => {
        test("should return CODE_000000 if there is no error", async () => {
            // order type 0
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, createResponseXML("OK", "1", ""))
                .persist();
            jest.spyOn(
                APICommonDAO.prototype,
                "getGroupPlans",
            ).mockImplementation(async () => {
                return GroupPlansEntity.build({
                    planId: 10401,
                    planClass: 2,
                    policyId: 201,
                    simType: null,
                    network: null,
                    planDescription: "test",
                    planName: "test",
                    refServicePattern: 0,
                    tpcServicePattern: 6,
                    servicePlanId: null,
                });
            });
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getLinesGroupId",
            ).mockImplementation(async () => {
                return LineLineGroupsEntity.build({
                    lineId: testdata.lineGroupId,
                    groupId: testdata.lineGroupId,
                    basicCapacity: 10240,
                    usageStatus: "2",
                });
            });

            const result = await LineGroupCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );
        });

        test("should return CODE_120401 if TPC cannot be reached (SOAPException)", async () => {
            // order type 0
            jest.spyOn(
                APICommonDAO.prototype,
                "getGroupPlans",
            ).mockImplementation(async () => {
                return GroupPlansEntity.build({
                    planId: 10401,
                    planClass: 2,
                    policyId: 201,
                    simType: null,
                    network: null,
                    planDescription: "test",
                    planName: "test",
                    refServicePattern: 0,
                    tpcServicePattern: 6,
                    servicePlanId: null,
                });
            });
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getLinesGroupId",
            ).mockImplementation(async () => {
                return LineLineGroupsEntity.build({
                    lineId: testdata.lineGroupId,
                    groupId: testdata.lineGroupId,
                    basicCapacity: 10240,
                    usageStatus: "2",
                });
            });

            const result = await LineGroupCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_120401,
            );
        });

        test("should return CODE_120401 if serviceProfileRequestLite SOAP response is NG", async () => {
            // order type 0
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, createResponseXML("NG", "1", "error"));
            jest.spyOn(
                APICommonDAO.prototype,
                "getGroupPlans",
            ).mockImplementation(async () => {
                return GroupPlansEntity.build({
                    planId: 10401,
                    planClass: 2,
                    policyId: 201,
                    simType: null,
                    network: null,
                    planDescription: "test",
                    planName: "test",
                    refServicePattern: 0,
                    tpcServicePattern: 6,
                    servicePlanId: null,
                });
            });
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getLinesGroupId",
            ).mockImplementation(async () => {
                return LineLineGroupsEntity.build({
                    lineId: testdata.lineGroupId,
                    groupId: testdata.lineGroupId,
                    basicCapacity: 10240,
                    usageStatus: "2",
                });
            });

            const result = await LineGroupCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_120401,
            );
        });

        test("should return CODE_000000 even if second TCP SOAP fails (SOAPException)", async () => {
            // order type 0
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, createResponseXML("OK", "1", ""));
            jest.spyOn(
                APICommonDAO.prototype,
                "getGroupPlans",
            ).mockImplementation(async () => {
                return GroupPlansEntity.build({
                    planId: 10401,
                    planClass: 2,
                    policyId: 201,
                    simType: null,
                    network: null,
                    planDescription: "test",
                    planName: "test",
                    refServicePattern: 0,
                    tpcServicePattern: 6,
                    servicePlanId: null,
                });
            });
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getLinesGroupId",
            ).mockImplementation(async () => {
                return LineLineGroupsEntity.build({
                    lineId: testdata.lineGroupId,
                    groupId: testdata.lineGroupId,
                    basicCapacity: 10240,
                    usageStatus: "2",
                });
            });

            const result = await LineGroupCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );
        });

        test("should return CODE_000000 even if second TCP SOAP response doesnt return CODE_000000", async () => {
            // order type 0
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, createResponseXML("OK", "1", ""))
                .post("")
                .reply(200, createResponseXML("NG", "1", "error"));
            jest.spyOn(
                APICommonDAO.prototype,
                "getGroupPlans",
            ).mockImplementation(async () => {
                return GroupPlansEntity.build({
                    planId: 10401,
                    planClass: 2,
                    policyId: 201,
                    simType: null,
                    network: null,
                    planDescription: "test",
                    planName: "test",
                    refServicePattern: 0,
                    tpcServicePattern: 6,
                    servicePlanId: null,
                });
            });
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getLinesGroupId",
            ).mockImplementation(async () => {
                return LineLineGroupsEntity.build({
                    lineId: testdata.lineGroupId,
                    groupId: testdata.lineGroupId,
                    basicCapacity: 10240,
                    usageStatus: "2",
                });
            });

            const result = await LineGroupCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );
        });
    });
    describe("NG (CODE_1XXXXX)", () => {
        test("should return CODE_120104 if there order type is 9 (予想以外オーダ)", async () => {
            DefaultContext.jsonBody.reserve_flag = true;
            const result = await LineGroupCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_120104,
            );
        });

        test("should return CODE_100017 if getTenants fails to query", async () => {
            jest.spyOn(APICommonDAO.prototype, "getTenants").mockImplementation(
                async () => {
                    throw new ConnectionTimedOutError(
                        new Error("time out error"),
                    );
                },
            );
            const result = await LineGroupCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_100017,
            );
        });

        test("should return CODE_100017 if getTenants returns null", async () => {
            jest.spyOn(APICommonDAO.prototype, "getTenants").mockReturnValue(
                Promise.resolve(null),
            );
            const result = await LineGroupCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_100017,
            );
        });

        test("should return CODE_100018 if getLineGroupsInfo fails to query", async () => {
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getLineGroupsInfo",
            ).mockImplementation(async () => {
                throw new ConnectionTimedOutError(new Error("time out error"));
            });
            const result = await LineGroupCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_100018,
            );
        });

        test("should return CODE_100018 if getLineGroupsInfo returns null", async () => {
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getLineGroupsInfo",
            ).mockReturnValue(Promise.resolve(null));
            const result = await LineGroupCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_100018,
            );
        });

        test("should return CODE_100018 if lineGroupsInfo status is not 1", async () => {
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getLineGroupsInfo",
            ).mockImplementation(async () => {
                return LineGroupsEntity.build({
                    groupId: request.lineGroupId,
                    status: 0,
                    tenantId: testdata.tenantId,
                    planId: parseInt(request.potalGroupPlanID),
                });
            });
            const result = await LineGroupCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_100018,
            );
        });

        test("should return CODE_100018 if lineGroupsInfo tenantId is eqaul to param tenantId", async () => {
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getLineGroupsInfo",
            ).mockImplementation(async () => {
                return LineGroupsEntity.build({
                    groupId: request.lineGroupId,
                    status: 1,
                    tenantId: "ICX000",
                    planId: parseInt(request.potalGroupPlanID),
                });
            });
            const result = await LineGroupCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_100018,
            );
        });

        test("should return CODE_100018 if lineGroupsInfo planId is null", async () => {
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getLineGroupsInfo",
            ).mockImplementation(async () => {
                return LineGroupsEntity.build({
                    groupId: "ICX000",
                    status: 1,
                    tenantId: testdata.tenantId,
                    planId: null,
                });
            });
            const result = await LineGroupCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_100018,
            );
        });

        test("should return CODE_100018 if テナント種別が１(C-OCN)の場合、回線グループ情報が存在すればエラー", async () => {
            jest.spyOn(APICommonDAO.prototype, "getTenants").mockImplementation(
                async () => {
                    return TenantsEntity.build({
                        tenantName: "test",
                        tenantType: 1,
                        tenantId: testdata.tenantId,
                        office: true,
                        status: true,
                        tenantLevel: 1,
                    });
                },
            );
            const result = await LineGroupCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_100018,
            );
        });

        test("should return CODE_120101 if lineGroupId is invalid", async () => {
            jest.spyOn(Check, "checkLineGroupId").mockReturnValue(false);
            const result = await LineGroupCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_120101,
            );
        });

        test("should return CODE_120101 if potalGroupPlanID is invalid", async () => {
            jest.spyOn(Check, "checkPlanIDFmt").mockReturnValue(false);
            const result = await LineGroupCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_120101,
            );
        });

        test("should return CODE_120101 if optionGroupPlanId is invalid", async () => {
            jest.spyOn(Check, "checkPlanIDFmt")
                .mockReturnValueOnce(true)
                .mockReturnValueOnce(false);
            const result = await LineGroupCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_120101,
            );
        });

        test("should return CODE_120101 if couponOnOff is invalid", async () => {
            DefaultContext.jsonBody.couponOnOff = "";
            const result = await LineGroupCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_120101,
            );
        });

        test("should return CODE_120101 if lineNo length is invalid", async () => {
            DefaultContext.jsonBody.lineNo = "123456";
            const result = await LineGroupCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_120101,
            );
        });

        test("should return CODE_120101 if lineNo is not number", async () => {
            DefaultContext.jsonBody.lineNo = "123456abcde";
            const result = await LineGroupCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_120101,
            );
        });

        test("should return CODE_120101 if reserveDate is invalid and order type is 1 (予約前オーダ)", async () => {
            // order type 1
            jest.spyOn(Check, "checkReserveDateFmt").mockReturnValue(false);
            DefaultContext.jsonBody.reserve_date = testdata.reserveDate;
            const result = await LineGroupCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_120101,
            );
        });

        test("should return CODE_120101 if reserveDate is invalid and order type is 2 (予約実行オーダ)", async () => {
            // order type 2
            jest.spyOn(Check, "checkReserveDateFmt").mockReturnValue(false);
            DefaultContext.jsonBody.reserve_soId = generateProcessID();
            DefaultContext.jsonBody.reserve_flag = true;
            DefaultContext.jsonBody.reserve_date = testdata.reserveDate;
            const result = await LineGroupCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_120101,
            );
        });

        test("should return CODE_120105 if 予約日と投入日相関チェック fails and order type is 1 (予約前オーダ)", async () => {
            // order type 1
            jest.spyOn(Check, "checkReserveDateOrderDate").mockReturnValue(
                false,
            );
            DefaultContext.jsonBody.reserve_date = testdata.reserveDate;
            const result = await LineGroupCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_120105,
            );
        });

        test("should return CODE_120106 if 予約日と投入日相関チェック fails and order type is 1 (予約前オーダ)", async () => {
            // order type 1
            jest.spyOn(
                Check,
                "checkReservationDateExecutionUnitsFmt",
            ).mockReturnValue(false);
            DefaultContext.jsonBody.reserve_date = testdata.reserveDate;
            const result = await LineGroupCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_120106,
            );
        });

        test("should return CODE_120107 if 予約実行日時単位と予約日相関チェックチェック fails and order type is 1 (予約前オーダ)", async () => {
            // order type 1
            jest.spyOn(
                Check,
                "checkReservationDateExecutionUnits",
            ).mockReturnValue(false);
            DefaultContext.jsonBody.reserve_date = testdata.reserveDate;
            const result = await LineGroupCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_120107,
            );
        });

        test("should return CODE_120108 if 予約可能制限日数フォーマットチェック fails and order type is 1 (予約前オーダ)", async () => {
            // order type 1
            jest.spyOn(Check, "checkReservationsLimitDaysFmt").mockReturnValue(
                false,
            );
            DefaultContext.jsonBody.reserve_date = testdata.reserveDate;
            const result = await LineGroupCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_120108,
            );
        });

        test("should return CODE_120109 if 予約可能制限日数範囲チェック fails and order type is 1 (予約前オーダ)", async () => {
            // order type 1
            jest.spyOn(Check, "checkReservationsLimitDays").mockReturnValue(
                false,
            );
            DefaultContext.jsonBody.reserve_date = testdata.reserveDate;
            const result = await LineGroupCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_120109,
            );
        });

        test("should return CODE_120102 if getTenantGroupPlans returns null", async () => {
            // order type 0
            jest.spyOn(
                APICommonDAO.prototype,
                "getTenantGroupPlans",
            ).mockReturnValue(Promise.resolve(null));
            const result = await LineGroupCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_120102,
            );
        });

        test("should return CODE_120102 if getTenantGroupPlans fails to query", async () => {
            // order type 0
            jest.spyOn(
                APICommonDAO.prototype,
                "getTenantGroupPlans",
            ).mockImplementation(async () => {
                throw new ConnectionTimedOutError(new Error("time out error"));
            });
            const result = await LineGroupCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_120102,
            );
        });

        test("should return CODE_120110 if getUsingGroupPlans fails to query", async () => {
            // order type 0
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getUsingGroupPlans",
            ).mockImplementation(async () => {
                throw new ConnectionTimedOutError(new Error("time out error"));
            });
            const result = await LineGroupCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_120110,
            );
        });

        test("should return CODE_120110 if getUsingGroupPlans returns null", async () => {
            // order type 0
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getUsingGroupPlans",
            ).mockReturnValue(Promise.resolve(null));
            const result = await LineGroupCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_120110,
            );
        });

        test("should return CODE_120103 if getCheckedGroupPlanOptionPlans fails to query", async () => {
            // order type 0
            jest.spyOn(
                APICommonDAO.prototype,
                "getCheckedGroupPlanOptionPlans",
            ).mockImplementation(async () => {
                throw new ConnectionTimedOutError(new Error("error"));
            });
            const result = await LineGroupCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_120103,
            );
        });

        test("should return CODE_120103 if getCheckedGroupPlanOptionPlans returns null", async () => {
            // order type 0
            jest.spyOn(
                APICommonDAO.prototype,
                "getCheckedGroupPlanOptionPlans",
            ).mockReturnValue(Promise.resolve(null));
            const result = await LineGroupCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_120103,
            );
        });

        test("should return CODE_120111 if getLinesGroupId returns null", async () => {
            // order type 0
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getLinesGroupId",
            ).mockReturnValue(Promise.resolve(null));
            const result = await LineGroupCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_120111,
            );
        });

        test("should return CODE_120111 if getLinesGroupId fails to query", async () => {
            // order type 0
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getLinesGroupId",
            ).mockImplementation(async () => {
                throw new ConnectionTimedOutError(new Error("error"));
            });
            const result = await LineGroupCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_120111,
            );
        });

        test("should return CODE_120112 if lineNo is abolished", async () => {
            // order type 0
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getLinesGroupId",
            ).mockImplementation(async () => {
                return LineLineGroupsEntity.build({
                    lineId: testdata.lineGroupId,
                    groupId: testdata.lineGroupId,
                    basicCapacity: 10240,
                    usageStatus: "2",
                });
            });
            jest.spyOn(ApiCommon.prototype, "checkAbolishSo").mockReturnValue(
                Promise.resolve(false),
            );
            const result = await LineGroupCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_120112,
            );
        });

        test("should return CODE_120112 if checkAbolishSo fails to query", async () => {
            // order type 0
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getLinesGroupId",
            ).mockImplementation(async () => {
                return LineLineGroupsEntity.build({
                    lineId: testdata.lineGroupId,
                    groupId: testdata.lineGroupId,
                    basicCapacity: 10240,
                    usageStatus: "2",
                });
            });
            jest.spyOn(
                ApiCommon.prototype,
                "checkAbolishSo",
            ).mockImplementation(async () => {
                throw new ConnectionTimedOutError(new Error("error"));
            });
            const result = await LineGroupCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_120112,
            );
        });

        test("should return CODE_120113 if checkLineSuspend fails to query", async () => {
            // order type 0
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getLinesGroupId",
            ).mockImplementation(async () => {
                return LineLineGroupsEntity.build({
                    lineId: testdata.lineGroupId,
                    groupId: testdata.lineGroupId,
                    basicCapacity: 10240,
                    usageStatus: "2",
                });
            });
            jest.spyOn(
                ApiCommon.prototype,
                "checkLineSuspend",
            ).mockImplementation(async () => {
                throw new ConnectionTimedOutError(new Error("error"));
            });
            const result = await LineGroupCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_120113,
            );
        });

        test("should return CODE_120113 if lineNo is suspended", async () => {
            // order type 0
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getLinesGroupId",
            ).mockImplementation(async () => {
                return LineLineGroupsEntity.build({
                    lineId: testdata.lineGroupId,
                    groupId: testdata.lineGroupId,
                    basicCapacity: 10240,
                    usageStatus: "2",
                });
            });
            jest.spyOn(ApiCommon.prototype, "checkLineSuspend").mockReturnValue(
                Promise.resolve(false),
            );
            const result = await LineGroupCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_120113,
            );
        });

        test("should return CODE_120201 if no policy id or pattern is found", async () => {
            // order type 0
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getLinesGroupId",
            ).mockImplementation(async () => {
                return LineLineGroupsEntity.build({
                    lineId: testdata.lineGroupId,
                    groupId: testdata.lineGroupId,
                    basicCapacity: 10240,
                    usageStatus: "2",
                });
            });
            jest.spyOn(APICommonDAO.prototype, "getGroupPlans").mockReturnValue(
                Promise.resolve(null),
            );
            const result = await LineGroupCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_120201,
            );
        });

        test("should return CODE_120201 if getGroupPlans fails to query", async () => {
            // order type 0
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getLinesGroupId",
            ).mockImplementation(async () => {
                return LineLineGroupsEntity.build({
                    lineId: testdata.lineGroupId,
                    groupId: testdata.lineGroupId,
                    basicCapacity: 10240,
                    usageStatus: "2",
                });
            });
            jest.spyOn(
                APICommonDAO.prototype,
                "getGroupPlans",
            ).mockImplementation(async () => {
                throw new ConnectionTimedOutError(new Error("error"));
            });
            const result = await LineGroupCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_120201,
            );
        });

        test("should return CODE_120202 if policyId doesnt pass format check", async () => {
            // order type 0
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getLinesGroupId",
            ).mockImplementation(async () => {
                return LineLineGroupsEntity.build({
                    lineId: testdata.lineGroupId,
                    groupId: testdata.lineGroupId,
                    basicCapacity: 10240,
                    usageStatus: "2",
                });
            });
            jest.spyOn(Check, "checkPolicyId").mockReturnValue(false);
            const result = await LineGroupCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_120202,
            );
        });

        test("should return CODE_120202 if tpcServicePattern doesnt pass format check", async () => {
            // order type 0
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getLinesGroupId",
            ).mockImplementation(async () => {
                return LineLineGroupsEntity.build({
                    lineId: testdata.lineGroupId,
                    groupId: testdata.lineGroupId,
                    basicCapacity: 10240,
                    usageStatus: "2",
                });
            });
            jest.spyOn(
                APICommonDAO.prototype,
                "getGroupPlans",
            ).mockImplementation(async () => {
                return GroupPlansEntity.build({
                    planId: 10401,
                    planClass: 2,
                    policyId: 201,
                    simType: null,
                    network: null,
                    planDescription: "test",
                    planName: "test",
                    refServicePattern: 0,
                    tpcServicePattern: 0,
                    servicePlanId: null,
                });
            });
            const result = await LineGroupCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_120202,
            );
        });

        test("should return CODE_120301 if tpcConnection returns false", async () => {
            // order type 0
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getLinesGroupId",
            ).mockImplementation(async () => {
                return LineLineGroupsEntity.build({
                    lineId: testdata.lineGroupId,
                    groupId: testdata.lineGroupId,
                    basicCapacity: 10240,
                    usageStatus: "2",
                });
            });
            jest.spyOn(
                TenantManage.prototype,
                "checkTpcConnection",
            ).mockReturnValue(Promise.resolve([false, null, null]));
            const result = await LineGroupCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_120301,
            );
        });

        test("should return CODE_120301 if tpcConnection fails to query", async () => {
            // order type 0
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getLinesGroupId",
            ).mockImplementation(async () => {
                return LineLineGroupsEntity.build({
                    lineId: testdata.lineGroupId,
                    groupId: testdata.lineGroupId,
                    basicCapacity: 10240,
                    usageStatus: "2",
                });
            });
            jest.spyOn(
                TenantManage.prototype,
                "checkTpcConnection",
            ).mockImplementation(async () => {
                throw new ConnectionTimedOutError(new Error("errro"));
            });
            const result = await LineGroupCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_120301,
            );
        });

        test("should return CODE_120401 if TPC cannot be reached (SOAPException)", async () => {
            // order type 0
            // no nock
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getLinesGroupId",
            ).mockImplementation(async () => {
                return LineLineGroupsEntity.build({
                    lineId: testdata.lineGroupId,
                    groupId: testdata.lineGroupId,
                    basicCapacity: 10240,
                    usageStatus: "2",
                });
            });
            const result = await LineGroupCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_120401,
            );
        });

        test("should return CODE_120401 if serviceProfileRequest SOAP response is NG", async () => {
            // order type 0
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, createResponseXML("NG", "", "some error"));
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getLinesGroupId",
            ).mockImplementation(async () => {
                return LineLineGroupsEntity.build({
                    lineId: testdata.lineGroupId,
                    groupId: testdata.lineGroupId,
                    basicCapacity: 10240,
                    usageStatus: "2",
                });
            });
            const result = await LineGroupCouponAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_120401,
            );
        });
    });
    describe("NG (CODE_9XXXXX)", () => {
        test("should return CODE_999999 if there is no error and order type 0", async () => {
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, createResponseXML("OK", "1", ""))
                .persist();
            jest.spyOn(
                APICommonDAO.prototype,
                "getTenantGroupPlans",
            ).mockRejectedValue(new TypeError("Unexpected error"));
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getLinesGroupId",
            ).mockImplementation(async () => {
                return LineLineGroupsEntity.build({
                    lineId: testdata.lineGroupId,
                    groupId: testdata.lineGroupId,
                    basicCapacity: 10240,
                    usageStatus: "2",
                });
            });
            const instance = new LineGroupCouponAcquisitionService(
                DefaultRequest,
                DefaultContext,
            );
            const result = await instance.service(request);
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_999999,
            );
        });
    });
});