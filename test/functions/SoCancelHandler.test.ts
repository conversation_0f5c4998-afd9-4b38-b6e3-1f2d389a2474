import { expect, test, beforeEach } from "@jest/globals";

const enqueueMessage = jest.fn();
jest.mock("../../src/services/servicebus", () => ({
    __esModule: true,
    default: enqueueMessage,
}));

import { add, addMinutes, formatDate } from "date-fns";
import { Sequelize } from "sequelize";
import defaultContext from "../testing/DefaultContext";
import {
    describeWithDB,
    generateProcessID,
    generateSOID,
} from "../testing/TestHelper";
import ServiceOrdersEntity from "@/core/entity/ServiceOrdersEntity";
import ResultCdConstants from "@/core/constant/ResultCdConstants";
import ResponseHeader from "@/core/dto/ResponseHeader";
import { usePsql } from "@/database/psql";
import { disconnectMongo, useMongo } from "@/database/mongo";
import AppConfig from "@/appconfig";
import DefaultRequest from "../testing/DefaultRequest";
import { SoCancelHandler } from "@/functions/SoCancelHandler";
import SoCancelInputDto from "@/core/dto/SoCancelInputDto";
import SoCancelService from "@/core/service/impl/SoCancelService";
import { addDays } from "date-fns";
import Check from "@/core/common/Check";
import { ConnectionTimedOutError } from "sequelize";
import APISoDAO from "@/core/dao/APISoDAO";
import DefaultContext from "../testing/DefaultContext";
import APILinesGroupDAO from "@/core/dao/ApiLinesGroupDAO";
import APICommonDAO from "@/core/dao/APICommonDAO";
import TenantManage from "@/core/common/TenantManage";
import LineGroupsEntity from "@/core/entity/LineGroupsEntity";
import TenantGroupPlansEntity from "@/core/entity/TenantGroupPlansEntity";
import APILinesDAO from "@/core/dao/APILinesDAO";
import CoreSwimmyApiLog from "@/models/coreSwimmyApiLog";
import { lineDeleteReservationResponse } from "./testdata/frontSoCancel.testdata";
import { getNumberValue } from "@/utils";

describeWithDB("62 予約キャンセル /MVNO/api/V100/so_cancel", () => {
    let sequelize: Sequelize;
    let instance: SoCancelService = null;
    const testData = {
        lineNo: "02006134003",
        serviceOrderIdKey: generateProcessID(),
        reserve_date: add(new Date(), { days: 7 }),
    };

    const restJson = {
        requestHeader: {
            sequenceNo: "1234567",
            senderSystemId: "0001",
            apiKey: "",
            functionType: "18",
        },
        tenantId: "OPF000",
        targetTenantId: "TST000",
        lineNo: testData.lineNo,
        mnpOutFlag: "0",
        csvUnnecessaryFlag: "0",
        reserve_date: testData.reserve_date,
        lineGroupId: "20",
        potalGroupPlanID: "10001",
    };
    const request = {
        requestHeader: {
            sequenceNo: "1234567",
            senderSystemId: "0001",
            apiKey: "",
            functionType: "56",
        },
        tenantId: "OPF000",
        targetTenantId: "TST000",
        targetSoId: generateSOID(),
        lineNo: "02006134003",
        registFlag: "1",
        csvUnnecessaryFlag: "0",
        serviceOrderIdKey: testData.serviceOrderIdKey,
        reserve_flag: false,
        reserve_soId: null,
    } as SoCancelInputDto;

    const responseHeader = {
        sequenceNo: request.requestHeader.sequenceNo,
        receivedDate: formatDate(new Date(), "yyyy/MM/dd HH:mm:ss"),
        processCode: ResultCdConstants.CODE_000000,
        apiProcessID: generateProcessID(),
    } as ResponseHeader;

    beforeAll(async () => {
        await AppConfig.loadCoreMvnoConfig(defaultContext);
        sequelize = await usePsql();
        await useMongo(defaultContext);
        await Promise.all([
            TenantGroupPlansEntity.create({
                tenantId: restJson.tenantId,
                planId: parseInt(restJson.potalGroupPlanID, 10),
            }),
            LineGroupsEntity.create({
                tenantId: restJson.tenantId,
                status: 1,
                groupId: restJson.lineGroupId,
                planId: parseInt(restJson.potalGroupPlanID, 10),
            }),
        ]);
    });

    afterAll(async () => {
        await Promise.all([
            TenantGroupPlansEntity.destroy({
                where: {
                    tenantId: restJson.tenantId,
                    planId: parseInt(restJson.potalGroupPlanID, 10),
                },
            }),
            LineGroupsEntity.destroy({
                where: {
                    tenantId: request.tenantId,
                    status: 1,
                    groupId: restJson.lineGroupId,
                    planId: parseInt(restJson.potalGroupPlanID, 10),
                },
            }),
        ]);
        await sequelize.close();
        await disconnectMongo(defaultContext);
    });

    beforeEach(async () => {
        // reset mock
        (enqueueMessage as jest.Mock).mockClear();

        // reset context to default
        defaultContext.jsonBody = Object.assign({}, request);
        defaultContext.responseHeader = Object.assign({}, responseHeader);
        instance = new SoCancelService(DefaultRequest, DefaultContext);
        await Promise.all([
            ServiceOrdersEntity.create({
                serviceOrderId: request.serviceOrderIdKey,
                tenantId: "UNM000",
                lineId: testData.lineNo,
                orderStatus: "予約中",
                orderType: "プラン変更",
                reserveDate: addDays(new Date(), 10),
                orderDate: new Date("2020-11-16 11:05:27.000"),
                functionType: "52",
                restMessage: JSON.stringify(restJson),
            }),
        ]);
    });

    afterEach(async () => {
        jest.restoreAllMocks();
        await Promise.all([
            ServiceOrdersEntity.destroy({
                where: {
                    serviceOrderId: [
                        request.serviceOrderIdKey,
                        responseHeader.apiProcessID,
                    ],
                },
            }),
            CoreSwimmyApiLog.deleteMany({
                requestOrderId: {
                    $in: [
                        responseHeader.apiProcessID,
                        testData.serviceOrderIdKey,
                    ],
                },
            }),
        ]);
    });

    const checkServiceOrder = async (
        serviceOrderId: string,
        exists = true,
        status = "予約中",
    ) => {
        const serviceOrder = await ServiceOrdersEntity.findOne({
            where: {
                serviceOrderId,
            },
        });

        if (exists) {
            expect(serviceOrder).not.toBeNull();
            expect(serviceOrder?.orderStatus).toEqual(status);
        } else {
            expect(serviceOrder).toBeNull();
        }
    };

    const checkCoreSwimmyApiLog = async (
        apiProcessID: string,
        exists: boolean,
    ) => {
        const apiLog = await CoreSwimmyApiLog.findOne({
            requestOrderId: apiProcessID,
        });
        if (exists) {
            expect(apiLog).not.toBeNull();
            expect(enqueueMessage).toBeCalledTimes(1);
        } else {
            expect(apiLog).toBeNull();
            expect(enqueueMessage).not.toBeCalled();
        }
    };

    const INVALID_CSV_FLAG = ["2", "3", 2];
    const JSON_FUNCTION_TYPE_14_15_20: string[] = ["14", "15", "20"];
    const JSON_FUNCTION_TYPE_10_12: string[] = ["10", "12"];
    const JSON_FUNCTION_TYPE_4_5_6: string[] = ["04", "05", "06"];
    const JSON_FUNCTION_TYPE_3_4_5_6: string[] = ["03", "04", "05", "06"];
    const JSON_FUNCTION_TYPES: string[] = [
        "03",
        "04",
        "05",
        "06",
        "10",
        "12",
        "14",
        "15",
        "20",
    ];

    describe("OK (CODE_000000)", () => {
        const delay = getNumberValue(
            AppConfig.getCoreConfig(null).CORE_SWIMMY_API.MESSAGE_DELAY_SECONDS,
            60,
        );
        beforeEach(async () => {
            (enqueueMessage as jest.Mock).mockClear();
            jest.spyOn(TenantManage.prototype, "doCheck")
                .mockImplementationOnce(() => {
                    return Promise.resolve([true, "test"] as [boolean, string]);
                })
                .mockImplementationOnce(() => {
                    return Promise.resolve([true, "test"] as [boolean, string]);
                });
            await CoreSwimmyApiLog.create({
                tenantId: request.tenantId,
                tempoId: "",
                kaisenNo: testData.lineNo,
                swimmyType: "008",
                requestOrderId: request.serviceOrderIdKey,
                soId: request.targetSoId,
                requestParam: {},
                responseParam: lineDeleteReservationResponse,
                createdAt: Math.floor(Date.now() / 1000) - delay - 10, // to avoid sending cancel request as scheduled message
            });
        });
        test.each(
            JSON_FUNCTION_TYPES.filter((row) => !["06", "14"].includes(row)),
        )(
            "should return CODE_000000 when there is no error, orderType is %s",
            async (functionType) => {
                await CoreSwimmyApiLog.deleteMany({
                    requestOrderId: {
                        $in: [
                            responseHeader.apiProcessID,
                            testData.serviceOrderIdKey,
                        ],
                    },
                });
                await ServiceOrdersEntity.update(
                    {
                        restMessage: JSON.stringify({
                            ...restJson,
                            requestHeader: {
                                ...restJson.requestHeader,
                                functionType,
                            },
                        }),
                        functionType,
                    },
                    {
                        where: {
                            serviceOrderId: request.serviceOrderIdKey,
                        },
                    },
                );

                const result = await instance.service(request);

                expect(typeof result).toBe("object");
                expect(result).toHaveProperty("jsonBody");
                expect(result.jsonBody).toHaveProperty("responseHeader");
                expect(result.jsonBody.responseHeader.apiProcessID).toBe(
                    defaultContext.responseHeader.apiProcessID,
                );
                expect(result.jsonBody.responseHeader.processCode).toBe(
                    defaultContext.responseHeader.processCode,
                );
                expect(result.jsonBody.responseHeader.sequenceNo).toBe(
                    defaultContext.responseHeader.sequenceNo,
                );
                expect(result.jsonBody.responseHeader.receivedDate).toBe(
                    defaultContext.responseHeader.receivedDate,
                );
                expect(result.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_000000,
                );
                expect(result.additionalData.lineNo).toBe(restJson.lineNo);
                if (functionType !== "14" && functionType !== "06") {
                    expect(result.additionalData.csvOutputKbn).toBe("0");
                    expect(result.additionalData.csvUnnecessary).toBe(true);
                }
                await checkServiceOrder(
                    defaultContext.jsonBody.serviceOrderIdKey,
                    true,
                    "キャンセル済み", // status should be updated
                );
                await checkCoreSwimmyApiLog(testData.serviceOrderIdKey, false);
            },
        );
        test.each(["06", "14"])(
            "should return CODE_000000 and generate core swimmy, orderType is %s",
            async (functionType) => {
                await ServiceOrdersEntity.update(
                    {
                        restMessage: JSON.stringify({
                            ...restJson,
                            requestHeader: {
                                ...restJson.requestHeader,
                                functionType,
                            },
                        }),
                        functionType,
                    },
                    {
                        where: {
                            serviceOrderId: request.serviceOrderIdKey,
                        },
                    },
                );

                DefaultContext.jsonBody = Object.assign({}, request);
                const result = await SoCancelHandler.Handler(
                    DefaultRequest,
                    defaultContext,
                );

                expect(typeof result).toBe("object");
                expect(result).toHaveProperty("jsonBody");
                expect(result.jsonBody).toHaveProperty("responseHeader");
                expect(result.jsonBody.responseHeader.apiProcessID).toBe(
                    defaultContext.responseHeader.apiProcessID,
                );
                expect(result.jsonBody.responseHeader.processCode).toBe(
                    defaultContext.responseHeader.processCode,
                );
                expect(result.jsonBody.responseHeader.sequenceNo).toBe(
                    defaultContext.responseHeader.sequenceNo,
                );
                expect(result.jsonBody.responseHeader.receivedDate).toBe(
                    defaultContext.responseHeader.receivedDate,
                );
                expect(result.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_000000,
                );
                await checkServiceOrder(
                    defaultContext.jsonBody.serviceOrderIdKey,
                    true,
                    "キャンセル済み", // status should be updated
                );
                await checkCoreSwimmyApiLog(testData.serviceOrderIdKey, true);
            },
        );

        test.each(["06", "14"])(
            "should return CODE_000000 and generate core swimmy, orderType is %s (csvUnncessaryFlag = 0 number)",
            async (functionType) => {
                await ServiceOrdersEntity.update(
                    {
                        restMessage: JSON.stringify({
                            ...restJson,
                            requestHeader: {
                                ...restJson.requestHeader,
                                functionType,
                            },
                        }),
                        functionType,
                    },
                    {
                        where: {
                            serviceOrderId: request.serviceOrderIdKey,
                        },
                    },
                );

                DefaultContext.jsonBody.csvUnnecessaryFlag = 0;
                DefaultContext.jsonBody = Object.assign({}, request);
                const result = await SoCancelHandler.Handler(
                    DefaultRequest,
                    defaultContext,
                );

                expect(typeof result).toBe("object");
                expect(result).toHaveProperty("jsonBody");
                expect(result.jsonBody).toHaveProperty("responseHeader");
                expect(result.jsonBody.responseHeader.apiProcessID).toBe(
                    defaultContext.responseHeader.apiProcessID,
                );
                expect(result.jsonBody.responseHeader.processCode).toBe(
                    defaultContext.responseHeader.processCode,
                );
                expect(result.jsonBody.responseHeader.sequenceNo).toBe(
                    defaultContext.responseHeader.sequenceNo,
                );
                expect(result.jsonBody.responseHeader.receivedDate).toBe(
                    defaultContext.responseHeader.receivedDate,
                );
                expect(result.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_000000,
                );
                await checkServiceOrder(
                    defaultContext.jsonBody.serviceOrderIdKey,
                    true,
                    "キャンセル済み", // status should be updated
                );
                await checkCoreSwimmyApiLog(testData.serviceOrderIdKey, true);
            },
        );

        test.each(["06", "14"])(
            "should return CODE_000000 if second doCheck returns false, orderType is %s",
            async (functionType) => {
                await ServiceOrdersEntity.update(
                    {
                        restMessage: JSON.stringify({
                            ...restJson,
                            requestHeader: {
                                ...restJson.requestHeader,
                                functionType,
                            },
                        }),
                        functionType,
                    },
                    {
                        where: {
                            serviceOrderId: request.serviceOrderIdKey,
                        },
                    },
                );
                jest.spyOn(TenantManage.prototype, "doCheck")
                    .mockImplementationOnce(() => {
                        return Promise.resolve([true, null] as [
                            boolean,
                            string,
                        ]);
                    })
                    .mockImplementationOnce(() => {
                        return Promise.resolve([false, null]);
                    });
                DefaultContext.jsonBody = Object.assign({}, request);
                const result = await SoCancelHandler.Handler(
                    DefaultRequest,
                    defaultContext,
                );

                expect(typeof result).toBe("object");
                expect(result).toHaveProperty("jsonBody");
                expect(result.jsonBody).toHaveProperty("responseHeader");
                expect(result.jsonBody.responseHeader.apiProcessID).toBe(
                    defaultContext.responseHeader.apiProcessID,
                );
                expect(result.jsonBody.responseHeader.processCode).toBe(
                    defaultContext.responseHeader.processCode,
                );
                expect(result.jsonBody.responseHeader.sequenceNo).toBe(
                    defaultContext.responseHeader.sequenceNo,
                );
                expect(result.jsonBody.responseHeader.receivedDate).toBe(
                    defaultContext.responseHeader.receivedDate,
                );
                expect(result.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_000000,
                );

                await checkServiceOrder(
                    defaultContext.jsonBody.serviceOrderIdKey,
                    true,
                    "キャンセル済み", // status should be updated
                );
                await checkCoreSwimmyApiLog(testData.serviceOrderIdKey, true);
            },
        );

        test.each(["14"])(
            "should return CODE_000000 if second doCheck throw db error, orderType is %s",
            async (functionType) => {
                await ServiceOrdersEntity.update(
                    {
                        restMessage: JSON.stringify({
                            ...restJson,
                            requestHeader: {
                                ...restJson.requestHeader,
                                functionType,
                            },
                        }),
                        functionType,
                    },
                    {
                        where: {
                            serviceOrderId: request.serviceOrderIdKey,
                        },
                    },
                );
                jest.spyOn(TenantManage.prototype, "doCheck")
                    .mockImplementationOnce(() => {
                        return Promise.resolve([true, "test"] as [
                            boolean,
                            string,
                        ]);
                    })
                    .mockImplementationOnce(() => {
                        return Promise.reject(new Error("Timeout error"));
                    });
                DefaultContext.jsonBody = Object.assign({}, request);
                const result = await SoCancelHandler.Handler(
                    DefaultRequest,
                    defaultContext,
                );

                expect(typeof result).toBe("object");
                expect(result).toHaveProperty("jsonBody");
                expect(result.jsonBody).toHaveProperty("responseHeader");
                expect(result.jsonBody.responseHeader.apiProcessID).toBe(
                    defaultContext.responseHeader.apiProcessID,
                );
                expect(result.jsonBody.responseHeader.processCode).toBe(
                    defaultContext.responseHeader.processCode,
                );
                expect(result.jsonBody.responseHeader.sequenceNo).toBe(
                    defaultContext.responseHeader.sequenceNo,
                );
                expect(result.jsonBody.responseHeader.receivedDate).toBe(
                    defaultContext.responseHeader.receivedDate,
                );
                expect(result.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_000000,
                );

                await checkServiceOrder(
                    defaultContext.jsonBody.serviceOrderIdKey,
                    true,
                    "キャンセル済み", // status should be updated
                );
                await checkCoreSwimmyApiLog(testData.serviceOrderIdKey, true);
            },
        );
        test.each(["14"])(
            "should return CODE_000000 if getOffice returns null while orderType is %s",
            async (functionType) => {
                await Promise.all([
                    CoreSwimmyApiLog.deleteMany({
                        requestOrderId: {
                            $in: [
                                responseHeader.apiProcessID,
                                testData.serviceOrderIdKey,
                            ],
                        },
                    }),
                    ServiceOrdersEntity.update(
                        {
                            restMessage: JSON.stringify({
                                ...restJson,
                                requestHeader: {
                                    ...restJson.requestHeader,
                                    functionType,
                                },
                            }),
                            functionType,
                        },
                        {
                            where: {
                                serviceOrderId: request.serviceOrderIdKey,
                            },
                        },
                    ),
                ]);
                jest.spyOn(
                    APILinesDAO.prototype,
                    "getOffice",
                ).mockImplementation(() => {
                    return Promise.resolve(null);
                });
                DefaultContext.jsonBody = Object.assign({}, request);
                const result = await SoCancelHandler.Handler(
                    DefaultRequest,
                    defaultContext,
                );

                expect(typeof result).toBe("object");
                expect(result).toHaveProperty("jsonBody");
                expect(result.jsonBody).toHaveProperty("responseHeader");
                expect(result.jsonBody.responseHeader.apiProcessID).toBe(
                    defaultContext.responseHeader.apiProcessID,
                );
                expect(result.jsonBody.responseHeader.processCode).toBe(
                    defaultContext.responseHeader.processCode,
                );
                expect(result.jsonBody.responseHeader.sequenceNo).toBe(
                    defaultContext.responseHeader.sequenceNo,
                );
                expect(result.jsonBody.responseHeader.receivedDate).toBe(
                    defaultContext.responseHeader.receivedDate,
                );
                expect(result.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_000000,
                );

                await checkServiceOrder(
                    defaultContext.jsonBody.serviceOrderIdKey,
                    true,
                    "キャンセル済み", // status should be updated
                );
                await checkCoreSwimmyApiLog(testData.serviceOrderIdKey, false);
            },
        );
        test.each(["06"])(
            "should return CODE_000000 if APILinesDAO getWholeFlag throws error other than sql orderType is %s",
            async (functionType) => {
                await ServiceOrdersEntity.update(
                    {
                        restMessage: JSON.stringify({
                            ...restJson,
                            requestHeader: {
                                ...restJson.requestHeader,
                                functionType,
                            },
                        }),
                        functionType,
                    },
                    {
                        where: {
                            serviceOrderId: request.serviceOrderIdKey,
                        },
                    },
                );
                jest.spyOn(
                    APILinesDAO.prototype,
                    "getOffice",
                ).mockImplementation(() => {
                    return Promise.resolve([false]);
                });
                jest.spyOn(
                    APILinesDAO.prototype,
                    "getWholeFlag",
                ).mockImplementation(() => {
                    throw new ConnectionTimedOutError(new Error("timeout"));
                });
                const result = await SoCancelHandler.Handler(
                    DefaultRequest,
                    defaultContext,
                );

                expect(typeof result).toBe("object");
                expect(result).toHaveProperty("jsonBody");
                expect(result.jsonBody).toHaveProperty("responseHeader");
                expect(result.jsonBody.responseHeader.apiProcessID).toBe(
                    defaultContext.responseHeader.apiProcessID,
                );
                expect(result.jsonBody.responseHeader.processCode).toBe(
                    defaultContext.responseHeader.processCode,
                );
                expect(result.jsonBody.responseHeader.sequenceNo).toBe(
                    defaultContext.responseHeader.sequenceNo,
                );
                expect(result.jsonBody.responseHeader.receivedDate).toBe(
                    defaultContext.responseHeader.receivedDate,
                );
                expect(result.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_000000,
                );

                await checkServiceOrder(
                    defaultContext.jsonBody.serviceOrderIdKey,
                    true,
                    "キャンセル済み", // status should be updated
                );
                await checkCoreSwimmyApiLog(testData.serviceOrderIdKey, true);
            },
        );

        test.each(["06"])(
            "same test case as above but to cover handler",
            async (functionType) => {
                await ServiceOrdersEntity.update(
                    {
                        restMessage: JSON.stringify({
                            ...restJson,
                            requestHeader: {
                                ...restJson.requestHeader,
                                functionType,
                            },
                        }),
                        functionType,
                    },
                    {
                        where: {
                            serviceOrderId: request.serviceOrderIdKey,
                        },
                    },
                );
                jest.spyOn(
                    APILinesDAO.prototype,
                    "getOffice",
                ).mockImplementation(() => {
                    return Promise.resolve([false]);
                });
                jest.spyOn(
                    APILinesDAO.prototype,
                    "getWholeFlag",
                ).mockImplementation(() => {
                    throw new ConnectionTimedOutError(new Error("timeout"));
                });
                DefaultContext.jsonBody = Object.assign({}, request);
                const result = await SoCancelHandler.Handler(
                    DefaultRequest,
                    defaultContext,
                );

                expect(typeof result).toBe("object");
                expect(result).toHaveProperty("jsonBody");
                expect(result.jsonBody).toHaveProperty("responseHeader");
                expect(result.jsonBody.responseHeader.apiProcessID).toBe(
                    defaultContext.responseHeader.apiProcessID,
                );
                expect(result.jsonBody.responseHeader.processCode).toBe(
                    defaultContext.responseHeader.processCode,
                );
                expect(result.jsonBody.responseHeader.sequenceNo).toBe(
                    defaultContext.responseHeader.sequenceNo,
                );
                expect(result.jsonBody.responseHeader.receivedDate).toBe(
                    defaultContext.responseHeader.receivedDate,
                );
                expect(result.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_000000,
                );

                await checkServiceOrder(
                    defaultContext.jsonBody.serviceOrderIdKey,
                    true,
                    "キャンセル済み", // status should be updated
                );
                await checkCoreSwimmyApiLog(testData.serviceOrderIdKey, true);
            },
        );
    });

    describe("NG (CODE_18xxxx)", () => {
        test("should return CODE_180101 if tenantId is not valid", async () => {
            defaultContext.jsonBody.tenantId = "";
            const result = await SoCancelHandler.Handler(
                DefaultRequest,
                defaultContext,
            );

            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_180101,
            );
            await checkCoreSwimmyApiLog(testData.serviceOrderIdKey, false);
        });

        test("should return CODE_180101 if serviceOrderIdKey is not valid", async () => {
            defaultContext.jsonBody.serviceOrderIdKey = "";
            const result = await SoCancelHandler.Handler(
                DefaultRequest,
                defaultContext,
            );

            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_180101,
            );
            await checkCoreSwimmyApiLog(testData.serviceOrderIdKey, false);
        });

        test.each(INVALID_CSV_FLAG)(
            "should return CODE_180101 if csvUnnecessaryFlag is not valid (%s)",
            async (csvUnnecessaryFlag) => {
                defaultContext.jsonBody.csvUnnecessaryFlag = csvUnnecessaryFlag;
                const result = await SoCancelHandler.Handler(
                    DefaultRequest,
                    defaultContext,
                );

                expect(typeof result).toBe("object");
                expect(result).toHaveProperty("jsonBody");
                expect(result.jsonBody).toHaveProperty("responseHeader");
                expect(result.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_180101,
                );
                await checkCoreSwimmyApiLog(testData.serviceOrderIdKey, false);
            },
        );

        test("should return CODE_180201 if service order is not found", async () => {
            await ServiceOrdersEntity.destroy({
                where: {
                    serviceOrderId: request.serviceOrderIdKey,
                },
            });

            const result = await SoCancelHandler.Handler(
                DefaultRequest,
                defaultContext,
            );

            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_180201,
            );
            await checkCoreSwimmyApiLog(testData.serviceOrderIdKey, false);
        });

        test("should return CODE_180201 if getCancelObj failed", async () => {
            const apiSoDao = new APISoDAO(DefaultRequest, DefaultContext);
            jest.spyOn(apiSoDao, "getCancelObj").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });
            Object.defineProperty(instance, "apiSoDao", { value: apiSoDao });

            const result = await instance.service(request);

            expect(result.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_180201,
            );
            expect(apiSoDao.getCancelObj).toHaveBeenCalled();
            await checkCoreSwimmyApiLog(testData.serviceOrderIdKey, false);
        });

        test("should return CODE_180202 if restMessage functionType is empty", async () => {
            await ServiceOrdersEntity.update(
                {
                    restMessage: JSON.stringify({
                        ...restJson,
                        requestHeader: {
                            ...restJson.requestHeader,
                            functionType: "",
                        },
                    }),
                },
                {
                    where: {
                        serviceOrderId: request.serviceOrderIdKey,
                    },
                },
            );

            const result = await SoCancelHandler.Handler(
                DefaultRequest,
                defaultContext,
            );

            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_180202,
            );
            await checkCoreSwimmyApiLog(testData.serviceOrderIdKey, false);
        });

        test("should return CODE_180202 if restMessage tenantId is empty", async () => {
            await ServiceOrdersEntity.update(
                {
                    restMessage: JSON.stringify({
                        ...restJson,
                        tenantId: "",
                    }),
                },
                {
                    where: {
                        serviceOrderId: request.serviceOrderIdKey,
                    },
                },
            );

            const result = await SoCancelHandler.Handler(
                DefaultRequest,
                defaultContext,
            );

            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_180202,
            );
            await checkCoreSwimmyApiLog(testData.serviceOrderIdKey, false);
        });

        test.each(JSON_FUNCTION_TYPE_3_4_5_6)(
            "should return CODE_180202 if restMessage lineNo is empty while functionType is one of either 03,04,05,06",
            async (functionType) => {
                await ServiceOrdersEntity.update(
                    {
                        restMessage: JSON.stringify({
                            ...restJson,
                            requestHeader: {
                                ...restJson.requestHeader,
                                functionType,
                            },
                            lineNo: "",
                        }),
                    },
                    {
                        where: {
                            serviceOrderId: request.serviceOrderIdKey,
                        },
                    },
                );

                const result = await SoCancelHandler.Handler(
                    DefaultRequest,
                    defaultContext,
                );

                expect(typeof result).toBe("object");
                expect(result).toHaveProperty("jsonBody");
                expect(result.jsonBody).toHaveProperty("responseHeader");
                expect(result.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_180202,
                );
                await checkCoreSwimmyApiLog(testData.serviceOrderIdKey, false);
            },
        );

        test("should return CODE_180202 if restMessage reserve_date is empty", async () => {
            await ServiceOrdersEntity.update(
                {
                    restMessage: JSON.stringify({
                        ...restJson,
                        reserve_date: "",
                    }),
                },
                {
                    where: {
                        serviceOrderId: request.serviceOrderIdKey,
                    },
                },
            );

            const result = await SoCancelHandler.Handler(
                DefaultRequest,
                defaultContext,
            );

            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_180202,
            );
            await checkCoreSwimmyApiLog(testData.serviceOrderIdKey, false);
        });

        test.each(JSON_FUNCTION_TYPE_10_12)(
            "should return CODE_180203 if restMessage potalGroupPlanID is empty while functionType is %s",
            async (functionType) => {
                await ServiceOrdersEntity.update(
                    {
                        restMessage: JSON.stringify({
                            ...restJson,
                            requestHeader: {
                                ...restJson.requestHeader,
                                functionType,
                            },
                            potalGroupPlanID: "",
                        }),
                    },
                    {
                        where: {
                            serviceOrderId: request.serviceOrderIdKey,
                        },
                    },
                );

                const result = await SoCancelHandler.Handler(
                    DefaultRequest,
                    defaultContext,
                );

                expect(typeof result).toBe("object");
                expect(result).toHaveProperty("jsonBody");
                expect(result.jsonBody).toHaveProperty("responseHeader");
                expect(result.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_180203,
                );
                await checkCoreSwimmyApiLog(testData.serviceOrderIdKey, false);
            },
        );

        test.each(JSON_FUNCTION_TYPE_14_15_20)(
            "should return CODE_180204 if restMessage lineGroupId is empty while functionType is one of either %s",
            async (functionType) => {
                await ServiceOrdersEntity.update(
                    {
                        restMessage: JSON.stringify({
                            ...restJson,
                            requestHeader: {
                                ...restJson.requestHeader,
                                functionType,
                            },
                            lineGroupId: "",
                        }),
                    },
                    {
                        where: {
                            serviceOrderId: request.serviceOrderIdKey,
                        },
                    },
                );

                const result = await SoCancelHandler.Handler(
                    DefaultRequest,
                    defaultContext,
                );

                expect(typeof result).toBe("object");
                expect(result).toHaveProperty("jsonBody");
                expect(result.jsonBody).toHaveProperty("responseHeader");
                expect(result.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_180204,
                );
                await checkCoreSwimmyApiLog(testData.serviceOrderIdKey, false);
            },
        );

        test("should return CODE_180301 if CancelReservationDisableDays config is invalid", async () => {
            jest.spyOn(
                Check,
                "checkCancelReservationDisableDaysFmt",
            ).mockReturnValue(false);
            const result = await SoCancelHandler.Handler(
                DefaultRequest,
                defaultContext,
            );

            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_180301,
            );
            await checkCoreSwimmyApiLog(testData.serviceOrderIdKey, false);
        });

        test("should return CODE_180302 if current date is not within CancelReservationDisableDays ", async () => {
            await ServiceOrdersEntity.update(
                {
                    reserveDate: addMinutes(new Date(), 2 * 60),
                },
                {
                    where: {
                        serviceOrderId: request.serviceOrderIdKey,
                    },
                },
            );
            const result = await SoCancelHandler.Handler(
                DefaultRequest,
                defaultContext,
            );

            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_180302,
            );
            await checkCoreSwimmyApiLog(testData.serviceOrderIdKey, false);
        });

        test("should return CODE_180401 if reserveDate is in the past", async () => {
            Check.checkCancelReservationDisableDays = jest
                .fn()
                .mockReturnValue(true);
            await ServiceOrdersEntity.update(
                {
                    reserveDate: addMinutes(new Date(), -2 * 60),
                },
                {
                    where: {
                        serviceOrderId: request.serviceOrderIdKey,
                    },
                },
            );
            const result = await SoCancelHandler.Handler(
                DefaultRequest,
                defaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_180401,
            );
            await checkCoreSwimmyApiLog(testData.serviceOrderIdKey, false);
        });

        test.each(JSON_FUNCTION_TYPE_4_5_6)(
            "should return CODE_180501 if tenant doCheck failed while rest functionType is %s",
            async (functionType) => {
                await ServiceOrdersEntity.update(
                    {
                        restMessage: JSON.stringify({
                            ...restJson,
                            requestHeader: {
                                ...restJson.requestHeader,
                                functionType,
                            },
                        }),
                    },
                    {
                        where: {
                            serviceOrderId: request.serviceOrderIdKey,
                        },
                    },
                );
                const tenantManage = new TenantManage(
                    DefaultRequest,
                    DefaultContext,
                );
                jest.spyOn(tenantManage, "doCheck").mockImplementation(() => {
                    throw new ConnectionTimedOutError(new Error("timeout"));
                });
                Object.defineProperty(instance, "tenantManage", {
                    value: tenantManage,
                });

                const result = await instance.service(request);

                expect(result.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_180501,
                );
                expect(result.additionalData.csvOutputKbn).toBe("0");
                expect(result.additionalData.csvUnnecessary).toBe(true);
                await checkCoreSwimmyApiLog(testData.serviceOrderIdKey, false);
            },
        );

        test.each(JSON_FUNCTION_TYPE_4_5_6)(
            "should return CODE_180501 if tenant doCheck returns false while rest functionType is %s",
            async (functionType) => {
                await ServiceOrdersEntity.update(
                    {
                        restMessage: JSON.stringify({
                            ...restJson,
                            requestHeader: {
                                ...restJson.requestHeader,
                                functionType,
                            },
                        }),
                    },
                    {
                        where: {
                            serviceOrderId: request.serviceOrderIdKey,
                        },
                    },
                );
                const tenantManage = new TenantManage(
                    DefaultRequest,
                    DefaultContext,
                );
                jest.spyOn(tenantManage, "doCheck").mockResolvedValue([
                    false,
                    "",
                ]);
                Object.defineProperty(instance, "tenantManage", {
                    value: tenantManage,
                });

                const result = await instance.service(request);

                expect(result.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_180501,
                );
                await checkCoreSwimmyApiLog(testData.serviceOrderIdKey, false);
            },
        );

        test.each(JSON_FUNCTION_TYPE_10_12)(
            "should return CODE_180503 if apiCommonDao getTenantGroupPlans throws db while rest functionType is %s",
            async (functionType) => {
                await ServiceOrdersEntity.update(
                    {
                        restMessage: JSON.stringify({
                            ...restJson,
                            requestHeader: {
                                ...restJson.requestHeader,
                                functionType,
                            },
                        }),
                    },
                    {
                        where: {
                            serviceOrderId: request.serviceOrderIdKey,
                        },
                    },
                );
                jest.spyOn(
                    APICommonDAO.prototype,
                    "getTenantGroupPlans",
                ).mockImplementation(async () => {
                    throw new ConnectionTimedOutError(new Error("timeout"));
                });
                const result = await instance.service(request);

                expect(result.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_180503,
                );
                await checkCoreSwimmyApiLog(testData.serviceOrderIdKey, false);
            },
        );

        test.each(JSON_FUNCTION_TYPE_10_12)(
            "should return CODE_180503 if apiCommonDao getTenantGroupPlans fails other than sqlException while rest functionType is %s",
            async (functionType) => {
                await ServiceOrdersEntity.update(
                    {
                        restMessage: JSON.stringify({
                            ...restJson,
                            requestHeader: {
                                ...restJson.requestHeader,
                                functionType,
                            },
                        }),
                    },
                    {
                        where: {
                            serviceOrderId: request.serviceOrderIdKey,
                        },
                    },
                );
                jest.spyOn(
                    APICommonDAO.prototype,
                    "getTenantGroupPlans",
                ).mockImplementation(async () => {
                    throw new Error("generic error");
                });
                const result = await instance.service(request);

                expect(result.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_180503,
                );
                await checkCoreSwimmyApiLog(testData.serviceOrderIdKey, false);
            },
        );

        test.each(JSON_FUNCTION_TYPE_10_12)(
            "should return CODE_180503 if apiCommonDao getTenantGroupPlans return null while rest functionType is %s",
            async (functionType) => {
                await ServiceOrdersEntity.update(
                    {
                        restMessage: JSON.stringify({
                            ...restJson,
                            requestHeader: {
                                ...restJson.requestHeader,
                                functionType,
                            },
                        }),
                    },
                    {
                        where: {
                            serviceOrderId: request.serviceOrderIdKey,
                        },
                    },
                );
                jest.spyOn(
                    APICommonDAO.prototype,
                    "getTenantGroupPlans",
                ).mockReturnValue(Promise.resolve(null));
                const result = await instance.service(request);

                expect(result.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_180503,
                );
                await checkCoreSwimmyApiLog(testData.serviceOrderIdKey, false);
            },
        );

        test.each(JSON_FUNCTION_TYPE_10_12)(
            "should return CODE_180503 if apiCommonDao getTenantGroupPlans's tenantId is not same as the param tenantId while rest functionType is %s",
            async (functionType) => {
                await ServiceOrdersEntity.update(
                    {
                        restMessage: JSON.stringify({
                            ...restJson,
                            requestHeader: {
                                ...restJson.requestHeader,
                                functionType,
                            },
                        }),
                    },
                    {
                        where: {
                            serviceOrderId: request.serviceOrderIdKey,
                        },
                    },
                );
                jest.spyOn(
                    APICommonDAO.prototype,
                    "getTenantGroupPlans",
                ).mockImplementation(async () => {
                    return TenantGroupPlansEntity.build({
                        tenantId: "999999",
                        planId: 20001,
                    });
                });

                const result = await instance.service(request);

                expect(result.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_180503,
                );
                await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
            },
        );

        test.each(JSON_FUNCTION_TYPE_14_15_20)(
            "should return CODE_180505 if apiLinesGroupDAO getLineGroupsInfo failed while rest functionType is %s",
            async (functionType) => {
                await ServiceOrdersEntity.update(
                    {
                        restMessage: JSON.stringify({
                            ...restJson,
                            requestHeader: {
                                ...restJson.requestHeader,
                                functionType,
                            },
                        }),
                    },
                    {
                        where: {
                            serviceOrderId: request.serviceOrderIdKey,
                        },
                    },
                );
                const apiLinesGroupDAO = new APILinesGroupDAO(
                    DefaultRequest,
                    DefaultContext,
                );
                jest.spyOn(
                    apiLinesGroupDAO,
                    "getLineGroupsInfo",
                ).mockImplementation(() => {
                    throw new ConnectionTimedOutError(new Error("timeout"));
                });
                Object.defineProperty(instance, "apiLinesGroupDAO", {
                    value: apiLinesGroupDAO,
                });

                const result = await instance.service(request);

                expect(result.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_180505,
                );
                await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
            },
        );

        test.each(JSON_FUNCTION_TYPE_14_15_20)(
            "should return CODE_180505 if apiLinesGroupDAO getLineGroupsInfo return null while rest functionType is %s",
            async (functionType) => {
                await ServiceOrdersEntity.update(
                    {
                        restMessage: JSON.stringify({
                            ...restJson,
                            requestHeader: {
                                ...restJson.requestHeader,
                                functionType,
                            },
                        }),
                    },
                    {
                        where: {
                            serviceOrderId: request.serviceOrderIdKey,
                        },
                    },
                );
                const apiLinesGroupDAO = new APILinesGroupDAO(
                    DefaultRequest,
                    DefaultContext,
                );
                jest.spyOn(
                    apiLinesGroupDAO,
                    "getLineGroupsInfo",
                ).mockReturnValue(Promise.resolve(null));
                Object.defineProperty(instance, "apiLinesGroupDAO", {
                    value: apiLinesGroupDAO,
                });

                const result = await instance.service(request);

                expect(result.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_180505,
                );
                await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
            },
        );

        test.each(JSON_FUNCTION_TYPE_14_15_20)(
            "should return CODE_180505 if apiLinesGroupDAO getLineGroupsInfo's tenantId is not same as the param tenantId while rest functionType is %s",
            async (functionType) => {
                await ServiceOrdersEntity.update(
                    {
                        restMessage: JSON.stringify({
                            ...restJson,
                            requestHeader: {
                                ...restJson.requestHeader,
                                functionType,
                            },
                        }),
                    },
                    {
                        where: {
                            serviceOrderId: request.serviceOrderIdKey,
                        },
                    },
                );
                jest.spyOn(
                    APILinesGroupDAO.prototype,
                    "getLineGroupsInfo",
                ).mockImplementation(async () => {
                    return LineGroupsEntity.build({
                        tenantId: "9999999",
                        status: 1,
                        groupId: "",
                        planId: 20001,
                    });
                });
                const result = await instance.service(request);
                expect(result.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_180505,
                );
                await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
            },
        );

        test.each(JSON_FUNCTION_TYPE_14_15_20)(
            "should return CODE_180505 if apiLinesGroupDAO getLineGroupsInfo's status is not 1 while rest functionType is %s",
            async (functionType) => {
                await ServiceOrdersEntity.update(
                    {
                        restMessage: JSON.stringify({
                            ...restJson,
                            requestHeader: {
                                ...restJson.requestHeader,
                                functionType,
                            },
                        }),
                    },
                    {
                        where: {
                            serviceOrderId: request.serviceOrderIdKey,
                        },
                    },
                );
                jest.spyOn(
                    APILinesGroupDAO.prototype,
                    "getLineGroupsInfo",
                ).mockImplementation(async () => {
                    return LineGroupsEntity.build({
                        tenantId: request.tenantId,
                        status: null,
                        groupId: "",
                        planId: 20001,
                    });
                });
                const result = await instance.service(request);
                expect(result.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_180505,
                );
                await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
            },
        );

        test.each(JSON_FUNCTION_TYPE_14_15_20)(
            "should return CODE_180505 if apiLinesGroupDAO getLineGroupsInfo's planId is null while rest functionType is %s",
            async (functionType) => {
                await ServiceOrdersEntity.update(
                    {
                        restMessage: JSON.stringify({
                            ...restJson,
                            requestHeader: {
                                ...restJson.requestHeader,
                                functionType,
                            },
                        }),
                    },
                    {
                        where: {
                            serviceOrderId: request.serviceOrderIdKey,
                        },
                    },
                );
                jest.spyOn(
                    APILinesGroupDAO.prototype,
                    "getLineGroupsInfo",
                ).mockImplementation(async () => {
                    return LineGroupsEntity.build({
                        tenantId: request.tenantId,
                        status: 1,
                        groupId: "",
                        planId: null,
                    });
                });
                const result = await instance.service(request);
                expect(result.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_180505,
                );
                await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
            },
        );
    });

    describe("NG (CODE_999999)", () => {
        test("should return CODE_999999 (error) if restMessage is not a valid JSON string", async () => {
            // NOTE since updating restMessage throws psql error, need to mock it instead
            // this error should not happen in normal operation (rejected during insertion)
            const serviceOrder = await ServiceOrdersEntity.findOne({
                where: {
                    serviceOrderId: request.serviceOrderIdKey,
                },
            });
            jest.spyOn(ServiceOrdersEntity, "findOne").mockImplementationOnce(
                async () => {
                    serviceOrder.restMessage = "invalid json";
                    return serviceOrder;
                },
            );

            const result = await SoCancelHandler.Handler(
                DefaultRequest,
                defaultContext,
            );

            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_999999,
            );

            // service order exists but not updated
            await checkServiceOrder(
                defaultContext.jsonBody.serviceOrderIdKey,
                true,
            );
            await checkCoreSwimmyApiLog(testData.serviceOrderIdKey, false);
        });

        test("should return CODE_999999 if apiSoDao.updServiceOrders faileds", async () => {
            jest.spyOn(
                APISoDAO.prototype,
                "updServiceOrders",
            ).mockRejectedValue(
                new ConnectionTimedOutError(Error("test error")),
            );
            const result = await instance.service(request);

            expect(result.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_999999,
            );
            await checkCoreSwimmyApiLog(testData.serviceOrderIdKey, false);
        });

        test.each(["14"])(
            "should return CODE_000000 if get office throw error other than sql while orderType is %s",
            async (functionType) => {
                await ServiceOrdersEntity.update(
                    {
                        restMessage: JSON.stringify({
                            ...restJson,
                            requestHeader: {
                                ...restJson.requestHeader,
                                functionType,
                            },
                        }),
                        functionType,
                    },
                    {
                        where: {
                            serviceOrderId: request.serviceOrderIdKey,
                        },
                    },
                );
                jest.spyOn(TenantManage.prototype, "doCheck")
                    .mockImplementationOnce(() => {
                        return Promise.resolve([true, "test"] as [
                            boolean,
                            string,
                        ]);
                    })
                    .mockImplementationOnce(() => {
                        return Promise.resolve([true, "test"] as [
                            boolean,
                            string,
                        ]);
                    });

                jest.spyOn(
                    APILinesDAO.prototype,
                    "getOffice",
                ).mockImplementation(() => {
                    throw new Error("generic error");
                });
                const result = await instance.service(request);

                expect(result.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_999999,
                );

                await checkServiceOrder(
                    defaultContext.jsonBody.serviceOrderIdKey,
                    true,
                    "キャンセル済み", // status should be updated
                );
                await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
            },
        );

        test.each(["14"])(
            "should return CODE_000000 if get office throw error other than sql while orderType is %s",
            async (functionType) => {
                await ServiceOrdersEntity.update(
                    {
                        restMessage: JSON.stringify({
                            ...restJson,
                            requestHeader: {
                                ...restJson.requestHeader,
                                functionType,
                            },
                        }),
                        functionType,
                    },
                    {
                        where: {
                            serviceOrderId: request.serviceOrderIdKey,
                        },
                    },
                );

                jest.spyOn(TenantManage.prototype, "doCheck")
                    .mockImplementationOnce(() => {
                        return Promise.resolve([true, "test"] as [
                            boolean,
                            string,
                        ]);
                    })
                    .mockImplementationOnce(() => {
                        return Promise.resolve([true, "test"] as [
                            boolean,
                            string,
                        ]);
                    });

                jest.spyOn(
                    APILinesDAO.prototype,
                    "getOffice",
                ).mockImplementation(() => {
                    throw new ConnectionTimedOutError(
                        new Error("timeout error"),
                    );
                });
                const result = await instance.service(request);

                expect(result.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_000000,
                );

                await checkServiceOrder(
                    defaultContext.jsonBody.serviceOrderIdKey,
                    true,
                    "キャンセル済み", // status should be updated
                );
                await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
            },
        );

        test.each(["06"])(
            "should return CODE_999999 if APILinesDAO getWholeFlag throws error other than sql orderType is %s",
            async (functionType) => {
                await ServiceOrdersEntity.update(
                    {
                        restMessage: JSON.stringify({
                            ...restJson,
                            requestHeader: {
                                ...restJson.requestHeader,
                                functionType,
                            },
                        }),
                        functionType,
                    },
                    {
                        where: {
                            serviceOrderId: request.serviceOrderIdKey,
                        },
                    },
                );
                jest.spyOn(TenantManage.prototype, "doCheck")
                    .mockImplementationOnce(() => {
                        return Promise.resolve([true, "test"] as [
                            boolean,
                            string,
                        ]);
                    })
                    .mockImplementationOnce(() => {
                        return Promise.resolve([true, "test"] as [
                            boolean,
                            string,
                        ]);
                    });
                jest.spyOn(
                    APILinesDAO.prototype,
                    "getOffice",
                ).mockImplementation(() => {
                    return Promise.resolve([false]);
                });
                jest.spyOn(
                    APILinesDAO.prototype,
                    "getWholeFlag",
                ).mockImplementation(() => {
                    throw new Error("generic error");
                });
                const result = await instance.service(request);
                expect(result.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_999999,
                );
                await checkServiceOrder(
                    defaultContext.jsonBody.serviceOrderIdKey,
                    true,
                    "キャンセル済み", // status should be updated
                );
                await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
            },
        );

        test("should return error if unexpected error occurred calling service", async () => {
            defaultContext.jsonBody.tenantId = "";
            jest.spyOn(
                SoCancelService.prototype,
                "service",
            ).mockImplementationOnce(async () => {
                throw new Error("Unexpected error");
            });
            const result = await SoCancelHandler.Handler(
                DefaultRequest,
                defaultContext,
            );
            expect(result?.body.toString()).toEqual("Error: Unexpected error");
            await checkCoreSwimmyApiLog(testData.serviceOrderIdKey, false);
        });
    });
});
