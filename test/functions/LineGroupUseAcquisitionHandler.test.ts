import { describeWithDB, generateProcessID } from "../testing/TestHelper";
import { ConnectionTimedOutError, Sequelize } from "sequelize";
import { beforeAll, beforeEach, describe, expect, jest, test } from "@jest/globals";
import { usePsql } from "@/database/psql";
import { disconnectMongo, useMongo } from "@/database/mongo";
import DefaultContext from "../testing/DefaultContext";
import { format, formatDate, subDays } from "date-fns";
import ResultCdConstants from "@/core/constant/ResultCdConstants";
import defaultContext from "../testing/DefaultContext";
import MvnoUtil from "@/core/common/MvnoUtil";
import defaultRequest from "../testing/DefaultRequest";
import { HttpResponseInit } from "@azure/functions";
import { LineGroupUseAcquisitionHandler } from "@/functions/LineGroupUseAcquisitionHandler";
import APICommonDAO from "@/core/dao/APICommonDAO";
import config from "config";
import nock from "nock";
import APILinesGroupDAO from "@/core/dao/ApiLinesGroupDAO";
import { TenantsEntity } from "@/core/entity/TenantsEntity";
import TenantManage from "@/core/common/TenantManage";
import SOAPCommon from "@/core/common/SOAPCommon";
import SOAPCommonOutputDto from "@/core/dto/SOAPCommonOutputDto";
import Check from "@/core/common/Check";
import { getConfig, getConfigAsNumber } from "@/helpers/configHelper";
import AppConfig from "@/appconfig";

const createResponseXML = (result: "OK" | "NG") => {
    return `
    <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
        xmlns:ns1="http://ws.apache.org/axis2">
        <soapenv:Body>
            <Result>${result}</Result>
            <dyn_grp_info>
                <dyn_node_info_0>
                    <dyn_bucket_info>
                        <dyn_acmlt_lst_info>
                            <dyn_20240901>1</dyn_20240901>
                            <dyn_20240902>2</dyn_20240902>
                            <dyn_20240903>3</dyn_20240903>
                            <dyn_20240904>4</dyn_20240904>
                            <dyn_20240905>5</dyn_20240905>
                            <dyn_20240906>6</dyn_20240906>
                            <dyn_20240907>7</dyn_20240907>
                            <dyn_20240908>8</dyn_20240908>
                            <dyn_20240909>9</dyn_20240909>
                            <dyn_20240910>10</dyn_20240910>
                            <dyn_20240911>11</dyn_20240911>
                            <dyn_20240912>12</dyn_20240912>
                            <dyn_20240913>13</dyn_20240913>
                            <dyn_20240914>14</dyn_20240914>
                            <dyn_20240915>15</dyn_20240915>
                            <dyn_20240916>16</dyn_20240916>
                            <dyn_20240917>17</dyn_20240917>
                            <dyn_20240918>18</dyn_20240918>
                            <dyn_20240919>19</dyn_20240919>
                            <dyn_20240920>20</dyn_20240920>
                            <dyn_20240921>21</dyn_20240921>
                            <dyn_20240922>22</dyn_20240922>
                            <dyn_20240923>23</dyn_20240923>
                            <dyn_20240924>24</dyn_20240924>
                            <dyn_20240925>25</dyn_20240925>
                            <dyn_20240926>26</dyn_20240926>
                            <dyn_20240927>27</dyn_20240927>
                            <dyn_20240928>28</dyn_20240928>
                            <dyn_20240929>29</dyn_20240929>
                            <dyn_20240930>30</dyn_20240930>
                        </dyn_acmlt_lst_info>
                        <dyn_acmlt_month_info>465</dyn_acmlt_month_info>
                        <dyn_cap_month_info>
                            <dyn_cap>1000</dyn_cap>
                            <dyn_use>465</dyn_use>
                            <dyn_exp_date>2024-12-31</dyn_exp_date>
                        </dyn_cap_month_info>
                        <dyn_cap_tran_info_0>
                            <dyn_cap>1000</dyn_cap>
                            <dyn_use>465</dyn_use>
                            <dyn_exp_date>2024-12-31</dyn_exp_date>
                        </dyn_cap_tran_info_0>
                        <dyn_cap_add_info_0>
                            <dyn_cap>1000</dyn_cap>
                            <dyn_use>465</dyn_use>
                            <dyn_exp_date>2024-12-31</dyn_exp_date>
                        </dyn_cap_add_info_0>
                        <dyn_hvy_lst_info>
                            <dyn_20240901>1</dyn_20240901>
                            <dyn_20240902>2</dyn_20240902>
                            <dyn_20240903>3</dyn_20240903>
                            <dyn_20240904>4</dyn_20240904>
                            <dyn_20240905>5</dyn_20240905>
                            <dyn_20240906>6</dyn_20240906>
                            <dyn_20240907>7</dyn_20240907>
                            <dyn_20240908>8</dyn_20240908>
                            <dyn_20240909>9</dyn_20240909>
                            <dyn_20240910>10</dyn_20240910>
                            <dyn_20240911>11</dyn_20240911>
                            <dyn_20240912>12</dyn_20240912>
                            <dyn_20240913>13</dyn_20240913>
                            <dyn_20240914>14</dyn_20240914>
                            <dyn_20240915>15</dyn_20240915>
                            <dyn_20240916>16</dyn_20240916>
                            <dyn_20240917>17</dyn_20240917>
                            <dyn_20240918>18</dyn_20240918>
                            <dyn_20240919>19</dyn_20240919>
                            <dyn_20240920>20</dyn_20240920>
                            <dyn_20240921>21</dyn_20240921>
                            <dyn_20240922>22</dyn_20240922>
                            <dyn_20240923>23</dyn_20240923>
                            <dyn_20240924>24</dyn_20240924>
                            <dyn_20240925>25</dyn_20240925>
                            <dyn_20240926>26</dyn_20240926>
                            <dyn_20240927>27</dyn_20240927>
                            <dyn_20240928>28</dyn_20240928>
                            <dyn_20240929>29</dyn_20240929>
                            <dyn_20240930>30</dyn_20240930>
                        </dyn_hvy_lst_info>
                    </dyn_bucket_info>
                    <dyn_reg_sta_month>1</dyn_reg_sta_month>
                </dyn_node_info_0>
            </dyn_grp_info>
        </soapenv:Body>
    </soapenv:Envelope>
    `.trim();
};

describeWithDB("09 回線グループ運用情報参照", () => {
    let sequelize: Sequelize;

    const request = {
        requestHeader: {
            sequenceNo: "1234567",
            senderSystemId: "0001",
            apiKey: "",
            functionType: "56",
        },
        tenantId: "TST000",
        lineGroupId: "7001025",
        potalGroupPlanID: "50001",
        trafficOfCouponOffFlag: "0",
        lineNo: "00001963316"
    };

    const responseHeader = {
        sequenceNo: request.requestHeader.sequenceNo,
        receivedDate: formatDate(new Date(), "yyyy/MM/dd HH:mm:ss"),
        processCode: ResultCdConstants.CODE_000000,
        apiProcessID: generateProcessID(),
    };

    const responseHeaders: nock.ReplyHeaders = {
        "Content-Type": "text/xml",
    };

    const getConfigData = () => ({
        timeout: config.get<number>("mvno.TpcTimeout"),
        soap: config.get<string>("mvno.SOAP"),
        soapApiUrl: getConfig("TpcDestServiceName").replace(
            "${IpAddr}",
            getConfig("TpcDestIpAddress"),
        ),
        timeoutOffset: 200,
    });
    type ConfigData = ReturnType<typeof getConfigData>
    let configData: ConfigData;

    const checkEmptyResponseBody = (result: HttpResponseInit, expected_process_code: string = ResultCdConstants.CODE_000000, trafficOfCouponOffFlag: string = "0") => {
        let baseJson: any = {
            responseHeader: {
                processCode: expected_process_code,
                apiProcessID: responseHeader.apiProcessID,
                sequenceNo: request.requestHeader.sequenceNo,
                receivedDate: responseHeader.receivedDate,
            },
            trafficOneDay: "",
            traffic1dayAgo: "",
            traffic2daysAgo: "",
            traffic3daysAgo: "",
            traffic4daysAgo: "",
            traffic5daysAgo: "",
            traffic6daysAgo: "",
            traffic7daysAgo: "",
            traffic8daysAgo: "",
            traffic9daysAgo: "",
            traffic10daysAgo: "",
            traffic11daysAgo: "",
            traffic12daysAgo: "",
            traffic13daysAgo: "",
            traffic14daysAgo: "",
            traffic15daysAgo: "",
            traffic16daysAgo: "",
            traffic17daysAgo: "",
            traffic18daysAgo: "",
            traffic19daysAgo: "",
            traffic20daysAgo: "",
            traffic21daysAgo: "",
            traffic22daysAgo: "",
            traffic23daysAgo: "",
            traffic24daysAgo: "",
            traffic25daysAgo: "",
            traffic26daysAgo: "",
            traffic27daysAgo: "",
            traffic28daysAgo: "",
            traffic29daysAgo: "",
            traffic30daysAgo: "",
            trafficThisMonth: "",
            trafficPreviousMonth: "",
            trafficBeforehandMonth: "",
            basicCouponRemains: "",
            basicCouponTermValidity: "",
            couponPieceTime: "",
            carryingOverCoupon1: {
                carryingOverCoupon1RemainCapacity: "",
                carryingOverCoupon1TermValidity: ""
            },
            carryingOverCoupon2: {
                carryingOverCoupon2RemainCapacity: "",
                carryingOverCoupon2TermValidity: ""
            },
            additionalCoupon1: {
                additionalCoupon1RemainCapacity: "",
                additionalCoupon1TermValidity: ""
            },
            additionalCoupon2: {
                additionalCoupon2RemainCapacity: "",
                additionalCoupon2TermValidity: ""
            },
            additionalCoupon3: {
                additionalCoupon3RemainCapacity: "",
                additionalCoupon3TermValidity: ""
            },
            additionalCoupon4: {
                additionalCoupon4RemainCapacity: "",
                additionalCoupon4TermValidity: ""
            },
            additionalCoupon5: {
                additionalCoupon5RemainCapacity: "",
                additionalCoupon5TermValidity: ""
            },
            couponOnOff: "",
            heavyUserMonitorStatus: "",
            heavyUserMonitorStartTimeMonth: "",
            heavyUserMonitorStartTimeLatest: "",
            regulationCause: "",
            totalVolumeControlStatus: "",
        };
        if (trafficOfCouponOffFlag === "1") {
            baseJson = {
                ...baseJson,
                trafficOneDay_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                traffic1dayAgo_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                traffic2daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                traffic3daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                traffic4daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                traffic5daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                traffic6daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                traffic7daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                traffic8daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                traffic9daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                traffic10daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                traffic11daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                traffic12daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                traffic13daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                traffic14daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                traffic15daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                traffic16daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                traffic17daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                traffic18daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                traffic19daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                traffic20daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                traffic21daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                traffic22daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                traffic23daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                traffic24daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                traffic25daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                traffic26daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                traffic27daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                traffic28daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                traffic29daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                traffic30daysAgo_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                trafficThisMonth_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                trafficPreviousMonth_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0),
                trafficBeforehandMonth_CouponOff: MvnoUtil.addSpaceAfterStr(null, 0)
            };
        }
        expect(result.jsonBody).toEqual(baseJson);
    };

    const checkNonEmptyResponseBody = (result: HttpResponseInit, trafficOfCouponOffFlag: string = "0") => {
        let baseJson : any = {
            jsonBody: {
                responseHeader,
                trafficOneDay: '30',
                traffic1dayAgo: '29',
                traffic2daysAgo: '28',
                traffic3daysAgo: '27',
                traffic4daysAgo: '26',
                traffic5daysAgo: '25',
                traffic6daysAgo: '24',
                traffic7daysAgo: '23',
                traffic8daysAgo: '22',
                traffic9daysAgo: '21',
                traffic10daysAgo: '20',
                traffic11daysAgo: '19',
                traffic12daysAgo: '18',
                traffic13daysAgo: '17',
                traffic14daysAgo: '16',
                traffic15daysAgo: '15',
                traffic16daysAgo: '14',
                traffic17daysAgo: '13',
                traffic18daysAgo: '12',
                traffic19daysAgo: '11',
                traffic20daysAgo: '10',
                traffic21daysAgo: '9',
                traffic22daysAgo: '8',
                traffic23daysAgo: '7',
                traffic24daysAgo: '6',
                traffic25daysAgo: '5',
                traffic26daysAgo: '4',
                traffic27daysAgo: '3',
                traffic28daysAgo: '2',
                traffic29daysAgo: '1',
                traffic30daysAgo: '',
                trafficThisMonth: '',
                trafficPreviousMonth: '',
                trafficBeforehandMonth: '',
                basicCouponRemains: '535',
                basicCouponTermValidity: '2024-12-31',
                couponPieceTime: '',
                carryingOverCoupon1: {
                    carryingOverCoupon1RemainCapacity: '535',
                    carryingOverCoupon1TermValidity: '2024-12-31'
                },
                carryingOverCoupon2: {
                    carryingOverCoupon2RemainCapacity: '',
                    carryingOverCoupon2TermValidity: ''
                },
                additionalCoupon1: {
                    additionalCoupon1RemainCapacity: '535',
                    additionalCoupon1TermValidity: '2024-12-31'
                },
                additionalCoupon2: {
                    additionalCoupon2RemainCapacity: '',
                    additionalCoupon2TermValidity: ''
                },
                additionalCoupon3: {
                    additionalCoupon3RemainCapacity: '',
                    additionalCoupon3TermValidity: ''
                },
                additionalCoupon4: {
                    additionalCoupon4RemainCapacity: '',
                    additionalCoupon4TermValidity: ''
                },
                additionalCoupon5: {
                    additionalCoupon5RemainCapacity: '',
                    additionalCoupon5TermValidity: ''
                },
                couponOnOff: '',
                heavyUserMonitorStatus: '',
                heavyUserMonitorStartTimeMonth: '',
                heavyUserMonitorStartTimeLatest: '',
                regulationCause: '',
                totalVolumeControlStatus: ''
            }
        };
        if (trafficOfCouponOffFlag === "1") {
            baseJson = {
                jsonBody: {
                    ...baseJson.jsonBody,
                    trafficOneDay_CouponOff: '30',
                    traffic1dayAgo_CouponOff: '29',
                    traffic2daysAgo_CouponOff: '28',
                    traffic3daysAgo_CouponOff: '27',
                    traffic4daysAgo_CouponOff: '26',
                    traffic5daysAgo_CouponOff: '25',
                    traffic6daysAgo_CouponOff: '24',
                    traffic7daysAgo_CouponOff: '23',
                    traffic8daysAgo_CouponOff: '22',
                    traffic9daysAgo_CouponOff: '21',
                    traffic10daysAgo_CouponOff: '20',
                    traffic11daysAgo_CouponOff: '19',
                    traffic12daysAgo_CouponOff: '18',
                    traffic13daysAgo_CouponOff: '17',
                    traffic14daysAgo_CouponOff: '16',
                    traffic15daysAgo_CouponOff: '15',
                    traffic16daysAgo_CouponOff: '14',
                    traffic17daysAgo_CouponOff: '13',
                    traffic18daysAgo_CouponOff: '12',
                    traffic19daysAgo_CouponOff: '11',
                    traffic20daysAgo_CouponOff: '10',
                    traffic21daysAgo_CouponOff: '9',
                    traffic22daysAgo_CouponOff: '8',
                    traffic23daysAgo_CouponOff: '7',
                    traffic24daysAgo_CouponOff: '6',
                    traffic25daysAgo_CouponOff: '5',
                    traffic26daysAgo_CouponOff: '4',
                    traffic27daysAgo_CouponOff: '3',
                    traffic28daysAgo_CouponOff: '2',
                    traffic29daysAgo_CouponOff: '1',
                    traffic30daysAgo_CouponOff: '',
                    trafficThisMonth_CouponOff: '',
                    trafficPreviousMonth_CouponOff: '',
                    trafficBeforehandMonth_CouponOff: ''
                }
            };
        }
        expect(result).toEqual(baseJson);
    };

    beforeAll(async () => {
        await AppConfig.loadCoreMvnoConfig(DefaultContext);
        sequelize = await usePsql();
        await useMongo(DefaultContext);
        nock.disableNetConnect();

        configData = getConfigData();
    });

    afterAll(async () => {
        await sequelize.close();
        await disconnectMongo(DefaultContext);
        nock.abortPendingRequests();
        nock.cleanAll();
        nock.enableNetConnect();
    });

    beforeEach(async () => {
        defaultContext.jsonBody = Object.assign({}, request);
        defaultContext.responseHeader = Object.assign({}, responseHeader);
        jest.spyOn(MvnoUtil, "getDateTimeNow").mockReturnValue(responseHeader.receivedDate);
    });

    afterEach(async () => {
        jest.restoreAllMocks();
        jest.clearAllMocks();
    });

    describe("CommonCheckFailedHandler", () => {
        it("should return as same as context's response code", async () => {
            defaultContext.jsonBody = Object.assign({}, {
                ...request,
            });
            defaultContext.responseHeader = Object.assign({}, { ...responseHeader, processCode: ResultCdConstants.CODE_999999 });
            const response = await LineGroupUseAcquisitionHandler.CommonCheckFailedHandler(defaultRequest, defaultContext, "フォーマットチェックエラーが発生しました。");
            checkEmptyResponseBody(response, ResultCdConstants.CODE_999999);
        });

        it("test on empty response with traffic of coupon off flag", async () => {
            defaultContext.jsonBody = Object.assign({}, {
                ...request,
                trafficOfCouponOffFlag: "1",
            });
            defaultContext.responseHeader = Object.assign({}, { ...responseHeader, processCode: ResultCdConstants.CODE_999999 });
            const response = await LineGroupUseAcquisitionHandler.CommonCheckFailedHandler(defaultRequest, defaultContext, "フォーマットチェックエラーが発生しました。");
            checkEmptyResponseBody(response, ResultCdConstants.CODE_999999, "1");
        });
    });

    describe("CommonCheckCatchErrorHandler", () => {
        it("should return as same as context's response code", async () => {
            defaultContext.jsonBody = Object.assign({}, {
                ...request,
            });
            defaultContext.responseHeader = Object.assign({}, { ...responseHeader, processCode: ResultCdConstants.CODE_999999 });
            const response = await LineGroupUseAcquisitionHandler.CommonCheckCatchErrorHandler(defaultRequest, defaultContext, new Error("test error"));
            checkEmptyResponseBody(response, ResultCdConstants.CODE_999999);
        });

        it("test on empty response with traffic of coupon off flag", async () => {
            defaultContext.jsonBody = Object.assign({}, {
                ...request,
                trafficOfCouponOffFlag: "1",
            });
            defaultContext.responseHeader = Object.assign({}, { ...responseHeader, processCode: ResultCdConstants.CODE_999999 });
            const response = await LineGroupUseAcquisitionHandler.CommonCheckCatchErrorHandler(defaultRequest, defaultContext, new Error("test error"));
            checkEmptyResponseBody(response, ResultCdConstants.CODE_999999, "1");
        });
    });

    describe("Handler", () => {
        it("should return 090101 if failing line group id format check", async () => {
            defaultContext.jsonBody = Object.assign({}, {
                ...request,
                lineGroupId: "",
            });
            const response = await LineGroupUseAcquisitionHandler.Handler(defaultRequest, defaultContext);
            checkEmptyResponseBody(response, ResultCdConstants.CODE_090101, "0");
        });

        it("test on empty response with traffic of coupon off flag", async () => {
            defaultContext.jsonBody = Object.assign({}, {
                ...request,
                trafficOfCouponOffFlag: "1",
                lineGroupId: "",
            });
            const response = await LineGroupUseAcquisitionHandler.Handler(defaultRequest, defaultContext);
            checkEmptyResponseBody(response, ResultCdConstants.CODE_090101, "1");
        });

        it("should return 090101 if failing plan id format check", async () => {
            defaultContext.jsonBody = Object.assign({}, {
                ...request,
                potalGroupPlanID: "",
            });
            const response = await LineGroupUseAcquisitionHandler.Handler(defaultRequest, defaultContext);
            checkEmptyResponseBody(response, ResultCdConstants.CODE_090101, "0");
        });

        it("should return 090101 if failing traffic of coupon off flag format check", async () => {
            defaultContext.jsonBody = Object.assign({}, {
                ...request,
                trafficOfCouponOffFlag: "2",
            });
            const response = await LineGroupUseAcquisitionHandler.Handler(defaultRequest, defaultContext);
            checkEmptyResponseBody(response, ResultCdConstants.CODE_090101, "0");
        });

        it("should return 090102 if failed to get tenant", async () => {
            defaultContext.jsonBody = Object.assign({}, {
                ...request,
            });
            jest.spyOn(APICommonDAO.prototype, "getTenants").mockImplementation(async () => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });
            const response = await LineGroupUseAcquisitionHandler.Handler(defaultRequest, defaultContext);
            checkEmptyResponseBody(response, ResultCdConstants.CODE_090102, "0");
        });

        it("should return 090102 if tenant type = 1 and line number is null or empty string", async () => {
            defaultContext.jsonBody = Object.assign({}, {
                ...request,
                tenantId: "CON000",     // tenant type = 1
                lineNo: "",
            });
            const response = await LineGroupUseAcquisitionHandler.Handler(defaultRequest, defaultContext);
            checkEmptyResponseBody(response, ResultCdConstants.CODE_090102, "0");
        });

        it("should return 090101 if failing line no format check (but not empty)", async () => {
            defaultContext.jsonBody = Object.assign({}, {
                ...request,
                lineNo: "0",
            });
            const response = await LineGroupUseAcquisitionHandler.Handler(defaultRequest, defaultContext);
            checkEmptyResponseBody(response, ResultCdConstants.CODE_090101, "0");
        });

        it("should return 090103 if failed to get tenant group plan", async () => {
            jest.spyOn(APICommonDAO.prototype, "getTenantGroupPlans").mockImplementation(async () => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });
            const response = await LineGroupUseAcquisitionHandler.Handler(defaultRequest, defaultContext);
            checkEmptyResponseBody(response, ResultCdConstants.CODE_090103, "0");
        });

        it("should return 090103 if plan does not belongs to tenant", async () => {
            defaultContext.jsonBody = Object.assign({}, {
                ...request,
                potalGroupPlanID: "10001",
            });
            const response = await LineGroupUseAcquisitionHandler.Handler(defaultRequest, defaultContext);
            checkEmptyResponseBody(response, ResultCdConstants.CODE_090103, "0");
        });

        it("should return 090104 if failed to get using group plans", async () => {
            jest.spyOn(APILinesGroupDAO.prototype, "getUsingGroupPlans").mockImplementation(async () => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });
            const response = await LineGroupUseAcquisitionHandler.Handler(defaultRequest, defaultContext);
            checkEmptyResponseBody(response, ResultCdConstants.CODE_090104, "0");
        });

        it("should return 090104 if plan does not belongs to group if tenant type is not 1", async () => {
            defaultContext.jsonBody = Object.assign({}, {
                ...request,
                lineGroupId: "1000000",
            });
            const response = await LineGroupUseAcquisitionHandler.Handler(defaultRequest, defaultContext);
            checkEmptyResponseBody(response, ResultCdConstants.CODE_090104, "0");
        });

        it("should return 090201 if group plan entity is null", async () => {
            jest.spyOn(APICommonDAO.prototype, "getGroupPlans").mockReturnValue(Promise.resolve(null));
            const response = await LineGroupUseAcquisitionHandler.Handler(defaultRequest, defaultContext);
            checkEmptyResponseBody(response, ResultCdConstants.CONST_090201, "0");
        });

        it("should return 090201 if not able to get group plan entity", async () => {
            jest.spyOn(APICommonDAO.prototype, "getGroupPlans").mockImplementation(async () => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });
            const response = await LineGroupUseAcquisitionHandler.Handler(defaultRequest, defaultContext);
            checkEmptyResponseBody(response, ResultCdConstants.CONST_090201, "0");
        });

        it("should return 090203 if failed to get line groups info", async () => {
            jest.spyOn(APILinesGroupDAO.prototype, "getLineGroupsInfo").mockImplementation(async () => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });
            const response = await LineGroupUseAcquisitionHandler.Handler(defaultRequest, defaultContext);
            checkEmptyResponseBody(response, ResultCdConstants.CONST_090203, "0");
        });

        describe("with tenant type != 1", () => {
            it("should return 090203 if failed to get a line group entity that status is 1 and have plan id", async() => {
                jest.spyOn(APILinesGroupDAO.prototype, "getLineGroupsInfo").mockReturnValue(Promise.resolve(null));
                const response = await LineGroupUseAcquisitionHandler.Handler(defaultRequest, defaultContext);
                checkEmptyResponseBody(response, ResultCdConstants.CONST_090203, "0");
            });

            it("should return 090202 if failed to get line id", async () => {
                jest.spyOn(APICommonDAO.prototype, "getLinesID").mockImplementation(async () => {
                    throw new ConnectionTimedOutError(new Error("timeout"));
                });
                const response = await LineGroupUseAcquisitionHandler.Handler(defaultRequest, defaultContext);
                checkEmptyResponseBody(response, ResultCdConstants.CONST_090202, "0");
            });

            it("should return 090202 if line id is null", async () => {
                jest.spyOn(APICommonDAO.prototype, "getLinesID").mockReturnValue(Promise.resolve(null));
                const response = await LineGroupUseAcquisitionHandler.Handler(defaultRequest, defaultContext);
                checkEmptyResponseBody(response, ResultCdConstants.CONST_090202, "0");
            });
        });

        describe("with tenant type = 1", () => {
            it("should return 090203 if line groups entity is not null", async () => {
                jest.spyOn(APICommonDAO.prototype, "getTenants").mockReturnValue(Promise.resolve(
                    TenantsEntity.build({
                        tenantId: "CON000",
                        tenantType: 1,
                    })
                ));
                const response = await LineGroupUseAcquisitionHandler.Handler(defaultRequest, defaultContext);
                checkEmptyResponseBody(response, ResultCdConstants.CONST_090203, "0");
            });
        });

        it("should return 090204 if failed to get last month traffic", async () => {
            jest.spyOn(APILinesGroupDAO.prototype, "getTraffic").mockImplementation(async () => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });
            const response = await LineGroupUseAcquisitionHandler.Handler(defaultRequest, defaultContext);
            checkEmptyResponseBody(response, ResultCdConstants.CONST_090204, "0");
        });

        it("should return 090205 if failed to get traffic 2 months ago", async() => {
            jest.spyOn(APILinesGroupDAO.prototype, "getTraffic").mockReturnValueOnce(Promise.resolve(null)).mockImplementationOnce(async () => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });
            const response = await LineGroupUseAcquisitionHandler.Handler(defaultRequest, defaultContext);
            checkEmptyResponseBody(response, ResultCdConstants.CONST_090205, "0");
        });

        it("should return 090301 if failed to get tpc connection", async () => {
            jest.spyOn(TenantManage.prototype, "checkTpcConnection").mockImplementation(async () => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });
            const response = await LineGroupUseAcquisitionHandler.Handler(defaultRequest, defaultContext);
            checkEmptyResponseBody(response, ResultCdConstants.CODE_090301, "0");
        });

        it("should return 090301 if first value of check tpc connection is false", async () => {
            jest.spyOn(TenantManage.prototype, "checkTpcConnection").mockReturnValue(Promise.resolve([false, "", ""]));
            const response = await LineGroupUseAcquisitionHandler.Handler(defaultRequest, defaultContext);
            checkEmptyResponseBody(response, ResultCdConstants.CODE_090301, "0");
        });
    });

    describe("Handler with SOAP", () => {
        beforeEach(() => {
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, createResponseXML("OK"), responseHeaders);
            jest.spyOn(Check, "getPreXDay").mockImplementation((preXDay: number) => {
                const sysdate = new Date(1727654400000);            // 2024-09-30
                const lastdate = subDays(sysdate, preXDay);
                return format(lastdate, "yyyyMMdd");
            })
        });

        afterEach(() => {
            nock.abortPendingRequests();
            nock.cleanAll();
        });

        it("should return 090401 if SOAP output returns 000951", async () => {
            jest.spyOn(SOAPCommonOutputDto.prototype, "getProcessCode").mockReturnValue(ResultCdConstants.CODE_000951);
            const response = await LineGroupUseAcquisitionHandler.Handler(defaultRequest, defaultContext);
            checkEmptyResponseBody(response, ResultCdConstants.CODE_090401, "0");
        });

        it("should return 090401 if failed to get node content", async () => {
            jest.spyOn(SOAPCommon.prototype, "getNodeContent").mockReturnValue("NG");
            const response = await LineGroupUseAcquisitionHandler.Handler(defaultRequest, defaultContext);
            checkEmptyResponseBody(response, ResultCdConstants.CODE_090401, "0");
        });

        it("should return 090401 if SOAPException", async () => {
            nock.abortPendingRequests();
            nock.cleanAll();
            nock(configData.soapApiUrl)
                .post("")
                .delay(configData.timeout + configData.timeoutOffset)
                .reply(200, createResponseXML("OK"));
            const response = await LineGroupUseAcquisitionHandler.Handler(defaultRequest, defaultContext);
            checkEmptyResponseBody(response, ResultCdConstants.CODE_090401, "0");
        });

        it("should return 000000 for trafficOfCouponOffFlag = 0", async () => {
            const response = await LineGroupUseAcquisitionHandler.Handler(defaultRequest, defaultContext);
            checkNonEmptyResponseBody(response, "0");
        });

        it("should return 000000 for trafficOfCouponOffFlag = 0 (number)", async () => {
            defaultContext.jsonBody = Object.assign({}, {
                ...request,
                trafficOfCouponOffFlag: "0",
            });
            const response = await LineGroupUseAcquisitionHandler.Handler(defaultRequest, defaultContext);
            checkNonEmptyResponseBody(response, "0");
        });

        it("should return 000000 for trafficOfCouponOffFlag = 1", async () => {
            defaultContext.jsonBody = Object.assign({}, {
                ...request,
                trafficOfCouponOffFlag: "1",
            });
            const response = await LineGroupUseAcquisitionHandler.Handler(defaultRequest, defaultContext);
            checkNonEmptyResponseBody(response, "1");
        });

        it("should return 000000 for trafficOfCouponOffFlag = 1 (number)", async () => {
            defaultContext.jsonBody = Object.assign({}, {
                ...request,
                trafficOfCouponOffFlag: 1,
            });
            const response = await LineGroupUseAcquisitionHandler.Handler(defaultRequest, defaultContext);
            checkNonEmptyResponseBody(response, "1");
        });

        it("should return 000000 : lineGroupId is number", async () => {
            defaultContext.jsonBody = Object.assign({}, {
                ...request,
                trafficOfCouponOffFlag: "1",
                lineGroupId: parseInt(request.lineGroupId, 10),
            });
            const response = await LineGroupUseAcquisitionHandler.Handler(defaultRequest, defaultContext);
            checkNonEmptyResponseBody(response, "1");
        });

        it("should return 000000 : potalGroupPlanID is number", async () => {
            defaultContext.jsonBody = Object.assign({}, {
                ...request,
                trafficOfCouponOffFlag: "1",
                potalGroupPlanID: parseInt(request.potalGroupPlanID, 10),
            });
            const response = await LineGroupUseAcquisitionHandler.Handler(defaultRequest, defaultContext);
            checkNonEmptyResponseBody(response, "1");
        });
    });
});