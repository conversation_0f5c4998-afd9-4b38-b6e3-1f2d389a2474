import { addMonths, formatDate } from "date-fns";
import { ConnectionTimedOutError, Sequelize } from "sequelize";

import {
    describeWithDB,
    generateProcessID,
    useFasterRetries,
} from "../testing/TestHelper";
import DefaultContext from "../testing/DefaultContext";
import DefaultRequest from "../testing/DefaultRequest";
import SOAPTestHelper from "../testing/soapTestHelper";

import ResultCdConstants from "@/core/constant/ResultCdConstants";

import { disconnectMongo, useMongo } from "@/database/mongo";
import { usePsql } from "@/database/psql";

import AppConfig from "@/appconfig";
import TenantManage from "@/core/common/TenantManage";
import APICommonDAO from "@/core/dao/APICommonDAO";
import APILinesDAO from "@/core/dao/APILinesDAO";
import APILinesGroupDAO from "@/core/dao/ApiLinesGroupDAO";
import RequestHeader from "@/core/dto/RequestHeader";
import ResponseHeader from "@/core/dto/ResponseHeader";

import { LinesEntity } from "@/core/entity/LinesEntity";
import PlansEntity from "@/core/entity/PlansEntity";
import { TenantNnumbersEntity } from "@/core/entity/TenantNnumbersEntity";
import { TenantsEntity } from "@/core/entity/TenantsEntity";

import TestHelper from "./testdata/lineUseAcquisitionHandler.testdata";
import { LineUseAcquisitionHandler } from "@/functions/LineUseAcquisitionHandler";
import LineUseAcquisitionService from "@/core/service/impl/LineUseAcquisitionService";

// TODO add tests with complete XML responses
describeWithDB("@/functions/LineUseAcquisitionHandler", () => {
    let sequelize: Sequelize;

    const testdata = {
        tenant: {
            tst: "TST000",
            con: "CON000",
        },
        nBan: {
            tst: "",
            con: "",
        },
        lineNo: {
            none: "08024101600",
            tst: "08024101601",
            con: "08024101602",
            tst_noplan: "08024101603",
        },
        potalPlanID: { tst: "40056", con: "99038" }, // only for CON
        resalePlanID: { tst: "AR708", con: "BS022" },
        getIpAddress: {
            /** 端末IPアドレス取得なし */
            off: "0",
            /** 端末IPアドレス取得あり */
            on: "1",
        },
        addOption: {
            o0: "0",
            o1: "1",
            o2: "2",
            o3: "3",
            o4: "4",
            null: null,
        },
        potalGroupPlanID: { tst: "50001", con: "10041" }, // option=4 only
        version: {
            on: "1",
            off: undefined,
        },
        tpcDest: {
            // [tpc1, tpc2]
            tst: ["", ""],
            con: ["", ""],
        },
        lineGroupId: {
            tst: "9090001",
            con: "9090002",
        },
    };

    const originalData = {
        // maybe null in local DB (valid is 1~12)
        refServicePattern: { tst: null as number, con: null as number },
    };

    const requestHeader: RequestHeader = {
        sequenceNo: "1234567",
        functionType: "02",
        senderSystemId: "000",
        apiKey: "",
    };

    const __request = {
        requestHeader,
        tenantId: testdata.tenant.tst,
        lineNo: testdata.lineNo.tst,
        potalPlanID: undefined, // testdata.potalPlanID, // only for OCN tenant
        getIpAddress: "0",
        addOption: "0",
        potalGroupPlanID: testdata.potalGroupPlanID.tst, // only for option=4
        version: "1",
    };

    const responseHeader = {
        sequenceNo: __request.requestHeader.sequenceNo,
        receivedDate: formatDate(new Date(), "yyyy/MM/dd HH:mm:ss"),
        processCode: ResultCdConstants.CODE_000000,
        apiProcessID: generateProcessID(),
    } as ResponseHeader;

    beforeAll(async () => {
        await AppConfig.loadCoreMvnoConfig(DefaultContext);
        sequelize = await usePsql();
        await useMongo(DefaultContext);

        await clearTestData();
        await getOriginalData();

        // get nBan
        const nBan = await TenantNnumbersEntity.findAll({
            where: {
                tenantId: [testdata.tenant.tst, testdata.tenant.con],
            },
            attributes: ["tenantId", "nnumber"],
        });
        testdata.nBan.tst = nBan.find(
            (n) => n.tenantId === testdata.tenant.tst,
        )?.nnumber;
        testdata.nBan.con = nBan.find(
            (n) => n.tenantId === testdata.tenant.con,
        )?.nnumber;

        // get tpcDest
        const tenants = await TenantsEntity.findAll({
            where: { tenantId: [testdata.tenant.tst, testdata.tenant.con] },
        });
        const tenantTST = tenants.find(
            (t) => t.tenantId === testdata.tenant.tst,
        );
        const tenantCON = tenants.find(
            (t) => t.tenantId === testdata.tenant.con,
        );
        testdata.tpcDest.tst = [tenantTST?.tpc, tenantTST?.tpc2];
        testdata.tpcDest.con = [tenantCON?.tpc, tenantCON?.tpc2];
    });

    afterAll(async () => {
        await restoreData();
        await disconnectMongo(DefaultContext);
        await sequelize.close();
    });

    beforeEach(async () => {
        await prepareTestData();

        DefaultContext.jsonBody = Object.assign({}, __request);
        DefaultContext.responseHeader = Object.assign({}, responseHeader);
        SOAPTestHelper.setup();
        useFasterRetries();
    });

    afterEach(async () => {
        jest.restoreAllMocks();
        jest.clearAllMocks();
        SOAPTestHelper.tearDown();

        await clearTestData();
        await restoreData();
    });

    async function getOriginalData() {
        const conPlan = await PlansEntity.findOne({
            where: {
                planId: testdata.potalPlanID.con,
            },
        });
        originalData.refServicePattern.con = conPlan?.refServicePattern;
        const tstPlan = await PlansEntity.findOne({
            where: {
                planId: testdata.potalPlanID.tst,
            },
        });
        originalData.refServicePattern.tst = tstPlan?.refServicePattern;
    }

    async function restoreData() {
        await TestHelper.setServicePattern(
            testdata.potalPlanID.con,
            originalData.refServicePattern.con,
        );
        await TestHelper.setServicePattern(
            testdata.potalPlanID.tst,
            originalData.refServicePattern.tst,
        );
    }

    async function prepareTestData() {
        await LinesEntity.create({
            lineId: testdata.lineNo.tst,
            lineStatus: "01",
            nnumber: testdata.nBan.tst,
            simFlag: false,
            pricePlanId: testdata.resalePlanID.tst,
            usageStatus: 1,
        });
        await LinesEntity.create({
            lineId: testdata.lineNo.tst_noplan,
            lineStatus: "01",
            nnumber: testdata.nBan.tst,
            simFlag: false,
        });
        await LinesEntity.create({
            lineId: testdata.lineNo.con,
            lineStatus: "01",
            nnumber: testdata.nBan.con,
            simFlag: false,
            pricePlanId: testdata.resalePlanID.con,
            usageStatus: 2,
        });

        // setup service pattern
        await TestHelper.setServicePattern(testdata.potalPlanID.con, 1);
        await TestHelper.setServicePattern(testdata.potalPlanID.tst, 2);
        // setup traffic
        const trafficMonths = {
            previous: addMonths(new Date(), -1),
            beforehand: addMonths(new Date(), -2),
        };
        await Promise.all([
            TestHelper.createTraffic(
                testdata.lineNo.tst,
                trafficMonths.previous.getFullYear(),
                trafficMonths.previous.getMonth() + 1,
                12345678,
                1234,
            ),
            TestHelper.createTraffic(
                testdata.lineNo.tst,
                trafficMonths.beforehand.getFullYear(),
                trafficMonths.beforehand.getMonth() + 1,
                300000,
                5555,
            ),
            TestHelper.createTraffic(
                testdata.lineNo.con,
                trafficMonths.previous.getFullYear(),
                trafficMonths.previous.getMonth() + 1,
                200000,
                1234,
            ),
            TestHelper.createTraffic(
                testdata.lineNo.con,
                trafficMonths.beforehand.getFullYear(),
                trafficMonths.beforehand.getMonth() + 1,
                100000,
                null,
            ),
        ]);
    }

    async function clearTestData() {
        await TestHelper.removeTraffic([
            testdata.lineNo.tst,
            testdata.lineNo.con,
            testdata.lineNo.tst_noplan,
        ]);
        await LinesEntity.destroy({
            where: {
                lineId: [
                    testdata.lineNo.tst,
                    testdata.lineNo.con,
                    testdata.lineNo.tst_noplan,
                ],
            },
        });
    }

    function expectResponseCode(result: any, code: string) {
        expect(typeof result).toBe("object");
        expect(result).toHaveProperty("jsonBody");
        expect(result.jsonBody).toHaveProperty("responseHeader");
        expect(result.jsonBody.responseHeader.apiProcessID).toEqual(
            DefaultContext.responseHeader.apiProcessID,
        );
        expect(result.jsonBody.responseHeader.processCode).toEqual(code);
        expect(result.jsonBody.responseHeader.sequenceNo).toEqual(
            DefaultContext.responseHeader.sequenceNo,
        );
        expect(result.jsonBody.responseHeader.receivedDate).toEqual(
            DefaultContext.responseHeader.receivedDate,
        );
    }

    describe("OK cases", () => {
        test("should return CODE_000000 and IP address if getIpAddress is 1", async () => {
            const ipAddress = "***********";
            SOAPTestHelper.mockResponse(
                "serviceProfileQuery",
                testdata.tpcDest.tst.find((r) => r),
                {
                    result: "OK",
                    data: [["//dyn_sub_info/dyn_ue_ipadr", ipAddress]],
                },
            );
            DefaultContext.jsonBody.getIpAddress = "1";
            const response = await LineUseAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expectResponseCode(response, ResultCdConstants.CODE_000000);
            expect(response.jsonBody).toHaveProperty("terminalIpAddress");
            expect(response.jsonBody.terminalIpAddress).toEqual(ipAddress);
        });

        test("should return CODE_000000 and IP address if getIpAddress is 1 [number]", async () => {
            const ipAddress = "***********";
            SOAPTestHelper.mockResponse(
                "serviceProfileQuery",
                testdata.tpcDest.tst.find((r) => r),
                {
                    result: "OK",
                    data: [["//dyn_sub_info/dyn_ue_ipadr", ipAddress]],
                },
            );
            DefaultContext.jsonBody.getIpAddress = 1;
            const response = await LineUseAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expectResponseCode(response, ResultCdConstants.CODE_000000);
            expect(response.jsonBody).toHaveProperty("terminalIpAddress");
            expect(response.jsonBody.terminalIpAddress).toEqual(ipAddress);
        });

        const versionCases = [null, "1"];

        test.each(versionCases)(
            "should return CODE_000000 (option=0, version=%s)",
            async (version) => {
                SOAPTestHelper.mockResponse(
                    "serviceProfileQuery",
                    testdata.tpcDest.tst.find((r) => r),
                    {
                        result: "OK",
                        data: [],
                    },
                );
                DefaultContext.jsonBody.version = version;
                DefaultContext.jsonBody.addOption = testdata.addOption.o0;
                const response = await LineUseAcquisitionHandler.Handler(
                    DefaultRequest,
                    DefaultContext,
                );
                expectResponseCode(response, ResultCdConstants.CODE_000000);
                if (version === "1") {
                    expect(response.jsonBody).toHaveProperty("lineStatus");
                } else {
                    expect(response.jsonBody).not.toHaveProperty("lineStatus");
                }
            },
        );

        test.each(versionCases)(
            "should return CODE_000000 (option=0, version=%s) [both are number]",
            async (version) => {
                SOAPTestHelper.mockResponse(
                    "serviceProfileQuery",
                    testdata.tpcDest.tst.find((r) => r),
                    {
                        result: "OK",
                        data: [],
                    },
                );
                DefaultContext.jsonBody.version = version ? parseInt(version, 10) : version;
                DefaultContext.jsonBody.addOption = parseInt(testdata.addOption.o0, 10);
                const response = await LineUseAcquisitionHandler.Handler(
                    DefaultRequest,
                    DefaultContext,
                );
                expectResponseCode(response, ResultCdConstants.CODE_000000);
                if (version === "1") {
                    expect(response.jsonBody).toHaveProperty("lineStatus");
                } else {
                    expect(response.jsonBody).not.toHaveProperty("lineStatus");
                }
            },
        );

        test.each(versionCases)(
            "should return CODE_000000 (option=1, version=%s)",
            async (version) => {
                SOAPTestHelper.mockResponse(
                    "serviceProfileQuery",
                    testdata.tpcDest.tst.find((r) => r),
                    {
                        result: "OK",
                        data: [],
                    },
                );
                DefaultContext.jsonBody.version = version;
                DefaultContext.jsonBody.addOption = testdata.addOption.o1;
                const response = await LineUseAcquisitionHandler.Handler(
                    DefaultRequest,
                    DefaultContext,
                );
                expectResponseCode(response, ResultCdConstants.CODE_000000);
                if (version === "1") {
                    expect(response.jsonBody).toHaveProperty("lineStatus");
                } else {
                    expect(response.jsonBody).not.toHaveProperty("lineStatus");
                }
            },
        );

        test.each(versionCases)(
            "should return CODE_000000 (option=2, version=%s)",
            async (version) => {
                SOAPTestHelper.mockResponse(
                    "serviceProfileQuery",
                    testdata.tpcDest.tst.find((r) => r),
                    {
                        result: "OK",
                        data: [],
                    },
                );
                DefaultContext.jsonBody.version = version;
                DefaultContext.jsonBody.addOption = testdata.addOption.o2;
                const response = await LineUseAcquisitionHandler.Handler(
                    DefaultRequest,
                    DefaultContext,
                );
                expectResponseCode(response, ResultCdConstants.CODE_000000);
                if (version === "1") {
                    expect(response.jsonBody).toHaveProperty("lineStatus");
                } else {
                    expect(response.jsonBody).not.toHaveProperty("lineStatus");
                }
            },
        );

        test.each(versionCases)(
            "should return CODE_000000 (option=3, version=%s)",
            async (version) => {
                SOAPTestHelper.mockResponse(
                    "serviceProfileQuery",
                    testdata.tpcDest.tst.find((r) => r),
                    {
                        result: "OK",
                        data: [],
                    },
                );
                DefaultContext.jsonBody.version = version;
                DefaultContext.jsonBody.addOption = testdata.addOption.o3;
                const response = await LineUseAcquisitionHandler.Handler(
                    DefaultRequest,
                    DefaultContext,
                );
                expectResponseCode(response, ResultCdConstants.CODE_000000);
                if (version === "1") {
                    expect(response.jsonBody).toHaveProperty("lineStatus");
                } else {
                    expect(response.jsonBody).not.toHaveProperty("lineStatus");
                }
            },
        );

        test.each(versionCases)(
            "should return CODE_000000 (option=4, version=%s)",
            async (version) => {
                SOAPTestHelper.mockResponse(
                    "serviceProfileQuery",
                    testdata.tpcDest.tst.find((r) => r),
                    {
                        result: "OK",
                        data: [],
                    },
                );
                DefaultContext.jsonBody.version = version;
                DefaultContext.jsonBody.addOption = testdata.addOption.o4;
                DefaultContext.jsonBody.tenantId = testdata.tenant.con;
                DefaultContext.jsonBody.lineNo = testdata.lineNo.con;
                DefaultContext.jsonBody.potalPlanID = testdata.potalPlanID.con;
                DefaultContext.jsonBody.potalGroupPlanID =
                    testdata.potalGroupPlanID.con;

                const response = await LineUseAcquisitionHandler.Handler(
                    DefaultRequest,
                    DefaultContext,
                );
                expectResponseCode(response, ResultCdConstants.CODE_000000);
                if (version === "1") {
                    expect(response.jsonBody).toHaveProperty("lineStatus");
                } else {
                    expect(response.jsonBody).not.toHaveProperty("lineStatus");
                }
            },
        );

        const servicePatterns = [
            "1",
            "2",
            "3",
            "4",
            "5",
            "6",
            "8",
            "9",
            "10",
            "11",
            "12",
        ];
        test.each(servicePatterns)(
            "should return CODE_000000 (servicePattern=%s (CON option=4))",
            async (servicePattern) => {
                SOAPTestHelper.mockResponse(
                    "serviceProfileQuery",
                    testdata.tpcDest.con.find((r) => r),
                    {
                        result: "OK",
                        data: [],
                    },
                );
                DefaultContext.jsonBody.addOption = testdata.addOption.o4;
                DefaultContext.jsonBody.tenantId = testdata.tenant.con;
                DefaultContext.jsonBody.lineNo = testdata.lineNo.con;
                DefaultContext.jsonBody.potalPlanID = testdata.potalPlanID.con;
                DefaultContext.jsonBody.potalGroupPlanID =
                    testdata.potalGroupPlanID.con;
                await TestHelper.setServicePattern(
                    testdata.potalGroupPlanID.con,
                    +servicePattern,
                );

                const response = await LineUseAcquisitionHandler.Handler(
                    DefaultRequest,
                    DefaultContext,
                );
                expectResponseCode(response, ResultCdConstants.CODE_000000);
            },
        );
    });

    describe("NG cases", () => {
        test("should return CODE_020101 if line is empty", async () => {
            delete DefaultContext.jsonBody.lineNo;
            const response = await LineUseAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expectResponseCode(response, ResultCdConstants.CODE_020101);
        });

        test("should return CODE_020101 if potalPlanID format is invalid", async () => {
            DefaultContext.jsonBody.tenantId = testdata.tenant.con;
            DefaultContext.jsonBody.potalPlanID = "abc";
            const response = await LineUseAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );

            expectResponseCode(response, ResultCdConstants.CODE_020101);
        });

        test("should return CODE_020101 if getIpAddress format is invalid", async () => {
            DefaultContext.jsonBody.getIpAddress = "2";
            const response = await LineUseAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expectResponseCode(response, ResultCdConstants.CODE_020101);
        });

        test("should return CODE_020101 if addOption format is invalid", async () => {
            DefaultContext.jsonBody.addOption = "5";
            const response = await LineUseAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );

            expectResponseCode(response, ResultCdConstants.CODE_020101);
        });

        test("should return CODE_020101 if potalGroupPlanID format is invalid", async () => {
            DefaultContext.jsonBody.addOption = "4";
            DefaultContext.jsonBody.potalGroupPlanID = "123456";
            const response = await LineUseAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expectResponseCode(response, ResultCdConstants.CODE_020101);
        });

        test("should return CODE_020101 if version format is invalid", async () => {
            DefaultContext.jsonBody.version = "2";
            const response = await LineUseAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expectResponseCode(response, ResultCdConstants.CODE_020101);
        });

        test("should return CODE_020102 if tenant could not be found", async () => {
            DefaultContext.jsonBody.tenantId = "TST001";
            const response = await LineUseAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expectResponseCode(response, ResultCdConstants.CODE_020102);
        });

        test("should return CODE_020102 if failed to get tenant information", async () => {
            jest.spyOn(APICommonDAO.prototype, "getTenants").mockRejectedValue(
                new ConnectionTimedOutError(new Error("test error [timeout]")),
            );

            const response = await LineUseAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expectResponseCode(response, ResultCdConstants.CODE_020102);
        });

        test("should return CODE_020103 if tenant is OCN and potalPlanID is empty", async () => {
            DefaultContext.jsonBody.tenantId = testdata.tenant.con;
            delete DefaultContext.jsonBody.potalPlanID;
            const response = await LineUseAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expectResponseCode(response, ResultCdConstants.CODE_020103);
        });

        test("should return CODE_020106 if tenant is not OCN and potalPlanID is not empty", async () => {
            DefaultContext.jsonBody.tenantId = testdata.tenant.tst;
            DefaultContext.jsonBody.potalPlanID = testdata.potalPlanID.tst;
            const response = await LineUseAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expectResponseCode(response, ResultCdConstants.CODE_020106);
        });

        test("should return CODE_020104 if lineNo does not exist", async () => {
            DefaultContext.jsonBody.lineNo = testdata.lineNo.none;
            const response = await LineUseAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expectResponseCode(response, ResultCdConstants.CODE_020104);
        });

        test("should return CODE_020104 if lineNo does not belong to tenant", async () => {
            DefaultContext.jsonBody.tenantId = testdata.tenant.tst;
            DefaultContext.jsonBody.lineNo = testdata.lineNo.con;
            const response = await LineUseAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expectResponseCode(response, ResultCdConstants.CODE_020104);
        });

        test("should return CODE_020104 if failed to fetch tenant/line information", async () => {
            jest.spyOn(TenantManage.prototype, "doCheck").mockRejectedValue(
                new ConnectionTimedOutError(new Error("test error [timeout]")),
            );
            const response = await LineUseAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expectResponseCode(response, ResultCdConstants.CODE_020104);
        });

        test("should return CODE_020105 if plan ID does not exist (CON)", async () => {
            DefaultContext.jsonBody.tenantId = testdata.tenant.con;
            DefaultContext.jsonBody.lineNo = testdata.lineNo.con;
            DefaultContext.jsonBody.potalPlanID = "99999";
            const response = await LineUseAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expectResponseCode(response, ResultCdConstants.CODE_020105);
        });

        test("should return CODE_020105 if failed to fetch plan information", async () => {
            DefaultContext.jsonBody.tenantId = testdata.tenant.con;
            DefaultContext.jsonBody.lineNo = testdata.lineNo.con;
            DefaultContext.jsonBody.potalPlanID = testdata.potalPlanID.con;
            jest.spyOn(
                APICommonDAO.prototype,
                "getTenantPlans",
            ).mockRejectedValue(
                new ConnectionTimedOutError(new Error("test error [timeout]")),
            );
            const response = await LineUseAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expectResponseCode(response, ResultCdConstants.CODE_020105);
        });

        test("should return CODE_020107 if group plan ID is empty while option is 4", async () => {
            DefaultContext.jsonBody.addOption = "4";
            delete DefaultContext.jsonBody.potalGroupPlanID;
            const response = await LineUseAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expectResponseCode(response, ResultCdConstants.CODE_020107);
        });

        test("should return CODE_020108 if group plan ID does not exist", async () => {
            DefaultContext.jsonBody.addOption = "4";
            DefaultContext.jsonBody.potalGroupPlanID = "99999";
            const response = await LineUseAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expectResponseCode(response, ResultCdConstants.CODE_020108);
        });

        test("should return CODE_020108 if failed to fetch group plan information", async () => {
            DefaultContext.jsonBody.addOption = "4";
            DefaultContext.jsonBody.potalGroupPlanID =
                testdata.potalGroupPlanID.tst;
            jest.spyOn(
                APICommonDAO.prototype,
                "getTenantGroupPlans",
            ).mockRejectedValue(
                new ConnectionTimedOutError(new Error("test error [timeout]")),
            );
            const response = await LineUseAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expectResponseCode(response, ResultCdConstants.CODE_020108);
        });

        test("should return CODE_020201 if plan does not exist (CON)", async () => {
            // NOTE this case won't be possible because of `getTenantPlans` validation before
            DefaultContext.jsonBody.tenantId = testdata.tenant.con;
            DefaultContext.jsonBody.lineNo = testdata.lineNo.con;
            DefaultContext.jsonBody.potalPlanID = testdata.potalPlanID.con;
            jest.spyOn(APICommonDAO.prototype, "getPlans").mockResolvedValue(
                [],
            );

            const response = await LineUseAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expectResponseCode(response, ResultCdConstants.CODE_020201);
        });

        test("should return CODE_020201 if failed to fetch plan information (CON)", async () => {
            DefaultContext.jsonBody.tenantId = testdata.tenant.con;
            DefaultContext.jsonBody.lineNo = testdata.lineNo.con;
            DefaultContext.jsonBody.potalPlanID = testdata.potalPlanID.con;
            jest.spyOn(APICommonDAO.prototype, "getPlans").mockRejectedValue(
                new ConnectionTimedOutError(new Error("test error [timeout]")),
            );

            const response = await LineUseAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expectResponseCode(response, ResultCdConstants.CODE_020201);
        });

        test("should return CODE_020201 if refServicePattern of plan is null (CON)", async () => {
            DefaultContext.jsonBody.tenantId = testdata.tenant.con;
            DefaultContext.jsonBody.lineNo = testdata.lineNo.con;
            DefaultContext.jsonBody.potalPlanID = testdata.potalPlanID.con;
            await TestHelper.setServicePattern(testdata.potalPlanID.con, null);

            const response = await LineUseAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expectResponseCode(response, ResultCdConstants.CODE_020201);
        });

        test("should return CODE_020201 if plan not found by lineNo", async () => {
            DefaultContext.jsonBody.tenantId = testdata.tenant.tst;
            DefaultContext.jsonBody.lineNo = testdata.lineNo.tst_noplan;
            delete DefaultContext.jsonBody.potalPlanID;

            const response = await LineUseAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expectResponseCode(response, ResultCdConstants.CODE_020201);
        });

        test("should return CODE_020201 if failed to find plan by lineNo", async () => {
            DefaultContext.jsonBody.tenantId = testdata.tenant.tst;
            DefaultContext.jsonBody.lineNo = testdata.lineNo.tst_noplan;
            jest.spyOn(
                APILinesDAO.prototype,
                "getRefServicePattern",
            ).mockRejectedValue(
                new ConnectionTimedOutError(new Error("test error [timeout]")),
            );

            const response = await LineUseAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expectResponseCode(response, ResultCdConstants.CODE_020201);
        });

        test("should return CODE_020201 if plan refServicePattern is empty", async () => {
            DefaultContext.jsonBody.tenantId = testdata.tenant.tst;
            DefaultContext.jsonBody.lineNo = testdata.lineNo.tst;
            await TestHelper.setServicePattern(testdata.potalPlanID.tst, null);

            const response = await LineUseAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expectResponseCode(response, ResultCdConstants.CODE_020201);
        });

        test("should return CODE_020201 if getLineInfo failed (version=1)", async () => {
            DefaultContext.jsonBody.version = "1";
            jest.spyOn(APILinesDAO.prototype, "getLineInfo").mockRejectedValue(
                new ConnectionTimedOutError(new Error("test error [timeout]")),
            );

            const response = await LineUseAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expectResponseCode(response, ResultCdConstants.CODE_020201);
        });

        test("should return CODE_020202 if failed to get traffic for previous month", async () => {
            jest.spyOn(APILinesDAO.prototype, "getTraffic").mockRejectedValue(
                new ConnectionTimedOutError(new Error("test error [timeout]")),
            );
            const response = await LineUseAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expectResponseCode(response, ResultCdConstants.CODE_020202);
        });

        test("should return CODE_020203 if failed to get traffic for beforehand month", async () => {
            jest.spyOn(APILinesDAO.prototype, "getTraffic")
            .mockResolvedValueOnce(null)
            .mockRejectedValue(
                new ConnectionTimedOutError(new Error("test error [timeout]")),
            );
            const response = await LineUseAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expectResponseCode(response, ResultCdConstants.CODE_020203);
        });

        test("should return CODE_020301 if checkTpcConnection returns false", async () => {
            // NOTE this case won't be possible because of tenant validation before
            jest.spyOn(
                TenantManage.prototype,
                "checkTpcConnection",
            ).mockResolvedValue([false, "123", "123"]);
            const response = await LineUseAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expectResponseCode(response, ResultCdConstants.CODE_020301);
        });

        test("should return CODE_020301 if checkTpcConnection throws error", async () => {
            jest.spyOn(
                TenantManage.prototype,
                "checkTpcConnection",
            ).mockRejectedValue(
                new ConnectionTimedOutError(new Error("test error [timeout]")),
            );
            const response = await LineUseAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expectResponseCode(response, ResultCdConstants.CODE_020301);
        });

        test("should return CODE_020401 if TPC destination is empty", async () => {
            jest.spyOn(
                TenantManage.prototype,
                "checkTpcConnection",
            ).mockResolvedValue([true, "", null]);
            const response = await LineUseAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expectResponseCode(response, ResultCdConstants.CODE_020401);
        });

        test("should return CODE_020401 if SOAP response NG", async () => {
            SOAPTestHelper.mockResponse(
                "serviceProfileQuery",
                testdata.tpcDest.tst.find((r) => r),
                {
                    result: "NG",
                    errorMessage: "test error",
                },
            );
            const response = await LineUseAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expectResponseCode(response, ResultCdConstants.CODE_020401);
        });

        test("should return CODE_020501 if line group is not found", async () => {
            DefaultContext.jsonBody.addOption = "4";
            SOAPTestHelper.mockResponse(
                "serviceProfileQuery",
                testdata.tpcDest.tst.find((r) => r),
                {
                    result: "OK",
                    data: [
                        ["//GroupProfileInformation/grpnum", "0"], // line group id
                    ],
                },
            );
            const response = await LineUseAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expectResponseCode(response, ResultCdConstants.CODE_020501);
        });

        test("should return CODE_020501 if failed to fetch line group", async () => {
            DefaultContext.jsonBody.addOption = "4";
            SOAPTestHelper.mockResponse(
                "serviceProfileQuery",
                testdata.tpcDest.tst.find((r) => r),
                {
                    result: "OK",
                    data: [
                        ["//GroupProfileInformation/grpnum", "0"], // line group id
                    ],
                },
            );
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getUsingGroupPlans",
            ).mockRejectedValue(
                new ConnectionTimedOutError(new Error("test error [timeout]")),
            );
            const response = await LineUseAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expectResponseCode(response, ResultCdConstants.CODE_020501);
        });

        test("should return CODE_020502 if group plan does not exist", async () => {
            // NOTE since there is validation before, this case won't be possible
            DefaultContext.jsonBody.addOption = "4";
            SOAPTestHelper.mockResponse(
                "serviceProfileQuery",
                testdata.tpcDest.tst.find((r) => r),
                {
                    result: "OK",
                    data: [
                        ["//GroupProfileInformation/grpnum", "0"], // line group id
                    ],
                },
            );
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getUsingGroupPlans",
            ).mockResolvedValue("something");
            jest.spyOn(
                APICommonDAO.prototype,
                "getGroupPlans",
            ).mockResolvedValue(null);
            const response = await LineUseAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expectResponseCode(response, ResultCdConstants.CODE_020502);
        });

        test("should return CODE_020502 if getGroupPlans fails to query", async () => {
            DefaultContext.jsonBody.addOption = "4";
            SOAPTestHelper.mockResponse(
                "serviceProfileQuery",
                testdata.tpcDest.tst.find((r) => r),
                {
                    result: "OK",
                    data: [
                        ["//GroupProfileInformation/grpnum", "0"], // line group id
                    ],
                },
            );
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getUsingGroupPlans",
            ).mockResolvedValue("something");
            jest.spyOn(
                APICommonDAO.prototype,
                "getGroupPlans",
            ).mockImplementation(async () =>{
                throw new ConnectionTimedOutError(new Error("timeout error"));
            })
            const response = await LineUseAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expectResponseCode(response, ResultCdConstants.CODE_020502);
        });

        test("should return CODE_020502 if service pattern group is null", async () => {
            // NOTE since there is validation before, this case won't be possible
            DefaultContext.jsonBody.addOption = "4";
            SOAPTestHelper.mockResponse(
                "serviceProfileQuery",
                testdata.tpcDest.tst.find((r) => r),
                {
                    result: "OK",
                    data: [
                        ["//GroupProfileInformation/grpnum", "0"], // line group id
                    ],
                },
            );
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getUsingGroupPlans",
            ).mockResolvedValue("something");
            jest.spyOn(
                APICommonDAO.prototype,
                "getGroupPlans",
            ).mockResolvedValue({ refServicePattern: null } as any);
            const response = await LineUseAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expectResponseCode(response, ResultCdConstants.CODE_020502);
        });

        test("should return CODE_020401 if failed to send SOAP request", async () => {
            SOAPTestHelper.mockResponse(
                "serviceProfileQuery",
                testdata.tpcDest.tst.find((r) => r),
                {
                    result: "NG",
                    throwError: true,
                },
            );
            const response = await LineUseAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expectResponseCode(response, ResultCdConstants.CODE_020401);
        });

        test("should return CODE_999999 if unexpected error occurred", async () => {
            jest.spyOn(
                APILinesDAO.prototype,
                "getRefServicePattern",
            ).mockRejectedValue(new Error("test error"));
            const response = await LineUseAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expectResponseCode(response, ResultCdConstants.CODE_999999);
        });

        test("should return CODE_999999 if an unexpected error occurs calling service", async () => {
            jest.spyOn(
                LineUseAcquisitionService.prototype,
                "service",
            ).mockImplementationOnce(async () => {
                throw new Error("Unexpected error");
            });
            const response = await LineUseAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expectResponseCode(response, ResultCdConstants.CODE_999999);
        });
        
        test("CommonCheckFailedHandler", async () => {
            const errorMessage = "some error";
            DefaultContext.responseHeader.processCode = ResultCdConstants.CODE_999999;
            const response = await LineUseAcquisitionHandler.CommonCheckFailedHandler(
                DefaultRequest,
                DefaultContext,
                errorMessage
            );
            expectResponseCode(response, ResultCdConstants.CODE_999999);
        });
    });
});
