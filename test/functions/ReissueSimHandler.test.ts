import { expect, test, beforeEach } from "@jest/globals";

const enqueueMessage = jest.fn();
jest.mock("../../src/services/servicebus", () => ({
    __esModule: true,
    default: enqueueMessage,
}));

import { addDays, addMinutes, formatDate } from "date-fns";
import { Sequelize } from "sequelize";

import "@/types/string.extension";

import defaultContext from "../testing/DefaultContext";
import DefaultRequest from "../testing/DefaultRequest";
import {
    describeWithDB,
    generateProcessID,
    generateSOID,
} from "../testing/TestHelper";

import { ReissueSimHandler } from "@/functions/ReissueSimHandler";

import AppConfig from "@/appconfig";
import ResultCdConstants from "@/core/constant/ResultCdConstants";
import ResponseHeader from "@/core/dto/ResponseHeader";
import { LinesEntity } from "@/core/entity/LinesEntity";
import LineTenantsEntity from "@/core/entity/LineTenantsEntity";
import ServiceOrdersEntity from "@/core/entity/ServiceOrdersEntity";

import CoreSwimmyApiLog from "@/models/coreSwimmyApiLog";

import { usePsql } from "@/database/psql";
import { disconnectMongo, useMongo } from "@/database/mongo";

describeWithDB("53 SIM再発行 /MVNO/api/V100/Lines/sim", () => {
    let sequelize: Sequelize;

    const testdata = {
        lineOK: "08024043001",
        lineNotExists: "08024043002",
        lineStatusNG: "08024043003",
        lineNotLinked: "08024043004",
        lineWithAbolish: "08024043005",
        lineESIM: "08024043006",
        eid: "89033023000000020240710000000089",
        eidNew: "89033023000000020240710000000186",
    };

    const deviceId = {
        lte: "AD7",
        sms: "AE0",
        voice: "AW9",
    };

    const abolishSo = generateProcessID();

    const request = {
        requestHeader: {
            sequenceNo: "1234567",
            senderSystemId: "0001",
            apiKey: "",
            functionType: "53",
        },
        targetSoId: generateSOID(),
        tenantId: "OPF000",
        targetTenantId: "TST000",
        lineNo: testdata.lineOK,
        sim_no: "SIMTEST2403301010",
        sim_type: "nanoSIM",
        cardTypeId: deviceId.lte,
        csvUnnecessaryFlag: "0",
    };

    const responseHeader = {
        sequenceNo: request.requestHeader.sequenceNo,
        receivedDate: formatDate(new Date(), "yyyy/MM/dd HH:mm:ss"),
        processCode: ResultCdConstants.CODE_000000,
        apiProcessID: generateProcessID(),
    } as ResponseHeader;

    beforeAll(async () => {
        await AppConfig.loadCoreMvnoConfig(defaultContext);
        sequelize = await usePsql();
        await useMongo(defaultContext);
        // createabolish order
        // status = 完了, exec_date >= open_date, exec_date <= today, function type 52
        await ServiceOrdersEntity.create({
            serviceOrderId: abolishSo,
            tenantId: request.targetTenantId,
            lineId: testdata.lineWithAbolish,
            orderStatus: "完了",
            orderType: "回線廃止",
            reserveDate: null,
            execDate: addMinutes(new Date(), -5),
            functionType: "52",
        });
    });

    afterAll(async () => {
        await ServiceOrdersEntity.destroy({
            where: {
                serviceOrderId: abolishSo,
            },
        });
        await sequelize.close();
        await disconnectMongo(defaultContext);
    });

    beforeEach(async () => {
        // reset mock
        (enqueueMessage as jest.Mock).mockClear();

        defaultContext.jsonBody = Object.assign({}, request);
        defaultContext.responseHeader = Object.assign({}, responseHeader);

        // insert line number
        await Promise.all([
            LinesEntity.create({
                lineId: testdata.lineOK,
                tenantId: request.targetTenantId,
                lineStatus: "01",
                simFlag: false,
                deviceTypeId: deviceId.lte,
                modelType: "テストSIM", // temporary
                simType: "microSIM",
                nnumber: "N121152174",
            }),
            LinesEntity.create({
                lineId: testdata.lineStatusNG,
                tenantId: request.targetTenantId,
                lineStatus: "03",
                simFlag: false,
                deviceTypeId: deviceId.lte,
                modelType: "テストSIM",
                simType: "microSIM",
                nnumber: "N121152174",
            }),
            LinesEntity.create({
                lineId: testdata.lineNotLinked,
                tenantId: request.targetTenantId,
                lineStatus: "01",
                simFlag: false,
                deviceTypeId: deviceId.lte,
                modelType: "テストSIM",
                simType: "microSIM",
            }),
            LinesEntity.create({
                lineId: testdata.lineWithAbolish,
                tenantId: request.targetTenantId,
                lineStatus: "01",
                simFlag: false,
                deviceTypeId: deviceId.lte,
                modelType: "テストSIM",
                simType: "microSIM",
                lineActDate: addDays(new Date(), -1), // yesterday
                nnumber: "N121152174",
            }),
            LinesEntity.create({
                lineId: testdata.lineESIM,
                tenantId: request.targetTenantId,
                lineStatus: "01",
                simFlag: false,
                deviceTypeId: deviceId.lte,
                modelType: "テストSIM", // temporary
                simType: "eSIM",
                simNumber: testdata.eid,
                nnumber: "N121152174",
            }),
        ]);
        // register to line tenant
        await Promise.all(
            [
                testdata.lineOK,
                testdata.lineESIM,
                testdata.lineStatusNG,
                testdata.lineWithAbolish,
            ].map(async (lineId) => {
                await LineTenantsEntity.create({
                    lineId,
                    tenantId: request.targetTenantId,
                });
            }),
        );
    });

    afterEach(async () => {
        await LineTenantsEntity.destroy({
            where: {
                lineId: [
                    testdata.lineOK,
                    testdata.lineESIM,
                    testdata.lineStatusNG,
                    testdata.lineWithAbolish,
                ],
            },
        });
        await Promise.all([
            LinesEntity.destroy({
                where: {
                    lineId: Object.values(testdata),
                },
            }),
            ServiceOrdersEntity.destroy({
                where: {
                    serviceOrderId: responseHeader.apiProcessID,
                },
            }),
            CoreSwimmyApiLog.deleteMany({
                requestOrderId: responseHeader.apiProcessID,
            }),
        ]);
    });

    /**
     * check whether SwimmyApiLog exists and Service Bus is called
     */
    const checkCoreSwimmyApiLog = async (
        apiProcessID: string,
        exists: boolean,
    ) => {
        const apiLog = await CoreSwimmyApiLog.findOne({
            requestOrderId: apiProcessID,
        });
        if (exists) {
            expect(apiLog).not.toBeNull();
            expect(enqueueMessage).toBeCalledTimes(1);
        } else {
            expect(apiLog).toBeNull();
            expect(enqueueMessage).not.toBeCalled();
        }
        return apiLog;
    };

    describe("OK (CODE_000000)", () => {
        test("should return CODE_000000 when there is no error", async () => {
            const result = await ReissueSimHandler.Handler(
                DefaultRequest,
                defaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.apiProcessID).toEqual(
                defaultContext.responseHeader.apiProcessID,
            );
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );
            expect(result.jsonBody.responseHeader.sequenceNo).toEqual(
                defaultContext.responseHeader.sequenceNo,
            );
            expect(result.jsonBody.responseHeader.receivedDate).toEqual(
                defaultContext.responseHeader.receivedDate,
            );

            const [serviceOrder, line] = await Promise.all([
                ServiceOrdersEntity.findOne({
                    where: {
                        serviceOrderId: responseHeader.apiProcessID,
                    },
                }),
                LinesEntity.findOne({
                    where: {
                        lineId: request.lineNo,
                    },
                }),
            ]);

            // check new service order should be created
            expect(serviceOrder).not.toBeNull();
            expect(serviceOrder.tenantId).toEqual(request.targetTenantId);
            expect(serviceOrder.lineId).toEqual(request.lineNo);
            expect(serviceOrder.orderStatus).toEqual("完了");
            expect(serviceOrder.orderType).toEqual("回線SIM再発行");
            expect(serviceOrder.reserveDate).toBeNull();

            // line should be updated
            expect(line).not.toBeNull();
            expect(line.simNumber).toEqual(request.sim_no);
            expect(line.simType).toEqual(request.sim_type);
            expect(line.deviceTypeId).toEqual(request.cardTypeId);
            expect(line.modelType).not.toEqual("SIM再発行");

            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, true);
        });

        test("should return CODE_000000 when there is no error (csvUnneccessaryFlag = 0 number)", async () => {
            defaultContext.jsonBody.csvUnnecessaryFlag = 0;
            const result = await ReissueSimHandler.Handler(
                DefaultRequest,
                defaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.apiProcessID).toEqual(
                defaultContext.responseHeader.apiProcessID,
            );
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );
            expect(result.jsonBody.responseHeader.sequenceNo).toEqual(
                defaultContext.responseHeader.sequenceNo,
            );
            expect(result.jsonBody.responseHeader.receivedDate).toEqual(
                defaultContext.responseHeader.receivedDate,
            );

            const [serviceOrder, line] = await Promise.all([
                ServiceOrdersEntity.findOne({
                    where: {
                        serviceOrderId: responseHeader.apiProcessID,
                    },
                }),
                LinesEntity.findOne({
                    where: {
                        lineId: request.lineNo,
                    },
                }),
            ]);

            // check new service order should be created
            expect(serviceOrder).not.toBeNull();
            expect(serviceOrder.tenantId).toEqual(request.targetTenantId);
            expect(serviceOrder.lineId).toEqual(request.lineNo);
            expect(serviceOrder.orderStatus).toEqual("完了");
            expect(serviceOrder.orderType).toEqual("回線SIM再発行");
            expect(serviceOrder.reserveDate).toBeNull();

            // line should be updated
            expect(line).not.toBeNull();
            expect(line.simNumber).toEqual(request.sim_no);
            expect(line.simType).toEqual(request.sim_type);
            expect(line.deviceTypeId).toEqual(request.cardTypeId);
            expect(line.modelType).not.toEqual("SIM再発行");

            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, true);
        });

        test("should return CODE_000000 when there is no error (csvUnneccessaryFlag undefined)", async () => {
            delete defaultContext.jsonBody.csvUnnecessaryFlag;
            const result = await ReissueSimHandler.Handler(
                DefaultRequest,
                defaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.apiProcessID).toEqual(
                defaultContext.responseHeader.apiProcessID,
            );
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );
            expect(result.jsonBody.responseHeader.sequenceNo).toEqual(
                defaultContext.responseHeader.sequenceNo,
            );
            expect(result.jsonBody.responseHeader.receivedDate).toEqual(
                defaultContext.responseHeader.receivedDate,
            );

            const [serviceOrder, line] = await Promise.all([
                ServiceOrdersEntity.findOne({
                    where: {
                        serviceOrderId: responseHeader.apiProcessID,
                    },
                }),
                LinesEntity.findOne({
                    where: {
                        lineId: request.lineNo,
                    },
                }),
            ]);

            // check new service order should be created
            expect(serviceOrder).not.toBeNull();
            expect(serviceOrder.tenantId).toEqual(request.targetTenantId);
            expect(serviceOrder.lineId).toEqual(request.lineNo);
            expect(serviceOrder.orderStatus).toEqual("完了");
            expect(serviceOrder.orderType).toEqual("回線SIM再発行");
            expect(serviceOrder.reserveDate).toBeNull();

            // line should be updated
            expect(line).not.toBeNull();
            expect(line.simNumber).toEqual(request.sim_no);
            expect(line.simType).toEqual(request.sim_type);
            expect(line.deviceTypeId).toEqual(request.cardTypeId);
            expect(line.modelType).not.toEqual("SIM再発行");

            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, true);
        });

        test("should not create CoreSwimmyApiLog if csvUnneccessaryFlag is 1", async () => {
            defaultContext.jsonBody.csvUnnecessaryFlag = "1";
            const result = await ReissueSimHandler.Handler(
                DefaultRequest,
                defaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.apiProcessID).toEqual(
                defaultContext.responseHeader.apiProcessID,
            );
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );
            expect(result.jsonBody.responseHeader.sequenceNo).toEqual(
                defaultContext.responseHeader.sequenceNo,
            );
            expect(result.jsonBody.responseHeader.receivedDate).toEqual(
                defaultContext.responseHeader.receivedDate,
            );

            const [serviceOrder, line] = await Promise.all([
                ServiceOrdersEntity.findOne({
                    where: {
                        serviceOrderId: responseHeader.apiProcessID,
                    },
                }),
                LinesEntity.findOne({
                    where: {
                        lineId: request.lineNo,
                    },
                }),
            ]);

            // check new service order should be created
            expect(serviceOrder).not.toBeNull();
            expect(serviceOrder.tenantId).toEqual(request.targetTenantId);
            expect(serviceOrder.lineId).toEqual(request.lineNo);
            expect(serviceOrder.orderStatus).toEqual("完了");
            expect(serviceOrder.orderType).toEqual("回線SIM再発行");
            expect(serviceOrder.reserveDate).toBeNull();

            // line should be updated
            expect(line).not.toBeNull();
            expect(line.simNumber).toEqual(request.sim_no);
            expect(line.simType).toEqual(request.sim_type);
            expect(line.deviceTypeId).toEqual(request.cardTypeId);
            expect(line.modelType).not.toEqual("SIM再発行");

            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
        });

        test("should not create CoreSwimmyApiLog if csvUnneccessaryFlag is 1 (number)", async () => {
            defaultContext.jsonBody.csvUnnecessaryFlag = 1;
            const result = await ReissueSimHandler.Handler(
                DefaultRequest,
                defaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.apiProcessID).toEqual(
                defaultContext.responseHeader.apiProcessID,
            );
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );
            expect(result.jsonBody.responseHeader.sequenceNo).toEqual(
                defaultContext.responseHeader.sequenceNo,
            );
            expect(result.jsonBody.responseHeader.receivedDate).toEqual(
                defaultContext.responseHeader.receivedDate,
            );

            const [serviceOrder, line] = await Promise.all([
                ServiceOrdersEntity.findOne({
                    where: {
                        serviceOrderId: responseHeader.apiProcessID,
                    },
                }),
                LinesEntity.findOne({
                    where: {
                        lineId: request.lineNo,
                    },
                }),
            ]);

            // check new service order should be created
            expect(serviceOrder).not.toBeNull();
            expect(serviceOrder.tenantId).toEqual(request.targetTenantId);
            expect(serviceOrder.lineId).toEqual(request.lineNo);
            expect(serviceOrder.orderStatus).toEqual("完了");
            expect(serviceOrder.orderType).toEqual("回線SIM再発行");
            expect(serviceOrder.reserveDate).toBeNull();

            // line should be updated
            expect(line).not.toBeNull();
            expect(line.simNumber).toEqual(request.sim_no);
            expect(line.simType).toEqual(request.sim_type);
            expect(line.deviceTypeId).toEqual(request.cardTypeId);
            expect(line.modelType).not.toEqual("SIM再発行");

            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
        });

        describe("eSIM", () => {
            test("should include appPrdtList if new EID is given (eSIM再発行)", async () => {
                const lineNo = testdata.lineESIM;
                const simNo = testdata.eidNew;
                const simType = "eSIM";

                defaultContext.jsonBody.lineNo = lineNo;
                defaultContext.jsonBody.sim_no = simNo;
                defaultContext.jsonBody.sim_type = simType;

                const result = await ReissueSimHandler.Handler(
                    DefaultRequest,
                    defaultContext,
                );
                expect(typeof result).toBe("object");
                expect(result).toHaveProperty("jsonBody");
                expect(result.jsonBody).toHaveProperty("responseHeader");
                expect(result.jsonBody.responseHeader.apiProcessID).toEqual(
                    defaultContext.responseHeader.apiProcessID,
                );
                expect(result.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_000000,
                );
                expect(result.jsonBody.responseHeader.sequenceNo).toEqual(
                    defaultContext.responseHeader.sequenceNo,
                );
                expect(result.jsonBody.responseHeader.receivedDate).toEqual(
                    defaultContext.responseHeader.receivedDate,
                );

                const [serviceOrder, line] = await Promise.all([
                    ServiceOrdersEntity.findOne({
                        where: {
                            serviceOrderId: responseHeader.apiProcessID,
                        },
                    }),
                    LinesEntity.findOne({
                        where: {
                            lineId: lineNo,
                        },
                    }),
                ]);

                // check new service order should be created
                expect(serviceOrder).not.toBeNull();
                expect(serviceOrder.tenantId).toEqual(request.targetTenantId);
                expect(serviceOrder.lineId).toEqual(lineNo);
                expect(serviceOrder.orderStatus).toEqual("完了");
                expect(serviceOrder.orderType).toEqual("回線SIM再発行");
                expect(serviceOrder.reserveDate).toBeNull();

                // line should be updated
                expect(line).not.toBeNull();
                expect(line.simNumber).toEqual(simNo);
                expect(line.simType).toEqual(simType);
                expect(line.deviceTypeId).toEqual(request.cardTypeId);
                expect(line.modelType).not.toEqual("SIM再発行");

                const apiLog = await checkCoreSwimmyApiLog(
                    responseHeader.apiProcessID,
                    true,
                );
                const requestParam = apiLog.requestParam;
                expect(requestParam).toHaveProperty("appRequestInfo");
                expect(requestParam.appRequestInfo).toHaveProperty(
                    "appInfoList",
                );
                expect(requestParam.appRequestInfo.appInfoList).toHaveLength(1);
                expect(
                    requestParam.appRequestInfo.appInfoList[0],
                ).toHaveProperty("appBasic");

                // eSIM download flag not set
                expect(
                    (
                        requestParam.appRequestInfo.appInfoList[0].appBasic
                            ?.appBasicDtlList || []
                    ).find((r) => r.appAttributeCode === "********"),
                ).toBe(undefined);
                // appPrdList should not be empty
                expect(
                    requestParam.appRequestInfo.appInfoList[0],
                ).toHaveProperty("appPrdtList");
                expect(
                    requestParam.appRequestInfo.appInfoList[0].appPrdtList,
                ).toHaveLength(1);
            });

            test("should include eSIM download flag if old EID is given (eSIMプロファイル再ダウンロード)", async () => {
                const lineNo = testdata.lineESIM;
                const simNo = testdata.eid;
                const simType = "eSIM";

                defaultContext.jsonBody.lineNo = lineNo;
                defaultContext.jsonBody.sim_no = simNo;
                defaultContext.jsonBody.sim_type = simType;

                const result = await ReissueSimHandler.Handler(
                    DefaultRequest,
                    defaultContext,
                );
                expect(typeof result).toBe("object");
                expect(result).toHaveProperty("jsonBody");
                expect(result.jsonBody).toHaveProperty("responseHeader");
                expect(result.jsonBody.responseHeader.apiProcessID).toEqual(
                    defaultContext.responseHeader.apiProcessID,
                );
                expect(result.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_000000,
                );
                expect(result.jsonBody.responseHeader.sequenceNo).toEqual(
                    defaultContext.responseHeader.sequenceNo,
                );
                expect(result.jsonBody.responseHeader.receivedDate).toEqual(
                    defaultContext.responseHeader.receivedDate,
                );

                const [serviceOrder, line] = await Promise.all([
                    ServiceOrdersEntity.findOne({
                        where: {
                            serviceOrderId: responseHeader.apiProcessID,
                        },
                    }),
                    LinesEntity.findOne({
                        where: {
                            lineId: lineNo,
                        },
                    }),
                ]);

                // check new service order should be created
                expect(serviceOrder).not.toBeNull();
                expect(serviceOrder.tenantId).toEqual(request.targetTenantId);
                expect(serviceOrder.lineId).toEqual(lineNo);
                expect(serviceOrder.orderStatus).toEqual("完了");
                expect(serviceOrder.orderType).toEqual("回線SIM再発行");
                expect(serviceOrder.reserveDate).toBeNull();

                // line should be updated
                expect(line).not.toBeNull();
                expect(line.simNumber).toEqual(simNo);
                expect(line.simType).toEqual(simType);
                expect(line.deviceTypeId).toEqual(request.cardTypeId);
                expect(line.modelType).not.toEqual("SIM再発行");

                const apiLog = await checkCoreSwimmyApiLog(
                    responseHeader.apiProcessID,
                    true,
                );
                const requestParam = apiLog.requestParam;
                expect(requestParam).toHaveProperty("appRequestInfo");
                expect(requestParam.appRequestInfo).toHaveProperty(
                    "appInfoList",
                );
                expect(requestParam.appRequestInfo.appInfoList).toHaveLength(1);
                expect(
                    requestParam.appRequestInfo.appInfoList[0],
                ).toHaveProperty("appBasic");

                // eSIM download flag should be set
                expect(
                    (
                        requestParam.appRequestInfo.appInfoList[0].appBasic
                            ?.appBasicDtlList || []
                    ).find((r) => r.appAttributeCode === "********"),
                ).toStrictEqual({
                    appAttributeCode: "********",
                    appAttributeValue: "1",
                });
                // appPrdList should be empty
                expect(
                    requestParam.appRequestInfo.appInfoList[0],
                ).toHaveProperty("appPrdtList");
                expect(
                    requestParam.appRequestInfo.appInfoList[0].appPrdtList,
                ).toHaveLength(0);
            });
        });
    });

    describe("NG (CODE_53xxxx)", () => {
        test("should return Code_530101 if tenantId is not Front API Tenant", async () => {
            defaultContext.jsonBody.tenantId = "TSA000";
            const result = await ReissueSimHandler.Handler(
                DefaultRequest,
                defaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_530101,
            );

            // service order should be created with failed status
            const serviceOrder = await ServiceOrdersEntity.findOne({
                where: {
                    serviceOrderId: responseHeader.apiProcessID,
                },
            });
            expect(serviceOrder).not.toBeNull();
            expect(serviceOrder.orderType).toEqual("回線SIM再発行");
            expect(serviceOrder.orderStatus).toEqual("失敗");

            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
        });

        test("should return CODE_530102 of line does not exist", async () => {
            defaultContext.jsonBody.lineNo = testdata.lineNotExists;
            const result = await ReissueSimHandler.Handler(
                DefaultRequest,
                defaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_530102,
            );

            // service order should be created with failed status
            const serviceOrder = await ServiceOrdersEntity.findOne({
                where: {
                    serviceOrderId: responseHeader.apiProcessID,
                },
            });
            expect(serviceOrder).not.toBeNull();
            expect(serviceOrder.orderType).toEqual("回線SIM再発行");
            expect(serviceOrder.orderStatus).toEqual("失敗");

            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
        });

        test("should return CODE_530102 if line is disabled", async () => {
            defaultContext.jsonBody.lineNo = testdata.lineStatusNG;
            const result = await ReissueSimHandler.Handler(
                DefaultRequest,
                defaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_530102,
            );

            // service order should be created with failed status
            const serviceOrder = await ServiceOrdersEntity.findOne({
                where: {
                    serviceOrderId: responseHeader.apiProcessID,
                },
            });
            expect(serviceOrder).not.toBeNull();
            expect(serviceOrder.orderType).toEqual("回線SIM再発行");
            expect(serviceOrder.orderStatus).toEqual("失敗");

            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
        });

        test("should return CODE_530103 if line is not linked to tenant", async () => {
            defaultContext.jsonBody.lineNo = testdata.lineNotLinked;
            const result = await ReissueSimHandler.Handler(
                DefaultRequest,
                defaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_530103,
            );

            // service order should be created with failed status
            const serviceOrder = await ServiceOrdersEntity.findOne({
                where: {
                    serviceOrderId: responseHeader.apiProcessID,
                },
            });
            expect(serviceOrder).not.toBeNull();
            expect(serviceOrder.orderType).toEqual("回線SIM再発行");
            expect(serviceOrder.orderStatus).toEqual("失敗");

            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
        });

        test("should return CODE_530104 if line has abolish order", async () => {
            defaultContext.jsonBody.lineNo = testdata.lineWithAbolish;
            const result = await ReissueSimHandler.Handler(
                DefaultRequest,
                defaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_530104,
            );

            // service order should be created with failed status
            const serviceOrder = await ServiceOrdersEntity.findOne({
                where: {
                    serviceOrderId: responseHeader.apiProcessID,
                },
            });
            expect(serviceOrder).not.toBeNull();
            expect(serviceOrder.orderType).toEqual("回線SIM再発行");
            expect(serviceOrder.orderStatus).toEqual("失敗");

            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
        });

        test("should return CODE_000000 if card info is not found", async () => {
            // NOTE even though cardTypeId is not found, it should return CODE_000000 (line entity is not updated)
            defaultContext.jsonBody.cardTypeId = "ZZZ";
            const result = await ReissueSimHandler.Handler(
                DefaultRequest,
                defaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );

            const [serviceOrder, line] = await Promise.all([
                ServiceOrdersEntity.findOne({
                    where: {
                        serviceOrderId: responseHeader.apiProcessID,
                    },
                }),
                LinesEntity.findOne({
                    where: {
                        lineId: request.lineNo,
                    },
                }),
            ]);
            // service order should be created with OK status
            expect(serviceOrder).not.toBeNull();
            expect(serviceOrder.orderType).toEqual("回線SIM再発行");
            expect(serviceOrder.orderStatus).toEqual("完了");
            // line entity should not be updated
            expect(line).not.toBeNull();
            expect(line.deviceTypeId).toEqual(deviceId.lte);
            expect(line.modelType).toEqual("テストSIM");
            expect(line.simType).toEqual("microSIM");

            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
        });
    });

    test("should return null", async () => {
        const result = ReissueSimHandler.Meta.getOrderType(request);
        expect(result).toBeNull();
    });
});
