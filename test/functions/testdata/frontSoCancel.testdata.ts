// only for getting receiptId, separated from main test file for better readability
export const receiptId = "UK2504170608485";
export const lineDeleteReservationResponse = {
    commonHeaderInfo: {
        requestFromSystemId: "MAP",
        otherSystemSendDatetime: "20170118000000",
        operatorId: "ZZZZZZZZ",
        operatorGroupId: "ZZZZZZZZZZ",
        serviceProcessResultType: "0",
        serviceProcessResultDtlType: null,
    },
    appResponseInfo: {
        processType: "03",
        resultCode: "0001",
        appInfoList: [
            {
                appBasic: {
                    receiptId, // <-- here
                    inputFromType: "01",
                    receiptIdSerialno: 1,
                    orderType: "03",
                    appStatus: "15",
                    appDate: "********",
                    requestDate: "********",
                    salesChannelCode: "79100100",
                    salesChannelName: "金沢OCNサービスセンタ",
                    inputFromReceiptId: null,
                    registName: null,
                    serviceStartDate: "********",
                    firstRequestFromSystemId: "MAP",
                    lastRequestFromSystemId: "MAP",
                    appBasicDtlList: [
                        {
                            appAttributeCode: "M0000001",
                            appAttributeValue: "AP00000********",
                        },
                        {
                            appAttributeCode: "M0000002",
                            appAttributeValue: "008",
                        },
                        {
                            appAttributeCode: "M0000013",
                            appAttributeValue: "N178001009",
                        },
                        {
                            appAttributeCode: "M0000012",
                            appAttributeValue: "09010002010",
                        },
                        {
                            appAttributeCode: "M0000006",
                            appAttributeValue: "**************",
                        },
                        {
                            appAttributeCode: "M0000007",
                            appAttributeValue: "",
                        },
                        {
                            appAttributeCode: "M0000004",
                            appAttributeValue: "**************",
                        },
                        {
                            appAttributeCode: "M0000005",
                            appAttributeValue: "",
                        },
                    ],
                    appTying: {
                        contractReceiptId: null,
                    },
                },
                appContract: null,
                appPrdtList: [
                    {
                        accountId: "AC0********72",
                        accountPrdtId: "**************************",
                        appPrdtStatus: "01",
                        appPrdtCancelCause: null,
                        prdtCode: "T1000001",
                        processType: "03",
                        prdtTypeCode: "0001",
                        price: "0",
                        billingStartDate: "********",
                        billingEndDate: "********",
                        prdtSerialno: 1,
                        parentPrdtSerialno: 0,
                        appPrdtDtlList: [
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "4",
                            },
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "0",
                            },
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "0",
                            },
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "********",
                            },
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "9",
                            },
                        ],
                    },
                    {
                        accountId: "AC0********72",
                        accountPrdtId: "**************************",
                        appPrdtStatus: "01",
                        appPrdtCancelCause: null,
                        prdtCode: "T2000232",
                        processType: "03",
                        prdtTypeCode: "0002",
                        price: null,
                        billingStartDate: "********",
                        billingEndDate: "********",
                        prdtSerialno: 901,
                        parentPrdtSerialno: 1,
                        appPrdtDtlList: [
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "***************",
                            },
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "DN09881188882",
                            },
                            {
                                prdtAttributeCode: "00000008",
                                prdtAttributeValue: "0",
                            },
                            {
                                prdtAttributeCode: "00000011",
                                prdtAttributeValue: "09010002010",
                            },
                            {
                                prdtAttributeCode: "00000015",
                                prdtAttributeValue: "********",
                            },
                            {
                                prdtAttributeCode: "00000016",
                                prdtAttributeValue: "20170415",
                            },
                            {
                                prdtAttributeCode: "00000017",
                                prdtAttributeValue: "1",
                            },
                            {
                                prdtAttributeCode: "00000018",
                                prdtAttributeValue: "送付先宛先太郎",
                            },
                            {
                                prdtAttributeCode: "00000019",
                                prdtAttributeValue: "送付先部課",
                            },
                            {
                                prdtAttributeCode: "00000020",
                                prdtAttributeValue: "担当花子",
                            },
                            {
                                prdtAttributeCode: "00000021",
                                prdtAttributeValue: "0355554444",
                            },
                            {
                                prdtAttributeCode: "00000080",
                                prdtAttributeValue: "2504170996423",
                            },
                            {
                                prdtAttributeCode: "J1000001",
                                prdtAttributeValue: "1",
                            },
                            {
                                prdtAttributeCode: "J1000301",
                                prdtAttributeValue: "2840015",
                            },
                            {
                                prdtAttributeCode: "J1000401",
                                prdtAttributeValue: "12228012001",
                            },
                            {
                                prdtAttributeCode: "J1000501",
                                prdtAttributeValue: "２",
                            },
                            {
                                prdtAttributeCode: "J1000601",
                                prdtAttributeValue: "サウスタワー",
                            },
                        ],
                    },
                    {
                        accountId: "AC0********72",
                        accountPrdtId: "AC0********72T300029700013",
                        appPrdtStatus: "01",
                        appPrdtCancelCause: null,
                        prdtCode: "T3000297",
                        processType: "03",
                        prdtTypeCode: "0003",
                        price: "0",
                        billingStartDate: "********",
                        billingEndDate: "********",
                        prdtSerialno: 902,
                        parentPrdtSerialno: 1,
                        appPrdtDtlList: [
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "2",
                            },
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "2",
                            },
                        ],
                    },
                    {
                        accountId: "AC0********72",
                        accountPrdtId: "AC0********72T400000100014",
                        appPrdtStatus: "01",
                        appPrdtCancelCause: null,
                        prdtCode: "T4000001",
                        processType: "03",
                        prdtTypeCode: "0004",
                        price: "0",
                        billingStartDate: "********",
                        billingEndDate: "********",
                        prdtSerialno: 903,
                        parentPrdtSerialno: 1,
                        appPrdtDtlList: [
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "192.168.168.233",
                            },
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "192.168.168.230",
                            },
                            {
                                prdtAttributeCode: "00000025",
                                prdtAttributeValue: "1",
                            },
                            {
                                prdtAttributeCode: "00000038",
                                prdtAttributeValue: "1",
                            },
                            {
                                prdtAttributeCode: "C0000002",
                                prdtAttributeValue: "ORSI1015",
                            },
                            {
                                prdtAttributeCode: "C0000001",
                                prdtAttributeValue: "orsitestdomain19",
                            },
                            {
                                prdtAttributeCode: "00000026",
                                prdtAttributeValue: "20250416",
                            },
                            {
                                prdtAttributeCode: "00000090",
                                prdtAttributeValue: "202504160900",
                            },
                            {
                                prdtAttributeCode: "00000091",
                                prdtAttributeValue: "************",
                            },
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "0900",
                            },
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "2359",
                            },
                        ],
                    },
                    {
                        accountId: "AC0********72",
                        accountPrdtId: "**************************",
                        appPrdtStatus: "01",
                        appPrdtCancelCause: null,
                        prdtCode: "T6000010",
                        processType: "03",
                        prdtTypeCode: "0006",
                        price: "0",
                        billingStartDate: "********",
                        billingEndDate: "********",
                        prdtSerialno: 904,
                        parentPrdtSerialno: 1,
                        appPrdtDtlList: [
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "********",
                            },
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "00054",
                            },
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "********",
                            },
                        ],
                    },
                    {
                        accountId: "AC0********72",
                        accountPrdtId: "**************************",
                        appPrdtStatus: "01",
                        appPrdtCancelCause: null,
                        prdtCode: "T6000010",
                        processType: "03",
                        prdtTypeCode: "0006",
                        price: "0",
                        billingStartDate: "********",
                        billingEndDate: "********",
                        prdtSerialno: 905,
                        parentPrdtSerialno: 1,
                        appPrdtDtlList: [
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "********",
                            },
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "00055",
                            },
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "********",
                            },
                        ],
                    },
                    {
                        accountId: "AC0********72",
                        accountPrdtId: "**************************",
                        appPrdtStatus: "01",
                        appPrdtCancelCause: null,
                        prdtCode: "T6000010",
                        processType: "03",
                        prdtTypeCode: "0006",
                        price: "0",
                        billingStartDate: "********",
                        billingEndDate: "********",
                        prdtSerialno: 906,
                        parentPrdtSerialno: 1,
                        appPrdtDtlList: [
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "********",
                            },
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "00066",
                            },
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "********",
                            },
                        ],
                    },
                    {
                        accountId: "AC0********72",
                        accountPrdtId: "**************************",
                        appPrdtStatus: "01",
                        appPrdtCancelCause: null,
                        prdtCode: "T6000010",
                        processType: "03",
                        prdtTypeCode: "0006",
                        price: "0",
                        billingStartDate: "********",
                        billingEndDate: "********",
                        prdtSerialno: 907,
                        parentPrdtSerialno: 1,
                        appPrdtDtlList: [
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "********",
                            },
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "00067",
                            },
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "********",
                            },
                        ],
                    },
                    {
                        accountId: "AC0********72",
                        accountPrdtId: "**************************",
                        appPrdtStatus: "01",
                        appPrdtCancelCause: null,
                        prdtCode: "T6000011",
                        processType: "03",
                        prdtTypeCode: "0006",
                        price: "0",
                        billingStartDate: "********",
                        billingEndDate: "********",
                        prdtSerialno: 908,
                        parentPrdtSerialno: 1,
                        appPrdtDtlList: [
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "********",
                            },
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "37",
                            },
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "********",
                            },
                        ],
                    },
                    {
                        accountId: "AC0********72",
                        accountPrdtId: "**************************",
                        appPrdtStatus: "01",
                        appPrdtCancelCause: null,
                        prdtCode: "T6000024",
                        processType: "03",
                        prdtTypeCode: "0006",
                        price: "0",
                        billingStartDate: "********",
                        billingEndDate: "********",
                        prdtSerialno: 909,
                        parentPrdtSerialno: 1,
                        appPrdtDtlList: [
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "********",
                            },
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "32",
                            },
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "********",
                            },
                        ],
                    },
                    {
                        accountId: "AC0********72",
                        accountPrdtId: "**************************",
                        appPrdtStatus: "01",
                        appPrdtCancelCause: null,
                        prdtCode: "T6000042",
                        processType: "03",
                        prdtTypeCode: "0006",
                        price: "0",
                        billingStartDate: "********",
                        billingEndDate: "********",
                        prdtSerialno: 910,
                        parentPrdtSerialno: 1,
                        appPrdtDtlList: [
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "********",
                            },
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "27",
                            },
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "********",
                            },
                        ],
                    },
                    {
                        accountId: "AC0********72",
                        accountPrdtId: "**************************",
                        appPrdtStatus: "01",
                        appPrdtCancelCause: null,
                        prdtCode: "T6000048",
                        processType: "03",
                        prdtTypeCode: "0006",
                        price: "0",
                        billingStartDate: "********",
                        billingEndDate: "********",
                        prdtSerialno: 911,
                        parentPrdtSerialno: 1,
                        appPrdtDtlList: [
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "********",
                            },
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "31",
                            },
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "********",
                            },
                        ],
                    },
                    {
                        accountId: "AC0********72",
                        accountPrdtId: "**************************",
                        appPrdtStatus: "01",
                        appPrdtCancelCause: null,
                        prdtCode: "T6000061",
                        processType: "03",
                        prdtTypeCode: "0006",
                        price: "0",
                        billingStartDate: "********",
                        billingEndDate: "********",
                        prdtSerialno: 912,
                        parentPrdtSerialno: 1,
                        appPrdtDtlList: [
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "********",
                            },
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "28",
                            },
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "********",
                            },
                        ],
                    },
                    {
                        accountId: "AC0********72",
                        accountPrdtId: "AC0********72T600007600030",
                        appPrdtStatus: "01",
                        appPrdtCancelCause: null,
                        prdtCode: "T6000076",
                        processType: "03",
                        prdtTypeCode: "0006",
                        price: "0",
                        billingStartDate: "********",
                        billingEndDate: "********",
                        prdtSerialno: 913,
                        parentPrdtSerialno: 1,
                        appPrdtDtlList: [
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "********",
                            },
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "30",
                            },
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "********",
                            },
                        ],
                    },
                    {
                        accountId: "AC0********72",
                        accountPrdtId: "AC0********72T600007800029",
                        appPrdtStatus: "01",
                        appPrdtCancelCause: null,
                        prdtCode: "T6000078",
                        processType: "03",
                        prdtTypeCode: "0006",
                        price: "0",
                        billingStartDate: "********",
                        billingEndDate: "********",
                        prdtSerialno: 914,
                        parentPrdtSerialno: 1,
                        appPrdtDtlList: [
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "********",
                            },
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "29",
                            },
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "********",
                            },
                        ],
                    },
                    {
                        accountId: "AC0********72",
                        accountPrdtId: "AC0********72T700000100016",
                        appPrdtStatus: "01",
                        appPrdtCancelCause: null,
                        prdtCode: "T7000001",
                        processType: "03",
                        prdtTypeCode: "0007",
                        price: "0",
                        billingStartDate: "********",
                        billingEndDate: "********",
                        prdtSerialno: 915,
                        parentPrdtSerialno: 1,
                        appPrdtDtlList: [
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "********",
                            },
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "代理店A",
                            },
                            {
                                prdtAttributeCode: "00000059",
                                prdtAttributeValue: "NTTコム",
                            },
                            {
                                prdtAttributeCode: "00000060",
                                prdtAttributeValue: "担当次郎",
                            },
                            {
                                prdtAttributeCode: "00000061",
                                prdtAttributeValue: "0356564444",
                            },
                            {
                                prdtAttributeCode: "00000062",
                                prdtAttributeValue: "0399998888",
                            },
                            {
                                prdtAttributeCode: "00000063",
                                prdtAttributeValue: "<EMAIL>",
                            },
                            {
                                prdtAttributeCode: "00000064",
                                prdtAttributeValue: "担当三郎",
                            },
                            {
                                prdtAttributeCode: "00000065",
                                prdtAttributeValue: "0399998888",
                            },
                            {
                                prdtAttributeCode: "00000055",
                                prdtAttributeValue: "99991234123",
                            },
                            {
                                prdtAttributeCode: "00000054",
                                prdtAttributeValue: "12",
                            },
                            {
                                prdtAttributeCode: "00000066",
                                prdtAttributeValue: "0377774444",
                            },
                            {
                                prdtAttributeCode: "00000067",
                                prdtAttributeValue: "<EMAIL>",
                            },
                            {
                                prdtAttributeCode: "00000081",
                                prdtAttributeValue: "卸試験０９",
                            },
                            {
                                prdtAttributeCode: "00000082",
                                prdtAttributeValue: "ｵﾛｼｼｹﾝｾﾞﾛｷｭｳ",
                            },
                            {
                                prdtAttributeCode: "00000083",
                                prdtAttributeValue: "連動試験担当",
                            },
                            {
                                prdtAttributeCode: "00000084",
                                prdtAttributeValue: "事務担当太郎",
                            },
                            {
                                prdtAttributeCode: "00000085",
                                prdtAttributeValue: "03232356565",
                            },
                            {
                                prdtAttributeCode: "00000086",
                                prdtAttributeValue: "<EMAIL>",
                            },
                            {
                                prdtAttributeCode: "00000087",
                                prdtAttributeValue: "事業所利用",
                            },
                            {
                                prdtAttributeCode: "J4000001",
                                prdtAttributeValue: "1",
                            },
                            {
                                prdtAttributeCode: "J4000301",
                                prdtAttributeValue: "1420063",
                            },
                            {
                                prdtAttributeCode: "J4000401",
                                prdtAttributeValue: "13109001001",
                            },
                            {
                                prdtAttributeCode: "J4000501",
                                prdtAttributeValue: "２",
                            },
                            {
                                prdtAttributeCode: "J4000601",
                                prdtAttributeValue: "サウスタワー",
                            },
                        ],
                    },
                    {
                        accountId: "AC0********72",
                        accountPrdtId: "AC0********72TB00000100017",
                        appPrdtStatus: "01",
                        appPrdtCancelCause: null,
                        prdtCode: "TB000001",
                        processType: "03",
                        prdtTypeCode: "0011",
                        price: "0",
                        billingStartDate: "********",
                        billingEndDate: "********",
                        prdtSerialno: 916,
                        parentPrdtSerialno: 1,
                        appPrdtDtlList: [],
                    },
                    {
                        accountId: "AC0********72",
                        accountPrdtId: "AC0********72T900000100180",
                        appPrdtStatus: "01",
                        appPrdtCancelCause: null,
                        prdtCode: "T9000001",
                        processType: "01",
                        prdtTypeCode: "0009",
                        price: null,
                        billingStartDate: null,
                        billingEndDate: null,
                        prdtSerialno: 917,
                        parentPrdtSerialno: 1,
                        appPrdtDtlList: [
                            {
                                prdtAttributeCode: "S1000001",
                                prdtAttributeValue: "1",
                            },
                        ],
                    },
                    {
                        accountId: "AC0********72",
                        accountPrdtId: "AC0********72T900000200181",
                        appPrdtStatus: "01",
                        appPrdtCancelCause: null,
                        prdtCode: "T9000002",
                        processType: "01",
                        prdtTypeCode: "0009",
                        price: null,
                        billingStartDate: null,
                        billingEndDate: null,
                        prdtSerialno: 918,
                        parentPrdtSerialno: 1,
                        appPrdtDtlList: [
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "3",
                            },
                            {
                                prdtAttributeCode: "S1000001",
                                prdtAttributeValue: "1",
                            },
                        ],
                    },
                    {
                        accountId: "AC0********72",
                        accountPrdtId: "**************************",
                        appPrdtStatus: "01",
                        appPrdtCancelCause: null,
                        prdtCode: "T9000003",
                        processType: "01",
                        prdtTypeCode: "0009",
                        price: null,
                        billingStartDate: null,
                        billingEndDate: null,
                        prdtSerialno: 919,
                        parentPrdtSerialno: 1,
                        appPrdtDtlList: [
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "0",
                            },
                            {
                                prdtAttributeCode: "********",
                                prdtAttributeValue: "0",
                            },
                            {
                                prdtAttributeCode: "S1000003",
                                prdtAttributeValue: "0",
                            },
                            {
                                prdtAttributeCode: "S1000004",
                                prdtAttributeValue: "0",
                            },
                        ],
                    },
                ],
                appPrdtLinkList: [
                    {
                        parentAccountPrdtId: "**************************",
                        childAccountPrdtId: "AC0********72T200023200021",
                    },
                    {
                        parentAccountPrdtId: "**************************",
                        childAccountPrdtId: "AC0********72TB00000100022",
                    },
                    {
                        parentAccountPrdtId: "**************************",
                        childAccountPrdtId: "AC0********72T400000100014",
                    },
                    {
                        parentAccountPrdtId: "**************************",
                        childAccountPrdtId: "AC0********72T900000100180",
                    },
                    {
                        parentAccountPrdtId: "**************************",
                        childAccountPrdtId: "AC0********72T900000200181",
                    },
                    {
                        parentAccountPrdtId: "**************************",
                        childAccountPrdtId: "**************************",
                    },
                ],
                appErrorInfo: null,
                checkErrorInfo: {
                    errorInfoList: [
                        {
                            checkId: "A20024",
                            errorLevel: "1",
                            messageId: "WA2000019",
                            message:
                                "随時請求オプションの適用年月が、廃止年月と同月で登録されています。",
                            fieldInfoList: [],
                            embedInfoList: [],
                        },
                    ],
                },
            },
        ],
    },
};
