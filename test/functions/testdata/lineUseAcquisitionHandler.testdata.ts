import LineGroupsEntity from "@/core/entity/LineGroupsEntity";
import LineTrafficsEntity from "@/core/entity/LineTrafficsEntity";
import PlansEntity from "@/core/entity/PlansEntity";

/** 回線運用API testdata helper */
export default abstract class LineUseAcquisitionHandlerTestData {
    public static async setServicePattern(
        planId: string,
        servicePattern: number,
    ) {
        await PlansEntity.update(
            {
                refServicePattern: servicePattern,
            },
            {
                where: { planId },
            },
        );
    }

    /**
     * @param lineNo
     * @param year
     * @param month 1-based
     * @param traffic
     * @param couponOff
     */
    public static async createTraffic(
        lineNo: string,
        year: number,
        month: number,
        traffic: number,
        couponOff?: number,
    ): Promise<void> {
        let trafficCouponOff: string;
        if (couponOff) {
            trafficCouponOff = couponOff.toString();
        }
        await LineTrafficsEntity.upsert({
            lineId: lineNo,
            year,
            month,
            traffic: BigInt(traffic),
            trafficCouponOff,
        });
    }

    public static async removeTraffic(lineNo: string[]) {
        await LineTrafficsEntity.destroy({
            where: { lineId: lineNo },
        });
    }

    public static async createLineGroup(
        groupId: string,
        planId: string,
        tenantId: string,
    ) {
        await LineGroupsEntity.create({
            groupId,
            planId: +planId,
            tenantId,
            status: 1,
        });
    }

    public static async deleteLineGroup(
        groupId: string,
        planId: string,
        tenantId: string,
    ) {
        await LineGroupsEntity.destroy({
            where: {
                groupId,
                planId: +planId,
                tenantId,
            },
        });
    }
}
