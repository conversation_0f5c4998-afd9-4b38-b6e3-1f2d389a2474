import { expect, test, beforeEach } from "@jest/globals";

const enqueueMessage = jest.fn();
jest.mock("../../src/services/servicebus", () => ({
    __esModule: true,
    default: enqueueMessage,
}));

import config from "config";
import { add, formatDate } from "date-fns";
import { Sequelize } from "sequelize";

import defaultContext from "../testing/DefaultContext";
import DefaultRequest from "../testing/DefaultRequest";
import {
    describeWithDB,
    generateProcessID,
    generateSOID,
} from "../testing/TestHelper";

import { LinesDelHandler } from "@/functions/LinesDelHandler";
import Constants from "@/core/constant/Constants";
import ResultCdConstants from "@/core/constant/ResultCdConstants";
import LineDeleteInputDto from "@/core/dto/LineDeleteInputDto";
import ResponseHeader from "@/core/dto/ResponseHeader";
import { LinesEntity } from "@/core/entity/LinesEntity";
import LineTenantsEntity from "@/core/entity/LineTenantsEntity";
import ServiceOrdersEntity from "@/core/entity/ServiceOrdersEntity";

import CoreSwimmyApiLog from "@/models/coreSwimmyApiLog";

import { usePsql } from "@/database/psql";
import MvnoUtil from "@/core/common/MvnoUtil";
import { disconnectMongo, useMongo } from "@/database/mongo";
import AppConfig from "@/appconfig";
import { getConfigAsNumber } from "@/helpers/configHelper";
import { TenantNnumbersEntity } from "@/core/entity/TenantNnumbersEntity";

describeWithDB("52 回線廃止 /MVNO/api/V100/Lines/del", () => {
    let sequelize: Sequelize;

    const reserve_date = add(new Date(), { days: 7 });
    const testdata = {
        lineOK: "08024042410",
        hankuroLine: "08024042411",
        disabledLine: "08024042412",
        notExistLine: "08024042413",
        lineNG: "08024042414", // no nnumber
    };
    const request = {
        requestHeader: {
            sequenceNo: "1234567",
            senderSystemId: "0001",
            apiKey: "",
            functionType: "52",
        },
        targetSoId: generateSOID(),
        tenantId: "OPF000",
        targetTenantId: "TST000",
        lineNo: testdata.lineOK,
        mnpOutFlag: "0",
        csvUnnecessaryFlag: "0",
        reserve_date: formatDate(reserve_date, "yyyy/MM/dd"),
    } as LineDeleteInputDto;
    const responseHeader = {
        sequenceNo: request.requestHeader.sequenceNo,
        receivedDate: formatDate(new Date(), "yyyy/MM/dd HH:mm:ss"),
        processCode: ResultCdConstants.CODE_000000,
        apiProcessID: generateProcessID(),
    } as ResponseHeader;

    beforeAll(async () => {
        await AppConfig.loadCoreMvnoConfig(defaultContext);
        sequelize = await usePsql();
        await useMongo(defaultContext);
        const nBan = await TenantNnumbersEntity.findOne({
            where: { tenantId: request.targetTenantId },
        });
        // insert a line number
        await Promise.all([
            LinesEntity.create({
                lineId: testdata.lineOK,
                tenantId: request.targetTenantId,
                lineStatus: "01",
                simFlag: false,
                nnumber: nBan.nnumber,
            }),
            LinesEntity.create({
                lineId: testdata.disabledLine,
                tenantId: request.targetTenantId,
                lineStatus: "03",
                simFlag: false,
                nnumber: nBan.nnumber,
            }),
            LinesEntity.create({
                lineId: testdata.hankuroLine,
                tenantId: request.targetTenantId,
                lineStatus: "01",
                simFlag: true,
                nnumber: nBan.nnumber,
            }),
            LinesEntity.create({
                lineId: testdata.lineNG,
                tenantId: request.targetTenantId,
                lineStatus: "01",
                simFlag: false,
            }),
        ]);
        // register to line tenant
        await Promise.all(
            [testdata.lineOK, testdata.disabledLine, testdata.hankuroLine, testdata.lineNG].map(
                async (lineId) => {
                    await LineTenantsEntity.create({
                        lineId,
                        tenantId: request.targetTenantId,
                    });
                },
            ),
        );
    });

    afterAll(async () => {
        // delete added line numbers
        await LineTenantsEntity.destroy({
            where: {
                lineId: [
                    testdata.lineOK,
                    testdata.disabledLine,
                    testdata.hankuroLine,
                    testdata.lineNG,
                ],
            },
        });
        await LinesEntity.destroy({
            where: {
                lineId: [
                    testdata.lineOK,
                    testdata.disabledLine,
                    testdata.hankuroLine,
                    testdata.lineNG,
                ],
            },
        });
        await sequelize.close();
        await disconnectMongo(defaultContext);
    });

    beforeEach(() => {
        // reset mock
        (enqueueMessage as jest.Mock).mockClear();

        // reset context to default
        defaultContext.jsonBody = Object.assign({}, request);
        defaultContext.responseHeader = Object.assign({}, responseHeader);
    });

    afterEach(async () => {
        // delete service order (if created)
        await Promise.all([
            ServiceOrdersEntity.destroy({
                where: {
                    serviceOrderId: responseHeader.apiProcessID,
                },
            }),
            CoreSwimmyApiLog.deleteMany({
                requestOrderId: responseHeader.apiProcessID,
            }),
        ]);
    });

    /**
     * check whether SwimmyApiLog exists and Service Bus is called
     */
    const checkCoreSwimmyApiLog = async (
        apiProcessID: string,
        exists: boolean,
    ) => {
        const apiLog = await CoreSwimmyApiLog.findOne({
            requestOrderId: apiProcessID,
        });
        if (exists) {
            expect(apiLog).not.toBeNull();
            expect(enqueueMessage).toBeCalledTimes(1);
        } else {
            expect(apiLog).toBeNull();
            expect(enqueueMessage).not.toBeCalled();
        }
    };

    describe("OK (CODE_000000) [即時オーダ]", () => {
        beforeEach(() => {
            delete defaultContext.jsonBody.reserve_date;
        });

        test("should return CODE_000000 when there is no error", async () => {
            const result = await LinesDelHandler.Handler(
                DefaultRequest,
                defaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.apiProcessID).toBe(
                defaultContext.responseHeader.apiProcessID,
            );
            expect(result.jsonBody.responseHeader.processCode).toBe(
                defaultContext.responseHeader.processCode,
            );
            expect(result.jsonBody.responseHeader.sequenceNo).toBe(
                defaultContext.responseHeader.sequenceNo,
            );
            expect(result.jsonBody.responseHeader.receivedDate).toBe(
                defaultContext.responseHeader.receivedDate,
            );

            // check should be inserted to DB
            const serviceOrder = await ServiceOrdersEntity.findOne({
                where: {
                    serviceOrderId: result.jsonBody.responseHeader.apiProcessID,
                },
            });
            expect(serviceOrder).not.toBeNull();
            expect(serviceOrder.tenantId).toEqual(request.targetTenantId);
            expect(serviceOrder.lineId).toEqual(request.lineNo);
            expect(serviceOrder.orderStatus).toEqual("完了");
            expect(serviceOrder.orderType).toEqual("回線廃止");
            expect(serviceOrder.reserveDate).toBeNull();

            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, true);
        });

        test("should return CODE_000000 when there is no error (csvUnncessaryFlag = 0 (number))", async () => {
            defaultContext.jsonBody.csvUnnecessaryFlag = 0;
            const result = await LinesDelHandler.Handler(
                DefaultRequest,
                defaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.apiProcessID).toBe(
                defaultContext.responseHeader.apiProcessID,
            );
            expect(result.jsonBody.responseHeader.processCode).toBe(
                defaultContext.responseHeader.processCode,
            );
            expect(result.jsonBody.responseHeader.sequenceNo).toBe(
                defaultContext.responseHeader.sequenceNo,
            );
            expect(result.jsonBody.responseHeader.receivedDate).toBe(
                defaultContext.responseHeader.receivedDate,
            );

            // check should be inserted to DB
            const serviceOrder = await ServiceOrdersEntity.findOne({
                where: {
                    serviceOrderId: result.jsonBody.responseHeader.apiProcessID,
                },
            });
            expect(serviceOrder).not.toBeNull();
            expect(serviceOrder.tenantId).toEqual(request.targetTenantId);
            expect(serviceOrder.lineId).toEqual(request.lineNo);
            expect(serviceOrder.orderStatus).toEqual("完了");
            expect(serviceOrder.orderType).toEqual("回線廃止");
            expect(serviceOrder.reserveDate).toBeNull();

            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, true);
        });

        test("should not create CoreSwimmyApiLog if csvUnneccessaryFlag is 1", async () => {
            defaultContext.jsonBody.csvUnnecessaryFlag = "1";
            const result = await LinesDelHandler.Handler(
                DefaultRequest,
                defaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.apiProcessID).toBe(
                defaultContext.responseHeader.apiProcessID,
            );
            expect(result.jsonBody.responseHeader.processCode).toBe(
                defaultContext.responseHeader.processCode,
            );
            expect(result.jsonBody.responseHeader.sequenceNo).toBe(
                defaultContext.responseHeader.sequenceNo,
            );
            expect(result.jsonBody.responseHeader.receivedDate).toBe(
                defaultContext.responseHeader.receivedDate,
            );

            // check should be inserted to DB
            const serviceOrder = await ServiceOrdersEntity.findOne({
                where: {
                    serviceOrderId: result.jsonBody.responseHeader.apiProcessID,
                },
            });
            expect(serviceOrder).not.toBeNull();
            expect(serviceOrder.tenantId).toEqual(request.targetTenantId);
            expect(serviceOrder.lineId).toEqual(request.lineNo);
            expect(serviceOrder.orderStatus).toEqual("完了");
            expect(serviceOrder.orderType).toEqual("回線廃止");
            expect(serviceOrder.reserveDate).toBeNull();

            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
        });

        test("should not create CoreSwimmyApiLog if csvUnneccessaryFlag is 1 (number)", async () => {
            defaultContext.jsonBody.csvUnnecessaryFlag = 1;
            const result = await LinesDelHandler.Handler(
                DefaultRequest,
                defaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.apiProcessID).toBe(
                defaultContext.responseHeader.apiProcessID,
            );
            expect(result.jsonBody.responseHeader.processCode).toBe(
                defaultContext.responseHeader.processCode,
            );
            expect(result.jsonBody.responseHeader.sequenceNo).toBe(
                defaultContext.responseHeader.sequenceNo,
            );
            expect(result.jsonBody.responseHeader.receivedDate).toBe(
                defaultContext.responseHeader.receivedDate,
            );

            // check should be inserted to DB
            const serviceOrder = await ServiceOrdersEntity.findOne({
                where: {
                    serviceOrderId: result.jsonBody.responseHeader.apiProcessID,
                },
            });
            expect(serviceOrder).not.toBeNull();
            expect(serviceOrder.tenantId).toEqual(request.targetTenantId);
            expect(serviceOrder.lineId).toEqual(request.lineNo);
            expect(serviceOrder.orderStatus).toEqual("完了");
            expect(serviceOrder.orderType).toEqual("回線廃止");
            expect(serviceOrder.reserveDate).toBeNull();

            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
        });

        test("should return CODE_000000 and not create swimmy api log if nNo not found for lineno", async () => {
            defaultContext.jsonBody.lineNo = testdata.lineNG;
            defaultContext.jsonBody.csvUnnecessaryFlag = "0";
            const result = await LinesDelHandler.Handler(
                DefaultRequest,
                defaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.apiProcessID).toBe(
                defaultContext.responseHeader.apiProcessID,
            );
            expect(result.jsonBody.responseHeader.processCode).toBe(
                defaultContext.responseHeader.processCode,
            );
            expect(result.jsonBody.responseHeader.sequenceNo).toBe(
                defaultContext.responseHeader.sequenceNo,
            );
            expect(result.jsonBody.responseHeader.receivedDate).toBe(
                defaultContext.responseHeader.receivedDate,
            );

            // check should be inserted to DB
            const serviceOrder = await ServiceOrdersEntity.findOne({
                where: {
                    serviceOrderId: result.jsonBody.responseHeader.apiProcessID,
                },
            });
            expect(serviceOrder).not.toBeNull();
            expect(serviceOrder.tenantId).toEqual(request.targetTenantId);
            expect(serviceOrder.lineId).toEqual(testdata.lineNG);
            expect(serviceOrder.orderStatus).toEqual("完了");
            expect(serviceOrder.orderType).toEqual("回線廃止");
            expect(serviceOrder.reserveDate).toBeNull();
            
            expect(defaultContext.error).toBeCalledTimes(1);
            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false); // not created
        });
    });

    describe("OK (CODE_000000) [予約前オーダ]", () => {
        test("should return CODE_000000 when there is no error", async () => {
            const result = await LinesDelHandler.Handler(
                DefaultRequest,
                defaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.apiProcessID).toBe(
                defaultContext.responseHeader.apiProcessID,
            );
            expect(result.jsonBody.responseHeader.processCode).toBe(
                defaultContext.responseHeader.processCode,
            );
            expect(result.jsonBody.responseHeader.sequenceNo).toBe(
                defaultContext.responseHeader.sequenceNo,
            );
            expect(result.jsonBody.responseHeader.receivedDate).toBe(
                defaultContext.responseHeader.receivedDate,
            );

            // check should be inserted to DB
            const serviceOrder = await ServiceOrdersEntity.findOne({
                where: {
                    serviceOrderId: result.jsonBody.responseHeader.apiProcessID,
                },
            });
            expect(serviceOrder).not.toBeNull();
            expect(serviceOrder.tenantId).toEqual(request.targetTenantId);
            expect(serviceOrder.lineId).toEqual(request.lineNo);
            expect(serviceOrder.orderStatus).toEqual("予約中");
            expect(serviceOrder.orderType).toEqual("回線廃止");
            expect(serviceOrder.reserveDate).not.toBeNull();

            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, true);
        });

        test("should return CODE_000000 when there is no error (csvUnncessaryFlag = 0 (number))", async () => {
            defaultContext.jsonBody.csvUnnecessaryFlag = 0;
            const result = await LinesDelHandler.Handler(
                DefaultRequest,
                defaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.apiProcessID).toBe(
                defaultContext.responseHeader.apiProcessID,
            );
            expect(result.jsonBody.responseHeader.processCode).toBe(
                defaultContext.responseHeader.processCode,
            );
            expect(result.jsonBody.responseHeader.sequenceNo).toBe(
                defaultContext.responseHeader.sequenceNo,
            );
            expect(result.jsonBody.responseHeader.receivedDate).toBe(
                defaultContext.responseHeader.receivedDate,
            );

            // check should be inserted to DB
            const serviceOrder = await ServiceOrdersEntity.findOne({
                where: {
                    serviceOrderId: result.jsonBody.responseHeader.apiProcessID,
                },
            });
            expect(serviceOrder).not.toBeNull();
            expect(serviceOrder.tenantId).toEqual(request.targetTenantId);
            expect(serviceOrder.lineId).toEqual(request.lineNo);
            expect(serviceOrder.orderStatus).toEqual("予約中");
            expect(serviceOrder.orderType).toEqual("回線廃止");
            expect(serviceOrder.reserveDate).not.toBeNull();

            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, true);
        });

        test("should not create CoreSwimmyApiLog if csvUnneccessaryFlag is 1", async () => {
            defaultContext.jsonBody.csvUnnecessaryFlag = "1";
            const result = await LinesDelHandler.Handler(
                DefaultRequest,
                defaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.apiProcessID).toBe(
                defaultContext.responseHeader.apiProcessID,
            );
            expect(result.jsonBody.responseHeader.processCode).toBe(
                defaultContext.responseHeader.processCode,
            );
            expect(result.jsonBody.responseHeader.sequenceNo).toBe(
                defaultContext.responseHeader.sequenceNo,
            );
            expect(result.jsonBody.responseHeader.receivedDate).toBe(
                defaultContext.responseHeader.receivedDate,
            );

            // check should be inserted to DB
            const serviceOrder = await ServiceOrdersEntity.findOne({
                where: {
                    serviceOrderId: result.jsonBody.responseHeader.apiProcessID,
                },
            });
            expect(serviceOrder).not.toBeNull();
            expect(serviceOrder.tenantId).toEqual(request.targetTenantId);
            expect(serviceOrder.lineId).toEqual(request.lineNo);
            expect(serviceOrder.orderStatus).toEqual("予約中");
            expect(serviceOrder.orderType).toEqual("回線廃止");
            expect(serviceOrder.reserveDate).not.toBeNull();

            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
        });

        test("should not create CoreSwimmyApiLog if csvUnneccessaryFlag is 1 (number)", async () => {
            defaultContext.jsonBody.csvUnnecessaryFlag = 1;
            const result = await LinesDelHandler.Handler(
                DefaultRequest,
                defaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.apiProcessID).toBe(
                defaultContext.responseHeader.apiProcessID,
            );
            expect(result.jsonBody.responseHeader.processCode).toBe(
                defaultContext.responseHeader.processCode,
            );
            expect(result.jsonBody.responseHeader.sequenceNo).toBe(
                defaultContext.responseHeader.sequenceNo,
            );
            expect(result.jsonBody.responseHeader.receivedDate).toBe(
                defaultContext.responseHeader.receivedDate,
            );

            // check should be inserted to DB
            const serviceOrder = await ServiceOrdersEntity.findOne({
                where: {
                    serviceOrderId: result.jsonBody.responseHeader.apiProcessID,
                },
            });
            expect(serviceOrder).not.toBeNull();
            expect(serviceOrder.tenantId).toEqual(request.targetTenantId);
            expect(serviceOrder.lineId).toEqual(request.lineNo);
            expect(serviceOrder.orderStatus).toEqual("予約中");
            expect(serviceOrder.orderType).toEqual("回線廃止");
            expect(serviceOrder.reserveDate).not.toBeNull();

            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
        });
    });

    describe("OK (CODE_000000) [予約実行オーダ]", () => {
        let processID = "";
        beforeEach(async () => {
            // insert a 予約前オーダ
            const result = await LinesDelHandler.Handler(
                DefaultRequest,
                defaultContext,
            );
            processID = result.jsonBody.responseHeader.apiProcessID;

            // check should be inserted to DB
            const serviceOrder = await ServiceOrdersEntity.findOne({
                where: {
                    serviceOrderId: result.jsonBody.responseHeader.apiProcessID,
                },
            });
            expect(serviceOrder).not.toBeNull();
            expect(serviceOrder.orderStatus).toEqual("予約中");
            expect(serviceOrder.orderType).toEqual("回線廃止");
            expect(serviceOrder.reserveDate).not.toBeNull();

            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, true); // should be created

            defaultContext.jsonBody.reserve_flag = true;
            defaultContext.jsonBody.reserve_soId = processID;
        });

        test("should return CODE_000000 when there is no error", async () => {
            const result = await LinesDelHandler.Handler(
                DefaultRequest,
                defaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.apiProcessID).toBe(
                defaultContext.responseHeader.apiProcessID,
            );
            expect(result.jsonBody.responseHeader.processCode).toBe(
                defaultContext.responseHeader.processCode,
            );
            expect(result.jsonBody.responseHeader.sequenceNo).toBe(
                defaultContext.responseHeader.sequenceNo,
            );
            expect(result.jsonBody.responseHeader.receivedDate).toBe(
                defaultContext.responseHeader.receivedDate,
            );

            // check should be inserted to DB
            const serviceOrder = await ServiceOrdersEntity.findOne({
                where: {
                    serviceOrderId: result.jsonBody.responseHeader.apiProcessID,
                },
            });
            expect(serviceOrder).not.toBeNull();
            expect(serviceOrder.tenantId).toEqual(request.targetTenantId);
            expect(serviceOrder.lineId).toEqual(request.lineNo);
            expect(serviceOrder.orderStatus).toEqual("完了");
            expect(serviceOrder.orderType).toEqual("回線廃止");
            expect(serviceOrder.reserveDate).not.toBeNull();

            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, true); // called once during 予約前
        });
    });

    describe("OK (CODE_000000) [予約実行オーダ, csvUnnecessaryFlag=1]", () => {
        let processID = "";
        beforeEach(async () => {
            // insert a 予約前オーダ
            defaultContext.jsonBody.csvUnnecessaryFlag = "1";
            const result = await LinesDelHandler.Handler(
                DefaultRequest,
                defaultContext,
            );
            processID = result.jsonBody.responseHeader.apiProcessID;

            // check should be inserted to DB
            const serviceOrder = await ServiceOrdersEntity.findOne({
                where: {
                    serviceOrderId: result.jsonBody.responseHeader.apiProcessID,
                },
            });
            expect(serviceOrder).not.toBeNull();
            expect(serviceOrder.orderStatus).toEqual("予約中");
            expect(serviceOrder.orderType).toEqual("回線廃止");
            expect(serviceOrder.reserveDate).not.toBeNull();

            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false); // should not be created

            defaultContext.jsonBody.reserve_flag = true;
            defaultContext.jsonBody.reserve_soId = processID;
        });

        test("should return CODE_000000 when there is no error", async () => {
            const result = await LinesDelHandler.Handler(
                DefaultRequest,
                defaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.apiProcessID).toBe(
                defaultContext.responseHeader.apiProcessID,
            );
            expect(result.jsonBody.responseHeader.processCode).toBe(
                defaultContext.responseHeader.processCode,
            );
            expect(result.jsonBody.responseHeader.sequenceNo).toBe(
                defaultContext.responseHeader.sequenceNo,
            );
            expect(result.jsonBody.responseHeader.receivedDate).toBe(
                defaultContext.responseHeader.receivedDate,
            );

            // check should be inserted to DB
            const serviceOrder = await ServiceOrdersEntity.findOne({
                where: {
                    serviceOrderId: result.jsonBody.responseHeader.apiProcessID,
                },
            });
            expect(serviceOrder).not.toBeNull();
            expect(serviceOrder.tenantId).toEqual(request.targetTenantId);
            expect(serviceOrder.lineId).toEqual(request.lineNo);
            expect(serviceOrder.orderStatus).toEqual("完了");
            expect(serviceOrder.orderType).toEqual("回線廃止");
            expect(serviceOrder.reserveDate).not.toBeNull();

            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false); // should not be created
        });
    });

    describe("NG (CODE_52xxxx)", () => {
        test("should return CODE_520112 if type order type is not valid", async () => {
            defaultContext.jsonBody.reserve_date = null;
            defaultContext.jsonBody.reserve_flag = true;
            const result = await LinesDelHandler.Handler(
                DefaultRequest,
                defaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_520112,
            );

            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
        });

        test("should return CODE_520107 if reserve date is in past", async () => {
            defaultContext.jsonBody.reserve_date = formatDate(
                add(new Date(), { days: -1 }),
                "yyyy/MM/dd",
            );
            const result = await LinesDelHandler.Handler(
                DefaultRequest,
                defaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_520107,
            );

            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
        });

        // this case is configuration format error so no need to test here
        test.skip("should return CODE_520108 if reservation unit format is wrong", () => {});

        // time part of reserve_date will be added inside the function (hardcoded)
        test.skip("should return CODE_520109 if reserve_date minute is odd", () => {});

        // this case is configuration format error so no need to test here
        test.skip("should return CODE_520110 if reservation limit day format is invalid", () => {});

        test("should return CODE_520111 if reserve_date is over configuration limit", async () => {
            const limitdays = getConfigAsNumber("ReservationsLimitDays");
            defaultContext.jsonBody.reserve_date = formatDate(
                add(new Date(), { days: limitdays + 1 }),
                "yyyy/MM/dd",
            );
            const result = await LinesDelHandler.Handler(
                DefaultRequest,
                defaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_520111,
            );

            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
        });

        test("should return CODE_520114 if mnpOutFlag set to '1'", async () => {
            defaultContext.jsonBody.mnpOutFlag = "1";
            const result = await LinesDelHandler.Handler(
                DefaultRequest,
                defaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_520114,
            );

            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
        });

        test("should return CODE_520101 if request made by other than front API tenant", async () => {
            // asume API key validation is done in middleware
            defaultContext.jsonBody.tenantId = "TST000";
            const result = await LinesDelHandler.Handler(
                DefaultRequest,
                defaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_520101,
            );

            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
        });

        test("should return CODE_520102 if lineNo is not found", async () => {
            defaultContext.jsonBody.lineNo = testdata.notExistLine;
            const result = await LinesDelHandler.Handler(
                DefaultRequest,
                defaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_520102,
            );

            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
        });

        test("should return CODE_520102 if lineNo status is disabled", async () => {
            defaultContext.jsonBody.lineNo = testdata.disabledLine;
            const result = await LinesDelHandler.Handler(
                DefaultRequest,
                defaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_520102,
            );

            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
        });

        // TODO add case when line already has abolition order

        test("should return CODE_520105 if line is hankuro", async () => {
            defaultContext.jsonBody.lineNo = testdata.hankuroLine;
            const result = await LinesDelHandler.Handler(
                DefaultRequest,
                defaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_520105,
            );

            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
        });
    });

    describe("NG (CODE_999999)", () => {
        test("should return CODE_999999 if clientIpAddress is not allowed", async () => {
            try {
                defaultContext.isInternalRequest = false;
                defaultContext.jsonBody.lineNo = testdata.hankuroLine;
                // ORDER_TYPE_2
                defaultContext.jsonBody.reserve_date = "2021/01/01";
                defaultContext.jsonBody.reserve_flag = true;
                defaultContext.jsonBody.reserve_soId = "*********";
                jest.spyOn(
                    MvnoUtil,
                    "getClientIPAddress",
                ).mockImplementationOnce(() => "*********");

                const result = await LinesDelHandler.Handler(
                    DefaultRequest,
                    defaultContext,
                );
                expect(typeof result).toBe("object");
                expect(result).toHaveProperty("jsonBody");
                expect(result.jsonBody).toHaveProperty("responseHeader");
                expect(result.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_999999,
                );

                await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
            } finally {
                defaultContext.isInternalRequest = true;
            }
        });
    });

    test("should return ORDER_TYPE_1", async () => {
        const result = LinesDelHandler.Meta.getOrderType(request);
        expect(result).not.toBeNull();
        expect(result).toEqual(Constants.ORDER_TYPE_1);
    });

    // TODO add more test cases
});
