import { ResetAutoApiBatchTimeHandler } from "@/functions/ResetAutoApiBatchTimeHandler";
import DefaultContext from "../testing/DefaultContext";
import { expect, test, beforeEach, beforeAll, afterAll, afterEach, describe } from "@jest/globals";
import { Sequelize } from "sequelize";
import { disconnectMongo, useMongo } from "@/database/mongo";
import { usePsql } from "@/database/psql";
import AutoApiBatchTimesEntity from "@/core/entity/AutoApiBatchTimesEntity";
import ResultCdConstants from "@/core/constant/ResultCdConstants";
import { HttpRequest } from "@azure/functions";
import ExtendedPortalInvocationContext from "@/types/ExtendedPortalInvocationContext";
import {extendedPortalInvocationContext} from "../testing/DefaultContext";
import { CommonError } from "@/constants/commonError";

describe("ResetAutoApiBatchTimeHandler", () => {
    let sequelize: Sequelize;
    const now = new Date((Math.floor(new Date().getTime() / 1000) * 1000));

    beforeAll(async () => {
        sequelize = await usePsql();
        await useMongo(DefaultContext);
    });

    afterAll(async () => {
        await sequelize.close();
        await disconnectMongo(DefaultContext);
    });

    beforeEach(async () => {
        await AutoApiBatchTimesEntity.destroy({
            where: {}
        });
    });

    afterEach(async () => {
        await AutoApiBatchTimesEntity.destroy({
            where: {}
        });

        jest.restoreAllMocks();
        jest.clearAllMocks();
    });

    describe("OK Case", () => {
        test("Able to reset time", async () => {
            await AutoApiBatchTimesEntity.create({
                preExecTime: now,
                execTime: new Date(now.getTime() - 3600000)
            });

            const mockRequest = {} as HttpRequest;

            const response = await ResetAutoApiBatchTimeHandler.Handler(mockRequest, extendedPortalInvocationContext);

            expect(response.jsonBody.error).toBe(CommonError.NO_ERROR);

            const batchTimes = await AutoApiBatchTimesEntity.findAll();
            expect(batchTimes.length).toBe(1);

            expect(batchTimes[0].preExecTime.getTime()).toBe(batchTimes[0].execTime.getTime());
        });

        test("Case for not exist api batch time", async () => {
            const mockRequest = {} as HttpRequest;

            const response = await ResetAutoApiBatchTimeHandler.Handler(mockRequest, extendedPortalInvocationContext);

            expect(response.jsonBody.error).toBe(CommonError.NO_ERROR);

            const batchTimes = await AutoApiBatchTimesEntity.findAll();
            expect(batchTimes.length).toBe(0);
        });
    });

    describe("Error case", () => {
        test("DB Error", async () => {
            const mockRequest = {} as HttpRequest;

            jest.spyOn(require("@/core/dao/BatchTimesDao").default, "getAutoApiBatchTimes")
                .mockImplementation(() => {
                    throw new Error("DB Error");
                });

            const response = await ResetAutoApiBatchTimeHandler.Handler(mockRequest, extendedPortalInvocationContext);

            expect(response.body).toBeDefined();
        });

        test("Failed to update", async () => {
            await AutoApiBatchTimesEntity.create({
                preExecTime: now,
                execTime: now
            });

            const mockRequest = {} as HttpRequest;

            jest.spyOn(require("@/core/dao/BatchTimesDao").default, "updAutoApiBatchTimes")
                .mockImplementation(() => {
                    throw new Error("Failed to update");
                });

            const response = await ResetAutoApiBatchTimeHandler.Handler(mockRequest, extendedPortalInvocationContext);

            expect(response.body).toBeDefined();
        });
    });
});