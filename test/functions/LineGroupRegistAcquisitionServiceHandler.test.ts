import { LineGroupRegistAcquisition<PERSON>andler } from "@/functions/LineGroupRegistAcquisitionHandler";
import { Sequelize } from "sequelize";
import {
    describeWithDB,
    generateProcessID,
    generateSOID,
} from "../testing/TestHelper";
import { usePsql } from "@/database/psql";
import AppConfig from "@/appconfig";
import { formatDate } from "date-fns";
import ResponseHeader from "@/core/dto/ResponseHeader";
import ResultCdConstants from "@/core/constant/ResultCdConstants";
import DefaultRequest from "../testing/DefaultRequest";
import DefaultContext from "../testing/DefaultContext";
import LineGroupsEntity from "@/core/entity/LineGroupsEntity";
import TenantGroupPlansEntity from "@/core/entity/TenantGroupPlansEntity";
import { useFasterRetries } from "../testing/TestHelper";
import TenantManage from "@/core/common/TenantManage";
import { disconnectMongo, useMongo } from "@/database/mongo";
import { ConnectionTimedOutError } from "sequelize";
import nock = require("nock");
import { getConfigDataForTest } from "../testing/testdataHelper";
import APILinesGroupDAO from "@/core/dao/ApiLinesGroupDAO";
import LineGroupRegistAcquisitionServiceTx from "@/core/service/impl/LineGroupRegistAcquisitionServiceTx";
import APICommonDAO from "@/core/dao/APICommonDAO";
import { TenantsEntity } from "@/core/entity/TenantsEntity";
import CheckUtil from "@/core/common/CheckUtil";
import SOCommon from "@/core/common/SOCommon";
import ServiceOrdersEntity from "@/core/entity/ServiceOrdersEntity";

const createResponseXML = (
    result: "OK" | "NG",
    lineGroupId: string,
    errorInfomation: string,
    policy_number?: string,
) => {
    if (policy_number) {
        return `
    <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
        xmlns:ns1="http://ws.apache.org/axis2">
        <soapenv:Body>
            <Result>${result}</Result>
            <ServiceProfileInfomation>
                <group_number>${lineGroupId}</group_number>
                <policy_number>${policy_number}</policy_number>
            </ServiceProfileInfomation>
            <ErrorInfomation>${errorInfomation}</ErrorInfomation>
        </soapenv:Body>
    </soapenv:Envelope>
    `.trim();
    }
    return `
    <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
        xmlns:ns1="http://ws.apache.org/axis2">
        <soapenv:Body>
            <Result>${result}</Result>
            <ServiceProfileInfomation>
                <group_number>${lineGroupId}</group_number>
            </ServiceProfileInfomation>
            <ErrorInfomation>${errorInfomation}</ErrorInfomation>
        </soapenv:Body>
    </soapenv:Envelope>
    `.trim();
};

describeWithDB(
    "13 回線グループ新規作成/廃止 /MVNO/api/v100LinesGroup/LineGroup_regist",
    () => {
        let sequelize: Sequelize;
        let instance: LineGroupRegistAcquisitionServiceTx = null;

        const request_with_flag_1 = {
            requestHeader: {
                sequenceNo: "1234567",
                senderSystemId: "0001",
                apiKey: "",
                functionType: "13",
            },
            targetSoId: generateSOID(),
            tenantId: "1RF999",
            lineGroupId: "123456789",
            registFlag: "1",
            potalGroupPlanID: "50001",
            reserve_flag: false,
            reserve_soId: "",
        };

        const request_with_no_lineId = {
            requestHeader: {
                sequenceNo: "1234567",
                senderSystemId: "0001",
                apiKey: "",
                functionType: "13",
            },
            targetSoId: generateSOID(),
            tenantId: "2RF999",
            lineGroupId: "",
            registFlag: "1",
            potalGroupPlanID: "50001",
            reserve_flag: false,
            reserve_soId: "",
        };
        const request_with_no_lineId_2 = {
            requestHeader: {
                sequenceNo: "1234567",
                senderSystemId: "0001",
                apiKey: "",
                functionType: "13",
            },
            targetSoId: generateSOID(),
            tenantId: "1RF999",
            lineGroupId: "",
            registFlag: "1",
            potalGroupPlanID: "50001",
            reserve_flag: false,
            reserve_soId: "",
        };
        const request_with_flag_0 = {
            requestHeader: {
                sequenceNo: "1234567",
                senderSystemId: "0001",
                apiKey: "",
                functionType: "13",
            },
            targetSoId: generateSOID(),
            tenantId: "0RF999",
            lineGroupId: "023456789",
            registFlag: "0",
            potalGroupPlanID: "",
            reserve_flag: false,
            reserve_soId: "",
        };

        const request_with_flag_else = {
            requestHeader: {
                sequenceNo: "1234567",
                senderSystemId: "0001",
                apiKey: "",
                functionType: "13",
            },
            targetSoId: generateSOID(),
            tenantId: "OPF000",
            lineGroupId: "123456789",
            registFlag: "2",
            potalGroupPlanID: "",
            reserve_flag: false,
            reserve_soId: "",
        };

        const responseHeader = {
            sequenceNo: request_with_flag_1.requestHeader.sequenceNo,
            receivedDate: formatDate(new Date(), "yyyy/MM/dd HH:mm:ss"),
            processCode: ResultCdConstants.CODE_000000,
            apiProcessID: generateProcessID(),
        } as ResponseHeader;
        let configData: ReturnType<typeof getConfigDataForTest>;
        const nockHeaders: nock.ReplyHeaders = {
            "Content-Type": "text/xml",
        };
        type ExcludeOpts = {
            checkTpcConnection?: boolean;
        };
        function mockEverything(exclude?: ExcludeOpts) {
            if (exclude?.checkTpcConnection !== true) {
                jest.spyOn(
                    TenantManage.prototype,
                    "checkTpcConnection",
                ).mockResolvedValue([
                    true,
                    "TpcDestIpAddress",
                    "TpcDestIpAddress",
                ]);
            }
        }
        const checkStatus = async (groupId: string, expectedValue: number) => {
            const res = await LineGroupsEntity.findOne({
                where: {
                    groupId,
                },
            });
            expect(res.status).toBe(expectedValue);
        };

        beforeAll(async () => {
            sequelize = await usePsql();
            await useMongo(DefaultContext);
            await AppConfig.loadCoreMvnoConfig(DefaultContext);
            configData = getConfigDataForTest();
            nock.disableNetConnect();
            await Promise.all([
                TenantsEntity.create({
                    tenantId: request_with_flag_1.tenantId,
                    tenantName: "test",
                    tenantMaxConnection: 10,
                    hashedPassword: "",
                    office: true,
                    status: true,
                    tenantLevel: 1,
                    customizeMaxConnection: 10,
                    notvoiceRoamingFlag: false,
                    pTenantId: "ZZZ000",
                    csvOutputPattern: 1,
                    tpc: "TpcDestIpAddress",
                    tpc2: "TpcDestIpAddress2",
                    tenantType: 1,
                }),
                TenantsEntity.create({
                    tenantId: request_with_flag_0.tenantId,
                    tenantName: "test",
                    tenantMaxConnection: 10,
                    hashedPassword: "",
                    office: true,
                    status: true,
                    tenantLevel: 4,
                    customizeMaxConnection: 10,
                    notvoiceRoamingFlag: false,
                    pTenantId: "ZZZ000",
                    csvOutputPattern: 1,
                    tpc: "TpcDestIpAddress",
                    tpc2: "TpcDestIpAddress2",
                    tenantType: 0,
                }),
                TenantsEntity.create({
                    tenantId: request_with_no_lineId.tenantId,
                    tenantName: "test",
                    tenantMaxConnection: 10,
                    hashedPassword: "",
                    office: true,
                    status: true,
                    tenantLevel: 4,
                    customizeMaxConnection: 10,
                    notvoiceRoamingFlag: false,
                    pTenantId: "ZZZ000",
                    csvOutputPattern: 1,
                    tpc: "TpcDestIpAddress",
                    tpc2: "TpcDestIpAddress2",
                    tenantType: 3,
                }),
            ]);
            await Promise.all([
                TenantGroupPlansEntity.create({
                    tenantId: request_with_flag_1.tenantId,
                    planId: parseInt(request_with_flag_1.potalGroupPlanID),
                }),
                TenantGroupPlansEntity.create({
                    tenantId: request_with_no_lineId.tenantId,
                    planId: parseInt(request_with_no_lineId.potalGroupPlanID),
                }),
            ]);
        });

        afterAll(async () => {
            await Promise.all([
                TenantGroupPlansEntity.destroy({
                    where: {
                        tenantId: request_with_flag_1.tenantId,
                        planId: parseInt(request_with_flag_1.potalGroupPlanID),
                    },
                }),
                TenantGroupPlansEntity.destroy({
                    where: {
                        tenantId: request_with_no_lineId.tenantId,
                        planId: parseInt(
                            request_with_no_lineId.potalGroupPlanID,
                        ),
                    },
                }),
            ]);
            await Promise.all([
                TenantsEntity.destroy({
                    where: {
                        tenantId: request_with_flag_1.tenantId,
                    },
                }),
                TenantsEntity.destroy({
                    where: {
                        tenantId: request_with_flag_0.tenantId,
                    },
                }),
                TenantsEntity.destroy({
                    where: {
                        tenantId: request_with_no_lineId.tenantId,
                    },
                }),
            ]);
            nock.enableNetConnect();
            await disconnectMongo(DefaultContext);
            await sequelize.close();
        });

        beforeEach(async () => {
            await LineGroupsEntity.create({
                groupId: request_with_flag_0.lineGroupId,
                status: 1,
                tenantId: request_with_flag_0.tenantId,
                planId: parseInt(request_with_flag_1.potalGroupPlanID),
                createdAt: new Date(),
            }),
                (DefaultContext.jsonBody = Object.assign(
                    {},
                    request_with_flag_1,
                ));
            DefaultContext.responseHeader = Object.assign({}, responseHeader);
            instance = new LineGroupRegistAcquisitionServiceTx(
                DefaultRequest,
                DefaultContext,
            );
            useFasterRetries();
        });

        afterEach(async () => {
            await Promise.all([
                LineGroupsEntity.destroy({
                    where: {
                        groupId: request_with_flag_0.lineGroupId,
                    },
                }),
                ServiceOrdersEntity.destroy({
                    where: {
                        serviceOrderId: responseHeader.apiProcessID,
                    }
                }),
            ]);
            jest.restoreAllMocks();
            jest.clearAllMocks();
            nock.abortPendingRequests();
            nock.cleanAll();
        });

        const INVALID_LINE_GROUP_UNUSABLE_TIME = ["", "11234444444", "abc"];
        const INVALID_LINE_GROUP_ID = ["abc", "11234444444"];
        const INVALID_POTAL_GROUP_PLAN_ID = [
            "11234444444",
            "dsadasdasa",
            "50s01",
            "",
        ];

        describe("OK CASES (CODE_000000)", () => {
            test(`should return CODE_000000 if no errors registflag = 1`, async () => {
                mockEverything();
                nock(configData.soapApiUrl)
                    .post("")
                    .reply(
                        200,
                        createResponseXML(
                            "OK",
                            request_with_flag_1.lineGroupId,
                            "",
                        ),
                    )
                    .persist();

                const response = await instance.service(request_with_flag_1);
                expect(response.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_000000,
                );
                expect(response.jsonBody.lineGroupId).toBe(
                    request_with_flag_1.lineGroupId,
                );
            });

            test(`should return CODE_000000 if no errors registflag = 1 (number)`, async () => {
                mockEverything();
                nock(configData.soapApiUrl)
                    .post("")
                    .reply(
                        200,
                        createResponseXML(
                            "OK",
                            request_with_flag_1.lineGroupId,
                            "",
                        ),
                    )
                    .persist();

                const localrequest = structuredClone(request_with_flag_1);
                localrequest.registFlag = 1 as unknown as string;
                const response = await instance.service(localrequest);
                expect(response.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_000000,
                );
                expect(response.jsonBody.lineGroupId).toBe(
                    request_with_flag_1.lineGroupId,
                );
            });

            test(`should return CODE_000000 if no errors registflag = 0`, async () => {
                mockEverything();
                nock(configData.soapApiUrl)
                    .post("")
                    .reply(
                        200,
                        createResponseXML(
                            "OK",
                            request_with_flag_0.lineGroupId,
                            "",
                        ),
                    )
                    .persist();
                checkStatus(request_with_flag_0.lineGroupId, 1);
                const response = await instance.service(request_with_flag_0);
                expect(response.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_000000,
                );
                expect(response.jsonBody.lineGroupId).toBe(
                    request_with_flag_0.lineGroupId,
                );
                checkStatus(request_with_flag_0.lineGroupId, 0);
            });

            test(`should return CODE_000000 if no errors registflag = 0 (number)`, async () => {
                mockEverything();
                nock(configData.soapApiUrl)
                    .post("")
                    .reply(
                        200,
                        createResponseXML(
                            "OK",
                            request_with_flag_0.lineGroupId,
                            "",
                        ),
                    )
                    .persist();
                checkStatus(request_with_flag_0.lineGroupId, 1);
                const localrequest = structuredClone(request_with_flag_0);
                localrequest.registFlag = 0 as unknown as string;
                const response = await instance.service(localrequest);
                expect(response.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_000000,
                );
                expect(response.jsonBody.lineGroupId).toBe(
                    request_with_flag_0.lineGroupId,
                );
                checkStatus(request_with_flag_0.lineGroupId, 0);
            });

            test(`should return CODE_000000 if no errors registflag = 1 and type is different`, async () => {
                mockEverything();
                nock(configData.soapApiUrl)
                    .post("")
                    .reply(
                        200,
                        createResponseXML("OK", "", "c407"),
                        nockHeaders,
                    );
                const response = await instance.service(request_with_no_lineId);
                expect(response.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_000000,
                );
            });

            test(`should return CODE_000000 if no errors registflag = 1 and type is different`, async () => {
                mockEverything();
                nock(configData.soapApiUrl)
                    .post("")
                    .reply(
                        200,
                        createResponseXML("OK", "", "", ""),
                        nockHeaders,
                    );
                const response = await instance.service(request_with_no_lineId);
                expect(response.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_000000,
                );
            });
            test(`should return CODE_000000 if no errors registflag = 1`, async () => {
                nock(configData.soapApiUrl)
                    .post("")
                    .reply(
                        200,
                        createResponseXML("OK", "", "", ""),
                        nockHeaders,
                    );
                DefaultContext.jsonBody = Object.assign(
                    {},
                    request_with_no_lineId_2,
                );
                jest.spyOn(
                    APICommonDAO.prototype,
                    "getTenants",
                ).mockReturnValue(
                    Promise.resolve(
                        TenantsEntity.build({
                            tenantId: request_with_flag_1.tenantId,
                            tenantType: 2,
                        }),
                    ),
                );
                const result = await LineGroupRegistAcquisitionHandler.Handler(
                    DefaultRequest,
                    DefaultContext,
                );
                expect(result.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_000000,
                );
            });

            test(`should return CODE_000000 if no errors : lineGroupId is number`, async () => {
                mockEverything();
                nock(configData.soapApiUrl)
                    .post("")
                    .reply(
                        200,
                        createResponseXML(
                            "OK",
                            request_with_flag_1.lineGroupId,
                            "",
                        ),
                    )
                    .persist();

                const localrequest = structuredClone(request_with_flag_1);
                localrequest.lineGroupId = parseInt(
                    request_with_flag_1.lineGroupId,
                ) as unknown as string;
                const response = await instance.service(localrequest);
                expect(response.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_000000,
                );
                expect(response.jsonBody.lineGroupId).toBe(
                    request_with_flag_1.lineGroupId,
                );
            });

            test(`should return CODE_000000 if no errors : potalGroupPlanID is number`, async () => {
                mockEverything();
                nock(configData.soapApiUrl)
                    .post("")
                    .reply(
                        200,
                        createResponseXML(
                            "OK",
                            request_with_flag_1.lineGroupId,
                            "",
                        ),
                    )
                    .persist();

                const localrequest = structuredClone(request_with_flag_1);
                localrequest.potalGroupPlanID = parseInt(
                    request_with_flag_1.potalGroupPlanID,
                ) as unknown as string;
                const response = await instance.service(localrequest);
                expect(response.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_000000,
                );
                expect(response.jsonBody.lineGroupId).toBe(
                    request_with_flag_1.lineGroupId,
                );
            });
        });
        describe("NG CASES (CODE_130XXX)", () => {
            INVALID_LINE_GROUP_UNUSABLE_TIME.forEach(
                (lineGroupUnusableTime) => {
                    test(`should return CODE_130001 if lineGroupUnusableTime %s is not valid ${lineGroupUnusableTime}`, async () => {
                        Object.defineProperty(
                            instance,
                            "lineGroupUnusableTime",
                            {
                                value: lineGroupUnusableTime,
                            },
                        );
                        const response = await instance.service(
                            request_with_flag_1,
                        );
                        expect(
                            response.jsonBody.responseHeader.processCode,
                        ).toEqual(ResultCdConstants.CODE_130001);
                    });
                },
            );

            test("should return CODE_130002 if registflag is other than 0 or 1", async () => {
                DefaultContext.jsonBody = Object.assign(
                    {},
                    request_with_flag_else,
                );
                const result = await LineGroupRegistAcquisitionHandler.Handler(
                    DefaultRequest,
                    DefaultContext,
                );
                expect(result.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_130002,
                );
            });

            test.each(INVALID_POTAL_GROUP_PLAN_ID)(
                "should return CODE_130003 if potalGroupPlanID %s not valid while registFlag is 1",
                async (potalGroupPlanID) => {
                    DefaultContext.jsonBody.potalGroupPlanID = potalGroupPlanID;
                    DefaultContext.jsonBody.registFlag =
                        request_with_flag_1.registFlag;
                    const result =
                        await LineGroupRegistAcquisitionHandler.Handler(
                            DefaultRequest,
                            DefaultContext,
                        );
                    expect(result.jsonBody.responseHeader.processCode).toEqual(
                        ResultCdConstants.CODE_130003,
                    );
                },
            );

            test.each(INVALID_LINE_GROUP_ID)(
                "should return CODE_130003 if lineGroupId %s not valid while registFlag is 1",
                async (lineGroupId) => {
                    DefaultContext.jsonBody.lineGroupId = lineGroupId;
                    DefaultContext.jsonBody.registFlag =
                        request_with_flag_1.registFlag;
                    const result =
                        await LineGroupRegistAcquisitionHandler.Handler(
                            DefaultRequest,
                            DefaultContext,
                        );
                    expect(result.jsonBody.responseHeader.processCode).toEqual(
                        ResultCdConstants.CODE_130003,
                    );
                },
            );

            test.each([INVALID_POTAL_GROUP_PLAN_ID])(
                "should return CODE_130004 if potalGroupPlanID %s not valid while registFlag is 0",
                async (potalGroupPlanID) => {
                    DefaultContext.jsonBody = Object.assign(
                        {},
                        request_with_flag_0,
                    );
                    DefaultContext.jsonBody.potalGroupPlanID = potalGroupPlanID;
                    const result =
                        await LineGroupRegistAcquisitionHandler.Handler(
                            DefaultRequest,
                            DefaultContext,
                        );
                    expect(result.jsonBody.responseHeader.processCode).toEqual(
                        ResultCdConstants.CODE_130004,
                    );
                },
            );

            test.each(["abc", "11234444444", "", undefined, null])(
                "should return CODE_130004 if lineGroupId %s not valid while registFlag is 0",
                async (lineGroupId) => {
                    DefaultContext.jsonBody = Object.assign(
                        {},
                        request_with_flag_0,
                    );
                    DefaultContext.jsonBody.lineGroupId = lineGroupId;
                    const result =
                        await LineGroupRegistAcquisitionHandler.Handler(
                            DefaultRequest,
                            DefaultContext,
                        );
                    expect(result.jsonBody.responseHeader.processCode).toEqual(
                        ResultCdConstants.CODE_130004,
                    );
                },
            );

            test.each([request_with_flag_1, request_with_flag_0])(
                "should return CODE_130005 if tenantsEntity throw db error ",
                async (req) => {
                    DefaultContext.jsonBody = Object.assign({}, req);
                    jest.spyOn(
                        APICommonDAO.prototype,
                        "getTenants",
                    ).mockImplementation(async () => {
                        throw new ConnectionTimedOutError(new Error("timeout"));
                    });
                    const result =
                        await LineGroupRegistAcquisitionHandler.Handler(
                            DefaultRequest,
                            DefaultContext,
                        );
                    expect(result.jsonBody.responseHeader.processCode).toEqual(
                        ResultCdConstants.CODE_130005,
                    );
                },
            );

            test.each([request_with_flag_1, request_with_flag_0])(
                "should return CODE_130005 if tenantsEntity not found ",
                async (req) => {
                    DefaultContext.jsonBody = Object.assign({}, req);
                    jest.spyOn(
                        APICommonDAO.prototype,
                        "getTenants",
                    ).mockReturnValue(Promise.resolve(null));
                    const result =
                        await LineGroupRegistAcquisitionHandler.Handler(
                            DefaultRequest,
                            DefaultContext,
                        );
                    expect(result.jsonBody.responseHeader.processCode).toEqual(
                        ResultCdConstants.CODE_130005,
                    );
                },
            );

            test("should return CODE_130006 if tenantsEntity's type is 1 and registflag is 0 ", async () => {
                DefaultContext.jsonBody = Object.assign(
                    {},
                    request_with_flag_0,
                );
                jest.spyOn(
                    APICommonDAO.prototype,
                    "getTenants",
                ).mockReturnValue(
                    Promise.resolve(
                        TenantsEntity.build({
                            tenantId: request_with_flag_1.tenantId,
                            tenantType: 1,
                        }),
                    ),
                );
                const result = await LineGroupRegistAcquisitionHandler.Handler(
                    DefaultRequest,
                    DefaultContext,
                );
                expect(result.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_130006,
                );
            });

            test("should return CODE_130007 if tenantsEntity's type is not 1 and lineGroupId is not null and registflag is 1", async () => {
                DefaultContext.jsonBody = Object.assign(
                    {},
                    request_with_flag_1,
                );
                jest.spyOn(
                    APICommonDAO.prototype,
                    "getTenants",
                ).mockReturnValue(
                    Promise.resolve(
                        TenantsEntity.build({
                            tenantId: request_with_flag_1.tenantId,
                            tenantType: 2,
                        }),
                    ),
                );
                const result = await LineGroupRegistAcquisitionHandler.Handler(
                    DefaultRequest,
                    DefaultContext,
                );
                expect(result.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_130007,
                );
            });

            test("should return CODE_130008 if lineGroupEntity exists and TenantEntity's tenantType is 1", async () => {
                DefaultContext.jsonBody = Object.assign(
                    {},
                    request_with_flag_1,
                );
                jest.spyOn(
                    APILinesGroupDAO.prototype,
                    "getLineGroupsInfo",
                ).mockReturnValue(
                    Promise.resolve(
                        LineGroupsEntity.build({
                            groupId: "1234567",
                            status: 1,
                            tenantId: request_with_flag_1.tenantId,
                            planId: parseInt(
                                request_with_flag_1.potalGroupPlanID,
                            ),
                            createdAt: new Date(),
                        }),
                    ),
                );
                const result = await LineGroupRegistAcquisitionHandler.Handler(
                    DefaultRequest,
                    DefaultContext,
                );
                expect(result.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_130008,
                );
            });

            test("should return CODE_130008 if TenantEntity's tenantType is 1 and apiLinesGroupDAO.getLineGroupsInfo throws db error", async () => {
                DefaultContext.jsonBody = Object.assign(
                    {},
                    request_with_flag_1,
                );
                jest.spyOn(
                    APILinesGroupDAO.prototype,
                    "getLineGroupsInfo",
                ).mockImplementation(async () => {
                    throw new ConnectionTimedOutError(new Error("timeout"));
                });
                const result = await LineGroupRegistAcquisitionHandler.Handler(
                    DefaultRequest,
                    DefaultContext,
                );
                expect(result.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_130008,
                );
            });

            test("should return CODE_130101 if registflag is 1 and apiLinesGroupDAO.getGroupPlanId throws db error", async () => {
                DefaultContext.jsonBody = Object.assign(
                    {},
                    request_with_flag_1,
                );
                jest.spyOn(
                    APILinesGroupDAO.prototype,
                    "getGroupPlanId",
                ).mockImplementation(async () => {
                    throw new ConnectionTimedOutError(new Error("timeout"));
                });
                const result = await LineGroupRegistAcquisitionHandler.Handler(
                    DefaultRequest,
                    DefaultContext,
                );
                expect(result.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_130101,
                );
            });

            test("should return CODE_130101 if registflag is 1 and tenantGroupPlansEntity is empty", async () => {
                DefaultContext.jsonBody = Object.assign(
                    {},
                    request_with_flag_1,
                );
                jest.spyOn(
                    APILinesGroupDAO.prototype,
                    "getGroupPlanId",
                ).mockReturnValue(Promise.resolve(null));
                const result = await LineGroupRegistAcquisitionHandler.Handler(
                    DefaultRequest,
                    DefaultContext,
                );
                expect(result.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_130101,
                );
            });

            test("should return CODE_130201 if registflag is 0 and lineGroupEntity is empty", async () => {
                DefaultContext.jsonBody = Object.assign(
                    {},
                    request_with_flag_0,
                );
                jest.spyOn(
                    APILinesGroupDAO.prototype,
                    "getLineGroupsInfo",
                ).mockReturnValue(Promise.resolve(null));
                const result = await LineGroupRegistAcquisitionHandler.Handler(
                    DefaultRequest,
                    DefaultContext,
                );
                expect(result.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_130201,
                );
            });

            test("should return CODE_130201 if registflag is 0 and apiLinesGroupDAO.getLineGroupsInfo throws db error", async () => {
                DefaultContext.jsonBody = Object.assign(
                    {},
                    request_with_flag_0,
                );
                jest.spyOn(
                    APILinesGroupDAO.prototype,
                    "getLineGroupsInfo",
                ).mockImplementation(async () => {
                    throw new ConnectionTimedOutError(new Error("timeout"));
                });
                const result = await LineGroupRegistAcquisitionHandler.Handler(
                    DefaultRequest,
                    DefaultContext,
                );
                expect(result.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_130201,
                );
            });

            test("should return CODE_130202 if registflag is 0 and apiLinesGroupDAO.getLineGroupsLock throws db erro", async () => {
                DefaultContext.jsonBody = Object.assign(
                    {},
                    request_with_flag_0,
                );
                jest.spyOn(
                    APILinesGroupDAO.prototype,
                    "getLineGroupsLock",
                ).mockImplementation(async () => {
                    throw new ConnectionTimedOutError(new Error("timeout"));
                });
                const result = await LineGroupRegistAcquisitionHandler.Handler(
                    DefaultRequest,
                    DefaultContext,
                );
                expect(result.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_130202,
                );
            });

            test("should return CODE_130202 if registflag is 0 and apiLinesGroupDAO.getLineGroupsLock returns null", async () => {
                DefaultContext.jsonBody = Object.assign(
                    {},
                    request_with_flag_0,
                );
                jest.spyOn(
                    APILinesGroupDAO.prototype,
                    "getLineGroupsLock",
                ).mockReturnValue(Promise.resolve(null));
                const result = await LineGroupRegistAcquisitionHandler.Handler(
                    DefaultRequest,
                    DefaultContext,
                );
                expect(result.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_130202,
                );
            });

            test("should return CODE_130203 if registflag is 0 and lineGroupsEntity.tenantId doesnt match param tenantId", async () => {
                DefaultContext.jsonBody = Object.assign(
                    {},
                    request_with_flag_0,
                );
                jest.spyOn(
                    APILinesGroupDAO.prototype,
                    "getLineGroupsLock",
                ).mockReturnValue(
                    Promise.resolve(
                        LineGroupsEntity.build({
                            groupId: "123456",
                            status: 1,
                            tenantId: "NOT999",
                            planId: parseInt(
                                request_with_flag_1.potalGroupPlanID,
                            ),
                            createdAt: new Date(),
                        }),
                    ),
                );
                const result = await LineGroupRegistAcquisitionHandler.Handler(
                    DefaultRequest,
                    DefaultContext,
                );
                expect(result.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_130203,
                );
            });

            test("should return CODE_130204 if registflag is 0 and lineGroupsEntity.tenantId doesnt match param tenantId", async () => {
                DefaultContext.jsonBody = Object.assign(
                    {},
                    request_with_flag_0,
                );
                jest.spyOn(
                    APILinesGroupDAO.prototype,
                    "getLineGroupsLock",
                ).mockReturnValue(
                    Promise.resolve(
                        LineGroupsEntity.build({
                            groupId: "123456",
                            status: 2,
                            tenantId: request_with_flag_0.tenantId,
                            planId: parseInt(
                                request_with_flag_1.potalGroupPlanID,
                            ),
                            createdAt: new Date(),
                        }),
                    ),
                );
                const result = await LineGroupRegistAcquisitionHandler.Handler(
                    DefaultRequest,
                    DefaultContext,
                );
                expect(result.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_130204,
                );
            });

            test("should return CODE_130205 if registflag is 0 and apiLinesGroupDAO.getLineIdCount throws db error", async () => {
                DefaultContext.jsonBody = Object.assign(
                    {},
                    request_with_flag_0,
                );
                jest.spyOn(
                    APILinesGroupDAO.prototype,
                    "getLineIdCount",
                ).mockImplementation(async () => {
                    throw new ConnectionTimedOutError(new Error("timeout"));
                });
                const result = await LineGroupRegistAcquisitionHandler.Handler(
                    DefaultRequest,
                    DefaultContext,
                );
                expect(result.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_130205,
                );
            });

            test("should return CODE_130205 if registflag is 0 and apiLinesGroupDAO.getLineIdCount returns number except 0", async () => {
                DefaultContext.jsonBody = Object.assign(
                    {},
                    request_with_flag_0,
                );
                jest.spyOn(
                    APILinesGroupDAO.prototype,
                    "getLineIdCount",
                ).mockReturnValue(Promise.resolve(null));
                const result = await LineGroupRegistAcquisitionHandler.Handler(
                    DefaultRequest,
                    DefaultContext,
                );
                expect(result.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_130205,
                );
            });

            test("should return CODE_130208 if registflag is 0 and checkTpcConnection throws db error", async () => {
                DefaultContext.jsonBody = Object.assign(
                    {},
                    request_with_flag_0,
                );
                mockEverything({ checkTpcConnection: true });
                jest.spyOn(
                    TenantManage.prototype,
                    "checkTpcConnection",
                ).mockRejectedValue(
                    new ConnectionTimedOutError(Error("test error")),
                );
                const result = await LineGroupRegistAcquisitionHandler.Handler(
                    DefaultRequest,
                    DefaultContext,
                );
                expect(result.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_130208,
                );
            });

            // TODO check this test case
            const tpcCases = [
                [ResultCdConstants.CODE_130108, request_with_flag_1],
                [ResultCdConstants.CODE_130208, request_with_flag_0],
            ]
            test.each(tpcCases)(
                "should return CODE_%s if checkTpcConnection is not available",
                async (code, request) => {
                    DefaultContext.jsonBody = Object.assign({}, request);
                    mockEverything({ checkTpcConnection: true });
                    jest.spyOn(
                        TenantManage.prototype,
                        "checkTpcConnection",
                    ).mockResolvedValue([false, "", ""]);
                    const result =
                        await LineGroupRegistAcquisitionHandler.Handler(
                            DefaultRequest,
                            DefaultContext,
                        );
                    expect(result.jsonBody.responseHeader.processCode).toEqual(
                        code,
                    );
                },
            );

            test("should return CODE_130206 if serviceProfileRequestLite SOAP response is NG", async () => {
                DefaultContext.jsonBody = Object.assign(
                    {},
                    request_with_flag_0,
                );
                mockEverything();
                nock(configData.soapApiUrl)
                    .post("")
                    .reply(
                        200,
                        createResponseXML("NG", "", "test error"),
                        nockHeaders,
                    );
                const result = await instance.service(request_with_flag_0);
                expect(result.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_130206,
                );
            });

            test("should return CODE_130106 if serviceProfileRequestLite SOAP response is NG", async () => {
                DefaultContext.jsonBody = Object.assign(
                    {},
                    request_with_flag_1,
                );
                mockEverything();
                nock(configData.soapApiUrl)
                    .post("")
                    .reply(
                        200,
                        createResponseXML("NG", "", "test error"),
                        nockHeaders,
                    );
                const result = await instance.service(request_with_flag_1);
                expect(result.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_130106,
                );
            });

            test("should return CODE_130206 if TPC cannot be reached (SOAPException)", async () => {
                DefaultContext.jsonBody = Object.assign(
                    {},
                    request_with_flag_0,
                );
                mockEverything();
                const result = await instance.service(request_with_flag_0);
                expect(result.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_130206,
                );
            });

            test("should return CODE_130106 if TPC cannot be reached (SOAPException)", async () => {
                DefaultContext.jsonBody = Object.assign(
                    {},
                    request_with_flag_1,
                );
                mockEverything();
                const result = await instance.service(request_with_flag_1);
                expect(result.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_130106,
                );
            });

            test(`should return CODE_130207 if registflag = 0 and apiLinesGroupDAO.updLineGroupsForNull throw error`, async () => {
                mockEverything();
                nock(configData.soapApiUrl)
                    .post("")
                    .reply(
                        200,
                        createResponseXML(
                            "OK",
                            request_with_flag_0.lineGroupId,
                            "",
                        ),
                    )
                    .persist();
                jest.spyOn(
                    APILinesGroupDAO.prototype,
                    "updLineGroupsForNull",
                ).mockImplementation(async () => {
                    throw new ConnectionTimedOutError(new Error("timeout"));
                });
                const response = await instance.service(request_with_flag_0);
                expect(response.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_130207,
                );
            });

            test(`should return CODE_130207 if registflag = 0 and apiLinesGroupDAO.updLineGroupsForNull fails other than sql`, async () => {
                mockEverything();
                nock(configData.soapApiUrl)
                    .post("")
                    .reply(
                        200,
                        createResponseXML(
                            "OK",
                            request_with_flag_0.lineGroupId,
                            "",
                        ),
                    )
                    .persist();
                jest.spyOn(
                    APILinesGroupDAO.prototype,
                    "updLineGroupsForNull",
                ).mockImplementation(async () => {
                    throw new Error("generic error");
                });
                const response = await instance.service(request_with_flag_0);
                expect(response.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_130207,
                );
            });

            test("should return CODE_130102 if registfalg is 1 and apiLinesGroupDAO.getServicePlanId returns null", async () => {
                DefaultContext.jsonBody = Object.assign(
                    {},
                    request_with_flag_1,
                );
                mockEverything();
                jest.spyOn(
                    APILinesGroupDAO.prototype,
                    "getServicePlanId",
                ).mockReturnValue(Promise.resolve(null));
                const result = await instance.service(request_with_flag_1);
                expect(result.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_130102,
                );
            });

            test("should return CODE_130102 if registfalg is 1 and apiLinesGroupDAO.getServicePlanId throws", async () => {
                DefaultContext.jsonBody = Object.assign(
                    {},
                    request_with_flag_1,
                );
                mockEverything();
                jest.spyOn(
                    APILinesGroupDAO.prototype,
                    "getServicePlanId",
                ).mockImplementation(async () => {
                    throw new ConnectionTimedOutError(new Error("timeout"));
                });
                const result = await instance.service(request_with_flag_1);
                expect(result.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_130102,
                );
            });

            test("should return CODE_130103 if tenantsEntity's type is not 1 and registflag is 1 and APILinesGroupDAO.getLinesGroupIdList returns null", async () => {
                DefaultContext.jsonBody = Object.assign(
                    {},
                    request_with_no_lineId_2,
                );
                jest.spyOn(
                    APICommonDAO.prototype,
                    "getTenants",
                ).mockReturnValue(
                    Promise.resolve(
                        TenantsEntity.build({
                            tenantId: request_with_flag_1.tenantId,
                            tenantType: 2,
                        }),
                    ),
                );
                jest.spyOn(
                    APILinesGroupDAO.prototype,
                    "getLinesGroupIdList",
                ).mockResolvedValue(Promise.resolve(null));
                const result = await LineGroupRegistAcquisitionHandler.Handler(
                    DefaultRequest,
                    DefaultContext,
                );
                expect(result.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_130103,
                );
            });

            test("should return CODE_130103 if tenantsEntity's type is not 1 and registflag is 1 and APILinesGroupDAO.getLinesGroupIdList throws db errpr", async () => {
                DefaultContext.jsonBody = Object.assign(
                    {},
                    request_with_no_lineId_2,
                );
                jest.spyOn(
                    APICommonDAO.prototype,
                    "getTenants",
                ).mockReturnValue(
                    Promise.resolve(
                        TenantsEntity.build({
                            tenantId: request_with_flag_1.tenantId,
                            tenantType: 2,
                        }),
                    ),
                );
                jest.spyOn(
                    APILinesGroupDAO.prototype,
                    "getLinesGroupIdList",
                ).mockRejectedValue(
                    new ConnectionTimedOutError(Error("test error")),
                );
                const result = await LineGroupRegistAcquisitionHandler.Handler(
                    DefaultRequest,
                    DefaultContext,
                );
                expect(result.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_130103,
                );
            });

            test("should return CODE_130104 if tenantsEntity's type is not 1 and registflag is 1 and APILinesGroupDAO.getLinesGroupIdList gets error", async () => {
                DefaultContext.jsonBody = Object.assign(
                    {},
                    request_with_no_lineId_2,
                );
                jest.spyOn(
                    APICommonDAO.prototype,
                    "getTenants",
                ).mockReturnValue(
                    Promise.resolve(
                        TenantsEntity.build({
                            tenantId: request_with_flag_1.tenantId,
                            tenantType: 2,
                        }),
                    ),
                );
                jest.spyOn(
                    APILinesGroupDAO.prototype,
                    "getLinesGroupIdList",
                ).mockRejectedValue(new TypeError("errror"));
                const result = await LineGroupRegistAcquisitionHandler.Handler(
                    DefaultRequest,
                    DefaultContext,
                );
                expect(result.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_130104,
                );
            });

            test("should return CODE_130104 if tenantsEntity's type is not 1 and registflag is 1 and APILinesGroupDAO.getLineGroupsLockForNew throws db error", async () => {
                DefaultContext.jsonBody = Object.assign(
                    {},
                    request_with_no_lineId_2,
                );
                jest.spyOn(
                    APICommonDAO.prototype,
                    "getTenants",
                ).mockReturnValue(
                    Promise.resolve(
                        TenantsEntity.build({
                            tenantId: request_with_flag_1.tenantId,
                            tenantType: 2,
                        }),
                    ),
                );
                jest.spyOn(
                    APILinesGroupDAO.prototype,
                    "getLineGroupsLockForNew",
                ).mockRejectedValue(
                    new ConnectionTimedOutError(Error("test error")),
                );
                const result = await LineGroupRegistAcquisitionHandler.Handler(
                    DefaultRequest,
                    DefaultContext,
                );
                expect(result.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_130104,
                );
            });

            // TODO fix this test
            test.skip("should return CODE_130102 if tenantsEntity's type is not 1 and registflag is 1 and checkTpcConnection returns falls", async () => {
                DefaultContext.jsonBody = Object.assign(
                    {},
                    request_with_no_lineId_2,
                );
                jest.spyOn(
                    APICommonDAO.prototype,
                    "getTenants",
                ).mockReturnValue(
                    Promise.resolve(
                        TenantsEntity.build({
                            tenantId: request_with_flag_1.tenantId,
                            tenantType: 2,
                        }),
                    ),
                );
                jest.spyOn(
                    TenantManage.prototype,
                    "checkTpcConnection",
                ).mockResolvedValue([false, "", ""]);
                const result = await LineGroupRegistAcquisitionHandler.Handler(
                    DefaultRequest,
                    DefaultContext,
                );
                expect(result.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_130102,
                );
            });

            test("should return CODE_130108 if tenantsEntity's type is not 1 and registflag is 1 and checkTpcConnection throws db error", async () => {
                DefaultContext.jsonBody = Object.assign(
                    {},
                    request_with_no_lineId_2,
                );
                jest.spyOn(
                    APICommonDAO.prototype,
                    "getTenants",
                ).mockReturnValue(
                    Promise.resolve(
                        TenantsEntity.build({
                            tenantId: request_with_flag_1.tenantId,
                            tenantType: 2,
                        }),
                    ),
                );
                jest.spyOn(
                    TenantManage.prototype,
                    "checkTpcConnection",
                ).mockImplementation(async () => {
                    throw new ConnectionTimedOutError(
                        new Error("timeout error"),
                    );
                });
                const result = await LineGroupRegistAcquisitionHandler.Handler(
                    DefaultRequest,
                    DefaultContext,
                );
                expect(result.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_130108,
                );
            });

            test("should return CODE_130106 if tenantsEntity's type is not 1 and registflag is 1 and soap gives NG", async () => {
                DefaultContext.jsonBody = Object.assign(
                    {},
                    request_with_no_lineId_2,
                );
                jest.spyOn(
                    APICommonDAO.prototype,
                    "getTenants",
                ).mockReturnValue(
                    Promise.resolve(
                        TenantsEntity.build({
                            tenantId: request_with_flag_1.tenantId,
                            tenantType: 2,
                        }),
                    ),
                );
                mockEverything();
                nock(configData.soapApiUrl)
                    .post("")
                    .reply(
                        200,
                        createResponseXML("NG", "", "test error"),
                        nockHeaders,
                    );
                const result = await LineGroupRegistAcquisitionHandler.Handler(
                    DefaultRequest,
                    DefaultContext,
                );
                expect(result.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_130106,
                );
            });

            // TODO fix this test (seems not returning CODE_000000)
            test.skip("should return CODE_000000 if soap error includes c407", async () => {
                DefaultContext.jsonBody = Object.assign(
                    {},
                    request_with_no_lineId_2,
                );
                jest.spyOn(
                    APICommonDAO.prototype,
                    "getTenants",
                ).mockReturnValue(
                    Promise.resolve(
                        TenantsEntity.build({
                            tenantId: request_with_flag_1.tenantId,
                            tenantType: 2,
                        }),
                    ),
                );
                mockEverything();
                nock(configData.soapApiUrl)
                    .post("")
                    .reply(
                        200,
                        createResponseXML("NG", "", "c407"),
                        nockHeaders,
                    );
                const result = await LineGroupRegistAcquisitionHandler.Handler(
                    DefaultRequest,
                    DefaultContext,
                );
                expect(result.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_000000,
                );
            });

            test(`should return CODE_130107 if apiLinesGroupDAO.updLineGroup fails`, async () => {
                mockEverything();
                nock(configData.soapApiUrl)
                    .post("")
                    .reply(
                        200,
                        createResponseXML(
                            "OK",
                            request_with_no_lineId_2.lineGroupId,
                            "",
                        ),
                    )
                    .persist();
                jest.spyOn(
                    APICommonDAO.prototype,
                    "getTenants",
                ).mockReturnValue(
                    Promise.resolve(
                        TenantsEntity.build({
                            tenantId: request_with_flag_1.tenantId,
                            tenantType: 2,
                        }),
                    ),
                );
                jest.spyOn(
                    APILinesGroupDAO.prototype,
                    "updLineGroups",
                ).mockImplementation(async () => {
                    throw new ConnectionTimedOutError(
                        new Error("time out error"),
                    );
                });
                const response = await instance.service(
                    request_with_no_lineId_2,
                );
                expect(response.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_130107,
                );
            });

            test(`should return CODE_130107 if apiLinesGroupDAO.updLineGroup fails other than db error`, async () => {
                mockEverything();
                nock(configData.soapApiUrl)
                    .post("")
                    .reply(
                        200,
                        createResponseXML(
                            "OK",
                            request_with_no_lineId_2.lineGroupId,
                            "",
                        ),
                    )
                    .persist();
                jest.spyOn(
                    APICommonDAO.prototype,
                    "getTenants",
                ).mockReturnValue(
                    Promise.resolve(
                        TenantsEntity.build({
                            tenantId: request_with_flag_1.tenantId,
                            tenantType: 2,
                        }),
                    ),
                );
                jest.spyOn(
                    APILinesGroupDAO.prototype,
                    "updLineGroups",
                ).mockImplementation(async () => {
                    throw new Error("test error");
                });
                const response = await instance.service(
                    request_with_no_lineId_2,
                );
                expect(response.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_130107,
                );
            });

            test(`should return CODE_130106 if soap exception`, async () => {
                mockEverything();
                jest.spyOn(
                    APICommonDAO.prototype,
                    "getTenants",
                ).mockReturnValue(
                    Promise.resolve(
                        TenantsEntity.build({
                            tenantId: request_with_flag_1.tenantId,
                            tenantType: 2,
                        }),
                    ),
                );
                const response = await instance.service(
                    request_with_no_lineId_2,
                );
                expect(response.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_130106,
                );
            });

            test(`should return CODE_999999 if unexpected error occured`, async () => {
                const biz = jest.spyOn(instance as any, "biz");
                biz.mockRejectedValue(() => {
                    new Error("some error");
                });
                const response = await instance.service(
                    request_with_no_lineId_2,
                );
                expect(response.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_999999,
                );
            });
        });
    },
);