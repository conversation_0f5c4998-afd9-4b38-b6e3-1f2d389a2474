import { expect, test, beforeEach } from "@jest/globals";
import { addMinutes, formatDate } from "date-fns";
import { Sequelize } from "sequelize";

const enqueueMessage = jest.fn();
jest.mock("../../src/services/servicebus", () => ({
    __esModule: true,
    default: enqueueMessage,
}));

import "@/types/string.extension";

import DefaultContext from "../testing/DefaultContext";
import DefaultRequest from "../testing/DefaultRequest";
import {
    describeWithDB,
    generateProcessID,
    generateSOID,
} from "../testing/TestHelper";

import ResultCdConstants from "@/core/constant/ResultCdConstants";
import ServiceOrdersEntity from "@/core/entity/ServiceOrdersEntity";
import CoreSwimmyApiLog from "@/models/coreSwimmyApiLog";

import AppConfig from "@/appconfig";
import { FrontSoCancelHandler } from "@/functions/FrontSoCancelHandler";

import { usePsql } from "@/database/psql";
import { disconnectMongo, useMongo } from "@/database/mongo";

import { deleteLineNumber, insertLineNumber } from "../testing/testdataHelper";
import { lineDeleteReservationResponse } from "./testdata/frontSoCancel.testdata";
import { getNumberValue } from "@/utils";

describeWithDB("56 フロント用予約キャンセル", () => {
    let sequelize: Sequelize;

    const testdata = {
        lineNo: "08024052801",
        abolishSo: generateProcessID(),
        targetSoId: generateSOID(),
        // reserveDate is 5 days after today
        reserveDate: addMinutes(new Date(), 1 * 24 * 60),
    };

    /** restMessage of service order */
    const restJson = {
        requestHeader: {
            sequenceNo: "1234567",
            senderSystemId: "0001",
            apiKey: "$$$",
            functionType: "52",
        },
        tenantId: "OPF000",
        targetTenantId: "TST000",
        lineNo: testdata.lineNo,
        mnpOutFlag: "0",
        csvUnnecessaryFlag: "0",
        reserve_date: formatDate(testdata.reserveDate, "yyyy/MM/dd"),
    };

    const request = {
        requestHeader: {
            sequenceNo: "1234567",
            senderSystemId: "0001",
            apiKey: "",
            functionType: "56",
        },
        targetSoId: testdata.targetSoId,
        tenantId: "OPF000",
        targetTenantId: "TST000",
        serviceOrderIdKey: testdata.abolishSo,
        csvUnnecessaryFlag: "0",
    };

    const responseHeader = {
        sequenceNo: request.requestHeader.sequenceNo,
        receivedDate: formatDate(new Date(), "yyyy/MM/dd HH:mm:ss"),
        processCode: ResultCdConstants.CODE_000000,
        apiProcessID: generateProcessID(),
    };
    const delay = getNumberValue(
        AppConfig.getCoreConfig(null).CORE_SWIMMY_API.MESSAGE_DELAY_SECONDS,
        60,
    );

    beforeAll(async () => {
        await AppConfig.loadCoreMvnoConfig(DefaultContext);
        sequelize = await usePsql();
        await useMongo(DefaultContext);

        await insertLineNumber(request.targetTenantId, testdata.lineNo);
    });

    afterAll(async () => {
        await deleteLineNumber(testdata.lineNo);

        await sequelize.close();
        await disconnectMongo(DefaultContext);
    });

    beforeEach(async () => {
        // reset mock
        (enqueueMessage as jest.Mock).mockClear();

        DefaultContext.jsonBody = Object.assign({}, request);
        DefaultContext.responseHeader = Object.assign({}, responseHeader);

        const createdAt = Math.floor(Date.now() / 1000) - delay - 10; // createdAt is delay + 10 seconds ago

        await Promise.all([
            ServiceOrdersEntity.create({
                serviceOrderId: testdata.abolishSo,
                tenantId: request.targetTenantId,
                lineId: testdata.lineNo,
                orderStatus: "予約中",
                orderType: "回線廃止",
                reserveDate: testdata.reserveDate,
                execDate: addMinutes(new Date(), -5),
                functionType: "52",
                restMessage: JSON.stringify(restJson),
            }),
            // insert core swimmy api log for abolish order (the one to cancel)
            CoreSwimmyApiLog.create({
                tenantId: request.targetTenantId,
                tempoId: "",
                kaisenNo: testdata.lineNo,
                swimmyType: "008",
                requestOrderId: testdata.abolishSo,
                soId: testdata.targetSoId,
                requestParam: {},
                responseParam: lineDeleteReservationResponse,
                createdAt,
            }),
        ]);
    });

    afterEach(async () => {
        await Promise.all([
            ServiceOrdersEntity.destroy({
                where: {
                    serviceOrderId: [
                        testdata.abolishSo,
                        responseHeader.apiProcessID,
                    ],
                },
            }),
            CoreSwimmyApiLog.deleteMany({
                requestOrderId: {
                    $in: [responseHeader.apiProcessID, testdata.abolishSo],
                },
            }),
        ]);
    });

    const checkServiceOrder = async (
        serviceOrderId: string,
        exists = true,
        status = "予約中",
    ) => {
        const serviceOrder = await ServiceOrdersEntity.findOne({
            where: {
                serviceOrderId,
            },
        });
        if (exists) {
            expect(serviceOrder).not.toBeNull();
            expect(serviceOrder?.orderStatus).toEqual(status);
        } else {
            expect(serviceOrder).toBeNull();
        }
    };

    /**
     * check whether SwimmyApiLog exists and Service Bus is called
     */
    const checkCoreSwimmyApiLog = async (
        apiProcessID: string,
        exists: boolean,
        sendToSB: boolean = true,
    ) => {
        const apiLog = await CoreSwimmyApiLog.findOne({
            requestOrderId: apiProcessID,
        });
        if (exists) {
            expect(apiLog).not.toBeNull();
            if (sendToSB) {
                expect(enqueueMessage).toBeCalledTimes(1);
            } else {
                expect(enqueueMessage).not.toBeCalled();
            }
        } else {
            expect(apiLog).toBeNull();
            expect(enqueueMessage).not.toBeCalled();
        }
    };

    describe("OK (CODE_000000)", () => {
        test("should return CODE_000000 if there is no error (csvUnneccessaryFlag = 0)", async () => {
            const result = await FrontSoCancelHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );

            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );

            await checkServiceOrder(
                DefaultContext.jsonBody.serviceOrderIdKey,
                true,
                "キャンセル済み", // status should be updated
            );
            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, true);
        });

        test("should return CODE_000000 if there is no error (csvUnneccessaryFlag = 0 number)", async () => {
            DefaultContext.jsonBody.csvUnnecessaryFlag = 0;
            const result = await FrontSoCancelHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );

            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );

            await checkServiceOrder(
                DefaultContext.jsonBody.serviceOrderIdKey,
                true,
                "キャンセル済み", // status should be updated
            );
            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, true);
        });

        test("should not create CoreSwimmyApiLog if csvUnneccessaryFlag is 1", async () => {
            DefaultContext.jsonBody.csvUnnecessaryFlag = "1";
            const result = await FrontSoCancelHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );

            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );

            await checkServiceOrder(
                DefaultContext.jsonBody.serviceOrderIdKey,
                true,
                "キャンセル済み", // status should be updated
            );
            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
        });

        test("should not create CoreSwimmyApiLog if csvUnneccessaryFlag is 1 (number)", async () => {
            DefaultContext.jsonBody.csvUnnecessaryFlag = 1;
            const result = await FrontSoCancelHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );

            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );

            await checkServiceOrder(
                DefaultContext.jsonBody.serviceOrderIdKey,
                true,
                "キャンセル済み", // status should be updated
            );
            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
        });

        test("Should send cancel request later if the order is newly created", async () => {
            await CoreSwimmyApiLog.updateOne(
                {
                    requestOrderId: testdata.abolishSo,
                },
                {
                    createdAt: Math.floor(Date.now() / 1000), // set createdAt to now
                },
            );

            const result = await FrontSoCancelHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );

            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );

            await checkServiceOrder(
                DefaultContext.jsonBody.serviceOrderIdKey,
                true,
                "キャンセル済み", // status should be updated
            );
            await checkCoreSwimmyApiLog(
                responseHeader.apiProcessID,
                true, // api log created
                false, // but not send to service bus yet
            );
            const haishiLog = await CoreSwimmyApiLog.findOne({
                requestOrderId: testdata.abolishSo,
            });
            const cancelLog = await CoreSwimmyApiLog.findOne({
                requestOrderId: responseHeader.apiProcessID,
            });
            expect(haishiLog).not.toBeNull();
            expect(cancelLog).not.toBeNull();
            expect(haishiLog.needToActiveTheseIDs).toContain(cancelLog.id);
        });
    });

    describe("NG (CODE_56xxxx | CODE_999999)", () => {
        test("should return CODE_560101 if tenant ID is not Front Tenant", async () => {
            DefaultContext.jsonBody.tenantId = "OPF001";
            const result = await FrontSoCancelHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_560101,
            );

            await checkServiceOrder(
                DefaultContext.jsonBody.serviceOrderIdKey,
                true,
            );
            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
        });

        test("should return CODE_560101 if serviceOrderIdKey is not valid", async () => {
            DefaultContext.jsonBody.serviceOrderIdKey = "AA123456789012345"; // length = 17
            const result = await FrontSoCancelHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );

            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_560101,
            );

            await checkServiceOrder(
                DefaultContext.jsonBody.serviceOrderIdKey,
                false,
            );
            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
        });

        const INVALID_TENANTS = ["CON01", "1CS00011023"];
        test.each(INVALID_TENANTS)(
            "should return CODE_560101 if targetTenantId is not valid (%s)",
            async (targetTenantId) => {
                DefaultContext.jsonBody.targetTenantId = targetTenantId;
                const result = await FrontSoCancelHandler.Handler(
                    DefaultRequest,
                    DefaultContext,
                );

                expect(typeof result).toBe("object");
                expect(result).toHaveProperty("jsonBody");
                expect(result.jsonBody).toHaveProperty("responseHeader");
                expect(result.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_560101,
                );

                await checkServiceOrder(
                    DefaultContext.jsonBody.serviceOrderIdKey,
                    true,
                );
                await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
            },
        );

        const INVALID_CSV_FLAG = [undefined, null, "", "3"];
        test.each(INVALID_CSV_FLAG)(
            "should return CODE_560101 if csvUnnecessaryFlag is not valid (%s)",
            async (csvUnnecessaryFlag) => {
                DefaultContext.jsonBody.csvUnnecessaryFlag = csvUnnecessaryFlag;
                const result = await FrontSoCancelHandler.Handler(
                    DefaultRequest,
                    DefaultContext,
                );

                expect(typeof result).toBe("object");
                expect(result).toHaveProperty("jsonBody");
                expect(result.jsonBody).toHaveProperty("responseHeader");
                expect(result.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_560101,
                );

                await checkServiceOrder(
                    DefaultContext.jsonBody.serviceOrderIdKey,
                    true,
                );
                await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
            },
        );

        test("should return CODE_560201 if service order is not found", async () => {
            await ServiceOrdersEntity.destroy({
                where: {
                    serviceOrderId: testdata.abolishSo,
                },
            });

            const result = await FrontSoCancelHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );

            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_560201,
            );

            await checkServiceOrder(
                DefaultContext.jsonBody.serviceOrderIdKey,
                false,
            );
            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
        });

        test("should return CODE_560201 if service order's reserve date is null", async () => {
            await ServiceOrdersEntity.update(
                { reserveDate: null },
                {
                    where: {
                        serviceOrderId: testdata.abolishSo,
                    },
                },
            );

            const result = await FrontSoCancelHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );

            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_560201,
            );

            await checkServiceOrder(
                DefaultContext.jsonBody.serviceOrderIdKey,
                true,
            );
            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
        });

        test("should return CODE_560201 if service order's restMessage is empty", async () => {
            await ServiceOrdersEntity.update(
                { restMessage: null },
                {
                    where: {
                        serviceOrderId: testdata.abolishSo,
                    },
                },
            );

            const result = await FrontSoCancelHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );

            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_560201,
            );

            await checkServiceOrder(
                DefaultContext.jsonBody.serviceOrderIdKey,
                true,
            );
            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
        });

        test("should return CODE_560202 if restMessage functionType is empty", async () => {
            await ServiceOrdersEntity.update(
                {
                    restMessage: JSON.stringify({
                        ...restJson,
                        requestHeader: {
                            ...restJson.requestHeader,
                            functionType: "",
                        },
                    }),
                },
                {
                    where: {
                        serviceOrderId: testdata.abolishSo,
                    },
                },
            );

            const result = await FrontSoCancelHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );

            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_560202,
            );

            await checkServiceOrder(
                DefaultContext.jsonBody.serviceOrderIdKey,
                true,
            );
            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
        });

        test("should return CODE_560202 if restMessage tenantId is empty", async () => {
            await ServiceOrdersEntity.update(
                {
                    restMessage: JSON.stringify({
                        ...restJson,
                        tenantId: "",
                    }),
                },
                {
                    where: {
                        serviceOrderId: testdata.abolishSo,
                    },
                },
            );

            const result = await FrontSoCancelHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );

            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_560202,
            );

            await checkServiceOrder(
                DefaultContext.jsonBody.serviceOrderIdKey,
                true,
            );
            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
        });

        test("should return CODE_560202 if restMessage targetTenantId is empty", async () => {
            await ServiceOrdersEntity.update(
                {
                    restMessage: JSON.stringify({
                        ...restJson,
                        targetTenantId: "",
                    }),
                },
                {
                    where: {
                        serviceOrderId: testdata.abolishSo,
                    },
                },
            );

            const result = await FrontSoCancelHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );

            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_560202,
            );

            await checkServiceOrder(
                DefaultContext.jsonBody.serviceOrderIdKey,
                true,
            );
            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
        });

        test("should return CODE_560202 if restMessage lineNo is empty", async () => {
            await ServiceOrdersEntity.update(
                {
                    restMessage: JSON.stringify({
                        ...restJson,
                        lineNo: "",
                    }),
                },
                {
                    where: {
                        serviceOrderId: testdata.abolishSo,
                    },
                },
            );

            const result = await FrontSoCancelHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );

            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_560202,
            );

            await checkServiceOrder(
                DefaultContext.jsonBody.serviceOrderIdKey,
                true,
            );
            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
        });

        test("should return CODE_560203 if restMessage functionType is 仮登録回線追加 (51)", async () => {
            await ServiceOrdersEntity.update(
                {
                    restMessage: JSON.stringify({
                        ...restJson,
                        requestHeader: {
                            ...restJson.requestHeader,
                            functionType: "51",
                        },
                    }),
                },
                {
                    where: {
                        serviceOrderId: testdata.abolishSo,
                    },
                },
            );

            const result = await FrontSoCancelHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );

            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_560203,
            );

            await checkServiceOrder(
                DefaultContext.jsonBody.serviceOrderIdKey,
                true,
            );
            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
        });

        // since this is a configuration error, no need to test
        test("should return CODE_560301 if CancelReservationDisableDays config is invalid", () => {});

        // reserve date validation logic (assuming CancelReservationDisableDays is 1)
        test("should return CODE_560302 if current date is not within CancelReservationDisableDays", async () => {
            await ServiceOrdersEntity.update(
                {
                    reserveDate: addMinutes(new Date(), 2 * 60), // reserve date is 2 hours after current date
                },
                {
                    where: {
                        serviceOrderId: testdata.abolishSo,
                    },
                },
            );

            const result = await FrontSoCancelHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );

            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_560302,
            );

            await checkServiceOrder(
                DefaultContext.jsonBody.serviceOrderIdKey,
                true,
            );
        });

        test.skip("should return CODE_560401 if reserve date is in the past", async () => {
            // this case is already handled by CancelReservationDisableDays validation (CODE_560302)
            await ServiceOrdersEntity.update(
                {
                    reserveDate: addMinutes(new Date(), -12 * 60), // reserve date is 12 hours before current date
                },
                {
                    where: {
                        serviceOrderId: testdata.abolishSo,
                    },
                },
            );

            const result = await FrontSoCancelHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );

            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_560401,
            );

            await checkServiceOrder(
                DefaultContext.jsonBody.serviceOrderIdKey,
                true,
            );
            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
        });

        test("should return CODE_560501 if target tenant ID in request and restMessage do not match", async () => {
            DefaultContext.jsonBody.targetTenantId = "TST000";
            await ServiceOrdersEntity.update(
                {
                    restMessage: JSON.stringify({
                        ...restJson,
                        targetTenantId: "CON001",
                    }),
                },
                {
                    where: {
                        serviceOrderId: testdata.abolishSo,
                    },
                },
            );

            const result = await FrontSoCancelHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );

            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_560501,
            );

            await checkServiceOrder(
                DefaultContext.jsonBody.serviceOrderIdKey,
                true,
            );
            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
        });

        test("should return CODE_999999 (error) if restMessage is not a valid JSON string", async () => {
            // NOTE since updating restMessage throws psql error, need to mock it instead
            // this error should not happen in normal operation (rejected during insertion)
            const serviceOrder = await ServiceOrdersEntity.findOne({
                where: {
                    serviceOrderId: testdata.abolishSo,
                },
            });
            jest.spyOn(ServiceOrdersEntity, "findOne").mockImplementationOnce(
                async () => {
                    serviceOrder.restMessage = "invalid json";
                    return serviceOrder;
                },
            );

            const result = await FrontSoCancelHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );

            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_999999,
            );

            // service order exists but not updated
            await checkServiceOrder(
                DefaultContext.jsonBody.serviceOrderIdKey,
                true,
            );
            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
        });
    });
});
