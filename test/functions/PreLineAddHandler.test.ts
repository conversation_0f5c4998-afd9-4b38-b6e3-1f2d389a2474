import config from "config";
import { add, addDays, format, formatDate } from "date-fns";
import { expect, test, beforeEach } from "@jest/globals";
import { ConnectionTimedOutError, Op, QueryTypes, Sequelize } from "sequelize";

const enqueueMessage = jest.fn();
jest.mock("../../src/services/servicebus", () => ({
    __esModule: true,
    default: enqueueMessage,
}));

import "@/types/string.extension";

import DefaultContext, { getDefaultContext } from "../testing/DefaultContext";
import DefaultRequest, { getDefaultRequest } from "../testing/DefaultRequest";
import {
    describeWithDB,
    generateProcessID,
    generateSOID,
} from "../testing/TestHelper";
import SOAPTestHelper from "../testing/soapTestHelper";

import PreLineAddService from "@/core/service/impl/PreLineAddService";
import { PreLineAddHandler } from "@/functions/PreLineAddHandler";

import ResultCdConstants from "@/core/constant/ResultCdConstants";
import AppConfig from "@/appconfig";
import MvnoUtil from "@/core/common/MvnoUtil";

import APICommonDAO from "@/core/dao/APICommonDAO";
import APILinesDAO from "@/core/dao/APILinesDAO";

import PreLineAddInputDto from "@/core/dto/PreLineAddInputDto";
import ResponseHeader from "@/core/dto/ResponseHeader";

import LineOptionsEntity from "@/core/entity/LineOptionsEntity";
import { LinesEntity } from "@/core/entity/LinesEntity";
import ServiceOrdersEntity from "@/core/entity/ServiceOrdersEntity";
import { TenantNnumbersEntity } from "@/core/entity/TenantNnumbersEntity";
import TenantPlanLineOptionsEntity from "@/core/entity/TenantPlanLineOptionsEntity";
import { TenantsEntity } from "@/core/entity/TenantsEntity";

import { usePsql } from "@/database/psql";
import { disconnectMongo, useMongo } from "@/database/mongo";
import CoreSwimmyApiLog from "@/models/coreSwimmyApiLog";
import { CoreSwimmyStatus } from "@/constants/coreSwimmy";
import PlansEntity from "@/core/entity/PlansEntity";
import { getConfig, getConfigAsNumber } from "@/helpers/configHelper";
import Check from "@/core/common/Check";
import TenantManage from "@/core/common/TenantManage";
import CoreAPIService from "@/services/coreAPIService";
import FrontAPIService from "@/services/frontAPIService";
import APISoDAO from "@/core/dao/APISoDAO";
import { CustomerInfoEntity } from "@/core/entity/CustomerInfoEntity";
import { SoCancelHandler } from "@/functions/SoCancelHandler";

describeWithDB("51 仮登録回線追加 /MVNO/api/V100/Lines/preadd", () => {
    let sequelize: Sequelize;

    const testdata = {
        lines: {
            exists: "08024092001",
            notexists: "08000100234",
            abone: "08024092002",
            reserved: "08024092003",
            tsa: "08024092004",
        },
        nBan: {
            tst: "", // N121152174
            tsa: "",
        },
        plans: {
            data: "40001",
            sms: "40029",
            voice: "40056",
            full: "40175",
        },
        cardType: {
            data: "BF0",
            sms: "BF1",
            voice: "BP0",
            full: "J05",
        },
        line_options: {
            // TST
        },
        simType: {
            lite: "microSIM",
            full: "フルマルチカットSIM(N)",
        },
        targetSoId: generateSOID(),
        reserveSOID: generateProcessID(),
        reservedLineSOID: generateProcessID(),
        /** 0 */
        MNP_IN_TYPE_NO: PreLineAddService.MNP_IN_TYPE_NO.toString(),
        /** 1 */
        MNP_IN_TYPE_YES: PreLineAddService.MNP_IN_TYPE_YES.toString(),
        /** 2 */
        MNP_IN_TYPE_SAME_MVNE_YES:
            PreLineAddService.MNP_IN_TYPE_SAME_MVNE_YES.toString(), // this require lineDelTenantId to be set
        tpcDest: {
            del: ["", ""],
            add: ["", ""],
        },
        reservedToCancel: {
            // 同一MVNE廃止前の予約キャンセル処理
            planChange: [generateProcessID(), generateProcessID()],
            kaisenHaishi: [generateProcessID()],
        },
    };
    const __request = {
        requestHeader: {
            sequenceNo: "1234567",
            senderSystemId: "0001",
            apiKey: "",
            functionType: "51",
        },
        tenantId: "OPF000",
        targetTenantId: "TST000",
        targetSoId: testdata.targetSoId,
        nBan: "1234567899",
        lineNo: testdata.lines.notexists,
        potalPlanID: testdata.plans.voice,
        id_intlRoaming: "AO105",
        id_voicemail: "AO143",
        id_callWaiting: "AO142",
        id_intlCall: "AO124",
        id_forwarding: "AO144",
        id_intlForwarding: "AO149",
        sim_no: "DN0402331988070",
        sim_type: "microSIM",
        access: "SH",
        cardTypeId: testdata.cardType.voice,
        psid: undefined,
        imsi: "123456789012345",
        puk1: "12345678",
        puk2: "12345678",
        lineStatus: "0",
        mnpInFlag: testdata.MNP_IN_TYPE_SAME_MVNE_YES,
        csvUnnecessaryFlag: "1",
        reserve_date: "", // formatDate(addDays(new Date(), 30), "yyyy/MM/dd"),
        lineDelTenantId: "TSA000",
    } as PreLineAddInputDto;

    const responseHeader = {
        sequenceNo: __request.requestHeader.sequenceNo,
        receivedDate: formatDate(new Date(), "yyyy/MM/dd HH:mm:ss"),
        processCode: ResultCdConstants.CODE_000000,
        apiProcessID: generateProcessID(),
    } as ResponseHeader;

    const spies = {
        warn: jest.spyOn(DefaultContext, "warn"),
        error: jest.spyOn(DefaultContext, "error"),
    };

    beforeAll(async () => {
        await AppConfig.loadCoreMvnoConfig(DefaultContext);
        sequelize = await usePsql();
        await useMongo(DefaultContext);

        await clearTestData();

        // get nBan
        const nBan = await TenantNnumbersEntity.findAll({
            where: {
                tenantId: ["TST000", "TSA000"],
            },
        });
        testdata.nBan.tst = nBan.find((n) => n.tenantId === "TST000")?.nnumber;
        testdata.nBan.tsa = nBan.find((n) => n.tenantId === "TSA000")?.nnumber;
        __request.nBan = testdata.nBan.tst;

        const [targetTenant, lineDelTenant] = await Promise.all([
            TenantsEntity.findOne({
                where: {
                    tenantId: __request.targetTenantId,
                },
            }),
            TenantsEntity.findOne({
                where: {
                    tenantId: __request.lineDelTenantId,
                },
            }),
        ]);
        if (targetTenant) {
            testdata.tpcDest.add = [targetTenant.tpc, targetTenant.tpc2];
        }
        if (lineDelTenant) {
            testdata.tpcDest.del = [lineDelTenant.tpc, lineDelTenant.tpc2];
        }
    });

    afterAll(async () => {
        await sequelize.close();
        await disconnectMongo(DefaultContext);
    });

    beforeEach(async () => {
        // reset mock
        (enqueueMessage as jest.Mock).mockClear();
        SOAPTestHelper.setup();

        DefaultContext.jsonBody = structuredClone(__request);
        DefaultContext.responseHeader = structuredClone(responseHeader);

        spies.warn = jest.spyOn(DefaultContext, "warn");
        spies.error = jest.spyOn(DefaultContext, "error");

        await prepareTestData();
    });

    afterEach(async () => {
        await clearTestData();

        SOAPTestHelper.tearDown();
        jest.restoreAllMocks();
        jest.clearAllMocks();
        jest.resetModules();
    });

    async function prepareTestData() {
        await Promise.all(
            [testdata.lines.exists, testdata.lines.abone].map(
                async (lineId) => {
                    await LinesEntity.create({
                        lineId,
                        lineStatus: "01",
                        nnumber: testdata.nBan.tst,
                        simFlag: false,
                    });
                },
            ),
        );

        const insertAboneLines = `
            INSERT INTO abone_lines
            (line_id, line_status, nnumber, resale_plan_id, created_at, sim_flag)
            VALUES
            (:lineId, '03', :nnumber, :resale_plan_id, NOW(), false)
        `;
        await sequelize.query(insertAboneLines, {
            replacements: {
                lineId: testdata.lines.abone,
                nnumber: testdata.nBan.tst,
                resale_plan_id: "AR711",
            },
        });
        // service order for reserved line
        await ServiceOrdersEntity.create({
            serviceOrderId: testdata.reservedLineSOID,
            lineId: testdata.lines.reserved,
            orderStatus: "予約中",
            functionType: "51",
        });
    }

    async function clearTestData() {
        const aboneLinesSQL =
            "DELETE FROM abone_lines WHERE line_id in (:lineId)";
        await sequelize.query(aboneLinesSQL, {
            replacements: {
                lineId: [testdata.lines.exists, testdata.lines.notexists],
            },
            type: QueryTypes.DELETE,
        });

        await Promise.all([
            LinesEntity.destroy({
                where: {
                    lineId: [
                        testdata.lines.exists,
                        testdata.lines.abone,
                        testdata.lines.notexists,
                    ],
                },
            }),
            ServiceOrdersEntity.destroy({
                where: {
                    serviceOrderId: [
                        responseHeader.apiProcessID,
                        testdata.reserveSOID,
                        testdata.reservedLineSOID,
                    ],
                },
            }),
            CoreSwimmyApiLog.deleteMany({
                requestOrderId: responseHeader.apiProcessID,
            }),
        ]);
    }

    async function checkCoreSwimmyApiLog(
        apiProcessID: string,
        exists: boolean,
        sameMvne: boolean,
    ) {
        const logs = await CoreSwimmyApiLog.find({
            requestOrderId: apiProcessID,
        });
        if (exists) {
            if (sameMvne) {
                expect(logs).toHaveLength(2);
                const haishi = logs.find((log) => log.swimmyType === "008");
                const shinki = logs.find((log) => log.swimmyType === "007");

                expect(haishi).not.toBeUndefined();
                expect(haishi.jikkouchuFlg).toEqual(true);
                expect(haishi.status).toEqual(
                    CoreSwimmyStatus.TOUROKU_IRAI_NOT_YET_SENT,
                );
                expect(haishi.isChainedTransaction).toEqual(true);
                expect(haishi.tenantId).toEqual(__request.lineDelTenantId);

                expect(shinki).not.toBeUndefined();
                expect(shinki.jikkouchuFlg).toEqual(false);
                expect(shinki.status).toEqual(CoreSwimmyStatus.ON_HOLD);
                expect(shinki.isChainedTransaction).toEqual(true);
                expect(shinki.tenantId).toEqual(__request.targetTenantId);
                expect(shinki.needToActiveTheseIDs).toHaveLength(0);

                expect(Array.isArray(haishi.needToActiveTheseIDs)).toEqual(
                    true,
                );
                expect(
                    haishi.needToActiveTheseIDs.find((id) => id === shinki.id),
                ).not.toBeUndefined();
            } else {
                expect(logs).toHaveLength(1);
                const shinki = logs[0];
                expect(shinki.swimmyType).toEqual("007");
                expect(shinki.jikkouchuFlg).toEqual(true);
                expect(shinki.status).toEqual(
                    CoreSwimmyStatus.TOUROKU_IRAI_NOT_YET_SENT,
                );
                expect(shinki.isChainedTransaction).toEqual(false);
                expect(shinki.needToActiveTheseIDs).toHaveLength(0);
                expect(shinki.tenantId).toEqual(__request.targetTenantId);
            }
            expect(enqueueMessage).toBeCalledTimes(1);
        } else {
            expect(logs).toHaveLength(0);
            expect(enqueueMessage).not.toBeCalled();
        }
    }

    async function changeLineNBan(lineId: string, nBan: string) {
        await LinesEntity.update(
            { nnumber: nBan },
            {
                where: {
                    lineId,
                },
            },
        );
    }

    async function createReservedOrder(
        soId: string,
        tenantId: string,
        lineNo: string,
        orderType: string,
        functionType: string,
    ) {
        // console.log(
        //     `Creating reserved order: ${soId}, ${tenantId}, ${lineNo}, ${orderType}, ${functionType}`,
        // );
        const typeCode =
            {
                プラン変更: "06",
                回線廃止: "52",
            }[orderType] || "06";
        const reserveDate = add(new Date(), { days: 15 });
        const reserveDateStr = format(reserveDate, "yyyy/MM/dd");
        const restMessage = `{"requestHeader":{"sequenceNo":"G463487","senderSystemId":"9a1z","apiKey":"","functionType":"${typeCode}"},"tenantId":"${tenantId}","lineNo":"${lineNo}","potalPlanID_pre":90001,"potalPlanID":40002,"reserve_date":"${reserveDateStr} 04:00"}`;
        await ServiceOrdersEntity.create({
            serviceOrderId: soId,
            tenantId,
            lineId: lineNo,
            orderStatus: "予約中",
            orderType,
            functionType,
            reserveDate,
            orderDate: new Date(),
            restMessage,
        });
        // console.log(">> created reserved order", res);
    }

    async function createApiLogForHaishi(
        requestOrderId: string,
        tenantId: string,
        lineNo: string,
    ) {
        const result = await CoreSwimmyApiLog.create({
            requestOrderId, // AP0~
            soId: generateSOID(),
            tenantId,
            swimmyType: "008",
            kaisenNo: lineNo,
            status: CoreSwimmyStatus.TOUROKU_IRAI_NOT_YET_SENT,
            statusTime: Date.now(),
            jikkouchuFlg: false,
            needToActiveTheseIDs: [],
            isChainedTransaction: false,
        });
        // console.warn(">> created API log for haishi order", {
        //     requestOrderId,
        //     soId: result.soId,
        // });
    }

    function mockApiService(mockCoreAPI = true) {
        const coreApiCancelSpy = !mockCoreAPI
            ? null
            : jest
                  .spyOn(CoreAPIService.prototype, "sendSoCancelRequest")
                  .mockImplementation(
                      async (
                          tenantId: string,
                          serviceOrderIdKey: string,
                          csvUnnecessaryFlag: "0" | "1" = "1",
                      ) => {
                          await ServiceOrdersEntity.update(
                              { orderStatus: "キャンセル済み" },
                              { where: { serviceOrderId: serviceOrderIdKey } },
                          );
                          // console.log(">> mock", serviceOrderIdKey, result);
                          return {
                              isOk: true,
                              processCode: "000000",
                              apiProcessID: generateProcessID(),
                          };
                      },
                  );
        const frontApiCancelSpy = jest
            .spyOn(FrontAPIService.prototype, "sendSoCancelRequest")
            .mockImplementation(async (tenantId: string, soId: string) => {
                // find AP0 then update service order
                const apiLog = await CoreSwimmyApiLog.findOne({
                    soId,
                });
                // console.warn(">>frontApiCancelSpy", { tenantId, soId });
                if (apiLog) {
                    // console.warn(">> cancelling order", {
                    //     requestOrderId: apiLog.requestOrderId,
                    //     tenantId,
                    // });

                    await ServiceOrdersEntity.update(
                        { orderStatus: "キャンセル済み" },
                        {
                            where: {
                                serviceOrderId: apiLog.requestOrderId,
                            },
                        },
                    );
                }
                return {
                    isOk: true,
                    apiProcessID: generateSOID(),
                    processCode: ResultCdConstants.CODE_000000,
                };
            });
        return {
            coreApiCancelSpy,
            frontApiCancelSpy,
        };
    }

    /**
     * Check whether reserved orders are cancelled or not
     *
     * cancelled if the order is MNP_IN_TYPE_SAME_MVNE_YES
     * @param cancelled - whether the orders should be cancelled or not
     * @param apiCall - whether the API call should be made or not
     */
    async function checkReservedOrderStatus(
        coreApiCancelSpy: jest.SpyInstance,
        frontApiCancelSpy: jest.SpyInstance,
        cancelled: boolean,
        apiCall: boolean = true, // for checking whether cancellation is by API or handler
    ) {
        // check for reserved orders (should be cancelled)
        const reservedOrders = await ServiceOrdersEntity.findAll({
            where: {
                serviceOrderId: {
                    [Op.in]: testdata.reservedToCancel.planChange.concat(
                        testdata.reservedToCancel.kaisenHaishi,
                    ),
                },
            },
        });
        expect(reservedOrders).not.toHaveLength(0);
        if (cancelled) {
            const planChange = reservedOrders.filter(
                (o) => o.orderType === "プラン変更",
            );
            const kaisenHaishi = reservedOrders.filter(
                (o) => o.orderType === "回線廃止",
            );
            // CancelAPI should have been called only for planChange orders
            if (apiCall) {
                expect(coreApiCancelSpy).toBeCalledTimes(planChange.length);
            } else {
                expect(coreApiCancelSpy).toBeCalledTimes(0);
            }
            expect(
                planChange.every((o) => o.orderStatus === "キャンセル済み"),
            ).toEqual(true);
            if (apiCall) {
                // FrontCancelAPI should have been called only for haishi orders
                expect(frontApiCancelSpy).toBeCalledTimes(
                    testdata.reservedToCancel.kaisenHaishi.length,
                );
            } else {
                expect(frontApiCancelSpy).toBeCalledTimes(0);
            }
            expect(
                kaisenHaishi.every((o) => o.orderStatus === "キャンセル済み"),
            ).toEqual(true);
        } else {
            // all reserved orders should not be cancelled
            expect(
                reservedOrders.every((o) => o.orderStatus === "予約中"),
            ).toEqual(true);
        }
    }

    describe("OK (CODE_000000) (ライトMVNO)", () => {
        let coreApiCancelSpy: jest.SpyInstance;
        let frontApiCancelSpy: jest.SpyInstance;

        beforeEach(async () => {
            delete DefaultContext.jsonBody.imsi;
            delete DefaultContext.jsonBody.puk2;
            delete DefaultContext.jsonBody.puk1;

            // mock call to CoreAPI and Front External API
            ({ coreApiCancelSpy, frontApiCancelSpy } = mockApiService());

            // create reserved orders
            await Promise.all([
                ...testdata.reservedToCancel.planChange.map(async (soId) => {
                    return await createReservedOrder(
                        soId,
                        __request.lineDelTenantId,
                        testdata.lines.exists,
                        "プラン変更",
                        "06",
                    );
                }),
                ...testdata.reservedToCancel.kaisenHaishi.map(async (soId) => {
                    return await createReservedOrder(
                        soId,
                        __request.lineDelTenantId,
                        testdata.lines.exists,
                        "回線廃止",
                        "52",
                    );
                }),
                // create api log for haishi order
                ...testdata.reservedToCancel.kaisenHaishi.map(
                    async (requestOrderId) => {
                        return await createApiLogForHaishi(
                            requestOrderId,
                            __request.lineDelTenantId,
                            testdata.lines.exists,
                        );
                    },
                ),
            ]);
        });

        afterEach(async () => {
            if (coreApiCancelSpy) {
                coreApiCancelSpy.mockRestore();
                coreApiCancelSpy = undefined;
            }
            if (frontApiCancelSpy) {
                frontApiCancelSpy.mockRestore();
                frontApiCancelSpy = undefined;
            }

            // remove reserved orders and API logs
            await ServiceOrdersEntity.destroy({
                where: {
                    serviceOrderId: {
                        [Op.in]: testdata.reservedToCancel.planChange.concat(
                            testdata.reservedToCancel.kaisenHaishi,
                        ),
                    },
                },
            });
            const result = await CoreSwimmyApiLog.deleteMany({
                requestOrderId: {
                    $in: [...testdata.reservedToCancel.kaisenHaishi],
                },
            });
            // console.warn(">> delete result", result);
        });

        test("should return CODE_000000 (csvUnnecessaryFlag = 1)", async () => {
            const del = testdata.tpcDest.del[0] ?? testdata.tpcDest.del[1];
            const add = testdata.tpcDest.add[0] ?? testdata.tpcDest.add[1];
            SOAPTestHelper.mockResponse(
                "serviceProfileRequestLite",
                testdata.tpcDest.del[0] ?? testdata.tpcDest.del[1],
                {
                    result: "OK",
                },
            );
            if (del !== add) {
                SOAPTestHelper.mockResponse(
                    "serviceProfileRequestLite",
                    testdata.tpcDest.add[0] ?? testdata.tpcDest.add[1],
                    {
                        result: "OK",
                    },
                );
            }
            DefaultContext.jsonBody.lineNo = testdata.lines.notexists;
            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );

            await checkCoreSwimmyApiLog(
                result.jsonBody.responseHeader.apiProcessID,
                false,
                false,
            );
        });

        test("should return CODE_000000 (csvUnnecessaryFlag = 1 number)", async () => {
            const del = testdata.tpcDest.del[0] ?? testdata.tpcDest.del[1];
            const add = testdata.tpcDest.add[0] ?? testdata.tpcDest.add[1];
            SOAPTestHelper.mockResponse(
                "serviceProfileRequestLite",
                testdata.tpcDest.del[0] ?? testdata.tpcDest.del[1],
                {
                    result: "OK",
                },
            );
            if (del !== add) {
                SOAPTestHelper.mockResponse(
                    "serviceProfileRequestLite",
                    testdata.tpcDest.add[0] ?? testdata.tpcDest.add[1],
                    {
                        result: "OK",
                    },
                );
            }
            DefaultContext.jsonBody.lineNo = testdata.lines.notexists;
            DefaultContext.jsonBody.csvUnnecessaryFlag = 1; // number
            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );

            await checkCoreSwimmyApiLog(
                result.jsonBody.responseHeader.apiProcessID,
                false,
                false,
            );
        });

        test("should return CODE_000000 (csvUnnecessaryFlag not set)", async () => {
            const del = testdata.tpcDest.del[0] ?? testdata.tpcDest.del[1];
            const add = testdata.tpcDest.add[0] ?? testdata.tpcDest.add[1];
            SOAPTestHelper.mockResponse(
                "serviceProfileRequestLite",
                testdata.tpcDest.del[0] ?? testdata.tpcDest.del[1],
                {
                    result: "OK",
                },
            );
            if (del !== add) {
                SOAPTestHelper.mockResponse(
                    "serviceProfileRequestLite",
                    testdata.tpcDest.add[0] ?? testdata.tpcDest.add[1],
                    {
                        result: "OK",
                    },
                );
            }
            DefaultContext.jsonBody.lineNo = testdata.lines.notexists;
            delete DefaultContext.jsonBody.csvUnnecessaryFlag;
            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );

            await checkCoreSwimmyApiLog(
                result.jsonBody.responseHeader.apiProcessID,
                false,
                false,
            );

            await checkReservedOrderStatus(
                coreApiCancelSpy,
                frontApiCancelSpy,
                false,
            );
        });

        test("should return CODE_000000 (csvUnnecessaryFlag = 0) MNP_IN_TYPE_SAME_MVNE_YES", async () => {
            // for MNP_IN_TYPE_SAME_MVNE_YES, need to use existing line (for getting customer info)
            DefaultContext.jsonBody.lineNo = testdata.lines.exists;

            const del = testdata.tpcDest.del[0] ?? testdata.tpcDest.del[1];
            const add = testdata.tpcDest.add[0] ?? testdata.tpcDest.add[1];
            SOAPTestHelper.mockResponse(
                "serviceProfileRequestLite",
                testdata.tpcDest.del[0] ?? testdata.tpcDest.del[1],
                {
                    result: "OK",
                },
            );
            if (del !== add) {
                SOAPTestHelper.mockResponse(
                    "serviceProfileRequestLite",
                    testdata.tpcDest.add[0] ?? testdata.tpcDest.add[1],
                    {
                        result: "OK",
                    },
                );
            }

            DefaultContext.jsonBody.csvUnnecessaryFlag = "0";
            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );

            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );

            await checkCoreSwimmyApiLog(
                result.jsonBody.responseHeader.apiProcessID,
                true,
                true,
            );

            // check for reserved orders (should be cancelled)
            await checkReservedOrderStatus(
                coreApiCancelSpy,
                frontApiCancelSpy,
                true,
            );
        });

        test("should return CODE_000000 (csvUnnecessaryFlag = 0 number) MNP_IN_TYPE_SAME_MVNE_YES", async () => {
            // for MNP_IN_TYPE_SAME_MVNE_YES, need to use existing line (for getting customer info)
            DefaultContext.jsonBody.lineNo = testdata.lines.exists;

            const del = testdata.tpcDest.del[0] ?? testdata.tpcDest.del[1];
            const add = testdata.tpcDest.add[0] ?? testdata.tpcDest.add[1];
            SOAPTestHelper.mockResponse(
                "serviceProfileRequestLite",
                testdata.tpcDest.del[0] ?? testdata.tpcDest.del[1],
                {
                    result: "OK",
                },
            );
            if (del !== add) {
                SOAPTestHelper.mockResponse(
                    "serviceProfileRequestLite",
                    testdata.tpcDest.add[0] ?? testdata.tpcDest.add[1],
                    {
                        result: "OK",
                    },
                );
            }

            DefaultContext.jsonBody.csvUnnecessaryFlag = 0;
            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );

            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );

            await checkCoreSwimmyApiLog(
                result.jsonBody.responseHeader.apiProcessID,
                true,
                true,
            );

            // check for reserved orders (should be cancelled)
            await checkReservedOrderStatus(
                coreApiCancelSpy,
                frontApiCancelSpy,
                true,
            );
        });

        test("should return CODE_000000 (csvUnnecessaryFlag = 0) MNP_IN_TYPE_YES", async () => {
            const del = testdata.tpcDest.del[0] ?? testdata.tpcDest.del[1];
            const add = testdata.tpcDest.add[0] ?? testdata.tpcDest.add[1];
            SOAPTestHelper.mockResponse(
                "serviceProfileRequestLite",
                testdata.tpcDest.del[0] ?? testdata.tpcDest.del[1],
                {
                    result: "OK",
                },
            );
            if (del !== add) {
                SOAPTestHelper.mockResponse(
                    "serviceProfileRequestLite",
                    testdata.tpcDest.add[0] ?? testdata.tpcDest.add[1],
                    {
                        result: "OK",
                    },
                );
            }

            DefaultContext.jsonBody.csvUnnecessaryFlag = "0";
            DefaultContext.jsonBody.mnpInFlag = testdata.MNP_IN_TYPE_YES;
            delete DefaultContext.jsonBody.lineDelTenantId;
            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );

            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );

            await checkCoreSwimmyApiLog(
                result.jsonBody.responseHeader.apiProcessID,
                true,
                false,
            );

            await checkReservedOrderStatus(
                coreApiCancelSpy,
                frontApiCancelSpy,
                false,
            );
        });

        test("should return CODE_000000 (csvUnnecessaryFlag = 0) MNP_IN_TYPE_NO", async () => {
            const del = testdata.tpcDest.del[0] ?? testdata.tpcDest.del[1];
            const add = testdata.tpcDest.add[0] ?? testdata.tpcDest.add[1];
            SOAPTestHelper.mockResponse(
                "serviceProfileRequestLite",
                testdata.tpcDest.del[0] ?? testdata.tpcDest.del[1],
                {
                    result: "OK",
                },
            );
            if (del !== add) {
                SOAPTestHelper.mockResponse(
                    "serviceProfileRequestLite",
                    testdata.tpcDest.add[0] ?? testdata.tpcDest.add[1],
                    {
                        result: "OK",
                    },
                );
            }

            DefaultContext.jsonBody.csvUnnecessaryFlag = "0";
            DefaultContext.jsonBody.mnpInFlag = testdata.MNP_IN_TYPE_NO;
            delete DefaultContext.jsonBody.lineDelTenantId;
            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );

            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );

            await checkCoreSwimmyApiLog(
                result.jsonBody.responseHeader.apiProcessID,
                true,
                false,
            );

            await checkReservedOrderStatus(
                coreApiCancelSpy,
                frontApiCancelSpy,
                false,
            );
        });

        test("should return CODE_000000 (csvUnnecessaryFlag = 0) MNP_IN_TYPE_NO potalPlanID is number", async () => {
            const del = testdata.tpcDest.del[0] ?? testdata.tpcDest.del[1];
            const add = testdata.tpcDest.add[0] ?? testdata.tpcDest.add[1];
            SOAPTestHelper.mockResponse(
                "serviceProfileRequestLite",
                testdata.tpcDest.del[0] ?? testdata.tpcDest.del[1],
                {
                    result: "OK",
                },
            );
            if (del !== add) {
                SOAPTestHelper.mockResponse(
                    "serviceProfileRequestLite",
                    testdata.tpcDest.add[0] ?? testdata.tpcDest.add[1],
                    {
                        result: "OK",
                    },
                );
            }

            DefaultContext.jsonBody.csvUnnecessaryFlag = "0";
            DefaultContext.jsonBody.mnpInFlag = testdata.MNP_IN_TYPE_NO;
            DefaultContext.jsonBody.potalPlanID = parseInt(
                DefaultContext.jsonBody.potalPlanID as unknown as string,
                10,
            );
            delete DefaultContext.jsonBody.lineDelTenantId;
            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );

            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );

            await checkCoreSwimmyApiLog(
                result.jsonBody.responseHeader.apiProcessID,
                true,
                false,
            );

            await checkReservedOrderStatus(
                coreApiCancelSpy,
                frontApiCancelSpy,
                false,
            );
        });
    });

    describe("同一MVNE仮登録回線 (予約キャンセルAPI)", () => {
        // here we call Core's Cancel API's Handler directly instead of mock (see beforeEach)
        let coreApiCancelSpy: jest.SpyInstance;
        let frontApiCancelSpy: jest.SpyInstance;

        beforeEach(async () => {
            delete DefaultContext.jsonBody.imsi;
            delete DefaultContext.jsonBody.puk2;
            delete DefaultContext.jsonBody.puk1;

            // create reserved orders
            await Promise.all([
                changeLineNBan(testdata.lines.exists, testdata.nBan.tsa),
                ...testdata.reservedToCancel.planChange.map(async (soId) => {
                    return await createReservedOrder(
                        soId,
                        __request.lineDelTenantId,
                        testdata.lines.exists,
                        "プラン変更",
                        "06",
                    );
                }),
                ...testdata.reservedToCancel.kaisenHaishi.map(async (soId) => {
                    return await createReservedOrder(
                        soId,
                        __request.lineDelTenantId,
                        testdata.lines.exists,
                        "回線廃止",
                        "52",
                    );
                }),
                // create api log for haishi order
                ...testdata.reservedToCancel.kaisenHaishi.map(
                    async (requestOrderId) => {
                        return await createApiLogForHaishi(
                            requestOrderId,
                            __request.lineDelTenantId,
                            testdata.lines.exists,
                        );
                    },
                ),
            ]);

            ({ coreApiCancelSpy, frontApiCancelSpy } = mockApiService(false));

            coreApiCancelSpy = jest
                .spyOn(CoreAPIService.prototype, "sendSoCancelRequest")
                .mockImplementation(
                    async (
                        tenantId: string,
                        serviceOrderIdKey: string,
                        csvUnnecessaryFlag: string,
                    ) => {
                        // get reserved order status
                        const orders = await ServiceOrdersEntity.findAll({
                            where: {
                                serviceOrderId: {
                                    [Op.in]:
                                        testdata.reservedToCancel.planChange.concat(
                                            testdata.reservedToCancel
                                                .kaisenHaishi,
                                        ),
                                },
                            },
                        });

                        const coreApiService = new CoreAPIService(
                            DefaultRequest,
                            DefaultContext,
                        );
                        const subRequest = getDefaultRequest();
                        const subContext = getDefaultContext();
                        subContext.jsonBody = {
                            requestHeader:
                                await coreApiService.getTenantRequestHeader(
                                    tenantId,
                                    "18",
                                ),
                            tenantId,
                            serviceOrderIdKey,
                            csvUnnecessaryFlag,
                        };
                        // initialize responseHeader (base external API handler)
                        subContext.responseHeader = {
                            sequenceNo:
                                subContext.jsonBody.requestHeader.sequenceNo,
                            processCode: ResultCdConstants.CODE_000000,
                            apiProcessID: generateProcessID(),
                            receivedDate: MvnoUtil.getDateTimeNow(),
                        };
                        const cancelResult = await SoCancelHandler.Handler(
                            subRequest,
                            subContext,
                        );
                        // console.log("SoCancelHandler result", cancelResult);
                        const processCode =
                            cancelResult?.jsonBody?.responseHeader?.processCode;
                        const apiProcessID =
                            cancelResult?.jsonBody?.responseHeader
                                ?.apiProcessID;
                        const isOk =
                            processCode === ResultCdConstants.CODE_000000;
                        return {
                            isOk,
                            processCode,
                            apiProcessID,
                        };
                    },
                );
        });

        afterEach(async () => {
            if (coreApiCancelSpy) {
                coreApiCancelSpy.mockRestore();
                coreApiCancelSpy = undefined;
            }
            if (frontApiCancelSpy) {
                frontApiCancelSpy.mockRestore();
                frontApiCancelSpy = undefined;
            }

            // remove reserved orders and API logs
            await ServiceOrdersEntity.destroy({
                where: {
                    serviceOrderId: {
                        [Op.in]: testdata.reservedToCancel.planChange.concat(
                            testdata.reservedToCancel.kaisenHaishi,
                        ),
                    },
                },
            });
            await CoreSwimmyApiLog.deleteMany({
                requestOrderId: {
                    $in: [...testdata.reservedToCancel.kaisenHaishi],
                },
            });
            // console.warn(">> delete result", result);
        });

        test("should return CODE_000000 (csvUnnecessaryFlag = 0) MNP_IN_TYPE_SAME_MVNE_YES", async () => {
            // for MNP_IN_TYPE_SAME_MVNE_YES, need to use existing line (for getting customer info)
            DefaultContext.jsonBody.lineNo = testdata.lines.exists;

            const del = testdata.tpcDest.del[0] ?? testdata.tpcDest.del[1];
            const add = testdata.tpcDest.add[0] ?? testdata.tpcDest.add[1];
            SOAPTestHelper.mockResponse(
                "serviceProfileRequestLite",
                testdata.tpcDest.del[0] ?? testdata.tpcDest.del[1],
                {
                    result: "OK",
                },
            );
            if (del !== add) {
                SOAPTestHelper.mockResponse(
                    "serviceProfileRequestLite",
                    testdata.tpcDest.add[0] ?? testdata.tpcDest.add[1],
                    {
                        result: "OK",
                    },
                );
            }

            DefaultContext.jsonBody.csvUnnecessaryFlag = "0";
            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            // console.log(">> PreLineAddHandler result", result);

            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );

            await checkCoreSwimmyApiLog(
                result.jsonBody.responseHeader.apiProcessID,
                true,
                true,
            );

            // check for reserved orders (should be cancelled)
            await checkReservedOrderStatus(
                coreApiCancelSpy,
                frontApiCancelSpy,
                true,
            );
        });
    });

    describe("OK (CODE_000000) (フルMVNO)", () => {
        // NOTE MNP_IN_TYPE_YES and MNP_IN_TYPE_SAME_MVNE_YES need voice plan
        beforeEach(() => {
            DefaultContext.jsonBody.potalPlanID = testdata.plans.full;
            DefaultContext.jsonBody.cardTypeId = testdata.cardType.full;
            DefaultContext.jsonBody.sim_type = testdata.simType.full;
            delete DefaultContext.jsonBody.id_intlRoaming;
            delete DefaultContext.jsonBody.id_voicemail;
            delete DefaultContext.jsonBody.id_callWaiting;
            delete DefaultContext.jsonBody.id_intlCall;
            delete DefaultContext.jsonBody.id_forwarding;
            delete DefaultContext.jsonBody.id_intlForwarding;
        });

        test("should return CODE_000000 (csvUnnecessaryFlag = 0)", async () => {
            const del = testdata.tpcDest.del[0] ?? testdata.tpcDest.del[1];
            const add = testdata.tpcDest.add[0] ?? testdata.tpcDest.add[1];
            SOAPTestHelper.mockResponse(
                "serviceProfileRequestLite",
                testdata.tpcDest.del[0] ?? testdata.tpcDest.del[1],
                {
                    result: "OK",
                },
            );
            if (del !== add) {
                SOAPTestHelper.mockResponse(
                    "serviceProfileRequestLite",
                    testdata.tpcDest.add[0] ?? testdata.tpcDest.add[1],
                    {
                        result: "OK",
                    },
                );
            }

            DefaultContext.jsonBody.csvUnnecessaryFlag = "0";
            DefaultContext.jsonBody.mnpInFlag = testdata.MNP_IN_TYPE_NO;
            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );

            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );

            await checkCoreSwimmyApiLog(
                result.jsonBody.responseHeader.apiProcessID,
                true,
                false,
            );
        });

        test("should return CODE_000000 (csvUnnecessaryFlag = 1)", async () => {
            const del = testdata.tpcDest.del[0] ?? testdata.tpcDest.del[1];
            const add = testdata.tpcDest.add[0] ?? testdata.tpcDest.add[1];
            SOAPTestHelper.mockResponse(
                "serviceProfileRequestLite",
                testdata.tpcDest.del[0] ?? testdata.tpcDest.del[1],
                {
                    result: "OK",
                },
            );
            if (del !== add) {
                SOAPTestHelper.mockResponse(
                    "serviceProfileRequestLite",
                    testdata.tpcDest.add[0] ?? testdata.tpcDest.add[1],
                    {
                        result: "OK",
                    },
                );
            }

            DefaultContext.jsonBody.csvUnnecessaryFlag = "1";
            DefaultContext.jsonBody.mnpInFlag = testdata.MNP_IN_TYPE_NO;
            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );

            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );

            await checkCoreSwimmyApiLog(
                result.jsonBody.responseHeader.apiProcessID,
                false,
                false,
            );
        });
    });

    describe("NG (CODE_51xxxx) (ライトMVNO)", () => {
        beforeEach(() => {
            delete DefaultContext.jsonBody.imsi;
            delete DefaultContext.jsonBody.puk2;
            delete DefaultContext.jsonBody.puk1;
        });

        afterEach(() => {
            DefaultContext.isInternalRequest = true;
        });

        const checkNGCode = (result: any, processCode: string) => {
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                processCode,
            );
        };

        test("should return CODE_999999 if it is 予約 and request not from localhost", async () => {
            DefaultContext.jsonBody.reserve_date = formatDate(
                new Date(),
                "yyyy/MM/dd",
            );
            DefaultContext.jsonBody.reserve_flag = true;
            DefaultContext.jsonBody.reserve_soId = testdata.reserveSOID;

            DefaultContext.isInternalRequest = false;

            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_999999);
        });

        test("should return CODE_510112 if order type is '9' (予約以外オーダー)", async () => {
            DefaultContext.jsonBody.reserve_date = formatDate(
                new Date(),
                "yyyy/MM/dd",
            );
            DefaultContext.jsonBody.reserve_flag = true;
            DefaultContext.jsonBody.reserve_soId = null;

            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510112);
        });

        test("should return CODE_510101 if tenantId is not Front API Tenant", async () => {
            DefaultContext.jsonBody.tenantId = "OPF001";

            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510101);
        });

        test("should return CODE_510101 if targetTenantId format is invalid", async () => {
            DefaultContext.jsonBody.targetTenantId = "TST0123456790";

            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510101);
        });

        test("should return CODE_510101 if nBan format is invalid", async () => {
            DefaultContext.jsonBody.nBan = "**********";

            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510101);
        });

        test("should return CODE_510101 if lineNo format is invalid", async () => {
            DefaultContext.jsonBody.lineNo = "0801234123"; // must be 11 digits

            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510101);
        });

        test("should return CODE_510101 if ptalPlanID format is invalid", async () => {
            DefaultContext.jsonBody.potalPlanID = "1111"; // must be 5 digits

            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510101);
        });

        test("should return CODE_510101 if intlRoaming is invalid", async () => {
            DefaultContext.jsonBody.id_intlRoaming = "AO"; // must be 5 characters

            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510101);
        });

        test("should return CODE_510101 if voicemail is invalid", async () => {
            DefaultContext.jsonBody.id_voicemail = "AO"; // must be 5 characters

            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510101);
        });

        test("should return CODE_510101 if callWaiting is invalid", async () => {
            DefaultContext.jsonBody.id_callWaiting = "AO"; // must be 5 characters

            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510101);
        });

        test("should return CODE_510101 if intlCall is invalid", async () => {
            DefaultContext.jsonBody.id_intlCall = "AO"; // must be 5 characters

            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510101);
        });

        test("should return CODE_510101 if forwarding is invalid", async () => {
            DefaultContext.jsonBody.id_forwarding = "AO"; // must be 5 characters

            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510101);
        });

        test("should return CODE_510101 if intlForwarding is invalid", async () => {
            DefaultContext.jsonBody.id_intlForwarding = "AO"; // must be 5 characters

            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510101);
        });

        test("should return CODE_510101 if sim_no length > 32", async () => {
            DefaultContext.jsonBody.sim_no =
                "DN04023319880701234567890123456789"; // 34

            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510101);
        });

        test("should return CODE_510101 if sim_type length > 16", async () => {
            DefaultContext.jsonBody.sim_type = "フルマルチカットSIM(N)1234"; // 18

            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510101);
        });

        test("should return CODE_510101 if access value is invalid", async () => {
            DefaultContext.jsonBody.access = "SK"; // SA~SJ ex SI

            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510101);
        });

        test("should return CODE_510101 if csvUnnecessaryFlag value is invalid", async () => {
            DefaultContext.jsonBody.csvUnnecessaryFlag = "2"; // must be 0, 1 or empty

            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510101);
        });

        test("should return CODE_510101 if csvUnnecessaryFlag value is 1 (and cardTypeId empty)", async () => {
            DefaultContext.jsonBody.csvUnnecessaryFlag = "0";
            DefaultContext.jsonBody.cardTypeId = "";

            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510101);
        });

        test("should return CODE_510101 if cardTypeId format is invalid", async () => {
            DefaultContext.jsonBody.cardTypeId = "AA123"; // must be up to 4 characters

            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510101);
        });

        test("should return CODE_510101 if psid length is greater than 20", async () => {
            DefaultContext.jsonBody.psid = "123456789012345678901"; // 21

            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510101);
        });

        test("should return CODE_510101 if mnpInFlag value is invalid", async () => {
            DefaultContext.jsonBody.mnpInFlag = "3"; // must be 0, 1 or 2

            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510101);
        });

        test("should return CODE_510101 if mnpFlag is 1 and reserveDate format is invalid", async () => {
            DefaultContext.jsonBody.mnpInFlag = testdata.MNP_IN_TYPE_YES;
            DefaultContext.jsonBody.reserve_date = "2022-01-01"; // must be yyyy/MM/dd

            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510101);
        });

        test("should return CODE_510101 if lineDelTenantId format is invalid", async () => {
            DefaultContext.jsonBody.lineDelTenantId = "TSA0123456790";

            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510101);
        });

        test("should return CODE_510101 if lineDelTenantId is empty and mnpInFlag is 2", async () => {
            DefaultContext.jsonBody.lineDelTenantId = "";
            DefaultContext.jsonBody.mnpInFlag =
                testdata.MNP_IN_TYPE_SAME_MVNE_YES;

            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510101);
        });

        test("should return CODE_510501 if reserve_date is in past", async () => {
            DefaultContext.jsonBody.mnpInFlag = testdata.MNP_IN_TYPE_YES;
            DefaultContext.jsonBody.reserve_date = formatDate(
                addDays(new Date(), -1),
                "yyyy/MM/dd",
            );

            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510501);
        });

        test.skip("should return CODE_510502 if 予約実行日時単位 config format is invalid", async () => {
            // since we can't change the config value, skip this test
            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510502);
        });

        test.skip("should return CODE_510503 if reservation date unit is not valid", async () => {
            // HH:mm part is hard-coded to 04:00, skip this test
            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510503);
        });

        test.skip("should return CODE_510504 if reservationsLimitDays config format is invalid", async () => {
            // since we can't change the config value, skip this test
            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510504);
        });

        test("should return CODE_510505 if reserve_date is too far in the future", async () => {
            const days = getConfigAsNumber("ReservationsLimitDays") + 1;
            const reserveDate = formatDate(
                addDays(new Date(), days),
                "yyyy/MM/dd",
            );
            DefaultContext.jsonBody.mnpInFlag = testdata.MNP_IN_TYPE_YES;
            DefaultContext.jsonBody.reserve_date = reserveDate;

            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510505);
        });

        test.skip("should return CODE_510102 if preLineAddNgTime config is invalid", async () => {
            // since we can't change the config value, skip this test
            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510102);
        });

        test("should return CODE_510103 if current time is within preLineAddNgTime", async () => {
            const preLineAddNgTime = getConfig("PreLineAddNgTime");
            const [start] = preLineAddNgTime.split("-");
            const now = new Date();
            const startTime = new Date(now);
            startTime.setHours(parseInt(start.split(":")[0], 10));
            startTime.setMinutes(parseInt(start.split(":")[1], 10));
            jest.spyOn(MvnoUtil, "getDateTimeNow").mockReturnValue(
                formatDate(startTime, "yyyy/MM/dd HH:mm:ss"),
            );

            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510103);
        });

        test("should return CODE_510105 if line not found", async () => {
            DefaultContext.jsonBody.mnpInFlag = testdata.MNP_IN_TYPE_YES;
            DefaultContext.jsonBody.lineNo = testdata.lines.exists;

            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510105);
        });

        test("should return CODE_510105 if failed retrieving line info", async () => {
            DefaultContext.jsonBody.mnpInFlag = testdata.MNP_IN_TYPE_YES;
            jest.spyOn(APILinesDAO.prototype, "getLineInfo").mockRejectedValue(
                new ConnectionTimedOutError(
                    new Error("test connection timeout"),
                ),
            );

            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510105);
        });

        test("should return CODE_510105 if line is already reserved in other SO", async () => {
            DefaultContext.jsonBody.mnpInFlag = testdata.MNP_IN_TYPE_YES;
            DefaultContext.jsonBody.lineNo = testdata.lines.reserved;
            DefaultContext.jsonBody.reserve_date = formatDate(
                new Date(),
                "yyyy/MM/dd",
            );
            DefaultContext.jsonBody.reserve_soId = testdata.reserveSOID;
            DefaultContext.jsonBody.reserve_flag = true;
            DefaultContext.jsonBody.mnpInFlag = testdata.MNP_IN_TYPE_YES;

            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510105);
        });

        test("should return CODE_510105 if failed retrieving reserved SO list", async () => {
            jest.spyOn(
                APILinesDAO.prototype,
                "getLineOpenReserveOrder",
            ).mockRejectedValue(
                new ConnectionTimedOutError(
                    new Error("test connection timeout"),
                ),
            );
            DefaultContext.jsonBody.mnpInFlag = testdata.MNP_IN_TYPE_YES;
            DefaultContext.jsonBody.lineNo = testdata.lines.reserved;
            DefaultContext.jsonBody.reserve_date = formatDate(
                new Date(),
                "yyyy/MM/dd",
            );
            DefaultContext.jsonBody.reserve_soId = testdata.reserveSOID;
            DefaultContext.jsonBody.reserve_flag = true;
            DefaultContext.jsonBody.mnpInFlag = testdata.MNP_IN_TYPE_YES;

            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510105);
        });

        test("should return CODE_510106 if plan not found", async () => {
            DefaultContext.jsonBody.potalPlanID = "10000";

            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510106);
        });

        test("should return CODE_510106 if failed retrieving plan info", async () => {
            jest.spyOn(APILinesDAO.prototype, "getPlanList").mockRejectedValue(
                new ConnectionTimedOutError(
                    new Error("test connection timeout"),
                ),
            );

            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510106);
        });

        test("should return CODE_510107 if plan is not voice plan (MNP IN)", async () => {
            DefaultContext.jsonBody.mnpInFlag = testdata.MNP_IN_TYPE_YES;
            DefaultContext.jsonBody.potalPlanID = testdata.plans.data;

            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510107);
        });

        test("should return CODE_510107 if plan is not voice plan (same MVNE)", async () => {
            DefaultContext.jsonBody.mnpInFlag =
                testdata.MNP_IN_TYPE_SAME_MVNE_YES;
            DefaultContext.jsonBody.potalPlanID = testdata.plans.data;

            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510107);
        });

        test("should return CODE_510107 if failed retrieving plan info", async () => {
            jest.spyOn(
                APILinesDAO.prototype,
                "getPlansInfoByPlanId",
            ).mockRejectedValue(
                new ConnectionTimedOutError(
                    new Error("test connection timeout"),
                ),
            );
            DefaultContext.jsonBody.mnpInFlag = testdata.MNP_IN_TYPE_YES;

            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510107);
        });

        test("should return CODE_510108 if target tenant not found", async () => {
            // invalid tenant causes validation to fail when searching for plan info first
            // so this test case should not be possible
            jest.spyOn(
                APICommonDAO.prototype,
                "getTenantsEntity",
            ).mockResolvedValue(null);

            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510108);
        });

        test("should return CODE_510108 if failed retrieving target tenant info", async () => {
            jest.spyOn(
                APICommonDAO.prototype,
                "getTenantsEntity",
            ).mockRejectedValue(
                new ConnectionTimedOutError(
                    new Error("test connection timeout"),
                ),
            );

            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510108);
        });

        test("should return CODE_510108 if voice mail line option not found", async () => {
            DefaultContext.jsonBody.id_voicemail = "AO999";

            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510108);
        });

        test("should return CODE_510108 if voice mail line option does not belong to target tenant", async () => {
            jest.spyOn(
                APILinesDAO.prototype,
                "getTenantPlanLineOptionsInfo",
            ).mockResolvedValue(null);

            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510108);
        });

        test("should return CODE_510108 if cannot use voicemail", async () => {
            jest.spyOn(
                APILinesDAO.prototype,
                "getVoicemailFromCustInfo",
            ).mockResolvedValue(null);

            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510108);
        });

        test("should return CODE_510108 if failed retrieving voicemail from customer info", async () => {
            jest.spyOn(
                APILinesDAO.prototype,
                "getVoicemailFromCustInfo",
            ).mockRejectedValue(
                new ConnectionTimedOutError(
                    new Error("test connection timeout"),
                ),
            );

            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510108);
        });

        test("should return CODE_510108 if callWaiting line option not found", async () => {
            DefaultContext.jsonBody.id_callWaiting = "AO999";

            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510108);
        });

        test("should return CODE_510108 if plan does not have callWaiting line option", async () => {
            const spy = jest.spyOn(
                APILinesDAO.prototype,
                "getTenantPlanLineOptionsInfo",
            );
            spy.mockImplementationOnce(
                async (tenantId, planId, lineOptionType) => {
                    // first call
                    const result = await TenantPlanLineOptionsEntity.findOne({
                        where: {
                            tenantId,
                            planId,
                            lineOptionType,
                        },
                    });
                    return result;
                },
            );
            spy.mockResolvedValueOnce(null);

            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510108);
        });

        test("should return CODE_510108 if cannot use callWaiting", async () => {
            jest.spyOn(
                APILinesDAO.prototype,
                "getCallWaitingFromCustInfo",
            ).mockReturnValue(null);

            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510108);
        });

        test("should return CODE_510108 if failed retrieving callWaiting from customer info", async () => {
            jest.spyOn(
                APILinesDAO.prototype,
                "getCallWaitingFromCustInfo",
            ).mockRejectedValue(
                new ConnectionTimedOutError(
                    new Error("test connection timeout"),
                ),
            );

            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510108);
        });

        test("should return CODE_510109 if getOffice fails to query", async () => {
            jest.spyOn(APILinesDAO.prototype, "getOffice").mockRejectedValue(
                new ConnectionTimedOutError(
                    new Error("test connection timeout"),
                ),
            );

            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510109);
        });

        test("should return CODE_510109 if getTenantNnumbers fails to query", async () => {
            jest.spyOn(
                APILinesDAO.prototype,
                "getTenantNnumbers",
            ).mockRejectedValue(
                new ConnectionTimedOutError(
                    new Error("test connection timeout"),
                ),
            );

            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510109);
        });

        test("should return CODE_510109 if getTenantNnumbers returns null", async () => {
            jest.spyOn(
                APILinesDAO.prototype,
                "getTenantNnumbers",
            ).mockResolvedValue(Promise.resolve(null));

            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510109);
        });

        test("should return CODE_510111 if tenant status is disabled", async () => {
            jest.spyOn(APICommonDAO.prototype, "getTenants").mockImplementation(
                async () => {
                    const badTenant = new TenantsEntity();
                    badTenant.status = false;
                    return Promise.resolve(badTenant);
                },
            );

            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510111);
        });

        test("should return CODE_510111 if failed retrieving target tenant entity", async () => {
            jest.spyOn(APICommonDAO.prototype, "getTenants").mockRejectedValue(
                new ConnectionTimedOutError(
                    new Error("test connection timeout"),
                ),
            );

            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510111);
        });

        test("should return CODE_510111 if tenant_nnumber not found", async () => {
            jest.spyOn(APILinesDAO.prototype, "getWholeFlag").mockResolvedValue(
                [],
            );

            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510111);
        });

        test("should return CODE_510111 if failed retrieving tenant_nnumber", async () => {
            jest.spyOn(APILinesDAO.prototype, "getWholeFlag").mockRejectedValue(
                new ConnectionTimedOutError(
                    new Error("test connection timeout"),
                ),
            );

            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510111);
        });

        describe("checkLineOptions", () => {
            const getLineOptionsInfo = async (lineOptionId: string) =>
                await LineOptionsEntity.findOne({
                    where: { lineOptionId },
                });
            const getTenantPlanLineOptionsInfo = async (
                tenantId: string,
                planId: string,
                lineOptionType: number,
            ) =>
                await TenantPlanLineOptionsEntity.findOne({
                    where: { tenantId, planId: +planId, lineOptionType },
                });

            test("should return CODE_510111 if failed to get line options info", async () => {
                const spy = jest.spyOn(
                    APILinesDAO.prototype,
                    "getLineOptionsInfo",
                );
                spy.mockImplementationOnce(getLineOptionsInfo);
                spy.mockImplementationOnce(getLineOptionsInfo);
                spy.mockRejectedValueOnce(
                    new ConnectionTimedOutError(
                        new Error("test connection timeout"),
                    ),
                );

                const result = await PreLineAddHandler.Handler(
                    DefaultRequest,
                    DefaultContext,
                );
                checkNGCode(result, ResultCdConstants.CODE_510111);
            });

            test("should return CODE_999999 if failed to get line options info (non DB error)", async () => {
                const spy = jest.spyOn(
                    APILinesDAO.prototype,
                    "getLineOptionsInfo",
                );
                spy.mockImplementationOnce(getLineOptionsInfo);
                spy.mockImplementationOnce(getLineOptionsInfo);
                spy.mockRejectedValueOnce(new Error("test some error"));

                const result = await PreLineAddHandler.Handler(
                    DefaultRequest,
                    DefaultContext,
                );
                checkNGCode(result, ResultCdConstants.CODE_999999);
            });

            test("should return CODE_510111 if line option not found", async () => {
                // this case should not be possible because of previous validations (inside `service`)
                const spy = jest.spyOn(
                    APILinesDAO.prototype,
                    "getLineOptionsInfo",
                );
                spy.mockImplementationOnce(getLineOptionsInfo);
                spy.mockImplementationOnce(getLineOptionsInfo);
                spy.mockResolvedValueOnce(null);

                const result = await PreLineAddHandler.Handler(
                    DefaultRequest,
                    DefaultContext,
                );
                checkNGCode(result, ResultCdConstants.CODE_510111);
            });

            test("should return CODE_510108 if tenant line option not found", async () => {
                // this case should not be possible because of previous validations (inside `service`)
                const spy = jest.spyOn(
                    APILinesDAO.prototype,
                    "getTenantPlanLineOptionsInfo",
                );
                spy.mockImplementationOnce(getTenantPlanLineOptionsInfo);
                spy.mockImplementationOnce(getTenantPlanLineOptionsInfo);
                spy.mockResolvedValueOnce(null);

                const result = await PreLineAddHandler.Handler(
                    DefaultRequest,
                    DefaultContext,
                );
                checkNGCode(result, ResultCdConstants.CODE_510108);
            });

            test("should return CODE_510108 if failed retrieving tenant line option", async () => {
                const spy = jest.spyOn(
                    APILinesDAO.prototype,
                    "getTenantPlanLineOptionsInfo",
                );
                spy.mockImplementationOnce(getTenantPlanLineOptionsInfo);
                spy.mockImplementationOnce(getTenantPlanLineOptionsInfo);
                spy.mockRejectedValueOnce(
                    new ConnectionTimedOutError(
                        new Error("test connection timeout"),
                    ),
                );

                const result = await PreLineAddHandler.Handler(
                    DefaultRequest,
                    DefaultContext,
                );
                checkNGCode(result, ResultCdConstants.CODE_510108);
            });

            test.skip("should return CODE_999999 if failed to get tenant line option (non DB error)", async () => {
                // TODO need to check this later (maybe require SOAP mock)
                const spy = jest.spyOn(
                    APILinesDAO.prototype,
                    "getTenantPlanLineOptionsInfo",
                );
                spy.mockImplementationOnce(getTenantPlanLineOptionsInfo);
                spy.mockImplementationOnce(getTenantPlanLineOptionsInfo);
                spy.mockRejectedValueOnce(new Error("test some error"));

                const result = await PreLineAddHandler.Handler(
                    DefaultRequest,
                    DefaultContext,
                );
                checkNGCode(result, ResultCdConstants.CODE_999999);
            });
        });
    });

    describe("NG (CODE_51xxxx) (フルMVNO)", () => {
        beforeEach(() => {
            DefaultContext.jsonBody.mnpInFlag = testdata.MNP_IN_TYPE_NO;
            DefaultContext.jsonBody.potalPlanID = testdata.plans.full;
            DefaultContext.jsonBody.cardTypeId = testdata.cardType.full;
            DefaultContext.jsonBody.sim_type = testdata.simType.full;
            delete DefaultContext.jsonBody.id_intlRoaming;
            delete DefaultContext.jsonBody.id_voicemail;
            delete DefaultContext.jsonBody.id_callWaiting;
            delete DefaultContext.jsonBody.id_intlCall;
            delete DefaultContext.jsonBody.id_forwarding;
            delete DefaultContext.jsonBody.id_intlForwarding;
        });

        const checkNGCode = (result: any, processCode: string) => {
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                processCode,
            );
        };

        test("should return CODE_510113 if failed to get full MVNO plan info", async () => {
            // first call is for all cases
            jest.spyOn(
                APILinesDAO.prototype,
                "getPlansInfoByPlanId",
            ).mockImplementationOnce(async (planId) => {
                return await PlansEntity.findOne({
                    where: { planId: +planId },
                });
            });
            jest.spyOn(
                APILinesDAO.prototype,
                "getPlansInfoByPlanId",
            ).mockRejectedValueOnce(
                new ConnectionTimedOutError(new Error("test error (fm)")),
            );
            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510113);
        });

        test("should return CODE_510113 if imsi is empty", async () => {
            delete DefaultContext.jsonBody.imsi;
            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510113);
        });

        test("should return CODE_510113 if imsi length > 15", async () => {
            DefaultContext.jsonBody.imsi = "1234567890123456";
            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510113);
        });

        test("should return CODE_510113 if puk1 is empty", async () => {
            delete DefaultContext.jsonBody.puk1;
            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510113);
        });

        test("should return CODE_510113 if puk1 length > 8", async () => {
            DefaultContext.jsonBody.puk1 = "123456789";
            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510113);
        });

        test("should return CODE_510113 if puk1 is not numeric", async () => {
            DefaultContext.jsonBody.puk1 = "123abc45";
            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510113);
        });

        test("should return CODE_510113 if puk2 is empty", async () => {
            delete DefaultContext.jsonBody.puk2;
            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510113);
        });

        test("should return CODE_510113 if puk2 length > 8", async () => {
            DefaultContext.jsonBody.puk2 = "123456789";
            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510113);
        });

        test("should return CODE_510113 if puk2 is not numeric", async () => {
            DefaultContext.jsonBody.puk2 = "123abc45";
            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510113);
        });

        test("should return CODE_510113 if lineStatus value is not valid", async () => {
            DefaultContext.jsonBody.lineStatus = "3";
            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510113);
        });

        test("should return CODE_510115 if checkTpcConnection fails to query", async () => {
            jest.spyOn(
                TenantManage.prototype,
                "checkTpcConnection",
            ).mockImplementation(async () => {
                throw new ConnectionTimedOutError(new Error("timeout error"));
            });
            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510115);
        });

        test("should return CODE_510115 if checkTpcConnection returns null", async () => {
            jest.spyOn(
                TenantManage.prototype,
                "checkTpcConnection",
            ).mockReturnValue(Promise.resolve([false, null, null]));
            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510115);
        });

        test("should return 510110 if serviceId in plansEntity is empty", async () => {
            jest.spyOn(
                APILinesDAO.prototype,
                "getPlansInfoByPlanId",
            ).mockResolvedValue(
                PlansEntity.build({
                    servicePlanId: null,
                }),
            );
            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510110);
        });

        test("should return 510110 if getLineInfoByLines fails to query", async () => {
            jest.spyOn(
                APILinesDAO.prototype,
                "getLineInfoByLines",
            ).mockImplementation(async () => {
                throw new ConnectionTimedOutError(new Error("timeout error"));
            });
            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510401);
        });

        test("should return 510110 if getLineInfoByLines gets an unexpected error", async () => {
            jest.spyOn(
                APILinesDAO.prototype,
                "getLineInfoByLines",
            ).mockRejectedValue(new TypeError("some error"));
            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510401);
        });

        test("should return 510110 if gets no planInfo or card info", async () => {
            jest.spyOn(
                APILinesDAO.prototype,
                "getPortalPlanIdInfo",
            ).mockResolvedValue(null);
            jest.spyOn(
                APILinesDAO.prototype,
                "getCardTypeInfo",
            ).mockResolvedValue(null);
            const result = await PreLineAddHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_510401);
        });
    });
});
