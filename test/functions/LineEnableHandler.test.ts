import "@/types/string.extension";

import { ConnectionTimedOutError, Sequelize } from "sequelize";
import { addDays, formatDate } from "date-fns";

const enqueueMessage = jest.fn();
jest.mock("../../src/services/servicebus", () => ({
    __esModule: true,
    default: enqueueMessage,
}));

import DefaultContext from "../testing/DefaultContext";
import DefaultRequest from "../testing/DefaultRequest";
import {
    describeWithDB,
    generateProcessID,
    generateSOID,
    useFasterRetries,
} from "../testing/TestHelper";

import ResultCdConstants from "@/core/constant/ResultCdConstants";

import { usePsql } from "@/database/psql";
import { disconnectMongo, useMongo } from "@/database/mongo";

import ApiCommon from "@/core/common/ApiCommon";
import SOCommon from "@/core/common/SOCommon";
import TenantManage from "@/core/common/TenantManage";

import { CoreSwimmyStatus } from "@/constants/coreSwimmy";
import { CoreSwimmyLineEnableRequest } from "@/types/coreSwimmyService";

import AppConfig from "@/appconfig";
import APILinesDAO from "@/core/dao/APILinesDAO";

import LineEnableInputDto from "@/core/dto/LineEnableInputDto";
import ResponseHeader from "@/core/dto/ResponseHeader";

import { LinesEntity } from "@/core/entity/LinesEntity";
import ServiceOrdersEntity from "@/core/entity/ServiceOrdersEntity";
import { TenantNnumbersEntity } from "@/core/entity/TenantNnumbersEntity";
import CoreSwimmyApiLog from "@/models/coreSwimmyApiLog";

import { LineEnableHandler } from "@/functions/LineEnableHandler";

describeWithDB("55 回線黒化 /V100/Lines/enable", () => {
    let sequelize: Sequelize;

    const testdata = {
        lines: {
            exist: "07024100201",
            notexists: "07024100202",
            abone: "07024100203",
            simFlag: "07024100204", // simFlag = true
            reserved: "07024100205", // 廃止オーダー受付中
            tenantNG: "07024100206", // nnumber mismatch
        },
        nBan: {
            tst: "",
            tsa: "",
        },
        reserveSOID: generateProcessID(),
        reservedLineSOID: generateProcessID(),
        sameMvneChain: generateProcessID(),
        cardId: "A99",
    };

    const __request: LineEnableInputDto = {
        targetSoId: generateSOID(),
        requestHeader: {
            sequenceNo: "1234567",
            senderSystemId: "0001",
            apiKey: "",
            functionType: "51",
        },
        reserve_flag: null,
        reserve_soId: null,
        tenantId: "OPF000",
        targetTenantId: "TST000",
        lineNo: testdata.lines.exist,
        chargeDate: undefined,
        csvUnnecessaryFlag: "0",
        sameMvneInFlag: "0",
    };

    const responseHeader = {
        sequenceNo: __request.requestHeader.sequenceNo,
        receivedDate: formatDate(new Date(), "yyyy/MM/dd HH:mm:ss"),
        processCode: ResultCdConstants.CODE_000000,
        apiProcessID: generateProcessID(),
    } as ResponseHeader;

    async function prepareTestData() {
        const nnumber = await Promise.all([
            LinesEntity.create({
                lineId: testdata.lines.exist,
                lineStatus: "01",
                nnumber: testdata.nBan.tst,
                simFlag: true,
                deviceTypeId: testdata.cardId,
            }),
            LinesEntity.create({
                lineId: testdata.lines.abone,
                lineStatus: "03",
                nnumber: testdata.nBan.tst,
                simFlag: true,
                deviceTypeId: testdata.cardId,
            }),
            LinesEntity.create({
                lineId: testdata.lines.simFlag,
                lineStatus: "01",
                nnumber: testdata.nBan.tst,
                simFlag: true,
                deviceTypeId: testdata.cardId,
            }),
            LinesEntity.create({
                lineId: testdata.lines.reserved,
                lineStatus: "01",
                nnumber: testdata.nBan.tst,
                simFlag: true,
                lineActDate: addDays(new Date(), -2), // today - 1 day
                deviceTypeId: testdata.cardId,
            }),
            LinesEntity.create({
                lineId: testdata.lines.tenantNG,
                lineStatus: "01",
                nnumber: testdata.nBan.tsa,
                simFlag: false,
                deviceTypeId: testdata.cardId,
            }),
        ]);
        // service order for reserved line
        await ServiceOrdersEntity.create({
            serviceOrderId: testdata.reservedLineSOID,
            lineId: testdata.lines.reserved,
            orderStatus: "予約中",
            functionType: "52",
            reserveDate: addDays(new Date(), -1), // today + 10 days
        });
    }

    async function clearTestData() {
        await Promise.all([
            LinesEntity.destroy({
                where: { lineId: Object.values(testdata.lines) },
            }),
            ServiceOrdersEntity.destroy({
                where: {
                    serviceOrderId: [
                        responseHeader.apiProcessID,
                        testdata.reservedLineSOID,
                    ],
                },
            }),
        ]);
        await CoreSwimmyApiLog.deleteMany({
            requestOrderId: [
                responseHeader.apiProcessID,
                testdata.sameMvneChain,
            ],
        });
    }

    async function checkCoreSwimmyApiLog(
        apiProcessID: string,
        exists: boolean,
        startDate: string = null,
        status: CoreSwimmyStatus = CoreSwimmyStatus.TOUROKU_IRAI_NOT_YET_SENT,
    ) {
        const log = await CoreSwimmyApiLog.findOne({
            requestOrderId: apiProcessID,
        });
        if (exists) {
            expect(log).not.toBeNull();
            expect(log.status).toEqual(status);

            let date =
                startDate ??
                responseHeader.receivedDate.substring(0, 10).replace(/\//g, "");
            expect(/^\d{8}$/.test(date)).toBe(true); // should be in yyyyMMdd format

            const requestObj = log.requestParam as CoreSwimmyLineEnableRequest;
            expect(requestObj).not.toBeNull();
            expect(requestObj).not.toBeUndefined();
            expect(requestObj.appRequestInfo).not.toBeUndefined();
            expect(requestObj.appRequestInfo.appInfoList).toHaveLength(1);

            const appInfoList = requestObj.appRequestInfo.appInfoList[0];
            expect(appInfoList?.appBasic?.appDate).toEqual(date);
            expect(appInfoList?.appBasic?.requestDate).toEqual(date);

            expect(appInfoList?.appPrdtList).toHaveLength(1);
            const appPrdt = appInfoList.appPrdtList[0];
            const activateDate = (appPrdt.appPrdtDtlList ?? []).find(
                (r) => r.prdtAttributeCode === "00000009",
            );
            expect(activateDate?.prdtAttributeValue).toEqual(date);
        } else {
            expect(log).toBeNull();
        }
    }

    beforeAll(async () => {
        await AppConfig.loadCoreMvnoConfig(DefaultContext);
        sequelize = await usePsql();
        useMongo(DefaultContext);

        testdata.nBan.tst = (
            await TenantNnumbersEntity.findOne({
                where: { tenantId: "TST000" },
            })
        )?.nnumber;
        testdata.nBan.tsa = (
            await TenantNnumbersEntity.findOne({
                where: { tenantId: "TSA000" },
            })
        )?.nnumber;
    });

    afterAll(async () => {
        await sequelize.close();
        await disconnectMongo(DefaultContext);
    });

    beforeEach(async () => {
        (enqueueMessage as jest.Mock).mockClear();
        await prepareTestData();

        DefaultContext.jsonBody = structuredClone(__request);
        DefaultContext.responseHeader = structuredClone(responseHeader);

        useFasterRetries();
    });

    afterEach(async () => {
        await clearTestData();

        jest.restoreAllMocks();
        jest.clearAllMocks();
    });

    describe("OK_CASES (CODE_000000)", () => {
        const checkResult = async (result: any, lineNo: string) => {
            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );

            const line = await LinesEntity.findOne({
                where: { lineId: lineNo },
            });
            expect(line).not.toBeNull();
            expect(line.lineStatus).toEqual("01");
            // status changed to 黒
            expect(line.simFlag).toBe(false);
        };

        test("should return CODE_000000 (sameMvneInFlag=0)", async () => {
            const result = await LineEnableHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );

            await checkResult(result, testdata.lines.exist);
            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, true);
        });

        test("should return CODE_000000 (sameMvneInFlag=1)", async () => {
            DefaultContext.jsonBody.sameMvneInFlag = "1";
            const result = await LineEnableHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );

            await checkResult(result, testdata.lines.exist);
            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, true);
        });

        test("should return CODE_000000 (chargeDate defined)", async () => {
            DefaultContext.jsonBody.chargeDate = formatDate(
                addDays(new Date(), 5),
                "yyyy/MM/dd",
            );
            const startDate = DefaultContext.jsonBody.chargeDate.replace(
                /\//g,
                "",
            );
            const result = await LineEnableHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );

            await checkResult(result, testdata.lines.exist);
            await checkCoreSwimmyApiLog(
                responseHeader.apiProcessID,
                true,
                startDate,
            );
        });

        test("should return CODE_000000 (csvUnnecessaryFlag=1)", async () => {
            DefaultContext.jsonBody.csvUnnecessaryFlag = "1";
            const result = await LineEnableHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );

            await checkResult(result, testdata.lines.exist);
            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
        });

        test("should return CODE_000000 and not creating CoreSwimmyApiLog (N番 not found)", async () => {
            jest.spyOn(TenantManage.prototype, "doCheck").mockResolvedValue([
                true,
                null,
            ]);
            DefaultContext.jsonBody.csvUnnecessaryFlag = "0"; // make sure it's not skipped
            const result = await LineEnableHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );

            await checkResult(result, testdata.lines.exist);
            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
        });
    });

    const checkNGCode = (result: any, processCode: string) => {
        expect(typeof result).toBe("object");
        expect(result).toHaveProperty("jsonBody");
        expect(result.jsonBody).toHaveProperty("responseHeader");
        expect(result.jsonBody.responseHeader.processCode).toEqual(processCode);
    };

    describe("NG_CASES (CODE_55xxxx)", () => {
        test("should return CODE_550101 if tenantId is not Front Tenant", async () => {
            DefaultContext.jsonBody.tenantId = "TSA000";

            const result = await LineEnableHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_550101);
            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
        });

        test("should return CODE_550102 if line status is '03' (deactivated)", async () => {
            DefaultContext.jsonBody.lineNo = testdata.lines.abone;

            const result = await LineEnableHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_550102);
            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
        });

        test("should return CODE_550102 if failed to find line info", async () => {
            DefaultContext.jsonBody.lineNo = testdata.lines.notexists;

            const result = await LineEnableHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_550102);
            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
        });

        test("should return CODE_550102 if failed to fetch line info", async () => {
            jest.spyOn(
                APILinesDAO.prototype,
                "getLineInfoByLines",
            ).mockRejectedValue(
                new ConnectionTimedOutError(
                    new Error("test connection timeout error"),
                ),
            );

            const result = await LineEnableHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_550102);
            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
        });

        test("should return CODE_550103 if tenant N番 and line mismatch", async () => {
            DefaultContext.jsonBody.lineNo = testdata.lines.tenantNG;

            const result = await LineEnableHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_550103);
            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
        });

        test("should return CODE_550103 if failed to fetch N番", async () => {
            jest.spyOn(TenantManage.prototype, "doCheck").mockRejectedValue(
                new ConnectionTimedOutError(
                    new Error("test connection timeout"),
                ),
            );

            const result = await LineEnableHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_550103);
            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
        });

        test("should return CODE_550104 if line has abolition order", async () => {
            DefaultContext.jsonBody.lineNo = testdata.lines.reserved;

            const result = await LineEnableHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_550104);
            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
        });

        test("should return CODE_550104 if failed to fetch abolition order", async () => {
            jest.spyOn(ApiCommon.prototype, "checkAbolishSo").mockRejectedValue(
                new ConnectionTimedOutError(
                    new Error("test connection timeout"),
                ),
            );

            const result = await LineEnableHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_550104);
            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
        });

        test("should return CODE_550201 if failed to update line simFlag (DB error)", async () => {
            jest.spyOn(
                APILinesDAO.prototype,
                "updateLineKurokaInfo",
            ).mockRejectedValue(
                new ConnectionTimedOutError(
                    new Error("test connection timeout"),
                ),
            );

            const result = await LineEnableHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_550201);
            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
        });

        test("should return CODE_550201 if failed to update line simFlag (non-DB error)", async () => {
            jest.spyOn(
                APILinesDAO.prototype,
                "updateLineKurokaInfo",
            ).mockRejectedValue(new Error("test error"));

            const result = await LineEnableHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_550201);
            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
        });

        test("should return CODE_999999 if unexpected error occurred", async () => {
            jest.spyOn(String.prototype, "equals").mockImplementationOnce(
                (_other: string) => {
                    throw new Error("test error");
                },
            );

            const result = await LineEnableHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_999999);
            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
        });

        test("should return CODE_999999 if unexpected error occurred (handler catch)", async () => {
            jest.spyOn(SOCommon.prototype, "soCommon").mockRejectedValue(
                new Error("test error"),
            );

            const result = await LineEnableHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_999999);
            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
        });
    });

    describe("NG cases specific to test env", () => {
        // NOTE in prod, there will be no case where card info is not found or it's T番 is null
        test("should return CODE_999999 if failed to get line's card info", async () => {
            jest.spyOn(
                APILinesDAO.prototype,
                "getCardTypeInfo",
            ).mockRejectedValue(new Error("some error"));

            const result = await LineEnableHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_999999);
            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
        });

        test("should return CODE_999999 if could not find line's card info", async () => {
            jest.spyOn(
                APILinesDAO.prototype,
                "getCardTypeInfo",
            ).mockResolvedValue(null);

            const result = await LineEnableHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkNGCode(result, ResultCdConstants.CODE_999999);
            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
        });
    });
});
