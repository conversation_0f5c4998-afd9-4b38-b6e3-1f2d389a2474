import { expect, test, beforeEach, describe, jest } from "@jest/globals";
import { addMinutes, formatDate } from "date-fns";
import { ConnectionTimedOutError, Sequelize } from "sequelize";
import { describeWithDB, generateProcessID, generateSOID } from "../testing/TestHelper";
import { usePsql } from "@/database/psql";
import { disconnectMongo, useMongo } from "@/database/mongo";
import ResultCdConstants from "@/core/constant/ResultCdConstants";
import DefaultContext from "../testing/DefaultContext";
import DefaultRequest from "../testing/DefaultRequest";
import defaultContext from "../testing/DefaultContext";
import { LinesEntity } from "@/core/entity/LinesEntity";
import LineTenantsEntity from "@/core/entity/LineTenantsEntity";
import defaultRequest from "../testing/DefaultRequest";
import { LineInfoAcquisitionHandler } from "@/functions/LineInfoAcquisitionHandler";
import { HttpResponseInit } from "@azure/functions";
import MvnoUtil from "@/core/common/MvnoUtil";
import { TenantsEntity } from "@/core/entity/TenantsEntity";
import APICommonDAO from "@/core/dao/APICommonDAO";
import TenantManage from "@/core/common/TenantManage";
import APILinesDAO from "@/core/dao/APILinesDAO";
import PlansEntity from "@/core/entity/PlansEntity";
import CheckUtil from "@/core/common/CheckUtil";
import LineInfoAcquisitionService from "@/core/service/impl/LineInfoAcquisitionService";
import AppConfig from "@/appconfig";

describeWithDB("01 回線基本情報参照機能 /MVNO/api/v100/Lines/info", () => {
    Object.defineProperty(LineInfoAcquisitionHandler, "apDbRetryInterval", { value: 0.01 }); // 10ms
    let sequelize: Sequelize;

    const testdata = {
        lineOK: "08024043001",
        lineNotExists: "08024043002",
        abolishSo: generateProcessID(),
        // reserveDate is 5 days after today
        reserveDate: addMinutes(new Date(), 1 * 24 * 60),
    };

    const deviceId = {
        lte: "AD7",
        sms: "AE0",
        voice: "AW9",
    };

    const request = {
        requestHeader: {
            sequenceNo: "1234567",
            senderSystemId: "0001",
            apiKey: "",
            functionType: "01",
            receivedDate: formatDate(new Date(), "yyyy/MM/dd HH:mm:ss"),
        },
        lineNo: testdata.lineOK,
        tenantId: "TST000"
    };

    const lineData = {
        lineId: testdata.lineOK,
        lineStatus: "01",
        nnumber: "N121152174", // NOTE TST000's nnumber
        groupId: "800001",
        lineActDate: new Date(),
        simNumber: "DN999999999999",
        modelType: "SIMのみ LTE SIM",
        imei: "PSIDimei",
        startDate: new Date(),
        domain: "domain",
        authId: "authId",
        actEastIp: "***********",
        actWestIp: "***********",
        sbyIp: "***********",
        fixedIp: "***********",
        pricePlanId: "AR550",
        pricePlanName: "[docomo] C_OCN卸プラン 先行/後発_日次 LTE(音声)",
        poi: "東POI接続（222）",
        createdAt: new Date(),
        updatedAt: new Date(),
        modifyFlag: false,
        contractType: "LTE(音声)",
        simType: "nanoSIM",
        deviceModelName: "UX302NC",
        authPattern: "1",
        imeisv_2: "S2001114",
        imeisv_3: "S2003011",
        notes: "notes",
        tempRegist: false,
        serviceDate: new Date(),
        roamingMaxId: "AO105",
        voiceMailId: null,
        callWaitingId: "AO150",
        intlCallId: "AO122",
        forwardingId: "AO144",
        intlForwardingId: "AO149",
        deviceTypeId: "A11",
        simFlag: false,
        activateDate: new Date(),
        imsi: "123456789012345",
        puk1: "87654321",
        puk2: "98765432",
        usageStatus: 0,
        nwModifyFlag: false,
        voicePlanId: "BtiredPlan",
        voicePlanName: "従量プラン",
        tenantId: request.tenantId,
    } as LinesEntity;

    const returnLineData = {
        ...lineData,
        callWaiting: "[docomo] キャッチホン ID卸",
        forwarding: "[docomo] 転送電話",
        interationalRoamingCreditLine: "[docomo] 国際ローミング[5万円]",
        intlCall: "[docomo] 国際電話[5千円]",
        intlForwarding: "[docomo] 国際着信転送電話",
        voicemail: "",
        potalPlanName: "[docomo] C_OCN卸プラン 先行/後発_日次 LTE(音声)",
        potalPlanID: "10023",       // SELECT plan_id FROM plans WHERE resale_plan_id = 'AR550';
    } as any;

    const responseHeader = {
        sequenceNo: request.requestHeader.sequenceNo,
        receivedDate: request.requestHeader.receivedDate,
        processCode: ResultCdConstants.CODE_000000,
        apiProcessID: generateProcessID(),
    };

    beforeAll(async () => {
        await AppConfig.loadCoreMvnoConfig(DefaultContext);
        sequelize = await usePsql();
        await useMongo(DefaultContext);
        // insert line number
        await LinesEntity.create(lineData);

        await LineTenantsEntity.create({
            lineId: testdata.lineOK,
            tenantId: request.tenantId,
        });
    });

    afterAll(async () => {
        await LineTenantsEntity.destroy({
            where: {
                lineId: testdata.lineOK,
            },
        });
        await LinesEntity.destroy({
            where: {
                lineId: testdata.lineOK,
            },
        });
        await sequelize.close();
        await disconnectMongo(DefaultContext);
    });

    beforeEach(async () => {
        defaultContext.jsonBody = Object.assign({}, request);
        defaultContext.responseHeader = Object.assign({}, responseHeader);
        jest.spyOn(MvnoUtil, "getDateTimeNow").mockReturnValue(responseHeader.receivedDate);
    });

    afterEach(async () => {
        jest.restoreAllMocks();
        jest.clearAllMocks();
    });

    const checkGroupAContent = (result: HttpResponseInit, is_empty: boolean, version: string = null) => {
        if (is_empty) {
            let json: any = {
                lineNo: "",
                tenantId: "",
                nBan: "",
                potalPlanID: "",
                potalPlanName: "",
                lineGroupID: "",
                cardClassificationName: "",
                lineUseStartDate: "",
                interationalRoamingCreditLine: "",
                contractType: "",
                voicemail: "",
                callWaiting: "",
                intlCall: "",
                forwarding: "",
                intlForwarding: "",
                lineRegStatus: "",
                simStatus: "",
                activateDate: "",
            }
            if (version === "1") {
                json = {
                    ...json,
                    simNo: "",
                    imsi: "",
                    puk1: "",
                    puk2: "",
                }
            } else if (version === "2") {
                json = {
                    ...json,
                    voicePlanId: "",
                    voicePlanName: "",
                }
            }
            expect(result.jsonBody.groupA).toEqual(json);
        } else {
            let json: any = {
                lineNo: returnLineData.lineId ?? "",
                tenantId: returnLineData.tenantId ?? "",
                nBan: returnLineData.nnumber ?? "",
                potalPlanID: returnLineData.potalPlanID ?? "",
                potalPlanName: returnLineData.potalPlanName ?? "",
                lineGroupID: returnLineData.lineGroupId ?? "",
                cardClassificationName: returnLineData.simType ?? "",
                lineUseStartDate: formatDate(returnLineData.startDate, "yyyy/MM/dd") ?? "",
                interationalRoamingCreditLine: returnLineData.interationalRoamingCreditLine ?? "",
                contractType: returnLineData.contractType ?? "",
                voicemail: returnLineData.voicemail ?? "",
                callWaiting: returnLineData.callWaiting ?? "",
                intlCall: returnLineData.intlCall ?? "",
                forwarding: returnLineData.forwarding ?? "",
                intlForwarding: returnLineData.intlForwarding ?? "",
                lineRegStatus: String(returnLineData.usageStatus ?? ""),
                simStatus: String(Number(returnLineData.simFlag ?? "")),
                activateDate: formatDate(returnLineData.activateDate, "yyyy/MM/dd") ?? "",
            }
            if (version === "1") {
                json = {
                    ...json,
                    simNo: returnLineData.simNumber ?? "",
                    imsi: returnLineData.imsi ?? "",
                    puk1: returnLineData.puk1 ?? "",
                    puk2: returnLineData.puk2 ?? "",
                }
            } else if (version === "2") {
                json = {
                    ...json,
                    voicePlanId: returnLineData.voicePlanId ?? "",
                    voicePlanName: returnLineData.voicePlanName ?? "",
                }
            }
            expect(result.jsonBody.groupA).toEqual(json);
        }
    }

    const checkGroupBContent = (result: HttpResponseInit, is_empty: boolean, addOption: string) => {
        if (addOption !== "1" && addOption !== '9') {
            expect(result.jsonBody.groupB).toBeUndefined();
        } else {
            if (is_empty) {
                expect(result.jsonBody.groupB).toEqual({
                    simNo: "",
                    terminalClassificationName: "",
                    imei: "",
                    terminalUseStartDate: ""
                });
            } else {
                expect(result.jsonBody.groupB).toEqual({
                    simNo: returnLineData.simNumber ?? "",
                    terminalClassificationName: returnLineData.modelType ?? "",
                    imei: returnLineData.imei ?? "",
                    terminalUseStartDate: formatDate(returnLineData.startDate, "yyyy/MM/dd") ?? "",
                });
            }
        }
    };

    const checkGroupCContent = (result: HttpResponseInit, is_empty: boolean, addOption: string) => {
        if (addOption !== "9") {
            expect(result.jsonBody.groupC).toBeUndefined();
        } else {
            if (is_empty) {
                expect(result.jsonBody.groupC).toEqual({
                    domain: "",
                    attestationId: "",
                    kokunaiyoIpACTeast: "",
                    kokunaiyoIpACTwest: "",
                    kokunaiyoIpACTSBY: "",
                    interationalIpAddress: "",
                    planId: "",
                    planName: "",
                    poiInfomation: "",
                    authPattern: "",
                    deviceModelName: "",
                    imeisv_2: "",
                    imeisv_3: "",
                    notes: ""
                });
            } else {
                expect(result.jsonBody.groupC).toEqual({
                    domain: returnLineData.domain ?? "",
                    attestationId: returnLineData.authId ?? "",
                    kokunaiyoIpACTeast: returnLineData.actEastIp ?? "",
                    kokunaiyoIpACTwest: returnLineData.actWestIp ?? "",
                    kokunaiyoIpACTSBY: returnLineData.sbyIp ?? "",
                    interationalIpAddress: returnLineData.fixedIp ?? "",
                    planId: returnLineData.pricePlanId ?? "",
                    planName: returnLineData.pricePlanName ?? "",
                    poiInfomation: returnLineData.poi ?? "",
                    authPattern: returnLineData.authPattern ?? "",
                    deviceModelName: returnLineData.deviceModelName ?? "",
                    imeisv_2: returnLineData.imeisv_2 ?? "",
                    imeisv_3: returnLineData.imeisv_3 ?? "",
                    notes: returnLineData.notes ?? "",
                });
            }
        }
    };

    const checkResponseHeader = (result: HttpResponseInit, expected_process_code: string = ResultCdConstants.CODE_000000) => {
        expect(result.jsonBody.responseHeader).toEqual({
            processCode: expected_process_code,
            apiProcessID: responseHeader.apiProcessID,
            sequenceNo: request.requestHeader.sequenceNo,
            receivedDate: responseHeader.receivedDate,
        });
    };

    const versionAddOptionArray = [
        {version: null, addOption: null},
        {version: "1", addOption: null},
        {version: "2", addOption: null},
        {version: null, addOption: "1"},
        {version: "1", addOption: "1"},
        {version: "2", addOption: "1"},
        {version: null, addOption: "9"},
        {version: "1", addOption: "9"},
        {version: "2", addOption: "9"},
    ];

    describe("CommonCheckFailedHandler", () => {
        test.each(versionAddOptionArray)('CommonCheckFailedHandler with %s',async ({ version, addOption }) => {
            defaultContext.jsonBody = Object.assign({}, {
                ...request,
                addOption,
                version
            });
            defaultContext.responseHeader = Object.assign({}, { ...responseHeader, processCode: ResultCdConstants.CODE_999999 });
            const response = await LineInfoAcquisitionHandler.CommonCheckFailedHandler(defaultRequest, defaultContext, "フォーマットチェックエラーが発生しました。");
            checkResponseHeader(response, ResultCdConstants.CODE_999999);
            checkGroupAContent(response, true, version);
            checkGroupBContent(response, true, addOption);
            checkGroupCContent(response, true, addOption);
        });
    });

    describe("CommonCheckCatchErrorHandler", () => {
        test.each(versionAddOptionArray)('CommonCheckCatchErrorHandler with %s',async ({ version, addOption }) => {
            defaultContext.jsonBody = Object.assign({}, {
                ...request,
                addOption,
                version
            });
            defaultContext.responseHeader = Object.assign({}, { ...responseHeader, processCode: ResultCdConstants.CODE_999999 });
            const response = await LineInfoAcquisitionHandler.CommonCheckCatchErrorHandler(defaultRequest, defaultContext, new Error("test error"));
            checkResponseHeader(response, ResultCdConstants.CODE_999999);
            checkGroupAContent(response, true, version);
            checkGroupBContent(response, true, addOption);
            checkGroupCContent(response, true, addOption);
        });
    });

    describe("Handler with CODE_0101xx error", () => {
        describe("Should return CODE_010101 if line length is not 11 or 13", () => {
            test.each(versionAddOptionArray)('CODE_010101 with %s',async ({ version, addOption }) => {
                defaultContext.jsonBody = Object.assign({}, {
                    ...request,
                    lineNo: "1234567890",
                    addOption,
                    version
                });
                defaultContext.responseHeader = Object.assign({}, responseHeader);
                const response = await LineInfoAcquisitionHandler.Handler(defaultRequest, defaultContext);
                checkResponseHeader(response, ResultCdConstants.CODE_010101);
                checkGroupAContent(response, true, version);
                checkGroupBContent(response, true, addOption);
                checkGroupCContent(response, true, addOption);
            });
        });

        describe("Should return CODE_010101 if invalid addOption", () => {
            test.each([
                {version: null},
                {version: "1"},
                {version: "2"},
            ])('CODE_010101 with %s',async ({ version }) => {
                const addOption = "2";
                defaultContext.jsonBody = Object.assign({}, {
                    ...request,
                    addOption,
                    version
                });
                defaultContext.responseHeader = Object.assign({}, responseHeader);
                const response = await LineInfoAcquisitionHandler.Handler(defaultRequest, defaultContext);
                checkResponseHeader(response, ResultCdConstants.CODE_010101);
                checkGroupAContent(response, true, version);
                checkGroupBContent(response, true, addOption);
                checkGroupCContent(response, true, addOption);
            });
        });

        describe("Should return CODE_010101 if invalid version", () => {
            test.each([
                {addOption: null},
                {addOption: "1"},
                {addOption: "9"},
            ])('CODE_010101 with %s',async ({ addOption }) => {
                const version = "9";
                defaultContext.jsonBody = Object.assign({}, {
                    ...request,
                    addOption,
                    version
                });
                defaultContext.responseHeader = Object.assign({}, responseHeader);
                const response = await LineInfoAcquisitionHandler.Handler(defaultRequest, defaultContext);
                checkResponseHeader(response, ResultCdConstants.CODE_010101);
                checkGroupAContent(response, true, version);
                checkGroupBContent(response, true, addOption);
                checkGroupCContent(response, true, addOption);
            });
        });

        describe("Should return CODE_010102 if line does not belongs to tenant", () => {
            test.each(versionAddOptionArray)('CODE_010102 with %s',async ({ version, addOption }) => {
                defaultContext.jsonBody = Object.assign({}, {
                    ...request,
                    tenantId: request.tenantId + "1",
                    addOption,
                    version
                });
                const response = await LineInfoAcquisitionHandler.Handler(defaultRequest, defaultContext);
                checkResponseHeader(response, ResultCdConstants.CODE_010102);
                checkGroupAContent(response, true, version);
                checkGroupBContent(response, true, addOption);
                checkGroupCContent(response, true, addOption);
            });
        });

        describe("Should return CODE_010102 if doCheck fails to query", () => {
            test('CODE_010102 with %s',async () => {
                defaultContext.jsonBody = Object.assign({}, {
                    ...request,
                    tenantId: request.tenantId + "1",
                    addOption: null,
                    version: null
                });
                jest.spyOn(TenantManage.prototype,"doCheck").mockImplementation(async () =>{
                    throw new ConnectionTimedOutError(new Error("timeout error"));
                })
                const response = await LineInfoAcquisitionHandler.Handler(defaultRequest, defaultContext);
                checkResponseHeader(response, ResultCdConstants.CODE_010102);
                checkGroupAContent(response, true, null);
                checkGroupBContent(response, true, null);
                checkGroupCContent(response, true, null);
            });
        });
        
        describe("For addOption = 9, should return CODE_010103 if tenants office is false", () => {
            test.each([
                {version: null},
                {version: "1"},
                {version: "2"},
            ])('CODE_010103 with %s',async ({ version }) => {
                const addOption = "9";
                defaultContext.jsonBody = Object.assign({}, {
                    ...request,
                    addOption,
                    version
                });
                const response = await LineInfoAcquisitionHandler.Handler(defaultRequest, defaultContext);
                checkResponseHeader(response, ResultCdConstants.CODE_010103);
                checkGroupAContent(response, true, version);
                checkGroupBContent(response, true, addOption);
                checkGroupCContent(response, true, addOption);
            });
        });

        describe("For addOption != 9, should return anything but CODE_010103 if tenants office is false", () => {
            test.each([
                {version: null, addOption: null},
                {version: "1", addOption: null},
                {version: "2", addOption: null},
                {version: null, addOption: "1"},
                {version: "1", addOption: "1"},
                {version: "2", addOption: "1"},
            ])('CODE_010103 with %s',async ({ version, addOption }) => {
                defaultContext.jsonBody = Object.assign({}, {
                    ...request,
                    addOption,
                    version
                });
                const response = await LineInfoAcquisitionHandler.Handler(defaultRequest, defaultContext);
                expect(response.jsonBody.responseHeader.processCode).not.toBe(ResultCdConstants.CODE_010103);
            });
        });

        describe("For addOption = 9", () => {
            test('should return CODE_010103 if getTenants fails to query',async () => {
                const addOption = "9";
                defaultContext.jsonBody = Object.assign({}, {
                    ...request,
                    addOption,
                    version:null,
                });
                jest.spyOn(APICommonDAO.prototype,"getTenants").mockImplementation(async ()=>{
                    throw new ConnectionTimedOutError(new Error("timeout error"));
                })
                const response = await LineInfoAcquisitionHandler.Handler(defaultRequest, defaultContext);
                checkResponseHeader(response, ResultCdConstants.CODE_010103);
            });
        }); 
    });

    describe("Handler with CODE_0102xx ERROR", () => {
        beforeEach(async () => {
            jest.spyOn(APICommonDAO.prototype, 'getTenants').mockImplementation(async () => {
                return TenantsEntity.build({
                    tenantId: request.tenantId,
                    office: true,
                });
            });
            jest.spyOn(TenantManage.prototype, 'doCheck').mockImplementation(async () => {
                return [true, ""] as [boolean, string];
            });
        });

        describe("Should return CODE_010201 if line does not exists (although passed CODE_010102 check)", () => {
            test.each(versionAddOptionArray)('CODE_010102 with %s',async ({ version, addOption }) => {
                defaultContext.jsonBody = Object.assign({}, {
                    ...request,
                    lineNo: testdata.lineNotExists,
                    addOption,
                    version
                });
                const response = await LineInfoAcquisitionHandler.Handler(defaultRequest, defaultContext);
                checkResponseHeader(response, ResultCdConstants.CODE_010201);
                checkGroupAContent(response, true, version);
                checkGroupBContent(response, true, addOption);
                checkGroupCContent(response, true, addOption);
            });
        });

        describe("Should return CODE_010201 if getLineInfo fails to query", () => {
            test.each(versionAddOptionArray)('CODE_010102 with %s',async ({ version, addOption }) => {
                defaultContext.jsonBody = Object.assign({}, {
                    ...request,
                    lineNo: testdata.lineNotExists,
                    addOption,
                    version
                });
                jest.spyOn(APILinesDAO.prototype,"getLineInfo").mockImplementation(async ()=> {
                    throw new ConnectionTimedOutError(new Error("timeout error"));
                })
                const response = await LineInfoAcquisitionHandler.Handler(defaultRequest, defaultContext);
                checkResponseHeader(response, ResultCdConstants.CODE_010201);
                checkGroupAContent(response, true, version);
                checkGroupBContent(response, true, addOption);
                checkGroupCContent(response, true, addOption);
            });
        });

        describe("Should return CODE_010201 if more than one price plan", () => {
            beforeEach(async () => {
                jest.spyOn(APILinesDAO.prototype, 'getPlanInfo').mockImplementation(async () => {
                    return [{
                        planId: 10001,
                        pricePlanId: lineData.pricePlanId,
                    }, {
                        planId: 10002,
                        pricePlanId: lineData.pricePlanId,
                    }] as PlansEntity[];
                });
            })

            test.each(versionAddOptionArray)('CODE_010201 with %s',async ({ version, addOption }) => {
                defaultContext.jsonBody = Object.assign({}, {
                    ...request,
                    addOption,
                    version
                });
                const response = await LineInfoAcquisitionHandler.Handler(defaultRequest, defaultContext);

                checkResponseHeader(response, ResultCdConstants.CODE_010201);
                checkGroupAContent(response, true, version);
                checkGroupBContent(response, true, addOption);
                checkGroupCContent(response, true, addOption);
            });
        });

        describe("Should return CODE_010201 if getLineOptionsName is null", () => {
            beforeEach(async () => {
                jest.spyOn(APILinesDAO.prototype, 'getLineOptionsName').mockImplementation(async () => {
                    return null;
                });
            });

            test.each(versionAddOptionArray)('CODE_010201 with %s',async ({ version, addOption }) => {
                defaultContext.jsonBody = Object.assign({}, {
                    ...request,
                    addOption,
                    version
                });
                const response = await LineInfoAcquisitionHandler.Handler(defaultRequest, defaultContext);

                checkResponseHeader(response, ResultCdConstants.CODE_010201);
                checkGroupAContent(response, true, version);
                checkGroupBContent(response, true, addOption);
                checkGroupCContent(response, true, addOption);
            });
        });

        describe("Should return CODE_010201 if getLineOptionsName fails to query", () => {
            beforeEach(async () => {
                jest.spyOn(APICommonDAO.prototype, 'getTenants').mockImplementation(async () => {
                    return TenantsEntity.build({
                        tenantId: request.tenantId,
                        office: true,
                    });
                });
                jest.spyOn(TenantManage.prototype, 'doCheck').mockImplementation(async () => {
                    return [true, ""] as [boolean, string];
                });
            });
            test.each(versionAddOptionArray)('CODE_000000 with %s',async ({ version, addOption }) => {
                defaultContext.jsonBody = Object.assign({}, {
                    ...request,
                    addOption,
                    version
                });
                jest.spyOn(APILinesDAO.prototype,"getLineOptionsName").mockImplementation(async ()=> {
                    throw new ConnectionTimedOutError(new Error("timeout error"));
                })
                const response = await LineInfoAcquisitionHandler.Handler(defaultRequest, defaultContext);
                checkResponseHeader(response, ResultCdConstants.CODE_010201);
            });
            
        });
    });

    describe("Handler with CODE_000000 (success)", () => {
        beforeEach(async () => {
            jest.spyOn(APICommonDAO.prototype, 'getTenants').mockImplementation(async () => {
                return TenantsEntity.build({
                    tenantId: request.tenantId,
                    office: true,
                });
            });
            jest.spyOn(TenantManage.prototype, 'doCheck').mockImplementation(async () => {
                return [true, ""] as [boolean, string];
            });
        });

        describe("Should return response successfully, with values inside", () => {
            test.each(versionAddOptionArray)('CODE_000000 with %s',async ({ version, addOption }) => {
                defaultContext.jsonBody = Object.assign({}, {
                    ...request,
                    addOption,
                    version
                });
                const response = await LineInfoAcquisitionHandler.Handler(defaultRequest, defaultContext);
                checkResponseHeader(response, ResultCdConstants.CODE_000000);
                checkGroupAContent(response, false, version);
                checkGroupBContent(response, false, addOption);
                checkGroupCContent(response, false, addOption);
            });

            test.each(versionAddOptionArray)('CODE_000000 with %s in number',async ({ version, addOption }) => {
                defaultContext.jsonBody = Object.assign({}, {
                    ...request,
                    addOption: addOption ? parseInt(addOption, 10) : addOption,
                    version: version ? parseInt(version, 10) : version,
                });
                const response = await LineInfoAcquisitionHandler.Handler(defaultRequest, defaultContext);
                checkResponseHeader(response, ResultCdConstants.CODE_000000);
                checkGroupAContent(response, false, version);
                checkGroupBContent(response, false, addOption);
                checkGroupCContent(response, false, addOption);
            });
        });
        test("Should failure with one error", async () => {
            jest.spyOn(LineInfoAcquisitionService.prototype, 'service').mockImplementationOnce(async () => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });
            defaultContext.jsonBody = Object.assign({}, {
                ...request
            });
            const response = await LineInfoAcquisitionHandler.Handler(defaultRequest, defaultContext);
            checkResponseHeader(response, ResultCdConstants.CODE_999999);
        });

        test("Should return CODE_999999 if unexpected happen error", async () => {
            defaultContext.jsonBody = Object.assign({}, {
                ...request
            });
            jest.spyOn(APILinesDAO.prototype,"getLineOptionsName").mockRejectedValue(new TypeError("some error"));
            const response = await LineInfoAcquisitionHandler.Handler(defaultRequest, defaultContext);
            checkResponseHeader(response, ResultCdConstants.CODE_999999);
        });

        test("Should return CODE_999999 if unexpected happen error", async () => {
            defaultContext.jsonBody = Object.assign({}, {
                ...request
            });
            jest.spyOn(APILinesDAO.prototype,"getPlanInfo").mockRejectedValue(new TypeError("some error"));
            const response = await LineInfoAcquisitionHandler.Handler(defaultRequest, defaultContext);
            checkResponseHeader(response, ResultCdConstants.CODE_999999);
        });

        test("Should return CODE_999999 if unexpected happen error", async () => {
            defaultContext.jsonBody = Object.assign({}, {
                ...request
            });
            jest.spyOn(APILinesDAO.prototype,"getLinesGroupId").mockRejectedValue(new TypeError("some error"));
            const response = await LineInfoAcquisitionHandler.Handler(defaultRequest, defaultContext);
            checkResponseHeader(response, ResultCdConstants.CODE_999999);
        });
    });
});