import AutoLineGroup<PERSON>od<PERSON><PERSON><PERSON><PERSON><PERSON>, { AutoLineGroupModbucket } from "@/functions/AutoLineGroupModbucket";
import DefaultContext from "../testing/DefaultContext";
import { expect, test, beforeEach, beforeAll, afterAll, afterEach, describe } from "@jest/globals";
import { QueryTypes, Sequelize } from "sequelize";
import { disconnectMongo, useMongo } from "@/database/mongo";
import { usePsql } from "@/database/psql";
import config from "config";
import StorageTableService from "@/services/storageTableService";
import axios from "axios";
import { format, addDays, subDays } from "date-fns";

import AutoApiBatchTimesEntity from "@/core/entity/AutoApiBatchTimesEntity";
import AutoModbucketLineGroupsEntity from "@/core/entity/AutoModbucketLineGroupsEntity";
import { TenantsEntity } from "@/core/entity/TenantsEntity";
import LineGroupsEntity from "@/core/entity/LineGroupsEntity";
import { GroupPlansEntity } from "@/core/entity/GroupPlansEntity";
import LineLineGroupsEntity from "@/core/entity/LineLineGroupsEntity";
import ServiceOrdersEntity from "@/core/entity/ServiceOrdersEntity";
import { LinesEntity } from "@/core/entity/LinesEntity";

describe("AutoLineGroupModbucket with DB Integration", () => {
    let sequelize: Sequelize;
    const STATUS_SUCCESS = 0;
    const STATUS_FAIL = 1;
    const baseUrl = config.get<string>("mvno.AutoModbucketApiBaseUri");

    const testTenantId = "TEST01";
    const testGroupId = "GROUP001";
    const testLineId = "LINE001";
    const testPlanId = 1001; // Using a numeric plan ID
    const testHashedPassword = "952a539b37a792169b18ae246a1btest";
    const testApiProcessId = "apiId456";

    const now = new Date((Math.floor(new Date().getTime() / 1000) * 1000));
    const yesterday = subDays(now, 1);
    const createdAt = subDays(now, 0.5); // 12 hours ago

    beforeAll(async () => {
        sequelize = await usePsql();
        await useMongo(DefaultContext);
    });

    afterAll(async () => {
        await sequelize.close();
        await disconnectMongo(DefaultContext);
    });

    afterEach(async () => {
        jest.restoreAllMocks();
        jest.clearAllMocks();

        await ServiceOrdersEntity.destroy({
            where: { serviceOrderId: testApiProcessId }
        });

        await AutoModbucketLineGroupsEntity.destroy({
            where: { groupId: testGroupId }
        });

        await LineLineGroupsEntity.destroy({
            where: { groupId: testGroupId }
        });

        await sequelize.query(
            "DELETE FROM lines WHERE line_id = :lineId",
            {
                replacements: { lineId: testLineId },
                type: QueryTypes.DELETE
            }
        );

        await LineGroupsEntity.destroy({
            where: { groupId: testGroupId }
        });

        await GroupPlansEntity.destroy({
            where: { planId: testPlanId }
        });

        await TenantsEntity.destroy({
            where: { tenantId: testTenantId }
        });

        await AutoApiBatchTimesEntity.destroy({
            where: {}
        });

        const storageTable = new StorageTableService(DefaultContext, "AutoLineGroupModbucketPid");
        await storageTable.init();
        await storageTable.clear();
    });

    beforeEach(async () => {
        jest.spyOn(axios, "post").mockResolvedValue({
            status: 200,
            data: {
                responseHeader: {
                    apiProcessID: testApiProcessId,
                    processCode: "000000"
                },
                capacity: "2000"
            }
        });

        try {
            await TenantsEntity.create({
                tenantId: "TEST01",
                tenantName: "OPF00",
                tenantMaxConnection: 10,
                hashedPassword: "$02$952a539b37a792169b18ae246a1btest",
                office: true,
                status: true,
                tenantLevel: 1,
                customizeMaxConnection: 10,
                notvoiceRoamingFlag: false,
                pTenantId: "ZZZ000",
                csvOutputPattern: 1,
            });
            await GroupPlansEntity.create({
                planId: testPlanId,
                planClass: 1,
                policyId: 201,
                network: 'LTE',
                planName: 'プラン名',
                planDescription: 'プラン説明',
                refServicePattern: 1,
                tpcServicePattern: 1,
            })
            await LinesEntity.create({
                lineId: testLineId,
                lineStatus: "01",
                nnumber: "N141003811",
                simFlag: false,
                pricePlanId: "BS020",
                voiceMailId: "AO143"
            });
            await LineGroupsEntity.create({
                groupId: testGroupId,
                status: 1,
                tenantId: testTenantId,
                planId: testPlanId,
            });
            await LineLineGroupsEntity.create({
                lineId: testLineId,
                groupId: testGroupId,
                basicCapacity: 1024,
            });
            await AutoModbucketLineGroupsEntity.create({
                groupId: testGroupId,
                lineId: testLineId,
                serviceOrderId: "SO001",
                createdAt: now
            });
            await ServiceOrdersEntity.create({
                serviceOrderId: testApiProcessId,
                execUserId: "initial_user"
            });
            await AutoApiBatchTimesEntity.create({
                preExecTime: now,
                execTime: now
            });

            const storageTable = new StorageTableService(DefaultContext, "AutoLineGroupModbucketPid");
            await storageTable.init();
            await storageTable.clear();
        } catch (error) {
            console.error("Error setting up test data:", error);
            throw error;
        }
    });

    describe("OK Cases", () => {
        it.skip("should process auto line group modbucket with direct DB access", async () => {
            // TODO fix test: expect(axios.post).toHaveBeenCalledTimes(1);
            const result = await AutoLineGroupModbucket.Handler(null, DefaultContext);

            expect(result).toBe(STATUS_SUCCESS);

            const batchTimesResult = await AutoApiBatchTimesEntity.findAll();
            expect(batchTimesResult.length).toBe(1);

            const originalExecTime = new Date(now).getTime();
            const updatedExecTime = new Date(batchTimesResult[0].execTime).getTime();
            expect(updatedExecTime).toBeGreaterThanOrEqual(originalExecTime);

            const serviceOrder = await ServiceOrdersEntity.findOne({
                where: { serviceOrderId: testApiProcessId }
            });

            expect(serviceOrder).not.toBeNull();
            expect(serviceOrder.execUserId).toBe("initial_user");

            expect(axios.post).toHaveBeenCalledTimes(1);
            expect(axios.post).toHaveBeenCalledWith(
                config.get<string>("mvno.AutoModbucketApiBaseUri"),
                expect.stringContaining(testGroupId),
                expect.any(Object)
            );
        });

        it("should handle empty group list", async () => {
            await AutoModbucketLineGroupsEntity.destroy({
                where: { groupId: testGroupId }
            });

            const result = await AutoLineGroupModbucket.Handler(null, DefaultContext);

            expect(result).toBe(STATUS_SUCCESS);

            const batchTimesResult = await AutoApiBatchTimesEntity.findAll();
            expect(batchTimesResult.length).toBe(1);

            const originalExecTime = new Date(now).getTime();
            const updatedExecTime = new Date(batchTimesResult[0].execTime).getTime();
            expect(updatedExecTime).toBeGreaterThanOrEqual(originalExecTime);

            expect(axios.post).not.toHaveBeenCalled();
        });
    })

    describe("Fail Cases", () => {
        it("should fail if is already running", async () => {
            const storageTableService = new StorageTableService(DefaultContext, "AutoLineGroupModbucketPid");
            const storageTable = await storageTableService.init();
            await storageTable.createEntity({
                partitionKey: "AutoLineGroupModbucket",
                rowKey: `${new Date().toISOString()}`,
                data: JSON.stringify({ pid: 12345, now: new Date().toISOString() }),
            });

            const result = await AutoLineGroupModbucket.Handler(null, DefaultContext);

            expect(result).toBe(STATUS_FAIL);
            expect(axios.post).not.toHaveBeenCalled();
        });

        it("should fail when BatchTimesDao.getAutoApiBatchTimes returns null", async () => {
            await AutoApiBatchTimesEntity.destroy({
                where: {}
            });

            const result = await AutoLineGroupModbucket.Handler(null, DefaultContext);

            expect(result).toBe(STATUS_FAIL);
            expect(axios.post).not.toHaveBeenCalled();
        });

        it("should fail if preExecTime is not valid", async () => {
            await AutoApiBatchTimesEntity.destroy({
                where: {}
            });

            await AutoApiBatchTimesEntity.create({
                preExecTime: new Date(now.getTime() + 3600000), // 1 hour in the future
                execTime: new Date(now.getTime() + 3600000)
            });

            const result = await AutoLineGroupModbucket.Handler(null, DefaultContext);

            expect(result).toBe(STATUS_FAIL);
            expect(axios.post).not.toHaveBeenCalled();
        });

        it("should fail when preExecTime and execTime don't match", async () => {
            await AutoApiBatchTimesEntity.destroy({
                where: {}
            });

            await AutoApiBatchTimesEntity.create({
                preExecTime: new Date(now.getTime() - 3600000), // 1 hour before
                execTime: now
            });

            const result = await AutoLineGroupModbucket.Handler(null, DefaultContext);

            expect(result).toBe(STATUS_FAIL);
            expect(axios.post).not.toHaveBeenCalled();
        });

        it("should handle empty tenant ID in target group", async () => {
            const originalGetAutoModBucketGroupList = require("@/core/dao/AutoModBucketDao").default.getAutoModBucketGroupList;

            require("@/core/dao/AutoModBucketDao").default.getAutoModBucketGroupList =
                jest.fn().mockResolvedValue([{ group_id: "EMPTY_GROUP", tenant_id: "" }]);

            const originalGetTenantHashedPassword = require("@/core/dao/AutoModBucketDao").default.getTenantHashedPassword;
            require("@/core/dao/AutoModBucketDao").default.getTenantHashedPassword =
                jest.fn().mockResolvedValue("dummy_password");

            jest.spyOn(axios, "post").mockResolvedValue({ data: { responseHeader: { processCode: "000000" } } });

            const result = await AutoLineGroupModbucket.Handler(null, DefaultContext);

            expect(result).toBe(STATUS_SUCCESS);

            require("@/core/dao/AutoModBucketDao").default.getAutoModBucketGroupList = originalGetAutoModBucketGroupList;
            require("@/core/dao/AutoModBucketDao").default.getTenantHashedPassword = originalGetTenantHashedPassword;
            jest.restoreAllMocks();
        });

        it("should fail when getTenantHashedPassword returns null", async () => {
            const originalGetTenantHashedPassword = require("@/core/dao/AutoModBucketDao").default.getTenantHashedPassword;

            require("@/core/dao/AutoModBucketDao").default.getTenantHashedPassword =
                jest.fn().mockResolvedValue(null);

            const originalGetAutoModBucketGroupList = require("@/core/dao/AutoModBucketDao").default.getAutoModBucketGroupList;

            require("@/core/dao/AutoModBucketDao").default.getAutoModBucketGroupList =
                jest.fn().mockResolvedValue([{ group_id: "TEST_GROUP", tenant_id: "TEST_TENANT" }]);

            const result = await AutoLineGroupModbucket.Handler(null, DefaultContext);

            expect(result).toBe(STATUS_FAIL);
            expect(axios.post).not.toHaveBeenCalled();

            require("@/core/dao/AutoModBucketDao").default.getTenantHashedPassword = originalGetTenantHashedPassword;
            require("@/core/dao/AutoModBucketDao").default.getAutoModBucketGroupList = originalGetAutoModBucketGroupList;
        });

        it.skip("should fail when API call fails", async () => {
            // TODO fix test: expect(result).toBe(STATUS_FAIL);
            jest.spyOn(axios, "post").mockRejectedValue(new Error("API call failed"));

            const result = await AutoLineGroupModbucket.Handler(null, DefaultContext);

            expect(result).toBe(STATUS_FAIL);
            expect(axios.post).toHaveBeenCalledTimes(1);
        });
    })
});
