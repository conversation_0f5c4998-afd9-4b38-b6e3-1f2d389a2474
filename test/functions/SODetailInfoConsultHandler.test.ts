import { describeWithDB, generateProcessID } from "../testing/TestHelper";
import { ConnectionTimedOutError, Sequelize } from "sequelize";
import { usePsql } from "@/database/psql";
import { disconnectMongo, useMongo } from "@/database/mongo";
import DefaultContext from "../testing/DefaultContext";
import { beforeEach, describe, expect, jest, test } from "@jest/globals";
import defaultContext from "../testing/DefaultContext";
import MvnoUtil from "@/core/common/MvnoUtil";
import ResultCdConstants from "@/core/constant/ResultCdConstants";
import { formatDate } from "date-fns";
import defaultRequest from "../testing/DefaultRequest";
import { HttpResponseInit } from "@azure/functions";
import { SODetailInfoConsultHandler } from "@/functions/SODetailInfoConsultHandler";
import APISoDAO from "@/core/dao/APISoDAO";
import AppConfig from "@/appconfig";

describeWithDB("SODetailInfoConsultHandler", () => {
    let sequelize: Sequelize;

    const testdata = {
        tenantId: "TSA000",
        parentTenantId: "ZZZ000",
        existSOId: "AP1000004929964",
        wrongFunctionTypeSOId: "AP1000004931078",
        wrongTenantIdSOId: "AP1000004926401"
    }

    const request = {
        requestHeader: {
            sequenceNo: "1234567",
            senderSystemId: "0001",
            apiKey: "",
            functionType: "17",
            receivedDate: formatDate(new Date(), "yyyy/MM/dd HH:mm:ss"),
        },
        tenantId: testdata.tenantId,
        serviceOrderIdKey: "AP1000004929964",
    }

    const responseHeader = {
        sequenceNo: request.requestHeader.sequenceNo,
        receivedDate: request.requestHeader.receivedDate,
        processCode: ResultCdConstants.CODE_000000,
        apiProcessID: generateProcessID(),
    };

    beforeAll(async () => {
        await AppConfig.loadCoreMvnoConfig(DefaultContext);
        sequelize = await usePsql();
        await useMongo(DefaultContext);
    });

    afterAll(async () => {
        await sequelize.close();
        await disconnectMongo(DefaultContext);
    });

    beforeEach(async () => {
        defaultContext.jsonBody = Object.assign({}, request);
        defaultContext.responseHeader = Object.assign({}, responseHeader);
        jest.spyOn(MvnoUtil, "getDateTimeNow").mockReturnValue(responseHeader.receivedDate);
    });

    afterEach(async () => {
        jest.restoreAllMocks();
        jest.clearAllMocks();
    });

    const checkResponseHeader = (result: HttpResponseInit, expected_process_code: string = ResultCdConstants.CODE_000000) => {
        expect(result.jsonBody.responseHeader).toEqual({
            processCode: expected_process_code,
            apiProcessID: responseHeader.apiProcessID,
            sequenceNo: request.requestHeader.sequenceNo,
            receivedDate: responseHeader.receivedDate,
        });
    };

    const checkSOContent = (result: HttpResponseInit, isEmpty: boolean) => {
        if (isEmpty) {
            expect(result.jsonBody.serviceOrderId).toEqual("");
            expect(result.jsonBody.order_date).toEqual("");
            expect(result.jsonBody.exec_date).toEqual("");
            expect(result.jsonBody.reserve_date).toEqual("");
            expect(result.jsonBody.order_type).toEqual("");
            expect(result.jsonBody.status).toEqual("");
            expect(result.jsonBody.lineNo).toEqual("");
            expect(result.jsonBody.content).toEqual("");
        } else {
            expect(result.jsonBody.serviceOrderId).toEqual(testdata.existSOId);
            expect(result.jsonBody.order_date).toEqual("2019/02/27 10:55:55");
            expect(result.jsonBody.exec_date).toEqual("2019/02/27 10:55:55");
            expect(result.jsonBody.reserve_date).toEqual("");
            expect(result.jsonBody.order_type).toEqual("プラン変更");
            expect(result.jsonBody.status).toEqual("完了");
            expect(result.jsonBody.lineNo).toEqual("08006135752");
            expect(result.jsonBody.content).toEqual("08006135752 90019、80092");

        }
    };

    test("CommonCheckFailedHandler", async () => {
        defaultContext.jsonBody = Object.assign({}, request);
        defaultContext.responseHeader = Object.assign({}, { ...responseHeader, processCode: ResultCdConstants.CODE_999999 });
        const response = await SODetailInfoConsultHandler.CommonCheckFailedHandler(defaultRequest, defaultContext, "フォーマットチェックエラーが発生しました。");
        checkResponseHeader(response, ResultCdConstants.CODE_999999);
        checkSOContent(response, true);
    });

    test("CommonCheckCatchErrorHandler", async () => {
        defaultContext.jsonBody = Object.assign({}, request);
        defaultContext.responseHeader = Object.assign({}, { ...responseHeader, processCode: ResultCdConstants.CODE_999999 });
        const response = await SODetailInfoConsultHandler.CommonCheckCatchErrorHandler(defaultRequest, defaultContext, new Error("test error"));
        checkResponseHeader(response, ResultCdConstants.CODE_999999);
        checkSOContent(response, true);
    });

    describe("Handler", () => {
        test("Should return CODE_170101 for null serviceOrderIdKey", async () => {
            defaultContext.jsonBody.serviceOrderIdKey = null;
            const response = await SODetailInfoConsultHandler.Handler(defaultRequest, defaultContext);
            checkResponseHeader(response, ResultCdConstants.CODE_170101);
            checkSOContent(response, true);
        });

        test("Should return CODE_170101 for wrong length serviceOrderIdKey", async () => {
            defaultContext.jsonBody.serviceOrderIdKey = "1234567890123456";
            const response = await SODetailInfoConsultHandler.Handler(defaultRequest, defaultContext);
            checkResponseHeader(response, ResultCdConstants.CODE_170101);
            checkSOContent(response, true);
        });

        test("Should return CODE_170101 for non alpha-numeric serviceOrderIdKey", async () => {
            defaultContext.jsonBody.serviceOrderIdKey = "!@#$%^&*()!@#$%";
            const response = await SODetailInfoConsultHandler.Handler(defaultRequest, defaultContext);
            checkResponseHeader(response, ResultCdConstants.CODE_170101);
            checkSOContent(response, true);
        });

        test("Should return CODE_170201 for getting 0 tenants", async () => {
            jest.spyOn(APISoDAO.prototype, "getTenantLevel").mockResolvedValue(null);
            const response = await SODetailInfoConsultHandler.Handler(defaultRequest, defaultContext);
            checkResponseHeader(response, ResultCdConstants.CODE_170201);
            checkSOContent(response, true);
        });

        test("Should return CODE_170201 for failing getting tenants", async () => {
            jest.spyOn(APISoDAO.prototype, "getTenantLevel").mockRejectedValue(new ConnectionTimedOutError(new Error("timeout")));
            const response = await SODetailInfoConsultHandler.Handler(defaultRequest, defaultContext);
            checkResponseHeader(response, ResultCdConstants.CODE_170201);
            checkSOContent(response, true);
        });

        test("Should return CODE_170301 for SO function type not 51-55", async () => {
            defaultContext.jsonBody.serviceOrderIdKey = testdata.wrongFunctionTypeSOId;
            const response = await SODetailInfoConsultHandler.Handler(defaultRequest, defaultContext);
            checkResponseHeader(response, ResultCdConstants.CODE_170301);
            checkSOContent(response, true);
        });

        test("Should return CODE_170301 for SO tenantId not equal to request tenantId", async () => {
            defaultContext.jsonBody.serviceOrderIdKey = testdata.wrongTenantIdSOId;
            const response = await SODetailInfoConsultHandler.Handler(defaultRequest, defaultContext);
            checkResponseHeader(response, ResultCdConstants.CODE_170301);
            checkSOContent(response, true);
        });

        test("Should return CODE_170302 for failing getting SO Info", async () => {
            jest.spyOn(APISoDAO.prototype, "getSOInfo").mockRejectedValue(new ConnectionTimedOutError(new Error("timeout")));
            const response = await SODetailInfoConsultHandler.Handler(defaultRequest, defaultContext);
            checkResponseHeader(response, ResultCdConstants.CODE_170302);
            checkSOContent(response, true);
        });

        test("Should return CODE_000000 for successful SO Info", async () => {
            const response = await SODetailInfoConsultHandler.Handler(defaultRequest, defaultContext);
            checkResponseHeader(response, ResultCdConstants.CODE_000000);
            checkSOContent(response, false);
        });
    });
});