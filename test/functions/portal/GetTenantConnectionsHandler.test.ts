import { GetTenantConnectionsHandler } from "@/functions/portal/GetTenantConnectionsHandler";
import { TenantsEntity } from "@/core/entity/TenantsEntity";
import { HttpRequest } from "@azure/functions";
import { MAWP_ROLE_ID } from "@/constants/userRoles";
import { usePsql } from "@/database/psql";
import { Sequelize } from "sequelize-typescript";
import DefaultRequest from "../../testing/DefaultRequest";
import DefaultContext from "../../testing/DefaultContext";
import {extendedPortalInvocationContext} from "../../testing/DefaultContext";
import ExtendedPortalInvocationContext from "@/types/ExtendedPortalInvocationContext";
import { useRedisMock } from "../../testing/RedisMock";
import { Redis } from "ioredis";
import AppConfig from "@/appconfig";
import { useRedis } from "@/database/redis";

describe("GetTenantConnectionsHandler Integration", () => {
    let tenant: any;
    let request: HttpRequest;
    let sequelize: Sequelize;
    let redisClient: Redis;

    beforeAll(async () => {
        // Initialize database connection
        sequelize = await usePsql();
        request = {
            query: new Map(),
        } as unknown as HttpRequest;
        await AppConfig.loadCoreMvnoConfig(DefaultContext);
        redisClient = await useRedis(DefaultContext);
    });

    beforeEach(async () => {
        tenant = await TenantsEntity.create({
            tenantId: `ABC000`,
            tenantName: "Test Tenant",
            tenantMaxConnection: 10,
            hashedPassword: "",
            office: true,
            status: true,
            tenantLevel: 1,
            customizeMaxConnection: 10,
            notvoiceRoamingFlag: false,
            pTenantId: "ZZZ000",
            csvOutputPattern: 1,
            tpc: "TpcDestIpAddress",
            tpc2: "TpcDestIpAddress2",
        });
    });

    afterEach(async () => {
        await TenantsEntity.destroy({ where: { tenantId: tenant.tenantId } });

        // Clear Redis mock data
        await redisClient.del(`conCount:${tenant.tenantId}`);
    });

    afterAll(async () => {
        // Clean up database connection
        await sequelize.close();
        await redisClient.quit();
    });

    it("returns tenant list when tenantId is not provided", async () => {
        const response = await GetTenantConnectionsHandler.Handler(request, extendedPortalInvocationContext);

        expect(response.status).toBe(200);
        expect(response.jsonBody.tenants).toEqual(
            expect.arrayContaining([
                expect.objectContaining({
                    tenantId: tenant.tenantId,
                    tenantName: tenant.tenantName,
                    tenantMaxConnection: tenant.tenantMaxConnection,
                }),
            ])
        );
        expect(response.jsonBody.tenantId).toBeNull();
        expect(response.jsonBody.currentConnections).toBeNull();
    });

    it("returns currentConnections for a valid tenantId", async () => {
        const now = Math.floor(Date.now() / 1000);
        await redisClient.zadd(
            `conCount:${tenant.tenantId}`,
            now,
            DefaultContext.invocationId,
        );
        // expires in 1 minute
        await redisClient.expire(`conCount:${tenant.tenantId}`, 60);

        const request = {
            query: new Map([["tenantId", tenant.tenantId]]),
        } as unknown as HttpRequest;

        const response = await GetTenantConnectionsHandler.Handler(request, extendedPortalInvocationContext);

        expect(response.status).toBe(200);
        expect(response.jsonBody.tenantId).toBe(tenant.tenantId);
        expect(response.jsonBody.currentConnections).toBe(1);
        expect(response.jsonBody.tenants).toBeNull();
    });

    it("returns 500 if an error occurs", async () => {
        const request = {
            query: null,
        } as unknown as HttpRequest;

        const response = await GetTenantConnectionsHandler.Handler(request, extendedPortalInvocationContext);

        expect(response.status).toBe(500);
        expect(response.jsonBody.error).toBe("Internal Server Error");
    });
});