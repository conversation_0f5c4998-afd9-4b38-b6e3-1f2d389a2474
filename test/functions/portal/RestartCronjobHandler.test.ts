import { RestartCronjob<PERSON>and<PERSON> } from "@/functions/portal/RestartCronjobHandler";
import { HttpRequest } from "@azure/functions";
import ExtendedPortalInvocationContext from "@/types/ExtendedPortalInvocationContext";
import AutoLineGroupModbucketHandler from "@/functions/AutoLineGroupModbucket";
import { ExecReservedSOHandler } from "@/functions/triggers/ExecReservedSOHandler";
import ReserveRewriteHandler from "@/functions/triggers/ReserveRewriteHandler";

jest.mock("@/functions/AutoLineGroupModbucket");

// TODO need to fix this test suite
describe("RestartCronjobHandler function call", () => {
    let context: ExtendedPortalInvocationContext;

    beforeEach(() => {
        context = {
            log: jest.fn(),
            error: jest.fn(),
        } as unknown as ExtendedPortalInvocationContext;
    });

    it("calls AutoLineGroupModbucketHandler for AutoLineGroupModbucketTimer", async () => {
        const autoLineGroupModbucketHandler = require("@/functions/AutoLineGroupModbucket").default;
        (autoLineGroupModbucketHandler as jest.Mock).mockResolvedValueOnce({});
        const request = {
            json: async () => ({ cronJobName: "AutoLineGroupModbucketTimer" }),
        } as unknown as HttpRequest;

        await RestartCronjobHandler.Handler(request, context);

        expect(autoLineGroupModbucketHandler).toHaveBeenCalled();
        autoLineGroupModbucketHandler.mockRestore();
    });

    it("calls ExecReservedSOHandler.Handler for ExecReservedSOTimer", async () => {
        const handler = jest.spyOn(ExecReservedSOHandler, "Handler").mockResolvedValueOnce({});
        const request = {
            json: async () => ({ cronJobName: "ExecReservedSOTimer" }),
        } as unknown as HttpRequest;

        await RestartCronjobHandler.Handler(request, context);

        expect(handler).toHaveBeenCalled();
        handler.mockRestore();
    });

    it("calls ReserveRewriteHandler.Handler for ReserveRewriteTimer", async () => {
        const handler = jest.spyOn(ReserveRewriteHandler, "Handler").mockResolvedValueOnce({});
        const request = {
            json: async () => ({ cronJobName: "ReserveRewriteTimer" }),
        } as unknown as HttpRequest;

        await RestartCronjobHandler.Handler(request, context);

        expect(handler).toHaveBeenCalled();
        handler.mockRestore();
    });
});