jest.mock("../../../src/services/notificationService", () => ({
    ...jest.requireActual("../../../src/services/notificationService"),
    NotificationService: {
        sendMessage: jest.fn(),
    },
}));
jest.mock("../../../src/services/servicebus", () => ({
    ...jest.requireActual("../../../src/services/servicebus"),
    __esModule: true,
    default: jest.fn(),
}));

import { describe, expect, test } from "@jest/globals";
import {extendedPortalInvocationContext} from "../../testing/DefaultContext";
import DefaultRequest from "../../testing/DefaultRequest";
import DefaultContext from "../../testing/DefaultContext";
import { ResendCoreSwimmyTransactionHandler } from "@/functions/portal/ResendCoreSwimmyTransactionHandler";
import { disconnectMongo, useMongo } from "@/database/mongo";
import { HttpRequest } from "@azure/functions";
import { CommonError, getCommonErrorName } from "@/constants/commonError";
import ExtendedPortalInvocationContext from "@/types/ExtendedPortalInvocationContext";
import CoreSwimmyApiLog from "@/models/coreSwimmyApiLog";
import { generateProcessID, generateSOID } from "../../testing/TestHelper";
import AppConfig from "@/appconfig";
import { PreLineAddPayload } from "@/types/coreSwimmyPayload";
import { addDays, format } from "date-fns";
import CoreSwimmyService from "@/services/coreSwimmyService";
import { CoreSwimmyStatus } from "@/constants/coreSwimmy";
import { Sequelize } from "sequelize";
import { usePsql } from "@/database/psql";
import { CoreSwimmyPreLineAddRequest } from "@/types/coreSwimmyService";

const NUM_LOGS = 2;

describe("functions/portal/ResendCoreSwimmyTransactionHandler", () => {
    let logEntries = [];
    let savedLogs = [];
    let sequelize: Sequelize;

    beforeAll(async () => {
        await AppConfig.loadCoreMvnoConfig(DefaultContext);
        await useMongo(DefaultContext);
        sequelize = await usePsql();
    });
    
    afterAll(async () => {
        await disconnectMongo(DefaultContext);
        await sequelize.close();
    });

    beforeEach(async () => {
        for (let i=0; i < NUM_LOGS; i++) {
            logEntries.push(new CoreSwimmyApiLog({
                tenantId: `TST000001${i}`,
                kaisenNo: `0211613400${i}`,
                swimmyType: '004',
                status: 1,
                requestOrderId: `AP101100524593${i}`,
                soId: generateSOID(),
                jikkouchuFlg: false,
                requestParam: i === 0 ? { something: "test" } : undefined,
            }));
        }
        for (const log of logEntries) {
            const savedLog = await log.save();
            savedLogs.push(savedLog);
        }
    })

    afterEach(async () => {
        await CoreSwimmyApiLog.deleteMany({
            _id: { $in: savedLogs.map((log) => log._id) },
        });
        logEntries = [];
        savedLogs = [];
    })

    describe("valid requests", () => {
        test("should return resendOK == true for all target ids", async () => {
            const payload = {
                targets: []
            };
            const expectedResult = {
                error: 0,
                results: []
            };

            for (const log of savedLogs) {
                payload.targets.push({
                    id: log._id.toString()
                });
                expectedResult.results.push({
                    id: log._id.toString(),
                    resendOK: true
                });
            }

            const newRequest = {
                ...DefaultRequest,
                json: () => { 
                    return payload
                }
            } as unknown as HttpRequest;

            const newContext = {
                ...extendedPortalInvocationContext,
                sessionData: {
                    user: {
                        plusId: "testUser"
                    }
                }
            } as unknown as ExtendedPortalInvocationContext;

            const { jsonBody } = await ResendCoreSwimmyTransactionHandler.Handler(newRequest, newContext);
            expect(jsonBody.error).toBe(expectedResult.error);
            expect(jsonBody.results).toEqual(expect.arrayContaining(expectedResult.results));
        });
    });

    // オプション設定
    describe("request options", () => {
        // [007:fullmvno, 007:lite+samemvne, 007:lite+!samemvne, other, other]
        let logIds: string[] = [];

        beforeEach(async () => {
            const getPayload = (): PreLineAddPayload => ({
                custInfo: {
                    agencyCode: null,
                    customerService: "testCustomerService",
                    customerServicePhoneNumber:
                        "testCustomerServicePhoneNumber",
                    customerServiceEmail: "testCustomerServiceEmail",
                    contractNameKana: "testContractNameKana",
                    contractName: "testContractName",
                },
                isFM: true,
                receivedDate: format(new Date(), "yyyyMMdd"),
                reserveDate:
                    format(addDays(new Date(), 2), "yyyy/MM/dd") + " 04:00",
                isSameMvne: false,
                mnpInType: 0,
                cardTypeIdT: "T0001",
                simNo: "**********0123",
                hankuro: false,
                simFlag: true,
                orderType: "0",
                pricePlanIdT: "T0002",
                delivery: {
                    postcode: "1234567",
                    prefecture: "Tokyo",
                    city: "Shinjuku",
                    ooaza: "",
                    aza: "",
                    block: "9999",
                    building: "testBuilding",
                    name: "testName",
                    department: undefined,
                    contact: undefined,
                    tel: "**********",
                },
                accessType: "SA",
                frontSoId: generateSOID(),
                coreSoId: generateProcessID(),
                opfTenantId: "OPF000",
                tenantId: "TST000",
                lineNo: "09910002000",
                nNo: "N9999999999",
            });
            const optionsLogEntries: PreLineAddPayload[] = [];
            optionsLogEntries.push({
                ...getPayload(),
                isFM: true,
                isSameMvne: false,
            });
            optionsLogEntries.push({
                ...getPayload(),
                isFM: false,
                isSameMvne: true,
            });
            optionsLogEntries.push({
                ...getPayload(),
                isFM: false,
                isSameMvne: false,
            });

            const service = new CoreSwimmyService(
                DefaultRequest,
                extendedPortalInvocationContext,
            );
            jest.spyOn(service, "sendToServiceBus").mockResolvedValue();
            await Promise.all(
                optionsLogEntries.map(
                    async (log) =>
                        await service.registerPreLineAddTransaction(log),
                ),
            );

            logIds = await Promise.all(
                optionsLogEntries.map(async (row) => {
                    const log = await CoreSwimmyApiLog.findOneAndUpdate(
                        {
                            requestOrderId: row.coreSoId,
                            soId: row.frontSoId,
                        },
                        {
                            $set: {
                                status: CoreSwimmyStatus.TOUROKU_IRAI_RESULT_NG,
                                jikkouchuFlg: false,
                            },
                        },
                    );
                    return log.id;
                }),
            );
        });

        afterEach(async () => {
            const result = await CoreSwimmyApiLog.deleteMany({
                _id: { $in: logIds },
            });
            logIds = [];

            jest.clearAllMocks();
            jest.resetAllMocks();
        });

        function checkSameMvneFlag(request: CoreSwimmyPreLineAddRequest, sameMvneFlag: boolean) {
            const appBasicDtl = request?.appRequestInfo?.appInfoList[0]?.appBasic?.appBasicDtlList;
            const sameMvneDtl = appBasicDtl?.find(dtl => dtl.appAttributeCode === "M0000018")
            if (sameMvneDtl) {
                expect(appBasicDtl).toBeDefined();
                expect(Array.isArray(appBasicDtl)).toEqual(true);
                expect(sameMvneDtl).toBeDefined();
                expect(sameMvneDtl.appAttributeValue).toEqual("1");
            } else {
                expect(sameMvneDtl).toBeUndefined();
            }
        }

        async function checkCoreApiLog(id: string, sameMvneFlag: boolean) {
            const doc = await CoreSwimmyApiLog.findById(id);
            expect(doc).toBeDefined();
            const requestParam = doc.requestParam as CoreSwimmyPreLineAddRequest;
            checkSameMvneFlag(requestParam, sameMvneFlag);
        }

        test("should process resendOptions.sameMvneFlag = 1 correctly", async () => {
            const targets = [...logIds, ...savedLogs.map((log) => log._id.toString())];
            const beforeFlags = [false, true, false, false, false];
            const afterFlags = [false, true, true, false, false];

            // check before
            for (const id of targets) {
                await checkCoreApiLog(id, beforeFlags[targets.indexOf(id)]);
            }

            const param = {
                targets: targets.map((id) => ({
                    id,
                    options: { sameMvneFlag: 1 },
                })),
            };
            const expectedResult = {
                error: 0,
                results: targets.map((id) => ({ id, resendOK: true })),
            };

            const newRequest = {
                ...DefaultRequest,
                json: () => {
                    return param;
                },
            } as unknown as HttpRequest;
            const newContext = {
                ...extendedPortalInvocationContext,
                sessionData: {
                    user: {
                        plusId: "testUser",
                    },
                },
            } as unknown as ExtendedPortalInvocationContext;
            const { jsonBody } =
                await ResendCoreSwimmyTransactionHandler.Handler(
                    newRequest,
                    newContext,
                );
            expect(jsonBody.error).toBe(expectedResult.error);
            expect(jsonBody.results).toEqual(
                expect.arrayContaining(expectedResult.results),
            );

            // check after
            for (const id of targets) {
                await checkCoreApiLog(id, afterFlags[targets.indexOf(id)]);
            }
        });

        test("should process resendOptions.sameMvneFlag = 0 correctly", async () => {
            const targets = [...logIds, ...savedLogs.map((log) => log._id.toString())];
            const beforeFlags = [false, true, false, false, false];
            const afterFlags = beforeFlags;

            // check before
            for (const id of targets) {
                await checkCoreApiLog(id, beforeFlags[targets.indexOf(id)]);
            }

            const param = {
                targets: targets.map((id) => ({
                    id,
                    options: { sameMvneFlag: 0 },
                })),
            };
            const expectedResult = {
                error: 0,
                results: targets.map((id) => ({ id, resendOK: true })),
            };

            const newRequest = {
                ...DefaultRequest,
                json: () => {
                    return param;
                },
            } as unknown as HttpRequest;
            const newContext = {
                ...extendedPortalInvocationContext,
                sessionData: {
                    user: {
                        plusId: "testUser",
                    },
                },
            } as unknown as ExtendedPortalInvocationContext;
            const { jsonBody } =
                await ResendCoreSwimmyTransactionHandler.Handler(
                    newRequest,
                    newContext,
                );
            expect(jsonBody.error).toBe(expectedResult.error);
            expect(jsonBody.results).toEqual(
                expect.arrayContaining(expectedResult.results),
            );

            // check after
            for (const id of targets) {
                await checkCoreApiLog(id, afterFlags[targets.indexOf(id)]);
            }
        })
    });

    describe("invalid requests", () => {
        test("should return invalid parameters error", async () => {
            const { jsonBody } = await ResendCoreSwimmyTransactionHandler.Handler(DefaultRequest, extendedPortalInvocationContext);
            expect(jsonBody.error).toBe(CommonError.INVALID_USER);
            expect(jsonBody.error_msg).toBe(getCommonErrorName(CommonError.INVALID_USER));
            expect(jsonBody.nttc_mvno_error_code).toBe('invalid parameters');
        });

        test("should return an error due to some sudden crash", async () => {
            const errorMsg = 'Make it crash';
            const newContext = {
                ...extendedPortalInvocationContext,
                log: () => { 
                    throw Error(errorMsg);
                }
            } as unknown as ExtendedPortalInvocationContext;
            const { body } = await ResendCoreSwimmyTransactionHandler.Handler(DefaultRequest, newContext);
            expect(String(body)).toBe(`Error: ${errorMsg}`);
        });

        test("should return an error due to wrong json format", async () => {
            const newRequest = {
                ...DefaultRequest,
                json: () => { 
                    throw Error();
                }
            } as unknown as HttpRequest;
            const { body } = await ResendCoreSwimmyTransactionHandler.Handler(newRequest, extendedPortalInvocationContext);
            expect(String(body)).toBe(`Error: Request body is empty`);
        });

        test("should return an error due to sessionData.user.plusId not set", async () => {
            const payload = {
                targets: []
            };
            const expectedResult = {
                error: 0,
                results: []
            };

            for (const log of savedLogs) {
                payload.targets.push({
                    id: log._id.toString()
                });
                expectedResult.results.push({
                    id: log._id.toString(),
                    resendOK: true
                });
            }

            const newRequest = {
                ...DefaultRequest,
                json: () => { 
                    return payload
                }
            } as unknown as HttpRequest;
            
            const { jsonBody } = await ResendCoreSwimmyTransactionHandler.Handler(newRequest, extendedPortalInvocationContext);
            expect(jsonBody.error).toBe(CommonError.INVALID_USER);
            expect(jsonBody.error_msg).toBe(getCommonErrorName(CommonError.INVALID_USER));
            expect(jsonBody.nttc_mvno_error_code).toBe('invalid parameters');
        });

        test("should return resendOK == false for each target due to IDs not in DB", async () => {
            const payload = {
                targets: [
                    { id: "667147e4b14c82ef86d802c1" },
                    { id: "667147e4b14c82ef86d8d2c1" }
                ]
            };

            const expectedResult = {
                error: 0,
                results: [
                    { id: "667147e4b14c82ef86d802c1", resendOK: false },
                    { id: "667147e4b14c82ef86d8d2c1", resendOK: false }
                ]
            };

            const newRequest = {
                ...DefaultRequest,
                json: () => { 
                    return payload
                }
            } as unknown as HttpRequest;

            const newContext = {
                ...extendedPortalInvocationContext,
                sessionData: {
                    user: {
                        plusId: "testUser"
                    }
                }
            } as unknown as ExtendedPortalInvocationContext;
            
            const { jsonBody } = await ResendCoreSwimmyTransactionHandler.Handler(newRequest, newContext);
            expect(jsonBody.error).toBe(expectedResult.error);
            expect(jsonBody.results).toEqual(expect.arrayContaining(expectedResult.results));
        });
    });
});