import { SwimmyType } from "@/types/coreSwimmyApiLog";

type OptionalParam = {
    kaisenNo?: string;
    tenantId?: string;
    swimmyType?: SwimmyType;
    createdAt?: number;
};

export function generateCoreSwimmyApiLog(
    requestOrderId: string,
    soId: string,
    optional: OptionalParam = {},
) {
    const defaultValue: OptionalParam = {
        kaisenNo: "02006134003",
        tenantId: "TST000",
        swimmyType: "004",
        createdAt: Math.floor(Date.now() / 1000),
    };
    return {
        // _id: {
        //     $oid: "668f780917e0687896dbc680",
        // },
        tenantId: optional.tenantId ?? defaultValue.tenantId,
        tempoId: "",
        kaisenNo: optional.kaisenNo ?? defaultValue.kaisenNo,
        swimmyType: optional.swimmyType ?? defaultValue.swimmyType,
        soId,
        requestOrderId,
        requestParam: {
            commonHeaderInfo: {
                requestFromSystemId: "MAP",
                otherSystemSendDatetime: "20240711151329",
            },
            appRequestInfo: {
                processType: "03",
                appInfoList: [
                    {
                        appBasic: {
                            inputFromType: "01",
                            orderType: "11",
                            appStatus: "15",
                            appDate: "20240711",
                            requestDate: "20240711",
                            appBasicDtlList: [
                                {
                                    appAttributeCode: "M0000002",
                                    appAttributeValue:
                                        optional.swimmyType ??
                                        defaultValue.swimmyType,
                                },
                                {
                                    appAttributeCode: "M0000012",
                                    appAttributeValue:
                                        optional.kaisenNo ??
                                        defaultValue.kaisenNo,
                                },
                                {
                                    appAttributeCode: "M0000013",
                                    appAttributeValue: "N121152174",
                                },
                                {
                                    appAttributeCode: "M0000001",
                                    appAttributeValue: soId,
                                },
                                {
                                    appAttributeCode: "M0000022",
                                    appAttributeValue: "1",
                                },
                            ],
                        },
                        appPrdtList: [
                            {
                                appPrdtStatus: "01",
                                prdtCode: "T2000AX2",
                                processType: "01",
                                prdtTypeCode: "0002",
                                prdtSerialno: 1,
                                appPrdtDtlList: [
                                    {
                                        prdtAttributeCode: "********",
                                        prdtAttributeValue: "SIM11231239813",
                                    },
                                ],
                            },
                        ],
                    },
                ],
            },
        },
        createdAt: optional.createdAt ?? defaultValue.createdAt,
        lastUpdatedAt: 1720678407,
        status: 1,
        statusTime: 1720678410489,
        statusLog: {
            "1720678409501": 0,
            "1720678410489": 1,
        },
        jikkouchuFlg: false,
        __v: 0,
        getResponseAt: 1720678410,
        httpStatusCodeLog: {
            "1720678410489": 200,
        },
        responseLog: {
            "1720678410489": {
                commonHeaderInfo: {
                    requestFromSystemId: "MAP",
                    otherSystemSendDatetime: "**************",
                    operatorId: "ZZZZZZZZ",
                    operatorGroupId: "ZZZZZZZZZZ",
                    serviceProcessResultType: "0",
                    serviceProcessResultDtlType: null,
                },
                appResponseInfo: {
                    processType: "03",
                    resultCode: "0001",
                    appInfoList: [
                        {
                            appBasic: {
                                receiptId: "***************",
                                inputFromType: "01",
                                receiptIdSerialno: 1,
                                orderType: "11",
                                appStatus: "15",
                                appDate: "********",
                                requestDate: "********",
                                salesChannelCode: "79100100",
                                salesChannelName: "金沢OCNサービスセンタ",
                                inputFromReceiptId: null,
                                registName: null,
                                serviceStartDate: "********",
                                firstRequestFromSystemId: "MAP",
                                lastRequestFromSystemId: "MAP",
                                appBasicDtlList: [
                                    {
                                        appAttributeCode: "M0000001",
                                        appAttributeValue: "AP00000********",
                                    },
                                    {
                                        appAttributeCode: "M0000002",
                                        appAttributeValue: "004",
                                    },
                                    {
                                        appAttributeCode: "M0000013",
                                        appAttributeValue: "N178001009",
                                    },
                                    {
                                        appAttributeCode: "M0000012",
                                        appAttributeValue: "09010002010",
                                    },
                                    {
                                        appAttributeCode: "M0000006",
                                        appAttributeValue: "20250417170337",
                                    },
                                    {
                                        appAttributeCode: "M0000007",
                                        appAttributeValue: "",
                                    },
                                    {
                                        appAttributeCode: "M0000004",
                                        appAttributeValue: "20250417170337",
                                    },
                                    {
                                        appAttributeCode: "M0000005",
                                        appAttributeValue: "",
                                    },
                                ],
                                appTying: {
                                    contractReceiptId: null,
                                },
                            },
                            appContract: null,
                            appPrdtList: [
                                {
                                    accountId: "AC00000000372",
                                    accountPrdtId: "**************************",
                                    appPrdtStatus: "01",
                                    appPrdtCancelCause: null,
                                    prdtCode: "T2000232",
                                    processType: "01",
                                    prdtTypeCode: "0002",
                                    price: null,
                                    billingStartDate: null,
                                    billingEndDate: null,
                                    prdtSerialno: 1,
                                    parentPrdtSerialno: 2,
                                    appPrdtDtlList: [
                                        {
                                            prdtAttributeCode: "********",
                                            prdtAttributeValue: "DN09881188882",
                                        },
                                        {
                                            prdtAttributeCode: "********",
                                            prdtAttributeValue:
                                                "送付先宛先太郎",
                                        },
                                        {
                                            prdtAttributeCode: "J1000301",
                                            prdtAttributeValue: "2840015",
                                        },
                                        {
                                            prdtAttributeCode: "********",
                                            prdtAttributeValue:
                                                "***************",
                                        },
                                        {
                                            prdtAttributeCode: "00000017",
                                            prdtAttributeValue: "1",
                                        },
                                        {
                                            prdtAttributeCode: "00000016",
                                            prdtAttributeValue: "20170415",
                                        },
                                        {
                                            prdtAttributeCode: "J1000001",
                                            prdtAttributeValue: "1",
                                        },
                                        {
                                            prdtAttributeCode: "J1000601",
                                            prdtAttributeValue: "サウスタワー",
                                        },
                                        {
                                            prdtAttributeCode: "J1000501",
                                            prdtAttributeValue: "２",
                                        },
                                        {
                                            prdtAttributeCode: "00000011",
                                            prdtAttributeValue: "09010002010",
                                        },
                                        {
                                            prdtAttributeCode: "J1000401",
                                            prdtAttributeValue: "12228012001",
                                        },
                                        {
                                            prdtAttributeCode: "00000008",
                                            prdtAttributeValue: "0",
                                        },
                                        {
                                            prdtAttributeCode: "00000019",
                                            prdtAttributeValue: "送付先部課",
                                        },
                                        {
                                            prdtAttributeCode: "00000021",
                                            prdtAttributeValue: "0355554444",
                                        },
                                        {
                                            prdtAttributeCode: "********",
                                            prdtAttributeValue: "担当花子",
                                        },
                                        {
                                            prdtAttributeCode: "********",
                                            prdtAttributeValue: "",
                                        },
                                    ],
                                },
                                {
                                    accountId: "AC00000000372",
                                    accountPrdtId: "**************************",
                                    appPrdtStatus: "01",
                                    appPrdtCancelCause: null,
                                    prdtCode: "T1000001",
                                    processType: "02",
                                    prdtTypeCode: "0001",
                                    price: "0",
                                    billingStartDate: "********",
                                    billingEndDate: "********",
                                    prdtSerialno: 2,
                                    parentPrdtSerialno: 0,
                                    appPrdtDtlList: [
                                        {
                                            prdtAttributeCode: "********",
                                            prdtAttributeValue: "4",
                                        },
                                        {
                                            prdtAttributeCode: "********",
                                            prdtAttributeValue: "0",
                                        },
                                        {
                                            prdtAttributeCode: "00000003",
                                            prdtAttributeValue: "0",
                                        },
                                        {
                                            prdtAttributeCode: "********",
                                            prdtAttributeValue: "********",
                                        },
                                        {
                                            prdtAttributeCode: "********",
                                            prdtAttributeValue: "9",
                                        },
                                    ],
                                },
                                {
                                    accountId: "AC00000000372",
                                    accountPrdtId: "**************************",
                                    appPrdtStatus: "01",
                                    appPrdtCancelCause: null,
                                    prdtCode: "T2000232",
                                    processType: "03",
                                    prdtTypeCode: "0002",
                                    price: "0",
                                    billingStartDate: "********",
                                    billingEndDate: "********",
                                    prdtSerialno: 901,
                                    parentPrdtSerialno: 2,
                                    appPrdtDtlList: [
                                        {
                                            prdtAttributeCode: "********",
                                            prdtAttributeValue:
                                                "***************",
                                        },
                                        {
                                            prdtAttributeCode: "********",
                                            prdtAttributeValue: "DN09881188881",
                                        },
                                        {
                                            prdtAttributeCode: "00000008",
                                            prdtAttributeValue: "0",
                                        },
                                        {
                                            prdtAttributeCode: "00000011",
                                            prdtAttributeValue: "09010002010",
                                        },
                                        {
                                            prdtAttributeCode: "00000016",
                                            prdtAttributeValue: "20170415",
                                        },
                                        {
                                            prdtAttributeCode: "00000017",
                                            prdtAttributeValue: "1",
                                        },
                                        {
                                            prdtAttributeCode: "********",
                                            prdtAttributeValue:
                                                "送付先宛先太郎",
                                        },
                                        {
                                            prdtAttributeCode: "00000019",
                                            prdtAttributeValue: "送付先部課",
                                        },
                                        {
                                            prdtAttributeCode: "********",
                                            prdtAttributeValue: "担当花子",
                                        },
                                        {
                                            prdtAttributeCode: "00000021",
                                            prdtAttributeValue: "0355554444",
                                        },
                                        {
                                            prdtAttributeCode: "********",
                                            prdtAttributeValue: "1703230003724",
                                        },
                                        {
                                            prdtAttributeCode: "J1000001",
                                            prdtAttributeValue: "1",
                                        },
                                        {
                                            prdtAttributeCode: "J1000301",
                                            prdtAttributeValue: "2840015",
                                        },
                                        {
                                            prdtAttributeCode: "J1000401",
                                            prdtAttributeValue: "12228012001",
                                        },
                                        {
                                            prdtAttributeCode: "J1000501",
                                            prdtAttributeValue: "２",
                                        },
                                        {
                                            prdtAttributeCode: "J1000601",
                                            prdtAttributeValue: "サウスタワー",
                                        },
                                    ],
                                },
                                {
                                    accountId: "AC00000000372",
                                    accountPrdtId: "**************************",
                                    appPrdtStatus: "01",
                                    appPrdtCancelCause: null,
                                    prdtCode: "T3000297",
                                    processType: "00",
                                    prdtTypeCode: "0003",
                                    price: "0",
                                    billingStartDate: "********",
                                    billingEndDate: "********",
                                    prdtSerialno: 902,
                                    parentPrdtSerialno: 2,
                                    appPrdtDtlList: [
                                        {
                                            prdtAttributeCode: "********",
                                            prdtAttributeValue: "2",
                                        },
                                        {
                                            prdtAttributeCode: "********",
                                            prdtAttributeValue: "2",
                                        },
                                    ],
                                },
                                {
                                    accountId: "AC00000000372",
                                    accountPrdtId: "**************************",
                                    appPrdtStatus: "01",
                                    appPrdtCancelCause: null,
                                    prdtCode: "T4000001",
                                    processType: "00",
                                    prdtTypeCode: "0004",
                                    price: "0",
                                    billingStartDate: "********",
                                    billingEndDate: "********",
                                    prdtSerialno: 903,
                                    parentPrdtSerialno: 2,
                                    appPrdtDtlList: [
                                        {
                                            prdtAttributeCode: "********",
                                            prdtAttributeValue:
                                                "192.168.168.233",
                                        },
                                        {
                                            prdtAttributeCode: "********",
                                            prdtAttributeValue:
                                                "192.168.168.230",
                                        },
                                        {
                                            prdtAttributeCode: "00000025",
                                            prdtAttributeValue: "1",
                                        },
                                        {
                                            prdtAttributeCode: "00000038",
                                            prdtAttributeValue: "1",
                                        },
                                        {
                                            prdtAttributeCode: "C0000002",
                                            prdtAttributeValue: "ORSI1015",
                                        },
                                        {
                                            prdtAttributeCode: "C0000001",
                                            prdtAttributeValue:
                                                "orsitestdomain19",
                                        },
                                        {
                                            prdtAttributeCode: "********",
                                            prdtAttributeValue: "",
                                        },
                                        {
                                            prdtAttributeCode: "********",
                                            prdtAttributeValue: "",
                                        },
                                    ],
                                },
                                {
                                    accountId: "AC00000000372",
                                    accountPrdtId: "**************************",
                                    appPrdtStatus: "01",
                                    appPrdtCancelCause: null,
                                    prdtCode: "T6000010",
                                    processType: "00",
                                    prdtTypeCode: "0006",
                                    price: "0",
                                    billingStartDate: "********",
                                    billingEndDate: "********",
                                    prdtSerialno: 904,
                                    parentPrdtSerialno: 2,
                                    appPrdtDtlList: [
                                        {
                                            prdtAttributeCode: "********",
                                            prdtAttributeValue: "********",
                                        },
                                        {
                                            prdtAttributeCode: "********",
                                            prdtAttributeValue: "00054",
                                        },
                                    ],
                                },
                                {
                                    accountId: "AC00000000372",
                                    accountPrdtId: "**************************",
                                    appPrdtStatus: "01",
                                    appPrdtCancelCause: null,
                                    prdtCode: "T6000010",
                                    processType: "00",
                                    prdtTypeCode: "0006",
                                    price: "0",
                                    billingStartDate: "********",
                                    billingEndDate: "********",
                                    prdtSerialno: 905,
                                    parentPrdtSerialno: 2,
                                    appPrdtDtlList: [
                                        {
                                            prdtAttributeCode: "********",
                                            prdtAttributeValue: "********",
                                        },
                                        {
                                            prdtAttributeCode: "********",
                                            prdtAttributeValue: "00055",
                                        },
                                    ],
                                },
                                {
                                    accountId: "AC00000000372",
                                    accountPrdtId: "**************************",
                                    appPrdtStatus: "01",
                                    appPrdtCancelCause: null,
                                    prdtCode: "T6000010",
                                    processType: "00",
                                    prdtTypeCode: "0006",
                                    price: "0",
                                    billingStartDate: "********",
                                    billingEndDate: "********",
                                    prdtSerialno: 906,
                                    parentPrdtSerialno: 2,
                                    appPrdtDtlList: [
                                        {
                                            prdtAttributeCode: "********",
                                            prdtAttributeValue: "********",
                                        },
                                        {
                                            prdtAttributeCode: "********",
                                            prdtAttributeValue: "00066",
                                        },
                                    ],
                                },
                                {
                                    accountId: "AC00000000372",
                                    accountPrdtId: "**************************",
                                    appPrdtStatus: "01",
                                    appPrdtCancelCause: null,
                                    prdtCode: "T6000010",
                                    processType: "00",
                                    prdtTypeCode: "0006",
                                    price: "0",
                                    billingStartDate: "********",
                                    billingEndDate: "********",
                                    prdtSerialno: 907,
                                    parentPrdtSerialno: 2,
                                    appPrdtDtlList: [
                                        {
                                            prdtAttributeCode: "********",
                                            prdtAttributeValue: "********",
                                        },
                                        {
                                            prdtAttributeCode: "********",
                                            prdtAttributeValue: "00067",
                                        },
                                    ],
                                },
                                {
                                    accountId: "AC00000000372",
                                    accountPrdtId: "**************************",
                                    appPrdtStatus: "01",
                                    appPrdtCancelCause: null,
                                    prdtCode: "T6000011",
                                    processType: "00",
                                    prdtTypeCode: "0006",
                                    price: "0",
                                    billingStartDate: "********",
                                    billingEndDate: "********",
                                    prdtSerialno: 908,
                                    parentPrdtSerialno: 2,
                                    appPrdtDtlList: [
                                        {
                                            prdtAttributeCode: "********",
                                            prdtAttributeValue: "********",
                                        },
                                        {
                                            prdtAttributeCode: "********",
                                            prdtAttributeValue: "37",
                                        },
                                    ],
                                },
                                {
                                    accountId: "AC00000000372",
                                    accountPrdtId: "**************************",
                                    appPrdtStatus: "01",
                                    appPrdtCancelCause: null,
                                    prdtCode: "T6000024",
                                    processType: "00",
                                    prdtTypeCode: "0006",
                                    price: "0",
                                    billingStartDate: "********",
                                    billingEndDate: "********",
                                    prdtSerialno: 909,
                                    parentPrdtSerialno: 2,
                                    appPrdtDtlList: [
                                        {
                                            prdtAttributeCode: "********",
                                            prdtAttributeValue: "********",
                                        },
                                        {
                                            prdtAttributeCode: "********",
                                            prdtAttributeValue: "32",
                                        },
                                    ],
                                },
                                {
                                    accountId: "AC00000000372",
                                    accountPrdtId: "**************************",
                                    appPrdtStatus: "01",
                                    appPrdtCancelCause: null,
                                    prdtCode: "T6000042",
                                    processType: "00",
                                    prdtTypeCode: "0006",
                                    price: "0",
                                    billingStartDate: "********",
                                    billingEndDate: "********",
                                    prdtSerialno: 910,
                                    parentPrdtSerialno: 2,
                                    appPrdtDtlList: [
                                        {
                                            prdtAttributeCode: "********",
                                            prdtAttributeValue: "********",
                                        },
                                        {
                                            prdtAttributeCode: "********",
                                            prdtAttributeValue: "27",
                                        },
                                    ],
                                },
                                {
                                    accountId: "AC00000000372",
                                    accountPrdtId: "**************************",
                                    appPrdtStatus: "01",
                                    appPrdtCancelCause: null,
                                    prdtCode: "T6000048",
                                    processType: "00",
                                    prdtTypeCode: "0006",
                                    price: "0",
                                    billingStartDate: "********",
                                    billingEndDate: "********",
                                    prdtSerialno: 911,
                                    parentPrdtSerialno: 2,
                                    appPrdtDtlList: [
                                        {
                                            prdtAttributeCode: "********",
                                            prdtAttributeValue: "********",
                                        },
                                        {
                                            prdtAttributeCode: "********",
                                            prdtAttributeValue: "31",
                                        },
                                    ],
                                },
                                {
                                    accountId: "AC00000000372",
                                    accountPrdtId: "**************************",
                                    appPrdtStatus: "01",
                                    appPrdtCancelCause: null,
                                    prdtCode: "T6000061",
                                    processType: "00",
                                    prdtTypeCode: "0006",
                                    price: "0",
                                    billingStartDate: "********",
                                    billingEndDate: "********",
                                    prdtSerialno: 912,
                                    parentPrdtSerialno: 2,
                                    appPrdtDtlList: [
                                        {
                                            prdtAttributeCode: "********",
                                            prdtAttributeValue: "********",
                                        },
                                        {
                                            prdtAttributeCode: "********",
                                            prdtAttributeValue: "28",
                                        },
                                    ],
                                },
                                {
                                    accountId: "AC00000000372",
                                    accountPrdtId: "**************************",
                                    appPrdtStatus: "01",
                                    appPrdtCancelCause: null,
                                    prdtCode: "T6000076",
                                    processType: "00",
                                    prdtTypeCode: "0006",
                                    price: "0",
                                    billingStartDate: "********",
                                    billingEndDate: "********",
                                    prdtSerialno: 913,
                                    parentPrdtSerialno: 2,
                                    appPrdtDtlList: [
                                        {
                                            prdtAttributeCode: "********",
                                            prdtAttributeValue: "********",
                                        },
                                        {
                                            prdtAttributeCode: "********",
                                            prdtAttributeValue: "30",
                                        },
                                    ],
                                },
                                {
                                    accountId: "AC00000000372",
                                    accountPrdtId: "**************************",
                                    appPrdtStatus: "01",
                                    appPrdtCancelCause: null,
                                    prdtCode: "T6000078",
                                    processType: "00",
                                    prdtTypeCode: "0006",
                                    price: "0",
                                    billingStartDate: "********",
                                    billingEndDate: "********",
                                    prdtSerialno: 914,
                                    parentPrdtSerialno: 2,
                                    appPrdtDtlList: [
                                        {
                                            prdtAttributeCode: "********",
                                            prdtAttributeValue: "********",
                                        },
                                        {
                                            prdtAttributeCode: "********",
                                            prdtAttributeValue: "29",
                                        },
                                    ],
                                },
                                {
                                    accountId: "AC00000000372",
                                    accountPrdtId: "**************************",
                                    appPrdtStatus: "01",
                                    appPrdtCancelCause: null,
                                    prdtCode: "T7000001",
                                    processType: "00",
                                    prdtTypeCode: "0007",
                                    price: "0",
                                    billingStartDate: "********",
                                    billingEndDate: "********",
                                    prdtSerialno: 915,
                                    parentPrdtSerialno: 2,
                                    appPrdtDtlList: [
                                        {
                                            prdtAttributeCode: "********",
                                            prdtAttributeValue: "********",
                                        },
                                        {
                                            prdtAttributeCode: "********",
                                            prdtAttributeValue: "代理店A",
                                        },
                                        {
                                            prdtAttributeCode: "00000059",
                                            prdtAttributeValue: "NTTコム",
                                        },
                                        {
                                            prdtAttributeCode: "00000060",
                                            prdtAttributeValue: "担当次郎",
                                        },
                                        {
                                            prdtAttributeCode: "00000061",
                                            prdtAttributeValue: "0356564444",
                                        },
                                        {
                                            prdtAttributeCode: "00000062",
                                            prdtAttributeValue: "0399998888",
                                        },
                                        {
                                            prdtAttributeCode: "00000063",
                                            prdtAttributeValue:
                                                "<EMAIL>",
                                        },
                                        {
                                            prdtAttributeCode: "00000064",
                                            prdtAttributeValue: "担当三郎",
                                        },
                                        {
                                            prdtAttributeCode: "00000065",
                                            prdtAttributeValue: "0399998888",
                                        },
                                        {
                                            prdtAttributeCode: "00000055",
                                            prdtAttributeValue: "99991234123",
                                        },
                                        {
                                            prdtAttributeCode: "00000054",
                                            prdtAttributeValue: "12",
                                        },
                                        {
                                            prdtAttributeCode: "00000066",
                                            prdtAttributeValue: "0377774444",
                                        },
                                        {
                                            prdtAttributeCode: "00000067",
                                            prdtAttributeValue:
                                                "<EMAIL>",
                                        },
                                        {
                                            prdtAttributeCode: "00000081",
                                            prdtAttributeValue: "卸試験０９",
                                        },
                                        {
                                            prdtAttributeCode: "00000082",
                                            prdtAttributeValue: "ｵﾛｼｼｹﾝｾﾞﾛｷｭｳ",
                                        },
                                        {
                                            prdtAttributeCode: "00000083",
                                            prdtAttributeValue: "連動試験担当",
                                        },
                                        {
                                            prdtAttributeCode: "00000084",
                                            prdtAttributeValue: "事務担当太郎",
                                        },
                                        {
                                            prdtAttributeCode: "00000085",
                                            prdtAttributeValue: "03232356565",
                                        },
                                        {
                                            prdtAttributeCode: "00000086",
                                            prdtAttributeValue:
                                                "<EMAIL>",
                                        },
                                        {
                                            prdtAttributeCode: "00000087",
                                            prdtAttributeValue: "事業所利用",
                                        },
                                        {
                                            prdtAttributeCode: "J4000001",
                                            prdtAttributeValue: "1",
                                        },
                                        {
                                            prdtAttributeCode: "J4000301",
                                            prdtAttributeValue: "1420063",
                                        },
                                        {
                                            prdtAttributeCode: "J4000401",
                                            prdtAttributeValue: "13109001001",
                                        },
                                        {
                                            prdtAttributeCode: "J4000501",
                                            prdtAttributeValue: "２",
                                        },
                                        {
                                            prdtAttributeCode: "J4000601",
                                            prdtAttributeValue: "サウスタワー",
                                        },
                                    ],
                                },
                                {
                                    accountId: "AC00000000372",
                                    accountPrdtId: "**************************",
                                    appPrdtStatus: "01",
                                    appPrdtCancelCause: null,
                                    prdtCode: "TB000001",
                                    processType: "00",
                                    prdtTypeCode: "0011",
                                    price: "0",
                                    billingStartDate: "********",
                                    billingEndDate: "********",
                                    prdtSerialno: 916,
                                    parentPrdtSerialno: 2,
                                    appPrdtDtlList: [],
                                },
                                {
                                    accountId: "AC00000000372",
                                    accountPrdtId: "**************************",
                                    appPrdtStatus: "01",
                                    appPrdtCancelCause: null,
                                    prdtCode: "T9000001",
                                    processType: "01",
                                    prdtTypeCode: "0009",
                                    price: null,
                                    billingStartDate: null,
                                    billingEndDate: null,
                                    prdtSerialno: 917,
                                    parentPrdtSerialno: 2,
                                    appPrdtDtlList: [
                                        {
                                            prdtAttributeCode: "S1000001",
                                            prdtAttributeValue: "0",
                                        },
                                    ],
                                },
                                {
                                    accountId: "AC00000000372",
                                    accountPrdtId: "**************************",
                                    appPrdtStatus: "01",
                                    appPrdtCancelCause: null,
                                    prdtCode: "T9000002",
                                    processType: "01",
                                    prdtTypeCode: "0009",
                                    price: null,
                                    billingStartDate: null,
                                    billingEndDate: null,
                                    prdtSerialno: 918,
                                    parentPrdtSerialno: 2,
                                    appPrdtDtlList: [
                                        {
                                            prdtAttributeCode: "********",
                                            prdtAttributeValue: "0",
                                        },
                                        {
                                            prdtAttributeCode: "S1000001",
                                            prdtAttributeValue: "0",
                                        },
                                    ],
                                },
                                {
                                    accountId: "AC00000000372",
                                    accountPrdtId: "**************************",
                                    appPrdtStatus: "01",
                                    appPrdtCancelCause: null,
                                    prdtCode: "T9000003",
                                    processType: "01",
                                    prdtTypeCode: "0009",
                                    price: null,
                                    billingStartDate: null,
                                    billingEndDate: null,
                                    prdtSerialno: 919,
                                    parentPrdtSerialno: 2,
                                    appPrdtDtlList: [
                                        {
                                            prdtAttributeCode: "********",
                                            prdtAttributeValue: "0",
                                        },
                                        {
                                            prdtAttributeCode: "********",
                                            prdtAttributeValue: "0",
                                        },
                                        {
                                            prdtAttributeCode: "S1000003",
                                            prdtAttributeValue: "0",
                                        },
                                        {
                                            prdtAttributeCode: "S1000004",
                                            prdtAttributeValue: "0",
                                        },
                                    ],
                                },
                                {
                                    accountId: "AC00000000372",
                                    accountPrdtId: "**************************",
                                    appPrdtStatus: "01",
                                    appPrdtCancelCause: null,
                                    prdtCode: "TA000001",
                                    processType: "01",
                                    prdtTypeCode: "0010",
                                    price: null,
                                    billingStartDate: null,
                                    billingEndDate: null,
                                    prdtSerialno: 920,
                                    parentPrdtSerialno: 2,
                                    appPrdtDtlList: [
                                        {
                                            prdtAttributeCode: "********",
                                            prdtAttributeValue: "",
                                        },
                                        {
                                            prdtAttributeCode: "********",
                                            prdtAttributeValue: "",
                                        },
                                        {
                                            prdtAttributeCode: "S1000001",
                                            prdtAttributeValue: "0",
                                        },
                                    ],
                                },
                                {
                                    accountId: "AC00000000372",
                                    accountPrdtId: "**************************",
                                    appPrdtStatus: "01",
                                    appPrdtCancelCause: null,
                                    prdtCode: "T5000002",
                                    processType: "01",
                                    prdtTypeCode: "0005",
                                    price: null,
                                    billingStartDate: null,
                                    billingEndDate: null,
                                    prdtSerialno: 921,
                                    parentPrdtSerialno: 2,
                                    appPrdtDtlList: [
                                        {
                                            prdtAttributeCode: "********",
                                            prdtAttributeValue: "0",
                                        },
                                    ],
                                },
                                {
                                    accountId: "AC00000000372",
                                    accountPrdtId: "**************************",
                                    appPrdtStatus: "01",
                                    appPrdtCancelCause: null,
                                    prdtCode: "T5000005",
                                    processType: "01",
                                    prdtTypeCode: "0005",
                                    price: null,
                                    billingStartDate: null,
                                    billingEndDate: null,
                                    prdtSerialno: 922,
                                    parentPrdtSerialno: 2,
                                    appPrdtDtlList: [
                                        {
                                            prdtAttributeCode: "********",
                                            prdtAttributeValue: "0",
                                        },
                                    ],
                                },
                            ],
                            appPrdtLinkList: [
                                {
                                    parentAccountPrdtId:
                                        "**************************",
                                    childAccountPrdtId:
                                        "**************************",
                                },
                                {
                                    parentAccountPrdtId:
                                        "**************************",
                                    childAccountPrdtId:
                                        "**************************",
                                },
                                {
                                    parentAccountPrdtId:
                                        "**************************",
                                    childAccountPrdtId:
                                        "**************************",
                                },
                                {
                                    parentAccountPrdtId:
                                        "**************************",
                                    childAccountPrdtId:
                                        "**************************",
                                },
                                {
                                    parentAccountPrdtId:
                                        "**************************",
                                    childAccountPrdtId:
                                        "**************************",
                                },
                                {
                                    parentAccountPrdtId:
                                        "**************************",
                                    childAccountPrdtId:
                                        "**************************",
                                },
                                {
                                    parentAccountPrdtId:
                                        "**************************",
                                    childAccountPrdtId:
                                        "**************************",
                                },
                                {
                                    parentAccountPrdtId:
                                        "**************************",
                                    childAccountPrdtId:
                                        "**************************",
                                },
                            ],
                            appErrorInfo: null,
                            checkErrorInfo: null,
                        },
                    ],
                },
            },
        },
        responseParam: {
            commonHeaderInfo: {
                requestFromSystemId: "MAP",
                otherSystemSendDatetime: "**************",
                operatorId: "ZZZZZZZZ",
                operatorGroupId: "ZZZZZZZZZZ",
                serviceProcessResultType: "0",
                serviceProcessResultDtlType: null,
            },
            appResponseInfo: {
                processType: "03",
                resultCode: "0001",
                appInfoList: [
                    {
                        appBasic: {
                            receiptId: "***************",
                            inputFromType: "01",
                            receiptIdSerialno: 1,
                            orderType: "11",
                            appStatus: "15",
                            appDate: "********",
                            requestDate: "********",
                            salesChannelCode: "79100100",
                            salesChannelName: "金沢OCNサービスセンタ",
                            inputFromReceiptId: null,
                            registName: null,
                            serviceStartDate: "********",
                            firstRequestFromSystemId: "MAP",
                            lastRequestFromSystemId: "MAP",
                            appBasicDtlList: [
                                {
                                    appAttributeCode: "M0000001",
                                    appAttributeValue: "AP00000********",
                                },
                                {
                                    appAttributeCode: "M0000002",
                                    appAttributeValue: "004",
                                },
                                {
                                    appAttributeCode: "M0000013",
                                    appAttributeValue: "N178001009",
                                },
                                {
                                    appAttributeCode: "M0000012",
                                    appAttributeValue: "09010002010",
                                },
                                {
                                    appAttributeCode: "M0000006",
                                    appAttributeValue: "20250417170337",
                                },
                                {
                                    appAttributeCode: "M0000007",
                                    appAttributeValue: "",
                                },
                                {
                                    appAttributeCode: "M0000004",
                                    appAttributeValue: "20250417170337",
                                },
                                {
                                    appAttributeCode: "M0000005",
                                    appAttributeValue: "",
                                },
                            ],
                            appTying: {
                                contractReceiptId: null,
                            },
                        },
                        appContract: null,
                        appPrdtList: [
                            {
                                accountId: "AC00000000372",
                                accountPrdtId: "**************************",
                                appPrdtStatus: "01",
                                appPrdtCancelCause: null,
                                prdtCode: "T2000232",
                                processType: "01",
                                prdtTypeCode: "0002",
                                price: null,
                                billingStartDate: null,
                                billingEndDate: null,
                                prdtSerialno: 1,
                                parentPrdtSerialno: 2,
                                appPrdtDtlList: [
                                    {
                                        prdtAttributeCode: "********",
                                        prdtAttributeValue: "DN09881188882",
                                    },
                                    {
                                        prdtAttributeCode: "********",
                                        prdtAttributeValue: "送付先宛先太郎",
                                    },
                                    {
                                        prdtAttributeCode: "J1000301",
                                        prdtAttributeValue: "2840015",
                                    },
                                    {
                                        prdtAttributeCode: "********",
                                        prdtAttributeValue: "***************",
                                    },
                                    {
                                        prdtAttributeCode: "00000017",
                                        prdtAttributeValue: "1",
                                    },
                                    {
                                        prdtAttributeCode: "00000016",
                                        prdtAttributeValue: "20170415",
                                    },
                                    {
                                        prdtAttributeCode: "J1000001",
                                        prdtAttributeValue: "1",
                                    },
                                    {
                                        prdtAttributeCode: "J1000601",
                                        prdtAttributeValue: "サウスタワー",
                                    },
                                    {
                                        prdtAttributeCode: "J1000501",
                                        prdtAttributeValue: "２",
                                    },
                                    {
                                        prdtAttributeCode: "00000011",
                                        prdtAttributeValue: "09010002010",
                                    },
                                    {
                                        prdtAttributeCode: "J1000401",
                                        prdtAttributeValue: "12228012001",
                                    },
                                    {
                                        prdtAttributeCode: "00000008",
                                        prdtAttributeValue: "0",
                                    },
                                    {
                                        prdtAttributeCode: "00000019",
                                        prdtAttributeValue: "送付先部課",
                                    },
                                    {
                                        prdtAttributeCode: "00000021",
                                        prdtAttributeValue: "0355554444",
                                    },
                                    {
                                        prdtAttributeCode: "********",
                                        prdtAttributeValue: "担当花子",
                                    },
                                    {
                                        prdtAttributeCode: "********",
                                        prdtAttributeValue: "",
                                    },
                                ],
                            },
                            {
                                accountId: "AC00000000372",
                                accountPrdtId: "**************************",
                                appPrdtStatus: "01",
                                appPrdtCancelCause: null,
                                prdtCode: "T1000001",
                                processType: "02",
                                prdtTypeCode: "0001",
                                price: "0",
                                billingStartDate: "********",
                                billingEndDate: "********",
                                prdtSerialno: 2,
                                parentPrdtSerialno: 0,
                                appPrdtDtlList: [
                                    {
                                        prdtAttributeCode: "********",
                                        prdtAttributeValue: "4",
                                    },
                                    {
                                        prdtAttributeCode: "********",
                                        prdtAttributeValue: "0",
                                    },
                                    {
                                        prdtAttributeCode: "00000003",
                                        prdtAttributeValue: "0",
                                    },
                                    {
                                        prdtAttributeCode: "********",
                                        prdtAttributeValue: "********",
                                    },
                                    {
                                        prdtAttributeCode: "********",
                                        prdtAttributeValue: "9",
                                    },
                                ],
                            },
                            {
                                accountId: "AC00000000372",
                                accountPrdtId: "**************************",
                                appPrdtStatus: "01",
                                appPrdtCancelCause: null,
                                prdtCode: "T2000232",
                                processType: "03",
                                prdtTypeCode: "0002",
                                price: "0",
                                billingStartDate: "********",
                                billingEndDate: "********",
                                prdtSerialno: 901,
                                parentPrdtSerialno: 2,
                                appPrdtDtlList: [
                                    {
                                        prdtAttributeCode: "********",
                                        prdtAttributeValue: "***************",
                                    },
                                    {
                                        prdtAttributeCode: "********",
                                        prdtAttributeValue: "DN09881188881",
                                    },
                                    {
                                        prdtAttributeCode: "00000008",
                                        prdtAttributeValue: "0",
                                    },
                                    {
                                        prdtAttributeCode: "00000011",
                                        prdtAttributeValue: "09010002010",
                                    },
                                    {
                                        prdtAttributeCode: "00000016",
                                        prdtAttributeValue: "20170415",
                                    },
                                    {
                                        prdtAttributeCode: "00000017",
                                        prdtAttributeValue: "1",
                                    },
                                    {
                                        prdtAttributeCode: "********",
                                        prdtAttributeValue: "送付先宛先太郎",
                                    },
                                    {
                                        prdtAttributeCode: "00000019",
                                        prdtAttributeValue: "送付先部課",
                                    },
                                    {
                                        prdtAttributeCode: "********",
                                        prdtAttributeValue: "担当花子",
                                    },
                                    {
                                        prdtAttributeCode: "00000021",
                                        prdtAttributeValue: "0355554444",
                                    },
                                    {
                                        prdtAttributeCode: "********",
                                        prdtAttributeValue: "1703230003724",
                                    },
                                    {
                                        prdtAttributeCode: "J1000001",
                                        prdtAttributeValue: "1",
                                    },
                                    {
                                        prdtAttributeCode: "J1000301",
                                        prdtAttributeValue: "2840015",
                                    },
                                    {
                                        prdtAttributeCode: "J1000401",
                                        prdtAttributeValue: "12228012001",
                                    },
                                    {
                                        prdtAttributeCode: "J1000501",
                                        prdtAttributeValue: "２",
                                    },
                                    {
                                        prdtAttributeCode: "J1000601",
                                        prdtAttributeValue: "サウスタワー",
                                    },
                                ],
                            },
                            {
                                accountId: "AC00000000372",
                                accountPrdtId: "**************************",
                                appPrdtStatus: "01",
                                appPrdtCancelCause: null,
                                prdtCode: "T3000297",
                                processType: "00",
                                prdtTypeCode: "0003",
                                price: "0",
                                billingStartDate: "********",
                                billingEndDate: "********",
                                prdtSerialno: 902,
                                parentPrdtSerialno: 2,
                                appPrdtDtlList: [
                                    {
                                        prdtAttributeCode: "********",
                                        prdtAttributeValue: "2",
                                    },
                                    {
                                        prdtAttributeCode: "********",
                                        prdtAttributeValue: "2",
                                    },
                                ],
                            },
                            {
                                accountId: "AC00000000372",
                                accountPrdtId: "**************************",
                                appPrdtStatus: "01",
                                appPrdtCancelCause: null,
                                prdtCode: "T4000001",
                                processType: "00",
                                prdtTypeCode: "0004",
                                price: "0",
                                billingStartDate: "********",
                                billingEndDate: "********",
                                prdtSerialno: 903,
                                parentPrdtSerialno: 2,
                                appPrdtDtlList: [
                                    {
                                        prdtAttributeCode: "********",
                                        prdtAttributeValue: "192.168.168.233",
                                    },
                                    {
                                        prdtAttributeCode: "********",
                                        prdtAttributeValue: "192.168.168.230",
                                    },
                                    {
                                        prdtAttributeCode: "00000025",
                                        prdtAttributeValue: "1",
                                    },
                                    {
                                        prdtAttributeCode: "00000038",
                                        prdtAttributeValue: "1",
                                    },
                                    {
                                        prdtAttributeCode: "C0000002",
                                        prdtAttributeValue: "ORSI1015",
                                    },
                                    {
                                        prdtAttributeCode: "C0000001",
                                        prdtAttributeValue: "orsitestdomain19",
                                    },
                                    {
                                        prdtAttributeCode: "********",
                                        prdtAttributeValue: "",
                                    },
                                    {
                                        prdtAttributeCode: "********",
                                        prdtAttributeValue: "",
                                    },
                                ],
                            },
                            {
                                accountId: "AC00000000372",
                                accountPrdtId: "**************************",
                                appPrdtStatus: "01",
                                appPrdtCancelCause: null,
                                prdtCode: "T6000010",
                                processType: "00",
                                prdtTypeCode: "0006",
                                price: "0",
                                billingStartDate: "********",
                                billingEndDate: "********",
                                prdtSerialno: 904,
                                parentPrdtSerialno: 2,
                                appPrdtDtlList: [
                                    {
                                        prdtAttributeCode: "********",
                                        prdtAttributeValue: "********",
                                    },
                                    {
                                        prdtAttributeCode: "********",
                                        prdtAttributeValue: "00054",
                                    },
                                ],
                            },
                            {
                                accountId: "AC00000000372",
                                accountPrdtId: "**************************",
                                appPrdtStatus: "01",
                                appPrdtCancelCause: null,
                                prdtCode: "T6000010",
                                processType: "00",
                                prdtTypeCode: "0006",
                                price: "0",
                                billingStartDate: "********",
                                billingEndDate: "********",
                                prdtSerialno: 905,
                                parentPrdtSerialno: 2,
                                appPrdtDtlList: [
                                    {
                                        prdtAttributeCode: "********",
                                        prdtAttributeValue: "********",
                                    },
                                    {
                                        prdtAttributeCode: "********",
                                        prdtAttributeValue: "00055",
                                    },
                                ],
                            },
                            {
                                accountId: "AC00000000372",
                                accountPrdtId: "**************************",
                                appPrdtStatus: "01",
                                appPrdtCancelCause: null,
                                prdtCode: "T6000010",
                                processType: "00",
                                prdtTypeCode: "0006",
                                price: "0",
                                billingStartDate: "********",
                                billingEndDate: "********",
                                prdtSerialno: 906,
                                parentPrdtSerialno: 2,
                                appPrdtDtlList: [
                                    {
                                        prdtAttributeCode: "********",
                                        prdtAttributeValue: "********",
                                    },
                                    {
                                        prdtAttributeCode: "********",
                                        prdtAttributeValue: "00066",
                                    },
                                ],
                            },
                            {
                                accountId: "AC00000000372",
                                accountPrdtId: "**************************",
                                appPrdtStatus: "01",
                                appPrdtCancelCause: null,
                                prdtCode: "T6000010",
                                processType: "00",
                                prdtTypeCode: "0006",
                                price: "0",
                                billingStartDate: "********",
                                billingEndDate: "********",
                                prdtSerialno: 907,
                                parentPrdtSerialno: 2,
                                appPrdtDtlList: [
                                    {
                                        prdtAttributeCode: "********",
                                        prdtAttributeValue: "********",
                                    },
                                    {
                                        prdtAttributeCode: "********",
                                        prdtAttributeValue: "00067",
                                    },
                                ],
                            },
                            {
                                accountId: "AC00000000372",
                                accountPrdtId: "**************************",
                                appPrdtStatus: "01",
                                appPrdtCancelCause: null,
                                prdtCode: "T6000011",
                                processType: "00",
                                prdtTypeCode: "0006",
                                price: "0",
                                billingStartDate: "********",
                                billingEndDate: "********",
                                prdtSerialno: 908,
                                parentPrdtSerialno: 2,
                                appPrdtDtlList: [
                                    {
                                        prdtAttributeCode: "********",
                                        prdtAttributeValue: "********",
                                    },
                                    {
                                        prdtAttributeCode: "********",
                                        prdtAttributeValue: "37",
                                    },
                                ],
                            },
                            {
                                accountId: "AC00000000372",
                                accountPrdtId: "**************************",
                                appPrdtStatus: "01",
                                appPrdtCancelCause: null,
                                prdtCode: "T6000024",
                                processType: "00",
                                prdtTypeCode: "0006",
                                price: "0",
                                billingStartDate: "********",
                                billingEndDate: "********",
                                prdtSerialno: 909,
                                parentPrdtSerialno: 2,
                                appPrdtDtlList: [
                                    {
                                        prdtAttributeCode: "********",
                                        prdtAttributeValue: "********",
                                    },
                                    {
                                        prdtAttributeCode: "********",
                                        prdtAttributeValue: "32",
                                    },
                                ],
                            },
                            {
                                accountId: "AC00000000372",
                                accountPrdtId: "**************************",
                                appPrdtStatus: "01",
                                appPrdtCancelCause: null,
                                prdtCode: "T6000042",
                                processType: "00",
                                prdtTypeCode: "0006",
                                price: "0",
                                billingStartDate: "********",
                                billingEndDate: "********",
                                prdtSerialno: 910,
                                parentPrdtSerialno: 2,
                                appPrdtDtlList: [
                                    {
                                        prdtAttributeCode: "********",
                                        prdtAttributeValue: "********",
                                    },
                                    {
                                        prdtAttributeCode: "********",
                                        prdtAttributeValue: "27",
                                    },
                                ],
                            },
                            {
                                accountId: "AC00000000372",
                                accountPrdtId: "**************************",
                                appPrdtStatus: "01",
                                appPrdtCancelCause: null,
                                prdtCode: "T6000048",
                                processType: "00",
                                prdtTypeCode: "0006",
                                price: "0",
                                billingStartDate: "********",
                                billingEndDate: "********",
                                prdtSerialno: 911,
                                parentPrdtSerialno: 2,
                                appPrdtDtlList: [
                                    {
                                        prdtAttributeCode: "********",
                                        prdtAttributeValue: "********",
                                    },
                                    {
                                        prdtAttributeCode: "********",
                                        prdtAttributeValue: "31",
                                    },
                                ],
                            },
                            {
                                accountId: "AC00000000372",
                                accountPrdtId: "**************************",
                                appPrdtStatus: "01",
                                appPrdtCancelCause: null,
                                prdtCode: "T6000061",
                                processType: "00",
                                prdtTypeCode: "0006",
                                price: "0",
                                billingStartDate: "********",
                                billingEndDate: "********",
                                prdtSerialno: 912,
                                parentPrdtSerialno: 2,
                                appPrdtDtlList: [
                                    {
                                        prdtAttributeCode: "********",
                                        prdtAttributeValue: "********",
                                    },
                                    {
                                        prdtAttributeCode: "********",
                                        prdtAttributeValue: "28",
                                    },
                                ],
                            },
                            {
                                accountId: "AC00000000372",
                                accountPrdtId: "**************************",
                                appPrdtStatus: "01",
                                appPrdtCancelCause: null,
                                prdtCode: "T6000076",
                                processType: "00",
                                prdtTypeCode: "0006",
                                price: "0",
                                billingStartDate: "********",
                                billingEndDate: "********",
                                prdtSerialno: 913,
                                parentPrdtSerialno: 2,
                                appPrdtDtlList: [
                                    {
                                        prdtAttributeCode: "********",
                                        prdtAttributeValue: "********",
                                    },
                                    {
                                        prdtAttributeCode: "********",
                                        prdtAttributeValue: "30",
                                    },
                                ],
                            },
                            {
                                accountId: "AC00000000372",
                                accountPrdtId: "**************************",
                                appPrdtStatus: "01",
                                appPrdtCancelCause: null,
                                prdtCode: "T6000078",
                                processType: "00",
                                prdtTypeCode: "0006",
                                price: "0",
                                billingStartDate: "********",
                                billingEndDate: "********",
                                prdtSerialno: 914,
                                parentPrdtSerialno: 2,
                                appPrdtDtlList: [
                                    {
                                        prdtAttributeCode: "********",
                                        prdtAttributeValue: "********",
                                    },
                                    {
                                        prdtAttributeCode: "********",
                                        prdtAttributeValue: "29",
                                    },
                                ],
                            },
                            {
                                accountId: "AC00000000372",
                                accountPrdtId: "**************************",
                                appPrdtStatus: "01",
                                appPrdtCancelCause: null,
                                prdtCode: "T7000001",
                                processType: "00",
                                prdtTypeCode: "0007",
                                price: "0",
                                billingStartDate: "********",
                                billingEndDate: "********",
                                prdtSerialno: 915,
                                parentPrdtSerialno: 2,
                                appPrdtDtlList: [
                                    {
                                        prdtAttributeCode: "********",
                                        prdtAttributeValue: "********",
                                    },
                                    {
                                        prdtAttributeCode: "********",
                                        prdtAttributeValue: "代理店A",
                                    },
                                    {
                                        prdtAttributeCode: "00000059",
                                        prdtAttributeValue: "NTTコム",
                                    },
                                    {
                                        prdtAttributeCode: "00000060",
                                        prdtAttributeValue: "担当次郎",
                                    },
                                    {
                                        prdtAttributeCode: "00000061",
                                        prdtAttributeValue: "0356564444",
                                    },
                                    {
                                        prdtAttributeCode: "00000062",
                                        prdtAttributeValue: "0399998888",
                                    },
                                    {
                                        prdtAttributeCode: "00000063",
                                        prdtAttributeValue: "<EMAIL>",
                                    },
                                    {
                                        prdtAttributeCode: "00000064",
                                        prdtAttributeValue: "担当三郎",
                                    },
                                    {
                                        prdtAttributeCode: "00000065",
                                        prdtAttributeValue: "0399998888",
                                    },
                                    {
                                        prdtAttributeCode: "00000055",
                                        prdtAttributeValue: "99991234123",
                                    },
                                    {
                                        prdtAttributeCode: "00000054",
                                        prdtAttributeValue: "12",
                                    },
                                    {
                                        prdtAttributeCode: "00000066",
                                        prdtAttributeValue: "0377774444",
                                    },
                                    {
                                        prdtAttributeCode: "00000067",
                                        prdtAttributeValue: "<EMAIL>",
                                    },
                                    {
                                        prdtAttributeCode: "00000081",
                                        prdtAttributeValue: "卸試験０９",
                                    },
                                    {
                                        prdtAttributeCode: "00000082",
                                        prdtAttributeValue: "ｵﾛｼｼｹﾝｾﾞﾛｷｭｳ",
                                    },
                                    {
                                        prdtAttributeCode: "00000083",
                                        prdtAttributeValue: "連動試験担当",
                                    },
                                    {
                                        prdtAttributeCode: "00000084",
                                        prdtAttributeValue: "事務担当太郎",
                                    },
                                    {
                                        prdtAttributeCode: "00000085",
                                        prdtAttributeValue: "03232356565",
                                    },
                                    {
                                        prdtAttributeCode: "00000086",
                                        prdtAttributeValue: "<EMAIL>",
                                    },
                                    {
                                        prdtAttributeCode: "00000087",
                                        prdtAttributeValue: "事業所利用",
                                    },
                                    {
                                        prdtAttributeCode: "J4000001",
                                        prdtAttributeValue: "1",
                                    },
                                    {
                                        prdtAttributeCode: "J4000301",
                                        prdtAttributeValue: "1420063",
                                    },
                                    {
                                        prdtAttributeCode: "J4000401",
                                        prdtAttributeValue: "13109001001",
                                    },
                                    {
                                        prdtAttributeCode: "J4000501",
                                        prdtAttributeValue: "２",
                                    },
                                    {
                                        prdtAttributeCode: "J4000601",
                                        prdtAttributeValue: "サウスタワー",
                                    },
                                ],
                            },
                            {
                                accountId: "AC00000000372",
                                accountPrdtId: "**************************",
                                appPrdtStatus: "01",
                                appPrdtCancelCause: null,
                                prdtCode: "TB000001",
                                processType: "00",
                                prdtTypeCode: "0011",
                                price: "0",
                                billingStartDate: "********",
                                billingEndDate: "********",
                                prdtSerialno: 916,
                                parentPrdtSerialno: 2,
                                appPrdtDtlList: [],
                            },
                            {
                                accountId: "AC00000000372",
                                accountPrdtId: "**************************",
                                appPrdtStatus: "01",
                                appPrdtCancelCause: null,
                                prdtCode: "T9000001",
                                processType: "01",
                                prdtTypeCode: "0009",
                                price: null,
                                billingStartDate: null,
                                billingEndDate: null,
                                prdtSerialno: 917,
                                parentPrdtSerialno: 2,
                                appPrdtDtlList: [
                                    {
                                        prdtAttributeCode: "S1000001",
                                        prdtAttributeValue: "0",
                                    },
                                ],
                            },
                            {
                                accountId: "AC00000000372",
                                accountPrdtId: "**************************",
                                appPrdtStatus: "01",
                                appPrdtCancelCause: null,
                                prdtCode: "T9000002",
                                processType: "01",
                                prdtTypeCode: "0009",
                                price: null,
                                billingStartDate: null,
                                billingEndDate: null,
                                prdtSerialno: 918,
                                parentPrdtSerialno: 2,
                                appPrdtDtlList: [
                                    {
                                        prdtAttributeCode: "********",
                                        prdtAttributeValue: "0",
                                    },
                                    {
                                        prdtAttributeCode: "S1000001",
                                        prdtAttributeValue: "0",
                                    },
                                ],
                            },
                            {
                                accountId: "AC00000000372",
                                accountPrdtId: "**************************",
                                appPrdtStatus: "01",
                                appPrdtCancelCause: null,
                                prdtCode: "T9000003",
                                processType: "01",
                                prdtTypeCode: "0009",
                                price: null,
                                billingStartDate: null,
                                billingEndDate: null,
                                prdtSerialno: 919,
                                parentPrdtSerialno: 2,
                                appPrdtDtlList: [
                                    {
                                        prdtAttributeCode: "********",
                                        prdtAttributeValue: "0",
                                    },
                                    {
                                        prdtAttributeCode: "********",
                                        prdtAttributeValue: "0",
                                    },
                                    {
                                        prdtAttributeCode: "S1000003",
                                        prdtAttributeValue: "0",
                                    },
                                    {
                                        prdtAttributeCode: "S1000004",
                                        prdtAttributeValue: "0",
                                    },
                                ],
                            },
                            {
                                accountId: "AC00000000372",
                                accountPrdtId: "**************************",
                                appPrdtStatus: "01",
                                appPrdtCancelCause: null,
                                prdtCode: "TA000001",
                                processType: "01",
                                prdtTypeCode: "0010",
                                price: null,
                                billingStartDate: null,
                                billingEndDate: null,
                                prdtSerialno: 920,
                                parentPrdtSerialno: 2,
                                appPrdtDtlList: [
                                    {
                                        prdtAttributeCode: "********",
                                        prdtAttributeValue: "",
                                    },
                                    {
                                        prdtAttributeCode: "********",
                                        prdtAttributeValue: "",
                                    },
                                    {
                                        prdtAttributeCode: "S1000001",
                                        prdtAttributeValue: "0",
                                    },
                                ],
                            },
                            {
                                accountId: "AC00000000372",
                                accountPrdtId: "**************************",
                                appPrdtStatus: "01",
                                appPrdtCancelCause: null,
                                prdtCode: "T5000002",
                                processType: "01",
                                prdtTypeCode: "0005",
                                price: null,
                                billingStartDate: null,
                                billingEndDate: null,
                                prdtSerialno: 921,
                                parentPrdtSerialno: 2,
                                appPrdtDtlList: [
                                    {
                                        prdtAttributeCode: "********",
                                        prdtAttributeValue: "0",
                                    },
                                ],
                            },
                            {
                                accountId: "AC00000000372",
                                accountPrdtId: "**************************",
                                appPrdtStatus: "01",
                                appPrdtCancelCause: null,
                                prdtCode: "T5000005",
                                processType: "01",
                                prdtTypeCode: "0005",
                                price: null,
                                billingStartDate: null,
                                billingEndDate: null,
                                prdtSerialno: 922,
                                parentPrdtSerialno: 2,
                                appPrdtDtlList: [
                                    {
                                        prdtAttributeCode: "********",
                                        prdtAttributeValue: "0",
                                    },
                                ],
                            },
                        ],
                        appPrdtLinkList: [
                            {
                                parentAccountPrdtId:
                                    "**************************",
                                childAccountPrdtId:
                                    "**************************",
                            },
                            {
                                parentAccountPrdtId:
                                    "**************************",
                                childAccountPrdtId:
                                    "**************************",
                            },
                            {
                                parentAccountPrdtId:
                                    "**************************",
                                childAccountPrdtId:
                                    "**************************",
                            },
                            {
                                parentAccountPrdtId:
                                    "**************************",
                                childAccountPrdtId:
                                    "**************************",
                            },
                            {
                                parentAccountPrdtId:
                                    "**************************",
                                childAccountPrdtId:
                                    "**************************",
                            },
                            {
                                parentAccountPrdtId:
                                    "**************************",
                                childAccountPrdtId:
                                    "**************************",
                            },
                            {
                                parentAccountPrdtId:
                                    "**************************",
                                childAccountPrdtId:
                                    "**************************",
                            },
                            {
                                parentAccountPrdtId:
                                    "**************************",
                                childAccountPrdtId:
                                    "**************************",
                            },
                        ],
                        appErrorInfo: null,
                        checkErrorInfo: null,
                    },
                ],
            },
        },
    };
}
