import { describe, expect, test } from "@jest/globals";
import { HttpRequest } from "@azure/functions";
import DefaultContext, {extendedPortalInvocationContext} from "../../testing/DefaultContext";
import DefaultRequest from "../../testing/DefaultRequest";
import CoreSwimmyService from "@/services/coreSwimmyService";
import {GetMaintenanceModeStatusHandler} from "@/functions/portal/GetMaintenanceModeStatusHandler";
import {ToggleMaintenanceModeStatusHandler} from "@/functions/portal/ToggleMaintenanceModeStatusHandler";
import AppConfig from "@/appconfig";

describe("functions/portal", () => {
    const request = DefaultRequest;

    beforeAll(async () => {
        await AppConfig.loadCoreMvnoConfig(DefaultContext);
    })

    describe("GetMaintenanceModeStatusHandler", () => {
        test("should return maintenance mode is on and setter function result should match getter function result", async () => {
            const newRequest = {
                ...request,
                json: () => { 
                    return {switch: 'on'}
                }
            } as unknown as HttpRequest;
            const setResult = await ToggleMaintenanceModeStatusHandler.Handler(newRequest, extendedPortalInvocationContext);
            const getResult = await GetMaintenanceModeStatusHandler.Handler(request, extendedPortalInvocationContext);
            expect(getResult.jsonBody.result.maintenance_mode).toBe(setResult.jsonBody.result.maintenance_mode);
        });

        test("should return maintenance mode is off and setter function result should match getter function result", async () => {
            const newRequest = {
                ...request,
                json: () => { 
                    return {switch: 'off'}
                }
            } as unknown as HttpRequest;
            const setResult = await ToggleMaintenanceModeStatusHandler.Handler(newRequest, extendedPortalInvocationContext);
            const getResult = await GetMaintenanceModeStatusHandler.Handler(request, extendedPortalInvocationContext);
            expect(getResult.jsonBody.result.maintenance_mode).toBe(setResult.jsonBody.result.maintenance_mode);
        });

        test("should return an error when isMaintenanceMode throws an error", async () => {
            const errorMessage = 'This is an error'
            jest
                .spyOn(CoreSwimmyService.prototype, 'isMaintenanceMode')
                .mockImplementationOnce(() => {
                    throw new Error(errorMessage);
                });

            const result = await GetMaintenanceModeStatusHandler.Handler(request, extendedPortalInvocationContext);
            expect(String(result.body)).toBe(`Error: ${errorMessage}`);
        });
    });

    describe("ToggleMaintenanceModeStatusHandler", () => {
        test("should return maintenance mode is on after setting it to on", async () => {
            const newRequest = {
                ...request,
                json: () => { 
                    return {switch: 'on'}
                }
            } as unknown as HttpRequest;
            const result = await ToggleMaintenanceModeStatusHandler.Handler(newRequest, extendedPortalInvocationContext);
            expect(result.jsonBody.result.maintenance_mode).toBe('on');
        });

        test("should return maintenance mode is off after setting it to off", async () => {
            const newRequest = {
                ...request,
                json: () => { 
                    return {switch: 'off'}
                }
            } as unknown as HttpRequest;
            const result = await ToggleMaintenanceModeStatusHandler.Handler(newRequest, extendedPortalInvocationContext);
            expect(result.jsonBody.result.maintenance_mode).toBe('off');
        });

        test("should return error message when setting wrong value (not on nor off)", async () => {
            const newRequest = {
                ...request,
                json: () => { 
                    return {switch: 'asdf'}
                }
            } as unknown as HttpRequest;
            const result = await ToggleMaintenanceModeStatusHandler.Handler(newRequest, extendedPortalInvocationContext);
            expect(String(result.body)).toBe('Error: "switch" must be one of [on, off]');
        });

        test("should return error message when json parser fails", async () => {
            const newRequest = {
                ...request,
                json: () => { 
                    throw Error()
                }
            } as unknown as HttpRequest;
            const result = await ToggleMaintenanceModeStatusHandler.Handler(newRequest, extendedPortalInvocationContext);
            expect(String(result.body)).toBe('Error: Request body is empty');
        });
    });
});