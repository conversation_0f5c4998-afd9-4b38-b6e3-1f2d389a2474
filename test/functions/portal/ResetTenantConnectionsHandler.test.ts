import { ResetTenantConnectionsHandler } from "@/functions/portal/ResetTenantConnectionsHandler";
import { TenantsEntity } from "@/core/entity/TenantsEntity";
import { HttpRequest } from "@azure/functions";
import { usePsql } from "@/database/psql";
import { Sequelize } from "sequelize-typescript";
import { useRedis } from "@/database/redis";
import DefaultContext, { extendedPortalInvocationContext } from "../../testing/DefaultContext";
import AppConfig from "@/appconfig";

describe("ResetTenantConnectionsHandler Integration", () => {
    let tenant: any;
    let sequelize: Sequelize;
    let redisClient: any;

    beforeAll(async () => {
        sequelize = await usePsql();
        redisClient = await useRedis(DefaultContext);
        await AppConfig.loadCoreMvnoConfig(DefaultContext);
    });

    beforeEach(async () => {
        tenant = await TenantsEntity.create({
            tenantId: `ABC000`,
            tenantName: "Reset Test Tenant",
            tenantMaxConnection: 5,
            hashedPassword: "",
            office: true,
            status: true,
            tenantLevel: 1,
            customizeMaxConnection: 5,
            notvoiceRoamingFlag: false,
            pTenantId: "ZZZ000",
            csvOutputPattern: 1,
            tpc: "TpcDestIpAddress",
            tpc2: "TpcDestIpAddress2",
        });
    });

    afterEach(async () => {
        await TenantsEntity.destroy({ where: { tenantId: tenant.tenantId } });
        await redisClient.del(`conCount:${tenant.tenantId}`);
    });

    afterAll(async () => {
        await sequelize.close();
        await redisClient.quit();
    });

    it("resets connections for a tenant with existing connections", async () => {
        const now = Math.floor(Date.now() / 1000);
        await redisClient.zadd(`conCount:${tenant.tenantId}`, now, "invocation1", now + 1, "invocation2");
        await redisClient.expire(`conCount:${tenant.tenantId}`, 60);

        const membersOld = await redisClient.zrange(`conCount:${tenant.tenantId}`, 0, -1);
        expect(membersOld.length).toBe(2);

        const request = {
            json: async () => ({ tenantId: [tenant.tenantId] }),
        } as unknown as HttpRequest;

        const response = await ResetTenantConnectionsHandler.Handler(request, extendedPortalInvocationContext);

        expect(response.status).toBe(200);
        expect(response.jsonBody.message).toContain(tenant.tenantId);

        const members = await redisClient.zrange(`conCount:${tenant.tenantId}`, 0, -1);
        expect(members.length).toBe(0);
    });

    it("returns 400 if tenantId is missing", async () => {
        const request = {
            json: async () => ({}),
        } as unknown as HttpRequest;

        const response = await ResetTenantConnectionsHandler.Handler(request, extendedPortalInvocationContext);

        expect(response.status).toBe(400);
        expect(response.jsonBody.error).toBe("tenantId is required");
    });

    it("returns 400 if body is invalid JSON", async () => {
        const request = {
            json: async () => { throw new Error("Invalid JSON"); },
        } as unknown as HttpRequest;

        const response = await ResetTenantConnectionsHandler.Handler(request, extendedPortalInvocationContext);

        expect(response.status).toBe(400);
        expect(response.jsonBody.error).toBe("Invalid JSON body");
    });

    it("returns 200 even if tenant has no connections", async () => {
        const request = {
            json: async () => ({ tenantId: tenant.tenantId }),
        } as unknown as HttpRequest;

        const response = await ResetTenantConnectionsHandler.Handler(request, extendedPortalInvocationContext);

        expect(response.status).toBe(200);
        expect(response.jsonBody.message).toContain(tenant.tenantId);

        const members = await redisClient.zrange(`conCount:${tenant.tenantId}`, 0, -1);
        expect(members.length).toBe(0);
    });
});