import { describe, expect, test } from "@jest/globals";
import mongoose from "mongoose";
import { Sequelize } from "sequelize";
import {extendedPortalInvocationContext} from "../../testing/DefaultContext";
import DefaultRequest from "../../testing/DefaultRequest";
import DefaultContext from "../../testing/DefaultContext";
import { GetCoreSwimmyApiLogDetailHandler } from "@/functions/portal/GetCoreSwimmyApiLogDetailHandler";
import { disconnectMongo, useMongo } from "@/database/mongo";
import { CommonError } from "@/constants/commonError";
import ExtendedPortalInvocationContext from "@/types/ExtendedPortalInvocationContext";
import { generateProcessID, generateSOID } from "../../testing/TestHelper";
import { generateCoreSwimmyApiLog } from "./testdata/portal.testdata";
import CoreSwimmyApiLog from "@/models/coreSwimmyApiLog";
import { usePsql } from "@/database/psql";
import AppConfig from "@/appconfig";

const request = DefaultRequest;

// TODO remove this and use prefilled data
// data might not exist in your collection and thus filters won't work
// this is a valid request order ID assigned to a valid tenant ID which exists in postgresql DB
let VALID_REQUEST_ORDER_ID = generateProcessID();
const INVALID_REQUEST_ORDER_ID = 'AP100000524598';
const NOT_EXISTING_REQUEST_ORDER_ID = new mongoose.Types.ObjectId().toHexString();

describe("functions/portal/GetCoreSwimmyApiLogDetailHandler", () => {
    const requestOrderId = VALID_REQUEST_ORDER_ID;
    const soId = generateSOID();
    let sequelize: Sequelize;

    let internalId = ""; // ObjectId string
    
    beforeAll(async () => {
        await AppConfig.loadCoreMvnoConfig(DefaultContext);
        // TODO create some dummy data in mongo with valid data Postgresql DB
        await useMongo(DefaultContext);
        sequelize = await usePsql();
        const doc = generateCoreSwimmyApiLog(requestOrderId, soId);
        const saved = await CoreSwimmyApiLog.create(doc)
        internalId = saved.id;
    });

    afterAll(async () => {
        await CoreSwimmyApiLog.deleteMany({requestOrderId});
        await sequelize.close();
        await disconnectMongo(DefaultContext);
    });

    describe("valid request", () => {
        test("should return some valid data when requestOrderId is valid", async () => {
            request.params.requestOrderId = internalId;
            const { jsonBody } = await GetCoreSwimmyApiLogDetailHandler.Handler(request, extendedPortalInvocationContext);
            expect(jsonBody.error).toBe(0);
            expect(jsonBody.requestOrderId).toBe(VALID_REQUEST_ORDER_ID);
        });
    })

    describe("invalid request", () => {
        test("should return error when requestOrderId could not be found", async () => {
            request.params.requestOrderId = NOT_EXISTING_REQUEST_ORDER_ID;
            const { jsonBody } = await GetCoreSwimmyApiLogDetailHandler.Handler(request, extendedPortalInvocationContext);
            expect(jsonBody.nttc_mvno_error_code).toBe('9999');
            expect(jsonBody.nttc_mvno_error_msg).toBe('エラーが発生しました。');
        });

        test("should return error when requestOrderId is invalid", async () => {
            request.params.requestOrderId = INVALID_REQUEST_ORDER_ID;
            const { jsonBody } = await GetCoreSwimmyApiLogDetailHandler.Handler(request, extendedPortalInvocationContext);
            expect(jsonBody.error).toBe(CommonError.BAD_REQUEST);
            expect(jsonBody.error_msg).toBe('invalid parameter');
            expect(jsonBody.nttc_mvno_error_code).toBe(CommonError.INVALID_PARAMETER);
        });

        test("should return error when requestOrderId is not set", async () => {
            request.params.requestOrderId = null;
            const { jsonBody } = await GetCoreSwimmyApiLogDetailHandler.Handler(request, extendedPortalInvocationContext);
            expect(jsonBody.error).toBe(CommonError.BAD_REQUEST);
            expect(jsonBody.error_msg).toBe('invalid parameter');
            expect(jsonBody.nttc_mvno_error_code).toBe(CommonError.INVALID_PARAMETER);
        });

        test("should return error when something crashes", async () => {
            const errorMsg = 'Make it crash';
            const newContext = {
                ...extendedPortalInvocationContext,
                log: () => { 
                    throw Error(errorMsg);
                }
            } as unknown as ExtendedPortalInvocationContext;
            const { body } = await GetCoreSwimmyApiLogDetailHandler.Handler(request, newContext);
            expect(String(body)).toBe(`Error: ${errorMsg}`);
        });
    })
});