import { useRedisMock } from "../../testing/RedisMock";
jest.mock('../../../src/database/redis', () => ({
  ...jest.requireActual("../../../src/database/redis"),
  useRedis: jest.fn().mockImplementation(useRedisMock),
}));
jest.mock("../../../src/services/notificationService", () => ({
    ...jest.requireActual("../../../src/services/notificationService"),
    NotificationService: {
        sendMessage: jest.fn(),
    },
}));

import { describe, expect, test } from "@jest/globals";
import { Sequelize } from "sequelize";
import {extendedPortalInvocationContext} from "../../testing/DefaultContext";
import DefaultRequest from "../../testing/DefaultRequest";
import DefaultContext from "../../testing/DefaultContext";
import { UpdateCoreSwimmyApiLogStatusHandler } from "@/functions/portal/UpdateCoreSwimmyApiLogStatusHandler";
import { GetCoreSwimmyApiLogDetailHandler } from "@/functions/portal/GetCoreSwimmyApiLogDetailHandler";
import { disconnectMongo, useMongo } from "@/database/mongo";
import { HttpRequest } from "@azure/functions";
import { CommonError, getCommonErrorName } from "@/constants/commonError";
import { CoreSwimmyStatus, CORE_SWIMMY_STATUS_TEXT } from "@/constants/coreSwimmy";
import ExtendedPortalInvocationContext from "@/types/ExtendedPortalInvocationContext";
import CoreSwimmyApiLog, { ICoreSwimmyApiLogDocument } from "@/models/coreSwimmyApiLog";
import { generateSOID } from "../../testing/TestHelper";
import { usePsql } from "@/database/psql";
import AppConfig from "@/appconfig";

const NUM_LOGS = 5;

describe("functions/portal/UpdateCoreSwimmyApiLogStatusHandler", () => {
    let sequelize: Sequelize;
    const logEntries = [];
    let savedLogs: ICoreSwimmyApiLogDocument[] = [];
    const requestOrderIds = Array.from(Array(NUM_LOGS).keys()).map(i => `AP101100524593${i}`);

    beforeAll(async () => {
        await AppConfig.loadCoreMvnoConfig(DefaultContext);
        sequelize = await usePsql();
        await useMongo(DefaultContext);
        await CoreSwimmyApiLog.deleteMany({requestOrderId: {
            $in: requestOrderIds
        }});
        savedLogs = await Promise.all(requestOrderIds.map(async (requestOrderId, i) => {
            return await CoreSwimmyApiLog.create({
                tenantId: `TST000001${i}`,
                kaisenNo: `0211613400${i}`,
                swimmyType: '004',
                status: CoreSwimmyStatus.TOUROKU_IRAI_NOT_YET_SENT,
                requestOrderId,
                soId: generateSOID(),
                jikkouchuFlg: false,
            })
        }))
    });
    
    afterAll(async () => {
        await CoreSwimmyApiLog.deleteMany({requestOrderId: {
            $in: requestOrderIds
        }});
        await disconnectMongo(DefaultContext);
        await sequelize.close();
    });

    describe("valid requests", () => {
        test("should return updateOK == true for all target requestOrderIds", async () => {
            const statusCode = CoreSwimmyStatus.TOUROKU_IRAI_RESULT_OK;
            const payload = {
                targets: [],
                statusCode
            };
            const expectedResult = {
                error: 0,
                results: []
            };

            for (const log of savedLogs) {
                payload.targets.push({
                    requestOrderId: log.requestOrderId,
                    swimmyType: '004',
                });
                expectedResult.results.push({
                    requestOrderId: log.requestOrderId,
                    swimmyType: '004',
                    updateOK: true
                });
            }

            const newRequest = {
                ...DefaultRequest,
                json: () => { 
                    return payload
                }
            } as unknown as HttpRequest;

            const newContext = {
                ...extendedPortalInvocationContext,
                sessionData: {
                    user: {
                        plusId: "testUser"
                    }
                }
            } as unknown as ExtendedPortalInvocationContext;

            // check response
            const { jsonBody: updateResponseJsonBody } = await UpdateCoreSwimmyApiLogStatusHandler.Handler(newRequest, newContext);
            expect(updateResponseJsonBody.error).toBe(expectedResult.error);
            expect(updateResponseJsonBody.results).toEqual(expect.arrayContaining(expectedResult.results));

            // check if data from db matches 
            for (const log of savedLogs) {
                newRequest.params.requestOrderId = log.id; // NOTE GetCoreSwimmyApiLogDetailHandler is using id instead of requestOrderId
                const { jsonBody: getDetailResponseJsonBody } = await GetCoreSwimmyApiLogDetailHandler.Handler(newRequest, newContext);
                expect(getDetailResponseJsonBody.status).toBe(CORE_SWIMMY_STATUS_TEXT[statusCode]);
            }
        });
    });

    describe("invalid requests", () => {
        test("should return invalid parameters error", async () => {
            const { jsonBody } = await UpdateCoreSwimmyApiLogStatusHandler.Handler(DefaultRequest, extendedPortalInvocationContext);
            expect(jsonBody.error).toBe(CommonError.TARGETS_INVALID);
            expect(jsonBody.error_msg).toBe(getCommonErrorName(CommonError.TARGETS_INVALID));
            expect(jsonBody.nttc_mvno_error_code).toBe('invalid parameters');
        });

        test("should return an error due to some sudden crash", async () => {
            const errorMsg = 'Make it crash';
            const newContext = {
                ...extendedPortalInvocationContext,
                log: () => { 
                    throw Error(errorMsg);
                }
            } as unknown as ExtendedPortalInvocationContext;
            const { body } = await UpdateCoreSwimmyApiLogStatusHandler.Handler(DefaultRequest, newContext);
            expect(String(body)).toBe(`Error: ${errorMsg}`);
        });

        test("should return an error due to wrong json format", async () => {
            const newRequest = {
                ...DefaultRequest,
                json: () => { 
                    throw Error();
                }
            } as unknown as HttpRequest;
            const { body } = await UpdateCoreSwimmyApiLogStatusHandler.Handler(newRequest, extendedPortalInvocationContext);
            expect(String(body)).toBe(`Error: Request body is empty`);
        });

        test("should return resendOK == false for each target due to requestOrderIds not in DB", async () => {
            const payload = {
                statusCode: CoreSwimmyStatus.TOUROKU_IRAI_RESULT_OK,
                targets: [
                    { requestOrderId: "667147e4b14c82ef86d802c1", swimmyType: '004' },
                    { requestOrderId: "667147e4b14c82ef86d8d2c1", swimmyType: '004' },
                ]
            };

            const expectedResult = {
                error: 0,
                results: [
                    { requestOrderId: "667147e4b14c82ef86d802c1", swimmyType: '004', updateOK: false },
                    { requestOrderId: "667147e4b14c82ef86d8d2c1", swimmyType: '004', updateOK: false }
                ]
            };

            const newRequest = {
                ...DefaultRequest,
                json: () => { 
                    return payload
                }
            } as unknown as HttpRequest;

            const newContext = {
                ...extendedPortalInvocationContext,
                sessionData: {
                    user: {
                        plusId: "testUser"
                    }
                }
            } as unknown as ExtendedPortalInvocationContext;
            
            const { jsonBody } = await UpdateCoreSwimmyApiLogStatusHandler.Handler(newRequest, newContext);
            expect(jsonBody.error).toBe(expectedResult.error);
            expect(jsonBody.results).toEqual(expect.arrayContaining(expectedResult.results));
        });
    });
});
