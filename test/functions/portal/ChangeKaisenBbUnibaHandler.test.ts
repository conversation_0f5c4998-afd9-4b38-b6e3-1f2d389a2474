import { ChangeKaisenBbUnibaHandler } from "@/functions/portal/ChangeKaisenBbUnibaHandler";
import { CommonError } from "@/constants/commonError";
import { LinesEntity } from "@/core/entity/LinesEntity";
import PlansEntity from "@/core/entity/PlansEntity";
import CoreServicesRestrictSetting from "@/models/servicesRestrictSetting";
import DefaultRequest from "../../testing/DefaultRequest";
import DefaultContext from "../../testing/DefaultContext";
import {extendedPortalInvocationContext} from "../../testing/DefaultContext";
import { usePsql } from "@/database/psql";
import { HttpRequest } from "@azure/functions";
import AppConfig from "@/appconfig";
import { Sequelize } from "sequelize-typescript";
import { TenantsEntity } from "@/core/entity/TenantsEntity";
import TenantPlansEntity from "@/core/entity/TenantPlansEntity";
import { generateProcessID } from "../../testing/TestHelper";
import LineTenantsEntity from "@/core/entity/LineTenantsEntity";
import { afterEach } from "@jest/globals";
import LineDeleteService from "@/core/service/impl/LineDeleteService";
import { TenantNnumbersEntity } from "@/core/entity/TenantNnumbersEntity";

describe("ChangeKaisenBbUnibaHandler", () => {
    let request: HttpRequest;
    let sequelize: Sequelize;

    const testdata = {
        lineId: "08024041801",
        resalePlanId: "ABCDE",
        constants: {
            lineStatusOK: "01",
            lineStatusNG: "03",
        },
    };

    const orderData = {
        processID: generateProcessID(),
        tenantId: "ZZZ000",
        targetTenantId: "TST000",
        lineNo: testdata.lineId,
    }

    beforeAll(async () => {
        // Initialize database connection
        sequelize = await usePsql();
        await AppConfig.loadCoreMvnoConfig(DefaultContext);
    });

    beforeEach(async () => {
        await PlansEntity.create({
            planId: 10000,
            planClass: 1,
            policyId: 201,
            network: "LTE",
            planName: "test",
            planDescription: "test",
            refServicePattern: 1,
            tpcServicePattern: 1,
            defaultSimFlag: false,
            planIdT: "********",
            pricePlanId: testdata.resalePlanId
        });
        await LinesEntity.create({
            lineId: testdata.lineId,
            lineStatus: testdata.constants.lineStatusOK,
            simFlag: false,
            pricePlanId: testdata.resalePlanId,
            nnumber: "0123456789"
        });
        await LineTenantsEntity.create({
            lineId: orderData.lineNo,
            tenantId: orderData.targetTenantId,
        });
        await TenantNnumbersEntity.create({
            tenantId: orderData.targetTenantId,
            nnumber: "0123456789",
        })
        jest.spyOn(CoreServicesRestrictSetting, "getBbUnibaEnabledTenants").mockResolvedValue(["TST000"]);
    });

    afterEach(async () => {
        jest.restoreAllMocks();
        jest.clearAllMocks();
        await TenantNnumbersEntity.destroy({
            where: {
                tenantId: orderData.targetTenantId,
                nnumber: "0123456789",
            },
        });
        await LineTenantsEntity.destroy({
            where: {
                lineId: orderData.lineNo
            },
        });
        await LinesEntity.destroy({
            where: {
                lineId: orderData.lineNo
            },
        });
        await PlansEntity.destroy({
            where: {
                pricePlanId: testdata.resalePlanId
            },
        });
    });

    afterAll(async () => {
        // Close database connection
        await sequelize.close();
    });

    it("returns error body when request.json throws (empty request body)", async () => {
        const error = new Error("Request body is empty");
        const request = {
            ...DefaultRequest,
            body: null,
            json: jest.fn().mockRejectedValue(error),
        } as unknown as HttpRequest;

        const response = await ChangeKaisenBbUnibaHandler.Handler(request, extendedPortalInvocationContext);
        expect(response.body.toString()).toBe(error.toString());
    });

    it("returns INVALID_PARAMETER when required parameters are missing", async () => {
        const request = {
            ...DefaultRequest,
            json: async() => {
                return {line_nos: [], bb_uniba_flag: "true"};
            },
        } as unknown as HttpRequest;

        const res = await ChangeKaisenBbUnibaHandler.Handler(request, extendedPortalInvocationContext);
        expect(res.jsonBody.error_msg).toBe("INVALID_PARAMETER");
    });

    it("should return INVALID_PARAMETER if bb_uniba_flag is not boolean string", async () => {
        const request = {
            ...DefaultRequest,
            json: async() => {
                return {line_nos: [testdata.lineId], bb_uniba_flag: "abc"};
            },
        } as unknown as HttpRequest;

        const res = await ChangeKaisenBbUnibaHandler.Handler(request, extendedPortalInvocationContext);
        expect(res.jsonBody.error_msg).toBe("INVALID_PARAMETER");
    });

    it("should return INVALID_PARAMETER when line's tenant is not enabled for bb uniba", async () => {
        const request = {
            ...DefaultRequest,
            json: async() => {
                return {line_nos: [testdata.lineId], bb_uniba_flag: "true"};
            },
        } as unknown as HttpRequest;

        jest.spyOn(CoreServicesRestrictSetting, "getBbUnibaEnabledTenants").mockResolvedValue([]);

        const res = await ChangeKaisenBbUnibaHandler.Handler(request, extendedPortalInvocationContext);
        expect(res.jsonBody.error_msg).toBe("INVALID_PARAMETER");
    });

    it("should return INVALID_PARAMETER when contains line's plan's planBbuniExFlag = true or some line's plan not found", async () => {
        const request = {
            ...DefaultRequest,
            json: async() => {
                return {line_nos: [testdata.lineId], bb_uniba_flag: "true"};
            },
        } as unknown as HttpRequest;

        await PlansEntity.update({ planBbuniExFlag: true }, { where: { pricePlanId: testdata.resalePlanId } });

        const res = await ChangeKaisenBbUnibaHandler.Handler(request, extendedPortalInvocationContext);
        expect(res.jsonBody.error_msg).toBe("INVALID_PARAMETER");
    });

    it("should return NO_ERROR when success", async () => {
        const request = {
            ...DefaultRequest,
            json: async() => {
                return {line_nos: [testdata.lineId], bb_uniba_flag: "true"};
            },
        } as unknown as HttpRequest;

        const res = await ChangeKaisenBbUnibaHandler.Handler(request, extendedPortalInvocationContext);
        expect(res.jsonBody.error).toBe(CommonError.NO_ERROR);
        const line = await LinesEntity.findOne({ where: { lineId: testdata.lineId } });
        expect(line?.userBbuniExFlag).toBe(true);
    });

    it("should update to null for false", async () => {
        const request = {
            ...DefaultRequest,
            json: async() => {
                return {line_nos: [testdata.lineId], bb_uniba_flag: "false"};
            },
        } as unknown as HttpRequest;

        await LinesEntity.update({ userBbuniExFlag: true }, { where: { lineId: testdata.lineId } });

        const res = await ChangeKaisenBbUnibaHandler.Handler(request, extendedPortalInvocationContext);
        expect(res.jsonBody.error).toBe(CommonError.NO_ERROR);
        const line = await LinesEntity.findOne({ where: { lineId: testdata.lineId } });
        expect(line?.userBbuniExFlag).toBe(null);
    });

    it("returns per-line errors when some line_nos not found and is_batch = true", async () => {
        const missingLine = "08099999999";
        const request = {
            ...DefaultRequest,
            json: async() => {
                return { line_nos: [testdata.lineId, missingLine], bb_uniba_flag: "true", is_batch: true };
            },
        } as unknown as HttpRequest;

        const res = await ChangeKaisenBbUnibaHandler.Handler(request, extendedPortalInvocationContext);
        expect(res.jsonBody.error).toBe(CommonError.INVALID_PARAMETER);
        expect(Array.isArray(res.jsonBody.result)).toBe(true);
        expect(res.jsonBody.result.length).toBe(2);

        const existingRes = res.jsonBody.result.find((r: any) => r.lineNo === testdata.lineId);
        const missingRes = res.jsonBody.result.find((r: any) => r.lineNo === missingLine);

        expect(existingRes).toBeDefined();
        expect(existingRes.error).toBe(CommonError.NO_ERROR);

        expect(missingRes).toBeDefined();
        expect(missingRes.error).toBe(CommonError.INVALID_PARAMETER);
        expect(missingRes.errorMessage).toBe('回線が存在しない');
    });

    it("should return NO_ERROR and per-line results for batch success (is_batch = true)", async () => {
        const line2 = "08024041802";
        // 作成: 2つ目の回線と関連情報
        await LinesEntity.create({
            lineId: line2,
            lineStatus: testdata.constants.lineStatusOK,
            simFlag: false,
            pricePlanId: testdata.resalePlanId,
            nnumber: "0123456790"
        });
        await LineTenantsEntity.create({
            lineId: line2,
            tenantId: orderData.targetTenantId,
        });
        await TenantNnumbersEntity.create({
            tenantId: orderData.targetTenantId,
            nnumber: "0123456790",
        });

        const request = {
            ...DefaultRequest,
            json: async() => {
                return { line_nos: [testdata.lineId, line2], bb_uniba_flag: "true", is_batch: true };
            },
        } as unknown as HttpRequest;

        const res = await ChangeKaisenBbUnibaHandler.Handler(request, extendedPortalInvocationContext);
        expect(res.jsonBody.error).toBe(CommonError.NO_ERROR);
        expect(Array.isArray(res.jsonBody.result)).toBe(true);
        expect(res.jsonBody.result.length).toBe(2);

        const r1 = res.jsonBody.result.find((r: any) => r.lineNo === testdata.lineId);
        const r2 = res.jsonBody.result.find((r: any) => r.lineNo === line2);
        expect(r1.error).toBe(CommonError.NO_ERROR);
        expect(r2.error).toBe(CommonError.NO_ERROR);

        // DB上の値確認
        const updated1 = await LinesEntity.findOne({ where: { lineId: testdata.lineId } });
        const updated2 = await LinesEntity.findOne({ where: { lineId: line2 } });
        expect(updated1?.userBbuniExFlag).toBe(true);
        expect(updated2?.userBbuniExFlag).toBe(true);

        // クリーンアップ（このテストで追加したデータ）
        await TenantNnumbersEntity.destroy({
            where: {
                tenantId: orderData.targetTenantId,
                nnumber: "0123456790",
            },
        });
        await LineTenantsEntity.destroy({
            where: {
                lineId: line2
            },
        });
        await LinesEntity.destroy({
            where: {
                lineId: line2
            },
        });
    });

    it("returns INVALID_PARAMETER when is_batch = null but multiple line_nos", async () => {
        const line2 = "08024041802";
        // 作成: 2つ目の回線と関連情報（テスト用にDBに作成）
        await LinesEntity.create({
            lineId: line2,
            lineStatus: testdata.constants.lineStatusOK,
            simFlag: false,
            pricePlanId: testdata.resalePlanId,
            nnumber: "0123456790"
        });
        await LineTenantsEntity.create({
            lineId: line2,
            tenantId: orderData.targetTenantId,
        });
        await TenantNnumbersEntity.create({
            tenantId: orderData.targetTenantId,
            nnumber: "0123456790",
        });

        const request = {
            ...DefaultRequest,
            json: async() => {
                return { line_nos: [testdata.lineId, line2], bb_uniba_flag: "true", is_batch: null };
            },
        } as unknown as HttpRequest;

        const res = await ChangeKaisenBbUnibaHandler.Handler(request, extendedPortalInvocationContext);
        expect(res.jsonBody.error_msg).toBe("INVALID_PARAMETER");

        // クリーンアップ（このテストで追加したデータ）
        await TenantNnumbersEntity.destroy({
            where: {
                tenantId: orderData.targetTenantId,
                nnumber: "0123456790",
            },
        });
        await LineTenantsEntity.destroy({
            where: {
                lineId: line2
            },
        });
        await LinesEntity.destroy({
            where: {
                lineId: line2
            },
        });
    });
});
