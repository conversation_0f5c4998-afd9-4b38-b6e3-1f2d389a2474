import { GetCronjobListHandler } from "@/functions/portal/GetCronjobListHandler";
import { HttpRequest } from "@azure/functions";
import ExtendedPortalInvocationContext from "@/types/ExtendedPortalInvocationContext";
import {extendedPortalInvocationContext} from "../../testing/DefaultContext";
import { beforeAll } from "@jest/globals";
import DefaultRequest from "../../testing/DefaultRequest";

describe("GetCronjobListHandler", () => {
    let context: ExtendedPortalInvocationContext;
    beforeAll(() => {
        context = extendedPortalInvocationContext;
    })
    it("returns a list of cronjobs with null status", async () => {
        const request = {} as HttpRequest;

        const response = await GetCronjobListHandler.Handler(request, context);

        expect(response.status).toBe(200);
        expect(response.jsonBody.status).toBe("OK");
        expect(response.jsonBody.cronjobList).toBeInstanceOf(Array);
        expect(response.jsonBody.cronjobList.length).toBeGreaterThan(0);
        response.jsonBody.cronjobList.forEach((cronjob: any) => {
            expect(cronjob).toHaveProperty("name");
            expect(cronjob).toHaveProperty("status");
        });
    });

    it("logs the start of the handler execution", async () => {
        await GetCronjobListHandler.Handler(DefaultRequest, context);

        expect(context.log).toHaveBeenCalledWith("GetCronjobListHandler START");
    });

    it("returns an error response if an exception occurs", async () => {
        context.log = jest.fn().mockImplementationOnce((...args) => {
            throw new Error("Logging error");
        });

        const response = await GetCronjobListHandler.Handler(DefaultRequest, context);

        expect(context.error).toHaveBeenCalled();
        expect(response.body).toBeDefined();
    });
});