import { describe, expect, test } from "@jest/globals";
import { HttpRequest } from "@azure/functions";
import { extendedPortalInvocationContext } from "../../testing/DefaultContext";
import DefaultRequest from "../../testing/DefaultRequest";
import DefaultContext from "../../testing/DefaultContext";
import { SearchCoreSwimmyApiLogHandler } from "@/functions/portal/SearchCoreSwimmyApiLogHandler";
import { disconnectMongo, useMongo } from "@/database/mongo";
import { CommonError, getCommonErrorName } from "@/constants/commonError";
import { generateProcessID, generateSOID } from "../../testing/TestHelper";
import CoreSwimmyApiLog from "@/models/coreSwimmyApiLog";
import { generateCoreSwimmyApiLog } from "./testdata/portal.testdata";
import { SwimmyType } from "@/types/coreSwimmyApiLog";
import AppConfig from "@/appconfig";

describe("functions/portal/SearchCoreSwimmyApiLogHandler", () => {
    const request = DefaultRequest;

    beforeAll(async () => {
        await AppConfig.loadCoreMvnoConfig(DefaultContext);
        await useMongo(DefaultContext);
        await prepareData();
    });

    afterAll(async () => {
        await deleteTestData();
        await disconnectMongo(DefaultContext);
    });

    // TODO: create temp data and use it for testing
    // data might not exist in your collection and thus filters won't work

    const testdata = {
        kaisen4: "02099884004",
        oneSO: generateProcessID(),
        customTenant: "TST900",
        from: 1672498800, // 23/01/01 00:00:00
        to: 1672671600, // 23/01/03 00:00:00
        orderType: "004" as SwimmyType,
        orderTypeCaseID: [
            generateProcessID(),
            generateProcessID(),
            generateProcessID(),
            generateProcessID(),
            generateProcessID(),
        ],
    };

    function getRandomInt(min: number, max: number) {
        min = Math.ceil(min);
        max = Math.floor(max);
        return Math.floor(Math.random() * (max - min + 1)) + min;
    }

    const prepareData = async () => {
        // case 1 line number in 4 logs
        await Promise.all(
            Array.from(Array(4).keys()).map(async (i) => {
                return await CoreSwimmyApiLog.create(
                    generateCoreSwimmyApiLog(
                        generateProcessID(),
                        generateSOID(),
                        {
                            kaisenNo: testdata.kaisen4,
                        },
                    ),
                );
            }),
        );
        // case 1 requestOrderId
        await CoreSwimmyApiLog.create(
            generateCoreSwimmyApiLog(testdata.oneSO, generateSOID()),
        );
        // case custom tenant (4 logs)
        await Promise.all(
            Array.from(Array(4).keys()).map(async (i) => {
                return await CoreSwimmyApiLog.create(
                    generateCoreSwimmyApiLog(
                        generateProcessID(),
                        generateSOID(),
                        {
                            tenantId: testdata.customTenant,
                        },
                    ),
                );
            }),
        );
        // case order type 004 5 logs
        // (this may already have been covered by existing data,
        // so in case empty db in local, lets create 5 logs)
        await Promise.all(
            testdata.orderTypeCaseID.map(async (id) => {
                return await CoreSwimmyApiLog.create(
                    generateCoreSwimmyApiLog(id, generateSOID(), {
                        swimmyType: testdata.orderType,
                    }),
                );
            }),
        );
        // case 5 orders from specific date range (use old date to avoid conflicts)
        // random 5 dates between testdata.from and testdata.to (exclusive)
        const dates = [
            getRandomInt(testdata.from, testdata.to),
            getRandomInt(testdata.from, testdata.to),
            getRandomInt(testdata.from, testdata.to),
            getRandomInt(testdata.from, testdata.to),
            getRandomInt(testdata.from, testdata.to),
        ];
        await Promise.all(
            dates.map(async (date) => {
                return await CoreSwimmyApiLog.create(
                    generateCoreSwimmyApiLog(
                        generateProcessID(),
                        generateSOID(),
                        {
                            createdAt: date,
                        },
                    ),
                );
            }),
        );
    };

    const deleteTestData = async () => {
        await Promise.all([
            CoreSwimmyApiLog.deleteMany({
                kaisenNo: testdata.kaisen4,
            }),
            CoreSwimmyApiLog.deleteMany({
                requestOrderId: {
                    $in: [testdata.oneSO, ...testdata.orderTypeCaseID],
                },
            }),
            CoreSwimmyApiLog.deleteMany({
                tenantId: testdata.customTenant,
            }),
            CoreSwimmyApiLog.deleteMany({
                createdAt: {
                    $gte: testdata.from,
                    $lt: testdata.to,
                },
            }),
        ]);
    };

    describe.each([
        {
            description:
                "filtering by kaisenNo should return searchResultCount == 4 and result.length == 4",
            expected: {
                searchResultCount: 4,
                resultCount: 4,
            },
            filter: {
                searchType: "1",
                searchValue: testdata.kaisen4,
                sortBy: "kaisenNo",
                sortOrder: "desc",
                limit: 10,
            },
        },
        {
            description:
                "offset (skip) 2 and filtering by kaisenNo should return searchResultCount == 4 and result.length == 2",
            expected: {
                searchResultCount: 4,
                resultCount: 2,
            },
            filter: {
                searchType: "1",
                searchValue: testdata.kaisen4,
                sortBy: "kaisenNo",
                sortOrder: "desc",
                skip: 2,
                limit: 10,
            },
        },
        {
            description:
                "filtering by kaisenNo and limit to 1 should return searchResultCount == 4 and result.length == 1",
            expected: {
                searchResultCount: 4,
                resultCount: 1,
            },
            filter: {
                searchType: "1",
                searchValue: testdata.kaisen4,
                sortBy: "requestOrderId",
                sortOrder: "desc",
                limit: 1,
            },
        },
        {
            description:
                "filtering by requestOrderId and limit to 10 should return searchResultCount == 1 and result.length == 1",
            expected: {
                searchResultCount: 1,
                resultCount: 1,
            },
            filter: {
                searchType: "0",
                searchValue: testdata.oneSO,
                limit: 10,
            },
        },
        {
            description:
                "filtering by tenantIds and limit to 10 should return searchResultCount == 4 and result.length == 4",
            expected: {
                searchResultCount: 4,
                resultCount: 4,
            },
            filter: {
                tenantIds: [testdata.customTenant],
                sortBy: "orderType",
                sortOrder: "asc",
                limit: 10,
            },
        },
        {
            description:
                "filtering by orderTypes and limit to 10 should return searchResultCount == 5 and result.length == 5",
            expected: {
                searchResultCount: 5,
                resultCount: 5,
                searchGte: true,
                resultGte: true,
            },
            filter: {
                orderTypes: [testdata.orderType],
                sortBy: "tenantId",
                sortOrder: "asc",
                limit: 10,
            },
        },
        // {
        //     description: 'filtering by status and limit to 10 should return searchResultCount == 1 and result.length == 1',
        //     expected: {
        //         searchResultCount: 1,
        //         resultCount: 1
        //     },
        //     filter:  {
        //         status: ['0'],
        //         sortBy: 'start_date',
        //         sortOrder: 'asc',
        //         limit: 10
        //     }
        // },
        // {
        //     description: 'filtering by tenantIds, orderTypes, status and limit to 10 should return searchResultCount == 1 and result.length == 1',
        //     expected: {
        //         searchResultCount: 1,
        //         resultCount: 1
        //     },
        //     filter:  {
        //         tenantIds: ['TST000'],
        //         orderTypes: ['004'],
        //         status: ['0'],
        //         limit: 10
        //     }
        // },
        {
            description:
                "filtering by fromOrderDatetime, toOrderDatetime, orderTypes and limit to 10 should return searchResultCount == 1 and result.length == 1",
            expected: {
                searchResultCount: 5,
                resultCount: 5,
            },
            filter: {
                fromOrderDatetime: testdata.from,
                toOrderDatetime: testdata.to,
                orderTypes: [testdata.orderType],
                limit: 10,
            },
        },

        // TODO check if filtering by fromOrderDatetime and toOrderDatetime is correct
        // TODO check if order by and sort order is correct
        // TODO filter by status
    ])(
        "valid filters should return some results",
        ({ description, expected, filter }) => {
            test(description, async () => {
                const newRequest = {
                    ...request,
                    json: () => {
                        return filter;
                    },
                } as unknown as HttpRequest;
                const { jsonBody } =
                    await SearchCoreSwimmyApiLogHandler.Handler(
                        newRequest,
                        extendedPortalInvocationContext,
                    );
                expect(jsonBody.error).toBe(0);
                if (expected.searchGte) {
                    expect(jsonBody.searchResultCount).toBeGreaterThanOrEqual(
                        expected.searchResultCount,
                    );
                } else {
                    expect(jsonBody.searchResultCount).toBe(
                        expected.searchResultCount,
                    );
                }
                if (expected.resultGte) {
                    expect(jsonBody.result.length).toBeGreaterThanOrEqual(
                        expected.resultCount,
                    );
                } else {
                    expect(jsonBody.result.length).toBe(expected.resultCount);
                }
            });
        },
    );

    describe.each([
        {
            description: "invalid json should return error",
            expected: "Error: Request body is empty",
            filter: {
                throw_error: true,
            },
        },
        {
            description: "no filters should return error",
            expected: {
                error: CommonError.INVALID_USER,
                error_msg: getCommonErrorName(CommonError.INVALID_USER),
                nttc_mvno_error_code: "invalid parameters",
                nttc_mvno_error_msg: null,
            },
            filter: {},
        },
        {
            description: "filters set to empty should return error",
            expected: {
                error: CommonError.INVALID_USER,
                error_msg: getCommonErrorName(CommonError.INVALID_USER),
                nttc_mvno_error_code: "invalid parameters",
                nttc_mvno_error_msg: null,
            },
            filter: {
                searchValue: "",
                fromOrderDatetime: "",
                toOrderDatetime: "",
                tenantIds: "",
                status: "",
                orderTypes: "",
                limit: 1,
            },
        },
        {
            description: "filtering without limit should return error",
            expected: {
                error: CommonError.INVALID_USER,
                error_msg: getCommonErrorName(CommonError.INVALID_USER),
                nttc_mvno_error_code: "invalid parameters",
                nttc_mvno_error_msg: null,
            },
            filter: {
                searchType: "1",
                searchValue: "02006134003",
            },
        },
    ])(
        "invalid filters should return an error",
        ({ description, expected, filter }) => {
            test(description, async () => {
                const newRequest = {
                    ...request,
                    json: () => {
                        if ("throw_error" in filter) throw new Error();
                        return filter;
                    },
                } as unknown as HttpRequest;
                const response = await SearchCoreSwimmyApiLogHandler.Handler(
                    newRequest,
                    extendedPortalInvocationContext,
                );
                if ("jsonBody" in response) {
                    expect(response.jsonBody).toMatchObject(expected as any);
                } else {
                    expect(String(response.body)).toBe(expected as any);
                }
            });
        },
    );
});
