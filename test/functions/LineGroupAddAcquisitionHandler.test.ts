import { afterAll, beforeEach, describe, expect, jest } from "@jest/globals";
const enqueueMessage = jest.fn();
jest.mock("../../src/services/servicebus", () => ({
    __esModule: true,
    default: enqueueMessage,
}));

import LineGroupAddAcquisitionInputDto from '@/core/dto/LineGroupAddAcquisitionInputDto';
import ResultCdConstants from '@/core/constant/ResultCdConstants';
import LineGroupsEntity from '@/core/entity/LineGroupsEntity';
import TenantGroupPlansEntity from '@/core/entity/TenantGroupPlansEntity';
import DefaultContext from "../testing/DefaultContext";
import DefaultRequest from "../testing/DefaultRequest";
import { LinesEntity } from "@/core/entity/LinesEntity";
import LineLineGroupsEntity from "@/core/entity/LineLineGroupsEntity";
import { Sequelize } from "sequelize";
import AppConfig from "@/appconfig";
import { usePsql } from "@/database/psql";
import { LineGroupAddAcquisitionHandler } from "@/functions/LineGroupAddAcquisitionHandler";
import { formatDate } from "date-fns";
import { generateProcessID, generateSOID, useFasterRetries } from "../testing/TestHelper";
import ResponseHeader from "@/core/dto/ResponseHeader";
import GroupOptionPlansEntity from "@/core/entity/GroupOptionPlansEntity";
import { GroupPlansEntity } from "@/core/entity/GroupPlansEntity";
import { GroupOptionPlanParametersEntity } from "@/core/entity/GroupOptionPlanParametersEntity";
import SOAPTestHelper from "../testing/soapTestHelper";
import nock from "nock";
import { getConfigDataForTest } from "../testing/testdataHelper";
import ServiceOrdersEntity from "@/core/entity/ServiceOrdersEntity";
import Check from "@/core/common/Check";
import MvnoUtil from "@/core/common/MvnoUtil";
import APILinesGroupDAO from "@/core/dao/ApiLinesGroupDAO";
import TenantManage from "@/core/common/TenantManage";
import LineTenantsEntity from "@/core/entity/LineTenantsEntity";
import APICommonDAO from "@/core/dao/APICommonDAO";
import ApiCommon from "@/core/common/ApiCommon";
import SOAPCommon from "@/core/common/SOAPCommon";
import SOAPCommonOutputDto from "@/core/dto/SOAPCommonOutputDto";
import CoreSwimmyApiLog from "@/models/coreSwimmyApiLog";
import { disconnectMongo, useMongo } from "@/database/mongo";
import APILinesDAO from "@/core/dao/APILinesDAO";

describe('LineGroupAddAcquisitionHandler', () => {
    let sequelize: Sequelize;

    const responseHeader = {
        sequenceNo: "1234567",
        receivedDate: formatDate(new Date(), "yyyy/MM/dd HH:mm:ss"),
        processCode: ResultCdConstants.CODE_000000,
        apiProcessID: generateProcessID(),
    } as ResponseHeader;

    const testData = {
        requestHeader: {
            sequenceNo: "1234567",
            senderSystemId: "0001",
            apiKey: "",
            functionType: "10",
        },
        tenantId: "TST000",
        potalGroupPlanID: "10000",
        optionGroupPlanId: "99999",
        AccountingLineNo: "**************",
        lineGroupId: "9999999",
        targetSoId: generateSOID(),
    } as LineGroupAddAcquisitionInputDto;

    const testDataRKM = {
        requestHeader: {
            sequenceNo: "1234567",
            senderSystemId: "0001",
            apiKey: "",
            functionType: "10",
        },
        tenantId: "RKM000",
        potalGroupPlanID: "60000",
        optionGroupPlanId: "99998",
        AccountingLineNo: "**************",
        lineGroupId: "9999998",
        targetSoId: generateSOID(),
    } as LineGroupAddAcquisitionInputDto;

    function setOrderType(orderType: "0" | "1" | "2" | "9") {
        const nextMonthDate = new Date();
        nextMonthDate.setMonth(nextMonthDate.getMonth() + 1);
        nextMonthDate.setHours(0, 0, 0, 0);
        switch (orderType) {
            case "1":
                DefaultContext.jsonBody.reserve_date = formatDate(nextMonthDate, "yyyy/MM/dd HH:mm");
                DefaultContext.jsonBody.reserve_flag = false;
                DefaultContext.jsonBody.reserve_soId = null;
                break;
            case "2":
                DefaultContext.jsonBody.reserve_date = formatDate(nextMonthDate, "yyyy/MM/dd HH:mm");
                DefaultContext.jsonBody.reserve_flag = true;
                DefaultContext.jsonBody.reserve_soId = responseHeader.apiProcessID;
                break;
            case "9":
                DefaultContext.jsonBody.reserve_date = null;
                DefaultContext.jsonBody.reserve_flag = false;
                DefaultContext.jsonBody.reserve_soId = responseHeader.apiProcessID;
                break;
            case "0":
            default:
                DefaultContext.jsonBody.reserve_date = null;
                DefaultContext.jsonBody.reserve_flag = null;
                DefaultContext.jsonBody.reserve_soId = null;
                break;
        }
    }

    let configData: any;

    beforeAll(async () => {
        sequelize = await usePsql();
        await useMongo(DefaultContext);
        await AppConfig.loadCoreMvnoConfig(DefaultContext);
        configData = getConfigDataForTest();
        try {
            await Promise.all([
                LinesEntity.create({
                    lineId: testData.AccountingLineNo,
                    lineStatus: "01",
                    nnumber: "N100000100",
                    simFlag: false,
                    pricePlanId: "AR305",
                    voiceMailId: "AO143"
                }),
                GroupPlansEntity.create({
                    planId: parseInt(testData.potalGroupPlanID, 10),
                    planClass: 1,
                    policyId: 201,
                    network: 'LTE',
                    planName: 'プラン名',
                    planDescription: 'プラン説明',
                    refServicePattern: 1,
                    tpcServicePattern: 1,
                }),
                GroupPlansEntity.create({
                    planId: parseInt(testData.optionGroupPlanId, 10),
                    planClass: 1,
                    policyId: 201,
                    network: 'LTE',
                    planName: 'プラン名',
                    planDescription: 'プラン説明',
                    refServicePattern: 1,
                    tpcServicePattern: 1,
                }),
            ]);
            await GroupOptionPlansEntity.create({
                optionPlanId: parseInt(testData.optionGroupPlanId, 10),
                optionPlanName: 'オプションプラン名',
                optionPlanDescription: 'オプションプラン説明',
                optionPlanType: "10",
                tpcServicePattern: 1,
                capacityCode: "12345"
            });

            await LineGroupsEntity.create({
                groupId: testData.lineGroupId,
                status: 1,
                tenantId: testData.tenantId,
                planId: parseInt(testData.potalGroupPlanID, 10),
            });

            await Promise.all([
                LineTenantsEntity.create({
                    lineId: testData.AccountingLineNo,
                    tenantId: testData.tenantId
                }),
                LineLineGroupsEntity.create({
                    lineId: testData.AccountingLineNo,
                    groupId: testData.lineGroupId
                }),
                TenantGroupPlansEntity.create({
                    tenantId: testData.tenantId,
                    planId: parseInt(testData.potalGroupPlanID, 10),
                }),
                TenantGroupPlansEntity.create({
                    tenantId: testData.tenantId,
                    planId: parseInt(testData.optionGroupPlanId, 10),
                }),
                GroupOptionPlanParametersEntity.create({
                    optionPlanId: parseInt(testData.optionGroupPlanId, 10),
                    key: "add_bucket",
                    value: "1000"
                }),
                // GroupOptionPlanParametersEntity.create({
                //     optionPlanId: parseInt(testData.optionGroupPlanId, 10),
                //     key: "addbd_bucket",
                //     value: "1000"
                // }),
                GroupOptionPlanParametersEntity.create({
                    optionPlanId: parseInt(testData.optionGroupPlanId, 10),
                    key: "expiration_date",
                    value: "3"
                }),
                sequelize.query(`INSERT INTO group_plan_option_plans (plan_id, option_plan_id) VALUES (${testData.potalGroupPlanID}, ${testData.optionGroupPlanId})`)
            ]);

            SOAPTestHelper.setup();
            useFasterRetries();
        } catch (e) {
            // tslint:disable-next-line:no-console
            console.error(e);
            throw e;
        }
    });

    afterAll(async () => {
        try {
            await sequelize.query(`DELETE FROM group_plan_option_plans WHERE plan_id = ${testData.potalGroupPlanID} AND option_plan_id = ${testData.optionGroupPlanId}`);
            await GroupOptionPlanParametersEntity.destroy({ where: { optionPlanId: testData.optionGroupPlanId } });
            await TenantGroupPlansEntity.destroy({ where: { planId: testData.optionGroupPlanId } });
            await TenantGroupPlansEntity.destroy({ where: { planId: testData.potalGroupPlanID } });
            await LineLineGroupsEntity.destroy({ where: { lineId: testData.AccountingLineNo } });
            await LineTenantsEntity.destroy({ where: { lineId: testData.AccountingLineNo } });
            await GroupOptionPlansEntity.destroy({ where: { optionPlanId: testData.optionGroupPlanId } });
            await LineGroupsEntity.destroy({ where: { groupId: testData.lineGroupId } });
            await GroupPlansEntity.destroy({ where: { planId: testData.optionGroupPlanId } });
            await GroupPlansEntity.destroy({ where: { planId: testData.potalGroupPlanID } });
            await LinesEntity.destroy({ where: { lineId: testData.AccountingLineNo } });
        } catch (e) {
            // tslint:disable-next-line:no-console
            console.error(e);
            throw e;
        }
        await sequelize.close();
        await disconnectMongo(DefaultContext);
    });

    beforeEach(async () => {
        (enqueueMessage as jest.Mock).mockClear();
        DefaultContext.jsonBody = Object.assign({}, testData);
        DefaultContext.responseHeader = Object.assign({}, responseHeader);
        jest.spyOn(APILinesDAO.prototype, "getWholeFlag").mockResolvedValue([false]);
    });

    afterEach(async () => {
        try {
            SOAPTestHelper.tearDown();
            jest.restoreAllMocks();
            jest.clearAllMocks();
            jest.resetModules();
            await ServiceOrdersEntity.destroy({ where: { serviceOrderId: responseHeader.apiProcessID } });
            setOrderType("0");
            await CoreSwimmyApiLog.deleteMany({ requestOrderId: responseHeader.apiProcessID });
        } catch (e) {
            // tslint:disable-next-line:no-console
            console.error(e);
            throw e;
        }
    });

    async function throwSqlException(...args: any[]): Promise<any> {
        await LinesEntity.create({
            lineId: testData.AccountingLineNo,
            lineStatus: "01",
            nnumber: "N100000100",
            simFlag: false,
            pricePlanId: "AR305",
            voiceMailId: "AO143"
        })
        throw new Error("Error");
    }

    /**
     * check whether SwimmyApiLog exists and Service Bus is called
     */
    const checkCoreSwimmyApiLog = async (
        exists: boolean,
    ) => {
        const apiLog = await CoreSwimmyApiLog.findOne({
            requestOrderId: responseHeader.apiProcessID,
        });
        if (exists) {
            expect(apiLog).not.toBeNull();
            expect(enqueueMessage).toBeCalledTimes(1);
        } else {
            expect(apiLog).toBeNull();
            expect(enqueueMessage).not.toBeCalled();
        }
    };

    describe('OK Case', () => {
        beforeEach(() => {
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, SOAPTestHelper.simpleResponse("OK"))
                .persist();
        });

        test('ordertype = 0', async () => {
            DefaultContext.jsonBody = testData;
            setOrderType("0");

            const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);

            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );
            // check should be inserted to DB
            const serviceOrder = await ServiceOrdersEntity.findOne({
                where: {
                    serviceOrderId: responseHeader.apiProcessID,
                },
            });
            expect(serviceOrder).not.toBeNull();
            expect(serviceOrder.tenantId).toEqual(testData.tenantId);
            expect(serviceOrder.lineId).toEqual(testData.AccountingLineNo);
            expect(serviceOrder.orderStatus).toEqual("完了");
            expect(serviceOrder.orderType).toEqual("回線グループクーポン追加");
            expect(serviceOrder.reserveDate).toBeNull();

            await checkCoreSwimmyApiLog(true);
        });

        test('ordertype = 1', async () => {
            DefaultContext.jsonBody = testData;
            setOrderType("1");

            const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);

            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );

            // check should be inserted to DB
            const serviceOrder = await ServiceOrdersEntity.findOne({
                where: {
                    serviceOrderId: responseHeader.apiProcessID,
                },
            });
            expect(serviceOrder).not.toBeNull();
            expect(serviceOrder.tenantId).toEqual(testData.tenantId);
            expect(serviceOrder.lineId).toEqual(testData.AccountingLineNo);
            expect(serviceOrder.orderStatus).toEqual("予約中");
            expect(serviceOrder.orderType).toEqual("回線グループクーポン追加");
            expect(serviceOrder.reserveDate).not.toBeNull();

            await checkCoreSwimmyApiLog(false);
        });

        test('ordertype = 2', async () => {
            DefaultContext.jsonBody = testData;
            setOrderType("2");

            const SO = await ServiceOrdersEntity.create({
                serviceOrderId: responseHeader.apiProcessID,
                tenantId: testData.tenantId,
                lineId: testData.AccountingLineNo,
                orderStatus: "予約中",
                orderType: "回線グループクーポン追加",
                reserveDate: new Date(),
                functionType: "10",
                orderDate: new Date(2020, 1, 1),
            })

            const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);

            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );

            // check should be inserted to DB
            const serviceOrder = await ServiceOrdersEntity.findOne({
                where: {
                    serviceOrderId: DefaultContext.jsonBody.reserve_soId,
                },
            });
            expect(serviceOrder).not.toBeNull();
            expect(serviceOrder.tenantId).toEqual(testData.tenantId);
            expect(serviceOrder.lineId).toEqual(testData.AccountingLineNo);
            expect(serviceOrder.orderStatus).toEqual("完了");

            await checkCoreSwimmyApiLog(true);
        });

        test('lineGroupId is number', async () => {
            DefaultContext.jsonBody = JSON.parse(JSON.stringify(testData));
            setOrderType("0");
            DefaultContext.jsonBody.lineGroupId = parseInt(testData.lineGroupId, 10);

            const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);

            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );
            // check should be inserted to DB
            const serviceOrder = await ServiceOrdersEntity.findOne({
                where: {
                    serviceOrderId: responseHeader.apiProcessID,
                },
            });
            expect(serviceOrder).not.toBeNull();
            expect(serviceOrder.tenantId).toEqual(testData.tenantId);
            expect(serviceOrder.lineId).toEqual(testData.AccountingLineNo);
            expect(serviceOrder.orderStatus).toEqual("完了");
            expect(serviceOrder.orderType).toEqual("回線グループクーポン追加");
            expect(serviceOrder.reserveDate).toBeNull();

            await checkCoreSwimmyApiLog(true);
        });

        test("potalGroupPlanID is number", async () => {
            DefaultContext.jsonBody = JSON.parse(JSON.stringify(testData));
            setOrderType("0");
            DefaultContext.jsonBody.potalGroupPlanID = parseInt(testData.potalGroupPlanID, 10);

            const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);

            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );
            // check should be inserted to DB
            const serviceOrder = await ServiceOrdersEntity.findOne({
                where: {
                    serviceOrderId: responseHeader.apiProcessID,
                },
            });
            expect(serviceOrder).not.toBeNull();
            expect(serviceOrder.tenantId).toEqual(testData.tenantId);
            expect(serviceOrder.lineId).toEqual(testData.AccountingLineNo);
            expect(serviceOrder.orderStatus).toEqual("完了");
            expect(serviceOrder.orderType).toEqual("回線グループクーポン追加");
            expect(serviceOrder.reserveDate).toBeNull();

            await checkCoreSwimmyApiLog(true);
        });

        test("optionGroupPlanId is number", async () => {
            DefaultContext.jsonBody = JSON.parse(JSON.stringify(testData));
            setOrderType("0");
            DefaultContext.jsonBody.optionGroupPlanId = parseInt(testData.optionGroupPlanId, 10);

            const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);

            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );
            // check should be inserted to DB
            const serviceOrder = await ServiceOrdersEntity.findOne({
                where: {
                    serviceOrderId: responseHeader.apiProcessID,
                },
            });
            expect(serviceOrder).not.toBeNull();
            expect(serviceOrder.tenantId).toEqual(testData.tenantId);
            expect(serviceOrder.lineId).toEqual(testData.AccountingLineNo);
            expect(serviceOrder.orderStatus).toEqual("完了");
            expect(serviceOrder.orderType).toEqual("回線グループクーポン追加");
            expect(serviceOrder.reserveDate).toBeNull();

            await checkCoreSwimmyApiLog(true);
        });

        test("accountingLineNo is not null and nnumber is null", async () => {
            DefaultContext.jsonBody = JSON.parse(JSON.stringify(testData));
            setOrderType("0");
            jest.spyOn(TenantManage.prototype, "doCheck").mockResolvedValue([true, null]);

            const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);

            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );
            // check should be inserted to DB
            const serviceOrder = await ServiceOrdersEntity.findOne({
                where: {
                    serviceOrderId: responseHeader.apiProcessID,
                },
            });
            expect(serviceOrder).not.toBeNull();
            expect(serviceOrder.tenantId).toEqual(testData.tenantId);
            expect(serviceOrder.lineId).toEqual(testData.AccountingLineNo);
            expect(serviceOrder.orderStatus).toEqual("完了");
            expect(serviceOrder.orderType).toEqual("回線グループクーポン追加");
            expect(serviceOrder.reserveDate).toBeNull();

            await checkCoreSwimmyApiLog(false);
        });

        test("nnumber is not null and wholeFlag is empty", async () => {
            DefaultContext.jsonBody = JSON.parse(JSON.stringify(testData));
            setOrderType("0");
            jest.spyOn(APILinesDAO.prototype, "getWholeFlag").mockResolvedValue([]);

            const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);

            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );
            // check should be inserted to DB
            const serviceOrder = await ServiceOrdersEntity.findOne({
                where: {
                    serviceOrderId: responseHeader.apiProcessID,
                },
            });
            expect(serviceOrder).not.toBeNull();
            expect(serviceOrder.tenantId).toEqual(testData.tenantId);
            expect(serviceOrder.lineId).toEqual(testData.AccountingLineNo);
            expect(serviceOrder.orderStatus).toEqual("完了");
            expect(serviceOrder.orderType).toEqual("回線グループクーポン追加");
            expect(serviceOrder.reserveDate).toBeNull();

            await checkCoreSwimmyApiLog(false);
        });

        test("tenantType is 1/2/3", async () => {
            DefaultContext.jsonBody = JSON.parse(JSON.stringify(testData));
            setOrderType("0");
            const apiCommonDao = new APICommonDAO(DefaultRequest, DefaultContext);
            const originalTenantInfo = await apiCommonDao.getTenants(testData.tenantId);
            jest.spyOn(APICommonDAO.prototype, "getTenants").mockImplementation(async (tenantId: string) => {
                const tenant = originalTenantInfo;
                tenant.tenantType = 2;
                return tenant;
            });

            const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);

            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );
            // check should be inserted to DB
            const serviceOrder = await ServiceOrdersEntity.findOne({
                where: {
                    serviceOrderId: responseHeader.apiProcessID,
                },
            });
            expect(serviceOrder).not.toBeNull();
            expect(serviceOrder.tenantId).toEqual(testData.tenantId);
            expect(serviceOrder.lineId).toEqual(testData.AccountingLineNo);
            expect(serviceOrder.orderStatus).toEqual("完了");
            expect(serviceOrder.orderType).toEqual("回線グループクーポン追加");
            expect(serviceOrder.reserveDate).toBeNull();

            await checkCoreSwimmyApiLog(false);
        });

        test("capacityCode format is wrong", async () => {
            DefaultContext.jsonBody = JSON.parse(JSON.stringify(testData));
            setOrderType("0");
            const apiLinesGroupDao = new APILinesGroupDAO(DefaultRequest, DefaultContext);
            const groupOptionPlans = await apiLinesGroupDao.getGroupOptionPlan(testData.optionGroupPlanId);
            groupOptionPlans.capacityCode = "1";
            jest.spyOn(APILinesGroupDAO.prototype, "getGroupOptionPlan").mockResolvedValue(groupOptionPlans);

            const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);

            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );
            // check should be inserted to DB
            const serviceOrder = await ServiceOrdersEntity.findOne({
                where: {
                    serviceOrderId: responseHeader.apiProcessID,
                },
            });
            expect(serviceOrder).not.toBeNull();
            expect(serviceOrder.tenantId).toEqual(testData.tenantId);
            expect(serviceOrder.lineId).toEqual(testData.AccountingLineNo);
            expect(serviceOrder.orderStatus).toEqual("完了");
            expect(serviceOrder.orderType).toEqual("回線グループクーポン追加");
            expect(serviceOrder.reserveDate).toBeNull();

            await checkCoreSwimmyApiLog(false);
        });
    });

    describe('OK Case RINKモバイル', () => {
        beforeEach(() => {
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, SOAPTestHelper.simpleResponse("OK"))
                .persist();
        });
        beforeAll(async () => {
            try {
                 // test data for Rink Mobile
                await Promise.all([
                    LinesEntity.create({
                        lineId: testDataRKM.AccountingLineNo,
                        lineStatus: "01",
                        nnumber: "N100000100",
                        simFlag: false,
                        pricePlanId: "AR305",
                        voiceMailId: "AO143"
                    }),
                    GroupPlansEntity.create({
                        planId: parseInt(testDataRKM.potalGroupPlanID, 10),
                        planClass: 1,
                        policyId: 201,
                        network: 'LTE',
                        planName: 'プラン名',
                        planDescription: 'プラン説明',
                        refServicePattern: 1,
                        tpcServicePattern: 1,
                    }),
                    GroupPlansEntity.create({
                        planId: parseInt(testDataRKM.optionGroupPlanId, 10),
                        planClass: 1,
                        policyId: 201,
                        network: 'LTE',
                        planName: 'プラン名',
                        planDescription: 'プラン説明',
                        refServicePattern: 1,
                        tpcServicePattern: 1,
                    }),
                ]);
                await GroupOptionPlansEntity.create({
                    optionPlanId: parseInt(testDataRKM.optionGroupPlanId, 10),
                    optionPlanName: 'オプションプラン名',
                    optionPlanDescription: 'オプションプラン説明',
                    optionPlanType: "10",
                    tpcServicePattern: 1,
                });

                await LineGroupsEntity.create({
                    groupId: testDataRKM.lineGroupId,
                    status: 1,
                    tenantId: testDataRKM.tenantId,
                    planId: parseInt(testDataRKM.potalGroupPlanID, 10),
                });

                await Promise.all([
                    LineTenantsEntity.create({
                        lineId: testDataRKM.AccountingLineNo,
                        tenantId: testDataRKM.tenantId
                    }),
                    LineLineGroupsEntity.create({
                        lineId: testDataRKM.AccountingLineNo,
                        groupId: testDataRKM.lineGroupId
                    }),
                    TenantGroupPlansEntity.create({
                        tenantId: testDataRKM.tenantId,
                        planId: parseInt(testDataRKM.potalGroupPlanID, 10),
                    }),
                    TenantGroupPlansEntity.create({
                        tenantId: testDataRKM.tenantId,
                        planId: parseInt(testDataRKM.optionGroupPlanId, 10),
                    }),
                    GroupOptionPlanParametersEntity.create({
                        optionPlanId: parseInt(testDataRKM.optionGroupPlanId, 10),
                        key: "add_bucket",
                        value: "1000"
                    }),
                    GroupOptionPlanParametersEntity.create({
                        optionPlanId: parseInt(testDataRKM.optionGroupPlanId, 10),
                        key: "expiration_date",
                        value: "3"
                    }),
                    sequelize.query(`INSERT INTO group_plan_option_plans (plan_id, option_plan_id) VALUES (${testDataRKM.potalGroupPlanID}, ${testDataRKM.optionGroupPlanId})`)
                ]);
            } catch(e){
                console.error(e);
                throw e;
            }
            
        })
        afterAll(async () => {
            try {
                await sequelize.query(`DELETE FROM group_plan_option_plans WHERE plan_id = ${testDataRKM.potalGroupPlanID} AND option_plan_id = ${testDataRKM.optionGroupPlanId}`);
                await GroupOptionPlanParametersEntity.destroy({ where: { optionPlanId: testDataRKM.optionGroupPlanId } });
                await TenantGroupPlansEntity.destroy({ where: { planId: testDataRKM.optionGroupPlanId } });
                await TenantGroupPlansEntity.destroy({ where: { planId: testDataRKM.potalGroupPlanID } });
                await LineLineGroupsEntity.destroy({ where: { lineId: testDataRKM.AccountingLineNo } });
                await LineTenantsEntity.destroy({ where: { lineId: testDataRKM.AccountingLineNo } });
                await GroupOptionPlansEntity.destroy({ where: { optionPlanId: testDataRKM.optionGroupPlanId } });
                await LineGroupsEntity.destroy({ where: { groupId: testDataRKM.lineGroupId } });
                await GroupPlansEntity.destroy({ where: { planId: testDataRKM.optionGroupPlanId } });
                await GroupPlansEntity.destroy({ where: { planId: testDataRKM.potalGroupPlanID } });
                await LinesEntity.destroy({ where: { lineId: testDataRKM.AccountingLineNo } });
            } catch(e) {
                console.error(e);
                throw e;
            }
            
        })
        test('ordertype = 0 RINKモバイル', async () => {
            DefaultContext.jsonBody = testDataRKM;
            setOrderType("0");

            const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);

            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );
            // check should be inserted to DB
            const serviceOrder = await ServiceOrdersEntity.findOne({
                where: {
                    serviceOrderId: responseHeader.apiProcessID,
                },
            });
            expect(serviceOrder).not.toBeNull();
            expect(serviceOrder.tenantId).toEqual(testDataRKM.tenantId);
            expect(serviceOrder.lineId).toEqual(testDataRKM.AccountingLineNo);
            expect(serviceOrder.orderStatus).toEqual("完了");
            expect(serviceOrder.orderType).toEqual("回線グループクーポン追加");
            expect(serviceOrder.reserveDate).toBeNull();

            await checkCoreSwimmyApiLog(false);
        });

        test('ordertype = 1 RINKモバイル', async () => {
            DefaultContext.jsonBody = testDataRKM;
            setOrderType("1");

            const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);

            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );

            // check should be inserted to DB
            const serviceOrder = await ServiceOrdersEntity.findOne({
                where: {
                    serviceOrderId: responseHeader.apiProcessID,
                },
            });
            expect(serviceOrder).not.toBeNull();
            expect(serviceOrder.tenantId).toEqual(testDataRKM.tenantId);
            expect(serviceOrder.lineId).toEqual(testDataRKM.AccountingLineNo);
            expect(serviceOrder.orderStatus).toEqual("予約中");
            expect(serviceOrder.orderType).toEqual("回線グループクーポン追加");
            expect(serviceOrder.reserveDate).not.toBeNull();

            await checkCoreSwimmyApiLog(false);
        });

        test('ordertype = 2 RINKモバイル', async () => {
            DefaultContext.jsonBody = testDataRKM;
            setOrderType("2");

            const SO = await ServiceOrdersEntity.create({
                serviceOrderId: responseHeader.apiProcessID,
                tenantId: testDataRKM.tenantId,
                lineId: testDataRKM.AccountingLineNo,
                orderStatus: "予約中",
                orderType: "回線グループクーポン追加",
                reserveDate: new Date(),
                functionType: "10",
                orderDate: new Date(2020, 1, 1),
            })

            const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);

            expect(typeof result).toBe("object");
            expect(result).toHaveProperty("jsonBody");
            expect(result.jsonBody).toHaveProperty("responseHeader");
            expect(result.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );

            // check should be inserted to DB
            const serviceOrder = await ServiceOrdersEntity.findOne({
                where: {
                    serviceOrderId: DefaultContext.jsonBody.reserve_soId,
                },
            });
            expect(serviceOrder).not.toBeNull();
            expect(serviceOrder.tenantId).toEqual(testDataRKM.tenantId);
            expect(serviceOrder.lineId).toEqual(testDataRKM.AccountingLineNo);
            expect(serviceOrder.orderStatus).toEqual("完了");

            await checkCoreSwimmyApiLog(false);
        });
    });

    describe('Error Case', () => {
        it("should return CODE_100105 if orderType = 9", async () => {
            DefaultContext.jsonBody = testData;
            setOrderType("9");

            const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_100105);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_100111 for error in finding tenant", async () => {
            jest.spyOn(APICommonDAO.prototype, "getTenants").mockImplementation(throwSqlException);
            const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_100111);
            await checkCoreSwimmyApiLog(false);
        });

        it('should return CODE_100111 for tenant not found', async () => {
            DefaultContext.jsonBody.tenantId = "NAN000";
            const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_100111);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_100112 for failing to get tenant group plans", async () => {
            jest.spyOn(APILinesGroupDAO.prototype, "getLineGroupsInfo").mockImplementation(throwSqlException);
            const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_100112);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_100112 for failing to get lineGroups", async () => {
            jest.spyOn(APILinesGroupDAO.prototype, "getLineGroupsInfo").mockResolvedValue(null);
            const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_100112);
            await checkCoreSwimmyApiLog(false);
        });

        it("should not return CODE_100112 if fail to get lineGroups, if tenant type = 1 (CON000)", async () => {
            DefaultContext.jsonBody.tenantId = "CON000";

            DefaultContext.jsonBody.lineGroupId = "0";      // not exist
            const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).not.toEqual(ResultCdConstants.CODE_100112);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_100112 if line group is not null for tenant type = 1 (CON000)", async () => {
            DefaultContext.jsonBody.tenantId = "CON000";

            const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_100112);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_100112 if lineGroup's tenantId != tenantId, if tenant type != 1 (CON000)", async () => {
            jest.spyOn(APILinesGroupDAO.prototype, "getLineGroupsInfo").mockImplementation(async (groupId: string) => {
                const lineGroupEntity = await LineGroupsEntity.findOne({ where: { groupId } });
                lineGroupEntity.tenantId = "CON000";
                return lineGroupEntity;
            });
            const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_100112);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_100112 if line group have no plan id, if tenant type != 1 (CON000)", async () => {
            jest.spyOn(APILinesGroupDAO.prototype, "getLineGroupsInfo").mockImplementation(async (groupId: string) => {
                const lineGroupEntity = await LineGroupsEntity.findOne({ where: { groupId } });
                lineGroupEntity.planId = null;
                return lineGroupEntity;
            });
            const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_100112);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_100112 if line group status != 1", async () => {
            jest.spyOn(APILinesGroupDAO.prototype, "getLineGroupsInfo").mockImplementation(async (groupId: string) => {
                const lineGroupEntity = await LineGroupsEntity.findOne({ where: { groupId } });
                lineGroupEntity.status = 0;
                return lineGroupEntity;
            });
            const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_100112);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_999999 for order type = 2, client IP Address not in local ip list", async () => {
            try {
                DefaultContext.isInternalRequest = false;
                setOrderType("2");
                jest.spyOn(MvnoUtil, "getClientIPAddress").mockReturnValue("10.0.0.0");
                const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
                expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_999999);
                await checkCoreSwimmyApiLog(false);
            } finally {
                DefaultContext.isInternalRequest = true;
            }
        });

        it("should return CODE_100101 for failing format check for line group id, if line group exist", async () => {
            const testGroupId = "ABCDE";
            DefaultContext.jsonBody.lineGroupId = testGroupId;
            jest.spyOn(APILinesGroupDAO.prototype, "getLineGroupsInfo").mockImplementation(async (groupId: string) => {
                return await LineGroupsEntity.findOne({ where: { groupId: testData.lineGroupId } });
            });
            const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_100101);
            await checkCoreSwimmyApiLog(false);
        });

        it('Should return CODE_100101 for failing format check for potal group plan id', async () => {
            DefaultContext.jsonBody.potalGroupPlanID = "1";
            const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_100101);
            await checkCoreSwimmyApiLog(false);
        });

        it('Should return CODE_100101 for failing format check for option group plan id', async () => {
            DefaultContext.jsonBody.optionGroupPlanId = "1";
            const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_100101);
            await checkCoreSwimmyApiLog(false);
        });

        it('Should return CODE_100101 for failing format check for accounting line no', async () => {
            DefaultContext.jsonBody.AccountingLineNo = "1";
            const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_100101);
            await checkCoreSwimmyApiLog(false);
        });

        it('Should return CODE_100101 for 1/2 orderType, and failing format check for reserve date', async () => {
            setOrderType("1");
            DefaultContext.jsonBody.reserve_date = "Not a date string";
            const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_100101);
            await checkCoreSwimmyApiLog(false);
        });

        it("Should return CODE_100106 for orderType = 1, reserveDate < currentDate", async () => {
            setOrderType("1");
            DefaultContext.jsonBody.reserve_date = "2000/01/01 00:00";
            const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_100106);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_100107 for orderType = 1, if failing format check for reservationDateExecutionUnits", async () => {
            setOrderType("1");
            jest.spyOn(Check, "checkReservationDateExecutionUnitsFmt").mockReturnValue(false);
            const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_100107);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_100108 for orderType = 1, if failing relation check for reservationDateExecutionUnits", async () => {
            setOrderType("1");
            jest.spyOn(Check, "checkReservationDateExecutionUnits").mockReturnValue(false);
            const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_100108);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_100109 for orderType = 1, if failing format check for checkReservationsLimitDaysFmt", async () => {
            setOrderType("1");
            jest.spyOn(Check, "checkReservationsLimitDaysFmt").mockReturnValue(false);
            const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_100109);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_100110 for orderType = 1, if failing relation check for checkReservationsLimitDays", async () => {
            setOrderType("1");
            jest.spyOn(Check, "checkReservationsLimitDays").mockReturnValue(false);
            const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_100110);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_100113 for tenant type != 1/2/3, accounting line no is null", async () => {
            DefaultContext.jsonBody.AccountingLineNo = null;
            const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_100113);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_100114 if doCheck throws error", async () => {
            jest.spyOn(TenantManage.prototype, "doCheck").mockImplementation(throwSqlException);
            const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_100114);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_100114 if fails doCheck", async () => {
            // await LineTenantsEntity.destroy({ where: { lineId: testData.AccountingLineNo } });
            jest.spyOn(TenantManage.prototype, "doCheck").mockResolvedValue([false, null]);
            const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_100114);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_100115 if getLinesGroupId throws error", async () => {
            jest.spyOn(APILinesGroupDAO.prototype, "getLinesGroupId").mockImplementation(throwSqlException);
            const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_100115);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_100115 if tenant type != 1 (CON000), accounting line no is not null, line line group is null", async () => {
            // await LineLineGroupsEntity.destroy({ where: { lineId: testData.AccountingLineNo } });
            jest.spyOn(APILinesGroupDAO.prototype, "getLinesGroupId").mockResolvedValue(null);
            const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_100115);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_100102 if getTenantGroupPlans throws error", async () => {
            jest.spyOn(APICommonDAO.prototype, "getTenantGroupPlans").mockImplementation(throwSqlException);
            const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_100102);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_100102 if tenant group entity is null", async () => {
            // await TenantGroupPlansEntity.destroy({ where: { planId: testData.optionGroupPlanId } });
            // await TenantGroupPlansEntity.destroy({ where: { planId: testData.potalGroupPlanID } });
            jest.spyOn(APICommonDAO.prototype, "getTenantGroupPlans").mockResolvedValue(null);
            const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_100102);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_100116 if sql error throws at get using group plans", async () => {
            jest.spyOn(APILinesGroupDAO.prototype, "getUsingGroupPlans").mockImplementation(throwSqlException);
            const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_100116);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_100116 if tenantType != 1 (CON00), group plans = null", async () => {
            jest.spyOn(APILinesGroupDAO.prototype, "getUsingGroupPlans").mockResolvedValue(null);
            const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_100116);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_100103 if getCheckedGroupPlanOptionPlans throws error", async () => {
            jest.spyOn(APICommonDAO.prototype, "getCheckedGroupPlanOptionPlans").mockImplementation(throwSqlException);
            const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_100103);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_100103 if failed to get group plan option plans", async () => {
            jest.spyOn(APICommonDAO.prototype, "getCheckedGroupPlanOptionPlans").mockResolvedValue(null);
            const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_100103);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_100201 if getGroupPlans throw error", async () => {
            jest.spyOn(APICommonDAO.prototype, "getGroupPlans").mockImplementation(throwSqlException);
            const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_100201);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_100201 if failed to get group plan", async () => {
            jest.spyOn(APICommonDAO.prototype, "getGroupPlans").mockResolvedValue(null);
            const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_100201);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_100201 if getGroupOptionPlan throw error", async () => {
            jest.spyOn(APILinesGroupDAO.prototype, "getGroupOptionPlan").mockImplementation(throwSqlException);
            const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_100201);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_100201 if group option plan not found", async () => {
            jest.spyOn(APILinesGroupDAO.prototype, "getGroupOptionPlan").mockResolvedValue(null);
            const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_100201);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_100104 if group option plan's tpcServicePattern is null", async () => {
            // await GroupOptionPlansEntity.update({ tpcServicePattern: null }, { where: { optionPlanId: testData.optionGroupPlanId } });
            jest.spyOn(APILinesGroupDAO.prototype, "getGroupOptionPlan").mockImplementation(async () => {
                const groupOptionPlan = await GroupOptionPlansEntity.findOne({ where: { optionPlanId: testData.optionGroupPlanId } });
                groupOptionPlan.tpcServicePattern = null;
                return groupOptionPlan;
            });
            const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_100104);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_100104 if group plan's policyId is null", async () => {
            // await GroupPlansEntity.update({ policyId: null }, { where: { planId: testData.potalGroupPlanID } });
            jest.spyOn(APICommonDAO.prototype, "getGroupPlans").mockImplementation(async () => {
                const groupPlan = await GroupPlansEntity.findOne({ where: { planId: testData.potalGroupPlanID } });
                groupPlan.policyId = null;
                return groupPlan;
            });
            const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_100104);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_100104 if group option plan's tpcServicePattern is not 1/2/4", async () => {
            // await GroupOptionPlansEntity.update({ tpcServicePattern: 3 }, { where: { optionPlanId: testData.optionGroupPlanId } });
            jest.spyOn(APILinesGroupDAO.prototype, "getGroupOptionPlan").mockImplementation(async () => {
                const groupOptionPlan = await GroupOptionPlansEntity.findOne({ where: { optionPlanId: testData.optionGroupPlanId } });
                groupOptionPlan.tpcServicePattern = 3;
                return groupOptionPlan;
            });
            const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_100104);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_100104 if group plan's policyId fails format check", async () => {
            // await GroupPlansEntity.update({ policyId: 99999 }, { where: { planId: testData.potalGroupPlanID } });
            jest.spyOn(APICommonDAO.prototype, "getGroupPlans").mockImplementation(async () => {
                const groupPlan = await GroupPlansEntity.findOne({ where: { planId: testData.potalGroupPlanID } });
                groupPlan.policyId = 99999;
                return groupPlan;
            });
            const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_100104);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_100117 if check abolish so throws error", async () => {
            jest.spyOn(ApiCommon.prototype, "checkAbolishSo").mockImplementation(throwSqlException);
            const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_100117);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_100117 if failed to check abolish so", async () => {
            jest.spyOn(ApiCommon.prototype, "checkAbolishSo").mockResolvedValue(false);
            const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_100117);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_100118 if check line suspend throws error", async () => {
            jest.spyOn(ApiCommon.prototype, "checkLineSuspend").mockImplementation(throwSqlException);
            const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_100118);
            await checkCoreSwimmyApiLog(false);
        });

        it("should return CODE_100118 if failed to check line suspend", async () => {
            jest.spyOn(ApiCommon.prototype, "checkLineSuspend").mockResolvedValue(false);
            const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
            expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_100118);
            await checkCoreSwimmyApiLog(false);
        });

        describe("Later half, only for orderType = 0/2", () => {
            beforeEach(() => {
                setOrderType("0");
            });

            it("should return CODE_100310 if checkTpcConnection throws error", async () => {
                jest.spyOn(TenantManage.prototype, "checkTpcConnection").mockImplementation(throwSqlException);
                const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
                expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_100310);
                await checkCoreSwimmyApiLog(false);
            });

            it("should return CODE_100310 if failed tpc connection check", async () => {
                jest.spyOn(TenantManage.prototype, "checkTpcConnection").mockResolvedValue([false, null, null]);
                const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
                expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_100310);
                await checkCoreSwimmyApiLog(false);
            });

            describe("group option plan's tpcServicePattern = 1/4", () => {
                it("should return CODE_100301 if getGroupOptionPlanParameters throws error", async () => {
                    jest.spyOn(APICommonDAO.prototype, "getGroupOptionPlanParameters").mockImplementation(throwSqlException);
                    const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
                    expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_100301);
                    await checkCoreSwimmyApiLog(false);
                });

                it("should return CODE_100301 if failed to get group option plan parameters", async () => {
                    jest.spyOn(APICommonDAO.prototype, "getGroupOptionPlanParameters").mockResolvedValue(null);
                    const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
                    expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_100301);
                    await checkCoreSwimmyApiLog(false);
                });

                it("should return CODE_100301 if getGroupOptionPlanParameters at getting expiration date throws error", async () => {
                    jest.spyOn(APICommonDAO.prototype, "getGroupOptionPlanParameters").mockImplementationOnce(async (optionPlanId: string, key: string,
                    ) => {
                        return await GroupOptionPlanParametersEntity.findOne({
                            where: {
                                optionPlanId: parseInt(optionPlanId, 10),
                                key,
                            },
                        });
                    }).mockImplementationOnce(throwSqlException);
                    const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
                    expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_100301);
                    await checkCoreSwimmyApiLog(false);
                });

                it("should return CODE_100301 if failed to get expiration date", async () => {
                    // await GroupOptionPlanParametersEntity.destroy({ where: { optionPlanId: testData.optionGroupPlanId, key: "expiration_date" } });
                    jest.spyOn(APICommonDAO.prototype, "getGroupOptionPlanParameters").mockImplementationOnce(async (optionPlanId: string, key: string,
                    ) => {
                        return await GroupOptionPlanParametersEntity.findOne({
                            where: {
                                optionPlanId: parseInt(optionPlanId, 10),
                                key,
                            },
                        });
                    }).mockImplementationOnce(async (optionPlanId: string, key: string,
                    ) => {
                        return null;
                    });
                    const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
                    expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_100301);
                    await checkCoreSwimmyApiLog(false);
                });

                it("should return CODE_100302 if addAcquisition out of range", async () => {
                    // await GroupOptionPlanParametersEntity.update({ value: "-1" }, { where: { optionPlanId: testData.optionGroupPlanId, key: "add_bucket" } });
                    jest.spyOn(APICommonDAO.prototype, "getGroupOptionPlanParameters").mockImplementationOnce(async (optionPlanId: string, key: string,
                    ) => {
                        const result = await GroupOptionPlanParametersEntity.findOne({
                            where: {
                                optionPlanId: parseInt(optionPlanId, 10),
                                key,
                            },
                        });
                        result.value = "-1";
                        return result;
                    }).mockImplementationOnce(async (optionPlanId: string, key: string,
                    ) => {
                        return await GroupOptionPlanParametersEntity.findOne({
                            where: {
                                optionPlanId: parseInt(optionPlanId, 10),
                                key,
                            },
                        });
                    });
                    const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
                    expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_100302);
                    await checkCoreSwimmyApiLog(false);
                });

                it("should return CODE_100302 if expiration date out of range", async () => {
                    // await GroupOptionPlanParametersEntity.update({ value: "-1" }, { where: { optionPlanId: testData.optionGroupPlanId, key: "expiration_date" } });
                    jest.spyOn(APICommonDAO.prototype, "getGroupOptionPlanParameters").mockImplementationOnce(async (optionPlanId: string, key: string,
                    ) => {
                        return await GroupOptionPlanParametersEntity.findOne({
                            where: {
                                optionPlanId: parseInt(optionPlanId, 10),
                                key,
                            },
                        });
                    }).mockImplementationOnce(async (optionPlanId: string, key: string,
                    ) => {
                        const result = await GroupOptionPlanParametersEntity.findOne({
                            where: {
                                optionPlanId: parseInt(optionPlanId, 10),
                                key,
                            },
                        });
                        result.value = "-1";
                        return result;
                    });
                    const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
                    expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_100302);
                    await checkCoreSwimmyApiLog(false);
                });

                it("should return CODE_100401 if soap handle code = 000951", async () => {
                    const soapOutput = new SOAPCommonOutputDto();
                    soapOutput.setProcessCode("000951");
                    jest.spyOn(SOAPCommon.prototype, "callWebSoapApi").mockResolvedValue(soapOutput);
                    const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
                    expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_100401);
                    await checkCoreSwimmyApiLog(false);
                });

                it("should return CODE_100401 if soap response error", async () => {
                    nock(configData.soapApiUrl)
                        .post("")
                        .reply(200, SOAPTestHelper.simpleResponse("NG", "Error"))
                        .persist();
                    const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
                    expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_100401);
                    await checkCoreSwimmyApiLog(false);
                });

                it("should not have effect if failed at secondary TPC", async () => {
                    nock(configData.soapApiUrl)
                        .post("")
                        .reply(200, SOAPTestHelper.simpleResponse("OK"))
                        .post("")
                        .reply(200, SOAPTestHelper.simpleResponse("NG", "Error"));
                    const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
                    expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_000000);
                    await checkCoreSwimmyApiLog(true);
                });
            });

            describe("group option plan's tpcServicePattern = 2", () => {
                beforeEach(async () => {
                    await GroupOptionPlansEntity.update({ tpcServicePattern: 2 }, { where: { optionPlanId: testData.optionGroupPlanId } });
                });

                it("should return CODE_100303 if group option plan parameters = null", async () => {
                    jest.spyOn(APICommonDAO.prototype, "getGroupOptionPlanParameters").mockResolvedValue(null);
                    const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
                    expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_100303);
                    await checkCoreSwimmyApiLog(false);
                });

                it("should return CODE_100304 if addAcquisition out of range", async () => {
                    // await GroupOptionPlanParametersEntity.update({ value: "-1" }, { where: { optionPlanId: testData.optionGroupPlanId, key: "add_bucket" } });
                    jest.spyOn(APICommonDAO.prototype, "getGroupOptionPlanParameters").mockImplementationOnce(async (optionPlanId: string, key: string,
                    ) => {
                        const result = await GroupOptionPlanParametersEntity.findOne({
                            where: {
                                optionPlanId: parseInt(optionPlanId, 10),
                                key,
                            },
                        });
                        result.value = "-1";
                        return result;
                    }).mockImplementationOnce(async (optionPlanId: string, key: string,
                    ) => {
                        return await GroupOptionPlanParametersEntity.findOne({
                            where: {
                                optionPlanId: parseInt(optionPlanId, 10),
                                key,
                            },
                        });
                    });
                    const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
                    expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_100304);
                    await checkCoreSwimmyApiLog(false);
                });

                it("should return CODE_100401 if soap handle code != 000951", async () => {
                    const soapOutput = new SOAPCommonOutputDto();
                    soapOutput.setProcessCode("000000");
                    jest.spyOn(SOAPCommon.prototype, "callWebSoapApi").mockResolvedValue(soapOutput);
                    const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
                    expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_100401);
                    await checkCoreSwimmyApiLog(false);
                });

                it("should return CODE_100401 if soap response error", async () => {
                    nock(configData.soapApiUrl)
                        .post("")
                        .reply(200, SOAPTestHelper.simpleResponse("NG", "Error"))
                        .persist();
                    const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
                    expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_100401);
                    await checkCoreSwimmyApiLog(false);
                });

                it("should not have effect if failed at secondary TPC", async () => {
                    nock(configData.soapApiUrl)
                        .post("")
                        .reply(200, SOAPTestHelper.simpleResponse("OK"))
                        .post("")
                        .reply(200, SOAPTestHelper.simpleResponse("NG", "Error"));
                    const result = await LineGroupAddAcquisitionHandler.Handler(DefaultRequest, DefaultContext);
                    expect(result.jsonBody.responseHeader.processCode).toEqual(ResultCdConstants.CODE_000000);
                    await checkCoreSwimmyApiLog(true);
                });
            });
        });
    });
});