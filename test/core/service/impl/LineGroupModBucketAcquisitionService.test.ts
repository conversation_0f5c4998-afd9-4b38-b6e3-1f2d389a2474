import config from "config";

import { addDays, addMinutes, format, formatDate, setMinutes } from "date-fns";
import { ConnectionTimedOutError, Sequelize } from "sequelize";
import nock from "nock";

import AppConfig from "@/appconfig";
import Constants from "@/core/constant/Constants";
import ResultCdConstants from "@/core/constant/ResultCdConstants";
import { usePsql } from "@/database/psql";
import { disconnectMongo, useMongo } from "@/database/mongo";
import Check from "@/core/common/Check";
import MvnoUtil from "@/core/common/MvnoUtil";
import TenantManage from "@/core/common/TenantManage";
import SOAPCommon from "@/core/common/SOAPCommon";
import { readXML } from "@/core/common/SOAPCommonUtils";
import APICommonDAO from "@/core/dao/APICommonDAO";
import APILinesGroupDAO from "@/core/dao/ApiLinesGroupDAO";
import LineGroupModBucketAcquisitionInputDto from "@/core/dto/LineGroupModBucketAcquisitionInputDto";
import SOAPCommonOutputDto from "@/core/dto/SOAPCommonOutputDto";
import LineGroupsEntity from "@/core/entity/LineGroupsEntity";
import ServiceOrdersEntity from "@/core/entity/ServiceOrdersEntity";
import LineGroupModBucketAcquisitionService from "@/core/service/impl/LineGroupModBucketAcquisitionService";
import DefaultContext from "../../../testing/DefaultContext";
import DefaultRequest from "../../../testing/DefaultRequest";
import { describeWithDB, generateProcessID } from "../../../testing/TestHelper";
import {
    getConfigDataForTest,
    getDateNowForReservationTest,
} from "../../../testing/testdataHelper";

const createResponseXML = (
    result: "OK" | "NG",
    cap_polnum: string,
    errorInfomation: string,
) => {
    return `
    <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
        xmlns:ns1="http://ws.apache.org/axis2">
        <soapenv:Body>
            <Result>${result}</Result>
            <ServiceProfileInfomation>
                <cap_polnum>${cap_polnum}</cap_polnum>
            </ServiceProfileInfomation>
            <ErrorInfomation>${errorInfomation}</ErrorInfomation>
        </soapenv:Body>
    </soapenv:Envelope>
    `.trim();
};

const CONST_LONG_CALCULATION = 104857600;

describeWithDB(
    "@/core/service/impl/LineGroupModBucketAcquisitionService",
    () => {
        let sequelize: Sequelize;
        let instance: LineGroupModBucketAcquisitionService;

        const testdata = {
            testTenant: "TST000",
            shanaiTenant: "CON000",
            tsaTenant: "TSA000",
            lineGroupId: {
                ok: "1000010",
                notexist: "1234567",
                statusng: "1000011",
                tsa: "1000013",
            },
            planId: 50001, // TST
            reserve_date: "", // set in beforeAll
            capacity: 3000,
            lineGroupSumCapacity: CONST_LONG_CALCULATION - 5000,
            groupBufferByte: 2000,
            overCap: CONST_LONG_CALCULATION - 1000, // lineGroupSumCapacity + groupBufferByte
            cap_polnum: 5000,
            // soap request check
            policy_num: "101",
        };

        const __request: LineGroupModBucketAcquisitionInputDto = {
            requestHeader: {
                sequenceNo: "1234567",
                senderSystemId: "0001",
                apiKey: "",
                functionType: "15",
            },
            targetSoId: undefined,
            reserve_flag: undefined,
            reserve_soId: undefined,
            tenantId: "TST000",
            lineGroupId: String(testdata.lineGroupId.ok),
            capacity: "", // 単位: MB
            calculation: "1", // 1
            reserve_date: "", // YYYY/MM/DD HH:mm
        };

        const responseHeader = {
            sequenceNo: __request.requestHeader.sequenceNo,
            receivedDate: formatDate(new Date(), "yyyy/MM/dd HH:mm:ss"),
            processCode: ResultCdConstants.CODE_000000,
            apiProcessID: generateProcessID(),
        };

        const nockHeaders: nock.ReplyHeaders = {
            "Content-Type": "text/xml",
        };

        type ExcludeOpts = {
            getLineLineGroupsSumCapacity?: boolean;
            checkTpcConnection?: boolean;
        };
        function mockEverything(exclude?: ExcludeOpts) {
            if (exclude?.getLineLineGroupsSumCapacity !== true) {
                jest.spyOn(
                    APILinesGroupDAO.prototype,
                    "getLineLineGroupsSumCapacity",
                ).mockResolvedValue(testdata.lineGroupSumCapacity);
            }
            if (exclude?.checkTpcConnection !== true) {
                jest.spyOn(
                    TenantManage.prototype,
                    "checkTpcConnection",
                ).mockResolvedValue([
                    true,
                    "TpcDestIpAddress",
                    "TpcDestIpAddress",
                ]);
            }
        }

        let configData: ReturnType<typeof getConfigDataForTest>;

        async function prepareTestData() {
            await Promise.all([
                LineGroupsEntity.create({
                    groupId: testdata.lineGroupId.ok,
                    status: 1,
                    tenantId: testdata.testTenant,
                    planId: testdata.planId,
                }),
                LineGroupsEntity.create({
                    groupId: testdata.lineGroupId.statusng,
                    status: 0,
                    tenantId: testdata.testTenant,
                    planId: testdata.planId,
                }),
                LineGroupsEntity.create({
                    groupId: testdata.lineGroupId.tsa,
                    status: 1,
                    tenantId: testdata.tsaTenant,
                    planId: testdata.planId,
                }),
            ]);
        }

        async function clearTestData() {
            await LineGroupsEntity.destroy({
                where: {
                    groupId: [
                        testdata.lineGroupId.ok,
                        testdata.lineGroupId.statusng,
                        testdata.lineGroupId.tsa,
                    ],
                },
            });
        }

        beforeAll(async () => {
            sequelize = await usePsql();
            await useMongo(DefaultContext);
            await AppConfig.loadCoreMvnoConfig(DefaultContext);
            configData = getConfigDataForTest();
            nock.disableNetConnect();

            await clearTestData();
            await prepareTestData();

            testdata.reserve_date = format(
                addDays(getDateNowForReservationTest(configData), 1),
                "yyyy/MM/dd HH:mm",
            );
        });

        afterAll(async () => {
            await clearTestData();
            nock.enableNetConnect();

            await sequelize.close();
            await disconnectMongo(DefaultContext);
        });

        beforeEach(async () => {
            DefaultContext.jsonBody = Object.assign({}, __request);
            DefaultContext.responseHeader = Object.assign({}, responseHeader);
            jest.spyOn(MvnoUtil, "getDateTimeNow").mockReturnValue(
                responseHeader.receivedDate,
            );
            instance = new LineGroupModBucketAcquisitionService(
                DefaultRequest,
                DefaultContext,
            );
        });

        afterEach(async () => {
            jest.restoreAllMocks();
            jest.clearAllMocks();

            nock.abortPendingRequests();
            nock.cleanAll();

            await ServiceOrdersEntity.destroy({
                where: {
                    serviceOrderId: responseHeader.apiProcessID,
                },
            });
        });

        describe("OK cases", () => {
            let param = Object.assign({}, __request);

            beforeEach(() => {
                // reset param object
                param = Object.assign({}, __request);
            });

            const ordertypes: [
                string,
                string,
                { date: boolean; flg: boolean; soid: string },
            ][] = [
                [
                    "即時オーダー",
                    Constants.ORDER_TYPE_0,
                    { date: false, flg: false, soid: undefined },
                ],
                [
                    "予約実行オーダー",
                    Constants.ORDER_TYPE_2,
                    { date: true, flg: true, soid: "soid123" },
                ],
            ];

            // [ groupBufferByte, lineGroupSumCapacity, expected_cap_month ]
            const calculations: [string, number, number, number][] = [
                [
                    `buffer=0, sum=${testdata.lineGroupSumCapacity}`,
                    0,
                    testdata.lineGroupSumCapacity,
                    testdata.lineGroupSumCapacity,
                ],
                [
                    `buffer=2000, sum=${testdata.lineGroupSumCapacity}`,
                    testdata.groupBufferByte,
                    testdata.lineGroupSumCapacity,
                    testdata.groupBufferByte + testdata.lineGroupSumCapacity,
                ],
                ["buffer=0, sum=0", 0, 0, 1],
                [
                    `over`,
                    testdata.overCap,
                    testdata.lineGroupSumCapacity,
                    CONST_LONG_CALCULATION,
                ],
            ];

            describe.each(ordertypes)(
                "should return CODE_000000 (%s, calculation=1)",
                (_type, orderType, data) => {
                    test.each(calculations)(
                        "%s",
                        async (
                            _title,
                            groupBufferByte,
                            lineGroupSumCap,
                            cap_month,
                        ) => {
                            param.reserve_date = data.date
                                ? testdata.reserve_date
                                : undefined;
                            param.reserve_flag = data.flg;
                            param.reserve_soId = data.soid;

                            param.calculation = "1";

                            mockEverything({
                                getLineLineGroupsSumCapacity: true,
                            });
                            nock(configData.soapApiUrl)
                                .post("")
                                .reply(
                                    200,
                                    createResponseXML(
                                        "OK",
                                        testdata.cap_polnum.toString(),
                                        "",
                                    ),
                                )
                                .persist();
                            // for 予約実行
                            jest.spyOn(Check, "checkReserve").mockReturnValue(
                                true,
                            );
                            const orderTypeSpy = jest.spyOn(
                                Check,
                                "checkOrderType",
                            );
                            const callWebSoapApiSpy = jest.spyOn(
                                SOAPCommon.prototype,
                                "callWebSoapApi",
                            );
                            // override groupBufferByte
                            Object.defineProperty(instance, "groupBufferByte", {
                                value: groupBufferByte,
                            });
                            jest.spyOn(
                                APILinesGroupDAO.prototype,
                                "getLineLineGroupsSumCapacity",
                            ).mockResolvedValue(lineGroupSumCap);

                            const response = await instance.service(
                                param,
                                false,
                            );
                            expect(response?.jsonBody).toBeDefined();
                            expect(
                                response.jsonBody.responseHeader,
                            ).toBeDefined();
                            expect(
                                response.jsonBody.responseHeader.processCode,
                            ).toBe(ResultCdConstants.CODE_000000);
                            expect(orderTypeSpy).toHaveBeenCalled();
                            expect(orderTypeSpy).toHaveReturnedWith(orderType);

                            // check 基本容量設定 values (2nd SOAP call)
                            expect(callWebSoapApiSpy).toHaveBeenCalledTimes(3);
                            const soapParam = callWebSoapApiSpy.mock.calls[1];
                            const soapParamList = soapParam[3];
                            expect(soapParamList.length).toEqual(4);
                            expect(soapParamList[0].getName()).toEqual(
                                "group_number",
                            );
                            expect(soapParamList[0].getValue()).toEqual(
                                testdata.lineGroupId.ok,
                            );
                            expect(soapParamList[1].getName()).toEqual(
                                "policy_number",
                            );
                            expect(soapParamList[1].getValue()).toEqual(
                                testdata.policy_num,
                            );
                            expect(soapParamList[2].getName()).toEqual(
                                "cap_month",
                            );
                            expect(soapParamList[2].getValue()).toEqual(
                                cap_month.toString(), // from DB + setting
                            );
                            expect(soapParamList[3].getName()).toEqual(
                                "cap_polnum",
                            );
                            expect(soapParamList[3].getValue()).toEqual(
                                testdata.cap_polnum.toString(), // from 1st SOAP call
                            );
                        },
                    );
                },
            );

            // [ title, capacity, expected_cap_month ]
            const capacityCases: [string, number, number][] = [
                ["> 0", testdata.capacity, testdata.capacity],
                ["= 0", 0, 1],
            ];
            describe.each(ordertypes)(
                "should return CODE_000000 (%s, capacity)",
                (_type, orderType, data) => {
                    test.each(capacityCases)(
                        "capacity %s",
                        async (_title, capacity, cap_month) => {
                            param.reserve_date = data.date
                                ? testdata.reserve_date
                                : undefined;
                            param.reserve_flag = data.flg;
                            param.reserve_soId = data.soid;
                            param.capacity = capacity.toString();
                            param.calculation = undefined;
                            mockEverything();
                            nock(configData.soapApiUrl)
                                .post("")
                                .reply(
                                    200,
                                    createResponseXML(
                                        "OK",
                                        testdata.cap_polnum.toString(),
                                        "",
                                    ),
                                )
                                .persist();
                            const orderTypeSpy = jest.spyOn(
                                Check,
                                "checkOrderType",
                            );
                            // for 予約実行
                            jest.spyOn(Check, "checkReserve").mockReturnValue(
                                true,
                            );
                            const callWebSoapApiSpy = jest.spyOn(
                                SOAPCommon.prototype,
                                "callWebSoapApi",
                            );

                            const response = await instance.service(
                                param,
                                false,
                            );
                            expect(response?.jsonBody).toBeDefined();
                            expect(
                                response.jsonBody.responseHeader,
                            ).toBeDefined();
                            expect(
                                response.jsonBody.responseHeader.processCode,
                            ).toBe(ResultCdConstants.CODE_000000);
                            expect(orderTypeSpy).toHaveBeenCalled();
                            expect(orderTypeSpy).toHaveReturnedWith(orderType);

                            // check 基本容量設定 values (2nd SOAP call)
                            expect(callWebSoapApiSpy).toHaveBeenCalledTimes(3);
                            const soapParam = callWebSoapApiSpy.mock.calls[1];
                            const soapParamList = soapParam[3];
                            expect(soapParamList.length).toEqual(4);
                            expect(soapParamList[0].getName()).toEqual(
                                "group_number",
                            );
                            expect(soapParamList[0].getValue()).toEqual(
                                testdata.lineGroupId.ok,
                            );
                            expect(soapParamList[1].getName()).toEqual(
                                "policy_number",
                            );
                            expect(soapParamList[1].getValue()).toEqual(
                                testdata.policy_num,
                            );
                            expect(soapParamList[2].getName()).toEqual(
                                "cap_month",
                            );
                            expect(soapParamList[2].getValue()).toEqual(
                                cap_month.toString(), // from parameter
                            );
                            expect(soapParamList[3].getName()).toEqual(
                                "cap_polnum",
                            );
                            expect(soapParamList[3].getValue()).toEqual(
                                testdata.cap_polnum.toString(), // from 1st SOAP call
                            );
                        },
                    );
                },
            );

            test("should return CODE_000000 even if secondary TPC SOAP call fails", async () => {
                param.capacity = testdata.capacity.toString();
                param.calculation = undefined;

                mockEverything();
                nock(configData.soapApiUrl)
                    .post("")
                    .reply(200, createResponseXML("OK", "101", ""))
                    .post("")
                    .reply(200, createResponseXML("OK", "101", ""))
                    .post("")
                    .reply(200, createResponseXML("NG", "", "some err"));

                const response = await instance.service(param, false);
                expect(response?.jsonBody).toBeDefined();
                expect(response.jsonBody.responseHeader).toBeDefined();
                expect(response.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_000000,
                );
            });

            test("should return CODE_000000 even if secondary TPC SOAP call returns CODE_000951", async () => {
                param.capacity = testdata.capacity.toString();
                param.calculation = undefined;

                mockEverything();
                nock(configData.soapApiUrl)
                    .post("")
                    .reply(200, createResponseXML("OK", "101", ""))
                    .persist();
                const output = new SOAPCommonOutputDto();
                output.setProcessCode(ResultCdConstants.CODE_000000);
                output.setDoc(
                    readXML(
                        createResponseXML(
                            "OK",
                            testdata.cap_polnum.toString(),
                            "",
                        ),
                    ),
                );
                const outputNG = new SOAPCommonOutputDto();
                outputNG.setProcessCode(ResultCdConstants.CODE_000951);
                jest.spyOn(SOAPCommon.prototype, "callWebSoapApi")
                    .mockResolvedValueOnce(output)
                    .mockResolvedValueOnce(output)
                    .mockResolvedValueOnce(outputNG);

                const response = await instance.service(param, false);
                expect(response?.jsonBody).toBeDefined();
                expect(response.jsonBody.responseHeader).toBeDefined();
                expect(response.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_000000,
                );
                // warn log
                expect(DefaultContext.warn).toHaveBeenCalledTimes(1);
            });

            test("should return CODE_000000 even if lineGroupId is number", async () => {
                param.capacity = testdata.capacity.toString();
                param.calculation = undefined;
                param.lineGroupId = parseInt(
                    testdata.lineGroupId.ok,
                    10,
                ) as any;

                mockEverything({
                    getLineLineGroupsSumCapacity: true,
                });
                nock(configData.soapApiUrl)
                    .post("")
                    .reply(
                        200,
                        createResponseXML(
                            "OK",
                            testdata.cap_polnum.toString(),
                            "",
                        ),
                    )
                    .persist();

                const response = await instance.service(param, false);
                expect(response?.jsonBody).toBeDefined();
                expect(response.jsonBody.responseHeader).toBeDefined();
                expect(response.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_000000,
                );
            });

            test("should return CODE_000000 even if capacity is number", async () => {
                param.capacity = testdata.capacity as any;
                param.calculation = undefined;

                mockEverything({
                    getLineLineGroupsSumCapacity: true,
                });
                nock(configData.soapApiUrl)
                    .post("")
                    .reply(
                        200,
                        createResponseXML(
                            "OK",
                            testdata.cap_polnum.toString(),
                            "",
                        ),
                    )
                    .persist();

                const response = await instance.service(param, false);
                expect(response?.jsonBody).toBeDefined();
                expect(response.jsonBody.responseHeader).toBeDefined();
                expect(response.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_000000,
                );
            });

            test("should return CODE_000000 even if calculation is number", async () => {
                param.capacity = undefined;
                param.calculation = 1 as any;

                mockEverything({
                    getLineLineGroupsSumCapacity: true,
                });
                nock(configData.soapApiUrl)
                    .post("")
                    .reply(
                        200,
                        createResponseXML(
                            "OK",
                            testdata.cap_polnum.toString(),
                            "",
                        ),
                    )
                    .persist();
                // for 予約実行
                jest.spyOn(Check, "checkReserve").mockReturnValue(true);
                const orderTypeSpy = jest.spyOn(Check, "checkOrderType");
                const callWebSoapApiSpy = jest.spyOn(
                    SOAPCommon.prototype,
                    "callWebSoapApi",
                );
                // override groupBufferByte
                Object.defineProperty(instance, "groupBufferByte", {
                    value: testdata.groupBufferByte,
                });
                jest.spyOn(
                    APILinesGroupDAO.prototype,
                    "getLineLineGroupsSumCapacity",
                ).mockResolvedValue(testdata.lineGroupSumCapacity);

                const response = await instance.service(param, false);
                expect(response?.jsonBody).toBeDefined();
                expect(response.jsonBody.responseHeader).toBeDefined();
                expect(response.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_000000,
                );
            });
        });

        describe("NG cases", () => {
            let param = Object.assign({}, __request);

            beforeEach(() => {
                // reset param object
                param = Object.assign({}, __request);
            });

            test("should return CODE_150105 if order type can't be determined", async () => {
                param.reserve_date = "some date";
                param.reserve_flag = true;
                param.reserve_soId = undefined;

                const response = await instance.service(param, false);
                expect(response?.jsonBody).toBeDefined();
                expect(response.jsonBody.responseHeader).toBeDefined();
                expect(response.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_150105,
                );
            });

            test("should return CODE_999999 if order type is 2 (予約実行オーダ) and client address invalid", async () => {
                try {
                    DefaultContext.isInternalRequest = false;
                    param.reserve_date = "some date";
                    param.reserve_flag = true;
                    param.reserve_soId = "1234567";

                    const response = await instance.service(param, false);
                    expect(response?.jsonBody).toBeDefined();
                    expect(response.jsonBody.responseHeader).toBeDefined();
                    expect(response.jsonBody.responseHeader.processCode).toBe(
                        ResultCdConstants.CODE_999999,
                    );
                } finally {
                    DefaultContext.isInternalRequest = true;
                }
            });

            test("should return CODE_150101 if groupBufferByte setting is not set correctly", async () => {
                Object.defineProperty(instance, "groupBufferByte", {
                    value: "",
                });

                const response = await instance.service(param, false);
                expect(response?.jsonBody).toBeDefined();
                expect(response.jsonBody.responseHeader).toBeDefined();
                expect(response.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_150101,
                );
            });

            test("should return CODE_150102 if lineGroupId is not set", async () => {
                param.lineGroupId = undefined;

                const response = await instance.service(param, false);
                expect(response?.jsonBody).toBeDefined();
                expect(response.jsonBody.responseHeader).toBeDefined();
                expect(response.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_150102,
                );
            });

            test("should return CODE_150102 if capacity and calculation are not set", async () => {
                param.capacity = undefined;
                param.calculation = undefined;

                const response = await instance.service(param, false);
                expect(response?.jsonBody).toBeDefined();
                expect(response.jsonBody.responseHeader).toBeDefined();
                expect(response.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_150102,
                );
            });

            test("should return CODE_150102 if capacity and calculation are both set", async () => {
                param.capacity = "100";
                param.calculation = "1";

                const response = await instance.service(param, false);
                expect(response?.jsonBody).toBeDefined();
                expect(response.jsonBody.responseHeader).toBeDefined();
                expect(response.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_150102,
                );
            });

            test("should return CODE_150102 if capacity is not numeric", async () => {
                param.capacity = "abc";
                param.calculation = undefined;

                const response = await instance.service(param, false);
                expect(response?.jsonBody).toBeDefined();
                expect(response.jsonBody.responseHeader).toBeDefined();
                expect(response.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_150102,
                );
            });

            test("should return CODE_150102 if capacity is less than 0", async () => {
                param.capacity = "-100";
                param.calculation = undefined;

                const response = await instance.service(param, false);
                expect(response?.jsonBody).toBeDefined();
                expect(response.jsonBody.responseHeader).toBeDefined();
                expect(response.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_150102,
                );
            });

            test("should return CODE_150102 if capacity is greater than CONST_LONG_CALCULATION (仕様書上限)", async () => {
                param.capacity = "104857601";
                param.calculation = undefined;

                const response = await instance.service(param, false);
                expect(response?.jsonBody).toBeDefined();
                expect(response.jsonBody.responseHeader).toBeDefined();
                expect(response.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_150102,
                );
            });

            test("should return CODE_150102 if calculation is not numeric", async () => {
                param.calculation = "abc";

                const response = await instance.service(param, false);
                expect(response?.jsonBody).toBeDefined();
                expect(response.jsonBody.responseHeader).toBeDefined();
                expect(response.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_150102,
                );
            });

            test("should retrun CODE_150102 if calculation is set but not equal to 1", async () => {
                param.calculation = "2";

                const response = await instance.service(param, false);
                expect(response?.jsonBody).toBeDefined();
                expect(response.jsonBody.responseHeader).toBeDefined();
                expect(response.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_150102,
                );
            });

            test("should return CODE_150102 if reserve_date is not in valid format", async () => {
                param.reserve_date = "2022-01-01 00:00";
                param.calculation = "1";

                const response = await instance.service(param, false);
                expect(response?.jsonBody).toBeDefined();
                expect(response.jsonBody.responseHeader).toBeDefined();
                expect(response.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_150102,
                );
            });

            test("should return CODE_150106 if reserve_date is in the past", async () => {
                param.reserve_date = format(
                    addDays(new Date(), -1),
                    "yyyy/MM/dd HH:mm",
                );
                param.calculation = "1";

                const response = await instance.service(param, false);
                expect(response?.jsonBody).toBeDefined();
                expect(response.jsonBody.responseHeader).toBeDefined();
                expect(response.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_150106,
                );
            });

            test("should return CODE_150107 if reservationDateExecutionUnits is not set correctly", async () => {
                Object.defineProperty(
                    instance,
                    "reservationDateExecutionUnits",
                    {
                        value: "",
                    },
                );
                param.reserve_date = testdata.reserve_date;

                const response = await instance.service(param, false);
                expect(response?.jsonBody).toBeDefined();
                expect(response.jsonBody.responseHeader).toBeDefined();
                expect(response.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_150107,
                );
            });

            test("should return CODE_150108 if reserve_date is after past reservationDateExecutionUnits", async () => {
                const minute =
                    parseInt(configData.reservationDateExecutionUnits, 10) + 1;
                const reserve_date = addDays(new Date(), 1);
                reserve_date.setMinutes(minute);
                param.reserve_date = format(reserve_date, "yyyy/MM/dd HH:mm");

                const response = await instance.service(param, false);
                expect(response?.jsonBody).toBeDefined();
                expect(response.jsonBody.responseHeader).toBeDefined();
                expect(response.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_150108,
                );
            });

            test("should return CODE_150109 if reservationsLimitDays setting is not set correctly", async () => {
                Object.defineProperty(instance, "reservationsLimitDays", {
                    value: "",
                });
                param.reserve_date = testdata.reserve_date;

                const response = await instance.service(param, false);
                expect(response?.jsonBody).toBeDefined();
                expect(response.jsonBody.responseHeader).toBeDefined();
                expect(response.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_150109,
                );
            });

            test("should return CODE_150110 if reserve_date is after past reservationsLimitDays", async () => {
                param.reserve_date = format(
                    addDays(
                        getDateNowForReservationTest(configData),
                        parseInt(configData.reservationsLimitDays, 10) + 1,
                    ),
                    "yyyy/MM/dd HH:mm",
                );

                const response = await instance.service(param, false);
                expect(response?.jsonBody).toBeDefined();
                expect(response.jsonBody.responseHeader).toBeDefined();
                expect(response.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_150110,
                );
            });

            test("should return CODE_150103 if tenantId is not found", async () => {
                param.tenantId = "notfound";

                const response = await instance.service(param, false);
                expect(response?.jsonBody).toBeDefined();
                expect(response.jsonBody.responseHeader).toBeDefined();
                expect(response.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_150103,
                );
            });

            test("should return CODE_150103 if tenantId is not set", async () => {
                param.tenantId = undefined;

                const response = await instance.service(param, false);
                expect(response?.jsonBody).toBeDefined();
                expect(response.jsonBody.responseHeader).toBeDefined();
                expect(response.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_150103,
                );
            });

            test("should return CODE_150103 if failed to query tenant data", async () => {
                jest.spyOn(
                    APICommonDAO.prototype,
                    "getTenants",
                ).mockRejectedValue(
                    new ConnectionTimedOutError(Error("test error")),
                );

                const response = await instance.service(param, false);
                expect(response?.jsonBody).toBeDefined();
                expect(response.jsonBody.responseHeader).toBeDefined();
                expect(response.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_150103,
                );
            });

            test("should return CODE_150104 if tenant is shanai tenant", async () => {
                param.tenantId = testdata.shanaiTenant;

                const response = await instance.service(param, false);
                expect(response?.jsonBody).toBeDefined();
                expect(response.jsonBody.responseHeader).toBeDefined();
                expect(response.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_150104,
                );
            });

            test("should return CODE_150201 if lineGroups data can't be found", async () => {
                param.lineGroupId = testdata.lineGroupId.notexist;

                const response = await instance.service(param, false);
                expect(response?.jsonBody).toBeDefined();
                expect(response.jsonBody.responseHeader).toBeDefined();
                expect(response.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_150201,
                );
            });

            test("should return CODE_150201 if line group tenant doesn't match request tenant", async () => {
                param.lineGroupId = testdata.lineGroupId.tsa;

                const response = await instance.service(param, false);
                expect(response?.jsonBody).toBeDefined();
                expect(response.jsonBody.responseHeader).toBeDefined();
                expect(response.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_150201,
                );
            });

            test("should return CODE_150201 if line group status is not 1", async () => {
                param.lineGroupId = testdata.lineGroupId.statusng;

                const response = await instance.service(param, false);
                expect(response?.jsonBody).toBeDefined();
                expect(response.jsonBody.responseHeader).toBeDefined();
                expect(response.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_150201,
                );
            });

            test("should return CODE_150201 if failed to query line group data", async () => {
                jest.spyOn(
                    APILinesGroupDAO.prototype,
                    "getLineGroupsCapacityShare",
                ).mockRejectedValue(
                    new ConnectionTimedOutError(Error("test error")),
                );

                const response = await instance.service(param, false);
                expect(response?.jsonBody).toBeDefined();
                expect(response.jsonBody.responseHeader).toBeDefined();
                expect(response.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_150201,
                );
            });

            test("should return CODE_150301 getLineLineGroupsSumCapacity if sum capacity is not found", async () => {
                jest.spyOn(
                    APILinesGroupDAO.prototype,
                    "getLineLineGroupsSumCapacity",
                ).mockResolvedValue(null);
                param.capacity = undefined;
                param.calculation = "1";

                const response = await instance.service(param, false);
                expect(response?.jsonBody).toBeDefined();
                expect(response.jsonBody.responseHeader).toBeDefined();
                expect(response.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_150301,
                );
            });

            test("should return CODE_150301 if failed to query sum capacity", async () => {
                jest.spyOn(
                    APILinesGroupDAO.prototype,
                    "getLineLineGroupsSumCapacity",
                ).mockRejectedValue(
                    new ConnectionTimedOutError(Error("test error")),
                );

                const response = await instance.service(param, false);
                expect(response?.jsonBody).toBeDefined();
                expect(response.jsonBody.responseHeader).toBeDefined();
                expect(response.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_150301,
                );
            });

            test("should return CODE_150401 if failed to query tpc connection", async () => {
                mockEverything({ checkTpcConnection: true });
                jest.spyOn(
                    TenantManage.prototype,
                    "checkTpcConnection",
                ).mockRejectedValue(
                    new ConnectionTimedOutError(Error("test error")),
                );

                const response = await instance.service(param, false);
                expect(response?.jsonBody).toBeDefined();
                expect(response.jsonBody.responseHeader).toBeDefined();
                expect(response.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_150401,
                );
            });

            test("should return CODE_150401 if tpc connection is not available for tenant", async () => {
                mockEverything({ checkTpcConnection: true });
                jest.spyOn(
                    TenantManage.prototype,
                    "checkTpcConnection",
                ).mockResolvedValue([false, "", ""]);

                const response = await instance.service(param, false);
                expect(response?.jsonBody).toBeDefined();
                expect(response.jsonBody.responseHeader).toBeDefined();
                expect(response.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_150401,
                );
            });

            test("should return CODE_150501 if IP address for SOAP is empty", async () => {
                mockEverything({ checkTpcConnection: true });
                jest.spyOn(
                    TenantManage.prototype,
                    "checkTpcConnection",
                ).mockResolvedValue([true, "", ""]);

                const response = await instance.service(param, false);
                expect(response?.jsonBody).toBeDefined();
                expect(response.jsonBody.responseHeader).toBeDefined();
                expect(response.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_150501,
                );
            });

            test("should return CODE_150501 if serviceProfileQuery SOAP response is NG", async () => {
                mockEverything();
                nock(configData.soapApiUrl)
                    .post("")
                    .reply(
                        200,
                        createResponseXML("NG", "", "some error message"),
                        nockHeaders,
                    );

                const response = await instance.service(param, false);
                expect(response?.jsonBody).toBeDefined();
                expect(response.jsonBody.responseHeader).toBeDefined();
                expect(response.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_150501,
                );
            });

            test("should return CODE_150601 if capPolNum is not found in serviceProfileQuery SOAP response", async () => {
                mockEverything();
                nock(configData.soapApiUrl)
                    .post("")
                    .reply(200, createResponseXML("OK", "", ""), nockHeaders);

                const response = await instance.service(param, false);
                expect(response?.jsonBody).toBeDefined();
                expect(response.jsonBody.responseHeader).toBeDefined();
                expect(response.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_150601,
                );
            });

            test("should return CODE_150801 if failed to send SOAP request serviceProfileRequestLite", async () => {
                mockEverything({ checkTpcConnection: true });
                jest.spyOn(
                    TenantManage.prototype,
                    "checkTpcConnection",
                ).mockResolvedValue([true, "", ""]);
                // since 1st call succeed, 2nd call usually not fail
                const output = new SOAPCommonOutputDto();
                output.setProcessCode(ResultCdConstants.CODE_000000);
                output.setDoc(
                    readXML(
                        createResponseXML(
                            "OK",
                            testdata.cap_polnum.toString(),
                            "",
                        ),
                    ),
                );
                jest.spyOn(
                    SOAPCommon.prototype,
                    "callWebSoapApi",
                ).mockResolvedValueOnce(output);

                const response = await instance.service(param, false);
                expect(response?.jsonBody).toBeDefined();
                expect(response.jsonBody.responseHeader).toBeDefined();
                expect(response.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_150801,
                );
            });

            test("should return CODE_150801 if serviceProfileRequestLite SOAP response is NG", async () => {
                mockEverything();
                nock(configData.soapApiUrl)
                    .post("")
                    .reply(
                        200,
                        createResponseXML(
                            "OK",
                            testdata.cap_polnum.toString(),
                            "",
                        ),
                    )
                    .post("")
                    .reply(200, createResponseXML("NG", "", ""), nockHeaders);

                const response = await instance.service(param, false);
                expect(response?.jsonBody).toBeDefined();
                expect(response.jsonBody.responseHeader).toBeDefined();
                expect(response.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_150801,
                );
            });

            test("should return CODE_150801 if TPC cannot be reached (SOAPException)", async () => {
                mockEverything();
                // no nock

                const response = await instance.service(param, false);
                expect(response?.jsonBody).toBeDefined();
                expect(response.jsonBody.responseHeader).toBeDefined();
                expect(response.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_150801,
                );
            });

            test("should return CODE_150801 if secondary TPC cannot be reached (SOAPException)", async () => {
                mockEverything();
                nock(configData.soapApiUrl)
                    .post("")
                    .reply(
                        200,
                        createResponseXML(
                            "OK",
                            testdata.cap_polnum.toString(),
                            "",
                        ),
                    )
                    .post("")
                    .reply(200, createResponseXML("OK", "", ""));

                const response = await instance.service(param, false);
                expect(response?.jsonBody).toBeDefined();
                expect(response.jsonBody.responseHeader).toBeDefined();
                expect(response.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_150801,
                );
            });

            test("should return CODE_999999 if unexpected error occurs", async () => {
                mockEverything();
                jest.spyOn(
                    SOAPCommon.prototype,
                    "callWebSoapApi",
                ).mockRejectedValue(new Error("test error"));

                const response = await instance.service(param, false);
                expect(response?.jsonBody).toBeDefined();
                expect(response.jsonBody.responseHeader).toBeDefined();
                expect(response.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_999999,
                );
            });
        });
    },
);
