import { addDays, format, formatDate, parse } from "date-fns";
import nock from "nock";
import { ConnectionTimedOutError, Sequelize, Transaction } from "sequelize";
import AppConfig from "@/appconfig";
import Check from "@/core/common/Check";
import MvnoUtil from "@/core/common/MvnoUtil";
import SOAPCommon from "@/core/common/SOAPCommon";
import TenantManage from "@/core/common/TenantManage";
import Constants from "@/core/constant/Constants";
import ResultCdConstants from "@/core/constant/ResultCdConstants";
import APICommonDAO from "@/core/dao/APICommonDAO";
import APILinesGroupDAO from "@/core/dao/ApiLinesGroupDAO";
import { usePsql } from "@/database/psql";
import { disconnectMongo, useMongo } from "@/database/mongo";
import ServiceOrdersEntity from "@/core/entity/ServiceOrdersEntity";
import { TenantsEntity } from "@/core/entity/TenantsEntity";
import DefaultContext from "../../../testing/DefaultContext";
import DefaultRequest from "../../../testing/DefaultRequest";
import {
    getConfigDataForTest,
    getDateNowForReservationTest,
} from "../../../testing/testdataHelper";
import {
    describeWithDB,
    generateProcessID,
    generateSOID,
    useFasterRetries,
} from "../../../testing/TestHelper";
const enqueueMessage = jest.fn();
jest.mock("../../../../src/services/servicebus", () => ({
    __esModule: true,
    default: enqueueMessage,
}));
import SOAPTestHelper from "../../../testing/soapTestHelper";
import LinePlanAcquisitionServiceTx from "@/core/service/impl/LinePlanAcquisitionServiceTx";
import LinePlanAcquisitionInputDto from "@/core/dto/LinePlanAcquisitionInputDto";
import { LinesEntity } from "@/core/entity/LinesEntity";
import PlansEntity from "@/core/entity/PlansEntity";
import APILinesDAO from "@/core/dao/APILinesDAO";
import ApiCommon from "@/core/common/ApiCommon";
import TenantPlansEntity from "@/core/entity/TenantPlansEntity";
import LineLineGroupsEntity from "@/core/entity/LineLineGroupsEntity";
import LineGroupsEntity from "@/core/entity/LineGroupsEntity";
import SOAPCommonOutputDto from "@/core/dto/SOAPCommonOutputDto";
import { readXML } from "@/core/common/SOAPCommonUtils";
import { LinePlanAcquisitionHandler } from "@/functions/LinePlanAcquisitionHandler";
import CoreSwimmyApiLog from "@/models/coreSwimmyApiLog";
import AutoModbucketLineGroupsEntity from "@/core/entity/AutoModbucketLineGroupsEntity";

// TODO need to fix this test suite (060131)
describeWithDB("@/core/service/impl/LinePlanAcquisitionServiceTx", () => {
    let sequelize: Sequelize;
    let instance: LinePlanAcquisitionServiceTx;
    const testdata = {
        lineId: "10001964699",
        lineStatus: "1",
        nnumber: "N141003812",
        simFlag: false,
        planName: "５G激安通信",
        network: "４G・５G",
        smsEnable: true,
        reserve_date: "",
        reserveSOID: generateProcessID(),
        potalPlanID_pre: "10001",
        potalPlanID: "10002",
        planIdT: "********",
        tenantId: "FON999",
        targetSoId: generateSOID(),
    };
    const dumpData = {
        tenant: {
            tenantId: "FON999",
            tenantName: "できる学生",
            tenantMaxConnection: 10,
            tenantType: 1,
            hashedPassword: "",
            office: true,
            status: true,
            tenantLevel: 1,
            customizeMaxConnection: 10,
            notvoiceRoamingFlag: false,
            pTenantId: "ZZZ000",
            csvOutputPattern: 1,
            tpc: "TpcDestIpAddress",
            tpc2: "TpcDestIpAddress2",
        },
        line: {
            lineId: testdata.lineId,
            lineStatus: testdata.lineStatus,
            nnumber: testdata.nnumber,
            simFlag: testdata.simFlag,
            planName: testdata.planName,
            pricePlanId: null,
        },
        tenantPlant: {
            tenantId: testdata.tenantId,
            planId: parseInt(testdata.potalPlanID_pre, 10),
            changeCount: 0,
            changeTiming: '',
            changePlanId: parseInt(testdata.potalPlanID, 10),
        },
    };
    const request: LinePlanAcquisitionInputDto = {
        requestHeader: {
            sequenceNo: "1234567",
            senderSystemId: "0001",
            apiKey: "",
            functionType: "06",
        },
        tenantId: testdata.tenantId,
        targetSoId: testdata.targetSoId,
        reserve_flag: undefined,
        reserve_soId: undefined,
        reserve_date: "",
        lineNo: testdata.lineId,
        csvUnnecessaryFlag: "0",
        potalPlanID_pre: testdata.potalPlanID_pre,
        potalPlanID: testdata.potalPlanID,
    };
    let param = Object.assign({}, request);

    const responseHeader = {
        sequenceNo: request.requestHeader.sequenceNo,
        receivedDate: formatDate(new Date(), "yyyy/MM/dd HH:mm:ss"),
        processCode: ResultCdConstants.CODE_000000,
        apiProcessID: generateProcessID(),
    };

    function setOrderType(orderType: "0" | "1" | "2" | "9") {
        const nextMonthDate = new Date();
        nextMonthDate.setMonth(nextMonthDate.getMonth() + 1);
        nextMonthDate.setDate(1);
        nextMonthDate.setHours(19, 0, 0, 0);
        switch (orderType) {
            case "1":
                DefaultContext.jsonBody.reserve_date = formatDate(nextMonthDate, "yyyy/MM/dd HH:mm");
                DefaultContext.jsonBody.reserve_flag = false;
                DefaultContext.jsonBody.reserve_soId = null;
                break;
            case "2":
                DefaultContext.jsonBody.reserve_date = formatDate(nextMonthDate, "yyyy/MM/dd HH:mm");
                DefaultContext.jsonBody.reserve_flag = true;
                DefaultContext.jsonBody.reserve_soId = responseHeader.apiProcessID;
                break;
            case "9":
                DefaultContext.jsonBody.reserve_date = null;
                DefaultContext.jsonBody.reserve_flag = false;
                DefaultContext.jsonBody.reserve_soId = responseHeader.apiProcessID;
                break;
            case "0":
            default:
                DefaultContext.jsonBody.reserve_date = null;
                DefaultContext.jsonBody.reserve_flag = null;
                DefaultContext.jsonBody.reserve_soId = null;
                break;
        }
    }

    let configData: ReturnType<typeof getConfigDataForTest>;
    async function removeServiceOrder() {
        await ServiceOrdersEntity.destroy({
            where: {
                serviceOrderId: testdata.reserveSOID,
                lineId: testdata.lineId,
                orderStatus: "予約中",
                orderType: "プラン変更",
            },
        });
    }
    const checkResponse = (result: any, processCode: string) => {
        expect(result?.jsonBody).toBeDefined();
        expect(result.jsonBody.responseHeader).toBeDefined();
        expect(result.jsonBody.responseHeader.processCode).toEqual(processCode);
    };

    const checkCoreSwimmyApiLog = async (
        apiProcessID: string,
        exists: boolean,
    ) => {
        const apiLog = await CoreSwimmyApiLog.findOne({
            requestOrderId: apiProcessID,
        });
        if (exists) {
            expect(apiLog).not.toBeNull();
            expect(enqueueMessage).toBeCalledTimes(1);
        } else {
            expect(apiLog).toBeNull();
            expect(enqueueMessage).not.toBeCalled();
        }
    };

    const nulls = [null, null, "127.0.0.1"];
    async function checkLine(
        lineNo: string,
        plan: string,
        planName: string,
        modifyFlag: boolean,
    ) {
        const result = await LinesEntity.findOne({
            where: {
                lineId: lineNo,
            },
        });
        expect(result.pricePlanId).toBe(plan);
        expect(result.pricePlanName).toBe(planName);
        expect(result.modifyFlag).toBe(modifyFlag);
    }
    async function checkLineLineGroup(
        lineNo: string,
        capacity: number,
        exist: boolean,
    ) {
        const result = await LineLineGroupsEntity.findOne({
            where: {
                lineId: lineNo,
            },
        });
        if (exist) {
            expect(result.lineId).toBe(lineNo);
            expect(result.basicCapacity).toBe(capacity);
        } else {
            expect(result).toBeNull();
        }
    }
    async function prepareTestData() {
        await LinesEntity.create(dumpData.line);
        await ServiceOrdersEntity.create({
            serviceOrderId: testdata.reserveSOID,
            lineId: testdata.lineId,
            orderStatus: "予約中",
            orderType: "プラン変更",
            reserveDate: addDays(new Date(), -1), // today + 10 days
        });
    }
    async function clearTestData() {
        await Promise.all([
            await LinesEntity.destroy({
                where: {
                    lineId: testdata.lineId,
                    lineStatus: testdata.lineStatus,
                },
            }),
            await ServiceOrdersEntity.destroy({
                where: {
                    serviceOrderId: testdata.reserveSOID,
                    lineId: testdata.lineId,
                    orderStatus: "予約中",
                    orderType: "プラン変更",
                },
            }),
            await ServiceOrdersEntity.destroy({
                where: {
                    serviceOrderId: responseHeader.apiProcessID,
                },
            }),
            await CoreSwimmyApiLog.deleteMany({
                requestOrderId: responseHeader.apiProcessID,
            }),
        ]);
    }
    const mockTenant = () => {
        jest.spyOn(TenantManage.prototype, "doCheck").mockReturnValue(
            Promise.resolve([true, ""]),
        );
        jest.spyOn(
            TenantManage.prototype,
            "checkTpcConnection",
        ).mockResolvedValue([true, "TpcDestIpAddress", "TpcDestIpAddress"]);
        jest.spyOn(TenantManage.prototype, "doCheck").mockReturnValue(
            Promise.resolve([true, ""]),
        );
    };
    beforeAll(async () => {
        sequelize = await usePsql();
        await useMongo(DefaultContext);
        await AppConfig.loadCoreMvnoConfig(DefaultContext);
        configData = getConfigDataForTest();
        nock.disableNetConnect();
        await clearTestData();
        await TenantsEntity.create(dumpData.tenant);
        await TenantPlansEntity.create(dumpData.tenantPlant);
    });

    afterAll(async () => {
        await TenantPlansEntity.destroy({
            where: {
                tenantId: testdata.tenantId,
            },
        });
        await TenantsEntity.destroy({
            where: {
                tenantId: testdata.tenantId,
            },
        });
        nock.enableNetConnect();
        await sequelize.close();
        await disconnectMongo(DefaultContext);
    });

    beforeEach(async () => {
        DefaultContext.jsonBody = Object.assign({}, request);
        DefaultContext.responseHeader = Object.assign({}, responseHeader);
        jest.spyOn(MvnoUtil, "getDateTimeNow").mockReturnValue(
            responseHeader.receivedDate,
        );
        useFasterRetries();
        instance = new LinePlanAcquisitionServiceTx(
            DefaultRequest,
            DefaultContext,
        );

        await prepareTestData();
        param = Object.assign({}, request);
    });

    afterEach(async () => {
        jest.restoreAllMocks();
        jest.clearAllMocks();
        await Promise.all([
            await ServiceOrdersEntity.destroy({
                where: {
                    serviceOrderId: testdata.reserveSOID,
                },
            }),
        ]);

        nock.abortPendingRequests();
        nock.cleanAll();
        await clearTestData();
    });

    type OrderTypeParam = {
        reserve_flag: boolean | undefined;
        reserve_soId: string | undefined;
        reserve_date: boolean;
    };
    const orderTypeCases: { [x: string]: [string, OrderTypeParam] } = {
        [Constants.ORDER_TYPE_0]: [
            "即時オーダ",
            {
                reserve_flag: undefined,
                reserve_soId: undefined,
                reserve_date: false,
            },
        ],
        [Constants.ORDER_TYPE_1]: [
            "予約前オーダ",
            {
                reserve_flag: false,
                reserve_soId: undefined,
                reserve_date: true,
            },
        ],
        [Constants.ORDER_TYPE_2]: [
            "予約実行オーダ",
            {
                reserve_flag: true,
                reserve_soId: testdata.reserveSOID,
                reserve_date: true,
            },
        ],
        [Constants.ORDER_TYPE_9]: [
            "",
            {
                reserve_flag: true,
                reserve_soId: testdata.reserveSOID,
                reserve_date: undefined,
            },
        ],
    };
    function updateParamForOrderCase(
        param: LinePlanAcquisitionInputDto,
        orderCase: OrderTypeParam,
    ) {
        param.reserve_flag = orderCase.reserve_flag;
        param.reserve_soId = orderCase.reserve_soId;
        param.reserve_date = orderCase.reserve_date
            ? testdata.reserve_date
            : "";
        if (param.reserve_soId) {
            // 予約実行オーダ will use reserve_soId instead of new apiProcessID
            DefaultContext.responseHeader.apiProcessID = param.reserve_soId;
        }
    }

    describe("tenantType RINKモバイル", () => {
        const lineRKM = {
            lineId: '99999999998',
            lineStatus: '1',
            nnumber: 'N250013024',
            simFlag: false,
            planName: null,
            pricePlanId: 'AR711',
            pricePlanName: 'RKMバンドル 1GB/月プラン LTE/5G(NSA)(音声)'
        }

        const requestRKM: LinePlanAcquisitionInputDto = {
            requestHeader: {
                sequenceNo: "1234567",
                senderSystemId: "0001",
                apiKey: "",
                functionType: "06",
            },
            tenantId: 'RKM000',
            targetSoId: generateSOID(),
            reserve_flag: undefined,
            reserve_soId: undefined,
            reserve_date: "",
            lineNo: lineRKM.lineId,
            csvUnnecessaryFlag: "0",
            potalPlanID_pre: '99957',
            potalPlanID: '99958'
        };

        beforeEach(async () => {
            mockTenant();
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, SOAPTestHelper.simpleResponse("OK"))
                .post("")
                .reply(200, SOAPTestHelper.simpleResponse("OK"));
            jest.spyOn(TenantManage.prototype, "doCheck").mockReturnValue(
                    Promise.resolve([true, 'N250013024']),
            );
            await LinesEntity.create(lineRKM)
        });

        afterEach(async () => {
            await Promise.all([
                ServiceOrdersEntity.destroy({
                    where: {
                        serviceOrderId: responseHeader.apiProcessID,
                    }
                }),
                CoreSwimmyApiLog.deleteMany({
                    requestOrderId: responseHeader.apiProcessID,
                }),
                LinesEntity.destroy({
                    where: {
                        lineId: lineRKM.lineId
                    }
                }),
                AutoModbucketLineGroupsEntity.destroy({
                    where: { lineId: lineRKM.lineId }
                })
            ])

        })

        test.each(['0','1','2'])("should return CODE_000000 if ORDER_TYPE = %s", async (orderType) => {
            await checkLine(lineRKM.lineId,'AR711','RKMバンドル 1GB/月プラン LTE/5G(NSA)(音声)', null)
            const param = Object.assign({}, requestRKM);
            DefaultContext.jsonBody = param;
            DefaultContext.responseHeader = Object.assign({}, responseHeader);
            setOrderType(orderType as "0" | "1" | "2" | "9");

            const result = await LinePlanAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkResponse(result, ResultCdConstants.CODE_000000);

            if (orderType === "0") {
                await checkCoreSwimmyApiLog(responseHeader.apiProcessID, true);
                await checkLine(lineRKM.lineId,'AR712','RKMバンドル 2GB/月プラン LTE/5G(NSA)(音声)',true)
            } else if (orderType === "1")  {
                await checkCoreSwimmyApiLog(responseHeader.apiProcessID, true);
                await checkLine(lineRKM.lineId,'AR711','RKMバンドル 1GB/月プラン LTE/5G(NSA)(音声)', null)
            } else if (orderType === "2") {
                await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
                await checkLine(lineRKM.lineId,'AR712','RKMバンドル 2GB/月プラン LTE/5G(NSA)(音声)',true)
            }
        });

        test("should return CODE_000000 , Update 回線Group record if found", async () => {
            await LineLineGroupsEntity.create({
                lineId: lineRKM.lineId,
                groupId: '100000003',
                basicCapacity: 1024
            })
            await checkLine(lineRKM.lineId,'AR711','RKMバンドル 1GB/月プラン LTE/5G(NSA)(音声)', null)
            const param = Object.assign({}, requestRKM);
            DefaultContext.jsonBody = param;
            DefaultContext.responseHeader = Object.assign({}, responseHeader);
            setOrderType('2');

            const result = await LinePlanAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkResponse(result, ResultCdConstants.CODE_000000);

            const groupRecord = await LineLineGroupsEntity.findOne({
                where: {
                    lineId: lineRKM.lineId
                }
            })

            expect(groupRecord.basicCapacity).toBe(1)
            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
            await checkLine(lineRKM.lineId, 'AR712', 'RKMバンドル 2GB/月プラン LTE/5G(NSA)(音声)', true)
            await LineLineGroupsEntity.destroy({
                where: {
                    lineId: lineRKM.lineId
                }
            })
        });

    });

    describe("OK CASES (CODE_000000) 回線グループIDが存在しない場合 & テナントがC-OCNの場合 > 回線情報を更新しない ", () => {
        beforeEach(async () => {
            mockTenant();
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, SOAPTestHelper.simpleResponse("OK"))
                .post("")
                .reply(200, SOAPTestHelper.simpleResponse("OK"));
            jest.spyOn(TenantManage.prototype, "doCheck").mockReturnValue(
                Promise.resolve([true, "test"]),
            );
            jest.spyOn(APICommonDAO.prototype, "getPlans").mockReturnValue(
                Promise.resolve([
                    PlansEntity.build({
                        planId: 10001,
                        servicePlanId: "AR708",
                        planIdT: testdata.planIdT,
                    }),
                ]),
            );
            jest.spyOn(
                APILinesDAO.prototype,
                "getResalePlanIdPlan",
            ).mockResolvedValue(["AR708"]);
        });
        test("should return CODE_000000 if [予約前オーダ]", async () => {
            await checkLine(testdata.lineId, null, null, null);
            await removeServiceOrder();
            DefaultContext.jsonBody = param;
            setOrderType("1")
            const result = await LinePlanAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkResponse(result, ResultCdConstants.CODE_000000);
            // no update in db as order type 1
            await checkLine(testdata.lineId, null, null, null);
            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, true);
        });
        test("should return CODE_000000 if [即時オーダ]", async () => {
            await checkLine(testdata.lineId, null, null, null);
            await removeServiceOrder();

            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_0][1],
            );

            DefaultContext.jsonBody = param;
            const result = await LinePlanAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkResponse(result, ResultCdConstants.CODE_000000);
            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, true);
        });
        test("should return CODE_000000 if [予約実行オーダ]", async () => {
            await checkLine(testdata.lineId, null, null, null);
            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_2][1],
            );

            param.reserve_date = format(
                addDays(getDateNowForReservationTest(configData), 1),
                "yyyy/MM/dd HH:mm",
            );

            jest.spyOn(ApiCommon.prototype, "checkAbolishSo").mockReturnValue(
                Promise.resolve(true),
            );
            DefaultContext.jsonBody = param;
            DefaultContext.responseHeader = Object.assign({}, responseHeader);
            const result = await LinePlanAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkResponse(result, ResultCdConstants.CODE_000000);
            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
        });

        test("should return CODE_000000 even if second TPC cannot be reached (SOAPException) ", async () => {
            await checkLine(testdata.lineId, null, null, null);
            await removeServiceOrder();
            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_0][1],
            );
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, SOAPTestHelper.simpleResponse("OK"));

            DefaultContext.jsonBody = param;
            const result = await LinePlanAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            checkResponse(result, ResultCdConstants.CODE_000000);
            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, true);
        });

        test("should return CODE_000000 even if second TPC SOAP call fails", async () => {
            await checkLine(testdata.lineId, null, null, null);
            await removeServiceOrder();
            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_0][1],
            );
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, SOAPTestHelper.simpleResponse("OK"))
                .post("")
                .reply(200, SOAPTestHelper.simpleResponse("NG"));

            DefaultContext.jsonBody = param;
            const result = await LinePlanAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );

            checkResponse(result, ResultCdConstants.CODE_000000);
            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, true);
        });
    });

    describe("OK CASES (CODE_000000) 回線グループIDが存在しない場合 & C-OCN以外の場合 > 回線情報を更新 ", () => {
        beforeAll(async () => {
            await TenantsEntity.update(
                {
                    tenantType: 2,
                },
                {
                    where: {
                        tenantId: testdata.tenantId,
                    },
                },
            );
        });
        beforeEach(async () => {
            mockTenant();
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, SOAPTestHelper.simpleResponse("OK"))
                .post("")
                .reply(200, SOAPTestHelper.simpleResponse("OK"));
            jest.spyOn(TenantManage.prototype, "doCheck").mockReturnValue(
                Promise.resolve([true, ""]),
            );
            jest.spyOn(APILinesDAO.prototype, "getPlanInfo").mockReturnValue(
                Promise.resolve([
                    PlansEntity.build({
                        planId: 10001,
                        servicePlanId: "AR708",
                        planIdT: testdata.planIdT,
                    }),
                ]),
            );
            jest.spyOn(APICommonDAO.prototype, "getPlans").mockReturnValue(
                Promise.resolve([
                    PlansEntity.build({
                        planId: 10001,
                        servicePlanId: "AR708",
                        planName: testdata.planName,
                        basicCapacity: 1000,
                        pricePlanId: "AR708",
                        planIdT: testdata.planIdT,
                    }),
                ]),
            );
        });

        test("should return CODE_000000 if [予約前オーダ]", async () => {
            await checkLine(testdata.lineId, null, null, null);
            await removeServiceOrder();
            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_1][1],
            );

            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_000000);
            // no update in db as order type 1
            await checkLine(testdata.lineId, "AR708", testdata.planName, true);
        });
        test("should return CODE_000000 if [即時オーダ]", async () => {
            await checkLine(testdata.lineId, null, null, null);
            await removeServiceOrder();

            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_0][1],
            );

            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_000000);
            await checkLine(testdata.lineId, "AR708", testdata.planName, true);
        });
        test("should return CODE_000000 if [予約実行オーダ]", async () => {
            await checkLine(testdata.lineId, null, null, null);
            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_2][1],
            );

            param.reserve_date = format(
                addDays(getDateNowForReservationTest(configData), 1),
                "yyyy/MM/dd HH:mm",
            );

            jest.spyOn(ApiCommon.prototype, "checkAbolishSo").mockReturnValue(
                Promise.resolve(true),
            );
            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_000000);
            await checkLine(testdata.lineId, "AR708", testdata.planName, true);
        });

        test("should return CODE_000000 even if second TPC cannot be reached (SOAPException) ", async () => {
            await checkLine(testdata.lineId, null, null, null);
            await removeServiceOrder();
            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_0][1],
            );
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, SOAPTestHelper.simpleResponse("OK"));

            const result = await instance.service(param, false, ...nulls);

            checkResponse(result, ResultCdConstants.CODE_000000);
            await checkLine(testdata.lineId, "AR708", testdata.planName, true);
        });

        test("should return CODE_000000 even if second TPC SOAP call fails", async () => {
            await checkLine(testdata.lineId, null, null, null);
            await removeServiceOrder();
            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_0][1],
            );
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, SOAPTestHelper.simpleResponse("OK"))
                .post("")
                .reply(200, SOAPTestHelper.simpleResponse("NG"));

            const result = await instance.service(param, false, ...nulls);

            checkResponse(result, ResultCdConstants.CODE_000000);
            await checkLine(testdata.lineId, "AR708", testdata.planName, true);
        });
    });
    describe("OK CASES 回線グループIDが存在しない場合 & C-OCN以外の場合 SOAP EXCEPTION", () => {
        beforeAll(async () => {
            await TenantsEntity.update(
                {
                    tenantType: 2,
                },
                {
                    where: {
                        tenantId: testdata.tenantId,
                    },
                },
            );
        });
        beforeEach(async () => {
            mockTenant();
            jest.spyOn(TenantManage.prototype, "doCheck").mockReturnValue(
                Promise.resolve([true, ""]),
            );
            jest.spyOn(APILinesDAO.prototype, "getPlanInfo").mockReturnValue(
                Promise.resolve([
                    PlansEntity.build({
                        planId: 10001,
                        servicePlanId: "AR708",
                        planIdT: testdata.planIdT,
                    }),
                ]),
            );
            jest.spyOn(APICommonDAO.prototype, "getPlans").mockReturnValue(
                Promise.resolve([
                    PlansEntity.build({
                        planId: 10001,
                        servicePlanId: "AR708",
                        planName: testdata.planName,
                        basicCapacity: 1000,
                        pricePlanId: "AR708",
                        planIdT: testdata.planIdT,
                    }),
                ]),
            );
        });

        test("should return CODE_061101 if SOAP Exception occurs", async () => {
            await checkLine(testdata.lineId, null, null, null);
            await removeServiceOrder();
            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_1][1],
            );

            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_061101);
        });

        test("should return CODE_000000 even if second TPC cannot be reached (SOAPException) ", async () => {
            await checkLine(testdata.lineId, null, null, null);
            await removeServiceOrder();
            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_0][1],
            );
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, SOAPTestHelper.simpleResponse("OK"));

            const result = await instance.service(param, false, ...nulls);

            checkResponse(result, ResultCdConstants.CODE_000000);
            await checkLine(testdata.lineId, "AR708", testdata.planName, true);
        });

        test("should return CODE_000000 even if second TPC SOAP call fails", async () => {
            await checkLine(testdata.lineId, null, null, null);
            await removeServiceOrder();
            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_0][1],
            );
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, SOAPTestHelper.simpleResponse("OK"))
                .post("")
                .reply(200, SOAPTestHelper.simpleResponse("NG"));

            const result = await instance.service(param, false, ...nulls);

            checkResponse(result, ResultCdConstants.CODE_000000);
            await checkLine(testdata.lineId, "AR708", testdata.planName, true);
        });
    });

    describe("OK CASES (CODE_000000) 回線グループIDが存在する場合 & テナントがC-OCN以外の場合 > 回線情報を更新 & 回線グループIDを更新", () => {
        beforeAll(async () => {
            await TenantsEntity.update(
                {
                    tenantType: 2,
                },
                {
                    where: {
                        tenantId: testdata.tenantId,
                    },
                },
            );
        });
        beforeEach(async () => {
            mockTenant();
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, SOAPTestHelper.simpleResponse("OK"))
                .post("")
                .reply(200, SOAPTestHelper.simpleResponse("OK"));
            jest.spyOn(TenantManage.prototype, "doCheck").mockReturnValue(
                Promise.resolve([true, ""]),
            );
            jest.spyOn(APILinesDAO.prototype, "getPlanInfo").mockReturnValue(
                Promise.resolve([
                    PlansEntity.build({
                        planId: 10001,
                        servicePlanId: "AR708",
                        planIdT: testdata.planIdT,
                    }),
                ]),
            );
            jest.spyOn(APICommonDAO.prototype, "getPlans").mockReturnValue(
                Promise.resolve([
                    PlansEntity.build({
                        planId: 10001,
                        servicePlanId: "AR708",
                        planName: testdata.planName,
                        basicCapacity: 1000,
                        pricePlanId: "AR708",
                        planIdT: testdata.planIdT,
                    }),
                ]),
            );
            await LineGroupsEntity.create({
                groupId: testdata.potalPlanID_pre,
                status: 0,
                tenantId: testdata.tenantId,
                planId: parseInt(testdata.potalPlanID_pre, 10),
            });

            await LineLineGroupsEntity.create({
                lineId: testdata.lineId,
                groupId: testdata.potalPlanID_pre,
                basicCapacity: 0,
                usageStatus: "0",
            });
        });
        afterEach(async () => {
            await LineLineGroupsEntity.destroy({
                where: {
                    lineId: testdata.lineId,
                },
            });

            await LineGroupsEntity.destroy({
                where: {
                    groupId: testdata.potalPlanID_pre,
                },
            });
        });

        test("should return CODE_000000 if [予約前オーダ]", async () => {
            await checkLine(testdata.lineId, null, null, null);
            await checkLineLineGroup(testdata.lineId, 0, true);
            await removeServiceOrder();
            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_1][1],
            );
            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_000000);
            await checkLine(testdata.lineId, "AR708", testdata.planName, true);
            await checkLineLineGroup(testdata.lineId, 1000, true);
        });
        test("should return CODE_000000 if [即時オーダ]", async () => {
            await checkLine(testdata.lineId, null, null, null);
            await checkLineLineGroup(testdata.lineId, 0, true);
            await removeServiceOrder();
            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_0][1],
            );

            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_000000);
            await checkLine(testdata.lineId, "AR708", testdata.planName, true);
            await checkLineLineGroup(testdata.lineId, 1000, true);
        });
        test("should return CODE_000000 if [予約実行オーダ]", async () => {
            await checkLine(testdata.lineId, null, null, null);
            await checkLineLineGroup(testdata.lineId, 0, true);
            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_2][1],
            );

            param.reserve_date = format(
                addDays(getDateNowForReservationTest(configData), 1),
                "yyyy/MM/dd HH:mm",
            );

            jest.spyOn(ApiCommon.prototype, "checkAbolishSo").mockReturnValue(
                Promise.resolve(true),
            );
            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_000000);
            await checkLine(testdata.lineId, "AR708", testdata.planName, true);
            await checkLineLineGroup(testdata.lineId, 1000, true);
        });

        test("should return CODE_000000 even if second TPC cannot be reached (SOAPException) ", async () => {
            await checkLine(testdata.lineId, null, null, null);
            await checkLineLineGroup(testdata.lineId, 0, true);
            await removeServiceOrder();
            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_0][1],
            );
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, SOAPTestHelper.simpleResponse("OK"));

            const result = await instance.service(param, false, ...nulls);

            checkResponse(result, ResultCdConstants.CODE_000000);
            await checkLine(testdata.lineId, "AR708", testdata.planName, true);
            await checkLineLineGroup(testdata.lineId, 1000, true);
        });

        test("should return CODE_000000 even if second TPC SOAP call fails", async () => {
            await checkLine(testdata.lineId, null, null, null);
            await checkLineLineGroup(testdata.lineId, 0, true);
            await removeServiceOrder();
            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_0][1],
            );
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, SOAPTestHelper.simpleResponse("OK"))
                .post("")
                .reply(200, SOAPTestHelper.simpleResponse("NG"));

            const result = await instance.service(param, false, ...nulls);

            checkResponse(result, ResultCdConstants.CODE_000000);
            await checkLine(testdata.lineId, "AR708", testdata.planName, true);
            await checkLineLineGroup(testdata.lineId, 1000, true);
        });
    });

    describe("OK CASES CODE_000000 回線グループIDが存在する場合 SAOP TEST", () => {
        beforeAll(async () => {
            await TenantsEntity.update(
                {
                    tenantType: 2,
                },
                {
                    where: {
                        tenantId: testdata.tenantId,
                    },
                },
            );
        });
        afterAll(async () => {
            await TenantsEntity.update(
                {
                    tenantType: 1,
                },
                {
                    where: {
                        tenantId: testdata.tenantId,
                    },
                },
            );
        });
        beforeEach(async () => {
            mockTenant();
            jest.spyOn(TenantManage.prototype, "doCheck").mockReturnValue(
                Promise.resolve([true, ""]),
            );
            jest.spyOn(APILinesDAO.prototype, "getPlanInfo").mockReturnValue(
                Promise.resolve([
                    PlansEntity.build({
                        planId: 10001,
                        servicePlanId: "AR708",
                        planIdT: testdata.planIdT,
                    }),
                ]),
            );
            jest.spyOn(APICommonDAO.prototype, "getPlans").mockReturnValue(
                Promise.resolve([
                    PlansEntity.build({
                        planId: 10001,
                        servicePlanId: "AR708",
                        planName: testdata.planName,
                        basicCapacity: 1000,
                        pricePlanId: "AR708",
                        tpcServicePattern: 0,
                        planIdT: testdata.planIdT,
                    }),
                ]),
            );
            await LineGroupsEntity.create({
                groupId: testdata.potalPlanID_pre,
                status: 0,
                tenantId: testdata.tenantId,
                planId: parseInt(testdata.potalPlanID_pre, 10),
            });

            await LineLineGroupsEntity.create({
                lineId: testdata.lineId,
                groupId: testdata.potalPlanID_pre,
                basicCapacity: 0,
                usageStatus: "0",
            });
        });
        afterEach(async () => {
            await LineLineGroupsEntity.destroy({
                where: {
                    lineId: testdata.lineId,
                },
            });

            await LineGroupsEntity.destroy({
                where: {
                    groupId: testdata.potalPlanID_pre,
                },
            });
        });

        test("should return CODE_000000 ", async () => {
            await checkLine(testdata.lineId, null, null, null);
            await checkLineLineGroup(testdata.lineId, 0, true);
            await removeServiceOrder();
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, SOAPTestHelper.simpleResponse("OK"))
                .persist();
            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_1][1],
            );
            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_000000);
            await checkLine(testdata.lineId, "AR708", testdata.planName, true);
            await checkLineLineGroup(testdata.lineId, 1000, true);
        });

        test("should return CODE_000000 even if second TPC fails SOAPException sendSoapApiToTpcAplysrv", async () => {
            await checkLine(testdata.lineId, null, null, null);
            await checkLineLineGroup(testdata.lineId, 0, true);
            await removeServiceOrder();
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, SOAPTestHelper.simpleResponse("OK"))
                .post("")
                .reply(200, SOAPTestHelper.simpleResponse("OK"))
                .post("")
                .reply(200, SOAPTestHelper.simpleResponse("OK"));
            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_1][1],
            );
            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_000000);
            await checkLine(testdata.lineId, "AR708", testdata.planName, true);
            await checkLineLineGroup(testdata.lineId, 1000, true);
        });

        test("should return CODE_000000 even if second TPC SOAP response is NG", async () => {
            await checkLine(testdata.lineId, null, null, null);
            await checkLineLineGroup(testdata.lineId, 0, true);
            await removeServiceOrder();
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, SOAPTestHelper.simpleResponse("OK"))
                .post("")
                .reply(200, SOAPTestHelper.simpleResponse("OK"))
                .post("")
                .reply(200, SOAPTestHelper.simpleResponse("OK"))
                .post("")
                .reply(200, SOAPTestHelper.simpleResponse("NG"));
            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_1][1],
            );
            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_000000);
            await checkLine(testdata.lineId, "AR708", testdata.planName, true);
            await checkLineLineGroup(testdata.lineId, 1000, true);
        });

        test("should return CODE_000000  even if  TPC SOAP call returns CODE_000951", async () => {
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, SOAPTestHelper.simpleResponse("OK"))
                .persist();

            const output = new SOAPCommonOutputDto();
            output.setProcessCode(ResultCdConstants.CODE_000000);
            output.setDoc(readXML(SOAPTestHelper.simpleResponse("OK")));
            const outputNG = new SOAPCommonOutputDto();
            outputNG.setProcessCode(ResultCdConstants.CODE_000951);
            jest.spyOn(SOAPCommon.prototype, "callWebSoapApi")
                .mockResolvedValueOnce(output)
                .mockResolvedValueOnce(output)
                .mockResolvedValueOnce(output)
                .mockResolvedValueOnce(outputNG);
            await checkLine(testdata.lineId, null, null, null);
            await checkLineLineGroup(testdata.lineId, 0, true);
            await removeServiceOrder();
            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_1][1],
            );
            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_000000);
            await checkLine(testdata.lineId, "AR708", testdata.planName, true);
            await checkLineLineGroup(testdata.lineId, 1000, true);
        });
    });

    describe("NG CASES 回線回線グループのレコードを更新", () => {
        beforeAll(async () => {
            await TenantsEntity.update(
                {
                    tenantType: 2,
                },
                {
                    where: {
                        tenantId: testdata.tenantId,
                    },
                },
            );
        });
        beforeEach(async () => {
            mockTenant();
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, SOAPTestHelper.simpleResponse("OK"))
                .persist();
            jest.spyOn(TenantManage.prototype, "doCheck").mockReturnValue(
                Promise.resolve([true, ""]),
            );
            jest.spyOn(APILinesDAO.prototype, "getPlanInfo").mockReturnValue(
                Promise.resolve([
                    PlansEntity.build({
                        planId: 10001,
                        servicePlanId: "AR708",
                        planIdT: testdata.planIdT,
                    }),
                ]),
            );
            jest.spyOn(APICommonDAO.prototype, "getPlans").mockReturnValue(
                Promise.resolve([
                    PlansEntity.build({
                        planId: 10001,
                        servicePlanId: "AR708",
                        planName: testdata.planName,
                        basicCapacity: 1000,
                        pricePlanId: "AR708",
                        planIdT: testdata.planIdT,
                    }),
                ]),
            );
            await LineGroupsEntity.create({
                groupId: testdata.potalPlanID_pre,
                status: 0,
                tenantId: testdata.tenantId,
                planId: parseInt(testdata.potalPlanID_pre, 10),
            });

            await LineLineGroupsEntity.create({
                lineId: testdata.lineId,
                groupId: testdata.potalPlanID_pre,
                basicCapacity: 0,
                usageStatus: "0",
            });
        });
        afterEach(async () => {
            await LineLineGroupsEntity.destroy({
                where: {
                    lineId: testdata.lineId,
                },
            });

            await LineGroupsEntity.destroy({
                where: {
                    groupId: testdata.potalPlanID_pre,
                },
            });
        });

        test("should return CODE_061201 if updateLineInfoPlanChange fails to query", async () => {
            jest.spyOn(
                APILinesDAO.prototype,
                "updateLineInfoPlanChange",
            ).mockImplementation(async () => {
                throw new ConnectionTimedOutError(new Error("test error"));
            });

            await checkLine(testdata.lineId, null, null, null);
            await checkLineLineGroup(testdata.lineId, 0, true);
            await removeServiceOrder();
            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_0][1],
            );

            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_061201);
            await checkLine(testdata.lineId, null, null, null);
            await checkLineLineGroup(testdata.lineId, 0, true);
        });

        test("should return CODE_061201 if updateLineInfoPlanChange couldnt modify data", async () => {
            jest.spyOn(
                APILinesDAO.prototype,
                "updateLineInfoPlanChange",
            ).mockReturnValue(Promise.resolve(0));
            await checkLine(testdata.lineId, null, null, null);
            await checkLineLineGroup(testdata.lineId, 0, true);
            await removeServiceOrder();
            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_0][1],
            );

            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_061201);
            await checkLine(testdata.lineId, null, null, null);
            await checkLineLineGroup(testdata.lineId, 0, true);
        });
        test("should return CODE_061701 if updateLineLineGroupsCapacity couldnt modify data", async () => {
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "updateLineLineGroupsCapacity",
            ).mockReturnValueOnce(Promise.resolve(0));

            await checkLine(testdata.lineId, null, null, null);
            await checkLineLineGroup(testdata.lineId, 0, true);
            await removeServiceOrder();
            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_0][1],
            );

            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_061701);
            await checkLine(testdata.lineId, null, null, null);
            await checkLineLineGroup(testdata.lineId, 0, true);
        });

        test("should return CODE_061701 if updateLineLineGroupsCapacity fails to query", async () => {
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "updateLineLineGroupsCapacity",
            ).mockImplementation(async () => {
                throw new ConnectionTimedOutError(new Error("test error"));
            });
            await checkLine(testdata.lineId, null, null, null);
            await checkLineLineGroup(testdata.lineId, 0, true);
            await removeServiceOrder();
            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_0][1],
            );

            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_061701);
            await checkLine(testdata.lineId, null, null, null);
            await checkLineLineGroup(testdata.lineId, 0, true);
        });
    });

    describe("NG CASE for 999999", () => {
        test("should return CODE_999999 if getGroupId throws error other than db", async () => {
            await removeServiceOrder();

            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_0][1],
            );
            jest.spyOn(APILinesDAO.prototype, "getGroupId").mockRejectedValue(
                new Error("test error"),
            );
            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_999999);
        });

        test("should return CODE_999999 if when [予約実行オーダ] (予約実行オーダ) and client address invalid", async () => {
            try {
                DefaultContext.isInternalRequest = false;
                param.reserve_date = "some date";
                param.reserve_flag = true;
                param.reserve_soId = "1234567";
                const result = await instance.service(param, false);
                checkResponse(result, ResultCdConstants.CODE_999999);
            } finally {
                DefaultContext.isInternalRequest = true;
            }
        });

        test("should return CODE_999999 if getTenants throws error other than db", async () => {
            await removeServiceOrder();

            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_1][1],
            );

            jest.spyOn(APICommonDAO.prototype, "getTenants").mockRejectedValue(
                new Error("test error"),
            );
            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_999999);
        });
    });

    describe("NG CASES C-OCNの場合", () => {
        beforeAll(async () => {
            await TenantsEntity.update(
                {
                    tenantType: 1,
                },
                {
                    where: {
                        tenantId: testdata.tenantId,
                    },
                },
            );
        });
        beforeEach(async () => {
            mockTenant();
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, SOAPTestHelper.simpleResponse("OK"))
                .post("")
                .reply(200, SOAPTestHelper.simpleResponse("OK"));
            jest.spyOn(TenantManage.prototype, "doCheck").mockReturnValue(
                Promise.resolve([true, ""]),
            );
            jest.spyOn(APICommonDAO.prototype, "getPlans").mockReturnValue(
                Promise.resolve([
                    PlansEntity.build({
                        planId: 10001,
                        servicePlanId: "AR708",
                        planIdT: testdata.planIdT,
                    }),
                ]),
            );
        });

        test("should return CODE_060133 if checkAbolishSo returns false", async () => {
            jest.spyOn(ApiCommon.prototype, "checkAbolishSo").mockReturnValue(
                Promise.resolve(false),
            );
            await checkLine(testdata.lineId, null, null, null);
            await removeServiceOrder();
            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_1][1],
            );
            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_060133);
            // no update in db as order type 1
            await checkLine(testdata.lineId, null, null, null);
        });

        test("should return CODE_060133 if checkAbolishSo fails to query", async () => {
            jest.spyOn(
                ApiCommon.prototype,
                "checkAbolishSo",
            ).mockImplementation(async () => {
                throw new ConnectionTimedOutError(new Error("time out error"));
            });
            await checkLine(testdata.lineId, null, null, null);
            await removeServiceOrder();
            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_1][1],
            );

            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_060133);
            // no update in db as order type 1
            await checkLine(testdata.lineId, null, null, null);
        });

        test("should return CODE_060134 if line simflag is true and tenatType is not 2,3,4 ", async () => {
            await LinesEntity.update(
                {
                    simFlag: true,
                },
                {
                    where: {
                        lineId: testdata.lineId,
                    },
                },
            );
            await checkLine(testdata.lineId, null, null, null);
            await removeServiceOrder();
            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_1][1],
            );

            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_060134);
            // no update in db as order type 1
            await checkLine(testdata.lineId, null, null, null);

            await LinesEntity.update(
                {
                    simFlag: false,
                },
                {
                    where: {
                        lineId: testdata.lineId,
                    },
                },
            );
        });

        test("should return CODE_060134 if line simflag is true and tenatType is not 2,3,4  & getLineInfo fails to query", async () => {
            await LinesEntity.update(
                {
                    simFlag: true,
                },
                {
                    where: {
                        lineId: testdata.lineId,
                    },
                },
            );
            await checkLine(testdata.lineId, null, null, null);
            await removeServiceOrder();
            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_1][1],
            );
            jest.spyOn(APILinesDAO.prototype,'getLineInfo').mockImplementation(async () => {
                throw new ConnectionTimedOutError(new Error("time out error"));
            });
            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_060134);
            // no update in db as order type 1
            await checkLine(testdata.lineId, null, null, null);

            await LinesEntity.update(
                {
                    simFlag: false,
                },
                {
                    where: {
                        lineId: testdata.lineId,
                    },
                },
            );
        });

        test("should return CODE_060135 if 変更後プランの対応NWが「5G(NSA)」&& 回線の契約種別が「5G(NSA)」もしくは「5G(NSA)(音声)」以外の場合", async () => {
            jest.spyOn(
                APICommonDAO.prototype,
                "getPlansTypeList",
            ).mockReturnValue(
                Promise.resolve(["true", "true", "5G(NSA)", "true"]),
            );
            await checkLine(testdata.lineId, null, null, null);
            await removeServiceOrder();
            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_1][1],
            );

            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_060135);
            // no update in db as order type 1
            await checkLine(testdata.lineId, null, null, null);
        });
    });

    describe("NG CASES C-OCNの以外の場合", () => {
        beforeAll(async () => {
            await TenantsEntity.update(
                {
                    tenantType: 2,
                },
                {
                    where: {
                        tenantId: testdata.tenantId,
                    },
                },
            );
        });
        beforeEach(async () => {
            mockTenant();
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, SOAPTestHelper.simpleResponse("OK"))
                .post("")
                .reply(200, SOAPTestHelper.simpleResponse("OK"));
            jest.spyOn(TenantManage.prototype, "doCheck").mockReturnValue(
                Promise.resolve([true, ""]),
            );
            jest.spyOn(APICommonDAO.prototype, "getPlans").mockReturnValue(
                Promise.resolve([
                    PlansEntity.build({
                        planId: 10001,
                        servicePlanId: "AR708",
                        planIdT: testdata.planIdT,
                    }),
                ]),
            );
        });

        test("should return CODE_060701 if 変更前プランと変更後プランのフルMVNOフラグが一致しない場合, プラン変更パターン定型フラグが3（社内システムタイプ）", async () => {
            await removeServiceOrder();

            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_0][1],
            );
            jest.spyOn(TenantManage.prototype, "doCheck").mockReturnValue(
                Promise.resolve([true, ""]),
            );
            jest.spyOn(
                APICommonDAO.prototype,
                "getPlansTypeList",
            ).mockReturnValueOnce(
                Promise.resolve(["null", "null", "LTE", "test"]),
            );
            jest.spyOn(APICommonDAO.prototype, 'getPlanChangeFlag').mockResolvedValue([1])
            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_060701);
        });

        test("should return CODE_060701 if 変更前プランと変更後プランの音声フラグ、SMS対応が一致しない場合, プラン変更パターン定型フラグが2（ID卸タイプ））", async () => {
            await removeServiceOrder();

            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_0][1],
            );
            jest.spyOn(TenantManage.prototype, "doCheck").mockReturnValue(
                Promise.resolve([true, ""]),
            );
            jest.spyOn(
                APICommonDAO.prototype,
                "getPlansTypeList",
            ).mockReturnValueOnce(
                Promise.resolve(["true", "true", "LTE", "true"]),
            );
            jest.spyOn(
                APICommonDAO.prototype,
                "getPlanChangeClass",
            ).mockReturnValue(Promise.resolve([0]));
            jest.spyOn(
                APICommonDAO.prototype,
                "getPlanChangeFlag",
            ).mockReturnValueOnce(Promise.resolve([2]));
            const nextMonthDate = new Date();
            nextMonthDate.setMonth(nextMonthDate.getMonth() + 1);
            nextMonthDate.setDate(1);
            nextMonthDate.setHours(19, 0, 0, 0);

            jest.spyOn(MvnoUtil, "getDateTimeNow").mockReturnValue(
                formatDate(nextMonthDate, "yyyy/MM/dd HH:mm")
            );
            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_060701);
        });

        test("should return CODE_060701 if 変更後プランのプラン変更種別が通常プラン(0 or null)ではない場合, プラン変更パターン定型フラグが2（ID卸タイプ））", async () => {
            await removeServiceOrder();

            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_0][1],
            );
            jest.spyOn(TenantManage.prototype, "doCheck").mockReturnValue(
                Promise.resolve([true, ""]),
            );
            jest.spyOn(
                APICommonDAO.prototype,
                "getPlansTypeList",
            ).mockReturnValueOnce(
                Promise.resolve(["true", "true", "LTE", "true"]),
            );
            jest.spyOn(
                APICommonDAO.prototype,
                "getPlanChangeFlag",
            ).mockReturnValueOnce(Promise.resolve([2]));
            const nextMonthDate = new Date();
            nextMonthDate.setMonth(nextMonthDate.getMonth() + 1);
            nextMonthDate.setDate(1);
            nextMonthDate.setHours(19, 0, 0, 0);

            jest.spyOn(MvnoUtil, "getDateTimeNow").mockReturnValue(
                formatDate(nextMonthDate, "yyyy/MM/dd HH:mm")
            );
            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_060701);
        });

        test("should return CODE_060701 if getTenantPlansPlanId fails to query, i gues number3", async () => {
            await removeServiceOrder();

            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_0][1],
            );
            jest.spyOn(TenantManage.prototype, "doCheck").mockReturnValue(
                Promise.resolve([true, ""]),
            );
            jest.spyOn(
                APICommonDAO.prototype,
                "getTenantPlansPlanId",
            ).mockImplementation(async () => {
                throw new ConnectionTimedOutError(new Error("time out error"));
            });
            jest.spyOn(APICommonDAO.prototype, 'getPlanChangeFlag').mockResolvedValue([1])
            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_060701);
        });

        test("should return CODE_060701 if 変更前プランと変更後プランの音声フラグ、SMS対応が一致しない場合, プラン変更パターン定型フラグが2（ID卸タイプ）の場合", async () => {
            await removeServiceOrder();

            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_0][1],
            );
            jest.spyOn(TenantManage.prototype, "doCheck").mockReturnValue(
                Promise.resolve([true, ""]),
            );
            jest.spyOn(
                APICommonDAO.prototype,
                "getPlanChangeFlag",
            ).mockReturnValueOnce(Promise.resolve([2]));
            jest.spyOn(APICommonDAO.prototype, "getPlansTypeList")
                .mockReturnValueOnce(Promise.resolve(["false", "", "", ""]))
                .mockReturnValueOnce(Promise.resolve(["true", "", "", ""]));
            const nextMonthDate = new Date();
            nextMonthDate.setMonth(nextMonthDate.getMonth() + 1);
            nextMonthDate.setDate(1);
            nextMonthDate.setHours(19, 0, 0, 0);

            jest.spyOn(MvnoUtil, "getDateTimeNow").mockReturnValue(
                formatDate(nextMonthDate, "yyyy/MM/dd HH:mm")
            );
            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_060701);
        });

        test("should return CODE_060701 if 変更前プランと変更後プランの対応NWが一致しない場合, プラン変更パターン定型フラグが2（ID卸タイプ）の場合", async () => {
            await removeServiceOrder();

            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_0][1],
            );
            jest.spyOn(TenantManage.prototype, "doCheck").mockReturnValue(
                Promise.resolve([true, ""]),
            );
            jest.spyOn(
                APICommonDAO.prototype,
                "getPlanChangeFlag",
            ).mockReturnValueOnce(Promise.resolve([2]));
            jest.spyOn(APICommonDAO.prototype, "getPlansTypeList")
                .mockReturnValueOnce(
                    Promise.resolve(["true", "true", "5G(aSA)", "null"]),
                )
                .mockReturnValueOnce(
                    Promise.resolve(["true", "true", "test", "null"]),
                );
            const nextMonthDate = new Date();
            nextMonthDate.setMonth(nextMonthDate.getMonth() + 1);
            nextMonthDate.setDate(1);
            nextMonthDate.setHours(19, 0, 0, 0);

            jest.spyOn(MvnoUtil, "getDateTimeNow").mockReturnValue(
                formatDate(nextMonthDate, "yyyy/MM/dd HH:mm")
            );
            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_060701);
        });

        test("should return CODE_060701 if テナントプランテーブルから変更後プランの「プランID」を取得 returns null, プラン変更パターン定型フラグが2（ID卸タイプ）の場合", async () => {
            await removeServiceOrder();

            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_0][1],
            );
            jest.spyOn(TenantManage.prototype, "doCheck").mockReturnValue(
                Promise.resolve([true, ""]),
            );
            jest.spyOn(
                APICommonDAO.prototype,
                "getPlanChangeFlag",
            ).mockReturnValue(Promise.resolve([]));
            jest.spyOn(
                APICommonDAO.prototype,
                "getPlanChangeClass",
            ).mockReturnValue(Promise.resolve([0]));
            jest.spyOn(
                APICommonDAO.prototype,
                "getPlanChangeFlag",
            ).mockReturnValueOnce(Promise.resolve([2]));
            jest.spyOn(APICommonDAO.prototype, "getPlansTypeList")
                .mockReturnValueOnce(
                    Promise.resolve(["true", "true", "LTE", "0"]),
                )
                .mockReturnValueOnce(
                    Promise.resolve(["true", "true", "LTE", "0"]),
                );
            jest.spyOn(
                APICommonDAO.prototype,
                "getTenantPlansPlanId",
            ).mockReturnValue(Promise.resolve([]));
            const nextMonthDate = new Date();
            nextMonthDate.setMonth(nextMonthDate.getMonth() + 1);
            nextMonthDate.setDate(1);
            nextMonthDate.setHours(19, 0, 0, 0);

            jest.spyOn(MvnoUtil, "getDateTimeNow").mockReturnValue(
                formatDate(nextMonthDate, "yyyy/MM/dd HH:mm")
            );
            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_060701);
        });

        test("should return CODE_060701 if getTenantPlansPlanId fails to query, プラン変更パターン定型フラグが2（ID卸タイプ）の場合", async () => {
            await removeServiceOrder();

            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_0][1],
            );
            jest.spyOn(TenantManage.prototype, "doCheck").mockReturnValue(
                Promise.resolve([true, ""]),
            );
            jest.spyOn(
                APICommonDAO.prototype,
                "getPlanChangeFlag",
            ).mockReturnValue(Promise.resolve([]));
            jest.spyOn(
                APICommonDAO.prototype,
                "getPlanChangeClass",
            ).mockReturnValue(Promise.resolve([0]));
            jest.spyOn(
                APICommonDAO.prototype,
                "getPlanChangeFlag",
            ).mockReturnValueOnce(Promise.resolve([2]));
            jest.spyOn(APICommonDAO.prototype, "getPlansTypeList")
                .mockReturnValueOnce(
                    Promise.resolve(["true", "true", "LTE", "0"]),
                )
                .mockReturnValueOnce(
                    Promise.resolve(["true", "true", "LTE", "0"]),
                );
            jest.spyOn(
                APICommonDAO.prototype,
                "getTenantPlansPlanId",
            ).mockImplementation(async () => {
                throw new ConnectionTimedOutError(new Error("time out error"));
            });

            const nextMonthDate = new Date();
            nextMonthDate.setMonth(nextMonthDate.getMonth() + 1);
            nextMonthDate.setDate(1);
            nextMonthDate.setHours(19, 0, 0, 0);
            jest.spyOn(MvnoUtil, "getDateTimeNow").mockReturnValue(
                formatDate(nextMonthDate, "yyyy/MM/dd HH:mm")
            );
            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_060701);
        });

        test("should return CODE_060501 if planList.planId doesnt match to potalPlanId_pre", async () => {
            await removeServiceOrder();

            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_0][1],
            );
            jest.spyOn(TenantManage.prototype, "doCheck").mockReturnValue(
                Promise.resolve([true, ""]),
            );
            jest.spyOn(APILinesDAO.prototype, "getPlanInfo").mockReturnValue(
                Promise.resolve([
                    PlansEntity.build({
                        planId: 10501,
                        planIdT: testdata.planIdT,
                    }),
                ]),
            );
            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_060501);
        });

        test("should return CODE_060601 if aPICommonDAO.getPlans fails to query", async () => {
            await removeServiceOrder();

            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_0][1],
            );
            jest.spyOn(TenantManage.prototype, "doCheck").mockReturnValue(
                Promise.resolve([true, ""]),
            );
            jest.spyOn(APICommonDAO.prototype, "getPlans").mockImplementation(
                async () => {
                    throw new ConnectionTimedOutError(
                        new Error("time out error"),
                    );
                },
            );
            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_060601);
        });

        test("should return CODE_060701 if  変更前プランと変更後プランのフルMVNOフラグが一致しない場合, プラン変更パターン定型フラグが3（社内システムタイプ）", async () => {
            await removeServiceOrder();

            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_0][1],
            );
            jest.spyOn(TenantManage.prototype, "doCheck").mockReturnValue(
                Promise.resolve([true, ""]),
            );
            jest.spyOn(
                APICommonDAO.prototype,
                "getPlansTypeList",
            ).mockReturnValueOnce(
                Promise.resolve(["null", "null", "LTE", "test"]),
            );
            jest.spyOn(APICommonDAO.prototype, 'getPlanChangeFlag').mockResolvedValue([1])
            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_060701);
        });

        test("should return CODE_060701 if 変更前プランと変更後プランの対応NWが一致しない場合, プラン変更パターン定型フラグが1（帯域卸タイプ）の場合", async () => {
            await removeServiceOrder();

            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_0][1],
            );
            jest.spyOn(TenantManage.prototype, "doCheck").mockReturnValue(
                Promise.resolve([true, ""]),
            );
            jest.spyOn(
                APICommonDAO.prototype,
                "getPlanChangeFlag",
            ).mockReturnValueOnce(Promise.resolve([1]));
            jest.spyOn(APICommonDAO.prototype, "getPlansTypeList")
                .mockReturnValueOnce(
                    Promise.resolve(["true", "true", "5G(aSA)", "null"]),
                )
                .mockReturnValueOnce(
                    Promise.resolve(["true", "true", "test", "null"]),
                );
            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_060701);
        });

        test("should return CODE_060701 if 変更前プランと変更後プランの音声フラグ、SMS対応が一致しない場合, プラン変更パターン定型フラグが1（帯域卸タイプ）の場合", async () => {
            await removeServiceOrder();

            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_0][1],
            );
            jest.spyOn(TenantManage.prototype, "doCheck").mockReturnValue(
                Promise.resolve([true, ""]),
            );
            jest.spyOn(
                APICommonDAO.prototype,
                "getPlanChangeFlag",
            ).mockReturnValueOnce(Promise.resolve([1]));
            jest.spyOn(APICommonDAO.prototype, "getPlansTypeList")
                .mockReturnValueOnce(
                    Promise.resolve(["false", "true", "5G(aSA)", "null"]),
                )
                .mockReturnValueOnce(
                    Promise.resolve(["true", "true", "test", "null"]),
                );

            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_060701);
        });

        test("should return CODE_060701 if 変更前プランと変更後プランのフルMVNOフラグが一致しない場合, プラン変更パターン定型フラグが1（帯域卸タイプ）の場合", async () => {
            await removeServiceOrder();

            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_0][1],
            );
            jest.spyOn(TenantManage.prototype, "doCheck").mockReturnValue(
                Promise.resolve([true, ""]),
            );
            jest.spyOn(
                APICommonDAO.prototype,
                "getPlanChangeFlag",
            ).mockReturnValueOnce(Promise.resolve([1]));
            jest.spyOn(APICommonDAO.prototype, "getPlansTypeList")
                .mockReturnValueOnce(
                    Promise.resolve(["true", "true", "LTE", "true"]),
                )
                .mockReturnValueOnce(
                    Promise.resolve(["true", "true", "LTE", "null"]),
                );

            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_060701);
        });

        test("should return CODE_060701 if getTenantPlansPlanId returns null, プラン変更パターン定型フラグが1（帯域卸タイプ）の場合", async () => {
            await removeServiceOrder();

            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_0][1],
            );
            jest.spyOn(TenantManage.prototype, "doCheck").mockReturnValue(
                Promise.resolve([true, ""]),
            );
            jest.spyOn(
                APICommonDAO.prototype,
                "getPlanChangeFlag",
            ).mockReturnValueOnce(Promise.resolve([1]));
            jest.spyOn(APICommonDAO.prototype, "getPlansTypeList")
                .mockReturnValueOnce(
                    Promise.resolve(["true", "true", "LTE", "true"]),
                )
                .mockReturnValueOnce(
                    Promise.resolve(["true", "true", "LTE", "true"]),
                );
            jest.spyOn(
                APICommonDAO.prototype,
                "getTenantPlansPlanId",
            ).mockReturnValue(Promise.resolve([]));

            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_060701);
        });

        test("should return CODE_060701 if getTenantPlansPlanId , プラン変更パターン定型フラグが1（帯域卸タイプ）の場合", async () => {
            await removeServiceOrder();

            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_0][1],
            );
            jest.spyOn(TenantManage.prototype, "doCheck").mockReturnValue(
                Promise.resolve([true, ""]),
            );
            jest.spyOn(
                APICommonDAO.prototype,
                "getPlanChangeFlag",
            ).mockReturnValueOnce(Promise.resolve([1]));
            jest.spyOn(APICommonDAO.prototype, "getPlansTypeList")
                .mockReturnValueOnce(
                    Promise.resolve(["true", "true", "LTE", "true"]),
                )
                .mockReturnValueOnce(
                    Promise.resolve(["true", "true", "LTE", "true"]),
                );
            jest.spyOn(
                APICommonDAO.prototype,
                "getTenantPlansPlanId",
            ).mockImplementation(async () => {
                throw new ConnectionTimedOutError(new Error("time out error"));
            });
            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_060701);
        });

        test("should return CODE_060701 if getTenantPlanforPlanChange fails to query, プラン変更パターン定型フラグ is not 1, 2 or 3", async () => {
            await removeServiceOrder();

            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_0][1],
            );
            jest.spyOn(TenantManage.prototype, "doCheck").mockReturnValue(
                Promise.resolve([true, ""]),
            );
            jest.spyOn(
                APICommonDAO.prototype,
                "getPlanChangeFlag",
            ).mockReturnValueOnce(Promise.resolve([4]));
            jest.spyOn(
                APICommonDAO.prototype,
                "getTenantPlanforPlanChange",
            ).mockImplementation(async () => {
                throw new ConnectionTimedOutError(new Error("time out error"));
            });

            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_060701);
        });

        test("should return CODE_060801 if planChangeTime from getPlanChangeKaisu is less than tenantPlans.changeCount", async () => {
            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_2][1],
            );

            param.reserve_date = format(
                addDays(getDateNowForReservationTest(configData), 1),
                "yyyy/MM/dd HH:mm",
            );
            jest.spyOn(TenantManage.prototype, "doCheck").mockReturnValue(
                Promise.resolve([true, ""]),
            );
            jest.spyOn(
                APICommonDAO.prototype,
                "getPlanChangeFlag",
            ).mockReturnValue(Promise.resolve([0]));
            jest.spyOn(ApiCommon.prototype, "checkAbolishSo").mockReturnValue(
                Promise.resolve(true),
            );
            jest.spyOn(
                APICommonDAO.prototype,
                "getTenantPlanforPlanChange",
            ).mockResolvedValue(
                Promise.resolve(
                    TenantPlansEntity.build({
                        planId: parseInt(testdata.potalPlanID, 10),
                        tenantId: testdata.tenantId,
                    }),
                ),
            );
            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_060801);
        });

        test("should return CODE_060801 if getPlanChangeKaisu fails to query", async () => {
            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_2][1],
            );

            param.reserve_date = format(
                addDays(getDateNowForReservationTest(configData), 1),
                "yyyy/MM/dd HH:mm",
            );
            jest.spyOn(TenantManage.prototype, "doCheck").mockReturnValue(
                Promise.resolve([true, ""]),
            );
            jest.spyOn(
                APICommonDAO.prototype,
                "getPlanChangeFlag",
            ).mockReturnValue(Promise.resolve([0]));

            jest.spyOn(ApiCommon.prototype, "checkAbolishSo").mockReturnValue(
                Promise.resolve(true),
            );
            jest.spyOn(
                APICommonDAO.prototype,
                "getTenantPlanforPlanChange",
            ).mockResolvedValue(
                Promise.resolve(
                    TenantPlansEntity.build({
                        planId: parseInt(testdata.potalPlanID, 10),
                        tenantId: testdata.tenantId,
                    }),
                ),
            );
            jest.spyOn(
                APICommonDAO.prototype,
                "getPlanChangeKaisu",
            ).mockImplementation(async () => {
                throw new ConnectionTimedOutError(new Error("time out error"));
            });
            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_060801);
        });

        test("should return CODE_060601 if aPICommonDAO.getPlans returns null", async () => {
            jest.spyOn(APICommonDAO.prototype, "getPlans").mockResolvedValue(
                Promise.resolve(null),
            );
            await checkLine(testdata.lineId, null, null, null);
            await removeServiceOrder();

            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_0][1],
            );

            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_060601);
        });
        test("should return CODE_060601 if pricePLanId returned by aPICommonDAO.getPlans is null", async () => {
            await removeServiceOrder();

            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_0][1],
            );
            jest.spyOn(TenantManage.prototype, "doCheck").mockReturnValue(
                Promise.resolve([true, ""]),
            );
            jest.spyOn(APICommonDAO.prototype, "getPlans").mockReturnValue(
                Promise.resolve([
                    PlansEntity.build({
                        planId: null,
                    }),
                ]),
            );
            const result = await instance.service(param, false, ...nulls);

            checkResponse(result, ResultCdConstants.CODE_060601);
        });

        test("should return CODE_060701 if getPlansTypeList fails to query", async () => {
            await removeServiceOrder();

            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_0][1],
            );
            jest.spyOn(TenantManage.prototype, "doCheck").mockReturnValue(
                Promise.resolve([true, ""]),
            );
            jest.spyOn(
                APICommonDAO.prototype,
                "getPlansTypeList",
            ).mockImplementation(async () => {
                throw new ConnectionTimedOutError(new Error("time out error"));
            });
            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_060701);
        });
    });

    describe("NG CASES", () => {
        test("should return CODE_060101 if lineNo is invalid", async () => {
            param.lineNo = "abc";
            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_060101);
        });

        test("should return CODE_060101 if potalPlanID_pre is invalid", async () => {
            param.potalPlanID_pre = "abc";
            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_060101);
        });
        test("should return CODE_060101 if potalPlanID is invalid ", async () => {
            await removeServiceOrder();

            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_0][1],
            );
            param.potalPlanID = "abc";
            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_060101);
        });

        test("should return CODE_060101 if potalPlanID amd potalPlanID_pre are the same", async () => {
            param.potalPlanID = param.potalPlanID_pre;
            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_060101);
        });

        test("should return CODE_060101 if [予約実行オーダ] or [予約前オーダ]  and reserveDate is invalid", async () => {
            param.reserve_date = "somedate";
            param.reserve_flag = false;
            param.reserve_soId = null;
            const result = await instance.service(param, false, ...nulls);

            checkResponse(result, ResultCdConstants.CODE_060101);
        });
        test("should return CODE_060101 if [予約前オーダ] and csvUnnecessaryFlag is invalid", async () => {
            param.reserve_date = "2024/05/28 10:30:00";
            param.reserve_flag = false;
            param.reserve_soId = null;
            param.csvUnnecessaryFlag = "3";
            jest.spyOn(Check, "checkReserveDateFmt").mockReturnValue(true);
            const result = await instance.service(param, false, ...nulls);

            checkResponse(result, ResultCdConstants.CODE_060101);
        });

        test("should return CODE_060102 if getTenants throws db error", async () => {
            await removeServiceOrder();

            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_1][1],
            );

            jest.spyOn(APICommonDAO.prototype, "getTenants").mockRejectedValue(
                new ConnectionTimedOutError(Error("test error")),
            );
            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_060102);
        });

        test("should return CODE_060102 if getTenants returns null", async () => {
            await removeServiceOrder();

            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_1][1],
            );
            jest.spyOn(APICommonDAO.prototype, "getTenants").mockReturnValue(
                null,
            );
            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_060102);
        });

        test("should return CODE_060105 if [予想以外オーダ]", async () => {
            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_9][1],
            );
            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_060105);
        });

        test("should return CODE_060105 if order type cannot be detected", async () => {
            param.reserve_date = "reserve_date";
            param.reserve_flag = true;
            param.reserve_soId = undefined;
            const result = await instance.service(param, false, ...nulls);

            checkResponse(result, ResultCdConstants.CODE_060105);
        });
        test("should return CODE_060106 if planChangeNgTime config is invalid", async () => {
            Object.defineProperty(instance, "planChangeNgTime", {
                value: "invalid",
            });
            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_060106);
        });

        test("should return CODE_060107 if [即時オーダ] and checkPlanChangeNgTime is invalid", async () => {
            await removeServiceOrder();

            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_0][1],
            );
            jest.spyOn(Check, "checkPlanChangeNgTime").mockReturnValue(false);
            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_060107);
        });

        test("should return CODE_060108 if theres existing reserved orders, [即時オーダ]", async () => {
            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_0][1],
            );
            const result = await instance.service(param, false, ...nulls);

            checkResponse(result, ResultCdConstants.CODE_060108);
        });
        test("should return CODE_060108 if [予約前オーダ] and  checkReserveOrder returns false", async () => {
            await removeServiceOrder();

            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_1][1],
            );
            jest.spyOn(Check, "checkReserveOrder").mockReturnValue(
                Promise.resolve(false),
            );
            const result = await instance.service(param, false, ...nulls);

            checkResponse(result, ResultCdConstants.CODE_060108);
        });

        test("should return CODE_060108 if [予約前オーダ] and  checkReserveOrder throws db error", async () => {
            await removeServiceOrder();

            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_1][1],
            );

            jest.spyOn(Check, "checkReserveOrder").mockRejectedValue(
                new ConnectionTimedOutError(Error("test error")),
            );
            const result = await instance.service(param, false, ...nulls);

            checkResponse(result, ResultCdConstants.CODE_060108);
        });

        test("should return CODE_060109 if [予約前オーダ] and checkPlanChangeNgTime is invalid", async () => {
            await removeServiceOrder();

            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_1][1],
            );
            param.reserve_date = format(
                addDays(getDateNowForReservationTest(configData), 1),
                "yyyy/MM/dd HH:mm",
            );
            jest.spyOn(Check, "checkPlanChangeNgTime").mockReturnValue(false);

            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_060109);
        });

        test("should return CODE_060110 if [予約前オーダ] and checkReserveDateOrderDate is invalid", async () => {
            await removeServiceOrder();

            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_1][1],
            );
            param.reserve_date = format(
                addDays(getDateNowForReservationTest(configData), 1),
                "yyyy/MM/dd HH:mm",
            );
            jest.spyOn(Check, "checkReserveDateOrderDate").mockReturnValue(
                false,
            );
            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_060110);
        });

        test("should return CODE_060111 if [予約前オーダ] and checkReservationDateExecutionUnitsFmt is invalid", async () => {
            await removeServiceOrder();

            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_1][1],
            );
            param.reserve_date = format(
                addDays(getDateNowForReservationTest(configData), 1),
                "yyyy/MM/dd HH:mm",
            );
            jest.spyOn(
                Check,
                "checkReservationDateExecutionUnitsFmt",
            ).mockReturnValue(false);
            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_060111);
        });

        test("should return CODE_060112 if [予約前オーダ] and checkReservationDateExecutionUnits is invalid", async () => {
            await removeServiceOrder();

            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_1][1],
            );
            param.reserve_date = format(
                addDays(getDateNowForReservationTest(configData), 1),
                "yyyy/MM/dd HH:mm",
            );
            jest.spyOn(
                Check,
                "checkReservationDateExecutionUnits",
            ).mockReturnValue(false);
            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_060112);
        });

        test("should return CODE_060113 if [予約前オーダ] and checkReservationsLimitDays is invalid", async () => {
            await removeServiceOrder();

            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_1][1],
            );
            param.reserve_date = format(
                addDays(getDateNowForReservationTest(configData), 1),
                "yyyy/MM/dd HH:mm",
            );
            Object.defineProperty(instance, "reservationsLimitDays", {
                value: "invalid",
            });
            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_060113);
        });

        test("should return CODE_060114 if [予約前オーダ] and checkReservationsLimitDays is invalid", async () => {
            await removeServiceOrder();

            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_1][1],
            );
            param.reserve_date = format(
                addDays(getDateNowForReservationTest(configData), 1),
                "yyyy/MM/dd HH:mm",
            );
            jest.spyOn(Check, "checkReservationsLimitDays").mockReturnValue(
                false,
            );
            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_060114);
        });

        test("should return CODE_060131 if getPlanChangeFlag fails, [即時オーダ]", async () => {
            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_0][1],
            );

            await removeServiceOrder();
            jest.spyOn(
                APICommonDAO.prototype,
                "getPlanChangeFlag",
            ).mockImplementation(async () => {
                throw new ConnectionTimedOutError(new Error("time out error"));
            });

            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_060131);
        });

        test("should return CODE_060132 if getPlanChangeFlag fails, [予約前オーダ]", async () => {
            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_1][1],
            );
            param.reserve_date = format(
                addDays(getDateNowForReservationTest(configData), 1),
                "yyyy/MM/dd HH:mm",
            );

            await removeServiceOrder();
            jest.spyOn(
                APICommonDAO.prototype,
                "getPlanChangeFlag",
            ).mockImplementation(async () => {
                throw new ConnectionTimedOutError(new Error("time out error"));
            });

            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_060132);
        });

        test("should return CODE_060201 if line info not found in db", async () => {
            await removeServiceOrder();

            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_0][1],
            );
            jest.spyOn(
                APILinesDAO.prototype,
                "getLineInfoforUpdate",
            ).mockResolvedValue(null);
            const result = await instance.service(param, false, ...nulls);

            checkResponse(result, ResultCdConstants.CODE_060201);
        });

        test("should return CODE_060201 if getLineInfoforUpdate fails to query", async () => {
            await removeServiceOrder();

            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_0][1],
            );
            jest.spyOn(
                APILinesDAO.prototype,
                "getLineInfoforUpdate",
            ).mockImplementation(async () => {
                throw new ConnectionTimedOutError(new Error("time out error"));
            });
            const result = await instance.service(param, false, ...nulls);

            checkResponse(result, ResultCdConstants.CODE_060201);
        });

        test("should return CODE_060201 if failed to get line info lock", async () => {
            await removeServiceOrder();

            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_0][1],
            );

            Object.defineProperty(instance, "LOCK_WAIT_MILLISEC", {
                value: 1,
            });
            let tx: Transaction;
            const aPILinesDAO = new APILinesDAO(DefaultRequest, DefaultContext);
            try {
                tx = await sequelize.transaction();
                await aPILinesDAO.getLineInfoforUpdate(param.lineNo, tx);
                const result = await instance.service(param, false, ...nulls);

                checkResponse(result, ResultCdConstants.CODE_060201);
            } finally {
                await tx.rollback();
            }
        });

        test("should return CODE_060201 if getLineInfoforUpdate occurs error", async () => {
            await removeServiceOrder();

            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_0][1],
            );
            jest.spyOn(
                APILinesDAO.prototype,
                "getLineInfoforUpdate",
            ).mockRejectedValue(new Error("test error"));
            const result = await instance.service(param, false, ...nulls);

            checkResponse(result, ResultCdConstants.CODE_060201);
        });

        test("should return CODE_060401 if doCheck fails to query", async () => {
            await removeServiceOrder();

            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_0][1],
            );

            jest.spyOn(TenantManage.prototype, "doCheck").mockImplementation(
                async () => {
                    throw new ConnectionTimedOutError(
                        new Error("time out error"),
                    );
                },
            );
            const result = await instance.service(param, false, ...nulls);

            checkResponse(result, ResultCdConstants.CODE_060401);
        });

        test("should return CODE_060401 if doCheck returns false", async () => {
            await removeServiceOrder();
            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_0][1],
            );

            jest.spyOn(TenantManage.prototype, "doCheck").mockReturnValueOnce(
                Promise.resolve([false, ""]),
            );
            const result = await instance.service(param, false, ...nulls);

            checkResponse(result, ResultCdConstants.CODE_060401);
        });

        test("should return CODE_061302 if getGroupId fails to query", async () => {
            await removeServiceOrder();

            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_0][1],
            );
            jest.spyOn(APILinesDAO.prototype, "getGroupId").mockImplementation(
                async () => {
                    throw new ConnectionTimedOutError(
                        new Error("time out error"),
                    );
                },
            );
            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_061302);
        });

        test("should return CODE_061302 if multiple groupId found in line_line_group", async () => {
            await removeServiceOrder();

            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_0][1],
            );
            jest.spyOn(APILinesDAO.prototype, "getGroupId").mockReturnValue(
                Promise.resolve(["tes1", "test2"]),
            );
            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_061302);
        });

        test("should return CODE_061302 if groupId found in line_line_group and tenantType is not null, 2, 3 or 4", async () => {
            await removeServiceOrder();

            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_0][1],
            );

            jest.spyOn(APILinesDAO.prototype, "getGroupId").mockResolvedValue([
                "test1",
            ]);

            jest.spyOn(APICommonDAO.prototype, "getTenants").mockReturnValue(
                Promise.resolve(
                    TenantsEntity.build({
                        tenantId: testdata.tenantId,
                        tenantType: 10,
                    }),
                ),
            );
            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_061302);
        });

        test("should return CODE_060901 if getPlans fails to query", async () => {
            await removeServiceOrder();
            jest.spyOn(TenantManage.prototype, "doCheck").mockReturnValue(
                Promise.resolve([true, ""]),
            );
            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_0][1],
            );
            jest.spyOn(APICommonDAO.prototype, "getTenants").mockReturnValue(
                Promise.resolve(
                    TenantsEntity.build({
                        tenantId: testdata.tenantId,
                        tenantType: 1,
                    }),
                ),
            );
            jest.spyOn(APICommonDAO.prototype, "getPlans").mockImplementation(
                async () => {
                    throw new ConnectionTimedOutError(
                        new Error("time out error"),
                    );
                },
            );
            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_060901);
        });

        test("should return CODE_060901 if getPlans returns null", async () => {
            await removeServiceOrder();
            jest.spyOn(TenantManage.prototype, "doCheck").mockReturnValue(
                Promise.resolve([true, ""]),
            );
            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_0][1],
            );
            jest.spyOn(APICommonDAO.prototype, "getTenants").mockReturnValue(
                Promise.resolve(
                    TenantsEntity.build({
                        tenantId: testdata.tenantId,
                        tenantType: 1,
                    }),
                ),
            );
            jest.spyOn(APICommonDAO.prototype, "getPlans").mockReturnValue(
                Promise.resolve([]),
            );
            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_060901);
        });
        test("should return CODE_060901 if servicePlanId from PlansEntity is empty", async () => {
            await removeServiceOrder();
            jest.spyOn(TenantManage.prototype, "doCheck").mockReturnValue(
                Promise.resolve([true, ""]),
            );
            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_0][1],
            );
            jest.spyOn(APICommonDAO.prototype, "getTenants").mockReturnValue(
                Promise.resolve(
                    TenantsEntity.build({
                        tenantId: testdata.tenantId,
                        tenantType: 1,
                    }),
                ),
            );
            jest.spyOn(APICommonDAO.prototype, "getPlans").mockReturnValue(
                Promise.resolve([
                    PlansEntity.build({
                        planId: parseInt(testdata.potalPlanID, 10),
                        planName: "test",
                        servicePlanId: null,
                        planIdT: testdata.planIdT,
                    }),
                ]),
            );

            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_060901);
        });

        test("should return CODE_060501 if aPILinesDAO.getLineInfo fails to query", async () => {
            await TenantsEntity.update(
                {
                    tenantType: 2,
                },
                {
                    where: {
                        tenantId: testdata.tenantId,
                    },
                },
            );
            await removeServiceOrder();
            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_0][1],
            );
            jest.spyOn(TenantManage.prototype, "doCheck").mockReturnValue(
                Promise.resolve([true, ""]),
            );
            jest.spyOn(APILinesDAO.prototype, "getLineInfo").mockImplementation(
                async () => {
                    throw new ConnectionTimedOutError(
                        new Error("time out error"),
                    );
                },
            );

            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_060501);
        });

        test("should return CODE_060501 if aPILinesDAO.getLineInfo return null", async () => {
            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_0][1],
            );
            jest.spyOn(TenantManage.prototype, "doCheck").mockReturnValue(
                Promise.resolve([true, ""]),
            );
            jest.spyOn(APILinesDAO.prototype, "getLineInfo").mockReturnValue(
                Promise.resolve(null),
            );
            jest.spyOn(Check, "checkReserveOrder").mockReturnValue(
                Promise.resolve(true),
            );
            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_060501);
        });

        test("should return CODE_060108 if theres existing reserved orders, [予約前オーダ]", async () => {
            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_1][1],
            );
            param.reserve_date = format(
                addDays(getDateNowForReservationTest(configData), 1),
                "yyyy/MM/dd HH:mm",
            );
            await PlansEntity.update(
                {
                    planChangeClass: 0,
                },
                {
                    where: {
                        planId: testdata.potalPlanID_pre,
                    },
                },
            );
            await PlansEntity.update(
                {
                    planChangeClass: 0,
                },
                {
                    where: {
                        planId: testdata.potalPlanID,
                    },
                },
            );
            await TenantsEntity.update(
                {
                    planChangeFlag: 2,
                },
                {
                    where: {
                        tenantId: testdata.tenantId,
                    },
                },
            );
            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_060108);
        });

        test("should return CODE_060131 if プラン変更種別 is null", async () => {
            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_0][1],
            );

            await removeServiceOrder();
            jest.spyOn(APICommonDAO.prototype,'getPlanChangeClass').mockResolvedValue(null)

            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_060131);
        });

        test("should return CODE_060131 if changeTiming is 月発 but orderdate is not first day of the month", async () => {
            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_0][1],
            );

            await removeServiceOrder();
            jest.spyOn(APILinesDAO.prototype,'getChangeTiming').mockResolvedValue(['月初'])
            const result = await instance.service(param, false, ...nulls);
            checkResponse(result, ResultCdConstants.CODE_060131);
        });
    });
});
