import { expect, test, beforeEach } from "@jest/globals";
import { addMinutes, formatDate } from "date-fns";
import { ConnectionTimedOutError, Sequelize } from "sequelize";

import "@/types/string.extension";

import {
    describeWithDB,
    generateProcessID,
    generateSOID,
    useFasterRetries,
} from "../../../testing/TestHelper";
import {
    deleteLineNumber,
    insertLineNumber,
} from "../../../testing/testdataHelper";
import DefaultContext from "../../../testing/DefaultContext";
import DefaultRequest from "../../../testing/DefaultRequest";

import AppConfig from "@/appconfig";
import TenantManage from "@/core/common/TenantManage";
import ResultCdConstants from "@/core/constant/ResultCdConstants";

import APISoDAO from "@/core/dao/APISoDAO";
import FrontSoCancelInputDto from "@/core/dto/FrontSoCancelInputDto";
import ServiceOrdersEntity from "@/core/entity/ServiceOrdersEntity";

import { usePsql } from "@/database/psql";

import FrontSoCancelService from "@/core/service/impl/FrontSoCancelService";

/**
 * Unit tests for cases which are not covered by FrontSoCancelHandler.test.ts
 */
describeWithDB("core/service/impl/FrontSoCancelService", () => {
    let sequelize: Sequelize;
    let instance: FrontSoCancelService = null;

    const testdata = {
        lineNo: "08024082301",
        abolishSo: generateProcessID(),
        targetSoId: generateSOID(),
        // reserveDate is 5 days after today
        reserveDate: addMinutes(new Date(), 1 * 24 * 60),
        targetTenantId: "TST000",
    };

    const restJson = {
        requestHeader: {
            sequenceNo: "1234567",
            senderSystemId: "0001",
            apiKey: "$$$",
            functionType: "52",
        },
        tenantId: "OPF000",
        targetTenantId: testdata.targetTenantId,
        lineNo: testdata.lineNo,
        mnpOutFlag: "0",
        csvUnnecessaryFlag: "0",
        reserve_date: formatDate(testdata.reserveDate, "yyyy/MM/dd"),
    };

    const request = {
        requestHeader: {
            sequenceNo: "1234567",
            senderSystemId: "0001",
            apiKey: "",
            functionType: "56",
        },
        targetSoId: testdata.targetSoId,
        tenantId: "OPF000",
        targetTenantId: "TST000",
        serviceOrderIdKey: testdata.abolishSo,
        csvUnnecessaryFlag: "0",
    };

    const responseHeader = {
        sequenceNo: request.requestHeader.sequenceNo,
        receivedDate: formatDate(new Date(), "yyyy/MM/dd HH:mm:ss"),
        processCode: ResultCdConstants.CODE_000000,
        apiProcessID: generateProcessID(),
    };

    beforeAll(async () => {
        await AppConfig.loadCoreMvnoConfig(DefaultContext);
        sequelize = await usePsql();

        await insertLineNumber(testdata.targetTenantId, testdata.lineNo);
    });

    afterAll(async () => {
        await deleteLineNumber(testdata.lineNo);
        await sequelize.close();
    });

    beforeEach(async () => {
        useFasterRetries();

        DefaultContext.jsonBody = Object.assign({}, request);
        DefaultContext.responseHeader = Object.assign({}, responseHeader);
        instance = new FrontSoCancelService(DefaultRequest, DefaultContext);

        await Promise.all([
            ServiceOrdersEntity.create({
                serviceOrderId: testdata.abolishSo,
                tenantId: request.targetTenantId,
                lineId: testdata.lineNo,
                orderStatus: "予約中",
                orderType: "回線廃止",
                reserveDate: testdata.reserveDate,
                execDate: addMinutes(new Date(), -5),
                functionType: "52",
                restMessage: JSON.stringify(restJson),
            }),
        ]);
    });

    afterEach(async () => {
        await Promise.all([
            ServiceOrdersEntity.destroy({
                where: {
                    serviceOrderId: [
                        testdata.abolishSo,
                        responseHeader.apiProcessID,
                    ],
                },
            }),
        ]);

        jest.restoreAllMocks();
        jest.clearAllMocks();
    });

    describe("service (NG result)", () => {
        const param: FrontSoCancelInputDto = {
            requestHeader: {
                sequenceNo: "1234567",
                senderSystemId: "0001",
                apiKey: "$$$",
                functionType: "52",
            },
            targetSoId: testdata.targetSoId,
            reserve_flag: false,
            reserve_soId: null,
            tenantId: "OPF000",
            targetTenantId: restJson.targetTenantId,
            serviceOrderIdKey: testdata.abolishSo,
            csvUnnecessaryFlag: "0",
        };

        it("should return CODE_560201 if getReserveFrontSo failed", async () => {
            jest.spyOn(ServiceOrdersEntity, "findOne").mockImplementation(
                () => {
                    throw new ConnectionTimedOutError(new Error("timeout"));
                },
            );

            const result = await instance.service(param);

            expect(result.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_560201,
            );
        });

        it("should return CODE_999999 if updServiceOrders failed", async () => {
            const apiSoDAO = new APISoDAO(DefaultRequest, DefaultContext);
            jest.spyOn(apiSoDAO, "updServiceOrders").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });
            Object.defineProperty(instance, "apiSoDAO", { value: apiSoDAO });

            const result = await instance.service(param);

            expect(result.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_999999,
            );
        });

        it("csvOutputKbn should be 0 if TenantManage.doCheck throws DB error", async () => {
            const tenantManage = new TenantManage(
                DefaultRequest,
                DefaultContext,
            );
            jest.spyOn(tenantManage, "doCheck").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });
            Object.defineProperty(instance, "tenantManage", {
                value: tenantManage,
            });

            const result = await instance.service(param);

            expect(result.additionalData.csvOutputKbn).toBe("0");
            expect(result.additionalData.csvUnnecessary).toBe(true);
        });

        it("should return CODE_999999 if TenantManage.doCheck throws non DB error", async () => {
            const tenantManage = new TenantManage(
                DefaultRequest,
                DefaultContext,
            );
            jest.spyOn(tenantManage, "doCheck").mockImplementation(() => {
                throw new TypeError("[test] required field is missing");
            });
            Object.defineProperty(instance, "tenantManage", {
                value: tenantManage,
            });

            const result = await instance.service(param);

            expect(result.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_999999,
            );
            expect(result.additionalData.csvOutputKbn).toBe("0");
            expect(result.additionalData.csvUnnecessary).toBe(true);
        });

        it("csvOutputKbn should be 0 if TenantManage.doCheck returns empty Nban", async () => {
            const tenantManage = new TenantManage(
                DefaultRequest,
                DefaultContext,
            );
            jest.spyOn(tenantManage, "doCheck").mockImplementation(() => {
                return Promise.resolve([true, null] as [boolean, string]);
            });
            Object.defineProperty(instance, "tenantManage", {
                value: tenantManage,
            });

            const result = await instance.service(param);

            expect(result.additionalData.csvOutputKbn).toBe("0");
            expect(result.additionalData.csvUnnecessary).toBe(true);
        });
    });
});
