import { formatDate } from "date-fns";
import { ConnectionTimedOutError, Sequelize } from "sequelize";

import "@/types/string.extension";

import {
    describeWithDB,
    generateProcessID,
    generateSOID,
    useFasterRetries,
} from "../../../testing/TestHelper";
import DefaultContext from "../../../testing/DefaultContext";
import DefaultRequest from "../../../testing/DefaultRequest";

import AppConfig from "@/appconfig";
import ApiCommon from "@/core/common/ApiCommon";
import MvnoUtil from "@/core/common/MvnoUtil";
import TenantManage from "@/core/common/TenantManage";

import ResultCdConstants from "@/core/constant/ResultCdConstants";

import APILinesDAO from "@/core/dao/APILinesDAO";
import ReissueSimInputDto from "@/core/dto/ReissueSimInputDto";
import ResponseHeader from "@/core/dto/ResponseHeader";

import { usePsql } from "@/database/psql";

import { LinesEntity } from "@/core/entity/LinesEntity";
import LineTenantsEntity from "@/core/entity/LineTenantsEntity";
import ServiceOrdersEntity from "@/core/entity/ServiceOrdersEntity";

import ReissueSimService from "@/core/service/impl/ReissueSimService";

/**
 * Unit tests for cases which are not covered by ReissueSimHandler.test.ts
 */
describeWithDB("core/service/impl/ReissueSimService", () => {
    let sequelize: Sequelize;
    let instance: ReissueSimService;

    const testdata = {
        lineOK: "08024013001",
    };

    const deviceId = {
        lte: "AD7",
        sms: "AE0",
        voice: "AW9",
    };

    const request = {
        requestHeader: {
            sequenceNo: "1234567",
            senderSystemId: "0001",
            apiKey: "",
            functionType: "53",
        },
        targetSoId: generateSOID(),
        tenantId: "OPF000",
        targetTenantId: "TST000",
        lineNo: testdata.lineOK,
        sim_no: "SIMTEST2403301010",
        sim_type: "nanoSIM",
        cardTypeId: deviceId.lte,
        csvUnnecessaryFlag: "0",
    } as ReissueSimInputDto;

    const responseHeader = {
        sequenceNo: request.requestHeader.sequenceNo,
        receivedDate: formatDate(new Date(), "yyyy/MM/dd HH:mm:ss"),
        processCode: ResultCdConstants.CODE_000000,
        apiProcessID: generateProcessID(),
    } as ResponseHeader;

    const clearTestData = async () => {
        await LineTenantsEntity.destroy({
            where: {
                lineId: testdata.lineOK,
            },
        });
        await LinesEntity.destroy({
            where: {
                lineId: testdata.lineOK,
            },
        });
        await ServiceOrdersEntity.destroy({
            where: {
                serviceOrderId: responseHeader.apiProcessID,
            },
        });
    };

    beforeAll(async () => {
        await AppConfig.loadCoreMvnoConfig(DefaultContext);
        sequelize = await usePsql();
    });

    afterAll(async () => {
        await sequelize.close();
    });

    beforeEach(async () => {
        useFasterRetries();
        DefaultContext.jsonBody = Object.assign({}, request);
        DefaultContext.responseHeader = Object.assign({}, responseHeader);
        instance = new ReissueSimService(DefaultRequest, DefaultContext);

        await LinesEntity.create({
            lineId: testdata.lineOK,
            tenantId: request.targetTenantId,
            lineStatus: "01",
            simFlag: false,
            deviceTypeId: deviceId.lte,
            modelType: "テストSIM",
            simType: "microSIM",
        });
        await LineTenantsEntity.create({
            lineId: testdata.lineOK,
            tenantId: request.targetTenantId,
        });
    });

    afterEach(async () => {
        await clearTestData();
        jest.restoreAllMocks();
        jest.clearAllMocks();
    });

    describe("NG cases", () => {
        it("should return CODE_530102 if ApiLinesDao.getLineInfoByLines failed (DB error)", async () => {
            const apiLinesDao = new APILinesDAO(DefaultRequest, DefaultContext);
            jest.spyOn(apiLinesDao, "getLineInfoByLines").mockRejectedValue(
                new ConnectionTimedOutError(new Error("Connection timed out")),
            );
            Object.defineProperty(instance, "apiLinesDao", {
                value: apiLinesDao,
            });

            const result = await instance.service(
                request,
                MvnoUtil.getClientIPAddress(DefaultRequest),
            );
            expect(result.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_530102,
            );
            expect(result.additionalData.lineInfoUpdateError).toBe(false); // not updated
        });

        it("should return CODE_530103 if TenantManage.doCheck failed (DB error)", async () => {
            const tenantManage = new TenantManage(
                DefaultRequest,
                DefaultContext,
            );
            jest.spyOn(tenantManage, "doCheck").mockRejectedValue(
                new ConnectionTimedOutError(new Error("Connection timed out")),
            );
            Object.defineProperty(instance, "tenantManage", {
                value: tenantManage,
            });

            const result = await instance.service(
                request,
                MvnoUtil.getClientIPAddress(DefaultRequest),
            );
            expect(result.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_530103,
            );
            expect(result.additionalData.lineInfoUpdateError).toBe(false); // not updated
        });

        it("should return CODE_530104 if ApiCommon.checkAbolishSo failed (DB error)", async () => {
            const apiCommon = new ApiCommon(DefaultRequest, DefaultContext);
            jest.spyOn(apiCommon, "checkAbolishSo").mockRejectedValue(
                new ConnectionTimedOutError(new Error("Connection timed out")),
            );
            Object.defineProperty(instance, "apiCommon", {
                value: apiCommon,
            });

            const result = await instance.service(
                request,
                MvnoUtil.getClientIPAddress(DefaultRequest),
            );
            expect(result.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_530104,
            );
            expect(result.additionalData.lineInfoUpdateError).toBe(false); // not updated
        });

        it("should return CODE_000000 with error flag if ApiLinesDao.getCard failed (DB error)", async () => {
            const apiLinesDao = new APILinesDAO(DefaultRequest, DefaultContext);
            jest.spyOn(apiLinesDao, "getCard").mockRejectedValue(
                new ConnectionTimedOutError(new Error("Connection timed out")),
            );
            Object.defineProperty(instance, "apiLinesDao", {
                value: apiLinesDao,
            });

            const result = await instance.service(
                request,
                MvnoUtil.getClientIPAddress(DefaultRequest),
            );
            expect(result.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_000000,
            );
            expect(result.additionalData.lineInfoUpdateError).toBe(true);
        });

        it("should return CODE_000000 if ApiLinesDao.updateLineSimInfo failed (DB error)", async () => {
            const apiLinesDao = new APILinesDAO(DefaultRequest, DefaultContext);
            jest.spyOn(apiLinesDao, "updateLineSimInfo").mockRejectedValue(
                new ConnectionTimedOutError(new Error("Connection timed out")),
            );
            Object.defineProperty(instance, "apiLinesDao", {
                value: apiLinesDao,
            });

            const result = await instance.service(
                request,
                MvnoUtil.getClientIPAddress(DefaultRequest),
            );
            expect(result.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_000000,
            );
            expect(result.additionalData.lineInfoUpdateError).toBe(true);
        });

        it("should return CODE_000000 if ApiLinesDao.updateLineSimInfo failed (non DB error)", async () => {
            const apiLinesDao = new APILinesDAO(DefaultRequest, DefaultContext);
            jest.spyOn(apiLinesDao, "updateLineSimInfo").mockRejectedValue(
                new Error("[test] Update data is null"),
            );
            Object.defineProperty(instance, "apiLinesDao", {
                value: apiLinesDao,
            });

            const result = await instance.service(
                request,
                MvnoUtil.getClientIPAddress(DefaultRequest),
            );
            expect(result.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_000000,
            );
            expect(result.additionalData.lineInfoUpdateError).toBe(true);
        });

        it("should return CODE_999999 for non DB error", async () => {
            const apiLinesDao = new APILinesDAO(DefaultRequest, DefaultContext);
            jest.spyOn(apiLinesDao, "getCard").mockRejectedValue(
                new TypeError("[test] cardTypeId is required"),
            );
            Object.defineProperty(instance, "apiLinesDao", {
                value: apiLinesDao,
            });

            const result = await instance.service(
                request,
                MvnoUtil.getClientIPAddress(DefaultRequest),
            );
            expect(result.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_999999,
            );
            expect(result.additionalData.lineInfoUpdateError).toBe(false); // not updated
        });
    });
});
