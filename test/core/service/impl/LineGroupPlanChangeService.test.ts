import { addDays, format, formatDate, parse } from "date-fns";
import nock from "nock";
import { ConnectionTimedOutError, Sequelize, Transaction } from "sequelize";

import AppConfig from "@/appconfig";
import Check from "@/core/common/Check";
import MvnoUtil from "@/core/common/MvnoUtil";
import SOAPCommon from "@/core/common/SOAPCommon";
import TenantManage from "@/core/common/TenantManage";
import Constants from "@/core/constant/Constants";
import ResultCdConstants from "@/core/constant/ResultCdConstants";
import APICommonDAO from "@/core/dao/APICommonDAO";
import APILinesGroupDAO from "@/core/dao/ApiLinesGroupDAO";
import LineGroupPlanChangeInputDto from "@/core/dto/LineGroupPlanChangeInputDto";
import { usePsql } from "@/database/psql";
import { disconnectMongo, useMongo } from "@/database/mongo";
import LineGroupsEntity from "@/core/entity/LineGroupsEntity";
import ServiceOrdersEntity from "@/core/entity/ServiceOrdersEntity";

import LineGroupPlanChangeService from "@/core/service/impl/LineGroupPlanChangeService";

import DefaultContext from "../../../testing/DefaultContext";
import DefaultRequest from "../../../testing/DefaultRequest";
import {
    getConfigDataForTest,
    getDateNowForReservationTest,
} from "../../../testing/testdataHelper";
import {
    describeWithDB,
    generateProcessID,
    useFasterRetries,
} from "../../../testing/TestHelper";
import SOAPTestHelper from "../../../testing/soapTestHelper";

describeWithDB("@/core/service/impl/LineGroupPlanChangeService", () => {
    let sequelize: Sequelize;
    let instance: LineGroupPlanChangeService;

    const testdata = {
        lineGroupId: "7109999", // should not exist in the database
        lineGroupIdReserved: "7109998", // has 予約中 order
        groupPlanId_pre: "90001",
        groupPlanId: "50001",
        tenantId: "TST000",
        reserve_date: "",
        reserveSOID: generateProcessID(),
        mismatchTenant: "TSA000",
        mismatchGroupPlanId_pre: "90002",
    };

    const __request: LineGroupPlanChangeInputDto = {
        requestHeader: {
            sequenceNo: "1234567",
            senderSystemId: "0001",
            apiKey: "",
            functionType: "20",
        },
        targetSoId: undefined,
        reserve_flag: undefined,
        reserve_soId: undefined,
        reserve_date: "",
        tenantId: testdata.tenantId,
        lineGroupId: testdata.lineGroupId,
        potalGroupPlanID: testdata.groupPlanId,
        potalGroupPlanID_pre: testdata.groupPlanId_pre,
    };
    let param = Object.assign({}, __request);

    const responseHeader = {
        sequenceNo: __request.requestHeader.sequenceNo,
        receivedDate: formatDate(new Date(), "yyyy/MM/dd HH:mm:ss"),
        processCode: ResultCdConstants.CODE_000000,
        apiProcessID: generateProcessID(),
    };

    const nockHeaders: nock.ReplyHeaders = {
        "Content-Type": "text/xml",
    };

    let configData: ReturnType<typeof getConfigDataForTest>;

    async function prepareTestData() {
        await Promise.all([
            // create new line group
            LineGroupsEntity.create({
                groupId: testdata.lineGroupId,
                status: 1,
                tenantId: testdata.tenantId,
                planId: parseInt(testdata.groupPlanId_pre),
            }),
            // create service order for reservation
            ServiceOrdersEntity.create({
                serviceOrderId: testdata.reserveSOID,
                orderDate: parse(
                    testdata.reserve_date,
                    "yyyy/MM/dd HH:mm",
                    new Date(),
                ),
                reserveDate: parse(
                    testdata.reserve_date,
                    "yyyy/MM/dd HH:mm",
                    new Date(),
                ),
                functionType: __request.requestHeader.functionType,
                orderType: "回線グループプラン変更",
                tenantId: testdata.tenantId,
                orderStatus: "予約中",
                groupId: testdata.lineGroupIdReserved,
            }),
        ]);
    }

    async function clearTestData() {
        await Promise.all([
            LineGroupsEntity.destroy({
                where: {
                    groupId: testdata.lineGroupId,
                },
            }),
            ServiceOrdersEntity.destroy({
                where: {
                    serviceOrderId: [
                        responseHeader.apiProcessID,
                        testdata.reserveSOID,
                    ],
                },
            }),
        ]);
    }

    beforeAll(async () => {
        sequelize = await usePsql();
        await useMongo(DefaultContext);
        await AppConfig.loadCoreMvnoConfig(DefaultContext);
        configData = getConfigDataForTest();
        nock.disableNetConnect();

        await clearTestData();
        testdata.reserve_date = format(
            addDays(getDateNowForReservationTest(configData), 1),
            "yyyy/MM/dd HH:mm",
        );
    });

    afterAll(async () => {
        nock.enableNetConnect();

        await sequelize.close();
        await disconnectMongo(DefaultContext);
    });

    beforeEach(async () => {
        DefaultContext.jsonBody = Object.assign({}, __request);
        DefaultContext.responseHeader = Object.assign({}, responseHeader);
        jest.spyOn(MvnoUtil, "getDateTimeNow").mockReturnValue(
            responseHeader.receivedDate,
        );
        useFasterRetries();
        // jest.useFakeTimers();
        instance = new LineGroupPlanChangeService(
            DefaultRequest,
            DefaultContext,
        );

        await prepareTestData();
        param = Object.assign({}, __request);
    });

    afterEach(async () => {
        jest.restoreAllMocks();
        jest.clearAllMocks();
        // jest.useRealTimers();
        nock.abortPendingRequests();
        nock.cleanAll();

        await clearTestData();
    });

    async function checkServiceOrder(
        created: boolean,
        success: boolean,
        tenantId?: string,
    ) {
        const so = await ServiceOrdersEntity.findOne({
            where: {
                serviceOrderId: DefaultContext.responseHeader.apiProcessID,
            },
        });

        // console.log(so);

        if (created) {
            expect(so).toBeDefined();
            expect(so).not.toBeNull();
            if (!success) {
                expect(so.orderStatus).toEqual("失敗");
            } else {
                expect(so.orderStatus).not.toEqual("失敗");
            }
            expect(so.orderType).toEqual("回線グループプラン変更");
            expect(so.tenantId).toEqual(tenantId ?? testdata.tenantId);
            expect(so.functionType).toEqual("20");
        } else {
            expect(so).toBeNull();
        }
        return so;
    }

    type OrderTypeParam = {
        reserve_flag: boolean | undefined;
        reserve_soId: string | undefined;
        reserve_date: boolean;
    };
    const orderTypeCases: { [x: string]: [string, OrderTypeParam] } = {
        [Constants.ORDER_TYPE_0]: [
            "即時オーダ",
            {
                reserve_flag: undefined,
                reserve_soId: undefined,
                reserve_date: false,
            },
        ],
        [Constants.ORDER_TYPE_1]: [
            "予約前オーダ",
            {
                reserve_flag: false,
                reserve_soId: undefined,
                reserve_date: true,
            },
        ],
        [Constants.ORDER_TYPE_2]: [
            "予約実行オーダ",
            {
                reserve_flag: true,
                reserve_soId: testdata.reserveSOID,
                reserve_date: true,
            },
        ],
    };
    function updateParamForOrderCase(
        param: LineGroupPlanChangeInputDto,
        orderCase: OrderTypeParam,
    ) {
        param.reserve_flag = orderCase.reserve_flag;
        param.reserve_soId = orderCase.reserve_soId;
        param.reserve_date = orderCase.reserve_date
            ? testdata.reserve_date
            : "";
        if (param.reserve_soId) {
            // 予約実行オーダ will use reserve_soId instead of new apiProcessID
            DefaultContext.responseHeader.apiProcessID = param.reserve_soId;
        }
    }

    describe("OK cases", () => {
        beforeEach(() => {
            jest.spyOn(
                TenantManage.prototype,
                "checkTpcConnection",
            ).mockResolvedValue([true, "TpcDestIpAddress", "TpcDestIpAddress"]);

            nock(configData.soapApiUrl)
                .post("")
                .reply(200, SOAPTestHelper.simpleResponse("OK"))
                .persist();
        });

        test("should return CODE_000000 and update planId for 即時オーダ", async () => {
            // check plan id before request
            const currentLineGroup = await LineGroupsEntity.findOne({
                where: {
                    groupId: testdata.lineGroupId,
                },
            });
            expect(currentLineGroup).toBeDefined();
            expect(currentLineGroup).not.toBeNull();
            expect(currentLineGroup.planId).toEqual(
                parseInt(testdata.groupPlanId_pre),
            );

            const response = await instance.service(param, false, null);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );
            await checkServiceOrder(true, true);

            const lineGroup = await LineGroupsEntity.findOne({
                where: {
                    groupId: testdata.lineGroupId,
                },
            });
            expect(lineGroup).toBeDefined();
            expect(lineGroup).not.toBeNull();
            // plan id updated
            expect(lineGroup.planId).toEqual(parseInt(testdata.groupPlanId));
        });

        test("should return CODE_000000 and update planId for 予約実行オーダ", async () => {
            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_2][1],
            );

            // check plan id before request
            const currentLineGroup = await LineGroupsEntity.findOne({
                where: {
                    groupId: testdata.lineGroupId,
                },
            });
            expect(currentLineGroup).toBeDefined();
            expect(currentLineGroup).not.toBeNull();
            expect(currentLineGroup.planId).toEqual(
                parseInt(testdata.groupPlanId_pre),
            );

            const response = await instance.service(param, false, null);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );
            await checkServiceOrder(true, true);

            const lineGroup = await LineGroupsEntity.findOne({
                where: {
                    groupId: testdata.lineGroupId,
                },
            });
            expect(lineGroup).toBeDefined();
            expect(lineGroup).not.toBeNull();
            // plan id updated
            expect(lineGroup.planId).toEqual(parseInt(testdata.groupPlanId));
        });

        test("should return CODE_000000 and not update planId for 予約前オーダ", async () => {
            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_1][1],
            );

            // check plan id before request
            const currentLineGroup = await LineGroupsEntity.findOne({
                where: {
                    groupId: testdata.lineGroupId,
                },
            });
            expect(currentLineGroup).toBeDefined();
            expect(currentLineGroup).not.toBeNull();
            expect(currentLineGroup.planId).toEqual(
                parseInt(testdata.groupPlanId_pre),
            );

            const soapAPICall = jest.spyOn(
                SOAPCommon.prototype,
                "callWebSoapApi",
            );

            const response = await instance.service(param, false, null);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );
            await checkServiceOrder(true, true);

            expect(soapAPICall).not.toHaveBeenCalled(); // no TPC連携

            const lineGroup = await LineGroupsEntity.findOne({
                where: {
                    groupId: testdata.lineGroupId,
                },
            });
            expect(lineGroup).toBeDefined();
            expect(lineGroup).not.toBeNull();
            // plan id not updated
            expect(lineGroup.planId).toEqual(
                parseInt(testdata.groupPlanId_pre),
            );
        });

        test("should return CODE_000000 : lineGroupId is number", async () => {
            // check plan id before request
            const currentLineGroup = await LineGroupsEntity.findOne({
                where: {
                    groupId: testdata.lineGroupId,
                },
            });
            expect(currentLineGroup).toBeDefined();
            expect(currentLineGroup).not.toBeNull();
            expect(currentLineGroup.planId).toEqual(
                parseInt(testdata.groupPlanId_pre),
            );

            param.lineGroupId = parseInt(testdata.lineGroupId, 10) as any;
            const response = await instance.service(param, false, null);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );
            await checkServiceOrder(true, true);

            const lineGroup = await LineGroupsEntity.findOne({
                where: {
                    groupId: testdata.lineGroupId,
                },
            });
            expect(lineGroup).toBeDefined();
            expect(lineGroup).not.toBeNull();
            // plan id updated
            expect(lineGroup.planId).toEqual(parseInt(testdata.groupPlanId));
        });

        test("should return CODE_000000 : potalGroupPlanID_pre is number", async () => {
            // check plan id before request
            const currentLineGroup = await LineGroupsEntity.findOne({
                where: {
                    groupId: testdata.lineGroupId,
                },
            });
            expect(currentLineGroup).toBeDefined();
            expect(currentLineGroup).not.toBeNull();
            expect(currentLineGroup.planId).toEqual(
                parseInt(testdata.groupPlanId_pre),
            );

            param.potalGroupPlanID_pre = parseInt(testdata.groupPlanId_pre, 10) as any;
            const response = await instance.service(param, false, null);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );
            await checkServiceOrder(true, true);

            const lineGroup = await LineGroupsEntity.findOne({
                where: {
                    groupId: testdata.lineGroupId,
                },
            });
            expect(lineGroup).toBeDefined();
            expect(lineGroup).not.toBeNull();
            // plan id updated
            expect(lineGroup.planId).toEqual(parseInt(testdata.groupPlanId));
        });

        test("should return CODE_000000 : potalGroupPlanID is number", async () => {
            // check plan id before request
            const currentLineGroup = await LineGroupsEntity.findOne({
                where: {
                    groupId: testdata.lineGroupId,
                },
            });
            expect(currentLineGroup).toBeDefined();
            expect(currentLineGroup).not.toBeNull();
            expect(currentLineGroup.planId).toEqual(
                parseInt(testdata.groupPlanId_pre),
            );

            param.potalGroupPlanID = parseInt(testdata.groupPlanId, 10) as any;
            const response = await instance.service(param, false, null);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );
            await checkServiceOrder(true, true);

            const lineGroup = await LineGroupsEntity.findOne({
                where: {
                    groupId: testdata.lineGroupId,
                },
            });
            expect(lineGroup).toBeDefined();
            expect(lineGroup).not.toBeNull();
            // plan id updated
            expect(lineGroup.planId).toEqual(parseInt(testdata.groupPlanId));
        });
    });

    describe("OK cases (secondary TPC fail)", () => {
        test("should return CODE_000000 even if secondary TPC address is invalid", async () => {
            jest.spyOn(
                TenantManage.prototype,
                "checkTpcConnection",
            ).mockResolvedValue([true, "TpcDestIpAddress", "invalidAddress"]);

            nock(configData.soapApiUrl)
                .post("") // primary TPC
                .reply(200, SOAPTestHelper.simpleResponse("OK"))
                .post("") // secondary TPC
                .reply(200, SOAPTestHelper.simpleResponse("NG"));

            const response = await instance.service(param, false, null);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );
            await checkServiceOrder(true, true);
        });

        test("should return CODE_000000 even if secondary TPC response NG", async () => {
            jest.spyOn(
                TenantManage.prototype,
                "checkTpcConnection",
            ).mockResolvedValue([true, "TpcDestIpAddress", "TpcDestIpAddress"]);

            nock(configData.soapApiUrl)
                .post("") // primary TPC
                .reply(200, SOAPTestHelper.simpleResponse("OK"))
                .post("") // secondary TPC
                .reply(200, SOAPTestHelper.simpleResponse("NG"));

            const response = await instance.service(param, false, null);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );
            await checkServiceOrder(true, true);
        });

        test("should return CODE_000000 even if failed to send request to secondary TPC", async () => {
            jest.spyOn(
                TenantManage.prototype,
                "checkTpcConnection",
            ).mockResolvedValue([true, "TpcDestIpAddress", "TpcDestIpAddress"]);

            nock(configData.soapApiUrl)
                .post("") // primary TPC
                .reply(200, SOAPTestHelper.simpleResponse("OK"));

            const response = await instance.service(param, false, null);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );
            await checkServiceOrder(true, true);
        });
    });

    describe("NG cases", () => {
        test("should return CODE_200101 if order type cannot be determined", async () => {
            param.reserve_date = "some date";
            param.reserve_flag = true;
            param.reserve_soId = undefined;

            const response = await instance.service(param, false, null);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_200101,
            );
            await checkServiceOrder(true, false);
        });

        test.each([
            [undefined, true],
            ["abc1234", true], // non-numeric
            ["1234567890", false], // DB limit is 9 characters
        ])(
            "should return CODE_200102 if lineGroupId is invalid (%s)",
            async (groupId, soCreated) => {
                param.lineGroupId = groupId;
                const response = await instance.service(param, false, null);
                expect(response?.jsonBody).toBeDefined();
                expect(response.jsonBody.responseHeader).toBeDefined();
                expect(response.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_200102,
                );
                await checkServiceOrder(soCreated, false);
            },
        );

        test.each([undefined, "abc12", "123456"])(
            "should return CODE_200102 if groupPlanId_pre is invalid",
            async (planIdPre) => {
                param.potalGroupPlanID_pre = planIdPre as string;
                const response = await instance.service(param, false, null);
                expect(response?.jsonBody).toBeDefined();
                expect(response.jsonBody.responseHeader).toBeDefined();
                expect(response.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_200102,
                );
                await checkServiceOrder(true, false);
            },
        );

        test.each([undefined, "abc12", "123456"])(
            "should return CODE_200102 if groupPlanId is invalid",
            async (planId) => {
                param.potalGroupPlanID = planId as string;
                const response = await instance.service(param, false, null);
                expect(response?.jsonBody).toBeDefined();
                expect(response.jsonBody.responseHeader).toBeDefined();
                expect(response.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_200102,
                );
                await checkServiceOrder(true, false);
            },
        );

        test("should return CODE_200102 if planId before and after are the same", async () => {
            param.potalGroupPlanID = testdata.groupPlanId_pre;
            const response = await instance.service(param, false, null);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_200102,
            );
            await checkServiceOrder(true, false);
        });

        test("should return CODE_200102 if reserve date is invalid [予約前オーダ]", async () => {
            param.reserve_date = "2021-01-01 00:01";

            const response = await instance.service(param, false, null);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_200102,
            );
            await checkServiceOrder(false, false); // bad reserve date
        });

        test("should return CODE_200102 if reserve date is invalid [予約実行オーダ]", async () => {
            param.reserve_date = "2021-01-01 00:01";
            param.reserve_flag = true;
            param.reserve_soId = "soid123";

            const response = await instance.service(param, false, null);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_200102,
            );
            await checkServiceOrder(false, false); // bad reserve date
        });

        test.each([
            orderTypeCases[Constants.ORDER_TYPE_0],
            orderTypeCases[Constants.ORDER_TYPE_2],
        ])(
            "should return CODE_200103 if groupPlanChangeNgTime config is invalid [%s]",
            async (_title, orderCase) => {
                Object.defineProperty(instance, "groupPlanChangeNgTime", {
                    value: "invalid",
                });
                updateParamForOrderCase(param, orderCase);

                const response = await instance.service(param, false, null);
                expect(response?.jsonBody).toBeDefined();
                expect(response.jsonBody.responseHeader).toBeDefined();
                expect(response.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_200103,
                );
                await checkServiceOrder(true, false);
            },
        );

        test(`should return CODE_200106 if groupPlanChangeNgTime config is invalid [${
            orderTypeCases[Constants.ORDER_TYPE_1][0]
        }]`, async () => {
            const orderCase = orderTypeCases[Constants.ORDER_TYPE_1][1];
            Object.defineProperty(instance, "groupPlanChangeNgTime", {
                value: "invalid",
            });
            updateParamForOrderCase(param, orderCase);

            const response = await instance.service(param, false, null);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_200106,
            );
            await checkServiceOrder(true, false);
        });

        test.each([
            // orderTypeCases[Constants.ORDER_TYPE_0],
            orderTypeCases[Constants.ORDER_TYPE_2],
        ])(
            "should return CODE_200104 if receivedDate is NG [%s]",
            async (_title, orderCase) => {
                updateParamForOrderCase(param, orderCase);
                jest.spyOn(Check, "checkPlanChangeNgTime").mockReturnValue(
                    false,
                );

                const response = await instance.service(param, false, null);
                expect(response?.jsonBody).toBeDefined();
                expect(response.jsonBody.responseHeader).toBeDefined();
                expect(response.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_200104,
                );
                await checkServiceOrder(true, false);
            },
        );

        test(`should return CODE_200107 if receivedDate is NG [${
            orderTypeCases[Constants.ORDER_TYPE_1][0]
        }]`, async () => {
            const orderCase = orderTypeCases[Constants.ORDER_TYPE_1][1];
            updateParamForOrderCase(param, orderCase);
            jest.spyOn(Check, "checkPlanChangeNgTime").mockReturnValue(false);

            const response = await instance.service(param, false, null);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_200107,
            );
            await checkServiceOrder(true, false);
        });

        test("should return CODE_200108 if received date and reserve date are NG", async () => {
            // reserve date <= received date
            param.reserve_date = format(
                addDays(getDateNowForReservationTest(configData), -1),
                "yyyy/MM/dd HH:mm",
            );
            param.reserve_flag = false;
            const response = await instance.service(param, false, null);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_200108,
            );
            await checkServiceOrder(true, false);
        });

        test("should return CODE_200109 if reservationDateExecutionUnits config is invalid", async () => {
            // part of 予約前オーダ
            param.reserve_date = testdata.reserve_date;
            param.reserve_flag = false;
            Object.defineProperty(instance, "reservationDateExecutionUnits", {
                value: "invalid",
            });
            const response = await instance.service(param, false, null);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_200109,
            );
            await checkServiceOrder(true, false);
        });

        test("should return CODE_200110 if reserveDate is past reservationDateExecutionUnits", async () => {
            // part of 予約前オーダ
            param.reserve_date = format(
                addDays(
                    new Date(),
                    parseInt(configData.reservationDateExecutionUnits, 10) + 1,
                ),
                "yyyy/MM/dd HH:mm",
            );
            param.reserve_flag = false;
            jest.spyOn(
                Check,
                "checkReservationDateExecutionUnits",
            ).mockReturnValue(false);

            const response = await instance.service(param, false, null);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_200110,
            );
            await checkServiceOrder(true, false);
        });

        test("should return CODE_200111 if reservationsLimitDays config is invalid", async () => {
            // part of 予約前オーダ
            param.reserve_date = testdata.reserve_date;
            param.reserve_flag = false;
            Object.defineProperty(instance, "reservationsLimitDays", {
                value: "invalid",
            });
            const response = await instance.service(param, false, null);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_200111,
            );
            await checkServiceOrder(true, false);
        });

        test("should return CODE_200112 if reserveDate is past reservationsLimitDays", async () => {
            // part of 予約前オーダ
            param.reserve_date = format(
                addDays(
                    getDateNowForReservationTest(configData),
                    parseInt(configData.reservationsLimitDays, 10) + 1,
                ),
                "yyyy/MM/dd HH:mm",
            );
            param.reserve_flag = false;
            const response = await instance.service(param, false, null);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_200112,
            );
            await checkServiceOrder(true, false);
        });

        test.each([
            orderTypeCases[Constants.ORDER_TYPE_0],
            orderTypeCases[Constants.ORDER_TYPE_1],
        ])(
            "should return CODE_200105 if there is existing reservation order for the linegroup [%s]",
            async (_title, orderCase) => {
                param.lineGroupId = testdata.lineGroupIdReserved;
                updateParamForOrderCase(param, orderCase);
                const response = await instance.service(param, false, null);
                expect(response?.jsonBody).toBeDefined();
                expect(response.jsonBody.responseHeader).toBeDefined();
                expect(response.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_200105,
                );
                await checkServiceOrder(true, false);
            },
        );

        test("should return CODE_200201 if tenantId is invalid", async () => {
            param.tenantId = "TST099";
            const response = await instance.service(param, false, null);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_200201,
            );
            const so = await checkServiceOrder(true, false, param.tenantId);
        });

        test("should return CODE_200201 if failed to query tenant data", async () => {
            jest.spyOn(
                APICommonDAO.prototype,
                "getTenantsEntity",
            ).mockRejectedValue(
                new ConnectionTimedOutError(Error("test timeout")),
            );

            const response = await instance.service(param, false, null);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_200201,
            );
            await checkServiceOrder(false, false); // bad tenant
        });

        test("should return CODE_200301 if line group not found in DB", async () => {
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getLineGroupsLock",
            ).mockResolvedValue(null);
            const response = await instance.service(param, false, null);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_200301,
            );
            await checkServiceOrder(true, false);
        });

        test("should return CODE_200301 if failed to get line group lock", async () => {
            Object.defineProperty(instance, "LOCK_WAIT_MILLISEC", {
                value: 1,
            });
            let tx: Transaction;
            const aPILinesGroupDAO = new APILinesGroupDAO(
                DefaultRequest,
                DefaultContext,
            );

            try {
                tx = await sequelize.transaction();
                await aPILinesGroupDAO.getLineGroupsLock(
                    param.lineGroupId,
                    tx,
                    1,
                    true,
                );

                const response = await instance.service(param, false, null);
                expect(response?.jsonBody).toBeDefined();
                expect(response.jsonBody.responseHeader).toBeDefined();
                expect(response.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_200301,
                );
            } finally {
                await tx.rollback();
            }
        });

        test("should return CODE_200301 if DB query failed to get line group", async () => {
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getLineGroupsLock",
            ).mockRejectedValue(
                new ConnectionTimedOutError(Error("test timeout")),
            );
            const response = await instance.service(param, false, null);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_200301,
            );
            await checkServiceOrder(false, false); // SQL exception does not create service order
        });

        test("should return CODE_200301 for other error during acquiring line group lock", async () => {
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getLineGroupsLock",
            ).mockRejectedValue(new Error("test error"));
            const response = await instance.service(param, false, null);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_200301,
            );
            await checkServiceOrder(true, false);
        });

        test("should return CODE_200301 when line group is not found [予約前オーダ]", async () => {
            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_1][1],
            );
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getLineGroupsLock",
            ).mockResolvedValue(null);

            const response = await instance.service(param, false, null);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_200301,
            );
            await checkServiceOrder(true, false);
        });

        test("should return CODE_200301 if failed to query line group data [予約前オーダ]", async () => {
            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_1][1],
            );
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getLineGroupsLock",
            ).mockRejectedValue(
                new ConnectionTimedOutError(Error("test timeout")),
            );

            const response = await instance.service(param, false, null);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_200301,
            );
            await checkServiceOrder(false, false); // SQL exception does not create service order
        });

        test("should return CODE_200301 for other error during acquiring line group lock [予約前オーダ]", async () => {
            updateParamForOrderCase(
                param,
                orderTypeCases[Constants.ORDER_TYPE_1][1],
            );
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getLineGroupsLock",
            ).mockRejectedValue(new Error("test error"));

            const response = await instance.service(param, false, null);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_200301,
            );
            await checkServiceOrder(true, false);
        });

        test("should return CODE_200401 if tenantId from param does not match tenantId from line group", async () => {
            param.tenantId = testdata.mismatchTenant;
            const response = await instance.service(param, false, null);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_200401,
            );
            await checkServiceOrder(true, false, param.tenantId);
        });

        test("should return CODE_200501 if groupPlanId_pre from param does not match planId from line group", async () => {
            param.potalGroupPlanID_pre = testdata.mismatchGroupPlanId_pre;
            const response = await instance.service(param, false, null);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_200501,
            );
            await checkServiceOrder(true, false);
        });

        test("should return CODE_200601 if failed to query group plan data", async () => {
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getServicePlanId",
            ).mockRejectedValue(
                new ConnectionTimedOutError(Error("test timeout")),
            );

            const response = await instance.service(param, false, null);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_200601,
            );
            await checkServiceOrder(false, false); // SQL exception does not create service order
        });

        test("should return CODE_200601 if group plan data is not found", async () => {
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getServicePlanId",
            ).mockResolvedValue(null);

            const response = await instance.service(param, false, null);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_200601,
            );
            await checkServiceOrder(true, false);
        });

        test("should return CODE_200701 if failed query tenant group plan data", async () => {
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getGroupPlanId",
            ).mockRejectedValue(
                new ConnectionTimedOutError(Error("test timeout")),
            );

            const response = await instance.service(param, false, null);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_200701,
            );
            await checkServiceOrder(false, false); // SQL exception does not create service order
        });

        test("should return CODE_200701 if tenant group plan data is not found", async () => {
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getGroupPlanId",
            ).mockResolvedValue(null);

            const response = await instance.service(param, false, null);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_200701,
            );
            await checkServiceOrder(true, false);
        });

        test("should return CODE_200801 when failed to query tpc connection data", async () => {
            jest.spyOn(
                TenantManage.prototype,
                "checkTpcConnection",
            ).mockRejectedValue(
                new ConnectionTimedOutError(Error("test timeout")),
            );

            const response = await instance.service(param, false, null);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_200801,
            );
            await checkServiceOrder(false, false); // SQL exception does not create service order
        });

        test("should return CODE_200801 if tpc connection check is NG", async () => {
            jest.spyOn(
                TenantManage.prototype,
                "checkTpcConnection",
            ).mockResolvedValue([
                false,
                "TpcDestIpAddress",
                "TpcDestIpAddress",
            ]);

            const response = await instance.service(param, false, null);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_200801,
            );
            await checkServiceOrder(true, false);
        });

        test("should return CODE_200901 when failed calling SOAP API", async () => {
            nock(configData.soapApiUrl).post("").reply(500, "").persist();

            const response = await instance.service(param, false, null);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_200901,
            );
            await checkServiceOrder(true, false);
        });

        test("should return CODE_99999 when unexpected error happened during SOAP API call", async () => {
            jest.spyOn(
                SOAPCommon.prototype,
                "callWebSoapApi",
            ).mockRejectedValue(new Error("test error"));

            const response = await instance.service(param, false, null);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_999999,
            );
            await checkServiceOrder(false, false);
        });

        test("should return CODE_200901 when TPC response with NG", async () => {
            nock(configData.soapApiUrl)
                .post("")
                .reply(
                    200,
                    SOAPTestHelper.simpleResponse("NG", "some error message"),
                )
                .persist();

            const response = await instance.service(param, false, null);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_200901,
            );
            await checkServiceOrder(true, false);
        });

        test("should return CODE_201001 if failed to update line group's plan", async () => {
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, SOAPTestHelper.simpleResponse("OK"))
                .persist();
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "updateLineGroupsPlan",
            ).mockRejectedValue(
                new ConnectionTimedOutError(Error("test timeout")),
            );

            const response = await instance.service(param, false, null);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_201001,
            );
            await checkServiceOrder(false, false); // SQL exception does not create service order
        });

        test("should return CODE_201001 when unexpected error happened during updating line group's plan", async () => {
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, SOAPTestHelper.simpleResponse("OK"))
                .persist();
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "updateLineGroupsPlan",
            ).mockRejectedValue(new Error("test error"));

            const response = await instance.service(param, false, null);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_201001,
            );
            await checkServiceOrder(true, false);
        });

        test("should return CODE_999999 when unexpected error happened", async () => {
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getGroupPlanId",
            ).mockRejectedValue(new Error("test error"));

            const response = await instance.service(param, false, null);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_999999,
            );
            await checkServiceOrder(false, false); // no service order created
        });
    });
});
