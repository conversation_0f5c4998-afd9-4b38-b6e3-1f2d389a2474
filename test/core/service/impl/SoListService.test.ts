import { addDays, formatDate } from "date-fns";
import { ConnectionTimedOutError, Sequelize } from "sequelize";

import "@/types/string.extension";

import {
    describeWithDB,
    generateProcessID,
    useFasterRetries,
} from "../../../testing/TestHelper";
import DefaultContext from "../../../testing/DefaultContext";
import DefaultRequest from "../../../testing/DefaultRequest";

import AppConfig from "@/appconfig";
import APISoDAO from "@/core/dao/APISoDAO";

import ResultCdConstants from "@/core/constant/ResultCdConstants";

import ResponseHeader from "@/core/dto/ResponseHeader";
import SoListInputDto from "@/core/dto/SolistInputDto";

import ServiceOrdersEntity from "@/core/entity/ServiceOrdersEntity";

import { usePsql } from "@/database/psql";

import SolistService from "@/core/service/impl/SolistService";
import CheckUtil from "@/core/common/CheckUtil";
import { expect, jest } from "@jest/globals";



describeWithDB("/core/service/impl/SolistService", () => {
    let sequelize: Sequelize;
    let instance: SolistService;
    const testDate = addDays(new Date(), -1);

    const validTestdata = {
        tenantId: "TST000",
        serviceOrderId: "AP000898672450",
        lineId: "08024020000",
        orderDate: testDate,
        reserveDate: null,
        execDate: testDate,
        functionType: "16",
        orderType: "回線廃止",
        orderStatus: "完了",
        execTenantId: "ZZZ000",
        execUserId: "ZZZ@test0",
    };

    const request = {
        requestHeader: {
            sequenceNo: "1234567",
            senderSystemId: "0001",
            apiKey: "",
            functionType: "07",
        },
        tenantId: validTestdata.tenantId,
        // lineNoKey: testdata.valid.lineOK,
        // serviceOrderIdKey:
        // tenantIdKey:
        // orderStatusKey:
        // orderTypeKey:
        // orderDateBeginKey:
        // orderDateEndKey:
        // execDateBeginKey:
        // execDateEndKey:
    } as SoListInputDto;

    const responseHeader = {
        sequenceNo: request.requestHeader.sequenceNo,
        receivedDate: formatDate(new Date(), "yyyy/MM/dd HH:mm:ss"),
        processCode: ResultCdConstants.CODE_000000,
        apiProcessID: generateProcessID(),
    } as ResponseHeader;

    const clearTestData = async () => {
        await ServiceOrdersEntity.destroy({
            where: {
                tenantId: validTestdata.tenantId,
                lineId: validTestdata.lineId,
            },
        });
    };

    beforeAll(async () => {
        await AppConfig.loadCoreMvnoConfig(DefaultContext);
        sequelize = await usePsql();
        await clearTestData();
    });

    afterAll(async () => {
        await sequelize.close();
    });

    beforeEach(async () => {
        useFasterRetries();

        DefaultContext.jsonBody = Object.assign({}, request);
        DefaultContext.responseHeader = Object.assign({}, responseHeader);

        instance = new SolistService(
            DefaultRequest,
            DefaultContext,
        );

        await ServiceOrdersEntity.create(validTestdata);
    });

    afterEach(async () => {
        await clearTestData();
        jest.restoreAllMocks();
        jest.clearAllMocks();
    });


    describe("Valid test cases", () => {
        it("should return CODE_000000 code and SoList", async() => {
            const result = await instance.service(request);
            expect(result.responseHeader).toEqual(responseHeader);
            expect(result[validTestdata.serviceOrderId]).not.toBeNull();
            const soList = result[validTestdata.serviceOrderId];
            if ("line_id" in soList) {
                expect(soList.line_id).toEqual(validTestdata.lineId);
            } else {
                expect("line id not exist in so list").toBeFalsy();
            }
        });

        it("should return CODE_000000 code and SoList when using searchCount", async() => {
            const result = await instance.service(request, 1);
            expect(result.responseHeader).toEqual(responseHeader);
            expect(result[validTestdata.serviceOrderId]).not.toBeNull();
            const soList = result[validTestdata.serviceOrderId];
            if ("line_id" in soList) {
                expect(soList.line_id).toEqual(validTestdata.lineId);
            } else {
                expect("line id not exist in so list").toBeFalsy();
            }
        });

        it("should return CODE_000000 code and no SoList in payload when using different tenantId", async() => {
            const modRequest = Object.assign({}, request);
            modRequest.tenantId = "BON000";

            const result = await instance.service(modRequest);
            expect(result.responseHeader).toEqual(responseHeader);
            expect(result).not.toHaveProperty('soList');
        });
    });

    describe("Invalid test cases", () => {
        it("should return CODE_160101 when tenantIdKey is greater than 10", async() => {
            const invalidRequest = Object.assign({}, request);
            invalidRequest.tenantIdKey = "dddddafdadfadf456a4dasdfd";
            const invalidResponseHeader = Object.assign({}, responseHeader);
            invalidResponseHeader.processCode = ResultCdConstants.CODE_160101;

            const result = await instance.service(invalidRequest);
            expect(result.responseHeader).toEqual(invalidResponseHeader);
        });

        it("should return CODE_160702 when apiSoDao.getSoList throws an error", async() => {
            const invalidResponseHeader = Object.assign({}, responseHeader);
            invalidResponseHeader.processCode = ResultCdConstants.CODE_160702;

            const apiSoDao = new APISoDAO(DefaultRequest, DefaultContext);
            jest.spyOn(apiSoDao, "getSoList").mockRejectedValue(
                new ConnectionTimedOutError(new Error("Connection timed out")),
            );
            Object.defineProperty(instance, "apiSoDao", {
                value: apiSoDao,
            });

            const result = await instance.service(request);
            expect(result.responseHeader).toEqual(invalidResponseHeader);
        });

        it("should return CODE_160601 when tenantIdKey and tenantId don't match", async() => {
            const invalidRequest = Object.assign({}, request);
            invalidRequest.tenantIdKey = "dddd";
            const invalidResponseHeader = Object.assign({}, responseHeader);
            invalidResponseHeader.processCode = ResultCdConstants.CODE_160601;

            const result = await instance.service(invalidRequest);
            expect(result.responseHeader).toEqual(invalidResponseHeader);
        });

        it("should return CODE_160101 when serviceOrderIdKey is greater than 15 characters", async() => {
            const invalidRequest = Object.assign({}, request);
            invalidRequest.serviceOrderIdKey = validTestdata.serviceOrderId + "89abcde33";
            const invalidResponseHeader = Object.assign({}, responseHeader);
            invalidResponseHeader.processCode = ResultCdConstants.CODE_160101;

            const result = await instance.service(invalidRequest);
            expect(result.responseHeader).toEqual(invalidResponseHeader);
        });

        it("should return CODE_160101 when serviceOrderIdKey contains invalid characters", async() => {
            const invalidRequest = Object.assign({}, request);
            invalidRequest.serviceOrderIdKey = "123456789abcde%";
            const invalidResponseHeader = Object.assign({}, responseHeader);
            invalidResponseHeader.processCode = ResultCdConstants.CODE_160101;

            const result = await instance.service(invalidRequest);
            expect(result.responseHeader).toEqual(invalidResponseHeader);
        });

        it("should return CODE_160301 when serviceOrderIdKey and lineNo are set", async() => {
            const invalidRequest = Object.assign({}, request);
            invalidRequest.serviceOrderIdKey = validTestdata.serviceOrderId;
            invalidRequest.lineNoKey = validTestdata.lineId;

            const invalidResponseHeader = Object.assign({}, responseHeader);
            invalidResponseHeader.processCode = ResultCdConstants.CODE_160301;

            const result = await instance.service(invalidRequest);
            expect(result.responseHeader).toEqual(invalidResponseHeader);
        });

        it("should return CODE_160101 when lineNo length is greater than 14", async() => {
            const invalidRequest = Object.assign({}, request);
            invalidRequest.lineNoKey = validTestdata.lineId + "123456789";
            const invalidResponseHeader = Object.assign({}, responseHeader);
            invalidResponseHeader.processCode = ResultCdConstants.CODE_160101;

            const result = await instance.service(invalidRequest);
            expect(result.responseHeader).toEqual(invalidResponseHeader);
        });

        it("should return CODE_160101 when lineNo containes some non numerical characters", async() => {
            const invalidRequest = Object.assign({}, request);
            invalidRequest.lineNoKey = "12345d789";
            const invalidResponseHeader = Object.assign({}, responseHeader);
            invalidResponseHeader.processCode = ResultCdConstants.CODE_160101;

            const result = await instance.service(invalidRequest);
            expect(result.responseHeader).toEqual(invalidResponseHeader);
        });

        it("should return CODE_160101 when orderStatus contains invalid status", async() => {
            const invalidRequest = Object.assign({}, request);
            invalidRequest.orderStatusKey = "wrongstatus";
            const invalidResponseHeader = Object.assign({}, responseHeader);
            invalidResponseHeader.processCode = ResultCdConstants.CODE_160101;

            const result = await instance.service(invalidRequest);
            expect(result.responseHeader).toEqual(invalidResponseHeader);
        });

        it("should return CODE_160201 when orderDateBeginKey has wrong format", async() => {
            const invalidRequest = Object.assign({}, request);
            invalidRequest.orderDateBeginKey = "wrongdate";
            invalidRequest.orderDateEndKey = "2024/10/01";
            const invalidResponseHeader = Object.assign({}, responseHeader);
            invalidResponseHeader.processCode = ResultCdConstants.CODE_160201;

            const result = await instance.service(invalidRequest);
            expect(result.responseHeader).toEqual(invalidResponseHeader);
        });

        it("should return CODE_160201 when orderDateEndKey has wrong format", async() => {
            const invalidRequest = Object.assign({}, request);
            invalidRequest.orderDateBeginKey = "2024/10/01";
            invalidRequest.orderDateEndKey = "wrongdate";
            const invalidResponseHeader = Object.assign({}, responseHeader);
            invalidResponseHeader.processCode = ResultCdConstants.CODE_160201;

            const result = await instance.service(invalidRequest);
            expect(result.responseHeader).toEqual(invalidResponseHeader);
        });

        it("should return CODE_160202 when orderDateEndKey and orderDateEndKey have no difference", async() => {
            const invalidRequest = Object.assign({}, request);
            invalidRequest.orderDateBeginKey = "2024/10/02";
            invalidRequest.orderDateEndKey = "2024/10/01";
            const invalidResponseHeader = Object.assign({}, responseHeader);
            invalidResponseHeader.processCode = ResultCdConstants.CODE_160202;

            const result = await instance.service(invalidRequest);
            expect(result.responseHeader).toEqual(invalidResponseHeader);
        });

        it("should return CODE_160101 when orderDateBeginKey or orderDateEndKey is not set", async() => {
            const invalidRequest = Object.assign({}, request);
            invalidRequest.orderDateEndKey = "2024/10/01";
            const invalidResponseHeader = Object.assign({}, responseHeader);
            invalidResponseHeader.processCode = ResultCdConstants.CODE_160101;

            const result = await instance.service(invalidRequest);
            expect(result.responseHeader).toEqual(invalidResponseHeader);
        });

        it("should return CODE_160201 when execDateBeginKey has wrong format", async() => {
            const invalidRequest = Object.assign({}, request);
            invalidRequest.execDateBeginKey = "wrongdate";
            invalidRequest.execDateEndKey = "2024/10/01";
            const invalidResponseHeader = Object.assign({}, responseHeader);
            invalidResponseHeader.processCode = ResultCdConstants.CODE_160201;

            const result = await instance.service(invalidRequest);
            expect(result.responseHeader).toEqual(invalidResponseHeader);
        });

        it("should return CODE_160201 when execDateEndKey has wrong format", async() => {
            const invalidRequest = Object.assign({}, request);
            invalidRequest.execDateBeginKey = "2024/10/01";
            invalidRequest.execDateEndKey = "wrongdate";
            const invalidResponseHeader = Object.assign({}, responseHeader);
            invalidResponseHeader.processCode = ResultCdConstants.CODE_160201;

            const result = await instance.service(invalidRequest);
            expect(result.responseHeader).toEqual(invalidResponseHeader);
        });

        it("should return CODE_160202 when execDateBeginKey and execDateEndKey have no difference", async() => {
            const invalidRequest = Object.assign({}, request);
            invalidRequest.execDateBeginKey = "2024/10/02";
            invalidRequest.execDateEndKey = "2024/10/01";
            const invalidResponseHeader = Object.assign({}, responseHeader);
            invalidResponseHeader.processCode = ResultCdConstants.CODE_160202;

            const result = await instance.service(invalidRequest);
            expect(result.responseHeader).toEqual(invalidResponseHeader);
        });

        it("should return CODE_160101 when execDateBeginKey or execDateEndKey is not set", async() => {
            const invalidRequest = Object.assign({}, request);
            invalidRequest.execDateEndKey = "2024/10/01";
            const invalidResponseHeader = Object.assign({}, responseHeader);
            invalidResponseHeader.processCode = ResultCdConstants.CODE_160101;

            const result = await instance.service(invalidRequest);
            expect(result.responseHeader).toEqual(invalidResponseHeader);
        });

        it("should return CODE_160101 when orderTypeKey is wrong", async() => {
            const invalidRequest = Object.assign({}, request);
            invalidRequest.orderTypeKey = "wrongtype";
            const invalidResponseHeader = Object.assign({}, responseHeader);
            invalidResponseHeader.processCode = ResultCdConstants.CODE_160101;

            const result = await instance.service(invalidRequest);
            expect(result.responseHeader).toEqual(invalidResponseHeader);
        });

        it("should return CODE_160501 when apiSoDao.getTenantsList throws an error", async() => {
            const invalidResponseHeader = Object.assign({}, responseHeader);
            invalidResponseHeader.processCode = ResultCdConstants.CODE_160501;

            const apiSoDao = new APISoDAO(DefaultRequest, DefaultContext);
            jest.spyOn(apiSoDao, "getTenantsList").mockRejectedValue(
                new ConnectionTimedOutError(new Error("Connection timed out")),
            );
            Object.defineProperty(instance, "apiSoDao", {
                value: apiSoDao,
            });

            const result = await instance.service(request);
            expect(result.responseHeader).toEqual(invalidResponseHeader);
        });

        it("should return CODE_160501 when tenantId is wrong", async() => {
            const invalidRequest = Object.assign({}, request);
            invalidRequest.tenantId = "wrongtenant";
            const invalidResponseHeader = Object.assign({}, responseHeader);
            invalidResponseHeader.processCode = ResultCdConstants.CODE_160501;

            const result = await instance.service(invalidRequest);
            expect(result.responseHeader).toEqual(invalidResponseHeader);
        });

        it("should return CODE_160601 when tenantId is different to tenantIdKey", async() => {
            const invalidRequest = Object.assign({}, request);
            invalidRequest.tenantIdKey = "BON000";
            const invalidResponseHeader = Object.assign({}, responseHeader);
            invalidResponseHeader.processCode = ResultCdConstants.CODE_160601;

            const result = await instance.service(invalidRequest);
            expect(result.responseHeader).toEqual(invalidResponseHeader);
        });

        it("should return CODE_160702 if error in SQL", async() => {
            const invalidRequest = Object.assign({}, request);
            invalidRequest.tenantIdKey = validTestdata.tenantId;
            const invalidResponseHeader = Object.assign({}, responseHeader);
            invalidResponseHeader.processCode = ResultCdConstants.CODE_160702;
            jest.spyOn(APISoDAO.prototype, 'getSoList').mockImplementation(() => {
                throw new Error('booom');
            });

            const result = await instance.service(invalidRequest);
            expect(result.responseHeader).toEqual(invalidResponseHeader);
        });

        it("should return CODE_999999 when something goes wrong catches error", async() => {
            const invalidRequest = Object.assign({}, request);
            invalidRequest.tenantIdKey = validTestdata.tenantId;
            const invalidResponseHeader = Object.assign({}, responseHeader);
            invalidResponseHeader.processCode = ResultCdConstants.CODE_999999;

            jest.spyOn(CheckUtil, 'checkIsNotNull').mockImplementation(() => {
                throw new Error('booom');
            });

            const result = await instance.service(invalidRequest);
            expect(result.responseHeader).toEqual(invalidResponseHeader);
        });
    });

});