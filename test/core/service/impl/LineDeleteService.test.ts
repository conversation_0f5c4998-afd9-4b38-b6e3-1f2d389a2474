import { add, formatDate } from "date-fns";
import { ConnectionTimedOutError, Sequelize } from "sequelize";

import {
    describeWithDB,
    generateProcessID,
    generateSOID,
    useFasterRetries,
} from "../../../testing/TestHelper";
import DefaultContext from "../../../testing/DefaultContext";
import DefaultRequest from "../../../testing/DefaultRequest";

import ResultCdConstants from "@/core/constant/ResultCdConstants";

import AppConfig from "@/appconfig";
import MvnoUtil from "@/core/common/MvnoUtil";
import TenantManage from "@/core/common/TenantManage";

import APICommonDAO from "@/core/dao/APICommonDAO";
import APILinesDAO from "@/core/dao/APILinesDAO";
import LineDeleteInputDto from "@/core/dto/LineDeleteInputDto";
import ResponseHeader from "@/core/dto/ResponseHeader";

import { LinesEntity } from "@/core/entity/LinesEntity";
import LineTenantsEntity from "@/core/entity/LineTenantsEntity";
import ServiceOrdersEntity from "@/core/entity/ServiceOrdersEntity";
import CoreSwimmyApiLog from "@/models/coreSwimmyApiLog";

import { usePsql } from "@/database/psql";
import { disconnectMongo, useMongo } from "@/database/mongo";

import LineDeleteService from "@/core/service/impl/LineDeleteService";

/**
 * Unit tests for cases which are not covered by LineDeleteHandler.test.ts
 */
describeWithDB("core/service/impl/LineDeleteService", () => {
    let sequelize: Sequelize;
    let instance: LineDeleteService;

    const reserve_date = add(new Date(), { days: 7 });
    const testdata = {
        lineOK: "08024042410",
        hankuroLine: "08024042411",
        disabledLine: "08024042412",
        notExistLine: "08024042413",
    };
    const request = {
        requestHeader: {
            sequenceNo: "1234567",
            senderSystemId: "0001",
            apiKey: "",
            functionType: "52",
        },
        targetSoId: generateSOID(),
        tenantId: "OPF000",
        targetTenantId: "TST000",
        lineNo: testdata.lineOK,
        mnpOutFlag: "0",
        csvUnnecessaryFlag: "0",
        reserve_date: formatDate(reserve_date, "yyyy/MM/dd"),
    } as LineDeleteInputDto;
    const responseHeader = {
        sequenceNo: request.requestHeader.sequenceNo,
        receivedDate: formatDate(new Date(), "yyyy/MM/dd HH:mm:ss"),
        processCode: ResultCdConstants.CODE_000000,
        apiProcessID: generateProcessID(),
    } as ResponseHeader;

    beforeAll(async () => {
        await AppConfig.loadCoreMvnoConfig(DefaultContext);
        sequelize = await usePsql();
        await useMongo(DefaultContext);
        // insert a line number
        await Promise.all([
            LinesEntity.create({
                lineId: testdata.lineOK,
                tenantId: request.targetTenantId,
                lineStatus: "01",
                simFlag: false,
            }),
            LinesEntity.create({
                lineId: testdata.disabledLine,
                tenantId: request.targetTenantId,
                lineStatus: "03",
                simFlag: false,
            }),
            LinesEntity.create({
                lineId: testdata.hankuroLine,
                tenantId: request.targetTenantId,
                lineStatus: "01",
                simFlag: true,
            }),
        ]);
        // register to line tenant
        await Promise.all(
            [testdata.lineOK, testdata.disabledLine, testdata.hankuroLine].map(
                async (lineId) => {
                    await LineTenantsEntity.create({
                        lineId,
                        tenantId: request.targetTenantId,
                    });
                },
            ),
        );
    });

    afterAll(async () => {
        // delete added line numbers
        await LineTenantsEntity.destroy({
            where: {
                lineId: [
                    testdata.lineOK,
                    testdata.disabledLine,
                    testdata.hankuroLine,
                ],
            },
        });
        await LinesEntity.destroy({
            where: {
                lineId: [
                    testdata.lineOK,
                    testdata.disabledLine,
                    testdata.hankuroLine,
                ],
            },
        });
        await sequelize.close();
        await disconnectMongo(DefaultContext);
    });

    beforeEach(() => {
        useFasterRetries();

        // reset context to default
        DefaultContext.jsonBody = Object.assign({}, request);
        DefaultContext.responseHeader = Object.assign({}, responseHeader);
        instance = new LineDeleteService(DefaultRequest, DefaultContext);
    });

    afterEach(async () => {
        // delete service order (if created)
        await Promise.all([
            ServiceOrdersEntity.destroy({
                where: {
                    serviceOrderId: responseHeader.apiProcessID,
                },
            }),
            CoreSwimmyApiLog.deleteMany({
                requestOrderId: responseHeader.apiProcessID,
            }),
        ]);

        jest.restoreAllMocks();
        jest.clearAllMocks();
    });

    describe("service (NG result)", () => {
        it("should return CODE_520102 if getLineInfoByLines failed", async () => {
            jest.spyOn(LinesEntity, "findOne").mockRejectedValue(
                new ConnectionTimedOutError(new Error("timeout")),
            );

            const result = await instance.service(
                request,
                MvnoUtil.getClientIPAddress(DefaultRequest),
            );

            expect(result.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_520102,
            );
        });

        it("should return CODE_520103 if getAbolitionOrder failed", async () => {
            const apiLinesDao = new APILinesDAO(DefaultRequest, DefaultContext);
            jest.spyOn(apiLinesDao, "getAbolitionOrder").mockRejectedValue(
                new ConnectionTimedOutError(new Error("timeout")),
            );
            Object.defineProperty(instance, "apiLinesDao", {
                value: apiLinesDao,
            });

            const result = await instance.service(
                request,
                MvnoUtil.getClientIPAddress(DefaultRequest),
            );
            expect(result.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_520103,
            );
        });

        it("should return CODE_520103 if getAbolitionOrder returns something", async () => {
            const apiLinesDao = new APILinesDAO(DefaultRequest, DefaultContext);
            jest.spyOn(apiLinesDao, "getAbolitionOrder").mockResolvedValue([
                "test123",
            ]);
            Object.defineProperty(instance, "apiLinesDao", {
                value: apiLinesDao,
            });

            const result = await instance.service(
                request,
                MvnoUtil.getClientIPAddress(DefaultRequest),
            );
            expect(result.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_520103,
            );
        });

        it("should return CODE_520104 if tenantManage.doCheck throws DB error", async () => {
            const tenantManage = new TenantManage(
                DefaultRequest,
                DefaultContext,
            );
            jest.spyOn(tenantManage, "doCheck").mockRejectedValue(
                new ConnectionTimedOutError(new Error("timeout")),
            );
            Object.defineProperty(instance, "tenantManage", {
                value: tenantManage,
            });

            const result = await instance.service(
                request,
                MvnoUtil.getClientIPAddress(DefaultRequest),
            );
            expect(result.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_520104,
            );
        });

        it("should return CODE_520106 if ApiCommonDAO.getEnableServiceOrder throws DB error", async () => {
            const aPICommonDAO = new APICommonDAO(
                DefaultRequest,
                DefaultContext,
            );
            jest.spyOn(aPICommonDAO, "getEnableServiceOrder").mockRejectedValue(
                new ConnectionTimedOutError(new Error("timeout")),
            );
            Object.defineProperty(instance, "aPICommonDAO", {
                value: aPICommonDAO,
            });

            const result = await instance.service(
                request,
                MvnoUtil.getClientIPAddress(DefaultRequest),
            );
            expect(result.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_520106,
            );
        });

        it("should return CODE_520106 if ApiCommonDAO.getEnableServiceOrder returns something", async () => {
            const aPICommonDAO = new APICommonDAO(
                DefaultRequest,
                DefaultContext,
            );
            jest.spyOn(aPICommonDAO, "getEnableServiceOrder").mockResolvedValue(
                ["test123"],
            );
            Object.defineProperty(instance, "aPICommonDAO", {
                value: aPICommonDAO,
            });

            const result = await instance.service(
                request,
                MvnoUtil.getClientIPAddress(DefaultRequest),
            );
            expect(result.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_520106,
            );
        });

        it("should return CODE_999999 if unexpected error occurred", async () => {
            const tenantManage = new TenantManage(
                DefaultRequest,
                DefaultContext,
            );
            jest.spyOn(tenantManage, "doCheck").mockRejectedValue(
                new TypeError("some error"),
            );
            Object.defineProperty(instance, "tenantManage", {
                value: tenantManage,
            });

            const result = await instance.service(
                request,
                MvnoUtil.getClientIPAddress(DefaultRequest),
            );

            expect(result.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_999999,
            );
        });

        it("should return CODE_520104 if tenantManage.doCheck returns false", async () => {
            const tenantManage = new TenantManage(
                DefaultRequest,
                DefaultContext,
            );
            jest.spyOn(tenantManage, "doCheck").mockResolvedValue(
                Promise.resolve([false, ""]),
            );
            Object.defineProperty(instance, "tenantManage", {
                value: tenantManage,
            });

            const result = await instance.service(
                request,
                MvnoUtil.getClientIPAddress(DefaultRequest),
            );

            expect(result.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_520104,
            );
        });

        it("should return CODE_520108 if reservationDateExecutionUnits config format is NG", async () => {
            const instanceLocal = new LineDeleteService(
                DefaultRequest,
                DefaultContext,
            );
            Object.defineProperty(
                instanceLocal,
                "reservationDateExecutionUnits",
                {
                    value: "ab",
                },
            );

            const result = await instanceLocal.service(
                request,
                MvnoUtil.getClientIPAddress(DefaultRequest),
            );

            expect(result.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_520108,
            );
        });

        it("should return CODE_520109 if reservationDateExecutionUnits config format is NG", async () => {
            const req = Object.assign({}, request);
            req.reserve_date = "2022/01/01 00:01";
            DefaultContext.jsonBody = Object.assign({}, DefaultContext);

            const instanceLocal = new LineDeleteService(
                DefaultRequest,
                DefaultContext,
            );
            Object.defineProperty(
                instanceLocal,
                "reservationDateExecutionUnits",
                {
                    value: "60",
                },
            );

            const result = await instanceLocal.service(
                request,
                MvnoUtil.getClientIPAddress(DefaultRequest),
            );
            // TODO failing
            expect(result.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_520109,
            );
        });

        it("should return CODE_520110 if reservationsLimitDays config format is NG", async () => {
            const instanceLocal = new LineDeleteService(
                DefaultRequest,
                DefaultContext,
            );
            Object.defineProperty(instanceLocal, "reservationsLimitDays", {
                value: -1,
            });

            const result = await instanceLocal.service(
                request,
                MvnoUtil.getClientIPAddress(DefaultRequest),
            );

            expect(result.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_520110,
            );
        });

        // TODO add more test cases
    });
});
