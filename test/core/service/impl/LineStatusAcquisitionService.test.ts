import config from "config";
import {
    add,
    addDays,
    Duration,
    formatDate,
    parse,
    setMinutes,
} from "date-fns";
import nock from "nock";
import { ConnectionTimedOutError, Sequelize } from "sequelize";

import AppConfig from "@/appconfig";
import ApiCommon from "@/core/common/ApiCommon";
import MvnoUtil from "@/core/common/MvnoUtil";
import TenantManage from "@/core/common/TenantManage";
import SOAPCommon from "@/core/common/SOAPCommon";
import ResultCdConstants from "@/core/constant/ResultCdConstants";
import { usePsql } from "@/database/psql";
import LineStatusAcquisitionInputDto from "@/core/dto/LineStatusAcquisitionInputDto";
import ResponseHeader from "@/core/dto/ResponseHeader";
import { LinesEntity } from "@/core/entity/LinesEntity";
import ServiceOrdersEntity from "@/core/entity/ServiceOrdersEntity";
import { TenantNnumbersEntity } from "@/core/entity/TenantNnumbersEntity";
import LineStatusAcquisitionService from "@/core/service/impl/LineStatusAcquisitionService";
import { getConfig } from "@/helpers/configHelper";

import DefaultContext from "../../../testing/DefaultContext";
import DefaultRequest from "../../../testing/DefaultRequest";
import SOAPTestHelper from "../../../testing/soapTestHelper";
import { insertLineNumber } from "../../../testing/testdataHelper";
import {
    describeWithDB,
    generateProcessID,
    useFasterRetries,
} from "../../../testing/TestHelper";

describeWithDB("@/core/service/impl/LineStatusAcquisitionService", () => {
    let sequelize: Sequelize;
    let instance: LineStatusAcquisitionService;

    const testdata = {
        lineNo: "09025021201",
        abolishLineNo: "09025021202",
        usageStatusNG: "09025021203", // full mvno
        tenantId: "TST000",
        reserveSOID: generateProcessID(),
        activate: "アクティベート",
        deactivate: "ディアクティベート",
        abolishSOID: generateProcessID(),
        resalePlanId: "JR057", // full mvno plan
        // planId: "40185",
        nNo: "",
    };

    const getConfigData = () => ({
        reservationDateExecutionUnits: "15",
        reservationsLimitDays: "60",
        // SOAP related configs:
        timeout: config.get<number>("mvno.TpcTimeout"),
        soap: config.get<string>("mvno.SOAP"),
        soapApiUrl: getConfig("TpcDestServiceName").replace(
            "${IpAddr}",
            getConfig("TpcDestIpAddress"),
        ),
        timeoutOffset: 200,
    });
    let configData: ReturnType<typeof getConfigData>;

    const __request: LineStatusAcquisitionInputDto = {
        requestHeader: {
            sequenceNo: "1234567",
            senderSystemId: "0001",
            apiKey: "",
            functionType: "05",
        },
        targetSoId: undefined,
        reserve_flag: undefined,
        reserve_soId: undefined,
        reserve_date: undefined,
        tenantId: testdata.tenantId,
        lineNo: testdata.lineNo,
        status: "1",
    };
    let param = Object.assign({}, __request);

    const nulls = [null, null, "127.0.0.1"];

    const responseHeader: ResponseHeader = {
        sequenceNo: __request.requestHeader.sequenceNo,
        receivedDate: formatDate(new Date(), "yyyy/MM/dd HH:mm:ss"),
        processCode: ResultCdConstants.CODE_000000,
        apiProcessID: generateProcessID(),
    };

    const nockHeaders: nock.ReplyHeaders = {
        "Content-Type": "text/xml",
    };

    function getDateNowForReservationTest(duration?: Duration) {
        return setMinutes(
            add(new Date(), duration ?? {}),
            +configData.reservationDateExecutionUnits,
        );
    }

    async function prepareTestData() {
        const nno = await TenantNnumbersEntity.findOne({
            where: {
                tenantId: testdata.tenantId,
                fullMvnoFlag: true,
            },
        });
        testdata.nNo = nno.nnumber;
        await Promise.all([
            ServiceOrdersEntity.create({
                serviceOrderId: testdata.abolishSOID,
                // tenantId: testdata.tenantId,
                lineId: testdata.abolishLineNo,
                orderStatus: "予約中",
                // orderType: "回線廃止",
                reserveDate: addDays(new Date(), -1),
                functionType: "52",
                // restMessage: "",
            }),
            insertLineNumber(testdata.tenantId, testdata.lineNo),
            insertLineNumber(testdata.tenantId, testdata.abolishLineNo),
            insertLineNumber(
                testdata.tenantId,
                testdata.usageStatusNG,
                testdata.nNo,
            ),
        ]);
        await Promise.all([
            LinesEntity.update(
                {
                    lineActDate: addDays(new Date(), -2),
                },
                { where: { lineId: testdata.abolishLineNo } },
            ),
            LinesEntity.update(
                {
                    usageStatus: 2,
                    pricePlanId: testdata.resalePlanId,
                },
                { where: { lineId: testdata.usageStatusNG } },
            ),
        ]);
    }

    async function clearTestData() {
        await Promise.all([
            ServiceOrdersEntity.destroy({
                where: {
                    serviceOrderId: [
                        responseHeader.apiProcessID,
                        testdata.reserveSOID,
                        testdata.abolishSOID,
                    ],
                },
            }),
            LinesEntity.destroy({
                where: {
                    lineId: [testdata.lineNo, testdata.abolishLineNo],
                },
            }),
        ]);
    }

    beforeAll(async () => {
        sequelize = await usePsql();
        nock.disableNetConnect();
        await AppConfig.loadCoreMvnoConfig(DefaultContext);
        configData = getConfigData();

        await clearTestData();
    });

    afterAll(async () => {
        nock.enableNetConnect();
        await sequelize.close();
    });

    beforeEach(async () => {
        DefaultContext.jsonBody = Object.assign({}, __request);
        DefaultContext.responseHeader = Object.assign({}, responseHeader);
        jest.spyOn(MvnoUtil, "getDateTimeNow").mockReturnValue(
            responseHeader.receivedDate,
        );
        useFasterRetries();
        instance = new LineStatusAcquisitionService(
            DefaultRequest,
            DefaultContext,
        );

        await prepareTestData();

        // reset param
        param = Object.assign({}, __request);
    });

    afterEach(async () => {
        jest.restoreAllMocks();
        jest.clearAllMocks();
        nock.abortPendingRequests();
        nock.cleanAll();

        await clearTestData();
    });

    async function checkServiceOrder(
        created: boolean,
        success: boolean,
        label: string = testdata.activate,
        tenantId?: string,
        serviceOrderId?: string,
        orderStatus: "失敗" | "予約中" | "完了" = "完了",
    ) {
        const so = await ServiceOrdersEntity.findOne({
            where: {
                serviceOrderId:
                    serviceOrderId ??
                    DefaultContext.responseHeader.apiProcessID,
            },
        });

        if (created) {
            expect(so).toBeDefined();
            expect(so).not.toBeNull();
            if (!success) {
                expect(so.orderStatus).toEqual("失敗");
            } else {
                expect(so.orderStatus).toEqual(orderStatus);
            }
            expect(so.orderType).toEqual(label);
            expect(so.tenantId).toEqual(tenantId ?? testdata.tenantId);
            expect(so.functionType).toEqual("05");
        } else {
            expect(so).toBeNull();
        }
        return so;
    }

    describe("OK cases", () => {
        beforeEach(() => {
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, SOAPTestHelper.dataResponse("OK", "", []))
                .persist();
        });

        const cases = [
            ["0", testdata.deactivate],
            ["1", testdata.activate],
        ];
        test.each(cases)(
            "should return CODE_000000 if request is valid: status=%s (%s) [即時オーダ]",
            async (status, label) => {
                param.status = status;

                const response = await instance.service(param, false, ...nulls);
                expect(response?.jsonBody).toBeDefined();
                expect(response.jsonBody.responseHeader).toBeDefined();
                expect(response.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_000000,
                );

                await checkServiceOrder(true, true, label);
            },
        );

        test.each(cases)(
            "should return CODE_000000 if request is valid: status=%s [number] (%s) [即時オーダ]",
            async (status, label) => {
                param.status = parseInt(status, 10) as any;

                const response = await instance.service(param, false, ...nulls);
                expect(response?.jsonBody).toBeDefined();
                expect(response.jsonBody.responseHeader).toBeDefined();
                expect(response.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_000000,
                );

                await checkServiceOrder(true, true, label);
            },
        );

        test.each(cases)(
            "should return CODE_000000 if request is valid: status=%s (%s) [予約実行オーダ]",
            async (status, label) => {
                // create reservation order
                await ServiceOrdersEntity.create({
                    serviceOrderId: testdata.reserveSOID,
                    tenantId: testdata.tenantId,
                    lineId: testdata.lineNo,
                    orderStatus: "予約中",
                    orderType: label,
                    reserveDate: getDateNowForReservationTest(),
                    functionType: "05",
                });
                DefaultContext.responseHeader.apiProcessID =
                    testdata.reserveSOID;

                param.status = status;
                param.reserve_flag = true;
                param.reserve_soId = testdata.reserveSOID;
                param.reserve_date = formatDate(
                    getDateNowForReservationTest(),
                    "yyyy/MM/dd HH:mm",
                );

                const response = await instance.service(param, false, ...nulls);
                expect(response?.jsonBody).toBeDefined();
                expect(response.jsonBody.responseHeader).toBeDefined();
                expect(response.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_000000,
                );

                await checkServiceOrder(
                    true,
                    true,
                    label,
                    param.tenantId,
                    param.reserve_soId,
                );
            },
        );

        test("should return CODE_000000 even if secondary TPC request failed", async () => {
            nock.cleanAll(); // reset nock
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, SOAPTestHelper.dataResponse("OK", "", []))
                .post("")
                .reply(
                    200,
                    SOAPTestHelper.dataResponse("NG", "test error", []),
                );

            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );

            await checkServiceOrder(true, true);
            expect(DefaultContext.warn).toHaveBeenCalledTimes(1);
        });

        test("should return CODE_000000 even if secondary TPC configuration is NG", async () => {
            jest.spyOn(
                TenantManage.prototype,
                "checkTpcConnection",
            ).mockResolvedValue([
                true,
                "TpcDestIpAddress",
                "TpcDestIpAddress99",
            ]);

            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_000000,
            );

            await checkServiceOrder(true, true);
            // 1x SOAPCommon + 1x LineStatusAcquisitionService
            expect(DefaultContext.warn).toHaveBeenCalledTimes(2);
        });
    });

    describe("NG cases", () => {
        test("should return CODE_050105 if order type cannot be determined", async () => {
            param.reserve_date = "some date";
            param.reserve_flag = true;
            param.reserve_soId = undefined;

            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_050105,
            );
            await checkServiceOrder(false, false);
        });

        test("should return CODE_999999 local IP address is not valid for 予約実行オーダ", async () => {
            try {
                DefaultContext.isInternalRequest = false;
                param.reserve_date = "some date";
                param.reserve_flag = true;
                param.reserve_soId = testdata.reserveSOID;

                const response = await instance.service(
                    param,
                    false,
                    null,
                    null,
                    null, // local IP address
                );
                expect(response?.jsonBody).toBeDefined();
                expect(response.jsonBody.responseHeader).toBeDefined();
                expect(response.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_999999,
                );
                await checkServiceOrder(false, false);
            } finally {
                DefaultContext.isInternalRequest = true;
            }
        });

        test("should return CODE_050101 if lineNo is not valid", async () => {
            param.lineNo = "invalid";

            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_050101,
            );

            await checkServiceOrder(true, false);
        });

        const ngStatus = [null, "10", "abc", "2", 3, -1, undefined];
        test.each(ngStatus)(
            "should return CODE_050101 if status parameter is invalid (%s)",
            async (status) => {
                param.status = status as any;

                const response = await instance.service(param, false, ...nulls);
                expect(response?.jsonBody).toBeDefined();
                expect(response.jsonBody.responseHeader).toBeDefined();
                expect(response.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_050101,
                );

                await checkServiceOrder(false, false); // no service order created
            },
        );

        test("should return CODE_050101 if reserve date is not valid [予約前オーダ]", async () => {
            param.reserve_date = "invalid";

            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_050101,
            );

            await checkServiceOrder(false, false);
        });

        test("should return CODE_050101 if reserve date is not valid [予約実行オーダ]", async () => {
            param.reserve_date = "invalid";
            param.reserve_flag = true;
            param.reserve_soId = testdata.reserveSOID;

            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_050101,
            );

            await checkServiceOrder(false, false);
        });

        test("should return CODE_050106 if reserve_date is before received date", async () => {
            param.reserve_date = formatDate(
                add(
                    parse(
                        responseHeader.receivedDate,
                        "yyyy/MM/dd HH:mm:ss",
                        new Date(),
                    ),
                    { days: -1 },
                ),
                "yyyy/MM/dd HH:mm",
            );

            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_050106,
            );

            await checkServiceOrder(true, false);
        });

        test("should return CODE_050107 if reservationDateExecutionUnits config is invalid", async () => {
            Object.defineProperty(instance, "reservationDateExecutionUnits", {
                value: "invalid",
            });
            param.reserve_date = formatDate(
                getDateNowForReservationTest({ days: 1 }),
                "yyyy/MM/dd HH:mm",
            );

            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_050107,
            );

            await checkServiceOrder(true, false);
        });

        test("should return CODE_050108 if reservedate is past reservationDateExecutionUnits", async () => {
            param.reserve_date = formatDate(
                addDays(
                    new Date(),
                    +configData.reservationDateExecutionUnits + 1,
                ),
                "yyyy/MM/dd HH:mm",
            );

            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_050108,
            );

            await checkServiceOrder(
                true,
                false,
                testdata.activate,
                null,
                null,
                "予約中",
            );
        });

        test("should return CODE_050109 if reservationsLimitDays config is invalid", async () => {
            Object.defineProperty(instance, "reservationsLimitDays", {
                value: "invalid",
            });
            param.reserve_date = formatDate(
                getDateNowForReservationTest({ days: 1 }),
                "yyyy/MM/dd HH:mm",
            );

            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_050109,
            );

            await checkServiceOrder(true, false);
        });

        test("should return CODE_050110 if reserveDate is past reservationsLimitDays", async () => {
            param.reserve_date = formatDate(
                addDays(
                    getDateNowForReservationTest(),
                    +configData.reservationsLimitDays + 1,
                ),
                "yyyy/MM/dd HH:mm",
            );

            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_050110,
            );

            await checkServiceOrder(true, false);
        });

        test("should reutrn CODE_050102 if tenant is not valid", async () => {
            param.tenantId = "invalid";

            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_050102,
            );

            await checkServiceOrder(
                true,
                false,
                testdata.activate,
                param.tenantId,
            );
        });

        test("should return CODE_050102 if failed to query tenant data", async () => {
            jest.spyOn(TenantManage.prototype, "doCheck").mockRejectedValue(
                new ConnectionTimedOutError(Error("test: timeout error")),
            );

            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_050102,
            );

            await checkServiceOrder(true, false); // SQL error does not create service order
        });

        test("should return CODE_050103 if there is 廃止 order [即時オーダ]", async () => {
            param.lineNo = testdata.abolishLineNo;

            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_050103,
            );

            await checkServiceOrder(true, false, testdata.activate);
        });

        test("should return CODE_050103 if there is 廃止 order [予約オーダ]", async () => {
            param.lineNo = testdata.abolishLineNo;
            param.reserve_flag = false;
            param.reserve_date = formatDate(
                getDateNowForReservationTest({ days: 2 }),
                "yyyy/MM/dd HH:mm",
            );
            param.reserve_soId = undefined;

            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_050103,
            );

            await checkServiceOrder(true, false, testdata.activate);
        });

        test("should return CODE_050103 if failed to query 廃止 order", async () => {
            jest.spyOn(ApiCommon.prototype, "checkAbolishSo").mockRejectedValue(
                new ConnectionTimedOutError(Error("test: timeout error")),
            );

            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_050103,
            );

            await checkServiceOrder(true, false, testdata.activate);
        });

        test("should return CODE_050104 if line is in suspend state", async () => {
            param.lineNo = testdata.usageStatusNG;

            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_050104,
            );

            await checkServiceOrder(true, false, testdata.activate);
        });

        test("should return CODE_050104 if failed to query line status", async () => {
            jest.spyOn(
                ApiCommon.prototype,
                "checkLineSuspend",
            ).mockRejectedValue(
                new ConnectionTimedOutError(Error("test: timeout error")),
            );

            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_050104,
            );

            await checkServiceOrder(true, false, testdata.activate);
        });

        test("should return CODE_050301 if TPC information for tenant is not found", async () => {
            jest.spyOn(
                TenantManage.prototype,
                "checkTpcConnection",
            ).mockResolvedValue([false, "", ""]);

            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_050301,
            );

            await checkServiceOrder(true, false, testdata.activate);
        });

        test("should return CODE_050301 if failed to fetch TPC information", async () => {
            jest.spyOn(
                TenantManage.prototype,
                "checkTpcConnection",
            ).mockRejectedValue(
                new ConnectionTimedOutError(Error("test: timeout error")),
            );

            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_050301,
            );

            await checkServiceOrder(true, false, testdata.activate);
        });

        test("should return CODE_050401 if cannot reach TPC server (SOAP)", async () => {
            // skip nock
            nock.cleanAll();

            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_050401,
            );

            await checkServiceOrder(true, false, testdata.activate);
        });

        test("should return CODE_999999 for other error during SOAP request", async () => {
            jest.spyOn(
                SOAPCommon.prototype,
                "callWebSoapApi",
            ).mockRejectedValue(new Error("test unexpected error"));

            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_999999,
            );

            await checkServiceOrder(true, false, testdata.activate);
        });

        test("should return CODE_050401 if SOAP response with NG", async () => {
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, SOAPTestHelper.dataResponse("NG", "test error", []))
                .persist();

            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_050401,
            );

            await checkServiceOrder(true, false, testdata.activate);
        });

        test("should return CODE_050401 if TPC request failed", async () => {
            nock.cleanAll(); // reset nock
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, SOAPTestHelper.dataResponse("OK", "", []))
                .post("")
                .reply(400, "test error");

            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toEqual(
                ResultCdConstants.CODE_050401,
            );

            await checkServiceOrder(true, false);
            expect(DefaultContext.warn).toHaveBeenCalled();
        });
    });
});
