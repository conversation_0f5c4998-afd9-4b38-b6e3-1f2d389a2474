import { addDays, formatDate } from "date-fns";
import { ConnectionTimedOutError, Sequelize } from "sequelize";

import "@/types/string.extension";

import {
    describeWithDB,
    generateProcessID,
    generateSOID,
    useFasterRetries,
} from "../../../testing/TestHelper";
import DefaultContext from "../../../testing/DefaultContext";
import DefaultRequest from "../../../testing/DefaultRequest";

import AppConfig from "@/appconfig";
import ApiCommon from "@/core/common/ApiCommon";
import TenantManage from "@/core/common/TenantManage";

import ResultCdConstants from "@/core/constant/ResultCdConstants";

import APILinesDAO from "@/core/dao/APILinesDAO";
import ResponseHeader from "@/core/dto/ResponseHeader";
import NetworkContractChangeInputDto from "@/core/dto/NetworkContractChangeInputDto";

import { LinesEntity } from "@/core/entity/LinesEntity";
import LineTenantsEntity from "@/core/entity/LineTenantsEntity";
import ServiceOrdersEntity from "@/core/entity/ServiceOrdersEntity";

import { usePsql } from "@/database/psql";

import NetworkContractChangeService from "@/core/service/impl/NetworkContractChangeService";

/**
 * Unit tests for cases which are not covered by NetworkContractChangeHandler.test.ts
 */
describeWithDB("/core/service/impl/NetworkContractChangeService", () => {
    let sequelize: Sequelize;
    let instance: NetworkContractChangeService;

    const testdata = {
        lineOK: "08024020701",
        lineUpdatedAt: addDays(new Date(), -1),
    };

    const deviceId = {
        lte: "AD7",
        sms: "AE0",
        voice: "AW9",
    };

    const request = {
        requestHeader: {
            sequenceNo: "1234567",
            senderSystemId: "0001",
            apiKey: "",
            functionType: "60",
        },
        targetSoId: generateSOID(),
        tenantId: "OPF000",
        targetTenantId: "TST000",
        lineNo: testdata.lineOK,
        access_pre: "SE", // not used in implementation
        access: "SJ",
        cardTypeId: deviceId.voice,
        csvUnnecessaryFlag: "0",
    } as NetworkContractChangeInputDto;

    const responseHeader = {
        sequenceNo: request.requestHeader.sequenceNo,
        receivedDate: formatDate(new Date(), "yyyy/MM/dd HH:mm:ss"),
        processCode: ResultCdConstants.CODE_000000,
        apiProcessID: generateProcessID(),
    } as ResponseHeader;

    const clearTestData = async () => {
        await LineTenantsEntity.destroy({
            where: { lineId: testdata.lineOK },
        });
        await LinesEntity.destroy({
            where: { lineId: testdata.lineOK },
        });
        await ServiceOrdersEntity.destroy({
            where: {
                serviceOrderId: responseHeader.apiProcessID,
            },
        });
    };

    beforeAll(async () => {
        await AppConfig.loadCoreMvnoConfig(DefaultContext);
        sequelize = await usePsql();
        await clearTestData();
    });

    afterAll(async () => {
        await sequelize.close();
    });

    beforeEach(async () => {
        DefaultContext.jsonBody = Object.assign({}, request);
        DefaultContext.responseHeader = Object.assign({}, responseHeader);
        Object.defineProperty(
            NetworkContractChangeService,
            "LOCK_WAIT_MILLISEC",
            {
                value: 10,
            },
        );
        instance = new NetworkContractChangeService(
            DefaultRequest,
            DefaultContext,
        );
        Object.defineProperty(instance, "LOCK_WAIT_MILLISEC", {
            value: 10,
        });

        useFasterRetries();

        await LinesEntity.create({
            lineId: testdata.lineOK,
            tenantId: request.targetTenantId,
            lineStatus: "01",
            simFlag: false,
            contractType: "3G",
            nwModifyFlag: false,
            updatedAt: testdata.lineUpdatedAt,
        });

        await LineTenantsEntity.create({
            lineId: testdata.lineOK,
            tenantId: request.targetTenantId,
        });
    });

    afterEach(async () => {
        await clearTestData();

        jest.restoreAllMocks();
        jest.clearAllMocks();
    });

    describe("NG cases", () => {
        it("should return CODE_600102 if ApiLinesDao.getLineInfoByLines failed (DB error)", async () => {
            const apiLinesDao = new APILinesDAO(DefaultRequest, DefaultContext);
            jest.spyOn(apiLinesDao, "getLineInfoByLines").mockRejectedValue(
                new ConnectionTimedOutError(new Error("Connection timed out")),
            );
            Object.defineProperty(instance, "apiLinesDao", {
                value: apiLinesDao,
            });

            const result = await instance.service(request);
            expect(result.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_600102,
            );
        });

        it("should return CODE_600103 if TenantManage.doCheck failed (DB error)", async () => {
            const tenantManage = new TenantManage(
                DefaultRequest,
                DefaultContext,
            );
            jest.spyOn(tenantManage, "doCheck").mockRejectedValue(
                new ConnectionTimedOutError(new Error("Connection timed out")),
            );
            Object.defineProperty(instance, "tenantManage", {
                value: tenantManage,
            });

            const result = await instance.service(request);
            expect(result.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_600103,
            );
        });

        it("should return CODE_600104 if ApiCommon.checkAbolishSo failed (DB error)", async () => {
            const apiCommon = new ApiCommon(DefaultRequest, DefaultContext);
            jest.spyOn(apiCommon, "checkAbolishSo").mockRejectedValue(
                new ConnectionTimedOutError(new Error("Connection timed out")),
            );
            Object.defineProperty(instance, "apiCommon", {
                value: apiCommon,
            });

            const result = await instance.service(request);
            expect(result.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_600104,
            );
        });

        it("should return CODE_600201 if failed to get lock for line update", async () => {
            const apiLinesDao = new APILinesDAO(DefaultRequest, DefaultContext);
            jest.spyOn(
                apiLinesDao,
                "getLineInfoforUpdate",
            ).mockResolvedValueOnce(null);
            Object.defineProperty(instance, "apiLinesDao", {
                value: apiLinesDao,
            });

            const result = await instance.service(request);
            expect(result.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_600201,
            );
        });

        it("should return CODE_600201 if lock not available 4 times", async () => {
            const tx = await sequelize.transaction();
            const query =
                "select line_id from lines where line_id = :line_id for update nowait";
            try {
                const apiLinesDao = new APILinesDAO(
                    DefaultRequest,
                    DefaultContext,
                );
                Object.defineProperty(instance, "apiLinesDao", {
                    value: apiLinesDao,
                });
                await sequelize.query(query, {
                    replacements: {
                        line_id: testdata.lineOK,
                    },
                    transaction: tx,
                });

                const result = await instance.service(request);
                expect(result.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_600201,
                );
            } finally {
                await tx.rollback();
            }
        });

        it("should return CODE_600201 if ApiLinesDao.getLineInfoForUpdate failed (DB error)", async () => {
            const apiLinesDao = new APILinesDAO(DefaultRequest, DefaultContext);
            jest.spyOn(apiLinesDao, "getLineInfoforUpdate").mockRejectedValue(
                new ConnectionTimedOutError(new Error("Connection timed out")),
            );
            Object.defineProperty(instance, "apiLinesDao", {
                value: apiLinesDao,
            });

            const result = await instance.service(request);
            expect(result.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_600201,
            );
        });

        it("should return CODE_600201 if ApiLinesDao.getLineInfoForUpdate (not DB error)", async () => {
            const apiLinesDao = new APILinesDAO(DefaultRequest, DefaultContext);
            jest.spyOn(apiLinesDao, "getLineInfoforUpdate").mockRejectedValue(
                new TypeError("Unexpected error"),
            );
            Object.defineProperty(instance, "apiLinesDao", {
                value: apiLinesDao,
            });

            const result = await instance.service(request);
            expect(result.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_600201,
            );
        });

        it("should return CODE_600301 if ApiLinesDao.getCardTypeInfo failed (DB error)", async () => {
            const apiLinesDao = new APILinesDAO(DefaultRequest, DefaultContext);
            jest.spyOn(apiLinesDao, "getCardTypeInfo").mockRejectedValue(
                new ConnectionTimedOutError(new Error("Connection timed out")),
            );
            Object.defineProperty(instance, "apiLinesDao", {
                value: apiLinesDao,
            });

            const result = await instance.service(request);
            expect(result.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_600301,
            );
        });

        it("should return CODE_600301 if ApiLinesDao.updateLineContractDeviceType failed (DB error)", async () => {
            const apiLinesDao = new APILinesDAO(DefaultRequest, DefaultContext);
            jest.spyOn(
                apiLinesDao,
                "updateLineContractDeviceType",
            ).mockRejectedValue(
                new ConnectionTimedOutError(new Error("Connection timed out")),
            );
            Object.defineProperty(instance, "apiLinesDao", {
                value: apiLinesDao,
            });

            const result = await instance.service(request);
            expect(result.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_600301,
            );
        });

        it("should return CODE_600301 if ApiLinesDao.updateLineContractDeviceType failed (not DB error)", async () => {
            const apiLinesDao = new APILinesDAO(DefaultRequest, DefaultContext);
            jest.spyOn(
                apiLinesDao,
                "updateLineContractDeviceType",
            ).mockRejectedValue(new TypeError("Unexpected error"));
            Object.defineProperty(instance, "apiLinesDao", {
                value: apiLinesDao,
            });

            const result = await instance.service(request);
            expect(result.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_600301,
            );
        });

        it("should return CODE_999999 if unexpected error occurred outside transaction block", async () => {
            const apiCommon = new ApiCommon(DefaultRequest, DefaultContext);
            jest.spyOn(apiCommon, "checkAbolishSo").mockRejectedValue(
                new TypeError("Unexpected error"),
            );
            Object.defineProperty(instance, "apiCommon", {
                value: apiCommon,
            });

            const result = await instance.service(request);
            expect(result.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_999999,
            );
        });
    });
});
