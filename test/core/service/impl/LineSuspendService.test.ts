import { addDays, formatDate } from "date-fns";
import { ConnectionTimedOutError, Op, Sequelize } from "sequelize";

import "@/types/string.extension";

import {
    describeWithDB,
    generateProcessID,
    generateSOID,
    useFasterRetries,
} from "../../../testing/TestHelper";
import DefaultRequest from "../../../testing/DefaultRequest";
import DefaultContext from "../../../testing/DefaultContext";

import ResultCdConstants from "@/core/constant/ResultCdConstants";

import AppConfig from "@/appconfig";
import TenantManage from "@/core/common/TenantManage";

import APILinesDAO from "@/core/dao/APILinesDAO";
import APICommonDAO from "@/core/dao/APICommonDAO";
import ResponseHeader from "@/core/dto/ResponseHeader";
import LineSuspendInputDto from "@/core/dto/LineSuspendInputDto";

import { LinesEntity } from "@/core/entity/LinesEntity";
import LineTenantsEntity from "@/core/entity/LineTenantsEntity";
import PlansEntity from "@/core/entity/PlansEntity";
import ServiceOrdersEntity from "@/core/entity/ServiceOrdersEntity";
import TenantPlansEntity from "@/core/entity/TenantPlansEntity";

import { usePsql } from "@/database/psql";

import LineSuspendService from "@/core/service/impl/LineSuspendService";

/**
 * Unit tests for cases which are not covered by LineSuspendHandler.test.ts
 */
describeWithDB("/core/service/impl/LineSuspendService", () => {
    let sequelize: Sequelize;
    let instance: LineSuspendService;

    const UsageStatusType = {
        ACTIVE: 0,
        SUSPENDING: 1,
        SUSPENDED: 2,
    };
    const OrderTypes = {
        [UsageStatusType.ACTIVE]: "利用状態変更（利用中）",
        [UsageStatusType.SUSPENDING]: "利用状態変更（利用中断中）",
        [UsageStatusType.SUSPENDED]: "利用状態変更（サスペンド中）",
    };

    const testdata = {
        lines: {
            ok: "08024021001",
        },
        abolishSo: generateProcessID(),
        lineGroupId: "",
        fullMvnoPlan: "",
        litePlan: "",
        lineUpdateAt: addDays(new Date(), -1),
    };

    const request: LineSuspendInputDto = {
        requestHeader: {
            sequenceNo: "1234567",
            senderSystemId: "0001",
            apiKey: "",
            functionType: "58",
        },
        targetSoId: generateSOID(),
        tenantId: "OPF000",
        targetTenantId: "TST000",
        lineNo: testdata.lines.ok,
        suspendFlag: `${UsageStatusType.SUSPENDED}`,
        reserve_flag: null,
        reserve_soId: null,
    };

    const responseHeader = {
        sequenceNo: request.requestHeader.sequenceNo,
        receivedDate: formatDate(new Date(), "yyyy/MM/dd HH:mm:ss"),
        processCode: ResultCdConstants.CODE_000000,
        apiProcessID: generateProcessID(),
    } as ResponseHeader;

    const clearTestData = async () => {
        await LineTenantsEntity.destroy({
            where: {
                lineId: Object.values(testdata.lines),
            },
        });
        await Promise.all([
            LinesEntity.destroy({
                where: {
                    lineId: Object.values(testdata.lines),
                },
            }),
            ServiceOrdersEntity.destroy({
                where: {
                    serviceOrderId: responseHeader.apiProcessID,
                },
            }),
        ]);
    };

    beforeAll(async () => {
        await AppConfig.loadCoreMvnoConfig(DefaultContext);
        sequelize = await usePsql();

        // get Full MVNO Plan and lite plan
        const [full, lite] = await Promise.all([
            PlansEntity.findOne({
                include: [
                    {
                        model: TenantPlansEntity,
                        as: "tenantPlansEntityList",
                        where: {
                            tenantId: request.targetTenantId,
                        },
                    },
                ],
                where: {
                    fullMvnoFlag: true,
                },
            }),
            PlansEntity.findOne({
                include: [
                    {
                        model: TenantPlansEntity,
                        as: "tenantPlansEntityList",
                        where: {
                            tenantId: request.targetTenantId,
                        },
                    },
                ],
                where: {
                    fullMvnoFlag: {
                        [Op.not]: true,
                    },
                },
            }),
        ]);
        if (!full || !lite) {
            throw new Error("could not find full MVNO or lite plan");
        }
        testdata.fullMvnoPlan = full.pricePlanId;
        testdata.litePlan = lite.pricePlanId;

        await clearTestData();
    });

    afterAll(async () => {
        await sequelize.close();
    });

    beforeEach(async () => {
        useFasterRetries();

        DefaultContext.jsonBody = Object.assign({}, request);
        DefaultContext.responseHeader = Object.assign({}, responseHeader);
        instance = new LineSuspendService(DefaultRequest, DefaultContext);
        Object.defineProperty(instance, "apDbRetryInterval", { value: 0.01 });

        await LinesEntity.create({
            lineId: testdata.lines.ok,
            tenantId: request.targetTenantId,
            lineStatus: "01",
            simFlag: false,
            pricePlanId: testdata.fullMvnoPlan,
            usageStatus: UsageStatusType.ACTIVE,
            updatedAt: testdata.lineUpdateAt,
        });
        await LineTenantsEntity.create({
            lineId: testdata.lines.ok,
            tenantId: request.targetTenantId,
        });
    });

    afterEach(async () => {
        jest.restoreAllMocks();
        jest.clearAllMocks();

        await clearTestData();
    });

    describe("NG cases", () => {
        it("should return CODE_580103 if TenantManage.doCheck failed", async () => {
            const tenantManage = new TenantManage(
                DefaultRequest,
                DefaultContext,
            );
            jest.spyOn(tenantManage, "doCheck").mockRejectedValue(
                new ConnectionTimedOutError(new Error("Connection timed out")),
            );

            Object.defineProperty(instance, "tenantManage", {
                value: tenantManage,
            });

            const result = await instance.service(request);
            expect(result.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_580103,
            );
        });

        it("should return CODE_580105 if ApiLinesDao.getPlanInfo failed", async () => {
            const apiLinesDao = new APILinesDAO(DefaultRequest, DefaultContext);
            jest.spyOn(apiLinesDao, "getPlanInfo").mockRejectedValue(
                new ConnectionTimedOutError(new Error("Connection timed out")),
            );

            Object.defineProperty(instance, "apiLinesDao", {
                value: apiLinesDao,
            });

            const result = await instance.service(request);
            expect(result.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_580105,
            );
        });

        it("should return CODE_580201 if ApiLinesDao.updateLineUsageStatus failed (non DB error)", async () => {
            const apiLinesDao = new APILinesDAO(DefaultRequest, DefaultContext);
            jest.spyOn(apiLinesDao, "updateLineUsageStatus").mockRejectedValue(
                new TypeError(
                    "[test] Cannot read property 'lineId' of undefined",
                ),
            );

            Object.defineProperty(instance, "apiLinesDao", {
                value: apiLinesDao,
            });

            const result = await instance.service(request);
            expect(result.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_580201,
            );
        });

        it("should return CODE_580201 if ApiLinesDao.updateLineUsageStatus failed (DB error)", async () => {
            const apiLinesDao = new APILinesDAO(DefaultRequest, DefaultContext);
            jest.spyOn(apiLinesDao, "updateLineUsageStatus").mockRejectedValue(
                new ConnectionTimedOutError(new Error("Connection timed out")),
            );

            Object.defineProperty(instance, "apiLinesDao", {
                value: apiLinesDao,
            });

            const result = await instance.service(request);
            expect(result.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_580201,
            );
        });

        it("should return CODE_580301 if apiCommonDao.getGroupIdList failed (non DB error)", async () => {
            const apiCommonDao = new APICommonDAO(
                DefaultRequest,
                DefaultContext,
            );
            jest.spyOn(apiCommonDao, "getGroupIdList").mockRejectedValue(
                new TypeError(
                    "[test] Cannot read property 'tenantId' of undefined",
                ),
            );

            Object.defineProperty(instance, "apiCommonDao", {
                value: apiCommonDao,
            });

            const result = await instance.service(request);
            expect(result.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_580301,
            );
        });

        it("should return CODE_580301 if apiCommonDao.getGroupIdList failed (DB error)", async () => {
            const apiCommonDao = new APICommonDAO(
                DefaultRequest,
                DefaultContext,
            );
            jest.spyOn(apiCommonDao, "getGroupIdList").mockRejectedValue(
                new ConnectionTimedOutError(new Error("Connection timed out")),
            );

            Object.defineProperty(instance, "apiCommonDao", {
                value: apiCommonDao,
            });

            const result = await instance.service(request);
            expect(result.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_580301,
            );
        });

        it("should return CODE_999999 for non DB error", async () => {
            const apiLinesDao = new APILinesDAO(DefaultRequest, DefaultContext);
            jest.spyOn(apiLinesDao, "getPlanInfo").mockRejectedValue(
                new TypeError("[test] planId is required"),
            );

            Object.defineProperty(instance, "apiLinesDao", {
                value: apiLinesDao,
            });

            const result = await instance.service(request);
            expect(result.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_999999,
            );
        });
    });
});
