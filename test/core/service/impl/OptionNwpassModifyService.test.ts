import { addDays, formatDate } from "date-fns";
import { ConnectionTimedOutError, Sequelize } from "sequelize";

import "@/types/string.extension";

import {
    describeWithDB,
    generateProcessID,
    generateSOID,
    useFasterRetries,
} from "../../../testing/TestHelper";
import DefaultContext from "../../../testing/DefaultContext";
import DefaultRequest from "../../../testing/DefaultRequest";

import ResultCdConstants from "@/core/constant/ResultCdConstants";

import AppConfig from "@/appconfig";
import ApiCommon from "@/core/common/ApiCommon";
import TenantManage from "@/core/common/TenantManage";

import APILinesDAO from "@/core/dao/APILinesDAO";
import OptionNwpassModifyInputDto from "@/core/dto/OptionNwpassModifyInputDto";
import ResponseHeader from "@/core/dto/ResponseHeader";

import { usePsql } from "@/database/psql";

import { LinesEntity } from "@/core/entity/LinesEntity";
import LineTenantsEntity from "@/core/entity/LineTenantsEntity";
import ServiceOrdersEntity from "@/core/entity/ServiceOrdersEntity";
import { TenantNnumbersEntity } from "@/core/entity/TenantNnumbersEntity";

import OptionNwpassModifyService from "@/core/service/impl/OptionNwpassModifyService";

/**
 * Unit tests for cases which are not covered by OptionNwpassModifyHandler.test.ts
 */
describeWithDB("/core/service/impl/OptionNwpassModifyService", () => {
    let sequelize: Sequelize;
    let instance: OptionNwpassModifyService;

    const testdata = {
        lineOK: "08024043010",
        lineUpdatedAt: addDays(new Date(), -1),
        nnumber: "",
    };

    const request: OptionNwpassModifyInputDto = {
        requestHeader: {
            sequenceNo: "1234567",
            senderSystemId: "0001",
            apiKey: "",
            functionType: "54",
        },
        targetSoId: generateSOID(),
        tenantId: "OPF000",
        targetTenantId: "TST000",
        lineNo: testdata.lineOK,
        pinChange: "1",
        intlRoaming: {
            changeCode: "1", // 追加
            beforeOpId: "",
            afterOpId: "AO147",
        },
        voicemail: {
            changeCode: "0",
            beforeOpId: null,
            afterOpId: null,
        },
        callWaiting: {
            changeCode: "0",
            beforeOpId: null,
            afterOpId: null,
        },
        intlCall: {
            changeCode: "2", // 変更
            beforeOpId: "AO122",
            afterOpId: "AO123",
        },
        forwarding: {
            changeCode: "3", // 削除
            beforeOpId: "AO144",
            afterOpId: "",
        },
        intlForwarding: {
            changeCode: "0",
            beforeOpId: null,
            afterOpId: null,
        },
        csvUnnecessaryFlag: "0",
        reserve_flag: null,
        reserve_soId: null,
    };

    const responseHeader = {
        sequenceNo: request.requestHeader.sequenceNo,
        receivedDate: formatDate(new Date(), "yyyy/MM/dd HH:mm:ss"),
        processCode: ResultCdConstants.CODE_000000,
        apiProcessID: generateProcessID(),
    } as ResponseHeader;

    const clearTestData = async () => {
        await ServiceOrdersEntity.destroy({
            where: {
                serviceOrderId: responseHeader.apiProcessID,
            },
        });
        await LineTenantsEntity.destroy({
            where: {
                lineId: testdata.lineOK,
            },
        });
        await LinesEntity.destroy({
            where: {
                lineId: testdata.lineOK,
            },
        });
    };

    beforeAll(async () => {
        await AppConfig.loadCoreMvnoConfig(DefaultContext);
        sequelize = await usePsql();
        const tenantNNo = await TenantNnumbersEntity.findOne({
            where: {
                tenantId: request.targetTenantId,
            },
        });
        if (!tenantNNo) {
            throw new Error("Tenant Nnumber not found");
        }
        testdata.nnumber = tenantNNo.nnumber;
    });

    afterAll(async () => {
        await sequelize.close();
    });

    beforeEach(async () => {
        useFasterRetries();

        DefaultContext.jsonBody = Object.assign({}, request);
        DefaultContext.responseHeader = Object.assign({}, responseHeader);
        instance = new OptionNwpassModifyService(
            DefaultRequest,
            DefaultContext,
        );

        await LinesEntity.create({
            lineId: testdata.lineOK,
            tenantId: request.targetTenantId,
            lineStatus: "01",
            simFlag: false,
            updatedAt: testdata.lineUpdatedAt,
            nnumber: testdata.nnumber,
        });
        await LineTenantsEntity.create({
            lineId: testdata.lineOK,
            tenantId: request.targetTenantId,
        });
    });

    afterEach(async () => {
        await clearTestData();
        jest.restoreAllMocks();
        jest.clearAllMocks();
    });

    describe("NG cases", () => {
        it("should return CODE_540102 if ApiLinesDao.getLineInfoByLines failed (DB error)", async () => {
            const apiLinesDao = new APILinesDAO(DefaultRequest, DefaultContext);
            jest.spyOn(apiLinesDao, "getLineInfoByLines").mockRejectedValue(
                new ConnectionTimedOutError(new Error("Connection timed out")),
            );
            Object.defineProperty(instance, "apiLinesDao", {
                value: apiLinesDao,
            });

            const result = await instance.service(request);
            expect(result.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_540102,
            );
        });

        it("should return CODE_540103 if TenantManage.doCheck failed (DB error)", async () => {
            const tenantManage = new TenantManage(
                DefaultRequest,
                DefaultContext,
            );
            jest.spyOn(tenantManage, "doCheck").mockRejectedValue(
                new ConnectionTimedOutError(new Error("Connection timed out")),
            );
            Object.defineProperty(instance, "tenantManage", {
                value: tenantManage,
            });

            const result = await instance.service(request);
            expect(result.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_540103,
            );
        });

        it("should return CODE_540104 if ApiCommon.checkAbolishSo failed (DB error)", async () => {
            const apiCommon = new ApiCommon(DefaultRequest, DefaultContext);
            jest.spyOn(apiCommon, "checkAbolishSo").mockRejectedValue(
                new ConnectionTimedOutError(new Error("Connection timed out")),
            );
            Object.defineProperty(instance, "apiCommon", {
                value: apiCommon,
            });

            const result = await instance.service(request);
            expect(result.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_540104,
            );
        });

        it("should return CODE_540201 if ApiLinesDao.updateLineOptionInfo failed (DB error)", async () => {
            const apiLinesDao = new APILinesDAO(DefaultRequest, DefaultContext);
            jest.spyOn(apiLinesDao, "updateLineOptionInfo").mockRejectedValue(
                new ConnectionTimedOutError(new Error("Connection timed out")),
            );
            Object.defineProperty(instance, "apiLinesDao", {
                value: apiLinesDao,
            });

            const result = await instance.service(request);
            expect(result.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_540201,
            );
        });

        it("should return CODE_540201 if ApiLinesDao.updateLineOptionInfo failed (non DB error)", async () => {
            const apiLinesDao = new APILinesDAO(DefaultRequest, DefaultContext);
            jest.spyOn(apiLinesDao, "updateLineOptionInfo").mockRejectedValue(
                new TypeError("[test] lineNo is required"),
            );
            Object.defineProperty(instance, "apiLinesDao", {
                value: apiLinesDao,
            });

            const result = await instance.service(request);
            expect(result.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_540201,
            );
        });

        it("should return CODE_999999 if non DB error occured (not caught by internal try/catch)", async () => {
            const tenantManage = new TenantManage(
                DefaultRequest,
                DefaultContext,
            );
            jest.spyOn(tenantManage, "doCheck").mockRejectedValue(
                new TypeError("[test] targetTenantId is required"),
            );
            Object.defineProperty(instance, "tenantManage", {
                value: tenantManage,
            });

            const result = await instance.service(request);
            expect(result.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_999999,
            );
        });
    });
});
