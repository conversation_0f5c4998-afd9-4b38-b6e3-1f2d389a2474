import { addDays, formatDate } from "date-fns";
import { ConnectionTimedOutError, Sequelize } from "sequelize";

import "@/types/string.extension";

import {
    describeWithDB,
    generateProcessID,
    useFasterRetries,
} from "../../../testing/TestHelper";
import DefaultContext from "../../../testing/DefaultContext";
import DefaultRequest from "../../../testing/DefaultRequest";

import ResultCdConstants from "@/core/constant/ResultCdConstants";

import AppConfig from "@/appconfig";
import APILinesDAO from "@/core/dao/APILinesDAO";
import ResponseHeader from "@/core/dto/ResponseHeader";
import LineTerminalListAcquisitionInputDto from "@/core/dto/LineTerminalListAcquisitionInputDto";

import { LinesEntity } from "@/core/entity/LinesEntity";
import { TenantNnumbersEntity } from "@/core/entity/TenantNnumbersEntity";

import { usePsql } from "@/database/psql";

import LineTerminalListAquisitionService from "@/core/service/impl/LineTerminalListAquisitionService";
import APICommonDAO from "@/core/dao/APICommonDAO";

/**
 * Unit tests for cases which are not covered by NetworkContractChangeHandler.test.ts
 */
describeWithDB("/core/service/impl/LineTerminalListAquisitionService", () => {
    let sequelize: Sequelize;
    let instance: LineTerminalListAquisitionService;

    const testdata = {
        valid: {
            tenantId: "TST000",
            tenantIdOffice: "BON000",
            lineOK: "08024020000",
            nBan: "N021152174",
            lineUpdatedAt: addDays(new Date(), -1),
        },
        invalid: {
            tenantId: "TST001",
            nBan: "N02115442175",
        },
    };

    const request = {
        requestHeader: {
            sequenceNo: "1234567",
            senderSystemId: "0001",
            apiKey: "",
            functionType: "07",
        },
        tenantId: testdata.valid.tenantId,
        nBan: testdata.valid.nBan,
    } as LineTerminalListAcquisitionInputDto;

    const responseHeader = {
        sequenceNo: request.requestHeader.sequenceNo,
        receivedDate: formatDate(new Date(), "yyyy/MM/dd HH:mm:ss"),
        processCode: ResultCdConstants.CODE_000000,
        apiProcessID: generateProcessID(),
    } as ResponseHeader;

    const clearTestData = async () => {
        await TenantNnumbersEntity.destroy({
            where: {
                tenantId: testdata.valid.tenantId,
                nnumber: testdata.valid.nBan,
            },
        });
        await LinesEntity.destroy({
            where: { lineId: testdata.valid.lineOK },
        });
    };

    beforeAll(async () => {
        await AppConfig.loadCoreMvnoConfig(DefaultContext);
        sequelize = await usePsql();
        await clearTestData();
    });

    afterAll(async () => {
        await sequelize.close();
    });

    beforeEach(async () => {
        DefaultContext.jsonBody = Object.assign({}, request);
        DefaultContext.responseHeader = Object.assign({}, responseHeader);

        instance = new LineTerminalListAquisitionService(
            DefaultRequest,
            DefaultContext,
        );

        useFasterRetries();

        await TenantNnumbersEntity.create({
            nnumber: testdata.valid.nBan,
            tenantId: testdata.valid.tenantId,
        });

        await LinesEntity.create({
            lineId: testdata.valid.lineOK,
            nnumber: testdata.valid.nBan,
            tenantId: testdata.valid.tenantId,
            lineStatus: "01",
            simFlag: false,
            contractType: "3G",
            nwModifyFlag: false,
            updatedAt: testdata.valid.lineUpdatedAt,
        });
    });

    afterEach(async () => {
        await clearTestData();
        jest.restoreAllMocks();
        jest.clearAllMocks();
    });

    describe("Valid cases", () => {
        it("should return CODE_000000 code and one created line", async () => {
            const result = await instance.service(request);
            expect(result.responseHeader).toEqual(responseHeader);
            expect(result.lineList).toEqual([
                { lineNo: testdata.valid.lineOK },
            ]);
        });

        it("should return CODE_000000 and all tenant's nNumberList when N番 is empty and tenantId is valid", async () => {
            const invalidRequest = Object.assign({}, request);
            invalidRequest.nBan = "";
            DefaultContext.jsonBody = invalidRequest;

            const result = await instance.service(invalidRequest);
            expect(result.responseHeader).toEqual(responseHeader);
            expect(result.lineList.length).toBeGreaterThan(0);
        });

        it("should return CODE_000000 code and nNumberList when when N番 is empty and tenantId is valid and apiLinesDao.getNnumbers is empty", async () => {
            const invalidRequest = Object.assign({}, request);
            invalidRequest.nBan = "";
            DefaultContext.jsonBody = invalidRequest;

            const apiLinesDao = new APILinesDAO(DefaultRequest, DefaultContext);
            jest.spyOn(apiLinesDao, "getNnumbers").mockResolvedValueOnce([]);

            Object.defineProperty(instance, "apiLinesDao", {
                value: apiLinesDao,
            });

            const result = await instance.service(request);
            expect(result.lineList).toEqual([
                { lineNo: testdata.valid.lineOK },
            ]);
            expect(result.responseHeader).toEqual(responseHeader);
        });

        it("should return CODE_000000 code when apiLinesDao.getNnumbers returns no nNumberList and tenant exists but has no office set", async () => {
            const invalidRequest = Object.assign({}, request);
            invalidRequest.nBan = "";
            DefaultContext.jsonBody = invalidRequest;

            const apiLinesDao = new APILinesDAO(DefaultRequest, DefaultContext);
            jest.spyOn(apiLinesDao, "getNnumbers").mockResolvedValueOnce([]);
            Object.defineProperty(instance, "apiLinesDao", {
                value: apiLinesDao,
            });

            const result = await instance.service(invalidRequest);
            expect(result.responseHeader).toEqual(responseHeader);
            expect(result.lineList.length).toBe(0);
        });

        it("should return CODE_000000 when tenant has office, has no nNumberList, has planIdList, has resalePlanIdList, has lineGetPlanIdList", async () => {
            const invalidRequest = Object.assign({}, request);
            invalidRequest.nBan = "";
            invalidRequest.tenantId = testdata.valid.tenantIdOffice;
            const invalidResponse = Object.assign({}, responseHeader);
            invalidResponse.processCode = ResultCdConstants.CODE_070201;

            DefaultContext.jsonBody = invalidRequest;
            DefaultContext.responseHeader = invalidResponse;

            const apiLinesDao = new APILinesDAO(DefaultRequest, DefaultContext);
            jest.spyOn(apiLinesDao, "getNnumbers").mockResolvedValueOnce([]);
            Object.defineProperty(instance, "apiLinesDao", {
                value: apiLinesDao,
            });

            const result = await instance.service(invalidRequest);
            expect(result.responseHeader).toEqual(responseHeader);
            expect(result.lineList.length).toBeGreaterThan(0);
        }, 20000);
    });

    describe("NG cases", () => {
        it("should return CODE_070101 when nBan is invalid", async () => {
            const invalidRequest = Object.assign({}, request);
            invalidRequest.nBan = testdata.invalid.nBan;
            const invalidResponse = Object.assign({}, responseHeader);
            invalidResponse.processCode = ResultCdConstants.CODE_070101;

            DefaultContext.jsonBody = invalidRequest;
            DefaultContext.responseHeader = invalidResponse;

            instance = new LineTerminalListAquisitionService(
                DefaultRequest,
                DefaultContext,
            );

            const result = await instance.service(invalidRequest);
            expect(result.responseHeader).toEqual(invalidResponse);
        });

        it("should return CODE_070201 when apiLinesDao.getTenantNnumbers fails (DB error)", async () => {
            const apiLinesDao = new APILinesDAO(DefaultRequest, DefaultContext);
            jest.spyOn(apiLinesDao, "getTenantNnumbers").mockRejectedValue(
                new ConnectionTimedOutError(new Error("Connection timed out")),
            );
            Object.defineProperty(instance, "apiLinesDao", {
                value: apiLinesDao,
            });

            const result = await instance.service(request);
            expect(result.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_070201,
            );
        });

        it("should return CODE_070201 when tenantNnumbersEntity is null", async () => {
            const apiLinesDao = new APILinesDAO(DefaultRequest, DefaultContext);
            jest.spyOn(apiLinesDao, "getTenantNnumbers").mockResolvedValueOnce(
                null,
            );
            Object.defineProperty(instance, "apiLinesDao", {
                value: apiLinesDao,
            });

            const result = await instance.service(request);
            expect(result.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_070201,
            );
        });

        it("should return CODE_070201 when apiLinesDao.getLineIDList1 fails (DB error)", async () => {
            const apiLinesDao = new APILinesDAO(DefaultRequest, DefaultContext);
            jest.spyOn(apiLinesDao, "getLineIDList1").mockRejectedValue(
                new ConnectionTimedOutError(new Error("Connection timed out")),
            );
            Object.defineProperty(instance, "apiLinesDao", {
                value: apiLinesDao,
            });

            const result = await instance.service(request);
            expect(result.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_070201,
            );
        });

        it("should return CODE_070201 when N番 is empty and apiLinesDao.getNnumbers fails (DB error)", async () => {
            const invalidRequest = Object.assign({}, request);
            invalidRequest.nBan = "";
            const invalidResponse = Object.assign({}, responseHeader);
            invalidResponse.processCode = ResultCdConstants.CODE_070201;

            DefaultContext.jsonBody = invalidRequest;
            DefaultContext.responseHeader = invalidResponse;

            const apiLinesDao = new APILinesDAO(DefaultRequest, DefaultContext);
            jest.spyOn(apiLinesDao, "getNnumbers").mockRejectedValue(
                new ConnectionTimedOutError(new Error("Connection timed out")),
            );
            Object.defineProperty(instance, "apiLinesDao", {
                value: apiLinesDao,
            });

            const result = await instance.service(invalidRequest);
            expect(result.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_070201,
            );
        });

        it("should return CODE_070201 when apiLinesDao.getLineIDList2 fails (DB error)", async () => {
            const invalidRequest = Object.assign({}, request);
            invalidRequest.nBan = "";
            const invalidResponse = Object.assign({}, responseHeader);
            invalidResponse.processCode = ResultCdConstants.CODE_070201;

            DefaultContext.jsonBody = invalidRequest;
            DefaultContext.responseHeader = invalidResponse;

            const apiLinesDao = new APILinesDAO(DefaultRequest, DefaultContext);
            jest.spyOn(apiLinesDao, "getLineIDList2").mockRejectedValue(
                new ConnectionTimedOutError(new Error("Connection timed out")),
            );
            Object.defineProperty(instance, "apiLinesDao", {
                value: apiLinesDao,
            });

            const result = await instance.service(invalidRequest);
            expect(result.responseHeader).toEqual(invalidResponse);
        });

        it("should return CODE_070201 when tenantId has no nNumberList and apiCommonDao.getTenants fails (DB error)", async () => {
            const invalidRequest = Object.assign({}, request);
            invalidRequest.nBan = "";
            invalidRequest.tenantId = testdata.invalid.tenantId;
            const invalidResponse = Object.assign({}, responseHeader);
            invalidResponse.processCode = ResultCdConstants.CODE_070201;

            DefaultContext.jsonBody = invalidRequest;
            DefaultContext.responseHeader = invalidResponse;

            const apiCommonDao = new APICommonDAO(
                DefaultRequest,
                DefaultContext,
            );
            jest.spyOn(apiCommonDao, "getTenants").mockRejectedValue(
                new ConnectionTimedOutError(new Error("Connection timed out")),
            );
            Object.defineProperty(instance, "apiCommonDao", {
                value: apiCommonDao,
            });

            const result = await instance.service(invalidRequest);
            expect(result.responseHeader).toEqual(invalidResponse);
        });

        it("should return CODE_070201 when tenant has office and has no nNumberList and apiLinesDao.getPlanID fails (DB error)", async () => {
            const invalidRequest = Object.assign({}, request);
            invalidRequest.nBan = "";
            invalidRequest.tenantId = testdata.valid.tenantIdOffice;
            const invalidResponse = Object.assign({}, responseHeader);
            invalidResponse.processCode = ResultCdConstants.CODE_070201;

            DefaultContext.jsonBody = invalidRequest;
            DefaultContext.responseHeader = invalidResponse;

            const apiLinesDao = new APILinesDAO(DefaultRequest, DefaultContext);
            jest.spyOn(apiLinesDao, "getNnumbers").mockResolvedValueOnce([]);
            jest.spyOn(apiLinesDao, "getPlanID").mockRejectedValue(
                new ConnectionTimedOutError(new Error("Connection timed out")),
            );
            Object.defineProperty(instance, "apiLinesDao", {
                value: apiLinesDao,
            });

            const result = await instance.service(invalidRequest);
            expect(result.responseHeader).toEqual(invalidResponse);
        });

        it("should return CODE_070201 when tenant has office, has no nNumberList, has planIdList and apiLinesDao.getResalePlanID fails (DB error)", async () => {
            const invalidRequest = Object.assign({}, request);
            invalidRequest.nBan = "";
            invalidRequest.tenantId = testdata.valid.tenantIdOffice;
            const invalidResponse = Object.assign({}, responseHeader);
            invalidResponse.processCode = ResultCdConstants.CODE_070201;

            DefaultContext.jsonBody = invalidRequest;
            DefaultContext.responseHeader = invalidResponse;

            const apiLinesDao = new APILinesDAO(DefaultRequest, DefaultContext);
            jest.spyOn(apiLinesDao, "getNnumbers").mockResolvedValueOnce([]);
            jest.spyOn(apiLinesDao, "getResalePlanID").mockRejectedValue(
                new ConnectionTimedOutError(new Error("Connection timed out")),
            );
            Object.defineProperty(instance, "apiLinesDao", {
                value: apiLinesDao,
            });

            const result = await instance.service(invalidRequest);
            expect(result.responseHeader).toEqual(invalidResponse);
        });

        it("should return CODE_070201 when tenant has office, has no nNumberList, has planIdList, has resalePlanIdList and apiCommonDao.getPlanID fails (DB error)", async () => {
            const invalidRequest = Object.assign({}, request);
            invalidRequest.nBan = "";
            invalidRequest.tenantId = testdata.valid.tenantIdOffice;
            const invalidResponse = Object.assign({}, responseHeader);
            invalidResponse.processCode = ResultCdConstants.CODE_070201;

            DefaultContext.jsonBody = invalidRequest;
            DefaultContext.responseHeader = invalidResponse;

            const apiLinesDao = new APILinesDAO(DefaultRequest, DefaultContext);
            jest.spyOn(apiLinesDao, "getNnumbers").mockResolvedValueOnce([]);
            Object.defineProperty(instance, "apiLinesDao", {
                value: apiLinesDao,
            });

            const apiCommonDao = new APICommonDAO(
                DefaultRequest,
                DefaultContext,
            );
            jest.spyOn(apiCommonDao, "getPlanID").mockRejectedValue(
                new ConnectionTimedOutError(new Error("Connection timed out")),
            );
            Object.defineProperty(instance, "apiCommonDao", {
                value: apiCommonDao,
            });

            const result = await instance.service(invalidRequest);
            expect(result.responseHeader).toEqual(invalidResponse);
        });

        it("should return CODE_070201 when tenant has office, has no nNumberList, has planIdList, has resalePlanIdList, has lineGetPlanIdList and apiLinesDao.getLineIDList3 fails (DB error)", async () => {
            const invalidRequest = Object.assign({}, request);
            invalidRequest.nBan = "";
            invalidRequest.tenantId = testdata.valid.tenantIdOffice;
            const invalidResponse = Object.assign({}, responseHeader);
            invalidResponse.processCode = ResultCdConstants.CODE_070201;

            DefaultContext.jsonBody = invalidRequest;
            DefaultContext.responseHeader = invalidResponse;

            const apiLinesDao = new APILinesDAO(DefaultRequest, DefaultContext);
            jest.spyOn(apiLinesDao, "getNnumbers").mockResolvedValueOnce([]);
            jest.spyOn(apiLinesDao, "getLineIDList3").mockRejectedValue(
                new ConnectionTimedOutError(new Error("Connection timed out")),
            );
            Object.defineProperty(instance, "apiLinesDao", {
                value: apiLinesDao,
            });

            const result = await instance.service(invalidRequest);
            expect(result.responseHeader).toEqual(invalidResponse);
        });

        it("should return CODE_070201 when tenant has office and has no planIdList and apiLinesDao.getLineIDList4 fails (DB error)", async () => {
            const invalidRequest = Object.assign({}, request);
            invalidRequest.nBan = "";
            invalidRequest.tenantId = testdata.valid.tenantIdOffice;
            const invalidResponse = Object.assign({}, responseHeader);
            invalidResponse.processCode = ResultCdConstants.CODE_070201;

            DefaultContext.jsonBody = invalidRequest;
            DefaultContext.responseHeader = invalidResponse;

            const apiLinesDao = new APILinesDAO(DefaultRequest, DefaultContext);
            jest.spyOn(apiLinesDao, "getNnumbers").mockResolvedValueOnce([]);
            jest.spyOn(apiLinesDao, "getPlanID").mockResolvedValueOnce([]);
            jest.spyOn(apiLinesDao, "getLineIDList4").mockRejectedValue(
                new ConnectionTimedOutError(new Error("Connection timed out")),
            );
            Object.defineProperty(instance, "apiLinesDao", {
                value: apiLinesDao,
            });

            const result = await instance.service(invalidRequest);
            expect(result.responseHeader).toEqual(invalidResponse);
        });

        it("should return CODE_999999 for non DB error", async () => {
            const apiLinesDao = new APILinesDAO(DefaultRequest, DefaultContext);
            jest.spyOn(apiLinesDao, "getTenantNnumbers").mockRejectedValue(
                new TypeError("[test] planId is required"),
            );

            Object.defineProperty(instance, "apiLinesDao", {
                value: apiLinesDao,
            });

            const result = await instance.service(request);
            expect(result.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_999999,
            );
        });
    });
});
