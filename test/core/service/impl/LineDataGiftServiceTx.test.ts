import SOAPTestHelper from "../../../testing/soapTestHelper";
import {
    describeWithDB,
    generateProcessID,
    useFasterRetries
} from "../../../testing/TestHelper";
import DefaultContext from "../../../testing/DefaultContext";
import DefaultRequest from "../../../testing/DefaultRequest";
import LineDataGiftServiceTx from "@/core/service/impl/LineDataGiftServiceTx";
import LineDataGiftInputDto from "@/core/dto/LineDataGiftInputDto";
import LineDataGiftOutputDto from "@/core/dto/LineDataGiftOutputDto";
import { ConnectionTimedOutError, Sequelize } from "sequelize";
import { usePsql } from "@/database/psql";
import { disconnectMongo, useMongo } from "@/database/mongo";
import ResultCdConstants from "@/core/constant/ResultCdConstants";
import { addDays, addYears, formatDate } from "date-fns";
import nock from "nock";
import {
    deleteLineNumber,
    getConfigDataForTest,
    getDateNowForReservationTest, insertLineNumber,
} from "../../../testing/testdataHelper";
import AppConfig from "@/appconfig";
import { format } from "date-fns/format";
import MvnoUtil from "@/core/common/MvnoUtil";
import TenantManage from "@/core/common/TenantManage";
import { beforeEach, describe, expect, jest, test } from "@jest/globals";
import Check from "@/core/common/Check";
import BigNumber from "bignumber.js";
import SOAPCommon from "@/core/common/SOAPCommon";
import ServiceOrdersEntity from "@/core/entity/ServiceOrdersEntity";
import config from "config";
import ApiCommon from "@/core/common/ApiCommon";
import APILinesDAO from "@/core/dao/APILinesDAO";

describeWithDB("@/core/services/impl/LineDataGiftServiceTx", () => {
    let sequelize: Sequelize
    let instance: LineDataGiftServiceTx;

    const testdata = {
        tenantId: "TST000",
        sourceLineId: "08024042301",
        destinationLineId: "08024042302",
        todayDate: formatDate(new Date(), "yyyy/MM/dd"),
        previousDate: formatDate(addYears(new Date(), -1), "yyyy/MM/dd"),
        futureDate: formatDate(addYears(new Date(), 1), "yyyy/MM/dd"),
    }

    const __request: LineDataGiftInputDto = {
        requestHeader: {
            sequenceNo: "1234567",
            senderSystemId: "0001",
            apiKey: "",
            functionType: "19",
        },
        tenantId: testdata.tenantId,
        sourceLineNo: testdata.sourceLineId,
        destinationLineNo: testdata.destinationLineId,
        giftData: "000001000",
        targetSoId: undefined,
        reserve_flag: undefined,
        reserve_soId: undefined,
    };

    let param = Object.assign({}, __request);

    const responseHeader = {
        sequenceNo: __request.requestHeader.sequenceNo,
        receivedDate: formatDate(new Date(), "yyyy/MM/dd HH:mm:ss"),
        processCode: ResultCdConstants.CODE_000000,
        apiProcessID: generateProcessID(),
    };

    const nockHeaders: nock.ReplyHeaders = {
        "Content-Type": "text/xml",
    };

    let configData: ReturnType<typeof getConfigDataForTest>;

    beforeAll(async () => {
        sequelize = await usePsql();
        await useMongo(DefaultContext);
        await AppConfig.loadCoreMvnoConfig(DefaultContext);
        configData = getConfigDataForTest();
        nock.disableNetConnect();

        // await clearTestData();
    });

    afterAll(async () => {
        nock.enableNetConnect();

        await sequelize.close();
        await disconnectMongo(DefaultContext);
    });

    beforeEach(async () => {
        DefaultContext.jsonBody = Object.assign({}, __request);
        DefaultContext.responseHeader = Object.assign({}, responseHeader);
        jest.spyOn(MvnoUtil, "getDateTimeNow").mockReturnValue(
            responseHeader.receivedDate,
        );
        useFasterRetries();
        // jest.useFakeTimers();
        instance = new LineDataGiftServiceTx(
            DefaultRequest,
            DefaultContext,
        );

        // reset param
        param = Object.assign({}, __request);
    });

    afterEach(async () => {
        jest.restoreAllMocks();
        jest.clearAllMocks();

        await ServiceOrdersEntity.destroy({
            where: {
                serviceOrderId: responseHeader.apiProcessID,
            },
        });
        // jest.useRealTimers();
        nock.abortPendingRequests();
        nock.cleanAll();
    });

    beforeEach(() => {
        jest.spyOn(
            TenantManage.prototype,
            "checkTpcConnection",
        ).mockResolvedValue([true, "TpcDestIpAddress", "TpcDestIpAddress"]);
    });

    const createResponseXML = (result: "OK"| "NG", month_cap: number, month_use: number, gift_cap: number, gift_exp: string) => {
        return `
    <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
        xmlns:ns1="http://ws.apache.org/axis2">
        <soapenv:Body>
            <Result>${result}</Result>
            <dyn_sub_info>
                <dyn_node_info_0>
                    <dyn_bucket_info>
                        <dyn_cap_month_info>
                            <dyn_cap>${BigNumber(1024).times(1024).times(month_cap)}</dyn_cap>
                            <dyn_use>${BigNumber(1024).times(1024).times(month_use)}</dyn_use>
                        </dyn_cap_month_info>
                        <dyn_cap_tms_info_0>
                            <dyn_cap>${gift_cap}</dyn_cap>
                            <dyn_exp_date>${gift_exp}</dyn_exp_date>
                        </dyn_cap_tms_info_0>
                        <dyn_cap_tms_info_1>
                            <dyn_cap>${-gift_cap}</dyn_cap>
                            <dyn_exp_date>${gift_exp}</dyn_exp_date>
                        </dyn_cap_tms_info_1>
                    </dyn_bucket_info>
                </dyn_node_info_0>
            </dyn_sub_info>
        </soapenv:Body>
    </soapenv:Envelope>
            `.trim();
    }

    describe("common NG Cases", () => {
        test("should return CODE_190001 if wrong format of source line no", async () => {
            param.sourceLineNo = "0";
            const result: LineDataGiftOutputDto = await instance.service(param);
            expect(result.jsonBody).toBeDefined();
            expect(result.jsonBody.responseHeader).toBeDefined();
            expect(result.jsonBody.responseHeader.processCode).toBe(ResultCdConstants.CODE_190001);
        });

        test("should return CODE_190001 if wrong format of destination line no", async () => {
            param.destinationLineNo = "0";
            const result: LineDataGiftOutputDto = await instance.service(param);
            expect(result.jsonBody).toBeDefined();
            expect(result.jsonBody.responseHeader).toBeDefined();
            expect(result.jsonBody.responseHeader.processCode).toBe(ResultCdConstants.CODE_190001);
        });

        test("should return CODE_190001 if wrong format of gift data", async () => {
            param.giftData = null;
            const result: LineDataGiftOutputDto = await instance.service(param);
            expect(result.jsonBody).toBeDefined();
            expect(result.jsonBody.responseHeader).toBeDefined();
            expect(result.jsonBody.responseHeader.processCode).toBe(ResultCdConstants.CODE_190001);
        });

        test("should return CODE_190002 if wrong format of giftNgFlag", async () => {
            Object.defineProperty(instance, "giftNgFlag", {
                value: -1,
            });
            const result: LineDataGiftOutputDto = await instance.service(param);
            expect(result.jsonBody).toBeDefined();
            expect(result.jsonBody.responseHeader).toBeDefined();
            expect(result.jsonBody.responseHeader.processCode).toBe(ResultCdConstants.CODE_190002);
        });

        test("should return CODE_190003 if wrong format of giftNgTime", async () => {
            Object.defineProperty(instance, "giftNgTime", {
                value: "-1",
            });
            const result: LineDataGiftOutputDto = await instance.service(param);
            expect(result.jsonBody).toBeDefined();
            expect(result.jsonBody.responseHeader).toBeDefined();
            expect(result.jsonBody.responseHeader.processCode).toBe(ResultCdConstants.CODE_190003);
        });

        test("should return CODE_190004 if gift not allowed", async () => {
            Object.defineProperty(instance, "giftNgFlag", {
                value: "1",
            });
            const result: LineDataGiftOutputDto = await instance.service(param);
            expect(result.jsonBody).toBeDefined();
            expect(result.jsonBody.responseHeader).toBeDefined();
            expect(result.jsonBody.responseHeader.processCode).toBe(ResultCdConstants.CODE_190004);
        });
    });

    async function prepareTestData() {
        await insertLineNumber(testdata.tenantId, testdata.sourceLineId);
        await insertLineNumber(testdata.tenantId, testdata.destinationLineId);
    }

    async function clearTestData() {
        await deleteLineNumber(testdata.sourceLineId);
        await deleteLineNumber(testdata.destinationLineId);
        await sequelize.query(`DELETE FROM gift_line WHERE line_id = '${testdata.sourceLineId}'`);
        await sequelize.query(`DELETE FROM gift_line WHERE line_id = '${testdata.destinationLineId}'`);
    }

    beforeEach(async () => {
        await prepareTestData();
    });

    afterEach(async () => {
        await clearTestData();
    });

    describe("doGift", () => {
        describe("OK Case", () => {
            test("should return CODE_000000 and do gift if destination != source line id", async () => {
                nock(configData.soapApiUrl)
                    .post("", (body) => {
                        if (body.includes("<OperationType name=\"AllView\">")) {
                            return true;
                        }
                        expect(body).toContain("<Parameter name=\"cap_gft0\">1000</Parameter>");
                        expect(body).toContain("<Parameter name=\"cap_gft1\">-1000</Parameter>");
                        return true;
                    })
                    .reply(200, createResponseXML("OK", 2000, 0, 100, testdata.futureDate))
                    .persist();
                const result: LineDataGiftOutputDto = await instance.service(param);
                expect(result.jsonBody).toBeDefined();
                expect(result.jsonBody.responseHeader).toBeDefined();
                expect(result.jsonBody.responseHeader.processCode).toBe(ResultCdConstants.CODE_000000);
            });

            test("should return CODE_000000 even if giftData is a number", async () => {
                nock(configData.soapApiUrl)
                    .post("", (body) => {
                        if (body.includes("<OperationType name=\"AllView\">")) {
                            return true;
                        }
                        expect(body).toContain("<Parameter name=\"cap_gft0\">1000</Parameter>");
                        expect(body).toContain("<Parameter name=\"cap_gft1\">-1000</Parameter>");
                        return true;
                    })
                    .reply(200, createResponseXML("OK", 2000, 0, 100, testdata.futureDate))
                    .persist();
                param.giftData = 1000 as unknown as string;
                const result: LineDataGiftOutputDto = await instance.service(param);
                expect(result.jsonBody).toBeDefined();
                expect(result.jsonBody.responseHeader).toBeDefined();
                expect(result.jsonBody.responseHeader.processCode).toBe(ResultCdConstants.CODE_000000);
            });

            test.skip("should return CODE_000000 even if giftData is a number", async () => {
                // TODO test negative numbers
                nock(configData.soapApiUrl)
                    .post("", (body) => {
                        if (body.includes("<OperationType name=\"AllView\">")) {
                            return true;
                        }
                        expect(body).toContain("<Parameter name=\"cap_gft0\">1000</Parameter>");
                        expect(body).toContain("<Parameter name=\"cap_gft1\">-1000</Parameter>");
                        return true;
                    })
                    .reply(200, createResponseXML("OK", 2000, 0, 100, testdata.futureDate))
                    .persist();
                param.destinationLineNo = "99999999999";
                param.giftData = -1000 as unknown as string;
                const result: LineDataGiftOutputDto = await instance.service(param);
                expect(result.jsonBody).toBeDefined();
                expect(result.jsonBody.responseHeader).toBeDefined();
                expect(result.jsonBody.responseHeader.processCode).toBe(ResultCdConstants.CODE_000000);
            });
        });

        test("should return CODE_190100 if sourceLineNo and tenantId are not related", async () => {
            param.sourceLineNo = "00000000000";
            const result: LineDataGiftOutputDto = await instance.service(param);
            expect(result.jsonBody).toBeDefined();
            expect(result.jsonBody.responseHeader).toBeDefined();
            expect(result.jsonBody.responseHeader.processCode).toBe(ResultCdConstants.CODE_190100);
        });

        test("should return CODE_190100 if doCheck fails to query", async () => {
            jest.spyOn(TenantManage.prototype,"doCheck").mockImplementation(async () =>{
                throw new ConnectionTimedOutError(new Error("timeout error"))
            })
            const result: LineDataGiftOutputDto = await instance.service(param);
            expect(result.jsonBody).toBeDefined();
            expect(result.jsonBody.responseHeader).toBeDefined();
            expect(result.jsonBody.responseHeader.processCode).toBe(ResultCdConstants.CODE_190100);
        });

        test("should return CODE_999999 if doCheck gets an unexpected error", async () => {
            jest.spyOn(TenantManage.prototype,"doCheck").mockRejectedValue(
                new TypeError("some error"),
            );
            const result: LineDataGiftOutputDto = await instance.service(param);
            expect(result.jsonBody).toBeDefined();
            expect(result.jsonBody.responseHeader).toBeDefined();
            expect(result.jsonBody.responseHeader.processCode).toBe(ResultCdConstants.CODE_999999);
        });

        test("should return CODE_190101 if destinationLineNo and tenantId are not related", async () => {
            param.destinationLineNo = "00000000000";
            const result: LineDataGiftOutputDto = await instance.service(param);
            expect(result.jsonBody).toBeDefined();
            expect(result.jsonBody.responseHeader).toBeDefined();
            expect(result.jsonBody.responseHeader.processCode).toBe(ResultCdConstants.CODE_190101);
        });
        // TODO add docheck when destination LineNo doesnt match
        test("should return CODE_190101 if doCheck fails to query", async () => {
            param.sourceLineNo = "99999999999";
            param.destinationLineNo = "00000000000";
            jest.spyOn(TenantManage.prototype,"doCheck").mockImplementation(async () =>{
                throw new ConnectionTimedOutError(new Error("timeout error"))
            })
            const result: LineDataGiftOutputDto = await instance.service(param);
            expect(result.jsonBody).toBeDefined();
            expect(result.jsonBody.responseHeader).toBeDefined();
            expect(result.jsonBody.responseHeader.processCode).toBe(ResultCdConstants.CODE_190101);
        });

        test("should return CODE_999999 if doCheck gets an unxpected error", async () => {
            param.sourceLineNo = "99999999999";
            param.destinationLineNo = "00000000000";
            jest.spyOn(TenantManage.prototype,"doCheck").mockRejectedValue(new TypeError("some error"));
            const result: LineDataGiftOutputDto = await instance.service(param);
            expect(result.jsonBody).toBeDefined();
            expect(result.jsonBody.responseHeader).toBeDefined();
            expect(result.jsonBody.responseHeader.processCode).toBe(ResultCdConstants.CODE_999999);
        });

        test("should return CODE_190102 if sourceLine is abolished", async () => {
            jest.spyOn(ApiCommon.prototype, "checkAbolishSo").mockResolvedValue(false);
            const result: LineDataGiftOutputDto = await instance.service(param);
            expect(result.jsonBody).toBeDefined();
            expect(result.jsonBody.responseHeader).toBeDefined();
            expect(result.jsonBody.responseHeader.processCode).toBe(ResultCdConstants.CODE_190102);
        });

        test("should return CODE_190102 if sourceLine fails to query", async () => {
            jest.spyOn(ApiCommon.prototype, "checkAbolishSo").mockImplementation(async () =>{
                throw new ConnectionTimedOutError(new Error("timeout error"));
            })
            const result: LineDataGiftOutputDto = await instance.service(param);
            expect(result.jsonBody).toBeDefined();
            expect(result.jsonBody.responseHeader).toBeDefined();
            expect(result.jsonBody.responseHeader.processCode).toBe(ResultCdConstants.CODE_190102);
        });

        test("should return CODE_999999 if sourceLine gets an unexpected error", async () => {
            jest.spyOn(ApiCommon.prototype, "checkAbolishSo").mockRejectedValue(new TypeError("some error"));
            const result: LineDataGiftOutputDto = await instance.service(param);
            expect(result.jsonBody).toBeDefined();
            expect(result.jsonBody.responseHeader).toBeDefined();
            expect(result.jsonBody.responseHeader.processCode).toBe(ResultCdConstants.CODE_999999);
        });

        test("should return CODE_190103 if destinationLine is abolished", async () => {
            jest.spyOn(ApiCommon.prototype, "checkAbolishSo").mockResolvedValueOnce(true).mockResolvedValueOnce(false);
            const result: LineDataGiftOutputDto = await instance.service(param);
            expect(result.jsonBody).toBeDefined();
            expect(result.jsonBody.responseHeader).toBeDefined();
            expect(result.jsonBody.responseHeader.processCode).toBe(ResultCdConstants.CODE_190103);
        });

        test("should return CODE_190103 if checkAbolishSo fails to query", async () => {
            param.sourceLineNo = "99999999999";
            jest.spyOn(ApiCommon.prototype, "checkAbolishSo").mockImplementation(async () =>{
                throw new ConnectionTimedOutError(new Error("timeout error"));
            })
            const result: LineDataGiftOutputDto = await instance.service(param);
            expect(result.jsonBody).toBeDefined();
            expect(result.jsonBody.responseHeader).toBeDefined();
            expect(result.jsonBody.responseHeader.processCode).toBe(ResultCdConstants.CODE_190103);
        });

        test("should return CODE_999999 if checkAbolishSo gets an unexpected error", async () => {
            param.sourceLineNo = "99999999999";
            jest.spyOn(ApiCommon.prototype, "checkAbolishSo").mockRejectedValue(new TypeError("some error"));
            const result: LineDataGiftOutputDto = await instance.service(param);
            expect(result.jsonBody).toBeDefined();
            expect(result.jsonBody.responseHeader).toBeDefined();
            expect(result.jsonBody.responseHeader.processCode).toBe(ResultCdConstants.CODE_999999);
        });

        test("should return CODE_190115 if sourceLine is suspended", async () => {
            jest.spyOn(ApiCommon.prototype, "checkLineSuspend").mockResolvedValue(false);
            const result: LineDataGiftOutputDto = await instance.service(param);
            expect(result.jsonBody).toBeDefined();
            expect(result.jsonBody.responseHeader).toBeDefined();
            expect(result.jsonBody.responseHeader.processCode).toBe(ResultCdConstants.CODE_190115);
        });

        test("should return CODE_190115 if checkLineSuspend fails to query", async () => {
            jest.spyOn(ApiCommon.prototype, "checkLineSuspend").mockImplementation(async () =>{
                throw new ConnectionTimedOutError(new Error("timeout error"))});
            const result: LineDataGiftOutputDto = await instance.service(param);
            expect(result.jsonBody).toBeDefined();
            expect(result.jsonBody.responseHeader).toBeDefined();
            expect(result.jsonBody.responseHeader.processCode).toBe(ResultCdConstants.CODE_190115);
        });

        test("should return CODE_999999 if checkLineSuspend gets an unexpected error", async () => {
            jest.spyOn(ApiCommon.prototype, "checkLineSuspend").mockRejectedValue(new TypeError("some error"))
            const result: LineDataGiftOutputDto = await instance.service(param);
            expect(result.jsonBody).toBeDefined();
            expect(result.jsonBody.responseHeader).toBeDefined();
            expect(result.jsonBody.responseHeader.processCode).toBe(ResultCdConstants.CODE_999999);
        });

        test("should return CODE_190116 if destinationLine is suspended", async () => {
            jest.spyOn(ApiCommon.prototype, "checkLineSuspend").mockResolvedValueOnce(true).mockResolvedValueOnce(false);
            const result: LineDataGiftOutputDto = await instance.service(param);
            expect(result.jsonBody).toBeDefined();
            expect(result.jsonBody.responseHeader).toBeDefined();
            expect(result.jsonBody.responseHeader.processCode).toBe(ResultCdConstants.CODE_190116);
        });

        test("should return CODE_190116 if checkLineSuspend fails to query", async () => {
            param.sourceLineNo = "99999999999";
            jest.spyOn(ApiCommon.prototype, "checkLineSuspend").mockImplementation(async () =>{
                throw new ConnectionTimedOutError(new Error("timeout error"));
            })
            const result: LineDataGiftOutputDto = await instance.service(param);
            expect(result.jsonBody).toBeDefined();
            expect(result.jsonBody.responseHeader).toBeDefined();
            expect(result.jsonBody.responseHeader.processCode).toBe(ResultCdConstants.CODE_190116);
        });

        test("should return CODE_999999 if checkLineSuspend gets an unexpected error", async () => {
            param.sourceLineNo = "99999999999";
            jest.spyOn(ApiCommon.prototype, "checkLineSuspend").mockRejectedValue(new TypeError("some error"));
            const result: LineDataGiftOutputDto = await instance.service(param);
            expect(result.jsonBody).toBeDefined();
            expect(result.jsonBody.responseHeader).toBeDefined();
            expect(result.jsonBody.responseHeader.processCode).toBe(ResultCdConstants.CODE_999999);
        });

        test("should return CODE_190104 if sourceLine exist in gift table when trying to insert", async () => {
            jest.spyOn(APILinesDAO.prototype, "getGiftLine").mockResolvedValue(testdata.sourceLineId);
            const result: LineDataGiftOutputDto = await instance.service(param);
            expect(result.jsonBody).toBeDefined();
            expect(result.jsonBody.responseHeader).toBeDefined();
            expect(result.jsonBody.responseHeader.processCode).toBe(ResultCdConstants.CODE_190104);
        });
        // TODO doesnt give response
        test.skip("should return CODE_190104 if getGiftLine fails to query", async () => {
            jest.spyOn(APILinesDAO.prototype, "getGiftLine").mockImplementation(async () => {
                throw new ConnectionTimedOutError(new Error("timeout error"));
            })
            const result: LineDataGiftOutputDto = await instance.service(param);
            expect(result.jsonBody).toBeDefined();
            expect(result.jsonBody.responseHeader).toBeDefined();
            expect(result.jsonBody.responseHeader.processCode).toBe(ResultCdConstants.CODE_190104);
        });

        test.skip("should return CODE_999999 if getGiftLine gets an unexpected error", async () => {
            jest.spyOn(APILinesDAO.prototype, "getGiftLine").mockImplementation(async () => {
                throw new ConnectionTimedOutError(new Error("timeout error"));
            })
            const result: LineDataGiftOutputDto = await instance.service(param);
            expect(result.jsonBody).toBeDefined();
            expect(result.jsonBody.responseHeader).toBeDefined();
            expect(result.jsonBody.responseHeader.processCode).toBe(ResultCdConstants.CODE_999999);
        });

        test("should return CODE_190105 if failed to insert sourceLine into table", async () => {
            jest.spyOn(APILinesDAO.prototype, "insertGiftLineNotDup").mockResolvedValue(false);
            const result: LineDataGiftOutputDto = await instance.service(param);
            expect(result.jsonBody).toBeDefined();
            expect(result.jsonBody.responseHeader).toBeDefined();
            expect(result.jsonBody.responseHeader.processCode).toBe(ResultCdConstants.CODE_190105);
        });

        test("should return CODE_190106 if destinationLine exist in gift table when trying to insert", async () => {
            jest.spyOn(APILinesDAO.prototype, "getGiftLine").mockResolvedValueOnce(null).mockResolvedValueOnce(testdata.destinationLineId);
            const result: LineDataGiftOutputDto = await instance.service(param);
            expect(result.jsonBody).toBeDefined();
            expect(result.jsonBody.responseHeader).toBeDefined();
            expect(result.jsonBody.responseHeader.processCode).toBe(ResultCdConstants.CODE_190106);
        });

        test("should return CODE_190107 if failed to insert destinationLine into table", async () => {
            jest.spyOn(APILinesDAO.prototype, "insertGiftLineNotDup").mockResolvedValueOnce(true).mockResolvedValueOnce(false);
            const result: LineDataGiftOutputDto = await instance.service(param);
            expect(result.jsonBody).toBeDefined();
            expect(result.jsonBody.responseHeader).toBeDefined();
            expect(result.jsonBody.responseHeader.processCode).toBe(ResultCdConstants.CODE_190107);
        });
        // timeout error i guess due to uncommitted or rollbacked transaction
        test.skip("should return CODE_190107 if failed to insert destinationLine into table", async () => {
            jest.spyOn(APILinesDAO.prototype, "insertGiftLineNotDup").mockImplementation(async () =>{
                throw new ConnectionTimedOutError(new Error("timeout error"));
            })
            const result: LineDataGiftOutputDto = await instance.service(param);
            expect(result.jsonBody).toBeDefined();
            expect(result.jsonBody.responseHeader).toBeDefined();
            expect(result.jsonBody.responseHeader.processCode).toBe(ResultCdConstants.CODE_190107);
        });

        test("should return CODE_190108 if failed to check TPC status", async () => {
            jest.spyOn(TenantManage.prototype, "checkTpcConnection").mockResolvedValue([false, "", ""]);
            const result: LineDataGiftOutputDto = await instance.service(param);
            expect(result.jsonBody).toBeDefined();
            expect(result.jsonBody.responseHeader).toBeDefined();
            expect(result.jsonBody.responseHeader.processCode).toBe(ResultCdConstants.CODE_190108);
        });

        test("should return CODE_190108 if checkTpcConnection fails to query", async () => {
            jest.spyOn(TenantManage.prototype, "checkTpcConnection").mockImplementation(async () =>{
                throw new ConnectionTimedOutError(new Error("timeout error"));
            })
            const result: LineDataGiftOutputDto = await instance.service(param);
            expect(result.jsonBody).toBeDefined();
            expect(result.jsonBody.responseHeader).toBeDefined();
            expect(result.jsonBody.responseHeader.processCode).toBe(ResultCdConstants.CODE_190108);
        });

        test("should return CODE_190114 if deleteGiftLine fails to query", async () => {
            jest.spyOn(APILinesDAO.prototype, "deleteGiftLine").mockImplementation(async () =>{
                throw new ConnectionTimedOutError(new Error("timeout error"));
            })
            const result: LineDataGiftOutputDto = await instance.service(param);
            expect(result.jsonBody).toBeDefined();
            expect(result.jsonBody.responseHeader).toBeDefined();
            expect(result.jsonBody.responseHeader.processCode).toBe(ResultCdConstants.CODE_190114);
        });

        test("should return CODE_190114 if deleteGiftLine gets an unexpected error", async () => {
            jest.spyOn(APILinesDAO.prototype, "deleteGiftLine").mockRejectedValue(new TypeError("some error"));
            const result: LineDataGiftOutputDto = await instance.service(param);
            expect(result.jsonBody).toBeDefined();
            expect(result.jsonBody.responseHeader).toBeDefined();
            expect(result.jsonBody.responseHeader.processCode).toBe(ResultCdConstants.CODE_190114);
        });

        test("should return CODE_999999 if checkTpcConnection gets an unexpected error", async () => {
            jest.spyOn(TenantManage.prototype, "checkTpcConnection").mockRejectedValue(new TypeError("some error"));
            const result: LineDataGiftOutputDto = await instance.service(param);
            expect(result.jsonBody).toBeDefined();
            expect(result.jsonBody.responseHeader).toBeDefined();
            expect(result.jsonBody.responseHeader.processCode).toBe(ResultCdConstants.CODE_999999);
        });

        test("should return CODE_190109 if failed to call SOAP API for allView", async () => {
            nock(configData.soapApiUrl)
                .post("")
                .reply(500, createResponseXML("NG", 2000, 0, 100, testdata.futureDate))
                .persist();
            const result: LineDataGiftOutputDto = await instance.service(param);
            expect(result.jsonBody).toBeDefined();
            expect(result.jsonBody.responseHeader).toBeDefined();
            expect(result.jsonBody.responseHeader.processCode).toBe(ResultCdConstants.CODE_190109);
        });

        test("should return CODE_190110 if failed to call SOAP API for gift", async () => {
            nock(configData.soapApiUrl)
                .post("", (body) => {
                    if (body.includes("<OperationType name=\"AllView\">")) {
                        return true;
                    }
                    return false;
                })
                .reply((uri, requestBody) => {
                    if (requestBody.includes("81" + testdata.sourceLineId.substring(1))) {
                        return [200, createResponseXML("OK", 2000, 0, 100, testdata.futureDate)];
                    }
                    if (requestBody.includes("81" + testdata.destinationLineId.substring(1))) {
                        return [500, createResponseXML("NG", 2000, 0, 100, testdata.futureDate)];
                    }
                    return [500, createResponseXML("NG", 2000, 0, 100, testdata.futureDate)];
                })
                .persist();
            const result: LineDataGiftOutputDto = await instance.service(param);
            expect(result.jsonBody).toBeDefined();
            expect(result.jsonBody.responseHeader).toBeDefined();
            expect(result.jsonBody.responseHeader.processCode).toBe(ResultCdConstants.CODE_190110);
        });

        test("should return CODE_190111 if failed format check of response XML data", async () => {
            nock(configData.soapApiUrl)
                .post("", (body) => {
                    if (body.includes("<OperationType name=\"AllView\">")) {
                        return true;
                    }
                    return false;
                })
                .reply(200, createResponseXML("OK", -1, -2, -3, "-1"))
                .persist();
            const result: LineDataGiftOutputDto = await instance.service(param);
            expect(result.jsonBody).toBeDefined();
            expect(result.jsonBody.responseHeader).toBeDefined();
            expect(result.jsonBody.responseHeader.processCode).toBe(ResultCdConstants.CODE_190111);
        });

        test("should return CODE_190112 if remaining data is not enough", async () => {
            nock(configData.soapApiUrl)
                .post("", (body) => {
                    if (body.includes("<OperationType name=\"AllView\">")) {
                        return true;
                    }
                    return false;
                })
                .reply(200, createResponseXML("OK", 0, 0, 100, testdata.futureDate))
                .persist();

            const result: LineDataGiftOutputDto = await instance.service(param);
            expect(result.jsonBody).toBeDefined();
            expect(result.jsonBody.responseHeader).toBeDefined();
            expect(result.jsonBody.responseHeader.processCode).toBe(ResultCdConstants.CODE_190112);
        });

        test("should return CODE_190113 if failed to call SOAP API for gift", async () => {
            nock(configData.soapApiUrl)
                .post("")
                .reply((uri, requestBody) => {
                    if (requestBody.includes("<OperationType name=\"AllView\">")) {
                        return [200, createResponseXML("OK", 2000, 0, 100, testdata.futureDate)];
                    } else {
                        return [500, createResponseXML("NG", 2000, 0, 100, testdata.futureDate)];
                    }
                })
                .persist();
            const result: LineDataGiftOutputDto = await instance.service(param);
            expect(result.jsonBody).toBeDefined();
            expect(result.jsonBody.responseHeader).toBeDefined();
            expect(result.jsonBody.responseHeader.processCode).toBe(ResultCdConstants.CODE_190113);
        });
    });

    describe("doSubtract", () => {
        beforeEach(() => {
            param.destinationLineNo = param.sourceLineNo;
        });
        describe("OK Case", () => {
            test("should return CODE_000000 and do subtract if destination == source line id", async () => {
                nock(configData.soapApiUrl)
                    .post("", (body) => {
                        if (body.includes("<OperationType name=\"AllView\">")) {
                            return true;
                        }
                        expect(body).toContain("<Parameter name=\"cap_gft1\">-1000</Parameter>");
                        return true;
                    })
                    .reply(200, createResponseXML("OK", 2000, 0, 100, testdata.futureDate))
                    .persist();
                // XMLの内容を確認
                const result: LineDataGiftOutputDto = await instance.service(param);
                expect(result.jsonBody).toBeDefined();
                expect(result.jsonBody.responseHeader).toBeDefined();
                expect(result.jsonBody.responseHeader.processCode).toBe(ResultCdConstants.CODE_000000);
            });
        });

        describe("NG Cases", () => {
            test("should return CODE_190201 if sourceLineNo and tenantId are not related", async () => {
                param.sourceLineNo = "00000000000";
                param.destinationLineNo = param.sourceLineNo;
                const result: LineDataGiftOutputDto = await instance.service(param);
                expect(result.jsonBody).toBeDefined();
                expect(result.jsonBody.responseHeader).toBeDefined();
                expect(result.jsonBody.responseHeader.processCode).toBe(ResultCdConstants.CODE_190201);
            });

            test("should return CODE_190202 if line is abolished", async () => {
                jest.spyOn(ApiCommon.prototype, "checkAbolishSo").mockResolvedValue(false);
                const result: LineDataGiftOutputDto = await instance.service(param);
                expect(result.jsonBody).toBeDefined();
                expect(result.jsonBody.responseHeader).toBeDefined();
                expect(result.jsonBody.responseHeader.processCode).toBe(ResultCdConstants.CODE_190202);
            });

            test("should return CODE_190202 if checkAbolishSo fails to query", async () => {
                param.destinationLineNo = param.sourceLineNo;
                jest.spyOn(ApiCommon.prototype, "checkAbolishSo").mockImplementation(async () =>{
                    throw new ConnectionTimedOutError(new Error("timeout error"));
                })
                const result: LineDataGiftOutputDto = await instance.service(param);
                expect(result.jsonBody).toBeDefined();
                expect(result.jsonBody.responseHeader).toBeDefined();
                expect(result.jsonBody.responseHeader.processCode).toBe(ResultCdConstants.CODE_190202);
            });

            test("should return CODE_999999 if checkAbolishSo gets an unexpected error", async () => {
                param.destinationLineNo = param.sourceLineNo;
                jest.spyOn(ApiCommon.prototype, "checkAbolishSo").mockRejectedValue(new TypeError("some error"));
                const result: LineDataGiftOutputDto = await instance.service(param);
                expect(result.jsonBody).toBeDefined();
                expect(result.jsonBody.responseHeader).toBeDefined();
                expect(result.jsonBody.responseHeader.processCode).toBe(ResultCdConstants.CODE_999999);
            });

            test("should return CODE_190211 if checkLineSuspend fails to query", async () => {
                param.destinationLineNo = param.sourceLineNo;
                jest.spyOn(ApiCommon.prototype, "checkLineSuspend").mockImplementation(async () =>{
                    throw new ConnectionTimedOutError(new Error("timeout error"));
                })
                const result: LineDataGiftOutputDto = await instance.service(param);
                expect(result.jsonBody).toBeDefined();
                expect(result.jsonBody.responseHeader).toBeDefined();
                expect(result.jsonBody.responseHeader.processCode).toBe(ResultCdConstants.CODE_190211);
            });

            test("should return CODE_999999 if checkLineSuspend gets an unxepected error", async () => {
                param.destinationLineNo = param.sourceLineNo;
                jest.spyOn(ApiCommon.prototype, "checkLineSuspend").mockRejectedValue(new TypeError("some error"));
                const result: LineDataGiftOutputDto = await instance.service(param);
                expect(result.jsonBody).toBeDefined();
                expect(result.jsonBody.responseHeader).toBeDefined();
                expect(result.jsonBody.responseHeader.processCode).toBe(ResultCdConstants.CODE_999999);
            });

            test("should return CODE_190205 if checkTpcConnection fails to query", async () => {
                param.destinationLineNo = param.sourceLineNo;
                jest.spyOn(TenantManage.prototype,"checkTpcConnection").mockImplementation(async () =>{
                    throw new ConnectionTimedOutError(new Error("timeout error"));
                })
                const result: LineDataGiftOutputDto = await instance.service(param);
                expect(result.jsonBody).toBeDefined();
                expect(result.jsonBody.responseHeader).toBeDefined();
                expect(result.jsonBody.responseHeader.processCode).toBe(ResultCdConstants.CODE_190205);
            });

            test("should return 190210 if deleteGiftLine fails to query and its doSubtract", async () => {
                param.destinationLineNo = param.sourceLineNo;
                jest.spyOn(APILinesDAO.prototype,"deleteGiftLine").mockImplementation(async () =>{
                    throw new ConnectionTimedOutError(new Error("timeout error"));
                })
                const result: LineDataGiftOutputDto = await instance.service(param);
                expect(result.jsonBody).toBeDefined();
                expect(result.jsonBody.responseHeader).toBeDefined();
                expect(result.jsonBody.responseHeader.processCode).toBe(ResultCdConstants.CODE_190210);
            });

            test("should return 190210 if deleteGiftLine gets an unexpected error and its doSubtract", async () => {
                param.destinationLineNo = param.sourceLineNo;
                jest.spyOn(APILinesDAO.prototype,"deleteGiftLine").mockRejectedValue(new TypeError("some error"));
                const result: LineDataGiftOutputDto = await instance.service(param);
                expect(result.jsonBody).toBeDefined();
                expect(result.jsonBody.responseHeader).toBeDefined();
                expect(result.jsonBody.responseHeader.processCode).toBe(ResultCdConstants.CODE_190210);
            });

            test("should return CODE_190211 if line is suspended", async () => {
                jest.spyOn(ApiCommon.prototype, "checkLineSuspend").mockResolvedValue(false);
                const result: LineDataGiftOutputDto = await instance.service(param);
                expect(result.jsonBody).toBeDefined();
                expect(result.jsonBody.responseHeader).toBeDefined();
                expect(result.jsonBody.responseHeader.processCode).toBe(ResultCdConstants.CODE_190211);
            });

            test("should return CODE_190203 if line exists in gift table when trying to insert", async () => {
                jest.spyOn(APILinesDAO.prototype, "getGiftLine").mockResolvedValue(testdata.sourceLineId);
                const result: LineDataGiftOutputDto = await instance.service(param);
                expect(result.jsonBody).toBeDefined();
                expect(result.jsonBody.responseHeader).toBeDefined();
                expect(result.jsonBody.responseHeader.processCode).toBe(ResultCdConstants.CODE_190203);
            });

            test("should return CODE_190204 if failed to insert line into table", async () => {
                jest.spyOn(APILinesDAO.prototype, "insertGiftLineNotDup").mockResolvedValue(false);
                const result: LineDataGiftOutputDto = await instance.service(param);
                expect(result.jsonBody).toBeDefined();
                expect(result.jsonBody.responseHeader).toBeDefined();
                expect(result.jsonBody.responseHeader.processCode).toBe(ResultCdConstants.CODE_190204);
            });

            test("should return CODE_190205 if failed to check TPC status", async () => {
                jest.spyOn(TenantManage.prototype, "checkTpcConnection").mockResolvedValue([false, "", ""]);
                const result: LineDataGiftOutputDto = await instance.service(param);
                expect(result.jsonBody).toBeDefined();
                expect(result.jsonBody.responseHeader).toBeDefined();
                expect(result.jsonBody.responseHeader.processCode).toBe(ResultCdConstants.CODE_190205);
            });
            test("should return CODE_190206 if failed to call SOAP API for allView", async () => {
                nock(configData.soapApiUrl)
                    .post("")
                    .reply(500, createResponseXML("NG", 2000, 0, 100, testdata.futureDate))
                    .persist();
                const result: LineDataGiftOutputDto = await instance.service(param);
                expect(result.jsonBody).toBeDefined();
                expect(result.jsonBody.responseHeader).toBeDefined();
                expect(result.jsonBody.responseHeader.processCode).toBe(ResultCdConstants.CODE_190206);
            });

            test("should return CODE_190207 if failed format check of response XML data", async () => {
                nock(configData.soapApiUrl)
                    .post("", (body) => {
                        if (body.includes("<OperationType name=\"AllView\">")) {
                            return true;
                        }
                        return false;
                    })
                    .reply(200, createResponseXML("OK", -1, -2, -3, "-1"))
                    .persist();
                const result: LineDataGiftOutputDto = await instance.service(param);
                expect(result.jsonBody).toBeDefined();
                expect(result.jsonBody.responseHeader).toBeDefined();
                expect(result.jsonBody.responseHeader.processCode).toBe(ResultCdConstants.CODE_190207);
            });

            test("should return CODE_190208 if remaining data is not enough", async () => {
                nock(configData.soapApiUrl)
                    .post("", (body) => {
                        if (body.includes("<OperationType name=\"AllView\">")) {
                            return true;
                        }
                        return false;
                    })
                    .reply(200, createResponseXML("OK", 0, 0, 100, testdata.futureDate))
                    .persist();

                const result: LineDataGiftOutputDto = await instance.service(param);
                expect(result.jsonBody).toBeDefined();
                expect(result.jsonBody.responseHeader).toBeDefined();
                expect(result.jsonBody.responseHeader.processCode).toBe(ResultCdConstants.CODE_190208);
            });

            test("should return CODE_190209 if failed to call SOAP API for gift", async () => {
                nock(configData.soapApiUrl)
                    .post("")
                    .reply((uri, requestBody) => {
                        if (requestBody.includes("<OperationType name=\"AllView\">")) {
                            return [200, createResponseXML("OK", 2000, 0, 100, testdata.futureDate)];
                        } else {
                            return [500, createResponseXML("NG", 2000, 0, 100, testdata.futureDate)];
                        }
                    })
                    .persist();
                const result: LineDataGiftOutputDto = await instance.service(param);
                expect(result.jsonBody).toBeDefined();
                expect(result.jsonBody.responseHeader).toBeDefined();
                expect(result.jsonBody.responseHeader.processCode).toBe(ResultCdConstants.CODE_190209);
            });
        });
    });
});