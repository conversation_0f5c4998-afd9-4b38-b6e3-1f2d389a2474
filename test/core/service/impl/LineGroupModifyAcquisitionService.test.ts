import config from "config";
import {
    describeWithDB,
    generateProcessID,
    generateSOID,
} from "../../../testing/TestHelper";
import { ConnectionTimedOutError, Sequelize, Transaction } from "sequelize";
import LineGroupModifyAcquisitionService from "@/core/service/impl/LineGroupModifyAcquisitionService";
import { usePsql } from "@/database/psql";
import { useMongo } from "@/database/mongo";
const enqueueMessage = jest.fn();
jest.mock("../../../../src/services/servicebus", () => ({
    __esModule: true,
    default: enqueueMessage,
}));
import { disconnectMongo } from "@/database/mongo";
import DefaultContext from "../../../testing/DefaultContext";
import DefaultRequest from "../../../testing/DefaultRequest";
import nock from "nock";
import AppConfig from "@/appconfig";
import MvnoUtil from "@/core/common/MvnoUtil";
import ServiceOrdersEntity from "@/core/entity/ServiceOrdersEntity";
import LineGroupModifyAcquisitionInputDto from "@/core/dto/LineGroupModifyAcquisitionInputDto";
import { addDays, formatDate, format } from "date-fns";
import ResultCdConstants from "@/core/constant/ResultCdConstants";
import {
    getConfigDataForTest,
    getDateNowForReservationTest,
} from "../../../testing/testdataHelper";
import APICommonDAO from "@/core/dao/APICommonDAO";
import APILinesGroupDAO from "@/core/dao/ApiLinesGroupDAO";
import { TenantsEntity } from "@/core/entity/TenantsEntity";
import TenantManage from "@/core/common/TenantManage";
import LineLineGroupsEntity from "@/core/entity/LineLineGroupsEntity";
import ApiCommon from "@/core/common/ApiCommon";
import { LineGroupModifyAcquisitionHandler } from "@/functions/LineGroupModifyAcquisitionHandler";
import CoreSwimmyApiLog from "@/models/coreSwimmyApiLog";
import LineGroupsEntity from "@/core/entity/LineGroupsEntity";

const createResponseXML = (
    result: "OK" | "NG",
    test: string,
    errorInfomation: string,
) => {
    return `
    <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
        xmlns:ns1="http://ws.apache.org/axis2">
        <soapenv:Body>
            <Result>${result}</Result>
            <ServiceProfileInfomation>
                <test>${test}</test>
            </ServiceProfileInfomation>
            <ErrorInfomation>${errorInfomation}</ErrorInfomation>
        </soapenv:Body>
    </soapenv:Envelope>
    `.trim();
};

describeWithDB("@/core/service/impl/LineGroupModifyAcquisitionService", () => {
    let sequelize: Sequelize;
    let instance: LineGroupModifyAcquisitionService;

    const testdata = {
        reserve_date: "",
        lineNo: "00001963317",
        lineGroupId: "500005",
        targetSoId: generateSOID(),
    };
    const request: LineGroupModifyAcquisitionInputDto = {
        requestHeader: {
            sequenceNo: "1234567",
            senderSystemId: "0001",
            apiKey: "",
            functionType: "15",
        },
        reserve_flag: undefined,
        reserve_soId: undefined,
        reserve_date: "", // YYYY/MM/DD HH:mm
        targetSoId: testdata.targetSoId, // Add the missing property 'targetSoId'
        tenantId: "BON000",
        lineNo: testdata.lineNo,
        operation: "1", // Add the missing property 'operation'
        lineGroupId: testdata.lineGroupId,
        totalVolumeControlFlag: "0",
        csvUnnecessaryFlag: "0",
    };

    const testdata_rkm = {
        tenantId: "RKM000",
        lineGroupId: "600005",
    }

    const _request_rnk: LineGroupModifyAcquisitionInputDto = {
        requestHeader: {
            sequenceNo: "1234567",
            senderSystemId: "0001",
            apiKey: "",
            functionType: "15",
        },
        reserve_flag: undefined,
        reserve_soId: undefined,
        reserve_date: "",
        targetSoId: testdata.targetSoId,
        tenantId: testdata_rkm.tenantId,
        lineNo: testdata.lineNo,
        operation: "0",
        lineGroupId: testdata_rkm.lineGroupId,
        totalVolumeControlFlag: "0",
        csvUnnecessaryFlag: "0",
    };
    const responseHeader = {
        sequenceNo: request.requestHeader.sequenceNo,
        receivedDate: formatDate(new Date(), "yyyy/MM/dd HH:mm:ss"),
        processCode: ResultCdConstants.CODE_000000,
        apiProcessID: generateProcessID(),
    };

    const nockHeaders: nock.ReplyHeaders = {
        "Content-Type": "text/xml",
    };

    const nulls = [null, null, "127.0.0.1"];

    let configData: ReturnType<typeof getConfigDataForTest>;

    /**
     * check whether SwimmyApiLog exists and Service Bus is called
     */
    const checkCoreSwimmyApiLog = async (
        apiProcessID: string,
        exists: boolean,
    ) => {
        const apiLog = await CoreSwimmyApiLog.findOne({
            requestOrderId: apiProcessID,
        });
        if (exists) {
            expect(apiLog).not.toBeNull();
            expect(enqueueMessage).toBeCalledTimes(1);
        } else {
            expect(apiLog).toBeNull();
            expect(enqueueMessage).not.toBeCalled();
        }
    };

    async function prepareTestData() {
        await Promise.all([
            LineLineGroupsEntity.create({
                lineId: testdata.lineNo,
                groupId: testdata.lineGroupId,
                basicCapacity: 1024,
            }),
        ]);
    }
    async function clearTestData() {
        await Promise.all([
            LineLineGroupsEntity.destroy({
                where: {
                    lineId: testdata.lineNo,
                    groupId: testdata.lineGroupId,
                },
            }),
            CoreSwimmyApiLog.deleteMany({
                requestOrderId: responseHeader.apiProcessID,
            }),
            ServiceOrdersEntity.destroy({
                where: {
                    serviceOrderId: responseHeader.apiProcessID,
                }
            }),
        ]);
    }
    beforeAll(async () => {
        sequelize = await usePsql();
        await useMongo(DefaultContext);
        await AppConfig.loadCoreMvnoConfig(DefaultContext);
        configData = getConfigDataForTest();
        nock.disableNetConnect();
        await useMongo(DefaultContext);
        await clearTestData();
        testdata.reserve_date = format(
            addDays(getDateNowForReservationTest(configData), 1),
            "yyyy/MM/dd HH:mm",
        );
    });

    afterAll(async () => {
        await clearTestData();
        nock.enableNetConnect();
        await sequelize.close();
        await disconnectMongo(DefaultContext);
    });

    beforeEach(async () => {
        (enqueueMessage as jest.Mock).mockClear();
        DefaultContext.jsonBody = Object.assign({}, request);
        DefaultContext.responseHeader = Object.assign({}, responseHeader);
        jest.spyOn(MvnoUtil, "getDateTimeNow").mockReturnValue(
            responseHeader.receivedDate,
        );
        await prepareTestData();
        instance = new LineGroupModifyAcquisitionService(
            DefaultRequest,
            DefaultContext,
        );
    });

    afterEach(async () => {
        jest.restoreAllMocks();
        jest.clearAllMocks();
        nock.abortPendingRequests();
        nock.cleanAll();
        await clearTestData();
        await ServiceOrdersEntity.destroy({
            where: {
                serviceOrderId: responseHeader.apiProcessID,
            },
        });
    });

    const checkLineLineGroupIdExists = async (
        lineId: string,
        groupId: string,
        exists = true,
    ) => {
        const lineLineGroupsEntity = await LineLineGroupsEntity.findOne({
            where: {
                lineId,
                groupId,
            },
        });
        if (exists) {
            expect(lineLineGroupsEntity).not.toBeNull();
        } else {
            expect(lineLineGroupsEntity).toBeNull();
        }
    };
    describe("OK CASES operation = 0, delete", () => {
        beforeEach(() => {
            DefaultContext.jsonBody.operation = "0";
            jest.spyOn(
                TenantManage.prototype,
                "checkTpcConnection",
            ).mockResolvedValue([true, "TpcDestIpAddress", "TpcDestIpAddress"]);

            nock(configData.soapApiUrl)
                .post("")
                .reply(200, createResponseXML("OK", "test", ""))
                .persist();
        });
        test("return CODE_000000 order type 0", async () => {
            await checkLineLineGroupIdExists(
                testdata.lineNo,
                testdata.lineGroupId,
                true,
            );
            const response = await LineGroupModifyAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_000000,
            );
            await checkLineLineGroupIdExists(
                testdata.lineNo,
                testdata.lineGroupId,
                false,
            );
            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, true);
        });
        test("return CODE_000000 order type 1", async () => {
            await checkLineLineGroupIdExists(
                testdata.lineNo,
                testdata.lineGroupId,
                true,
            );
            DefaultContext.jsonBody.reserve_date = testdata.reserve_date;
            const response = await LineGroupModifyAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_000000,
            );
            // only ordertype 0,2 is deleted
            await checkLineLineGroupIdExists(
                testdata.lineNo,
                testdata.lineGroupId,
                true,
            );
            // only ordertype 0,2 is csv is created
            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, true);
        });

        test("return CODE_000000 order type 2", async () => {
            await checkLineLineGroupIdExists(
                testdata.lineNo,
                testdata.lineGroupId,
                true,
            );
            DefaultContext.jsonBody.reserve_date = testdata.reserve_date;
            DefaultContext.jsonBody.reserve_flag = true;
            DefaultContext.jsonBody.reserve_soId = "1234567";
            const response = await LineGroupModifyAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_000000,
            );
            await checkLineLineGroupIdExists(
                testdata.lineNo,
                testdata.lineGroupId,
                false,
            );
            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
        });

        test("return CODE_000000 : operation is number (0)", async () => {
            await checkLineLineGroupIdExists(
                testdata.lineNo,
                testdata.lineGroupId,
                true,
            );
            DefaultContext.jsonBody.operation = 0;
            const response = await LineGroupModifyAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_000000,
            );
            await checkLineLineGroupIdExists(
                testdata.lineNo,
                testdata.lineGroupId,
                false,
            );
            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, true);
        });

        test("return CODE_000000 : lineGroupId is number", async () => {
            await checkLineLineGroupIdExists(
                testdata.lineNo,
                testdata.lineGroupId,
                true,
            );
            DefaultContext.jsonBody.lineGroupId = parseInt(
                testdata.lineGroupId,
                10,
            );
            const response = await LineGroupModifyAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_000000,
            );
            await checkLineLineGroupIdExists(
                testdata.lineNo,
                testdata.lineGroupId,
                false,
            );
            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, true);
        });

        test("return CODE_000000 : totalVolumeControlFlag is number (0)", async () => {
            await checkLineLineGroupIdExists(
                testdata.lineNo,
                testdata.lineGroupId,
                true,
            );
            DefaultContext.jsonBody.totalVolumeControlFlag = 0;
            const response = await LineGroupModifyAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_000000,
            );
            await checkLineLineGroupIdExists(
                testdata.lineNo,
                testdata.lineGroupId,
                false,
            );
            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, true);
        });

        test("return CODE_000000 : totalVolumeControlFlag is number (1)", async () => {
            await checkLineLineGroupIdExists(
                testdata.lineNo,
                testdata.lineGroupId,
                true,
            );
            DefaultContext.jsonBody.totalVolumeControlFlag = 1;
            const response = await LineGroupModifyAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_000000,
            );
            await checkLineLineGroupIdExists(
                testdata.lineNo,
                testdata.lineGroupId,
                false,
            );
            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, true);
        });

        test("return CODE_000000 : csvUnnecessaryFlag is number (1)", async () => {
            await checkLineLineGroupIdExists(
                testdata.lineNo,
                testdata.lineGroupId,
                true,
            );
            DefaultContext.jsonBody.csvUnnecessaryFlag = 1;
            const response = await LineGroupModifyAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_000000,
            );
            await checkLineLineGroupIdExists(
                testdata.lineNo,
                testdata.lineGroupId,
                false,
            );
            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false); // CSVフラグ不要
        });

        test("return CODE_000000 : lineGroupId is number", async () => {
            await checkLineLineGroupIdExists(
                testdata.lineNo,
                testdata.lineGroupId,
                true,
            );
            DefaultContext.jsonBody.lineGroupId = parseInt(
                testdata.lineGroupId,
                10,
            );
            const response = await LineGroupModifyAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_000000,
            );
            await checkLineLineGroupIdExists(
                testdata.lineNo,
                testdata.lineGroupId,
                false,
            );
            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, true);
        });
    });

    describe("OK CASES operation = 0, delete RINKモバイル", () => {

        beforeAll(async () => {
            await LineGroupsEntity.create({
                tenantId: 'RKM000',
                groupId: '600005',
                status: 1,
                planId: 20001
            })
        })
        afterAll(async () => {
            await LineGroupsEntity.destroy({
                where: {
                    tenantId: 'RKM000',
                }
            })
        })
        beforeEach(async () => {
            await Promise.all([
                LineLineGroupsEntity.create({
                    lineId: testdata.lineNo,
                    groupId: '600005',
                    basicCapacity: 1024,
                }),
            ]);

            jest.spyOn(
                TenantManage.prototype,
                "checkTpcConnection",
            ).mockResolvedValue([true, "TpcDestIpAddress", "TpcDestIpAddress"]);

            nock(configData.soapApiUrl)
                .post("")
                .reply(200, createResponseXML("OK", "test", ""))
                .persist();
            jest.spyOn(TenantManage.prototype, "doCheck").mockResolvedValue([
                true,
                "N250013024",
            ]);
            DefaultContext.jsonBody = Object.assign({}, _request_rnk);
        });

        afterEach(async () => {
            await Promise.all([
                LineLineGroupsEntity.destroy({
                    where: {
                        lineId: testdata.lineNo,
                        groupId: testdata_rkm.lineGroupId,
                    },
                })
            ])
        })
        test("return CODE_000000 order type 0", async () => {
            await checkLineLineGroupIdExists(
                testdata.lineNo,
                testdata_rkm.lineGroupId,
                true,
            );
            const response = await LineGroupModifyAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_000000,
            );
            await checkLineLineGroupIdExists(
                testdata.lineNo,
                testdata_rkm.lineGroupId,
                false,
            );
            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, true);
        });
        test("return CODE_000000 order type 1", async () => {
            await checkLineLineGroupIdExists(
                testdata.lineNo,
                testdata_rkm.lineGroupId,
                true,
            );
            DefaultContext.jsonBody.reserve_date = testdata.reserve_date;
            const response = await LineGroupModifyAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_000000,
            );

            await checkLineLineGroupIdExists(
                testdata.lineNo,
                testdata_rkm.lineGroupId,
                true,
            );

            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, true);
        });

        test("return CODE_000000 order type 2", async () => {
            await checkLineLineGroupIdExists(
                testdata.lineNo,
                testdata_rkm.lineGroupId,
                true,
            );
            DefaultContext.jsonBody.reserve_date = testdata.reserve_date;
            DefaultContext.jsonBody.reserve_flag = true;
            DefaultContext.jsonBody.reserve_soId = "1234567";
            const response = await LineGroupModifyAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_000000,
            );
            await checkLineLineGroupIdExists(
                testdata.lineNo,
                testdata_rkm.lineGroupId,
                false,
            );
            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
        });

        test("return CODE_000000 : csvUnnecessaryFlag is number (1)", async () => {
            await checkLineLineGroupIdExists(
                testdata.lineNo,
                testdata_rkm.lineGroupId,
                true,
            );
            DefaultContext.jsonBody.csvUnnecessaryFlag = 1;
            const response = await LineGroupModifyAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_000000,
            );
            await checkLineLineGroupIdExists(
                testdata.lineNo,
                testdata_rkm.lineGroupId,
                false,
            );
            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
        });

        test("return CODE_000000 : totalVolumeControlFlag is number (1)", async () => {
            await checkLineLineGroupIdExists(
                testdata.lineNo,
                testdata_rkm.lineGroupId,
                true,
            );
            DefaultContext.jsonBody.totalVolumeControlFlag = 1;
            const response = await LineGroupModifyAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_000000,
            );
            await checkLineLineGroupIdExists(
                testdata.lineNo,
                testdata_rkm.lineGroupId,
                false,
            );
            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, true);
        });

    });

    describe("OK CASES operation = 1, insert", () => {
        beforeEach(async () => {
            await clearTestData();
            DefaultContext.jsonBody.operation = "1";
            jest.spyOn(
                TenantManage.prototype,
                "checkTpcConnection",
            ).mockResolvedValue([true, "TpcDestIpAddress", "TpcDestIpAddress"]);

            nock(configData.soapApiUrl)
                .post("")
                .reply(200, createResponseXML("OK", "test", ""));
            // .persist();
        });

        test("return CODE_000000 order type 0", async () => {
            await checkLineLineGroupIdExists(
                testdata.lineNo,
                testdata.lineGroupId,
                false,
            );
            const response = await LineGroupModifyAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_000000,
            );
            await checkLineLineGroupIdExists(
                testdata.lineNo,
                testdata.lineGroupId,
                true,
            );
            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, true); // core swimmy created??
        });

        test("return CODE_000000 order type 1", async () => {
            await checkLineLineGroupIdExists(
                testdata.lineNo,
                testdata.lineGroupId,
                false,
            );
            DefaultContext.jsonBody.reserve_date = testdata.reserve_date;
            const response = await LineGroupModifyAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_000000,
            );
            // only ordertype 0,2 is inserted
            await checkLineLineGroupIdExists(
                testdata.lineNo,
                testdata.lineGroupId,
                false,
            );
            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, true); // coreswimmy created??
        });
        test("return CODE_000000 order type 2", async () => {
            await checkLineLineGroupIdExists(
                testdata.lineNo,
                testdata.lineGroupId,
                false,
            );
            DefaultContext.jsonBody.reserve_date = testdata.reserve_date;
            DefaultContext.jsonBody.reserve_flag = true;
            DefaultContext.jsonBody.reserve_soId = "1234567";
            const response = await LineGroupModifyAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_000000,
            );
            await checkLineLineGroupIdExists(
                testdata.lineNo,
                testdata.lineGroupId,
                true,
            );
            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
        });

        test("return CODE_000000 : operation is number (1)", async () => {
            await checkLineLineGroupIdExists(
                testdata.lineNo,
                testdata.lineGroupId,
                false,
            );
            DefaultContext.jsonBody.operation = 1;
            const response = await LineGroupModifyAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_000000,
            );
            await checkLineLineGroupIdExists(
                testdata.lineNo,
                testdata.lineGroupId,
                true,
            );
            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, true); // core swimmy created??
        });
    });

    describe("OK CASES operation = 1, insert RINKモバイル", () => {
        beforeEach(async () => {
            await clearTestData();
            DefaultContext.jsonBody = Object.assign({}, _request_rnk);
            DefaultContext.jsonBody.operation = "1";
            jest.spyOn(
                TenantManage.prototype,
                "checkTpcConnection",
            ).mockResolvedValue([true, "TpcDestIpAddress", "TpcDestIpAddress"]);

            jest.spyOn(TenantManage.prototype, "doCheck").mockResolvedValue([
                true,
                "N250013024",
            ]);

            nock(configData.soapApiUrl)
                .post("")
                .reply(200, createResponseXML("OK", "test", "test"));
            await Promise.all([
                LineGroupsEntity.create({
                    tenantId: 'RKM000',
                    groupId: '600005',
                    status: 1,
                    planId: 20001
                })
            ]);
        });

        afterEach(async () => {
            await Promise.all([
                LineLineGroupsEntity.destroy({
                    where: {
                        lineId: testdata.lineNo,
                        groupId: testdata_rkm.lineGroupId,
                    },
                }),
                await LineGroupsEntity.destroy({
                    where: {
                        tenantId: 'RKM000',
                    }
                })
            ])
        })

        test("return CODE_000000 order type 0", async () => {
            await checkLineLineGroupIdExists(
                testdata.lineNo,
                testdata_rkm.lineGroupId,
                false,
            );
            const response = await LineGroupModifyAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_000000,
            );
            await checkLineLineGroupIdExists(
                testdata.lineNo,
                testdata_rkm.lineGroupId,
                true,
            );
            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, true);
        })

        test("RKM return CODE_000000 order type 1 RKM", async () => {
            await checkLineLineGroupIdExists(
                testdata.lineNo,
                testdata_rkm.lineGroupId,
                false,
            );
            DefaultContext.jsonBody.reserve_date = testdata.reserve_date;
            const response = await LineGroupModifyAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_000000,
            );

            await checkLineLineGroupIdExists(
                testdata.lineNo,
                testdata_rkm.lineGroupId,
                false,
            );
            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, true);
        });

        test("RKM return CODE_000000 order type 2", async () => {

            await checkLineLineGroupIdExists(
                testdata.lineNo,
                testdata_rkm.lineGroupId,
                false,
            );
            DefaultContext.jsonBody.reserve_date = testdata.reserve_date;
            DefaultContext.jsonBody.reserve_flag = true;
            DefaultContext.jsonBody.reserve_soId = "1234567";
            const response = await LineGroupModifyAcquisitionHandler.Handler(
                DefaultRequest,
                DefaultContext,
            );
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_000000,
            );
            await checkLineLineGroupIdExists(
                testdata.lineNo,
                testdata_rkm.lineGroupId,
                true,
            );
            await checkCoreSwimmyApiLog(responseHeader.apiProcessID, false);
        });
    });

    describe("NG CASES before operation", () => {
        let param = Object.assign({}, request);
        beforeEach(() => {
            param = Object.assign({}, request);
        });
        test("return CODE_140006 if order type cant be determined", async () => {
            param.reserve_date = "some date";
            param.reserve_flag = true;
            param.reserve_soId = undefined;
            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_140006,
            );
        });

        test("return CODE_999999 if order type is 2 (予約実行オーダ) and client address invalid", async () => {
            try {
                DefaultContext.isInternalRequest = false;
                param.reserve_date = "some date";
                param.reserve_flag = true;
                param.reserve_soId = "1234567";
                const response = await instance.service(
                    param,
                    false,
                    null,
                    null,
                    null,
                );
                expect(response?.jsonBody).toBeDefined();
                expect(response.jsonBody.responseHeader).toBeDefined();
                expect(response.jsonBody.responseHeader.processCode).toBe(
                    ResultCdConstants.CODE_999999,
                );
            } finally {
                DefaultContext.isInternalRequest = true;
            }
        });

        test("return CODE_140001 if lineNo is not valid", async () => {
            param.lineNo = "";
            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_140001,
            );
        });

        test("return CODE_140001 if operation is not 1 or 0", async () => {
            param.operation = "";
            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_140001,
            );
        });

        test("return CODE_140001 if lineGroupID is not valid", async () => {
            param.lineGroupId = "";
            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_140001,
            );
        });

        test("return CODE_140001 if totalVolumeControlFlag is not 1 or 0", async () => {
            param.totalVolumeControlFlag = "";
            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_140001,
            );
        });

        test("return CODE_140001 if totalVolumeControlFlag is undefined", async () => {
            delete param.totalVolumeControlFlag;
            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_140001,
            );
        });

        test("return CODE_140001 if totalVolumeControlFlag is null", async () => {
            param.totalVolumeControlFlag = null;
            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_140001,
            );
        });

        test("return CODE_140001 if csvUnnecessaryFlag is not 1 or 0", async () => {
            param.csvUnnecessaryFlag = "3";
            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_140001,
            );
        });

        test("return CODE_140002 if tenant info is null", async () => {
            param.tenantId = "";
            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_140002,
            );
        });

        test("return CODE_140002 if fails to get tenant info", async () => {
            jest.spyOn(
                APICommonDAO.prototype,
                "getTenants",
            ).mockImplementationOnce(async () => {
                throw new ConnectionTimedOutError(new Error("timeout error"));
            });
            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_140002,
            );
        });

        test("return CODE_140004 if lineGroupId doesnt exist in lineGroups while tenant is （C-OCN）以外", async () => {
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getLineGroupsInfo",
            ).mockResolvedValueOnce(Promise.resolve(null));
            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_140004,
            );
        });

        test("return CODE_140004 if fails to get lineGroups info", async () => {
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getLineGroupsInfo",
            ).mockImplementationOnce(async () => {
                throw new ConnectionTimedOutError(new Error("timeout error"));
            });
            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_140004,
            );
        });

        test("return CODE_140004 if lineGroupId exist in lineGroups while tenant is （C-OCN）", async () => {
            jest.spyOn(
                APICommonDAO.prototype,
                "getTenants",
            ).mockResolvedValueOnce(
                Promise.resolve(
                    TenantsEntity.build({
                        tenantId: "test",
                        tenantType: 1,
                    }),
                ),
            );
            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_140004,
            );
        });

        test("return 140001 if reserveDate is invalid [予約前オーダ,予約実行オーダ] ", async () => {
            param.reserve_date = "2025-01-01 00:00:00";
            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_140001,
            );
        });

        test("return CODE_140007 if reserveDate is in the past [予約前オーダ] ", async () => {
            param.reserve_date = format(
                addDays(new Date(), -1),
                "yyyy/MM/dd HH:mm",
            );
            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_140007,
            );
        });

        test("return CODE_140008 if reservationDateExecutionUnits is not set correctly", async () => {
            param.reserve_date = testdata.reserve_date;
            Object.defineProperty(instance, "reservationDateExecutionUnits", {
                value: "",
            });
            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_140008,
            );
        });

        test("return CODE_140009 if reserve_date is after past reservationDateExecutionUnits", async () => {
            param.reserve_date = format(
                addDays(
                    new Date(),
                    parseInt(configData.reservationDateExecutionUnits, 10) + 1,
                ),
                "yyyy/MM/dd HH:mm",
            );
            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_140009,
            );
        });

        test("return CODE_140010 if reservationsLimitDays is not set correctly", async () => {
            param.reserve_date = testdata.reserve_date;
            Object.defineProperty(instance, "reservationsLimitDays", {
                value: "",
            });
            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_140010,
            );
        });

        test("return CODE_140011 if reserve_date is after past reservationsLimitDays", async () => {
            param.reserve_date = format(
                addDays(
                    getDateNowForReservationTest(configData),
                    parseInt(configData.reservationsLimitDays, 10) + 1,
                ),
                "yyyy/MM/dd HH:mm",
            );

            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_140011,
            );
        });

        test("return CODE_999999 if unxepected error occurs", async () => {
            jest.spyOn(APICommonDAO.prototype, "getTenants").mockRejectedValue(
                new Error("test error"),
            );
            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_999999,
            );
        });
    });
    describe("NG CASES operation = 1", () => {
        let param = Object.assign({}, request);
        beforeEach(async () => {
            await clearTestData();
            param = Object.assign({}, request);
            param.operation = "1";
            jest.spyOn(
                TenantManage.prototype,
                "checkTpcConnection",
            ).mockResolvedValue([true, "TpcDestIpAddress", "TpcDestIpAddress"]);
        });

        test("CODE_140100 if sql exception occurs querying LineLineGroups", async () => {
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getLineId",
            ).mockImplementation(async () => {
                throw new ConnectionTimedOutError(new Error("timeout error"));
            });
            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_140100,
            );
        });

        test("CODE_140100 if lineId exist in LineLineGroups", async () => {
            await prepareTestData();
            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_140100,
            );
        });

        test("CODE_140100 if tenant check returns false", async () => {
            jest.spyOn(TenantManage.prototype, "doCheck").mockResolvedValue([
                false,
                "",
            ]);
            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_140100,
            );
        });

        test("CODE_140100 if sql exception occurs querying tenant check", async () => {
            jest.spyOn(TenantManage.prototype, "doCheck").mockImplementation(
                async () => {
                    throw new ConnectionTimedOutError(
                        new Error("timeout error"),
                    );
                },
            );
            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_140100,
            );
        });

        test("CODE_140100 if line is abolished", async () => {
            jest.spyOn(ApiCommon.prototype, "checkAbolishSo").mockResolvedValue(
                false,
            );
            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_140100,
            );
        });

        test("CODE_140100 if sql exception occurs querying abolished check", async () => {
            jest.spyOn(
                ApiCommon.prototype,
                "checkAbolishSo",
            ).mockImplementation(async () => {
                throw new ConnectionTimedOutError(new Error("timeout error"));
            });
            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_140100,
            );
        });

        test("CODE_140101 if line not found in lines", async () => {
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getResaleChargePlanLock",
            ).mockResolvedValue(null);
            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_140101,
            );
        });

        test.skip("CODE_140101 if sql exception occurs getting lines", async () => {
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getResaleChargePlanLock",
            ).mockImplementation(async () => {
                throw new ConnectionTimedOutError(new Error("timeout error"));
            });
            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_140101,
            );
        });

        test("CODE_140101 if unexpected error occurs getting lines", async () => {
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getResaleChargePlanLock",
            ).mockRejectedValue(new Error("test error"));
            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_140101,
            );
        });

        test("should return CODE_140101 if failed to get line lock", async () => {
            Object.defineProperty(instance, "LOCK_WAIT_MILLISEC", {
                value: 1,
            });
            let tx: Transaction;
            const aPILinesGroupDAO = new APILinesGroupDAO(
                DefaultRequest,
                DefaultContext,
            );

            try {
                tx = await sequelize.transaction();
                await aPILinesGroupDAO.getResaleChargePlanLock(
                    param.lineNo,
                    true,
                    tx,
                );

                const response = await instance.service(param, false, ...nulls);
                expect(response?.jsonBody).toBeDefined();
                expect(response.jsonBody.responseHeader).toBeDefined();
                expect(response.jsonBody.responseHeader.processCode).toEqual(
                    ResultCdConstants.CODE_140101,
                );
            } finally {
                await tx.rollback();
            }
        });

        test("CODE_140101 if line.planId not found in groupPlans", async () => {
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getUpdateServicePatternGroup",
            ).mockResolvedValue(null);
            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_140101,
            );
        });

        test.skip("CODE_140101 if sql exception occurs querying getUpdateServicePatternGroup", async () => {
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getUpdateServicePatternGroup",
            ).mockImplementation(async () => {
                throw new ConnectionTimedOutError(new Error("timeout error"));
            });
            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_140101,
            );
        });

        test("CODE_141111 if no TPC connection found", async () => {
            jest.spyOn(
                TenantManage.prototype,
                "checkTpcConnection",
            ).mockResolvedValue([false, "", ""]);
            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_141111,
            );
        });

        test.skip("CODE_141111 if sql exception occurs querying TPC connection", async () => {
            jest.spyOn(
                TenantManage.prototype,
                "checkTpcConnection",
            ).mockImplementation(async () => {
                throw new ConnectionTimedOutError(new Error("timeout error"));
            });
            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_141111,
            );
        });

        test("CODE_140103 if if TPC cannot be reached (SOAPException)", async () => {
            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_140103,
            );
        });

        test("CODE_140103 if if SOAP response is NG", async () => {
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, createResponseXML("NG", "", "errror"));
            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_140103,
            );
        });

        test("CODE_140104 if unexpected error occurs inserting linesGroup", async () => {
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, createResponseXML("OK", "test", ""));
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "insertLinesGroup",
            ).mockRejectedValue(new Error("test error"));
            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_140104,
            );
        });

        test.skip("CODE_140104 if sql error occurs inserting linesGroup", async () => {
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, createResponseXML("OK", "test", ""));
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "insertLinesGroup",
            ).mockImplementation(async () => {
                throw new ConnectionTimedOutError(new Error("timeout error"));
            });
            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_140104,
            );
        });
    });
    describe("NG CASES operation = 0", () => {
        let param = Object.assign({}, request);
        beforeEach(() => {
            param = Object.assign({}, request);
            param.operation = "0";
            jest.spyOn(
                TenantManage.prototype,
                "checkTpcConnection",
            ).mockResolvedValue([true, "TpcDestIpAddress", "TpcDestIpAddress"]);
        });
        test("should return CODE_140201 if lineId is not found in lineLineGroups", async () => {
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getLineIdGroup",
            ).mockResolvedValue(null);
            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_140201,
            );
        });

        test("should return CODE_140201 if sql exception occurs querying LineLineGroups", async () => {
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "getLineIdGroup",
            ).mockImplementation(async () => {
                throw new ConnectionTimedOutError(new Error("timeout error"));
            });

            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_140201,
            );
        });

        test("should return CODE_140204 if no TPC connection found", async () => {
            jest.spyOn(
                TenantManage.prototype,
                "checkTpcConnection",
            ).mockResolvedValue([false, "", ""]);
            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_140204,
            );
        });

        test("should return CODE_140204 if sql exception occurs querying TPC connection", async () => {
            jest.spyOn(
                TenantManage.prototype,
                "checkTpcConnection",
            ).mockImplementation(async () => {
                throw new ConnectionTimedOutError(new Error("timeout error"));
            });
            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_140204,
            );
        });

        test("should return CODE_140202 if SOAPException occurs", async () => {
            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_140202,
            );
        });

        test("should return CODE_140202 if SOAP Response is NG", async () => {
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, createResponseXML("NG", "", "error"));
            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_140202,
            );
        });
        test("should return CODE_140203 if unexpected error occurs deleting linesGroup", async () => {
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, createResponseXML("OK", "test", ""));
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "deleteLinesGroup",
            ).mockRejectedValue(new Error("test error"));
            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_140203,
            );
        });

        test("should return CODE_140203 if sql error occurs deleting linesGroup", async () => {
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, createResponseXML("OK", "test", ""));
            jest.spyOn(
                APILinesGroupDAO.prototype,
                "deleteLinesGroup",
            ).mockImplementation(async () => {
                throw new ConnectionTimedOutError(new Error("timeout error"));
            });
            const response = await instance.service(param, false, ...nulls);
            expect(response?.jsonBody).toBeDefined();
            expect(response.jsonBody.responseHeader).toBeDefined();
            expect(response.jsonBody.responseHeader.processCode).toBe(
                ResultCdConstants.CODE_140203,
            );
        });
    });
});
