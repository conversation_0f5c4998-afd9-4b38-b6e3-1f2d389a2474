import { expect, test } from "@jest/globals";
import { Sequelize } from "sequelize";
import { describeWithDB } from "../../testing/TestHelper";
import { LinesEntity } from "@/core/entity/LinesEntity";
import { usePsql } from "@/database/psql";
import APILinesDAO from "@/core/dao/APILinesDAO";
import DefaultRequest from "../../testing/DefaultRequest";
import DefaultContext from "../../testing/DefaultContext";
import AppConfig from "@/appconfig";

describeWithDB("core/entity/LinesEntity", () => {
    let sequelize: Sequelize = null;
    let instance: APILinesDAO;

    const testdata = {
        lineId: "08024041810",            
        nnumber: "N141003812",
        lineStatus: "01",
        simFlag: false,
        planName: "test",
        network: "lte",
        smsEnable: true,
    }

    beforeAll(async () => {
        await AppConfig.loadCoreMvnoConfig(DefaultContext);
        instance = new APILinesDAO(DefaultRequest, DefaultContext);

        sequelize = await usePsql();
        await LinesEntity.create({
            lineId: testdata.lineId,
            lineStatus: testdata.lineStatus,
            nnumber: testdata.nnumber,
            simFlag: testdata.simFlag,
            planName: testdata.planName,
            network: testdata.network,
            smsEnable: testdata.smsEnable,
        });
    });

    afterAll(async () => {
        await LinesEntity.destroy({
            where: {
                lineId: testdata.lineId
            }
        })
        await sequelize.close();
    });

    test("should create LinesEntity with data still not tested", async () => {
        const result = await instance.getLineInfoByLines(
            testdata.lineId,
        );

        expect(result).not.toBeNull();
        expect(result.lineId).toBe(testdata.lineId);
        expect(result.lineStatus).toBe(testdata.lineStatus);
        expect(result.nnumber).toBe(testdata.nnumber);
        expect(result.simFlag).toBe(testdata.simFlag);
        // TODO: these fields aren't store in DB. Add checks when there's an actual implementation
        // expect(result.planName).toBe(testdata.planName);
        // expect(result.network).toBe(testdata.network);
        // expect(result.smsEnable).toBe(testdata.smsEnable);
    });
});