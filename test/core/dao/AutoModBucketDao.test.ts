import { describe, expect, test, jest, beforeEach, afterEach, beforeAll, afterAll } from "@jest/globals";
import { ConnectionTimedOutError, QueryTypes, Sequelize } from "sequelize";

import DefaultContext from "../../testing/DefaultContext";
import DefaultRequest from "../../testing/DefaultRequest";
import { describeWithDB, generateProcessID } from "../../testing/TestHelper";
import AppConfig from "@/appconfig";
import { usePsql } from "@/database/psql";
import AutoModBucketDao from "@/core/dao/AutoModBucketDao";
import CommonUtil from "@/core/common/CommonUtil";

describe("core/dao/AutoModBucketDao", () => {
    let sequelize: Sequelize;

    // Test data
    const testdata = {
        group: {
            groupId: "7001025",
            tenantId: "TST000"
        },
        tenant: {
            tenantId: "TST000",
            hashedPassword: "test_hashed_password"
        },
        serviceOrder: {
            soId: generateProcessID()
        },
        dates: {
            preExecTime: "2024/01/01 00:00:00",
            execTime: "2024/01/02 00:00:00",
            targetDate: new Date("2025/01/01")
        }
    };

    beforeAll(async () => {
        await AppConfig.loadCoreMvnoConfig(DefaultContext);
        sequelize = await usePsql();
    });

    afterAll(async () => {
        await sequelize.close();
    });

    beforeEach(() => {
        DefaultContext.invocationId = "test-invocation-id";
    });

    afterEach(() => {
        jest.restoreAllMocks();
        jest.clearAllMocks();
    });

    describe("getAutoModBucketGroupList", () => {
        beforeEach(async () => {
            try {
                await sequelize.query("INSERT INTO auto_modbucket_line_groups (line_id, group_id, service_order_id, created_at) VALUES (:lineId, :groupId, :soId, :createdAt)", {
                    replacements: {
                        lineId: "04000000001",
                        groupId: testdata.group.groupId,
                        soId: testdata.serviceOrder.soId,
                        createdAt: testdata.dates.preExecTime
                    }
                });
            } catch (e) {
                console.error(e);
                throw e;
            }
        });

        afterEach(async () => {
            await sequelize.query("DELETE FROM auto_modbucket_line_groups WHERE line_id = :lineId", {
                replacements: {
                    lineId: "04000000001"
                }
            });
        })

        test("should return auto mod bucket group list", async () => {
            jest.spyOn(CommonUtil, "convDateFormat").mockImplementation((date: string) => {
                return date === testdata.dates.preExecTime ? new Date(testdata.dates.preExecTime) : new Date(testdata.dates.execTime);
            });

            const result = await AutoModBucketDao.getAutoModBucketGroupList(
                DefaultContext,
                sequelize,
                testdata.dates.preExecTime,
                testdata.dates.execTime
            );

            expect(result).toBeDefined();
            expect(result).not.toBeNull();
            expect(Array.isArray(result)).toBe(true);
            expect(result[0]).toBeDefined();
            expect(result[0].group_id).toBe(testdata.group.groupId);
            expect(result[0].tenant_id).toBe(testdata.tenant.tenantId);
        });
    });

    describe("getTenantHashedPassword", () => {
        test("should return hashed password for tenant", async () => {
            // Mock the query response
            const mockResult = [{ hashed_password: testdata.tenant.hashedPassword }];
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                return Promise.resolve(mockResult as any);
            });

            const result = await AutoModBucketDao.getTenantHashedPassword(
                DefaultContext,
                sequelize,
                testdata.tenant.tenantId
            );

            expect(result).toBeDefined();
            expect(result).not.toBeNull();
            expect(result).toBe(testdata.tenant.hashedPassword);
            expect(sequelize.query).toHaveBeenCalledWith(
                expect.stringContaining("SELECT hashed_password FROM tenants"),
                expect.objectContaining({
                    replacements: { tenantId: testdata.tenant.tenantId },
                    type: QueryTypes.SELECT
                })
            );
        });

        test("should return null if tenant not found", async () => {
            // Mock empty query response
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                return Promise.resolve([] as any);
            });

            const result = await AutoModBucketDao.getTenantHashedPassword(
                DefaultContext,
                sequelize,
                "NONEXISTENT"
            );

            expect(result).toBeNull();
            expect(sequelize.query).toHaveBeenCalledWith(
                expect.stringContaining("SELECT hashed_password FROM tenants"),
                expect.objectContaining({
                    replacements: { tenantId: "NONEXISTENT" },
                    type: QueryTypes.SELECT
                })
            );
        });

        test("should return null if hashed_password is null", async () => {
            // Mock query response with null hashed_password
            const mockResult = [{ hashed_password: null }];
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                return Promise.resolve(mockResult as any);
            });

            const result = await AutoModBucketDao.getTenantHashedPassword(
                DefaultContext,
                sequelize,
                testdata.tenant.tenantId
            );

            expect(result).toBeNull();
        });

        test("should handle database error", async () => {
            // Mock the query to throw an error
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => {
                await AutoModBucketDao.getTenantHashedPassword(
                    DefaultContext,
                    sequelize,
                    testdata.tenant.tenantId
                );
            }).rejects.toThrow();

            expect(DefaultContext.error).toHaveBeenCalled();
        });
    });

    describe("updServiceOrderExecUserId", () => {
        test("should update service order exec user id", async () => {
            // Mock successful query execution
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                return Promise.resolve([] as any);
            });

            await AutoModBucketDao.updServiceOrderExecUserId(
                DefaultContext,
                sequelize,
                testdata.serviceOrder.soId
            );

            expect(sequelize.query).toHaveBeenCalledWith(
                expect.stringContaining("UPDATE service_orders SET exec_user_id = 'システム自動実行'"),
                expect.objectContaining({
                    replacements: { soId: testdata.serviceOrder.soId },
                    type: QueryTypes.UPDATE
                })
            );
        });

        test("should handle database error", async () => {
            // Mock the query to throw an error
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => {
                await AutoModBucketDao.updServiceOrderExecUserId(
                    DefaultContext,
                    sequelize,
                    testdata.serviceOrder.soId
                );
            }).rejects.toThrow();

            expect(DefaultContext.error).toHaveBeenCalled();
        });
    });

    describe("delAutoModBucketGroupList", () => {
        beforeEach(async () => {
            try {
                await sequelize.query("INSERT INTO auto_modbucket_line_groups (line_id, group_id, service_order_id, created_at) VALUES (:lineId, :groupId, :soId, :createdAt)", {
                    replacements: {
                        lineId: "04000000001",
                        groupId: testdata.group.groupId,
                        soId: testdata.serviceOrder.soId,
                        createdAt: testdata.dates.preExecTime
                    }
                });
            } catch (e) {
                console.error(e);
                throw e;
            }
        });

        afterEach(async () => {
            await sequelize.query("DELETE FROM auto_modbucket_line_groups WHERE line_id = :lineId", {
                replacements: {
                    lineId: "04000000001"
                }
            });
        })

        test("should delete auto mod bucket group list", async () => {
            await AutoModBucketDao.delAutoModBucketGroupList(
                DefaultContext,
                sequelize,
                testdata.dates.targetDate
            );

            // Get the raw result to see what's actually in the database
            const [result] = await sequelize.query("SELECT * FROM auto_modbucket_line_groups WHERE line_id = :lineId", {
                replacements: {
                    lineId: "04000000001"
                }
            });

            expect(result).toBeDefined();
            expect(result).not.toBeNull();
            expect(Array.isArray(result)).toBe(true);
            expect(result.length).toBe(0);
        });
    });
});
