import { describe, expect, test } from "@jest/globals";
import { ConnectionTimedOutError, Sequelize } from "sequelize";

import DefaultRequest from "../../testing/DefaultRequest";
import DefaultContext from "../../testing/DefaultContext";
import { describeWithDB } from "../../testing/TestHelper";
import { generateProcessID } from "../../testing/TestHelper";
import AppConfig from "@/appconfig";
import APISoDAO from "@/core/dao/APISoDAO";
import { usePsql } from "@/database/psql";
import ServiceOrdersEntity from "@/core/entity/ServiceOrdersEntity";

describeWithDB("core/dao/APISoDAO", () => {
    let sequelize: Sequelize = null;
    let instance: APISoDAO;

    const testdata = {
        optionPlan: {
            ok: "10101",
            ng: "99999",
        },
        tenantId: {
            ok: "TST000",
            ng: "TST999",
        },
        serviceOrder: {
            ok: {
                soId: "AP1000004926674",
                tenantId: "TST000",
                lineId: "08010140000",
                status: "完了",
                orderDate: "2018-12-27",
                execDate: "2019-01-01",
                type: "プラン変更",
            },
            ok_for_cancel_obj:{
                soId: generateProcessID(),
            },
            ng:{
                soId: "999999999999999",
            },
            for_get_reserve_order: {
                soId: generateProcessID(),
                tenantId: "TST000",
                lineId: "08024041899",
                status: "予約中",
                orderDate: "2018-12-27",
                execDate: "2019-01-01",
                type: "プラン変更",
            },
        }
    };

    beforeAll(async () => {
        await AppConfig.loadCoreMvnoConfig(DefaultContext);
        instance = new APISoDAO(DefaultRequest, DefaultContext);
        // override timeout
        Object.defineProperty(instance, "apDbRetryInterval", { value: 0.01 }); // 10ms
        sequelize = await usePsql();
        await createTestData();
    });

    afterAll(async () => {
        await deleteTestData();
        await sequelize.close();
    });

    afterEach(() => {
        jest.restoreAllMocks();
        jest.clearAllMocks();
    });

    const createTestData = async () => {
        await ServiceOrdersEntity.create({
                serviceOrderId: testdata.serviceOrder.ok_for_cancel_obj.soId,
                tenantId: testdata.serviceOrder.ok.tenantId,
                lineId: testdata.serviceOrder.ok.lineId,
                orderStatus: "予約中",
                orderType: testdata.serviceOrder.ok.type,
                reserveDate: new Date(),
        })

        await ServiceOrdersEntity.create({
            serviceOrderId: testdata.serviceOrder.for_get_reserve_order.soId,
            tenantId: testdata.serviceOrder.for_get_reserve_order.tenantId,
            lineId: testdata.serviceOrder.for_get_reserve_order.lineId,
            orderStatus: testdata.serviceOrder.for_get_reserve_order.status,
            orderType: testdata.serviceOrder.for_get_reserve_order.type,
            reserveDate: new Date(),
    })
    }

    const deleteTestData = async () => {
        await ServiceOrdersEntity.destroy({
            where:{
                serviceOrderId: testdata.serviceOrder.ok_for_cancel_obj.soId,
            }
        })
        await ServiceOrdersEntity.destroy({
            where:{
                serviceOrderId: testdata.serviceOrder.for_get_reserve_order.soId,
            }
        })
    }

    describe("getOptionPlanss", () => {
        test("should return option plan description if found", async () => {
            const result = await instance.getOptionPlans(
                +testdata.optionPlan.ok,
            );
            expect(result).toBeDefined();
            expect(result).not.toBeNull();
            expect(typeof result).toBe("string");
            expect(result.length).toBeGreaterThan(0);
        });

        test("should return null if plan option plan not found", async () => {
            const result = await instance.getOptionPlans(
                +testdata.optionPlan.ng,
            );
            expect(result).toBeNull();
        });
    });

    describe("getGruopOptionPlans", () => {
        test("should return option plan description if found", async () => {
            const result = await instance.getGruopOptionPlans(
                +testdata.optionPlan.ok,
            );
            expect(result).toBeDefined();
            expect(result).not.toBeNull();
            expect(typeof result).toBe("string");
            expect(result.length).toBeGreaterThan(0);
        });

        test("should return null if plan option plan not found", async () => {
            const result = await instance.getGruopOptionPlans(
                +testdata.optionPlan.ng,
            );
            expect(result).toBeNull();
        });
    });

    describe("updServiceOrders", () => {
        // TODO add unit tests for updServiceOrders
        test("should throw DB error if timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => {
                await instance.updServiceOrders(
                    new Date(),
                    "完了",
                    "AP01234567",
                    "08012341234",
                );
            }).rejects.toThrowError(ConnectionTimedOutError);
        });

        test("should throw other error", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new SyntaxError("unknown error");
            });

            await expect(async () => {
                await instance.updServiceOrders(
                    new Date(),
                    "完了",
                    "AP01234567",
                    "08012341234",
                );
            }).rejects.toThrow(Error);
        });
    });

    describe("getTenantLevel", () => {
        test("should return an array of tenantId if found", async () => {
            const result = await instance.getTenantLevel("TST000");
            expect(result).toBeDefined();
            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(Array);
            expect(result.length).toBeGreaterThan(0);
        });

        test("should return an empty array if not found", async () => {
            const result = await instance.getTenantLevel("TST999");
            expect(result).toBeDefined();
            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(Array);
            expect(result.length).toBe(0);
        });

        test("should throw DB error if timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => {
                await instance.getTenantLevel("TST000");
            }).rejects.toThrowError(ConnectionTimedOutError);
        });

        test("should throw other error", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new SyntaxError("unknown error");
            });

            await expect(async () => {
                await instance.getTenantLevel("TST000");
            }).rejects.toThrow(Error);
        });
    });

    describe.skip("insertServiceOrders", () => {
        // TODO add unit tests for insertServiceOrders
    });

    describe("getTenantsList", () => {
        test("should return an array of tenantId if found", async () => {
            const result = await instance.getTenantsList(testdata.tenantId.ok);
            expect(result).toBeDefined();
            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(Array);
            expect(result.length).toBeGreaterThan(0);
            expect(result[0].tenantId).toBe(testdata.tenantId.ok);

            jest.spyOn(sequelize, "query").mockImplementationOnce(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            const result2 = await instance.getTenantsList(testdata.tenantId.ok);
            expect(result2).toBeDefined();
            expect(result2).not.toBeNull();
            expect(result2).toBeInstanceOf(Array);
            expect(result2.length).toBeGreaterThan(0);
            expect(result2[0].tenantId).toBe(testdata.tenantId.ok);
        });

        test("should throw DB error if timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => {
                await instance.getTenantsList(testdata.tenantId.ok);
            }).rejects.toThrowError(ConnectionTimedOutError);
        });

        test("should return an empty array if not found", async () => {
            const result = await instance.getTenantsList(testdata.tenantId.ng);
            expect(result).toBeDefined();
            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(Array);
            expect(result.length).toBe(0);
        });
    });

    describe("getSoListDisplayDataCount", () => {
        test("should return a number of count if found", async () => {
            const result = await instance.getSoListDisplayDataCount([], "", "", "", "", "", "", "", "");
            expect(result).toBeDefined();
            expect(result).not.toBeNull();
            expect(typeof result).toBe("number");
            expect(result).toBeGreaterThan(0);      // probably should not set a fixed number as it may change, although may set it to be 1374

            jest.spyOn(sequelize, "query").mockImplementationOnce(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            const result2 = await instance.getSoListDisplayDataCount([], "", "", "", "", "", "", "", "");
            expect(result2).toBeDefined();
            expect(result2).not.toBeNull();
            expect(typeof result2).toBe("number");
            expect(result2).toBeGreaterThan(0);
        });

        test("Test with all filter", async () => {
            const result = await instance.getSoListDisplayDataCount(
                [
                    testdata.tenantId.ok,
                    testdata.tenantId.ng
                ],
                testdata.serviceOrder.ok.soId,
                testdata.serviceOrder.ok.lineId,
                testdata.serviceOrder.ok.status,
                testdata.serviceOrder.ok.orderDate,
                testdata.serviceOrder.ok.orderDate,
                testdata.serviceOrder.ok.execDate,
                testdata.serviceOrder.ok.execDate,
                testdata.serviceOrder.ok.type,
            );

            expect(result).toBeDefined();
            expect(result).not.toBeNull();
            expect(typeof result).toBe("number");
            expect(result).toBe(1);

            jest.spyOn(sequelize, "query").mockImplementationOnce(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            const result2 = await instance.getSoListDisplayDataCount(
                [
                    testdata.tenantId.ok,
                ],
                testdata.serviceOrder.ok.soId,
                testdata.serviceOrder.ok.lineId,
                testdata.serviceOrder.ok.status,
                testdata.serviceOrder.ok.orderDate,
                testdata.serviceOrder.ok.orderDate,
                testdata.serviceOrder.ok.execDate,
                testdata.serviceOrder.ok.execDate,
                testdata.serviceOrder.ok.type,
            );

            expect(result2).toBeDefined();
            expect(result2).not.toBeNull();
            expect(typeof result2).toBe("number");
            expect(result2).toBe(1);
        });

        test("test date filter works expectedly (if the test with all filter works)", async () => {
            const result = await instance.getSoListDisplayDataCount(
                [
                    testdata.tenantId.ok,
                ],
                testdata.serviceOrder.ok.soId,
                testdata.serviceOrder.ok.lineId,
                testdata.serviceOrder.ok.status,
                "2019-01-01",
                "2019-12-31",
                testdata.serviceOrder.ok.execDate,
                testdata.serviceOrder.ok.execDate,
                testdata.serviceOrder.ok.type,
            );

            expect(result).toBeDefined();
            expect(result).not.toBeNull();
            expect(typeof result).toBe("number");
            expect(result).toBe(0);

            const result2 = await instance.getSoListDisplayDataCount(
                [
                    testdata.tenantId.ok,
                ],
                testdata.serviceOrder.ok.soId,
                testdata.serviceOrder.ok.lineId,
                testdata.serviceOrder.ok.status,
                testdata.serviceOrder.ok.orderDate,
                testdata.serviceOrder.ok.orderDate,
                "2019-01-02",
                "2020-12-31",
                testdata.serviceOrder.ok.type,
            );

            expect(result2).toBeDefined();
            expect(result2).not.toBeNull();
            expect(typeof result2).toBe("number");
            expect(result2).toBe(0);

            const result3 = await instance.getSoListDisplayDataCount(
                [
                    testdata.tenantId.ok,
                ],
                testdata.serviceOrder.ok.soId,
                testdata.serviceOrder.ok.lineId,
                testdata.serviceOrder.ok.status,
                "2017-01-01",
                "2019-12-31",
                testdata.serviceOrder.ok.execDate,
                testdata.serviceOrder.ok.execDate,
                testdata.serviceOrder.ok.type,
            );

            expect(result3).toBeDefined();
            expect(result3).not.toBeNull();
            expect(typeof result3).toBe("number");
            expect(result3).toBe(1);

            const result4 = await instance.getSoListDisplayDataCount(
                [
                    testdata.tenantId.ok,
                ],
                testdata.serviceOrder.ok.soId,
                testdata.serviceOrder.ok.lineId,
                testdata.serviceOrder.ok.status,
                testdata.serviceOrder.ok.orderDate,
                testdata.serviceOrder.ok.orderDate,
                "2018-01-02",
                "2020-12-31",
                testdata.serviceOrder.ok.type,
            );

            expect(result4).toBeDefined();
            expect(result4).not.toBeNull();
            expect(typeof result4).toBe("number");
            expect(result4).toBe(1);
        });

        test("Test on lineId (if test all filter works)", async () => {
            const result = await instance.getSoListDisplayDataCount(
                [
                    testdata.tenantId.ok,
                ],
                testdata.serviceOrder.ok.soId,
                "08010140001",
                testdata.serviceOrder.ok.status,
                testdata.serviceOrder.ok.orderDate,
                testdata.serviceOrder.ok.orderDate,
                testdata.serviceOrder.ok.execDate,
                testdata.serviceOrder.ok.execDate,
                testdata.serviceOrder.ok.type,
            );

            expect(result).toBeDefined();
            expect(result).not.toBeNull();
            expect(typeof result).toBe("number");
            expect(result).toBe(0);
        });

        test("Test on status (if test all filter works)", async () => {
            const result = await instance.getSoListDisplayDataCount(
                [
                    testdata.tenantId.ok,
                ],
                testdata.serviceOrder.ok.soId,
                testdata.serviceOrder.ok.lineId,
                "失敗",
                testdata.serviceOrder.ok.orderDate,
                testdata.serviceOrder.ok.orderDate,
                testdata.serviceOrder.ok.execDate,
                testdata.serviceOrder.ok.execDate,
                testdata.serviceOrder.ok.type,
            );

            expect(result).toBeDefined();
            expect(result).not.toBeNull();
            expect(typeof result).toBe("number");
            expect(result).toBe(0);
        });

        test("Test on type (if test all filter works)", async () => {
            const result = await instance.getSoListDisplayDataCount(
                [
                    testdata.tenantId.ok,
                ],
                testdata.serviceOrder.ok.soId,
                testdata.serviceOrder.ok.lineId,
                testdata.serviceOrder.ok.status,
                testdata.serviceOrder.ok.orderDate,
                testdata.serviceOrder.ok.orderDate,
                testdata.serviceOrder.ok.execDate,
                testdata.serviceOrder.ok.execDate,
                "クーポンOFF",
            );

            expect(result).toBeDefined();
            expect(result).not.toBeNull();
            expect(typeof result).toBe("number");
            expect(result).toBe(0);
        });

        test("should throw DB error if timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => {
                await instance.getSoListDisplayDataCount([], "", "", "", "", "", "", "", "");
            }).rejects.toThrowError(ConnectionTimedOutError);
        });
    });

    describe("getSoList", () => {
        test("should return an array if found", async () => {
            const result = await instance.getSoList([], "", "", "", "", "", "", "", "");
            expect(result).toBeDefined();
            expect(result).not.toBeNull();
            expect(result.length).toBeGreaterThan(0);

            jest.spyOn(sequelize, "query").mockImplementationOnce(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            const result2 = await instance.getSoList([], "", "", "", "", "", "", "", "");
            expect(result2).toBeDefined();
            expect(result2).not.toBeNull();
            expect(result.length).toBeGreaterThan(0);
        });

        test("Test with all filter", async () => {
            const result = await instance.getSoList(
                [
                    testdata.tenantId.ok,
                    testdata.tenantId.ng
                ],
                testdata.serviceOrder.ok.soId,
                testdata.serviceOrder.ok.lineId,
                testdata.serviceOrder.ok.status,
                testdata.serviceOrder.ok.orderDate,
                testdata.serviceOrder.ok.orderDate,
                testdata.serviceOrder.ok.execDate,
                testdata.serviceOrder.ok.execDate,
                testdata.serviceOrder.ok.type,
            );

            // console.log("result: ", result);

            expect(result).toBeDefined();
            expect(result).not.toBeNull();
            expect(result.length).toBeGreaterThan(0);
            expect(result[0].tenantId).toBe(testdata.tenantId.ok);

            jest.spyOn(sequelize, "query").mockImplementationOnce(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            const result2 = await instance.getSoList(
                [
                    testdata.tenantId.ok,
                ],
                testdata.serviceOrder.ok.soId,
                testdata.serviceOrder.ok.lineId,
                testdata.serviceOrder.ok.status,
                testdata.serviceOrder.ok.orderDate,
                testdata.serviceOrder.ok.orderDate,
                testdata.serviceOrder.ok.execDate,
                testdata.serviceOrder.ok.execDate,
                testdata.serviceOrder.ok.type,
            );

            expect(result2).toBeDefined();
            expect(result2).not.toBeNull();
            expect(result2.length).toBeGreaterThan(0);
            expect(result2[0].tenantId).toBe(testdata.tenantId.ok);
        });

        test("test date filter works expectedly (if the test with all filter works)", async () => {
            const result = await instance.getSoList(
                [
                    testdata.tenantId.ok,
                ],
                testdata.serviceOrder.ok.soId,
                testdata.serviceOrder.ok.lineId,
                testdata.serviceOrder.ok.status,
                "2019-01-01",
                "2019-12-31",
                testdata.serviceOrder.ok.execDate,
                testdata.serviceOrder.ok.execDate,
                testdata.serviceOrder.ok.type,
            );

            expect(result).toBeDefined();
            expect(result).not.toBeNull();
            expect(result.length).toBe(0);

            const result2 = await instance.getSoList(
                [
                    testdata.tenantId.ok,
                ],
                testdata.serviceOrder.ok.soId,
                testdata.serviceOrder.ok.lineId,
                testdata.serviceOrder.ok.status,
                testdata.serviceOrder.ok.orderDate,
                testdata.serviceOrder.ok.orderDate,
                "2019-01-02",
                "2020-12-31",
                testdata.serviceOrder.ok.type,
            );

            expect(result2).toBeDefined();
            expect(result2).not.toBeNull();
            expect(result2.length).toBe(0);

            const result3 = await instance.getSoList(
                [
                    testdata.tenantId.ok,
                ],
                testdata.serviceOrder.ok.soId,
                testdata.serviceOrder.ok.lineId,
                testdata.serviceOrder.ok.status,
                "2017-01-01",
                "2019-12-31",
                testdata.serviceOrder.ok.execDate,
                testdata.serviceOrder.ok.execDate,
                testdata.serviceOrder.ok.type,
            );

            expect(result3).toBeDefined();
            expect(result3).not.toBeNull();
            expect(result3.length).toBe(1);

            const result4 = await instance.getSoList(
                [
                    testdata.tenantId.ok,
                ],
                testdata.serviceOrder.ok.soId,
                testdata.serviceOrder.ok.lineId,
                testdata.serviceOrder.ok.status,
                testdata.serviceOrder.ok.orderDate,
                testdata.serviceOrder.ok.orderDate,
                "2018-01-02",
                "2020-12-31",
                testdata.serviceOrder.ok.type,
            );

            expect(result4).toBeDefined();
            expect(result4).not.toBeNull();
            expect(result4.length).toBe(1);
        });

        test("Test on lineId (if test all filter works)", async () => {
            const result = await instance.getSoList(
                [
                    testdata.tenantId.ok,
                ],
                testdata.serviceOrder.ok.soId,
                "08010140001",
                testdata.serviceOrder.ok.status,
                testdata.serviceOrder.ok.orderDate,
                testdata.serviceOrder.ok.orderDate,
                testdata.serviceOrder.ok.execDate,
                testdata.serviceOrder.ok.execDate,
                testdata.serviceOrder.ok.type,
            );

            expect(result).toBeDefined();
            expect(result).not.toBeNull();
            expect(result.length).toBe(0);
        });

        test("Test on status (if test all filter works)", async () => {
            const result = await instance.getSoList(
                [
                    testdata.tenantId.ok,
                ],
                testdata.serviceOrder.ok.soId,
                testdata.serviceOrder.ok.lineId,
                "失敗",
                testdata.serviceOrder.ok.orderDate,
                testdata.serviceOrder.ok.orderDate,
                testdata.serviceOrder.ok.execDate,
                testdata.serviceOrder.ok.execDate,
                testdata.serviceOrder.ok.type,
            );

            expect(result).toBeDefined();
            expect(result).not.toBeNull();
            expect(result.length).toBe(0);
        });

        test("Test on type (if test all filter works)", async () => {
            const result = await instance.getSoList(
                [
                    testdata.tenantId.ok,
                ],
                testdata.serviceOrder.ok.soId,
                testdata.serviceOrder.ok.lineId,
                testdata.serviceOrder.ok.status,
                testdata.serviceOrder.ok.orderDate,
                testdata.serviceOrder.ok.orderDate,
                testdata.serviceOrder.ok.execDate,
                testdata.serviceOrder.ok.execDate,
                "クーポンOFF",
            );

            expect(result).toBeDefined();
            expect(result).not.toBeNull();
            expect(result.length).toBe(0);
        });

        test("should throw DB error if timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => {
                await instance.getSoList([], "", "", "", "", "", "", "", "");
            }).rejects.toThrowError(ConnectionTimedOutError);
        });
    });

    describe("getSOInfo", () => {
        test("should return an array of tenantId if found", async () => {
            const result = await instance.getSOInfo(testdata.serviceOrder.ok.soId, [testdata.tenantId.ok]);
            expect(result).toBeDefined();
            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(ServiceOrdersEntity);
            expect(result.tenantId).toBe(testdata.tenantId.ok);

            jest.spyOn(sequelize, "query").mockImplementationOnce(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            const result2 = await instance.getSOInfo(testdata.serviceOrder.ok.soId, [testdata.tenantId.ok]);
            expect(result2).toBeDefined();
            expect(result2).not.toBeNull();
            expect(result2).toBeInstanceOf(ServiceOrdersEntity);
            expect(result2.tenantId).toBe(testdata.tenantId.ok);
        });

        test("should throw DB error if timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => {
                await instance.getSOInfo(testdata.serviceOrder.ok.soId, [testdata.tenantId.ok]);
            }).rejects.toThrowError(ConnectionTimedOutError);
        });

        test("should return an null if not found", async () => {
            const result = await instance.getSOInfo(testdata.serviceOrder.ok.soId, [testdata.tenantId.ng]);
            expect(result).toBeNull();
        });
    });

    describe("getSonTenant", () => {
        test("should return an array of tenantId if found", async () => {
            const result = await instance.getSonTenant("ZZZ000");

            expect(result).toBeDefined();
            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(Array);
            expect(result.length).toBeGreaterThan(0);
            expect(result).toContain(testdata.tenantId.ok);

            jest.spyOn(sequelize, "query").mockImplementationOnce(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            const result2 = await instance.getSonTenant("ZZZ000");

            expect(result2).toBeDefined();
            expect(result2).not.toBeNull();
            expect(result2).toBeInstanceOf(Array);
            expect(result2.length).toBeGreaterThan(0);
            expect(result2).toContain(testdata.tenantId.ok);
        });

        test("should return an empty array if not found", async () => {
            const result = await instance.getSonTenant(testdata.tenantId.ng);

            expect(result).toBeDefined();
            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(Array);
            expect(result.length).toBe(0);
        });
    });

    describe("getCancelObj", () =>{
        test("should return an ServiceOrderEntity if found", async () => {
            const result = await instance.getCancelObj(testdata.serviceOrder.ok_for_cancel_obj.soId);
            expect(result).toBeDefined();
            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(ServiceOrdersEntity);
            expect(result.serviceOrderId).toContain(testdata.serviceOrder.ok_for_cancel_obj.soId);

            jest.spyOn(sequelize, "query").mockImplementationOnce(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            const result2 = await instance.getCancelObj(testdata.serviceOrder.ok_for_cancel_obj.soId);
            expect(result2).toBeDefined();
            expect(result2).not.toBeNull();
            expect(result2).toBeInstanceOf(ServiceOrdersEntity);
            expect(result2.serviceOrderId).toContain(testdata.serviceOrder.ok_for_cancel_obj.soId);
        });

        test("should return an null if not found", async () => {
            const result = await instance.getCancelObj(testdata.serviceOrder.ng.soId);
            expect(result).toBeNull();
        });

        test("should throw DB error if timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => {
                await instance.getCancelObj(testdata.serviceOrder.ng.soId);
            }).rejects.toThrowError(ConnectionTimedOutError);
        });
    })

    describe("getReserveOrder", () =>{
        test("should return service order id if found", async ()=>{

            const result = await instance.getReserveOrder(testdata.serviceOrder.for_get_reserve_order.lineId);
            expect(result).toBeDefined();
            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(Array);
            expect(result[0]).toBe(testdata.serviceOrder.for_get_reserve_order.soId);

            jest.spyOn(sequelize, "query").mockImplementationOnce(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            const result2 = await instance.getReserveOrder("08024041801");

            expect(result2).toBeDefined();
            expect(result2).not.toBeNull();
            expect(result2).toBeInstanceOf(Array);
        })

        test("should return an empty array if not found", async () => {
            const result = await instance.getReserveOrder("432431444444");

            expect(result).toBeDefined();
            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(Array);
            expect(result.length).toBe(0);
        });
    })
});
