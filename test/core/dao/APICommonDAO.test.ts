import { afterAll, beforeAll, beforeEach, describe, expect, test } from "@jest/globals";
import { ConnectionTimedOutError, QueryTypes, Sequelize, Transaction } from "sequelize";

import DefaultRequest from "../../testing/DefaultRequest";
import DefaultContext from "../../testing/DefaultContext";
import { describeWithDB, generateProcessID } from "../../testing/TestHelper";

import AppConfig from "@/appconfig";
import APICommonDAO from "@/core/dao/APICommonDAO";
import { LinesEntity } from "@/core/entity/LinesEntity";
import LineTenantsEntity from "@/core/entity/LineTenantsEntity";
import { TenantsEntity } from "@/core/entity/TenantsEntity";
import LineLineGroupsEntity from "@/core/entity/LineLineGroupsEntity";
import { usePsql } from "@/database/psql";
import TenantPlansEntity from "@/core/entity/TenantPlansEntity";
import PlansEntity from "@/core/entity/PlansEntity";
import { GroupOptionPlanParametersEntity } from "@/core/entity/GroupOptionPlanParametersEntity";
import GroupOptionPlansEntity from "@/core/entity/GroupOptionPlansEntity";
import ServiceOrdersEntity from "@/core/entity/ServiceOrdersEntity";
import OptionPlanParametersEntity from "@/core/entity/OptionPlanParametersEntity";
import OptionPlansEntity from "@/core/entity/OptionPlansEntity";
import IpaddressEntity from "@/core/entity/IpaddressEntity";
import MvnoUtil from "@/core/common/MvnoUtil";
import ResultCdConstants from "@/core/constant/ResultCdConstants";
import Constants from "@/core/constant/Constants";

describeWithDB("core/dao/APICommonDAO", () => {
    let sequelize: Sequelize = null;
    let instance: APICommonDAO;
    let tx: Transaction = null;
    // test data
    const testdata = {
        plan: {
            any: "40001",
            notexist: "40000",
            con: "10001", // CON000 only plan
        },
        line: {
            enabled: "08024041801",
            disabled: "08024041802",
            notexist: "08024041803",
            nnumber: ["N141003811", "N141003812"],
            nnumberNG: ["N141004000"],
            deleteLineLineGroups: "08024041899",
        },
        resalePlan: {
            ok: "BS020",
            ng: "BS999",
        },
        lineGroupId: {
            tst: "7001025",
            notfound: "9999999",
        },
        constants: {
            lineStatusOK: "01",
            lineStatusNG: "03",
        },
        tenantGroupPlan: {
            exist: {
                tenantId: "TST000",
                planId: "50001",
                groupId: "7001025",
            },
            notexist: {
                tenantId: "TST000",
                planId: "-1",
                groupId: "-1",
            },
        },
        tenantPlan: {
            exist: {
                tenantId: "TST000",
                planId: "40001",
            },
            notexist: {
                tenantId: "TST000",
                planId: "-1",
            }
        },
        voicePlan: {
            exist: {
                tenantId: "TSA000",
                planId: "BtiredPlan",
            },
            notexist: {
                tenantId: "TST999",
                planId: "notexist",
            },
        },
        ipAddress:{
            exist:{
                tenantId:'OPF000',
                ipAddress:'*******',
                subnetMask:32,
            },
            notexist:{
                tenantId:'TST000',
            }
        },
        LineLineGroups:{
            exist:{
                lineId:'00001963317',
                groupId:'500096',
                usageStatus:2024,
            },
            notexist:{
                lineId:'9999999',
            }
        },
        tenant:{
            exist:{
                tenantId:"TSA000",
            },
            notexist:{
                tenantId:"TSA999",
            }
        }
    };

    beforeAll(async () => {
        await AppConfig.loadCoreMvnoConfig(DefaultContext);
        instance = new APICommonDAO(DefaultRequest, DefaultContext);
        // override timeout
        Object.defineProperty(instance, "apDbRetryInterval", { value: 0.01 }); // 10ms

        sequelize = await usePsql();
        await createTestData();
    });

    afterAll(async () => {
        await deleteTestData();
        await sequelize.close();
    });

    afterEach(() => {
        jest.restoreAllMocks();
        jest.clearAllMocks();
    });
    const createTransaction = async () => {
        tx = await sequelize.transaction();
    }

    const commitTransaction = async () => {
        await tx.commit();
    }
    const removeAutoModBucketData = async (soId: string)=> {
        const sequelize = await usePsql();
        const sql = "DELETE FROM auto_modbucket_line_groups where service_order_id = :service_order_id";
        await sequelize.query(sql, {
            replacements: { service_order_id: soId },
            type: QueryTypes.DELETE,
        });
     }
    const checkAutoModBucketData = async (service_order_id:string, exist: boolean,data:any) =>{
        const sql = "select * from auto_modbucket_line_groups where service_order_id = :service_order_id";
        const sequelize = await usePsql();
        const result = await sequelize.query(sql, {
            replacements: {
                service_order_id,
            },
            type: QueryTypes.SELECT,
        }) as any;
        if(!exist){
            expect(result.length).toBe(0);
        }else{
            expect(result[0].line_id).toBe(data.lineId);
        }
    }

    const createTestData = async () => {
        await Promise.all([
            TenantsEntity.update(
                {
                    status: false, // disable tenant
                },
                {
                    where: {
                        tenantId: "TSA000",
                    },
                },
            ),
            LinesEntity.create({
                lineId: testdata.line.enabled,
                lineStatus: testdata.constants.lineStatusOK,
                nnumber: testdata.line.nnumber[0],
                simFlag: false,
            }),
            LinesEntity.create({
                lineId: testdata.line.disabled,
                lineStatus: testdata.constants.lineStatusNG,
                nnumber: testdata.line.nnumber[1],
                simFlag: false,
                pricePlanId: "JR109",
                usageStatus: 2
            }),
            LinesEntity.create({
                lineId: testdata.line.deleteLineLineGroups,
                lineStatus: testdata.constants.lineStatusOK,
                nnumber: testdata.line.nnumber[1],
                simFlag: false,
            }),
            IpaddressEntity.create({
                tenantId:testdata.ipAddress.exist.tenantId,
                subnetMask:testdata.ipAddress.exist.subnetMask,
                ipAddress:testdata.ipAddress.exist.ipAddress,
            }),
            await LineLineGroupsEntity.create({
                groupId: testdata.LineLineGroups.exist.groupId,
                lineId: testdata.LineLineGroups.exist.lineId,
                basicCapacity: testdata.LineLineGroups.exist.usageStatus,
            }),
        ]);
        // add line to line tenants
        await LineTenantsEntity.create({
            lineId: testdata.line.enabled,
            tenantId: "TST000",
        });
    };

    const deleteTestData = async () => {
        // remove line tenant
        await LineTenantsEntity.destroy({
            where: {
                lineId: testdata.line.enabled,
            },
        });
        await Promise.all([
            TenantsEntity.update(
                {
                    status: true, // restore tenant
                },
                {
                    where: {
                        tenantId: "TSA000",
                    },
                },
            ),
            LinesEntity.destroy({
                where: {
                    lineId: [testdata.line.enabled, testdata.line.disabled, testdata.line.deleteLineLineGroups],
                },
            }),
            IpaddressEntity.destroy({
                where: {
                    tenantId:testdata.ipAddress.exist.tenantId,
                }
            }),
            LineLineGroupsEntity.destroy({
                where: {
                    lineId: testdata.LineLineGroups.exist.lineId,
                }
            }),
        ]);
    };

    describe("getTenants without transaction", () => {
        test("should return TenantEntity if found", async () => {
            const result = await instance.getTenants("TST000");
            expect(result).toBeDefined();
            expect(result).not.toBeNull();
            expect(result.tenantId).toBe("TST000");
            expect(result.tenantName).toBe("事前検証用テナント");
        });

        test("should return TenantEntity if found (無効テナント)", async () => {
            const result = await instance.getTenants("TSA000");
            expect(result).toBeDefined();
            expect(result).not.toBeNull();
            expect(result.tenantId).toBe("TSA000");
            expect(result.tenantName).toBe("開発試験用テナントA");
        });

        test("should return null if not found", async () => {
            const result = await instance.getTenants("TST999");
            expect(result).toBeNull();
        });

        test("should throw DB error if timeout", async () => {
            jest.spyOn(TenantsEntity, "findOne").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => {
                await instance.getTenants("TST000");
            }).rejects.toThrowError(ConnectionTimedOutError);
        });

        test("should throw other error", async () => {
            jest.spyOn(TenantsEntity, "findOne").mockImplementation(() => {
                throw new SyntaxError("unknown error");
            });

            await expect(async () => {
                await instance.getTenants("TST000");
            }).rejects.toThrow(Error);
        });
    });
    describe("getTenants with transaction", () => {
        beforeAll(async () => {
            await createTransaction();
        });

        afterAll(async () => {
            await commitTransaction();
        });
        test("should return TenantEntity if found", async () => {
            const result = await instance.getTenants("TST000",tx);
            expect(result).toBeDefined();
            expect(result).not.toBeNull();
            expect(result.tenantId).toBe("TST000");
            expect(result.tenantName).toBe("事前検証用テナント");
        })
        test("should return TenantEntity if found (無効テナント)", async () => {
            const result = await instance.getTenants("TSA000",tx);
            expect(result).toBeDefined();
            expect(result).not.toBeNull();
            expect(result.tenantId).toBe("TSA000");
            expect(result.tenantName).toBe("開発試験用テナントA");
        })
        test("should return null if not found", async () => {
            const result = await instance.getTenants("TST999",tx);
            expect(result).toBeNull();
        })
        test("should throw DB error if timeout", async () => {
            jest.spyOn(TenantsEntity, "findOne").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => {
                await instance.getTenants("TST000",tx);
            }).rejects.toThrowError(ConnectionTimedOutError);
        })
    })
    describe("getTenantsEntity", () => {
        test("should return TenantsEntity if found", async () => {
            const result = await instance.getTenantsEntity("TST000");
            expect(result).toBeDefined();
            expect(result).not.toBeNull();
            expect(result.tenantId).toBe("TST000");
            expect(result.tenantName).toBe("事前検証用テナント");
        });

        test("should return null for disabled tenant", async () => {
            const result = await instance.getTenantsEntity("TSA000");
            expect(result).toBeNull();
        });

        test("should return null if tenant does not exist", async () => {
            const result = await instance.getTenantsEntity("TST999");
            expect(result).toBeNull();
        });
    });

    describe("getTenantID2", () => {
        test("should return a tenant ID if plan exists", async () => {
            const result = await instance.getTenantID2(
                +testdata.plan.any,
                "TST000",
            );
            expect(result).toBeInstanceOf(Array);
            expect(result).toHaveLength(1);
            expect(result.at(0)).toEqual("TST000");
        });

        test("should return a tenant ID if plan exists (CON000)", async () => {
            const result = await instance.getTenantID2(
                +testdata.plan.con,
                "CON000",
            );
            expect(result).toBeInstanceOf(Array);
            expect(result).toHaveLength(1);
            expect(result.at(0)).toEqual("CON000");
        });

        test("should return empty array if plan id is not tenant plan", async () => {
            const result = await instance.getTenantID2(
                +testdata.plan.con,
                "TST000",
            );
            expect(result).toBeInstanceOf(Array);
            expect(result).toHaveLength(0);
        });

        test("should return empty array if plan does not exist", async () => {
            const result = await instance.getTenantID2(
                +testdata.plan.notexist,
                "TST000",
            );
            expect(result).toBeInstanceOf(Array);
            expect(result).toHaveLength(0);
        });

        test("should throw DB error if timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => {
                await instance.getTenantID2(+testdata.plan.any, "TST000");
            }).rejects.toThrowError(ConnectionTimedOutError);
        });

        test("should throw other error", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new SyntaxError("unknown error");
            });

            await expect(async () => {
                await instance.getTenantID2(+testdata.plan.any, "TST000");
            }).rejects.toThrow(Error);
        });
    });

    describe("getLineTenantCount", () => {
        test("should return count of line tenant", async () => {
            const result = await instance.getLineTenantCount(
                "TST000",
                testdata.line.enabled,
            );
            expect(result).toBeGreaterThan(0);
        });

        test("should throw DB error if timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => {
                await instance.getLineTenantCount(
                    "TST000",
                    testdata.line.enabled,
                );
            }).rejects.toThrowError(ConnectionTimedOutError);
        });

        test("should throw other error", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new SyntaxError("unknown error");
            });

            await expect(async () => {
                await instance.getLineTenantCount(
                    "TST000",
                    testdata.line.enabled,
                );
            }).rejects.toThrow(Error);
        });
    });

    describe("getLineID1", () => {
        test("should return a LineEntity if lineId exists", async () => {
            const result = await instance.getLineID1(testdata.line.enabled);
            expect(result).toBeDefined();
            expect(result).not.toBeNull();
            expect(result.lineId).toBe(testdata.line.enabled);
            expect(result.lineStatus).toBe(testdata.constants.lineStatusOK);
        });

        test("should return null if line is disabled", () => {
            return expect(
                instance.getLineID1(testdata.line.disabled),
            ).resolves.toBeNull();
        });

        test("should return null if line does not exist", () => {
            return expect(
                instance.getLineID1(testdata.line.notexist),
            ).resolves.toBeNull();
        });
    });

    describe("getLineID2", () => {
        test("should return a LineEntity if line exists and have correct nnumber", async () => {
            const result = await instance.getLineID2(
                testdata.line.enabled,
                testdata.line.nnumber,
            );
            expect(result).toBeDefined();
            expect(result).not.toBeNull();
            expect(result.lineId).toBe(testdata.line.enabled);
            expect(result.lineStatus).toBe(testdata.constants.lineStatusOK);
        });

        test("should return null if line is disabled", async () => {
            const result = await instance.getLineID2(
                testdata.line.disabled,
                testdata.line.nnumber,
            );
            expect(result).toBeNull();
        });

        test("should return null if nnumber does not match with lineId", async () => {
            const result = await instance.getLineID2(
                testdata.line.enabled,
                testdata.line.nnumberNG,
            );
            expect(result).toBeNull();
        });
    });

    describe("getNNumber", () => {
        test("should returns an array of NNumber if found", async () => {
            const result = await instance.getNNumber("TST000");
            expect(result).toBeInstanceOf(Array);
            expect(result.length).toBeGreaterThan(0);
        });

        test("should returns empty array if NNumber or tenant does not exist", async () => {
            const result = await instance.getNNumber("TST999");
            expect(result).toBeInstanceOf(Array);
            expect(result).toHaveLength(0);
        });
    });

    describe("getResalePlanID", () => {
        test("should return a LineEntity if lineId exists", async () => {
            const result = await instance.getResalePlanID(
                testdata.line.enabled,
            );
            expect(result).toBeDefined();
            expect(result).not.toBeNull();
            expect(result.lineId).toBe(testdata.line.enabled);
            expect(result.lineStatus).toBe(testdata.constants.lineStatusOK);
        });

        test("should return null if line is disabled", async () => {
            // NOTE this case is based on the current implementation
            return expect(
                instance.getResalePlanID(testdata.line.disabled),
            ).resolves.toBeNull();
        });

        test("should return null if line does not exist", () => {
            return expect(
                instance.getResalePlanID(testdata.line.notexist),
            ).resolves.toBeNull();
        });
    });

    describe("getPlanID", () => {
        test("should return list of plan ID if found", async () => {
            const result = await instance.getPlanID(testdata.resalePlan.ok);
            expect(result).toBeInstanceOf(Array);
            expect(result.length).toBeGreaterThanOrEqual(1);
        });

        test("should return an empty array if resalePlanID does not exist", async () => {
            const result = await instance.getPlanID(testdata.resalePlan.ng);
            expect(result).toBeInstanceOf(Array);
            expect(result).toHaveLength(0);
        });

        test("should throw DB error if timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => {
                await instance.getPlanID(testdata.resalePlan.ok);
            }).rejects.toThrowError(ConnectionTimedOutError);
        });

        test("should throw other error", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new SyntaxError("unknown error");
            });

            await expect(async () => {
                await instance.getPlanID(testdata.resalePlan.ok);
            }).rejects.toThrow(Error);
        });
    });

    describe.skip("getEnableServiceOrder", () => {
        // TODO add unit test
    });

    describe("getLineGroupsEntity", () => {
        test("should return a LineGroupsEntity if found", async () => {
            const result = await instance.getLineGroupsEntity(
                testdata.lineGroupId.tst,
            );
            expect(result).toBeDefined();
            expect(result).not.toBeNull();
            expect(result.groupId).toBe(testdata.lineGroupId.tst);
        });

        test("should return null if line group does not exist", async () => {
            const result = await instance.getLineGroupsEntity(
                testdata.lineGroupId.notfound,
            );
            expect(result).toBeNull();
        });
    });

    describe("getLineTenantsEntity", () => {
        test("should return an array of LineTenantsEntity if line exists and linked", async () => {
            const result = await instance.getLineTenantsEntity(
                testdata.line.enabled,
            );
            expect(result).toBeInstanceOf(Array);
            expect(result.length).toBeGreaterThan(0);
            expect(result.at(0).lineId).toEqual(testdata.line.enabled);
            expect(result.at(0).tenantId).toEqual("TST000");
        });

        test("should return an empty array if line is not linked to any tenant", async () => {
            const result = await instance.getLineTenantsEntity(
                testdata.line.disabled,
            );
            expect(result).toBeInstanceOf(Array);
            expect(result.length).toEqual(0);
        });

        test("should return an empty array if line does not exist", async () => {
            const result = await instance.getLineTenantsEntity(
                testdata.line.notexist,
            );
            expect(result).toBeInstanceOf(Array);
            expect(result.length).toEqual(0);
        });
    });

    describe("getTenantNnumbersEntity", () => {
        test("should return an array of TenantNnumberEntity if nnumber exist", async () => {
            const result = await instance.getTenantNnumbersEntity(
                testdata.line.nnumber[0],
            );
            expect(result).toBeInstanceOf(Array);
            expect(result.length).toBeGreaterThan(0);
            expect(result.at(0).nnumber).toEqual(testdata.line.nnumber[0]);
        });

        test("should return an empty array if nnumber does not exist", async () => {
            const result = await instance.getTenantNnumbersEntity(
                testdata.line.nnumberNG[0],
            );
            expect(result).toBeInstanceOf(Array);
            expect(result.length).toEqual(0);
        });
    });

    describe("getTenantIdFromTenantPlans", () => {
        test("should return an array of tenantId if plan exists", async () => {
            const result = await instance.getTenantIdFromTenantPlans(
                +testdata.plan.any,
            );
            expect(result).toBeInstanceOf(Array);
            expect(result.length).toBeGreaterThan(0);
        });

        test("should return an empty array if plan does not exist", async () => {
            const result = await instance.getTenantIdFromTenantPlans(
                +testdata.plan.notexist,
            );
            expect(result).toBeInstanceOf(Array);
            expect(result.length).toEqual(0);
        });

        test("should throw DB error if timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => {
                await instance.getTenantIdFromTenantPlans(+testdata.plan.any);
            }).rejects.toThrowError(ConnectionTimedOutError);
        });

        test("should throw other error", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new SyntaxError("unknown error");
            });

            await expect(async () => {
                await instance.getTenantIdFromTenantPlans(+testdata.plan.any);
            }).rejects.toThrow(Error);
        });
    });

    describe("getSequence", () => {
        test("should return a sequence number correctly", async () => {
            const result = await instance.getSequence();
            expect(result).toBeDefined();
            expect(typeof result).toBe("string");
            expect(result.length).toBeGreaterThan(0);
        });

        test("should throw error if query result is empty", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                return Promise.resolve([] as any);
            });

            await expect(
                async () => await instance.getSequence(),
            ).rejects.toThrowError(Error);
        });

        test("should throw DB error if timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => {
                await instance.getSequence();
            }).rejects.toThrowError(ConnectionTimedOutError);
        });

        test("should throw other error", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new SyntaxError("unknown error");
            });

            await expect(async () => {
                await instance.getSequence();
            }).rejects.toThrow(Error);
        });
    });

    describe("getCommonAbolitionOrder", () => {
        // NOTE only test error cases
        test("should throw DB error if timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => {
                await instance.getCommonAbolitionOrder(
                    testdata.line.enabled,
                    "20240822",
                );
            }).rejects.toThrowError(ConnectionTimedOutError);
        });

        test("should throw other error", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new SyntaxError("unknown error");
            });

            await expect(async () => {
                await instance.getCommonAbolitionOrder(
                    testdata.line.enabled,
                    "20240822",
                );
            }).rejects.toThrow(Error);
        });
    });

    describe("getLineLineGroupToLineId", () => {
        test("should return an array of lines", async () => {
            const result = await instance.getLineLineGroupToLineId(
                testdata.lineGroupId.tst,
            );
            expect(result).toBeDefined();
            expect(typeof result).toBe("object");
            expect(result.length).toBeGreaterThan(0);
        });

        test("should throw DB error if timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => {
                await instance.getLineLineGroupToLineId(
                    testdata.lineGroupId.tst,
                );
            }).rejects.toThrowError(ConnectionTimedOutError);
        });

        test("should throw other error", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new SyntaxError("unknown error");
            });

            await expect(async () => {
                await instance.getLineLineGroupToLineId(
                    testdata.lineGroupId.tst,
                );
            }).rejects.toThrow(Error);
        });
    });

    describe("getDefaultVoicePlanInfo", () => {
        test("should return a VoiceInfoEntity if found", async () => {
            const result = await instance.getDefaultVoicePlanInfo(testdata.voicePlan.exist.tenantId);
            expect(result).toBeDefined();
            expect(result).not.toBeNull();
            expect(typeof result).toBe("object");
            expect(result.voicePlanId).toBeDefined();

            jest.spyOn(sequelize, "query").mockImplementationOnce(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            const result2 = await instance.getDefaultVoicePlanInfo(testdata.voicePlan.exist.tenantId);
            expect(result2).toBeDefined();
            expect(result2).not.toBeNull();
            expect(typeof result2).toBe("object");
            expect(result2.voicePlanId).toBeDefined();
        });

        test("should throw DB error if timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => {
                await instance.getDefaultVoicePlanInfo(testdata.voicePlan.exist.tenantId);
            }).rejects.toThrowError(ConnectionTimedOutError);
        });

        test("should return null if voice plan does not exist", async () => {
            const result = await instance.getDefaultVoicePlanInfo(testdata.voicePlan.notexist.tenantId);
            expect(result).toBeNull();
        });
    });

    describe("getVoicePlanName", () => {
        test("should return a voice plan name if found", async () => {
            const result = await instance.getVoicePlanName(testdata.voicePlan.exist.tenantId, testdata.voicePlan.exist.planId);
            expect(result).toBeDefined();
            expect(result).not.toBeNull();
            expect(result).toBe("従量プラン");

            jest.spyOn(sequelize, "query").mockImplementationOnce(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            const result2 = await instance.getVoicePlanName(testdata.voicePlan.exist.tenantId, testdata.voicePlan.exist.planId);
            expect(result2).toBeDefined();
            expect(result2).not.toBeNull();
            expect(result2).toBe("従量プラン");
        });

        test("should throw DB error if timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => {
                await instance.getVoicePlanName(testdata.voicePlan.exist.tenantId, testdata.voicePlan.exist.planId);
            }).rejects.toThrowError(ConnectionTimedOutError);
        });

        test("should return null if voice plan does not exist", async () => {
            const result = await instance.getVoicePlanName(testdata.voicePlan.notexist.tenantId, testdata.voicePlan.notexist.planId);
            expect(result).toBeNull();
        });
    });
    describe("getTenantPlans", () => {
        test("should return a list of tenant plans", async () => {
            const result = await instance.getTenantPlans(testdata.tenantPlan.exist.planId, testdata.tenantPlan.exist.tenantId);
            expect(result).toBeInstanceOf(Array);
            expect(result.length).toBeGreaterThan(0);
            expect(result.at(0).planId).toBe(40001);

            jest.spyOn(sequelize, "query").mockImplementationOnce(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            const result2 = await instance.getTenantPlans(testdata.tenantPlan.exist.planId, testdata.tenantPlan.exist.tenantId);
            expect(result2).toBeInstanceOf(Array);
            expect(result2.length).toBeGreaterThan(0);
            expect(result2.at(0).planId).toBe(40001);
        });

        test("should throw DB error if timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => {
                await instance.getTenantPlans(testdata.tenantPlan.exist.planId, testdata.tenantPlan.exist.tenantId);
            }).rejects.toThrowError(ConnectionTimedOutError);
        });

        test("should return an empty array if tenant plan does not exist", async () => {
            const result = await instance.getTenantPlans(testdata.tenantPlan.notexist.planId, testdata.tenantPlan.notexist.tenantId);
            expect(result).toBeInstanceOf(Array);
            expect(result.length).toEqual(0);
        });
    });
    describe("getTenantGroupPlans", () => {
        test("should return a tenant group plan if found", async () => {
            const result = await instance.getTenantGroupPlans(testdata.tenantGroupPlan.exist.planId, testdata.tenantGroupPlan.exist.tenantId);
            expect(result).toBeDefined();
            expect(result).not.toBeNull();
            expect(typeof result).toBe("object");
            expect(result.planId).toBe(Number(testdata.tenantGroupPlan.exist.planId));
            expect(result.tenantId).toBe(testdata.tenantGroupPlan.exist.tenantId);

            jest.spyOn(sequelize, "query").mockImplementationOnce(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            const result2 = await instance.getTenantGroupPlans(testdata.tenantGroupPlan.exist.planId, testdata.tenantGroupPlan.exist.tenantId);
            expect(result2).toBeDefined();
            expect(result2).not.toBeNull();
            expect(typeof result2).toBe("object");
            expect(result2.planId).toBe(Number(testdata.tenantGroupPlan.exist.planId));
            expect(result2.tenantId).toBe(testdata.tenantGroupPlan.exist.tenantId);
        });

        test("should throw DB error if timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => {
                await instance.getTenantGroupPlans(testdata.tenantGroupPlan.exist.planId, testdata.tenantGroupPlan.exist.tenantId);
            }).rejects.toThrowError(ConnectionTimedOutError);
        });

        test("should return null if tenant group plan does not exist", async () => {
            const result = await instance.getTenantGroupPlans(testdata.tenantGroupPlan.notexist.planId, testdata.tenantGroupPlan.notexist.tenantId);
            expect(result).toBeNull();
        });
    });
    describe("getPlans", () => {
        test("should return a list of plans if found", async () => {
            const result = await instance.getPlans(testdata.plan.any);
            console.log("ahh =>> ", result);
            expect(result).toBeInstanceOf(Array);
            expect(result.length).toBeGreaterThan(0);
            expect(result.at(0).planId).toBe(Number(testdata.plan.any));

            jest.spyOn(sequelize, "query").mockImplementationOnce(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            const result2 = await instance.getPlans(testdata.plan.any);
            expect(result2).toBeInstanceOf(Array);
            expect(result2.length).toBeGreaterThan(0);
            expect(result2.at(0).planId).toBe(Number(testdata.plan.any));
        });

        test("should throw DB error if timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => {
                await instance.getPlans(testdata.plan.any);
            }).rejects.toThrowError(ConnectionTimedOutError);
        });

        test("should return an empty array if tenant does not exist", async () => {
            const result = await instance.getPlans(testdata.plan.notexist);
            expect(result).toBeInstanceOf(Array);
            expect(result.length).toEqual(0);
        });
    });

    describe("getPlans with transaction", () => {
        test("should return a list of plans if found", async () => {
            let tx: Transaction = null;
            try{
                tx = await sequelize.transaction();
                const result = await instance.getPlans(testdata.plan.any);
                expect(result).toBeInstanceOf(Array);
                expect(result.length).toBeGreaterThan(0);
                expect(result.at(0).planId).toBe(Number(testdata.plan.any));

            } finally {
                if(tx){
                    await tx.rollback();
                }
            }
        });

        test("should return an empty array if tenant does not exist", async () => {
            let tx: Transaction = null;
            try{
                const result = await instance.getPlans(testdata.plan.notexist);
                expect(result).toBeInstanceOf(Array);
                expect(result.length).toEqual(0);
            } finally {
                if (tx){
                    await tx.rollback();
                }
            }
        });
    });
    describe("getGroupPlans", () => {
        test("should return a group plan if found", async () => {
            const result = await instance.getGroupPlans(testdata.tenantGroupPlan.exist.planId);
            expect(result).toBeDefined();
            expect(result).not.toBeNull();
            expect(result.planId).toBe(Number(testdata.tenantGroupPlan.exist.planId));

            jest.spyOn(sequelize, "query").mockImplementationOnce(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            const result2 = await instance.getGroupPlans(testdata.tenantGroupPlan.exist.planId);
            expect(result2).toBeDefined();
            expect(result2).not.toBeNull();
            expect(result2.planId).toBe(Number(testdata.tenantGroupPlan.exist.planId));
        });

        test("should throw DB error if timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => {
                await instance.getGroupPlans(testdata.tenantGroupPlan.exist.planId);
            }).rejects.toThrowError(ConnectionTimedOutError);
        });

        test("should return null if group plan does not exist", async () => {
            const result = await instance.getGroupPlans(testdata.plan.notexist);
            expect(result).toBeNull();
        });
    });

    describe("getGroupOptionPlanParameters", () => {
        beforeAll(async () => {
            await GroupOptionPlansEntity.create({
                optionPlanId: 999999,
                optionPlanName: "test_option_plan",
                optionPlanDescription: "test_option_plan_description",
                optionPlanType: "00",
            });
            await GroupOptionPlanParametersEntity.create({
                optionPlanId: 999999,
                key: "test_key",
                value: "test_value",
            });
        })

        afterAll(async () => {
            await GroupOptionPlanParametersEntity.destroy({
                where: {
                    optionPlanId: "999999",
                },
            });
            await GroupOptionPlansEntity.destroy({
                where: {
                    optionPlanId: "999999",
                },
            });
        });
        test("should return a parameter if found", async () => {
            const result = await instance.getGroupOptionPlanParameters("999999", "test_key");

            expect(result).toBeDefined();
            expect(result).not.toBeNull();
            expect(result.optionPlanId).toBe(999999);
            expect(result.key).toBe("test_key");
            expect(result.value).toBe("test_value");
        });

        test("should return null if parameter does not exist", async () => {
            const result = await instance.getGroupOptionPlanParameters("0", "nonexistent_key");

            expect(result).toBeNull();
        });

        test("should not execute query if optionPlanId is null", async () => {
            const findOneSpy = jest.spyOn(GroupOptionPlanParametersEntity, "findOne");

            const result = await instance.getGroupOptionPlanParameters(null, "test_key");

            expect(findOneSpy).not.toHaveBeenCalled();
            expect(result).toBeNull();
        });

        test("should not execute query if optionPlanId is not a number", async () => {
            const findOneSpy = jest.spyOn(GroupOptionPlanParametersEntity, "findOne");

            const result = await instance.getGroupOptionPlanParameters("not-a-number", "test_key");

            expect(findOneSpy).not.toHaveBeenCalled();
            expect(result).toBeNull();
        });

        test("should throw DB error if timeout", async () => {
            jest.spyOn(GroupOptionPlanParametersEntity, "findOne").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => {
                await instance.getGroupOptionPlanParameters("10401", "test_key");
            }).rejects.toThrowError(ConnectionTimedOutError);
        });

        test("should throw error for non-SQL exceptions", async () => {
            const customError = new Error("Custom error");

            jest.spyOn(GroupOptionPlanParametersEntity, "findOne").mockImplementation(() => {
                throw customError;
            });

            await expect(async () => {
                await instance.getGroupOptionPlanParameters("10401", "test_key");
            }).rejects.toThrow(Error);
        });
    });

    describe("getLinesID", () => {
        test("should return a line if found", async () => {
            const result = await instance.getLinesID(testdata.lineGroupId.tst);
            expect(result).toBeDefined();
            expect(result).not.toBeNull();
            expect(result.lineId).toBeDefined();

            jest.spyOn(sequelize, "query").mockImplementationOnce(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            }).mockImplementationOnce(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            const result2 = await instance.getLinesID(testdata.lineGroupId.tst);
            expect(result2).toBeDefined();
            expect(result2).not.toBeNull();
            expect(result2.lineId).toBeDefined();
        });

        test("should throw DB error if timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => {
                await instance.getLinesID(testdata.lineGroupId.tst);
            }).rejects.toThrowError(ConnectionTimedOutError);
        });

        test("should return a line even if line does not exist", async () => {
            const result = await instance.getLinesID(testdata.lineGroupId.notfound);
            expect(result).toBeDefined();
            expect(result).not.toBeNull();
            expect(result.lineId).toBeNull();
        });
    });

    describe("getLineLineGroupsCount", () => {
        test("should return count of line", async () => {
            const tx = await sequelize.transaction();
            try {
                const lineGroupLines = await instance.getLineLineGroupToLineId(
                    testdata.lineGroupId.tst,
                );
                expect(lineGroupLines).toBeDefined();
                expect(lineGroupLines.length).toBeGreaterThan(0);
                const result = await instance.getLineLineGroupsCount(
                    lineGroupLines[0],
                    tx,
                );
                expect(result).toBeGreaterThan(0);
            } finally {
                await tx.rollback();
            }
        });

        test("should return 0 if line does not exist", async () => {
            const tx = await sequelize.transaction();
            try {
                const lineGroupLines = await instance.getLineLineGroupToLineId(
                    testdata.lineGroupId.tst,
                );
                expect(lineGroupLines).toBeDefined();
                expect(lineGroupLines.length).toBeGreaterThan(0);
                const result = await instance.getLineLineGroupsCount(
                    lineGroupLines[0],
                    tx,
                );
                expect(result).toBeGreaterThan(0);
            } finally {
                await tx.rollback();
            }
        });
    });

    describe("getLineLineGroupsLock", () => {
        test("should get list of lines", async () => {
            const tx = await sequelize.transaction();
            try {
                const lineGroupLines = await instance.getLineLineGroupToLineId(
                    testdata.lineGroupId.tst,
                );
                expect(lineGroupLines).toBeDefined();
                expect(lineGroupLines.length).toBeGreaterThan(0);
                const result = await instance.getLineLineGroupsLock(
                    lineGroupLines[0],
                    tx,
                );
                expect(Array.isArray(result)).toBe(true);
                expect(result.length).toBeGreaterThan(0);
            } finally {
                await tx.rollback();
            }
        });
    });

    describe("deleteLineLineGroups", () => {
        test("should delete line from line group", async () => {
            const tx: Transaction = await sequelize.transaction();
            try {
                await LineLineGroupsEntity.create(
                    {
                        lineId: testdata.line.deleteLineLineGroups,
                        groupId: testdata.lineGroupId.tst,
                    },
                    { transaction: tx },
                );

                await instance.deleteLineLineGroups(
                    testdata.line.deleteLineLineGroups,
                    tx,
                );
                const result = await LineLineGroupsEntity.findOne({
                    where: {
                        lineId: testdata.line.deleteLineLineGroups,
                        groupId: testdata.lineGroupId.tst,
                    },
                    transaction: tx,
                });

                expect(result).toBeNull();
            } finally {
                await tx.rollback();
            }
        });
    });
    describe("getPlanChangeFlag", () => {
        test("should return array of plan_change_flag if found", async () => {
            const tx = await sequelize.transaction();
            try{
                const result = await instance.getPlanChangeFlag(testdata.tenant.exist.tenantId,tx);
                expect(result).toBeInstanceOf(Array);
                expect(result.length).toBeGreaterThan(0);
            } finally{
                await tx.rollback();
            }
            
        })

        test("should return empty array if not found", async () => {
            const tx = await sequelize.transaction();
            try{
                const result = await instance.getPlanChangeFlag(testdata.tenant.notexist.tenantId,tx);
                expect(result).toBeInstanceOf(Array);
                expect(result.length).toBe(0);
            } finally{
                await tx.rollback();
            }
            
        })
    })

    describe("getPlanChangeClass", () => {

        test("should return array of plan_change_class if found", async () => {
            const tx = await sequelize.transaction();
            try{
                const result = await instance.getPlanChangeClass(testdata.plan.any,tx);
                expect(result).toBeInstanceOf(Array);
                expect(result.length).toBeGreaterThan(0);
            } finally {
                await tx.rollback();
            }
            
        })

        test("should return empty array if not found", async () => {
            const tx = await sequelize.transaction();
            try{
                const result = await instance.getPlanChangeFlag(testdata.plan.notexist,tx);
                expect(result).toBeInstanceOf(Array);
                expect(result.length).toBe(0);
            } finally {
                await tx.rollback();
            }
        });
    })


    describe("getLineLineGroupsCount", () => {
        test("should return array of string if found", async () => {
            const tx = await sequelize.transaction();
            try {
                const result = await instance.getPlansTypeList("99038",tx)
                expect(result).toBeInstanceOf(Array)
                expect(result.length).toBeGreaterThan(0)
                expect(result[0]).toBe("true");
            } finally {
                await tx.rollback();
            }
        });

        test("should return empty array if not found", async () => {
            const tx = await sequelize.transaction();
            try {
                const result = await instance.getPlansTypeList("99999",tx)
                expect(result).toBeInstanceOf(Array)
                expect(result.length).toBe(0)
            } finally {
                await tx.rollback();
            }
        });
    });

    describe("getTenantPlansPlanId", () => {
        test("should return array of string if found", async () => {
            const tx = await sequelize.transaction();
            try {
                const result = await instance.getTenantPlansPlanId("CON000","10001",tx)
                expect(result).toBeInstanceOf(Array)
                expect(result.length).toBeGreaterThan(0)
                expect(result[0]).toBe(parseInt("10001"))
            } finally {
                await tx.rollback();
            }
        });

        test("should return empty array if not found", async () => {
            const tx = await sequelize.transaction();
            try {
                const result = await instance.getTenantPlansPlanId("CON000","99999",tx)
                expect(result).toBeInstanceOf(Array)
                expect(result.length).toBe(0)
            } finally {
                await tx.rollback();
            }
        });
    });

    describe("getTenantPlanforPlanChange with transaction", () => {
        test("should return TenantPlansEntity if found", async () => {
            const tx = await sequelize.transaction();
            try {
                const result = await instance.getTenantPlanforPlanChange("CON000","10001","10001",tx)
                expect(result).toBeInstanceOf(TenantPlansEntity)
                expect(result).not.toBe(null)
                expect(result.tenantId).toBe("CON000")
                expect(result.planId).toBe(10001)
                expect(result.changePlanId).toBe(10001);
            } finally {
                await tx.rollback();
            }
        });

        test("should return null if not found", async () => {
            const tx = await sequelize.transaction();
            try {
                const result = await instance.getTenantPlanforPlanChange("CON000","99999","10001",tx)
                expect(result).toBe(null)

            } finally {
                await tx.rollback();
            }
        });
    });

    describe("getTenantPlanforPlanChange without transaction", () => {
        test("should return TenantPlansEntity if found", async () => {
            const result = await instance.getTenantPlanforPlanChange("CON000","10001","10001")
                expect(result).toBeInstanceOf(TenantPlansEntity)
                expect(result).not.toBe(null)
                expect(result.tenantId).toBe("CON000")
                expect(result.planId).toBe(10001)
                expect(result.changePlanId).toBe(10001);

        });

        test("should return null if not found", async () => {
            const result = await instance.getTenantPlanforPlanChange("CON000","99999","10001")
            expect(result).toBe(null)
        });
    });

    describe("getPlanChangeKaisu with transaction", () => {
        test("should return count number if found", async () => {
            const tx = await sequelize.transaction();
            try{
                const result = await instance.getPlanChangeKaisu("02006100041",new Date('2020-10-11 16:35:05.000'),new Date('2021-10-11 16:35:05.000'),tx)
                expect(result).not.toBe(0);
                expect(result).toBeGreaterThan(0);
                expect(result).toBe(1)
            }finally{
                await tx.rollback();
            }
        });

        test("should return 0 if not found", async () => {
            const tx = await sequelize.transaction();
            try{
                const result = await instance.getPlanChangeKaisu("02006100041",new Date(),new Date(),tx)
            expect(result).toBe(0)
            }finally{
                await tx.rollback();
            }
            
        });
    });

    describe.skip("getPlanChangeKaisu without", () => {
        test("should return count number if found", async () => {
            const result = await instance.getPlanChangeKaisu("02006100041",'2020-10-11 16:35:05.000','2021-10-11 16:35:05.000')
                expect(result).not.toBe(0);
                expect(result).toBeGreaterThan(0);
                expect(result).toBe(1)
        });

        test("should return 0 if not found", async () => {
            const result = await instance.getPlanChangeKaisu("02006199999",'2020-10-11 16:35:05.000','2021-10-11 16:35:05.000')
            expect(result).toBe(0)
        });
    });

    describe("getLineUsageStatus", () => {
        afterEach(async () => {
            jest.restoreAllMocks();
            jest.clearAllMocks();
        });

        test("should return usage status if line is suspended", async () => {
            const result = await instance.getLineUsageStatus(testdata.line.disabled);
            expect(result).toEqual([2]);
        });
    });
    describe.skip("getCheckedGroupPlanOptionPlans",()=>{
        test.skip("should return a planId if found", async () => {
            const result = await instance.getCheckedGroupPlanOptionPlans("10401","10001","12");
            expect(result).toBeDefined();
            expect(result).not.toBeNull();

            jest.spyOn(sequelize, "query").mockImplementationOnce(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            const result2 = await instance.getCheckedGroupPlanOptionPlans("10401","10001","12");
            expect(result2).toBeDefined();
            expect(result2).not.toBeNull();
        });

        test("should throw DB error if timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => {
                await instance.getCheckedGroupPlanOptionPlans("10401","10001","12");
            }).rejects.toThrowError(ConnectionTimedOutError);
        });

        test("should return null if GroupPlanOptionPlans does not exist", async () => {
            const result = await instance.getCheckedGroupPlanOptionPlans("10401","10001","12");
            expect(result).toBeNull();
        });
    });
    describe("getCheckedPlanOptionPlans", () => {
        test("should return a list of plans if found", async () => {
            const result = await instance.getCheckedPlanOptionPlans("10101","10001","03");
            expect(result).toBe("10001");

            jest.spyOn(sequelize, "query").mockImplementationOnce(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            const result2 = await instance.getCheckedPlanOptionPlans("10101","10001","03");
            expect(result2).toBe("10001");
        });

        test("should throw DB error if timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => {
                await instance.getCheckedPlanOptionPlans("10101","10001","03");
            }).rejects.toThrowError(ConnectionTimedOutError);
        });

        test("should return an empty array if tenant does not exist", async () => {
            const result = await instance.getCheckedPlanOptionPlans("10101","10001","00000");
            expect(result).toBe(null)
        });
    });

    describe("getIpAddressList", () => {
        test("should return an array of IpAddress Entity if found", async() =>{
            const result = await instance.getIpAddressList(testdata.ipAddress.exist.tenantId);
            expect(result[0].tenantId).toBe(testdata.ipAddress.exist.tenantId)
            expect(result[0].ipAddress).toBe(testdata.ipAddress.exist.ipAddress)
            expect(result[0].subnetMask).toBe(testdata.ipAddress.exist.subnetMask)
        })

        test("should return null if not found", async() =>{
            const result = await instance.getIpAddressList(testdata.ipAddress.notexist.tenantId);
            expect(result.length).toBe(0)
        })

        test("should throw DB error if timeout", async() =>{

            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () =>{
                await instance.getIpAddressList(testdata.ipAddress.exist.tenantId);
            }).rejects.toThrowError(ConnectionTimedOutError);
        })
    });

    describe("getGroupIdList", () => {
        test("should return an array of groupId if found", async() =>{
            const result = await instance.getGroupIdList(testdata.LineLineGroups.exist.lineId);
            expect(result[0]).toBe(testdata.LineLineGroups.exist.groupId);
        })

        test("should return [] if not found", async() =>{
            const result = await instance.getGroupIdList(testdata.LineLineGroups.notexist.lineId);
            expect(result).toStrictEqual([]);
        })

        test("should throw db error if timeout", async() =>{

            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () =>{
                await instance.getGroupIdList(testdata.LineLineGroups.exist.lineId);
            }).rejects.toThrowError(ConnectionTimedOutError);
        })
    });

    describe("insertAutoModBucketLineGroup", () => {

        const data = {
            soId: generateProcessID(),
            lineId: "04000000001",
            groupId: "7000402",
        };
        test("should create autoModBucket", async() =>{
            await instance.insertAutoModBucketLineGroup(
                data.lineId,
                data.groupId,
                data.soId,
                MvnoUtil.getDateTimeNow(),
            );
            await checkAutoModBucketData(data.soId,true,data)
            await removeAutoModBucketData(data.soId);
        })

        test("should throw db error if timeout", async() =>{
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () =>{
                await instance.insertAutoModBucketLineGroup(
                    data.lineId,
                    data.groupId,
                    data.soId,
                    MvnoUtil.getDateTimeNow(),
                )
            }).rejects.toThrowError(ConnectionTimedOutError);
        })
    });

    describe("getOptionPlanTBan", () => {
        beforeAll(async () => {
            await OptionPlansEntity.create({
                optionPlanId: 10,
                optionPlanName: "test",
                optionPlanDescription: "test",
                optionPlanType: "01",
                optionPlanIdT: "T10000",
            });
        });

        afterAll(async () => {
            await OptionPlansEntity.destroy({
                where: {
                    optionPlanId: 10,
                },
            });
        });

        it("should return option plan tban if found", async () => {
            const result = await instance.getOptionPlanTBan("10");
            expect(result).toBe("T10000");
        });
    });

    describe("getServiceOrder", () => {
        beforeAll(async () => {
            await ServiceOrdersEntity.create({
                serviceOrderId: "100000000000000",
                orderDate: new Date(),
                execDate: new Date(),
                orderType: "test",
                orderStatus: "完了",
                lineId: testdata.line.enabled,
                tenantId: "TST000",
                content: "test",
                internalFlag: false,
                functionType: "01",
            });
        });

        afterAll(async () => {
            await ServiceOrdersEntity.destroy({
                where: {
                    serviceOrderId: "100000000000000",
                },
            });
        });
        it("should return SO if found", async () => {
            const result = await instance.getServiceOrder("100000000000000");
            expect(result).toBeDefined();
            expect(result).not.toBeNull();
            expect(result.serviceOrderId).toBe("100000000000000");
        });

        it("should return null if SO does not exist", async () => {
            const result = await instance.getServiceOrder("00000");
            expect(result).toBeNull();
        });
    })

    describe("getOptionPlanParameters", () => {
        beforeAll(async () => {
            try {
                await OptionPlansEntity.create({
                    optionPlanId: 10,
                    optionPlanName: "test",
                    optionPlanDescription: "test",
                    optionPlanType: "01",
                });
                await OptionPlanParametersEntity.create({
                    optionPlanId: 10,
                    key: "test",
                    value: "100",
                });
            } catch (e) {
                console.error(e);
                throw e;
            }
        });

        afterAll(async () => {
            try {
                await OptionPlanParametersEntity.destroy({
                    where: {
                        optionPlanId: 10,
                    },
                });
                await OptionPlansEntity.destroy({
                    where: {
                        optionPlanId: 10,
                    },
                });
            } catch (e) {
                console.error(e);
                throw e;
            }
        });

        it("should return option plan parameter object if found", async () => {
            const result = await instance.getOptionPlanParameters("10", "test");
            expect(result).toBeDefined();
            expect(result).not.toBeNull();
            expect(result.optionPlanId).toBe(10);
            expect(result.key).toBe("test");
            expect(result.value).toBe("100");
        });

        it("should return null if not found", async () => {
            const result = await instance.getOptionPlanParameters("10", "notfound");
            expect(result).toBeNull();
        });

        it("should return null if option plan id is null, key is null or option plan id is not number", async () => {
            const result = await instance.getOptionPlanParameters("abc", "test");
            expect(result).toBeNull();
        });
    });
});
