import { describeWithDB } from "../../testing/TestHelper";
import { ConnectionTimedOutError, Sequelize, Transaction } from "sequelize";
import APILinesGroupDAO from "@/core/dao/ApiLinesGroupDAO";
import DefaultRequest from "../../testing/DefaultRequest";
import DefaultContext from "../../testing/DefaultContext";
import { usePsql } from "@/database/psql";
import { afterAll, beforeAll, describe, expect } from "@jest/globals";
import LineGroupsEntity from "@/core/entity/LineGroupsEntity";
import AppConfig from "@/appconfig";
import { GroupPlansEntity } from "@/core/entity/GroupPlansEntity";
import TenantGroupPlansEntity from "@/core/entity/TenantGroupPlansEntity";
import LineLineGroupsEntity from "@/core/entity/LineLineGroupsEntity";
import GroupOptionPlansEntity from "@/core/entity/GroupOptionPlansEntity";
import { GroupOptionPlanParametersEntity } from "@/core/entity/GroupOptionPlanParametersEntity";

describeWithDB("core/dao/ApiLinesGroupDAO", () => {
    let sequelize: Sequelize = null;
    let instance: APILinesGroupDAO;
    let tx: Transaction = null;

    const testData = {
        lineGroupId: {
            tst: "7001025",
            notfound: "9999999",
        },
        GroupTrafficsEntity: {},
        linesGroupId: {     // group_id in table lines
            enabled: "800803",
            status3: "100000218",
            notfound: "111111",
        },
        LineLineGroup: {
            exist: {
                group_id: "7000402",
                line_id: "04000000001",
            },
            notExist: {
                group_id: "9999999",
                line_id: "9999999",
            }
        },
        LineGroup: {
            exist: {
                planId: "50001",
                groupId: "7001025",
            },
            wrongStatus: {
                planId: "30001",
                groupId: "7000001",
            },
            notExist: {
                planId: "99999",
                groupId: "9999999",
            },
        },
        GroupTraffic: {
            exist: {
                group_id: '500001',
                year: '2016',
                month: '2'
            },
            notExist: {
                group_id: '9999999',
                year: '2010',
                month: '2'
            }
        },
        GroupPlan:{
            exist:{
                plan_id:10001,
            },
            notExist:{
                plan_id:9999999,
            },
        },
        TenantGroupPlan:{
            exist:{
                plan_id:10001,
                tenant_id:"CON000",
            },
            notExist:{
                plan_id:9999999,
                tenant_id:'9999999',
            },
        },
        constants: {
            lineStatusOK: "01",
            lineStatusNG: "03",
        },
        GroupPlans: {
            exist: 90001,
            notExist: 9999999
        }
    };

    beforeAll(async () => {
        await AppConfig.loadCoreMvnoConfig(DefaultContext);
        instance = new APILinesGroupDAO(DefaultRequest, DefaultContext);
        // override timeout
        Object.defineProperty(instance, "apDbRetryInterval", { value: 0.01 }); // 10ms

        sequelize = await usePsql();
    });

    afterAll(async () => {
        await sequelize.close();
    });

    afterEach(() => {
        jest.restoreAllMocks();
        jest.clearAllMocks();
    });

    const createTransaction = async () => {
        tx = await sequelize.transaction();
    }

    const commitTransaction = async () => {
        await tx.commit();
    }

    describe("getUsingGroupPlans", () => {
        
        it("should return number if line group exist", async () => {
            // test getUsingGroupPlans
            const result = await instance.getUsingGroupPlans(testData.LineGroup.exist.planId, testData.LineGroup.exist.groupId);
            expect(result).toBe("50001");

            jest.spyOn(sequelize, "query").mockImplementationOnce(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            const result2 = await instance.getUsingGroupPlans(testData.LineGroup.exist.planId, testData.LineGroup.exist.groupId);
            expect(result2).toBe("50001");
        });

        it("should throw error if DB timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => {
                await instance.getUsingGroupPlans(testData.LineGroup.exist.planId, testData.LineGroup.exist.groupId);
            }).rejects.toThrowError(ConnectionTimedOutError);
        });

        it("should return null if line group not found", async () => {
            const result = await instance.getUsingGroupPlans(testData.LineGroup.notExist.planId, testData.LineGroup.notExist.groupId);
            expect(result).toBeNull();
        });
    });

    describe("getTraffic", () => {
        it("should return GroupTrafficsEntity if found", async () => {
            const result = await instance.getTraffic(testData.GroupTraffic.exist.group_id, testData.GroupTraffic.exist.year, testData.GroupTraffic.exist.month);
            expect(result).toBeDefined();
            expect(result).not.toBeNull();
            expect(result.groupId).toBe(testData.GroupTraffic.exist.group_id);
            expect(result.year).toBe(Number(testData.GroupTraffic.exist.year));
            expect(result.month).toBe(Number(testData.GroupTraffic.exist.month));

            jest.spyOn(sequelize, "query").mockImplementationOnce(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            const result2 = await instance.getTraffic(testData.GroupTraffic.exist.group_id, testData.GroupTraffic.exist.year, testData.GroupTraffic.exist.month);
            expect(result2).toBeDefined();
            expect(result2).not.toBeNull();
            expect(result2.groupId).toBe(testData.GroupTraffic.exist.group_id);
            expect(result2.year).toBe(Number(testData.GroupTraffic.exist.year));
            expect(result2.month).toBe(Number(testData.GroupTraffic.exist.month));
        });

        it("should throw error if DB timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => {
                await instance.getTraffic(testData.GroupTraffic.exist.group_id, testData.GroupTraffic.exist.year, testData.GroupTraffic.exist.month);
            }).rejects.toThrowError(ConnectionTimedOutError);
        });

        it("should return null if not found", async () => {
            const result = await instance.getTraffic(testData.GroupTraffic.notExist.group_id, testData.GroupTraffic.notExist.year, testData.GroupTraffic.notExist.month);
            expect(result).toBeNull();
        });
    });

    describe("getLineGroupsInfo without transaction", () => {
        it("should return LineGroupsEntity", async () => {
            const result = await instance.getLineGroupsInfo(testData.lineGroupId.tst);
            expect(result).toBeDefined();
            expect(result).not.toBeNull();
            expect(result.groupId).toBe(testData.lineGroupId.tst);

            jest.spyOn(sequelize, "query").mockImplementationOnce(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            const result2 = await instance.getLineGroupsInfo(testData.lineGroupId.tst);
            expect(result2).toBeDefined();
            expect(result2).not.toBeNull();
            expect(result2.groupId).toBe(testData.lineGroupId.tst);
        });

        it("should throw error if DB timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => {
                await instance.getLineGroupsInfo(testData.lineGroupId.tst);
            }).rejects.toThrowError(ConnectionTimedOutError);
        });

        it("should return null if not found", async () => {
            const result = await instance.getLineGroupsInfo(testData.lineGroupId.notfound);
            expect(result).toBeNull();
        });
    });

    describe("getLineGroupsInfo with transaction", () => {
        beforeAll(async () => {
            await createTransaction();
        });

        afterAll(async () => {
            await commitTransaction();
        });

        it("should return LineGroupsEntity", async () => {
            const result = await instance.getLineGroupsInfo(testData.lineGroupId.tst, tx);
            expect(result).toBeDefined();
            expect(result).not.toBeNull();
            expect(result.groupId).toBe(testData.lineGroupId.tst);
        });

        it("should failed with one fail try", async () => {
            jest.spyOn(sequelize, "query").mockImplementationOnce(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => {
                await instance.getLineGroupsInfo(testData.lineGroupId.tst, tx);
            }).rejects.toThrowError(ConnectionTimedOutError);
        });

        it("should return null if not found", async () => {
            const result = await instance.getLineGroupsInfo(testData.lineGroupId.notfound, tx);
            expect(result).toBeNull();
        });
    });

    describe("getLinesGroupInfo", () => {
        it("should return array of LinesEntity if found", async () => {
            const result = await instance.getLinesGroupInfo(testData.linesGroupId.enabled);
            expect(result).toBeDefined();
            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(Array);
            expect(result.length).toBeGreaterThanOrEqual(1);
            expect(result[0].groupId).toBe(testData.linesGroupId.enabled);

            jest.spyOn(sequelize, "query").mockImplementationOnce(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            const result2 = await instance.getLinesGroupInfo(testData.linesGroupId.enabled);
            expect(result2).toBeDefined();
            expect(result2).not.toBeNull();
            expect(result2).toBeInstanceOf(Array);
            expect(result2.length).toBeGreaterThanOrEqual(1);
            expect(result2[0].groupId).toBe(testData.linesGroupId.enabled);
        });

        it("should throw error if DB timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => {
                await instance.getLinesGroupInfo(testData.linesGroupId.enabled);
            }).rejects.toThrowError(ConnectionTimedOutError);
        });

        it("should return empty array if only found status = 3", async () => {
            const result = await instance.getLinesGroupInfo(testData.linesGroupId.status3);
            expect(result).toBeDefined();
            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(Array);
            expect(result.length).toBe(0);
        });

        it("should return empty array if not found", async () => {
            const result = await instance.getLinesGroupInfo(testData.linesGroupId.notfound);
            expect(result).toBeDefined();
            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(Array);
            expect(result.length).toBe(0);
        });
    });

    describe("getLineIds", () => {
        it("should return array of LineLineGroupsEntity", async () => {
            const result = await instance.getLineIds(testData.LineLineGroup.exist.group_id);
            expect(result).toBeDefined();
            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(Array);
            expect(result.length).toBeGreaterThanOrEqual(1);
            expect(result[0].groupId).toBe(testData.LineLineGroup.exist.group_id);

            jest.spyOn(sequelize, "query").mockImplementationOnce(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            const result2 = await instance.getLineIds(testData.LineLineGroup.exist.group_id);
            expect(result2).toBeDefined();
            expect(result2).not.toBeNull();
            expect(result2).toBeInstanceOf(Array);
            expect(result2.length).toBeGreaterThanOrEqual(1);
            expect(result2[0].groupId).toBe(testData.LineLineGroup.exist.group_id);
        });

        it("should throw error if DB timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => {
                await instance.getLineIds(testData.LineLineGroup.exist.group_id);
            }).rejects.toThrowError(ConnectionTimedOutError);
        });

        it("should return empty array if not found", async () => {
            const result = await instance.getLineIds(testData.LineLineGroup.notExist.group_id);
            expect(result).toBeDefined();
            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(Array);
            expect(result.length).toBe(0);
        });
    });

    describe("getPlanInfoFromLineLineGroups", () => {
        it("should return LineGroupsEntity with only planId and planName set", async () => {
            const result = await instance.getPlanInfoFromLineLineGroups(testData.LineGroup.exist.groupId);

            expect(result).toBeDefined();
            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(LineGroupsEntity);
            expect(result.planId).toBe(Number(testData.LineGroup.exist.planId));
            expect(result.planName).toBeDefined();
            expect(result.planName).not.toBeNull();
            expect(result.planName).not.toBe("");

            jest.spyOn(sequelize, "query").mockImplementationOnce(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            const result2 = await instance.getPlanInfoFromLineLineGroups(testData.LineGroup.exist.groupId);
            expect(result2).toBeDefined();
            expect(result2).not.toBeNull();
            expect(result2).toBeInstanceOf(LineGroupsEntity);
            expect(result2.planId).toBe(Number(testData.LineGroup.exist.planId));
            expect(result2.planName).toBeDefined();
            expect(result2.planName).not.toBeNull();
            expect(result2.planName).not.toBe("");
        });

        it("should throw error if DB timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => {
                await instance.getPlanInfoFromLineLineGroups(testData.LineGroup.exist.groupId);
            }).rejects.toThrowError(ConnectionTimedOutError);
        });

        it("should return LineGroupsEntity with planId and planName set to null if not found", async () => {
            const result = await instance.getPlanInfoFromLineLineGroups(testData.LineGroup.notExist.groupId);
            expect(result).toBeNull();
        });
    });

    describe("getLinesGroupId", () => {
        it("should return LineLineGroupsEntity if found", async () =>{
            const result = await instance.getLinesGroupId(testData.LineLineGroup.exist.line_id);
            expect(result).toBeDefined();
            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(LineLineGroupsEntity);
            expect(result.lineId).toBe(testData.LineLineGroup.exist.line_id);

            jest.spyOn(sequelize, "query").mockImplementationOnce(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            const result2 = await instance.getLinesGroupId(testData.LineLineGroup.exist.line_id);
            expect(result2).toBeDefined();
            expect(result2).not.toBeNull();
            expect(result2).toBeInstanceOf(LineLineGroupsEntity);
            expect(result2.lineId).toBe(testData.LineLineGroup.exist.line_id);
        })

        it("should return null if not found", async () =>{
            const result = await instance.getLinesGroupId(testData.LineLineGroup.notExist.line_id);
            expect(result).toBeNull();
        })

        it("should throw error if DB timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => {
                await instance.getLinesGroupId(testData.LineLineGroup.exist.line_id);
            }).rejects.toThrowError(ConnectionTimedOutError);
        });
    });

    describe("getGroupOptionPlan", () => {
        const mockEntity = {
            optionPlanId: 999999,
            optionPlanName: "test_option_plan",
            optionPlanDescription: "test_option_plan_description",
            optionPlanType: "00",
        };

        beforeAll(async () => {
            await GroupOptionPlansEntity.create(mockEntity);
        })

        afterAll(async () => {
            await GroupOptionPlansEntity.destroy({
                where: {
                    optionPlanId: 999999,
                }
            });
        });

        it("should return a GroupOptionPlansEntity if found", async () => {
            const result = await instance.getGroupOptionPlan("999999");

            expect(result).toBeDefined();
            expect(result).not.toBeNull();
            expect(result.optionPlanId).toEqual(mockEntity.optionPlanId);
            expect(result.optionPlanName).toEqual(mockEntity.optionPlanName);
            expect(result.optionPlanDescription).toEqual(mockEntity.optionPlanDescription);
            expect(result.optionPlanType).toEqual(mockEntity.optionPlanType);
        });

        it("should return null if groupPlanId is not found", async () => {
            const groupPlanId = "0";

            jest.spyOn(GroupOptionPlansEntity, "findOne").mockResolvedValueOnce(null);

            const result = await instance.getGroupOptionPlan(groupPlanId);

            expect(result).toBeNull();
        });

        it("should throw DB error if timeout", async () => {
            const groupPlanId = "999999";

            jest.spyOn(GroupOptionPlansEntity, "findOne").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => {
                await instance.getGroupOptionPlan(groupPlanId);
            }).rejects.toThrowError(ConnectionTimedOutError);
        });

        it("should throw other error", async () => {
            const groupPlanId = "999999";

            jest.spyOn(GroupOptionPlansEntity, "findOne").mockImplementation(() => {
                throw new SyntaxError("unknown error");
            });

            await expect(async () => {
                await instance.getGroupOptionPlan(groupPlanId);
            }).rejects.toThrow(Error);
        });
    })

    describe("getGroupPlanId with transaction", () => {
        beforeAll(async () => {
            await createTransaction();
        });

        afterAll(async () => {
            await commitTransaction();
        });

        it("should return array of TenantGroupPlansEntity", async () => {
            const result = await instance.getGroupPlanId(testData.TenantGroupPlan.exist.tenant_id,testData.TenantGroupPlan.exist.plan_id,tx);
            expect(result).toBeDefined();
            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(TenantGroupPlansEntity);
            expect(result.planId).toBe(testData.TenantGroupPlan.exist.plan_id);
            expect(result.tenantId).toBe(testData.TenantGroupPlan.exist.tenant_id);
        });

        it("should return null if not found", async () => {
            const result = await instance.getGroupPlanId(testData.TenantGroupPlan.notExist.tenant_id,testData.TenantGroupPlan.notExist.plan_id,tx);
            expect(result).toBeDefined();
            expect(result).toBeNull();
        });

        it("should throw error if DB timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => {
                await instance.getGroupPlanId(testData.TenantGroupPlan.exist.tenant_id,testData.TenantGroupPlan.exist.plan_id,tx);
            }).rejects.toThrowError(ConnectionTimedOutError);
        });
    });

    describe("getServicePlanId with transaction", () => {
        beforeAll(async () => {
            await createTransaction();
        });

        afterAll(async () => {
            await commitTransaction();
        });

        it("should return GroupPlansEntity if found", async () => {
            const result = await instance.getServicePlanId(testData.GroupPlan.exist.plan_id,tx);
            expect(result).toBeDefined();
            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(GroupPlansEntity);
            expect(result.planId).toBe(testData.TenantGroupPlan.exist.plan_id);
        });

        it("should return null if not found", async () => {
            const result = await instance.getServicePlanId(testData.GroupPlan.notExist.plan_id,tx);
            expect(result).toBeDefined();
            expect(result).toBeNull();
        });
    });

    describe("getLinesGroupIdList", () => {

        it("should return array of group_id", async () => {
            const result = await instance.getLinesGroupIdList(new Date(),"10");
            expect(result).toBeDefined();
            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(Array);
        });

        it("should throw error if DB timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => {
                await instance.getLinesGroupIdList(new Date(),"10");
            }).rejects.toThrowError(ConnectionTimedOutError);
        });
    });

    describe("getLineGroupsLockForNew with transaction", () => {

        beforeAll(async () => {
            await createTransaction();
        });

        afterAll(async () => {
            await commitTransaction();
        });

        it("should return single LineGroupsEntity with group_id, tenant_id and status", async () => {
            const result = await instance.getLineGroupsLockForNew(testData.LineGroup.exist.groupId,tx);
            expect(result).toBeDefined();
            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(LineGroupsEntity);
            expect(result.groupId).toBe(testData.LineGroup.exist.groupId);
            expect(result.tenantId).toBeDefined();
            expect(result.status).toBeDefined();
        });

        it("should return null if line group not found", async () => {
            const result = await instance.getLineGroupsLockForNew(testData.LineGroup.notExist.groupId,tx);
            expect(result).toBeNull();
        });
 
        it("should throw error if DB timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => {
                await instance.getLineGroupsLockForNew(testData.LineGroup.exist.groupId,tx);
            }).rejects.toThrowError(ConnectionTimedOutError);
        });
    });

    describe("getLineGroupsLock with transaction", () => {

        beforeAll(async () => {
            await createTransaction();
        });

        afterAll(async () => {
            await commitTransaction();
        });

        it("should return single LineGroupsEntity with group_id, tenant_id and status", async () => {
            const result = await instance.getLineGroupsLock(testData.LineGroup.exist.groupId,tx);
            expect(result).toBeDefined();
            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(LineGroupsEntity);
            expect(result.groupId).toBe(testData.LineGroup.exist.groupId);
            expect(result.tenantId).toBeDefined();
            expect(result.status).toBeDefined();
        });

        it("should return null if line group not found", async () => {
            const result = await instance.getLineGroupsLock(testData.LineGroup.notExist.groupId,tx);
            expect(result).toBeNull();
        });

        it("should throw error if DB timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => {
                await instance.getLineGroupsLock(testData.LineGroup.exist.groupId,tx);
            }).rejects.toThrowError(ConnectionTimedOutError);
        });
    });

    describe("getLineIdCount with transaction", () => {
 
        beforeAll(async () => {
            await createTransaction();
        });

        afterAll(async () => {
            await commitTransaction();
        });

        it("should return number except 0 if line group exist", async () => {

            const result = await instance.getLineIdCount(testData.LineLineGroup.exist.group_id,tx);
            expect(result).toBe(10241);
        });

        it("should return 0 if line group does not exist", async () => {
            const result = await instance.getLineIdCount(testData.LineLineGroup.notExist.group_id,tx);
            expect(result).toBe(0);
        });

        it("should throw error if DB timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => {
                await instance.getLineIdCount(testData.LineLineGroup.exist.group_id,tx);
            }).rejects.toThrowError(ConnectionTimedOutError);
        });
    });

    describe("getLineIdCount without transaction", () => {
 
        it("should return number except 0 if line group exist", async () => {

            const result = await instance.getLineIdCount(testData.LineLineGroup.exist.group_id);
            expect(result).toBe(10241);
        });

        it("should return 0 if line group does not exist", async () => {
            const result = await instance.getLineIdCount(testData.LineLineGroup.notExist.group_id);
            expect(result).toBe(0);
        });

        it("should throw error if DB timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => {
                await instance.getLineIdCount(testData.LineLineGroup.exist.group_id);
            }).rejects.toThrowError(ConnectionTimedOutError);
        });
    });

    describe("updateLineLineGroupsUsageStatus", ()=>{

        it("update usageStatus", async () => {
            let tx: Transaction = null;
            try{
                tx = await sequelize.transaction();
                await instance.updateLineLineGroupsUsageStatus(testData.LineLineGroup.exist.group_id,testData.LineLineGroup.exist.line_id,3072,tx);

                await tx.commit();
                const res = await LineLineGroupsEntity.findOne({
                    where:{
                        lineId: testData.LineLineGroup.exist.line_id,
                    }
                });
                expect(res.usageStatus).toBe(3072)
            } finally{
                tx = await sequelize.transaction();
                await instance.updateLineLineGroupsUsageStatus(testData.LineLineGroup.exist.group_id,testData.LineLineGroup.exist.line_id,null,tx);
                await tx.commit();
            };
        });


        it("should throw error if DB timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => {
                await instance.updateLineLineGroupsUsageStatus(testData.LineLineGroup.exist.group_id,testData.LineLineGroup.exist.line_id,3072,tx);
            }).rejects.toThrowError(ConnectionTimedOutError);
        });
    });

    describe("getLineLineGroupsSumCapacity", ()=>{
        it("should return sum of basic_capacity of the groupId if found", async () =>{
            const result = await instance.getLineLineGroupsSumCapacity(testData.LineLineGroup.exist.group_id);
            expect(result).toBeDefined();
            expect(result).not.toBeNull();
            expect(result).toBe(104858624);
        })

        it("should return null if not found", async () =>{
            const result = await instance.getLineLineGroupsSumCapacity(testData.LineLineGroup.notExist.line_id);
            expect(result).toBeNull();
        })
    });

    describe("getUpdateServicePatternGroup",  () => {
        it("should return GroupPlansEntity if found", async () => {
            const result = await instance.getUpdateServicePatternGroup(testData.GroupPlans.exist);
            expect(result).toBeDefined();
            expect(result).not.toBeNull();
            expect(result.planId).toBe(testData.GroupPlans.exist);

            jest.spyOn(sequelize, "query").mockImplementationOnce(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            const result2 = await instance.getUpdateServicePatternGroup(testData.GroupPlans.exist);
            expect(result2).toBeDefined();
            expect(result2).not.toBeNull();
            expect(result2.planId).toBe(testData.GroupPlans.exist);
        })

        it("should return null if not found", async () => {
            const result = await instance.getUpdateServicePatternGroup(testData.GroupPlans.notExist);
            expect(result).toBeNull();
        })

        it("should throw error if DB timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => {
                await instance.getUpdateServicePatternGroup(testData.GroupPlans.exist);
            }).rejects.toThrowError(ConnectionTimedOutError);
        })
    })

});