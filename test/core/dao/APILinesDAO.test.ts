import { afterAll, beforeAll, beforeEach, describe, expect, test } from "@jest/globals";
import { ConnectionTimedOutError, Sequelize, Transaction, QueryTypes } from "sequelize";
import { format, fromUnixTime } from "date-fns";

import DefaultRequest from "../../testing/DefaultRequest";
import DefaultContext from "../../testing/DefaultContext";
import { describeWithDB, generateProcessID } from "../../testing/TestHelper";

import APILinesDAO from "@/core/dao/APILinesDAO";
import PreLineAddInputDto from "@/core/dto/PreLineAddInputDto";
import { LinesEntity } from "@/core/entity/LinesEntity";
import ServiceOrdersEntity from "@/core/entity/ServiceOrdersEntity";
import { usePsql } from "@/database/psql";
import MvnoUtil from "@/core/common/MvnoUtil";
import { TenantNnumbersEntity } from "@/core/entity/TenantNnumbersEntity";
import PlansEntity from "@/core/entity/PlansEntity";
import { CustomerInfoEntity } from "@/core/entity/CustomerInfoEntity";
import TenantPlansEntity from "@/core/entity/TenantPlansEntity";
import LineTenantsEntity from "@/core/entity/LineTenantsEntity";
import LineOptionsEntity from "@/core/entity/LineOptionsEntity";
import TenantPlanLineOptionsEntity from "@/core/entity/TenantPlanLineOptionsEntity";
import LineTrafficsEntity from "@/core/entity/LineTrafficsEntity";
import LineLineGroupsEntity from "@/core/entity/LineLineGroupsEntity";
import AppConfig from "@/appconfig";
import OptionPlansEntity from "@/core/entity/OptionPlansEntity";

describeWithDB("core/dao/APILinesDAO", () => {
    let sequelize: Sequelize = null;
    let instance: APILinesDAO;
    let tx: Transaction = null;

    const deviceId = {
        lte: "AD7",
        sms: "AE0",
        voice: "AW9",
    };

    // test data
    const testdata = {
        line: {
            enabled: "08024041801",
            disabled: "08024041802",
            notexist: "08024041803",
            nnumber: ["N100000100", "N141003812"],
            nnumberNG: ["N141004000"],
            traffic: "07001100009",
            resale_plan_id: "AR305"
        },
        constants: {
            lineStatusOK: "01",
            lineStatusNG: "03",
        },
        planId: {
            exist: "10001",
            notexist: "-1",
        },
        customerInfo: {
            exist: "N999999901",
            notexist: "0",
            voicemailOptionIdType: "AO143",
            callWaitingOptionId: "AO142",
            alsoExistInLines:'01011112111',
        },
        line_option_id: {
            exist: "test:",
            notexist: "0",
        },
        lineTraffics: {
            year: 2018,
            month: 8,
        },
        cardCheckPlan:{
            exist:{
                purpose: 'C_OCN',
                planId: '99038',
                resalePlanId: 'BS022',
            },
            notexist:{
                purpose: '',
            },
        }
    };

    const systemDateTime = MvnoUtil.getDateTimeNow();
    const targetDate = format(
        fromUnixTime(MvnoUtil.convDateFormat(systemDateTime) / 1000),
        "yyyyMMdd",
    );

    const orderData = {
        processID: generateProcessID(),
        tenantId: "OPF000",
        targetTenantId: "TST000",
        nonExistTenantId: "TST9999",
        lineNo: testdata.line.enabled,
        sim_no: "SIMTEST2403301010",
        sim_type: "nanoSIM",
        cardTypeId: deviceId.lte,
        csvUnnecessaryFlag: "0",
        lineOptionType: 1000,
        lineGroupId: "7001025",
    };

    beforeAll(async () => {
        await AppConfig.loadCoreMvnoConfig(DefaultContext);
        instance = new APILinesDAO(DefaultRequest, DefaultContext);
        // override timeout
        Object.defineProperty(instance, "apDbRetryInterval", { value: 0.01 }); // 10ms

        sequelize = await usePsql();
        await deleteTestData();
        await createTestData();
    });

    afterAll(async () => {
        await deleteTestData();
        await sequelize.close();
    });

    afterEach(() => {
        jest.restoreAllMocks();
        jest.clearAllMocks();
    });

    const createTestData = async () => {
        await Promise.all([
            LinesEntity.create({
                lineId: testdata.line.enabled,
                lineStatus: testdata.constants.lineStatusOK,
                nnumber: testdata.line.nnumber[0],
                simFlag: false,
                pricePlanId: testdata.line.resale_plan_id,
                voiceMailId: testdata.customerInfo.voicemailOptionIdType
            }),
            LinesEntity.create({
                lineId: testdata.line.disabled,
                lineStatus: testdata.constants.lineStatusNG,
                nnumber: testdata.line.nnumber[1],
                simFlag: false,
            }),
            ServiceOrdersEntity.create({
                serviceOrderId: orderData.processID,
                tenantId: orderData.targetTenantId,
                lineId: orderData.lineNo,
                orderStatus: "予約中",
                orderType: "プラン変更",
                reserveDate: new Date(),
                functionType: "51",
            }),
            TenantNnumbersEntity.create({
                tenantId: orderData.targetTenantId,
                nnumber: testdata.line.nnumber[0],
                wholeFlag: true,
                fullMvnoFlag: true,
            }),
            TenantPlansEntity.create({
                tenantId: orderData.targetTenantId,
                planId: parseInt(testdata.planId.exist, 10),
                changePlanId: parseInt(testdata.planId.exist, 10),
                changeCount: 1,
                changeTiming: "1"
            }),
            LineTenantsEntity.create({
                lineId: orderData.lineNo,
                tenantId: orderData.targetTenantId,
            }),
            LineOptionsEntity.create({
                lineOptionId: testdata.line_option_id.exist,
                lineOptionType: 4,
                optionPlanName: "testing",
                cOcn: true,
                bOcn: false,
                uno: false,
                whole: true,
                id: true,
                lineOptionIdT: "testing"
            }),
            TenantPlanLineOptionsEntity.create({
                tenantId: orderData.targetTenantId,
                planId: parseInt(testdata.planId.exist, 10),
                lineOptionType: orderData.lineOptionType,
            }),
            LineLineGroupsEntity.create({
                lineId: testdata.line.enabled,
                groupId: orderData.lineGroupId,
                basicCapacity: 10240
            }),
        ]);
    };

    const deleteTestData = async () => {
        await Promise.all([
            LineTenantsEntity.destroy({
                where: {
                    lineId: orderData.lineNo,
                    tenantId: orderData.targetTenantId,
                },
            }),
            LineOptionsEntity.destroy({
                where: {
                    lineOptionId: testdata.line_option_id.exist,
                }
            }),
            LineLineGroupsEntity.destroy({
                where: {
                    lineId: testdata.line.enabled,
                    groupId: orderData.lineGroupId,
                }
            }),
            TenantPlanLineOptionsEntity.destroy({
                where: {
                    tenantId: orderData.targetTenantId,
                    planId: parseInt(testdata.planId.exist, 10),
                    lineOptionType: orderData.lineOptionType,
                }
            }),
            ServiceOrdersEntity.destroy({
                where: {
                    serviceOrderId: orderData.processID,
                },
            }),
            TenantNnumbersEntity.destroy({
                where: {
                    tenantId: orderData.targetTenantId,
                    nnumber: testdata.line.nnumber,
                },
            }),
            TenantPlansEntity.destroy({
                where: {
                    tenantId: orderData.targetTenantId,
                    planId: parseInt(testdata.planId.exist, 10),
                }
            }),
            LinesEntity.destroy({
                where: {
                    lineId: [testdata.line.enabled, testdata.line.disabled],
                },
            }),
        ]);
    };

    describe("getLineInfoByLines", () => {
        test("should return LinesEntity if lineNo exists", async () => {
            const result = await instance.getLineInfoByLines(
                testdata.line.enabled,
            );
            expect(result).not.toBeNull();
            expect(result.lineId).toBe(testdata.line.enabled);
        });

        test("should return LinesEntity even if line status is '03'", async () => {
            // NOTE: line status '03' is disabled and not used in query
            const result = await instance.getLineInfoByLines(
                testdata.line.disabled,
            );
            expect(result).not.toBeNull();
            expect(result.lineId).toBe(testdata.line.disabled);
        });

        test("should return null if lineNo does not exist", async () => {
            const result = await instance.getLineInfoByLines(
                testdata.line.notexist,
            );
            expect(result).toBeNull();
        });

        test("should throw DB error if timeout", async () => {
            jest.spyOn(LinesEntity, "findOne").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => {
                await instance.getLineInfoByLines(testdata.line.disabled);
            }).rejects.toThrowError(ConnectionTimedOutError);
        });

        test("should throw other error", async () => {
            jest.spyOn(LinesEntity, "findOne").mockImplementation(() => {
                throw new SyntaxError("unknown error");
            });

            await expect(async () => {
                await instance.getLineInfoByLines(testdata.line.disabled);
            }).rejects.toThrow(Error);
        });
    });

    describe("getAbolitionOrder", () => {
        // TODO add unit tests for getAbolitionOrder
        test("should throw DB error if timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => {
                await instance.getAbolitionOrder(
                    testdata.line.disabled,
                    "AP12345678",
                );
            }).rejects.toThrowError(ConnectionTimedOutError);
        });

        test("should throw other error", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new SyntaxError("unknown error");
            });

            await expect(async () => {
                await instance.getAbolitionOrder(
                    testdata.line.disabled,
                    "AP12345678",
                );
            }).rejects.toThrow(Error);
        });
    });

    describe("getReservedServiceOrder", () => {
        test("should return a list of reserved service order IDs", async () => {
            const result = await instance.getReservedServiceOrder(
                orderData.lineNo,
                targetDate,
            );
            expect(result).not.toBeNull();
            expect(typeof result).toEqual("object");
            expect(result).toEqual([orderData.processID]);
        });

        test("should throw DB error if timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => {
                await instance.getReservedServiceOrder(
                    orderData.lineNo,
                    targetDate,
                );
            }).rejects.toThrowError(ConnectionTimedOutError);
        });

        test("should throw other error", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new SyntaxError("unknown error");
            });

            await expect(async () => {
                await instance.getReservedServiceOrder(
                    orderData.lineNo,
                    targetDate,
                );
            }).rejects.toThrow(Error);
        });
    });

    describe("getLineInfoForUpdate (transaction)", () => {
        test("should return null if line does not exist", async () => {
            let tx: Transaction = null;
            try {
                tx = await sequelize.transaction();
                const result = await instance.getLineInfoforUpdate(
                    testdata.line.notexist,
                    tx,
                );
                expect(result).toBeNull();
            } finally {
                if (tx) {
                    await tx.rollback();
                }
            }
        });

        test("should return line_id if found and not locked", async () => {
            let tx: Transaction = null;
            try {
                tx = await sequelize.transaction();
                const result = await instance.getLineInfoforUpdate(
                    testdata.line.enabled,
                    tx,
                );
                expect(result).not.toBeNull();
                expect(result).toBe(testdata.line.enabled);
            } finally {
                if (tx) {
                    await tx.rollback();
                }
            }
        });

        test("should throw error if line is locked", async () => {
            let tx1: Transaction = null;
            let tx2: Transaction = null;
            try {
                tx1 = await sequelize.transaction();
                tx2 = await sequelize.transaction();
                const line1Promise = instance.getLineInfoforUpdate(
                    testdata.line.enabled,
                    tx1,
                );
                const line2Promise = instance.getLineInfoforUpdate(
                    testdata.line.enabled,
                    tx2,
                );

                let errorThrown = false;
                try {
                    await line1Promise;
                    await line2Promise;
                } catch (error) {
                    errorThrown = true;
                }
                expect(errorThrown).toBe(true);
            } finally {
                await Promise.all([tx1.rollback(), tx2.rollback()]);
            }
        });
    });

    describe("updateLineContractDeviceType (transaction)", () => {
        test("should return number of updated rows if line exists", async () => {
            let tx: Transaction = null;
            try {
                tx = await sequelize.transaction();
                const result = await instance.updateLineContractDeviceType(
                    testdata.line.enabled,
                    "SC",
                    "SA12",
                    "some model name",
                    new Date().getTime(),
                    tx,
                );
                expect(result).toBe(1);
            } catch (error) {
                // console.error(">>", error);
                // should not return error
                expect(error).toBeNull();
            } finally {
                if (tx) {
                    await tx.rollback();
                }
            }
        });
    });

    describe("updateLineOptionInfo", () => {
        afterAll(async () => {
            await LinesEntity.update(
                {
                    roamingMaxId: null,
                    voiceMailId: testdata.customerInfo.voicemailOptionIdType,
                    callWaitingId: null,
                    intlCallId: null,
                    forwardingId: null,
                    intlForwardingId: null,
                },
                {
                    where: {
                        lineId: testdata.line.enabled,
                    },
                },
            );
        });

        test("should update line option info", async () => {
            const lineOptionAddInfo = new LinesEntity();
            const lineOptionUpdateKubn: Map<string, boolean> = new Map<
                string,
                boolean
            >();

            lineOptionAddInfo.roamingMaxId = "123";
            lineOptionUpdateKubn.set("intlRoaming", true);
            lineOptionAddInfo.voiceMailId = "123";
            lineOptionUpdateKubn.set("voicemail", true);
            lineOptionAddInfo.callWaitingId = "123";
            lineOptionUpdateKubn.set("callWaiting", true);
            lineOptionAddInfo.intlCallId = "123";
            lineOptionUpdateKubn.set("intlCall", true);
            lineOptionAddInfo.forwardingId = "123";
            lineOptionUpdateKubn.set("forwarding", true);
            lineOptionAddInfo.intlForwardingId = "123";
            lineOptionUpdateKubn.set("intlForwarding", true);

            await instance.updateLineOptionInfo(
                testdata.line.enabled,
                lineOptionAddInfo,
                lineOptionUpdateKubn,
            );

            const result = await instance.getLineInfoByLines(
                testdata.line.enabled,
            );

            expect(result.dataValues.roamingMaxId).toEqual(
                lineOptionAddInfo.roamingMaxId,
            );
            expect(result.dataValues.voiceMailId).toEqual(
                lineOptionAddInfo.voiceMailId,
            );
            expect(result.dataValues.callWaitingId).toEqual(
                lineOptionAddInfo.callWaitingId,
            );
            expect(result.dataValues.intlCallId).toEqual(
                lineOptionAddInfo.intlCallId,
            );
            expect(result.dataValues.forwardingId).toEqual(
                lineOptionAddInfo.forwardingId,
            );
            expect(result.dataValues.intlForwardingId).toEqual(
                lineOptionAddInfo.intlForwardingId,
            );

            // expect(result).not.toBeNull();
            // expect(typeof result).toEqual('object');
            // expect(result).toEqual([orderData.processID]);
        });

        test("should do nothing if all options are false", async () => {
            const querySpy = jest.spyOn(LinesEntity, "update");

            const lineOptionAddInfo = new LinesEntity();
            const lineOptionUpdateKubn: Map<string, boolean> = new Map<
                string,
                boolean
            >();

            lineOptionAddInfo.roamingMaxId = "123";
            lineOptionUpdateKubn.set("intlRoaming", false);
            lineOptionAddInfo.voiceMailId = "123";
            lineOptionUpdateKubn.set("voicemail", false);
            lineOptionAddInfo.callWaitingId = "123";
            lineOptionUpdateKubn.set("callWaiting", false);
            lineOptionAddInfo.intlCallId = "123";
            lineOptionUpdateKubn.set("intlCall", false);
            lineOptionAddInfo.forwardingId = "123";
            lineOptionUpdateKubn.set("forwarding", false);
            lineOptionAddInfo.intlForwardingId = "123";
            lineOptionUpdateKubn.set("intlForwarding", false);

            await instance.updateLineOptionInfo(
                testdata.line.enabled,
                lineOptionAddInfo,
                lineOptionUpdateKubn,
            );

            expect(querySpy).not.toHaveBeenCalled();
        });

        test("should throw DB error if timeout", async () => {
            jest.spyOn(LinesEntity, "update").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => {
                const lineOptionAddInfo = new LinesEntity();
                const lineOptionUpdateKubn: Map<string, boolean> = new Map<
                    string,
                    boolean
                >();

                lineOptionAddInfo.roamingMaxId = "123";
                lineOptionUpdateKubn.set("intlRoaming", true);
                lineOptionAddInfo.voiceMailId = "123";
                lineOptionUpdateKubn.set("voicemail", true);
                lineOptionAddInfo.callWaitingId = "123";
                lineOptionUpdateKubn.set("callWaiting", true);
                lineOptionAddInfo.intlCallId = "123";
                lineOptionUpdateKubn.set("intlCall", true);
                lineOptionAddInfo.forwardingId = "123";
                lineOptionUpdateKubn.set("forwarding", true);
                lineOptionAddInfo.intlForwardingId = "123";
                lineOptionUpdateKubn.set("intlForwarding", true);

                await instance.updateLineOptionInfo(
                    testdata.line.enabled,
                    lineOptionAddInfo,
                    lineOptionUpdateKubn,
                );

                await instance.getLineInfoByLines(testdata.line.enabled);
            }).rejects.toThrowError(ConnectionTimedOutError);
        });

        test("should throw other error", async () => {
            jest.spyOn(LinesEntity, "update").mockImplementation(() => {
                throw new SyntaxError("unknown error");
            });

            await expect(async () => {
                const lineOptionAddInfo = new LinesEntity();
                const lineOptionUpdateKubn: Map<string, boolean> = new Map<
                    string,
                    boolean
                >();

                lineOptionAddInfo.roamingMaxId = "123";
                lineOptionUpdateKubn.set("intlRoaming", true);
                lineOptionAddInfo.voiceMailId = "123";
                lineOptionUpdateKubn.set("voicemail", true);
                lineOptionAddInfo.callWaitingId = "123";
                lineOptionUpdateKubn.set("callWaiting", true);
                lineOptionAddInfo.intlCallId = "123";
                lineOptionUpdateKubn.set("intlCall", true);
                lineOptionAddInfo.forwardingId = "123";
                lineOptionUpdateKubn.set("forwarding", true);
                lineOptionAddInfo.intlForwardingId = "123";
                lineOptionUpdateKubn.set("intlForwarding", true);

                await instance.updateLineOptionInfo(
                    testdata.line.enabled,
                    lineOptionAddInfo,
                    lineOptionUpdateKubn,
                );

                await instance.getLineInfoByLines(testdata.line.enabled);
            }).rejects.toThrow(Error);
        });
    });

    describe("getTenantNnumbers", () => {
        test("should return single TenantNnumbersEntity for given tetnantId and nnumber", async () => {
            const result: TenantNnumbersEntity = await instance.getTenantNnumbers(
                orderData.targetTenantId,
                testdata.line.nnumber[0],
            );
            expect(result).not.toBeNull();
            expect(typeof result).toEqual("object");
            expect(result.nnumber).toEqual(testdata.line.nnumber[0]);
            expect(result.tenantId).toEqual(orderData.targetTenantId);
            expect(result.wholeFlag).toEqual(true);
            expect(result.fullMvnoFlag).toEqual(true);

            jest.spyOn(TenantNnumbersEntity, "findOne").mockImplementationOnce(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            const result2: TenantNnumbersEntity = await instance.getTenantNnumbers(
                orderData.targetTenantId,
                testdata.line.nnumber[0],
            );
            expect(result2).not.toBeNull();
            expect(typeof result2).toEqual("object");
            expect(result2.nnumber).toEqual(testdata.line.nnumber[0]);
            expect(result2.tenantId).toEqual(orderData.targetTenantId);
            expect(result2.wholeFlag).toEqual(true);
            expect(result2.fullMvnoFlag).toEqual(true);
        });

        test("should return null if nnumber does not exist for a given tetnantId", async () => {
            const result: TenantNnumbersEntity = await instance.getTenantNnumbers(
                orderData.targetTenantId,
                testdata.line.nnumberNG[0],
            );
            expect(result).toBeNull();
        });

        test("should throw DB error if timeout", async () => {
            jest.spyOn(TenantNnumbersEntity, "findOne").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => {
                await instance.getTenantNnumbers(
                    orderData.targetTenantId,
                    testdata.line.nnumberNG[0],
                );
            }).rejects.toThrowError(ConnectionTimedOutError);
        });
    });

    describe("updateLineInfoLineDeleteStatus (transaction)", () => {
        afterAll(async () => {
            await LinesEntity.update({
                lineStatus: testdata.constants.lineStatusOK,
                updatedAt: null,
            }, {
                where: {
                    lineId: testdata.line.enabled,
                },
            })
        });
        test("line line_status should be 03 and updatedAt should not be null", async () => {
            tx = await sequelize.transaction();
            await instance.updateLineInfoLineDeleteStatus(
                tx,
                testdata.line.enabled,
                new Date().getTime()
            );

            await tx.commit();

            const line = await LinesEntity.findOne({
                where: {
                    lineId: [testdata.line.enabled],
                },
            })

            expect(line.lineStatus).toBe(testdata.constants.lineStatusNG);
            expect(line.updatedAt).not.toBeNull();
        });
    });

    describe("insertAboneLines (transaction)", () => {
        test("should create new entry in abone_lines with existing line number", async () => {
            // const lineData: PreLineAddInputDto = {
            const lineData: any = {
                lineNo: testdata.line.enabled,
                lineDelTenantId: orderData.tenantId,
            };

            let tx: Transaction = null;
            try {
                tx = await sequelize.transaction();
                await instance.insertAboneLines(
                    tx,
                    lineData,
                    new Date().getTime()
                );

                await tx.commit();

                const result = await sequelize.query(
                    'SELECT * \
                    FROM abone_lines \
                    WHERE line_id = :line_id \
                    AND old_tenant_id = :old_tenant_id', {
                        replacements: {
                            line_id: testdata.line.enabled,
                            old_tenant_id: orderData.tenantId
                        },
                        type: QueryTypes.SELECT
                }) as any[];

                expect(result).not.toBeNull();
                expect(result.length).toEqual(1);
                expect(result[0].line_id).toBe(testdata.line.enabled);
                expect(result[0].old_tenant_id).toBe(orderData.tenantId);

            } catch (error) {
                // should not return error
                expect(error).toBeNull();
            } finally {
                if (tx) {
                    await sequelize.query(
                        'DELETE \
                        FROM abone_lines \
                        WHERE line_id = :line_id \
                        AND old_tenant_id = :old_tenant_id', {
                            replacements: {
                                line_id: testdata.line.enabled,
                                old_tenant_id: orderData.tenantId
                            },
                            type: QueryTypes.DELETE
                    })
                }
            }
        });

        test("should create new entry in abone_lines with non-existing line number", async () => {
            // const lineData: PreLineAddInputDto = {
            const lineData: any = {
                lineNo: testdata.line.notexist,
                lineDelTenantId: orderData.tenantId,
            };

            let tx: Transaction = null;
            try {
                tx = await sequelize.transaction();
                await instance.insertAboneLines(
                    tx,
                    lineData,
                    new Date().getTime()
                );

                await tx.commit();

                const result = await sequelize.query(
                    'SELECT * \
                    FROM abone_lines \
                    WHERE line_id = :line_id \
                    AND old_tenant_id = :old_tenant_id', {
                        replacements: {
                            line_id: testdata.line.notexist,
                            old_tenant_id: orderData.tenantId
                        },
                        type: QueryTypes.SELECT
                }) as any[];

                expect(result).not.toBeNull();
                expect(result.length).toEqual(1);
                expect(result[0].line_id).toBe(testdata.line.notexist);
                expect(result[0].old_tenant_id).toBe(orderData.tenantId);

            } catch (error) {
                // should not return error
                expect(error).toBeNull();
            } finally {
                if (tx) {
                    await sequelize.query(
                        'DELETE \
                        FROM abone_lines \
                        WHERE line_id = :line_id \
                        AND old_tenant_id = :old_tenant_id', {
                            replacements: {
                                line_id: testdata.line.notexist,
                                old_tenant_id: orderData.tenantId
                            },
                            type: QueryTypes.DELETE
                    })
                }
            }
        });

        test("should throw DB error if timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            const lineData: any = {
                lineNo: testdata.line.notexist,
                lineDelTenantId: orderData.tenantId,
            };

            await expect(async () => {
                const tx: Transaction = await sequelize.transaction();
                return await instance.insertAboneLines(
                    tx,
                    lineData,
                    new Date().getTime()
                );
                }).rejects.toThrowError(ConnectionTimedOutError);
        });
    });

    describe("getTrafficforUpdate (transaction)", () => {
        test("should return one record", async () => {
            let tx: Transaction = null;
            try {
                tx = await sequelize.transaction();
                const result = await instance.getTrafficforUpdate(
                    tx,
                    testdata.line.traffic,
                    '2018/04/01'
                );
                expect(result).not.toBeNull();
                expect(result.length).toBe(1);
                expect(result[0]).toBe(testdata.line.traffic);
            } catch (error) {
                // should not return error
                expect(error).toBeNull();
            } finally {
                if (tx) {
                    await tx.rollback();
                }
            }
        });

        test("should return no records", async () => {
            let tx: Transaction = null;
            try {
                tx = await sequelize.transaction();
                const result = await instance.getTrafficforUpdate(
                    tx,
                    testdata.line.enabled,
                    '2018/04/01'
                );
                expect(result).not.toBeNull();
                expect(result.length).toBe(0);
            } catch (error) {
                // should not return error
                expect(error).toBeNull();
            } finally {
                if (tx) {
                    await tx.rollback();
                }
            }
        });

        test("should throw DB error if timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });
            await expect(async () => {
                const tx: Transaction = await sequelize.transaction();
                return await instance.getTrafficforUpdate(
                    tx,
                    testdata.line.traffic,
                    '2018/04/01'
                );
            }).rejects.toThrowError(ConnectionTimedOutError);
        });
    });

    describe("updateLineTraffictoNull (transaction)", () => {
        test("should update entry entry in line_traffics table and set traffic and traffic_coupon_off to null", async () => {
            let tx: Transaction = null;
            const year = 2000;
            const month = 3;
            try {
                // set some traffic data
                await sequelize.query(
                    `INSERT INTO line_traffics (line_id, year, month, traffic, traffic_coupon_off) \
                    VALUES ('${testdata.line.traffic}', ${year}, ${month}, 1000, 2000)`, {
                        type: QueryTypes.INSERT
                });


                // update traffic data
                tx = await sequelize.transaction();
                await instance.updateLineTraffictoNull(
                    tx,
                    testdata.line.traffic,
                    `${year}/${month+1}/01`
                );

                await tx.commit();

                // check data from database
                const result = await sequelize.query(
                    'SELECT * \
                    FROM line_traffics \
                    WHERE line_id = :line_id \
                    AND year = :year \
                    AND month = :month', {
                        replacements: {
                            line_id: testdata.line.traffic,
                            year,
                            month
                        },
                        type: QueryTypes.SELECT
                }) as any[];

                expect(result).not.toBeNull();
                expect(result.length).toEqual(1);
                expect(result[0].line_id).toBe(testdata.line.traffic);
                expect(result[0].year).toBe(year);
                expect(result[0].month).toBe(month);
                expect(result[0].traffic).toBeNull();
                expect(result[0].traffic_coupon_off).toBeNull();

            } catch (error) {
                if (tx) {
                    await tx.rollback();
                }
                // should not return error
                expect(error).toBeNull();
            } finally {
                await sequelize.query(
                    `DELETE FROM line_traffics \
                    WHERE line_id = '${testdata.line.traffic}' \
                    AND year = ${year} \
                    AND month = ${month}`, {
                        type: QueryTypes.DELETE
                });
            }
        });

        test("should throw DB error if timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });
            await expect(async () => {
                const tx: Transaction = await sequelize.transaction();
                return await instance.updateLineTraffictoNull(
                    tx,
                    testdata.line.traffic,
                    `2000/4/01`
                );
            }).rejects.toThrowError(ConnectionTimedOutError);
        });
    });

    describe("getPortalPlanIdInfo", () => {
        it("should able to get from plans entity", async () => {
            const result = await instance.getPortalPlanIdInfo(testdata.planId.exist);
            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(PlansEntity);
            expect(result.planId).toBe(parseInt((testdata.planId.exist), 10));

            jest.spyOn(sequelize, "query").mockImplementationOnce(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            const result2 = await instance.getPortalPlanIdInfo(testdata.planId.exist);
            expect(result2).not.toBeNull();
            expect(result2).toBeInstanceOf(PlansEntity);
            expect(result2.planId).toBe(parseInt((testdata.planId.exist), 10));
        });

        it("should return null if planId does not exist", async () => {
            const result = await instance.getPortalPlanIdInfo(testdata.planId.notexist);
            expect(result).toBeNull();
        });

        it("should throw DB error if timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => instance.getPortalPlanIdInfo(testdata.planId.exist))
                .rejects.toThrowError(ConnectionTimedOutError);
        });
    });

    describe("updateLines", () => {
        let line: LinesEntity;
        beforeAll(async () => {
            jest.restoreAllMocks();
            jest.clearAllMocks();
            await LinesEntity.destroy({
                where: {
                    lineId: '0'
                }
            });
        });

        beforeEach(async () => {
            line = await LinesEntity.create({
                lineId: "0",
                lineStatus: "01",
                nnumber: "N141003811",
                groupId: "800001",
                lineActDate: new Date("2014-10-23"),
                simNumber: "DN999999999999",
                modelType: "SIMのみ LTE SIM",
                imei: null,
                pricePlanId: "AR262",
                pricePlanName: "[docomo] OCN モバイル ONE for Business 200Kbps (3G)",
                contractType: "LTE",
                tempRegist: false,
                simFlag: false,
            });
        });

        afterEach(async () => {
            jest.restoreAllMocks();
            jest.clearAllMocks();
            await LinesEntity.destroy({
                where: {
                    lineId: '0'
                }
            });
        });

        it("should able to update the line", async () => {
            line.lineStatus = "02";
            line.groupId = null;
            line.nnumber = "N999999999";
            line.pricePlanId = "AR104";
            line.pricePlanName = "[docomo] C_OCN卸プラン(SMS)";
            line.contractType = "3G(SMS)";
            line.simType = "microSIM";
            line.tempRegist = null;
            line.serviceDate = new Date("2015-10-23");
            line.roamingMaxId = "AO105";
            line.deviceTypeId = "A96";
            line.poi = "poi";
            line.modifyFlag = true;
            line.authPattern = "1";
            line.deviceModelName = "deviceModelName";
            line.imeisv_2 = "imeisv_2";
            line.imeisv_3 = "imeisv_3";
            line.notes = "notes";
            line.voiceMailId = "AO151";
            line.callWaitingId = "AO142";
            line.intlCallId = "AO129";
            line.forwardingId = "AO149";
            line.intlForwardingId = "AO149";
            line.simFlag = true;

            await instance.updateLines(line);

            const result = await LinesEntity.findOne({
                where: {
                    lineId: '0'
                }
            });

            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(LinesEntity);
            expect(result.groupId).toBeNull();
            expect(result.nnumber).toBe("N999999999");
            expect(result.pricePlanId).toBe("AR104");
            expect(result.pricePlanName).toBe("[docomo] C_OCN卸プラン(SMS)");
            expect(result.contractType).toBe("3G(SMS)");
            expect(result.simType).toBe("microSIM");
            expect(result.tempRegist).toBeNull();
            expect(result.serviceDate).toEqual(new Date("2015-10-23"));
            expect(result.roamingMaxId).toBe("AO105");
            expect(result.deviceTypeId).toBe("A96");
            expect(result.poi).toBe("poi");
            expect(result.modifyFlag).toBe(true);
            expect(result.authPattern).toBe(1);
            expect(result.deviceModelName).toBe("deviceModelName");
            expect(result.imeisv_2).toBe("imeisv_2");
            expect(result.imeisv_3).toBe("imeisv_3");
            expect(result.notes).toBe("notes");
            expect(result.voiceMailId).toBe("AO151");
            expect(result.callWaitingId).toBe("AO142");
            expect(result.intlCallId).toBe("AO129");
            expect(result.forwardingId).toBe("AO149");
            expect(result.intlForwardingId).toBe("AO149");
            expect(result.simFlag).toBe(true);


            jest.spyOn(sequelize, "query").mockImplementationOnce(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            try {
                await instance.updateLines(line);
            } catch (error) {
                // should not return error
                expect(error).toBeNull();
            }
        });

        it("should throw DB error if timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => await instance.updateLines(line))
                .rejects.toThrowError(ConnectionTimedOutError);
        });
    });

    describe("insertLines", () => {
        let line: LinesEntity;
        beforeAll(async () => {
            await LinesEntity.destroy({
                where: {
                    lineId: '0'
                }
            });
        });

        beforeEach(async () => {
            line = LinesEntity.build({
                lineId: "0",
                lineStatus: "02",
                groupId: null,
                nnumber: "N999999999",
                pricePlanId: "AR104",
                pricePlanName: "[docomo] C_OCN卸プラン(SMS)",
                contractType: "3G(SMS)",
                simType: "microSIM",
                tempRegist: null,
                serviceDate: new Date("2015-10-23"),
                roamingMaxId: "AO105",
                deviceTypeId: "A96",
                poi: "poi",
                modifyFlag: true,
                authPattern: "1",
                deviceModelName: "deviceModelName",
                imeisv_2: "imeisv_2",
                imeisv_3: "imeisv_3",
                notes: "notes",
                voiceMailId: "AO151",
                callWaitingId: "AO142",
                intlCallId: "AO129",
                forwardingId: "AO149",
                intlForwardingId: "AO149",
                simFlag: true,
            });
        });

        afterEach(async () => {
            jest.restoreAllMocks();
            jest.clearAllMocks();
            await LinesEntity.destroy({
                where: {
                    lineId: '0'
                }
            });
        });

        it("should able to insert the line", async () => {
            await instance.insertLines(line);

            const result = await LinesEntity.findOne({
                where: {
                    lineId: '0'
                }
            });

            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(LinesEntity);
            expect(result.groupId).toBe(null);
            expect(result.nnumber).toBe("N999999999");
            expect(result.pricePlanId).toBe("AR104");
            expect(result.pricePlanName).toBe("[docomo] C_OCN卸プラン(SMS)");
            expect(result.contractType).toBe("3G(SMS)");
            expect(result.simFlag).toBe(true);
            expect(result.simType).toBe("microSIM");
            expect(result.tempRegist).toBeNull();
            expect(result.serviceDate).toEqual(new Date("2015-10-23"));
            expect(result.roamingMaxId).toBe("AO105");
            expect(result.deviceTypeId).toBe("A96");
            expect(result.poi).toBe("poi");
            expect(result.modifyFlag).toBe(true);
            expect(result.authPattern).toBe(1);
            expect(result.deviceModelName).toBe("deviceModelName");
            expect(result.imeisv_2).toBe("imeisv_2");
            expect(result.imeisv_3).toBe("imeisv_3");
            expect(result.notes).toBe("notes");
            expect(result.voiceMailId).toBe("AO151");
            expect(result.callWaitingId).toBe("AO142");
            expect(result.intlCallId).toBe("AO129");
            expect(result.forwardingId).toBe("AO149");
            expect(result.intlForwardingId).toBe("AO149");
        });

        it("should able to insert the line even if failed once", async () => {
            jest.spyOn(sequelize, "query").mockImplementationOnce(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await instance.insertLines(line);

            const result = await LinesEntity.findOne({
                where: {
                    lineId: '0'
                }
            });

            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(LinesEntity);
            expect(result.nnumber).toBe("N999999999");
        });

        it("should throw error if timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => {
                await instance.insertLines(line)
            }).rejects.toThrowError(ConnectionTimedOutError);
        });
    });

    describe("getCustomerInfo", () => {
        it("should able to get customer info", async () => {
            const result = await instance.getCustomerInfo(testdata.customerInfo.exist);
            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(CustomerInfoEntity);
            expect(result.nnumber).toBe(testdata.customerInfo.exist);

            jest.spyOn(sequelize, "query").mockImplementationOnce(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            const result2 = await instance.getCustomerInfo(testdata.customerInfo.exist);
            expect(result2).not.toBeNull();
            expect(result2).toBeInstanceOf(CustomerInfoEntity);
            expect(result2.nnumber).toBe(testdata.customerInfo.exist);
        });

        it("should return null if line does not exist", async () => {
            const result = await instance.getCustomerInfo(testdata.customerInfo.notexist);
            expect(result).toBeNull();
        });

        it("should throw DB error if timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => instance.getCustomerInfo(testdata.customerInfo.exist))
                .rejects.toThrowError(ConnectionTimedOutError);
        });
    });

    describe("getCardCheckCard", () => {
        it("should return card type", async () => {
            const result = await instance.getCardCheckCard("A93", "開発試験用テナント");
            expect(result).not.toBeNull();
            expect(result).toBe("A93");

            jest.spyOn(sequelize, "query").mockImplementationOnce(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            const result2 = await instance.getCardCheckCard("A93", "開発試験用テナント");
            expect(result2).not.toBeNull();
            expect(result2).toBe("A93");
        });

        it("should return null if line does not exist", async () => {
            const result = await instance.getCardCheckCard("AAA", "開発試験用テナント");
            expect(result).toBeNull();
        });

        it("should throw DB error if timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => instance.getCardCheckCard("A93", "開発試験用テナント"))
                .rejects.toThrowError(ConnectionTimedOutError);
        });
    });

    describe("getPlanCard", () => {
        it("should return card type", async () => {
            const result = await instance.getPlanCard("90011", 'A93');
            expect(result).not.toBeNull();
            expect(result).toBe("A93");

            jest.spyOn(sequelize, "query").mockImplementationOnce(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            const result2 = await instance.getPlanCard("90011", 'A93');
            expect(result2).not.toBeNull();
            expect(result2).toBe("A93");
        });

        it("should return null if line does not exist", async () => {
            const result = await instance.getPlanCard("0", 'AAA');
            expect(result).toBeNull();
        });

        it("should throw DB error if timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => instance.getPlanCard("90011", 'A93'))
                .rejects.toThrowError(ConnectionTimedOutError);
        });
    });

    describe("updateLineKurokaInfo",  () => {
        beforeAll(async () => {
            await LinesEntity.destroy({
                where: {
                    lineId: "02"
                }
            });
        });

        beforeEach(async () => {
            await LinesEntity.create({
                lineId: "02",
                lineStatus: "01",
                nnumber: "N141003811",
                groupId: "800001",
                lineActDate: new Date("2014-10-23"),
                simNumber: "DN999999999999",
                modelType: "SIMのみ LTE SIM",
                imei: null,
                pricePlanId: "AR262",
                pricePlanName: "[docomo] OCN モバイル ONE for Business 200Kbps (3G)",
                contractType: "LTE",
                tempRegist: false,
                simFlag: false,
            });
        });

        afterEach(async () => {
            jest.restoreAllMocks();
            jest.clearAllMocks();
            await LinesEntity.destroy({
                where: {
                    lineId: "02"
                }
            });
        });

        it("should able to update line", async () => {
            await instance.updateLineKurokaInfo("02", true, new Date("2020-02-02"), new Date("2020-02-02"));

            const result = await LinesEntity.findOne({
                where: {
                    lineId: '02'
                }
            });

            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(LinesEntity);
            expect(result.simFlag).toBe(true);
            expect(result.activateDate).toEqual(new Date("2020-02-02"));
            expect(result.updatedAt).toEqual(new Date("2020-02-02"));
        });

        it("should able to update line even if failed once", async () => {
            jest.spyOn(sequelize, "query").mockImplementationOnce(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await instance.updateLineKurokaInfo("02", true, new Date("2020-02-02"), new Date("2020-02-02"));

            const result = await LinesEntity.findOne({
                where: {
                    lineId: '02'
                }
            });

            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(LinesEntity);
            expect(result.simFlag).toBe(true);
            expect(result.activateDate).toEqual(new Date("2020-02-02"));
            expect(result.updatedAt).toEqual(new Date("2020-02-02"));
        });

        it("should throw DB Error if timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () =>
                await instance.updateLineKurokaInfo("02", true, new Date("2020-02-02"), new Date("2020-02-02")))
            .rejects.toThrowError(ConnectionTimedOutError);
        });
    });

    describe("getLineIDList1" , () => {
        it("should return lineId list", async () => {
            const result = await instance.getLineIDList1(testdata.line.nnumber[0]);
            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(Array);
            expect(result.length).toBeGreaterThan(0);
            expect(result).toContain(testdata.line.enabled);

            jest.spyOn(sequelize, "query").mockImplementationOnce(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });
            const result2 = await instance.getLineIDList1(testdata.line.nnumber[0]);
            expect(result2).not.toBeNull();
            expect(result2).toBeInstanceOf(Array);
            expect(result2.length).toBeGreaterThan(0);
            expect(result2).toContain(testdata.line.enabled);
        });

        it("should return empty array if line does not exist", async () => {
            const result = await instance.getLineIDList1(testdata.line.nnumberNG[0]);
            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(Array);
            expect(result.length).toBe(0);
        });

        it("should throw DB error if timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => instance.getLineIDList1(testdata.line.nnumber[0]))
                .rejects.toThrowError(ConnectionTimedOutError);
        });
    });

    describe("getNnumbers", () => {
        it("should return nnumber list", async () => {
            const result = await instance.getNnumbers(orderData.targetTenantId);
            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(Array);
            expect(result.length).toBeGreaterThan(0);
            expect(result).toContain(testdata.line.nnumber[0]);

            jest.spyOn(sequelize, "query").mockImplementationOnce(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });
            const result2 = await instance.getNnumbers(orderData.targetTenantId);
            expect(result2).not.toBeNull();
            expect(result2).toBeInstanceOf(Array);
            expect(result2.length).toBeGreaterThan(0);
            expect(result2).toContain(testdata.line.nnumber[0]);
        });

        it("should return empty array if line does not exist", async () => {
            const result = await instance.getNnumbers(orderData.nonExistTenantId);
            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(Array);
            expect(result.length).toBe(0);
        });

        it("should throw DB error if timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => instance.getNnumbers(orderData.targetTenantId))
                .rejects.toThrowError(ConnectionTimedOutError);
        });
    });

    describe("getLineIDList2" , () => {
        it("should return lineId list even if failed once", async () => {
            const result = await instance.getLineIDList2(testdata.line.nnumber, orderData.targetTenantId);
            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(Array);
            expect(result.length).toBeGreaterThan(0);
            expect(result).toContain(testdata.line.enabled);
            expect(result).not.toContain(testdata.line.disabled);

            jest.spyOn(sequelize, "query").mockImplementationOnce(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            const result2 = await instance.getLineIDList2(testdata.line.nnumber, orderData.targetTenantId);
            expect(result2).not.toBeNull();
            expect(result2).toBeInstanceOf(Array);
            expect(result2.length).toBeGreaterThan(0);
            expect(result2).toContain(testdata.line.enabled);
            expect(result2).not.toContain(testdata.line.disabled);
        });

        it("should return empty array if empty nnumber array", async () => {
            const result = await instance.getLineIDList2([], orderData.targetTenantId);
            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(Array);
            expect(result.length).toBe(0);
        });

        it("should return empty array if line does not exist", async () => {
            const result = await instance.getLineIDList2(testdata.line.nnumberNG, orderData.nonExistTenantId);
            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(Array);
            expect(result.length).toBe(0);
        });

        it("should throw DB error if timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => instance.getLineIDList2(testdata.line.nnumber, orderData.targetTenantId))
                .rejects.toThrowError(ConnectionTimedOutError);
        });
    });

    describe("getPlanID", () => {
        it("should return list of planId", async () => {
            const result = await instance.getPlanID(orderData.targetTenantId);
            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(Array);
            expect(result.length).toBeGreaterThan(0);
            expect(result).toContain(testdata.planId.exist);

            jest.spyOn(sequelize, "query").mockImplementationOnce(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            const result2 = await instance.getPlanID(orderData.targetTenantId);
            expect(result2).not.toBeNull();
            expect(result2).toBeInstanceOf(Array);
            expect(result2.length).toBeGreaterThan(0);
            expect(result2).toContain(testdata.planId.exist);
        });

        it("should return empty array if tenant id does not exist", async () => {
            const result = await instance.getPlanID(orderData.targetTenantId + "9");
            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(Array);
            expect(result.length).toBe(0);
        });

        it("should throw DB error if timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => instance.getPlanID(orderData.targetTenantId))
                .rejects.toThrowError(ConnectionTimedOutError
            );
        });
    });

    describe("getResalePlanID", () => {
        it("should return list of planId", async () => {
            const result = await instance.getResalePlanID([testdata.planId.exist, '0']);
            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(Array);
            expect(result.length).toBeGreaterThan(0);

            jest.spyOn(sequelize, "query").mockImplementationOnce(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            const result2 = await instance.getResalePlanID([testdata.planId.exist, '0']);
            expect(result2).not.toBeNull();
            expect(result2).toBeInstanceOf(Array);
            expect(result2.length).toBeGreaterThan(0);
        });

        it("should return empty array if empty plan id", async () => {
            const result = await instance.getResalePlanID([]);
            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(Array);
            expect(result.length).toBe(0);
        });

        it("should return empty array if resale plan id does not exist", async () => {
            const result = await instance.getResalePlanID([testdata.planId.notexist, '0']);
            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(Array);
            expect(result.length).toBe(0);
        });

        it("should throw DB error if timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => instance.getResalePlanID([testdata.planId.exist, '0']))
                .rejects.toThrowError(ConnectionTimedOutError);
        });
    });

    describe("`getLineIDList3`" , () => {
        it("should return lineId list", async () => {
            const result = await instance.getLineIDList3(testdata.line.nnumber[0], [testdata.line.resale_plan_id, "0"]);
            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(Array);
            expect(result.length).toBeGreaterThan(0);
            expect(result).toContain(testdata.line.enabled);
            expect(result).not.toContain(testdata.line.disabled);

            jest.spyOn(sequelize, "query").mockImplementationOnce(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            const result2 = await instance.getLineIDList3(testdata.line.nnumber[0], [testdata.line.resale_plan_id, "0"]);
            expect(result2).not.toBeNull();
            expect(result2).toBeInstanceOf(Array);
            expect(result2.length).toBeGreaterThan(0);
            expect(result2).toContain(testdata.line.enabled);
            expect(result2).not.toContain(testdata.line.disabled);
        });

        it("should return empty array if empty resale plan id", async () => {
            const result = await instance.getLineIDList3(testdata.line.nnumber[0], []);
            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(Array);
            expect(result.length).toBe(0);
        });

        it("should return empty array if line does not exist", async () => {
            const result = await instance.getLineIDList3(testdata.line.nnumberNG[0], ["test1"]);
            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(Array);
            expect(result.length).toBe(0);
        });

        it("should throw DB error if timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => instance.getLineIDList3(testdata.line.nnumber[0], [testdata.line.resale_plan_id, "0"]))
                .rejects.toThrowError(ConnectionTimedOutError);
        });
    });

    describe("getLineIDList4" , () => {
        it("should return lineId list", async () => {
            const result = await instance.getLineIDList4(orderData.targetTenantId);
            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(Array);
            expect(result.length).toBeGreaterThan(0);
            expect(result).toContain(testdata.line.enabled);
            expect(result).not.toContain(testdata.line.disabled);

            jest.spyOn(sequelize, "query").mockImplementationOnce(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            const result2 = await instance.getLineIDList4(orderData.targetTenantId);
            expect(result2).not.toBeNull();
            expect(result2).toBeInstanceOf(Array);
            expect(result2.length).toBeGreaterThan(0);
            expect(result2).toContain(testdata.line.enabled);
            expect(result2).not.toContain(testdata.line.disabled);
        });

        it("should return empty array if line does not exist", async () => {
            const result = await instance.getLineIDList4(orderData.nonExistTenantId);
            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(Array);
            expect(result.length).toBe(0);
        });

        it("should throw DB error if timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => instance.getLineIDList4(orderData.targetTenantId))
                .rejects.toThrowError(ConnectionTimedOutError);
        });
    });

    describe("getRefServicePattern", () => {
        it("should return array of plans if found", async () => {
            const result = await instance.getRefServicePattern(testdata.line.enabled);
            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(Array);
            expect(result.length).toBeGreaterThan(0);
            expect(result[0]).toBeInstanceOf(PlansEntity);

            jest.spyOn(sequelize, "query").mockImplementationOnce(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            const result2 = await instance.getRefServicePattern(testdata.line.enabled);
            expect(result2).not.toBeNull();
            expect(result2).toBeInstanceOf(Array);
            expect(result2.length).toBeGreaterThan(0);
            expect(result2[0]).toBeInstanceOf(PlansEntity);
        });

        it("should return empty array if line does not exist", async () => {
            const result = await instance.getRefServicePattern(testdata.line.disabled);
            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(Array);
            expect(result.length).toBe(0);
        });

        it("should throw DB error if timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => instance.getRefServicePattern(testdata.line.enabled))
                .rejects.toThrowError(ConnectionTimedOutError);
        });
    });

    describe("getLineInfo", () => {
        describe("without transaction", () => {
            it("should return line info", async () => {
                const result = await instance.getLineInfo(testdata.line.enabled);
                expect(result).not.toBeNull();
                expect(result).toBeInstanceOf(LinesEntity);
                expect(result.lineId).toBe(testdata.line.enabled);
            });

            it("should not throw error with one fail", async () => {
                jest.spyOn(sequelize, "query").mockImplementationOnce(() => {
                    throw new ConnectionTimedOutError(new Error("timeout"));
                });

                const result = await instance.getLineInfo(testdata.line.enabled);
                expect(result).not.toBeNull();
                expect(result).toBeInstanceOf(LinesEntity);
                expect(result.lineId).toBe(testdata.line.enabled);
            });

            it("should throw error with multiple fail", async () => {
                jest.spyOn(sequelize, "query").mockImplementation(() => {
                    throw new ConnectionTimedOutError(new Error("timeout"));
                });

                await expect(async () => {
                    await instance.getLineInfo(testdata.line.enabled);
                }).rejects.toThrowError(ConnectionTimedOutError);
            });

            it("should return null if line does not exist", async () => {
                const result = await instance.getLineInfo(testdata.line.disabled);
                expect(result).toBeNull();
            });
        });

        describe("with transaction", () => {
            beforeEach(async () => {
                tx = await sequelize.transaction();
            });

            afterEach(async () => {
                await tx.rollback();
            });

            it("should return line info", async () => {
                const result = await instance.getLineInfo(testdata.line.enabled, tx);
                expect(result).not.toBeNull();
                expect(result).toBeInstanceOf(LinesEntity);
                expect(result.lineId).toBe(testdata.line.enabled);
            });

            it("should throw exception with one fail", async () => {
                jest.spyOn(sequelize, "query").mockImplementationOnce(() => {
                    throw new ConnectionTimedOutError(new Error("timeout"));
                });

                await expect(async () => {
                    await instance.getLineInfo(testdata.line.enabled, tx);
                }).rejects.toThrowError(ConnectionTimedOutError);
            });

            it("should return null if line does not exist", async () => {
                const result = await instance.getLineInfo(testdata.line.disabled, tx);
                expect(result).toBeNull();
            });
        });
    });

    describe("getLineOpenReserveOrder", () => {
        it("should return service order IDs if lineNo exists", async () => {
            const result = await instance.getLineOpenReserveOrder(orderData.lineNo);

            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(Array);
            expect(result.length).toBeGreaterThan(0);
            expect(result).toContain(orderData.processID);


            jest.spyOn(sequelize, "query").mockImplementationOnce(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            const result2 = await instance.getLineOpenReserveOrder(orderData.lineNo);

            expect(result2).not.toBeNull();
            expect(result2).toBeInstanceOf(Array);
            expect(result2.length).toBeGreaterThan(0);
            expect(result2).toContain(orderData.processID);
        });

        it("should return an empty array if no service orders are found", async () => {
            const result = await instance.getLineOpenReserveOrder(testdata.line.notexist);

            expect(result).toEqual([]);
        });

        it("should throw an error if persistent database error occurs", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(instance.getLineOpenReserveOrder(orderData.lineNo)).rejects.toThrowError(ConnectionTimedOutError);
        });
    });

    describe("getPlanList", () => {
        it("should return plan ids if exists", async () => {
            const result = await instance.getPlanList(orderData.targetTenantId, testdata.planId.exist);

            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(Array);
            expect(result.length).toBeGreaterThan(0);
            expect(result).toContain(testdata.planId.exist);


            jest.spyOn(sequelize, "query").mockImplementationOnce(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            const result2 = await instance.getPlanList(orderData.targetTenantId, testdata.planId.exist);

            expect(result2).not.toBeNull();
            expect(result2).toBeInstanceOf(Array);
            expect(result2.length).toBeGreaterThan(0);
            expect(result2).toContain(testdata.planId.exist);
        });

        it("should return an empty array if no plan ids are found", async () => {
            const result = await instance.getPlanList(orderData.targetTenantId, testdata.planId.notexist);

            expect(result).toEqual([]);
        });

        it("should throw an error if persistent database error occurs", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(instance.getPlanList(orderData.targetTenantId, testdata.planId.exist)).rejects.toThrowError(ConnectionTimedOutError);
        });
    });

    describe("getPlansInfoByPlanId", () => {
        it("should return plan info if planId exists", async () => {
            const result = await instance.getPlansInfoByPlanId(testdata.planId.exist);
            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(PlansEntity);
            expect(result.planId).toBe(parseInt(testdata.planId.exist, 10));

            jest.spyOn(sequelize, "query").mockImplementationOnce(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            const result2 = await instance.getPlansInfoByPlanId(testdata.planId.exist);
            expect(result2).not.toBeNull();
            expect(result2).toBeInstanceOf(PlansEntity);
            expect(result2.planId).toBe(parseInt(testdata.planId.exist, 10));
        });

        it("should return null if planId does not exist", async () => {
            const result = await instance.getPlansInfoByPlanId(testdata.planId.notexist);
            expect(result).toBeNull();
        });

        it("should throw DB error if timeout", async () => {
            jest.spyOn(PlansEntity, "findOne").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => {
                await instance.getPlansInfoByPlanId(testdata.planId.exist);
            }).rejects.toThrowError(ConnectionTimedOutError);
        });
    });

    describe("getLineOptionsInfo", () => {
        it("should return LineOptionsEntity if lineOptionId exists", async () => {
            const result = await instance.getLineOptionsInfo(testdata.line_option_id.exist);
            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(LineOptionsEntity);
            expect(result.lineOptionId).toBe(testdata.line_option_id.exist);


            jest.spyOn(sequelize, "query").mockImplementationOnce(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });
            const result2 = await instance.getLineOptionsInfo(testdata.line_option_id.exist);
            expect(result2).not.toBeNull();
            expect(result2).toBeInstanceOf(LineOptionsEntity);
            expect(result2.lineOptionId).toBe(testdata.line_option_id.exist);
        });

        it("should return null if lineOptionId does not exist", async () => {
            const result = await instance.getLineOptionsInfo(testdata.line_option_id.notexist);
            expect(result).toBeNull();
        });

        it("should throw DB error if timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => await instance.getLineOptionsInfo(testdata.line_option_id.exist))
                .rejects.toThrowError(ConnectionTimedOutError);
        });
    });

    describe("getTenantPlanLineOptionsInfo", () => {
        it("should return TenantPlanLineOptionsEntity if tenantId, planId, and lineOptionType exist", async () => {
            const result = await instance.getTenantPlanLineOptionsInfo(
                orderData.targetTenantId,
                testdata.planId.exist,
                orderData.lineOptionType
            );

            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(TenantPlanLineOptionsEntity);
            expect(result.tenantId).toBe(orderData.targetTenantId);
            expect(result.planId).toBe(parseInt(testdata.planId.exist, 10));
            expect(result.lineOptionType).toBe(orderData.lineOptionType);

            jest.spyOn(sequelize, "query").mockImplementationOnce(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            const result2 = await instance.getTenantPlanLineOptionsInfo(
                orderData.targetTenantId,
                testdata.planId.exist,
                orderData.lineOptionType
            );

            expect(result2).not.toBeNull();
            expect(result2).toBeInstanceOf(TenantPlanLineOptionsEntity);
            expect(result2.tenantId).toBe(orderData.targetTenantId);
            expect(result2.planId).toBe(parseInt(testdata.planId.exist, 10));
            expect(result2.lineOptionType).toBe(orderData.lineOptionType);
        });

        it("should return null if tenantId, planId, or lineOptionType do not exist", async () => {
            const result = await instance.getTenantPlanLineOptionsInfo(
                orderData.targetTenantId,
                testdata.planId.notexist,
                orderData.lineOptionType
            );

            expect(result).toBeNull();
        });

        it("should throw DB error if timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => await instance.getTenantPlanLineOptionsInfo(
                orderData.targetTenantId,
                testdata.planId.exist,
                orderData.lineOptionType
            )).rejects.toThrowError(ConnectionTimedOutError);
        });
    });

    describe("getVoicemailFromCustInfo", () => {
        it("should return voicemail id if voicemail option id matches", async () => {
            const result = await instance.getVoicemailFromCustInfo(testdata.customerInfo.exist, testdata.customerInfo.voicemailOptionIdType);
            expect(result).not.toBeNull();
            expect(result).toBe(testdata.customerInfo.voicemailOptionIdType);

            jest.spyOn(sequelize, "query").mockImplementationOnce(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            const result2 = await instance.getVoicemailFromCustInfo(testdata.customerInfo.exist, testdata.customerInfo.voicemailOptionIdType);
            expect(result2).not.toBeNull();
            expect(result2).toBe(testdata.customerInfo.voicemailOptionIdType);
        });

        it("should return null if does not match", async () => {
            const result = await instance.getVoicemailFromCustInfo(testdata.customerInfo.notexist, testdata.customerInfo.voicemailOptionIdType);
            expect(result).toBeNull();
        });

        it("should throw DB error if timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => await instance.getVoicemailFromCustInfo(testdata.customerInfo.exist, testdata.customerInfo.voicemailOptionIdType))
                .rejects.toThrowError(ConnectionTimedOutError);
        });
    });

    describe("getCallWaitingFromCustInfo", () => {
        it("should return call waiting id if call waiting option id matches", async () => {
            const result = await instance.getCallWaitingFromCustInfo(testdata.customerInfo.exist, testdata.customerInfo.callWaitingOptionId);
            expect(result).not.toBeNull();
            expect(result).toBe(testdata.customerInfo.callWaitingOptionId);

            jest.spyOn(sequelize, "query").mockImplementationOnce(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            const result2 = await instance.getCallWaitingFromCustInfo(testdata.customerInfo.exist, testdata.customerInfo.callWaitingOptionId);
            expect(result2).not.toBeNull();
            expect(result2).toBe(testdata.customerInfo.callWaitingOptionId);
        });

        it("should return null if does not match", async () => {
            const result = await instance.getCallWaitingFromCustInfo(testdata.customerInfo.notexist, testdata.customerInfo.callWaitingOptionId);
            expect(result).toBeNull();
        });

        it("should throw DB error if timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => await instance.getCallWaitingFromCustInfo(testdata.customerInfo.exist, testdata.customerInfo.callWaitingOptionId))
                .rejects.toThrowError(ConnectionTimedOutError);
        });
    });

    describe("getWholeFlag", () => {
        it("should return list of whole flags if line exists", async () => {
            const result = await instance.getWholeFlag(orderData.targetTenantId, testdata.line.nnumber[0]);
            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(Array);
            expect(result.length).toBeGreaterThan(0);
            expect(result[0]).toBe(true);

            jest.spyOn(sequelize, "query").mockImplementationOnce(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            const result2 = await instance.getWholeFlag(orderData.targetTenantId, testdata.line.nnumber[0]);
            expect(result2).not.toBeNull();
            expect(result2).toBeInstanceOf(Array);
            expect(result2.length).toBeGreaterThan(0);
            expect(result2[0]).toBe(true);
        });

        it("should return empty array if line does not exist", async () => {
            const result = await instance.getWholeFlag(orderData.tenantId, testdata.line.nnumber[1]);
            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(Array);
            expect(result.length).toBe(0);
        });

        it("should throw DB error if timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => await instance.getWholeFlag(orderData.targetTenantId, testdata.line.nnumber[0]))
                .rejects.toThrowError(ConnectionTimedOutError);
        });
    });

    describe("getOffice", () => {
        it("should return array of boolean if tenant exists", async () => {
            const result = await instance.getOffice(orderData.targetTenantId);
            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(Array);
            expect(result.length).toBeGreaterThan(0);
            expect(result[0]).toBe(false);

            jest.spyOn(sequelize, "query").mockImplementationOnce(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            const result2 = await instance.getOffice(orderData.targetTenantId);
            expect(result2).not.toBeNull();
            expect(result).toBeInstanceOf(Array);
            expect(result.length).toBeGreaterThan(0);
            expect(result[0]).toBe(false);
        });

        it("should return empty array if tenant does not exist", async () => {
            const result = await instance.getOffice(orderData.nonExistTenantId);
            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(Array);
            expect(result.length).toBe(0);
        });

        it("should throw DB error if timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => await instance.getOffice(orderData.targetTenantId))
                .rejects.toThrowError(ConnectionTimedOutError);
        });
    });

    describe("updateLineVoicePlan", () => {
        beforeEach(async () => {
            tx = await sequelize.transaction();
        });

        afterEach(async () => {
            await tx.rollback();
        });

        it("should return number of row updated", async () => {
            const result = await instance.updateLineVoicePlan(
                testdata.line.enabled, 'BtiredPlan', '従量プラン', new Date().getTime(), tx);

            expect(result).not.toBeNull();
            expect(result).toBe(1);
        });

        it("should throw DB error with one fail", () => {
            jest.spyOn(sequelize, "query").mockImplementationOnce(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            expect(async () => {
                await instance.updateLineVoicePlan(
                    testdata.line.enabled, 'BtiredPlan', '従量プラン', new Date().getTime(), tx);
            }).rejects.toThrowError(ConnectionTimedOutError);
        });

        it("should return 0 if line does not exist", async () => {
            const result = await instance.updateLineVoicePlan(
                testdata.line.notexist, 'BtiredPlan', '従量プラン', new Date().getTime(), tx);

            expect(result).not.toBeNull();
            expect(result).toBe(0);
        });
    });

    describe("getLinesGroupId", () => {
        it("should return group id", async () => {
            const result = await instance.getLinesGroupId(testdata.line.enabled);
            expect(result).not.toBeNull();
            expect(result).toBe(orderData.lineGroupId);

            jest.spyOn(sequelize, "query").mockImplementationOnce(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            const result2 = await instance.getLinesGroupId(testdata.line.enabled);
            expect(result2).not.toBeNull();
            expect(result2).toBe(orderData.lineGroupId);
        });

        it("should return null if line does not exist", async () => {
            const result = await instance.getLinesGroupId(testdata.line.disabled);
            expect(result).toBeNull();
        });

        it("should throw DB error if timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => await instance.getLinesGroupId(testdata.line.enabled))
                .rejects.toThrowError(ConnectionTimedOutError);
        });
    });

    describe("getLineOptionsName", () => {
        it("should return array of line option name", async () => {
            const result = await instance.getLineOptionsName(testdata.line.enabled);
            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(Array);
            expect(result.length).toBe(6);
            expect(result[0]).toBe(null);
            expect(result[1]).toBe('[docomo] 留守番電話');

            jest.spyOn(sequelize, "query").mockImplementationOnce(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            const result2 = await instance.getLineOptionsName(testdata.line.enabled);
            expect(result2).not.toBeNull();
            expect(result2).toBeInstanceOf(Array);
            expect(result2.length).toBe(6);
            expect(result[0]).toBe(null);
            expect(result2[1]).toBe('[docomo] 留守番電話');
        });

        it("should return null if line does not exist", async () => {
            const result = await instance.getLineOptionsName(testdata.line.notexist);
            expect(result).toBeNull();
        });

        it("should throw DB error if timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => await instance.getLineOptionsName(testdata.line.enabled))
                .rejects.toThrowError(ConnectionTimedOutError);
        });
    });

    describe("getTraffic", () => {
        it("should return lineTrafficsEntity if found", async () => {
            const result = await instance.getTraffic(testdata.line.traffic,
                testdata.lineTraffics.year.toString(), testdata.lineTraffics.month.toString());
            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(LineTrafficsEntity);
            expect(result.lineId).toBe(testdata.line.traffic);

            jest.spyOn(sequelize, "query").mockImplementationOnce(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            const result2 = await instance.getTraffic(testdata.line.traffic,
                testdata.lineTraffics.year.toString(), testdata.lineTraffics.month.toString());
            expect(result2).not.toBeNull();
            expect(result2).toBeInstanceOf(LineTrafficsEntity);
            expect(result2.lineId).toBe(testdata.line.traffic);
        });

        it("should return null if not found", async () => {
            const result = await instance.getTraffic(testdata.line.notexist,
                testdata.lineTraffics.year.toString(), testdata.lineTraffics.month.toString());
            expect(result).toBeNull();
        });

        it("should throw DB error if timeout", async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => await instance.getTraffic(testdata.line.traffic,
                testdata.lineTraffics.year.toString(), testdata.lineTraffics.month.toString()))
                .rejects.toThrowError(ConnectionTimedOutError);
        });
    });

    describe("insertGiftLineNotDup", () => {
        let sequelize: Sequelize;
        let transaction: Transaction;

        beforeEach(async () => {
            sequelize = await usePsql();
            transaction = await sequelize.transaction();
        });

        afterEach(async () => {
            await transaction.rollback();
        });

        describe("insertGiftLineNotDup", () => {
            it("should return true if insert successfully", async () => {
                const result = await instance.insertGiftLineNotDup("lineId", "soId", MvnoUtil.convDateFormat(systemDateTime), transaction);
                expect(result).toBe(true);
            });

            it("should return false if duplication was found", async () => {
                await instance.insertGiftLineNotDup("lineId", "soId", MvnoUtil.convDateFormat(systemDateTime), transaction);
                const result = await instance.insertGiftLineNotDup("lineId", "soId", MvnoUtil.convDateFormat(systemDateTime), transaction);
                expect(result).toBe(false);
            });

            it("should throw error if other error occurs", async () => {
                jest.spyOn(sequelize, "query").mockImplementationOnce(() => {
                    throw new Error("some other error");
                });

                await expect(instance.insertGiftLineNotDup("lineId", "soId", MvnoUtil.convDateFormat(systemDateTime), transaction))
                    .rejects.toThrow("some other error");
            });
        });
    });

    describe("getLineUsingPlan", () => {
        it("should return string if found", async () => {
            const result = await instance.getLineUsingPlan('00001963316');
            expect(result).toBe("20007");

            jest.spyOn(sequelize, "query").mockImplementationOnce(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            const result2 = await instance.getLineUsingPlan('00001963316');
            expect(result2).toBe("20007");
        });

        it("should return null if not found", async () => {
            const result = await instance.getLineUsingPlan('99999999999');
            expect(result).toBe(null);
        });

        it("should throw error if other error occurs", async () => {
            jest.spyOn(sequelize, "query").mockImplementationOnce(() => {
                throw new Error("some other error");
            });

            await expect(instance.getLineUsingPlan('00001963316'))
                .rejects.toThrow("some other error");
        });
    });

    describe("getCardCheckPlan", ()=> {
        it("should return planId purchasable with card", async () =>{
            const result = await instance.getCardCheckPlan(testdata.cardCheckPlan.exist.planId,testdata.cardCheckPlan.exist.purpose);
            expect(result).toBeDefined();
            expect(result).toBe(testdata.cardCheckPlan.exist.resalePlanId)
        })

        it("should return null if not found", async () =>{
            const result = await instance.getCardCheckPlan(testdata.cardCheckPlan.exist.planId,testdata.cardCheckPlan.notexist.purpose);
            expect(result).toBeNull();
        })

        it("should throw error if other error occurs", async () => {
            jest.spyOn(sequelize, "query").mockImplementationOnce(() => {
                throw new Error("some other error");
            });

            await expect(instance.getCardCheckPlan(testdata.cardCheckPlan.exist.planId,testdata.cardCheckPlan.exist.purpose))
                .rejects.toThrow("some other error");
        });
    })

    describe("getChangeTiming", () => {
        it("should return array of change_timing if found", async () =>{
            const result = await instance.getChangeTiming(orderData.targetTenantId,testdata.planId.exist,testdata.planId.exist);
            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(Array);
            expect(result.length).toBeGreaterThan(0);

            jest.spyOn(sequelize, "query").mockImplementationOnce(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            const result2 = await instance.getChangeTiming(orderData.targetTenantId,testdata.planId.exist,testdata.planId.exist);
            expect(result2).not.toBeNull();
            expect(result2).toBeInstanceOf(Array);
            expect(result2.length).toBeGreaterThan(0);
        })

        it("should return empty array if not found", async () =>{
            const result = await instance.getChangeTiming(orderData.nonExistTenantId,testdata.planId.notexist,testdata.planId.notexist);
            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(Array);
            expect(result.length).toBe(0);
        })

        it("should throw DB error if timeout", async () =>{
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => instance.getChangeTiming(orderData.targetTenantId,testdata.planId.exist,testdata.planId.exist))
                .rejects.toThrowError(ConnectionTimedOutError);
        })
    })
    describe("getGroupId", ()=>{
        it("should return empty array if not found", async () =>{
            const result = await instance.getGroupId(testdata.line.enabled);
            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(Array);
            expect(result.length).toBeGreaterThan(0);
            expect(result[0]).toBe(orderData.lineGroupId)

            jest.spyOn(sequelize, "query").mockImplementationOnce(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            const result2 = await instance.getGroupId(testdata.line.enabled);
            expect(result2).not.toBeNull();
            expect(result2).toBeInstanceOf(Array);
            expect(result2.length).toBeGreaterThan(0);
            expect(result2[0]).toBe(orderData.lineGroupId)
        })

        it("should return empty array if not found", async () =>{
            const result = await instance.getGroupId(testdata.line.notexist);
            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(Array);
            expect(result.length).toBe(0);
        })

        it("should throw DB error if timeout", async () =>{
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => instance.getGroupId(testdata.line.enabled))
                .rejects.toThrowError(ConnectionTimedOutError);
        })
    });

    describe("getResalePlanIdPlan", ()=>{
        it("should return string array if found", async () =>{
            const result = await instance.getResalePlanIdPlan('99038');
            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(Array);
            expect(result.length).toBeGreaterThan(0);
            expect(result[0]).toBe('BS022')

            jest.spyOn(sequelize, "query").mockImplementationOnce(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            const result2 = await instance.getResalePlanIdPlan('99038');
            expect(result2).not.toBeNull();
            expect(result2).toBeInstanceOf(Array);
            expect(result2.length).toBeGreaterThan(0);
            expect(result2[0]).toBe('BS022')
        })

        it("should return empty array if not found", async () =>{
            const result = await instance.getResalePlanIdPlan(testdata.planId.notexist);
            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(Array);
            expect(result.length).toBe(0);
        })

        it("should throw DB error if timeout", async () =>{
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => instance.getResalePlanIdPlan(testdata.planId.exist))
                .rejects.toThrowError(ConnectionTimedOutError);
        })
    });

    describe("getPlanClass", ()=>{
        it("should return string array if found", async () =>{
            const result = await instance.getPlanClass('99038');
            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(Array);
            expect(result.length).toBeGreaterThan(0);
            expect(result[0]).toBe(1)

            jest.spyOn(sequelize, "query").mockImplementationOnce(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            const result2 = await instance.getPlanClass('99038');
            expect(result2).not.toBeNull();
            expect(result2).toBeInstanceOf(Array);
            expect(result2.length).toBeGreaterThan(0);
            expect(result2[0]).toBe(1)
        })

        it("should return empty array if not found", async () =>{
            const result = await instance.getPlanClass(testdata.planId.notexist);
            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(Array);
            expect(result.length).toBe(0);
        })

        it("should throw DB error if timeout", async () =>{
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });

            await expect(async () => instance.getPlanClass('99038'))
                .rejects.toThrowError(ConnectionTimedOutError);
        })
    })
    describe("updateLineInfoPlanChange", () =>{

        it("should return 1 if updated successfully", async () => {

            const tx = await sequelize.transaction();
            const result = await instance.updateLineInfoPlanChange(
                testdata.line.enabled, 'AR999', 'test plan', new Date().getTime(), tx);

            expect(result).not.toBeNull();
            expect(result).toBe(1);

            await tx.commit();

            const response = await LinesEntity.findOne({
                where: {
                    lineId: testdata.line.enabled
                }
            });

            expect(response).not.toBeNull();
            expect(response).toBeInstanceOf(LinesEntity);
            expect(response.pricePlanName).toBe('test plan');
            expect(response.modifyFlag).toBe(true);
            expect(response.pricePlanId).toBe("AR999");
        });

        it("should throw DB error with one fail",async () => {
            jest.spyOn(sequelize, "query").mockImplementation(() => {
                throw new ConnectionTimedOutError(new Error("timeout"));
            });
            await expect(async () => {
                const tx: Transaction = await sequelize.transaction();
                return await instance.updateLineInfoPlanChange(
                    testdata.line.enabled, 'AR104', 'test', new Date().getTime(), tx);
            }).rejects.toThrowError(ConnectionTimedOutError);
        });

        it("should return 0 if line does not exist", async () => {
            tx = await sequelize.transaction();
            const result = await instance.updateLineInfoPlanChange(
                testdata.line.notexist, 'AR104', 'test', new Date().getTime(), tx);

            expect(result).not.toBeNull();
            expect(result).toBe(0);
            await tx.rollback();
        });
    })

    describe("getCapacityCode", () => {
        it("should return list of capacity code if matching potal plan id", async () => {
            const result = await instance.getCapacityCode("81015");
            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(Array);
            expect(result.length).toBe(1);      // as option_plan_id is primary key
            expect(result[0]).toContain("03001");
        });

        it("should return empty list if not found", async () => {
            const result = await instance.getCapacityCode("999999");
            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(Array);
            expect(result.length).toBe(0);
        });
    });

    describe("getTenantIdByLineIds", () => {
        it("should return tenant ids for given line ids", async () => {
            const result= await instance.getTenantIdByLineIds([testdata.line.enabled]);
            expect(result).not.toBeNull();
            expect(result).toBeInstanceOf(Array);
            expect(result[0].tenant_id).toBe(orderData.targetTenantId);
        });
    });
});
