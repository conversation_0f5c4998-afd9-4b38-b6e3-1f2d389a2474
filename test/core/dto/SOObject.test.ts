import { describe, expect, test } from "@jest/globals";
import SOObject from "@/core/dto/SOObject";

describe("core/dto/SOObject", () => {
    const instance = new SOObject();

    test("should set and get properties", () => {
        instance.setLineGroupCreateFlag(true);
        expect(instance.isLineGroupCreateFlag()).toBe(true);

        instance.setChangeOldplanId('12345');
        expect(instance.getChangeOldplanId()).toBe('12345');

        instance.setChangeNewplanId('67890');
        expect(instance.getChangeNewplanId()).toBe('67890');

        instance.setChangePlanId('12345');
        expect(instance.getChangePlanId()).toBe('12345');

        instance.setBasicCapacity('12345');
        expect(instance.getBasicCapacity()).toBe('12345');

        instance.setDestinationLineNo('12345');
        expect(instance.getDestinationLineNo()).toBe('12345');

        instance.setGiftData('12345'); 
        expect(instance.getGiftData()).toBe('12345');

        instance.setInternalFlag(true);
        expect(instance.isInternalFlag()).toBe(true);

        instance.setMnpInFlag('12345');
        expect(instance.getMnpInFlag()).toBe('12345');

        instance.setChangeOldGroupPlanId('12345');
        expect(instance.getChangeOldGroupPlanId()).toBe('12345');

        instance.setChangeNewGroupPlanId('12345');
        expect(instance.getChangeNewGroupPlanId()).toBe('12345');
    });
});
