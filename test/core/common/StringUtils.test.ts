import { describe, expect, test } from "@jest/globals";
import StringUtils from "@/core/common/StringUtils";

describe("core/common/StringUtils", () => {
    describe("isEmpty", () => {
        test("should return true for an empty string", () => {
            expect(StringUtils.isEmpty("")).toBe(true);
        });

        test("should return false for a string with only whitespace characters", () => {
            expect(StringUtils.isEmpty("   ")).toBe(false);
        });

        test("should return false for a non-empty string", () => {
            expect(StringUtils.isEmpty("Hello, world!")).toBe(false);
        });

        test("should return true for a null or undefined value", () => {
            expect(StringUtils.isEmpty(null)).toBe(true);
            expect(StringUtils.isEmpty(undefined)).toBe(true);
        });
    });

    describe("isNotEmpty", () => {
        test("should return false for an empty string", () => {
            expect(StringUtils.isNotEmpty("")).toBe(false);
        });

        test("should return true for a string with only whitespace characters", () => {
            expect(StringUtils.isNotEmpty("   ")).toBe(true);
        });

        test("should return true for a non-empty string", () => {
            expect(StringUtils.isNotEmpty("Hello, world!")).toBe(true);
        });
    });

    describe("isBlank", () => {
        test("should return true for an empty string", () => {
            expect(StringUtils.isBlank("")).toBe(true);
        });

        test("should return true for a string with only whitespace characters", () => {
            expect(StringUtils.isBlank("   ")).toBe(true);
        });

        test("should return false for a non-empty string", () => {
            expect(StringUtils.isBlank("Hello, world!")).toBe(false);
        });

        test("should return true for a null or undefined value", () => {
            expect(StringUtils.isBlank(null)).toBe(true);
            expect(StringUtils.isBlank(undefined)).toBe(true);
        });
    });

    describe("isNotBlank", () => {
        test("should return false for an empty string", () => {
            expect(StringUtils.isNotBlank("")).toBe(false);
        });

        test("should return false for a string with only whitespace characters", () => {
            expect(StringUtils.isNotBlank("   ")).toBe(false);
        });

        test("should return true for a non-empty string", () => {
            expect(StringUtils.isNotBlank("Hello, world!")).toBe(true);
        });
    });

    describe("trimToEmpty", () => {
        test("should return an empty string for a null or undefined value", () => {
            expect(StringUtils.trimToEmpty(null)).toBe("");
            expect(StringUtils.trimToEmpty(undefined)).toBe("");
        });

        test("should return the trimmed string for a non-null or undefined value", () => {
            expect(StringUtils.trimToEmpty("   Hello, world!   ")).toBe(
                "Hello, world!",
            );
        });
    });

    describe("equals", () => {
        test("should return false for a null and string value", () => {
            expect(StringUtils.equals(null, "abc")).toBe(false);
        });
        test("should return false for string value and a null", () => {
            expect(StringUtils.equals("abc", null)).toBe(false);
        });
        test("should return false for different case strings", () => {
            expect(StringUtils.equals("abc", "ABC")).toBe(false);
        });
        test("should return true for two nulls", () => {
            expect(StringUtils.equals(null, null)).toBe(true);
        });
        test("shoud return true for two equal strings", () => {
            expect(StringUtils.equals("abc", "abc")).toBe(true);
        });
        test("should return true for undefined and null", () => {
            expect(StringUtils.equals(undefined, null)).toBe(true);
        });
        test("should return true for null and undefined", () => {
            expect(StringUtils.equals(null, undefined)).toBe(true);
        });
        test("should return true for undefined and undefined", () => {
            expect(StringUtils.equals(undefined, undefined)).toBe(true);
        });
        test("should return true for two empty strings", () => {
            expect(StringUtils.equals("", "")).toBe(true);
        });
    });
});
// generate unit tests for StringUtils
