import { describe, expect, test } from "@jest/globals";
import { addMonths, format, lastDayOfMonth, parse, subDays } from "date-fns";
import Check from "@/core/common/Check";
import APISoDAO from "@/core/dao/APISoDAO";
import DefaultRequest from "../../testing/DefaultRequest";
import DefaultContext from "../../testing/DefaultContext";
import { Sequelize } from "sequelize";
import { usePsql } from "@/database/psql";
import ServiceOrdersEntity from "@/core/entity/ServiceOrdersEntity";
import { generateProcessID } from "../../testing/TestHelper";

describe("core/common/Check", () => {
    describe("checkOrderType", () => {
        test("should return '0' when reserve_date is null, reserve_flg is false, and so_id is null", () => {
            expect(Check.checkOrderType(null, false, null)).toBe("0");
        });

        test("should return '1' when reserve_date is not null, reserve_flg is false and so_id is null", () => {
            expect(Check.checkOrderType("reserve_date", false, null)).toBe("1");
        });

        test("should return '2' when reserve_date is not null, reserve_flg is true and so_id is not null", () => {
            expect(Check.checkOrderType("reserve_date", true, "so_id")).toBe(
                "2",
            );
        });

        test("should return '9' when reserve_date is null", () => {
            expect(Check.checkOrderType(null, false, "so_id")).toBe("9");
        });
        test("should return '9' when so_id is null and 予約フラグ is true", () => {
            expect(Check.checkOrderType("reserve_date", true, null)).toBe("9");
        });
    });

    describe("checkReserve", () => {
        // this function always returns true
        test("should return true when local address is localhost", () => {
            expect(Check.checkReserve("127.0.0.1", ["*********"])).toBe(true);
        });

        test("should return true when localAddr is in ap_ipAdrressList", () => {
            expect(Check.checkReserve("*********", ["*********"])).toBe(true);
        });

        test.skip("should return false when localAddr is not in ap_ipAdrressList", () => {
            expect(Check.checkReserve("*********", ["*********"])).toBe(false);
        });
    });

    describe("checkReserveDateOrderDate", () => {
        test("should return true when reserve_date is after order_date", () => {
            const order_date = "2022/01/01";
            const reserve_date = "2022/01/02";
            expect(
                Check.checkReserveDateOrderDate(order_date, reserve_date),
            ).toBe(true);
        });

        test("should return false when reserve_date is before order_date", () => {
            const order_date = "2022/01/02";
            const reserve_date = "2022/01/01";
            expect(
                Check.checkReserveDateOrderDate(order_date, reserve_date),
            ).toBe(false);
        });

        test("should return false when reserve_date is equal to order_date", () => {
            const order_date = "2022/01/01";
            const reserve_date = "2022/01/01";
            expect(
                Check.checkReserveDateOrderDate(order_date, reserve_date),
            ).toBe(false);
        });

        test("should return false when reserve_date is not a valid string", () => {
            const order_date = "2022-01-01";
            const reserve_date = "asdfasdf";
            expect(
                Check.checkReserveDateOrderDate(order_date, reserve_date),
            ).toBe(false);
        });

        test("should return false when order_date is not a valid string", () => {
            const order_date = "asdfasdf";
            const reserve_date = "2022-01-01";
            expect(
                Check.checkReserveDateOrderDate(order_date, reserve_date),
            ).toBe(false);
        });
    });

    describe("checkReservationDateExecutionUnitsFmt", () => {
        test("should return false if reservationDateExecutionUnits is null", () => {
            expect(Check.checkReservationDateExecutionUnitsFmt(null)).toBe(
                false,
            );
        });

        test("should return false if reservationDateExecutionUnits is not a number", () => {
            expect(Check.checkReservationDateExecutionUnitsFmt("ab")).toBe(
                false,
            );
        });

        test("should return false if reservationDateExecutionUnits is not a factor of 60", () => {
            expect(Check.checkReservationDateExecutionUnitsFmt("31")).toBe(
                false,
            );
        });

        test("should return true if reservationDateExecutionUnits is not 2 characters long", () => {
            expect(Check.checkReservationDateExecutionUnitsFmt("1")).toBe(true);
        });

        test("should return false if reservationDateExecutionUnits is more than 2 characters long", () => {
            expect(Check.checkReservationDateExecutionUnitsFmt("123")).toBe(
                false,
            );
        });

        test("should return true if reservationDateExecutionUnits is 0", () => {
            expect(Check.checkReservationDateExecutionUnitsFmt("00")).toBe(
                true,
            );
        });

        test("should return true if reservationDateExecutionUnits is between 0 and 60", () => {
            expect(Check.checkReservationDateExecutionUnitsFmt("30")).toBe(
                true,
            );
        });

        test("should return true if reservationDateExecutionUnits is a factor of 60", () => {
            expect(Check.checkReservationDateExecutionUnitsFmt("15")).toBe(
                true,
            );
        });
    });

    describe("checkReservationDateExecutionUnits", () => {
        // reserve_date format is YYYY-MM-DD HH:mm
        test("should return true if reservationDateExecutionUnits is 0 and reserve_date's minute is 00", () => {
            expect(
                Check.checkReservationDateExecutionUnits(
                    "0",
                    "2022/01/01 00:00",
                ),
            ).toBe(true);
        });

        test("should return true if reservationDateExecutionUnits is 60 and reserve_date's minute is 00", () => {
            expect(
                Check.checkReservationDateExecutionUnits(
                    "60",
                    "2022/01/01 00:00",
                ),
            ).toBe(true);
        });

        test("should return false if reservationDateExecutionUnits is 0 and reserve_date's minute is not 00", () => {
            expect(
                Check.checkReservationDateExecutionUnits(
                    "0",
                    "2022/01/01 00:01",
                ),
            ).toBe(false);
        });

        test("should return false if reservationDateExecutionUnits is 60 and reserve_date's minute is not 00", () => {
            expect(
                Check.checkReservationDateExecutionUnits(
                    "60",
                    "2022/01/01 00:01",
                ),
            ).toBe(false);
        });

        test("should return true if reserve_date's minute is divisible by reservationDateExecutionUnits", () => {
            expect(
                Check.checkReservationDateExecutionUnits(
                    "15",
                    "2022/01/01 00:00",
                ),
            ).toBe(true);
        });

        test("should return true if reserve_date's minute (not 00) is divisible by reservationDateExecutionUnits", () => {
            expect(
                Check.checkReservationDateExecutionUnits(
                    "15",
                    "2022/01/01 00:30",
                ),
            ).toBe(true);
        });

        test("should return false if reserve_date's minute is not divisible by reservationDateExecutionUnits", () => {
            expect(
                Check.checkReservationDateExecutionUnits(
                    "15",
                    "2022/01/01 00:41",
                ),
            ).toBe(false);
        });
    });

    describe("checkReservationsLimitDaysFmt", () => {
        test("should return false if reservationsLimitDays is null", () => {
            expect(Check.checkReservationsLimitDaysFmt(null)).toBe(false);
        });

        test("should return false if length of reservationsLimitDays is not 3", () => {
            expect(Check.checkReservationsLimitDaysFmt("1234")).toBe(false);
        });

        test("should return false if reservationsLimitDays is not a number", () => {
            expect(Check.checkReservationsLimitDaysFmt("ab")).toBe(false);
        });

        test("should return true if reservationsLimitDays is a number", () => {
            expect(Check.checkReservationsLimitDaysFmt("1")).toBe(true);
        });
    });

    describe("checkReservationsLimitDays", () => {
        const today = new Date();
        test("returns true if reserveDate is within reservationsLimitDays", () => {
            const reserveDate = new Date(
                today.getFullYear(),
                today.getMonth(),
                today.getDate() + 3,
            );
            expect(
                Check.checkReservationsLimitDays("3", reserveDate.toString()),
            ).toBe(true);
        });

        test("returns false if reserveDate is before today", () => {
            const reserveDate = new Date(
                today.getFullYear(),
                today.getMonth(),
                today.getDate() - 1,
            );
            expect(
                Check.checkReservationsLimitDays("3", reserveDate.toString()),
            ).toBe(false);
        });

        test("returns false if reserveDate is after reservationsLimitDays", () => {
            const reserveDate = new Date(
                today.getFullYear(),
                today.getMonth(),
                today.getDate() + 4,
            );
            expect(
                Check.checkReservationsLimitDays("3", reserveDate.toString()),
            ).toBe(false);
        });
    });

    describe("checkIpAddressFmt", () => {
        test("should return true if ipAddress is in valid format", () => {
            expect(Check.checkIpAddressFmt("********")).toBe(true);
        });

        test("should return false if ipAddress is not in valid format", () => {
            expect(Check.checkIpAddressFmt("10.0.1")).toBe(false);
        });

        test("should return false if octet is greater than 255", () => {
            expect(Check.checkIpAddressFmt("10.0.1.256")).toBe(false);
        });
    });

    describe("checkIpAddressPermit", () => {
        test("should return true if subnetMask is null and permitIpAddress is equal to targetIpAddress", () => {
            const result = Check.checkIpAddressPermit(
                "********",
                "********",
                null,
            );
            expect(result).toBe(true);
        });

        test("should return true if targetIpAddress is in the range of permitIpAddress", () => {
            const result = Check.checkIpAddressPermit(
                "********",
                "********",
                27,
            );
            expect(result).toBe(true);
        });

        test("should return true even if targetIpAddress is network address", () => {
            const result = Check.checkIpAddressPermit(
                "********",
                "********",
                27,
            );
            expect(result).toBe(true);
        });

        test("should return true even if targetIpAddress is broadcast address", () => {
            const result = Check.checkIpAddressPermit(
                "*********",
                "********",
                27,
            );
            expect(result).toBe(true);
        });

        test("should return false if targetIpAddress is not in the range of permitIpAddress", () => {
            const result = Check.checkIpAddressPermit(
                "*********",
                "********",
                27,
            );
            expect(result).toBe(false);
        });
    });

    describe("checkTenatIdFmt", () => {
        const OK_CASE = ["TSA000", "ZZZ000", "1CS000", "TSB001", "TENANT0012"]; // max 10 characters
        test.each(OK_CASE)(
            "should return true if tenantId is in valid format (%s)",
            (tenantId) => {
                const result = Check.checkTenatIdFmt(tenantId);
                expect(result).toBe(true);
            },
        );

        const EMPTY_CASE = [null, undefined, ""];
        test.each(EMPTY_CASE)(
            "should return false if tenantId is null, undefined, or empty string (%s)",
            (tenantId) => {
                const result = Check.checkTenatIdFmt(tenantId);
                expect(result).toBe(false);
            },
        );

        test("should return false if tenantId includes invalid characters", () => {
            const result = Check.checkTenatIdFmt("TSA00-");
            expect(result).toBe(false);
        });

        test("should return false if tenantId length is greater than 10", () => {
            const result = Check.checkTenatIdFmt("TSA12345678"); // length = 11
            expect(result).toBe(false);
        });
    });

    describe("checkLineNo", () => {
        const OK_CASE = ["08012341234", "02012341234567"];
        test.each(OK_CASE)(
            "should return true if lineNo is valid (%s)",
            (lineNo) => {
                const result = Check.checkLineNo(lineNo);
                expect(result).toBe(true);
            },
        );

        const EMPTY_CASE = [null, undefined, ""];
        test.each(EMPTY_CASE)(
            "should return false if lineNo is null, undefined, or empty string (%s)",
            (lineNo) => {
                const result = Check.checkLineNo(lineNo);
                expect(result).toBe(false);
            },
        );

        const INVALID_LENGTH = [
            "0801234123",
            "080123123456",
            "020123412345678",
        ];
        test.each(INVALID_LENGTH)(
            "should return false if lineNo length is not 11 or 14 (%s)",
            (lineNo) => {
                const result = Check.checkLineNo(lineNo);
                expect(result).toBe(false);
            },
        );

        test("should return false if lineNo is not numeric", () => {
            const result = Check.checkLineNo("0801234abcd");
            expect(result).toBe(false);
        });
    });

    describe("checkSoId", () => {
        const OK_CASE = ["123456789012345", "12345abcde12345", "12345abcde"];
        test.each(OK_CASE)(
            "should return true if service order ID format is valid (%s)",
            (soId) => {
                const result = Check.checkSoId(soId);
                expect(result).toBe(true);
            },
        );

        test("should return false if service order ID is null", () => {
            const result = Check.checkSoId(null);
            expect(result).toBe(false);
        });

        test("should return false if service order ID is undefined", () => {
            const result = Check.checkSoId(undefined);
            expect(result).toBe(false);
        });

        test("should return false if service order ID is an empty string", () => {
            const result = Check.checkSoId("");
            expect(result).toBe(false);
        });

        test("should return false if service order ID length is greater than 15", () => {
            const result = Check.checkSoId("1234567890123456");
            expect(result).toBe(false);
        });

        test("should return false if service order ID includes invalid characters", () => {
            const result = Check.checkSoId("12345-12345");
            expect(result).toBe(false);
        });
    });

    describe("checkTargetTenatIdFmt", () => {
        const OK_CASE = ["TSA000", "1CS000", "TSB001", "TSC00102"];
        test.each(OK_CASE)(
            "should return true if tenant ID format is valid (%s)",
            (tenantId) => {
                const result = Check.checkTargetTenatIdFmt(tenantId);
                expect(result).toBe(true);
            },
        );

        test("should return false if tenantId is null", () => {
            const result = Check.checkTargetTenatIdFmt(null);
            expect(result).toBe(false);
        });

        test("should return false if tenantId is undefined", () => {
            const result = Check.checkTargetTenatIdFmt(undefined);
            expect(result).toBe(false);
        });

        test("should return false if tenantId is empty string", () => {
            const result = Check.checkTargetTenatIdFmt("");
            expect(result).toBe(false);
        });

        test("should return false if tenantId includes invalid characters", () => {
            const result = Check.checkTargetTenatIdFmt("TSA00-");
            expect(result).toBe(false);
        });

        test("should return false if tenantId length is less than 6", () => {
            const result = Check.checkTargetTenatIdFmt("TSA00");
            expect(result).toBe(false);
        });

        test("should return false if tenantId length is greater than 10", () => {
            const result = Check.checkTargetTenatIdFmt("TSA12345678");
            expect(result).toBe(false);
        });
    });

    describe("checkCancelReservationDisableDaysFmt", () => {
        const OK_CASE = ["1", "10", "100"];
        test.each(OK_CASE)(
            "should return true if cancelReservationDisableDays is valid (%s)",
            (cancelReservationDisableDays) => {
                const result = Check.checkCancelReservationDisableDaysFmt(
                    cancelReservationDisableDays,
                );
                expect(result).toBe(true);
            },
        );

        test("should return false if cancelReservationDisableDays is null", () => {
            const result = Check.checkCancelReservationDisableDaysFmt(null);
            expect(result).toBe(false);
        });

        test("should return false if cancelReservationDisableDays is undefined", () => {
            const result =
                Check.checkCancelReservationDisableDaysFmt(undefined);
            expect(result).toBe(false);
        });

        test("should return false if cancelReservationDisableDays is empty string", () => {
            const result = Check.checkCancelReservationDisableDaysFmt("");
            expect(result).toBe(false);
        });

        test("should return false if cancelReservationDisableDays length is greater than 3", () => {
            const result = Check.checkCancelReservationDisableDaysFmt("1000");
            expect(result).toBe(false);
        });

        test("should return false if cancelReservationDisableDays is not a number", () => {
            const result = Check.checkCancelReservationDisableDaysFmt("abc");
            expect(result).toBe(false);
        });

        test("should return false if cancelReservationDisableDays is not half-width number", () => {
            const result = Check.checkCancelReservationDisableDaysFmt("１");
            expect(result).toBe(false);
        });
    });

    describe("checkCancelReservationDisableDays", () => {
        test("should return true if reserve date - cancel day is after order date", () => {
            const order_date = "2024/05/21 14:30:00";
            const reserve_date = "2024/05/28 10:30:00";
            const cancelReservationDisableDays = "5";
            expect(
                Check.checkCancelReservationDisableDays(
                    cancelReservationDisableDays,
                    reserve_date,
                    order_date,
                ),
            ).toBe(true);
        });

        test("should return true if reserve date - cancel day is equal order date", () => {
            const order_date = "2024/05/23 22:00:00";
            const reserve_date = "2024/05/28 10:30:00";
            const cancelReservationDisableDays = "5";
            expect(
                Check.checkCancelReservationDisableDays(
                    cancelReservationDisableDays,
                    reserve_date,
                    order_date,
                ),
            ).toBe(true);
        });

        test("should return true if cancelReservationDisableDays is 0", () => {
            const order_date = "2024/05/23 22:00:01";
            const reserve_date = "2024/05/28 10:30:00";
            const cancelReservationDisableDays = "0";
            expect(
                Check.checkCancelReservationDisableDays(
                    cancelReservationDisableDays,
                    reserve_date,
                    order_date,
                ),
            ).toBe(true);
        });

        test("should return false if reserve date - cancel day is after order date", () => {
            const order_date = "2024/05/23 22:00:01";
            const reserve_date = "2024/05/28 10:30:00";
            const cancelReservationDisableDays = "5";
            expect(
                Check.checkCancelReservationDisableDays(
                    cancelReservationDisableDays,
                    reserve_date,
                    order_date,
                ),
            ).toBe(false);
        });

        test("should return false if order date format is invalid", () => {
            const order_date = "2024-05-21 22:00:00";
            const reserve_date = "2024/05/28 10:30:00";
            const cancelReservationDisableDays = "5";
            expect(
                Check.checkCancelReservationDisableDays(
                    cancelReservationDisableDays,
                    reserve_date,
                    order_date,
                ),
            ).toBe(false);
        });

        test("should return false if reserve date format is invalid", () => {
            const order_date = "2024/05/21 22:00:00";
            const reserve_date = "2024-05-28 10:30:00";
            const cancelReservationDisableDays = "5";
            expect(
                Check.checkCancelReservationDisableDays(
                    cancelReservationDisableDays,
                    reserve_date,
                    order_date,
                ),
            ).toBe(false);
        });
    });

    describe("checkNNumberFmt", () => {
        test("should return false if nNumber length is greater than 10", () => {
            const nNumber = "adkfjakjsdfojasdkjasjdfajsdf";
            expect(Check.checkNNumberFmt(nNumber)).toBe(false);
        });

        test("should return false if nNumber length is less than 10", () => {
            const nNumber = "adkfjakj";
            expect(Check.checkNNumberFmt(nNumber)).toBe(false);
        });

        test("should return false if nNumber not begining with an N", () => {
            const nNumber = "qwertyuiop";
            expect(Check.checkNNumberFmt(nNumber)).toBe(false);
        });

        test("should return false if nNumber has no numbers after N", () => {
            const nNumber = "Nqertyuiop";
            expect(Check.checkNNumberFmt(nNumber)).toBe(false);
        });

        test("should return true if nNumber has numbers after N and length equals 10", () => {
            const nNumber = "N123456789";
            expect(Check.checkNNumberFmt(nNumber)).toBe(true);
        });
    });

    describe("checkPotalPlanID", () => {
        test("should return false if potalPlanID length is greater than 5", () => {
            const nNumber = "1234567";
            expect(Check.checkPotalPlanID(nNumber)).toBe(false);
        });

        test("should return false if potalPlanID length is less than 5", () => {
            const nNumber = "1234";
            expect(Check.checkPotalPlanID(nNumber)).toBe(false);
        });

        test("should return false if potalPlanID contains something different to numbers", () => {
            const nNumber = "12d34";
            expect(Check.checkPotalPlanID(nNumber)).toBe(false);
        });

        test("should return true if potalPlanID has length 5 and it contains only numbers", () => {
            const nNumber = "12345";
            expect(Check.checkPotalPlanID(nNumber)).toBe(true);
        });
    });

    describe("checkStrIdFmt", () => {
        test("should return false if id is null", () => {
            expect(Check.checkStrIdFmt(null, 5, true)).toBe(false);
        });

        test("should return false if id is undefined", () => {
            expect(Check.checkStrIdFmt(undefined, 5, true)).toBe(false);
        });

        test("should return false if id is empty", () => {
            expect(Check.checkStrIdFmt("", 5, true)).toBe(false);
        });

        test("should return false if id is not exactly 5 characters long", () => {
            expect(Check.checkStrIdFmt("123456", 5, true)).toBe(false);
        });

        test("should return false if id is greater than 5 characters long", () => {
            expect(Check.checkStrIdFmt("123456", 5, false)).toBe(false);
        });

        test("should return true if id is exactly 5 characters long", () => {
            expect(Check.checkStrIdFmt("12345", 5, true)).toBe(true);
        });

        test("should return true if id is less than 5 characters long", () => {
            expect(Check.checkStrIdFmt("1234", 5, false)).toBe(true);
        });

        test("should return true if id is 5 characters long", () => {
            expect(Check.checkStrIdFmt("12345", 5, false)).toBe(true);
        });
    });

    describe("checkReserveDateFmt", () => {
        test("should return false if reserveDate is null", () => {
            expect(Check.checkReserveDateFmt(null)).toBe(false);
        });

        test("should return false if reserveDate is undefined", () => {
            expect(Check.checkReserveDateFmt(undefined)).toBe(false);
        });

        test("should return false if reserveDate is empty", () => {
            expect(Check.checkReserveDateFmt("")).toBe(false);
        });

        test("should return false if reserveDate is not 16 characters long", () => {
            expect(Check.checkReserveDateFmt("yyyy/MM/dd HH:m")).toBe(false);
        });

        test("should return false if reserveDate has not valid date format (yyyy/MM/dd HH:mm)", () => {
            expect(Check.checkReserveDateFmt("2024/13/34 56:22")).toBe(false);
        });

        test("should return true if reserveDate has valid date format (yyyy/MM/dd HH:mm)", () => {
            expect(Check.checkReserveDateFmt("2024/08/21 12:22")).toBe(true);
        });
    });

    describe("checkTimeSpanHMFmt", () => {
        test("should return false if timeSpan is null", () => {
            expect(Check.checkTimeSpanHMFmt(null)).toBe(false);
        });

        test("should return false if timeSpan is undefined", () => {
            expect(Check.checkTimeSpanHMFmt(undefined)).toBe(false);
        });

        test("should return false if timeSpan is empty", () => {
            expect(Check.checkTimeSpanHMFmt("")).toBe(false);
        });

        test("should return false if timeSpan is not including a - character", () => {
            expect(Check.checkTimeSpanHMFmt("h:mmh:mm")).toBe(false);
        });

        test("should return false if startTime and stopTime do not have valid time format (HH:mm)", () => {
            expect(Check.checkTimeSpanHMFmt("25:15-03:10")).toBe(false);
            expect(Check.checkTimeSpanHMFmt("02:15-53:10")).toBe(false);
        });

        test("should return false if startTime is greater than endTime in timeSpan", () => {
            expect(Check.checkTimeSpanHMFmt("02:15-01:10")).toBe(false);
        });

        test("should return true if startTime is smaller than endTime in timeSpan", () => {
            expect(Check.checkTimeSpanHMFmt("01:15-02:10")).toBe(true);
        });
    });

    describe("checkTimeSpanOutHM", () => {
        test("should return true if startTime and endTime values are equal", () => {
            expect(Check.checkTimeSpanOutHM("", "1:15-1:15")).toBe(true);
        });

        test("should return true if timeHM is less than startTime", () => {
            expect(Check.checkTimeSpanOutHM("01:15", "3:15-4:15")).toBe(true);
        });

        test("should return true if timeHM is greater or equal than endTime", () => {
            expect(Check.checkTimeSpanOutHM("05:15", "3:15-4:15")).toBe(true);
            expect(Check.checkTimeSpanOutHM("04:15", "3:15-4:15")).toBe(true);
        });

        test("should return false if timeHM is greater than startTime and less than endTime", () => {
            expect(Check.checkTimeSpanOutHM("04:15", "3:15-5:15")).toBe(false);
        });
    });

    describe("getPreXDay", () => {
        test("should return today's date minus 6 in yyyyMMdd format", () => {
            const preXDay = 6;
            const lastdate = subDays(new Date(), preXDay);
            const targetDate = format(lastdate, "yyyyMMdd");
            expect(Check.getPreXDay(preXDay)).toEqual(targetDate);
        });
    });

    describe("checkGroupPlanIDFmt", () => {
        test("should return false if potalGroupPlanID isn't 5 chars long", () => {
            expect(Check.checkGroupPlanIDFmt("123456")).toBe(false);
            expect(Check.checkGroupPlanIDFmt("123")).toBe(false);
        });

        test("should return false if potalGroupPlanID are not numbers", () => {
            expect(Check.checkGroupPlanIDFmt("qwert")).toBe(false);
            expect(Check.checkGroupPlanIDFmt("12d45")).toBe(false);
        });

        test("should return true if potalGroupPlanID are all numbers and length is 5", () => {
            expect(Check.checkGroupPlanIDFmt("12345")).toBe(true);
        });
    });

    describe("getMonthEndDay", () => {
        // Mock the current date to 2024/06/17
        const mockDate = new Date(2024, 5, 17); // Month is 0-indexed in JS Date

        beforeEach(() => {
            jest.useFakeTimers();
            jest.setSystemTime(mockDate);
        });

        afterEach(() => {
            jest.useRealTimers();
        });

        it("should return end of current month when termValidityMonth is '0'", () => {
            const result = Check.getMonthEndDay("0");
            expect(result).toBe("240630");
        });

        it("should return end of month after specified months", () => {
            expect(Check.getMonthEndDay("1")).toBe("240731");
            expect(Check.getMonthEndDay("3")).toBe("240930");
            expect(Check.getMonthEndDay("6")).toBe("241231");
            expect(Check.getMonthEndDay("12")).toBe("250630");
            expect(Check.getMonthEndDay("-4")).toBe("240229");
        });
    });

    describe("checkLineGroupId", () => {
        test("should return false if lineGroupId is null, undefined or empty", () => {
            expect(Check.checkLineGroupId("")).toBe(false);
            expect(Check.checkLineGroupId(null)).toBe(false);
            expect(Check.checkLineGroupId(undefined)).toBe(false);
        });

        test("should return false if lineGroupId length is greater than 9", () => {
            expect(Check.checkLineGroupId("1234567890")).toBe(false);
        });

        test("should return false if lineGroupId is not a number", () => {
            expect(Check.checkLineGroupId("asdfasdf")).toBe(false);
            expect(Check.checkLineGroupId("1234t789")).toBe(false);
        });

        test("should return true if lineGroupId is a number of at most 9 digits", () => {
            expect(Check.checkLineGroupId("123456789")).toBe(true);
            expect(Check.checkLineGroupId("1234567")).toBe(true);
        });
    });

    describe("checkPlanIDFmt", () => {
        test("should return false if potalGroupPlanID is null, undefined or empty", () => {
            expect(Check.checkPlanIDFmt("")).toBe(false);
            expect(Check.checkPlanIDFmt(null)).toBe(false);
            expect(Check.checkPlanIDFmt(undefined)).toBe(false);
        });

        test("should return false if potalGroupPlanID length longer than 5", () => {
            expect(Check.checkPlanIDFmt("123456")).toBe(false);
        });

        test("should return false if potalGroupPlanID is not a number", () => {
            expect(Check.checkPlanIDFmt("asdfa")).toBe(false);
            expect(Check.checkPlanIDFmt("12t45")).toBe(false);
        });

        test("should return true if potalGroupPlanID is a 5-digit number", () => {
            expect(Check.checkPlanIDFmt("12345")).toBe(true);
        });
    });

    describe("checkTrafficOfCouponOffFlag", () => {
        test("should return false if trafficOfCouponOffFlag is not 0 or 1", () => {
            expect(Check.checkTrafficOfCouponOffFlag("2")).toBe(false);
        });

        test("should return true if trafficOfCouponOffFlag is 0 or 1", () => {
            expect(Check.checkTrafficOfCouponOffFlag("0")).toBe(true);
            expect(Check.checkTrafficOfCouponOffFlag("1")).toBe(true);
        });
    });

    describe("checkReserveOrder", ()=>{
        const aPISoDAO = new APISoDAO(DefaultRequest,DefaultContext)
        let sequelize: Sequelize = null;
        beforeAll(async ()=>{
            sequelize = await usePsql();
        })
        afterAll(async ()=>{
            await sequelize.close();
        })

        afterEach(() => {
            jest.restoreAllMocks();
            jest.clearAllMocks();
        })

        test("should return false if found", async ()=>{
            const soid = generateProcessID();
            try {
                // insert data
                await ServiceOrdersEntity.create({
                    serviceOrderId: soid,
                    tenantId: "TSA000",
                    lineId: "02006016261",
                    orderType: "プラン変更",
                    orderStatus: "予約中"
                });

                const result = await Check.checkReserveOrder("02006016261", aPISoDAO);
                expect(result).toBeDefined();
                expect(result).not.toBeNull();
                expect(result).toBe(false)
            } finally {
                await ServiceOrdersEntity.destroy({
                    where: { serviceOrderId: soid }
                });
            }
        })

        test("should return true if not found", async ()=>{
            const result = await Check.checkReserveOrder("123456778",aPISoDAO);
            expect(result).toBeDefined();
            expect(result).not.toBeNull();
            expect(result).toBe(true)
        })
    });

    describe("checkCsvResalePlanIdFmt", () => {
        test("should return false if planId neither below 5 chars long nor 半角英数字", () => {
            expect(Check.checkCsvResalePlanIdFmt("123AAB42225")).toBe(false);
            expect(Check.checkCsvResalePlanIdFmt("B2あC22e2")).toBe(false);
        });

        test("should return false if planId is empty", () => {
            expect(Check.checkCsvResalePlanIdFmt("")).toBe(false);
            expect(Check.checkCsvResalePlanIdFmt(null)).toBe(false);
            expect(Check.checkCsvResalePlanIdFmt(undefined)).toBe(false);
        });

        test("should return true if planId is  半角英数字 and below 5 in range of length", () => {
            expect(Check.checkCsvResalePlanIdFmt("B23C2")).toBe(true);
            expect(Check.checkCsvResalePlanIdFmt("E23C2")).toBe(true);
        });
    });
    
    describe("checkGiftData", () => {
        test("should return false if giftData is null", () => {
            expect(Check.checkGiftData(null, "12345678901", "12345678901")).toBe(false);
        });

        test("should return false if giftData is greater 9 characters long", () => {
            expect(Check.checkGiftData("1234567890", "12345678901", "12345678901")).toBe(false);
        });

        test("should return false if giftData is not a number", () => {
            expect(Check.checkGiftData("12345678a", "12345678901", "12345678901")).toBe(false);
        });

        test("should return true if giftData is a number and length is <= 9", () => {
            expect(Check.checkGiftData("123456789", "12345678901", "12345678901")).toBe(true);
            expect(Check.checkGiftData("12345678", "12345678901", "12345678901")).toBe(true);
        });

        test("should able to trim negative giftData if sourceLineNo or destinationLineNo is 99999999999", () => {
            expect(Check.checkGiftData("-123456789", "99999999999", "12345678901")).toBe(true);
            expect(Check.checkGiftData("-123456789", "12345678901", "99999999999")).toBe(true);
        });
    });

    describe("getEndOfMonth", () => {
        test("should return the last day of the month", () => {
            const baseTime = "2024/05/21";
            const lastDay = "2024/05/31";
            expect(Check.getEndOfMonth(baseTime)).toBe(lastDay);
        });
    });

    describe("getFirstOfMonth", () => {
        test("should return the first day of the month", () => {
            const baseTime = "2024/05/21";
            const firstDay = "2024/05/01";
            expect(Check.getFirstOfMonth(baseTime)).toBe(firstDay);
        });
    });

    describe("checkIsEndOfMonth", () => {
        test("should return true if targetTime is the last day of the month", () => {
            const baseTime = "2024/05/21";
            const targetTime = "2024/05/31";
            expect(Check.checkIsEndOfMonth(targetTime, baseTime)).toBe(true);
        });

        test("should return false if targetTime is not the last day of the month", () => {
            const baseTime = "2024/05/21";
            const targetTime = "2024/05/30";
            expect(Check.checkIsEndOfMonth(targetTime, baseTime)).toBe(false);
        });

        test("should return false if targetTime same month as baseTime", () => {
            const baseTime = "2024/05/21";
            const targetTime = "2024/06/30";
            expect(Check.checkIsEndOfMonth(targetTime, baseTime)).toBe(false);
        });
    });

    describe("checkIsFirstOfMonth", () => {
        test("should return true if targetTime is the first day of the month", () => {
            const targetTime = "2024/05/01";
            expect(Check.checkIsFirstOfMonth(targetTime)).toBe(true);
        });

        test("should return false if targetTime is not the first day of the month", () => {
            const targetTime = "2024/05/02";
            expect(Check.checkIsFirstOfMonth(targetTime)).toBe(false);
        });
    });

    describe("checkIsNegativeNum", () => {
        test("should return false if checkString is null", () => {
            expect(Check.checkIsNegativeNum(null, 5)).toBe(false);
        });

        test("should return false if checkString is not a number", () => {
            expect(Check.checkIsNegativeNum("abc", 5)).toBe(false);
        });

        test("should return false if checkString is positive", () => {
            expect(Check.checkIsNegativeNum("10", 5)).toBe(false);
            expect(Check.checkIsNegativeNum("0", 5)).toBe(false);
        });

        test("should return false if checkString is greater than maxLength", () => {
            expect(Check.checkIsNegativeNum("-123456", 5)).toBe(false);
        });

        test("should return true if checkString is less than maxLength", () => {
            expect(Check.checkIsNegativeNum("-12345", 5)).toBe(true);
            expect(Check.checkIsNegativeNum("-1234", 5)).toBe(true);
        });
    });

    describe("checkDataGiftNgTimeFormat", () => {
        test("should return false if dataGiftNgTime is null", () => {
            expect(Check.checkDataGiftNgTimeFormat(null)).toBe(false);
        });

        test("should return true if dataGiftNgTime is 'ALL'", () => {
            expect(Check.checkDataGiftNgTimeFormat("ALL")).toBe(true);
        });

        test("should return false if dataGiftNgTime does not contain a hyphen", () => {
            expect(Check.checkDataGiftNgTimeFormat("12:00")).toBe(false);
        });

        test("should return false if dataGiftNgTime has invalid start time format", () => {
            expect(Check.checkDataGiftNgTimeFormat("25:00-12:00")).toBe(false);
        });

        test("should return false if dataGiftNgTime has invalid end time format", () => {
            expect(Check.checkDataGiftNgTimeFormat("12:00-25:00")).toBe(false);
        });

        test("should return false if start time is greater than end time", () => {
            expect(Check.checkDataGiftNgTimeFormat("13:00-12:00")).toBe(false);
        });

        test("should return true if dataGiftNgTime has valid format and start time is less than end time", () => {
            expect(Check.checkDataGiftNgTimeFormat("12:00-13:00")).toBe(true);
        });
    });

    describe("checkDataGiftNgTime", () => {
        test("should return false if dataGiftNgTime is 'ALL' and dataGiftNgFlag is '1'", () => {
            const result = Check.checkDataGiftNgTime("2024/05/21 12:00:00", "ALL", "1");
            expect(result).toBe(false);
        });

        test("should return true if dataGiftNgTime is 'ALL' and dataGiftNgFlag is '0' and not first of month", () => {
            const result = Check.checkDataGiftNgTime("2024/05/21 12:00:00", "ALL", "0");
            expect(result).toBe(true);
        });

        test("should return false if dataGiftNgTime is 'ALL' and dataGiftNgFlag is '0' and first of month", () => {
            const result = Check.checkDataGiftNgTime("2024/05/01 12:00:00", "ALL", "0");
            expect(result).toBe(false);
        });

        test("should return false if dataGiftNgTime is 'ALL' and dataGiftNgFlag is Not '0'", () => {
            const result = Check.checkDataGiftNgTime("2024/05/21 12:00:00", "ALL", "1");
            expect(result).toBe(false);
        });

        test("should return true if dataGiftNgTime is not 'ALL' and dataGiftNgFlag is '0' and not first of month", () => {
            const result = Check.checkDataGiftNgTime("2024/05/21 12:00:00", "12:00-13:00", "0");
            expect(result).toBe(true);
        });

        test("should return true if not in dataGiftNgTime", () => {
            const result = Check.checkDataGiftNgTime("2024/05/01 11:00:00", "12:00-13:00", "0");
            expect(result).toBe(true);

            const result2 = Check.checkDataGiftNgTime("2024/05/21 11:00:00", "12:00-13:00", "1");
            expect(result2).toBe(true);
        });

        test("should return false if in dataGiftNgTime", () => {
            const result = Check.checkDataGiftNgTime("2024/05/01 12:30:00", "12:00-13:00", "0");
            expect(result).toBe(false);

            const result2 = Check.checkDataGiftNgTime("2024/05/21 11:00:00", "12:00-13:00", "1");
            expect(result2).toBe(true);
        });
    });

    describe("checkLineGiftTpcData", () => {
        test("should return false if basicCap is invalid", () => {
            const result = Check.checkLineGiftTpcData("invalid", "100", "0", "2024/12/31", "0", "2024/12/31");
            expect(result.getResult()).toBe(false);
            expect(result.getErrName()).toBe("基本bucket容量");
        });

        test("should return false if basicUse is invalid", () => {
            const result = Check.checkLineGiftTpcData("100", "invalid", "0", "2024/12/31", "0", "2024/12/31");
            expect(result.getResult()).toBe(false);
            expect(result.getErrName()).toBe("基本bucket使用量");
        });

        test("should return false if giftCap1 is invalid", () => {
            const result = Check.checkLineGiftTpcData("100", "100", "-invalid", "2024/12/31", "0", "2024/12/31");
            expect(result.getResult()).toBe(false);
            expect(result.getErrName()).toBe("譲渡bucket1容量");
        });

        test("should return false if giftExpDate1 is invalid", () => {
            const result = Check.checkLineGiftTpcData("100", "100", "0", "invalid", "0", "2024/12/31");
            expect(result.getResult()).toBe(false);
            expect(result.getErrName()).toBe("譲渡bucket1有効期限");
        });

        test("should return false if giftCap0 is invalid", () => {
            const result = Check.checkLineGiftTpcData("100", "100", "0", "2024/12/31", "invalid", "2024/12/31");
            expect(result.getResult()).toBe(false);
            expect(result.getErrName()).toBe("譲渡bucket0容量");
        });

        test("should return false if giftExpDate0 is invalid", () => {
            const result = Check.checkLineGiftTpcData("100", "100", "0", "2024/12/31", "0", "invalid");
            expect(result.getResult()).toBe(false);
            expect(result.getErrName()).toBe("譲渡bucket0有効期限");
        });

        test("should return true if all inputs are valid", () => {
            const result = Check.checkLineGiftTpcData("100", "100", "0", "2024/12/31", "0", "2024/12/31");
            expect(result.getResult()).toBe(true);
        });
    });

    describe("checkCouponOnOff", () => {
        test("should return true if couponOnOff is 0 or 1", () => {
            expect(Check.checkCouponOnOff("0")).toBe(true);
            expect(Check.checkCouponOnOff("1")).toBe(true);
        });

        test("should return false if couponOnOff is not 0 or 1", () => {
            expect(Check.checkCouponOnOff("2")).toBe(false);
            expect(Check.checkCouponOnOff("")).toBe(false);
            expect(Check.checkCouponOnOff("22")).toBe(false);
            expect(Check.checkCouponOnOff("a")).toBe(false);
        });
    });

    describe("checkPolicyId", () =>{
        test("should return true if policyId is valid", () => {
            expect(Check.checkPolicyId("123")).toBe(true);
            expect(Check.checkPolicyId("205")).toBe(true);
        });

        test("should return false if policyId is not valid", () => {
            expect(Check.checkPolicyId("ac")).toBe(false);
            expect(Check.checkPolicyId("")).toBe(false);
        });
    })

    describe("checkCsvCapacityCodeFmt", () => {
        it("should return true if capacity code is valid", () => {
            expect(Check.checkCsvCapacityCodeFmt("12345")).toBe(true);
            expect(Check.checkCsvCapacityCodeFmt("ABCDE")).toBe(true);
            expect(Check.checkCsvCapacityCodeFmt("12ABC")).toBe(true);
        });

        it("should return false if capacity code is null undefined or empty", async () => {
            expect(Check.checkCsvCapacityCodeFmt(null)).toBe(false);
            expect(Check.checkCsvCapacityCodeFmt(undefined)).toBe(false);
            expect(Check.checkCsvCapacityCodeFmt("")).toBe(false);
        });

        it("should return false if length is not exactly 5", async () => {
            expect(Check.checkCsvCapacityCodeFmt("1234")).toBe(false);
            expect(Check.checkCsvCapacityCodeFmt("123456")).toBe(false);
            expect(Check.checkCsvCapacityCodeFmt("ABCDEF")).toBe(false);
        });

        it("should return false if contains non-alphanumeric characters", async () => {
            expect(Check.checkCsvCapacityCodeFmt("12-45")).toBe(false);
            expect(Check.checkCsvCapacityCodeFmt("1234@")).toBe(false);
            expect(Check.checkCsvCapacityCodeFmt("1234あ")).toBe(false);
        });
    });

    describe("checkOptionPlanTBanFmt", () => {
        it("should return true if optionPlanTBan is valid", () => {
            expect(Check.checkOptionPlanTBanFmt("T6000140")).toBe(true);
            expect(Check.checkOptionPlanTBanFmt("T0123456")).toBe(true);
        });

        it("should return false if optionPlanTBan is null, undefined or empty", () => {
            expect(Check.checkOptionPlanTBanFmt(null)).toBe(false);
            expect(Check.checkOptionPlanTBanFmt(undefined)).toBe(false);
            expect(Check.checkOptionPlanTBanFmt("")).toBe(false);
        });

        it("should return false if optionPlanTBan length is not 8", () => {
            expect(Check.checkOptionPlanTBanFmt("T600014")).toBe(false);
            expect(Check.checkOptionPlanTBanFmt("T60001400")).toBe(false);
        });

        it("should return false if optionPlanTBan does not start with 'T'", () => {
            expect(Check.checkOptionPlanTBanFmt("60001400")).toBe(false);
        });

        it("should return false if optionPlanTBan contains non-numeric characters after 'T'", () => {
            expect(Check.checkOptionPlanTBanFmt("T600014A")).toBe(false);
            expect(Check.checkOptionPlanTBanFmt("T6000A140")).toBe(false);
        });
    });
});
