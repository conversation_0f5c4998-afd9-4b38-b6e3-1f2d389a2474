import MvnoOnlineUtil from "@/core/common/MvnoOnlineUtil";
import { describe, expect } from "@jest/globals";

describe("MvnoOnlineUtil", () => {
    describe("dateFormat(date, format)", () => {
        it("should return an empty string when date is null", () => {
            expect(MvnoOnlineUtil.dateFormat(null, "yyyy-MM-dd")).toBe("");
        });

        it("should format a Date object correctly", () => {
            const date = new Date(2023, 0, 1, 23, 58, 59); // January 1, 2023
            expect(MvnoOnlineUtil.dateFormat(date, "yyyy-MM-dd")).toBe("2023-01-01");
            expect(MvnoOnlineUtil.dateFormat(date, "yyyy-MM-dd HH:mm:ss")).toBe("2023-01-01 23:58:59");
            expect(MvnoOnlineUtil.dateFormat(date, "yyyy/MM/dd HH:mm:ss")).toBe("2023/01/01 23:58:59");
        });
    });

    describe("dateFormat(str, format)", () => {
        it("should return empty string if it is empty", () => {
            expect(MvnoOnlineUtil.dateFormat("", "yyyy-MM-dd")).toBe("");
        });

        it("should return the original string if it is not 8 or 14 characters long", () => {
            expect(MvnoOnlineUtil.dateFormat("202301", "yyyy-MM-dd")).toBe("202301");
        });

        it("should format a string of length 8 correctly", () => {
            expect(MvnoOnlineUtil.dateFormat("20230101", "yyyy-MM-dd HH:mm:ss")).toBe("2023-01-01 00:00:00");
        });

        it("should format a string of length 14 correctly", () => {
            expect(MvnoOnlineUtil.dateFormat("20230101123456", "yyyy-MM-dd HH:mm:ss")).toBe("2023-01-01 12:34:56");
        });

        it("should replace missing time parts with 00 for a string of length 8", () => {
            expect(MvnoOnlineUtil.dateFormat("20230101", "yyyy-MM-dd HH:mm:ss")).toBe("2023-01-01 00:00:00");
        });

        it("should return format string if it's not yyyy MM dd HH mm or ss", () => {
            expect(MvnoOnlineUtil.dateFormat("20230101", "abcde")).toBe("abcde");
        });
    });

    describe("dateFormat(input)", () => {
        it("should format a string yyyy/MM/dd:HH:mm correctly", () => {
            expect(MvnoOnlineUtil.dateFormat("2023/1/1:1:1")).toBe("2023/01/01 01:01");
            expect(MvnoOnlineUtil.dateFormat("2023/11/30:23:59")).toBe("2023/11/30 23:59");
        });

        it("should format a string yyyy/MM/dd correctly", () => {
            expect(MvnoOnlineUtil.dateFormat("2023/1/1")).toBe("2023/01/01");
            expect(MvnoOnlineUtil.dateFormat("2023/11/30")).toBe("2023/11/30");
            // expect(MvnoOnlineUtil.dateFormat("2023/11/30 23:59")).toBe("2023/11/30");        // is this expected behaviour? or should be empty string?
        });

        it("should return empty string if not matching the format", () => {
            expect(MvnoOnlineUtil.dateFormat("2023-1-1")).toBe("");
        });
    })
});