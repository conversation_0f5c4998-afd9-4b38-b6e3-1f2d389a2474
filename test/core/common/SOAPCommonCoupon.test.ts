/* tslint:disable:no-empty */
import { describe, expect, test } from "@jest/globals";
import { AxiosError } from "axios";
import config from "config";
import nock from "nock";

import SOAPException from "@/types/soapException";

import AppConfig from "@/appconfig";
import SOAPCommonCoupon from "@/core/common/SOAPCommonCoupon";
import DefaultContext from "../../testing/DefaultContext";
import DefaultRequest from "../../testing/DefaultRequest";

import Constants from "@/core/constant/Constants";
import ResultCdConstants from "@/core/constant/ResultCdConstants";
import SOAPCommonOutputDto from "@/core/dto/SOAPCommonOutputDto";
import ParameterName from "@/core/dto/ParameterName";
import * as SOAPCommonUtils from "@/core/common/SOAPCommonUtils";
import { getConfig } from "@/helpers/configHelper";

interface Spies {
    warn: jest.SpyInstance;
    error: jest.SpyInstance;
    info: jest.SpyInstance;
}

const createResponseXML = (result: "OK" | "NG") => {
    return `
    <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
        xmlns:ns1="http://ws.apache.org/axis2">
        <soapenv:Body>
            <Result>${result}</Result>
        </soapenv:Body>
    </soapenv:Envelope>
    `.trim();
};

describe("core/common/SOAPCommonCoupon", () => {
    let instance: SOAPCommonCoupon;
    const loadConfig = () => ({
        tpcLiteRequestURI: getConfig("TpcLiteRequestURI"),
        tpcDestServiceName: getConfig("TpcDestServiceName"),
        timeout: config.get<number>("mvno.TpcTimeout"),
        tpcSourceIpAddress: getConfig("TpcSourceIpAddress"),
        tpcDestIpAddress: getConfig("TpcDestIpAddress"),
        tpcDestIpAddress2: getConfig("TpcDestIpAddress2"),
        soap: config.get<string>("mvno.SOAP"),
        soapApiUrlLite: "",
        soapApiUrl: "",
        timeoutOffset: 200,
    });
    type ConfigData = ReturnType<typeof loadConfig>;
    let configData: ConfigData;
    // type of Jest spies
    const spies: Spies = {
        warn: null,
        error: null,
        info: null,
    };

    beforeAll(async () => {
        await AppConfig.loadCoreMvnoConfig(DefaultContext);
        configData = loadConfig();
        instance = new SOAPCommonCoupon(DefaultRequest, DefaultContext);
        nock.disableNetConnect();

        configData.soapApiUrlLite = configData.tpcLiteRequestURI.replace(
            "${IpAddr}",
            configData.tpcDestIpAddress,
        );
        configData.soapApiUrl = configData.tpcDestServiceName.replace(
            "${IpAddr}",
            configData.tpcDestIpAddress,
        );

        // console.warn(configData);
    });

    afterAll(() => {
        nock.abortPendingRequests();
        nock.cleanAll();
        nock.enableNetConnect();
    });

    beforeEach(() => {
        // reset nock
        nock.cleanAll();

        spies.warn = jest
            .spyOn(DefaultContext, "warn")
            .mockImplementation(() => {});
        spies.error = jest
            .spyOn(DefaultContext, "error")
            .mockImplementation(() => {});
        spies.info = jest
            .spyOn(DefaultContext, "info")
            .mockImplementation(() => {});
    });

    afterEach(() => {
        jest.restoreAllMocks();
        jest.clearAllMocks();
        spies.warn = jest
            .spyOn(DefaultContext, "warn")
            .mockImplementation(() => {});
        spies.error = jest
            .spyOn(DefaultContext, "error")
            .mockImplementation(() => {});
        spies.info = jest
            .spyOn(DefaultContext, "info")
            .mockImplementation(() => {});
    });

    const getSpyInstance = (primaryFlg: boolean) => {
        return primaryFlg ? spies.error : spies.warn;
    };

    const makeParameters = () => {
        return [ParameterName.new("msisdn", "08012345678")];
    };

    const responseHeaders: nock.ReplyHeaders = {
        "Content-Type": "text/xml",
    };

    const primaryFlagCases = [true, false];

    describe("response 200", () => {
        it("should return CODE_000000 if result code is OK", async () => {
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, createResponseXML("OK"), responseHeaders);

            const result = await Promise.allSettled([
                instance.callWebSoapApi(
                    "serviceProfileRequest",
                    Constants.PROFILEVIEW,
                    "profileName",
                    makeParameters(),
                    Constants.PROFILEVIEW,
                    "profileName",
                    makeParameters(),
                    "TST000",
                    "123456",
                    "TpcDestIpAddress",
                    true,
                ),
            ]);

            expect(result[0].status).toBe("fulfilled");
            const outputDto = (
                result[0] as PromiseFulfilledResult<SOAPCommonOutputDto>
            ).value;

            // console error is called
            expect(getSpyInstance(true)).not.toHaveBeenCalled();
            expect(getSpyInstance(false)).not.toHaveBeenCalled();
            expect(spies.info).toHaveBeenCalled(); // soapLogSend

            expect(outputDto.getProcessCode()).toBe(
                ResultCdConstants.CODE_000000,
            );
            expect(outputDto.getDoc()).not.toBeNull();
            expect(outputDto.getDoc()).not.toBeUndefined();
            expect(outputDto.isError()).toBeFalsy(); // error flag is not changed
        });

        it("should return CODE_000000 if result code is OK (tpcLite)", async () => {
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, createResponseXML("OK"), responseHeaders);

            const result = await Promise.allSettled([
                instance.callWebSoapApi(
                    "serviceProfileRequestLite",
                    Constants.PROFILEVIEW,
                    "profileName",
                    makeParameters(),
                    Constants.PROFILEVIEW,
                    "profileName",
                    makeParameters(),
                    "TST000",
                    "123456",
                    "TpcDestIpAddress",
                    true,
                ),
            ]);

            expect(result[0].status).toBe("fulfilled");
            const outputDto = (
                result[0] as PromiseFulfilledResult<SOAPCommonOutputDto>
            ).value;

            // console error is called
            expect(getSpyInstance(true)).not.toHaveBeenCalled();
            expect(getSpyInstance(false)).not.toHaveBeenCalled();
            expect(spies.info).toHaveBeenCalled(); // soapLogSend

            expect(outputDto.getProcessCode()).toBe(
                ResultCdConstants.CODE_000000,
            );
            expect(outputDto.getDoc()).not.toBeNull();
            expect(outputDto.getDoc()).not.toBeUndefined();
            expect(outputDto.isError()).toBeFalsy(); // error flag is not changed
        });

        it("should return CODE_001102 if result code is NG", async () => {
            nock(configData.soapApiUrl)
                .post("")
                .reply(200, createResponseXML("NG"), responseHeaders);

            const result = await Promise.allSettled([
                instance.callWebSoapApi(
                    "serviceProfileRequest",
                    Constants.PROFILEVIEW,
                    "profileName",
                    makeParameters(),
                    Constants.PROFILEVIEW,
                    "profileName",
                    makeParameters(),
                    "TST000",
                    "123456",
                    "TpcDestIpAddress",
                    true,
                ),
            ]);

            expect(result[0].status).toBe("fulfilled");
            const outputDto = (
                result[0] as PromiseFulfilledResult<SOAPCommonOutputDto>
            ).value;

            // console error is called
            expect(getSpyInstance(true)).not.toHaveBeenCalled();
            expect(getSpyInstance(false)).not.toHaveBeenCalled();
            expect(spies.info).toHaveBeenCalled(); // soapLogSend

            expect(outputDto.getProcessCode()).toBe(
                ResultCdConstants.CODE_001102,
            );
            expect(outputDto.getDoc()).not.toBeNull();
            expect(outputDto.getDoc()).not.toBeUndefined();
            expect(outputDto.isError()).toBeFalsy(); // error flag is not changed
        });
    });

    describe("SOAP version", () => {
        const testdata = [
            ["SOAP1.2", "application/soap+xml"],
            ["SOAP1.1", "text/xml"],
        ];

        it.each(testdata)(
            "should sent correct headers for %s",
            async (version, mediaType) => {
                nock(configData.soapApiUrl, {
                    reqheaders: {
                        "Content-Type": mediaType,
                    },
                })
                    .post("")
                    .reply(200, createResponseXML("OK"), responseHeaders);

                const soapTestInstance = new SOAPCommonCoupon(
                    DefaultRequest,
                    DefaultContext,
                );
                // inject SOAP version
                Object.defineProperty(soapTestInstance, "soap", {
                    value: version,
                });

                const result = await Promise.allSettled([
                    soapTestInstance.callWebSoapApi(
                        "serviceProfileRequest",
                        Constants.PROFILEVIEW,
                        "profileName",
                        makeParameters(),
                        Constants.PROFILEVIEW,
                        "profileName",
                        makeParameters(),
                        "TST000",
                        "123456",
                        "TpcDestIpAddress",
                        true,
                    ),
                ]);

                expect(result[0].status).toBe("fulfilled");
                const outputDto = (
                    result[0] as PromiseFulfilledResult<SOAPCommonOutputDto>
                ).value;

                // console error is called
                expect(getSpyInstance(true)).not.toHaveBeenCalled();
                expect(getSpyInstance(false)).not.toHaveBeenCalled();
                expect(spies.info).toHaveBeenCalled(); // soapLogSend

                expect(outputDto.getProcessCode()).toBe(
                    ResultCdConstants.CODE_000000,
                );
                expect(outputDto.getDoc()).not.toBeNull();
                expect(outputDto.getDoc()).not.toBeUndefined();
                expect(outputDto.isError()).toBeFalsy(); // error flag is not changed

                // check sent request headers
                expect(nock.isDone()).toBeTruthy();
            },
        );
    });

    describe("NG input or bad response", () => {
        describe.each(primaryFlagCases)(
            "primaryFlag `%s`",
            (primaryFlg: boolean) => {
                it("should return null document if profileName is not provied for ProfileView operation", async () => {
                    const result = await instance.callWebSoapApi(
                        "serviceProfileRequest",
                        Constants.PROFILEVIEW,
                        "",
                        makeParameters(),
                        Constants.PROFILEVIEW,
                        "",
                        makeParameters(),
                        "TST000",
                        "123456",
                        "TpcDestIpAddress",
                        primaryFlg,
                    );
                    // process code is not set
                    expect(result.getProcessCode()).toBeUndefined();
                    expect(result.getDoc()).toBeNull();
                    // error flag is not modified (false)
                    expect(result.isError()).toBeFalsy();
                    // console error is called
                    expect(getSpyInstance(primaryFlg)).toHaveBeenCalled();
                    expect(spies.info).not.toHaveBeenCalled(); // soapLogSend
                });

                it("should return CODE_000951 if tpcDestInfo is not a valid config key", async () => {
                    const result = await instance.callWebSoapApi(
                        "serviceProfileRequest",
                        Constants.PROFILEVIEW,
                        "profileName",
                        makeParameters(),
                        Constants.PROFILEVIEW,
                        "profileName",
                        makeParameters(),
                        "TST000",
                        "123456",
                        "invalidKey",
                        primaryFlg,
                    );
                    expect(result.getProcessCode()).toBe(
                        ResultCdConstants.CODE_000951,
                    );
                    expect(result.getDoc()).toBeNull();
                    // error flag is set
                    expect(result.isError()).toBeTruthy();
                    // console error is called
                    expect(getSpyInstance(primaryFlg)).toHaveBeenCalled();
                    expect(spies.info).not.toHaveBeenCalled(); // soapLogSend
                });

                it("should throw error if request exceeds timeout (ECONNABORTED)", async () => {
                    nock(configData.soapApiUrl)
                        .post("")
                        .delay(configData.timeout + configData.timeoutOffset)
                        .reply(200, createResponseXML("OK"));

                    const result = await Promise.allSettled([
                        instance.callWebSoapApi(
                            "serviceProfileRequest",
                            Constants.PROFILEVIEW,
                            "profileName",
                            makeParameters(),
                            Constants.PROFILEVIEW,
                            "profileName",
                            makeParameters(),
                            "TST000",
                            "123456",
                            "TpcDestIpAddress",
                            primaryFlg,
                        ),
                    ]);

                    expect(result[0].status).toBe("rejected");
                    const errResult = result[0] as PromiseRejectedResult;
                    expect(errResult.reason).toBeInstanceOf(AxiosError);
                    expect((errResult.reason as AxiosError).code).toBe(
                        AxiosError.ECONNABORTED,
                    );
                    expect(
                        SOAPException.isSOAPException(errResult.reason),
                    ).toBe(true);

                    // console error is called
                    expect(getSpyInstance(primaryFlg)).toHaveBeenCalled();
                    expect(spies.info).toHaveBeenCalled(); // soapLogSend
                });

                it("should throw error if request exceeds timeout (simulate ETIMEDOUT)", async () => {
                    nock(configData.soapApiUrl)
                        .post("")
                        .replyWithError({ code: "ETIMEDOUT" });

                    const result = await Promise.allSettled([
                        instance.callWebSoapApi(
                            "serviceProfileRequest",
                            Constants.PROFILEVIEW,
                            "profileName",
                            makeParameters(),
                            Constants.PROFILEVIEW,
                            "profileName",
                            makeParameters(),
                            "TST000",
                            "123456",
                            "TpcDestIpAddress",
                            primaryFlg,
                        ),
                    ]);

                    expect(result[0].status).toBe("rejected");
                    const errResult = result[0] as PromiseRejectedResult;
                    expect(errResult.reason).toBeInstanceOf(AxiosError);
                    expect((errResult.reason as AxiosError).code).toBe(
                        AxiosError.ETIMEDOUT,
                    );
                    expect(
                        SOAPException.isSOAPException(errResult.reason),
                    ).toBe(true);

                    // console error is called
                    expect(getSpyInstance(primaryFlg)).toHaveBeenCalled();
                    expect(spies.info).toHaveBeenCalled(); // soapLogSend
                });

                it("should throw error if server response with NG status code", async () => {
                    nock(configData.soapApiUrl).post("").reply(500);

                    const result = await Promise.allSettled([
                        instance.callWebSoapApi(
                            "serviceProfileRequest",
                            Constants.PROFILEVIEW,
                            "profileName",
                            makeParameters(),
                            Constants.PROFILEVIEW,
                            "profileName",
                            makeParameters(),
                            "TST000",
                            "123456",
                            "TpcDestIpAddress",
                            primaryFlg,
                        ),
                    ]);

                    expect(result[0].status).toBe("rejected");
                    const errResult = result[0] as PromiseRejectedResult;
                    expect(errResult.reason).toBeInstanceOf(AxiosError);
                    expect((errResult.reason as AxiosError).code).toBe(
                        AxiosError.ERR_BAD_RESPONSE,
                    );
                    expect(
                        SOAPException.isSOAPException(errResult.reason),
                    ).toBe(true);

                    // console error is called
                    expect(getSpyInstance(primaryFlg)).toHaveBeenCalled();
                    expect(spies.info).toHaveBeenCalled(); // soapLogSend
                });

                it("should return CODE_001101 if response code is not 200", async () => {
                    nock(configData.soapApiUrl)
                        .post("")
                        .reply(299, createResponseXML("NG"));

                    const result = await Promise.allSettled([
                        instance.callWebSoapApi(
                            "serviceProfileRequest",
                            Constants.PROFILEVIEW,
                            "profileName",
                            makeParameters(),
                            Constants.PROFILEVIEW,
                            "profileName",
                            makeParameters(),
                            "TST000",
                            "123456",
                            "TpcDestIpAddress",
                            primaryFlg,
                        ),
                    ]);

                    expect(result[0].status).toBe("fulfilled");
                    const outputDto = (
                        result[0] as PromiseFulfilledResult<SOAPCommonOutputDto>
                    ).value;

                    // console error is called
                    expect(getSpyInstance(primaryFlg)).toHaveBeenCalled();
                    expect(spies.info).toHaveBeenCalled(); // soapLogSend

                    expect(outputDto.getProcessCode()).toBe(
                        ResultCdConstants.CODE_001101,
                    );
                    expect(outputDto.getDoc()).not.toBeNull();
                    expect(outputDto.getDoc()).not.toBeUndefined();
                    expect(outputDto.isError()).toBeTruthy();
                });
            },
        );
    });

    describe("getNodeContent", () => {
        const xml = `
        <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
            xmlns:ns1="http://ws.apache.org/axis2">
            <soapenv:Body>
                <Result>OK</Result>
                <SomeVal>
                    <Value1>123</Value1>
                    <Value2>456</Value2>
                </SomeVal>
                <AnotherVal>
                    <Value1>789</Value1>
                </AnotherVal>
            </soapenv:Body>
        </soapenv:Envelope>`.trim();
        const doc = SOAPCommonUtils.readXML(xml);
        let soapCommon: SOAPCommonCoupon;

        beforeAll(() => {
            soapCommon = new SOAPCommonCoupon(DefaultRequest, DefaultContext);
        });

        test("should return content of the node", () => {
            const result = soapCommon.getNodeContent(doc, "//Result");
            expect(result).toBe("OK");

            const result2 = soapCommon.getNodeContent(doc, "//Value1");
            expect(result2).toBe("123");

            const result3 = soapCommon.getNodeContent(
                doc,
                "//AnotherVal/Value1",
            );
            expect(result3).toBe("789");
        });

        test("should return content of a node with multiple values", () => {
            const result = soapCommon.getNodeContent(doc, "//SomeVal");
            expect(result).toContain("123");
            expect(result).toContain("456");
            expect(result).not.toContain("789");
        });

        test("should return empty string if node not found", () => {
            const result = soapCommon.getNodeContent(doc, "//SomeNode");
            expect(result).toBe("");
        });

        test("should throw error if node is empty", () => {
            const result = () => soapCommon.getNodeContent(null, "//Value1");
            expect(result).toThrowError();
            try {
                result();
                expect(false).toBe(true);
            } catch (e) {
                expect(SOAPException.isSOAPException(e)).toBe(true);
            }
        });

        test("should return empty string if weird xpath", () => {
            expect(soapCommon.getNodeContent(doc, null)).toBe("");
            expect(soapCommon.getNodeContent(doc, "SomeVal")).toBe("");
            expect(soapCommon.getNodeContent(doc, "/Value1")).toBe("");
        });
    });

    describe("checkNodeExist", () => {
        const xml = `
        <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
            xmlns:ns1="http://ws.apache.org/axis2">
            <soapenv:Body>
                <Result>OK</Result>
                <SomeVal>
                    <Value1>123</Value1>
                    <Value2>456</Value2>
                </SomeVal>
                <AnotherVal>
                    <Value1>789</Value1>
                </AnotherVal>
            </soapenv:Body>
        </soapenv:Envelope>`.trim();
        const doc = SOAPCommonUtils.readXML(xml);
        let soapCommon: SOAPCommonCoupon;

        beforeAll(() => {
            soapCommon = new SOAPCommonCoupon(DefaultRequest, DefaultContext);
        });

        test("should return true", () => {
            expect(soapCommon.CheckNodeExist(doc, "//Result")).toBe(true);

            expect(soapCommon.CheckNodeExist(doc, "//Value1")).toBe(true);

            expect(soapCommon.CheckNodeExist(doc, "//AnotherVal/Value1")).toBe(
                true,
            );
        });

        test("should return true for having multiple contents", () => {
            expect(soapCommon.CheckNodeExist(doc, "//SomeVal")).toBe(true);
        });

        test("should return false if node not found", () => {
            expect(soapCommon.CheckNodeExist(doc, "//SomeNode")).toBe(false);
        });

        test("should throw error if node is empty", () => {
            const result = () => soapCommon.CheckNodeExist(null, "//Value1");
            expect(result).toThrowError();
            try {
                result();
                expect(false).toBe(true);
            } catch (e) {
                expect(SOAPException.isSOAPException(e)).toBe(true);
            }
        });

        test("should return false if weird xpath", () => {
            expect(soapCommon.CheckNodeExist(doc, null)).toBe(false);
            expect(soapCommon.CheckNodeExist(doc, "SomeVal")).toBe(false);
            expect(soapCommon.CheckNodeExist(doc, "/Value1")).toBe(false);
        });
    });
});
