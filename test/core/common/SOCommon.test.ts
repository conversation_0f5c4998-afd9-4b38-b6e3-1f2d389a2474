import { describe, expect, test } from "@jest/globals";
import { Sequelize } from "sequelize";
import { add, formatDate } from "date-fns";

import "@/types/string.extension";
import { describeWithDB, generateProcessID } from "../../testing/TestHelper";
import DefaultRequest from "../../testing/DefaultRequest";
import DefaultContext from "../../testing/DefaultContext";

import AppConfig from "@/appconfig";
import MvnoUtil from "@/core/common/MvnoUtil";
import SOCommon from "@/core/common/SOCommon";
import Constants from "@/core/constant/Constants";
import ResultCdConstants from "@/core/constant/ResultCdConstants";
import LineDeleteInputDto from "@/core/dto/LineDeleteInputDto";
import RequestHeader from "@/core/dto/RequestHeader";
import ResponseHeader from "@/core/dto/ResponseHeader";
import SOObject from "@/core/dto/SOObject";
import ServiceOrdersEntity from "@/core/entity/ServiceOrdersEntity";
import { usePsql } from "@/database/psql";

describeWithDB("core/common/SOCommon", () => {
    let sequelize: Sequelize;
    let instance: SOCommon;

    beforeAll(async () => {
        await AppConfig.loadCoreMvnoConfig(DefaultContext);
        sequelize = await usePsql();
        instance = new SOCommon(DefaultRequest, DefaultContext);
    });

    afterAll(async () => {
        await sequelize.close();
    });

    afterEach(async () => {
        // delete service order (if created)
        await ServiceOrdersEntity.destroy({
            where: {
                serviceOrderId: testdata.responseHeader.apiProcessID,
            },
        });
    });

    const testdata = {
        requestHeader: {
            sequenceNo: "1234567",
            senderSystemId: "0001",
            apiKey: "$02$123",
            functionType: "52",
        } as RequestHeader,

        responseHeader: {
            sequenceNo: "1234567",
            receivedDate: formatDate(new Date(), "yyyy/MM/dd HH:mm:ss"),
            processCode: ResultCdConstants.CODE_000000,
            apiProcessID: generateProcessID(),
        } as ResponseHeader,

        // reserve date string (now + 7)
        reserveDate:
            formatDate(add(new Date(), { days: 7 }), "yyyy/MM/dd") + " 02:30",
        lineNo: "08024042501",
        targetTenantId: "TST000",
    };

    const createSOObject = (
        param: LineDeleteInputDto,
        apiProcessID: string,
        executeUserId: string,
        executeTenantId: string,
        code: string,
        receivedDate: string,
        orderType: string,
        reserveDate: string,
        lineNo: string,
        mnpOutFlag: string,
    ) => {
        // based on LineDeleteService.soManagementCommon
        // TODO add other cases if needed
        const soObject = new SOObject();
        // サービスオーダID
        // 「予約実行オーダ」の場合
        if (Constants.ORDER_TYPE_2.equals(orderType)) {
            soObject.setServiceOrderId(param.reserve_soId);
        } else {
            soObject.setServiceOrderId(apiProcessID);
        }
        // 申込日
        // 「予約実行オーダ」の場合
        if (Constants.ORDER_TYPE_2.equals(orderType)) {
            soObject.setOrderDate(null);
        } else {
            soObject.setOrderDate(receivedDate);
        }
        // 予約日
        // 「予約前オーダ」の場合
        if (Constants.ORDER_TYPE_1.equals(orderType)) {
            soObject.setReserveDate(reserveDate);
        } else {
            soObject.setReserveDate(null);
        }
        // オーダ種別
        // 「即時オーダ」の場合
        if (Constants.ORDER_TYPE_0.equals(orderType)) {
            soObject.setOrderType(null);
        } else {
            soObject.setOrderType(orderType);
        }
        // 完了日
        // 「即時オーダ」又は「予約実行オーダ」の場合
        if (
            Constants.ORDER_TYPE_0.equals(orderType) ||
            Constants.ORDER_TYPE_2.equals(orderType)
        ) {
            soObject.setExecDate(MvnoUtil.getDateTimeNow());
        } else {
            soObject.setExecDate(null);
        }
        // デーダステータス
        soObject.setOrderStatus(code);
        // 回線ID
        // 「予約実行オーダの場合」
        if (Constants.ORDER_TYPE_2.equals(orderType)) {
            soObject.setLineId(null);
        } else {
            soObject.setLineId(lineNo);
        }
        // 回線グループID
        soObject.setLineGroupId(null);
        // 所属テナントID
        soObject.setTenantId(param.targetTenantId);
        // 実行ユーザID
        soObject.setExecuteUserId(executeUserId);
        // 実行テナントID
        soObject.setExecuteTenantId(executeTenantId);
        // 機能種別
        soObject.setFunctionType(param.requestHeader.functionType);
        // 操作区分
        // 「予約実行オーダ」の場合
        if (Constants.ORDER_TYPE_2.equals(orderType)) {
            soObject.setOperationDivision(null);
        } else {
            soObject.setOperationDivision("");
        }
        // 変更前プランID
        // 「予約実行オーダ」の場合
        if (Constants.ORDER_TYPE_2.equals(orderType)) {
            soObject.setChangeOldplanId(null);
        } else {
            soObject.setChangeOldplanId("");
        }
        // 変更後プランID
        // 「予約実行オーダ」の場合
        if (Constants.ORDER_TYPE_2.equals(orderType)) {
            soObject.setChangeNewplanId(null);
        } else {
            soObject.setChangeNewplanId("");
        }
        // 回線所属変更回線ID
        soObject.setChangePlanId("");
        // 基本容量
        soObject.setBasicCapacity("");
        // オプションプランID
        soObject.setOptionPlanId("");
        // MNP転出フラグ
        // 「予約実行オーダ」の場合
        if (Constants.ORDER_TYPE_2.equals(orderType)) {
            soObject.setMnpOutFlag(null);
        } else {
            soObject.setMnpOutFlag(mnpOutFlag);
        }
        // REST電文
        // 「即時オーダ」又は「予約実行オーダ」の場合
        if (
            Constants.ORDER_TYPE_0.equals(orderType) ||
            Constants.ORDER_TYPE_2.equals(orderType)
        ) {
            soObject.setRestMessage(null);
        } else {
            soObject.setRestMessage(MvnoUtil.getRestMessage(param));
        }
        return soObject;
    };

    describe("soCommon", () => {
        test("should create new SOObject", async () => {
            const soObject = createSOObject(
                {
                    targetSoId: "PF0123456789abcdef123456789",
                    requestHeader: testdata.requestHeader,
                    tenantId: "OPF000",
                    targetTenantId: testdata.targetTenantId,
                    lineNo: testdata.lineNo,
                    mnpOutFlag: "0",
                    csvUnnecessaryFlag: "0",
                    reserve_date: testdata.reserveDate,
                    reserve_flag: null,
                    reserve_soId: null,
                },
                testdata.responseHeader.apiProcessID,
                null,
                null,
                ResultCdConstants.CODE_000000,
                testdata.responseHeader.receivedDate,
                Constants.ORDER_TYPE_1,
                testdata.reserveDate,
                testdata.lineNo,
                "0",
            );

            await instance.soCommon(soObject);

            const found = await ServiceOrdersEntity.findOne({
                where: {
                    serviceOrderId: testdata.responseHeader.apiProcessID,
                },
            });

            expect(found).not.toBeNull();
            expect(found.lineId).toEqual(testdata.lineNo);
            expect(found.orderStatus).toEqual("予約中");
            expect(found.tenantId).toEqual(testdata.targetTenantId);
        });

        test("should update order status", async () => {
            // insert base data first
            const soObject = createSOObject(
                {
                    targetSoId: "PF0123456789abcdef123456789",
                    requestHeader: testdata.requestHeader,
                    tenantId: "OPF000",
                    targetTenantId: testdata.targetTenantId,
                    lineNo: testdata.lineNo,
                    mnpOutFlag: "0",
                    csvUnnecessaryFlag: "0",
                    reserve_date: testdata.reserveDate,
                    reserve_flag: null,
                    reserve_soId: null,
                },
                testdata.responseHeader.apiProcessID,
                null,
                null,
                ResultCdConstants.CODE_000000,
                testdata.responseHeader.receivedDate,
                Constants.ORDER_TYPE_1,
                testdata.reserveDate,
                testdata.lineNo,
                "0",
            );

            await instance.soCommon(soObject);

            const found = await ServiceOrdersEntity.findOne({
                where: {
                    serviceOrderId: testdata.responseHeader.apiProcessID,
                },
            });

            expect(found).not.toBeNull();

            // second update
            const soObject2 = createSOObject(
                {
                    targetSoId: "PF0123456789abcdef123456789",
                    requestHeader: testdata.requestHeader,
                    tenantId: "OPF000",
                    targetTenantId: testdata.targetTenantId,
                    lineNo: testdata.lineNo,
                    mnpOutFlag: "0",
                    csvUnnecessaryFlag: "0",
                    reserve_date: testdata.reserveDate,
                    reserve_flag: null,
                    reserve_soId: testdata.responseHeader.apiProcessID,
                },
                testdata.responseHeader.apiProcessID,
                null,
                null,
                ResultCdConstants.CODE_000921,
                testdata.responseHeader.receivedDate,
                Constants.ORDER_TYPE_2,
                testdata.reserveDate,
                testdata.lineNo,
                "0",
            );

            await instance.soCommon(soObject2);

            const found2 = await ServiceOrdersEntity.findOne({
                where: {
                    serviceOrderId: testdata.responseHeader.apiProcessID,
                },
            });
            expect(found2).not.toBeNull();
            // change from 予約中 to 失敗
            expect(found2.orderStatus).toEqual("失敗");
        });
    });
});
