import { describe, expect, test } from "@jest/globals";
import { HttpRequest } from "@azure/functions";
import CheckUtil from "@/core/common/MvnoUtil";
import DefaultRequest from "../../testing/DefaultRequest";

describe("core/common/MvnoUtil", () => {
    describe("getDateTimeNow", () => {
        test("should return the current date and time and returned in correct format", () => {
            const date = CheckUtil.getDateTimeNow();
            expect(date).toMatch(/^\d{4}\/\d{2}\/\d{2} \d{2}:\d{2}:\d{2}$/);
        });
    });

    describe("getDateTime", () => {
        test("should return the current date and time and returned in correct format", () => {
            const date = CheckUtil.getDateTime();
            expect(date).toMatch(
                /^\d{4}\/\d{2}\/\d{2} \d{2}:\d{2}:\d{2}\.\d{3}$/,
            );
        });
    });

    describe("getOutputList", () => {
        test("should return a comma-separated string of the list elements", () => {
            const list = ["a", "b", "c"];
            const result = CheckUtil.getOutputList(list);
            expect(result).toBe("a, b, c");
        });

        test("should return an empty string for an empty list", () => {
            const list: string[] = [];
            const result = CheckUtil.getOutputList(list);
            expect(result).toBe("");
        });
    });

    describe("addSpaceAfterStr", () => {
        test("should return string as it is", () => {
            const result = CheckUtil.addSpaceAfterStr("Hello", 5);
            expect(result).toBe("Hello");
        });

        test("should return empty string if input is null or undefined", () => {
            const result = CheckUtil.addSpaceAfterStr(null, 5);
            expect(result).toBe("");

            const result2 = CheckUtil.addSpaceAfterStr(undefined, 5);
            expect(result2).toBe("");
        });

        test("should return stringified input", () => {
            const result = CheckUtil.addSpaceAfterStr(123, 5);
            expect(result).toBe("123");
        });
    });

    describe("convDateFormat", () => {
        test("should return date as unix timestamp in milliseconds", () => {
            // NOTE date is in local timezone (JST)
            const result = CheckUtil.convDateFormat("2024/04/17 09:28:57");
            expect(result).toBe(1713313737000);
        });
    });

    describe("getRestMessage", () => {
        test("should return stringified JSON without excluded fields", () => {
            const obj = {
                test: 123,
                internal_flag: "1",
                reserve_soId: "so_id",
                name: "test",
            };
            expect(CheckUtil.getRestMessage(obj)).toBe(
                '{"test":123,"name":"test"}',
            );
        });
    });

    describe("addZeroBeforeStr", () => {
        test("should return string as it is if it is null or undefined", () => {
            expect(CheckUtil.addZeroBeforeStr(null, 5)).toBe(null);
            expect(CheckUtil.addZeroBeforeStr(undefined, 5)).toBe(undefined);
        });

        test("should return string with zeros added before it", () => {
            expect(CheckUtil.addZeroBeforeStr("123", 5)).toBe("00123");
            expect(CheckUtil.addZeroBeforeStr("12345", 5)).toBe("12345");
            expect(CheckUtil.addZeroBeforeStr("1234567", 5)).toBe("1234567");
        });
    });

    describe("getLocalIpList", () => {
        test("should return an array of local IP addresses", () => {
            const result = CheckUtil.getLocalIpList();
            expect(result).toBeInstanceOf(Array);
        });
        // TODO add more test cases when function is implemented
    });

    describe("getClientIPAddress", () => {
        test("should return an IP addresses", () => {
            const headers = new Headers({
                "x-forwarded-for": "***********:4567, ***********"
            })
            const request = {
                ...DefaultRequest,
                headers,
            } as HttpRequest;
            const ipAddress = CheckUtil.getClientIPAddress(request);
            const regex = /^([0-9]{1,3}\.){3}[0-9]{1,3}$/;
            expect(regex.test(ipAddress)).toBeTruthy();
        });
        // TODO add more test cases when function is implemented
    });

    describe("getLogApiIdentId", () => {
        test("should return a string with correct format", () => {
            const result = CheckUtil.getLogApiIdentId();
            // format is yyyy/MM/dd HH:mm:ss.SSS XXXXXXXXXX
            const regex =
                /^\d{4}\/\d{2}\/\d{2} \d{2}:\d{2}:\d{2}\.\d{3} \d{10}$/;
            expect(regex.test(result)).toBeTruthy();
        });
    });

    describe("getTrafficPreviousMonth", () => {
        test("normal cases", () => {
            jest.useFakeTimers()
                .setSystemTime(new Date('2024-08-20'));
            const result = CheckUtil.getTrafficPreviousMonth(-1);
            expect(result).toEqual(["2024", "7"]);
        });

        test("should return [2024, 1] for 2024/03/31, -2", () => {
            jest.useFakeTimers()
                .setSystemTime(new Date('2024-03-31'));
            const result = CheckUtil.getTrafficPreviousMonth(-2);
            expect(result).toEqual(["2024", "1"]);
        });

        test("should return [2024, 2] for 2024/03/31, -1", () => {
            jest.useFakeTimers()
                .setSystemTime(new Date('2024-03-31'));
            const result = CheckUtil.getTrafficPreviousMonth(-1);
            expect(result).toEqual(["2024", "2"]);
        });

        test("should return [2023, 12] for 2024/01, -1", () => {
            jest.useFakeTimers()
                .setSystemTime(new Date('2024-01-01'));
            const result = CheckUtil.getTrafficPreviousMonth(-1);
            expect(result).toEqual(["2023", "12"]);
        });
    });

    describe("checkDateFmt", () => {
        test("should return true for valid date format", () => {
            expect(CheckUtil.checkDateFmt("2024/08/20")).toBe(true);
            expect(CheckUtil.checkDateFmt("2024/02/29")).toBe(true);
        });

        test("should return false for invalid date format", () => {
            expect(CheckUtil.checkDateFmt("2024-08/20")).toBe(false);
            expect(CheckUtil.checkDateFmt("2024-08-20")).toBe(false);
            expect(CheckUtil.checkDateFmt("2024/08/20 02:08:00")).toBe(false);
            expect(CheckUtil.checkDateFmt("2024/08/    03:02:08")).toBe(false);
        });

        test("should return false for non-exist date", () => {
            expect(CheckUtil.checkDateFmt("2023/02/29")).toBe(false);
            expect(CheckUtil.checkDateFmt("2024/08/32")).toBe(false);
            expect(CheckUtil.checkDateFmt("2024/13/01")).toBe(false);
            expect(CheckUtil.checkDateFmt("2024/O8/20")).toBe(false); // O is not 0
        });
    });

    describe("checkIsDataComparison", () => {
        test("should return true for first date earlier or same as second date", () => {
            expect(CheckUtil.checkIsDataComparison("2024/08/20", "2024/08/20")).toBe(true);
            expect(CheckUtil.checkIsDataComparison("2024/08/20", "2024/08/24")).toBe(true);
        });

        test("should return false for first date later than second date", () => {
            expect(CheckUtil.checkIsDataComparison("2024/08/20", "2024/02/02")).toBe(false);
            expect(CheckUtil.checkIsDataComparison("2024/08/20", "2024/01/03")).toBe(false);
        });

        test("should return false for invalid date format", () => {
            expect(CheckUtil.checkIsDataComparison("2024-08/20", "2024/08/20")).toBe(false);
            expect(CheckUtil.checkIsDataComparison("2024/08/20", "2024/08/20 02:08:00")).toBe(false);
            expect(CheckUtil.checkIsDataComparison("2024/08/20", "2024/08/    03:02:08")).toBe(false);
            expect(CheckUtil.checkIsDataComparison("2024/08/20", "2024/08/32")).toBe(false);
            expect(CheckUtil.checkIsDataComparison("2024/08/20", "2024/O8/20")).toBe(false);        // O is not 0
        });
    });
    
    describe("getRecent30Day", () => {
        test("should return a date 30 days ago", () => {
            jest.useFakeTimers()
                .setSystemTime(new Date('2024-08-20'));
            const result = CheckUtil.getRecent30Day("30");
            expect(result).toBe("2024/07/21");

            jest.useFakeTimers()
                .setSystemTime(new Date('2024-03-01'));
            const result2 = CheckUtil.getRecent30Day("30");
            expect(result2).toBe("2024/01/31");
        });
    });

    describe("chkLenRange", () => {
        test("should return true if string length is within the range", () => {
            expect(CheckUtil.chkLenRange("123456", 6, 10)).toBe(true);
            expect(CheckUtil.chkLenRange("12345", 5, 5)).toBe(true);
        })
        test("should return false if string length is out of the range", () => {
            expect(CheckUtil.chkLenRange("12345", 1, 4)).toBe(false);
            expect(CheckUtil.chkLenRange("12345", 6, 10)).toBe(false);
        })
    });
    describe("chkStrType1", () => {
        test("should return true if string matches「a-zA-Z0-9」regex", () => {
            expect(CheckUtil.chkStrType1("odiowqdowiqdownw")).toBe(true);
            expect(CheckUtil.chkStrType1("dnwjqdnqjdnwjdnndlmsa3432432432434")).toBe(true);
        })
        test("should return false if string does not match「a-zA-Z0-9」regex", () => {
            expect(CheckUtil.chkStrType1("_+321_@")).toBe(false);
            expect(CheckUtil.chkStrType1("|+*^¥>")).toBe(false);
        })
    });
    describe("propertiesCheck", () => {
        test("should return true if string matches「a-zA-Z0-9」regex", () => {
            expect(CheckUtil.propertiesCheck("123456",["ABCDEFGHIJKLMNOPQRSTUVWXYZ","abcdefghijklmnopqrstuvwxyz","0123456789"])).toBe(true);
            expect(CheckUtil.propertiesCheck("djasdiojadaoda",["ABCDEFGHIJKLMNOPQRSTUVWXYZ","abcdefghijklmnopqrstuvwxyz","0123456789"])).toBe(true);
        })

        test("should return false if string does not match「a-zA-Z0-9」regex", () => {
            expect(CheckUtil.propertiesCheck("+*^¥>",["ABCDEFGHIJKLMNOPQRSTUVWXYZ","abcdefghijklmnopqrstuvwxyz","0123456789"])).toBe(false);
            expect(CheckUtil.propertiesCheck("+321_",["ABCDEFGHIJKLMNOPQRSTUVWXYZ","abcdefghijklmnopqrstuvwxyz","0123456789"])).toBe(false);
        })
    });
    
    // TODO add more unit tests
});
