import { useRedisMock } from "../../testing/RedisMock";
jest.mock("../../../src/database/redis", () => ({
    ...jest.requireActual("../../../src/database/redis"),
    useRedis: jest.fn().mockImplementation(useRedisMock),
}));

import { describe, expect, test } from "@jest/globals";
import { ConnectionTimedOutError, QueryTypes, Sequelize } from "sequelize";

import DefaultContext, {
    getNextInvocationId,
} from "../../testing/DefaultContext";

import DefaultRequest from "../../testing/DefaultRequest";
import { describeWithDB, generateProcessID, generateSOID } from "../../testing/TestHelper";
// import Check from "@/core/common/Check";
import AppConfig from "@/appconfig";
import CheckUtil from "@/core/common/CheckUtil";
import APICommonDAO from "@/core/dao/APICommonDAO";
import RESTCommon from "@/core/common/RESTCommon";
import Constants from "@/core/constant/Constants";
import ResultCdConstants from "@/core/constant/ResultCdConstants";
import IpaddressEntity from "@/core/entity/IpaddressEntity";
import { TenantsEntity } from "@/core/entity/TenantsEntity";
import { usePsql } from "@/database/psql";
import { LinesDelHandler } from "@/functions/LinesDelHandler";
import { cosmosDB } from "@azure/functions/types/app";
import { getConfigAsNumber } from "@/helpers/configHelper";

describeWithDB("core/common/RESTCommon", () => {
    const request = DefaultRequest;
    const context = DefaultContext;
    let instance: RESTCommon;
    let sequelize: Sequelize;

    beforeAll(async () => {
        sequelize = await usePsql();
        await AppConfig.loadCoreMvnoConfig(DefaultContext);
        instance = new RESTCommon(request, context);
    });

    beforeEach(() => {
        // set context.invocationId to random value
        context.invocationId = getNextInvocationId();
    });

    afterAll(async () => {
        await sequelize.close();
        jest.restoreAllMocks();
    });

    describe("checkSourceIpAddress", () => {
        afterEach(async () => {
            // assumming TSA tenant does not have ip address
            await IpaddressEntity.destroy({
                where: {
                    tenantId: "TSA000",
                },
            });
        });

        const registerIpAddress = async (
            ipAddresses: string[],
            mask: number,
            tenant: string,
        ) => {
            const ipAddressList = ipAddresses.map((ip) => ({
                ipAddress: ip,
                subnetMask: mask,
                tenantId: tenant,
            }));
            await IpaddressEntity.bulkCreate(ipAddressList);
        };

        test("should return CODE_000000 when the IP address is valid", async () => {
            await registerIpAddress(
                ["********", "********"],
                Constants.SUBNET_MASK_MIN,
                "TSA000",
            );
            const result = await instance.checkSourceIpAddress(
                "TSA000",
                "********",
                "123456",
            );
            expect(result).toBe(ResultCdConstants.CODE_000000);
        });

        test("should return CODE_000000 when tenant ip address is empty", async () => {
            const result = await instance.checkSourceIpAddress(
                "TSA000",
                "********",
                "123456",
            );
            expect(result).toBe(ResultCdConstants.CODE_000000);
        });

        test("should return CODE_000000 when ip address is in allowed list", async () => {
            await registerIpAddress(["********", "********"], 27, "TSA000");
            const result1 = await instance.checkSourceIpAddress(
                "TSA000",
                "*************",
                "123456",
            );
            expect(result1).toBe(ResultCdConstants.CODE_000000);
            const result2 = await instance.checkSourceIpAddress(
                "TSA000",
                "*************",
                "123456",
            );
            expect(result2).toBe(ResultCdConstants.CODE_000000);
        });

        test("should return CODE_000000 if any of tenant ip address is invalid (post-refactoring requirement)", async () => {
            // full-width character in the second ip address
            await registerIpAddress(["********", "10.0.2.１"], 27, "TSA000");
            const result = await instance.checkSourceIpAddress(
                "TSA000",
                "*************",
                "123456",
            );
            expect(result).toBe(ResultCdConstants.CODE_000000);
        });

        test.skip("should return CODE_000946 if any of tenant ip address is invalid", async () => {
            // full-width character in the second ip address
            await registerIpAddress(["********", "10.0.2.１"], 27, "TSA000");
            const result = await instance.checkSourceIpAddress(
                "TSA000",
                "*************",
                "123456",
            );
            expect(result).toBe(ResultCdConstants.CODE_000946);
        });

        test.skip("should return CODE_000946 if subnet mask of tenant ip address is outside allowed range (min)", async () => {
            await registerIpAddress(
                ["********", "********"],
                Constants.SUBNET_MASK_MIN - 1,
                "TSA000",
            );
            const result = await instance.checkSourceIpAddress(
                "TSA000",
                "*************",
                "123456",
            );
            expect(result).toBe(ResultCdConstants.CODE_000946);
        });

        test.skip("should return CODE_000946 if subnet mask of tenant ip address is outside allowed range (max)", async () => {
            await registerIpAddress(
                ["********", "********"],
                Constants.SUBNET_MASK_MAX + 1,
                "TSA000",
            );
            const result = await instance.checkSourceIpAddress(
                "TSA000",
                "*************",
                "123456",
            );
            expect(result).toBe(ResultCdConstants.CODE_000946);
        });

        test.skip("should return CODE_000947 if allowed source ip address is invalid", async () => {
            await registerIpAddress(["********", "10.0.2.１"], 27, "TSA000");
            // TODO stub config.get or modify its value during test
            // const originalGet = config.get;
            // sandbox
            //     .stub(config, "get")
            //     .withArgs("mvno.AllowedSourceIpAddress")
            //     .returns("*************,************１");
            const result = await instance.checkSourceIpAddress(
                "TSA000",
                "*************",
                "123456",
            );
            expect(result).toBe(ResultCdConstants.CODE_000947);
        });

        test.skip("should return CODE_000948 if ip address does not match any tenant ip address or allowed source ip address", async () => {
            await registerIpAddress(["********", "********"], 27, "TSA000");
            const result = await instance.checkSourceIpAddress(
                "TSA000",
                "**************",
                "123456",
            );
            expect(result).toBe(ResultCdConstants.CODE_000948);
        });

        test.skip("should return CODE_000947 when allowedSourceIpAddress is null (checkIsNotNull returns false)", async () => {
            jest.spyOn(CheckUtil, "checkIsNotNull").mockImplementationOnce(
                (checkString: string): boolean => {
                    return true;
                },
            );

            await registerIpAddress(["********", "********"], 27, "TSA000");
            const result = await instance.checkSourceIpAddress(
                "TSA000",
                "**************",
                "123456",
            );
            expect(result).toBe(ResultCdConstants.CODE_000947);
        });

        test.skip("should return CODE_000947 when allowedIpAddrList length is larger than Constants.ALLOWED_IP_MAX_CNT", async () => {
            const originalValue = Constants.ALLOWED_IP_MAX_CNT;
            Object.defineProperty(Constants, "ALLOWED_IP_MAX_CNT", {
                value: 0,
                writable: true,
            });

            await registerIpAddress(["********", "********"], 27, "TSA000");
            const result = await instance.checkSourceIpAddress(
                "TSA000",
                "**************",
                "123456",
            );
            expect(result).toBe(ResultCdConstants.CODE_000947);

            Object.defineProperty(Constants, "ALLOWED_IP_MAX_CNT", {
                value: originalValue,
                writable: false,
            });
        });
    });

    describe("checkRestCommon", () => {
        const sequenceHash = "$02$a9f6ddd4144344edfdd5a50efb814119"; // sequence 1234567
        let tenantPwd: string = "";
        const data = {
            sequenceNo: "1234567",
            senderSystemId: "0001",
            apiKey: "",
            tenantId: "OPF000",
            functionType: LinesDelHandler.Meta.localFunctionType, // LINE_DEL
            localFunctionType: LinesDelHandler.Meta.localFunctionType,
            url: LinesDelHandler.Meta.url,
            orderType: Constants.ORDER_TYPE_1,
            reserve_soId: undefined,
            clientIPAddress: "********",
        };

        beforeAll(async () => {
            // get tenant key
            const tenant = await TenantsEntity.findOne({
                where: {
                    tenantId: data.tenantId,
                },
            });
            if (tenant) {
                data.apiKey = tenant.hashedPassword + sequenceHash;
                tenantPwd = tenant.hashedPassword;
            }
        });

        afterEach(async () => {
            // reset connection count based on latest invocationId
            instance.modConCount(false);
            instance.decTenantConnectCount(
                data.tenantId,
                ResultCdConstants.CODE_000000,
            );
        });

        // helper function to set connection count to max
        const setConnectionCount = async (maxConnection: number) => {
            const invocationList: string[] = [];
            for (let i = 0; i < maxConnection; i++) {
                invocationList.push(context.invocationId);
                await instance.modConCount(true);
                context.invocationId = getNextInvocationId();
            }
            expect(invocationList.length).toBe(maxConnection);
            const con = await instance.getConCount();
            expect(con).toBe(maxConnection);
            return invocationList;
        };

        // helper function to set tenant connection count to max
        const setTenantConnectionCount = async (
            tenantId: string,
            maxConnection: number,
        ) => {
            const invocationList: string[] = [];
            const now = Math.floor(Date.now() / 1000);
            const redisClient = await useRedisMock(context);
            for (let i = 0; i < maxConnection; i++) {
                invocationList.push(context.invocationId);
                // add to Redis directly since tenantConCountHelper is private
                await redisClient.zadd(
                    `conCount:${tenantId}`,
                    now,
                    context.invocationId,
                );
                // expires in 1 minute
                await redisClient.expire(`conCount:${tenantId}`, 60);
                context.invocationId = getNextInvocationId();
            }
            expect(
                await redisClient.zcount(`conCount:${tenantId}`, now, "+inf"),
            ).toBe(maxConnection); // should be maximum connection
            return invocationList;
        };

        // Since checkHeaderFormat is a private method, we must check via checkRestCommon
        test("should return code CODE_000921 when senderSystemId is less than 4 digits", async () => {
            const response = await instance.checkRestCommon(
                data.sequenceNo,
                "123",
                data.apiKey,
                data.functionType,
                data.tenantId,
                data.localFunctionType,
                data.url,
                data.orderType,
                data.reserve_soId,
                data.clientIPAddress,
            );

            expect(response.checkResult).toEqual(false);
            expect(response.processingCode).toEqual(
                ResultCdConstants.CODE_000921,
            );
            expect(typeof response.others).toBe("string");
        });

        // Since checkHeaderFormat is a private method, we must check via checkRestCommon
        test("should return code CODE_000921 when tenantId is null, empty, undefined and orderType is 2", async () => {
            for (const tenantId of [null, "", undefined]) {
                const response = await instance.checkRestCommon(
                    data.sequenceNo,
                    data.senderSystemId,
                    data.apiKey,
                    data.functionType,
                    tenantId,
                    data.localFunctionType,
                    data.url,
                    Constants.ORDER_TYPE_2,
                    data.reserve_soId,
                    data.clientIPAddress,
                );

                expect(response.checkResult).toEqual(false);
                expect(response.processingCode).toEqual(
                    ResultCdConstants.CODE_000921,
                );
                expect(typeof response.others).toBe("undefined");
            }
        });

        // Since checkHeaderFormat is a private method, we must check via checkRestCommon
        test("should return code CODE_000921 when tenantId is not alphanumeric string and orderType is 2", async () => {
            const response = await instance.checkRestCommon(
                data.sequenceNo,
                data.senderSystemId,
                data.apiKey,
                data.functionType,
                "$%全角()",
                data.localFunctionType,
                data.url,
                Constants.ORDER_TYPE_2,
                data.reserve_soId,
                data.clientIPAddress,
            );

            expect(response.checkResult).toEqual(false);
            expect(response.processingCode).toEqual(
                ResultCdConstants.CODE_000921,
            );
            expect(typeof response.others).toBe("undefined");
        });

        // Since checkAuthentication is a private method, we must check via checkRestCommon
        test("should return code CODE_000941 when forcing tenantsEntity to be null", async () => {
            jest.spyOn(
                APICommonDAO.prototype,
                "getTenants",
            ).mockImplementationOnce(
                (tenantId: string): Promise<TenantsEntity> => {
                    return Promise.resolve(null);
                },
            );

            const response = await instance.checkRestCommon(
                data.sequenceNo,
                data.senderSystemId,
                data.apiKey,
                data.functionType,
                data.tenantId,
                data.localFunctionType,
                data.url,
                data.orderType,
                data.reserve_soId,
                data.clientIPAddress,
            );

            expect(response.checkResult).toEqual(false);
            expect(response.processingCode).toEqual(
                ResultCdConstants.CODE_000941,
            );
            expect(typeof response.others).toBe("string");
        });

        test("should return CODE_000000 when all parameters are valid", async () => {
            const result = await instance.checkRestCommon(
                data.sequenceNo,
                data.senderSystemId,
                data.apiKey,
                data.functionType,
                data.tenantId,
                data.localFunctionType,
                data.url,
                data.orderType,
                data.reserve_soId,
                data.clientIPAddress,
            );
            expect(result.checkResult).toBe(true);
            expect(result.processingCode).toBe(ResultCdConstants.CODE_000000);
        });

        test("should return CODE_000931 if connection limit is exceeded", async () => {
            let invocationList: string[] = [];
            try {
                const maxConnection = getConfigAsNumber(
                    "ServerConnectionLimit",
                );
                // insert dummy data to simulate max connection limit
                invocationList = await setConnectionCount(maxConnection);

                // run instance again with new invocationId
                const result = await instance.checkRestCommon(
                    data.sequenceNo,
                    data.senderSystemId,
                    data.apiKey,
                    data.functionType,
                    data.tenantId,
                    data.localFunctionType,
                    data.url,
                    data.orderType,
                    data.reserve_soId,
                    data.clientIPAddress,
                );
                expect(result.checkResult).toBe(false);
                expect(result.processingCode).toBe(
                    ResultCdConstants.CODE_000931,
                );
            } finally {
                // clean up dummy connections count before next test
                if (invocationList.length > 0) {
                    const redisClient = await useRedisMock(context);
                    redisClient.zrem("conCount", ...invocationList);
                }
            }
        });

        test("should return CODE_000934 if tenant cannot be found", async () => {
            const result = await instance.checkRestCommon(
                data.sequenceNo,
                data.senderSystemId,
                data.apiKey,
                data.functionType,
                "UNKNOWN",
                data.localFunctionType,
                data.url,
                data.orderType,
                data.reserve_soId,
                data.clientIPAddress,
            );
            expect(result.checkResult).toBe(false);
            expect(result.processingCode).toBe(ResultCdConstants.CODE_000934);
            expect(result.others).toBe("");
        });

        test("should return CODE_000934 if getTenantsEntity fails to query", async () => {
            jest.spyOn(APICommonDAO.prototype,"getTenantsEntity").mockImplementationOnce(async () =>{
                throw new ConnectionTimedOutError(new Error("timeout error"));
            })
            const result = await instance.checkRestCommon(
                data.sequenceNo,
                data.senderSystemId,
                data.apiKey,
                data.functionType,
                data.tenantId,
                data.localFunctionType,
                data.url,
                data.orderType,
                data.reserve_soId,
                data.clientIPAddress,
            );

            expect(result.checkResult).toBe(false);
            expect(result.processingCode).toBe(ResultCdConstants.CODE_000934);
            expect(result.others).toBe("");
        });

        test("should return CODE_000936 if tenant connection limit is exceeded", async () => {
            let invocationList: string[] = [];
            try {
                const tenant = await TenantsEntity.findOne({
                    where: {
                        tenantId: data.tenantId,
                    },
                });
                expect(tenant).not.toBeNull(); // tenant should exist

                // set tenant connection count to max
                invocationList = await setTenantConnectionCount(
                    data.tenantId,
                    tenant.tenantMaxConnection,
                );

                const result = await instance.checkRestCommon(
                    data.sequenceNo,
                    data.senderSystemId,
                    data.apiKey,
                    data.functionType,
                    data.tenantId,
                    data.localFunctionType,
                    data.url,
                    data.orderType,
                    data.reserve_soId,
                    data.clientIPAddress,
                );
                expect(result.checkResult).toBe(false);
                expect(result.processingCode).toBe(
                    ResultCdConstants.CODE_000936,
                );
                expect(result.others).toBe("");
            } finally {
                // clean up
                const redisClient = await useRedisMock(context);
                redisClient.zrem(
                    `conCount:${data.tenantId}`,
                    ...invocationList,
                );
            }
        });

        test("should return CODE_000921 if sequenceNo is empty", async () => {
            const result = await instance.checkRestCommon(
                "",
                data.senderSystemId,
                data.apiKey,
                data.functionType,
                data.tenantId,
                data.localFunctionType,
                data.url,
                data.orderType,
                data.reserve_soId,
                data.clientIPAddress,
            );
            expect(result.checkResult).toBe(false);
            expect(result.processingCode).toBe(ResultCdConstants.CODE_000921);
            expect(typeof result.others).toBe("string");
            expect(result.others.length).toBeGreaterThan(0);
        });

        test("should return CODE_000921 if sequenceNo length is not 7", async () => {
            const result = await instance.checkRestCommon(
                "123456",
                data.senderSystemId,
                data.apiKey,
                data.functionType,
                data.tenantId,
                data.localFunctionType,
                data.url,
                data.orderType,
                data.reserve_soId,
                data.clientIPAddress,
            );
            expect(result.checkResult).toBe(false);
            expect(result.processingCode).toBe(ResultCdConstants.CODE_000921);
            expect(typeof result.others).toBe("string");
            expect(result.others.length).toBeGreaterThan(0);
        });

        test("should return CODE_000921 if sequenceNo is not half-width characters", async () => {
            const result = await instance.checkRestCommon(
                "１２３４５６７",
                data.senderSystemId,
                data.apiKey,
                data.functionType,
                data.tenantId,
                data.localFunctionType,
                data.url,
                data.orderType,
                data.reserve_soId,
                data.clientIPAddress,
            );
            expect(result.checkResult).toBe(false);
            expect(result.processingCode).toBe(ResultCdConstants.CODE_000921);
            expect(typeof result.others).toBe("string");
            expect(result.others.length).toBeGreaterThan(0);
        });

        test("should return CODE_000921 if senderSystemId is empty", async () => {
            const result = await instance.checkRestCommon(
                data.sequenceNo,
                "",
                data.apiKey,
                data.functionType,
                data.tenantId,
                data.localFunctionType,
                data.url,
                data.orderType,
                data.reserve_soId,
                data.clientIPAddress,
            );
            expect(result.checkResult).toBe(false);
            expect(result.processingCode).toBe(ResultCdConstants.CODE_000921);
            expect(typeof result.others).toBe("string");
            expect(result.others.length).toBeGreaterThan(0);
        });

        test("should return CODE_000921 if senderSystemId is not half-width characters", async () => {
            const result = await instance.checkRestCommon(
                data.sequenceNo,
                "０００１",
                data.apiKey,
                data.functionType,
                data.tenantId,
                data.localFunctionType,
                data.url,
                data.orderType,
                data.reserve_soId,
                data.clientIPAddress,
            );
            expect(result.checkResult).toBe(false);
            expect(result.processingCode).toBe(ResultCdConstants.CODE_000921);
            expect(typeof result.others).toBe("string");
            expect(result.others.length).toBeGreaterThan(0);
        });

        test("should return CODE_000921 if apiKey is empty", async () => {
            const result = await instance.checkRestCommon(
                data.sequenceNo,
                data.senderSystemId,
                "",
                data.functionType,
                data.tenantId,
                data.localFunctionType,
                data.url,
                data.orderType,
                data.reserve_soId,
                data.clientIPAddress,
            );
            expect(result.checkResult).toBe(false);
            expect(result.processingCode).toBe(ResultCdConstants.CODE_000921);
            expect(typeof result.others).toBe("string");
            expect(result.others.length).toBeGreaterThan(0);
        });

        test("should return CODE_000921 if apiKey length is not 72", async () => {
            const result = await instance.checkRestCommon(
                data.sequenceNo,
                data.senderSystemId,
                "123456",
                data.functionType,
                data.tenantId,
                data.localFunctionType,
                data.url,
                data.orderType,
                data.reserve_soId,
                data.clientIPAddress,
            );
            expect(result.checkResult).toBe(false);
            expect(result.processingCode).toBe(ResultCdConstants.CODE_000921);
            expect(typeof result.others).toBe("string");
            expect(result.others.length).toBeGreaterThan(0);
        });

        test("should return CODE_000921 if functionType is empty", async () => {
            const result = await instance.checkRestCommon(
                data.sequenceNo,
                data.senderSystemId,
                data.apiKey,
                "",
                data.tenantId,
                data.localFunctionType,
                data.url,
                data.orderType,
                data.reserve_soId,
                data.clientIPAddress,
            );
            expect(result.checkResult).toBe(false);
            expect(result.processingCode).toBe(ResultCdConstants.CODE_000921);
            expect(typeof result.others).toBe("string");
            expect(result.others.length).toBeGreaterThan(0);
        });

        test("should return CODE_000921 if functionType length is not 2", async () => {
            const result = await instance.checkRestCommon(
                data.sequenceNo,
                data.senderSystemId,
                data.apiKey,
                "123",
                data.tenantId,
                data.localFunctionType,
                data.url,
                data.orderType,
                data.reserve_soId,
                data.clientIPAddress,
            );
            expect(result.checkResult).toBe(false);
            expect(result.processingCode).toBe(ResultCdConstants.CODE_000921);
            expect(typeof result.others).toBe("string");
            expect(result.others.length).toBeGreaterThan(0);
        });

        test("should return CODE_000921 if functionType is not numeric", async () => {
            const result = await instance.checkRestCommon(
                data.sequenceNo,
                data.senderSystemId,
                data.apiKey,
                "AB",
                data.tenantId,
                data.localFunctionType,
                data.url,
                data.orderType,
                data.reserve_soId,
                data.clientIPAddress,
            );
            expect(result.checkResult).toBe(false);
            expect(result.processingCode).toBe(ResultCdConstants.CODE_000921);
            expect(typeof result.others).toBe("string");
            expect(result.others.length).toBeGreaterThan(0);
        });

        test("should return CODE_000921 if functionType is 00", async () => {
            const result = await instance.checkRestCommon(
                data.sequenceNo,
                data.senderSystemId,
                data.apiKey,
                "00",
                data.tenantId,
                data.localFunctionType,
                data.url,
                data.orderType,
                data.reserve_soId,
                data.clientIPAddress,
            );
            expect(result.checkResult).toBe(false);
            expect(result.processingCode).toBe(ResultCdConstants.CODE_000921);
            expect(typeof result.others).toBe("string");
            expect(result.others.length).toBeGreaterThan(0);
        });

        // skip this test because it will fail during tenant connection check
        test.skip("should return CODE_000921 if tenantId is empty", () => {});

        test("should return CODE_000921 if tenantId length is not 6", async () => {
            try {
                // insert tenant to simulate invalid tenantId
                await TenantsEntity.create({
                    tenantId: "OPF00",
                    tenantName: "OPF00",
                    tenantMaxConnection: 10,
                    hashedPassword: tenantPwd,
                    office: true,
                    status: true,
                    tenantLevel: 1,
                    customizeMaxConnection: 10,
                    notvoiceRoamingFlag: false,
                    pTenantId: "ZZZ000",
                    csvOutputPattern: 1,
                });
                const result = await instance.checkRestCommon(
                    data.sequenceNo,
                    data.senderSystemId,
                    data.apiKey,
                    data.functionType,
                    "OPF00",
                    data.localFunctionType,
                    data.url,
                    data.orderType,
                    data.reserve_soId,
                    data.clientIPAddress,
                );
                expect(result.checkResult).toBe(false);
                expect(result.processingCode).toBe(
                    ResultCdConstants.CODE_000921,
                );
                expect(typeof result.others).toBe("string");
                expect(result.others.length).toBeGreaterThan(0);
            } finally {
                // clean up
                TenantsEntity.destroy({
                    where: {
                        tenantId: "OPF00",
                    },
                });
            }
        });

        // skip this test because it will fail during tenant connection check
        test.skip("should return CODE_000921 if tenantId is not half-width characters", () => {});

        test("should return CODE_000922 if localFunctionType and functionType do not match", async () => {
            const result = await instance.checkRestCommon(
                data.sequenceNo,
                data.senderSystemId,
                data.apiKey,
                data.functionType,
                data.tenantId,
                "01",
                data.url,
                data.orderType,
                data.reserve_soId,
                data.clientIPAddress,
            );
            expect(result.checkResult).toBe(false);
            expect(result.processingCode).toBe(ResultCdConstants.CODE_000922);
            expect(typeof result.others).toBe("string");
            expect(result.others.length).toBeGreaterThan(0);
        });

        test.skip("should return CODE_000941 if tenant password is emtpy (DB)", async () => {
            try {
                // remove tenant password temporarily
                await TenantsEntity.update(
                    {
                        hashedPassword: "",
                    },
                    {
                        where: {
                            tenantId: data.tenantId,
                        },
                    },
                );

                const result = await instance.checkRestCommon(
                    data.sequenceNo,
                    data.senderSystemId,
                    data.apiKey,
                    data.functionType,
                    data.tenantId,
                    data.localFunctionType,
                    data.url,
                    data.orderType,
                    data.reserve_soId,
                    data.clientIPAddress,
                );
                expect(result.checkResult).toBe(false);
                expect(result.processingCode).toBe(
                    ResultCdConstants.CODE_000941,
                );
                expect(typeof result.others).toBe("string");
                expect(result.others.length).toBeGreaterThan(0);
            } finally {
                // restore tenant password
                await TenantsEntity.update(
                    {
                        hashedPassword: tenantPwd,
                    },
                    {
                        where: {
                            tenantId: data.tenantId,
                        },
                    },
                );
            }
        });

        test("should return CODE_000942 if API key is invalid", async () => {
            const result = await instance.checkRestCommon(
                data.sequenceNo,
                data.senderSystemId,
                tenantPwd.slice(0, -5) + "00000" + sequenceHash, // remove last charater
                data.functionType,
                data.tenantId,
                data.localFunctionType,
                data.url,
                data.orderType,
                data.reserve_soId,
                data.clientIPAddress,
            );
            expect(result.checkResult).toBe(false);
            expect(result.processingCode).toBe(ResultCdConstants.CODE_000942);
            expect(typeof result.others).toBe("string");
            expect(result.others.length).toBeGreaterThan(0);
        });

        test("should return CODE_000942 if API key is invalid (not match with sequenceNo)", async () => {
            const result = await instance.checkRestCommon(
                "9990000",
                data.senderSystemId,
                data.apiKey,
                data.functionType,
                data.tenantId,
                data.localFunctionType,
                data.url,
                data.orderType,
                data.reserve_soId,
                data.clientIPAddress,
            );
            expect(result.checkResult).toBe(false);
            expect(result.processingCode).toBe(ResultCdConstants.CODE_000942);
            expect(typeof result.others).toBe("string");
            expect(result.others.length).toBeGreaterThan(0);
        });

        test.skip("should return CODE_000946 if DB IP address is invalid", async () => {
            try {
                // insert bad IP address
                await IpaddressEntity.create({
                    tenantId: "OPF000",
                    ipAddress: "10.1.2",
                    subnetMask: 27,
                });
                const result = await instance.checkRestCommon(
                    data.sequenceNo,
                    data.senderSystemId,
                    data.apiKey,
                    data.functionType,
                    data.tenantId,
                    data.localFunctionType,
                    data.url,
                    data.orderType,
                    data.reserve_soId,
                    data.clientIPAddress,
                );
                expect(result.checkResult).toBe(false);
                expect(result.processingCode).toBe(
                    ResultCdConstants.CODE_000946,
                );
                expect(typeof result.others).toBe("string");
                expect(result.others.length).toBeGreaterThan(0);
            } finally {
                // clean up
                await IpaddressEntity.destroy({
                    where: {
                        tenantId: "OPF000",
                        ipAddress: "10.1.2",
                    },
                });
            }
        });

        test.skip("should return CODE_000946 if subnet mask of DB IP address is invalid", async () => {
            try {
                // insert bad IP address
                await IpaddressEntity.create({
                    tenantId: "OPF000",
                    ipAddress: "********",
                    subnetMask: 16,
                });
                const result = await instance.checkRestCommon(
                    data.sequenceNo,
                    data.senderSystemId,
                    data.apiKey,
                    data.functionType,
                    data.tenantId,
                    data.localFunctionType,
                    data.url,
                    data.orderType,
                    data.reserve_soId,
                    data.clientIPAddress,
                );
                expect(result.checkResult).toBe(false);
                expect(result.processingCode).toBe(
                    ResultCdConstants.CODE_000946,
                );
                expect(typeof result.others).toBe("string");
                expect(result.others.length).toBeGreaterThan(0);
            } finally {
                // clean up
                await IpaddressEntity.destroy({
                    where: {
                        tenantId: "OPF000",
                        ipAddress: "********",
                        subnetMask: 16,
                    },
                });
            }
        });

        // since this is a configuration error, no need to test
        test.skip("should return CODE_000947 if allowed source IP address is invalid", () => {});
        // since this is a configuration error, no need to test
        test.skip("should return CODE_000947 if number of allowed source IP addresses exceeds limit", () => {});

        test.skip("should return CODE_000948 if current IP address is not in allowed list", async () => {
            try {
                // add allowed IP address
                await IpaddressEntity.create({
                    tenantId: "OPF000",
                    ipAddress: "********",
                    subnetMask: 27,
                });
                const result = await instance.checkRestCommon(
                    data.sequenceNo,
                    data.senderSystemId,
                    data.apiKey,
                    data.functionType,
                    data.tenantId,
                    data.localFunctionType,
                    data.url,
                    data.orderType,
                    data.reserve_soId,
                    "*********", // allowed range is ******** ~ *********
                );
                expect(result.checkResult).toBe(false);
                expect(result.processingCode).toBe(
                    ResultCdConstants.CODE_000948,
                );
                expect(typeof result.others).toBe("string");
                expect(result.others.length).toBeGreaterThan(0);
            } finally {
                // clean up
                await IpaddressEntity.destroy({
                    where: {
                        tenantId: "OPF000",
                        ipAddress: "********",
                        subnetMask: 27,
                    },
                });
            }
        });

        test("should return CODE_999999 if checkAuthentication fails", async () => {
            jest.spyOn(APICommonDAO.prototype,"getTenants").mockImplementationOnce(async () =>{
                throw new ConnectionTimedOutError(new Error("timeout error"));
            })
            const result = await instance.checkRestCommon(
                data.sequenceNo,
                data.senderSystemId,
                data.apiKey,
                data.functionType,
                data.tenantId,
                data.localFunctionType,
                data.url,
                data.orderType,
                data.reserve_soId,
                data.clientIPAddress,
            );
            expect(result.processingCode).toBe(ResultCdConstants.CODE_999999);
        });

        test("should return CODE_000000 if all parameters are valid", async () => {
            const result = await instance.checkRestCommon(
                data.sequenceNo,
                data.senderSystemId,
                data.apiKey,
                data.functionType,
                data.tenantId,
                data.localFunctionType,
                data.url,
                data.orderType,
                data.reserve_soId,
                data.clientIPAddress,
            );
            expect(result.checkResult).toBe(true);
            expect(result.processingCode).toBe(ResultCdConstants.CODE_000000);
            expect(typeof result.others).toBe("string");
            expect(result.others.length).toBeGreaterThan(0);
        });

        test("should return CODE_000000 if all parameters are valid (allowed IP address)", async () => {
            try {
                // add allowed IP address
                await IpaddressEntity.create({
                    tenantId: "OPF000",
                    ipAddress: "********",
                    subnetMask: 27,
                });
                const result = await instance.checkRestCommon(
                    data.sequenceNo,
                    data.senderSystemId,
                    data.apiKey,
                    data.functionType,
                    data.tenantId,
                    data.localFunctionType,
                    data.url,
                    data.orderType,
                    data.reserve_soId,
                    data.clientIPAddress,
                );
                expect(result.checkResult).toBe(true);
                expect(result.processingCode).toBe(
                    ResultCdConstants.CODE_000000,
                );
                expect(typeof result.others).toBe("string");
                expect(result.others.length).toBeGreaterThan(0);
            } finally {
                // clean up
                await IpaddressEntity.destroy({
                    where: {
                        tenantId: "OPF000",
                        ipAddress: "********",
                        subnetMask: 27,
                    },
                });
            }
        });

        test("should return code CODE_999999 when getAPIID throws an error", async () => {
            jest.spyOn(
                APICommonDAO.prototype,
                "getSequence",
            ).mockImplementationOnce(() => {
                throw new Error();
            });

            const response = await instance.checkRestCommon(
                data.sequenceNo,
                data.senderSystemId,
                data.apiKey,
                data.functionType,
                data.tenantId,
                data.localFunctionType,
                data.url,
                data.orderType,
                data.reserve_soId,
                data.clientIPAddress,
            );
            expect(response.checkResult).toEqual(false);
            expect(response.processingCode).toEqual(
                ResultCdConstants.CODE_999999,
            );
            expect(typeof response.others).toBe("string");
        });

        test("should return code CODE_000000 when order type is 2", async () => {
            const response = await instance.checkRestCommon(
                data.sequenceNo,
                data.senderSystemId,
                data.apiKey,
                data.functionType,
                data.tenantId,
                data.localFunctionType,
                data.url,
                Constants.ORDER_TYPE_2,
                data.reserve_soId,
                data.clientIPAddress,
            );
            expect(response.checkResult).toEqual(true);
            expect(response.processingCode).toEqual(
                ResultCdConstants.CODE_000000,
            );
            expect(typeof response.others).toBe("undefined");
        });

        test("should return code CODE_999999 when checkAuthentication throws an error and order type is 2", async () => {
            jest.spyOn(
                APICommonDAO.prototype,
                "getTenants",
            ).mockImplementationOnce((tenantId: string) => {
                throw new Error();
            });

            const response = await instance.checkRestCommon(
                data.sequenceNo,
                data.senderSystemId,
                data.apiKey,
                data.functionType,
                data.tenantId,
                data.localFunctionType,
                data.url,
                Constants.ORDER_TYPE_2,
                data.reserve_soId,
                data.clientIPAddress,
            );
            expect(response.checkResult).toEqual(false);
            expect(response.processingCode).toEqual(
                ResultCdConstants.CODE_999999,
            );
            expect(typeof response.others).toBe("undefined");
        });

        test("should return code CODE_999999 when getAPIID fails to query", async () => {
            jest.spyOn(
                APICommonDAO.prototype,
                "getSequence",
            ).mockImplementationOnce(() => {
                throw new ConnectionTimedOutError(new Error("timeout error"));
            });

            const response = await instance.checkRestCommon(
                data.sequenceNo,
                data.senderSystemId,
                data.apiKey,
                data.functionType,
                data.tenantId,
                data.localFunctionType,
                data.url,
                data.orderType,
                data.reserve_soId,
                data.clientIPAddress,
            );
            expect(response.checkResult).toEqual(false);
            expect(response.processingCode).toEqual(
                ResultCdConstants.CODE_999999,
            );
            expect(typeof response.others).toBe("string");
        });
    });

    describe("checkRestCommonInside", () => {
        const sequenceHash = "$02$a9f6ddd4144344edfdd5a50efb814119"; // sequence 1234567
        let tenantPwd: string = "";
        const data = {
            sequenceNo: "1234567",
            senderSystemId: "0001",
            apiKey: "",
            tenantId: "OPF000",
            functionType: LinesDelHandler.Meta.localFunctionType, // LINE_DEL
            localFunctionType: LinesDelHandler.Meta.localFunctionType,
            url: LinesDelHandler.Meta.url,
            orderType: Constants.ORDER_TYPE_1,
            reserve_soId: undefined,
            clientIPAddress: "********",
        };

        beforeAll(async () => {
            // get tenant key
            const tenant = await TenantsEntity.findOne({
                where: {
                    tenantId: data.tenantId,
                },
            });
            if (tenant) {
                data.apiKey = tenant.hashedPassword + sequenceHash;
                tenantPwd = tenant.hashedPassword;
            }
        });

        afterEach(async () => {
            // reset connection count based on latest invocationId
            instance.modConCount(false);
            instance.decTenantConnectCount(
                data.tenantId,
                ResultCdConstants.CODE_000000,
            );
        });

        // helper function to set connection count to max
        const setConnectionCount = async (maxConnection: number) => {
            const invocationList: string[] = [];
            for (let i = 0; i < maxConnection; i++) {
                invocationList.push(context.invocationId);
                await instance.modConCount(true);
                context.invocationId = getNextInvocationId();
            }
            expect(invocationList.length).toBe(maxConnection);
            const con = await instance.getConCount();
            expect(con).toBe(maxConnection);
            return invocationList;
        };

        // helper function to set tenant connection count to max
        const setTenantConnectionCount = async (
            tenantId: string,
            maxConnection: number,
        ) => {
            const invocationList: string[] = [];
            const now = Math.floor(Date.now() / 1000);
            const redisClient = await useRedisMock(context);
            for (let i = 0; i < maxConnection; i++) {
                invocationList.push(context.invocationId);
                // add to Redis directly since tenantConCountHelper is private
                await redisClient.zadd(
                    `conCount:${tenantId}`,
                    now,
                    context.invocationId,
                );
                // expires in 1 minute
                await redisClient.expire(`conCount:${tenantId}`, 60);
                context.invocationId = getNextInvocationId();
            }
            expect(
                await redisClient.zcount(`conCount:${tenantId}`, now, "+inf"),
            ).toBe(maxConnection); // should be maximum connection
            return invocationList;
        };

        // Since checkHeaderFormat is a private method, we must check via checkRestCommon
        test("should return code CODE_000921 when senderSystemId is less than 4 digits", async () => {
            const response = await instance.checkRestCommonInside(
                data.sequenceNo,
                "123",
                data.apiKey,
                data.functionType,
                data.tenantId,
                data.localFunctionType,
                data.url,
                data.orderType,
                data.reserve_soId,
            );

            expect(response.checkResult).toEqual(false);
            expect(response.processingCode).toEqual(
                ResultCdConstants.CODE_000921,
            );
            expect(typeof response.others).toBe("string");
        });

        // Since checkHeaderFormat is a private method, we must check via checkRestCommon
        test("should return code CODE_000921 when tenantId is null, empty, undefined and orderType is 2", async () => {
            for (const tenantId of [null, "", undefined]) {
                const response = await instance.checkRestCommonInside(
                    data.sequenceNo,
                    data.senderSystemId,
                    data.apiKey,
                    data.functionType,
                    tenantId,
                    data.localFunctionType,
                    data.url,
                    Constants.ORDER_TYPE_2,
                    data.reserve_soId,
                );

                expect(response.checkResult).toEqual(false);
                expect(response.processingCode).toEqual(
                    ResultCdConstants.CODE_000921,
                );
                expect(typeof response.others).toBe("undefined");
            }
        });

        // Since checkHeaderFormat is a private method, we must check via checkRestCommon
        test("should return code CODE_000921 when tenantId is not alphanumeric string and orderType is 2", async () => {
            const response = await instance.checkRestCommonInside(
                data.sequenceNo,
                data.senderSystemId,
                data.apiKey,
                data.functionType,
                "$%全角()",
                data.localFunctionType,
                data.url,
                Constants.ORDER_TYPE_2,
                data.reserve_soId,
            );

            expect(response.checkResult).toEqual(false);
            expect(response.processingCode).toEqual(
                ResultCdConstants.CODE_000921,
            );
            expect(typeof response.others).toBe("undefined");
        });

        test("should return CODE_000000 when all parameters are valid", async () => {
            const result = await instance.checkRestCommonInside(
                data.sequenceNo,
                data.senderSystemId,
                data.apiKey,
                data.functionType,
                data.tenantId,
                data.localFunctionType,
                data.url,
                data.orderType,
                data.reserve_soId,
            );
            expect(result.checkResult).toBe(true);
            expect(result.processingCode).toBe(ResultCdConstants.CODE_000000);
        });

        test("should return CODE_000931 if connection limit is exceeded", async () => {
            let invocationList: string[] = [];
            try {
                const maxConnection = getConfigAsNumber(
                    "ServerConnectionLimit",
                );
                // insert dummy data to simulate max connection limit
                invocationList = await setConnectionCount(maxConnection);

                // run instance again with new invocationId
                const result = await instance.checkRestCommonInside(
                    data.sequenceNo,
                    data.senderSystemId,
                    data.apiKey,
                    data.functionType,
                    data.tenantId,
                    data.localFunctionType,
                    data.url,
                    data.orderType,
                    data.reserve_soId,
                );
                expect(result.checkResult).toBe(false);
                expect(result.processingCode).toBe(
                    ResultCdConstants.CODE_000931,
                );
            } finally {
                // clean up dummy connections count before next test
                if (invocationList.length > 0) {
                    const redisClient = await useRedisMock(context);
                    redisClient.zrem("conCount", ...invocationList);
                }
            }
        });

        test("should return CODE_000934 if tenant cannot be found", async () => {
            const result = await instance.checkRestCommonInside(
                data.sequenceNo,
                data.senderSystemId,
                data.apiKey,
                data.functionType,
                "UNKNOWN",
                data.localFunctionType,
                data.url,
                data.orderType,
                data.reserve_soId,
            );
            expect(result.checkResult).toBe(false);
            expect(result.processingCode).toBe(ResultCdConstants.CODE_000934);
            expect(result.others).toBe("");
        });

        test("should return CODE_000936 if tenant connection limit is exceeded", async () => {
            let invocationList: string[] = [];
            try {
                const tenant = await TenantsEntity.findOne({
                    where: {
                        tenantId: data.tenantId,
                    },
                });
                expect(tenant).not.toBeNull(); // tenant should exist

                // set tenant connection count to max
                invocationList = await setTenantConnectionCount(
                    data.tenantId,
                    tenant.tenantMaxConnection,
                );

                const result = await instance.checkRestCommonInside(
                    data.sequenceNo,
                    data.senderSystemId,
                    data.apiKey,
                    data.functionType,
                    data.tenantId,
                    data.localFunctionType,
                    data.url,
                    data.orderType,
                    data.reserve_soId,
                );
                expect(result.checkResult).toBe(false);
                expect(result.processingCode).toBe(
                    ResultCdConstants.CODE_000936,
                );
                expect(result.others).toBe("");
            } finally {
                // clean up
                const redisClient = await useRedisMock(context);
                redisClient.zrem(
                    `conCount:${data.tenantId}`,
                    ...invocationList,
                );
            }
        });

        test("should return CODE_000921 if sequenceNo is empty", async () => {
            const result = await instance.checkRestCommonInside(
                "",
                data.senderSystemId,
                data.apiKey,
                data.functionType,
                data.tenantId,
                data.localFunctionType,
                data.url,
                data.orderType,
                data.reserve_soId,
            );
            expect(result.checkResult).toBe(false);
            expect(result.processingCode).toBe(ResultCdConstants.CODE_000921);
            expect(typeof result.others).toBe("string");
            expect(result.others.length).toBeGreaterThan(0);
        });

        test("should return CODE_000921 if sequenceNo length is not 7", async () => {
            const result = await instance.checkRestCommonInside(
                "123456",
                data.senderSystemId,
                data.apiKey,
                data.functionType,
                data.tenantId,
                data.localFunctionType,
                data.url,
                data.orderType,
                data.reserve_soId,
            );
            expect(result.checkResult).toBe(false);
            expect(result.processingCode).toBe(ResultCdConstants.CODE_000921);
            expect(typeof result.others).toBe("string");
            expect(result.others.length).toBeGreaterThan(0);
        });

        test("should return CODE_000921 if sequenceNo is not half-width characters", async () => {
            const result = await instance.checkRestCommonInside(
                "１２３４５６７",
                data.senderSystemId,
                data.apiKey,
                data.functionType,
                data.tenantId,
                data.localFunctionType,
                data.url,
                data.orderType,
                data.reserve_soId,
            );
            expect(result.checkResult).toBe(false);
            expect(result.processingCode).toBe(ResultCdConstants.CODE_000921);
            expect(typeof result.others).toBe("string");
            expect(result.others.length).toBeGreaterThan(0);
        });

        test("should return CODE_000921 if senderSystemId is empty", async () => {
            const result = await instance.checkRestCommonInside(
                data.sequenceNo,
                "",
                data.apiKey,
                data.functionType,
                data.tenantId,
                data.localFunctionType,
                data.url,
                data.orderType,
                data.reserve_soId,
            );
            expect(result.checkResult).toBe(false);
            expect(result.processingCode).toBe(ResultCdConstants.CODE_000921);
            expect(typeof result.others).toBe("string");
            expect(result.others.length).toBeGreaterThan(0);
        });

        test("should return CODE_000921 if senderSystemId is not half-width characters", async () => {
            const result = await instance.checkRestCommonInside(
                data.sequenceNo,
                "０００１",
                data.apiKey,
                data.functionType,
                data.tenantId,
                data.localFunctionType,
                data.url,
                data.orderType,
                data.reserve_soId,
            );
            expect(result.checkResult).toBe(false);
            expect(result.processingCode).toBe(ResultCdConstants.CODE_000921);
            expect(typeof result.others).toBe("string");
            expect(result.others.length).toBeGreaterThan(0);
        });

        test("should return CODE_000921 if apiKey is empty", async () => {
            const result = await instance.checkRestCommonInside(
                data.sequenceNo,
                data.senderSystemId,
                "",
                data.functionType,
                data.tenantId,
                data.localFunctionType,
                data.url,
                data.orderType,
                data.reserve_soId,
            );
            expect(result.checkResult).toBe(false);
            expect(result.processingCode).toBe(ResultCdConstants.CODE_000921);
            expect(typeof result.others).toBe("string");
            expect(result.others.length).toBeGreaterThan(0);
        });

        test("should return CODE_000921 if apiKey length is not 72", async () => {
            const result = await instance.checkRestCommonInside(
                data.sequenceNo,
                data.senderSystemId,
                "123456",
                data.functionType,
                data.tenantId,
                data.localFunctionType,
                data.url,
                data.orderType,
                data.reserve_soId,
            );
            expect(result.checkResult).toBe(false);
            expect(result.processingCode).toBe(ResultCdConstants.CODE_000921);
            expect(typeof result.others).toBe("string");
            expect(result.others.length).toBeGreaterThan(0);
        });

        test("should return CODE_000921 if functionType is empty", async () => {
            const result = await instance.checkRestCommonInside(
                data.sequenceNo,
                data.senderSystemId,
                data.apiKey,
                "",
                data.tenantId,
                data.localFunctionType,
                data.url,
                data.orderType,
                data.reserve_soId,
            );
            expect(result.checkResult).toBe(false);
            expect(result.processingCode).toBe(ResultCdConstants.CODE_000921);
            expect(typeof result.others).toBe("string");
            expect(result.others.length).toBeGreaterThan(0);
        });

        test("should return CODE_000921 if functionType length is not 2", async () => {
            const result = await instance.checkRestCommonInside(
                data.sequenceNo,
                data.senderSystemId,
                data.apiKey,
                "123",
                data.tenantId,
                data.localFunctionType,
                data.url,
                data.orderType,
                data.reserve_soId,
            );
            expect(result.checkResult).toBe(false);
            expect(result.processingCode).toBe(ResultCdConstants.CODE_000921);
            expect(typeof result.others).toBe("string");
            expect(result.others.length).toBeGreaterThan(0);
        });

        test("should return CODE_000921 if functionType is not numeric", async () => {
            const result = await instance.checkRestCommonInside(
                data.sequenceNo,
                data.senderSystemId,
                data.apiKey,
                "AB",
                data.tenantId,
                data.localFunctionType,
                data.url,
                data.orderType,
                data.reserve_soId,
            );
            expect(result.checkResult).toBe(false);
            expect(result.processingCode).toBe(ResultCdConstants.CODE_000921);
            expect(typeof result.others).toBe("string");
            expect(result.others.length).toBeGreaterThan(0);
        });

        test("should return CODE_000921 if functionType is 00", async () => {
            const result = await instance.checkRestCommonInside(
                data.sequenceNo,
                data.senderSystemId,
                data.apiKey,
                "00",
                data.tenantId,
                data.localFunctionType,
                data.url,
                data.orderType,
                data.reserve_soId,
            );
            expect(result.checkResult).toBe(false);
            expect(result.processingCode).toBe(ResultCdConstants.CODE_000921);
            expect(typeof result.others).toBe("string");
            expect(result.others.length).toBeGreaterThan(0);
        });

        // skip this test because it will fail during tenant connection check
        test.skip("should return CODE_000921 if tenantId is empty", () => {});

        test("should return CODE_000921 if tenantId length is not 6", async () => {
            try {
                // insert tenant to simulate invalid tenantId
                await TenantsEntity.create({
                    tenantId: "OPF00",
                    tenantName: "OPF00",
                    tenantMaxConnection: 10,
                    hashedPassword: tenantPwd,
                    office: true,
                    status: true,
                    tenantLevel: 1,
                    customizeMaxConnection: 10,
                    notvoiceRoamingFlag: false,
                    pTenantId: "ZZZ000",
                    csvOutputPattern: 1,
                });
                const result = await instance.checkRestCommonInside(
                    data.sequenceNo,
                    data.senderSystemId,
                    data.apiKey,
                    data.functionType,
                    "OPF00",
                    data.localFunctionType,
                    data.url,
                    data.orderType,
                    data.reserve_soId,
                );
                expect(result.checkResult).toBe(false);
                expect(result.processingCode).toBe(
                    ResultCdConstants.CODE_000921,
                );
                expect(typeof result.others).toBe("string");
                expect(result.others.length).toBeGreaterThan(0);
            } finally {
                // clean up
                TenantsEntity.destroy({
                    where: {
                        tenantId: "OPF00",
                    },
                });
            }
        });

        // skip this test because it will fail during tenant connection check
        test.skip("should return CODE_000921 if tenantId is not half-width characters", () => {});

        test("should return CODE_000922 if localFunctionType and functionType do not match", async () => {
            const result = await instance.checkRestCommonInside(
                data.sequenceNo,
                data.senderSystemId,
                data.apiKey,
                data.functionType,
                data.tenantId,
                "01",
                data.url,
                data.orderType,
                data.reserve_soId,
            );
            expect(result.checkResult).toBe(false);
            expect(result.processingCode).toBe(ResultCdConstants.CODE_000922);
            expect(typeof result.others).toBe("string");
            expect(result.others.length).toBeGreaterThan(0);
        });

        test.skip("should return CODE_000941 if tenant password is emtpy (DB)", async () => {
            try {
                // remove tenant password temporarily
                await TenantsEntity.update(
                    {
                        hashedPassword: "",
                    },
                    {
                        where: {
                            tenantId: data.tenantId,
                        },
                    },
                );

                const result = await instance.checkRestCommonInside(
                    data.sequenceNo,
                    data.senderSystemId,
                    data.apiKey,
                    data.functionType,
                    data.tenantId,
                    data.localFunctionType,
                    data.url,
                    data.orderType,
                    data.reserve_soId,
                );
                expect(result.checkResult).toBe(false);
                expect(result.processingCode).toBe(
                    ResultCdConstants.CODE_000941,
                );
                expect(typeof result.others).toBe("string");
                expect(result.others.length).toBeGreaterThan(0);
            } finally {
                // restore tenant password
                await TenantsEntity.update(
                    {
                        hashedPassword: tenantPwd,
                    },
                    {
                        where: {
                            tenantId: data.tenantId,
                        },
                    },
                );
            }
        });

        // since this is a configuration error, no need to test
        test.skip("should return CODE_000947 if allowed source IP address is invalid", () => {});
        // since this is a configuration error, no need to test
        test.skip("should return CODE_000947 if number of allowed source IP addresses exceeds limit", () => {});

        test("should return CODE_000000 if all parameters are valid", async () => {
            const result = await instance.checkRestCommonInside(
                data.sequenceNo,
                data.senderSystemId,
                data.apiKey,
                data.functionType,
                data.tenantId,
                data.localFunctionType,
                data.url,
                data.orderType,
                data.reserve_soId,
            );
            expect(result.checkResult).toBe(true);
            expect(result.processingCode).toBe(ResultCdConstants.CODE_000000);
            expect(typeof result.others).toBe("string");
            expect(result.others.length).toBeGreaterThan(0);
        });

        test("should return CODE_000000 if all parameters are valid (allowed IP address)", async () => {
            try {
                // add allowed IP address
                await IpaddressEntity.create({
                    tenantId: "OPF000",
                    ipAddress: "********",
                    subnetMask: 27,
                });
                const result = await instance.checkRestCommonInside(
                    data.sequenceNo,
                    data.senderSystemId,
                    data.apiKey,
                    data.functionType,
                    data.tenantId,
                    data.localFunctionType,
                    data.url,
                    data.orderType,
                    data.reserve_soId,
                );
                expect(result.checkResult).toBe(true);
                expect(result.processingCode).toBe(
                    ResultCdConstants.CODE_000000,
                );
                expect(typeof result.others).toBe("string");
                expect(result.others.length).toBeGreaterThan(0);
            } finally {
                // clean up
                await IpaddressEntity.destroy({
                    where: {
                        tenantId: "OPF000",
                        ipAddress: "********",
                        subnetMask: 27,
                    },
                });
            }
        });

        test("should return code CODE_999999 when getAPIID throws an error", async () => {
            jest.spyOn(
                APICommonDAO.prototype,
                "getSequence",
            ).mockImplementationOnce(() => {
                throw new Error();
            });

            const response = await instance.checkRestCommonInside(
                data.sequenceNo,
                data.senderSystemId,
                data.apiKey,
                data.functionType,
                data.tenantId,
                data.localFunctionType,
                data.url,
                data.orderType,
                data.reserve_soId,
            );
            expect(response.checkResult).toEqual(false);
            expect(response.processingCode).toEqual(
                ResultCdConstants.CODE_999999,
            );
            expect(typeof response.others).toBe("string");
        });

        test("should return code CODE_000000 when order type is 2", async () => {
            const response = await instance.checkRestCommonInside(
                data.sequenceNo,
                data.senderSystemId,
                data.apiKey,
                data.functionType,
                data.tenantId,
                data.localFunctionType,
                data.url,
                Constants.ORDER_TYPE_2,
                data.reserve_soId,
            );
            expect(response.checkResult).toEqual(true);
            expect(response.processingCode).toEqual(
                ResultCdConstants.CODE_000000,
            );
            expect(typeof response.others).toBe("undefined");
        });

        // test("should return code CODE_999999 when checkAuthentication throws an error and order type is 2", async () => {
        //     jest
        //         .spyOn(APICommonDAO.prototype, 'getTenants')
        //         .mockImplementationOnce((tenantId: string) => {
        //             throw new Error();
        //         });

        //     const response = await instance.checkRestCommonInside(
        //         data.sequenceNo,
        //         data.senderSystemId,
        //         data.apiKey,
        //         data.functionType,
        //         data.tenantId,
        //         data.localFunctionType,
        //         data.url,
        //         Constants.ORDER_TYPE_2,
        //         data.reserve_soId,
        //     );
        //     expect(response.checkResult).toEqual(false);
        //     expect(response.processingCode).toEqual(ResultCdConstants.CODE_999999);
        //     expect(typeof response.others).toBe("undefined");
        // });
    });

    describe("getTenantConCount", () => {
        test("should throw an error when getting tenantId is null", async () => {
            const promise = instance.getTenantConCount(null);
            await expect(promise).rejects.toThrow("Tenant ID is required");
        });
    });

    describe("addAutoModbucketLineGroup", ()=>{
         const clearData = async (soId: string)=> {
            const sequelize = await usePsql();
            const sql = "DELETE FROM auto_modbucket_line_groups where service_order_id = :service_order_id";
            await sequelize.query(sql, {
                replacements: { service_order_id: soId },
                type: QueryTypes.DELETE,
            });
         }
        const checkAutoModBucketData = async (service_order_id:string, exist: boolean,data:any) =>{
            const sql = "select * from auto_modbucket_line_groups where service_order_id = :service_order_id";
            const sequelize = await usePsql();
            const result = await sequelize.query(sql, {
                replacements: {
                    service_order_id,
                },
                type: QueryTypes.SELECT,
            }) as any;
            if(!exist){
                expect(result.length).toBe(0);
            }else{
                expect(result[0].line_id).toBe(data.lineId);
            }
        }
        const data = {
            sequenceNo: "1234567",
            tenantId: "OPF000",
            functionType: "06",
            orderType: Constants.ORDER_TYPE_0,
            soId: generateProcessID(),
            lineId: "04000000001",
            groupId: "7000402",
            procssCode:ResultCdConstants.CODE_000000,
        };

        test("should fail if functionType is neither one of ['06','14','51','20','58']", async () =>{
            await instance.addAutoModbucketLineGroup(
                data.soId,
                "60",
                data.procssCode,
                data.lineId,
                data.groupId,
                data.tenantId,
                data.sequenceNo,
                data.orderType,
            )
            await checkAutoModBucketData(data.soId,false,data)
        })

        test("should fail if orderType is neither 1 nor 2", async () =>{
            await instance.addAutoModbucketLineGroup(
                data.soId,
                data.functionType,
                data.procssCode,
                data.lineId,
                data.groupId,
                data.tenantId,
                data.sequenceNo,
                Constants.ORDER_TYPE_9,
            )
            await checkAutoModBucketData(data.soId,false,data)
        })

        test("should fail if handleCode is not 000000", async () =>{
            await instance.addAutoModbucketLineGroup(
                data.soId,
                data.functionType,
                ResultCdConstants.CODE_999999,
                data.lineId,
                data.groupId,
                data.tenantId,
                data.sequenceNo,
                data.orderType,
            )
            await checkAutoModBucketData(data.soId,false,data)
        })

        test("should fail if insertAutoModBucketLineGroup gets an unexpected error", async () =>{
            jest.spyOn(APICommonDAO.prototype,"insertAutoModBucketLineGroup").mockImplementationOnce(async () =>{
                throw new ConnectionTimedOutError(new Error("timeout error"));
            })
            await instance.addAutoModbucketLineGroup(
                data.soId,
                data.functionType,
                data.procssCode,
                data.lineId,
                data.groupId,
                data.tenantId,
                data.sequenceNo,
                data.orderType,
            )
            await checkAutoModBucketData(data.soId,false,data)
        })

        test("should fail if insertAutoModBucketLineGroup fails to insert", async () =>{
            jest.spyOn(APICommonDAO.prototype,"insertAutoModBucketLineGroup").mockRejectedValueOnce(new TypeError("some error"));
            await instance.addAutoModbucketLineGroup(
                data.soId,
                data.functionType,
                data.procssCode,
                data.lineId,
                data.groupId,
                data.tenantId,
                data.sequenceNo,
                data.orderType,
            )
            await checkAutoModBucketData(data.soId,false,data)
        })

        test("should fail if fails to get lineID (functionType 20)", async () =>{
            jest.spyOn(APICommonDAO.prototype,"getLineLineGroupToLineId").mockImplementationOnce(async () =>{
                throw new ConnectionTimedOutError(new Error("timeout error"));
            })
            await instance.addAutoModbucketLineGroup(
                data.soId,
                "20",
                data.procssCode,
                data.lineId,
                data.groupId,
                data.tenantId,
                data.sequenceNo,
                data.orderType,
            )
            await checkAutoModBucketData(data.soId,false,data)
        })

        test("should fail if lineId doesnt exist in line_line_group (functionType 20)", async () =>{
            jest.spyOn(APICommonDAO.prototype,"getLineLineGroupToLineId").mockResolvedValueOnce([]);
            await instance.addAutoModbucketLineGroup(
                data.soId,
                "20",
                data.procssCode,
                data.lineId,
                data.groupId,
                data.tenantId,
                data.sequenceNo,
                data.orderType,
            )
            await checkAutoModBucketData(data.soId,false,data)
        })

        test("should fail if mnpInFlg doesnt match is 同一MVNE転入 (functionType 51)", async () =>{
            await instance.addAutoModbucketLineGroup(
                data.soId,
                "51",
                data.procssCode,
                data.lineId,
                data.groupId,
                data.tenantId,
                data.sequenceNo,
                data.orderType,
            )
            await checkAutoModBucketData(data.soId,false,data)
        })

        test("should fail if mnpInFlg doesnt match is 同一MVNE転入 (functionType 51 && process code is not 000000) ", async () =>{
            await instance.addAutoModbucketLineGroup(
                data.soId,
                "51",
                ResultCdConstants.CODE_510401,
                data.lineId,
                data.groupId,
                data.tenantId,
                data.sequenceNo,
                Constants.ORDER_TYPE_0,
            )
            await checkAutoModBucketData(data.soId,false,data)
        })

        test("should fail if getTenants fails to query (functionType 14)", async () =>{
            const random = generateProcessID();
            jest.spyOn(APICommonDAO.prototype,"getTenants").mockImplementationOnce(async () =>{
                throw new ConnectionTimedOutError(new Error("timeout error"));
            })
            await instance.addAutoModbucketLineGroup(
                random,
                "14",
                data.procssCode,
                data.lineId,
                data.groupId,
                data.tenantId,
                data.sequenceNo,
                data.orderType,
            )
            await checkAutoModBucketData(random,false,data)
        })

        test("should fail if tenantType is 1 (functionType 14)", async () =>{
            await instance.addAutoModbucketLineGroup(
                data.soId,
                "14",
                data.procssCode,
                data.lineId,
                data.groupId,
                "CON000",
                data.sequenceNo,
                data.orderType,
            )
            await checkAutoModBucketData(data.soId,false,data)
        })

        test("should fail if getGroupIdList fails to query (functionType 06)", async () =>{
            jest.spyOn(APICommonDAO.prototype,"getGroupIdList").mockImplementationOnce(async () =>{
                throw new ConnectionTimedOutError(new Error("timeout error"));
            })
            await instance.addAutoModbucketLineGroup(
                data.soId,
                data.functionType,
                data.procssCode,
                data.lineId,
                data.groupId,
                data.tenantId,
                data.sequenceNo,
                data.orderType,
            )
            await checkAutoModBucketData(data.soId,false,data)
        })

        test("should fail  if lineId doesnt exist in line_line_group (functionType 06 )", async () =>{
            jest.spyOn(APICommonDAO.prototype,"getGroupIdList").mockResolvedValueOnce([]);
            await instance.addAutoModbucketLineGroup(
                data.soId,
                data.functionType,
                data.procssCode,
                data.lineId,
                data.groupId,
                data.tenantId,
                data.sequenceNo,
                data.orderType,
            )
            await checkAutoModBucketData(data.soId,false,data)
        })

        const functionTypes = ["14",data.functionType];
        test.each(functionTypes)("should add into autobucket if data is valid (functionType %s)", async(functionType)=>{
            await instance.addAutoModbucketLineGroup(
                data.soId,
                functionType,
                data.procssCode,
                data.lineId,
                data.groupId,
                data.tenantId,
                data.sequenceNo,
                data.orderType,
            )
            await checkAutoModBucketData(data.soId,true,data)
            await clearData(data.soId)
        });
    });

    describe("resetTenantConnectCount", () => {
        const tenantId = "ABC000";
        beforeEach(async () => {
            // Clear any existing connection counts for the tenant
            const redisClient = await useRedisMock(context);
            await redisClient.del(`conCount:${tenantId}`);
        });

        test("should reset connection count for the specified tenant", async () => {
            // Set initial connection count
            const redisClient = await useRedisMock(context);
            await redisClient.zadd(`conCount:${tenantId}`, 1, "invocation1");

            // Call the resetTenantConnectCount method
            await instance.resetTenantConnectCount(tenantId);

            // Verify that the connection count is reset
            const count = await redisClient.zcard(`conCount:${tenantId}`);
            expect(count).toBe(0);
        });

        test("should handle non-existent tenant gracefully", async () => {
            const redisClient = await useRedisMock(context);
            // Call the resetTenantConnectCount method for a non-existent tenant
            await instance.resetTenantConnectCount("NON_EXISTENT");

            // Verify that no error is thrown and the method completes successfully
            const count = await redisClient.zcard(`conCount:NON_EXISTENT`);
            expect(count).toBe(0);
        });
    });
});