import { describe, expect, test } from "@jest/globals";
import { ConnectionTimedOutError, Op, Sequelize } from "sequelize";

import DefaultRequest from "../../testing/DefaultRequest";
import DefaultContext from "../../testing/DefaultContext";
import { describeWithDB, useFasterRetries } from "../../testing/TestHelper";

import AppConfig from "@/appconfig";
import TenantManage from "@/core/common/TenantManage";
import APICommonDAO from "@/core/dao/APICommonDAO";
import { LinesEntity } from "@/core/entity/LinesEntity";
import LineTenantsEntity from "@/core/entity/LineTenantsEntity";
import { TenantsEntity } from "@/core/entity/TenantsEntity";
import { TenantNnumbersEntity } from "@/core/entity/TenantNnumbersEntity";
import { usePsql } from "@/database/psql";

describeWithDB("core/common/TenantManage", () => {
    let instance: TenantManage;
    let sequelize: Sequelize = null;

    const testdata = {
        tenantId: "TST000",
        lineNo: ["08024042301", "08024042302", "08024042303", "08024042304"],
        lineNG: "08024042305",
        nnumber: "N121152174", // NOTE TST000's nnumber
        resalePlan: {
            ok: "BS020",
            ng: "BS999",
        },
        planId: [99036, 99055], // BS020
        planIdNG: [99999],
        checkTPCConnectionTenantId: "OPF00",
    };

    beforeAll(async () => {
        sequelize = await usePsql();
        await AppConfig.loadCoreMvnoConfig(DefaultContext);
        await deleteTestData();
        await createTestData();
    });

    afterAll(async () => {
        await deleteTestData();
        await sequelize.close();
    });

    beforeEach(() => {
        instance = new TenantManage(DefaultRequest, DefaultContext);
        useFasterRetries();
    });

    afterEach(() => {
        jest.restoreAllMocks();
        jest.clearAllMocks();
    });

    const createTestData = async () => {
        await Promise.all([
            LinesEntity.create({
                lineId: testdata.lineNo[0],
                lineStatus: "01",
                nnumber: testdata.nnumber,
                simFlag: false,
            }),
            LinesEntity.create({
                lineId: testdata.lineNo[1],
                lineStatus: "03",
                nnumber: testdata.nnumber,
                simFlag: false,
            }),
            LinesEntity.create({
                lineId: testdata.lineNo[2],
                lineStatus: "01",
                nnumber: testdata.nnumber,
                simFlag: false,
                planId: `${testdata.planId[0]}`,
            }),
            LinesEntity.create({
                lineId: testdata.lineNo[3],
                lineStatus: "01",
                nnumber: testdata.nnumber,
                simFlag: false,
                planId: `${testdata.planIdNG[0]}`,
            }),
            TenantsEntity.create({
                tenantId: testdata.checkTPCConnectionTenantId,
                tenantName: testdata.checkTPCConnectionTenantId,
                tenantMaxConnection: 10,
                hashedPassword: "",
                office: true,
                status: true,
                tenantLevel: 1,
                customizeMaxConnection: 10,
                notvoiceRoamingFlag: false,
                pTenantId: "ZZZ000",
                csvOutputPattern: 1,
                tpc: "TpcDestIpAddress",
                tpc2: "TpcDestIpAddress2",
            }),
        ]);
        await LineTenantsEntity.create({
            lineId: testdata.lineNo[0],
            tenantId: testdata.tenantId,
        });
    };

    const deleteTestData = async () => {
        await LineTenantsEntity.destroy({
            where: { lineId: testdata.lineNo },
        });
        await LinesEntity.destroy({
            where: { lineId: testdata.lineNo },
        });
        await TenantsEntity.destroy({
            where: { tenantId: testdata.checkTPCConnectionTenantId },
        });
    };

    const updateTenantOffice = async (tenantId: string, newOffice: boolean) => {
        let oldOffice: boolean = null;
        const tenant = await TenantsEntity.findOne({
            where: { tenantId },
        });
        oldOffice = tenant.office;
        await tenant.update({ office: newOffice });
        return oldOffice;
    };

    describe("doCheck", () => {
        test("should return false if tenantId cannot be found", async () => {
            const result = await instance.doCheck(testdata.lineNo[0], "OPF999");
            expect(result[0]).toBe(false);
        });

        test("should return false if line not found and tenant office is false", async () => {
            let originalFlag = null;
            try {
                originalFlag = await updateTenantOffice(
                    testdata.tenantId,
                    false,
                );

                const result = await instance.doCheck(
                    testdata.lineNG,
                    testdata.tenantId,
                );
                expect(result[0]).toBe(false);
            } finally {
                // restore flag
                if (originalFlag !== null) {
                    await updateTenantOffice(testdata.tenantId, originalFlag);
                }
            }
        });

        test("should return true if line found in line_tenants table", async () => {
            const result = await instance.doCheck(
                testdata.lineNo[0],
                testdata.tenantId,
            );
            expect(result[0]).toBe(true);
            expect(result[1]).toBe(testdata.nnumber);
        });

        test("should return false if lineEntity cannot be found from resale plan id", async () => {
            let originalFlag = null;
            try {
                originalFlag = await updateTenantOffice(
                    testdata.tenantId,
                    true,
                );
                const result = await instance.doCheck(
                    testdata.lineNo[1],
                    testdata.tenantId,
                );
                expect(result[0]).toBe(false);
                expect(result[1]).toBe("");
            } finally {
                // restore flag
                if (originalFlag !== null) {
                    await updateTenantOffice(testdata.tenantId, originalFlag);
                }
            }
        });

        test("should return false if office flag is false", async () => {
            let originalFlag = null;
            try {
                originalFlag = await updateTenantOffice(
                    testdata.tenantId,
                    false,
                );
                const result = await instance.doCheck(
                    testdata.lineNo[1],
                    testdata.tenantId,
                );
                expect(result[0]).toBe(false);
                expect(result[1]).toBe("");
            } finally {
                // restore flag
                if (originalFlag !== null) {
                    await updateTenantOffice(testdata.tenantId, originalFlag);
                }
            }
        });

        test("should return false if planId not found", async () => {
            let originalFlag = null;
            try {
                originalFlag = await updateTenantOffice(
                    testdata.tenantId,
                    true,
                );
                const apiCommonDAO = new APICommonDAO(
                    DefaultRequest,
                    DefaultContext,
                );
                jest.spyOn(
                    apiCommonDAO,
                    "getResalePlanID",
                ).mockImplementationOnce(async (lineId) => {
                    // skip Op:ne 03 filter
                    return await LinesEntity.findOne({
                        where: { lineId: lineId },
                    });
                });
                Object.defineProperty(instance, "aPICommonDAO", {
                    value: apiCommonDAO,
                });

                const result = await instance.doCheck(
                    testdata.lineNo[1],
                    testdata.tenantId,
                );
                expect(result[0]).toBe(false);
                expect(result[1]).toBe("");
            } finally {
                // restore flag
                if (originalFlag !== null) {
                    await updateTenantOffice(testdata.tenantId, originalFlag);
                }
            }
        });

        test("should return false if tenant/plan not found", async () => {
            let originalFlag = null;
            try {
                originalFlag = await updateTenantOffice(
                    testdata.tenantId,
                    true,
                );
                const apiCommonDAO = new APICommonDAO(
                    DefaultRequest,
                    DefaultContext,
                );
                jest.spyOn(
                    apiCommonDAO,
                    "getResalePlanID",
                ).mockImplementationOnce(async (lineId) => {
                    // skip Op:ne 03 filter
                    return await LinesEntity.findOne({
                        where: { lineId: lineId },
                    });
                });
                jest.spyOn(apiCommonDAO, "getPlanID").mockResolvedValueOnce([
                    "99055",
                ]);
                jest.spyOn(apiCommonDAO, "getTenantID2").mockResolvedValueOnce(
                    [],
                );
                Object.defineProperty(instance, "aPICommonDAO", {
                    value: apiCommonDAO,
                });

                const result = await instance.doCheck(
                    testdata.lineNo[1],
                    testdata.tenantId,
                );
                expect(result[0]).toBe(false);
                expect(result[1]).toBe("");
            } finally {
                // restore flag
                if (originalFlag !== null) {
                    await updateTenantOffice(testdata.tenantId, originalFlag);
                }
            }
        });

        test("should return true if tenant/plan found", async () => {
            let originalFlag = null;
            try {
                originalFlag = await updateTenantOffice(
                    testdata.tenantId,
                    true,
                );
                const apiCommonDAO = new APICommonDAO(
                    DefaultRequest,
                    DefaultContext,
                );
                jest.spyOn(
                    apiCommonDAO,
                    "getResalePlanID",
                ).mockImplementationOnce(async (lineId) => {
                    // skip Op:ne 03 filter
                    return await LinesEntity.findOne({
                        where: { lineId: lineId },
                    });
                });
                jest.spyOn(apiCommonDAO, "getPlanID").mockResolvedValueOnce([
                    "99055",
                ]);
                jest.spyOn(apiCommonDAO, "getTenantID2").mockResolvedValueOnce([
                    testdata.tenantId,
                ]);
                Object.defineProperty(instance, "aPICommonDAO", {
                    value: apiCommonDAO,
                });

                const result = await instance.doCheck(
                    testdata.lineNo[1],
                    testdata.tenantId,
                );
                expect(result[0]).toBe(true);
                expect(result[1]).toBe(testdata.nnumber);
            } finally {
                // restore flag
                if (originalFlag !== null) {
                    await updateTenantOffice(testdata.tenantId, originalFlag);
                }
            }
        });

        test("should return true if plan does not match to tenant plans", async () => {
            let originalFlag = null;
            try {
                originalFlag = await updateTenantOffice(
                    testdata.tenantId,
                    true,
                );
                const result = await instance.doCheck(
                    testdata.lineNo[3],
                    testdata.tenantId,
                );
                expect(result[0]).toBe(true);
                expect(result[1]).toBe(testdata.nnumber);
            } finally {
                // restore flag
                if (originalFlag !== null) {
                    await updateTenantOffice(testdata.tenantId, originalFlag);
                }
            }
        });
    });

    describe("getCheckedTenantId", () => {
        test("should return true if line is found in line_tenants table", async () => {
            const result = await instance.getCheckedTenantId(
                testdata.lineNo[0],
            );
            expect(result[0]).toBe(true);
            expect(result[1]).toBe(testdata.tenantId);
        });

        test("should return true if tenant has 1 nnumber", async () => {
            const apiCommonDAO = new APICommonDAO(
                DefaultRequest,
                DefaultContext,
            );
            jest.spyOn(
                apiCommonDAO,
                "getTenantNnumbersEntity",
            ).mockResolvedValueOnce(
                Promise.resolve([
                    new TenantNnumbersEntity({
                        tenantId: testdata.tenantId,
                        nnumber: "N0001110001",
                    }),
                ]),
            );
            Object.defineProperty(instance, "aPICommonDAO", {
                value: apiCommonDAO,
            });

            const result = await instance.getCheckedTenantId(
                testdata.lineNo[2],
            );
            expect(result[0]).toBe(true);
        });

        test("should return true if plan does exist and tenant's office flag is true", async () => {
            const apiCommonDAO = new APICommonDAO(
                DefaultRequest,
                DefaultContext,
            );
            jest.spyOn(
                apiCommonDAO,
                "getTenantNnumbersEntity",
            ).mockResolvedValueOnce(
                Promise.resolve([
                    new TenantNnumbersEntity({
                        tenantId: testdata.tenantId,
                        nnumber: "N0001110001",
                    }),
                    new TenantNnumbersEntity({
                        tenantId: testdata.tenantId,
                        nnumber: "N0001110002",
                    }),
                ]),
            );
            jest.spyOn(apiCommonDAO, "getPlanID").mockResolvedValueOnce([
                "99055",
            ]);
            jest.spyOn(apiCommonDAO, "getTenantsEntity").mockImplementationOnce(
                async (_tenantId: string) => {
                    return Promise.resolve({
                        tenantId: testdata.tenantId,
                        office: true,
                    } as TenantsEntity);
                },
            );
            Object.defineProperty(instance, "aPICommonDAO", {
                value: apiCommonDAO,
            });

            const result = await instance.getCheckedTenantId(
                testdata.lineNo[2],
            );
            expect(result[0]).toBe(true);
        });

        test("should return false if line is found in lines table but tenant has multiple nnumber", async () => {
            const result = await instance.getCheckedTenantId(
                testdata.lineNo[1],
            );
            expect(result[0]).toBe(false);
            expect(result[1]).toBe("");
        });

        test("should return false if line is not found", async () => {
            const result = await instance.getCheckedTenantId(testdata.lineNG);
            expect(result[0]).toBe(false);
            expect(result[1]).toBe("");
        });

        test("should return false if DB query failed", async () => {
            const apiCommonDAO = new APICommonDAO(
                DefaultRequest,
                DefaultContext,
            );
            jest.spyOn(
                apiCommonDAO,
                "getLineTenantsEntity",
            ).mockRejectedValueOnce(
                new ConnectionTimedOutError(new Error("Connection timed out")),
            );
            Object.defineProperty(instance, "aPICommonDAO", {
                value: apiCommonDAO,
            });

            const result = await instance.getCheckedTenantId(
                testdata.lineNo[0],
            );
            expect(result[0]).toBe(false);
            expect(result[1]).toBe("");
        });

        test("should throw error for non DB related error", async () => {
            const apiCommonDAO = new APICommonDAO(
                DefaultRequest,
                DefaultContext,
            );
            jest.spyOn(
                apiCommonDAO,
                "getLineTenantsEntity",
            ).mockRejectedValueOnce(new Error("Test Error"));
            Object.defineProperty(instance, "aPICommonDAO", {
                value: apiCommonDAO,
            });

            await expect(
                instance.getCheckedTenantId(testdata.lineNo[0]),
            ).rejects.toThrow("Test Error");
        });
    });

    describe("checkTpcConnection", () => {
        test("should return false if tenantId cannot be found", async () => {
            const result = await instance.checkTpcConnection("OPF999");
            expect(result[0]).toBe(false);
        });

        test("should return true if tenant has tpc", async () => {
            const result = await instance.checkTpcConnection(
                testdata.checkTPCConnectionTenantId,
            );
            expect(result[0]).toBe(true);
            expect(result[1]).toBe("TpcDestIpAddress");
            expect(result[2]).toBe("TpcDestIpAddress2");
        });

        test("should throw error if ApiCommonDAO.getTenantsEntity failed", async () => {
            const apiCommonDAO = new APICommonDAO(
                DefaultRequest,
                DefaultContext,
            );
            jest.spyOn(apiCommonDAO, "getTenantsEntity").mockRejectedValueOnce(
                new Error("Test Error"),
            );
            Object.defineProperty(instance, "aPICommonDAO", {
                value: apiCommonDAO,
            });

            await expect(
                instance.checkTpcConnection(
                    testdata.checkTPCConnectionTenantId,
                ),
            ).rejects.toThrow("Test Error");
        });
    });
});
