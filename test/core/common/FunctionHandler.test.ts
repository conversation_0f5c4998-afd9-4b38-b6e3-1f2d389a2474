import { describe, expect, test } from "@jest/globals";
import { HttpRequest, HttpResponseInit } from "@azure/functions";
import ExtendedInvocationContext from "@/types/ExtendedInvocationContext";
import FunctionHandlerBase from "@/core/common/FunctionHandler";

const mockHttpRequest: HttpRequest = jest.fn() as any;
const mockContext: ExtendedInvocationContext = jest.fn() as any;

// class ConcreteClass extends FunctionHandlerBase {
//     public static async Handler(
//         request: HttpRequest,
//         context: ExtendedInvocationContext,
//     ): Promise<HttpResponseInit> {
//         return {
//             status: 200,
//             headers: { 'Content-Type': 'application/json' },
//             body: 'Hello, World!'
//         };
//     }
// }

describe("core/common/FunctionHandler", () => {
    describe("Handler", () => {
        test("should throw an error as not implemented yet", async () => {
            // const request: HttpRequest = {
            //     method: 'GET',
            //     url: '/test',
            //     headers: {}
            //   } as HttpRequest;
          
            // const context: ExtendedInvocationContext = {
            //     jsonBody: {
            //         something: 1
            //     }
            //   } as ExtendedInvocationContext;

            // const response: HttpResponseInit = await FunctionHandlerBase.Handler(mockHttpRequest, mockContext);
            // expect(response.status).toBe(200);

            await expect(async () => await FunctionHandlerBase.Handler(mockHttpRequest, mockContext)).rejects.toThrow(Error);
        });
    });
});