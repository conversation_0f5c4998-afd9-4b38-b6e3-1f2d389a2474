import { describe, expect, test, jest } from "@jest/globals";
import CommonUtil from "@/core/common/CommonUtil";

describe("core/common/CommonUtil", () => {
    afterEach(() => {
        jest.restoreAllMocks();
        jest.clearAllMocks();
    });

    describe("getDateTimeNow", () => {
        test("should return the current date and time in correct format", () => {
            const date = CommonUtil.getDateTimeNow();
            expect(date).toMatch(/^\d{4}\/\d{2}\/\d{2} \d{2}:\d{2}:\d{2}$/);
        });

        test("should return date in the format yyyy/MM/dd HH:mm:ss", () => {
            jest.useFakeTimers().setSystemTime(new Date('2024-08-20T15:30:45'));
            const date = CommonUtil.getDateTimeNow();
            expect(date).toBe("2024/08/20 15:30:45");
            jest.useRealTimers();
        });
    });

    describe("convDateFormat", () => {
        test("should return date as unix timestamp in milliseconds", () => {
            const result = CommonUtil.convDateFormat("2024/04/17 09:28:57");
            // Note: This timestamp might need adjustment based on timezone
            expect(result).toBeInstanceOf(Date);
            expect(result.getTime()).toBeGreaterThan(0);
        });

        test("should return null for empty string", () => {
            expect(CommonUtil.convDateFormat("")).toBe(null);
        });

        test("should return null for invalid date string", () => {
            const result = CommonUtil.convDateFormat("invalid-date");
            // Now the implementation should convert NaN to null
            expect(result).toBe(null);
        });

        test("should return null for null input", () => {
            expect(CommonUtil.convDateFormat(null as unknown as string)).toBe(null);
        });
    });

    describe("convDateToString", () => {
        test("should return null for null input", () => {
            expect(CommonUtil.convDateToString(null)).toBe(null);
        });

        test("should format date to yyyy/MM/dd 00:00:00", () => {
            const date = new Date(2024, 3, 17, 9, 28, 57); // April 17, 2024, 09:28:57
            expect(CommonUtil.convDateToString(date)).toBe("2024/04/17 00:00:00");
        });
    });

    describe("convDateTimeToString", () => {
        test("should return null for null input", () => {
            expect(CommonUtil.convDateTimeToString(null)).toBe(null);
        });

        test("should format date to yyyy/MM/dd HH:mm:ss", () => {
            const date = new Date(2024, 3, 17, 9, 28, 57); // April 17, 2024, 09:28:57
            expect(CommonUtil.convDateTimeToString(date)).toBe("2024/04/17 09:28:57");
        });
    });

    describe("chkStrType", () => {
        test("should return false for null input", () => {
            expect(CommonUtil.chkStrType(null)).toBe(false);
        });

        test("should return true for alphanumeric strings", () => {
            expect(CommonUtil.chkStrType("abc123")).toBe(true);
            expect(CommonUtil.chkStrType("ABC123")).toBe(true);
            expect(CommonUtil.chkStrType("123456")).toBe(true);
            expect(CommonUtil.chkStrType("abcDEF")).toBe(true);
        });

        test("should return false for strings with non-alphanumeric characters", () => {
            expect(CommonUtil.chkStrType("abc-123")).toBe(false);
            expect(CommonUtil.chkStrType("abc 123")).toBe(false);
            expect(CommonUtil.chkStrType("abc@123")).toBe(false);
            expect(CommonUtil.chkStrType("あいうえお")).toBe(false);
        });
    });

    describe("digest", () => {
        test("should return a hexadecimal string", () => {
            const result = CommonUtil.digest("test");
            expect(typeof result).toBe("string");
            // Should only contain hexadecimal characters
            expect(result).toMatch(/^[0-9a-f]+$/);
        });

        test("should return consistent hash for the same input", () => {
            const hash1 = CommonUtil.digest("Y000000");
            const hash2 = "6249234a9383cbdcd5785f99e1970a12"
            expect(hash1).toBe(hash2);
        });

        test("should return different hash for different inputs", () => {
            const hash1 = CommonUtil.digest("test1");
            const hash2 = CommonUtil.digest("test2");
            expect(hash1).not.toBe(hash2);
        });
    });
});
