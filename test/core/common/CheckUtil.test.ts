import { describe, expect, test } from "@jest/globals";
import CheckUtil from "@/core/common/CheckUtil";

describe("core/common/CheckUtil", () => {
    afterEach(() => {
        jest.restoreAllMocks();
        jest.clearAllMocks();
    });

    describe("checkIsNotNull", () => {
        // TODO this function name is misleading, update test label when the function is renamed
        test("should return false for non-null and non-empty strings", () => {
            expect(CheckUtil.checkIsNotNull("Hello")).toBe(false);
            expect(CheckUtil.checkIsNotNull("12345")).toBe(false);
        });

        test("checkIsNotNull should return true for null or empty strings", () => {
            expect(CheckUtil.checkIsNotNull(null)).toBe(true);
            expect(CheckUtil.checkIsNotNull(undefined)).toBe(true);
            expect(CheckUtil.checkIsNotNull("")).toBe(true);
        });
    });

    describe("checkLength", () => {
        test("should return true for strings with the correct length (fixed flag = true)", () => {
            expect(CheckUtil.checkLength("Hello", 5, true)).toBe(true);
            expect(CheckUtil.checkLength("12345", 5, true)).toBe(true);
        });

        test("should return false for strings with incorrect length (fixed flag = true)", () => {
            expect(CheckUtil.checkLength("Hello", 10, true)).toBe(false);
            expect(CheckUtil.checkLength("12345", 3, true)).toBe(false);
        });

        test("should return true for strings with length up to length parameter (fixed flag = false)", () => {
            expect(CheckUtil.checkLength("Hello", 10, false)).toBe(true);
            expect(CheckUtil.checkLength("12345", 5, false)).toBe(true);
            expect(CheckUtil.checkLength("", 5, false)).toBe(true);
        });

        test("should return false for strings with length greater than length parameter (fixed flag = false)", () => {
            expect(CheckUtil.checkLength("Hello", 3, false)).toBe(false);
            expect(CheckUtil.checkLength("12345", 3, false)).toBe(false);
        });

        test("should return false when checkString is null or undefined", () => {
            // console.log("***");
            expect(CheckUtil.checkLength(null, 5, true)).toBe(false);
            expect(CheckUtil.checkLength(undefined, 5, true)).toBe(false);
            // console.log("***");
        });
    });

    describe("checkIsNum", () => {
        test("should return true for strings containing only numbers", () => {
            expect(CheckUtil.checkIsNum("12345")).toBe(true);
            expect(CheckUtil.checkIsNum("0")).toBe(true);
        });

        test("should return false for strings containing non-numeric characters", () => {
            expect(CheckUtil.checkIsNum("Hello")).toBe(false);
            expect(CheckUtil.checkIsNum("12345a")).toBe(false);
        });

        test("should return false when checkString is null or undefined", () => {
            expect(CheckUtil.checkIsNum(null)).toBe(false);
            expect(CheckUtil.checkIsNum(undefined)).toBe(false);
        });
    });

    describe("checkDateFmt", () => {
        test("should return true for valid date formats", () => {
            expect(CheckUtil.checkDateFmt("2022-01-01", "yyyy-MM-dd")).toBe(
                true,
            );
            expect(CheckUtil.checkDateFmt("01/01/2022", "MM/dd/yyyy")).toBe(
                true,
            );
        });

        test("should return false for invalid date formats", () => {
            expect(CheckUtil.checkDateFmt("2022-01-01", "MM/dd/yyyy")).toBe(
                false,
            );
            expect(CheckUtil.checkDateFmt("01/01/2022", "yyyy-MM-dd")).toBe(
                false,
            );
        });
    });

    describe("addDay", () => {
        test("should return a date object with the correct number of days added", () => {
            const date = new Date("2022-01-01");
            const result = CheckUtil.addDay(date, 5);
            expect(result).toEqual(new Date("2022-01-06"));
        });
    });

    describe("checkIsSemiangleAlphanumic", () => {
        test("should return true for strings containing only half-width alphanumeric characters", () => {
            expect(CheckUtil.checkIsSemiangleAlphanumic("12345")).toBe(true);
            expect(CheckUtil.checkIsSemiangleAlphanumic("abcde")).toBe(true);
            expect(CheckUtil.checkIsSemiangleAlphanumic("12345abcde")).toBe(
                true,
            );
        });

        test("should return false for strings containing non-half-width alphanumeric characters", () => {
            expect(CheckUtil.checkIsSemiangleAlphanumic("１２３４５")).toBe(
                false,
            );
            expect(CheckUtil.checkIsSemiangleAlphanumic("ａｂｃｄｅ")).toBe(
                false,
            );
            expect(
                CheckUtil.checkIsSemiangleAlphanumic("12345ａｂｃｄｅ"),
            ).toBe(false);
        });
    });

    describe("checkRangeLength", () => {
        test("should return true for strings with length within the specified range", () => {
            expect(CheckUtil.checkRangeLength("12345", 3, 5)).toBe(true);
            expect(CheckUtil.checkRangeLength("12345", 5, 5)).toBe(true);
        });

        test("should return false for strings with length outside the specified range", () => {
            expect(CheckUtil.checkRangeLength("1", 2, 3)).toBe(false);
            expect(CheckUtil.checkRangeLength("12345", 3, 4)).toBe(false);
        });

        test("should return false when checkString is null or undefined", () => {
            expect(CheckUtil.checkRangeLength(null, 3, 5)).toBe(false);
            expect(CheckUtil.checkRangeLength(undefined, 3, 5)).toBe(false);
        });
    });

    describe("checkTimeFmt", () => {
        test("should return true for valid time format", () => {
            expect(CheckUtil.checkDateFmt("14:30", "HH:mm")).toBe(true);
        });

        test("should return false for invalid date formats", () => {
            expect(CheckUtil.checkDateFmt("140:30", "HH:mm")).toBe(false);
            expect(CheckUtil.checkDateFmt("14:300", "HH:mmm")).toBe(false);
        });
    });

    describe("checkIsNull", () => {
        test("should return true for null or undefined values", () => {
            expect(CheckUtil.checkIsNull(null)).toBe(true);
            expect(CheckUtil.checkIsNull(undefined)).toBe(true);
        });

        test("should return false for non-null or non-undefined values", () => {
            expect(CheckUtil.checkIsNull("")).toBe(false);
            expect(CheckUtil.checkIsNull(12345)).toBe(false);
            expect(CheckUtil.checkIsNull([])).toBe(false);
            expect(CheckUtil.checkIsNull({})).toBe(false);
        });
    });

    describe("returnPrams", () => {
        test("should return empty string for null or undefined values", () => {
            expect(CheckUtil.returnPrams(null, 0)).toBe("");
            expect(CheckUtil.returnPrams(undefined, 100)).toBe("");
            expect(CheckUtil.returnPrams("", 100)).toBe("");
        });

        test("should return input string for non-null or non-undefined values", () => {
            expect(CheckUtil.returnPrams("Test", 0)).toBe("Test");
            expect(CheckUtil.returnPrams("null", 100)).toBe("null");
        });
    });

    describe("forMartdynDate", () => {
        test("should return formatted date string", () => {
            expect(CheckUtil.forMartdynDate("2022-01-01")).toBe("2022-01-01");
            expect(CheckUtil.forMartdynDate("3052-01-01 12:13")).toBe(
                "3052-01-01 12:13",
            );
            expect(CheckUtil.forMartdynDate("2024-11-24 12:13:14")).toBe(
                "2024-11-24 12:13:14",
            );
        });

        test("should return with minutes if only hour is provided", () => {
            expect(CheckUtil.forMartdynDate("2022-01-01 12")).toBe(
                "2022-01-01 12:00",
            );
        });

        test("should return empty string for empty input", () => {
            expect(CheckUtil.forMartdynDate("")).toBe("");
        });
    });

    describe("forMartDate", () => {
        test("should return formatted date string", () => {
            expect(CheckUtil.forMartDate("020241016")).toEqual("2024/10/16");
            expect(CheckUtil.forMartDate("XYZ241016")).toEqual("2024/10/16");
        });

        test("should return empty string if input length is less than 9", () => {
            expect(CheckUtil.forMartDate("20241016")).toEqual("");
        });

        test("should return empty string if date format (???yyMMdd) is invalid", () => {
            jest.spyOn(console, "error").mockImplementation(() => {});
            expect(CheckUtil.forMartDate("123456789")).toEqual("");
        });
    });
});
