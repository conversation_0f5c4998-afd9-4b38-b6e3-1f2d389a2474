import { describe, expect, test } from "@jest/globals";

import SOAPException from "@/types/soapException";

import * as SOAPCommonUtils from "@/core/common/SOAPCommonUtils";
import Constants from "@/core/constant/Constants";
import ParameterName from "@/core/dto/ParameterName";

describe("core/common/SOAPCommonUtils", () => {
    const data = {
        tpcSourceIpAddress: "*********",
        soap1_1: "SOAP1.1",
        soap1_2: "SOAP1.2",
    };
    describe("buildMessageXML", () => {
        describe(data.soap1_1, () => {
            const config = {
                soapProtocol: data.soap1_1,
                sourceIpAddress: data.tpcSourceIpAddress,
            };

            test("should return correct XML", () => {
                const operationName = "serviceProfileQuery";
                const operationTypeName = "AllView";
                const profileName = "";
                const paramList: ParameterName[] = [
                    ParameterName.new("msisdn", "818045671234"),
                ];
                const result = `<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ns1="http://ws.apache.org/axis2"><soapenv:Body><serviceProfileQuery><SourceIPaddr>${data.tpcSourceIpAddress}</SourceIPaddr><OperationType name="AllView"><Parameter name="msisdn">818045671234</Parameter></OperationType></serviceProfileQuery></soapenv:Body></soapenv:Envelope>`;
                const xml = SOAPCommonUtils.buildMessageXML(
                    operationName,
                    operationTypeName,
                    profileName,
                    paramList,
                    config,
                );

                expect(xml).toBe(result);
            });

            test("should return correct XML with multiple parameters", () => {
                const operationName = "serviceProfileQuery";
                const operationTypeName = "AllView";
                const profileName = "";
                const paramList: ParameterName[] = [
                    ParameterName.new("msisdn", "818045671234"),
                    ParameterName.new("imsi", "510110123456789"),
                    ParameterName.new("pin", "123456"),
                ];
                const result = `<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ns1="http://ws.apache.org/axis2"><soapenv:Body><serviceProfileQuery><SourceIPaddr>${data.tpcSourceIpAddress}</SourceIPaddr><OperationType name="AllView"><Parameter name="msisdn">818045671234</Parameter><Parameter name="imsi">510110123456789</Parameter><Parameter name="pin">123456</Parameter></OperationType></serviceProfileQuery></soapenv:Body></soapenv:Envelope>`;
                const xml = SOAPCommonUtils.buildMessageXML(
                    operationName,
                    operationTypeName,
                    profileName,
                    paramList,
                    config,
                );

                expect(xml).toBe(result);
            });

            test("should return correct XML for profile view", () => {
                const operationName = Constants.SERVICEPROFILEREQUEST;
                const operationTypeName = Constants.PROFILEVIEW;
                const profileName = "TestProfile";
                const paramList: ParameterName[] = [
                    ParameterName.new("test", "123abc"),
                ];
                const result = `<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ns1="http://ws.apache.org/axis2"><soapenv:Body><serviceProfileRequest><SourceIPaddr>${data.tpcSourceIpAddress}</SourceIPaddr><OperationType name="ProfileView"><Profile>TestProfile</Profile><Parameter name="test">123abc</Parameter></OperationType></serviceProfileRequest></soapenv:Body></soapenv:Envelope>`;
                const xml = SOAPCommonUtils.buildMessageXML(
                    operationName,
                    operationTypeName,
                    profileName,
                    paramList,
                    config,
                );

                expect(xml).toBe(result);
            });

            test("should return correct XML with empty parameters", () => {
                const operationName = Constants.SERVICEPROFILEREQUEST;
                const operationTypeName = Constants.PROFILEVIEW;
                const profileName = "TestProfile";
                const paramList: ParameterName[] = [];
                const result = `<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ns1="http://ws.apache.org/axis2"><soapenv:Body><serviceProfileRequest><SourceIPaddr>${data.tpcSourceIpAddress}</SourceIPaddr><OperationType name="ProfileView"><Profile>TestProfile</Profile></OperationType></serviceProfileRequest></soapenv:Body></soapenv:Envelope>`;
                const xml = SOAPCommonUtils.buildMessageXML(
                    operationName,
                    operationTypeName,
                    profileName,
                    paramList,
                    config,
                );

                expect(xml).toBe(result);
            });
        });

        describe(data.soap1_2, () => {
            const config = {
                soapProtocol: data.soap1_2,
                sourceIpAddress: data.tpcSourceIpAddress,
            };

            test("should return correct XML", () => {
                const operationName = "serviceProfileQuery";
                const operationTypeName = "AllView";
                const profileName = "";
                const paramList: ParameterName[] = [
                    ParameterName.new("msisdn", "818045671234"),
                ];
                const result = `<soapenv:Envelope xmlns:soapenv="http://www.w3.org/2003/05/soap-envelope"><soapenv:Body><serviceProfileQuery><SourceIPaddr>${data.tpcSourceIpAddress}</SourceIPaddr><OperationType name="AllView"><Parameter name="msisdn">818045671234</Parameter></OperationType></serviceProfileQuery></soapenv:Body></soapenv:Envelope>`;
                const xml = SOAPCommonUtils.buildMessageXML(
                    operationName,
                    operationTypeName,
                    profileName,
                    paramList,
                    config,
                );

                expect(xml).toBe(result);
            });

            test("should return correct XML with multiple parameters", () => {
                const operationName = "serviceProfileQuery";
                const operationTypeName = "AllView";
                const profileName = "";
                const paramList: ParameterName[] = [
                    ParameterName.new("msisdn", "818045671234"),
                    ParameterName.new("imsi", "510110123456789"),
                    ParameterName.new("pin", "123456"),
                ];
                const result = `<soapenv:Envelope xmlns:soapenv="http://www.w3.org/2003/05/soap-envelope"><soapenv:Body><serviceProfileQuery><SourceIPaddr>${data.tpcSourceIpAddress}</SourceIPaddr><OperationType name="AllView"><Parameter name="msisdn">818045671234</Parameter><Parameter name="imsi">510110123456789</Parameter><Parameter name="pin">123456</Parameter></OperationType></serviceProfileQuery></soapenv:Body></soapenv:Envelope>`;
                const xml = SOAPCommonUtils.buildMessageXML(
                    operationName,
                    operationTypeName,
                    profileName,
                    paramList,
                    config,
                );

                expect(xml).toBe(result);
            });

            test("should return correct XML for profile view", () => {
                const operationName = Constants.SERVICEPROFILEREQUEST;
                const operationTypeName = Constants.PROFILEVIEW;
                const profileName = "TestProfile";
                const paramList: ParameterName[] = [
                    ParameterName.new("test", "123abc"),
                ];
                const result = `<soapenv:Envelope xmlns:soapenv="http://www.w3.org/2003/05/soap-envelope"><soapenv:Body><serviceProfileRequest><SourceIPaddr>${data.tpcSourceIpAddress}</SourceIPaddr><OperationType name="ProfileView"><Profile>TestProfile</Profile><Parameter name="test">123abc</Parameter></OperationType></serviceProfileRequest></soapenv:Body></soapenv:Envelope>`;
                const xml = SOAPCommonUtils.buildMessageXML(
                    operationName,
                    operationTypeName,
                    profileName,
                    paramList,
                    config,
                );

                expect(xml).toBe(result);
            });

            test("should return correct XML with empty parameters", () => {
                const operationName = Constants.SERVICEPROFILEREQUEST;
                const operationTypeName = Constants.PROFILEVIEW;
                const profileName = "TestProfile";
                const paramList: ParameterName[] = [];
                const result = `<soapenv:Envelope xmlns:soapenv="http://www.w3.org/2003/05/soap-envelope"><soapenv:Body><serviceProfileRequest><SourceIPaddr>${data.tpcSourceIpAddress}</SourceIPaddr><OperationType name="ProfileView"><Profile>TestProfile</Profile></OperationType></serviceProfileRequest></soapenv:Body></soapenv:Envelope>`;
                const xml = SOAPCommonUtils.buildMessageXML(
                    operationName,
                    operationTypeName,
                    profileName,
                    paramList,
                    config,
                );

                expect(xml).toBe(result);
            });
        });
    });

    describe("buildMessageXMLCoupon", () => {
        describe(data.soap1_1, () => {
            const config = {
                soapProtocol: data.soap1_1,
                sourceIpAddress: data.tpcSourceIpAddress,
            };

            test("should return correct XML with same operation type name and profile name", () => {
                const operationName = "serviceProfileQuery";
                const operationTypeName = "AllView";
                const profileName = "";
                const paramList: ParameterName[] = [
                    ParameterName.new("msisdn", "818045671234"),
                    ParameterName.new("imsi", "510110123456789"),
                    ParameterName.new("pin", "123456"),
                ];
                const operationTypeName2 = "AllView";
                const profileName2 = "";
                const paramList2: ParameterName[] = [
                    ParameterName.new("msisdn", "000000000000"),
                    ParameterName.new("imsi", "000000000000000"),
                    ParameterName.new("pin", "000000"),
                ];
                const result = `<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ns1="http://ws.apache.org/axis2"><soapenv:Body><serviceProfileQuery><SourceIPaddr>*********</SourceIPaddr><OperationType name="AllView"><Parameter name="msisdn">818045671234</Parameter><Parameter name="imsi">510110123456789</Parameter><Parameter name="pin">123456</Parameter></OperationType><OperationType name="AllView"><Parameter name="msisdn">000000000000</Parameter><Parameter name="imsi">000000000000000</Parameter><Parameter name="pin">000000</Parameter></OperationType></serviceProfileQuery></soapenv:Body></soapenv:Envelope>`;
                const xml = SOAPCommonUtils.buildMessageXMLCoupon(
                    operationName,
                    operationTypeName,
                    profileName,
                    paramList,
                    operationTypeName2,
                    profileName2,
                    paramList2,
                    config,
                );

                expect(xml).toBe(result);
            });

            test("should return correct XML for profile view", () => {
                const operationName = Constants.SERVICEPROFILEREQUEST;
                const operationTypeName = Constants.PROFILEVIEW;
                const profileName = "TestProfile";
                const paramList: ParameterName[] = [
                    ParameterName.new("msisdn", "818045671234"),
                    ParameterName.new("imsi", "510110123456789"),
                    ParameterName.new("pin", "123456"),
                ];
                const operationTypeName2 = Constants.PROFILEVIEW;
                const profileName2 = "TestProfile";
                const paramList2: ParameterName[] = [
                    ParameterName.new("msisdn", "000000000000"),
                    ParameterName.new("imsi", "000000000000000"),
                    ParameterName.new("pin", "000000"),
                ];
                const result = `<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ns1="http://ws.apache.org/axis2"><soapenv:Body><serviceProfileRequest><SourceIPaddr>*********</SourceIPaddr><OperationType name="ProfileView"><Profile>TestProfile</Profile><Parameter name="msisdn">818045671234</Parameter><Parameter name="imsi">510110123456789</Parameter><Parameter name="pin">123456</Parameter></OperationType><OperationType name="ProfileView"><Profile>TestProfile</Profile><Parameter name="msisdn">000000000000</Parameter><Parameter name="imsi">000000000000000</Parameter><Parameter name="pin">000000</Parameter></OperationType></serviceProfileRequest></soapenv:Body></soapenv:Envelope>`;
                const xml = SOAPCommonUtils.buildMessageXMLCoupon(
                    operationName,
                    operationTypeName,
                    profileName,
                    paramList,
                    operationTypeName2,
                    profileName2,
                    paramList2,
                    config,
                );

                expect(xml).toBe(result);
            });

            test("should return correct XML with empty parameters", () => {
                const operationName = Constants.SERVICEPROFILEREQUEST;
                const operationTypeName = Constants.PROFILEVIEW;
                const profileName = "TestProfile";
                const paramList: ParameterName[] = [
                ];
                const operationTypeName2 = Constants.PROFILEVIEW;
                const profileName2 = "TestProfile";
                const paramList2: ParameterName[] = [
                ];
                const result = `<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ns1="http://ws.apache.org/axis2"><soapenv:Body><serviceProfileRequest><SourceIPaddr>*********</SourceIPaddr><OperationType name="ProfileView"><Profile>TestProfile</Profile></OperationType><OperationType name="ProfileView"><Profile>TestProfile</Profile></OperationType></serviceProfileRequest></soapenv:Body></soapenv:Envelope>`;
                const xml = SOAPCommonUtils.buildMessageXMLCoupon(
                    operationName,
                    operationTypeName,
                    profileName,
                    paramList,
                    operationTypeName2,
                    profileName2,
                    paramList2,
                    config,
                );

                expect(xml).toBe(result);
            });
        });

        describe(data.soap1_2, () => {
            const config = {
                soapProtocol: data.soap1_2,
                sourceIpAddress: data.tpcSourceIpAddress,
            };

            test("should return correct XML with same operation type name and profile name", () => {
                const operationName = "serviceProfileQuery";
                const operationTypeName = "AllView";
                const profileName = "";
                const paramList: ParameterName[] = [
                    ParameterName.new("msisdn", "818045671234"),
                    ParameterName.new("imsi", "510110123456789"),
                    ParameterName.new("pin", "123456"),
                ];
                const operationTypeName2 = "AllView";
                const profileName2 = "";
                const paramList2: ParameterName[] = [
                    ParameterName.new("msisdn", "000000000000"),
                    ParameterName.new("imsi", "000000000000000"),
                    ParameterName.new("pin", "000000"),
                ];
                const result = `<soapenv:Envelope xmlns:soapenv="http://www.w3.org/2003/05/soap-envelope"><soapenv:Body><serviceProfileQuery><SourceIPaddr>*********</SourceIPaddr><OperationType name="AllView"><Parameter name="msisdn">818045671234</Parameter><Parameter name="imsi">510110123456789</Parameter><Parameter name="pin">123456</Parameter></OperationType><OperationType name="AllView"><Parameter name="msisdn">000000000000</Parameter><Parameter name="imsi">000000000000000</Parameter><Parameter name="pin">000000</Parameter></OperationType></serviceProfileQuery></soapenv:Body></soapenv:Envelope>`;
                const xml = SOAPCommonUtils.buildMessageXMLCoupon(
                    operationName,
                    operationTypeName,
                    profileName,
                    paramList,
                    operationTypeName2,
                    profileName2,
                    paramList2,
                    config,
                );

                expect(xml).toBe(result);
            });

            test("should return correct XML for profile view", () => {
                const operationName = Constants.SERVICEPROFILEREQUEST;
                const operationTypeName = Constants.PROFILEVIEW;
                const profileName = "TestProfile";
                const paramList: ParameterName[] = [
                    ParameterName.new("msisdn", "818045671234"),
                    ParameterName.new("imsi", "510110123456789"),
                    ParameterName.new("pin", "123456"),
                ];
                const operationTypeName2 = Constants.PROFILEVIEW;
                const profileName2 = "TestProfile";
                const paramList2: ParameterName[] = [
                    ParameterName.new("msisdn", "000000000000"),
                    ParameterName.new("imsi", "000000000000000"),
                    ParameterName.new("pin", "000000"),
                ];
                const result = `<soapenv:Envelope xmlns:soapenv="http://www.w3.org/2003/05/soap-envelope"><soapenv:Body><serviceProfileRequest><SourceIPaddr>*********</SourceIPaddr><OperationType name="ProfileView"><Profile>TestProfile</Profile><Parameter name="msisdn">818045671234</Parameter><Parameter name="imsi">510110123456789</Parameter><Parameter name="pin">123456</Parameter></OperationType><OperationType name="ProfileView"><Profile>TestProfile</Profile><Parameter name="msisdn">000000000000</Parameter><Parameter name="imsi">000000000000000</Parameter><Parameter name="pin">000000</Parameter></OperationType></serviceProfileRequest></soapenv:Body></soapenv:Envelope>`;
                const xml = SOAPCommonUtils.buildMessageXMLCoupon(
                    operationName,
                    operationTypeName,
                    profileName,
                    paramList,
                    operationTypeName2,
                    profileName2,
                    paramList2,
                    config,
                );

                expect(xml).toBe(result);
            });

            test("should return correct XML with empty parameters", () => {
                const operationName = Constants.SERVICEPROFILEREQUEST;
                const operationTypeName = Constants.PROFILEVIEW;
                const profileName = "TestProfile";
                const paramList: ParameterName[] = [
                ];
                const operationTypeName2 = Constants.PROFILEVIEW;
                const profileName2 = "TestProfile";
                const paramList2: ParameterName[] = [
                ];
                const result = `<soapenv:Envelope xmlns:soapenv="http://www.w3.org/2003/05/soap-envelope"><soapenv:Body><serviceProfileRequest><SourceIPaddr>*********</SourceIPaddr><OperationType name="ProfileView"><Profile>TestProfile</Profile></OperationType><OperationType name="ProfileView"><Profile>TestProfile</Profile></OperationType></serviceProfileRequest></soapenv:Body></soapenv:Envelope>`;
                const xml = SOAPCommonUtils.buildMessageXMLCoupon(
                    operationName,
                    operationTypeName,
                    profileName,
                    paramList,
                    operationTypeName2,
                    profileName2,
                    paramList2,
                    config,
                );

                expect(xml).toBe(result);
            });
        });
    });

    describe("readXML", () => {
        const xml = `
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
    xmlns:ns1="http://ws.apache.org/axis2">
    <soapenv:Body>
        <Result>OK</Result>
        <SomeVal>123</SomeVal>
    </soapenv:Body>
</soapenv:Envelope>`.trim();

        test("should return Document object", () => {
            const doc = SOAPCommonUtils.readXML(xml);
            expect(doc).toBeDefined();
            expect(typeof doc).toBe("object");
        });

        test("should throw error if XML is invalid", () => {
            expect(() => SOAPCommonUtils.readXML("<img>")).toThrow();
        });

        test("should throw a SOAPException if XML is invalid", () => {
            try {
                SOAPCommonUtils.readXML("<img>");
                expect(true).toBe(false);
            } catch (e) {
                expect(SOAPException.isIt(e)).toBe(true);
            }
        });
    });

    describe("convertToXML", () => {
        test("should throw error if object is null", () => {
            expect(() => SOAPCommonUtils.convertToXML(null)).toThrow();
            try {
                SOAPCommonUtils.convertToXML(null);
                expect(true).toBe(false);
            } catch (e) {
                console.warn(e);
                expect(SOAPException.isIt(e)).toBe(true);
            }
        });
    });
});
