# MVNO_CORE_API 卸ポータルコアAPI

## ローカル開発環境

### pull code with submodules

```
git clone <SSH LINK> --recursive
```

### 環境を準備

```
Azure Functions Core Tools v4 (Make sure Azure Functions Core Tools v4.0.5382+)
node v20
pnpm 8.9.2 
mongo 4.2
postgre 14
Redis 7
```

-   Nodejs: https://nodejs.org/en/
-   Pnpm: https://pnpm.io/installation
-   Mongo: https://www.mongodb.com/docs/manual/installation/
-   Azure Functions Core Tools: https://github.com/Azure/azure-functions-core-tools
-   Postgre: https://www.postgresql.org/download/
-   Redis: https://redis.io/docs/latest/operate/oss_and_stack/install/install-redis/install-redis-on-mac-os/

### プロジェクトのパッケージをインストール

```
pnpm install
```

### ローカルで関数を実行する


```
# 1番目のコンソルで
#start storage service
npm run start-storage-service

# 2番目のコンソルで
#start function
pnpm start

# 3番目のコンソルで
pnpm watch
```

http://localhost:7712/MVNO/api/V100/healthcheck
にアクセスして確認できます。

### 単体テスト

1. 共通の関数（関数レベル）

```
test/* (exclude functions)
```

フォルダに、srcの中の各関数それぞれに関数を開発終わってから、単体テストを整備する。

→ 全てテスト実行:

```
pnpm run test:unit
```

→ 指定ファイルをテスト実行:

```
pnpm run test:unit -- <file name>

ex:
pnpm run test:unit -- Check.test.ts
```

2. API の関数（API レベル）

```
test/functions/<関数名>.test.ts
```

フォルダに、API のロジックを開発終わってから、単体テストを整備する。

→ 全てテスト実行:

```
pnpm run test:fuctions
```

→ 指定 API をテスト実行:

```
pnpm run test:fuctions -- <file name>

ex:
pnpm run test:fuctions -- LinesDelHandler.test.ts
```

## 利用パッケージ

-   [axios](https://github.com/axios/axios). Promise based HTTP client for the browser and node.js
-   [date-fns](https://date-fns.org/). Modern JavaScript date utility library
-   [sequelize](https://sequelize.org/). Sequelize is a modern TypeScript and Node.js ORM for Oracle, Postgres, MySQL, MariaDB, SQLite and SQL Server, and more
-   [sequelize-typescript](https://www.npmjs.com/package/sequelize-typescript). Decorators and some other features for sequelize (v6).
-   [joi](https://joi.dev/api/?v=17.6.0). Describe your data using a simple, intuitive, and readable language.
-   [mongoose](https://mongoosejs.com/). Elegant MongoDB object modeling for Node.js

-   [Jest](https://jestjs.io/). Jest is a delightful JavaScript Testing Framework with a focus on simplicity.

## フォルダ-構造

    .
    ├── config                          # 設定ファイルなど
    ├── dist                            # jsにコンパイルしたコード
    ├── src                             # メインのソースコードフォルダ
    │   ├── appconfig                   # Azure App Configurationや環境変数定義
    │   ├── core                        # 1.war/src/com/ntt/mawp のように
    │   ├── database                    # データベース定義
    │   ├── functions                   # Azure Function folder
    │   ├── middleware                  # ミドルウェア定義
    │   └── types                       # タイプなど
    │   └── index.ts                    # Rootファイル。APIエンドポイント登録などは、ここ
    ├── test                            # Test files (alternatively `test`)
    │   ├── ...                         # srcのように
    │   ├── testing                     # Azure Function Test contextなど 定義
    └── ...


## Local curl

```
curl -XPOST http://localhost:7713/MVNO/api/V100/portal/coreswimmylog/get_maintenance_mode -H "X-role-id: 1" -H "X-tenant-id: TST000" -H "x-user-plus-id: ZZZ@test0" -H "Content-Type: application/json" -d '{}'
```